---
type: "agent_requested"
description: "A mandatory checklist of security best practices for all code generation, including input validation, output sanitization, and permission checks."
---
# Rule: Security Best Practices Checklist

When generating or modifying any code that handles user input or displays data, you must adhere to the following security principles:

1.  **Input Validation:** Never trust user input. All data from API requests, forms, or URL parameters must be validated against a strict schema before being used.
2.  **Output Sanitization:** When rendering data in the UI, ensure it is properly sanitized to prevent Cross-Site Scripting (XSS) attacks.
3.  **Permissions Checks:** For any action that modifies data, verify that the authenticated user has the necessary permissions to perform that action.
4.  **No Secrets in Code:** Never hard-code API keys, passwords, or other secrets directly in the source code. Use environment variables.