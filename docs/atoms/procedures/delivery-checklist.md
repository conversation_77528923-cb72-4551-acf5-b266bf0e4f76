---
type: "agent_requested"
description: "Defines the final checklist and verification artifacts required to mark a task as complete, including running tests and builds."
---
# Rule: Final Delivery & Verification Checklist

This rule defines the final set of commands to run and artifacts to produce to mark a task as complete. You must execute these steps in order and present the results in a final, consolidated "Automated Validation Report."

**1. Test Suite Verification:**
   - **Command:** Run `npm test`.
   - **Artifact:** Present the complete, unabridged console output, ensuring it ends with a clear "ALL TESTS PASSED" status.

**2. Production Build Verification:**
   - **Command:** Run `npm run build`.
   - **Artifact:** Present the complete, unabridged console output, ensuring it shows a successful build with zero errors.

**3. Documentation Generation & Update:**
   - **Action:** For every file you significantly modified, run the `/docs` command to ensure code-level documentation is accurate and up-to-date.
   - **Action:** Use the `/edit` command on the main project `README.md` to add a concise, plain-English summary of the new feature or fix under a "Recent Changes" section.
   - **Artifact:** Confirm that both actions have been completed.

**4. Change Impact Analysis:**
   - **Command:** Run `git diff --stat main`.
   - **Artifact:** Present the list of modified files in a summary. For each file, provide a one-sentence explanation of *why* it was changed. Explicitly state if any new dependencies were added.