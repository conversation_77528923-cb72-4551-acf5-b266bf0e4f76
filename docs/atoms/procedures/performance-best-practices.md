---
type: "agent_requested"
description: "A checklist of performance optimization best practices, focusing on efficient database queries and frontend rendering"
---
# Rule: Performance Best Practices Checklist

When generating or modifying code, consider the following performance implications:

1.  **Avoid N+1 Queries:** When fetching a list of items and their related data, use a single, optimized query (e.g., Prisma's `include`) instead of making one query for the list and then N additional queries inside a loop.
2.  **Use Lazy Loading:** For UI components that are not immediately visible (e.g., modals, content below the fold), use dynamic imports to lazy-load them.
3.  **Memoize Expensive Computations:** For complex calculations or components in React, use `useMemo` and `React.memo` to avoid unnecessary re-renders and re-computations.