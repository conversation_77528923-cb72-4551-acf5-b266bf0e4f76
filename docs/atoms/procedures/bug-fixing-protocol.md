---
type: "agent_requested"
description: "The mandatory, step-by-step protocol for diagnosing and fixing bugs using automated tests and the /debug command."
---
# Rule: Bug Fixing Protocol

To diagnose and fix a bug, you must follow this exact, systematic procedure.

**1. Isolate and Define:**
   - Clearly state the bug and the exact steps to reproduce it manually.

**2. Prove the Bug's Existence:**
   - **Command:** Use the `/test` command to generate a single, new automated test that specifically targets the buggy behavior.
   - **Artifact:** Run the test and present the failing test log as objective proof of the bug. This is the only accepted starting point.

**3. Debug with Evidence:**
   - **Command:** Use the `/debug` command.
   - **Context:** Your prompt MUST include the full error message and stack trace from the failing test in Step 2.
   - **Action:** Apply the suggested fix provided by the AI.

**4. Verify the Fix:**
   - **Action:** Re-run the specific test from Step 2.
   - **Outcome:**
     - **If it passes:** The fix is provisionally successful. Now run the *entire* test suite (`npm test`) to check for regressions. If all tests pass, the bug is considered fixed.
     - **If it fails:** The fix was incorrect. Provide the new error message back to the `/debug` command and iterate until the test passes.