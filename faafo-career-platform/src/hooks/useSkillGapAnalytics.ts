/**
 * React Hook for Skill Gap Analytics Integration
 * Provides easy-to-use analytics tracking for React components
 */

import { useEffect, useRef, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { skillGapAnalytics } from '@/lib/analytics/skill-gap-analytics';

interface UseSkillGapAnalyticsOptions {
  autoTrackPageViews?: boolean;
  autoTrackPerformance?: boolean;
  trackUserInteractions?: boolean;
}

interface AnalyticsHook {
  // Assessment tracking
  trackAssessmentStarted: (skillCount: number, assessmentType?: 'initial' | 'update' | 'comprehensive') => void;
  trackSkillAdded: (skillName: string, skillCategory: string, source: 'search' | 'suggestion' | 'manual', position: number) => void;
  trackSkillRated: (skillName: string, rating: number, confidenceLevel: number, timeSpent: number) => void;
  trackSkillRemoved: (skillName: string, reason: 'user_action' | 'duplicate' | 'irrelevant') => void;
  trackAssessmentSubmitted: (skillCount: number, averageRating: number, averageConfidence: number, timeSpent: number, completionRate: number) => void;

  // Gap analysis tracking
  trackGapAnalysisStarted: (careerPath: string, experienceLevel: string, timeframe: string, skillCount: number) => void;
  trackGapAnalysisCompleted: (analysisId: string, gaps: any[], processingTime: number, aiServiceUsed: boolean, edgeCaseHandlerUsed: boolean, fallbackDataUsed: boolean) => void;
  trackGapAnalysisFailed: (errorType: string, errorMessage: string, retryCount: number, fallbackUsed: boolean) => void;

  // Learning plan tracking
  trackLearningPlanViewed: (analysisId: string, planType: 'basic' | 'comprehensive' | 'personalized', phaseCount: number, totalHours: number) => void;
  trackLearningResourceClicked: (resourceTitle: string, resourceType: string, resourceUrl: string, position: number, context: 'learning_plan' | 'skill_gap' | 'recommendation') => void;
  trackMilestoneCompleted: (milestoneId: string, skillName: string, timeToComplete: number) => void;

  // UI interaction tracking
  trackTabChanged: (fromTab: string, toTab: string, timeSpent: number) => void;
  trackFilterApplied: (filterType: string, filterValue: string, resultCount: number) => void;
  trackSearchPerformed: (searchTerm: string, resultCount: number, selectedResult: string | undefined, searchTime: number) => void;

  // Error tracking
  trackEdgeCaseTriggered: (edgeCaseType: string, context: string, fallbackUsed: boolean, userRecovered: boolean) => void;
  trackErrorEncountered: (errorType: string, errorMessage: string, context: string, userAction: string, recovered: boolean) => void;

  // Performance tracking
  trackPerformanceMetric: (metricName: string, value: number, context: string, threshold?: number) => void;
  trackTiming: (name: string, duration: number, category?: string) => void;

  // Page tracking
  trackPageView: (page: string, properties?: Record<string, any>) => void;

  // Utility functions
  startTimer: (name: string) => () => void;
  measureAsync: <T>(name: string, asyncFn: () => Promise<T>) => Promise<T>;
}

export function useSkillGapAnalytics(options: UseSkillGapAnalyticsOptions = {}): AnalyticsHook {
  const { data: session } = useSession();
  const timers = useRef<Map<string, number>>(new Map());
  const pageStartTime = useRef<number>(Date.now());
  const currentPage = useRef<string>('');

  const {
    autoTrackPageViews = true,
    autoTrackPerformance = true,
    trackUserInteractions = true
  } = options;

  // Initialize analytics when user session is available
  useEffect(() => {
    if (session?.user?.id) {
      skillGapAnalytics.initialize(session.user.id);
    }
  }, [session?.user?.id]);

  // Auto-track page views
  useEffect(() => {
    if (autoTrackPageViews && typeof window !== 'undefined') {
      const currentUrl = window.location.pathname;
      if (currentUrl !== currentPage.current) {
        currentPage.current = currentUrl;
        pageStartTime.current = Date.now();
        skillGapAnalytics.trackPageView(currentUrl);
      }
    }
  }, [autoTrackPageViews]);

  // Auto-track performance metrics
  useEffect(() => {
    if (autoTrackPerformance && typeof window !== 'undefined') {
      // Track page load performance
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            skillGapAnalytics.trackTiming('page_load', navEntry.loadEventEnd - navEntry.fetchStart);
          }
        });
      });
      observer.observe({ entryTypes: ['navigation'] });

      return () => observer.disconnect();
    }
  }, [autoTrackPerformance]);

  // Track user interactions
  useEffect(() => {
    if (trackUserInteractions && typeof window !== 'undefined') {
      const handleClick = (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        if (target.tagName === 'BUTTON' || target.tagName === 'A') {
          const text = target.textContent?.trim() || target.getAttribute('aria-label') || 'unknown';
          skillGapAnalytics.track('button_clicked' as any, {
            userId: session?.user?.id || 'anonymous',
            buttonText: text,
            elementType: target.tagName.toLowerCase(),
            page: window.location.pathname
          });
        }
      };

      document.addEventListener('click', handleClick);
      return () => document.removeEventListener('click', handleClick);
    }
  }, [trackUserInteractions, session?.user?.id]);

  // Assessment tracking functions
  const trackAssessmentStarted = useCallback((skillCount: number, assessmentType: 'initial' | 'update' | 'comprehensive' = 'initial') => {
    skillGapAnalytics.trackAssessmentFlow.started(skillCount, assessmentType);
  }, []);

  const trackSkillAdded = useCallback((skillName: string, skillCategory: string, source: 'search' | 'suggestion' | 'manual', position: number) => {
    skillGapAnalytics.trackAssessmentFlow.skillAdded(skillName, skillCategory, source, position);
  }, []);

  const trackSkillRated = useCallback((skillName: string, rating: number, confidenceLevel: number, timeSpent: number) => {
    skillGapAnalytics.trackAssessmentFlow.skillRated(skillName, rating, confidenceLevel, timeSpent);
  }, []);

  const trackSkillRemoved = useCallback((skillName: string, reason: 'user_action' | 'duplicate' | 'irrelevant') => {
    skillGapAnalytics.trackAssessmentFlow.skillRemoved(skillName, reason);
  }, []);

  const trackAssessmentSubmitted = useCallback((skillCount: number, averageRating: number, averageConfidence: number, timeSpent: number, completionRate: number) => {
    skillGapAnalytics.trackAssessmentFlow.submitted(skillCount, averageRating, averageConfidence, timeSpent, completionRate);
  }, []);

  // Gap analysis tracking functions
  const trackGapAnalysisStarted = useCallback((careerPath: string, experienceLevel: string, timeframe: string, skillCount: number) => {
    skillGapAnalytics.trackGapAnalysis.started(careerPath, experienceLevel, timeframe, skillCount);
  }, []);

  const trackGapAnalysisCompleted = useCallback((analysisId: string, gaps: any[], processingTime: number, aiServiceUsed: boolean, edgeCaseHandlerUsed: boolean, fallbackDataUsed: boolean) => {
    skillGapAnalytics.trackGapAnalysis.completed(analysisId, gaps, processingTime, aiServiceUsed, edgeCaseHandlerUsed, fallbackDataUsed);
  }, []);

  const trackGapAnalysisFailed = useCallback((errorType: string, errorMessage: string, retryCount: number, fallbackUsed: boolean) => {
    skillGapAnalytics.trackGapAnalysis.failed(errorType, errorMessage, retryCount, fallbackUsed);
  }, []);

  // Learning plan tracking functions
  const trackLearningPlanViewed = useCallback((analysisId: string, planType: 'basic' | 'comprehensive' | 'personalized', phaseCount: number, totalHours: number) => {
    skillGapAnalytics.trackLearningPlan.viewed(analysisId, planType, phaseCount, totalHours);
  }, []);

  const trackLearningResourceClicked = useCallback((resourceTitle: string, resourceType: string, resourceUrl: string, position: number, context: 'learning_plan' | 'skill_gap' | 'recommendation') => {
    skillGapAnalytics.trackLearningPlan.resourceClicked(resourceTitle, resourceType, resourceUrl, position, context);
  }, []);

  const trackMilestoneCompleted = useCallback((milestoneId: string, skillName: string, timeToComplete: number) => {
    skillGapAnalytics.trackLearningPlan.milestoneCompleted(milestoneId, skillName, timeToComplete);
  }, []);

  // UI interaction tracking functions
  const trackTabChanged = useCallback((fromTab: string, toTab: string, timeSpent: number) => {
    skillGapAnalytics.trackUI.tabChanged(fromTab, toTab, timeSpent);
  }, []);

  const trackFilterApplied = useCallback((filterType: string, filterValue: string, resultCount: number) => {
    skillGapAnalytics.trackUI.filterApplied(filterType, filterValue, resultCount);
  }, []);

  const trackSearchPerformed = useCallback((searchTerm: string, resultCount: number, selectedResult: string | undefined, searchTime: number) => {
    skillGapAnalytics.trackUI.searchPerformed(searchTerm, resultCount, selectedResult, searchTime);
  }, []);

  // Error tracking functions
  const trackEdgeCaseTriggered = useCallback((edgeCaseType: string, context: string, fallbackUsed: boolean, userRecovered: boolean) => {
    skillGapAnalytics.trackError.edgeCaseTriggered(edgeCaseType, context, fallbackUsed, userRecovered);
  }, []);

  const trackErrorEncountered = useCallback((errorType: string, errorMessage: string, context: string, userAction: string, recovered: boolean) => {
    skillGapAnalytics.trackError.errorEncountered(errorType, errorMessage, context, userAction, recovered);
  }, []);

  // Performance tracking functions
  const trackPerformanceMetric = useCallback((metricName: string, value: number, context: string, threshold?: number) => {
    skillGapAnalytics.track('performance_metric' as any, {
      userId: session?.user?.id || 'anonymous',
      metricName,
      value,
      context,
      threshold,
      exceeded: threshold ? value > threshold : undefined
    });
  }, [session?.user?.id]);

  const trackTiming = useCallback((name: string, duration: number, category?: string) => {
    skillGapAnalytics.trackTiming(name, duration, category);
  }, []);

  // Page tracking function
  const trackPageView = useCallback((page: string, properties?: Record<string, any>) => {
    skillGapAnalytics.trackPageView(page, properties);
  }, []);

  // Utility functions
  const startTimer = useCallback((name: string) => {
    const startTime = Date.now();
    timers.current.set(name, startTime);
    
    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      timers.current.delete(name);
      trackTiming(name, duration);
      return duration;
    };
  }, [trackTiming]);

  const measureAsync = useCallback(async <T>(name: string, asyncFn: () => Promise<T>): Promise<T> => {
    const stopTimer = startTimer(name);
    try {
      const result = await asyncFn();
      stopTimer();
      return result;
    } catch (error) {
      stopTimer();
      trackErrorEncountered('async_function_error', error instanceof Error ? error.message : 'Unknown error', name, 'function_execution', false);
      throw error;
    }
  }, [startTimer, trackErrorEncountered]);

  return {
    // Assessment tracking
    trackAssessmentStarted,
    trackSkillAdded,
    trackSkillRated,
    trackSkillRemoved,
    trackAssessmentSubmitted,

    // Gap analysis tracking
    trackGapAnalysisStarted,
    trackGapAnalysisCompleted,
    trackGapAnalysisFailed,

    // Learning plan tracking
    trackLearningPlanViewed,
    trackLearningResourceClicked,
    trackMilestoneCompleted,

    // UI interaction tracking
    trackTabChanged,
    trackFilterApplied,
    trackSearchPerformed,

    // Error tracking
    trackEdgeCaseTriggered,
    trackErrorEncountered,

    // Performance tracking
    trackPerformanceMetric,
    trackTiming,

    // Page tracking
    trackPageView,

    // Utility functions
    startTimer,
    measureAsync
  };
}
