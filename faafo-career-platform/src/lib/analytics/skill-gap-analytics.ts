/**
 * Analytics & Telemetry System for Skill Gap Analyzer
 * Tracks user behavior, feature usage, and performance metrics
 */

import { PerformanceMonitor } from '@/lib/performance-monitor';

// Analytics event types
export interface AnalyticsEvent {
  eventName: string;
  userId?: string;
  sessionId: string;
  timestamp: Date;
  properties: Record<string, any>;
  context: AnalyticsContext;
}

export interface AnalyticsContext {
  userAgent: string;
  url: string;
  referrer: string;
  viewport: { width: number; height: number };
  device: 'mobile' | 'tablet' | 'desktop';
  browser: string;
  os: string;
}

// Skill Gap Analyzer specific events
export interface SkillGapAnalyticsEvents {
  // Assessment events
  'skill_assessment_started': {
    userId: string;
    assessmentType: 'initial' | 'update' | 'comprehensive';
    skillCount: number;
  };
  
  'skill_added': {
    userId: string;
    skillName: string;
    skillCategory: string;
    source: 'search' | 'suggestion' | 'manual';
    position: number;
  };
  
  'skill_rated': {
    userId: string;
    skillName: string;
    rating: number;
    confidenceLevel: number;
    timeSpent: number;
  };
  
  'skill_removed': {
    userId: string;
    skillName: string;
    reason: 'user_action' | 'duplicate' | 'irrelevant';
  };
  
  'assessment_submitted': {
    userId: string;
    skillCount: number;
    averageRating: number;
    averageConfidence: number;
    timeSpent: number;
    completionRate: number;
  };
  
  // Analysis events
  'gap_analysis_started': {
    userId: string;
    careerPath: string;
    experienceLevel: string;
    timeframe: string;
    skillCount: number;
  };
  
  'gap_analysis_completed': {
    userId: string;
    analysisId: string;
    gapCount: number;
    criticalGaps: number;
    highGaps: number;
    mediumGaps: number;
    lowGaps: number;
    processingTime: number;
    aiServiceUsed: boolean;
    edgeCaseHandlerUsed: boolean;
    fallbackDataUsed: boolean;
  };
  
  'gap_analysis_failed': {
    userId: string;
    errorType: string;
    errorMessage: string;
    retryCount: number;
    fallbackUsed: boolean;
  };
  
  // Learning plan events
  'learning_plan_viewed': {
    userId: string;
    analysisId: string;
    planType: 'basic' | 'comprehensive' | 'personalized';
    phaseCount: number;
    totalHours: number;
  };
  
  'learning_resource_clicked': {
    userId: string;
    resourceTitle: string;
    resourceType: string;
    resourceUrl: string;
    position: number;
    context: 'learning_plan' | 'skill_gap' | 'recommendation';
  };
  
  'milestone_marked_complete': {
    userId: string;
    milestoneId: string;
    skillName: string;
    timeToComplete: number;
  };
  
  // UI interaction events
  'tab_changed': {
    userId: string;
    fromTab: string;
    toTab: string;
    timeSpent: number;
  };
  
  'filter_applied': {
    userId: string;
    filterType: string;
    filterValue: string;
    resultCount: number;
  };
  
  'search_performed': {
    userId: string;
    searchTerm: string;
    resultCount: number;
    selectedResult?: string;
    searchTime: number;
  };
  
  // Error and edge case events
  'edge_case_triggered': {
    userId: string;
    edgeCaseType: string;
    context: string;
    fallbackUsed: boolean;
    userRecovered: boolean;
  };
  
  'error_encountered': {
    userId: string;
    errorType: string;
    errorMessage: string;
    context: string;
    userAction: string;
    recovered: boolean;
  };
  
  // Performance events
  'performance_metric': {
    userId: string;
    metricName: string;
    value: number;
    context: string;
    threshold?: number;
    exceeded?: boolean;
  };
}

export class SkillGapAnalytics {
  private sessionId: string;
  private userId?: string;
  private performanceMonitor: PerformanceMonitor;
  private eventQueue: AnalyticsEvent[] = [];
  private isOnline: boolean = true;
  private batchSize: number = 10;
  private flushInterval: number = 30000; // 30 seconds
  private flushTimer?: NodeJS.Timeout;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.performanceMonitor = new PerformanceMonitor();
    this.setupEventListeners();
    this.startBatchFlush();
  }

  /**
   * Initialize analytics with user context
   */
  initialize(userId: string): void {
    this.userId = userId;
    this.track('session_started', {
      userId,
      timestamp: new Date(),
      context: this.getAnalyticsContext()
    });
  }

  /**
   * Track a skill gap analyzer event
   */
  track<T extends keyof SkillGapAnalyticsEvents>(
    eventName: T,
    properties: SkillGapAnalyticsEvents[T]
  ): void {
    const event: AnalyticsEvent = {
      eventName,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date(),
      properties,
      context: this.getAnalyticsContext()
    };

    this.eventQueue.push(event);
    
    // Flush immediately for critical events
    if (this.isCriticalEvent(eventName)) {
      this.flush();
    }
  }

  /**
   * Track page view
   */
  trackPageView(page: string, properties?: Record<string, any>): void {
    this.track('page_viewed' as any, {
      userId: this.userId || 'anonymous',
      page,
      url: window.location.href,
      referrer: document.referrer,
      ...properties
    });
  }

  /**
   * Track user timing
   */
  trackTiming(name: string, duration: number, category?: string): void {
    this.track('performance_metric' as any, {
      userId: this.userId || 'anonymous',
      metricName: name,
      value: duration,
      context: category || 'timing'
    });
  }

  /**
   * Track skill assessment flow
   */
  trackAssessmentFlow = {
    started: (skillCount: number, assessmentType: 'initial' | 'update' | 'comprehensive' = 'initial') => {
      this.track('skill_assessment_started', {
        userId: this.userId!,
        assessmentType,
        skillCount
      });
    },

    skillAdded: (skillName: string, skillCategory: string, source: 'search' | 'suggestion' | 'manual', position: number) => {
      this.track('skill_added', {
        userId: this.userId!,
        skillName,
        skillCategory,
        source,
        position
      });
    },

    skillRated: (skillName: string, rating: number, confidenceLevel: number, timeSpent: number) => {
      this.track('skill_rated', {
        userId: this.userId!,
        skillName,
        rating,
        confidenceLevel,
        timeSpent
      });
    },

    skillRemoved: (skillName: string, reason: 'user_action' | 'duplicate' | 'irrelevant') => {
      this.track('skill_removed', {
        userId: this.userId!,
        skillName,
        reason
      });
    },

    submitted: (skillCount: number, averageRating: number, averageConfidence: number, timeSpent: number, completionRate: number) => {
      this.track('assessment_submitted', {
        userId: this.userId!,
        skillCount,
        averageRating,
        averageConfidence,
        timeSpent,
        completionRate
      });
    }
  };

  /**
   * Track gap analysis flow
   */
  trackGapAnalysis = {
    started: (careerPath: string, experienceLevel: string, timeframe: string, skillCount: number) => {
      this.track('gap_analysis_started', {
        userId: this.userId!,
        careerPath,
        experienceLevel,
        timeframe,
        skillCount
      });
    },

    completed: (analysisId: string, gaps: any[], processingTime: number, aiServiceUsed: boolean, edgeCaseHandlerUsed: boolean, fallbackDataUsed: boolean) => {
      const gapCounts = this.categorizeGaps(gaps);
      this.track('gap_analysis_completed', {
        userId: this.userId!,
        analysisId,
        gapCount: gaps.length,
        ...gapCounts,
        processingTime,
        aiServiceUsed,
        edgeCaseHandlerUsed,
        fallbackDataUsed
      });
    },

    failed: (errorType: string, errorMessage: string, retryCount: number, fallbackUsed: boolean) => {
      this.track('gap_analysis_failed', {
        userId: this.userId!,
        errorType,
        errorMessage,
        retryCount,
        fallbackUsed
      });
    }
  };

  /**
   * Track learning plan interactions
   */
  trackLearningPlan = {
    viewed: (analysisId: string, planType: 'basic' | 'comprehensive' | 'personalized', phaseCount: number, totalHours: number) => {
      this.track('learning_plan_viewed', {
        userId: this.userId!,
        analysisId,
        planType,
        phaseCount,
        totalHours
      });
    },

    resourceClicked: (resourceTitle: string, resourceType: string, resourceUrl: string, position: number, context: 'learning_plan' | 'skill_gap' | 'recommendation') => {
      this.track('learning_resource_clicked', {
        userId: this.userId!,
        resourceTitle,
        resourceType,
        resourceUrl,
        position,
        context
      });
    },

    milestoneCompleted: (milestoneId: string, skillName: string, timeToComplete: number) => {
      this.track('milestone_marked_complete', {
        userId: this.userId!,
        milestoneId,
        skillName,
        timeToComplete
      });
    }
  };

  /**
   * Track UI interactions
   */
  trackUI = {
    tabChanged: (fromTab: string, toTab: string, timeSpent: number) => {
      this.track('tab_changed', {
        userId: this.userId!,
        fromTab,
        toTab,
        timeSpent
      });
    },

    filterApplied: (filterType: string, filterValue: string, resultCount: number) => {
      this.track('filter_applied', {
        userId: this.userId!,
        filterType,
        filterValue,
        resultCount
      });
    },

    searchPerformed: (searchTerm: string, resultCount: number, selectedResult: string | undefined, searchTime: number) => {
      this.track('search_performed', {
        userId: this.userId!,
        searchTerm,
        resultCount,
        selectedResult,
        searchTime
      });
    }
  };

  /**
   * Track errors and edge cases
   */
  trackError = {
    edgeCaseTriggered: (edgeCaseType: string, context: string, fallbackUsed: boolean, userRecovered: boolean) => {
      this.track('edge_case_triggered', {
        userId: this.userId!,
        edgeCaseType,
        context,
        fallbackUsed,
        userRecovered
      });
    },

    errorEncountered: (errorType: string, errorMessage: string, context: string, userAction: string, recovered: boolean) => {
      this.track('error_encountered', {
        userId: this.userId!,
        errorType,
        errorMessage,
        context,
        userAction,
        recovered
      });
    }
  };

  /**
   * Get analytics context
   */
  private getAnalyticsContext(): AnalyticsContext {
    return {
      userAgent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      device: this.getDeviceType(),
      browser: this.getBrowser(),
      os: this.getOS()
    };
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Categorize skill gaps by severity
   */
  private categorizeGaps(gaps: any[]): {
    criticalGaps: number;
    highGaps: number;
    mediumGaps: number;
    lowGaps: number;
  } {
    return gaps.reduce((counts, gap) => {
      switch (gap.gapSeverity) {
        case 'CRITICAL':
          counts.criticalGaps++;
          break;
        case 'HIGH':
          counts.highGaps++;
          break;
        case 'MEDIUM':
          counts.mediumGaps++;
          break;
        case 'LOW':
          counts.lowGaps++;
          break;
      }
      return counts;
    }, { criticalGaps: 0, highGaps: 0, mediumGaps: 0, lowGaps: 0 });
  }

  /**
   * Check if event is critical and should be flushed immediately
   */
  private isCriticalEvent(eventName: string): boolean {
    const criticalEvents = [
      'gap_analysis_failed',
      'error_encountered',
      'edge_case_triggered'
    ];
    return criticalEvents.includes(eventName);
  }

  /**
   * Setup event listeners for automatic tracking
   */
  private setupEventListeners(): void {
    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.flush();
      }
    });

    // Track before page unload
    window.addEventListener('beforeunload', () => {
      this.flush();
    });

    // Track online/offline status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flush();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * Start batch flush timer
   */
  private startBatchFlush(): void {
    this.flushTimer = setInterval(() => {
      if (this.eventQueue.length > 0) {
        this.flush();
      }
    }, this.flushInterval);
  }

  /**
   * Flush events to analytics service
   */
  private async flush(): Promise<void> {
    if (this.eventQueue.length === 0 || !this.isOnline) {
      return;
    }

    const events = this.eventQueue.splice(0, this.batchSize);
    
    try {
      await this.sendEvents(events);
    } catch (error) {
      console.error('Failed to send analytics events:', error);
      // Re-queue events for retry
      this.eventQueue.unshift(...events);
    }
  }

  /**
   * Send events to analytics service
   */
  private async sendEvents(events: AnalyticsEvent[]): Promise<void> {
    const response = await fetch('/api/analytics/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ events }),
    });

    if (!response.ok) {
      throw new Error(`Analytics API error: ${response.status}`);
    }
  }

  /**
   * Get device type
   */
  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  /**
   * Get browser name
   */
  private getBrowser(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  /**
   * Get operating system
   */
  private getOS(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  /**
   * Cleanup analytics instance
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush();
  }
}

// Global analytics instance
export const skillGapAnalytics = new SkillGapAnalytics();
