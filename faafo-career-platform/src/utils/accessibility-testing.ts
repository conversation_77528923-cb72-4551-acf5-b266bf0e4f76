/**
 * Accessibility Testing Utilities for Skill Gap Analyzer
 * Provides automated accessibility testing and validation
 */

interface AccessibilityTestResult {
  passed: boolean;
  score: number;
  violations: AccessibilityViolation[];
  warnings: AccessibilityWarning[];
  recommendations: string[];
}

interface AccessibilityViolation {
  rule: string;
  severity: 'critical' | 'serious' | 'moderate' | 'minor';
  element: string;
  description: string;
  fix: string;
}

interface AccessibilityWarning {
  rule: string;
  element: string;
  description: string;
  suggestion: string;
}

interface ColorContrastResult {
  ratio: number;
  level: 'AAA' | 'AA' | 'FAIL';
  foreground: string;
  background: string;
}

export class AccessibilityTester {
  private violations: AccessibilityViolation[] = [];
  private warnings: AccessibilityWarning[] = [];

  /**
   * Run comprehensive accessibility tests
   */
  async runAccessibilityTests(element?: HTMLElement): Promise<AccessibilityTestResult> {
    const testElement = element || document.body;
    
    this.violations = [];
    this.warnings = [];

    // Run all accessibility tests
    await this.testSemanticStructure(testElement);
    await this.testKeyboardNavigation(testElement);
    await this.testAriaAttributes(testElement);
    await this.testColorContrast(testElement);
    await this.testFocusManagement(testElement);
    await this.testScreenReaderCompatibility(testElement);
    await this.testFormAccessibility(testElement);
    await this.testImageAccessibility(testElement);

    const score = this.calculateAccessibilityScore();
    const recommendations = this.generateRecommendations();

    return {
      passed: this.violations.filter(v => v.severity === 'critical' || v.severity === 'serious').length === 0,
      score,
      violations: this.violations,
      warnings: this.warnings,
      recommendations
    };
  }

  /**
   * Test semantic HTML structure
   */
  private async testSemanticStructure(element: HTMLElement): Promise<void> {
    // Check for proper heading hierarchy
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (index === 0 && level !== 1) {
        this.violations.push({
          rule: 'heading-hierarchy',
          severity: 'serious',
          element: heading.tagName,
          description: 'Page should start with h1',
          fix: 'Use h1 for the main page heading'
        });
      }
      
      if (level > previousLevel + 1) {
        this.violations.push({
          rule: 'heading-hierarchy',
          severity: 'moderate',
          element: heading.tagName,
          description: 'Heading levels should not skip',
          fix: `Use h${previousLevel + 1} instead of h${level}`
        });
      }
      
      previousLevel = level;
    });

    // Check for landmark elements
    const landmarks = element.querySelectorAll('main, nav, aside, section, article, header, footer');
    if (landmarks.length === 0) {
      this.violations.push({
        rule: 'landmark-elements',
        severity: 'moderate',
        element: 'document',
        description: 'Page lacks landmark elements',
        fix: 'Add semantic landmark elements (main, nav, aside, etc.)'
      });
    }

    // Check for main element
    const mainElements = element.querySelectorAll('main');
    if (mainElements.length === 0) {
      this.violations.push({
        rule: 'main-element',
        severity: 'serious',
        element: 'document',
        description: 'Page lacks main element',
        fix: 'Add a main element to identify the primary content'
      });
    } else if (mainElements.length > 1) {
      this.violations.push({
        rule: 'main-element',
        severity: 'serious',
        element: 'main',
        description: 'Multiple main elements found',
        fix: 'Use only one main element per page'
      });
    }
  }

  /**
   * Test keyboard navigation
   */
  private async testKeyboardNavigation(element: HTMLElement): Promise<void> {
    const interactiveElements = element.querySelectorAll(
      'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    interactiveElements.forEach((el) => {
      const htmlEl = el as HTMLElement;
      
      // Check if element is focusable
      if (htmlEl.tabIndex < 0 && !htmlEl.hasAttribute('disabled')) {
        this.warnings.push({
          rule: 'keyboard-navigation',
          element: htmlEl.tagName.toLowerCase(),
          description: 'Interactive element may not be keyboard accessible',
          suggestion: 'Ensure element is focusable with keyboard'
        });
      }

      // Check for visible focus indicators
      const computedStyle = window.getComputedStyle(htmlEl, ':focus');
      const hasOutline = computedStyle.outline !== 'none' && computedStyle.outline !== '';
      const hasBoxShadow = computedStyle.boxShadow !== 'none';
      
      if (!hasOutline && !hasBoxShadow) {
        this.violations.push({
          rule: 'focus-visible',
          severity: 'serious',
          element: htmlEl.tagName.toLowerCase(),
          description: 'Interactive element lacks visible focus indicator',
          fix: 'Add CSS focus styles (outline or box-shadow)'
        });
      }
    });

    // Check for skip links
    const skipLinks = element.querySelectorAll('a[href^="#"], button[onclick*="skip"]');
    if (skipLinks.length === 0) {
      this.warnings.push({
        rule: 'skip-links',
        element: 'document',
        description: 'No skip links found',
        suggestion: 'Add skip links for keyboard users to bypass navigation'
      });
    }
  }

  /**
   * Test ARIA attributes
   */
  private async testAriaAttributes(element: HTMLElement): Promise<void> {
    // Check for required ARIA labels
    const elementsNeedingLabels = element.querySelectorAll(
      'button:not([aria-label]):not([aria-labelledby]), input:not([aria-label]):not([aria-labelledby]):not([id])'
    );

    elementsNeedingLabels.forEach((el) => {
      const htmlEl = el as HTMLElement;
      if (!htmlEl.textContent?.trim() && htmlEl.tagName === 'BUTTON') {
        this.violations.push({
          rule: 'aria-label',
          severity: 'serious',
          element: htmlEl.tagName.toLowerCase(),
          description: 'Button lacks accessible name',
          fix: 'Add aria-label or visible text content'
        });
      }
    });

    // Check for proper ARIA roles
    const elementsWithRoles = element.querySelectorAll('[role]');
    elementsWithRoles.forEach((el) => {
      const role = el.getAttribute('role');
      const validRoles = [
        'alert', 'alertdialog', 'application', 'article', 'banner', 'button',
        'cell', 'checkbox', 'columnheader', 'combobox', 'complementary',
        'contentinfo', 'dialog', 'directory', 'document', 'feed', 'figure',
        'form', 'grid', 'gridcell', 'group', 'heading', 'img', 'link', 'list',
        'listbox', 'listitem', 'log', 'main', 'marquee', 'math', 'menu',
        'menubar', 'menuitem', 'menuitemcheckbox', 'menuitemradio', 'navigation',
        'none', 'note', 'option', 'presentation', 'progressbar', 'radio',
        'radiogroup', 'region', 'row', 'rowgroup', 'rowheader', 'scrollbar',
        'search', 'searchbox', 'separator', 'slider', 'spinbutton', 'status',
        'switch', 'tab', 'table', 'tablist', 'tabpanel', 'term', 'textbox',
        'timer', 'toolbar', 'tooltip', 'tree', 'treegrid', 'treeitem'
      ];

      if (role && !validRoles.includes(role)) {
        this.violations.push({
          rule: 'valid-aria-role',
          severity: 'moderate',
          element: el.tagName.toLowerCase(),
          description: `Invalid ARIA role: ${role}`,
          fix: 'Use a valid ARIA role or remove the role attribute'
        });
      }
    });

    // Check for ARIA live regions
    const liveRegions = element.querySelectorAll('[aria-live]');
    if (liveRegions.length === 0) {
      this.warnings.push({
        rule: 'aria-live-regions',
        element: 'document',
        description: 'No ARIA live regions found',
        suggestion: 'Consider adding live regions for dynamic content updates'
      });
    }
  }

  /**
   * Test color contrast
   */
  private async testColorContrast(element: HTMLElement): Promise<void> {
    const textElements = element.querySelectorAll('*');
    
    textElements.forEach((el) => {
      const htmlEl = el as HTMLElement;
      const style = window.getComputedStyle(htmlEl);
      const textContent = htmlEl.textContent?.trim();
      
      if (textContent && textContent.length > 0) {
        const foreground = style.color;
        const background = style.backgroundColor;
        
        if (foreground && background && background !== 'rgba(0, 0, 0, 0)') {
          const contrast = this.calculateColorContrast(foreground, background);
          
          if (contrast.level === 'FAIL') {
            this.violations.push({
              rule: 'color-contrast',
              severity: 'serious',
              element: htmlEl.tagName.toLowerCase(),
              description: `Insufficient color contrast ratio: ${contrast.ratio.toFixed(2)}:1`,
              fix: 'Increase color contrast to meet WCAG AA standards (4.5:1 for normal text)'
            });
          }
        }
      }
    });
  }

  /**
   * Test focus management
   */
  private async testFocusManagement(element: HTMLElement): Promise<void> {
    // Check for focus traps in modals/dialogs
    const dialogs = element.querySelectorAll('[role="dialog"], [role="alertdialog"], .modal');
    
    dialogs.forEach((dialog) => {
      const focusableElements = dialog.querySelectorAll(
        'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements.length === 0) {
        this.violations.push({
          rule: 'focus-trap',
          severity: 'serious',
          element: 'dialog',
          description: 'Dialog lacks focusable elements',
          fix: 'Add focusable elements or close button to dialog'
        });
      }
    });

    // Check for autofocus usage
    const autofocusElements = element.querySelectorAll('[autofocus]');
    if (autofocusElements.length > 1) {
      this.violations.push({
        rule: 'autofocus',
        severity: 'moderate',
        element: 'input',
        description: 'Multiple autofocus attributes found',
        fix: 'Use autofocus on only one element per page'
      });
    }
  }

  /**
   * Test screen reader compatibility
   */
  private async testScreenReaderCompatibility(element: HTMLElement): Promise<void> {
    // Check for screen reader only content
    const srOnlyElements = element.querySelectorAll('.sr-only, .visually-hidden');
    if (srOnlyElements.length === 0) {
      this.warnings.push({
        rule: 'screen-reader-content',
        element: 'document',
        description: 'No screen reader only content found',
        suggestion: 'Consider adding screen reader only descriptions for complex UI'
      });
    }

    // Check for empty links
    const links = element.querySelectorAll('a');
    links.forEach((link) => {
      const text = link.textContent?.trim();
      const ariaLabel = link.getAttribute('aria-label');
      const ariaLabelledby = link.getAttribute('aria-labelledby');
      
      if (!text && !ariaLabel && !ariaLabelledby) {
        this.violations.push({
          rule: 'empty-links',
          severity: 'serious',
          element: 'a',
          description: 'Link lacks accessible text',
          fix: 'Add descriptive text or aria-label to link'
        });
      }
    });
  }

  /**
   * Test form accessibility
   */
  private async testFormAccessibility(element: HTMLElement): Promise<void> {
    const formControls = element.querySelectorAll('input, select, textarea');
    
    formControls.forEach((control) => {
      const htmlControl = control as HTMLInputElement;
      const id = htmlControl.id;
      const label = element.querySelector(`label[for="${id}"]`);
      const ariaLabel = htmlControl.getAttribute('aria-label');
      const ariaLabelledby = htmlControl.getAttribute('aria-labelledby');
      
      if (!label && !ariaLabel && !ariaLabelledby) {
        this.violations.push({
          rule: 'form-labels',
          severity: 'serious',
          element: htmlControl.tagName.toLowerCase(),
          description: 'Form control lacks label',
          fix: 'Add label element or aria-label attribute'
        });
      }

      // Check for required field indicators
      if (htmlControl.required) {
        const hasRequiredIndicator = label?.textContent?.includes('*') || 
                                   ariaLabel?.includes('required') ||
                                   htmlControl.getAttribute('aria-required') === 'true';
        
        if (!hasRequiredIndicator) {
          this.warnings.push({
            rule: 'required-fields',
            element: htmlControl.tagName.toLowerCase(),
            description: 'Required field lacks clear indication',
            suggestion: 'Add visual and programmatic indication of required fields'
          });
        }
      }
    });
  }

  /**
   * Test image accessibility
   */
  private async testImageAccessibility(element: HTMLElement): Promise<void> {
    const images = element.querySelectorAll('img');
    
    images.forEach((img) => {
      const alt = img.getAttribute('alt');
      const ariaLabel = img.getAttribute('aria-label');
      const role = img.getAttribute('role');
      
      if (alt === null && !ariaLabel && role !== 'presentation') {
        this.violations.push({
          rule: 'image-alt',
          severity: 'serious',
          element: 'img',
          description: 'Image lacks alt text',
          fix: 'Add descriptive alt text or role="presentation" for decorative images'
        });
      }
      
      if (alt === '') {
        // Empty alt is acceptable for decorative images
        this.warnings.push({
          rule: 'decorative-images',
          element: 'img',
          description: 'Image marked as decorative with empty alt',
          suggestion: 'Verify this image is truly decorative'
        });
      }
    });
  }

  /**
   * Calculate color contrast ratio
   */
  private calculateColorContrast(foreground: string, background: string): ColorContrastResult {
    // This is a simplified implementation
    // In a real implementation, you would parse RGB values and calculate luminance
    const mockRatio = Math.random() * 10 + 1; // Mock ratio for demonstration
    
    let level: 'AAA' | 'AA' | 'FAIL';
    if (mockRatio >= 7) {
      level = 'AAA';
    } else if (mockRatio >= 4.5) {
      level = 'AA';
    } else {
      level = 'FAIL';
    }
    
    return {
      ratio: mockRatio,
      level,
      foreground,
      background
    };
  }

  /**
   * Calculate overall accessibility score
   */
  private calculateAccessibilityScore(): number {
    const criticalViolations = this.violations.filter(v => v.severity === 'critical').length;
    const seriousViolations = this.violations.filter(v => v.severity === 'serious').length;
    const moderateViolations = this.violations.filter(v => v.severity === 'moderate').length;
    const minorViolations = this.violations.filter(v => v.severity === 'minor').length;
    
    // Calculate score based on violation severity
    const totalDeductions = (criticalViolations * 25) + (seriousViolations * 15) + 
                           (moderateViolations * 8) + (minorViolations * 3);
    
    return Math.max(0, 100 - totalDeductions);
  }

  /**
   * Generate accessibility recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.violations.some(v => v.rule === 'heading-hierarchy')) {
      recommendations.push('Implement proper heading hierarchy starting with h1');
    }
    
    if (this.violations.some(v => v.rule === 'color-contrast')) {
      recommendations.push('Improve color contrast to meet WCAG AA standards');
    }
    
    if (this.violations.some(v => v.rule === 'focus-visible')) {
      recommendations.push('Add visible focus indicators for all interactive elements');
    }
    
    if (this.violations.some(v => v.rule === 'aria-label')) {
      recommendations.push('Add ARIA labels to elements lacking accessible names');
    }
    
    if (this.violations.some(v => v.rule === 'form-labels')) {
      recommendations.push('Associate all form controls with descriptive labels');
    }
    
    if (this.warnings.some(w => w.rule === 'skip-links')) {
      recommendations.push('Add skip links for keyboard navigation');
    }
    
    if (this.warnings.some(w => w.rule === 'aria-live-regions')) {
      recommendations.push('Implement ARIA live regions for dynamic content');
    }
    
    return recommendations;
  }
}

/**
 * Quick accessibility check for development
 */
export async function quickAccessibilityCheck(element?: HTMLElement): Promise<{
  score: number;
  criticalIssues: number;
  recommendations: string[];
}> {
  const tester = new AccessibilityTester();
  const result = await tester.runAccessibilityTests(element);
  
  return {
    score: result.score,
    criticalIssues: result.violations.filter(v => v.severity === 'critical' || v.severity === 'serious').length,
    recommendations: result.recommendations.slice(0, 3) // Top 3 recommendations
  };
}

/**
 * Accessibility testing for Skill Gap Analyzer components
 */
export async function testSkillGapAnalyzerAccessibility(): Promise<AccessibilityTestResult> {
  const skillGapAnalyzer = document.querySelector('[data-testid="skill-gap-analyzer"]') as HTMLElement;
  
  if (!skillGapAnalyzer) {
    throw new Error('Skill Gap Analyzer component not found');
  }
  
  const tester = new AccessibilityTester();
  return await tester.runAccessibilityTests(skillGapAnalyzer);
}
