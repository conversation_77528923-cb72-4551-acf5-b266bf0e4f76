import { render, screen } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import '@testing-library/jest-dom';

// Simple test to check if React components can render
describe('Simple Navigation Test', () => {
  it('should render a simple div', () => {
    render(<div data-testid="test-div">Hello World</div>);
    expect(screen.getByTestId('test-div')).toBeInTheDocument();
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });

  it('should verify window.matchMedia is mocked', () => {
    expect(window.matchMedia).toBeDefined();
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    expect(mediaQuery).toBeDefined();
    expect(mediaQuery.matches).toBeDefined();
    expect(mediaQuery.addEventListener).toBeDefined();
  });

  it('should verify localStorage is mocked', () => {
    expect(global.localStorage).toBeDefined();
    expect(global.localStorage.getItem).toBeDefined();
    expect(global.localStorage.setItem).toBeDefined();
  });

  it('should render with SessionProvider', () => {
    const mockSession = {
      user: { id: '1', email: '<EMAIL>', name: 'Test User' },
      expires: '2024-12-31',
    };

    render(
      <SessionProvider session={mockSession}>
        <div data-testid="session-test">Session Test</div>
      </SessionProvider>
    );

    expect(screen.getByTestId('session-test')).toBeInTheDocument();
  });
});

// Test NavigationBar component separately
describe('NavigationBar Component Test', () => {
  it('should import NavigationBar without errors', async () => {
    // Dynamic import to catch any import errors
    try {
      const { NavigationBar } = await import('@/components/layout/NavigationBar');
      expect(NavigationBar).toBeDefined();
    } catch (error) {
      console.error('NavigationBar import error:', error);
      throw error;
    }
  });

  it('should render NavigationBar with session', async () => {
    const { NavigationBar } = await import('@/components/layout/NavigationBar');
    const mockSession = {
      user: { id: '1', email: '<EMAIL>', name: 'Test User' },
      expires: '2024-12-31',
    };

    try {
      render(
        <SessionProvider session={mockSession}>
          <NavigationBar />
        </SessionProvider>
      );

      // Just check that it renders without throwing
      expect(document.body).toBeInTheDocument();
    } catch (error) {
      console.error('NavigationBar render error:', error);
      throw error;
    }
  });
});
