/**
 * Resume Builder API Tests
 * 
 * Tests for the resume builder API endpoints including CRUD operations,
 * authentication, validation, and error handling.
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { GET, POST } from '@/app/api/resume-builder/route';
import { GET as GetResume, PUT, DELETE } from '@/app/api/resume-builder/[id]/route';
import prisma from '@/lib/prisma';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/lib/prisma', () => ({
  user: {
    findUnique: jest.fn(),
  },
  resume: {
    findMany: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
}));

jest.mock('@/lib/errorReporting');
jest.mock('@/lib/logger');
jest.mock('@/lib/errorTracking');
jest.mock('@/lib/csrf');
jest.mock('@/lib/rateLimit');

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('Resume Builder API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/resume-builder', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/resume-builder');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Not authenticated');
    });

    it('should return user resumes when authenticated', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };
      const mockUser = { id: 'user-1' };
      const mockResumes = [
        {
          id: 'resume-1',
          title: 'Software Engineer Resume',
          template: 'modern',
          isPublic: false,
          lastExported: null,
          exportCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ];

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.resume.findMany.mockResolvedValue(mockResumes);

      const request = new NextRequest('http://localhost:3000/api/resume-builder');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockResumes);
      expect(mockPrisma.resume.findMany).toHaveBeenCalledWith({
        where: { 
          userId: 'user-1',
          isActive: true
        },
        select: {
          id: true,
          title: true,
          template: true,
          isPublic: true,
          lastExported: true,
          exportCount: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { updatedAt: 'desc' }
      });
    });

    it('should return 404 when user not found', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/resume-builder');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });
  });

  describe('POST /api/resume-builder', () => {
    it('should create a new resume when valid data is provided', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };
      const mockUser = { id: 'user-1' };
      const mockResumeData = {
        title: 'My Resume',
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>'
        },
        summary: 'Experienced developer',
        experience: [],
        education: [],
        skills: [],
        template: 'modern',
        isPublic: false
      };
      const mockCreatedResume = {
        id: 'resume-1',
        userId: 'user-1',
        ...mockResumeData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.resume.create.mockResolvedValue(mockCreatedResume);

      const request = new NextRequest('http://localhost:3000/api/resume-builder', {
        method: 'POST',
        body: JSON.stringify(mockResumeData),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockCreatedResume);
    });

    it('should return 400 when validation fails', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };
      const mockUser = { id: 'user-1' };
      const invalidResumeData = {
        // Missing required title
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: 'invalid-email' // Invalid email format
        }
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);

      const request = new NextRequest('http://localhost:3000/api/resume-builder', {
        method: 'POST',
        body: JSON.stringify(invalidResumeData),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Validation failed');
      expect(data.details).toBeDefined();
    });
  });

  describe('GET /api/resume-builder/[id]', () => {
    it('should return specific resume when user owns it', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };
      const mockUser = { id: 'user-1' };
      const mockResume = {
        id: 'resume-1',
        userId: 'user-1',
        title: 'My Resume',
        personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
        template: 'modern',
        isActive: true
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.resume.findFirst.mockResolvedValue(mockResume);

      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1');
      const response = await GetResume(request, { params: Promise.resolve({ id: 'resume-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockResume);
    });

    it('should return 404 when resume not found or not owned by user', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };
      const mockUser = { id: 'user-1' };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.resume.findFirst.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1');
      const response = await GetResume(request, { params: Promise.resolve({ id: 'resume-1' }) });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Resume not found');
    });
  });

  describe('PUT /api/resume-builder/[id]', () => {
    it('should update resume when user owns it', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };
      const mockUser = { id: 'user-1' };
      const mockExistingResume = {
        id: 'resume-1',
        userId: 'user-1',
        title: 'Old Title',
        isActive: true
      };
      const updateData = {
        title: 'Updated Title',
        summary: 'Updated summary'
      };
      const mockUpdatedResume = {
        ...mockExistingResume,
        ...updateData,
        updatedAt: new Date()
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);
      mockPrisma.resume.update.mockResolvedValue(mockUpdatedResume);

      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await PUT(request, { params: Promise.resolve({ id: 'resume-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockUpdatedResume);
    });
  });

  describe('DELETE /api/resume-builder/[id]', () => {
    it('should soft delete resume when user owns it', async () => {
      const mockSession = {
        user: { email: '<EMAIL>' }
      };
      const mockUser = { id: 'user-1' };
      const mockExistingResume = {
        id: 'resume-1',
        userId: 'user-1',
        title: 'My Resume',
        isActive: true
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);
      mockPrisma.resume.update.mockResolvedValue({ ...mockExistingResume, isActive: false });

      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
        method: 'DELETE'
      });

      const response = await DELETE(request, { params: Promise.resolve({ id: 'resume-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Resume deleted successfully');
      expect(mockPrisma.resume.update).toHaveBeenCalledWith({
        where: { id: 'resume-1' },
        data: { 
          isActive: false,
          updatedAt: expect.any(Date)
        }
      });
    });
  });
});
