<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="19" failures="1" errors="0" time="0.908">
  <testsuite name="LoginForm" errors="0" failures="1" skipped="18" timestamp="2025-07-05T11:38:09" time="0.517" tests="19">
    <testcase classname="LoginForm should render login form correctly" name="LoginForm should render login form correctly" time="0.065">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;getHeaders&apos;)
    at LoginForm (/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/LoginForm.tsx:23:21)
    at renderWithHooks (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/faafo/faafo/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/faafo/faafo/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/faafo/faafo/node_modules/react/cjs/react.development.js:2582:11)
    at /Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/@testing-library/react/dist/act-compat.js:47:25
    at renderRoot (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/@testing-library/react/dist/pure.js:190:26)
    at render (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/@testing-library/react/dist/pure.js:292:10)
    at Object.&lt;anonymous&gt; (/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/login-form.test.tsx:82:11)
    at Promise.then.completed (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/faafo/faafo/faafo-career-platform/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LoginForm should handle successful login" name="LoginForm should handle successful login" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should handle login error" name="LoginForm should handle login error" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should handle email verification error and shows resend option" name="LoginForm should handle email verification error and shows resend option" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should handle resend verification email successfully" name="LoginForm should handle resend verification email successfully" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should handle resend verification email error" name="LoginForm should handle resend verification email error" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should show loading state during resend verification" name="LoginForm should show loading state during resend verification" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should clear error when starting new login attempt" name="LoginForm should clear error when starting new login attempt" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should require email and password fields" name="LoginForm should require email and password fields" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm should have correct input types" name="LoginForm should have correct input types" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm handles network error during resend verification" name="LoginForm handles network error during resend verification" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Security and Edge Cases should prevent XSS in error messages" name="LoginForm Security and Edge Cases should prevent XSS in error messages" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Security and Edge Cases should handle extremely long email addresses" name="LoginForm Security and Edge Cases should handle extremely long email addresses" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Security and Edge Cases should handle special characters in credentials" name="LoginForm Security and Edge Cases should handle special characters in credentials" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Security and Edge Cases should handle rapid form submissions" name="LoginForm Security and Edge Cases should handle rapid form submissions" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Security and Edge Cases should maintain form state during loading" name="LoginForm Security and Edge Cases should maintain form state during loading" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Accessibility should have proper ARIA labels and roles" name="LoginForm Accessibility should have proper ARIA labels and roles" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Accessibility should announce errors to screen readers" name="LoginForm Accessibility should announce errors to screen readers" time="0">
      <skipped/>
    </testcase>
    <testcase classname="LoginForm Accessibility should support keyboard navigation" name="LoginForm Accessibility should support keyboard navigation" time="0">
      <skipped/>
    </testcase>
  </testsuite>
</testsuites>