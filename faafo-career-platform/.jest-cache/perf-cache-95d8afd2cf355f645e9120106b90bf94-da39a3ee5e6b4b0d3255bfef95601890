{"/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/comprehensive-ai-workflow.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/ai-service-live-testing.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/ai-service-edge-cases.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/assessment.api.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/tests/ai-service-comprehensive.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/ai-service-comprehensive.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/e2e/userFlows.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/ai-service-production-validation.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/performance-monitoring-enhancement.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/ai-service-core-live-testing.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/resume-builder-comprehensive.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/integration/email-verification-flow.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/ai-optimization.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/integration/security.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/optimization/data-flow-optimization.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/PersonalizedResources.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/integration/database.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/learningResources.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/e2e/navigation.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/integration/api.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/ai-service/security-validation.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/signup.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/ai-service/gemini-service.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/integration/resume-builder.integration.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/ai-service-basic-validation.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/api/resume-builder.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/freedom-fund/__tests__/route.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/assessment.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/analytics/analytics-api.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/auth/resend-verification.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/session-timeout-logic.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/forum-reaction-race-condition.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/integration/resourcesPage.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/auth/verify-email.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/auth.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/assessment/AIInsightsPanel.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/EdgeCaseHandler-user-flow.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api-integration.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/assessment-flow.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/skills/assessment-edge-case.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/auth-flow.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/e2e/homepage.spec.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/session-consistency.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/tools/salary-calculator/__tests__/page.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/error-tracking/skill-gap-error-tracking.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/LoginForm.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/PersonalizedLearningPathService.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/SignupForm.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/assessment/enhanced-assessment-logic.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/feature-flags/skill-gap-feature-flags.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/lib/services/geminiService-skillGap.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/migration/skill-data-migration.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security/skill-gap-security.test.js": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/data-integrity-issues.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/ai-service/integration.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security/skill-gap-security.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/resume-builder-e2e.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/accessibility-responsive.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/ui-components.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/api/ai/skills-analysis-comprehensive.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/SkillAssessmentEngine.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/comprehensive-validation.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/final-integration-verification.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/real-database.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/ai-service/monitoring.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/ai-service/redis-cache.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/security/security-fixes.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/SkillMarketDataService.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/SkillAssessmentForm.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/testing-coverage-assessment.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/api/skills/assessment.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/components/resume-builder/ResumeBuilder.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/MarketInsightsChart.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security-fixes-verification.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/resume-builder-functional.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/resume-builder-simple.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/SkillProgressTracker.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/profile-integration.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security-vulnerabilities.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/comprehensive-api.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/examples/working-test-example.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/critical-endpoints.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.business-logic.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/interview-practice/__tests__/InterviewPracticeInterface.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/profile-management.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/api/auth-simplified.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/auth-api-routes.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security/integration-security.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.system-failures.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security/business-logic-security.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/analytics/AdvancedAnalyticsDashboard.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/SkillGapRadarChart.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/assessment/EnhancedAssessmentResults.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/monitoring/monitoring-system.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/auth-middleware.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.validation.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/seo/skill-gap-seo.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/session-management.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/error-boundaries.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/hydration-fixes.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/auth-configuration.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/auth-validation-logic.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/analytics/analytics-service.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/auth-security-issues.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture-review.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security/csrf-protection.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/ai-service/basic-functionality.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/interview-practice/__tests__/InterviewConfigurationWizard.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/critical-issues.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/auth-state-manager-memory-leak.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/redirect-logic.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/integration-db.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/csrf-security.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/analytics/MetricCard.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/interview-practice-useeffect-fix.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/dev-rate-limiting.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/setup-verification.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/edge-cases/simple-validation.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/assessment-form.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/basic.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/simple-navigation.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/simple-db-test.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/FreedomFundCalculatorForm.test.tsx": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/validation.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/module-resolution.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/setup.test.ts": [1, 0], "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/login-form.test.tsx": [0, 517]}