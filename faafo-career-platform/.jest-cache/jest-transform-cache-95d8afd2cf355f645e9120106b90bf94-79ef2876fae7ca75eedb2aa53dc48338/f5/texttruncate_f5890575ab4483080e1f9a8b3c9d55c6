c19f10c49e497c3deece2be557d15a77
"use strict";
'use client';

/* istanbul ignore next */
function cov_195fsi0tqv() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/text-truncate.tsx";
  var hash = "620e78a5619f87d4045ec490630e94136685ba86";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/text-truncate.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "2": {
        start: {
          line: 4,
          column: 26
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 52
        }
      },
      "4": {
        start: {
          line: 6,
          column: 4
        },
        end: {
          line: 8,
          column: 5
        }
      },
      "5": {
        start: {
          line: 7,
          column: 6
        },
        end: {
          line: 7,
          column: 68
        }
      },
      "6": {
        start: {
          line: 7,
          column: 51
        },
        end: {
          line: 7,
          column: 63
        }
      },
      "7": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 39
        }
      },
      "8": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "9": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "10": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 17
        }
      },
      "11": {
        start: {
          line: 14,
          column: 25
        },
        end: {
          line: 18,
          column: 2
        }
      },
      "12": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 72
        }
      },
      "13": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 21
        }
      },
      "14": {
        start: {
          line: 19,
          column: 19
        },
        end: {
          line: 35,
          column: 4
        }
      },
      "15": {
        start: {
          line: 20,
          column: 18
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "16": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 25,
          column: 10
        }
      },
      "17": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 23
        }
      },
      "18": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "19": {
        start: {
          line: 23,
          column: 29
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "20": {
        start: {
          line: 23,
          column: 77
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "21": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 24,
          column: 22
        }
      },
      "22": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 26
        }
      },
      "23": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 34,
          column: 6
        }
      },
      "24": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "25": {
        start: {
          line: 29,
          column: 35
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "26": {
        start: {
          line: 30,
          column: 21
        },
        end: {
          line: 30,
          column: 23
        }
      },
      "27": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "28": {
        start: {
          line: 31,
          column: 25
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "29": {
        start: {
          line: 31,
          column: 38
        },
        end: {
          line: 31,
          column: 50
        }
      },
      "30": {
        start: {
          line: 31,
          column: 56
        },
        end: {
          line: 31,
          column: 57
        }
      },
      "31": {
        start: {
          line: 31,
          column: 78
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "32": {
        start: {
          line: 31,
          column: 102
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "33": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 40
        }
      },
      "34": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 22
        }
      },
      "35": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 62
        }
      },
      "36": {
        start: {
          line: 37,
          column: 0
        },
        end: {
          line: 37,
          column: 36
        }
      },
      "37": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 36
        }
      },
      "38": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 42
        }
      },
      "39": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 48
        }
      },
      "40": {
        start: {
          line: 41,
          column: 14
        },
        end: {
          line: 41,
          column: 44
        }
      },
      "41": {
        start: {
          line: 42,
          column: 14
        },
        end: {
          line: 42,
          column: 36
        }
      },
      "42": {
        start: {
          line: 44,
          column: 15
        },
        end: {
          line: 44,
          column: 22
        }
      },
      "43": {
        start: {
          line: 44,
          column: 29
        },
        end: {
          line: 44,
          column: 41
        }
      },
      "44": {
        start: {
          line: 44,
          column: 55
        },
        end: {
          line: 44,
          column: 79
        }
      },
      "45": {
        start: {
          line: 44,
          column: 93
        },
        end: {
          line: 44,
          column: 105
        }
      },
      "46": {
        start: {
          line: 44,
          column: 112
        },
        end: {
          line: 44,
          column: 125
        }
      },
      "47": {
        start: {
          line: 44,
          column: 140
        },
        end: {
          line: 44,
          column: 165
        }
      },
      "48": {
        start: {
          line: 44,
          column: 172
        },
        end: {
          line: 44,
          column: 185
        }
      },
      "49": {
        start: {
          line: 44,
          column: 200
        },
        end: {
          line: 44,
          column: 232
        }
      },
      "50": {
        start: {
          line: 44,
          column: 239
        },
        end: {
          line: 44,
          column: 254
        }
      },
      "51": {
        start: {
          line: 44,
          column: 271
        },
        end: {
          line: 44,
          column: 303
        }
      },
      "52": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "53": {
        start: {
          line: 45,
          column: 56
        },
        end: {
          line: 45,
          column: 61
        }
      },
      "54": {
        start: {
          line: 45,
          column: 79
        },
        end: {
          line: 45,
          column: 84
        }
      },
      "55": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "56": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 47,
          column: 88
        }
      },
      "57": {
        start: {
          line: 49,
          column: 24
        },
        end: {
          line: 49,
          column: 48
        }
      },
      "58": {
        start: {
          line: 50,
          column: 22
        },
        end: {
          line: 50,
          column: 73
        }
      },
      "59": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 348
        }
      },
      "60": {
        start: {
          line: 51,
          column: 165
        },
        end: {
          line: 51,
          column: 199
        }
      },
      "61": {
        start: {
          line: 54,
          column: 19
        },
        end: {
          line: 54,
          column: 30
        }
      },
      "62": {
        start: {
          line: 54,
          column: 44
        },
        end: {
          line: 54,
          column: 56
        }
      },
      "63": {
        start: {
          line: 54,
          column: 63
        },
        end: {
          line: 54,
          column: 74
        }
      },
      "64": {
        start: {
          line: 54,
          column: 87
        },
        end: {
          line: 54,
          column: 109
        }
      },
      "65": {
        start: {
          line: 55,
          column: 25
        },
        end: {
          line: 61,
          column: 48
        }
      },
      "66": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 155
        }
      },
      "67": {
        start: {
          line: 65,
          column: 15
        },
        end: {
          line: 65,
          column: 22
        }
      },
      "68": {
        start: {
          line: 65,
          column: 29
        },
        end: {
          line: 65,
          column: 41
        }
      },
      "69": {
        start: {
          line: 65,
          column: 55
        },
        end: {
          line: 65,
          column: 79
        }
      },
      "70": {
        start: {
          line: 65,
          column: 86
        },
        end: {
          line: 65,
          column: 97
        }
      },
      "71": {
        start: {
          line: 65,
          column: 110
        },
        end: {
          line: 65,
          column: 132
        }
      },
      "72": {
        start: {
          line: 65,
          column: 146
        },
        end: {
          line: 65,
          column: 158
        }
      },
      "73": {
        start: {
          line: 65,
          column: 165
        },
        end: {
          line: 65,
          column: 182
        }
      },
      "74": {
        start: {
          line: 65,
          column: 201
        },
        end: {
          line: 65,
          column: 226
        }
      },
      "75": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 67,
          column: 104
        }
      },
      "76": {
        start: {
          line: 68,
          column: 22
        },
        end: {
          line: 70,
          column: 23
        }
      },
      "77": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 73,
          column: 5
        }
      },
      "78": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 123
        }
      },
      "79": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 74,
          column: 119
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 3,
            column: 75
          }
        },
        loc: {
          start: {
            line: 3,
            column: 96
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 38
          },
          end: {
            line: 7,
            column: 39
          }
        },
        loc: {
          start: {
            line: 7,
            column: 49
          },
          end: {
            line: 7,
            column: 65
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 10,
            column: 7
          }
        },
        loc: {
          start: {
            line: 10,
            column: 28
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 10
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 14,
            column: 81
          }
        },
        loc: {
          start: {
            line: 14,
            column: 95
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 16,
            column: 6
          }
        },
        loc: {
          start: {
            line: 16,
            column: 20
          },
          end: {
            line: 18,
            column: 1
          }
        },
        line: 16
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 52
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 19
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 19
          }
        },
        loc: {
          start: {
            line: 20,
            column: 30
          },
          end: {
            line: 27,
            column: 5
          }
        },
        line: 20
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 21,
            column: 49
          }
        },
        loc: {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 25,
            column: 9
          }
        },
        line: 21
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 28,
            column: 11
          },
          end: {
            line: 28,
            column: 12
          }
        },
        loc: {
          start: {
            line: 28,
            column: 26
          },
          end: {
            line: 34,
            column: 5
          }
        },
        line: 28
      },
      "9": {
        name: "TextTruncate",
        decl: {
          start: {
            line: 43,
            column: 9
          },
          end: {
            line: 43,
            column: 21
          }
        },
        loc: {
          start: {
            line: 43,
            column: 26
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 43
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 51,
            column: 151
          },
          end: {
            line: 51,
            column: 152
          }
        },
        loc: {
          start: {
            line: 51,
            column: 163
          },
          end: {
            line: 51,
            column: 201
          }
        },
        line: 51
      },
      "11": {
        name: "TextOverflow",
        decl: {
          start: {
            line: 53,
            column: 9
          },
          end: {
            line: 53,
            column: 21
          }
        },
        loc: {
          start: {
            line: 53,
            column: 26
          },
          end: {
            line: 63,
            column: 1
          }
        },
        line: 53
      },
      "12": {
        name: "SafeTextDisplay",
        decl: {
          start: {
            line: 64,
            column: 9
          },
          end: {
            line: 64,
            column: 24
          }
        },
        loc: {
          start: {
            line: 64,
            column: 29
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 64
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 13,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 27
          }
        }, {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 3,
            column: 51
          }
        }, {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 10,
            column: 1
          }
        }, {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 13,
            column: 1
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "4": {
        loc: {
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 13
          }
        }, {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "5": {
        loc: {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 6,
            column: 34
          },
          end: {
            line: 6,
            column: 47
          }
        }, {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "6": {
        loc: {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 63
          }
        }, {
          start: {
            line: 6,
            column: 67
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "7": {
        loc: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "8": {
        loc: {
          start: {
            line: 14,
            column: 25
          },
          end: {
            line: 18,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 30
          }
        }, {
          start: {
            line: 14,
            column: 34
          },
          end: {
            line: 14,
            column: 57
          }
        }, {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "9": {
        loc: {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 16,
            column: 1
          }
        }, {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "10": {
        loc: {
          start: {
            line: 19,
            column: 19
          },
          end: {
            line: 35,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 20
          },
          end: {
            line: 19,
            column: 24
          }
        }, {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 19,
            column: 45
          }
        }, {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 35,
            column: 4
          }
        }],
        line: 19
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 25,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 21,
            column: 44
          }
        }, {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 25,
            column: 9
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "13": {
        loc: {
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "14": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 15
          }
        }, {
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 29,
            column: 33
          }
        }],
        line: 29
      },
      "15": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "16": {
        loc: {
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "17": {
        loc: {
          start: {
            line: 44,
            column: 55
          },
          end: {
            line: 44,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 71
          },
          end: {
            line: 44,
            column: 74
          }
        }, {
          start: {
            line: 44,
            column: 77
          },
          end: {
            line: 44,
            column: 79
          }
        }],
        line: 44
      },
      "18": {
        loc: {
          start: {
            line: 44,
            column: 140
          },
          end: {
            line: 44,
            column: 165
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 156
          },
          end: {
            line: 44,
            column: 160
          }
        }, {
          start: {
            line: 44,
            column: 163
          },
          end: {
            line: 44,
            column: 165
          }
        }],
        line: 44
      },
      "19": {
        loc: {
          start: {
            line: 44,
            column: 200
          },
          end: {
            line: 44,
            column: 232
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 216
          },
          end: {
            line: 44,
            column: 227
          }
        }, {
          start: {
            line: 44,
            column: 230
          },
          end: {
            line: 44,
            column: 232
          }
        }],
        line: 44
      },
      "20": {
        loc: {
          start: {
            line: 44,
            column: 271
          },
          end: {
            line: 44,
            column: 303
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 287
          },
          end: {
            line: 44,
            column: 298
          }
        }, {
          start: {
            line: 44,
            column: 301
          },
          end: {
            line: 44,
            column: 303
          }
        }],
        line: 44
      },
      "21": {
        loc: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "22": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 13
          }
        }, {
          start: {
            line: 46,
            column: 17
          },
          end: {
            line: 46,
            column: 41
          }
        }],
        line: 46
      },
      "23": {
        loc: {
          start: {
            line: 50,
            column: 22
          },
          end: {
            line: 50,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 35
          },
          end: {
            line: 50,
            column: 39
          }
        }, {
          start: {
            line: 50,
            column: 42
          },
          end: {
            line: 50,
            column: 73
          }
        }],
        line: 50
      },
      "24": {
        loc: {
          start: {
            line: 51,
            column: 92
          },
          end: {
            line: 51,
            column: 342
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 92
          },
          end: {
            line: 51,
            column: 102
          }
        }, {
          start: {
            line: 51,
            column: 107
          },
          end: {
            line: 51,
            column: 341
          }
        }],
        line: 51
      },
      "25": {
        loc: {
          start: {
            line: 51,
            column: 300
          },
          end: {
            line: 51,
            column: 338
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 51,
            column: 313
          },
          end: {
            line: 51,
            column: 325
          }
        }, {
          start: {
            line: 51,
            column: 328
          },
          end: {
            line: 51,
            column: 338
          }
        }],
        line: 51
      },
      "26": {
        loc: {
          start: {
            line: 54,
            column: 87
          },
          end: {
            line: 54,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 54,
            column: 103
          },
          end: {
            line: 54,
            column: 104
          }
        }, {
          start: {
            line: 54,
            column: 107
          },
          end: {
            line: 54,
            column: 109
          }
        }],
        line: 54
      },
      "27": {
        loc: {
          start: {
            line: 55,
            column: 25
          },
          end: {
            line: 61,
            column: 48
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 10
          },
          end: {
            line: 56,
            column: 24
          }
        }, {
          start: {
            line: 57,
            column: 10
          },
          end: {
            line: 61,
            column: 48
          }
        }],
        line: 55
      },
      "28": {
        loc: {
          start: {
            line: 57,
            column: 10
          },
          end: {
            line: 61,
            column: 48
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 58,
            column: 14
          },
          end: {
            line: 58,
            column: 28
          }
        }, {
          start: {
            line: 59,
            column: 14
          },
          end: {
            line: 61,
            column: 48
          }
        }],
        line: 57
      },
      "29": {
        loc: {
          start: {
            line: 59,
            column: 14
          },
          end: {
            line: 61,
            column: 48
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 60,
            column: 18
          },
          end: {
            line: 60,
            column: 32
          }
        }, {
          start: {
            line: 61,
            column: 18
          },
          end: {
            line: 61,
            column: 48
          }
        }],
        line: 59
      },
      "30": {
        loc: {
          start: {
            line: 65,
            column: 55
          },
          end: {
            line: 65,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 65,
            column: 71
          },
          end: {
            line: 65,
            column: 74
          }
        }, {
          start: {
            line: 65,
            column: 77
          },
          end: {
            line: 65,
            column: 79
          }
        }],
        line: 65
      },
      "31": {
        loc: {
          start: {
            line: 65,
            column: 110
          },
          end: {
            line: 65,
            column: 132
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 65,
            column: 126
          },
          end: {
            line: 65,
            column: 127
          }
        }, {
          start: {
            line: 65,
            column: 130
          },
          end: {
            line: 65,
            column: 132
          }
        }],
        line: 65
      },
      "32": {
        loc: {
          start: {
            line: 65,
            column: 201
          },
          end: {
            line: 65,
            column: 226
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 65,
            column: 217
          },
          end: {
            line: 65,
            column: 221
          }
        }, {
          start: {
            line: 65,
            column: 224
          },
          end: {
            line: 65,
            column: 226
          }
        }],
        line: 65
      },
      "33": {
        loc: {
          start: {
            line: 67,
            column: 24
          },
          end: {
            line: 67,
            column: 104
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 97
          }
        }, {
          start: {
            line: 67,
            column: 102
          },
          end: {
            line: 67,
            column: 104
          }
        }],
        line: 67
      },
      "34": {
        loc: {
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 60
          },
          end: {
            line: 67,
            column: 66
          }
        }, {
          start: {
            line: 67,
            column: 69
          },
          end: {
            line: 67,
            column: 97
          }
        }],
        line: 67
      },
      "35": {
        loc: {
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 38
          }
        }, {
          start: {
            line: 67,
            column: 42
          },
          end: {
            line: 67,
            column: 57
          }
        }],
        line: 67
      },
      "36": {
        loc: {
          start: {
            line: 68,
            column: 22
          },
          end: {
            line: 70,
            column: 23
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 10
          },
          end: {
            line: 69,
            column: 43
          }
        }, {
          start: {
            line: 70,
            column: 10
          },
          end: {
            line: 70,
            column: 23
          }
        }],
        line: 68
      },
      "37": {
        loc: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "38": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 22
          }
        }, {
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 71,
            column: 58
          }
        }],
        line: 71
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/text-truncate.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcb,oCA+BC;AAQD,oCAcC;AAUD,0CA4BC;;AAvGD,6CAAwC;AACxC,qCAAiC;AAWjC,SAAgB,YAAY,CAAC,EAOT;QANlB,IAAI,UAAA,EACJ,iBAAe,EAAf,SAAS,mBAAG,GAAG,KAAA,EACf,SAAS,eAAA,EACT,kBAAiB,EAAjB,UAAU,mBAAG,IAAI,KAAA,EACjB,kBAAwB,EAAxB,UAAU,mBAAG,WAAW,KAAA,EACxB,oBAA0B,EAA1B,YAAY,mBAAG,WAAW,KAAA;IAEpB,IAAA,KAA8B,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAA5C,UAAU,QAAA,EAAE,aAAa,QAAmB,CAAC;IAEpD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QACtC,OAAO,iCAAM,SAAS,EAAE,SAAS,YAAG,IAAI,GAAQ,CAAC;IACnD,CAAC;IAED,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IAC/C,IAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAG,aAAa,QAAK,CAAC;IAE9D,OAAO,CACL,kCAAM,SAAS,EAAE,SAAS,aACvB,WAAW,EACX,UAAU,IAAI,CACb,mCACE,OAAO,EAAE,cAAM,OAAA,aAAa,CAAC,CAAC,UAAU,CAAC,EAA1B,CAA0B,EACzC,SAAS,EAAC,0DAA0D,EACpE,IAAI,EAAC,QAAQ,YAEZ,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,GAChC,CACV,IACI,CACR,CAAC;AACJ,CAAC;AAQD,SAAgB,YAAY,CAAC,EAAwD;QAAtD,QAAQ,cAAA,EAAE,SAAS,eAAA,EAAE,gBAAY,EAAZ,QAAQ,mBAAG,CAAC,KAAA;IAC9D,IAAM,cAAc,GAAG,QAAQ,KAAK,CAAC;QACnC,CAAC,CAAC,cAAc;QAChB,CAAC,CAAC,QAAQ,KAAK,CAAC;YAChB,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,QAAQ,KAAK,CAAC;gBAChB,CAAC,CAAC,cAAc;gBAChB,CAAC,CAAC,qBAAc,QAAQ,CAAE,CAAC;IAE7B,OAAO,CACL,gCAAK,SAAS,EAAE,IAAA,UAAE,EAAC,+BAA+B,EAAE,cAAc,EAAE,SAAS,CAAC,YAC3E,QAAQ,GACL,CACP,CAAC;AACJ,CAAC;AAUD,SAAgB,eAAe,CAAC,EAMT;QALrB,IAAI,UAAA,EACJ,iBAAe,EAAf,SAAS,mBAAG,GAAG,KAAA,EACf,gBAAY,EAAZ,QAAQ,mBAAG,CAAC,KAAA,EACZ,SAAS,eAAA,EACT,sBAAqB,EAArB,cAAc,mBAAG,IAAI,KAAA;IAErB,iCAAiC;IACjC,IAAM,aAAa,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,KAAI,EAAE,CAAC;IAC1D,IAAM,WAAW,GAAG,aAAa,CAAC,MAAM,GAAG,SAAS;QAClD,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;QACnC,CAAC,CAAC,aAAa,CAAC;IAElB,IAAI,cAAc,IAAI,aAAa,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;QACvD,OAAO,CACL,uBAAC,YAAY,IACX,IAAI,EAAE,aAAa,EACnB,SAAS,EAAE,SAAS,EACpB,SAAS,EAAE,SAAS,GACpB,CACH,CAAC;IACJ,CAAC;IAED,OAAO,CACL,uBAAC,YAAY,IAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,YACnD,WAAW,GACC,CAChB,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/text-truncate.tsx"],
      sourcesContent: ["'use client';\n\nimport React, { useState } from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface TextTruncateProps {\n  text: string;\n  maxLength?: number;\n  className?: string;\n  showToggle?: boolean;\n  expandText?: string;\n  collapseText?: string;\n}\n\nexport function TextTruncate({\n  text,\n  maxLength = 100,\n  className,\n  showToggle = true,\n  expandText = 'Show more',\n  collapseText = 'Show less'\n}: TextTruncateProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  if (!text || text.length <= maxLength) {\n    return <span className={className}>{text}</span>;\n  }\n\n  const truncatedText = text.slice(0, maxLength);\n  const displayText = isExpanded ? text : `${truncatedText}...`;\n\n  return (\n    <span className={className}>\n      {displayText}\n      {showToggle && (\n        <button\n          onClick={() => setIsExpanded(!isExpanded)}\n          className=\"ml-1 text-blue-600 hover:text-blue-800 underline text-sm\"\n          type=\"button\"\n        >\n          {isExpanded ? collapseText : expandText}\n        </button>\n      )}\n    </span>\n  );\n}\n\ninterface TextOverflowProps {\n  children: React.ReactNode;\n  className?: string;\n  maxLines?: number;\n}\n\nexport function TextOverflow({ children, className, maxLines = 1 }: TextOverflowProps) {\n  const lineClampClass = maxLines === 1 \n    ? 'line-clamp-1' \n    : maxLines === 2 \n    ? 'line-clamp-2' \n    : maxLines === 3 \n    ? 'line-clamp-3' \n    : `line-clamp-${maxLines}`;\n\n  return (\n    <div className={cn('overflow-hidden text-ellipsis', lineClampClass, className)}>\n      {children}\n    </div>\n  );\n}\n\ninterface SafeTextDisplayProps {\n  text: string;\n  maxLength?: number;\n  maxLines?: number;\n  className?: string;\n  allowExpansion?: boolean;\n}\n\nexport function SafeTextDisplay({\n  text,\n  maxLength = 200,\n  maxLines = 3,\n  className,\n  allowExpansion = true\n}: SafeTextDisplayProps) {\n  // Sanitize and limit text length\n  const sanitizedText = text?.replace(/<[^>]*>/g, '') || '';\n  const limitedText = sanitizedText.length > maxLength \n    ? sanitizedText.slice(0, maxLength) \n    : sanitizedText;\n\n  if (allowExpansion && sanitizedText.length > maxLength) {\n    return (\n      <TextTruncate\n        text={sanitizedText}\n        maxLength={maxLength}\n        className={className}\n      />\n    );\n  }\n\n  return (\n    <TextOverflow className={className} maxLines={maxLines}>\n      {limitedText}\n    </TextOverflow>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "620e78a5619f87d4045ec490630e94136685ba86"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_195fsi0tqv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_195fsi0tqv();
var __createBinding =
/* istanbul ignore next */
(cov_195fsi0tqv().s[0]++,
/* istanbul ignore next */
(cov_195fsi0tqv().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_195fsi0tqv().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_195fsi0tqv().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_195fsi0tqv().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[0]++;
  cov_195fsi0tqv().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_195fsi0tqv().b[2][0]++;
    cov_195fsi0tqv().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_195fsi0tqv().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_195fsi0tqv().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_195fsi0tqv().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[5][1]++,
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_195fsi0tqv().b[3][0]++;
    cov_195fsi0tqv().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_195fsi0tqv().f[1]++;
        cov_195fsi0tqv().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_195fsi0tqv().b[3][1]++;
  }
  cov_195fsi0tqv().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_195fsi0tqv().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[2]++;
  cov_195fsi0tqv().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_195fsi0tqv().b[7][0]++;
    cov_195fsi0tqv().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_195fsi0tqv().b[7][1]++;
  }
  cov_195fsi0tqv().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_195fsi0tqv().s[11]++,
/* istanbul ignore next */
(cov_195fsi0tqv().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_195fsi0tqv().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_195fsi0tqv().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_195fsi0tqv().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[3]++;
  cov_195fsi0tqv().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_195fsi0tqv().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[4]++;
  cov_195fsi0tqv().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_195fsi0tqv().s[14]++,
/* istanbul ignore next */
(cov_195fsi0tqv().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_195fsi0tqv().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_195fsi0tqv().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[5]++;
  cov_195fsi0tqv().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_195fsi0tqv().f[6]++;
    cov_195fsi0tqv().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_195fsi0tqv().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_195fsi0tqv().s[17]++, []);
      /* istanbul ignore next */
      cov_195fsi0tqv().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_195fsi0tqv().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_195fsi0tqv().b[12][0]++;
          cov_195fsi0tqv().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_195fsi0tqv().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_195fsi0tqv().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_195fsi0tqv().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_195fsi0tqv().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_195fsi0tqv().f[8]++;
    cov_195fsi0tqv().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_195fsi0tqv().b[13][0]++;
      cov_195fsi0tqv().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_195fsi0tqv().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[26]++, {});
    /* istanbul ignore next */
    cov_195fsi0tqv().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_195fsi0tqv().b[15][0]++;
      cov_195fsi0tqv().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_195fsi0tqv().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_195fsi0tqv().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_195fsi0tqv().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_195fsi0tqv().b[16][0]++;
          cov_195fsi0tqv().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_195fsi0tqv().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_195fsi0tqv().b[15][1]++;
    }
    cov_195fsi0tqv().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_195fsi0tqv().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_195fsi0tqv().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_195fsi0tqv().s[36]++;
exports.TextTruncate = TextTruncate;
/* istanbul ignore next */
cov_195fsi0tqv().s[37]++;
exports.TextOverflow = TextOverflow;
/* istanbul ignore next */
cov_195fsi0tqv().s[38]++;
exports.SafeTextDisplay = SafeTextDisplay;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_195fsi0tqv().s[39]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_195fsi0tqv().s[40]++, __importStar(require("react")));
var utils_1 =
/* istanbul ignore next */
(cov_195fsi0tqv().s[41]++, require("@/lib/utils"));
function TextTruncate(_a) {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[9]++;
  var text =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[42]++, _a.text),
    _b =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[43]++, _a.maxLength),
    maxLength =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[44]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[17][0]++, 100) :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[17][1]++, _b)),
    className =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[45]++, _a.className),
    _c =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[46]++, _a.showToggle),
    showToggle =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[47]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[18][0]++, true) :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[18][1]++, _c)),
    _d =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[48]++, _a.expandText),
    expandText =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[49]++, _d === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[19][0]++, 'Show more') :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[19][1]++, _d)),
    _e =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[50]++, _a.collapseText),
    collapseText =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[51]++, _e === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[20][0]++, 'Show less') :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[20][1]++, _e));
  var _f =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[52]++, (0, react_1.useState)(false)),
    isExpanded =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[53]++, _f[0]),
    setIsExpanded =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[54]++, _f[1]);
  /* istanbul ignore next */
  cov_195fsi0tqv().s[55]++;
  if (
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[22][0]++, !text) ||
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[22][1]++, text.length <= maxLength)) {
    /* istanbul ignore next */
    cov_195fsi0tqv().b[21][0]++;
    cov_195fsi0tqv().s[56]++;
    return (0, jsx_runtime_1.jsx)("span", {
      className: className,
      children: text
    });
  } else
  /* istanbul ignore next */
  {
    cov_195fsi0tqv().b[21][1]++;
  }
  var truncatedText =
  /* istanbul ignore next */
  (cov_195fsi0tqv().s[57]++, text.slice(0, maxLength));
  var displayText =
  /* istanbul ignore next */
  (cov_195fsi0tqv().s[58]++, isExpanded ?
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[23][0]++, text) :
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[23][1]++, "".concat(truncatedText, "...")));
  /* istanbul ignore next */
  cov_195fsi0tqv().s[59]++;
  return (0, jsx_runtime_1.jsxs)("span", {
    className: className,
    children: [displayText,
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[24][0]++, showToggle) &&
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[24][1]++, (0, jsx_runtime_1.jsx)("button", {
      onClick: function () {
        /* istanbul ignore next */
        cov_195fsi0tqv().f[10]++;
        cov_195fsi0tqv().s[60]++;
        return setIsExpanded(!isExpanded);
      },
      className: "ml-1 text-blue-600 hover:text-blue-800 underline text-sm",
      type: "button",
      children: isExpanded ?
      /* istanbul ignore next */
      (cov_195fsi0tqv().b[25][0]++, collapseText) :
      /* istanbul ignore next */
      (cov_195fsi0tqv().b[25][1]++, expandText)
    }))]
  });
}
function TextOverflow(_a) {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[11]++;
  var children =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[61]++, _a.children),
    className =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[62]++, _a.className),
    _b =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[63]++, _a.maxLines),
    maxLines =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[64]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[26][0]++, 1) :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[26][1]++, _b));
  var lineClampClass =
  /* istanbul ignore next */
  (cov_195fsi0tqv().s[65]++, maxLines === 1 ?
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[27][0]++, 'line-clamp-1') :
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[27][1]++, maxLines === 2 ?
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[28][0]++, 'line-clamp-2') :
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[28][1]++, maxLines === 3 ?
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[29][0]++, 'line-clamp-3') :
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[29][1]++, "line-clamp-".concat(maxLines)))));
  /* istanbul ignore next */
  cov_195fsi0tqv().s[66]++;
  return (0, jsx_runtime_1.jsx)("div", {
    className: (0, utils_1.cn)('overflow-hidden text-ellipsis', lineClampClass, className),
    children: children
  });
}
function SafeTextDisplay(_a) {
  /* istanbul ignore next */
  cov_195fsi0tqv().f[12]++;
  var text =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[67]++, _a.text),
    _b =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[68]++, _a.maxLength),
    maxLength =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[69]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[30][0]++, 200) :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[30][1]++, _b)),
    _c =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[70]++, _a.maxLines),
    maxLines =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[71]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[31][0]++, 3) :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[31][1]++, _c)),
    className =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[72]++, _a.className),
    _d =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[73]++, _a.allowExpansion),
    allowExpansion =
    /* istanbul ignore next */
    (cov_195fsi0tqv().s[74]++, _d === void 0 ?
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[32][0]++, true) :
    /* istanbul ignore next */
    (cov_195fsi0tqv().b[32][1]++, _d));
  // Sanitize and limit text length
  var sanitizedText =
  /* istanbul ignore next */
  (cov_195fsi0tqv().s[75]++,
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[33][0]++,
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[35][0]++, text === null) ||
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[35][1]++, text === void 0) ?
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[34][0]++, void 0) :
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[34][1]++, text.replace(/<[^>]*>/g, ''))) ||
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[33][1]++, ''));
  var limitedText =
  /* istanbul ignore next */
  (cov_195fsi0tqv().s[76]++, sanitizedText.length > maxLength ?
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[36][0]++, sanitizedText.slice(0, maxLength)) :
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[36][1]++, sanitizedText));
  /* istanbul ignore next */
  cov_195fsi0tqv().s[77]++;
  if (
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[38][0]++, allowExpansion) &&
  /* istanbul ignore next */
  (cov_195fsi0tqv().b[38][1]++, sanitizedText.length > maxLength)) {
    /* istanbul ignore next */
    cov_195fsi0tqv().b[37][0]++;
    cov_195fsi0tqv().s[78]++;
    return (0, jsx_runtime_1.jsx)(TextTruncate, {
      text: sanitizedText,
      maxLength: maxLength,
      className: className
    });
  } else
  /* istanbul ignore next */
  {
    cov_195fsi0tqv().b[37][1]++;
  }
  cov_195fsi0tqv().s[79]++;
  return (0, jsx_runtime_1.jsx)(TextOverflow, {
    className: className,
    maxLines: maxLines,
    children: limitedText
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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