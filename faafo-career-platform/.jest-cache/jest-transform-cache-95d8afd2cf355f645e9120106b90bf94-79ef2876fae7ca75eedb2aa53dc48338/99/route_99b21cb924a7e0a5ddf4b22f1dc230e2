6ae69cb230a2ab7fa991efa092a7a5c9
"use strict";

/* istanbul ignore next */
function cov_1gmjtbve37() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/route.ts";
  var hash = "b3f117f8947f337bf8bc6fb9413c4dda99863d5e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 52
        }
      },
      "80": {
        start: {
          line: 51,
          column: 15
        },
        end: {
          line: 51,
          column: 37
        }
      },
      "81": {
        start: {
          line: 52,
          column: 18
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "82": {
        start: {
          line: 53,
          column: 13
        },
        end: {
          line: 53,
          column: 34
        }
      },
      "83": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 38
        }
      },
      "84": {
        start: {
          line: 55,
          column: 34
        },
        end: {
          line: 55,
          column: 76
        }
      },
      "85": {
        start: {
          line: 56,
          column: 18
        },
        end: {
          line: 56,
          column: 44
        }
      },
      "86": {
        start: {
          line: 57,
          column: 35
        },
        end: {
          line: 57,
          column: 87
        }
      },
      "87": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 26
        }
      },
      "88": {
        start: {
          line: 59,
          column: 19
        },
        end: {
          line: 59,
          column: 46
        }
      },
      "89": {
        start: {
          line: 60,
          column: 31
        },
        end: {
          line: 72,
          column: 2
        }
      },
      "90": {
        start: {
          line: 74,
          column: 0
        },
        end: {
          line: 194,
          column: 7
        }
      },
      "91": {
        start: {
          line: 74,
          column: 99
        },
        end: {
          line: 194,
          column: 3
        }
      },
      "92": {
        start: {
          line: 75,
          column: 17
        },
        end: {
          line: 75,
          column: 26
        }
      },
      "93": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 193,
          column: 7
        }
      },
      "94": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 192,
          column: 20
        }
      },
      "95": {
        start: {
          line: 78,
          column: 26
        },
        end: {
          line: 192,
          column: 15
        }
      },
      "96": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 191,
          column: 19
        }
      },
      "97": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 190,
          column: 21
        }
      },
      "98": {
        start: {
          line: 83,
          column: 32
        },
        end: {
          line: 83,
          column: 61
        }
      },
      "99": {
        start: {
          line: 85,
          column: 28
        },
        end: {
          line: 85,
          column: 48
        }
      },
      "100": {
        start: {
          line: 86,
          column: 28
        },
        end: {
          line: 86,
          column: 104
        }
      },
      "101": {
        start: {
          line: 88,
          column: 28
        },
        end: {
          line: 88,
          column: 48
        }
      },
      "102": {
        start: {
          line: 89,
          column: 28
        },
        end: {
          line: 89,
          column: 150
        }
      },
      "103": {
        start: {
          line: 90,
          column: 28
        },
        end: {
          line: 90,
          column: 102
        }
      },
      "104": {
        start: {
          line: 91,
          column: 28
        },
        end: {
          line: 91,
          column: 111
        }
      },
      "105": {
        start: {
          line: 93,
          column: 28
        },
        end: {
          line: 93,
          column: 47
        }
      },
      "106": {
        start: {
          line: 94,
          column: 28
        },
        end: {
          line: 100,
          column: 29
        }
      },
      "107": {
        start: {
          line: 95,
          column: 32
        },
        end: {
          line: 99,
          column: 40
        }
      },
      "108": {
        start: {
          line: 101,
          column: 28
        },
        end: {
          line: 101,
          column: 81
        }
      },
      "109": {
        start: {
          line: 102,
          column: 28
        },
        end: {
          line: 168,
          column: 36
        }
      },
      "110": {
        start: {
          line: 170,
          column: 28
        },
        end: {
          line: 170,
          column: 53
        }
      },
      "111": {
        start: {
          line: 171,
          column: 28
        },
        end: {
          line: 173,
          column: 29
        }
      },
      "112": {
        start: {
          line: 172,
          column: 32
        },
        end: {
          line: 172,
          column: 153
        }
      },
      "113": {
        start: {
          line: 174,
          column: 28
        },
        end: {
          line: 176,
          column: 29
        }
      },
      "114": {
        start: {
          line: 175,
          column: 32
        },
        end: {
          line: 175,
          column: 160
        }
      },
      "115": {
        start: {
          line: 177,
          column: 28
        },
        end: {
          line: 180,
          column: 79
        }
      },
      "116": {
        start: {
          line: 179,
          column: 36
        },
        end: {
          line: 179,
          column: 171
        }
      },
      "117": {
        start: {
          line: 182,
          column: 28
        },
        end: {
          line: 182,
          column: 207
        }
      },
      "118": {
        start: {
          line: 185,
          column: 28
        },
        end: {
          line: 185,
          column: 38
        }
      },
      "119": {
        start: {
          line: 186,
          column: 28
        },
        end: {
          line: 189,
          column: 36
        }
      },
      "120": {
        start: {
          line: 196,
          column: 0
        },
        end: {
          line: 305,
          column: 7
        }
      },
      "121": {
        start: {
          line: 196,
          column: 99
        },
        end: {
          line: 305,
          column: 3
        }
      },
      "122": {
        start: {
          line: 197,
          column: 17
        },
        end: {
          line: 197,
          column: 26
        }
      },
      "123": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 304,
          column: 7
        }
      },
      "124": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 303,
          column: 20
        }
      },
      "125": {
        start: {
          line: 200,
          column: 26
        },
        end: {
          line: 303,
          column: 15
        }
      },
      "126": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 302,
          column: 19
        }
      },
      "127": {
        start: {
          line: 204,
          column: 20
        },
        end: {
          line: 301,
          column: 21
        }
      },
      "128": {
        start: {
          line: 205,
          column: 32
        },
        end: {
          line: 205,
          column: 108
        }
      },
      "129": {
        start: {
          line: 207,
          column: 28
        },
        end: {
          line: 207,
          column: 48
        }
      },
      "130": {
        start: {
          line: 208,
          column: 28
        },
        end: {
          line: 210,
          column: 29
        }
      },
      "131": {
        start: {
          line: 209,
          column: 32
        },
        end: {
          line: 209,
          column: 153
        }
      },
      "132": {
        start: {
          line: 211,
          column: 28
        },
        end: {
          line: 211,
          column: 97
        }
      },
      "133": {
        start: {
          line: 213,
          column: 28
        },
        end: {
          line: 213,
          column: 48
        }
      },
      "134": {
        start: {
          line: 214,
          column: 28
        },
        end: {
          line: 216,
          column: 29
        }
      },
      "135": {
        start: {
          line: 215,
          column: 32
        },
        end: {
          line: 215,
          column: 151
        }
      },
      "136": {
        start: {
          line: 217,
          column: 28
        },
        end: {
          line: 217,
          column: 57
        }
      },
      "137": {
        start: {
          line: 219,
          column: 28
        },
        end: {
          line: 219,
          column: 48
        }
      },
      "138": {
        start: {
          line: 220,
          column: 28
        },
        end: {
          line: 220,
          column: 65
        }
      },
      "139": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 45
        }
      },
      "140": {
        start: {
          line: 223,
          column: 28
        },
        end: {
          line: 223,
          column: 82
        }
      },
      "141": {
        start: {
          line: 224,
          column: 28
        },
        end: {
          line: 230,
          column: 29
        }
      },
      "142": {
        start: {
          line: 225,
          column: 32
        },
        end: {
          line: 229,
          column: 57
        }
      },
      "143": {
        start: {
          line: 231,
          column: 28
        },
        end: {
          line: 231,
          column: 341
        }
      },
      "144": {
        start: {
          line: 232,
          column: 28
        },
        end: {
          line: 234,
          column: 36
        }
      },
      "145": {
        start: {
          line: 236,
          column: 28
        },
        end: {
          line: 236,
          column: 53
        }
      },
      "146": {
        start: {
          line: 237,
          column: 28
        },
        end: {
          line: 239,
          column: 29
        }
      },
      "147": {
        start: {
          line: 238,
          column: 32
        },
        end: {
          line: 238,
          column: 153
        }
      },
      "148": {
        start: {
          line: 240,
          column: 28
        },
        end: {
          line: 240,
          column: 53
        }
      },
      "149": {
        start: {
          line: 241,
          column: 28
        },
        end: {
          line: 241,
          column: 98
        }
      },
      "150": {
        start: {
          line: 241,
          column: 74
        },
        end: {
          line: 241,
          column: 98
        }
      },
      "151": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 246,
          column: 40
        }
      },
      "152": {
        start: {
          line: 247,
          column: 28
        },
        end: {
          line: 252,
          column: 36
        }
      },
      "153": {
        start: {
          line: 254,
          column: 28
        },
        end: {
          line: 254,
          column: 51
        }
      },
      "154": {
        start: {
          line: 255,
          column: 28
        },
        end: {
          line: 257,
          column: 29
        }
      },
      "155": {
        start: {
          line: 256,
          column: 32
        },
        end: {
          line: 256,
          column: 176
        }
      },
      "156": {
        start: {
          line: 258,
          column: 28
        },
        end: {
          line: 258,
          column: 41
        }
      },
      "157": {
        start: {
          line: 259,
          column: 32
        },
        end: {
          line: 288,
          column: 32
        }
      },
      "158": {
        start: {
          line: 264,
          column: 78
        },
        end: {
          line: 264,
          column: 98
        }
      },
      "159": {
        start: {
          line: 269,
          column: 83
        },
        end: {
          line: 269,
          column: 103
        }
      },
      "160": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 52
        }
      },
      "161": {
        start: {
          line: 292,
          column: 28
        },
        end: {
          line: 292,
          column: 163
        }
      },
      "162": {
        start: {
          line: 295,
          column: 28
        },
        end: {
          line: 295,
          column: 38
        }
      },
      "163": {
        start: {
          line: 296,
          column: 28
        },
        end: {
          line: 300,
          column: 36
        }
      },
      "164": {
        start: {
          line: 307,
          column: 0
        },
        end: {
          line: 378,
          column: 7
        }
      },
      "165": {
        start: {
          line: 307,
          column: 102
        },
        end: {
          line: 378,
          column: 3
        }
      },
      "166": {
        start: {
          line: 308,
          column: 17
        },
        end: {
          line: 308,
          column: 26
        }
      },
      "167": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 377,
          column: 7
        }
      },
      "168": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 376,
          column: 20
        }
      },
      "169": {
        start: {
          line: 311,
          column: 26
        },
        end: {
          line: 376,
          column: 15
        }
      },
      "170": {
        start: {
          line: 314,
          column: 16
        },
        end: {
          line: 375,
          column: 19
        }
      },
      "171": {
        start: {
          line: 315,
          column: 20
        },
        end: {
          line: 374,
          column: 21
        }
      },
      "172": {
        start: {
          line: 316,
          column: 32
        },
        end: {
          line: 316,
          column: 108
        }
      },
      "173": {
        start: {
          line: 318,
          column: 28
        },
        end: {
          line: 318,
          column: 48
        }
      },
      "174": {
        start: {
          line: 319,
          column: 28
        },
        end: {
          line: 321,
          column: 29
        }
      },
      "175": {
        start: {
          line: 320,
          column: 32
        },
        end: {
          line: 320,
          column: 153
        }
      },
      "176": {
        start: {
          line: 322,
          column: 28
        },
        end: {
          line: 322,
          column: 97
        }
      },
      "177": {
        start: {
          line: 324,
          column: 28
        },
        end: {
          line: 324,
          column: 48
        }
      },
      "178": {
        start: {
          line: 325,
          column: 28
        },
        end: {
          line: 327,
          column: 29
        }
      },
      "179": {
        start: {
          line: 326,
          column: 32
        },
        end: {
          line: 326,
          column: 151
        }
      },
      "180": {
        start: {
          line: 328,
          column: 28
        },
        end: {
          line: 328,
          column: 57
        }
      },
      "181": {
        start: {
          line: 330,
          column: 28
        },
        end: {
          line: 330,
          column: 48
        }
      },
      "182": {
        start: {
          line: 331,
          column: 28
        },
        end: {
          line: 340,
          column: 36
        }
      },
      "183": {
        start: {
          line: 342,
          column: 28
        },
        end: {
          line: 342,
          column: 53
        }
      },
      "184": {
        start: {
          line: 343,
          column: 28
        },
        end: {
          line: 345,
          column: 29
        }
      },
      "185": {
        start: {
          line: 344,
          column: 32
        },
        end: {
          line: 344,
          column: 153
        }
      },
      "186": {
        start: {
          line: 346,
          column: 28
        },
        end: {
          line: 346,
          column: 94
        }
      },
      "187": {
        start: {
          line: 346,
          column: 70
        },
        end: {
          line: 346,
          column: 94
        }
      },
      "188": {
        start: {
          line: 347,
          column: 28
        },
        end: {
          line: 350,
          column: 36
        }
      },
      "189": {
        start: {
          line: 352,
          column: 28
        },
        end: {
          line: 352,
          column: 38
        }
      },
      "190": {
        start: {
          line: 353,
          column: 28
        },
        end: {
          line: 356,
          column: 36
        }
      },
      "191": {
        start: {
          line: 359,
          column: 24
        },
        end: {
          line: 361,
          column: 32
        }
      },
      "192": {
        start: {
          line: 364,
          column: 28
        },
        end: {
          line: 364,
          column: 38
        }
      },
      "193": {
        start: {
          line: 366,
          column: 28
        },
        end: {
          line: 366,
          column: 163
        }
      },
      "194": {
        start: {
          line: 369,
          column: 28
        },
        end: {
          line: 369,
          column: 38
        }
      },
      "195": {
        start: {
          line: 370,
          column: 28
        },
        end: {
          line: 373,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 74,
            column: 72
          },
          end: {
            line: 74,
            column: 73
          }
        },
        loc: {
          start: {
            line: 74,
            column: 97
          },
          end: {
            line: 194,
            column: 5
          }
        },
        line: 74
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 74,
            column: 149
          },
          end: {
            line: 74,
            column: 150
          }
        },
        loc: {
          start: {
            line: 74,
            column: 172
          },
          end: {
            line: 194,
            column: 1
          }
        },
        line: 74
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 76,
            column: 29
          },
          end: {
            line: 76,
            column: 30
          }
        },
        loc: {
          start: {
            line: 76,
            column: 43
          },
          end: {
            line: 193,
            column: 5
          }
        },
        line: 76
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 78,
            column: 12
          },
          end: {
            line: 78,
            column: 13
          }
        },
        loc: {
          start: {
            line: 78,
            column: 24
          },
          end: {
            line: 192,
            column: 17
          }
        },
        line: 78
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 78,
            column: 67
          },
          end: {
            line: 78,
            column: 68
          }
        },
        loc: {
          start: {
            line: 78,
            column: 79
          },
          end: {
            line: 192,
            column: 13
          }
        },
        line: 78
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 81,
            column: 41
          },
          end: {
            line: 81,
            column: 42
          }
        },
        loc: {
          start: {
            line: 81,
            column: 55
          },
          end: {
            line: 191,
            column: 17
          }
        },
        line: 81
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 177,
            column: 299
          },
          end: {
            line: 177,
            column: 300
          }
        },
        loc: {
          start: {
            line: 177,
            column: 315
          },
          end: {
            line: 180,
            column: 33
          }
        },
        line: 177
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 196,
            column: 72
          },
          end: {
            line: 196,
            column: 73
          }
        },
        loc: {
          start: {
            line: 196,
            column: 97
          },
          end: {
            line: 305,
            column: 5
          }
        },
        line: 196
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 196,
            column: 149
          },
          end: {
            line: 196,
            column: 150
          }
        },
        loc: {
          start: {
            line: 196,
            column: 172
          },
          end: {
            line: 305,
            column: 1
          }
        },
        line: 196
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 198,
            column: 29
          },
          end: {
            line: 198,
            column: 30
          }
        },
        loc: {
          start: {
            line: 198,
            column: 43
          },
          end: {
            line: 304,
            column: 5
          }
        },
        line: 198
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 200,
            column: 12
          },
          end: {
            line: 200,
            column: 13
          }
        },
        loc: {
          start: {
            line: 200,
            column: 24
          },
          end: {
            line: 303,
            column: 17
          }
        },
        line: 200
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 200,
            column: 67
          },
          end: {
            line: 200,
            column: 68
          }
        },
        loc: {
          start: {
            line: 200,
            column: 79
          },
          end: {
            line: 303,
            column: 13
          }
        },
        line: 200
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 203,
            column: 41
          },
          end: {
            line: 203,
            column: 42
          }
        },
        loc: {
          start: {
            line: 203,
            column: 55
          },
          end: {
            line: 302,
            column: 17
          }
        },
        line: 203
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 264,
            column: 62
          },
          end: {
            line: 264,
            column: 63
          }
        },
        loc: {
          start: {
            line: 264,
            column: 76
          },
          end: {
            line: 264,
            column: 100
          }
        },
        line: 264
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 269,
            column: 67
          },
          end: {
            line: 269,
            column: 68
          }
        },
        loc: {
          start: {
            line: 269,
            column: 81
          },
          end: {
            line: 269,
            column: 105
          }
        },
        line: 269
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 307,
            column: 75
          },
          end: {
            line: 307,
            column: 76
          }
        },
        loc: {
          start: {
            line: 307,
            column: 100
          },
          end: {
            line: 378,
            column: 5
          }
        },
        line: 307
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 307,
            column: 152
          },
          end: {
            line: 307,
            column: 153
          }
        },
        loc: {
          start: {
            line: 307,
            column: 175
          },
          end: {
            line: 378,
            column: 1
          }
        },
        line: 307
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 309,
            column: 29
          },
          end: {
            line: 309,
            column: 30
          }
        },
        loc: {
          start: {
            line: 309,
            column: 43
          },
          end: {
            line: 377,
            column: 5
          }
        },
        line: 309
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 311,
            column: 12
          },
          end: {
            line: 311,
            column: 13
          }
        },
        loc: {
          start: {
            line: 311,
            column: 24
          },
          end: {
            line: 376,
            column: 17
          }
        },
        line: 311
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 311,
            column: 67
          },
          end: {
            line: 311,
            column: 68
          }
        },
        loc: {
          start: {
            line: 311,
            column: 79
          },
          end: {
            line: 376,
            column: 13
          }
        },
        line: 311
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 314,
            column: 41
          },
          end: {
            line: 314,
            column: 42
          }
        },
        loc: {
          start: {
            line: 314,
            column: 55
          },
          end: {
            line: 375,
            column: 17
          }
        },
        line: 314
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 82,
            column: 20
          },
          end: {
            line: 190,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 83,
            column: 24
          },
          end: {
            line: 83,
            column: 61
          }
        }, {
          start: {
            line: 84,
            column: 24
          },
          end: {
            line: 86,
            column: 104
          }
        }, {
          start: {
            line: 87,
            column: 24
          },
          end: {
            line: 91,
            column: 111
          }
        }, {
          start: {
            line: 92,
            column: 24
          },
          end: {
            line: 168,
            column: 36
          }
        }, {
          start: {
            line: 169,
            column: 24
          },
          end: {
            line: 182,
            column: 207
          }
        }, {
          start: {
            line: 183,
            column: 24
          },
          end: {
            line: 189,
            column: 36
          }
        }],
        line: 82
      },
      "36": {
        loc: {
          start: {
            line: 89,
            column: 37
          },
          end: {
            line: 89,
            column: 149
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 89,
            column: 135
          },
          end: {
            line: 89,
            column: 141
          }
        }, {
          start: {
            line: 89,
            column: 144
          },
          end: {
            line: 89,
            column: 149
          }
        }],
        line: 89
      },
      "37": {
        loc: {
          start: {
            line: 89,
            column: 37
          },
          end: {
            line: 89,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 89,
            column: 37
          },
          end: {
            line: 89,
            column: 115
          }
        }, {
          start: {
            line: 89,
            column: 119
          },
          end: {
            line: 89,
            column: 132
          }
        }],
        line: 89
      },
      "38": {
        loc: {
          start: {
            line: 89,
            column: 43
          },
          end: {
            line: 89,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 89,
            column: 84
          },
          end: {
            line: 89,
            column: 90
          }
        }, {
          start: {
            line: 89,
            column: 93
          },
          end: {
            line: 89,
            column: 105
          }
        }],
        line: 89
      },
      "39": {
        loc: {
          start: {
            line: 89,
            column: 43
          },
          end: {
            line: 89,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 89,
            column: 43
          },
          end: {
            line: 89,
            column: 59
          }
        }, {
          start: {
            line: 89,
            column: 63
          },
          end: {
            line: 89,
            column: 81
          }
        }],
        line: 89
      },
      "40": {
        loc: {
          start: {
            line: 90,
            column: 79
          },
          end: {
            line: 90,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 90,
            column: 79
          },
          end: {
            line: 90,
            column: 85
          }
        }, {
          start: {
            line: 90,
            column: 89
          },
          end: {
            line: 90,
            column: 100
          }
        }],
        line: 90
      },
      "41": {
        loc: {
          start: {
            line: 94,
            column: 28
          },
          end: {
            line: 100,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 28
          },
          end: {
            line: 100,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "42": {
        loc: {
          start: {
            line: 101,
            column: 36
          },
          end: {
            line: 101,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 101,
            column: 55
          },
          end: {
            line: 101,
            column: 65
          }
        }, {
          start: {
            line: 101,
            column: 68
          },
          end: {
            line: 101,
            column: 80
          }
        }],
        line: 101
      },
      "43": {
        loc: {
          start: {
            line: 129,
            column: 62
          },
          end: {
            line: 140,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 129,
            column: 71
          },
          end: {
            line: 140,
            column: 49
          }
        }, {
          start: {
            line: 140,
            column: 52
          },
          end: {
            line: 140,
            column: 57
          }
        }],
        line: 129
      },
      "44": {
        loc: {
          start: {
            line: 143,
            column: 51
          },
          end: {
            line: 160,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 143,
            column: 60
          },
          end: {
            line: 160,
            column: 41
          }
        }, {
          start: {
            line: 160,
            column: 44
          },
          end: {
            line: 160,
            column: 49
          }
        }],
        line: 143
      },
      "45": {
        loc: {
          start: {
            line: 171,
            column: 28
          },
          end: {
            line: 173,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 28
          },
          end: {
            line: 173,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 171
      },
      "46": {
        loc: {
          start: {
            line: 174,
            column: 28
          },
          end: {
            line: 176,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 28
          },
          end: {
            line: 176,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "47": {
        loc: {
          start: {
            line: 177,
            column: 185
          },
          end: {
            line: 177,
            column: 267
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 186
          },
          end: {
            line: 177,
            column: 258
          }
        }, {
          start: {
            line: 177,
            column: 263
          },
          end: {
            line: 177,
            column: 267
          }
        }],
        line: 177
      },
      "48": {
        loc: {
          start: {
            line: 177,
            column: 186
          },
          end: {
            line: 177,
            column: 258
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 177,
            column: 244
          },
          end: {
            line: 177,
            column: 250
          }
        }, {
          start: {
            line: 177,
            column: 253
          },
          end: {
            line: 177,
            column: 258
          }
        }],
        line: 177
      },
      "49": {
        loc: {
          start: {
            line: 177,
            column: 186
          },
          end: {
            line: 177,
            column: 241
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 186
          },
          end: {
            line: 177,
            column: 224
          }
        }, {
          start: {
            line: 177,
            column: 228
          },
          end: {
            line: 177,
            column: 241
          }
        }],
        line: 177
      },
      "50": {
        loc: {
          start: {
            line: 179,
            column: 89
          },
          end: {
            line: 179,
            column: 166
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 90
          },
          end: {
            line: 179,
            column: 157
          }
        }, {
          start: {
            line: 179,
            column: 162
          },
          end: {
            line: 179,
            column: 166
          }
        }],
        line: 179
      },
      "51": {
        loc: {
          start: {
            line: 179,
            column: 90
          },
          end: {
            line: 179,
            column: 157
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 179,
            column: 143
          },
          end: {
            line: 179,
            column: 149
          }
        }, {
          start: {
            line: 179,
            column: 152
          },
          end: {
            line: 179,
            column: 157
          }
        }],
        line: 179
      },
      "52": {
        loc: {
          start: {
            line: 179,
            column: 90
          },
          end: {
            line: 179,
            column: 140
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 90
          },
          end: {
            line: 179,
            column: 123
          }
        }, {
          start: {
            line: 179,
            column: 127
          },
          end: {
            line: 179,
            column: 140
          }
        }],
        line: 179
      },
      "53": {
        loc: {
          start: {
            line: 204,
            column: 20
          },
          end: {
            line: 301,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 205,
            column: 24
          },
          end: {
            line: 205,
            column: 108
          }
        }, {
          start: {
            line: 206,
            column: 24
          },
          end: {
            line: 211,
            column: 97
          }
        }, {
          start: {
            line: 212,
            column: 24
          },
          end: {
            line: 217,
            column: 57
          }
        }, {
          start: {
            line: 218,
            column: 24
          },
          end: {
            line: 220,
            column: 65
          }
        }, {
          start: {
            line: 221,
            column: 24
          },
          end: {
            line: 234,
            column: 36
          }
        }, {
          start: {
            line: 235,
            column: 24
          },
          end: {
            line: 252,
            column: 36
          }
        }, {
          start: {
            line: 253,
            column: 24
          },
          end: {
            line: 258,
            column: 41
          }
        }, {
          start: {
            line: 259,
            column: 24
          },
          end: {
            line: 288,
            column: 32
          }
        }, {
          start: {
            line: 289,
            column: 24
          },
          end: {
            line: 292,
            column: 163
          }
        }, {
          start: {
            line: 293,
            column: 24
          },
          end: {
            line: 300,
            column: 36
          }
        }],
        line: 204
      },
      "54": {
        loc: {
          start: {
            line: 208,
            column: 28
          },
          end: {
            line: 210,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 28
          },
          end: {
            line: 210,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "55": {
        loc: {
          start: {
            line: 208,
            column: 34
          },
          end: {
            line: 208,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 208,
            column: 132
          },
          end: {
            line: 208,
            column: 138
          }
        }, {
          start: {
            line: 208,
            column: 141
          },
          end: {
            line: 208,
            column: 146
          }
        }],
        line: 208
      },
      "56": {
        loc: {
          start: {
            line: 208,
            column: 34
          },
          end: {
            line: 208,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 208,
            column: 34
          },
          end: {
            line: 208,
            column: 112
          }
        }, {
          start: {
            line: 208,
            column: 116
          },
          end: {
            line: 208,
            column: 129
          }
        }],
        line: 208
      },
      "57": {
        loc: {
          start: {
            line: 208,
            column: 40
          },
          end: {
            line: 208,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 208,
            column: 81
          },
          end: {
            line: 208,
            column: 87
          }
        }, {
          start: {
            line: 208,
            column: 90
          },
          end: {
            line: 208,
            column: 102
          }
        }],
        line: 208
      },
      "58": {
        loc: {
          start: {
            line: 208,
            column: 40
          },
          end: {
            line: 208,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 208,
            column: 40
          },
          end: {
            line: 208,
            column: 56
          }
        }, {
          start: {
            line: 208,
            column: 60
          },
          end: {
            line: 208,
            column: 78
          }
        }],
        line: 208
      },
      "59": {
        loc: {
          start: {
            line: 214,
            column: 28
          },
          end: {
            line: 216,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 28
          },
          end: {
            line: 216,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "60": {
        loc: {
          start: {
            line: 224,
            column: 28
          },
          end: {
            line: 230,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 28
          },
          end: {
            line: 230,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "61": {
        loc: {
          start: {
            line: 237,
            column: 28
          },
          end: {
            line: 239,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 28
          },
          end: {
            line: 239,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "62": {
        loc: {
          start: {
            line: 241,
            column: 28
          },
          end: {
            line: 241,
            column: 98
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 28
          },
          end: {
            line: 241,
            column: 98
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "63": {
        loc: {
          start: {
            line: 241,
            column: 34
          },
          end: {
            line: 241,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 241,
            column: 34
          },
          end: {
            line: 241,
            column: 39
          }
        }, {
          start: {
            line: 241,
            column: 43
          },
          end: {
            line: 241,
            column: 71
          }
        }],
        line: 241
      },
      "64": {
        loc: {
          start: {
            line: 255,
            column: 28
          },
          end: {
            line: 257,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 255,
            column: 28
          },
          end: {
            line: 257,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 255
      },
      "65": {
        loc: {
          start: {
            line: 261,
            column: 142
          },
          end: {
            line: 261,
            column: 179
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 142
          },
          end: {
            line: 261,
            column: 147
          }
        }, {
          start: {
            line: 261,
            column: 151
          },
          end: {
            line: 261,
            column: 179
          }
        }],
        line: 261
      },
      "66": {
        loc: {
          start: {
            line: 261,
            column: 184
          },
          end: {
            line: 261,
            column: 227
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 184
          },
          end: {
            line: 261,
            column: 195
          }
        }, {
          start: {
            line: 261,
            column: 199
          },
          end: {
            line: 261,
            column: 227
          }
        }],
        line: 261
      },
      "67": {
        loc: {
          start: {
            line: 261,
            column: 232
          },
          end: {
            line: 261,
            column: 272
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 232
          },
          end: {
            line: 261,
            column: 242
          }
        }, {
          start: {
            line: 261,
            column: 246
          },
          end: {
            line: 261,
            column: 272
          }
        }],
        line: 261
      },
      "68": {
        loc: {
          start: {
            line: 261,
            column: 277
          },
          end: {
            line: 261,
            column: 329
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 277
          },
          end: {
            line: 261,
            column: 291
          }
        }, {
          start: {
            line: 261,
            column: 295
          },
          end: {
            line: 261,
            column: 329
          }
        }],
        line: 261
      },
      "69": {
        loc: {
          start: {
            line: 261,
            column: 334
          },
          end: {
            line: 261,
            column: 368
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 334
          },
          end: {
            line: 261,
            column: 342
          }
        }, {
          start: {
            line: 261,
            column: 346
          },
          end: {
            line: 261,
            column: 368
          }
        }],
        line: 261
      },
      "70": {
        loc: {
          start: {
            line: 261,
            column: 373
          },
          end: {
            line: 261,
            column: 436
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 373
          },
          end: {
            line: 261,
            column: 400
          }
        }, {
          start: {
            line: 261,
            column: 404
          },
          end: {
            line: 261,
            column: 436
          }
        }],
        line: 261
      },
      "71": {
        loc: {
          start: {
            line: 261,
            column: 441
          },
          end: {
            line: 261,
            column: 477
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 441
          },
          end: {
            line: 261,
            column: 459
          }
        }, {
          start: {
            line: 261,
            column: 463
          },
          end: {
            line: 261,
            column: 477
          }
        }],
        line: 261
      },
      "72": {
        loc: {
          start: {
            line: 261,
            column: 482
          },
          end: {
            line: 261,
            column: 530
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 482
          },
          end: {
            line: 261,
            column: 504
          }
        }, {
          start: {
            line: 261,
            column: 508
          },
          end: {
            line: 261,
            column: 530
          }
        }],
        line: 261
      },
      "73": {
        loc: {
          start: {
            line: 261,
            column: 535
          },
          end: {
            line: 261,
            column: 583
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 535
          },
          end: {
            line: 261,
            column: 557
          }
        }, {
          start: {
            line: 261,
            column: 561
          },
          end: {
            line: 261,
            column: 583
          }
        }],
        line: 261
      },
      "74": {
        loc: {
          start: {
            line: 261,
            column: 588
          },
          end: {
            line: 266,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 588
          },
          end: {
            line: 261,
            column: 596
          }
        }, {
          start: {
            line: 261,
            column: 600
          },
          end: {
            line: 266,
            column: 33
          }
        }],
        line: 261
      },
      "75": {
        loc: {
          start: {
            line: 266,
            column: 38
          },
          end: {
            line: 271,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 266,
            column: 38
          },
          end: {
            line: 266,
            column: 51
          }
        }, {
          start: {
            line: 266,
            column: 55
          },
          end: {
            line: 271,
            column: 33
          }
        }],
        line: 266
      },
      "76": {
        loc: {
          start: {
            line: 315,
            column: 20
          },
          end: {
            line: 374,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 316,
            column: 24
          },
          end: {
            line: 316,
            column: 108
          }
        }, {
          start: {
            line: 317,
            column: 24
          },
          end: {
            line: 322,
            column: 97
          }
        }, {
          start: {
            line: 323,
            column: 24
          },
          end: {
            line: 328,
            column: 57
          }
        }, {
          start: {
            line: 329,
            column: 24
          },
          end: {
            line: 340,
            column: 36
          }
        }, {
          start: {
            line: 341,
            column: 24
          },
          end: {
            line: 350,
            column: 36
          }
        }, {
          start: {
            line: 351,
            column: 24
          },
          end: {
            line: 356,
            column: 36
          }
        }, {
          start: {
            line: 357,
            column: 24
          },
          end: {
            line: 361,
            column: 32
          }
        }, {
          start: {
            line: 362,
            column: 24
          },
          end: {
            line: 366,
            column: 163
          }
        }, {
          start: {
            line: 367,
            column: 24
          },
          end: {
            line: 373,
            column: 36
          }
        }],
        line: 315
      },
      "77": {
        loc: {
          start: {
            line: 319,
            column: 28
          },
          end: {
            line: 321,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 28
          },
          end: {
            line: 321,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "78": {
        loc: {
          start: {
            line: 319,
            column: 34
          },
          end: {
            line: 319,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 319,
            column: 132
          },
          end: {
            line: 319,
            column: 138
          }
        }, {
          start: {
            line: 319,
            column: 141
          },
          end: {
            line: 319,
            column: 146
          }
        }],
        line: 319
      },
      "79": {
        loc: {
          start: {
            line: 319,
            column: 34
          },
          end: {
            line: 319,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 34
          },
          end: {
            line: 319,
            column: 112
          }
        }, {
          start: {
            line: 319,
            column: 116
          },
          end: {
            line: 319,
            column: 129
          }
        }],
        line: 319
      },
      "80": {
        loc: {
          start: {
            line: 319,
            column: 40
          },
          end: {
            line: 319,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 319,
            column: 81
          },
          end: {
            line: 319,
            column: 87
          }
        }, {
          start: {
            line: 319,
            column: 90
          },
          end: {
            line: 319,
            column: 102
          }
        }],
        line: 319
      },
      "81": {
        loc: {
          start: {
            line: 319,
            column: 40
          },
          end: {
            line: 319,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 40
          },
          end: {
            line: 319,
            column: 56
          }
        }, {
          start: {
            line: 319,
            column: 60
          },
          end: {
            line: 319,
            column: 78
          }
        }],
        line: 319
      },
      "82": {
        loc: {
          start: {
            line: 325,
            column: 28
          },
          end: {
            line: 327,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 325,
            column: 28
          },
          end: {
            line: 327,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 325
      },
      "83": {
        loc: {
          start: {
            line: 343,
            column: 28
          },
          end: {
            line: 345,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 343,
            column: 28
          },
          end: {
            line: 345,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 343
      },
      "84": {
        loc: {
          start: {
            line: 346,
            column: 28
          },
          end: {
            line: 346,
            column: 94
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 346,
            column: 28
          },
          end: {
            line: 346,
            column: 94
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 346
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,uCAAsC;AACtC,6EAA2E;AAC3E,6CAAgD;AAChD,wFAA8E;AAC9E,2BAAwB;AAExB,+CAA+C;AAE/C,IAAM,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC5C,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACnD,UAAU,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;IACjF,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACtD,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,cAAc,EAAE,oBAAoB,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC7T,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC7C,IAAI,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACpC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;IACrC,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IAChC,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC/C,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE;CACrD,CAAC,CAAC;AAEH,wCAAwC;AAC3B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE,8BAA8B;YAC9E;;;;;gCACiB,qBAAM,MAAM,EAAA;;4BAAnB,EAAE,GAAK,CAAA,SAAY,CAAA,GAAjB;4BACM,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAC7C,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAC;4BAGzB,QAAQ,GAAG,wBAAiB,EAAE,cAAI,MAAM,IAAI,WAAW,CAAE,CAAC;4BAGjD,qBAAM,8CAAiB,CAAC,GAAG,CAAM,QAAQ,CAAC,EAAA;;4BAAnD,MAAM,GAAG,SAA0C;4BACzD,IAAI,MAAM,EAAE,CAAC;gCACX,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,IAAI;wCACb,IAAI,EAAE,MAAM;wCACZ,MAAM,EAAE,IAAI;qCACb,CAAC,EAAC;4BACL,CAAC;4BAGK,KAAK,GAAG,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,IAAA,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;4BAElC,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oCACxD,KAAK,OAAA;oCACL,OAAO,EAAE;wCACP,MAAM,EAAE,IAAI;wCACZ,WAAW,EAAE;4CACX,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,IAAI,EAAE,IAAI;gDACV,IAAI,EAAE,IAAI;gDACV,QAAQ,EAAE,IAAI;6CACf;yCACF;wCACD,KAAK,EAAE;4CACL,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;4CAC7B,OAAO,EAAE;gDACP,QAAQ,EAAE;oDACR,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,KAAK,EAAE,IAAI;wDACX,WAAW,EAAE,IAAI;wDACjB,IAAI,EAAE,IAAI;wDACV,GAAG,EAAE,IAAI;wDACT,MAAM,EAAE,IAAI;wDACZ,QAAQ,EAAE,IAAI;wDACd,UAAU,EAAE,IAAI;qDACjB;iDACF;gDACD,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;oDACrB,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE;oDACjB,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,MAAM,EAAE,IAAI;wDACZ,SAAS,EAAE,IAAI;wDACf,WAAW,EAAE,IAAI;wDACjB,SAAS,EAAE,IAAI;wDACf,KAAK,EAAE,IAAI;wDACX,KAAK,EAAE,IAAI;qDACZ;iDACF,CAAC,CAAC,CAAC,KAAK;6CACV;yCACF;wCACD,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;4CAClB,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE;4CACjB,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,MAAM,EAAE,IAAI;gDACZ,SAAS,EAAE,IAAI;gDACf,WAAW,EAAE,IAAI;gDACjB,cAAc,EAAE,IAAI;gDACpB,aAAa,EAAE,IAAI;gDACnB,cAAc,EAAE,IAAI;gDACpB,UAAU,EAAE,IAAI;gDAChB,eAAe,EAAE,IAAI;gDACrB,cAAc,EAAE,IAAI;gDACpB,KAAK,EAAE,IAAI;gDACX,MAAM,EAAE,IAAI;gDACZ,MAAM,EAAE,IAAI;6CACb;yCACF,CAAC,CAAC,CAAC,KAAK;wCACT,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,KAAK,EAAE,IAAI;gDACX,SAAS,EAAE,IAAI;6CAChB;yCACF;qCACF;iCACF,CAAC,EAAA;;4BAlEI,YAAY,GAAG,SAkEnB;4BAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gCAClB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAED,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gCAC3B,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAC3D,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGK,eAAe,yBAChB,YAAY,KACf,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,KAAK,EACpC,eAAe,EAAE,YAAY,CAAC,MAAM,CAAC,SAAS,EAC9C,YAAY,EAAE,CAAA,MAAA,YAAY,CAAC,SAAS,0CAAG,CAAC,CAAC,KAAI,IAAI,EACjD,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI;;oCAAI,OAAA,uBACjC,IAAI,KACP,YAAY,EAAE,CAAA,MAAA,IAAI,CAAC,YAAY,0CAAG,CAAC,CAAC,KAAI,IAAI,IAC5C,CAAA;iCAAA,CAAC,EACH,MAAM,EAAE,SAAS,EACjB,SAAS,EAAE,SAAS,GACrB,CAAC;4BAEJ,sBAAsB;4BACtB,qBAAM,8CAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,CAAC,gBAAgB,EAAE,wBAAiB,EAAE,CAAE,CAAC,EAAE,CAAC,EAAA;;4BAD/H,sBAAsB;4BACtB,SAA+H,CAAC;4BAEhI,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,eAAe;iCACtB,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,0CAA0C;AAC7B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,4BAA4B;YAC3E;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGe,qBAAM,IAAA,wBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;4BAA5C,OAAO,GAAG,SAAkC;4BAClD,IAAI,CAAC,OAAO,EAAE,CAAC;gCACb,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAClD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEc,qBAAM,MAAM,EAAA;;4BAAnB,EAAE,GAAK,CAAA,SAAY,CAAA,GAAjB;4BACG,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;4BAA3B,IAAI,GAAG,SAAoB;4BACzB,UAAU,GAAG,wBAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;4BAE5D,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gCACxB,sBAAO,qBAAY,CAAC,IAAI,CACtB;wCACE,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,sBAAsB;wCAC7B,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM;qCACjC,EACD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,KAYF,UAAU,CAAC,IAAI,EAXjB,KAAK,WAAA,EACL,WAAW,iBAAA,EACX,UAAU,gBAAA,EACV,cAAc,oBAAA,EACd,QAAQ,cAAA,EACR,aAAa,mBAAA,EACb,IAAI,UAAA,EACJ,QAAQ,cAAA,EACR,QAAQ,cAAA,EACR,QAAQ,cAAA,EACR,aAAa,mBAAA,CACK;4BAGC,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oCACxD,KAAK,EAAE,EAAE,EAAE,IAAA,EAAE;iCACd,CAAC,EAAA;;4BAFI,YAAY,GAAG,SAEnB;4BAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gCAClB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;iCACzB,CAAA,KAAK,IAAI,KAAK,KAAK,YAAY,CAAC,KAAK,CAAA,EAArC,wBAAqC;4BACvC,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE;iCACvB,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;iCAC5B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;iCACpB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;iCACnB,IAAI,EAAE,CAAC;4BAGS,qBAAM,eAAM,CAAC,YAAY,CAAC,SAAS,CAAC;oCACrD,KAAK,EAAE;wCACL,IAAI,MAAA;wCACJ,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;qCAChB;iCACF,CAAC,EAAA;;4BALI,UAAU,GAAG,SAKjB;4BAEF,IAAI,UAAU,EAAE,CAAC;gCACf,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gDAAgD,EAAE,EAC3E,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;;gCAIiB,qBAAM,eAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gCACnD,KAAK,EAAE,EAAE,EAAE,IAAA,EAAE;gCACb,IAAI,yGACC,CAAC,KAAK,IAAI,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,CAAC,GAC1B,CAAC,WAAW,IAAI,EAAE,WAAW,aAAA,EAAE,CAAC,GAChC,CAAC,UAAU,IAAI,EAAE,UAAU,YAAA,EAAE,CAAC,GAC9B,CAAC,cAAc,IAAI,EAAE,cAAc,gBAAA,EAAE,CAAC,GACtC,CAAC,QAAQ,IAAI,EAAE,QAAQ,UAAA,EAAE,CAAC,GAC1B,CAAC,aAAa,KAAK,SAAS,IAAI,EAAE,aAAa,eAAA,EAAE,CAAC,GAClD,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,MAAA,EAAE,CAAC,GAChC,CAAC,QAAQ,KAAK,SAAS,IAAI,EAAE,QAAQ,UAAA,EAAE,CAAC,GACxC,CAAC,QAAQ,KAAK,SAAS,IAAI,EAAE,QAAQ,UAAA,EAAE,CAAC,GACxC,CAAC,QAAQ,IAAI;oCACd,MAAM,EAAE;wCACN,GAAG,EAAE,EAAE,EAAE,iBAAiB;wCAC1B,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,EAAE,EAAE,IAAA,EAAE,CAAC,EAAR,CAAQ,CAAC;qCACtC;iCACF,CAAC,GACC,CAAC,aAAa,IAAI;oCACnB,WAAW,EAAE;wCACX,GAAG,EAAE,EAAE,EAAE,iBAAiB;wCAC1B,OAAO,EAAE,aAAa,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,EAAE,EAAE,IAAA,EAAE,CAAC,EAAR,CAAQ,CAAC;qCAC3C;iCACF,CAAC,CACH;gCACD,OAAO,EAAE;oCACP,MAAM,EAAE,IAAI;oCACZ,WAAW,EAAE;wCACX,MAAM,EAAE;4CACN,EAAE,EAAE,IAAI;4CACR,IAAI,EAAE,IAAI;4CACV,IAAI,EAAE,IAAI;yCACX;qCACF;oCACD,MAAM,EAAE;wCACN,MAAM,EAAE;4CACN,KAAK,EAAE,IAAI;4CACX,SAAS,EAAE,IAAI;yCAChB;qCACF;iCACF;6BACF,CAAC,EAAA;;4BAzCI,WAAW,GAAG,SAyClB;4BAEF,cAAc;4BACd,qBAAM,8CAAiB,CAAC,gBAAgB,CAAC,CAAC,gBAAgB,EAAE,wBAAiB,EAAE,CAAE,CAAC,CAAC,EAAA;;4BADnF,cAAc;4BACd,SAAmF,CAAC;4BAEtF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,wBACC,WAAW,KACd,SAAS,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK,EACnC,eAAe,EAAE,WAAW,CAAC,MAAM,CAAC,SAAS,EAC7C,MAAM,EAAE,SAAS,GAClB;oCACD,OAAO,EAAE,oCAAoC;iCAC9C,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,6CAA6C;AAChC,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,uFAC7C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,8BAA8B;YAC7E;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGe,qBAAM,IAAA,wBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;4BAA5C,OAAO,GAAG,SAAkC;4BAClD,IAAI,CAAC,OAAO,EAAE,CAAC;gCACb,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAClD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BACgB,qBAAM,MAAM,EAAA;;4BAAnB,EAAE,GAAK,CAAA,SAAY,CAAA,GAAjB;4BAGW,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oCACxD,KAAK,EAAE,EAAE,EAAE,IAAA,EAAE;oCACb,OAAO,EAAE;wCACP,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,SAAS,EAAE,IAAI;6CAChB;yCACF;qCACF;iCACF,CAAC,EAAA;;4BATI,YAAY,GAAG,SASnB;4BAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gCAClB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;iCAGG,CAAA,YAAY,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAA,EAAjC,wBAAiC;4BACnC,qBAAM,eAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oCAC/B,KAAK,EAAE,EAAE,EAAE,IAAA,EAAE;oCACb,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;iCAC1B,CAAC,EAAA;;4BAHF,SAGE,CAAC;4BAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,OAAO,EAAE,uDAAuD;iCACjE,CAAC,EAAC;;wBAGL,oDAAoD;wBACpD,qBAAM,eAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gCAC/B,KAAK,EAAE,EAAE,EAAE,IAAA,EAAE;6BACd,CAAC,EAAA;;4BAHF,oDAAoD;4BACpD,SAEE,CAAC;4BAEL,cAAc;4BACd,qBAAM,8CAAiB,CAAC,gBAAgB,CAAC,CAAC,gBAAgB,EAAE,wBAAiB,EAAE,CAAE,CAAC,CAAC,EAAA;;4BADnF,cAAc;4BACd,SAAmF,CAAC;4BAEpF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,OAAO,EAAE,oCAAoC;iCAC9C,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { consolidatedCache } from '@/lib/services/consolidated-cache-service';\nimport { z } from 'zod';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { isUserAdmin } from '@/lib/auth-utils';\n\nconst updateLearningPathSchema = z.object({\n  title: z.string().min(1).max(200).optional(),\n  description: z.string().min(1).max(2000).optional(),\n  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),\n  estimatedHours: z.number().min(1).max(1000).optional(),\n  category: z.enum(['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP', 'UX_UI_DESIGN', 'PRODUCT_MANAGEMENT', 'DEVOPS']).optional(),\n  prerequisites: z.array(z.string()).optional(),\n  tags: z.array(z.string()).optional(),\n  imageUrl: z.string().url().optional(),\n  isActive: z.boolean().optional(),\n  skillIds: z.array(z.string().uuid()).optional(),\n  careerPathIds: z.array(z.string().uuid()).optional(),\n});\n\n// GET - Retrieve specific learning path\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 requests per 15 minutes\n    async () => {\n      const { id } = await params;\n      const session = await getServerSession(authOptions);\n      const userId = session?.user?.id;\n\n        // Build cache key\n        const cacheKey = `learning_path:${id}:${userId || 'anonymous'}`;\n        \n        // Check cache first\n        const cached = await consolidatedCache.get<any>(cacheKey);\n        if (cached) {\n          return NextResponse.json({\n            success: true,\n            data: cached,\n            cached: true\n          });\n        }\n\n        // Find learning path by ID or slug\n        const where = id.length === 36 ? { id } : { slug: id };\n\n        const learningPath = await prisma.learningPath.findUnique({\n          where,\n          include: {\n            skills: true,\n            careerPaths: {\n              select: {\n                id: true,\n                name: true,\n                slug: true,\n                overview: true,\n              }\n            },\n            steps: {\n              orderBy: { stepOrder: 'asc' },\n              include: {\n                resource: {\n                  select: {\n                    id: true,\n                    title: true,\n                    description: true,\n                    type: true,\n                    url: true,\n                    author: true,\n                    duration: true,\n                    skillLevel: true,\n                  }\n                },\n                userProgress: userId ? {\n                  where: { userId },\n                  select: {\n                    id: true,\n                    status: true,\n                    startedAt: true,\n                    completedAt: true,\n                    timeSpent: true,\n                    score: true,\n                    notes: true,\n                  }\n                } : false,\n              }\n            },\n            userPaths: userId ? {\n              where: { userId },\n              select: {\n                id: true,\n                status: true,\n                startedAt: true,\n                completedAt: true,\n                lastAccessedAt: true,\n                currentStepId: true,\n                completedSteps: true,\n                totalSteps: true,\n                progressPercent: true,\n                totalTimeSpent: true,\n                notes: true,\n                rating: true,\n                review: true,\n              }\n            } : false,\n            _count: {\n              select: {\n                steps: true,\n                userPaths: true,\n              }\n            }\n          }\n        });\n\n        if (!learningPath) {\n          return NextResponse.json(\n            { success: false, error: 'Learning path not found' },\n            { status: 404 }\n          );\n        }\n\n        if (!learningPath.isActive) {\n          return NextResponse.json(\n            { success: false, error: 'Learning path is not available' },\n            { status: 404 }\n          );\n        }\n\n        // Transform data\n        const transformedPath = {\n          ...learningPath,\n          stepCount: learningPath._count.steps,\n          enrollmentCount: learningPath._count.userPaths,\n          userProgress: learningPath.userPaths?.[0] || null,\n          steps: learningPath.steps.map(step => ({\n            ...step,\n            userProgress: step.userProgress?.[0] || null,\n          })),\n          _count: undefined,\n          userPaths: undefined,\n        };\n\n      // Cache for 5 minutes\n      await consolidatedCache.set(cacheKey, transformedPath, { ttl: 5 * 60 * 1000, tags: ['learning_paths', `learning_path:${id}`] });\n\n      return NextResponse.json({\n        success: true,\n        data: transformedPath\n      });\n    }\n  );\n});\n\n// PUT - Update learning path (admin only)\nexport const PUT = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 updates per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      // Check admin access using proper role-based authorization\n      const isAdmin = await isUserAdmin(session.user.id);\n      if (!isAdmin) {\n        return NextResponse.json(\n          { success: false, error: 'Admin access required' },\n          { status: 403 }\n        );\n      }\n\n      const { id } = await params;\n      const body = await request.json();\n        const validation = updateLearningPathSchema.safeParse(body);\n        \n        if (!validation.success) {\n          return NextResponse.json(\n            { \n              success: false, \n              error: 'Invalid request data',\n              details: validation.error.errors \n            },\n            { status: 400 }\n          );\n        }\n\n        const {\n          title,\n          description,\n          difficulty,\n          estimatedHours,\n          category,\n          prerequisites,\n          tags,\n          imageUrl,\n          isActive,\n          skillIds,\n          careerPathIds,\n        } = validation.data;\n\n        // Check if learning path exists\n        const existingPath = await prisma.learningPath.findUnique({\n          where: { id }\n        });\n\n        if (!existingPath) {\n          return NextResponse.json(\n            { success: false, error: 'Learning path not found' },\n            { status: 404 }\n          );\n        }\n\n        // Generate new slug if title changed\n        let slug = existingPath.slug;\n        if (title && title !== existingPath.title) {\n          slug = title.toLowerCase()\n            .replace(/[^a-z0-9\\s-]/g, '')\n            .replace(/\\s+/g, '-')\n            .replace(/-+/g, '-')\n            .trim();\n\n          // Check if new slug already exists\n          const slugExists = await prisma.learningPath.findFirst({\n            where: { \n              slug,\n              id: { not: id }\n            }\n          });\n\n          if (slugExists) {\n            return NextResponse.json(\n              { success: false, error: 'A learning path with this title already exists' },\n              { status: 409 }\n            );\n          }\n        }\n\n        // Update learning path\n        const updatedPath = await prisma.learningPath.update({\n          where: { id },\n          data: {\n            ...(title && { title, slug }),\n            ...(description && { description }),\n            ...(difficulty && { difficulty }),\n            ...(estimatedHours && { estimatedHours }),\n            ...(category && { category }),\n            ...(prerequisites !== undefined && { prerequisites }),\n            ...(tags !== undefined && { tags }),\n            ...(imageUrl !== undefined && { imageUrl }),\n            ...(isActive !== undefined && { isActive }),\n            ...(skillIds && {\n              skills: {\n                set: [], // Clear existing\n                connect: skillIds.map(id => ({ id }))\n              }\n            }),\n            ...(careerPathIds && {\n              careerPaths: {\n                set: [], // Clear existing\n                connect: careerPathIds.map(id => ({ id }))\n              }\n            }),\n          },\n          include: {\n            skills: true,\n            careerPaths: {\n              select: {\n                id: true,\n                name: true,\n                slug: true,\n              }\n            },\n            _count: {\n              select: {\n                steps: true,\n                userPaths: true,\n              }\n            }\n          }\n        });\n\n        // Clear cache\n        await consolidatedCache.invalidateByTags(['learning_paths', `learning_path:${id}`]);\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          ...updatedPath,\n          stepCount: updatedPath._count.steps,\n          enrollmentCount: updatedPath._count.userPaths,\n          _count: undefined,\n        },\n        message: 'Learning path updated successfully'\n      });\n    }\n  );\n});\n\n// DELETE - Delete learning path (admin only)\nexport const DELETE = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      // Check admin access using proper role-based authorization\n      const isAdmin = await isUserAdmin(session.user.id);\n      if (!isAdmin) {\n        return NextResponse.json(\n          { success: false, error: 'Admin access required' },\n          { status: 403 }\n        );\n      }\n        const { id } = await params;\n\n        // Check if learning path exists and has enrollments\n        const learningPath = await prisma.learningPath.findUnique({\n          where: { id },\n          include: {\n            _count: {\n              select: {\n                userPaths: true,\n              }\n            }\n          }\n        });\n\n        if (!learningPath) {\n          return NextResponse.json(\n            { success: false, error: 'Learning path not found' },\n            { status: 404 }\n          );\n        }\n\n        // If there are enrollments, deactivate instead of delete\n        if (learningPath._count.userPaths > 0) {\n          await prisma.learningPath.update({\n            where: { id },\n            data: { isActive: false }\n          });\n\n          return NextResponse.json({\n            success: true,\n            message: 'Learning path deactivated due to existing enrollments'\n          });\n        }\n\n        // Delete learning path (this will cascade to steps)\n        await prisma.learningPath.delete({\n          where: { id }\n        });\n\n      // Clear cache\n      await consolidatedCache.invalidateByTags(['learning_paths', `learning_path:${id}`]);\n\n      return NextResponse.json({\n        success: true,\n        message: 'Learning path deleted successfully'\n      });\n    }\n  );\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b3f117f8947f337bf8bc6fb9413c4dda99863d5e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1gmjtbve37 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1gmjtbve37();
var __assign =
/* istanbul ignore next */
(cov_1gmjtbve37().s[0]++,
/* istanbul ignore next */
(cov_1gmjtbve37().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1gmjtbve37().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1gmjtbve37().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1gmjtbve37().f[0]++;
  cov_1gmjtbve37().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1gmjtbve37().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1gmjtbve37().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[1]++;
    cov_1gmjtbve37().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1gmjtbve37().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1gmjtbve37().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1gmjtbve37().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1gmjtbve37().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1gmjtbve37().b[2][0]++;
          cov_1gmjtbve37().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1gmjtbve37().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1gmjtbve37().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1gmjtbve37().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1gmjtbve37().s[11]++,
/* istanbul ignore next */
(cov_1gmjtbve37().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1gmjtbve37().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1gmjtbve37().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1gmjtbve37().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[3]++;
    cov_1gmjtbve37().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[4]++;
      cov_1gmjtbve37().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1gmjtbve37().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1gmjtbve37().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1gmjtbve37().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[6]++;
      cov_1gmjtbve37().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[7]++;
      cov_1gmjtbve37().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[8]++;
      cov_1gmjtbve37().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1gmjtbve37().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1gmjtbve37().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1gmjtbve37().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1gmjtbve37().s[23]++,
/* istanbul ignore next */
(cov_1gmjtbve37().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1gmjtbve37().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1gmjtbve37().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1gmjtbve37().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1gmjtbve37().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1gmjtbve37().f[10]++;
        cov_1gmjtbve37().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1gmjtbve37().b[9][0]++;
          cov_1gmjtbve37().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1gmjtbve37().b[9][1]++;
        }
        cov_1gmjtbve37().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1gmjtbve37().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1gmjtbve37().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1gmjtbve37().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1gmjtbve37().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[11]++;
    cov_1gmjtbve37().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[12]++;
    cov_1gmjtbve37().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[13]++;
      cov_1gmjtbve37().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[14]++;
    cov_1gmjtbve37().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1gmjtbve37().b[12][0]++;
      cov_1gmjtbve37().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1gmjtbve37().b[12][1]++;
    }
    cov_1gmjtbve37().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1gmjtbve37().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1gmjtbve37().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[18][0]++,
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[19][1]++,
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1gmjtbve37().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1gmjtbve37().b[15][0]++;
          cov_1gmjtbve37().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1gmjtbve37().b[15][1]++;
        }
        cov_1gmjtbve37().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1gmjtbve37().b[21][0]++;
          cov_1gmjtbve37().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1gmjtbve37().b[21][1]++;
        }
        cov_1gmjtbve37().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1gmjtbve37().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1gmjtbve37().b[22][1]++;
            cov_1gmjtbve37().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1gmjtbve37().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1gmjtbve37().b[22][2]++;
            cov_1gmjtbve37().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1gmjtbve37().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1gmjtbve37().b[22][3]++;
            cov_1gmjtbve37().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1gmjtbve37().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1gmjtbve37().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1gmjtbve37().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1gmjtbve37().b[22][4]++;
            cov_1gmjtbve37().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1gmjtbve37().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1gmjtbve37().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1gmjtbve37().b[22][5]++;
            cov_1gmjtbve37().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1gmjtbve37().b[23][0]++;
              cov_1gmjtbve37().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1gmjtbve37().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1gmjtbve37().b[23][1]++;
            }
            cov_1gmjtbve37().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1gmjtbve37().b[26][0]++;
              cov_1gmjtbve37().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1gmjtbve37().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1gmjtbve37().b[26][1]++;
            }
            cov_1gmjtbve37().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1gmjtbve37().b[28][0]++;
              cov_1gmjtbve37().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1gmjtbve37().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1gmjtbve37().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1gmjtbve37().b[28][1]++;
            }
            cov_1gmjtbve37().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1gmjtbve37().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1gmjtbve37().b[30][0]++;
              cov_1gmjtbve37().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1gmjtbve37().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1gmjtbve37().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1gmjtbve37().b[30][1]++;
            }
            cov_1gmjtbve37().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1gmjtbve37().b[32][0]++;
              cov_1gmjtbve37().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1gmjtbve37().b[32][1]++;
            }
            cov_1gmjtbve37().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1gmjtbve37().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1gmjtbve37().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1gmjtbve37().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1gmjtbve37().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1gmjtbve37().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1gmjtbve37().b[33][0]++;
      cov_1gmjtbve37().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1gmjtbve37().b[33][1]++;
    }
    cov_1gmjtbve37().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1gmjtbve37().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1gmjtbve37().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1gmjtbve37().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1gmjtbve37().s[79]++;
exports.DELETE = exports.PUT = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[80]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[81]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[82]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[83]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[84]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[85]++, require("@/lib/rateLimit"));
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[86]++, require("@/lib/services/consolidated-cache-service"));
var zod_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[87]++, require("zod"));
var auth_utils_1 =
/* istanbul ignore next */
(cov_1gmjtbve37().s[88]++, require("@/lib/auth-utils"));
var updateLearningPathSchema =
/* istanbul ignore next */
(cov_1gmjtbve37().s[89]++, zod_1.z.object({
  title: zod_1.z.string().min(1).max(200).optional(),
  description: zod_1.z.string().min(1).max(2000).optional(),
  difficulty: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  estimatedHours: zod_1.z.number().min(1).max(1000).optional(),
  category: zod_1.z.enum(['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP', 'UX_UI_DESIGN', 'PRODUCT_MANAGEMENT', 'DEVOPS']).optional(),
  prerequisites: zod_1.z.array(zod_1.z.string()).optional(),
  tags: zod_1.z.array(zod_1.z.string()).optional(),
  imageUrl: zod_1.z.string().url().optional(),
  isActive: zod_1.z.boolean().optional(),
  skillIds: zod_1.z.array(zod_1.z.string().uuid()).optional(),
  careerPathIds: zod_1.z.array(zod_1.z.string().uuid()).optional()
}));
// GET - Retrieve specific learning path
/* istanbul ignore next */
cov_1gmjtbve37().s[90]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1gmjtbve37().f[15]++;
  cov_1gmjtbve37().s[91]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[16]++;
    var params =
    /* istanbul ignore next */
    (cov_1gmjtbve37().s[92]++, _b.params);
    /* istanbul ignore next */
    cov_1gmjtbve37().s[93]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[17]++;
      cov_1gmjtbve37().s[94]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 200
      },
      // 200 requests per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_1gmjtbve37().f[18]++;
        cov_1gmjtbve37().s[95]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1gmjtbve37().f[19]++;
          var id, session, userId, cacheKey, cached, where, learningPath, transformedPath;
          var _a, _b;
          /* istanbul ignore next */
          cov_1gmjtbve37().s[96]++;
          return __generator(this, function (_c) {
            /* istanbul ignore next */
            cov_1gmjtbve37().f[20]++;
            cov_1gmjtbve37().s[97]++;
            switch (_c.label) {
              case 0:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[35][0]++;
                cov_1gmjtbve37().s[98]++;
                return [4 /*yield*/, params];
              case 1:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[35][1]++;
                cov_1gmjtbve37().s[99]++;
                id = _c.sent().id;
                /* istanbul ignore next */
                cov_1gmjtbve37().s[100]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 2:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[35][2]++;
                cov_1gmjtbve37().s[101]++;
                session = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[102]++;
                userId =
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[37][0]++, (_a =
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[39][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[39][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[38][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[38][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[37][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[36][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[36][1]++, _a.id);
                /* istanbul ignore next */
                cov_1gmjtbve37().s[103]++;
                cacheKey = "learning_path:".concat(id, ":").concat(
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[40][0]++, userId) ||
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[40][1]++, 'anonymous'));
                /* istanbul ignore next */
                cov_1gmjtbve37().s[104]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.get(cacheKey)];
              case 3:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[35][3]++;
                cov_1gmjtbve37().s[105]++;
                cached = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[106]++;
                if (cached) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[41][0]++;
                  cov_1gmjtbve37().s[107]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: true,
                    data: cached,
                    cached: true
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[41][1]++;
                }
                cov_1gmjtbve37().s[108]++;
                where = id.length === 36 ?
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[42][0]++, {
                  id: id
                }) :
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[42][1]++, {
                  slug: id
                });
                /* istanbul ignore next */
                cov_1gmjtbve37().s[109]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.findUnique({
                  where: where,
                  include: {
                    skills: true,
                    careerPaths: {
                      select: {
                        id: true,
                        name: true,
                        slug: true,
                        overview: true
                      }
                    },
                    steps: {
                      orderBy: {
                        stepOrder: 'asc'
                      },
                      include: {
                        resource: {
                          select: {
                            id: true,
                            title: true,
                            description: true,
                            type: true,
                            url: true,
                            author: true,
                            duration: true,
                            skillLevel: true
                          }
                        },
                        userProgress: userId ?
                        /* istanbul ignore next */
                        (cov_1gmjtbve37().b[43][0]++, {
                          where: {
                            userId: userId
                          },
                          select: {
                            id: true,
                            status: true,
                            startedAt: true,
                            completedAt: true,
                            timeSpent: true,
                            score: true,
                            notes: true
                          }
                        }) :
                        /* istanbul ignore next */
                        (cov_1gmjtbve37().b[43][1]++, false)
                      }
                    },
                    userPaths: userId ?
                    /* istanbul ignore next */
                    (cov_1gmjtbve37().b[44][0]++, {
                      where: {
                        userId: userId
                      },
                      select: {
                        id: true,
                        status: true,
                        startedAt: true,
                        completedAt: true,
                        lastAccessedAt: true,
                        currentStepId: true,
                        completedSteps: true,
                        totalSteps: true,
                        progressPercent: true,
                        totalTimeSpent: true,
                        notes: true,
                        rating: true,
                        review: true
                      }
                    }) :
                    /* istanbul ignore next */
                    (cov_1gmjtbve37().b[44][1]++, false),
                    _count: {
                      select: {
                        steps: true,
                        userPaths: true
                      }
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[35][4]++;
                cov_1gmjtbve37().s[110]++;
                learningPath = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[111]++;
                if (!learningPath) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[45][0]++;
                  cov_1gmjtbve37().s[112]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Learning path not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[45][1]++;
                }
                cov_1gmjtbve37().s[113]++;
                if (!learningPath.isActive) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[46][0]++;
                  cov_1gmjtbve37().s[114]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Learning path is not available'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[46][1]++;
                }
                cov_1gmjtbve37().s[115]++;
                transformedPath = __assign(__assign({}, learningPath), {
                  stepCount: learningPath._count.steps,
                  enrollmentCount: learningPath._count.userPaths,
                  userProgress:
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[47][0]++,
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[49][0]++, (_b = learningPath.userPaths) === null) ||
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[49][1]++, _b === void 0) ?
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[48][0]++, void 0) :
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[48][1]++, _b[0])) ||
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[47][1]++, null),
                  steps: learningPath.steps.map(function (step) {
                    /* istanbul ignore next */
                    cov_1gmjtbve37().f[21]++;
                    var _a;
                    /* istanbul ignore next */
                    cov_1gmjtbve37().s[116]++;
                    return __assign(__assign({}, step), {
                      userProgress:
                      /* istanbul ignore next */
                      (cov_1gmjtbve37().b[50][0]++,
                      /* istanbul ignore next */
                      (cov_1gmjtbve37().b[52][0]++, (_a = step.userProgress) === null) ||
                      /* istanbul ignore next */
                      (cov_1gmjtbve37().b[52][1]++, _a === void 0) ?
                      /* istanbul ignore next */
                      (cov_1gmjtbve37().b[51][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_1gmjtbve37().b[51][1]++, _a[0])) ||
                      /* istanbul ignore next */
                      (cov_1gmjtbve37().b[50][1]++, null)
                    });
                  }),
                  _count: undefined,
                  userPaths: undefined
                });
                // Cache for 5 minutes
                /* istanbul ignore next */
                cov_1gmjtbve37().s[117]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.set(cacheKey, transformedPath, {
                  ttl: 5 * 60 * 1000,
                  tags: ['learning_paths', "learning_path:".concat(id)]
                })];
              case 5:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[35][5]++;
                cov_1gmjtbve37().s[118]++;
                // Cache for 5 minutes
                _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[119]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: transformedPath
                })];
            }
          });
        });
      })];
    });
  });
});
// PUT - Update learning path (admin only)
/* istanbul ignore next */
cov_1gmjtbve37().s[120]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1gmjtbve37().f[22]++;
  cov_1gmjtbve37().s[121]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[23]++;
    var params =
    /* istanbul ignore next */
    (cov_1gmjtbve37().s[122]++, _b.params);
    /* istanbul ignore next */
    cov_1gmjtbve37().s[123]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[24]++;
      cov_1gmjtbve37().s[124]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 20
      },
      // 20 updates per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_1gmjtbve37().f[25]++;
        cov_1gmjtbve37().s[125]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1gmjtbve37().f[26]++;
          var session, isAdmin, id, body, validation, _a, title, description, difficulty, estimatedHours, category, prerequisites, tags, imageUrl, isActive, skillIds, careerPathIds, existingPath, slug, slugExists, updatedPath;
          var _b;
          /* istanbul ignore next */
          cov_1gmjtbve37().s[126]++;
          return __generator(this, function (_c) {
            /* istanbul ignore next */
            cov_1gmjtbve37().f[27]++;
            cov_1gmjtbve37().s[127]++;
            switch (_c.label) {
              case 0:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][0]++;
                cov_1gmjtbve37().s[128]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][1]++;
                cov_1gmjtbve37().s[129]++;
                session = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[130]++;
                if (!(
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[56][0]++, (_b =
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[58][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[58][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[57][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[57][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[56][1]++, _b === void 0) ?
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[55][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[55][1]++, _b.id))) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[54][0]++;
                  cov_1gmjtbve37().s[131]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[54][1]++;
                }
                cov_1gmjtbve37().s[132]++;
                return [4 /*yield*/, (0, auth_utils_1.isUserAdmin)(session.user.id)];
              case 2:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][2]++;
                cov_1gmjtbve37().s[133]++;
                isAdmin = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[134]++;
                if (!isAdmin) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[59][0]++;
                  cov_1gmjtbve37().s[135]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Admin access required'
                  }, {
                    status: 403
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[59][1]++;
                }
                cov_1gmjtbve37().s[136]++;
                return [4 /*yield*/, params];
              case 3:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][3]++;
                cov_1gmjtbve37().s[137]++;
                id = _c.sent().id;
                /* istanbul ignore next */
                cov_1gmjtbve37().s[138]++;
                return [4 /*yield*/, request.json()];
              case 4:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][4]++;
                cov_1gmjtbve37().s[139]++;
                body = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[140]++;
                validation = updateLearningPathSchema.safeParse(body);
                /* istanbul ignore next */
                cov_1gmjtbve37().s[141]++;
                if (!validation.success) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[60][0]++;
                  cov_1gmjtbve37().s[142]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Invalid request data',
                    details: validation.error.errors
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[60][1]++;
                }
                cov_1gmjtbve37().s[143]++;
                _a = validation.data, title = _a.title, description = _a.description, difficulty = _a.difficulty, estimatedHours = _a.estimatedHours, category = _a.category, prerequisites = _a.prerequisites, tags = _a.tags, imageUrl = _a.imageUrl, isActive = _a.isActive, skillIds = _a.skillIds, careerPathIds = _a.careerPathIds;
                /* istanbul ignore next */
                cov_1gmjtbve37().s[144]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.findUnique({
                  where: {
                    id: id
                  }
                })];
              case 5:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][5]++;
                cov_1gmjtbve37().s[145]++;
                existingPath = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[146]++;
                if (!existingPath) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[61][0]++;
                  cov_1gmjtbve37().s[147]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Learning path not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[61][1]++;
                }
                cov_1gmjtbve37().s[148]++;
                slug = existingPath.slug;
                /* istanbul ignore next */
                cov_1gmjtbve37().s[149]++;
                if (!(
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[63][0]++, title) &&
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[63][1]++, title !== existingPath.title))) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[62][0]++;
                  cov_1gmjtbve37().s[150]++;
                  return [3 /*break*/, 7];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[62][1]++;
                }
                cov_1gmjtbve37().s[151]++;
                slug = title.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').trim();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[152]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.findFirst({
                  where: {
                    slug: slug,
                    id: {
                      not: id
                    }
                  }
                })];
              case 6:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][6]++;
                cov_1gmjtbve37().s[153]++;
                slugExists = _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[154]++;
                if (slugExists) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[64][0]++;
                  cov_1gmjtbve37().s[155]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'A learning path with this title already exists'
                  }, {
                    status: 409
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[64][1]++;
                }
                cov_1gmjtbve37().s[156]++;
                _c.label = 7;
              case 7:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][7]++;
                cov_1gmjtbve37().s[157]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.update({
                  where: {
                    id: id
                  },
                  data: __assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign({},
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[65][0]++, title) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[65][1]++, {
                    title: title,
                    slug: slug
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[66][0]++, description) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[66][1]++, {
                    description: description
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[67][0]++, difficulty) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[67][1]++, {
                    difficulty: difficulty
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[68][0]++, estimatedHours) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[68][1]++, {
                    estimatedHours: estimatedHours
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[69][0]++, category) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[69][1]++, {
                    category: category
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[70][0]++, prerequisites !== undefined) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[70][1]++, {
                    prerequisites: prerequisites
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[71][0]++, tags !== undefined) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[71][1]++, {
                    tags: tags
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[72][0]++, imageUrl !== undefined) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[72][1]++, {
                    imageUrl: imageUrl
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[73][0]++, isActive !== undefined) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[73][1]++, {
                    isActive: isActive
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[74][0]++, skillIds) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[74][1]++, {
                    skills: {
                      set: [],
                      // Clear existing
                      connect: skillIds.map(function (id) {
                        /* istanbul ignore next */
                        cov_1gmjtbve37().f[28]++;
                        cov_1gmjtbve37().s[158]++;
                        return {
                          id: id
                        };
                      })
                    }
                  })),
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[75][0]++, careerPathIds) &&
                  /* istanbul ignore next */
                  (cov_1gmjtbve37().b[75][1]++, {
                    careerPaths: {
                      set: [],
                      // Clear existing
                      connect: careerPathIds.map(function (id) {
                        /* istanbul ignore next */
                        cov_1gmjtbve37().f[29]++;
                        cov_1gmjtbve37().s[159]++;
                        return {
                          id: id
                        };
                      })
                    }
                  })),
                  include: {
                    skills: true,
                    careerPaths: {
                      select: {
                        id: true,
                        name: true,
                        slug: true
                      }
                    },
                    _count: {
                      select: {
                        steps: true,
                        userPaths: true
                      }
                    }
                  }
                })];
              case 8:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][8]++;
                cov_1gmjtbve37().s[160]++;
                updatedPath = _c.sent();
                // Clear cache
                /* istanbul ignore next */
                cov_1gmjtbve37().s[161]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.invalidateByTags(['learning_paths', "learning_path:".concat(id)])];
              case 9:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[53][9]++;
                cov_1gmjtbve37().s[162]++;
                // Clear cache
                _c.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[163]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: __assign(__assign({}, updatedPath), {
                    stepCount: updatedPath._count.steps,
                    enrollmentCount: updatedPath._count.userPaths,
                    _count: undefined
                  }),
                  message: 'Learning path updated successfully'
                })];
            }
          });
        });
      })];
    });
  });
});
// DELETE - Delete learning path (admin only)
/* istanbul ignore next */
cov_1gmjtbve37().s[164]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1gmjtbve37().f[30]++;
  cov_1gmjtbve37().s[165]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_1gmjtbve37().f[31]++;
    var params =
    /* istanbul ignore next */
    (cov_1gmjtbve37().s[166]++, _b.params);
    /* istanbul ignore next */
    cov_1gmjtbve37().s[167]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1gmjtbve37().f[32]++;
      cov_1gmjtbve37().s[168]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 10
      },
      // 10 deletions per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_1gmjtbve37().f[33]++;
        cov_1gmjtbve37().s[169]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1gmjtbve37().f[34]++;
          var session, isAdmin, id, learningPath;
          var _a;
          /* istanbul ignore next */
          cov_1gmjtbve37().s[170]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_1gmjtbve37().f[35]++;
            cov_1gmjtbve37().s[171]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][0]++;
                cov_1gmjtbve37().s[172]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][1]++;
                cov_1gmjtbve37().s[173]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[174]++;
                if (!(
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[79][0]++, (_a =
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[81][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[81][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[80][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[80][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[79][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[78][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1gmjtbve37().b[78][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[77][0]++;
                  cov_1gmjtbve37().s[175]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[77][1]++;
                }
                cov_1gmjtbve37().s[176]++;
                return [4 /*yield*/, (0, auth_utils_1.isUserAdmin)(session.user.id)];
              case 2:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][2]++;
                cov_1gmjtbve37().s[177]++;
                isAdmin = _b.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[178]++;
                if (!isAdmin) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[82][0]++;
                  cov_1gmjtbve37().s[179]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Admin access required'
                  }, {
                    status: 403
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[82][1]++;
                }
                cov_1gmjtbve37().s[180]++;
                return [4 /*yield*/, params];
              case 3:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][3]++;
                cov_1gmjtbve37().s[181]++;
                id = _b.sent().id;
                /* istanbul ignore next */
                cov_1gmjtbve37().s[182]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.findUnique({
                  where: {
                    id: id
                  },
                  include: {
                    _count: {
                      select: {
                        userPaths: true
                      }
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][4]++;
                cov_1gmjtbve37().s[183]++;
                learningPath = _b.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[184]++;
                if (!learningPath) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[83][0]++;
                  cov_1gmjtbve37().s[185]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Learning path not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[83][1]++;
                }
                cov_1gmjtbve37().s[186]++;
                if (!(learningPath._count.userPaths > 0)) {
                  /* istanbul ignore next */
                  cov_1gmjtbve37().b[84][0]++;
                  cov_1gmjtbve37().s[187]++;
                  return [3 /*break*/, 6];
                } else
                /* istanbul ignore next */
                {
                  cov_1gmjtbve37().b[84][1]++;
                }
                cov_1gmjtbve37().s[188]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.update({
                  where: {
                    id: id
                  },
                  data: {
                    isActive: false
                  }
                })];
              case 5:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][5]++;
                cov_1gmjtbve37().s[189]++;
                _b.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[190]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  message: 'Learning path deactivated due to existing enrollments'
                })];
              case 6:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][6]++;
                cov_1gmjtbve37().s[191]++;
                // Delete learning path (this will cascade to steps)
                return [4 /*yield*/, prisma_1.prisma.learningPath.delete({
                  where: {
                    id: id
                  }
                })];
              case 7:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][7]++;
                cov_1gmjtbve37().s[192]++;
                // Delete learning path (this will cascade to steps)
                _b.sent();
                // Clear cache
                /* istanbul ignore next */
                cov_1gmjtbve37().s[193]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.invalidateByTags(['learning_paths', "learning_path:".concat(id)])];
              case 8:
                /* istanbul ignore next */
                cov_1gmjtbve37().b[76][8]++;
                cov_1gmjtbve37().s[194]++;
                // Clear cache
                _b.sent();
                /* istanbul ignore next */
                cov_1gmjtbve37().s[195]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  message: 'Learning path deleted successfully'
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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