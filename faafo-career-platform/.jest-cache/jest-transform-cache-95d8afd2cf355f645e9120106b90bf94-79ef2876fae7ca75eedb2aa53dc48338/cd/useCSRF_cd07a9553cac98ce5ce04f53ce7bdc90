db4393b9f2d76ce29833de0c5f68d5c9
"use strict";

/* istanbul ignore next */
function cov_xpy7hlm9u() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useCSRF.ts";
  var hash = "79943a297801e4a6d71830fa7d5497cd12142107";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useCSRF.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 26
        }
      },
      "80": {
        start: {
          line: 51,
          column: 14
        },
        end: {
          line: 51,
          column: 30
        }
      },
      "81": {
        start: {
          line: 53,
          column: 16
        },
        end: {
          line: 53,
          column: 20
        }
      },
      "82": {
        start: {
          line: 54,
          column: 13
        },
        end: {
          line: 54,
          column: 40
        }
      },
      "83": {
        start: {
          line: 54,
          column: 54
        },
        end: {
          line: 54,
          column: 59
        }
      },
      "84": {
        start: {
          line: 54,
          column: 76
        },
        end: {
          line: 54,
          column: 81
        }
      },
      "85": {
        start: {
          line: 55,
          column: 13
        },
        end: {
          line: 55,
          column: 40
        }
      },
      "86": {
        start: {
          line: 55,
          column: 54
        },
        end: {
          line: 55,
          column: 59
        }
      },
      "87": {
        start: {
          line: 55,
          column: 76
        },
        end: {
          line: 55,
          column: 81
        }
      },
      "88": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 40
        }
      },
      "89": {
        start: {
          line: 56,
          column: 50
        },
        end: {
          line: 56,
          column: 55
        }
      },
      "90": {
        start: {
          line: 56,
          column: 68
        },
        end: {
          line: 56,
          column: 73
        }
      },
      "91": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 90,
          column: 11
        }
      },
      "92": {
        start: {
          line: 58,
          column: 29
        },
        end: {
          line: 88,
          column: 13
        }
      },
      "93": {
        start: {
          line: 58,
          column: 43
        },
        end: {
          line: 88,
          column: 11
        }
      },
      "94": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 87,
          column: 15
        }
      },
      "95": {
        start: {
          line: 61,
          column: 16
        },
        end: {
          line: 86,
          column: 17
        }
      },
      "96": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 63,
          column: 51
        }
      },
      "97": {
        start: {
          line: 64,
          column: 24
        },
        end: {
          line: 64,
          column: 43
        }
      },
      "98": {
        start: {
          line: 65,
          column: 24
        },
        end: {
          line: 65,
          column: 39
        }
      },
      "99": {
        start: {
          line: 66,
          column: 24
        },
        end: {
          line: 66,
          column: 71
        }
      },
      "100": {
        start: {
          line: 68,
          column: 24
        },
        end: {
          line: 68,
          column: 45
        }
      },
      "101": {
        start: {
          line: 69,
          column: 24
        },
        end: {
          line: 71,
          column: 25
        }
      },
      "102": {
        start: {
          line: 70,
          column: 28
        },
        end: {
          line: 70,
          column: 74
        }
      },
      "103": {
        start: {
          line: 72,
          column: 24
        },
        end: {
          line: 72,
          column: 62
        }
      },
      "104": {
        start: {
          line: 74,
          column: 24
        },
        end: {
          line: 74,
          column: 41
        }
      },
      "105": {
        start: {
          line: 75,
          column: 24
        },
        end: {
          line: 75,
          column: 53
        }
      },
      "106": {
        start: {
          line: 76,
          column: 24
        },
        end: {
          line: 76,
          column: 48
        }
      },
      "107": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 42
        }
      },
      "108": {
        start: {
          line: 79,
          column: 24
        },
        end: {
          line: 79,
          column: 91
        }
      },
      "109": {
        start: {
          line: 80,
          column: 24
        },
        end: {
          line: 80,
          column: 76
        }
      },
      "110": {
        start: {
          line: 81,
          column: 24
        },
        end: {
          line: 81,
          column: 48
        }
      },
      "111": {
        start: {
          line: 83,
          column: 24
        },
        end: {
          line: 83,
          column: 44
        }
      },
      "112": {
        start: {
          line: 84,
          column: 24
        },
        end: {
          line: 84,
          column: 50
        }
      },
      "113": {
        start: {
          line: 85,
          column: 28
        },
        end: {
          line: 85,
          column: 50
        }
      },
      "114": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 25
        }
      },
      "115": {
        start: {
          line: 91,
          column: 21
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "116": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 69
        }
      },
      "117": {
        start: {
          line: 92,
          column: 44
        },
        end: {
          line: 92,
          column: 67
        }
      },
      "118": {
        start: {
          line: 93,
          column: 22
        },
        end: {
          line: 93,
          column: 89
        }
      },
      "119": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 96,
          column: 9
        }
      },
      "120": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 48
        }
      },
      "121": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 97,
          column: 23
        }
      },
      "122": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 104,
          column: 6
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "useCSRF",
        decl: {
          start: {
            line: 52,
            column: 9
          },
          end: {
            line: 52,
            column: 16
          }
        },
        loc: {
          start: {
            line: 52,
            column: 19
          },
          end: {
            line: 105,
            column: 1
          }
        },
        line: 52
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 57,
            column: 27
          },
          end: {
            line: 57,
            column: 28
          }
        },
        loc: {
          start: {
            line: 57,
            column: 39
          },
          end: {
            line: 90,
            column: 5
          }
        },
        line: 57
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 58,
            column: 29
          },
          end: {
            line: 58,
            column: 30
          }
        },
        loc: {
          start: {
            line: 58,
            column: 41
          },
          end: {
            line: 88,
            column: 13
          }
        },
        line: 58
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 58,
            column: 83
          },
          end: {
            line: 58,
            column: 84
          }
        },
        loc: {
          start: {
            line: 58,
            column: 95
          },
          end: {
            line: 88,
            column: 9
          }
        },
        line: 58
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 60,
            column: 37
          },
          end: {
            line: 60,
            column: 38
          }
        },
        loc: {
          start: {
            line: 60,
            column: 51
          },
          end: {
            line: 87,
            column: 13
          }
        },
        line: 60
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 91,
            column: 21
          },
          end: {
            line: 91,
            column: 22
          }
        },
        loc: {
          start: {
            line: 91,
            column: 50
          },
          end: {
            line: 98,
            column: 5
          }
        },
        line: 91
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 61,
            column: 16
          },
          end: {
            line: 86,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 62,
            column: 20
          },
          end: {
            line: 66,
            column: 71
          }
        }, {
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 72,
            column: 62
          }
        }, {
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 76,
            column: 48
          }
        }, {
          start: {
            line: 77,
            column: 20
          },
          end: {
            line: 81,
            column: 48
          }
        }, {
          start: {
            line: 82,
            column: 20
          },
          end: {
            line: 84,
            column: 50
          }
        }, {
          start: {
            line: 85,
            column: 20
          },
          end: {
            line: 85,
            column: 50
          }
        }],
        line: 61
      },
      "36": {
        loc: {
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 71,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 71,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "37": {
        loc: {
          start: {
            line: 79,
            column: 33
          },
          end: {
            line: 79,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 79,
            column: 58
          },
          end: {
            line: 79,
            column: 71
          }
        }, {
          start: {
            line: 79,
            column: 74
          },
          end: {
            line: 79,
            column: 89
          }
        }],
        line: 79
      },
      "38": {
        loc: {
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 92,
            column: 69
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 92,
            column: 69
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "39": {
        loc: {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useCSRF.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,0BAgDC;AAlDD,+BAA4C;AAE5C,SAAgB,OAAO;IAAvB,iBAgDC;IA/CO,IAAA,KAA4B,IAAA,gBAAQ,EAAgB,IAAI,CAAC,EAAxD,SAAS,QAAA,EAAE,YAAY,QAAiC,CAAC;IAC1D,IAAA,KAA4B,IAAA,gBAAQ,EAAC,IAAI,CAAC,EAAzC,SAAS,QAAA,EAAE,YAAY,QAAkB,CAAC;IAC3C,IAAA,KAAoB,IAAA,gBAAQ,EAAgB,IAAI,CAAC,EAAhD,KAAK,QAAA,EAAE,QAAQ,QAAiC,CAAC;IAExD,IAAA,iBAAS,EAAC;QACR,IAAM,cAAc,GAAG;;;;;;wBAEnB,YAAY,CAAC,IAAI,CAAC,CAAC;wBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAEE,qBAAM,KAAK,CAAC,iBAAiB,CAAC,EAAA;;wBAAzC,QAAQ,GAAG,SAA8B;wBAC/C,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;4BACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;wBAChD,CAAC;wBAEY,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAClC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;;;wBAE7B,QAAQ,CAAC,KAAG,YAAY,KAAK,CAAC,CAAC,CAAC,KAAG,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;wBAC/D,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAG,CAAC,CAAC;;;wBAElD,YAAY,CAAC,KAAK,CAAC,CAAC;;;;;aAEvB,CAAC;QAEF,cAAc,EAAE,CAAC;IACnB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,UAAU,GAAG,UAAC,iBAA8C;QAA9C,kCAAA,EAAA,sBAA8C;QAChE,IAAM,OAAO,cACX,cAAc,EAAE,kBAAkB,IAC/B,iBAAiB,CACrB,CAAC;QAEF,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC;QACtC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,OAAO;QACL,SAAS,WAAA;QACT,SAAS,WAAA;QACT,KAAK,OAAA;QACL,UAAU,YAAA;KACX,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useCSRF.ts"],
      sourcesContent: ["import { useState, useEffect } from 'react';\n\nexport function useCSRF() {\n  const [csrfToken, setCsrfToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchCSRFToken = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        \n        const response = await fetch('/api/csrf-token');\n        if (!response.ok) {\n          throw new Error('Failed to fetch CSRF token');\n        }\n        \n        const data = await response.json();\n        setCsrfToken(data.csrfToken);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Unknown error');\n        console.error('Failed to fetch CSRF token:', err);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchCSRFToken();\n  }, []);\n\n  const getHeaders = (additionalHeaders: Record<string, string> = {}) => {\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json',\n      ...additionalHeaders,\n    };\n\n    if (csrfToken) {\n      headers['x-csrf-token'] = csrfToken;\n    }\n\n    return headers;\n  };\n\n  return {\n    csrfToken,\n    isLoading,\n    error,\n    getHeaders,\n  };\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "79943a297801e4a6d71830fa7d5497cd12142107"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_xpy7hlm9u = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xpy7hlm9u();
var __assign =
/* istanbul ignore next */
(cov_xpy7hlm9u().s[0]++,
/* istanbul ignore next */
(cov_xpy7hlm9u().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_xpy7hlm9u().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_xpy7hlm9u().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_xpy7hlm9u().f[0]++;
  cov_xpy7hlm9u().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_xpy7hlm9u().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_xpy7hlm9u().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[1]++;
    cov_xpy7hlm9u().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_xpy7hlm9u().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_xpy7hlm9u().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_xpy7hlm9u().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_xpy7hlm9u().b[2][0]++;
          cov_xpy7hlm9u().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_xpy7hlm9u().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_xpy7hlm9u().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_xpy7hlm9u().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_xpy7hlm9u().s[11]++,
/* istanbul ignore next */
(cov_xpy7hlm9u().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_xpy7hlm9u().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_xpy7hlm9u().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_xpy7hlm9u().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[3]++;
    cov_xpy7hlm9u().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().f[4]++;
      cov_xpy7hlm9u().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_xpy7hlm9u().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_xpy7hlm9u().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_xpy7hlm9u().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().f[6]++;
      cov_xpy7hlm9u().s[15]++;
      try {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().f[7]++;
      cov_xpy7hlm9u().s[18]++;
      try {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().f[8]++;
      cov_xpy7hlm9u().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_xpy7hlm9u().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_xpy7hlm9u().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_xpy7hlm9u().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_xpy7hlm9u().s[23]++,
/* istanbul ignore next */
(cov_xpy7hlm9u().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_xpy7hlm9u().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_xpy7hlm9u().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_xpy7hlm9u().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_xpy7hlm9u().f[10]++;
        cov_xpy7hlm9u().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_xpy7hlm9u().b[9][0]++;
          cov_xpy7hlm9u().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_xpy7hlm9u().b[9][1]++;
        }
        cov_xpy7hlm9u().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_xpy7hlm9u().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_xpy7hlm9u().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_xpy7hlm9u().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[11]++;
    cov_xpy7hlm9u().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[12]++;
    cov_xpy7hlm9u().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().f[13]++;
      cov_xpy7hlm9u().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[14]++;
    cov_xpy7hlm9u().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().b[12][0]++;
      cov_xpy7hlm9u().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_xpy7hlm9u().b[12][1]++;
    }
    cov_xpy7hlm9u().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_xpy7hlm9u().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().s[36]++;
      try {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[18][0]++,
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[19][1]++,
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_xpy7hlm9u().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_xpy7hlm9u().b[15][0]++;
          cov_xpy7hlm9u().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_xpy7hlm9u().b[15][1]++;
        }
        cov_xpy7hlm9u().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_xpy7hlm9u().b[21][0]++;
          cov_xpy7hlm9u().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_xpy7hlm9u().b[21][1]++;
        }
        cov_xpy7hlm9u().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_xpy7hlm9u().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_xpy7hlm9u().b[22][1]++;
            cov_xpy7hlm9u().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_xpy7hlm9u().b[22][2]++;
            cov_xpy7hlm9u().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_xpy7hlm9u().b[22][3]++;
            cov_xpy7hlm9u().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_xpy7hlm9u().b[22][4]++;
            cov_xpy7hlm9u().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_xpy7hlm9u().b[22][5]++;
            cov_xpy7hlm9u().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[23][0]++;
              cov_xpy7hlm9u().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_xpy7hlm9u().b[23][1]++;
            }
            cov_xpy7hlm9u().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[26][0]++;
              cov_xpy7hlm9u().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xpy7hlm9u().b[26][1]++;
            }
            cov_xpy7hlm9u().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[28][0]++;
              cov_xpy7hlm9u().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xpy7hlm9u().b[28][1]++;
            }
            cov_xpy7hlm9u().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_xpy7hlm9u().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[30][0]++;
              cov_xpy7hlm9u().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xpy7hlm9u().b[30][1]++;
            }
            cov_xpy7hlm9u().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[32][0]++;
              cov_xpy7hlm9u().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_xpy7hlm9u().b[32][1]++;
            }
            cov_xpy7hlm9u().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_xpy7hlm9u().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_xpy7hlm9u().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().b[33][0]++;
      cov_xpy7hlm9u().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_xpy7hlm9u().b[33][1]++;
    }
    cov_xpy7hlm9u().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_xpy7hlm9u().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_xpy7hlm9u().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_xpy7hlm9u().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_xpy7hlm9u().s[79]++;
exports.useCSRF = useCSRF;
var react_1 =
/* istanbul ignore next */
(cov_xpy7hlm9u().s[80]++, require("react"));
function useCSRF() {
  /* istanbul ignore next */
  cov_xpy7hlm9u().f[15]++;
  var _this =
  /* istanbul ignore next */
  (cov_xpy7hlm9u().s[81]++, this);
  var _a =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[82]++, (0, react_1.useState)(null)),
    csrfToken =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[83]++, _a[0]),
    setCsrfToken =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[84]++, _a[1]);
  var _b =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[85]++, (0, react_1.useState)(true)),
    isLoading =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[86]++, _b[0]),
    setIsLoading =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[87]++, _b[1]);
  var _c =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[88]++, (0, react_1.useState)(null)),
    error =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[89]++, _c[0]),
    setError =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[90]++, _c[1]);
  /* istanbul ignore next */
  cov_xpy7hlm9u().s[91]++;
  (0, react_1.useEffect)(function () {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[16]++;
    cov_xpy7hlm9u().s[92]++;
    var fetchCSRFToken = function () {
      /* istanbul ignore next */
      cov_xpy7hlm9u().f[17]++;
      cov_xpy7hlm9u().s[93]++;
      return __awaiter(_this, void 0, void 0, function () {
        /* istanbul ignore next */
        cov_xpy7hlm9u().f[18]++;
        var response, data, err_1;
        /* istanbul ignore next */
        cov_xpy7hlm9u().s[94]++;
        return __generator(this, function (_a) {
          /* istanbul ignore next */
          cov_xpy7hlm9u().f[19]++;
          cov_xpy7hlm9u().s[95]++;
          switch (_a.label) {
            case 0:
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[35][0]++;
              cov_xpy7hlm9u().s[96]++;
              _a.trys.push([0, 3, 4, 5]);
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[97]++;
              setIsLoading(true);
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[98]++;
              setError(null);
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[99]++;
              return [4 /*yield*/, fetch('/api/csrf-token')];
            case 1:
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[35][1]++;
              cov_xpy7hlm9u().s[100]++;
              response = _a.sent();
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[101]++;
              if (!response.ok) {
                /* istanbul ignore next */
                cov_xpy7hlm9u().b[36][0]++;
                cov_xpy7hlm9u().s[102]++;
                throw new Error('Failed to fetch CSRF token');
              } else
              /* istanbul ignore next */
              {
                cov_xpy7hlm9u().b[36][1]++;
              }
              cov_xpy7hlm9u().s[103]++;
              return [4 /*yield*/, response.json()];
            case 2:
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[35][2]++;
              cov_xpy7hlm9u().s[104]++;
              data = _a.sent();
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[105]++;
              setCsrfToken(data.csrfToken);
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[106]++;
              return [3 /*break*/, 5];
            case 3:
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[35][3]++;
              cov_xpy7hlm9u().s[107]++;
              err_1 = _a.sent();
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[108]++;
              setError(err_1 instanceof Error ?
              /* istanbul ignore next */
              (cov_xpy7hlm9u().b[37][0]++, err_1.message) :
              /* istanbul ignore next */
              (cov_xpy7hlm9u().b[37][1]++, 'Unknown error'));
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[109]++;
              console.error('Failed to fetch CSRF token:', err_1);
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[110]++;
              return [3 /*break*/, 5];
            case 4:
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[35][4]++;
              cov_xpy7hlm9u().s[111]++;
              setIsLoading(false);
              /* istanbul ignore next */
              cov_xpy7hlm9u().s[112]++;
              return [7 /*endfinally*/];
            case 5:
              /* istanbul ignore next */
              cov_xpy7hlm9u().b[35][5]++;
              cov_xpy7hlm9u().s[113]++;
              return [2 /*return*/];
          }
        });
      });
    };
    /* istanbul ignore next */
    cov_xpy7hlm9u().s[114]++;
    fetchCSRFToken();
  }, []);
  /* istanbul ignore next */
  cov_xpy7hlm9u().s[115]++;
  var getHeaders = function (additionalHeaders) {
    /* istanbul ignore next */
    cov_xpy7hlm9u().f[20]++;
    cov_xpy7hlm9u().s[116]++;
    if (additionalHeaders === void 0) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().b[38][0]++;
      cov_xpy7hlm9u().s[117]++;
      additionalHeaders = {};
    } else
    /* istanbul ignore next */
    {
      cov_xpy7hlm9u().b[38][1]++;
    }
    var headers =
    /* istanbul ignore next */
    (cov_xpy7hlm9u().s[118]++, __assign({
      'Content-Type': 'application/json'
    }, additionalHeaders));
    /* istanbul ignore next */
    cov_xpy7hlm9u().s[119]++;
    if (csrfToken) {
      /* istanbul ignore next */
      cov_xpy7hlm9u().b[39][0]++;
      cov_xpy7hlm9u().s[120]++;
      headers['x-csrf-token'] = csrfToken;
    } else
    /* istanbul ignore next */
    {
      cov_xpy7hlm9u().b[39][1]++;
    }
    cov_xpy7hlm9u().s[121]++;
    return headers;
  };
  /* istanbul ignore next */
  cov_xpy7hlm9u().s[122]++;
  return {
    csrfToken: csrfToken,
    isLoading: isLoading,
    error: error,
    getHeaders: getHeaders
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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