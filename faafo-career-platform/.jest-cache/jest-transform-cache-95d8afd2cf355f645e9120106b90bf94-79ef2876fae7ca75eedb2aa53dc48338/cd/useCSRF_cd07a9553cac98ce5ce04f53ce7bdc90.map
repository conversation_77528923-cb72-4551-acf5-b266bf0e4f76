{"version": 3, "names": ["exports", "useCSRF", "react_1", "cov_xpy7hlm9u", "s", "require", "f", "_this", "_a", "useState", "csrfToken", "setCsrfToken", "_b", "isLoading", "setIsLoading", "_c", "error", "setError", "useEffect", "fetchCSRFToken", "__awaiter", "fetch", "response", "sent", "ok", "b", "Error", "json", "data", "err_1", "message", "console", "getHeaders", "additionalHeaders", "headers", "__assign"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useCSRF.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\nexport function useCSRF() {\n  const [csrfToken, setCsrfToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchCSRFToken = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        \n        const response = await fetch('/api/csrf-token');\n        if (!response.ok) {\n          throw new Error('Failed to fetch CSRF token');\n        }\n        \n        const data = await response.json();\n        setCsrfToken(data.csrfToken);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Unknown error');\n        console.error('Failed to fetch CSRF token:', err);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchCSRFToken();\n  }, []);\n\n  const getHeaders = (additionalHeaders: Record<string, string> = {}) => {\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json',\n      ...additionalHeaders,\n    };\n\n    if (csrfToken) {\n      headers['x-csrf-token'] = csrfToken;\n    }\n\n    return headers;\n  };\n\n  return {\n    csrfToken,\n    isLoading,\n    error,\n    getHeaders,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEAA,OAAA,CAAAC,OAAA,GAAAA,OAAA;AAFA,IAAAC,OAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA,SAAgBJ,OAAOA,CAAA;EAAA;EAAAE,aAAA,GAAAG,CAAA;EAAvB,IAAAC,KAAA;EAAA;EAAA,CAAAJ,aAAA,GAAAC,CAAA;EACQ,IAAAI,EAAA;IAAA;IAAA,CAAAL,aAAA,GAAAC,CAAA,QAA4B,IAAAF,OAAA,CAAAO,QAAQ,EAAgB,IAAI,CAAC;IAAxDC,SAAS;IAAA;IAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAI,EAAA;IAAEG,YAAY;IAAA;IAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAI,EAAA,GAAiC;EACzD,IAAAI,EAAA;IAAA;IAAA,CAAAT,aAAA,GAAAC,CAAA,QAA4B,IAAAF,OAAA,CAAAO,QAAQ,EAAC,IAAI,CAAC;IAAzCI,SAAS;IAAA;IAAA,CAAAV,aAAA,GAAAC,CAAA,QAAAQ,EAAA;IAAEE,YAAY;IAAA;IAAA,CAAAX,aAAA,GAAAC,CAAA,QAAAQ,EAAA,GAAkB;EAC1C,IAAAG,EAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAC,CAAA,QAAoB,IAAAF,OAAA,CAAAO,QAAQ,EAAgB,IAAI,CAAC;IAAhDO,KAAK;IAAA;IAAA,CAAAb,aAAA,GAAAC,CAAA,QAAAW,EAAA;IAAEE,QAAQ;IAAA;IAAA,CAAAd,aAAA,GAAAC,CAAA,QAAAW,EAAA,GAAiC;EAAC;EAAAZ,aAAA,GAAAC,CAAA;EAExD,IAAAF,OAAA,CAAAgB,SAAS,EAAC;IAAA;IAAAf,aAAA,GAAAG,CAAA;IAAAH,aAAA,GAAAC,CAAA;IACR,IAAMe,cAAc,GAAG,SAAAA,CAAA;MAAA;MAAAhB,aAAA,GAAAG,CAAA;MAAAH,aAAA,GAAAC,CAAA;MAAA,OAAAgB,SAAA,CAAAb,KAAA;QAAA;QAAAJ,aAAA,GAAAG,CAAA;;;;;;;;;;;;;;;;cAEnBQ,YAAY,CAAC,IAAI,CAAC;cAAC;cAAAX,aAAA,GAAAC,CAAA;cACnBa,QAAQ,CAAC,IAAI,CAAC;cAAC;cAAAd,aAAA,GAAAC,CAAA;cAEE,qBAAMiB,KAAK,CAAC,iBAAiB,CAAC;;;;;cAAzCC,QAAQ,GAAGd,EAAA,CAAAe,IAAA,EAA8B;cAAA;cAAApB,aAAA,GAAAC,CAAA;cAC/C,IAAI,CAACkB,QAAQ,CAACE,EAAE,EAAE;gBAAA;gBAAArB,aAAA,GAAAsB,CAAA;gBAAAtB,aAAA,GAAAC,CAAA;gBAChB,MAAM,IAAIsB,KAAK,CAAC,4BAA4B,CAAC;cAC/C,CAAC;cAAA;cAAA;gBAAAvB,aAAA,GAAAsB,CAAA;cAAA;cAAAtB,aAAA,GAAAC,CAAA;cAEY,qBAAMkB,QAAQ,CAACK,IAAI,EAAE;;;;;cAA5BC,IAAI,GAAGpB,EAAA,CAAAe,IAAA,EAAqB;cAAA;cAAApB,aAAA,GAAAC,CAAA;cAClCO,YAAY,CAACiB,IAAI,CAAClB,SAAS,CAAC;cAAC;cAAAP,aAAA,GAAAC,CAAA;;;;;;;;;cAE7Ba,QAAQ,CAACY,KAAG,YAAYH,KAAK;cAAA;cAAA,CAAAvB,aAAA,GAAAsB,CAAA,WAAGI,KAAG,CAACC,OAAO;cAAA;cAAA,CAAA3B,aAAA,GAAAsB,CAAA,WAAG,eAAe,EAAC;cAAC;cAAAtB,aAAA,GAAAC,CAAA;cAC/D2B,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAEa,KAAG,CAAC;cAAC;cAAA1B,aAAA,GAAAC,CAAA;;;;;;cAElDU,YAAY,CAAC,KAAK,CAAC;cAAC;cAAAX,aAAA,GAAAC,CAAA;;;;;;;;;;KAEvB;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAEFe,cAAc,EAAE;EAClB,CAAC,EAAE,EAAE,CAAC;EAAC;EAAAhB,aAAA,GAAAC,CAAA;EAEP,IAAM4B,UAAU,GAAG,SAAAA,CAACC,iBAA8C;IAAA;IAAA9B,aAAA,GAAAG,CAAA;IAAAH,aAAA,GAAAC,CAAA;IAA9C,IAAA6B,iBAAA;MAAA;MAAA9B,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAC,CAAA;MAAA6B,iBAAA,KAA8C;IAAA;IAAA;IAAA;MAAA9B,aAAA,GAAAsB,CAAA;IAAA;IAChE,IAAMS,OAAO;IAAA;IAAA,CAAA/B,aAAA,GAAAC,CAAA,SAAA+B,QAAA;MACX,cAAc,EAAE;IAAkB,GAC/BF,iBAAiB,CACrB;IAAC;IAAA9B,aAAA,GAAAC,CAAA;IAEF,IAAIM,SAAS,EAAE;MAAA;MAAAP,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAC,CAAA;MACb8B,OAAO,CAAC,cAAc,CAAC,GAAGxB,SAAS;IACrC,CAAC;IAAA;IAAA;MAAAP,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IAED,OAAO8B,OAAO;EAChB,CAAC;EAAC;EAAA/B,aAAA,GAAAC,CAAA;EAEF,OAAO;IACLM,SAAS,EAAAA,SAAA;IACTG,SAAS,EAAAA,SAAA;IACTG,KAAK,EAAAA,KAAA;IACLgB,UAAU,EAAAA;GACX;AACH", "ignoreList": []}