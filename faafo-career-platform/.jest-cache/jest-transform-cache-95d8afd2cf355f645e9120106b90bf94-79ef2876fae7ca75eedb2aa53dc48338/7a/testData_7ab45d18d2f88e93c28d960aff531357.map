{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/fixtures/testData.ts", "mappings": ";AAAA,4CAA4C;AAC5C,0DAA0D;;;AAE7C,QAAA,SAAS,GAAG;IACvB,SAAS,EAAE;QACT,KAAK,EAAE,sBAAsB;QAC7B,QAAQ,EAAE,kBAAkB;QAC5B,IAAI,EAAE,WAAW;KAClB;IACD,SAAS,EAAE;QACT,KAAK,EAAE,mBAAmB;QAC1B,QAAQ,EAAE,mBAAmB;QAC7B,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE;QACd,KAAK,EAAE,wBAAwB;QAC/B,mBAAmB;QACnB,IAAI,EAAE,iBAAiB;KACxB;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,eAAe;QACtB,QAAQ,EAAE,kBAAkB;QAC5B,IAAI,EAAE,oBAAoB;KAC3B;CACF,CAAC;AAEW,QAAA,eAAe,GAAG;IAC7B,kBAAkB,EAAE;QAClB,+CAA+C;QAC/C,wBAAwB,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,cAAc,CAAC;QACjF,yBAAyB,EAAE,oBAAoB;QAC/C,gBAAgB,EAAE,MAAM;QAExB,yCAAyC;QACzC,iBAAiB,EAAE,CAAC;QACpB,8BAA8B,EAAE,CAAC;QACjC,0BAA0B,EAAE,gBAAgB;QAC5C,6BAA6B,EAAE,mBAAmB;QAClD,yBAAyB,EAAE,CAAC;QAE5B,0CAA0C;QAC1C,UAAU,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,oBAAoB,CAAC;QACtE,0BAA0B,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;QACrE,mBAAmB,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;QAE5D,sCAAsC;QACtC,WAAW,EAAE,CAAC,UAAU,EAAE,mBAAmB,EAAE,qBAAqB,CAAC;QACrE,2BAA2B,EAAE,aAAa;QAC1C,oBAAoB,EAAE,YAAY;QAClC,cAAc,EAAE,CAAC;QAEjB,sCAAsC;QACtC,mBAAmB,EAAE,aAAa;QAClC,iBAAiB,EAAE,CAAC,uBAAuB,EAAE,gBAAgB,CAAC;QAC9D,cAAc,EAAE,CAAC;QACjB,wBAAwB,EAAE,gBAAgB;QAC1C,gBAAgB,EAAE,CAAC;QAEnB,8BAA8B;QAC9B,qBAAqB,EAAE,qJAAqJ;QAC5K,kBAAkB,EAAE,0GAA0G;QAC9H,mBAAmB,EAAE,6GAA6G;KACnI;IACD,iBAAiB,EAAE;QACjB,wBAAwB,EAAE,CAAC,gBAAgB,CAAC;QAC5C,0BAA0B,EAAE,gBAAgB;QAC5C,yBAAyB,EAAE,CAAC;QAC5B,2BAA2B,EAAE,QAAQ;KACtC;IACD,iBAAiB,EAAE;QACjB,wBAAwB,EAAE,iCAAiC;QAC3D,0BAA0B,EAAE,eAAe;QAC3C,2BAA2B,EAAE,IAAI;KAClC;CACF,CAAC;AAEW,QAAA,qBAAqB,GAAG;IACnC,mBAAmB,EAAE;QACnB,KAAK,EAAE,8BAA8B;QACrC,WAAW,EAAE,6DAA6D;QAC1E,GAAG,EAAE,qCAAqC;QAC1C,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,eAAe;QACzB,UAAU,EAAE,UAAU;QACtB,MAAM,EAAE,iBAAiB;QACzB,QAAQ,EAAE,UAAU;QACpB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,YAAY;KACrB;IACD,kBAAkB,EAAE;QAClB,KAAK,EAAE,kCAAkC;QACzC,WAAW,EAAE,oDAAoD;QACjE,GAAG,EAAE,8BAA8B;QACnC,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,cAAc;QACxB,UAAU,EAAE,cAAc;QAC1B,MAAM,EAAE,gBAAgB;QACxB,QAAQ,EAAE,SAAS;QACnB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,YAAY;KACrB;IACD,WAAW,EAAE;QACX,KAAK,EAAE,uBAAuB;QAC9B,WAAW,EAAE,kDAAkD;QAC/D,GAAG,EAAE,iCAAiC;QACtC,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,iBAAiB;QAC3B,UAAU,EAAE,UAAU;QACtB,MAAM,EAAE,cAAc;QACtB,QAAQ,EAAE,SAAS;QACnB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,gBAAgB;KACzB;CACF,CAAC;AAEW,QAAA,eAAe,GAAG;IAC7B,oBAAoB,EAAE;QACpB,IAAI,EAAE,uBAAuB;QAC7B,IAAI,EAAE,uBAAuB;QAC7B,QAAQ,EAAE,yEAAyE;QACnF,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,2BAA2B,CAAC;QACjE,IAAI,EAAE,CAAC,aAAa,EAAE,8BAA8B,EAAE,0BAA0B,CAAC;QACjF,eAAe,EAAE;YACf,EAAE,KAAK,EAAE,6BAA6B,EAAE,WAAW,EAAE,wCAAwC,EAAE;YAC/F,EAAE,KAAK,EAAE,wBAAwB,EAAE,WAAW,EAAE,qCAAqC,EAAE;YACvF,EAAE,KAAK,EAAE,oBAAoB,EAAE,WAAW,EAAE,0CAA0C,EAAE;SACzF;KACF;IACD,aAAa,EAAE;QACb,IAAI,EAAE,gBAAgB;QACtB,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,wDAAwD;QAClE,IAAI,EAAE,CAAC,aAAa,EAAE,yBAAyB,EAAE,eAAe,CAAC;QACjE,IAAI,EAAE,CAAC,6BAA6B,EAAE,qBAAqB,EAAE,sBAAsB,CAAC;QACpF,eAAe,EAAE;YACf,EAAE,KAAK,EAAE,gBAAgB,EAAE,WAAW,EAAE,gDAAgD,EAAE;YAC1F,EAAE,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,qCAAqC,EAAE;YACjF,EAAE,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,0CAA0C,EAAE;SACvF;KACF;CACF,CAAC;AAEW,QAAA,gBAAgB,GAAG;IAC9B,gBAAgB,EAAE;QAChB,MAAM,EAAE,aAAa;QACrB,KAAK,EAAE,0CAA0C;QACjD,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;KACb;IACD,iBAAiB,EAAE;QACjB,MAAM,EAAE,WAAW;QACnB,KAAK,EAAE,iCAAiC;QACxC,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,4CAA4C;QACpD,WAAW,EAAE,IAAI,IAAI,EAAE;KACxB;IACD,kBAAkB,EAAE;QAClB,MAAM,EAAE,YAAY;QACpB,KAAK,EAAE,iCAAiC;QACxC,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;KACb;CACF,CAAC;AAEW,QAAA,WAAW,GAAG;IACzB,eAAe,EAAE;QACf,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,kEAAkE;QAC1E,SAAS,EAAE,IAAI;KAChB;IACD,UAAU,EAAE;QACV,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,2CAA2C;QACnD,SAAS,EAAE,IAAI;KAChB;IACD,UAAU,EAAE;QACV,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,0CAA0C;QAClD,SAAS,EAAE,KAAK;KACjB;IACD,mBAAmB,EAAE;QACnB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;KAChB;CACF,CAAC;AAEW,QAAA,gBAAgB,GAAG;IAC9B,eAAe,EAAE;QACf,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE;KACrD;IACD,aAAa,EAAE;QACb,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,sBAAsB;KAC9B;IACD,oBAAoB,EAAE;QACpB,KAAK,EAAE,cAAc;KACtB;IACD,uBAAuB,EAAE;QACvB,KAAK,EAAE,mBAAmB;QAC1B,OAAO,EAAE,CAAC,mBAAmB,EAAE,wCAAwC,CAAC;KACzE;CACF,CAAC;AAEW,QAAA,YAAY,GAAG;IAC1B,cAAc,EAAE;QACd,KAAK,EAAE,kBAAkB;QACzB,QAAQ,EAAE,kBAAkB;KAC7B;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,eAAe;QACtB,QAAQ,EAAE,KAAK,CAAC,YAAY;KAC7B;IACD,eAAe,EAAE;QACf,KAAK,EAAE,qBAAqB;QAC5B,QAAQ,EAAE,iBAAiB;QAC3B,eAAe,EAAE,iBAAiB;KACnC;IACD,iBAAiB,EAAE;QACjB,KAAK,EAAE,qBAAqB;QAC5B,QAAQ,EAAE,iBAAiB;QAC3B,eAAe,EAAE,uBAAuB,CAAC,wBAAwB;KAClE;CACF,CAAC;AAEW,QAAA,kBAAkB,GAAG;IAChC,oBAAoB,EAAE;QACpB,yBAAyB;QACzB,cAAc;QACd,UAAU;QACV,gCAAgC;KACjC;IACD,WAAW,EAAE;QACX,+BAA+B;QAC/B,yBAAyB;QACzB,kCAAkC;QAClC,2BAA2B;KAC5B;IACD,qBAAqB,EAAE;QACrB,qBAAqB;QACrB,4CAA4C;QAC5C,8BAA8B;QAC9B,yCAAyC;KAC1C;IACD,eAAe,EAAE;QACf,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;QAC7B,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc;QAC5C,YAAY,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM;KACxC;CACF,CAAC;AAEW,QAAA,mBAAmB,GAAG;IACjC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC;QACpD,EAAE,EAAE,mBAAY,CAAC,CAAE;QACnB,KAAK,EAAE,mBAAY,CAAC,CAAE;QACtB,WAAW,EAAE,mCAA4B,CAAC,CAAE;QAC5C,QAAQ,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACtE,CAAC,EALmD,CAKnD,CAAC;IACH,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC;QACrD,KAAK,EAAE,cAAO,CAAC,iBAAc;QAC7B,QAAQ,EAAE,kBAAkB;KAC7B,CAAC,EAHoD,CAGpD,CAAC;CACJ,CAAC;AAEW,QAAA,kBAAkB,GAAG;IAChC,YAAY,EAAE,IAAI,KAAK,CAAC,wBAAwB,CAAC;IACjD,YAAY,EAAE,IAAI,KAAK,CAAC,iBAAiB,CAAC;IAC1C,WAAW,EAAE,IAAI,KAAK,CAAC,uBAAuB,CAAC;IAC/C,eAAe,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC;IAC/C,mBAAmB,EAAE,IAAI,KAAK,CAAC,uBAAuB,CAAC;IACvD,kBAAkB,EAAE,IAAI,KAAK,CAAC,eAAe,CAAC;CAC/C,CAAC;AAEW,QAAA,qBAAqB,GAAG;IACnC,eAAe,EAAE,gBAAgB;IACjC,cAAc,EAAE,uBAAuB;IACvC,WAAW,EAAE,KAAK;IAClB,UAAU,EAAE,CAAC;CACd,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/fixtures/testData.ts"], "sourcesContent": ["// Test data fixtures for consistent testing\n// Note: <EMAIL> has been permanently eliminated\n\nexport const testUsers = {\n  validUser: {\n    email: '<EMAIL>',\n    password: 'TestPassword123!',\n    name: 'Test User'\n  },\n  adminUser: {\n    email: '<EMAIL>',\n    password: 'AdminPassword123!',\n    name: 'Admin User'\n  },\n  incompleteUser: {\n    email: '<EMAIL>',\n    // Missing password\n    name: 'Incomplete User'\n  },\n  invalidEmailUser: {\n    email: 'invalid-email',\n    password: 'TestPassword123!',\n    name: 'Invalid Email User'\n  }\n};\n\nexport const testAssessments = {\n  completeAssessment: {\n    // Step 1: Understanding Your Current Situation\n    dissatisfaction_triggers: ['lack_of_growth', 'work_life_balance', 'compensation'],\n    current_employment_status: 'employed_full_time',\n    years_experience: '6-10',\n\n    // Step 2: Defining Your Desired Outcomes\n    financial_comfort: 3,\n    financial_preparation_interest: 4,\n    desired_outcomes_work_life: 'very_important',\n    desired_outcomes_compensation: 'moderate_increase',\n    desired_outcomes_autonomy: 4,\n\n    // Step 3: Skills and Strengths Assessment\n    top_skills: ['communication', 'problem_solving', 'project_management'],\n    skill_development_interest: ['digital_marketing', 'entrepreneurship'],\n    learning_preference: ['online_courses', 'hands_on_practice'],\n\n    // Step 4: Values and Work Preferences\n    core_values: ['autonomy', 'work_life_balance', 'continuous_learning'],\n    work_environment_preference: 'remote_home',\n    team_size_preference: 'small_team',\n    risk_tolerance: 3,\n\n    // Step 5: Career Transition Readiness\n    transition_timeline: 'medium_term',\n    biggest_obstacles: ['financial_constraints', 'lack_of_skills'],\n    support_system: 4,\n    career_change_motivation: 'pursue_passion',\n    confidence_level: 3,\n\n    // Step 6: Additional Insights\n    ideal_day_description: 'I would start my day at 9 AM, work on creative projects for 4 hours, have lunch with colleagues, then spend the afternoon in meetings and planning.',\n    career_inspiration: 'I am inspired by entrepreneurs who have built successful businesses while maintaining work-life balance.',\n    additional_thoughts: 'I am excited about the possibility of transitioning to a more fulfilling career that aligns with my values.'\n  },\n  partialAssessment: {\n    dissatisfaction_triggers: ['lack_of_growth'],\n    desired_outcomes_work_life: 'very_important',\n    desired_outcomes_autonomy: 3,\n    work_environment_preference: 'hybrid'\n  },\n  invalidAssessment: {\n    dissatisfaction_triggers: 'invalid_string_instead_of_array',\n    desired_outcomes_work_life: 'invalid_value',\n    work_environment_preference: null\n  }\n};\n\nexport const testLearningResources = {\n  cybersecurityCourse: {\n    title: 'Ethical Hacking Fundamentals',\n    description: 'Learn the basics of ethical hacking and penetration testing',\n    url: 'https://example.com/ethical-hacking',\n    type: 'COURSE',\n    category: 'CYBERSECURITY',\n    skillLevel: 'BEGINNER',\n    author: 'Security Expert',\n    duration: '40 hours',\n    cost: 'FREE',\n    format: 'SELF_PACED'\n  },\n  dataScienceArticle: {\n    title: 'Introduction to Machine Learning',\n    description: 'A comprehensive guide to machine learning concepts',\n    url: 'https://example.com/ml-intro',\n    type: 'ARTICLE',\n    category: 'DATA_SCIENCE',\n    skillLevel: 'INTERMEDIATE',\n    author: 'Data Scientist',\n    duration: '2 hours',\n    cost: 'FREE',\n    format: 'SELF_PACED'\n  },\n  webDevVideo: {\n    title: 'React Hooks Deep Dive',\n    description: 'Advanced React hooks patterns and best practices',\n    url: 'https://example.com/react-hooks',\n    type: 'VIDEO',\n    category: 'WEB_DEVELOPMENT',\n    skillLevel: 'ADVANCED',\n    author: 'React Expert',\n    duration: '3 hours',\n    cost: 'PAID',\n    format: 'INSTRUCTOR_LED'\n  }\n};\n\nexport const testCareerPaths = {\n  cybersecurityAnalyst: {\n    name: 'Cybersecurity Analyst',\n    slug: 'cybersecurity-analyst',\n    overview: 'Protect organizations from cyber threats and analyze security incidents',\n    pros: ['High demand', 'Good salary', 'Remote work opportunities'],\n    cons: ['High stress', 'Continuous learning required', 'On-call responsibilities'],\n    actionableSteps: [\n      { title: 'Get Security+ Certification', description: 'Obtain CompTIA Security+ certification' },\n      { title: 'Learn Network Security', description: 'Study network security fundamentals' },\n      { title: 'Practice with Labs', description: 'Use virtual labs for hands-on experience' }\n    ]\n  },\n  dataScientist: {\n    name: 'Data Scientist',\n    slug: 'data-scientist',\n    overview: 'Extract insights from data to drive business decisions',\n    pros: ['High salary', 'Intellectual challenges', 'Growing field'],\n    cons: ['Requires strong math skills', 'Data quality issues', 'Long analysis cycles'],\n    actionableSteps: [\n      { title: 'Learn Python/R', description: 'Master programming languages for data analysis' },\n      { title: 'Study Statistics', description: 'Build strong statistical foundation' },\n      { title: 'Work on Projects', description: 'Build portfolio with real-world projects' }\n    ]\n  }\n};\n\nexport const testProgressData = {\n  beginnerProgress: {\n    status: 'IN_PROGRESS',\n    notes: 'Making good progress on the fundamentals',\n    rating: null,\n    review: null\n  },\n  completedProgress: {\n    status: 'COMPLETED',\n    notes: 'Excellent course, learned a lot',\n    rating: 5,\n    review: 'Highly recommend this course for beginners',\n    completedAt: new Date()\n  },\n  bookmarkedProgress: {\n    status: 'BOOKMARKED',\n    notes: 'Want to come back to this later',\n    rating: null,\n    review: null\n  }\n};\n\nexport const testRatings = {\n  excellentRating: {\n    rating: 5,\n    review: 'Outstanding resource! Clear explanations and practical examples.',\n    isHelpful: true\n  },\n  goodRating: {\n    rating: 4,\n    review: 'Good content but could use more examples.',\n    isHelpful: true\n  },\n  poorRating: {\n    rating: 2,\n    review: 'Content was outdated and hard to follow.',\n    isHelpful: false\n  },\n  ratingWithoutReview: {\n    rating: 3,\n    review: null,\n    isHelpful: null\n  }\n};\n\nexport const testAPIResponses = {\n  successResponse: {\n    success: true,\n    data: { id: '123', message: 'Operation successful' }\n  },\n  errorResponse: {\n    success: false,\n    error: 'Something went wrong'\n  },\n  unauthorizedResponse: {\n    error: 'Unauthorized'\n  },\n  validationErrorResponse: {\n    error: 'Validation failed',\n    details: ['Email is required', 'Password must be at least 8 characters']\n  }\n};\n\nexport const testFormData = {\n  validLoginForm: {\n    email: '<EMAIL>',\n    password: 'TestPassword123!'\n  },\n  invalidLoginForm: {\n    email: 'invalid-email',\n    password: '123' // Too short\n  },\n  validSignupForm: {\n    email: '<EMAIL>',\n    password: 'NewPassword123!',\n    confirmPassword: 'NewPassword123!'\n  },\n  invalidSignupForm: {\n    email: '<EMAIL>',\n    password: 'NewPassword123!',\n    confirmPassword: 'DifferentPassword123!' // Passwords don't match\n  }\n};\n\nexport const testSecurityInputs = {\n  sqlInjectionAttempts: [\n    \"'; DROP TABLE users; --\",\n    \"1' OR '1'='1\",\n    \"admin'--\",\n    \"' UNION SELECT * FROM users --\"\n  ],\n  xssAttempts: [\n    \"<script>alert('xss')</script>\",\n    \"javascript:alert('xss')\",\n    \"<img src=x onerror=alert('xss')>\",\n    \"<svg onload=alert('xss')>\"\n  ],\n  pathTraversalAttempts: [\n    \"../../../etc/passwd\",\n    \"..\\\\..\\\\..\\\\windows\\\\system32\\\\config\\\\sam\",\n    \"....//....//....//etc/passwd\",\n    \"%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd\"\n  ],\n  oversizedInputs: {\n    longString: \"A\".repeat(10000),\n    longEmail: \"a\".repeat(1000) + \"@example.com\",\n    longPassword: \"P\".repeat(1000) + \"123!\"\n  }\n};\n\nexport const testPerformanceData = {\n  largeDataset: Array.from({ length: 1000 }, (_, i) => ({\n    id: `resource-${i}`,\n    title: `Resource ${i}`,\n    description: `Description for resource ${i}`,\n    category: ['CYBERSECURITY', 'DATA_SCIENCE', 'WEB_DEVELOPMENT'][i % 3]\n  })),\n  concurrentUsers: Array.from({ length: 50 }, (_, i) => ({\n    email: `user${i}@example.com`,\n    password: 'TestPassword123!'\n  }))\n};\n\nexport const testErrorScenarios = {\n  networkError: new Error('Network request failed'),\n  timeoutError: new Error('Request timeout'),\n  serverError: new Error('Internal server error'),\n  validationError: new Error('Validation failed'),\n  authenticationError: new Error('Authentication failed'),\n  authorizationError: new Error('Access denied')\n};\n\nexport const testEnvironmentConfig = {\n  testDatabaseUrl: 'file:./test.db',\n  testApiBaseUrl: 'http://localhost:3000',\n  testTimeout: 30000,\n  maxRetries: 3\n};\n"], "version": 3}