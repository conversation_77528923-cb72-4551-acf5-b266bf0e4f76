f635564760a1a5a02b469bd6f1c32f14
"use strict";
// Test data fixtures for consistent testing
// Note: <EMAIL> has been permanently eliminated
Object.defineProperty(exports, "__esModule", { value: true });
exports.testEnvironmentConfig = exports.testErrorScenarios = exports.testPerformanceData = exports.testSecurityInputs = exports.testFormData = exports.testAPIResponses = exports.testRatings = exports.testProgressData = exports.testCareerPaths = exports.testLearningResources = exports.testAssessments = exports.testUsers = void 0;
exports.testUsers = {
    validUser: {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Test User'
    },
    adminUser: {
        email: '<EMAIL>',
        password: 'AdminPassword123!',
        name: 'Admin User'
    },
    incompleteUser: {
        email: '<EMAIL>',
        // Missing password
        name: 'Incomplete User'
    },
    invalidEmailUser: {
        email: 'invalid-email',
        password: 'TestPassword123!',
        name: 'Invalid Email User'
    }
};
exports.testAssessments = {
    completeAssessment: {
        // Step 1: Understanding Your Current Situation
        dissatisfaction_triggers: ['lack_of_growth', 'work_life_balance', 'compensation'],
        current_employment_status: 'employed_full_time',
        years_experience: '6-10',
        // Step 2: Defining Your Desired Outcomes
        financial_comfort: 3,
        financial_preparation_interest: 4,
        desired_outcomes_work_life: 'very_important',
        desired_outcomes_compensation: 'moderate_increase',
        desired_outcomes_autonomy: 4,
        // Step 3: Skills and Strengths Assessment
        top_skills: ['communication', 'problem_solving', 'project_management'],
        skill_development_interest: ['digital_marketing', 'entrepreneurship'],
        learning_preference: ['online_courses', 'hands_on_practice'],
        // Step 4: Values and Work Preferences
        core_values: ['autonomy', 'work_life_balance', 'continuous_learning'],
        work_environment_preference: 'remote_home',
        team_size_preference: 'small_team',
        risk_tolerance: 3,
        // Step 5: Career Transition Readiness
        transition_timeline: 'medium_term',
        biggest_obstacles: ['financial_constraints', 'lack_of_skills'],
        support_system: 4,
        career_change_motivation: 'pursue_passion',
        confidence_level: 3,
        // Step 6: Additional Insights
        ideal_day_description: 'I would start my day at 9 AM, work on creative projects for 4 hours, have lunch with colleagues, then spend the afternoon in meetings and planning.',
        career_inspiration: 'I am inspired by entrepreneurs who have built successful businesses while maintaining work-life balance.',
        additional_thoughts: 'I am excited about the possibility of transitioning to a more fulfilling career that aligns with my values.'
    },
    partialAssessment: {
        dissatisfaction_triggers: ['lack_of_growth'],
        desired_outcomes_work_life: 'very_important',
        desired_outcomes_autonomy: 3,
        work_environment_preference: 'hybrid'
    },
    invalidAssessment: {
        dissatisfaction_triggers: 'invalid_string_instead_of_array',
        desired_outcomes_work_life: 'invalid_value',
        work_environment_preference: null
    }
};
exports.testLearningResources = {
    cybersecurityCourse: {
        title: 'Ethical Hacking Fundamentals',
        description: 'Learn the basics of ethical hacking and penetration testing',
        url: 'https://example.com/ethical-hacking',
        type: 'COURSE',
        category: 'CYBERSECURITY',
        skillLevel: 'BEGINNER',
        author: 'Security Expert',
        duration: '40 hours',
        cost: 'FREE',
        format: 'SELF_PACED'
    },
    dataScienceArticle: {
        title: 'Introduction to Machine Learning',
        description: 'A comprehensive guide to machine learning concepts',
        url: 'https://example.com/ml-intro',
        type: 'ARTICLE',
        category: 'DATA_SCIENCE',
        skillLevel: 'INTERMEDIATE',
        author: 'Data Scientist',
        duration: '2 hours',
        cost: 'FREE',
        format: 'SELF_PACED'
    },
    webDevVideo: {
        title: 'React Hooks Deep Dive',
        description: 'Advanced React hooks patterns and best practices',
        url: 'https://example.com/react-hooks',
        type: 'VIDEO',
        category: 'WEB_DEVELOPMENT',
        skillLevel: 'ADVANCED',
        author: 'React Expert',
        duration: '3 hours',
        cost: 'PAID',
        format: 'INSTRUCTOR_LED'
    }
};
exports.testCareerPaths = {
    cybersecurityAnalyst: {
        name: 'Cybersecurity Analyst',
        slug: 'cybersecurity-analyst',
        overview: 'Protect organizations from cyber threats and analyze security incidents',
        pros: ['High demand', 'Good salary', 'Remote work opportunities'],
        cons: ['High stress', 'Continuous learning required', 'On-call responsibilities'],
        actionableSteps: [
            { title: 'Get Security+ Certification', description: 'Obtain CompTIA Security+ certification' },
            { title: 'Learn Network Security', description: 'Study network security fundamentals' },
            { title: 'Practice with Labs', description: 'Use virtual labs for hands-on experience' }
        ]
    },
    dataScientist: {
        name: 'Data Scientist',
        slug: 'data-scientist',
        overview: 'Extract insights from data to drive business decisions',
        pros: ['High salary', 'Intellectual challenges', 'Growing field'],
        cons: ['Requires strong math skills', 'Data quality issues', 'Long analysis cycles'],
        actionableSteps: [
            { title: 'Learn Python/R', description: 'Master programming languages for data analysis' },
            { title: 'Study Statistics', description: 'Build strong statistical foundation' },
            { title: 'Work on Projects', description: 'Build portfolio with real-world projects' }
        ]
    }
};
exports.testProgressData = {
    beginnerProgress: {
        status: 'IN_PROGRESS',
        notes: 'Making good progress on the fundamentals',
        rating: null,
        review: null
    },
    completedProgress: {
        status: 'COMPLETED',
        notes: 'Excellent course, learned a lot',
        rating: 5,
        review: 'Highly recommend this course for beginners',
        completedAt: new Date()
    },
    bookmarkedProgress: {
        status: 'BOOKMARKED',
        notes: 'Want to come back to this later',
        rating: null,
        review: null
    }
};
exports.testRatings = {
    excellentRating: {
        rating: 5,
        review: 'Outstanding resource! Clear explanations and practical examples.',
        isHelpful: true
    },
    goodRating: {
        rating: 4,
        review: 'Good content but could use more examples.',
        isHelpful: true
    },
    poorRating: {
        rating: 2,
        review: 'Content was outdated and hard to follow.',
        isHelpful: false
    },
    ratingWithoutReview: {
        rating: 3,
        review: null,
        isHelpful: null
    }
};
exports.testAPIResponses = {
    successResponse: {
        success: true,
        data: { id: '123', message: 'Operation successful' }
    },
    errorResponse: {
        success: false,
        error: 'Something went wrong'
    },
    unauthorizedResponse: {
        error: 'Unauthorized'
    },
    validationErrorResponse: {
        error: 'Validation failed',
        details: ['Email is required', 'Password must be at least 8 characters']
    }
};
exports.testFormData = {
    validLoginForm: {
        email: '<EMAIL>',
        password: 'TestPassword123!'
    },
    invalidLoginForm: {
        email: 'invalid-email',
        password: '123' // Too short
    },
    validSignupForm: {
        email: '<EMAIL>',
        password: 'NewPassword123!',
        confirmPassword: 'NewPassword123!'
    },
    invalidSignupForm: {
        email: '<EMAIL>',
        password: 'NewPassword123!',
        confirmPassword: 'DifferentPassword123!' // Passwords don't match
    }
};
exports.testSecurityInputs = {
    sqlInjectionAttempts: [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'--",
        "' UNION SELECT * FROM users --"
    ],
    xssAttempts: [
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
        "<svg onload=alert('xss')>"
    ],
    pathTraversalAttempts: [
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        "....//....//....//etc/passwd",
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
    ],
    oversizedInputs: {
        longString: "A".repeat(10000),
        longEmail: "a".repeat(1000) + "@example.com",
        longPassword: "P".repeat(1000) + "123!"
    }
};
exports.testPerformanceData = {
    largeDataset: Array.from({ length: 1000 }, function (_, i) { return ({
        id: "resource-".concat(i),
        title: "Resource ".concat(i),
        description: "Description for resource ".concat(i),
        category: ['CYBERSECURITY', 'DATA_SCIENCE', 'WEB_DEVELOPMENT'][i % 3]
    }); }),
    concurrentUsers: Array.from({ length: 50 }, function (_, i) { return ({
        email: "user".concat(i, "@example.com"),
        password: 'TestPassword123!'
    }); })
};
exports.testErrorScenarios = {
    networkError: new Error('Network request failed'),
    timeoutError: new Error('Request timeout'),
    serverError: new Error('Internal server error'),
    validationError: new Error('Validation failed'),
    authenticationError: new Error('Authentication failed'),
    authorizationError: new Error('Access denied')
};
exports.testEnvironmentConfig = {
    testDatabaseUrl: 'file:./test.db',
    testApiBaseUrl: 'http://localhost:3000',
    testTimeout: 30000,
    maxRetries: 3
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************