bc29d303477e0fdbcd10acba99e464f6
"use strict";

/* istanbul ignore next */
function cov_xrl7693qr() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/ai/skills-analysis/comprehensive/route.ts";
  var hash = "f7ba946a66c188025b875c8abd6c9e984da788c3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/ai/skills-analysis/comprehensive/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 46,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 44,
          column: 5
        }
      },
      "69": {
        start: {
          line: 39,
          column: 40
        },
        end: {
          line: 44,
          column: 5
        }
      },
      "70": {
        start: {
          line: 39,
          column: 53
        },
        end: {
          line: 39,
          column: 54
        }
      },
      "71": {
        start: {
          line: 39,
          column: 60
        },
        end: {
          line: 39,
          column: 71
        }
      },
      "72": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 43,
          column: 9
        }
      },
      "73": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 41,
          column: 65
        }
      },
      "74": {
        start: {
          line: 41,
          column: 21
        },
        end: {
          line: 41,
          column: 65
        }
      },
      "75": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 42,
          column: 28
        }
      },
      "76": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 61
        }
      },
      "77": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 62
        }
      },
      "78": {
        start: {
          line: 48,
          column: 0
        },
        end: {
          line: 48,
          column: 22
        }
      },
      "79": {
        start: {
          line: 49,
          column: 15
        },
        end: {
          line: 49,
          column: 37
        }
      },
      "80": {
        start: {
          line: 50,
          column: 18
        },
        end: {
          line: 50,
          column: 38
        }
      },
      "81": {
        start: {
          line: 51,
          column: 13
        },
        end: {
          line: 51,
          column: 34
        }
      },
      "82": {
        start: {
          line: 52,
          column: 22
        },
        end: {
          line: 52,
          column: 61
        }
      },
      "83": {
        start: {
          line: 53,
          column: 35
        },
        end: {
          line: 53,
          column: 87
        }
      },
      "84": {
        start: {
          line: 54,
          column: 34
        },
        end: {
          line: 54,
          column: 76
        }
      },
      "85": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 44
        }
      },
      "86": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "87": {
        start: {
          line: 57,
          column: 15
        },
        end: {
          line: 57,
          column: 38
        }
      },
      "88": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 26
        }
      },
      "89": {
        start: {
          line: 59,
          column: 30
        },
        end: {
          line: 59,
          column: 80
        }
      },
      "90": {
        start: {
          line: 60,
          column: 31
        },
        end: {
          line: 60,
          column: 77
        }
      },
      "91": {
        start: {
          line: 61,
          column: 33
        },
        end: {
          line: 61,
          column: 83
        }
      },
      "92": {
        start: {
          line: 62,
          column: 36
        },
        end: {
          line: 62,
          column: 89
        }
      },
      "93": {
        start: {
          line: 64,
          column: 40
        },
        end: {
          line: 87,
          column: 2
        }
      },
      "94": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 124,
          column: 7
        }
      },
      "95": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 123,
          column: 11
        }
      },
      "96": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 122,
          column: 13
        }
      },
      "97": {
        start: {
          line: 94,
          column: 20
        },
        end: {
          line: 94,
          column: 46
        }
      },
      "98": {
        start: {
          line: 95,
          column: 20
        },
        end: {
          line: 106,
          column: 28
        }
      },
      "99": {
        start: {
          line: 108,
          column: 20
        },
        end: {
          line: 108,
          column: 44
        }
      },
      "100": {
        start: {
          line: 109,
          column: 20
        },
        end: {
          line: 116,
          column: 32
        }
      },
      "101": {
        start: {
          line: 109,
          column: 82
        },
        end: {
          line: 116,
          column: 27
        }
      },
      "102": {
        start: {
          line: 118,
          column: 20
        },
        end: {
          line: 118,
          column: 40
        }
      },
      "103": {
        start: {
          line: 119,
          column: 20
        },
        end: {
          line: 119,
          column: 85
        }
      },
      "104": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 46
        }
      },
      "105": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 121,
          column: 46
        }
      },
      "106": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 203,
          column: 7
        }
      },
      "107": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 202,
          column: 11
        }
      },
      "108": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 201,
          column: 13
        }
      },
      "109": {
        start: {
          line: 132,
          column: 20
        },
        end: {
          line: 132,
          column: 46
        }
      },
      "110": {
        start: {
          line: 133,
          column: 20
        },
        end: {
          line: 144,
          column: 28
        }
      },
      "111": {
        start: {
          line: 146,
          column: 20
        },
        end: {
          line: 146,
          column: 45
        }
      },
      "112": {
        start: {
          line: 147,
          column: 20
        },
        end: {
          line: 149,
          column: 21
        }
      },
      "113": {
        start: {
          line: 148,
          column: 24
        },
        end: {
          line: 148,
          column: 50
        }
      },
      "114": {
        start: {
          line: 150,
          column: 20
        },
        end: {
          line: 150,
          column: 48
        }
      },
      "115": {
        start: {
          line: 152,
          column: 20
        },
        end: {
          line: 194,
          column: 23
        }
      },
      "116": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 193,
          column: 25
        }
      },
      "117": {
        start: {
          line: 154,
          column: 40
        },
        end: {
          line: 156,
          column: 54
        }
      },
      "118": {
        start: {
          line: 158,
          column: 28
        },
        end: {
          line: 170,
          column: 29
        }
      },
      "119": {
        start: {
          line: 159,
          column: 32
        },
        end: {
          line: 169,
          column: 35
        }
      },
      "120": {
        start: {
          line: 161,
          column: 36
        },
        end: {
          line: 168,
          column: 37
        }
      },
      "121": {
        start: {
          line: 162,
          column: 40
        },
        end: {
          line: 167,
          column: 43
        }
      },
      "122": {
        start: {
          line: 172,
          column: 28
        },
        end: {
          line: 189,
          column: 29
        }
      },
      "123": {
        start: {
          line: 173,
          column: 54
        },
        end: {
          line: 173,
          column: 73
        }
      },
      "124": {
        start: {
          line: 174,
          column: 54
        },
        end: {
          line: 174,
          column: 55
        }
      },
      "125": {
        start: {
          line: 175,
          column: 32
        },
        end: {
          line: 183,
          column: 33
        }
      },
      "126": {
        start: {
          line: 176,
          column: 36
        },
        end: {
          line: 176,
          column: 56
        }
      },
      "127": {
        start: {
          line: 178,
          column: 37
        },
        end: {
          line: 183,
          column: 33
        }
      },
      "128": {
        start: {
          line: 179,
          column: 36
        },
        end: {
          line: 179,
          column: 56
        }
      },
      "129": {
        start: {
          line: 181,
          column: 37
        },
        end: {
          line: 183,
          column: 33
        }
      },
      "130": {
        start: {
          line: 182,
          column: 36
        },
        end: {
          line: 182,
          column: 56
        }
      },
      "131": {
        start: {
          line: 185,
          column: 32
        },
        end: {
          line: 188,
          column: 35
        }
      },
      "132": {
        start: {
          line: 186,
          column: 36
        },
        end: {
          line: 186,
          column: 71
        }
      },
      "133": {
        start: {
          line: 187,
          column: 36
        },
        end: {
          line: 187,
          column: 76
        }
      },
      "134": {
        start: {
          line: 192,
          column: 28
        },
        end: {
          line: 192,
          column: 87
        }
      },
      "135": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 195,
          column: 66
        }
      },
      "136": {
        start: {
          line: 197,
          column: 20
        },
        end: {
          line: 197,
          column: 40
        }
      },
      "137": {
        start: {
          line: 198,
          column: 20
        },
        end: {
          line: 198,
          column: 92
        }
      },
      "138": {
        start: {
          line: 199,
          column: 20
        },
        end: {
          line: 199,
          column: 46
        }
      },
      "139": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 46
        }
      },
      "140": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 375,
          column: 7
        }
      },
      "141": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 374,
          column: 11
        }
      },
      "142": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 373,
          column: 13
        }
      },
      "143": {
        start: {
          line: 211,
          column: 20
        },
        end: {
          line: 211,
          column: 46
        }
      },
      "144": {
        start: {
          line: 212,
          column: 20
        },
        end: {
          line: 212,
          column: 43
        }
      },
      "145": {
        start: {
          line: 213,
          column: 20
        },
        end: {
          line: 220,
          column: 26
        }
      },
      "146": {
        start: {
          line: 221,
          column: 20
        },
        end: {
          line: 306,
          column: 28
        }
      },
      "147": {
        start: {
          line: 308,
          column: 20
        },
        end: {
          line: 308,
          column: 43
        }
      },
      "148": {
        start: {
          line: 309,
          column: 20
        },
        end: {
          line: 309,
          column: 55
        }
      },
      "149": {
        start: {
          line: 311,
          column: 20
        },
        end: {
          line: 313,
          column: 21
        }
      },
      "150": {
        start: {
          line: 312,
          column: 24
        },
        end: {
          line: 312,
          column: 147
        }
      },
      "151": {
        start: {
          line: 314,
          column: 20
        },
        end: {
          line: 315,
          column: 52
        }
      },
      "152": {
        start: {
          line: 315,
          column: 24
        },
        end: {
          line: 315,
          column: 52
        }
      },
      "153": {
        start: {
          line: 316,
          column: 20
        },
        end: {
          line: 316,
          column: 52
        }
      },
      "154": {
        start: {
          line: 317,
          column: 20
        },
        end: {
          line: 366,
          column: 22
        }
      },
      "155": {
        start: {
          line: 322,
          column: 88
        },
        end: {
          line: 328,
          column: 27
        }
      },
      "156": {
        start: {
          line: 332,
          column: 48
        },
        end: {
          line: 334,
          column: 35
        }
      },
      "157": {
        start: {
          line: 333,
          column: 90
        },
        end: {
          line: 333,
          column: 112
        }
      },
      "158": {
        start: {
          line: 335,
          column: 28
        },
        end: {
          line: 346,
          column: 30
        }
      },
      "159": {
        start: {
          line: 345,
          column: 127
        },
        end: {
          line: 345,
          column: 145
        }
      },
      "160": {
        start: {
          line: 350,
          column: 28
        },
        end: {
          line: 357,
          column: 31
        }
      },
      "161": {
        start: {
          line: 356,
          column: 123
        },
        end: {
          line: 356,
          column: 141
        }
      },
      "162": {
        start: {
          line: 367,
          column: 20
        },
        end: {
          line: 367,
          column: 50
        }
      },
      "163": {
        start: {
          line: 369,
          column: 20
        },
        end: {
          line: 369,
          column: 40
        }
      },
      "164": {
        start: {
          line: 370,
          column: 20
        },
        end: {
          line: 370,
          column: 88
        }
      },
      "165": {
        start: {
          line: 371,
          column: 20
        },
        end: {
          line: 371,
          column: 48
        }
      },
      "166": {
        start: {
          line: 372,
          column: 24
        },
        end: {
          line: 372,
          column: 46
        }
      },
      "167": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 418,
          column: 7
        }
      },
      "168": {
        start: {
          line: 381,
          column: 8
        },
        end: {
          line: 417,
          column: 11
        }
      },
      "169": {
        start: {
          line: 382,
          column: 12
        },
        end: {
          line: 416,
          column: 13
        }
      },
      "170": {
        start: {
          line: 384,
          column: 20
        },
        end: {
          line: 384,
          column: 46
        }
      },
      "171": {
        start: {
          line: 385,
          column: 20
        },
        end: {
          line: 385,
          column: 43
        }
      },
      "172": {
        start: {
          line: 386,
          column: 20
        },
        end: {
          line: 386,
          column: 65
        }
      },
      "173": {
        start: {
          line: 387,
          column: 20
        },
        end: {
          line: 407,
          column: 28
        }
      },
      "174": {
        start: {
          line: 409,
          column: 20
        },
        end: {
          line: 409,
          column: 49
        }
      },
      "175": {
        start: {
          line: 410,
          column: 20
        },
        end: {
          line: 410,
          column: 60
        }
      },
      "176": {
        start: {
          line: 412,
          column: 20
        },
        end: {
          line: 412,
          column: 40
        }
      },
      "177": {
        start: {
          line: 413,
          column: 20
        },
        end: {
          line: 413,
          column: 81
        }
      },
      "178": {
        start: {
          line: 414,
          column: 20
        },
        end: {
          line: 414,
          column: 34
        }
      },
      "179": {
        start: {
          line: 415,
          column: 24
        },
        end: {
          line: 415,
          column: 46
        }
      },
      "180": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 497,
          column: 7
        }
      },
      "181": {
        start: {
          line: 424,
          column: 8
        },
        end: {
          line: 496,
          column: 11
        }
      },
      "182": {
        start: {
          line: 425,
          column: 12
        },
        end: {
          line: 495,
          column: 13
        }
      },
      "183": {
        start: {
          line: 426,
          column: 24
        },
        end: {
          line: 426,
          column: 100
        }
      },
      "184": {
        start: {
          line: 428,
          column: 20
        },
        end: {
          line: 428,
          column: 40
        }
      },
      "185": {
        start: {
          line: 429,
          column: 20
        },
        end: {
          line: 433,
          column: 21
        }
      },
      "186": {
        start: {
          line: 430,
          column: 24
        },
        end: {
          line: 430,
          column: 69
        }
      },
      "187": {
        start: {
          line: 431,
          column: 24
        },
        end: {
          line: 431,
          column: 47
        }
      },
      "188": {
        start: {
          line: 432,
          column: 24
        },
        end: {
          line: 432,
          column: 36
        }
      },
      "189": {
        start: {
          line: 434,
          column: 20
        },
        end: {
          line: 434,
          column: 45
        }
      },
      "190": {
        start: {
          line: 435,
          column: 20
        },
        end: {
          line: 435,
          column: 57
        }
      },
      "191": {
        start: {
          line: 437,
          column: 20
        },
        end: {
          line: 437,
          column: 37
        }
      },
      "192": {
        start: {
          line: 438,
          column: 20
        },
        end: {
          line: 438,
          column: 83
        }
      },
      "193": {
        start: {
          line: 439,
          column: 20
        },
        end: {
          line: 444,
          column: 21
        }
      },
      "194": {
        start: {
          line: 440,
          column: 24
        },
        end: {
          line: 440,
          column: 66
        }
      },
      "195": {
        start: {
          line: 441,
          column: 24
        },
        end: {
          line: 441,
          column: 47
        }
      },
      "196": {
        start: {
          line: 442,
          column: 24
        },
        end: {
          line: 442,
          column: 64
        }
      },
      "197": {
        start: {
          line: 443,
          column: 24
        },
        end: {
          line: 443,
          column: 36
        }
      },
      "198": {
        start: {
          line: 445,
          column: 20
        },
        end: {
          line: 445,
          column: 50
        }
      },
      "199": {
        start: {
          line: 446,
          column: 20
        },
        end: {
          line: 446,
          column: 240
        }
      },
      "200": {
        start: {
          line: 447,
          column: 20
        },
        end: {
          line: 452,
          column: 38
        }
      },
      "201": {
        start: {
          line: 453,
          column: 20
        },
        end: {
          line: 453,
          column: 103
        }
      },
      "202": {
        start: {
          line: 455,
          column: 20
        },
        end: {
          line: 455,
          column: 39
        }
      },
      "203": {
        start: {
          line: 456,
          column: 20
        },
        end: {
          line: 463,
          column: 21
        }
      },
      "204": {
        start: {
          line: 457,
          column: 24
        },
        end: {
          line: 462,
          column: 32
        }
      },
      "205": {
        start: {
          line: 464,
          column: 20
        },
        end: {
          line: 464,
          column: 33
        }
      },
      "206": {
        start: {
          line: 466,
          column: 20
        },
        end: {
          line: 466,
          column: 47
        }
      },
      "207": {
        start: {
          line: 467,
          column: 20
        },
        end: {
          line: 468,
          column: 27
        }
      },
      "208": {
        start: {
          line: 470,
          column: 20
        },
        end: {
          line: 470,
          column: 44
        }
      },
      "209": {
        start: {
          line: 471,
          column: 20
        },
        end: {
          line: 471,
          column: 92
        }
      },
      "210": {
        start: {
          line: 471,
          column: 68
        },
        end: {
          line: 471,
          column: 92
        }
      },
      "211": {
        start: {
          line: 473,
          column: 20
        },
        end: {
          line: 473,
          column: 156
        }
      },
      "212": {
        start: {
          line: 476,
          column: 20
        },
        end: {
          line: 476,
          column: 30
        }
      },
      "213": {
        start: {
          line: 477,
          column: 20
        },
        end: {
          line: 484,
          column: 28
        }
      },
      "214": {
        start: {
          line: 485,
          column: 24
        },
        end: {
          line: 485,
          column: 88
        }
      },
      "215": {
        start: {
          line: 486,
          column: 24
        },
        end: {
          line: 486,
          column: 49
        }
      },
      "216": {
        start: {
          line: 488,
          column: 20
        },
        end: {
          line: 488,
          column: 45
        }
      },
      "217": {
        start: {
          line: 489,
          column: 20
        },
        end: {
          line: 489,
          column: 114
        }
      },
      "218": {
        start: {
          line: 490,
          column: 20
        },
        end: {
          line: 490,
          column: 109
        }
      },
      "219": {
        start: {
          line: 493,
          column: 16
        },
        end: {
          line: 493,
          column: 49
        }
      },
      "220": {
        start: {
          line: 494,
          column: 25
        },
        end: {
          line: 494,
          column: 47
        }
      },
      "221": {
        start: {
          line: 500,
          column: 4
        },
        end: {
          line: 641,
          column: 7
        }
      },
      "222": {
        start: {
          line: 502,
          column: 20
        },
        end: {
          line: 502,
          column: 24
        }
      },
      "223": {
        start: {
          line: 503,
          column: 8
        },
        end: {
          line: 640,
          column: 11
        }
      },
      "224": {
        start: {
          line: 504,
          column: 12
        },
        end: {
          line: 639,
          column: 13
        }
      },
      "225": {
        start: {
          line: 505,
          column: 24
        },
        end: {
          line: 510,
          column: 24
        }
      },
      "226": {
        start: {
          line: 512,
          column: 20
        },
        end: {
          line: 512,
          column: 84
        }
      },
      "227": {
        start: {
          line: 513,
          column: 20
        },
        end: {
          line: 513,
          column: 81
        }
      },
      "228": {
        start: {
          line: 513,
          column: 57
        },
        end: {
          line: 513,
          column: 81
        }
      },
      "229": {
        start: {
          line: 514,
          column: 20
        },
        end: {
          line: 514,
          column: 80
        }
      },
      "230": {
        start: {
          line: 516,
          column: 20
        },
        end: {
          line: 516,
          column: 35
        }
      },
      "231": {
        start: {
          line: 517,
          column: 20
        },
        end: {
          line: 517,
          column: 44
        }
      },
      "232": {
        start: {
          line: 519,
          column: 20
        },
        end: {
          line: 519,
          column: 28
        }
      },
      "233": {
        start: {
          line: 520,
          column: 20
        },
        end: {
          line: 520,
          column: 33
        }
      },
      "234": {
        start: {
          line: 522,
          column: 20
        },
        end: {
          line: 522,
          column: 48
        }
      },
      "235": {
        start: {
          line: 523,
          column: 20
        },
        end: {
          line: 529,
          column: 22
        }
      },
      "236": {
        start: {
          line: 530,
          column: 20
        },
        end: {
          line: 540,
          column: 61
        }
      },
      "237": {
        start: {
          line: 531,
          column: 56
        },
        end: {
          line: 531,
          column: 80
        }
      },
      "238": {
        start: {
          line: 534,
          column: 24
        },
        end: {
          line: 539,
          column: 27
        }
      },
      "239": {
        start: {
          line: 541,
          column: 20
        },
        end: {
          line: 551,
          column: 27
        }
      },
      "240": {
        start: {
          line: 542,
          column: 39
        },
        end: {
          line: 542,
          column: 133
        }
      },
      "241": {
        start: {
          line: 542,
          column: 63
        },
        end: {
          line: 542,
          column: 130
        }
      },
      "242": {
        start: {
          line: 543,
          column: 24
        },
        end: {
          line: 549,
          column: 25
        }
      },
      "243": {
        start: {
          line: 544,
          column: 28
        },
        end: {
          line: 544,
          column: 44
        }
      },
      "244": {
        start: {
          line: 546,
          column: 29
        },
        end: {
          line: 549,
          column: 25
        }
      },
      "245": {
        start: {
          line: 548,
          column: 28
        },
        end: {
          line: 548,
          column: 59
        }
      },
      "246": {
        start: {
          line: 550,
          column: 24
        },
        end: {
          line: 550,
          column: 35
        }
      },
      "247": {
        start: {
          line: 552,
          column: 20
        },
        end: {
          line: 622,
          column: 40
        }
      },
      "248": {
        start: {
          line: 552,
          column: 140
        },
        end: {
          line: 622,
          column: 27
        }
      },
      "249": {
        start: {
          line: 554,
          column: 28
        },
        end: {
          line: 621,
          column: 31
        }
      },
      "250": {
        start: {
          line: 555,
          column: 32
        },
        end: {
          line: 620,
          column: 33
        }
      },
      "251": {
        start: {
          line: 556,
          column: 44
        },
        end: {
          line: 576,
          column: 44
        }
      },
      "252": {
        start: {
          line: 559,
          column: 99
        },
        end: {
          line: 563,
          column: 51
        }
      },
      "253": {
        start: {
          line: 578,
          column: 40
        },
        end: {
          line: 578,
          column: 103
        }
      },
      "254": {
        start: {
          line: 579,
          column: 40
        },
        end: {
          line: 599,
          column: 41
        }
      },
      "255": {
        start: {
          line: 580,
          column: 44
        },
        end: {
          line: 580,
          column: 87
        }
      },
      "256": {
        start: {
          line: 582,
          column: 45
        },
        end: {
          line: 599,
          column: 41
        }
      },
      "257": {
        start: {
          line: 583,
          column: 44
        },
        end: {
          line: 583,
          column: 117
        }
      },
      "258": {
        start: {
          line: 584,
          column: 44
        },
        end: {
          line: 584,
          column: 117
        }
      },
      "259": {
        start: {
          line: 587,
          column: 44
        },
        end: {
          line: 587,
          column: 140
        }
      },
      "260": {
        start: {
          line: 588,
          column: 44
        },
        end: {
          line: 597,
          column: 45
        }
      },
      "261": {
        start: {
          line: 589,
          column: 48
        },
        end: {
          line: 596,
          column: 55
        }
      },
      "262": {
        start: {
          line: 598,
          column: 44
        },
        end: {
          line: 598,
          column: 91
        }
      },
      "263": {
        start: {
          line: 600,
          column: 40
        },
        end: {
          line: 600,
          column: 218
        }
      },
      "264": {
        start: {
          line: 602,
          column: 40
        },
        end: {
          line: 602,
          column: 69
        }
      },
      "265": {
        start: {
          line: 603,
          column: 40
        },
        end: {
          line: 618,
          column: 42
        }
      },
      "266": {
        start: {
          line: 619,
          column: 40
        },
        end: {
          line: 619,
          column: 76
        }
      },
      "267": {
        start: {
          line: 624,
          column: 20
        },
        end: {
          line: 624,
          column: 45
        }
      },
      "268": {
        start: {
          line: 626,
          column: 20
        },
        end: {
          line: 626,
          column: 152
        }
      },
      "269": {
        start: {
          line: 629,
          column: 20
        },
        end: {
          line: 629,
          column: 30
        }
      },
      "270": {
        start: {
          line: 631,
          column: 20
        },
        end: {
          line: 631,
          column: 177
        }
      },
      "271": {
        start: {
          line: 632,
          column: 20
        },
        end: {
          line: 638,
          column: 28
        }
      },
      "272": {
        start: {
          line: 644,
          column: 0
        },
        end: {
          line: 653,
          column: 7
        }
      },
      "273": {
        start: {
          line: 644,
          column: 94
        },
        end: {
          line: 653,
          column: 3
        }
      },
      "274": {
        start: {
          line: 645,
          column: 4
        },
        end: {
          line: 652,
          column: 7
        }
      },
      "275": {
        start: {
          line: 646,
          column: 8
        },
        end: {
          line: 651,
          column: 20
        }
      },
      "276": {
        start: {
          line: 646,
          column: 84
        },
        end: {
          line: 651,
          column: 15
        }
      },
      "277": {
        start: {
          line: 647,
          column: 16
        },
        end: {
          line: 650,
          column: 19
        }
      },
      "278": {
        start: {
          line: 648,
          column: 20
        },
        end: {
          line: 649,
          column: 93
        }
      },
      "279": {
        start: {
          line: 649,
          column: 38
        },
        end: {
          line: 649,
          column: 88
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 52
          },
          end: {
            line: 38,
            column: 53
          }
        },
        loc: {
          start: {
            line: 38,
            column: 78
          },
          end: {
            line: 46,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "getUserSkillAssessments",
        decl: {
          start: {
            line: 88,
            column: 9
          },
          end: {
            line: 88,
            column: 32
          }
        },
        loc: {
          start: {
            line: 88,
            column: 41
          },
          end: {
            line: 125,
            column: 1
          }
        },
        line: 88
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 89,
            column: 43
          },
          end: {
            line: 89,
            column: 44
          }
        },
        loc: {
          start: {
            line: 89,
            column: 55
          },
          end: {
            line: 124,
            column: 5
          }
        },
        line: 89
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 91,
            column: 33
          },
          end: {
            line: 91,
            column: 34
          }
        },
        loc: {
          start: {
            line: 91,
            column: 47
          },
          end: {
            line: 123,
            column: 9
          }
        },
        line: 91
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 109,
            column: 58
          },
          end: {
            line: 109,
            column: 59
          }
        },
        loc: {
          start: {
            line: 109,
            column: 80
          },
          end: {
            line: 116,
            column: 29
          }
        },
        line: 109
      },
      "18": {
        name: "getSkillsFromCareerAssessment",
        decl: {
          start: {
            line: 126,
            column: 9
          },
          end: {
            line: 126,
            column: 38
          }
        },
        loc: {
          start: {
            line: 126,
            column: 47
          },
          end: {
            line: 204,
            column: 1
          }
        },
        line: 126
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 127,
            column: 43
          },
          end: {
            line: 127,
            column: 44
          }
        },
        loc: {
          start: {
            line: 127,
            column: 55
          },
          end: {
            line: 203,
            column: 5
          }
        },
        line: 127
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 129,
            column: 33
          },
          end: {
            line: 129,
            column: 34
          }
        },
        loc: {
          start: {
            line: 129,
            column: 47
          },
          end: {
            line: 202,
            column: 9
          }
        },
        line: 129
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 152,
            column: 51
          },
          end: {
            line: 152,
            column: 52
          }
        },
        loc: {
          start: {
            line: 152,
            column: 71
          },
          end: {
            line: 194,
            column: 21
          }
        },
        line: 152
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 159,
            column: 46
          },
          end: {
            line: 159,
            column: 47
          }
        },
        loc: {
          start: {
            line: 159,
            column: 63
          },
          end: {
            line: 169,
            column: 33
          }
        },
        line: 159
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 185,
            column: 63
          },
          end: {
            line: 185,
            column: 64
          }
        },
        loc: {
          start: {
            line: 185,
            column: 80
          },
          end: {
            line: 188,
            column: 33
          }
        },
        line: 185
      },
      "24": {
        name: "getEnhancedCareerPathData",
        decl: {
          start: {
            line: 205,
            column: 9
          },
          end: {
            line: 205,
            column: 34
          }
        },
        loc: {
          start: {
            line: 205,
            column: 65
          },
          end: {
            line: 376,
            column: 1
          }
        },
        line: 205
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 206,
            column: 43
          },
          end: {
            line: 206,
            column: 44
          }
        },
        loc: {
          start: {
            line: 206,
            column: 55
          },
          end: {
            line: 375,
            column: 5
          }
        },
        line: 206
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 208,
            column: 33
          },
          end: {
            line: 208,
            column: 34
          }
        },
        loc: {
          start: {
            line: 208,
            column: 47
          },
          end: {
            line: 374,
            column: 9
          }
        },
        line: 208
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 322,
            column: 69
          },
          end: {
            line: 322,
            column: 70
          }
        },
        loc: {
          start: {
            line: 322,
            column: 86
          },
          end: {
            line: 328,
            column: 29
          }
        },
        line: 322
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 329,
            column: 76
          },
          end: {
            line: 329,
            column: 77
          }
        },
        loc: {
          start: {
            line: 329,
            column: 96
          },
          end: {
            line: 347,
            column: 25
          }
        },
        line: 329
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 333,
            column: 70
          },
          end: {
            line: 333,
            column: 71
          }
        },
        loc: {
          start: {
            line: 333,
            column: 88
          },
          end: {
            line: 333,
            column: 114
          }
        },
        line: 333
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 345,
            column: 108
          },
          end: {
            line: 345,
            column: 109
          }
        },
        loc: {
          start: {
            line: 345,
            column: 125
          },
          end: {
            line: 345,
            column: 147
          }
        },
        line: 345
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 348,
            column: 68
          },
          end: {
            line: 348,
            column: 69
          }
        },
        loc: {
          start: {
            line: 348,
            column: 84
          },
          end: {
            line: 358,
            column: 25
          }
        },
        line: 348
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 356,
            column: 104
          },
          end: {
            line: 356,
            column: 105
          }
        },
        loc: {
          start: {
            line: 356,
            column: 121
          },
          end: {
            line: 356,
            column: 143
          }
        },
        line: 356
      },
      "33": {
        name: "createSkillGapAnalysis",
        decl: {
          start: {
            line: 377,
            column: 9
          },
          end: {
            line: 377,
            column: 31
          }
        },
        loc: {
          start: {
            line: 377,
            column: 79
          },
          end: {
            line: 419,
            column: 1
          }
        },
        line: 377
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 378,
            column: 43
          },
          end: {
            line: 378,
            column: 44
          }
        },
        loc: {
          start: {
            line: 378,
            column: 55
          },
          end: {
            line: 418,
            column: 5
          }
        },
        line: 378
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 381,
            column: 33
          },
          end: {
            line: 381,
            column: 34
          }
        },
        loc: {
          start: {
            line: 381,
            column: 47
          },
          end: {
            line: 417,
            column: 9
          }
        },
        line: 381
      },
      "36": {
        name: "handleComprehensiveSkillsAnalysis",
        decl: {
          start: {
            line: 420,
            column: 9
          },
          end: {
            line: 420,
            column: 42
          }
        },
        loc: {
          start: {
            line: 420,
            column: 52
          },
          end: {
            line: 498,
            column: 1
          }
        },
        line: 420
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 421,
            column: 44
          },
          end: {
            line: 421,
            column: 45
          }
        },
        loc: {
          start: {
            line: 421,
            column: 56
          },
          end: {
            line: 497,
            column: 5
          }
        },
        line: 421
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 424,
            column: 33
          },
          end: {
            line: 424,
            column: 34
          }
        },
        loc: {
          start: {
            line: 424,
            column: 47
          },
          end: {
            line: 496,
            column: 9
          }
        },
        line: 424
      },
      "39": {
        name: "handleIndividualAnalysis",
        decl: {
          start: {
            line: 499,
            column: 9
          },
          end: {
            line: 499,
            column: 33
          }
        },
        loc: {
          start: {
            line: 499,
            column: 76
          },
          end: {
            line: 642,
            column: 1
          }
        },
        line: 499
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 500,
            column: 44
          },
          end: {
            line: 500,
            column: 45
          }
        },
        loc: {
          start: {
            line: 500,
            column: 56
          },
          end: {
            line: 641,
            column: 5
          }
        },
        line: 500
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 503,
            column: 33
          },
          end: {
            line: 503,
            column: 34
          }
        },
        loc: {
          start: {
            line: 503,
            column: 47
          },
          end: {
            line: 640,
            column: 9
          }
        },
        line: 503
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 531,
            column: 32
          },
          end: {
            line: 531,
            column: 33
          }
        },
        loc: {
          start: {
            line: 531,
            column: 54
          },
          end: {
            line: 531,
            column: 82
          }
        },
        line: 531
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 532,
            column: 29
          },
          end: {
            line: 532,
            column: 30
          }
        },
        loc: {
          start: {
            line: 532,
            column: 51
          },
          end: {
            line: 540,
            column: 21
          }
        },
        line: 532
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 541,
            column: 59
          },
          end: {
            line: 541,
            column: 60
          }
        },
        loc: {
          start: {
            line: 541,
            column: 81
          },
          end: {
            line: 551,
            column: 21
          }
        },
        line: 541
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 542,
            column: 48
          },
          end: {
            line: 542,
            column: 49
          }
        },
        loc: {
          start: {
            line: 542,
            column: 61
          },
          end: {
            line: 542,
            column: 132
          }
        },
        line: 542
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 552,
            column: 126
          },
          end: {
            line: 552,
            column: 127
          }
        },
        loc: {
          start: {
            line: 552,
            column: 138
          },
          end: {
            line: 622,
            column: 29
          }
        },
        line: 552
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 552,
            column: 180
          },
          end: {
            line: 552,
            column: 181
          }
        },
        loc: {
          start: {
            line: 552,
            column: 192
          },
          end: {
            line: 622,
            column: 25
          }
        },
        line: 552
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 554,
            column: 53
          },
          end: {
            line: 554,
            column: 54
          }
        },
        loc: {
          start: {
            line: 554,
            column: 67
          },
          end: {
            line: 621,
            column: 29
          }
        },
        line: 554
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 559,
            column: 80
          },
          end: {
            line: 559,
            column: 81
          }
        },
        loc: {
          start: {
            line: 559,
            column: 97
          },
          end: {
            line: 563,
            column: 53
          }
        },
        line: 559
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 644,
            column: 73
          },
          end: {
            line: 644,
            column: 74
          }
        },
        loc: {
          start: {
            line: 644,
            column: 92
          },
          end: {
            line: 653,
            column: 5
          }
        },
        line: 644
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 644,
            column: 136
          },
          end: {
            line: 644,
            column: 137
          }
        },
        loc: {
          start: {
            line: 644,
            column: 148
          },
          end: {
            line: 653,
            column: 1
          }
        },
        line: 644
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 645,
            column: 29
          },
          end: {
            line: 645,
            column: 30
          }
        },
        loc: {
          start: {
            line: 645,
            column: 43
          },
          end: {
            line: 652,
            column: 5
          }
        },
        line: 645
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 646,
            column: 70
          },
          end: {
            line: 646,
            column: 71
          }
        },
        loc: {
          start: {
            line: 646,
            column: 82
          },
          end: {
            line: 651,
            column: 17
          }
        },
        line: 646
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 646,
            column: 125
          },
          end: {
            line: 646,
            column: 126
          }
        },
        loc: {
          start: {
            line: 646,
            column: 137
          },
          end: {
            line: 651,
            column: 13
          }
        },
        line: 646
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 647,
            column: 41
          },
          end: {
            line: 647,
            column: 42
          }
        },
        loc: {
          start: {
            line: 647,
            column: 55
          },
          end: {
            line: 650,
            column: 17
          }
        },
        line: 647
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 649,
            column: 24
          },
          end: {
            line: 649,
            column: 25
          }
        },
        loc: {
          start: {
            line: 649,
            column: 36
          },
          end: {
            line: 649,
            column: 90
          }
        },
        line: 649
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 46,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 21
          },
          end: {
            line: 38,
            column: 25
          }
        }, {
          start: {
            line: 38,
            column: 29
          },
          end: {
            line: 38,
            column: 47
          }
        }, {
          start: {
            line: 38,
            column: 52
          },
          end: {
            line: 46,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 44,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 44,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 12
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 38
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 43,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 43,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "36": {
        loc: {
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 40,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 40,
            column: 14
          }
        }, {
          start: {
            line: 40,
            column: 18
          },
          end: {
            line: 40,
            column: 30
          }
        }],
        line: 40
      },
      "37": {
        loc: {
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 41,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 41,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "38": {
        loc: {
          start: {
            line: 45,
            column: 21
          },
          end: {
            line: 45,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 21
          },
          end: {
            line: 45,
            column: 23
          }
        }, {
          start: {
            line: 45,
            column: 27
          },
          end: {
            line: 45,
            column: 59
          }
        }],
        line: 45
      },
      "39": {
        loc: {
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 122,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 106,
            column: 28
          }
        }, {
          start: {
            line: 107,
            column: 16
          },
          end: {
            line: 116,
            column: 32
          }
        }, {
          start: {
            line: 117,
            column: 16
          },
          end: {
            line: 120,
            column: 46
          }
        }, {
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 121,
            column: 46
          }
        }],
        line: 92
      },
      "40": {
        loc: {
          start: {
            line: 130,
            column: 12
          },
          end: {
            line: 201,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 131,
            column: 16
          },
          end: {
            line: 144,
            column: 28
          }
        }, {
          start: {
            line: 145,
            column: 16
          },
          end: {
            line: 195,
            column: 66
          }
        }, {
          start: {
            line: 196,
            column: 16
          },
          end: {
            line: 199,
            column: 46
          }
        }, {
          start: {
            line: 200,
            column: 16
          },
          end: {
            line: 200,
            column: 46
          }
        }],
        line: 130
      },
      "41": {
        loc: {
          start: {
            line: 147,
            column: 20
          },
          end: {
            line: 149,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 20
          },
          end: {
            line: 149,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "42": {
        loc: {
          start: {
            line: 154,
            column: 40
          },
          end: {
            line: 156,
            column: 54
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 155,
            column: 34
          },
          end: {
            line: 155,
            column: 66
          }
        }, {
          start: {
            line: 156,
            column: 34
          },
          end: {
            line: 156,
            column: 54
          }
        }],
        line: 154
      },
      "43": {
        loc: {
          start: {
            line: 158,
            column: 28
          },
          end: {
            line: 170,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 28
          },
          end: {
            line: 170,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "44": {
        loc: {
          start: {
            line: 158,
            column: 32
          },
          end: {
            line: 158,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 158,
            column: 32
          },
          end: {
            line: 158,
            column: 84
          }
        }, {
          start: {
            line: 158,
            column: 88
          },
          end: {
            line: 158,
            column: 108
          }
        }],
        line: 158
      },
      "45": {
        loc: {
          start: {
            line: 161,
            column: 36
          },
          end: {
            line: 168,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 36
          },
          end: {
            line: 168,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "46": {
        loc: {
          start: {
            line: 161,
            column: 40
          },
          end: {
            line: 161,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 161,
            column: 40
          },
          end: {
            line: 161,
            column: 65
          }
        }, {
          start: {
            line: 161,
            column: 69
          },
          end: {
            line: 161,
            column: 81
          }
        }],
        line: 161
      },
      "47": {
        loc: {
          start: {
            line: 166,
            column: 54
          },
          end: {
            line: 166,
            column: 169
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 55
          },
          end: {
            line: 166,
            column: 140
          }
        }, {
          start: {
            line: 166,
            column: 145
          },
          end: {
            line: 166,
            column: 169
          }
        }],
        line: 166
      },
      "48": {
        loc: {
          start: {
            line: 166,
            column: 55
          },
          end: {
            line: 166,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 115
          },
          end: {
            line: 166,
            column: 121
          }
        }, {
          start: {
            line: 166,
            column: 124
          },
          end: {
            line: 166,
            column: 140
          }
        }],
        line: 166
      },
      "49": {
        loc: {
          start: {
            line: 166,
            column: 55
          },
          end: {
            line: 166,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 55
          },
          end: {
            line: 166,
            column: 95
          }
        }, {
          start: {
            line: 166,
            column: 99
          },
          end: {
            line: 166,
            column: 112
          }
        }],
        line: 166
      },
      "50": {
        loc: {
          start: {
            line: 172,
            column: 28
          },
          end: {
            line: 189,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 28
          },
          end: {
            line: 189,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "51": {
        loc: {
          start: {
            line: 172,
            column: 32
          },
          end: {
            line: 172,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 172,
            column: 32
          },
          end: {
            line: 172,
            column: 89
          }
        }, {
          start: {
            line: 172,
            column: 93
          },
          end: {
            line: 172,
            column: 118
          }
        }],
        line: 172
      },
      "52": {
        loc: {
          start: {
            line: 175,
            column: 32
          },
          end: {
            line: 183,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 32
          },
          end: {
            line: 183,
            column: 33
          }
        }, {
          start: {
            line: 178,
            column: 37
          },
          end: {
            line: 183,
            column: 33
          }
        }],
        line: 175
      },
      "53": {
        loc: {
          start: {
            line: 175,
            column: 36
          },
          end: {
            line: 175,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 175,
            column: 36
          },
          end: {
            line: 175,
            column: 70
          }
        }, {
          start: {
            line: 175,
            column: 74
          },
          end: {
            line: 175,
            column: 108
          }
        }],
        line: 175
      },
      "54": {
        loc: {
          start: {
            line: 178,
            column: 37
          },
          end: {
            line: 183,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 37
          },
          end: {
            line: 183,
            column: 33
          }
        }, {
          start: {
            line: 181,
            column: 37
          },
          end: {
            line: 183,
            column: 33
          }
        }],
        line: 178
      },
      "55": {
        loc: {
          start: {
            line: 178,
            column: 41
          },
          end: {
            line: 178,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 178,
            column: 41
          },
          end: {
            line: 178,
            column: 72
          }
        }, {
          start: {
            line: 178,
            column: 76
          },
          end: {
            line: 178,
            column: 116
          }
        }],
        line: 178
      },
      "56": {
        loc: {
          start: {
            line: 181,
            column: 37
          },
          end: {
            line: 183,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 37
          },
          end: {
            line: 183,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "57": {
        loc: {
          start: {
            line: 181,
            column: 41
          },
          end: {
            line: 181,
            column: 115
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 181,
            column: 41
          },
          end: {
            line: 181,
            column: 75
          }
        }, {
          start: {
            line: 181,
            column: 79
          },
          end: {
            line: 181,
            column: 115
          }
        }],
        line: 181
      },
      "58": {
        loc: {
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 373,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 210,
            column: 16
          },
          end: {
            line: 306,
            column: 28
          }
        }, {
          start: {
            line: 307,
            column: 16
          },
          end: {
            line: 367,
            column: 50
          }
        }, {
          start: {
            line: 368,
            column: 16
          },
          end: {
            line: 371,
            column: 48
          }
        }, {
          start: {
            line: 372,
            column: 16
          },
          end: {
            line: 372,
            column: 46
          }
        }],
        line: 209
      },
      "59": {
        loc: {
          start: {
            line: 213,
            column: 34
          },
          end: {
            line: 220,
            column: 25
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 26
          },
          end: {
            line: 214,
            column: 46
          }
        }, {
          start: {
            line: 215,
            column: 26
          },
          end: {
            line: 220,
            column: 25
          }
        }],
        line: 213
      },
      "60": {
        loc: {
          start: {
            line: 218,
            column: 40
          },
          end: {
            line: 218,
            column: 153
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 218,
            column: 95
          },
          end: {
            line: 218,
            column: 101
          }
        }, {
          start: {
            line: 218,
            column: 104
          },
          end: {
            line: 218,
            column: 153
          }
        }],
        line: 218
      },
      "61": {
        loc: {
          start: {
            line: 218,
            column: 40
          },
          end: {
            line: 218,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 218,
            column: 40
          },
          end: {
            line: 218,
            column: 63
          }
        }, {
          start: {
            line: 218,
            column: 67
          },
          end: {
            line: 218,
            column: 92
          }
        }],
        line: 218
      },
      "62": {
        loc: {
          start: {
            line: 311,
            column: 20
          },
          end: {
            line: 313,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 311,
            column: 20
          },
          end: {
            line: 313,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 311
      },
      "63": {
        loc: {
          start: {
            line: 312,
            column: 114
          },
          end: {
            line: 312,
            column: 144
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 312,
            column: 114
          },
          end: {
            line: 312,
            column: 126
          }
        }, {
          start: {
            line: 312,
            column: 130
          },
          end: {
            line: 312,
            column: 144
          }
        }],
        line: 312
      },
      "64": {
        loc: {
          start: {
            line: 314,
            column: 20
          },
          end: {
            line: 315,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 314,
            column: 20
          },
          end: {
            line: 315,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 314
      },
      "65": {
        loc: {
          start: {
            line: 327,
            column: 40
          },
          end: {
            line: 327,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 327,
            column: 40
          },
          end: {
            line: 327,
            column: 59
          }
        }, {
          start: {
            line: 327,
            column: 63
          },
          end: {
            line: 327,
            column: 67
          }
        }],
        line: 327
      },
      "66": {
        loc: {
          start: {
            line: 332,
            column: 48
          },
          end: {
            line: 334,
            column: 35
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 333,
            column: 34
          },
          end: {
            line: 333,
            column: 156
          }
        }, {
          start: {
            line: 334,
            column: 34
          },
          end: {
            line: 334,
            column: 35
          }
        }],
        line: 332
      },
      "67": {
        loc: {
          start: {
            line: 345,
            column: 40
          },
          end: {
            line: 345,
            column: 155
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 345,
            column: 41
          },
          end: {
            line: 345,
            column: 148
          }
        }, {
          start: {
            line: 345,
            column: 153
          },
          end: {
            line: 345,
            column: 155
          }
        }],
        line: 345
      },
      "68": {
        loc: {
          start: {
            line: 345,
            column: 41
          },
          end: {
            line: 345,
            column: 148
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 345,
            column: 92
          },
          end: {
            line: 345,
            column: 98
          }
        }, {
          start: {
            line: 345,
            column: 101
          },
          end: {
            line: 345,
            column: 148
          }
        }],
        line: 345
      },
      "69": {
        loc: {
          start: {
            line: 345,
            column: 41
          },
          end: {
            line: 345,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 345,
            column: 41
          },
          end: {
            line: 345,
            column: 72
          }
        }, {
          start: {
            line: 345,
            column: 76
          },
          end: {
            line: 345,
            column: 89
          }
        }],
        line: 345
      },
      "70": {
        loc: {
          start: {
            line: 356,
            column: 40
          },
          end: {
            line: 356,
            column: 151
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 356,
            column: 41
          },
          end: {
            line: 356,
            column: 144
          }
        }, {
          start: {
            line: 356,
            column: 149
          },
          end: {
            line: 356,
            column: 151
          }
        }],
        line: 356
      },
      "71": {
        loc: {
          start: {
            line: 356,
            column: 41
          },
          end: {
            line: 356,
            column: 144
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 356,
            column: 88
          },
          end: {
            line: 356,
            column: 94
          }
        }, {
          start: {
            line: 356,
            column: 97
          },
          end: {
            line: 356,
            column: 144
          }
        }],
        line: 356
      },
      "72": {
        loc: {
          start: {
            line: 356,
            column: 41
          },
          end: {
            line: 356,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 356,
            column: 41
          },
          end: {
            line: 356,
            column: 68
          }
        }, {
          start: {
            line: 356,
            column: 72
          },
          end: {
            line: 356,
            column: 85
          }
        }],
        line: 356
      },
      "73": {
        loc: {
          start: {
            line: 382,
            column: 12
          },
          end: {
            line: 416,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 383,
            column: 16
          },
          end: {
            line: 407,
            column: 28
          }
        }, {
          start: {
            line: 408,
            column: 16
          },
          end: {
            line: 410,
            column: 60
          }
        }, {
          start: {
            line: 411,
            column: 16
          },
          end: {
            line: 414,
            column: 34
          }
        }, {
          start: {
            line: 415,
            column: 16
          },
          end: {
            line: 415,
            column: 46
          }
        }],
        line: 382
      },
      "74": {
        loc: {
          start: {
            line: 390,
            column: 52
          },
          end: {
            line: 390,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 390,
            column: 52
          },
          end: {
            line: 390,
            column: 89
          }
        }, {
          start: {
            line: 390,
            column: 93
          },
          end: {
            line: 390,
            column: 97
          }
        }],
        line: 390
      },
      "75": {
        loc: {
          start: {
            line: 395,
            column: 43
          },
          end: {
            line: 395,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 395,
            column: 43
          },
          end: {
            line: 395,
            column: 65
          }
        }, {
          start: {
            line: 395,
            column: 69
          },
          end: {
            line: 395,
            column: 71
          }
        }],
        line: 395
      },
      "76": {
        loc: {
          start: {
            line: 396,
            column: 46
          },
          end: {
            line: 396,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 396,
            column: 46
          },
          end: {
            line: 396,
            column: 71
          }
        }, {
          start: {
            line: 396,
            column: 75
          },
          end: {
            line: 396,
            column: 77
          }
        }],
        line: 396
      },
      "77": {
        loc: {
          start: {
            line: 397,
            column: 44
          },
          end: {
            line: 397,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 397,
            column: 44
          },
          end: {
            line: 397,
            column: 71
          }
        }, {
          start: {
            line: 397,
            column: 75
          },
          end: {
            line: 397,
            column: 79
          }
        }],
        line: 397
      },
      "78": {
        loc: {
          start: {
            line: 399,
            column: 48
          },
          end: {
            line: 399,
            column: 139
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 399,
            column: 49
          },
          end: {
            line: 399,
            column: 132
          }
        }, {
          start: {
            line: 399,
            column: 137
          },
          end: {
            line: 399,
            column: 139
          }
        }],
        line: 399
      },
      "79": {
        loc: {
          start: {
            line: 399,
            column: 49
          },
          end: {
            line: 399,
            column: 132
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 399,
            column: 110
          },
          end: {
            line: 399,
            column: 116
          }
        }, {
          start: {
            line: 399,
            column: 119
          },
          end: {
            line: 399,
            column: 132
          }
        }],
        line: 399
      },
      "80": {
        loc: {
          start: {
            line: 399,
            column: 49
          },
          end: {
            line: 399,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 399,
            column: 49
          },
          end: {
            line: 399,
            column: 90
          }
        }, {
          start: {
            line: 399,
            column: 94
          },
          end: {
            line: 399,
            column: 107
          }
        }],
        line: 399
      },
      "81": {
        loc: {
          start: {
            line: 425,
            column: 12
          },
          end: {
            line: 495,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 426,
            column: 16
          },
          end: {
            line: 426,
            column: 100
          }
        }, {
          start: {
            line: 427,
            column: 16
          },
          end: {
            line: 435,
            column: 57
          }
        }, {
          start: {
            line: 436,
            column: 16
          },
          end: {
            line: 453,
            column: 103
          }
        }, {
          start: {
            line: 454,
            column: 16
          },
          end: {
            line: 464,
            column: 33
          }
        }, {
          start: {
            line: 465,
            column: 16
          },
          end: {
            line: 468,
            column: 27
          }
        }, {
          start: {
            line: 469,
            column: 16
          },
          end: {
            line: 473,
            column: 156
          }
        }, {
          start: {
            line: 474,
            column: 16
          },
          end: {
            line: 484,
            column: 28
          }
        }, {
          start: {
            line: 485,
            column: 16
          },
          end: {
            line: 485,
            column: 88
          }
        }, {
          start: {
            line: 486,
            column: 16
          },
          end: {
            line: 486,
            column: 49
          }
        }, {
          start: {
            line: 487,
            column: 16
          },
          end: {
            line: 490,
            column: 109
          }
        }, {
          start: {
            line: 491,
            column: 16
          },
          end: {
            line: 493,
            column: 49
          }
        }, {
          start: {
            line: 494,
            column: 16
          },
          end: {
            line: 494,
            column: 47
          }
        }],
        line: 425
      },
      "82": {
        loc: {
          start: {
            line: 429,
            column: 20
          },
          end: {
            line: 433,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 429,
            column: 20
          },
          end: {
            line: 433,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 429
      },
      "83": {
        loc: {
          start: {
            line: 429,
            column: 26
          },
          end: {
            line: 429,
            column: 138
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 429,
            column: 124
          },
          end: {
            line: 429,
            column: 130
          }
        }, {
          start: {
            line: 429,
            column: 133
          },
          end: {
            line: 429,
            column: 138
          }
        }],
        line: 429
      },
      "84": {
        loc: {
          start: {
            line: 429,
            column: 26
          },
          end: {
            line: 429,
            column: 121
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 429,
            column: 26
          },
          end: {
            line: 429,
            column: 104
          }
        }, {
          start: {
            line: 429,
            column: 108
          },
          end: {
            line: 429,
            column: 121
          }
        }],
        line: 429
      },
      "85": {
        loc: {
          start: {
            line: 429,
            column: 32
          },
          end: {
            line: 429,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 429,
            column: 73
          },
          end: {
            line: 429,
            column: 79
          }
        }, {
          start: {
            line: 429,
            column: 82
          },
          end: {
            line: 429,
            column: 94
          }
        }],
        line: 429
      },
      "86": {
        loc: {
          start: {
            line: 429,
            column: 32
          },
          end: {
            line: 429,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 429,
            column: 32
          },
          end: {
            line: 429,
            column: 48
          }
        }, {
          start: {
            line: 429,
            column: 52
          },
          end: {
            line: 429,
            column: 70
          }
        }],
        line: 429
      },
      "87": {
        loc: {
          start: {
            line: 439,
            column: 20
          },
          end: {
            line: 444,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 439,
            column: 20
          },
          end: {
            line: 444,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 439
      },
      "88": {
        loc: {
          start: {
            line: 450,
            column: 24
          },
          end: {
            line: 450,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 450,
            column: 56
          },
          end: {
            line: 450,
            column: 69
          }
        }, {
          start: {
            line: 450,
            column: 72
          },
          end: {
            line: 450,
            column: 74
          }
        }],
        line: 450
      },
      "89": {
        loc: {
          start: {
            line: 456,
            column: 20
          },
          end: {
            line: 463,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 456,
            column: 20
          },
          end: {
            line: 463,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 456
      },
      "90": {
        loc: {
          start: {
            line: 461,
            column: 45
          },
          end: {
            line: 461,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 461,
            column: 46
          },
          end: {
            line: 461,
            column: 112
          }
        }, {
          start: {
            line: 461,
            column: 117
          },
          end: {
            line: 461,
            column: 141
          }
        }],
        line: 461
      },
      "91": {
        loc: {
          start: {
            line: 461,
            column: 46
          },
          end: {
            line: 461,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 461,
            column: 85
          },
          end: {
            line: 461,
            column: 91
          }
        }, {
          start: {
            line: 461,
            column: 94
          },
          end: {
            line: 461,
            column: 112
          }
        }],
        line: 461
      },
      "92": {
        loc: {
          start: {
            line: 461,
            column: 46
          },
          end: {
            line: 461,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 461,
            column: 46
          },
          end: {
            line: 461,
            column: 61
          }
        }, {
          start: {
            line: 461,
            column: 65
          },
          end: {
            line: 461,
            column: 82
          }
        }],
        line: 461
      },
      "93": {
        loc: {
          start: {
            line: 471,
            column: 20
          },
          end: {
            line: 471,
            column: 92
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 471,
            column: 20
          },
          end: {
            line: 471,
            column: 92
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 471
      },
      "94": {
        loc: {
          start: {
            line: 471,
            column: 26
          },
          end: {
            line: 471,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 471,
            column: 26
          },
          end: {
            line: 471,
            column: 45
          }
        }, {
          start: {
            line: 471,
            column: 49
          },
          end: {
            line: 471,
            column: 65
          }
        }],
        line: 471
      },
      "95": {
        loc: {
          start: {
            line: 480,
            column: 36
          },
          end: {
            line: 480,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 480,
            column: 36
          },
          end: {
            line: 480,
            column: 54
          }
        }, {
          start: {
            line: 480,
            column: 58
          },
          end: {
            line: 480,
            column: 63
          }
        }],
        line: 480
      },
      "96": {
        loc: {
          start: {
            line: 485,
            column: 40
          },
          end: {
            line: 485,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 485,
            column: 40
          },
          end: {
            line: 485,
            column: 57
          }
        }, {
          start: {
            line: 485,
            column: 61
          },
          end: {
            line: 485,
            column: 86
          }
        }],
        line: 485
      },
      "97": {
        loc: {
          start: {
            line: 504,
            column: 12
          },
          end: {
            line: 639,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 505,
            column: 16
          },
          end: {
            line: 510,
            column: 24
          }
        }, {
          start: {
            line: 511,
            column: 16
          },
          end: {
            line: 514,
            column: 80
          }
        }, {
          start: {
            line: 515,
            column: 16
          },
          end: {
            line: 517,
            column: 44
          }
        }, {
          start: {
            line: 518,
            column: 16
          },
          end: {
            line: 520,
            column: 33
          }
        }, {
          start: {
            line: 521,
            column: 16
          },
          end: {
            line: 622,
            column: 40
          }
        }, {
          start: {
            line: 623,
            column: 16
          },
          end: {
            line: 626,
            column: 152
          }
        }, {
          start: {
            line: 627,
            column: 16
          },
          end: {
            line: 638,
            column: 28
          }
        }],
        line: 504
      },
      "98": {
        loc: {
          start: {
            line: 507,
            column: 24
          },
          end: {
            line: 509,
            column: 111
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 508,
            column: 30
          },
          end: {
            line: 508,
            column: 157
          }
        }, {
          start: {
            line: 509,
            column: 30
          },
          end: {
            line: 509,
            column: 111
          }
        }],
        line: 507
      },
      "99": {
        loc: {
          start: {
            line: 513,
            column: 20
          },
          end: {
            line: 513,
            column: 81
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 513,
            column: 20
          },
          end: {
            line: 513,
            column: 81
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 513
      },
      "100": {
        loc: {
          start: {
            line: 523,
            column: 42
          },
          end: {
            line: 529,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 523,
            column: 42
          },
          end: {
            line: 523,
            column: 56
          }
        }, {
          start: {
            line: 523,
            column: 60
          },
          end: {
            line: 529,
            column: 21
          }
        }],
        line: 523
      },
      "101": {
        loc: {
          start: {
            line: 535,
            column: 39
          },
          end: {
            line: 535,
            column: 133
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 535,
            column: 40
          },
          end: {
            line: 535,
            column: 108
          }
        }, {
          start: {
            line: 535,
            column: 113
          },
          end: {
            line: 535,
            column: 133
          }
        }],
        line: 535
      },
      "102": {
        loc: {
          start: {
            line: 535,
            column: 40
          },
          end: {
            line: 535,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 535,
            column: 92
          },
          end: {
            line: 535,
            column: 98
          }
        }, {
          start: {
            line: 535,
            column: 101
          },
          end: {
            line: 535,
            column: 108
          }
        }],
        line: 535
      },
      "103": {
        loc: {
          start: {
            line: 535,
            column: 40
          },
          end: {
            line: 535,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 535,
            column: 40
          },
          end: {
            line: 535,
            column: 72
          }
        }, {
          start: {
            line: 535,
            column: 76
          },
          end: {
            line: 535,
            column: 89
          }
        }],
        line: 535
      },
      "104": {
        loc: {
          start: {
            line: 538,
            column: 38
          },
          end: {
            line: 538,
            column: 211
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 538,
            column: 39
          },
          end: {
            line: 538,
            column: 120
          }
        }, {
          start: {
            line: 538,
            column: 126
          },
          end: {
            line: 538,
            column: 210
          }
        }],
        line: 538
      },
      "105": {
        loc: {
          start: {
            line: 538,
            column: 39
          },
          end: {
            line: 538,
            column: 120
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 538,
            column: 95
          },
          end: {
            line: 538,
            column: 101
          }
        }, {
          start: {
            line: 538,
            column: 104
          },
          end: {
            line: 538,
            column: 120
          }
        }],
        line: 538
      },
      "106": {
        loc: {
          start: {
            line: 538,
            column: 39
          },
          end: {
            line: 538,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 538,
            column: 39
          },
          end: {
            line: 538,
            column: 75
          }
        }, {
          start: {
            line: 538,
            column: 79
          },
          end: {
            line: 538,
            column: 92
          }
        }],
        line: 538
      },
      "107": {
        loc: {
          start: {
            line: 538,
            column: 126
          },
          end: {
            line: 538,
            column: 210
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 538,
            column: 185
          },
          end: {
            line: 538,
            column: 191
          }
        }, {
          start: {
            line: 538,
            column: 194
          },
          end: {
            line: 538,
            column: 210
          }
        }],
        line: 538
      },
      "108": {
        loc: {
          start: {
            line: 538,
            column: 126
          },
          end: {
            line: 538,
            column: 182
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 538,
            column: 126
          },
          end: {
            line: 538,
            column: 165
          }
        }, {
          start: {
            line: 538,
            column: 169
          },
          end: {
            line: 538,
            column: 182
          }
        }],
        line: 538
      },
      "109": {
        loc: {
          start: {
            line: 543,
            column: 24
          },
          end: {
            line: 549,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 543,
            column: 24
          },
          end: {
            line: 549,
            column: 25
          }
        }, {
          start: {
            line: 546,
            column: 29
          },
          end: {
            line: 549,
            column: 25
          }
        }],
        line: 543
      },
      "110": {
        loc: {
          start: {
            line: 546,
            column: 29
          },
          end: {
            line: 549,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 546,
            column: 29
          },
          end: {
            line: 549,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 546
      },
      "111": {
        loc: {
          start: {
            line: 546,
            column: 33
          },
          end: {
            line: 546,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 546,
            column: 33
          },
          end: {
            line: 546,
            column: 49
          }
        }, {
          start: {
            line: 546,
            column: 53
          },
          end: {
            line: 546,
            column: 98
          }
        }],
        line: 546
      },
      "112": {
        loc: {
          start: {
            line: 546,
            column: 73
          },
          end: {
            line: 546,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 546,
            column: 73
          },
          end: {
            line: 546,
            column: 92
          }
        }, {
          start: {
            line: 546,
            column: 96
          },
          end: {
            line: 546,
            column: 97
          }
        }],
        line: 546
      },
      "113": {
        loc: {
          start: {
            line: 555,
            column: 32
          },
          end: {
            line: 620,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 556,
            column: 36
          },
          end: {
            line: 576,
            column: 44
          }
        }, {
          start: {
            line: 577,
            column: 36
          },
          end: {
            line: 600,
            column: 218
          }
        }, {
          start: {
            line: 601,
            column: 36
          },
          end: {
            line: 619,
            column: 76
          }
        }],
        line: 555
      },
      "114": {
        loc: {
          start: {
            line: 565,
            column: 59
          },
          end: {
            line: 568,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 565,
            column: 114
          },
          end: {
            line: 565,
            column: 115
          }
        }, {
          start: {
            line: 566,
            column: 52
          },
          end: {
            line: 568,
            column: 119
          }
        }],
        line: 565
      },
      "115": {
        loc: {
          start: {
            line: 566,
            column: 52
          },
          end: {
            line: 568,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 566,
            column: 105
          },
          end: {
            line: 566,
            column: 106
          }
        }, {
          start: {
            line: 567,
            column: 56
          },
          end: {
            line: 568,
            column: 119
          }
        }],
        line: 566
      },
      "116": {
        loc: {
          start: {
            line: 567,
            column: 56
          },
          end: {
            line: 568,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 567,
            column: 107
          },
          end: {
            line: 567,
            column: 109
          }
        }, {
          start: {
            line: 568,
            column: 60
          },
          end: {
            line: 568,
            column: 119
          }
        }],
        line: 567
      },
      "117": {
        loc: {
          start: {
            line: 568,
            column: 60
          },
          end: {
            line: 568,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 568,
            column: 112
          },
          end: {
            line: 568,
            column: 114
          }
        }, {
          start: {
            line: 568,
            column: 117
          },
          end: {
            line: 568,
            column: 119
          }
        }],
        line: 568
      },
      "118": {
        loc: {
          start: {
            line: 571,
            column: 56
          },
          end: {
            line: 573,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 571,
            column: 100
          },
          end: {
            line: 571,
            column: 101
          }
        }, {
          start: {
            line: 572,
            column: 52
          },
          end: {
            line: 573,
            column: 110
          }
        }],
        line: 571
      },
      "119": {
        loc: {
          start: {
            line: 572,
            column: 52
          },
          end: {
            line: 573,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 572,
            column: 100
          },
          end: {
            line: 572,
            column: 103
          }
        }, {
          start: {
            line: 573,
            column: 56
          },
          end: {
            line: 573,
            column: 110
          }
        }],
        line: 572
      },
      "120": {
        loc: {
          start: {
            line: 573,
            column: 56
          },
          end: {
            line: 573,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 573,
            column: 100
          },
          end: {
            line: 573,
            column: 104
          }
        }, {
          start: {
            line: 573,
            column: 107
          },
          end: {
            line: 573,
            column: 110
          }
        }],
        line: 573
      },
      "121": {
        loc: {
          start: {
            line: 579,
            column: 40
          },
          end: {
            line: 599,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 579,
            column: 40
          },
          end: {
            line: 599,
            column: 41
          }
        }, {
          start: {
            line: 582,
            column: 45
          },
          end: {
            line: 599,
            column: 41
          }
        }],
        line: 579
      },
      "122": {
        loc: {
          start: {
            line: 579,
            column: 44
          },
          end: {
            line: 579,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 579,
            column: 44
          },
          end: {
            line: 579,
            column: 81
          }
        }, {
          start: {
            line: 579,
            column: 85
          },
          end: {
            line: 579,
            column: 113
          }
        }],
        line: 579
      },
      "123": {
        loc: {
          start: {
            line: 582,
            column: 45
          },
          end: {
            line: 599,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 582,
            column: 45
          },
          end: {
            line: 599,
            column: 41
          }
        }, {
          start: {
            line: 586,
            column: 45
          },
          end: {
            line: 599,
            column: 41
          }
        }],
        line: 582
      },
      "124": {
        loc: {
          start: {
            line: 582,
            column: 49
          },
          end: {
            line: 582,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 582,
            column: 49
          },
          end: {
            line: 582,
            column: 86
          }
        }, {
          start: {
            line: 582,
            column: 90
          },
          end: {
            line: 582,
            column: 118
          }
        }],
        line: 582
      },
      "125": {
        loc: {
          start: {
            line: 587,
            column: 59
          },
          end: {
            line: 587,
            column: 139
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 587,
            column: 99
          },
          end: {
            line: 587,
            column: 132
          }
        }, {
          start: {
            line: 587,
            column: 135
          },
          end: {
            line: 587,
            column: 139
          }
        }],
        line: 587
      },
      "126": {
        loc: {
          start: {
            line: 588,
            column: 44
          },
          end: {
            line: 597,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 588,
            column: 44
          },
          end: {
            line: 597,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 588
      },
      "127": {
        loc: {
          start: {
            line: 605,
            column: 55
          },
          end: {
            line: 605,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 605,
            column: 55
          },
          end: {
            line: 605,
            column: 89
          }
        }, {
          start: {
            line: 605,
            column: 93
          },
          end: {
            line: 605,
            column: 95
          }
        }],
        line: 605
      },
      "128": {
        loc: {
          start: {
            line: 606,
            column: 58
          },
          end: {
            line: 610,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 606,
            column: 58
          },
          end: {
            line: 606,
            column: 95
          }
        }, {
          start: {
            line: 606,
            column: 99
          },
          end: {
            line: 610,
            column: 45
          }
        }],
        line: 606
      },
      "129": {
        loc: {
          start: {
            line: 611,
            column: 61
          },
          end: {
            line: 616,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 611,
            column: 61
          },
          end: {
            line: 611,
            column: 101
          }
        }, {
          start: {
            line: 611,
            column: 105
          },
          end: {
            line: 616,
            column: 45
          }
        }],
        line: 611
      },
      "130": {
        loc: {
          start: {
            line: 617,
            column: 60
          },
          end: {
            line: 617,
            column: 143
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 617,
            column: 92
          },
          end: {
            line: 617,
            column: 131
          }
        }, {
          start: {
            line: 617,
            column: 134
          },
          end: {
            line: 617,
            column: 143
          }
        }],
        line: 617
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0, 0],
      "40": [0, 0, 0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0, 0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0, 0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0],
      "94": [0, 0],
      "95": [0, 0],
      "96": [0, 0],
      "97": [0, 0, 0, 0, 0, 0, 0],
      "98": [0, 0],
      "99": [0, 0],
      "100": [0, 0],
      "101": [0, 0],
      "102": [0, 0],
      "103": [0, 0],
      "104": [0, 0],
      "105": [0, 0],
      "106": [0, 0],
      "107": [0, 0],
      "108": [0, 0],
      "109": [0, 0],
      "110": [0, 0],
      "111": [0, 0],
      "112": [0, 0],
      "113": [0, 0, 0],
      "114": [0, 0],
      "115": [0, 0],
      "116": [0, 0],
      "117": [0, 0],
      "118": [0, 0],
      "119": [0, 0],
      "120": [0, 0],
      "121": [0, 0],
      "122": [0, 0],
      "123": [0, 0],
      "124": [0, 0],
      "125": [0, 0],
      "126": [0, 0],
      "127": [0, 0],
      "128": [0, 0],
      "129": [0, 0],
      "130": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/ai/skills-analysis/comprehensive/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,8DAA6D;AAC7D,wFAA8E;AAC9E,6EAAwF;AACxF,6CAAgD;AAChD,mCAAgD;AAChD,uCAAsC;AACtC,2BAAwB;AACxB,iFAAqF;AACrF,8EAA6E;AAC7E,oFAAiF;AAEjF,0FAAuF;AAEvF,wDAAwD;AACxD,IAAM,iCAAiC,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QAC9B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC9B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;QACtD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QACrC,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC/B,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;KACxD,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,iCAAiC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,CAAC,EAAE,oBAAoB;IAE9F,gBAAgB,EAAE,OAAC,CAAC,MAAM,CAAC;QACzB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QACnC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,8BAA8B,CAAC;QACjE,WAAW,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KACxE,CAAC;IAEF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;QACpB,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QACpF,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QACvC,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;QACzD,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;QAClE,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;KACvD,CAAC;IAEF,iBAAiB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAC5C,wBAAwB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CACpD,CAAC,CAAC;AA0DH,SAAe,uBAAuB,CAAC,MAAc;;;;;;;oBAE7B,qBAAM,eAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;4BACxD,KAAK,EAAE;gCACL,MAAM,QAAA;gCACN,QAAQ,EAAE,IAAI;6BACf;4BACD,OAAO,EAAE;gCACP,KAAK,EAAE,IAAI;6BACZ;4BACD,OAAO,EAAE;gCACP,cAAc,EAAE,MAAM;6BACvB;yBACF,CAAC,EAAA;;oBAXI,WAAW,GAAG,SAWlB;oBAEF,sBAAO,WAAW,CAAC,GAAG,CAAC,UAAA,UAAU,IAAI,OAAA,CAAC;4BACpC,OAAO,EAAE,UAAU,CAAC,OAAO;4BAC3B,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI;4BAChC,UAAU,EAAE,UAAU,CAAC,UAAU;4BACjC,eAAe,EAAE,UAAU,CAAC,eAAe;4BAC3C,YAAY,EAAE,UAAU,CAAC,cAAc;4BACvC,cAAc,EAAE,UAAU,CAAC,cAAc;yBAC1C,CAAC,EAPmC,CAOnC,CAAC,EAAC;;;oBAEJ,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,OAAK,CAAC,CAAC;oBAC/D,sBAAO,EAAE,EAAC;;;;;CAEb;AAED,SAAe,6BAA6B,CAAC,MAAc;;;;;;;oBAEpC,qBAAM,eAAM,CAAC,UAAU,CAAC,SAAS,CAAC;4BACnD,KAAK,EAAE;gCACL,MAAM,QAAA;gCACN,MAAM,EAAE,WAAW;6BACpB;4BACD,OAAO,EAAE;gCACP,SAAS,EAAE,IAAI;6BAChB;4BACD,OAAO,EAAE;gCACP,WAAW,EAAE,MAAM;6BACpB;yBACF,CAAC,EAAA;;oBAXI,eAAa,SAWjB;oBAEF,IAAI,CAAC,YAAU,EAAE,CAAC;wBAChB,sBAAO,EAAE,EAAC;oBACZ,CAAC;oBAEK,yBAKD,EAAE,CAAC;oBAER,2CAA2C;oBAC3C,YAAU,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;wBACnC,IAAI,CAAC;4BACH,IAAM,KAAK,GAAG,OAAO,QAAQ,CAAC,WAAW,KAAK,QAAQ;gCACpD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;gCAClC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;4BAEzB,oCAAoC;4BACpC,IAAI,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gCACjF,KAAK,CAAC,OAAO,CAAC,UAAC,KAAa;;oCAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;wCAC9C,sBAAoB,CAAC,IAAI,CAAC;4CACxB,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE;4CACvB,UAAU,EAAE,CAAC,EAAE,0BAA0B;4CACzC,eAAe,EAAE,CAAC,EAAE,8BAA8B;4CAClD,QAAQ,EAAE,CAAA,MAAA,YAAU,CAAC,WAAW,0CAAE,WAAW,EAAE,KAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yCAC5E,CAAC,CAAC;oCACL,CAAC;gCACH,CAAC,CAAC,CAAC;4BACL,CAAC;4BAED,uCAAuC;4BACvC,IAAI,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gCAC3F,IAAM,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gCAC5C,IAAI,eAAa,GAAG,CAAC,CAAC;gCACtB,IAAI,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oCAC7E,eAAa,GAAG,CAAC,CAAC;gCACpB,CAAC;qCAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;oCACvF,eAAa,GAAG,CAAC,CAAC;gCACpB,CAAC;qCAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;oCACtF,eAAa,GAAG,CAAC,CAAC;gCACpB,CAAC;gCAED,qCAAqC;gCACrC,sBAAoB,CAAC,OAAO,CAAC,UAAA,KAAK;oCAChC,KAAK,CAAC,UAAU,GAAG,eAAa,CAAC;oCACjC,KAAK,CAAC,eAAe,GAAG,eAAa,CAAC;gCACxC,CAAC,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;wBAC7D,CAAC;oBACH,CAAC,CAAC,CAAC;oBAEH,sBAAO,sBAAoB,EAAC;;;oBAE5B,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,OAAK,CAAC,CAAC;oBACtE,sBAAO,EAAE,EAAC;;;;;CAEb;AAED,SAAe,yBAAyB,CAAC,YAAqB,EAAE,cAAuB;;;;;;;oBAE7E,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAEvB,WAAW,GAAG,YAAY;wBAC9B,CAAC,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE;wBACtB,CAAC,CAAC;4BACE,EAAE,EAAE;gCACF,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,cAAc,EAAE,IAAI,EAAE,aAAsB,EAAE,EAAE;gCACpE,EAAE,IAAI,EAAE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;6BAC7D;yBACF,CAAC;oBAGa,qBAAM,eAAM,CAAC,UAAU,CAAC,SAAS,CAAC;4BACnD,KAAK,EAAE,WAAW;4BAClB,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,IAAI,EAAE,IAAI;gCACV,IAAI,EAAE,IAAI;gCACV,QAAQ,EAAE,IAAI;gCACd,oDAAoD;gCACpD,aAAa,EAAE;oCACb,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,IAAI,EAAE,IAAI;wCACV,QAAQ,EAAE,IAAI;wCACd,WAAW,EAAE,IAAI;wCACjB,UAAU,EAAE;4CACV,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;4CACzB,OAAO,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;4CAC7B,IAAI,EAAE,CAAC;4CACP,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,mBAAmB,EAAE,IAAI;gDACzB,WAAW,EAAE,IAAI;gDACjB,WAAW,EAAE,IAAI;gDACjB,QAAQ,EAAE,IAAI;6CACf;yCACF;qCACF;oCACD,IAAI,EAAE,EAAE,CAAC,0CAA0C;iCACpD;gCACD,qDAAqD;gCACrD,iBAAiB,EAAE;oCACjB,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;oCACzB,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,KAAK,EAAE,IAAI;wCACX,IAAI,EAAE,IAAI;wCACV,UAAU,EAAE,IAAI;wCAChB,IAAI,EAAE,IAAI;wCACV,QAAQ,EAAE,IAAI;wCACd,GAAG,EAAE,IAAI;wCACT,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,IAAI,EAAE,IAAI;6CACX;4CACD,IAAI,EAAE,CAAC,CAAC,4BAA4B;yCACrC;wCACD,OAAO,EAAE;4CACP,MAAM,EAAE;gDACN,MAAM,EAAE,IAAI;6CACb;4CACD,IAAI,EAAE,GAAG,CAAC,wCAAwC;yCACnD;qCACF;oCACD,IAAI,EAAE,EAAE,EAAE,2BAA2B;oCACrC,OAAO,EAAE;wCACP,EAAE,UAAU,EAAE,KAAK,EAAE;wCACrB,EAAE,IAAI,EAAE,KAAK,EAAE;qCAChB;iCACF;gCACD,oDAAoD;gCACpD,aAAa,EAAE;oCACb,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;oCACzB,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,KAAK,EAAE,IAAI;wCACX,UAAU,EAAE,IAAI;wCAChB,cAAc,EAAE,IAAI;wCACpB,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,IAAI,EAAE,IAAI;6CACX;4CACD,IAAI,EAAE,EAAE,CAAC,wBAAwB;yCAClC;wCACD,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,KAAK,EAAE,IAAI;6CACZ;yCACF;qCACF;oCACD,IAAI,EAAE,EAAE,EAAE,uBAAuB;oCACjC,OAAO,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE;iCAC/B;6BACF;yBACF,CAAC,EAAA;;oBArFI,UAAU,GAAG,SAqFjB;oBAEI,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;oBAEzC,yCAAyC;oBACzC,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC;wBACrB,OAAO,CAAC,IAAI,CAAC,gDAAyC,SAAS,oBAAU,YAAY,IAAI,cAAc,CAAE,CAAC,CAAC;oBAC7G,CAAC;oBAED,IAAI,CAAC,UAAU;wBAAE,sBAAO,IAAI,EAAC;oBAGvB,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAEhC,MAAM,GAAG;wBACb,EAAE,EAAE,UAAU,CAAC,EAAE;wBACjB,IAAI,EAAE,UAAU,CAAC,IAAI;wBACrB,IAAI,EAAE,UAAU,CAAC,IAAI;wBACrB,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,cAAc,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,CAAC;4BACrD,EAAE,EAAE,KAAK,CAAC,EAAE;4BACZ,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;4BACxB,WAAW,EAAE,KAAK,CAAC,WAAW;4BAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI;yBACxC,CAAC,EANoD,CAMpD,CAAC;wBACH,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAA,QAAQ;;4BAC1D,+BAA+B;4BAC/B,IAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;gCAC/C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,MAAM,EAAd,CAAc,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;gCAC1G,CAAC,CAAC,CAAC,CAAC;4BAEN,OAAO;gCACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gCACf,KAAK,EAAE,QAAQ,CAAC,KAAK;gCACrB,IAAI,EAAE,QAAQ,CAAC,IAAI;gCACnB,UAAU,EAAE,QAAQ,CAAC,UAAU;gCAC/B,IAAI,EAAE,QAAQ,CAAC,IAAI;gCACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gCAC3B,GAAG,EAAE,QAAQ,CAAC,GAAG;gCACjB,aAAa,eAAA;gCACb,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM;gCACpC,MAAM,EAAE,CAAA,MAAA,QAAQ,CAAC,MAAM,0CAAE,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,EAAV,CAAU,CAAC,KAAI,EAAE;6BACxD,CAAC;wBACJ,CAAC,CAAC;wBACF,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,UAAA,IAAI;;4BAAI,OAAA,CAAC;gCACnD,EAAE,EAAE,IAAI,CAAC,EAAE;gCACX,KAAK,EAAE,IAAI,CAAC,KAAK;gCACjB,UAAU,EAAE,IAAI,CAAC,UAAU;gCAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;gCACnC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gCAC5B,MAAM,EAAE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,EAAV,CAAU,CAAC,KAAI,EAAE;6BACpD,CAAC,CAAA;yBAAA,CAAC;wBACH,WAAW,EAAE;4BACX,SAAS,WAAA;4BACT,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,kBAAkB;4BAC9C,WAAW,EAAE,UAAU,CAAC,aAAa,CAAC,MAAM;4BAC5C,cAAc,EAAE,UAAU,CAAC,iBAAiB,CAAC,MAAM;4BACnD,UAAU,EAAE,UAAU,CAAC,aAAa,CAAC,MAAM;yBAC5C;qBACF,CAAC;oBAEF,sBAAO,MAAM,EAAC;;;oBAEd,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,OAAK,CAAC,CAAC;oBAClE,sBAAO,IAAI,EAAC;;;;;CAEf;AAED,SAAe,sBAAsB,CACnC,MAAc,EACd,OAA2C,EAC3C,YAAiB,EACjB,cAAmB;;;;;;;;oBAGX,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC7B,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,8BAA8B;oBAEnD,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;4BAC5D,IAAI,EAAE;gCACJ,MAAM,QAAA;gCACN,kBAAkB,EAAE,OAAO,CAAC,gBAAgB,CAAC,YAAY,IAAI,IAAI;gCACjE,oBAAoB,EAAE,OAAO,CAAC,gBAAgB,CAAC,cAAc;gCAC7D,eAAe,EAAE,OAAO,CAAC,gBAAgB,CAAC,WAAW;gCACrD,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,SAAS;gCACxC,YAAY,EAAE,YAAY;gCAC1B,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI,EAAE;gCACvC,YAAY,EAAE,YAAY,CAAC,YAAY,IAAI,EAAE;gCAC7C,UAAU,EAAE,YAAY,CAAC,cAAc,IAAI,IAAI;gCAC/C,gBAAgB,EAAE;oCAChB,UAAU,EAAE,CAAA,MAAA,YAAY,CAAC,YAAY,0CAAE,UAAU,KAAI,EAAE;oCACvD,mBAAmB,EAAE,EAAE;oCACvB,YAAY,EAAE,UAAU;iCACzB;gCACD,MAAM,EAAE,QAAQ;gCAChB,oBAAoB,EAAE,CAAC;gCACvB,SAAS,WAAA;6BACV;yBACF,CAAC,EAAA;;oBApBI,gBAAgB,GAAG,SAoBvB;oBAEF,sBAAO,gBAAgB,EAAC;;;oBAExB,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,OAAK,CAAC,CAAC;oBAC3D,MAAM,OAAK,CAAC;;;;;CAEf;AAED,SAAe,iCAAiC,CAAC,OAAoB;mCAAG,OAAO;;;;;wBAC7D,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;oBAA7C,OAAO,GAAG,SAAmC;oBACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;wBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;wBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;wBACvB,MAAM,KAAK,CAAC;oBACd,CAAC;oBAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAElB,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;oBAA3B,IAAI,GAAG,SAAoB;oBAC3B,UAAU,GAAG,iCAAiC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAErE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;wBAClB,KAAK,GAAG,IAAI,KAAK,CAAC,sBAAsB,CAAQ,CAAC;wBACvD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;wBACvB,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC;wBACxC,MAAM,KAAK,CAAC;oBACd,CAAC;oBAEK,WAAW,GAAG,UAAU,CAAC,IAAI,CAAC;oBAG9B,QAAQ,GAAG,6BAAsB,MAAM,4BAAkB,WAAW,CAAC,gBAAgB,CAAC,cAAc,cAAI,WAAW,CAAC,gBAAgB,CAAC,WAAW,cAAI,WAAW,CAAC,WAAW,CAAC,SAAS,CAAE,CAAC;oBAExL,SAAS,GAAG;wBAChB,gBAAgB;wBAChB,aAAa;wBACb,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;wBAClD,MAAM;qBACP,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAGH,qBAAM,8CAAiB,CAAC,GAAG,CAAM,QAAQ,CAAC,EAAA;;oBAAnD,MAAM,GAAG,SAA0C;oBACzD,IAAI,MAAM,EAAE,CAAC;wBACX,sBAAO,qBAAY,CAAC,IAAI,CAAC;gCACvB,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE,MAAqD;gCAC3D,MAAM,EAAE,IAAI;gCACZ,WAAW,EAAE,CAAC,MAAc,aAAd,MAAM,uBAAN,MAAM,CAAU,WAAW,KAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;6BACtE,CAAC,EAAC;oBACL,CAAC;;;;oBAIqB,qBAAM,iDAAsB,CAAC,0BAA0B,CACzE,MAAM,EACN,WAAW,EACX,QAAQ,CAAC,WAAW;yBACrB,EAAA;;oBAJK,WAAW,GAAG,SAInB;yBAEG,CAAA,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,CAAA,EAAvC,wBAAuC;oBACzC,yCAAyC;oBACzC,qBAAM,8CAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAA;;oBAD1F,yCAAyC;oBACzC,SAA0F,CAAC,CAAC,aAAa;oBAEzG,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,WAAW,CAAC,IAAI;4BACtB,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,KAAK;4BACnC,cAAc,EAAE,IAAI;4BACpB,cAAc,EAAE,WAAW,CAAC,cAAc;4BAC1C,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACtC,CAAC,EAAC;wBAEH,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,yBAAyB,CAAC,CAAC;;;;oBAGlE,OAAO,CAAC,IAAI,CAAC,iEAAiE,EAAE,YAAU,CAAC,CAAC;oBAGrF,qBAAM,wBAAwB,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAA;;gBAD/E,mDAAmD;gBACnD,sBAAO,SAAwE,EAAC;;;;;CAEnF;AAED,SAAe,wBAAwB,CACrC,MAAc,EACd,WAA+C,EAC/C,QAAgB,EAChB,SAAmB;mCAClB,OAAO;;;;;wBAGkC,qBAAM,OAAO,CAAC,GAAG,CAAC;wBAC1D,uDAAyB,CAAC,6BAA6B,CAAC,MAAM,CAAC;wBAC/D,WAAW,CAAC,gBAAgB,CAAC,YAAY;4BACvC,CAAC,CAAC,uDAAyB,CAAC,4BAA4B,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC;4BACnG,CAAC,CAAC,yBAAyB,CAAC,SAAS,EAAE,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC;qBACtF,CAAC,EAAA;;oBALI,KAAoC,SAKxC,EALK,eAAe,QAAA,EAAE,cAAc,QAAA;yBAQP,CAAA,eAAe,CAAC,MAAM,KAAK,CAAC,CAAA,EAA5B,wBAA4B;oBACvD,qBAAM,6BAA6B,CAAC,MAAM,CAAC,EAAA;;oBAA3C,KAAA,SAA2C,CAAA;;;oBAC3C,KAAA,EAAE,CAAA;;;oBAFA,sBAAsB,KAEtB;oBAGA,mBAAmB,GAAG,cAAc,IAAI;wBAC5C,EAAE,EAAE,yBAAyB;wBAC7B,IAAI,EAAE,WAAW,CAAC,gBAAgB,CAAC,cAAc;wBACjD,cAAc,EAAE,EAAE;wBAClB,iBAAiB,EAAE,EAAE;wBACrB,aAAa,EAAE,EAAE;qBAClB,CAAC;oBAGI,gBAAgB,iDACjB,WAAW,CAAC,aAAa,SACzB,eAAe;yBACf,MAAM,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,CAAC,KAAK,EAAhB,CAAgB,CAAC,CAAC,2CAA2C;yBAClF,GAAG,CAAC,UAAA,UAAU;;wBAAI,OAAA,CAAC;4BAClB,SAAS,EAAE,CAAA,MAAA,UAAU,CAAC,KAAK,0CAAE,IAAI,KAAI,UAAU,CAAC,SAAS;4BACzD,UAAU,EAAE,UAAU,CAAC,UAAU;4BACjC,eAAe,EAAE,UAAU,CAAC,eAAe;4BAC3C,QAAQ,EAAE,CAAA,MAAA,UAAU,CAAC,SAAS,0CAAE,WAAW,EAAE,MAAI,MAAA,UAAU,CAAC,YAAY,0CAAE,WAAW,EAAE,CAAA;yBACxF,CAAC,CAAA;qBAAA,CAAC,SACF,sBAAsB,OAC1B,CAAC;oBAGI,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK;wBACtD,IAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,EAA3D,CAA2D,CAAC,CAAC;wBAC5F,IAAI,CAAC,QAAQ,EAAE,CAAC;4BACd,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAClB,CAAC;6BAAM,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC;4BAC7E,sCAAsC;4BACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;wBACjC,CAAC;wBACD,OAAO,GAAG,CAAC;oBACb,CAAC,EAAE,EAA6B,CAAC,CAAC;oBAGb,qBAAM,kDAA0B,CAAC,oBAAoB,CACxE,WAAW,EACX;;;;4CAE2C,qBAAM,OAAO,CAAC,UAAU,CAAC;4CAChE,+CAAsB,CAAC,4BAA4B,CAAC;gDAClD,MAAM,QAAA;gDACN,aAAa,EAAE,YAAY,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,CAAC;oDACxC,KAAK,EAAE,KAAK,CAAC,SAAS;oDACtB,KAAK,EAAE,KAAK,CAAC,UAAU;oDACvB,UAAU,EAAE,KAAK,CAAC,eAAe;iDAClC,CAAC,EAJuC,CAIvC,CAAC;gDACH,UAAU,EAAE,WAAW,CAAC,gBAAgB,CAAC,cAAc;gDACvD,SAAS,EAAE,WAAW,CAAC,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oDAC3D,WAAW,CAAC,WAAW,CAAC,SAAS,KAAK,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wDACxD,WAAW,CAAC,WAAW,CAAC,SAAS,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;4DACvD,WAAW,CAAC,WAAW,CAAC,SAAS,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gDACrE,aAAa,EAAE,UAAU;gDACzB,YAAY,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY;gDAClD,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oDAChD,WAAW,CAAC,WAAW,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wDACrD,WAAW,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;6CAC9D,CAAC;4CACF,6BAAa,CAAC,4BAA4B,CACxC,YAAY,EACZ,WAAW,CAAC,gBAAgB,EAC5B,WAAW,CAAC,WAAW,EACvB,mBAAmB,EACnB,MAAM,CACP;yCACF,CAAC,EAAA;;wCA1BI,KAAmC,SA0BvC,EA1BK,cAAc,QAAA,EAAE,cAAc,QAAA;wCA+BrC,IAAI,cAAc,CAAC,MAAM,KAAK,WAAW,IAAI,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;4CAC1E,mBAAmB,GAAG,cAAc,CAAC,KAAK,CAAC;wCAC7C,CAAC;6CAAM,IAAI,cAAc,CAAC,MAAM,KAAK,WAAW,IAAI,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;4CACjF,OAAO,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;4CACzE,mBAAmB,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;wCAC3E,CAAC;6CAAM,CAAC;4CAEA,YAAY,GAAG,cAAc,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;4CACtG,IAAI,YAAY,EAAE,CAAC;gDACjB,sBAAO;wDACL,SAAS,EAAE,EAAE;wDACb,YAAY,EAAE,YAAY;wDAC1B,eAAe,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE;wDACnG,cAAc,EAAE,SAAS;wDACzB,mBAAmB,EAAE,IAAI;wDACzB,gBAAgB,EAAE,IAAI;qDACvB,EAAC;4CACJ,CAAC;4CACD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;wCACjD,CAAC;wCAGwB,qBAAM,uDAAyB,CAAC,+BAA+B,CACtF,MAAM,EACN,WAAW,EACX,mBAAmB,CAAC,IAAI,EACxB,mBAAmB,CACpB,EAAA;;wCALK,gBAAgB,GAAG,SAKxB;wCAGK,YAAY,GAAgD;4CAChE,UAAU,EAAE,gBAAgB,CAAC,EAAE;4CAC/B,SAAS,EAAE,mBAAmB,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE;4CACnD,YAAY,EAAE,mBAAmB,CAAC,IAAI,CAAC,YAAY,IAAI;gDACrD,mBAAmB,EAAE,CAAC;gDACtB,UAAU,EAAE,EAAE;gDACd,oBAAoB,EAAE,EAAE;6CACzB;4CACD,eAAe,EAAE,mBAAmB,CAAC,IAAI,CAAC,eAAe,IAAI;gDAC3D,YAAY,EAAE,CAAC;gDACf,WAAW,EAAE,GAAG;gDAChB,oBAAoB,EAAE,GAAG;gDACzB,YAAY,EAAE,EAAE;6CACjB;4CACD,cAAc,EAAE,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS;yCACpG,CAAC;wCAEF,sBAAO,YAAY,EAAC;;;6BACrB,EACD,MAAM,CACP,EAAA;;oBArFK,YAAY,GAAG,SAqFpB;oBAED,kDAAkD;oBAClD,qBAAM,8CAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAA;;oBADtF,kDAAkD;oBAClD,SAAsF,CAAC,CAAC,aAAa;oBAErG,wBAAwB;oBACxB,OAAO,CAAC,GAAG,CAAC,sEAA+D,MAAM,uBAAa,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAE,CAAC,CAAC;oBAE7I,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,YAAY;4BAClB,MAAM,EAAE,KAAK;4BACb,oBAAoB,EAAE,IAAI;4BAC1B,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACtC,CAAC,EAAC;;;;CACJ;AAED,2CAA2C;AAC9B,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;QAChF,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,0CAA0C;wBACxF,cAAM,OAAA,iCAAiC,CAAC,OAAO,CAAC,EAA1C,CAA0C,CACjD,EAAC;;iBACH,CAAoF,EAAC;;KACvF,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/ai/skills-analysis/comprehensive/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { geminiService } from '@/lib/services/geminiService';\nimport { consolidatedCache } from '@/lib/services/consolidated-cache-service';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { prisma } from '@/lib/prisma';\nimport { z } from 'zod';\nimport { skillGapPerformanceMonitor } from '@/lib/performance/skill-gap-performance';\nimport { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';\nimport { requestBatchingService } from '@/lib/services/request-batching-service';\n\nimport { concurrentDatabaseService } from '@/lib/services/concurrent-database-service';\n\n// Enhanced validation schema for comprehensive analysis\nconst comprehensiveSkillsAnalysisSchema = z.object({\n  currentSkills: z.array(z.object({\n    skillId: z.string().optional(),\n    skillName: z.string().min(1, 'Skill name is required'),\n    selfRating: z.number().min(1).max(10),\n    confidenceLevel: z.number().min(1).max(10),\n    lastUsed: z.string().optional(),\n    yearsOfExperience: z.number().min(0).max(50).optional(),\n  })).min(0, 'Skills array cannot be negative').max(50, 'Too many skills'), // Allow empty array\n  \n  targetCareerPath: z.object({\n    careerPathId: z.string().optional(),\n    careerPathName: z.string().min(2, 'Career path name is required'),\n    targetLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),\n  }),\n  \n  preferences: z.object({\n    timeframe: z.enum(['THREE_MONTHS', 'SIX_MONTHS', 'ONE_YEAR', 'TWO_YEARS', 'CUSTOM']),\n    hoursPerWeek: z.number().min(1).max(80),\n    learningStyle: z.array(z.string()).optional().default([]),\n    budget: z.enum(['FREE', 'FREEMIUM', 'PAID', 'ANY']).default('ANY'),\n    focusAreas: z.array(z.string()).optional().default([]),\n  }),\n  \n  includeMarketData: z.boolean().default(true),\n  includePersonalizedPaths: z.boolean().default(true),\n});\n\ntype ComprehensiveSkillsAnalysisRequest = z.infer<typeof comprehensiveSkillsAnalysisSchema>;\n\ninterface ComprehensiveSkillsAnalysisResponse {\n  success: boolean;\n  data: {\n    analysisId: string;\n    skillGaps: Array<{\n      skillId: string;\n      skillName: string;\n      currentLevel: number;\n      targetLevel: number;\n      gapSeverity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';\n      priority: number;\n      estimatedLearningTime: number;\n      marketDemand?: string;\n      salaryImpact?: number;\n    }>;\n    learningPlan: {\n      totalEstimatedHours: number;\n      milestones: Array<{\n        month: number;\n        skills: string[];\n        estimatedHours: number;\n        learningPaths: string[];\n      }>;\n      recommendedResources: Array<{\n        resourceId: string;\n        resourceType: string;\n        priority: string;\n        skillsAddressed: string[];\n        estimatedHours: number;\n      }>;\n    };\n    careerReadiness: {\n      currentScore: number;\n      targetScore: number;\n      improvementPotential: number;\n      timeToTarget: number;\n    };\n    marketInsights?: {\n      industryTrends: Array<{\n        skill: string;\n        trend: string;\n        demandLevel: string;\n      }>;\n      salaryProjections: {\n        currentEstimate: number;\n        targetEstimate: number;\n        improvementPotential: number;\n      };\n    };\n  };\n  cached: boolean;\n  generatedAt: string;\n}\n\nasync function getUserSkillAssessments(userId: string) {\n  try {\n    const assessments = await prisma.skillAssessment.findMany({\n      where: {\n        userId,\n        isActive: true,\n      },\n      include: {\n        skill: true,\n      },\n      orderBy: {\n        assessmentDate: 'desc',\n      },\n    });\n\n    return assessments.map(assessment => ({\n      skillId: assessment.skillId,\n      skillName: assessment.skill.name,\n      selfRating: assessment.selfRating,\n      confidenceLevel: assessment.confidenceLevel,\n      lastAssessed: assessment.assessmentDate,\n      assessmentType: assessment.assessmentType,\n    }));\n  } catch (error) {\n    console.error('Error fetching user skill assessments:', error);\n    return [];\n  }\n}\n\nasync function getSkillsFromCareerAssessment(userId: string) {\n  try {\n    const assessment = await prisma.assessment.findFirst({\n      where: {\n        userId,\n        status: 'COMPLETED',\n      },\n      include: {\n        responses: true,\n      },\n      orderBy: {\n        completedAt: 'desc',\n      },\n    });\n\n    if (!assessment) {\n      return [];\n    }\n\n    const skillsFromAssessment: Array<{\n      skillName: string;\n      selfRating: number;\n      confidenceLevel: number;\n      lastUsed: string;\n    }> = [];\n\n    // Extract skills from assessment responses\n    assessment.responses.forEach(response => {\n      try {\n        const value = typeof response.answerValue === 'string'\n          ? JSON.parse(response.answerValue)\n          : response.answerValue;\n\n        // Look for skills-related questions\n        if (response.questionKey.toLowerCase().includes('skill') && Array.isArray(value)) {\n          value.forEach((skill: string) => {\n            if (typeof skill === 'string' && skill.trim()) {\n              skillsFromAssessment.push({\n                skillName: skill.trim(),\n                selfRating: 6, // Default moderate rating\n                confidenceLevel: 6, // Default moderate confidence\n                lastUsed: assessment.completedAt?.toISOString() || new Date().toISOString(),\n              });\n            }\n          });\n        }\n\n        // Look for experience level indicators\n        if (response.questionKey.toLowerCase().includes('experience') && typeof value === 'string') {\n          const experienceLevel = value.toLowerCase();\n          let defaultRating = 5;\n          if (experienceLevel.includes('senior') || experienceLevel.includes('expert')) {\n            defaultRating = 8;\n          } else if (experienceLevel.includes('mid') || experienceLevel.includes('intermediate')) {\n            defaultRating = 6;\n          } else if (experienceLevel.includes('junior') || experienceLevel.includes('beginner')) {\n            defaultRating = 4;\n          }\n\n          // Update ratings for existing skills\n          skillsFromAssessment.forEach(skill => {\n            skill.selfRating = defaultRating;\n            skill.confidenceLevel = defaultRating;\n          });\n        }\n      } catch (error) {\n        console.error('Error parsing assessment response:', error);\n      }\n    });\n\n    return skillsFromAssessment;\n  } catch (error) {\n    console.error('Error fetching skills from career assessment:', error);\n    return [];\n  }\n}\n\nasync function getEnhancedCareerPathData(careerPathId?: string, careerPathName?: string) {\n  try {\n    const startTime = Date.now();\n\n    const whereClause = careerPathId\n      ? { id: careerPathId }\n      : {\n          OR: [\n            { name: { contains: careerPathName, mode: 'insensitive' as const } },\n            { slug: careerPathName?.toLowerCase().replace(/\\s+/g, '-') }\n          ]\n        };\n\n    // Use optimized select with selective loading for better performance\n    const careerPath = await prisma.careerPath.findFirst({\n      where: whereClause,\n      select: {\n        id: true,\n        name: true,\n        slug: true,\n        overview: true,\n        // Optimized related skills with minimal market data\n        relatedSkills: {\n          select: {\n            id: true,\n            name: true,\n            category: true,\n            description: true,\n            marketData: {\n              where: { isActive: true },\n              orderBy: { dataDate: 'desc' },\n              take: 1,\n              select: {\n                id: true,\n                averageSalaryImpact: true,\n                demandLevel: true,\n                growthTrend: true,\n                dataDate: true\n              }\n            }\n          },\n          take: 20 // Limit to prevent excessive data loading\n        },\n        // Optimized learning resources with selective fields\n        learningResources: {\n          where: { isActive: true },\n          select: {\n            id: true,\n            title: true,\n            type: true,\n            skillLevel: true,\n            cost: true,\n            duration: true,\n            url: true,\n            skills: {\n              select: {\n                id: true,\n                name: true\n              },\n              take: 5 // Limit skills per resource\n            },\n            ratings: {\n              select: {\n                rating: true\n              },\n              take: 100 // Limit ratings for average calculation\n            }\n          },\n          take: 15, // Limit learning resources\n          orderBy: [\n            { skillLevel: 'asc' },\n            { cost: 'asc' }\n          ]\n        },\n        // Optimized learning paths with essential data only\n        learningPaths: {\n          where: { isActive: true },\n          select: {\n            id: true,\n            title: true,\n            difficulty: true,\n            estimatedHours: true,\n            skills: {\n              select: {\n                id: true,\n                name: true\n              },\n              take: 10 // Limit skills per path\n            },\n            _count: {\n              select: {\n                steps: true\n              }\n            }\n          },\n          take: 10, // Limit learning paths\n          orderBy: { difficulty: 'asc' }\n        }\n      }\n    });\n\n    const queryTime = Date.now() - startTime;\n\n    // Log performance metrics for monitoring\n    if (queryTime > 1000) {\n      console.warn(`Slow getEnhancedCareerPathData query: ${queryTime}ms for ${careerPathId || careerPathName}`);\n    }\n\n    if (!careerPath) return null;\n\n    // Optimized data transformation with performance monitoring\n    const transformStartTime = Date.now();\n\n    const result = {\n      id: careerPath.id,\n      name: careerPath.name,\n      slug: careerPath.slug,\n      overview: careerPath.overview,\n      requiredSkills: careerPath.relatedSkills.map(skill => ({\n        id: skill.id,\n        name: skill.name,\n        category: skill.category,\n        description: skill.description,\n        marketData: skill.marketData[0] || null,\n      })),\n      learningResources: careerPath.learningResources.map(resource => {\n        // Optimized rating calculation\n        const averageRating = resource.ratings.length > 0\n          ? Math.round((resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length) * 10) / 10\n          : 0;\n\n        return {\n          id: resource.id,\n          title: resource.title,\n          type: resource.type,\n          skillLevel: resource.skillLevel,\n          cost: resource.cost,\n          duration: resource.duration,\n          url: resource.url,\n          averageRating,\n          ratingCount: resource.ratings.length,\n          skills: resource.skills?.map(skill => skill.name) || [],\n        };\n      }),\n      learningPaths: careerPath.learningPaths.map(path => ({\n        id: path.id,\n        title: path.title,\n        difficulty: path.difficulty,\n        estimatedHours: path.estimatedHours,\n        stepCount: path._count.steps,\n        skills: path.skills?.map(skill => skill.name) || [],\n      })),\n      performance: {\n        queryTime,\n        transformTime: Date.now() - transformStartTime,\n        totalSkills: careerPath.relatedSkills.length,\n        totalResources: careerPath.learningResources.length,\n        totalPaths: careerPath.learningPaths.length\n      }\n    };\n\n    return result;\n  } catch (error) {\n    console.error('Error fetching enhanced career path data:', error);\n    return null;\n  }\n}\n\nasync function createSkillGapAnalysis(\n  userId: string,\n  request: ComprehensiveSkillsAnalysisRequest,\n  analysisData: any,\n  careerPathData: any\n) {\n  try {\n    const expiresAt = new Date();\n    expiresAt.setMonth(expiresAt.getMonth() + 3); // Analysis valid for 3 months\n\n    const skillGapAnalysis = await prisma.skillGapAnalysis.create({\n      data: {\n        userId,\n        targetCareerPathId: request.targetCareerPath.careerPathId || null,\n        targetCareerPathName: request.targetCareerPath.careerPathName,\n        experienceLevel: request.targetCareerPath.targetLevel,\n        timeframe: request.preferences.timeframe,\n        analysisData: analysisData,\n        skillGaps: analysisData.skillGaps || [],\n        learningPlan: analysisData.learningPlan || {},\n        marketData: analysisData.marketInsights || null,\n        progressTracking: {\n          milestones: analysisData.learningPlan?.milestones || [],\n          completedMilestones: [],\n          currentPhase: 'planning',\n        },\n        status: 'ACTIVE',\n        completionPercentage: 0,\n        expiresAt,\n      },\n    });\n\n    return skillGapAnalysis;\n  } catch (error) {\n    console.error('Error creating skill gap analysis:', error);\n    throw error;\n  }\n}\n\nasync function handleComprehensiveSkillsAnalysis(request: NextRequest): Promise<NextResponse<ApiResponse<ComprehensiveSkillsAnalysisResponse['data']>>> {\n  const session = await getServerSession(authOptions);\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const userId = session.user.id;\n\n  const body = await request.json();\n  const validation = comprehensiveSkillsAnalysisSchema.safeParse(body);\n\n  if (!validation.success) {\n    const error = new Error('Invalid request data') as any;\n    error.statusCode = 400;\n    error.details = validation.error.errors;\n    throw error;\n  }\n\n  const requestData = validation.data;\n\n  // Use enhanced caching with multi-level cache and compression\n  const cacheKey = `ai:skills_analysis:${userId}:comprehensive_${requestData.targetCareerPath.careerPathName}_${requestData.targetCareerPath.targetLevel}_${requestData.preferences.timeframe}`;\n\n  const cacheTags = [\n    'skill_analysis',\n    'career_path',\n    requestData.includeMarketData ? 'market_data' : '',\n    userId\n  ].filter(Boolean);\n\n  // Check enhanced cache first (L1 + L2 + shared cache)\n  const cached = await consolidatedCache.get<any>(cacheKey);\n  if (cached) {\n    return NextResponse.json({\n      success: true,\n      data: cached as ComprehensiveSkillsAnalysisResponse['data'],\n      cached: true,\n      generatedAt: (cached as any)?.generatedAt || new Date().toISOString(),\n    });\n  }\n\n  // Use request batching service for optimized processing\n  try {\n    const batchResult = await requestBatchingService.batchComprehensiveAnalysis(\n      userId,\n      requestData,\n      'medium' // priority\n    );\n\n    if (batchResult.success && batchResult.data) {\n      // Cache the result with enhanced caching\n      await consolidatedCache.set(cacheKey, batchResult.data, { ttl: 1800000, tags: cacheTags }); // 30 minutes\n\n      return NextResponse.json({\n        success: true,\n        data: batchResult.data,\n        cached: batchResult.cached || false,\n        batchProcessed: true,\n        processingTime: batchResult.processingTime,\n        generatedAt: new Date().toISOString(),\n      });\n    } else {\n      throw new Error(batchResult.error || 'Batch processing failed');\n    }\n  } catch (batchError) {\n    console.warn('Batch processing failed, falling back to individual processing:', batchError);\n\n    // Fallback to individual processing if batch fails\n    return await handleIndividualAnalysis(userId, requestData, cacheKey, cacheTags);\n  }\n}\n\nasync function handleIndividualAnalysis(\n  userId: string,\n  requestData: ComprehensiveSkillsAnalysisRequest,\n  cacheKey: string,\n  cacheTags: string[]\n): Promise<NextResponse<ApiResponse<ComprehensiveSkillsAnalysisResponse['data']>>> {\n\n  // Use concurrent database operations for optimized data fetching\n  const [userAssessments, careerPathData] = await Promise.all([\n    concurrentDatabaseService.fetchUserAssessmentsOptimized(userId),\n    requestData.targetCareerPath.careerPathId\n      ? concurrentDatabaseService.fetchCareerPathDataOptimized(requestData.targetCareerPath.careerPathId)\n      : getEnhancedCareerPathData(undefined, requestData.targetCareerPath.careerPathName)\n  ]);\n\n  // Get skills from career assessment if no skill assessments exist\n  const careerAssessmentSkills = userAssessments.length === 0\n    ? await getSkillsFromCareerAssessment(userId)\n    : [];\n\n  // Fallback career path data if not found\n  const finalCareerPathData = careerPathData || {\n    id: 'fallback-career-path-id',\n    name: requestData.targetCareerPath.careerPathName,\n    requiredSkills: [],\n    learningResources: [],\n    learningPaths: [],\n  };\n\n  // Prepare data for AI analysis with optimized skill deduplication\n  const allCurrentSkills = [\n    ...requestData.currentSkills,\n    ...userAssessments\n      .filter(assessment => assessment.skill) // Only include assessments with skill data\n      .map(assessment => ({\n        skillName: assessment.skill?.name || assessment.skillName,\n        selfRating: assessment.selfRating,\n        confidenceLevel: assessment.confidenceLevel,\n        lastUsed: assessment.createdAt?.toISOString() || assessment.lastAssessed?.toISOString(),\n      })),\n    ...careerAssessmentSkills\n  ];\n\n  // Optimized skill deduplication with performance tracking\n  const uniqueSkills = allCurrentSkills.reduce((acc, skill) => {\n    const existing = acc.find(s => s.skillName.toLowerCase() === skill.skillName.toLowerCase());\n    if (!existing) {\n      acc.push(skill);\n    } else if (skill.selfRating && skill.selfRating > (existing.selfRating || 0)) {\n      // Keep the higher rating if duplicate\n      Object.assign(existing, skill);\n    }\n    return acc;\n  }, [] as typeof allCurrentSkills);\n\n  // Perform comprehensive AI analysis with optimized concurrent processing\n  const responseData = await skillGapPerformanceMonitor.monitorSkillAnalysis(\n    requestData,\n    async () => {\n      // Use concurrent processing for AI analysis and database operations\n      const [edgeCaseResult, analysisResult] = await Promise.allSettled([\n        edgeCaseHandlerService.handleLearningPathGeneration({\n          userId,\n          currentSkills: uniqueSkills.map(skill => ({\n            skill: skill.skillName,\n            level: skill.selfRating,\n            confidence: skill.confidenceLevel\n          })),\n          targetRole: requestData.targetCareerPath.careerPathName,\n          timeframe: requestData.preferences.timeframe === 'THREE_MONTHS' ? 3 :\n                    requestData.preferences.timeframe === 'SIX_MONTHS' ? 6 :\n                    requestData.preferences.timeframe === 'ONE_YEAR' ? 12 :\n                    requestData.preferences.timeframe === 'TWO_YEARS' ? 24 : 12,\n          learningStyle: 'balanced',\n          availability: requestData.preferences.hoursPerWeek,\n          budget: requestData.preferences.budget === 'FREE' ? 0 :\n                 requestData.preferences.budget === 'FREEMIUM' ? 100 :\n                 requestData.preferences.budget === 'PAID' ? 1000 : 500\n        }),\n        geminiService.analyzeComprehensiveSkillGap(\n          uniqueSkills,\n          requestData.targetCareerPath,\n          requestData.preferences,\n          finalCareerPathData,\n          userId\n        )\n      ]);\n\n      // Process results with fallback handling\n      let finalAnalysisResult;\n\n      if (analysisResult.status === 'fulfilled' && analysisResult.value.success) {\n        finalAnalysisResult = analysisResult.value;\n      } else if (edgeCaseResult.status === 'fulfilled' && edgeCaseResult.value.success) {\n        console.warn('Primary AI analysis failed, using EdgeCaseHandler result');\n        finalAnalysisResult = { success: true, data: edgeCaseResult.value.data };\n      } else {\n        // Both failed, use fallback data\n        const fallbackData = edgeCaseResult.status === 'fulfilled' ? edgeCaseResult.value.fallbackData : null;\n        if (fallbackData) {\n          return {\n            skillGaps: [],\n            learningPlan: fallbackData,\n            careerReadiness: { currentScore: 0, targetScore: 100, improvementPotential: 100, timeToTarget: 12 },\n            marketInsights: undefined,\n            edgeCaseHandlerUsed: true,\n            fallbackDataUsed: true\n          };\n        }\n        throw new Error('All analysis methods failed');\n      }\n\n      // Use optimized concurrent database operations for analysis creation\n      const skillGapAnalysis = await concurrentDatabaseService.createSkillGapAnalysisOptimized(\n        userId,\n        requestData,\n        finalAnalysisResult.data,\n        finalCareerPathData\n      );\n\n      // Prepare comprehensive response\n      const responseData: ComprehensiveSkillsAnalysisResponse['data'] = {\n        analysisId: skillGapAnalysis.id,\n        skillGaps: finalAnalysisResult.data.skillGaps || [],\n        learningPlan: finalAnalysisResult.data.learningPlan || {\n          totalEstimatedHours: 0,\n          milestones: [],\n          recommendedResources: [],\n        },\n        careerReadiness: finalAnalysisResult.data.careerReadiness || {\n          currentScore: 0,\n          targetScore: 100,\n          improvementPotential: 100,\n          timeToTarget: 12,\n        },\n        marketInsights: requestData.includeMarketData ? finalAnalysisResult.data.marketInsights : undefined,\n      };\n\n      return responseData;\n    },\n    userId\n  );\n\n  // Cache the result using enhanced caching service\n  await consolidatedCache.set(cacheKey, responseData, { ttl: 1800000, tags: cacheTags }); // 30 minutes\n\n  // Track usage analytics\n  console.log(`Individual comprehensive skills analysis completed for user ${userId}, career: ${requestData.targetCareerPath.careerPathName}`);\n\n  return NextResponse.json({\n    success: true,\n    data: responseData,\n    cached: false,\n    individualProcessing: true,\n    generatedAt: new Date().toISOString(),\n  });\n}\n\n// POST endpoint for comprehensive analysis\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ComprehensiveSkillsAnalysisResponse['data']>>> => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 comprehensive analyses per 15 minutes\n      () => handleComprehensiveSkillsAnalysis(request)\n    );\n  }) as Promise<NextResponse<ApiResponse<ComprehensiveSkillsAnalysisResponse['data']>>>;\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f7ba946a66c188025b875c8abd6c9e984da788c3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_xrl7693qr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xrl7693qr();
var __awaiter =
/* istanbul ignore next */
(cov_xrl7693qr().s[0]++,
/* istanbul ignore next */
(cov_xrl7693qr().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_xrl7693qr().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_xrl7693qr().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_xrl7693qr().f[1]++;
    cov_xrl7693qr().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_xrl7693qr().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_xrl7693qr().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[2]++;
      cov_xrl7693qr().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_xrl7693qr().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_xrl7693qr().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_xrl7693qr().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_xrl7693qr().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[4]++;
      cov_xrl7693qr().s[4]++;
      try {
        /* istanbul ignore next */
        cov_xrl7693qr().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_xrl7693qr().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[5]++;
      cov_xrl7693qr().s[7]++;
      try {
        /* istanbul ignore next */
        cov_xrl7693qr().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_xrl7693qr().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[6]++;
      cov_xrl7693qr().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_xrl7693qr().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_xrl7693qr().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_xrl7693qr().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_xrl7693qr().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_xrl7693qr().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_xrl7693qr().s[12]++,
/* istanbul ignore next */
(cov_xrl7693qr().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_xrl7693qr().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_xrl7693qr().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_xrl7693qr().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_xrl7693qr().f[8]++;
        cov_xrl7693qr().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_xrl7693qr().b[6][0]++;
          cov_xrl7693qr().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_xrl7693qr().b[6][1]++;
        }
        cov_xrl7693qr().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_xrl7693qr().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_xrl7693qr().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_xrl7693qr().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_xrl7693qr().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_xrl7693qr().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_xrl7693qr().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[9]++;
    cov_xrl7693qr().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_xrl7693qr().f[10]++;
    cov_xrl7693qr().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[11]++;
      cov_xrl7693qr().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_xrl7693qr().f[12]++;
    cov_xrl7693qr().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_xrl7693qr().b[9][0]++;
      cov_xrl7693qr().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_xrl7693qr().b[9][1]++;
    }
    cov_xrl7693qr().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_xrl7693qr().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_xrl7693qr().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_xrl7693qr().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_xrl7693qr().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_xrl7693qr().s[25]++;
      try {
        /* istanbul ignore next */
        cov_xrl7693qr().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_xrl7693qr().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_xrl7693qr().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_xrl7693qr().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_xrl7693qr().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_xrl7693qr().b[15][0]++,
        /* istanbul ignore next */
        (cov_xrl7693qr().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_xrl7693qr().b[16][1]++,
        /* istanbul ignore next */
        (cov_xrl7693qr().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_xrl7693qr().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_xrl7693qr().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_xrl7693qr().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_xrl7693qr().b[12][0]++;
          cov_xrl7693qr().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_xrl7693qr().b[12][1]++;
        }
        cov_xrl7693qr().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_xrl7693qr().b[18][0]++;
          cov_xrl7693qr().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_xrl7693qr().b[18][1]++;
        }
        cov_xrl7693qr().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_xrl7693qr().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_xrl7693qr().b[19][1]++;
            cov_xrl7693qr().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_xrl7693qr().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_xrl7693qr().b[19][2]++;
            cov_xrl7693qr().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_xrl7693qr().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_xrl7693qr().b[19][3]++;
            cov_xrl7693qr().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_xrl7693qr().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_xrl7693qr().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_xrl7693qr().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_xrl7693qr().b[19][4]++;
            cov_xrl7693qr().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_xrl7693qr().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_xrl7693qr().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_xrl7693qr().b[19][5]++;
            cov_xrl7693qr().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_xrl7693qr().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_xrl7693qr().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_xrl7693qr().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_xrl7693qr().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_xrl7693qr().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_xrl7693qr().b[20][0]++;
              cov_xrl7693qr().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_xrl7693qr().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_xrl7693qr().b[20][1]++;
            }
            cov_xrl7693qr().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_xrl7693qr().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_xrl7693qr().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_xrl7693qr().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_xrl7693qr().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_xrl7693qr().b[23][0]++;
              cov_xrl7693qr().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_xrl7693qr().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xrl7693qr().b[23][1]++;
            }
            cov_xrl7693qr().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_xrl7693qr().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_xrl7693qr().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_xrl7693qr().b[25][0]++;
              cov_xrl7693qr().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_xrl7693qr().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_xrl7693qr().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xrl7693qr().b[25][1]++;
            }
            cov_xrl7693qr().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_xrl7693qr().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_xrl7693qr().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_xrl7693qr().b[27][0]++;
              cov_xrl7693qr().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_xrl7693qr().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_xrl7693qr().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xrl7693qr().b[27][1]++;
            }
            cov_xrl7693qr().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_xrl7693qr().b[29][0]++;
              cov_xrl7693qr().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_xrl7693qr().b[29][1]++;
            }
            cov_xrl7693qr().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_xrl7693qr().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_xrl7693qr().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_xrl7693qr().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_xrl7693qr().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_xrl7693qr().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_xrl7693qr().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_xrl7693qr().b[30][0]++;
      cov_xrl7693qr().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_xrl7693qr().b[30][1]++;
    }
    cov_xrl7693qr().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_xrl7693qr().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_xrl7693qr().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_xrl7693qr().s[67]++,
/* istanbul ignore next */
(cov_xrl7693qr().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_xrl7693qr().b[32][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_xrl7693qr().b[32][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[13]++;
  cov_xrl7693qr().s[68]++;
  if (
  /* istanbul ignore next */
  (cov_xrl7693qr().b[34][0]++, pack) ||
  /* istanbul ignore next */
  (cov_xrl7693qr().b[34][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_xrl7693qr().b[33][0]++;
    cov_xrl7693qr().s[69]++;
    for (var i =
      /* istanbul ignore next */
      (cov_xrl7693qr().s[70]++, 0), l =
      /* istanbul ignore next */
      (cov_xrl7693qr().s[71]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_xrl7693qr().s[72]++;
      if (
      /* istanbul ignore next */
      (cov_xrl7693qr().b[36][0]++, ar) ||
      /* istanbul ignore next */
      (cov_xrl7693qr().b[36][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_xrl7693qr().b[35][0]++;
        cov_xrl7693qr().s[73]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_xrl7693qr().b[37][0]++;
          cov_xrl7693qr().s[74]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_xrl7693qr().b[37][1]++;
        }
        cov_xrl7693qr().s[75]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_xrl7693qr().b[35][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_xrl7693qr().b[33][1]++;
  }
  cov_xrl7693qr().s[76]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_xrl7693qr().b[38][0]++, ar) ||
  /* istanbul ignore next */
  (cov_xrl7693qr().b[38][1]++, Array.prototype.slice.call(from)));
}));
/* istanbul ignore next */
cov_xrl7693qr().s[77]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_xrl7693qr().s[78]++;
exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[79]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[80]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[81]++, require("@/lib/auth"));
var geminiService_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[82]++, require("@/lib/services/geminiService"));
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[83]++, require("@/lib/services/consolidated-cache-service"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[84]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[85]++, require("@/lib/rateLimit"));
var csrf_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[86]++, require("@/lib/csrf"));
var prisma_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[87]++, require("@/lib/prisma"));
var zod_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[88]++, require("zod"));
var skill_gap_performance_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[89]++, require("@/lib/performance/skill-gap-performance"));
var EdgeCaseHandlerService_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[90]++, require("@/lib/skills/EdgeCaseHandlerService"));
var request_batching_service_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[91]++, require("@/lib/services/request-batching-service"));
var concurrent_database_service_1 =
/* istanbul ignore next */
(cov_xrl7693qr().s[92]++, require("@/lib/services/concurrent-database-service"));
// Enhanced validation schema for comprehensive analysis
var comprehensiveSkillsAnalysisSchema =
/* istanbul ignore next */
(cov_xrl7693qr().s[93]++, zod_1.z.object({
  currentSkills: zod_1.z.array(zod_1.z.object({
    skillId: zod_1.z.string().optional(),
    skillName: zod_1.z.string().min(1, 'Skill name is required'),
    selfRating: zod_1.z.number().min(1).max(10),
    confidenceLevel: zod_1.z.number().min(1).max(10),
    lastUsed: zod_1.z.string().optional(),
    yearsOfExperience: zod_1.z.number().min(0).max(50).optional()
  })).min(0, 'Skills array cannot be negative').max(50, 'Too many skills'),
  // Allow empty array
  targetCareerPath: zod_1.z.object({
    careerPathId: zod_1.z.string().optional(),
    careerPathName: zod_1.z.string().min(2, 'Career path name is required'),
    targetLevel: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'])
  }),
  preferences: zod_1.z.object({
    timeframe: zod_1.z.enum(['THREE_MONTHS', 'SIX_MONTHS', 'ONE_YEAR', 'TWO_YEARS', 'CUSTOM']),
    hoursPerWeek: zod_1.z.number().min(1).max(80),
    learningStyle: zod_1.z.array(zod_1.z.string()).optional().default([]),
    budget: zod_1.z.enum(['FREE', 'FREEMIUM', 'PAID', 'ANY']).default('ANY'),
    focusAreas: zod_1.z.array(zod_1.z.string()).optional().default([])
  }),
  includeMarketData: zod_1.z.boolean().default(true),
  includePersonalizedPaths: zod_1.z.boolean().default(true)
}));
function getUserSkillAssessments(userId) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[14]++;
  cov_xrl7693qr().s[94]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[15]++;
    var assessments, error_1;
    /* istanbul ignore next */
    cov_xrl7693qr().s[95]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[16]++;
      cov_xrl7693qr().s[96]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_xrl7693qr().b[39][0]++;
          cov_xrl7693qr().s[97]++;
          _a.trys.push([0, 2,, 3]);
          /* istanbul ignore next */
          cov_xrl7693qr().s[98]++;
          return [4 /*yield*/, prisma_1.prisma.skillAssessment.findMany({
            where: {
              userId: userId,
              isActive: true
            },
            include: {
              skill: true
            },
            orderBy: {
              assessmentDate: 'desc'
            }
          })];
        case 1:
          /* istanbul ignore next */
          cov_xrl7693qr().b[39][1]++;
          cov_xrl7693qr().s[99]++;
          assessments = _a.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[100]++;
          return [2 /*return*/, assessments.map(function (assessment) {
            /* istanbul ignore next */
            cov_xrl7693qr().f[17]++;
            cov_xrl7693qr().s[101]++;
            return {
              skillId: assessment.skillId,
              skillName: assessment.skill.name,
              selfRating: assessment.selfRating,
              confidenceLevel: assessment.confidenceLevel,
              lastAssessed: assessment.assessmentDate,
              assessmentType: assessment.assessmentType
            };
          })];
        case 2:
          /* istanbul ignore next */
          cov_xrl7693qr().b[39][2]++;
          cov_xrl7693qr().s[102]++;
          error_1 = _a.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[103]++;
          console.error('Error fetching user skill assessments:', error_1);
          /* istanbul ignore next */
          cov_xrl7693qr().s[104]++;
          return [2 /*return*/, []];
        case 3:
          /* istanbul ignore next */
          cov_xrl7693qr().b[39][3]++;
          cov_xrl7693qr().s[105]++;
          return [2 /*return*/];
      }
    });
  });
}
function getSkillsFromCareerAssessment(userId) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[18]++;
  cov_xrl7693qr().s[106]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[19]++;
    var assessment_1, skillsFromAssessment_1, error_2;
    /* istanbul ignore next */
    cov_xrl7693qr().s[107]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[20]++;
      cov_xrl7693qr().s[108]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_xrl7693qr().b[40][0]++;
          cov_xrl7693qr().s[109]++;
          _a.trys.push([0, 2,, 3]);
          /* istanbul ignore next */
          cov_xrl7693qr().s[110]++;
          return [4 /*yield*/, prisma_1.prisma.assessment.findFirst({
            where: {
              userId: userId,
              status: 'COMPLETED'
            },
            include: {
              responses: true
            },
            orderBy: {
              completedAt: 'desc'
            }
          })];
        case 1:
          /* istanbul ignore next */
          cov_xrl7693qr().b[40][1]++;
          cov_xrl7693qr().s[111]++;
          assessment_1 = _a.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[112]++;
          if (!assessment_1) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[41][0]++;
            cov_xrl7693qr().s[113]++;
            return [2 /*return*/, []];
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[41][1]++;
          }
          cov_xrl7693qr().s[114]++;
          skillsFromAssessment_1 = [];
          // Extract skills from assessment responses
          /* istanbul ignore next */
          cov_xrl7693qr().s[115]++;
          assessment_1.responses.forEach(function (response) {
            /* istanbul ignore next */
            cov_xrl7693qr().f[21]++;
            cov_xrl7693qr().s[116]++;
            try {
              var value =
              /* istanbul ignore next */
              (cov_xrl7693qr().s[117]++, typeof response.answerValue === 'string' ?
              /* istanbul ignore next */
              (cov_xrl7693qr().b[42][0]++, JSON.parse(response.answerValue)) :
              /* istanbul ignore next */
              (cov_xrl7693qr().b[42][1]++, response.answerValue));
              // Look for skills-related questions
              /* istanbul ignore next */
              cov_xrl7693qr().s[118]++;
              if (
              /* istanbul ignore next */
              (cov_xrl7693qr().b[44][0]++, response.questionKey.toLowerCase().includes('skill')) &&
              /* istanbul ignore next */
              (cov_xrl7693qr().b[44][1]++, Array.isArray(value))) {
                /* istanbul ignore next */
                cov_xrl7693qr().b[43][0]++;
                cov_xrl7693qr().s[119]++;
                value.forEach(function (skill) {
                  /* istanbul ignore next */
                  cov_xrl7693qr().f[22]++;
                  var _a;
                  /* istanbul ignore next */
                  cov_xrl7693qr().s[120]++;
                  if (
                  /* istanbul ignore next */
                  (cov_xrl7693qr().b[46][0]++, typeof skill === 'string') &&
                  /* istanbul ignore next */
                  (cov_xrl7693qr().b[46][1]++, skill.trim())) {
                    /* istanbul ignore next */
                    cov_xrl7693qr().b[45][0]++;
                    cov_xrl7693qr().s[121]++;
                    skillsFromAssessment_1.push({
                      skillName: skill.trim(),
                      selfRating: 6,
                      // Default moderate rating
                      confidenceLevel: 6,
                      // Default moderate confidence
                      lastUsed:
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[47][0]++,
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[49][0]++, (_a = assessment_1.completedAt) === null) ||
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[49][1]++, _a === void 0) ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[48][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[48][1]++, _a.toISOString())) ||
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[47][1]++, new Date().toISOString())
                    });
                  } else
                  /* istanbul ignore next */
                  {
                    cov_xrl7693qr().b[45][1]++;
                  }
                });
              } else
              /* istanbul ignore next */
              {
                cov_xrl7693qr().b[43][1]++;
              }
              // Look for experience level indicators
              cov_xrl7693qr().s[122]++;
              if (
              /* istanbul ignore next */
              (cov_xrl7693qr().b[51][0]++, response.questionKey.toLowerCase().includes('experience')) &&
              /* istanbul ignore next */
              (cov_xrl7693qr().b[51][1]++, typeof value === 'string')) {
                /* istanbul ignore next */
                cov_xrl7693qr().b[50][0]++;
                var experienceLevel =
                /* istanbul ignore next */
                (cov_xrl7693qr().s[123]++, value.toLowerCase());
                var defaultRating_1 =
                /* istanbul ignore next */
                (cov_xrl7693qr().s[124]++, 5);
                /* istanbul ignore next */
                cov_xrl7693qr().s[125]++;
                if (
                /* istanbul ignore next */
                (cov_xrl7693qr().b[53][0]++, experienceLevel.includes('senior')) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[53][1]++, experienceLevel.includes('expert'))) {
                  /* istanbul ignore next */
                  cov_xrl7693qr().b[52][0]++;
                  cov_xrl7693qr().s[126]++;
                  defaultRating_1 = 8;
                } else {
                  /* istanbul ignore next */
                  cov_xrl7693qr().b[52][1]++;
                  cov_xrl7693qr().s[127]++;
                  if (
                  /* istanbul ignore next */
                  (cov_xrl7693qr().b[55][0]++, experienceLevel.includes('mid')) ||
                  /* istanbul ignore next */
                  (cov_xrl7693qr().b[55][1]++, experienceLevel.includes('intermediate'))) {
                    /* istanbul ignore next */
                    cov_xrl7693qr().b[54][0]++;
                    cov_xrl7693qr().s[128]++;
                    defaultRating_1 = 6;
                  } else {
                    /* istanbul ignore next */
                    cov_xrl7693qr().b[54][1]++;
                    cov_xrl7693qr().s[129]++;
                    if (
                    /* istanbul ignore next */
                    (cov_xrl7693qr().b[57][0]++, experienceLevel.includes('junior')) ||
                    /* istanbul ignore next */
                    (cov_xrl7693qr().b[57][1]++, experienceLevel.includes('beginner'))) {
                      /* istanbul ignore next */
                      cov_xrl7693qr().b[56][0]++;
                      cov_xrl7693qr().s[130]++;
                      defaultRating_1 = 4;
                    } else
                    /* istanbul ignore next */
                    {
                      cov_xrl7693qr().b[56][1]++;
                    }
                  }
                }
                // Update ratings for existing skills
                /* istanbul ignore next */
                cov_xrl7693qr().s[131]++;
                skillsFromAssessment_1.forEach(function (skill) {
                  /* istanbul ignore next */
                  cov_xrl7693qr().f[23]++;
                  cov_xrl7693qr().s[132]++;
                  skill.selfRating = defaultRating_1;
                  /* istanbul ignore next */
                  cov_xrl7693qr().s[133]++;
                  skill.confidenceLevel = defaultRating_1;
                });
              } else
              /* istanbul ignore next */
              {
                cov_xrl7693qr().b[50][1]++;
              }
            } catch (error) {
              /* istanbul ignore next */
              cov_xrl7693qr().s[134]++;
              console.error('Error parsing assessment response:', error);
            }
          });
          /* istanbul ignore next */
          cov_xrl7693qr().s[135]++;
          return [2 /*return*/, skillsFromAssessment_1];
        case 2:
          /* istanbul ignore next */
          cov_xrl7693qr().b[40][2]++;
          cov_xrl7693qr().s[136]++;
          error_2 = _a.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[137]++;
          console.error('Error fetching skills from career assessment:', error_2);
          /* istanbul ignore next */
          cov_xrl7693qr().s[138]++;
          return [2 /*return*/, []];
        case 3:
          /* istanbul ignore next */
          cov_xrl7693qr().b[40][3]++;
          cov_xrl7693qr().s[139]++;
          return [2 /*return*/];
      }
    });
  });
}
function getEnhancedCareerPathData(careerPathId, careerPathName) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[24]++;
  cov_xrl7693qr().s[140]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[25]++;
    var startTime, whereClause, careerPath, queryTime, transformStartTime, result, error_3;
    /* istanbul ignore next */
    cov_xrl7693qr().s[141]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[26]++;
      cov_xrl7693qr().s[142]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_xrl7693qr().b[58][0]++;
          cov_xrl7693qr().s[143]++;
          _a.trys.push([0, 2,, 3]);
          /* istanbul ignore next */
          cov_xrl7693qr().s[144]++;
          startTime = Date.now();
          /* istanbul ignore next */
          cov_xrl7693qr().s[145]++;
          whereClause = careerPathId ?
          /* istanbul ignore next */
          (cov_xrl7693qr().b[59][0]++, {
            id: careerPathId
          }) :
          /* istanbul ignore next */
          (cov_xrl7693qr().b[59][1]++, {
            OR: [{
              name: {
                contains: careerPathName,
                mode: 'insensitive'
              }
            }, {
              slug:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[61][0]++, careerPathName === null) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[61][1]++, careerPathName === void 0) ?
              /* istanbul ignore next */
              (cov_xrl7693qr().b[60][0]++, void 0) :
              /* istanbul ignore next */
              (cov_xrl7693qr().b[60][1]++, careerPathName.toLowerCase().replace(/\s+/g, '-'))
            }]
          });
          /* istanbul ignore next */
          cov_xrl7693qr().s[146]++;
          return [4 /*yield*/, prisma_1.prisma.careerPath.findFirst({
            where: whereClause,
            select: {
              id: true,
              name: true,
              slug: true,
              overview: true,
              // Optimized related skills with minimal market data
              relatedSkills: {
                select: {
                  id: true,
                  name: true,
                  category: true,
                  description: true,
                  marketData: {
                    where: {
                      isActive: true
                    },
                    orderBy: {
                      dataDate: 'desc'
                    },
                    take: 1,
                    select: {
                      id: true,
                      averageSalaryImpact: true,
                      demandLevel: true,
                      growthTrend: true,
                      dataDate: true
                    }
                  }
                },
                take: 20 // Limit to prevent excessive data loading
              },
              // Optimized learning resources with selective fields
              learningResources: {
                where: {
                  isActive: true
                },
                select: {
                  id: true,
                  title: true,
                  type: true,
                  skillLevel: true,
                  cost: true,
                  duration: true,
                  url: true,
                  skills: {
                    select: {
                      id: true,
                      name: true
                    },
                    take: 5 // Limit skills per resource
                  },
                  ratings: {
                    select: {
                      rating: true
                    },
                    take: 100 // Limit ratings for average calculation
                  }
                },
                take: 15,
                // Limit learning resources
                orderBy: [{
                  skillLevel: 'asc'
                }, {
                  cost: 'asc'
                }]
              },
              // Optimized learning paths with essential data only
              learningPaths: {
                where: {
                  isActive: true
                },
                select: {
                  id: true,
                  title: true,
                  difficulty: true,
                  estimatedHours: true,
                  skills: {
                    select: {
                      id: true,
                      name: true
                    },
                    take: 10 // Limit skills per path
                  },
                  _count: {
                    select: {
                      steps: true
                    }
                  }
                },
                take: 10,
                // Limit learning paths
                orderBy: {
                  difficulty: 'asc'
                }
              }
            }
          })];
        case 1:
          /* istanbul ignore next */
          cov_xrl7693qr().b[58][1]++;
          cov_xrl7693qr().s[147]++;
          careerPath = _a.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[148]++;
          queryTime = Date.now() - startTime;
          // Log performance metrics for monitoring
          /* istanbul ignore next */
          cov_xrl7693qr().s[149]++;
          if (queryTime > 1000) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[62][0]++;
            cov_xrl7693qr().s[150]++;
            console.warn("Slow getEnhancedCareerPathData query: ".concat(queryTime, "ms for ").concat(
            /* istanbul ignore next */
            (cov_xrl7693qr().b[63][0]++, careerPathId) ||
            /* istanbul ignore next */
            (cov_xrl7693qr().b[63][1]++, careerPathName)));
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[62][1]++;
          }
          cov_xrl7693qr().s[151]++;
          if (!careerPath) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[64][0]++;
            cov_xrl7693qr().s[152]++;
            return [2 /*return*/, null];
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[64][1]++;
          }
          cov_xrl7693qr().s[153]++;
          transformStartTime = Date.now();
          /* istanbul ignore next */
          cov_xrl7693qr().s[154]++;
          result = {
            id: careerPath.id,
            name: careerPath.name,
            slug: careerPath.slug,
            overview: careerPath.overview,
            requiredSkills: careerPath.relatedSkills.map(function (skill) {
              /* istanbul ignore next */
              cov_xrl7693qr().f[27]++;
              cov_xrl7693qr().s[155]++;
              return {
                id: skill.id,
                name: skill.name,
                category: skill.category,
                description: skill.description,
                marketData:
                /* istanbul ignore next */
                (cov_xrl7693qr().b[65][0]++, skill.marketData[0]) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[65][1]++, null)
              };
            }),
            learningResources: careerPath.learningResources.map(function (resource) {
              /* istanbul ignore next */
              cov_xrl7693qr().f[28]++;
              var _a;
              // Optimized rating calculation
              var averageRating =
              /* istanbul ignore next */
              (cov_xrl7693qr().s[156]++, resource.ratings.length > 0 ?
              /* istanbul ignore next */
              (cov_xrl7693qr().b[66][0]++, Math.round(resource.ratings.reduce(function (sum, r) {
                /* istanbul ignore next */
                cov_xrl7693qr().f[29]++;
                cov_xrl7693qr().s[157]++;
                return sum + r.rating;
              }, 0) / resource.ratings.length * 10) / 10) :
              /* istanbul ignore next */
              (cov_xrl7693qr().b[66][1]++, 0));
              /* istanbul ignore next */
              cov_xrl7693qr().s[158]++;
              return {
                id: resource.id,
                title: resource.title,
                type: resource.type,
                skillLevel: resource.skillLevel,
                cost: resource.cost,
                duration: resource.duration,
                url: resource.url,
                averageRating: averageRating,
                ratingCount: resource.ratings.length,
                skills:
                /* istanbul ignore next */
                (cov_xrl7693qr().b[67][0]++,
                /* istanbul ignore next */
                (cov_xrl7693qr().b[69][0]++, (_a = resource.skills) === null) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[69][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_xrl7693qr().b[68][0]++, void 0) :
                /* istanbul ignore next */
                (cov_xrl7693qr().b[68][1]++, _a.map(function (skill) {
                  /* istanbul ignore next */
                  cov_xrl7693qr().f[30]++;
                  cov_xrl7693qr().s[159]++;
                  return skill.name;
                }))) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[67][1]++, [])
              };
            }),
            learningPaths: careerPath.learningPaths.map(function (path) {
              /* istanbul ignore next */
              cov_xrl7693qr().f[31]++;
              var _a;
              /* istanbul ignore next */
              cov_xrl7693qr().s[160]++;
              return {
                id: path.id,
                title: path.title,
                difficulty: path.difficulty,
                estimatedHours: path.estimatedHours,
                stepCount: path._count.steps,
                skills:
                /* istanbul ignore next */
                (cov_xrl7693qr().b[70][0]++,
                /* istanbul ignore next */
                (cov_xrl7693qr().b[72][0]++, (_a = path.skills) === null) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[72][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_xrl7693qr().b[71][0]++, void 0) :
                /* istanbul ignore next */
                (cov_xrl7693qr().b[71][1]++, _a.map(function (skill) {
                  /* istanbul ignore next */
                  cov_xrl7693qr().f[32]++;
                  cov_xrl7693qr().s[161]++;
                  return skill.name;
                }))) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[70][1]++, [])
              };
            }),
            performance: {
              queryTime: queryTime,
              transformTime: Date.now() - transformStartTime,
              totalSkills: careerPath.relatedSkills.length,
              totalResources: careerPath.learningResources.length,
              totalPaths: careerPath.learningPaths.length
            }
          };
          /* istanbul ignore next */
          cov_xrl7693qr().s[162]++;
          return [2 /*return*/, result];
        case 2:
          /* istanbul ignore next */
          cov_xrl7693qr().b[58][2]++;
          cov_xrl7693qr().s[163]++;
          error_3 = _a.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[164]++;
          console.error('Error fetching enhanced career path data:', error_3);
          /* istanbul ignore next */
          cov_xrl7693qr().s[165]++;
          return [2 /*return*/, null];
        case 3:
          /* istanbul ignore next */
          cov_xrl7693qr().b[58][3]++;
          cov_xrl7693qr().s[166]++;
          return [2 /*return*/];
      }
    });
  });
}
function createSkillGapAnalysis(userId, request, analysisData, careerPathData) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[33]++;
  cov_xrl7693qr().s[167]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[34]++;
    var expiresAt, skillGapAnalysis, error_4;
    var _a;
    /* istanbul ignore next */
    cov_xrl7693qr().s[168]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[35]++;
      cov_xrl7693qr().s[169]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_xrl7693qr().b[73][0]++;
          cov_xrl7693qr().s[170]++;
          _b.trys.push([0, 2,, 3]);
          /* istanbul ignore next */
          cov_xrl7693qr().s[171]++;
          expiresAt = new Date();
          /* istanbul ignore next */
          cov_xrl7693qr().s[172]++;
          expiresAt.setMonth(expiresAt.getMonth() + 3); // Analysis valid for 3 months
          /* istanbul ignore next */
          cov_xrl7693qr().s[173]++;
          return [4 /*yield*/, prisma_1.prisma.skillGapAnalysis.create({
            data: {
              userId: userId,
              targetCareerPathId:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[74][0]++, request.targetCareerPath.careerPathId) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[74][1]++, null),
              targetCareerPathName: request.targetCareerPath.careerPathName,
              experienceLevel: request.targetCareerPath.targetLevel,
              timeframe: request.preferences.timeframe,
              analysisData: analysisData,
              skillGaps:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[75][0]++, analysisData.skillGaps) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[75][1]++, []),
              learningPlan:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[76][0]++, analysisData.learningPlan) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[76][1]++, {}),
              marketData:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[77][0]++, analysisData.marketInsights) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[77][1]++, null),
              progressTracking: {
                milestones:
                /* istanbul ignore next */
                (cov_xrl7693qr().b[78][0]++,
                /* istanbul ignore next */
                (cov_xrl7693qr().b[80][0]++, (_a = analysisData.learningPlan) === null) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[80][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_xrl7693qr().b[79][0]++, void 0) :
                /* istanbul ignore next */
                (cov_xrl7693qr().b[79][1]++, _a.milestones)) ||
                /* istanbul ignore next */
                (cov_xrl7693qr().b[78][1]++, []),
                completedMilestones: [],
                currentPhase: 'planning'
              },
              status: 'ACTIVE',
              completionPercentage: 0,
              expiresAt: expiresAt
            }
          })];
        case 1:
          /* istanbul ignore next */
          cov_xrl7693qr().b[73][1]++;
          cov_xrl7693qr().s[174]++;
          skillGapAnalysis = _b.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[175]++;
          return [2 /*return*/, skillGapAnalysis];
        case 2:
          /* istanbul ignore next */
          cov_xrl7693qr().b[73][2]++;
          cov_xrl7693qr().s[176]++;
          error_4 = _b.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[177]++;
          console.error('Error creating skill gap analysis:', error_4);
          /* istanbul ignore next */
          cov_xrl7693qr().s[178]++;
          throw error_4;
        case 3:
          /* istanbul ignore next */
          cov_xrl7693qr().b[73][3]++;
          cov_xrl7693qr().s[179]++;
          return [2 /*return*/];
      }
    });
  });
}
function handleComprehensiveSkillsAnalysis(request) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[36]++;
  cov_xrl7693qr().s[180]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[37]++;
    var session, error, userId, body, validation, error, requestData, cacheKey, cacheTags, cached, batchResult, batchError_1;
    var _a;
    /* istanbul ignore next */
    cov_xrl7693qr().s[181]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[38]++;
      cov_xrl7693qr().s[182]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][0]++;
          cov_xrl7693qr().s[183]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][1]++;
          cov_xrl7693qr().s[184]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[185]++;
          if (!(
          /* istanbul ignore next */
          (cov_xrl7693qr().b[84][0]++, (_a =
          /* istanbul ignore next */
          (cov_xrl7693qr().b[86][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_xrl7693qr().b[86][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_xrl7693qr().b[85][0]++, void 0) :
          /* istanbul ignore next */
          (cov_xrl7693qr().b[85][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_xrl7693qr().b[84][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_xrl7693qr().b[83][0]++, void 0) :
          /* istanbul ignore next */
          (cov_xrl7693qr().b[83][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[82][0]++;
            cov_xrl7693qr().s[186]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_xrl7693qr().s[187]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_xrl7693qr().s[188]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[82][1]++;
          }
          cov_xrl7693qr().s[189]++;
          userId = session.user.id;
          /* istanbul ignore next */
          cov_xrl7693qr().s[190]++;
          return [4 /*yield*/, request.json()];
        case 2:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][2]++;
          cov_xrl7693qr().s[191]++;
          body = _b.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[192]++;
          validation = comprehensiveSkillsAnalysisSchema.safeParse(body);
          /* istanbul ignore next */
          cov_xrl7693qr().s[193]++;
          if (!validation.success) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[87][0]++;
            cov_xrl7693qr().s[194]++;
            error = new Error('Invalid request data');
            /* istanbul ignore next */
            cov_xrl7693qr().s[195]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_xrl7693qr().s[196]++;
            error.details = validation.error.errors;
            /* istanbul ignore next */
            cov_xrl7693qr().s[197]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[87][1]++;
          }
          cov_xrl7693qr().s[198]++;
          requestData = validation.data;
          /* istanbul ignore next */
          cov_xrl7693qr().s[199]++;
          cacheKey = "ai:skills_analysis:".concat(userId, ":comprehensive_").concat(requestData.targetCareerPath.careerPathName, "_").concat(requestData.targetCareerPath.targetLevel, "_").concat(requestData.preferences.timeframe);
          /* istanbul ignore next */
          cov_xrl7693qr().s[200]++;
          cacheTags = ['skill_analysis', 'career_path', requestData.includeMarketData ?
          /* istanbul ignore next */
          (cov_xrl7693qr().b[88][0]++, 'market_data') :
          /* istanbul ignore next */
          (cov_xrl7693qr().b[88][1]++, ''), userId].filter(Boolean);
          /* istanbul ignore next */
          cov_xrl7693qr().s[201]++;
          return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.get(cacheKey)];
        case 3:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][3]++;
          cov_xrl7693qr().s[202]++;
          cached = _b.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[203]++;
          if (cached) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[89][0]++;
            cov_xrl7693qr().s[204]++;
            return [2 /*return*/, server_1.NextResponse.json({
              success: true,
              data: cached,
              cached: true,
              generatedAt:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[90][0]++,
              /* istanbul ignore next */
              (cov_xrl7693qr().b[92][0]++, cached === null) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[92][1]++, cached === void 0) ?
              /* istanbul ignore next */
              (cov_xrl7693qr().b[91][0]++, void 0) :
              /* istanbul ignore next */
              (cov_xrl7693qr().b[91][1]++, cached.generatedAt)) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[90][1]++, new Date().toISOString())
            })];
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[89][1]++;
          }
          cov_xrl7693qr().s[205]++;
          _b.label = 4;
        case 4:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][4]++;
          cov_xrl7693qr().s[206]++;
          _b.trys.push([4, 9,, 11]);
          /* istanbul ignore next */
          cov_xrl7693qr().s[207]++;
          return [4 /*yield*/, request_batching_service_1.requestBatchingService.batchComprehensiveAnalysis(userId, requestData, 'medium' // priority
          )];
        case 5:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][5]++;
          cov_xrl7693qr().s[208]++;
          batchResult = _b.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[209]++;
          if (!(
          /* istanbul ignore next */
          (cov_xrl7693qr().b[94][0]++, batchResult.success) &&
          /* istanbul ignore next */
          (cov_xrl7693qr().b[94][1]++, batchResult.data))) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[93][0]++;
            cov_xrl7693qr().s[210]++;
            return [3 /*break*/, 7];
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[93][1]++;
          }
          // Cache the result with enhanced caching
          cov_xrl7693qr().s[211]++;
          return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.set(cacheKey, batchResult.data, {
            ttl: 1800000,
            tags: cacheTags
          })];
        case 6:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][6]++;
          cov_xrl7693qr().s[212]++;
          // Cache the result with enhanced caching
          _b.sent(); // 30 minutes
          /* istanbul ignore next */
          cov_xrl7693qr().s[213]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: batchResult.data,
            cached:
            /* istanbul ignore next */
            (cov_xrl7693qr().b[95][0]++, batchResult.cached) ||
            /* istanbul ignore next */
            (cov_xrl7693qr().b[95][1]++, false),
            batchProcessed: true,
            processingTime: batchResult.processingTime,
            generatedAt: new Date().toISOString()
          })];
        case 7:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][7]++;
          cov_xrl7693qr().s[214]++;
          throw new Error(
          /* istanbul ignore next */
          (cov_xrl7693qr().b[96][0]++, batchResult.error) ||
          /* istanbul ignore next */
          (cov_xrl7693qr().b[96][1]++, 'Batch processing failed'));
        case 8:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][8]++;
          cov_xrl7693qr().s[215]++;
          return [3 /*break*/, 11];
        case 9:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][9]++;
          cov_xrl7693qr().s[216]++;
          batchError_1 = _b.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[217]++;
          console.warn('Batch processing failed, falling back to individual processing:', batchError_1);
          /* istanbul ignore next */
          cov_xrl7693qr().s[218]++;
          return [4 /*yield*/, handleIndividualAnalysis(userId, requestData, cacheKey, cacheTags)];
        case 10:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][10]++;
          cov_xrl7693qr().s[219]++;
          // Fallback to individual processing if batch fails
          return [2 /*return*/, _b.sent()];
        case 11:
          /* istanbul ignore next */
          cov_xrl7693qr().b[81][11]++;
          cov_xrl7693qr().s[220]++;
          return [2 /*return*/];
      }
    });
  });
}
function handleIndividualAnalysis(userId, requestData, cacheKey, cacheTags) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[39]++;
  cov_xrl7693qr().s[221]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[40]++;
    var _a, userAssessments, careerPathData, careerAssessmentSkills, _b, finalCareerPathData, allCurrentSkills, uniqueSkills, responseData;
    var _this =
    /* istanbul ignore next */
    (cov_xrl7693qr().s[222]++, this);
    /* istanbul ignore next */
    cov_xrl7693qr().s[223]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[41]++;
      cov_xrl7693qr().s[224]++;
      switch (_c.label) {
        case 0:
          /* istanbul ignore next */
          cov_xrl7693qr().b[97][0]++;
          cov_xrl7693qr().s[225]++;
          return [4 /*yield*/, Promise.all([concurrent_database_service_1.concurrentDatabaseService.fetchUserAssessmentsOptimized(userId), requestData.targetCareerPath.careerPathId ?
          /* istanbul ignore next */
          (cov_xrl7693qr().b[98][0]++, concurrent_database_service_1.concurrentDatabaseService.fetchCareerPathDataOptimized(requestData.targetCareerPath.careerPathId)) :
          /* istanbul ignore next */
          (cov_xrl7693qr().b[98][1]++, getEnhancedCareerPathData(undefined, requestData.targetCareerPath.careerPathName))])];
        case 1:
          /* istanbul ignore next */
          cov_xrl7693qr().b[97][1]++;
          cov_xrl7693qr().s[226]++;
          _a = _c.sent(), userAssessments = _a[0], careerPathData = _a[1];
          /* istanbul ignore next */
          cov_xrl7693qr().s[227]++;
          if (!(userAssessments.length === 0)) {
            /* istanbul ignore next */
            cov_xrl7693qr().b[99][0]++;
            cov_xrl7693qr().s[228]++;
            return [3 /*break*/, 3];
          } else
          /* istanbul ignore next */
          {
            cov_xrl7693qr().b[99][1]++;
          }
          cov_xrl7693qr().s[229]++;
          return [4 /*yield*/, getSkillsFromCareerAssessment(userId)];
        case 2:
          /* istanbul ignore next */
          cov_xrl7693qr().b[97][2]++;
          cov_xrl7693qr().s[230]++;
          _b = _c.sent();
          /* istanbul ignore next */
          cov_xrl7693qr().s[231]++;
          return [3 /*break*/, 4];
        case 3:
          /* istanbul ignore next */
          cov_xrl7693qr().b[97][3]++;
          cov_xrl7693qr().s[232]++;
          _b = [];
          /* istanbul ignore next */
          cov_xrl7693qr().s[233]++;
          _c.label = 4;
        case 4:
          /* istanbul ignore next */
          cov_xrl7693qr().b[97][4]++;
          cov_xrl7693qr().s[234]++;
          careerAssessmentSkills = _b;
          /* istanbul ignore next */
          cov_xrl7693qr().s[235]++;
          finalCareerPathData =
          /* istanbul ignore next */
          (cov_xrl7693qr().b[100][0]++, careerPathData) ||
          /* istanbul ignore next */
          (cov_xrl7693qr().b[100][1]++, {
            id: 'fallback-career-path-id',
            name: requestData.targetCareerPath.careerPathName,
            requiredSkills: [],
            learningResources: [],
            learningPaths: []
          });
          /* istanbul ignore next */
          cov_xrl7693qr().s[236]++;
          allCurrentSkills = __spreadArray(__spreadArray(__spreadArray([], requestData.currentSkills, true), userAssessments.filter(function (assessment) {
            /* istanbul ignore next */
            cov_xrl7693qr().f[42]++;
            cov_xrl7693qr().s[237]++;
            return assessment.skill;
          }) // Only include assessments with skill data
          .map(function (assessment) {
            /* istanbul ignore next */
            cov_xrl7693qr().f[43]++;
            var _a, _b, _c;
            /* istanbul ignore next */
            cov_xrl7693qr().s[238]++;
            return {
              skillName:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[101][0]++,
              /* istanbul ignore next */
              (cov_xrl7693qr().b[103][0]++, (_a = assessment.skill) === null) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[103][1]++, _a === void 0) ?
              /* istanbul ignore next */
              (cov_xrl7693qr().b[102][0]++, void 0) :
              /* istanbul ignore next */
              (cov_xrl7693qr().b[102][1]++, _a.name)) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[101][1]++, assessment.skillName),
              selfRating: assessment.selfRating,
              confidenceLevel: assessment.confidenceLevel,
              lastUsed:
              /* istanbul ignore next */
              (cov_xrl7693qr().b[104][0]++,
              /* istanbul ignore next */
              (cov_xrl7693qr().b[106][0]++, (_b = assessment.createdAt) === null) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[106][1]++, _b === void 0) ?
              /* istanbul ignore next */
              (cov_xrl7693qr().b[105][0]++, void 0) :
              /* istanbul ignore next */
              (cov_xrl7693qr().b[105][1]++, _b.toISOString())) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[104][1]++,
              /* istanbul ignore next */
              (cov_xrl7693qr().b[108][0]++, (_c = assessment.lastAssessed) === null) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[108][1]++, _c === void 0) ?
              /* istanbul ignore next */
              (cov_xrl7693qr().b[107][0]++, void 0) :
              /* istanbul ignore next */
              (cov_xrl7693qr().b[107][1]++, _c.toISOString()))
            };
          }), true), careerAssessmentSkills, true);
          /* istanbul ignore next */
          cov_xrl7693qr().s[239]++;
          uniqueSkills = allCurrentSkills.reduce(function (acc, skill) {
            /* istanbul ignore next */
            cov_xrl7693qr().f[44]++;
            var existing =
            /* istanbul ignore next */
            (cov_xrl7693qr().s[240]++, acc.find(function (s) {
              /* istanbul ignore next */
              cov_xrl7693qr().f[45]++;
              cov_xrl7693qr().s[241]++;
              return s.skillName.toLowerCase() === skill.skillName.toLowerCase();
            }));
            /* istanbul ignore next */
            cov_xrl7693qr().s[242]++;
            if (!existing) {
              /* istanbul ignore next */
              cov_xrl7693qr().b[109][0]++;
              cov_xrl7693qr().s[243]++;
              acc.push(skill);
            } else {
              /* istanbul ignore next */
              cov_xrl7693qr().b[109][1]++;
              cov_xrl7693qr().s[244]++;
              if (
              /* istanbul ignore next */
              (cov_xrl7693qr().b[111][0]++, skill.selfRating) &&
              /* istanbul ignore next */
              (cov_xrl7693qr().b[111][1]++, skill.selfRating > (
              /* istanbul ignore next */
              (cov_xrl7693qr().b[112][0]++, existing.selfRating) ||
              /* istanbul ignore next */
              (cov_xrl7693qr().b[112][1]++, 0)))) {
                /* istanbul ignore next */
                cov_xrl7693qr().b[110][0]++;
                cov_xrl7693qr().s[245]++;
                // Keep the higher rating if duplicate
                Object.assign(existing, skill);
              } else
              /* istanbul ignore next */
              {
                cov_xrl7693qr().b[110][1]++;
              }
            }
            /* istanbul ignore next */
            cov_xrl7693qr().s[246]++;
            return acc;
          }, []);
          /* istanbul ignore next */
          cov_xrl7693qr().s[247]++;
          return [4 /*yield*/, skill_gap_performance_1.skillGapPerformanceMonitor.monitorSkillAnalysis(requestData, function () {
            /* istanbul ignore next */
            cov_xrl7693qr().f[46]++;
            cov_xrl7693qr().s[248]++;
            return __awaiter(_this, void 0, void 0, function () {
              /* istanbul ignore next */
              cov_xrl7693qr().f[47]++;
              var _a, edgeCaseResult, analysisResult, finalAnalysisResult, fallbackData, skillGapAnalysis, responseData;
              /* istanbul ignore next */
              cov_xrl7693qr().s[249]++;
              return __generator(this, function (_b) {
                /* istanbul ignore next */
                cov_xrl7693qr().f[48]++;
                cov_xrl7693qr().s[250]++;
                switch (_b.label) {
                  case 0:
                    /* istanbul ignore next */
                    cov_xrl7693qr().b[113][0]++;
                    cov_xrl7693qr().s[251]++;
                    return [4 /*yield*/, Promise.allSettled([EdgeCaseHandlerService_1.edgeCaseHandlerService.handleLearningPathGeneration({
                      userId: userId,
                      currentSkills: uniqueSkills.map(function (skill) {
                        /* istanbul ignore next */
                        cov_xrl7693qr().f[49]++;
                        cov_xrl7693qr().s[252]++;
                        return {
                          skill: skill.skillName,
                          level: skill.selfRating,
                          confidence: skill.confidenceLevel
                        };
                      }),
                      targetRole: requestData.targetCareerPath.careerPathName,
                      timeframe: requestData.preferences.timeframe === 'THREE_MONTHS' ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[114][0]++, 3) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[114][1]++, requestData.preferences.timeframe === 'SIX_MONTHS' ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[115][0]++, 6) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[115][1]++, requestData.preferences.timeframe === 'ONE_YEAR' ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[116][0]++, 12) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[116][1]++, requestData.preferences.timeframe === 'TWO_YEARS' ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[117][0]++, 24) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[117][1]++, 12)))),
                      learningStyle: 'balanced',
                      availability: requestData.preferences.hoursPerWeek,
                      budget: requestData.preferences.budget === 'FREE' ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[118][0]++, 0) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[118][1]++, requestData.preferences.budget === 'FREEMIUM' ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[119][0]++, 100) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[119][1]++, requestData.preferences.budget === 'PAID' ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[120][0]++, 1000) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[120][1]++, 500)))
                    }), geminiService_1.geminiService.analyzeComprehensiveSkillGap(uniqueSkills, requestData.targetCareerPath, requestData.preferences, finalCareerPathData, userId)])];
                  case 1:
                    /* istanbul ignore next */
                    cov_xrl7693qr().b[113][1]++;
                    cov_xrl7693qr().s[253]++;
                    _a = _b.sent(), edgeCaseResult = _a[0], analysisResult = _a[1];
                    /* istanbul ignore next */
                    cov_xrl7693qr().s[254]++;
                    if (
                    /* istanbul ignore next */
                    (cov_xrl7693qr().b[122][0]++, analysisResult.status === 'fulfilled') &&
                    /* istanbul ignore next */
                    (cov_xrl7693qr().b[122][1]++, analysisResult.value.success)) {
                      /* istanbul ignore next */
                      cov_xrl7693qr().b[121][0]++;
                      cov_xrl7693qr().s[255]++;
                      finalAnalysisResult = analysisResult.value;
                    } else {
                      /* istanbul ignore next */
                      cov_xrl7693qr().b[121][1]++;
                      cov_xrl7693qr().s[256]++;
                      if (
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[124][0]++, edgeCaseResult.status === 'fulfilled') &&
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[124][1]++, edgeCaseResult.value.success)) {
                        /* istanbul ignore next */
                        cov_xrl7693qr().b[123][0]++;
                        cov_xrl7693qr().s[257]++;
                        console.warn('Primary AI analysis failed, using EdgeCaseHandler result');
                        /* istanbul ignore next */
                        cov_xrl7693qr().s[258]++;
                        finalAnalysisResult = {
                          success: true,
                          data: edgeCaseResult.value.data
                        };
                      } else {
                        /* istanbul ignore next */
                        cov_xrl7693qr().b[123][1]++;
                        cov_xrl7693qr().s[259]++;
                        fallbackData = edgeCaseResult.status === 'fulfilled' ?
                        /* istanbul ignore next */
                        (cov_xrl7693qr().b[125][0]++, edgeCaseResult.value.fallbackData) :
                        /* istanbul ignore next */
                        (cov_xrl7693qr().b[125][1]++, null);
                        /* istanbul ignore next */
                        cov_xrl7693qr().s[260]++;
                        if (fallbackData) {
                          /* istanbul ignore next */
                          cov_xrl7693qr().b[126][0]++;
                          cov_xrl7693qr().s[261]++;
                          return [2 /*return*/, {
                            skillGaps: [],
                            learningPlan: fallbackData,
                            careerReadiness: {
                              currentScore: 0,
                              targetScore: 100,
                              improvementPotential: 100,
                              timeToTarget: 12
                            },
                            marketInsights: undefined,
                            edgeCaseHandlerUsed: true,
                            fallbackDataUsed: true
                          }];
                        } else
                        /* istanbul ignore next */
                        {
                          cov_xrl7693qr().b[126][1]++;
                        }
                        cov_xrl7693qr().s[262]++;
                        throw new Error('All analysis methods failed');
                      }
                    }
                    /* istanbul ignore next */
                    cov_xrl7693qr().s[263]++;
                    return [4 /*yield*/, concurrent_database_service_1.concurrentDatabaseService.createSkillGapAnalysisOptimized(userId, requestData, finalAnalysisResult.data, finalCareerPathData)];
                  case 2:
                    /* istanbul ignore next */
                    cov_xrl7693qr().b[113][2]++;
                    cov_xrl7693qr().s[264]++;
                    skillGapAnalysis = _b.sent();
                    /* istanbul ignore next */
                    cov_xrl7693qr().s[265]++;
                    responseData = {
                      analysisId: skillGapAnalysis.id,
                      skillGaps:
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[127][0]++, finalAnalysisResult.data.skillGaps) ||
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[127][1]++, []),
                      learningPlan:
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[128][0]++, finalAnalysisResult.data.learningPlan) ||
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[128][1]++, {
                        totalEstimatedHours: 0,
                        milestones: [],
                        recommendedResources: []
                      }),
                      careerReadiness:
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[129][0]++, finalAnalysisResult.data.careerReadiness) ||
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[129][1]++, {
                        currentScore: 0,
                        targetScore: 100,
                        improvementPotential: 100,
                        timeToTarget: 12
                      }),
                      marketInsights: requestData.includeMarketData ?
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[130][0]++, finalAnalysisResult.data.marketInsights) :
                      /* istanbul ignore next */
                      (cov_xrl7693qr().b[130][1]++, undefined)
                    };
                    /* istanbul ignore next */
                    cov_xrl7693qr().s[266]++;
                    return [2 /*return*/, responseData];
                }
              });
            });
          }, userId)];
        case 5:
          /* istanbul ignore next */
          cov_xrl7693qr().b[97][5]++;
          cov_xrl7693qr().s[267]++;
          responseData = _c.sent();
          // Cache the result using enhanced caching service
          /* istanbul ignore next */
          cov_xrl7693qr().s[268]++;
          return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.set(cacheKey, responseData, {
            ttl: 1800000,
            tags: cacheTags
          })];
        case 6:
          /* istanbul ignore next */
          cov_xrl7693qr().b[97][6]++;
          cov_xrl7693qr().s[269]++;
          // Cache the result using enhanced caching service
          _c.sent(); // 30 minutes
          // Track usage analytics
          /* istanbul ignore next */
          cov_xrl7693qr().s[270]++;
          console.log("Individual comprehensive skills analysis completed for user ".concat(userId, ", career: ").concat(requestData.targetCareerPath.careerPathName));
          /* istanbul ignore next */
          cov_xrl7693qr().s[271]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: responseData,
            cached: false,
            individualProcessing: true,
            generatedAt: new Date().toISOString()
          })];
      }
    });
  });
}
// POST endpoint for comprehensive analysis
/* istanbul ignore next */
cov_xrl7693qr().s[272]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_xrl7693qr().f[50]++;
  cov_xrl7693qr().s[273]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_xrl7693qr().f[51]++;
    cov_xrl7693qr().s[274]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_xrl7693qr().f[52]++;
      cov_xrl7693qr().s[275]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_xrl7693qr().f[53]++;
        cov_xrl7693qr().s[276]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_xrl7693qr().f[54]++;
          cov_xrl7693qr().s[277]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_xrl7693qr().f[55]++;
            cov_xrl7693qr().s[278]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 5
            },
            // 5 comprehensive analyses per 15 minutes
            function () {
              /* istanbul ignore next */
              cov_xrl7693qr().f[56]++;
              cov_xrl7693qr().s[279]++;
              return handleComprehensiveSkillsAnalysis(request);
            })];
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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