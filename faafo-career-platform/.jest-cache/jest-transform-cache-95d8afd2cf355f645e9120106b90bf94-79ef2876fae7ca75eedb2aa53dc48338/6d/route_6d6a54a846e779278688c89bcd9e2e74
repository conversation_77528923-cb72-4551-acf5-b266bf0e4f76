c3876e2e0a19457301058577b37d6d2d
"use strict";

/* istanbul ignore next */
function cov_1og5ww7zxm() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/user/route.ts";
  var hash = "c47758decf57f740f71361fc336268a628951413";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/user/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "68": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 21
        }
      },
      "69": {
        start: {
          line: 40,
          column: 15
        },
        end: {
          line: 40,
          column: 37
        }
      },
      "70": {
        start: {
          line: 41,
          column: 18
        },
        end: {
          line: 41,
          column: 38
        }
      },
      "71": {
        start: {
          line: 42,
          column: 13
        },
        end: {
          line: 42,
          column: 34
        }
      },
      "72": {
        start: {
          line: 43,
          column: 34
        },
        end: {
          line: 43,
          column: 76
        }
      },
      "73": {
        start: {
          line: 44,
          column: 18
        },
        end: {
          line: 44,
          column: 44
        }
      },
      "74": {
        start: {
          line: 45,
          column: 15
        },
        end: {
          line: 45,
          column: 38
        }
      },
      "75": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 113,
          column: 7
        }
      },
      "76": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 112,
          column: 11
        }
      },
      "77": {
        start: {
          line: 51,
          column: 12
        },
        end: {
          line: 111,
          column: 13
        }
      },
      "78": {
        start: {
          line: 52,
          column: 24
        },
        end: {
          line: 52,
          column: 100
        }
      },
      "79": {
        start: {
          line: 54,
          column: 20
        },
        end: {
          line: 54,
          column: 40
        }
      },
      "80": {
        start: {
          line: 55,
          column: 20
        },
        end: {
          line: 59,
          column: 21
        }
      },
      "81": {
        start: {
          line: 56,
          column: 24
        },
        end: {
          line: 56,
          column: 69
        }
      },
      "82": {
        start: {
          line: 57,
          column: 24
        },
        end: {
          line: 57,
          column: 47
        }
      },
      "83": {
        start: {
          line: 58,
          column: 24
        },
        end: {
          line: 58,
          column: 36
        }
      },
      "84": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 45
        }
      },
      "85": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 68,
          column: 28
        }
      },
      "86": {
        start: {
          line: 70,
          column: 20
        },
        end: {
          line: 70,
          column: 41
        }
      },
      "87": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 79,
          column: 27
        }
      },
      "88": {
        start: {
          line: 71,
          column: 70
        },
        end: {
          line: 79,
          column: 23
        }
      },
      "89": {
        start: {
          line: 80,
          column: 20
        },
        end: {
          line: 80,
          column: 99
        }
      },
      "90": {
        start: {
          line: 80,
          column: 66
        },
        end: {
          line: 80,
          column: 95
        }
      },
      "91": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 102,
          column: 21
        }
      },
      "92": {
        start: {
          line: 82,
          column: 24
        },
        end: {
          line: 82,
          column: 75
        }
      },
      "93": {
        start: {
          line: 83,
          column: 24
        },
        end: {
          line: 83,
          column: 141
        }
      },
      "94": {
        start: {
          line: 84,
          column: 24
        },
        end: {
          line: 84,
          column: 161
        }
      },
      "95": {
        start: {
          line: 85,
          column: 24
        },
        end: {
          line: 85,
          column: 114
        }
      },
      "96": {
        start: {
          line: 86,
          column: 24
        },
        end: {
          line: 88,
          column: 27
        }
      },
      "97": {
        start: {
          line: 87,
          column: 28
        },
        end: {
          line: 87,
          column: 76
        }
      },
      "98": {
        start: {
          line: 89,
          column: 24
        },
        end: {
          line: 101,
          column: 26
        }
      },
      "99": {
        start: {
          line: 103,
          column: 20
        },
        end: {
          line: 109,
          column: 22
        }
      },
      "100": {
        start: {
          line: 110,
          column: 20
        },
        end: {
          line: 110,
          column: 84
        }
      },
      "101": {
        start: {
          line: 116,
          column: 18
        },
        end: {
          line: 116,
          column: 37
        }
      },
      "102": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 117,
          column: 58
        }
      },
      "103": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 118,
          column: 33
        }
      },
      "104": {
        start: {
          line: 120,
          column: 0
        },
        end: {
          line: 125,
          column: 7
        }
      },
      "105": {
        start: {
          line: 120,
          column: 93
        },
        end: {
          line: 125,
          column: 3
        }
      },
      "106": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 124,
          column: 7
        }
      },
      "107": {
        start: {
          line: 122,
          column: 8
        },
        end: {
          line: 123,
          column: 72
        }
      },
      "108": {
        start: {
          line: 123,
          column: 26
        },
        end: {
          line: 123,
          column: 67
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "handleGetUserGapAnalyses",
        decl: {
          start: {
            line: 46,
            column: 9
          },
          end: {
            line: 46,
            column: 33
          }
        },
        loc: {
          start: {
            line: 46,
            column: 43
          },
          end: {
            line: 114,
            column: 1
          }
        },
        line: 46
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 47,
            column: 44
          },
          end: {
            line: 47,
            column: 45
          }
        },
        loc: {
          start: {
            line: 47,
            column: 56
          },
          end: {
            line: 113,
            column: 5
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 50,
            column: 33
          },
          end: {
            line: 50,
            column: 34
          }
        },
        loc: {
          start: {
            line: 50,
            column: 47
          },
          end: {
            line: 112,
            column: 9
          }
        },
        line: 50
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 71,
            column: 48
          },
          end: {
            line: 71,
            column: 49
          }
        },
        loc: {
          start: {
            line: 71,
            column: 68
          },
          end: {
            line: 79,
            column: 25
          }
        },
        line: 71
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 80,
            column: 51
          },
          end: {
            line: 80,
            column: 52
          }
        },
        loc: {
          start: {
            line: 80,
            column: 64
          },
          end: {
            line: 80,
            column: 97
          }
        },
        line: 80
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 86,
            column: 56
          },
          end: {
            line: 86,
            column: 57
          }
        },
        loc: {
          start: {
            line: 86,
            column: 69
          },
          end: {
            line: 88,
            column: 25
          }
        },
        line: 86
      },
      "19": {
        name: "calculateMilestoneDueDate",
        decl: {
          start: {
            line: 115,
            column: 9
          },
          end: {
            line: 115,
            column: 34
          }
        },
        loc: {
          start: {
            line: 115,
            column: 62
          },
          end: {
            line: 119,
            column: 1
          }
        },
        line: 115
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 120,
            column: 72
          },
          end: {
            line: 120,
            column: 73
          }
        },
        loc: {
          start: {
            line: 120,
            column: 91
          },
          end: {
            line: 125,
            column: 5
          }
        },
        line: 120
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 120,
            column: 134
          },
          end: {
            line: 120,
            column: 135
          }
        },
        loc: {
          start: {
            line: 120,
            column: 146
          },
          end: {
            line: 125,
            column: 1
          }
        },
        line: 120
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 121,
            column: 29
          },
          end: {
            line: 121,
            column: 30
          }
        },
        loc: {
          start: {
            line: 121,
            column: 43
          },
          end: {
            line: 124,
            column: 5
          }
        },
        line: 121
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 123,
            column: 12
          },
          end: {
            line: 123,
            column: 13
          }
        },
        loc: {
          start: {
            line: 123,
            column: 24
          },
          end: {
            line: 123,
            column: 69
          }
        },
        line: 123
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 52,
            column: 16
          },
          end: {
            line: 52,
            column: 100
          }
        }, {
          start: {
            line: 53,
            column: 16
          },
          end: {
            line: 68,
            column: 28
          }
        }, {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 110,
            column: 84
          }
        }],
        line: 51
      },
      "33": {
        loc: {
          start: {
            line: 55,
            column: 20
          },
          end: {
            line: 59,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 20
          },
          end: {
            line: 59,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "34": {
        loc: {
          start: {
            line: 55,
            column: 26
          },
          end: {
            line: 55,
            column: 138
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 124
          },
          end: {
            line: 55,
            column: 130
          }
        }, {
          start: {
            line: 55,
            column: 133
          },
          end: {
            line: 55,
            column: 138
          }
        }],
        line: 55
      },
      "35": {
        loc: {
          start: {
            line: 55,
            column: 26
          },
          end: {
            line: 55,
            column: 121
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 26
          },
          end: {
            line: 55,
            column: 104
          }
        }, {
          start: {
            line: 55,
            column: 108
          },
          end: {
            line: 55,
            column: 121
          }
        }],
        line: 55
      },
      "36": {
        loc: {
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 55,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 73
          },
          end: {
            line: 55,
            column: 79
          }
        }, {
          start: {
            line: 55,
            column: 82
          },
          end: {
            line: 55,
            column: 94
          }
        }],
        line: 55
      },
      "37": {
        loc: {
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 55,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 55,
            column: 48
          }
        }, {
          start: {
            line: 55,
            column: 52
          },
          end: {
            line: 55,
            column: 70
          }
        }],
        line: 55
      },
      "38": {
        loc: {
          start: {
            line: 81,
            column: 20
          },
          end: {
            line: 102,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 81,
            column: 20
          },
          end: {
            line: 102,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 81
      },
      "39": {
        loc: {
          start: {
            line: 83,
            column: 37
          },
          end: {
            line: 83,
            column: 140
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 38
          },
          end: {
            line: 83,
            column: 133
          }
        }, {
          start: {
            line: 83,
            column: 138
          },
          end: {
            line: 83,
            column: 140
          }
        }],
        line: 83
      },
      "40": {
        loc: {
          start: {
            line: 83,
            column: 38
          },
          end: {
            line: 83,
            column: 133
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 83,
            column: 97
          },
          end: {
            line: 83,
            column: 103
          }
        }, {
          start: {
            line: 83,
            column: 106
          },
          end: {
            line: 83,
            column: 133
          }
        }],
        line: 83
      },
      "41": {
        loc: {
          start: {
            line: 83,
            column: 38
          },
          end: {
            line: 83,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 38
          },
          end: {
            line: 83,
            column: 63
          }
        }, {
          start: {
            line: 83,
            column: 67
          },
          end: {
            line: 83,
            column: 94
          }
        }],
        line: 83
      },
      "42": {
        loc: {
          start: {
            line: 84,
            column: 48
          },
          end: {
            line: 84,
            column: 160
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 49
          },
          end: {
            line: 84,
            column: 153
          }
        }, {
          start: {
            line: 84,
            column: 158
          },
          end: {
            line: 84,
            column: 160
          }
        }],
        line: 84
      },
      "43": {
        loc: {
          start: {
            line: 84,
            column: 49
          },
          end: {
            line: 84,
            column: 153
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 84,
            column: 108
          },
          end: {
            line: 84,
            column: 114
          }
        }, {
          start: {
            line: 84,
            column: 117
          },
          end: {
            line: 84,
            column: 153
          }
        }],
        line: 84
      },
      "44": {
        loc: {
          start: {
            line: 84,
            column: 49
          },
          end: {
            line: 84,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 49
          },
          end: {
            line: 84,
            column: 74
          }
        }, {
          start: {
            line: 84,
            column: 78
          },
          end: {
            line: 84,
            column: 105
          }
        }],
        line: 84
      },
      "45": {
        loc: {
          start: {
            line: 85,
            column: 36
          },
          end: {
            line: 85,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 78
          },
          end: {
            line: 85,
            column: 109
          }
        }, {
          start: {
            line: 85,
            column: 112
          },
          end: {
            line: 85,
            column: 113
          }
        }],
        line: 85
      },
      "46": {
        loc: {
          start: {
            line: 94,
            column: 43
          },
          end: {
            line: 100,
            column: 29
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 94,
            column: 59
          },
          end: {
            line: 97,
            column: 29
          }
        }, {
          start: {
            line: 97,
            column: 32
          },
          end: {
            line: 100,
            column: 29
          }
        }],
        line: 94
      },
      "47": {
        loc: {
          start: {
            line: 95,
            column: 40
          },
          end: {
            line: 95,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 95,
            column: 40
          },
          end: {
            line: 95,
            column: 60
          }
        }, {
          start: {
            line: 95,
            column: 64
          },
          end: {
            line: 95,
            column: 66
          }
        }],
        line: 95
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/user/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,6EAAwF;AACxF,6CAAgD;AAChD,uCAAsC;AA2BtC,SAAe,wBAAwB,CAAC,OAAoB;mCAAG,OAAO;;;;;wBACpD,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;oBAA7C,OAAO,GAAG,SAAmC;oBACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;wBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;wBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;wBACvB,MAAM,KAAK,CAAC;oBACd,CAAC;oBAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAEd,qBAAM,eAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;4BACtD,KAAK,EAAE;gCACL,MAAM,QAAA;6BACP;4BACD,OAAO,EAAE;gCACP,SAAS,EAAE,MAAM;6BAClB;yBACF,CAAC,EAAA;;oBAPI,QAAQ,GAAG,SAOf;oBAEM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,CAAC;wBAC7C,EAAE,EAAE,QAAQ,CAAC,EAAE;wBACf,gBAAgB,EAAE,QAAQ,CAAC,oBAAoB;wBAC/C,MAAM,EAAE,QAAQ,CAAC,MAAM;wBACvB,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB;wBACnD,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE;wBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE;wBAC/C,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE;qBAC5C,CAAC,EAR4C,CAQ5C,CAAC,CAAC;oBAGE,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,QAAQ,EAArB,CAAqB,CAAC,CAAC;oBAGjE,IAAI,cAAc,EAAE,CAAC;wBACb,gBAAgB,GAAG,cAAc,CAAC,gBAAuB,CAAC;wBAC1D,UAAU,GAAG,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,UAAU,KAAI,EAAE,CAAC;wBAChD,wBAAsB,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,mBAAmB,KAAI,EAAE,CAAC;wBAClE,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBAG1F,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,UAAC,CAAM;4BAC3C,OAAA,CAAC,qBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;wBAAtC,CAAsC,CACvC,CAAC;wBAEF,kBAAkB,GAAG;4BACnB,EAAE,EAAE,cAAc,CAAC,EAAE;4BACrB,SAAS,WAAA;4BACT,mBAAmB,EAAE,qBAAmB,CAAC,MAAM;4BAC/C,eAAe,EAAE,UAAU,CAAC,MAAM;4BAClC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;gCAC7B,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,EAAE;gCAClC,OAAO,EAAE,yBAAyB,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC;6BAClF,CAAC,CAAC,CAAC;gCACF,MAAM,EAAE,EAAE;gCACV,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;6BAClC;yBACF,CAAC;oBACJ,CAAC;oBAEG,YAAY,GAA4B;wBAC5C,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,QAAQ,EAAE,YAAY;4BACtB,cAAc,EAAE,kBAAkB;yBACnC;qBACF,CAAC;oBAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC,YAAY,CAAC,EAAC;;;;CACxC;AAED,SAAS,yBAAyB,CAAC,SAAe,EAAE,cAAsB;IACxE,IAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,cAAc,CAAC,CAAC;IACtD,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC;AAC/B,CAAC;AAEY,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACrE,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,6BAA6B;YAC5E,cAAM,OAAA,wBAAwB,CAAC,OAAO,CAAC,EAAjC,CAAiC,CACxC,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/user/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { prisma } from '@/lib/prisma';\n\ninterface UserGapAnalysesResponse {\n  success: boolean;\n  data: {\n    analyses: Array<{\n      id: string;\n      targetCareerPath: string;\n      status: string;\n      completionPercentage: number;\n      createdAt: string;\n      lastUpdated: string;\n      expiresAt: string;\n    }>;\n    activeAnalysis?: {\n      id: string;\n      skillGaps: number;\n      completedMilestones: number;\n      totalMilestones: number;\n      nextMilestone: {\n        skills: string[];\n        dueDate: string;\n      };\n    };\n  };\n}\n\nasync function handleGetUserGapAnalyses(request: NextRequest): Promise<NextResponse> {\n  const session = await getServerSession(authOptions);\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const userId = session.user.id;\n  // Get all gap analyses for the user\n  const analyses = await prisma.skillGapAnalysis.findMany({\n    where: {\n      userId,\n    },\n    orderBy: {\n      createdAt: 'desc',\n    },\n  });\n\n    const analysesData = analyses.map(analysis => ({\n      id: analysis.id,\n      targetCareerPath: analysis.targetCareerPathName,\n      status: analysis.status,\n      completionPercentage: analysis.completionPercentage,\n      createdAt: analysis.createdAt.toISOString(),\n      lastUpdated: analysis.lastUpdated.toISOString(),\n      expiresAt: analysis.expiresAt.toISOString(),\n    }));\n\n    // Find active analysis\n    const activeAnalysis = analyses.find(a => a.status === 'ACTIVE');\n    let activeAnalysisData;\n\n    if (activeAnalysis) {\n      const progressTracking = activeAnalysis.progressTracking as any;\n      const milestones = progressTracking?.milestones || [];\n      const completedMilestones = progressTracking?.completedMilestones || [];\n      const skillGaps = Array.isArray(activeAnalysis.skillGaps) ? activeAnalysis.skillGaps.length : 0;\n\n      // Find next milestone\n      const nextMilestone = milestones.find((m: any) => \n        !completedMilestones.includes(m.month)\n      );\n\n      activeAnalysisData = {\n        id: activeAnalysis.id,\n        skillGaps,\n        completedMilestones: completedMilestones.length,\n        totalMilestones: milestones.length,\n        nextMilestone: nextMilestone ? {\n          skills: nextMilestone.skills || [],\n          dueDate: calculateMilestoneDueDate(activeAnalysis.createdAt, nextMilestone.month),\n        } : {\n          skills: [],\n          dueDate: new Date().toISOString(),\n        },\n      };\n    }\n\n  const responseData: UserGapAnalysesResponse = {\n    success: true,\n    data: {\n      analyses: analysesData,\n      activeAnalysis: activeAnalysisData,\n    },\n  };\n\n  return NextResponse.json(responseData);\n}\n\nfunction calculateMilestoneDueDate(startDate: Date, milestoneMonth: number): string {\n  const dueDate = new Date(startDate);\n  dueDate.setMonth(dueDate.getMonth() + milestoneMonth);\n  return dueDate.toISOString();\n}\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 60 }, // 60 requests per 15 minutes\n    () => handleGetUserGapAnalyses(request)\n  );\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c47758decf57f740f71361fc336268a628951413"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1og5ww7zxm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1og5ww7zxm();
var __awaiter =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[0]++,
/* istanbul ignore next */
(cov_1og5ww7zxm().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1og5ww7zxm().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1og5ww7zxm().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1og5ww7zxm().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1og5ww7zxm().f[1]++;
    cov_1og5ww7zxm().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().f[2]++;
      cov_1og5ww7zxm().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1og5ww7zxm().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1og5ww7zxm().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1og5ww7zxm().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1og5ww7zxm().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().f[4]++;
      cov_1og5ww7zxm().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().f[5]++;
      cov_1og5ww7zxm().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().f[6]++;
      cov_1og5ww7zxm().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1og5ww7zxm().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1og5ww7zxm().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1og5ww7zxm().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[12]++,
/* istanbul ignore next */
(cov_1og5ww7zxm().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1og5ww7zxm().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1og5ww7zxm().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1og5ww7zxm().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1og5ww7zxm().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1og5ww7zxm().f[8]++;
        cov_1og5ww7zxm().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1og5ww7zxm().b[6][0]++;
          cov_1og5ww7zxm().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1og5ww7zxm().b[6][1]++;
        }
        cov_1og5ww7zxm().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1og5ww7zxm().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1og5ww7zxm().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1og5ww7zxm().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1og5ww7zxm().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1og5ww7zxm().f[9]++;
    cov_1og5ww7zxm().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1og5ww7zxm().f[10]++;
    cov_1og5ww7zxm().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().f[11]++;
      cov_1og5ww7zxm().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1og5ww7zxm().f[12]++;
    cov_1og5ww7zxm().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().b[9][0]++;
      cov_1og5ww7zxm().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1og5ww7zxm().b[9][1]++;
    }
    cov_1og5ww7zxm().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1og5ww7zxm().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[15][0]++,
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[16][1]++,
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1og5ww7zxm().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1og5ww7zxm().b[12][0]++;
          cov_1og5ww7zxm().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1og5ww7zxm().b[12][1]++;
        }
        cov_1og5ww7zxm().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1og5ww7zxm().b[18][0]++;
          cov_1og5ww7zxm().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1og5ww7zxm().b[18][1]++;
        }
        cov_1og5ww7zxm().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[19][1]++;
            cov_1og5ww7zxm().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[19][2]++;
            cov_1og5ww7zxm().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[19][3]++;
            cov_1og5ww7zxm().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[19][4]++;
            cov_1og5ww7zxm().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[19][5]++;
            cov_1og5ww7zxm().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1og5ww7zxm().b[20][0]++;
              cov_1og5ww7zxm().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1og5ww7zxm().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1og5ww7zxm().b[20][1]++;
            }
            cov_1og5ww7zxm().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1og5ww7zxm().b[23][0]++;
              cov_1og5ww7zxm().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1og5ww7zxm().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1og5ww7zxm().b[23][1]++;
            }
            cov_1og5ww7zxm().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1og5ww7zxm().b[25][0]++;
              cov_1og5ww7zxm().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1og5ww7zxm().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1og5ww7zxm().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1og5ww7zxm().b[25][1]++;
            }
            cov_1og5ww7zxm().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1og5ww7zxm().b[27][0]++;
              cov_1og5ww7zxm().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1og5ww7zxm().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1og5ww7zxm().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1og5ww7zxm().b[27][1]++;
            }
            cov_1og5ww7zxm().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1og5ww7zxm().b[29][0]++;
              cov_1og5ww7zxm().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1og5ww7zxm().b[29][1]++;
            }
            cov_1og5ww7zxm().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1og5ww7zxm().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1og5ww7zxm().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().b[30][0]++;
      cov_1og5ww7zxm().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1og5ww7zxm().b[30][1]++;
    }
    cov_1og5ww7zxm().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1og5ww7zxm().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1og5ww7zxm().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1og5ww7zxm().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1og5ww7zxm().s[68]++;
exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[69]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[70]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[71]++, require("@/lib/auth"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[72]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[73]++, require("@/lib/rateLimit"));
var prisma_1 =
/* istanbul ignore next */
(cov_1og5ww7zxm().s[74]++, require("@/lib/prisma"));
function handleGetUserGapAnalyses(request) {
  /* istanbul ignore next */
  cov_1og5ww7zxm().f[13]++;
  cov_1og5ww7zxm().s[75]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1og5ww7zxm().f[14]++;
    var session, error, userId, analyses, analysesData, activeAnalysis, activeAnalysisData, progressTracking, milestones, completedMilestones_1, skillGaps, nextMilestone, responseData;
    var _a;
    /* istanbul ignore next */
    cov_1og5ww7zxm().s[76]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().f[15]++;
      cov_1og5ww7zxm().s[77]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_1og5ww7zxm().b[32][0]++;
          cov_1og5ww7zxm().s[78]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_1og5ww7zxm().b[32][1]++;
          cov_1og5ww7zxm().s[79]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_1og5ww7zxm().s[80]++;
          if (!(
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[35][0]++, (_a =
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[37][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[37][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[36][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[36][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[35][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[34][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1og5ww7zxm().b[34][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[33][0]++;
            cov_1og5ww7zxm().s[81]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[82]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[83]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1og5ww7zxm().b[33][1]++;
          }
          cov_1og5ww7zxm().s[84]++;
          userId = session.user.id;
          /* istanbul ignore next */
          cov_1og5ww7zxm().s[85]++;
          return [4 /*yield*/, prisma_1.prisma.skillGapAnalysis.findMany({
            where: {
              userId: userId
            },
            orderBy: {
              createdAt: 'desc'
            }
          })];
        case 2:
          /* istanbul ignore next */
          cov_1og5ww7zxm().b[32][2]++;
          cov_1og5ww7zxm().s[86]++;
          analyses = _b.sent();
          /* istanbul ignore next */
          cov_1og5ww7zxm().s[87]++;
          analysesData = analyses.map(function (analysis) {
            /* istanbul ignore next */
            cov_1og5ww7zxm().f[16]++;
            cov_1og5ww7zxm().s[88]++;
            return {
              id: analysis.id,
              targetCareerPath: analysis.targetCareerPathName,
              status: analysis.status,
              completionPercentage: analysis.completionPercentage,
              createdAt: analysis.createdAt.toISOString(),
              lastUpdated: analysis.lastUpdated.toISOString(),
              expiresAt: analysis.expiresAt.toISOString()
            };
          });
          /* istanbul ignore next */
          cov_1og5ww7zxm().s[89]++;
          activeAnalysis = analyses.find(function (a) {
            /* istanbul ignore next */
            cov_1og5ww7zxm().f[17]++;
            cov_1og5ww7zxm().s[90]++;
            return a.status === 'ACTIVE';
          });
          /* istanbul ignore next */
          cov_1og5ww7zxm().s[91]++;
          if (activeAnalysis) {
            /* istanbul ignore next */
            cov_1og5ww7zxm().b[38][0]++;
            cov_1og5ww7zxm().s[92]++;
            progressTracking = activeAnalysis.progressTracking;
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[93]++;
            milestones =
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[39][0]++,
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[41][0]++, progressTracking === null) ||
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[41][1]++, progressTracking === void 0) ?
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[40][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[40][1]++, progressTracking.milestones)) ||
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[39][1]++, []);
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[94]++;
            completedMilestones_1 =
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[42][0]++,
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[44][0]++, progressTracking === null) ||
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[44][1]++, progressTracking === void 0) ?
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[43][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[43][1]++, progressTracking.completedMilestones)) ||
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[42][1]++, []);
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[95]++;
            skillGaps = Array.isArray(activeAnalysis.skillGaps) ?
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[45][0]++, activeAnalysis.skillGaps.length) :
            /* istanbul ignore next */
            (cov_1og5ww7zxm().b[45][1]++, 0);
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[96]++;
            nextMilestone = milestones.find(function (m) {
              /* istanbul ignore next */
              cov_1og5ww7zxm().f[18]++;
              cov_1og5ww7zxm().s[97]++;
              return !completedMilestones_1.includes(m.month);
            });
            /* istanbul ignore next */
            cov_1og5ww7zxm().s[98]++;
            activeAnalysisData = {
              id: activeAnalysis.id,
              skillGaps: skillGaps,
              completedMilestones: completedMilestones_1.length,
              totalMilestones: milestones.length,
              nextMilestone: nextMilestone ?
              /* istanbul ignore next */
              (cov_1og5ww7zxm().b[46][0]++, {
                skills:
                /* istanbul ignore next */
                (cov_1og5ww7zxm().b[47][0]++, nextMilestone.skills) ||
                /* istanbul ignore next */
                (cov_1og5ww7zxm().b[47][1]++, []),
                dueDate: calculateMilestoneDueDate(activeAnalysis.createdAt, nextMilestone.month)
              }) :
              /* istanbul ignore next */
              (cov_1og5ww7zxm().b[46][1]++, {
                skills: [],
                dueDate: new Date().toISOString()
              })
            };
          } else
          /* istanbul ignore next */
          {
            cov_1og5ww7zxm().b[38][1]++;
          }
          cov_1og5ww7zxm().s[99]++;
          responseData = {
            success: true,
            data: {
              analyses: analysesData,
              activeAnalysis: activeAnalysisData
            }
          };
          /* istanbul ignore next */
          cov_1og5ww7zxm().s[100]++;
          return [2 /*return*/, server_1.NextResponse.json(responseData)];
      }
    });
  });
}
function calculateMilestoneDueDate(startDate, milestoneMonth) {
  /* istanbul ignore next */
  cov_1og5ww7zxm().f[19]++;
  var dueDate =
  /* istanbul ignore next */
  (cov_1og5ww7zxm().s[101]++, new Date(startDate));
  /* istanbul ignore next */
  cov_1og5ww7zxm().s[102]++;
  dueDate.setMonth(dueDate.getMonth() + milestoneMonth);
  /* istanbul ignore next */
  cov_1og5ww7zxm().s[103]++;
  return dueDate.toISOString();
}
/* istanbul ignore next */
cov_1og5ww7zxm().s[104]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1og5ww7zxm().f[20]++;
  cov_1og5ww7zxm().s[105]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1og5ww7zxm().f[21]++;
    cov_1og5ww7zxm().s[106]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1og5ww7zxm().f[22]++;
      cov_1og5ww7zxm().s[107]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 60
      },
      // 60 requests per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_1og5ww7zxm().f[23]++;
        cov_1og5ww7zxm().s[108]++;
        return handleGetUserGapAnalyses(request);
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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