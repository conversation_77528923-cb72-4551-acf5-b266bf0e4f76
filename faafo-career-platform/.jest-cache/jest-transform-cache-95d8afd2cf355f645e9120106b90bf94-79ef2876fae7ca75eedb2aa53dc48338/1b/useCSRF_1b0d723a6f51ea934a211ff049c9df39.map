{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/__mocks__/useCSRF.ts", "mappings": ";;;;;;;;;;;;;;AAAA,+BAA+B;AAC/B,IAAM,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,UAAC,iBAAsB;IAAtB,kCAAA,EAAA,sBAAsB;IAAK,OAAA,YACzD,cAAc,EAAE,kBAAkB,EAClC,cAAc,EAAE,iBAAiB,IAC9B,iBAAiB,EACpB;AAJyD,CAIzD,CAAC,CAAC;AAUK,wCAAc;AARV,QAAA,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;IACpC,SAAS,EAAE,iBAAiB;IAC5B,SAAS,EAAE,KAAK;IAChB,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,cAAc;CAC3B,CAAC,EALmC,CAKnC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/__mocks__/useCSRF.ts"], "sourcesContent": ["// Manual mock for useCSRF hook\nconst mockGetHeaders = jest.fn((additionalHeaders = {}) => ({\n  'Content-Type': 'application/json',\n  'X-CSRF-Token': 'test-csrf-token',\n  ...additionalHeaders,\n}));\n\nexport const useCSRF = jest.fn(() => ({\n  csrfToken: 'test-csrf-token',\n  isLoading: false,\n  error: null,\n  getHeaders: mockGetHeaders,\n}));\n\n// Export the mock function for test access\nexport { mockGetHeaders };\n"], "version": 3}