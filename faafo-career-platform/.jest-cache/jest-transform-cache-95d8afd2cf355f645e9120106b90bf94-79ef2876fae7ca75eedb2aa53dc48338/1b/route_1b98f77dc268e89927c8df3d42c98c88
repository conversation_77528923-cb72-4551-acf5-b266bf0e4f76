92cf83b37ea5d4258fa78b58d830cc2b
"use strict";

/* istanbul ignore next */
function cov_oijs2ccho() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/verify-email/route.ts";
  var hash = "80677b1244234e42f749ba79cce9f09bfbd1458c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/verify-email/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 36
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 15
        },
        end: {
          line: 44,
          column: 55
        }
      },
      "73": {
        start: {
          line: 45,
          column: 34
        },
        end: {
          line: 45,
          column: 76
        }
      },
      "74": {
        start: {
          line: 46,
          column: 30
        },
        end: {
          line: 46,
          column: 68
        }
      },
      "75": {
        start: {
          line: 48,
          column: 24
        },
        end: {
          line: 48,
          column: 26
        }
      },
      "76": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 54,
          column: 7
        }
      },
      "77": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 53,
          column: 11
        }
      },
      "78": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 118
        }
      },
      "79": {
        start: {
          line: 52,
          column: 67
        },
        end: {
          line: 52,
          column: 113
        }
      },
      "80": {
        start: {
          line: 56,
          column: 0
        },
        end: {
          line: 166,
          column: 7
        }
      },
      "81": {
        start: {
          line: 56,
          column: 94
        },
        end: {
          line: 166,
          column: 3
        }
      },
      "82": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 165,
          column: 7
        }
      },
      "83": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 164,
          column: 9
        }
      },
      "84": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 112
        }
      },
      "85": {
        start: {
          line: 62,
          column: 16
        },
        end: {
          line: 62,
          column: 44
        }
      },
      "86": {
        start: {
          line: 63,
          column: 16
        },
        end: {
          line: 68,
          column: 17
        }
      },
      "87": {
        start: {
          line: 64,
          column: 20
        },
        end: {
          line: 64,
          column: 103
        }
      },
      "88": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 65,
          column: 43
        }
      },
      "89": {
        start: {
          line: 66,
          column: 20
        },
        end: {
          line: 66,
          column: 60
        }
      },
      "90": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 67,
          column: 32
        }
      },
      "91": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 39
        }
      },
      "92": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 70,
          column: 29
        }
      },
      "93": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 72,
          column: 44
        }
      },
      "94": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 73,
          column: 53
        }
      },
      "95": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 75,
          column: 67
        }
      },
      "96": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 76,
          column: 65
        }
      },
      "97": {
        start: {
          line: 76,
          column: 41
        },
        end: {
          line: 76,
          column: 65
        }
      },
      "98": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 54
        }
      },
      "99": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 79,
          column: 26
        }
      },
      "100": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 80,
          column: 65
        }
      },
      "101": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 85,
          column: 20
        }
      },
      "102": {
        start: {
          line: 87,
          column: 16
        },
        end: {
          line: 87,
          column: 46
        }
      },
      "103": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 53
        }
      },
      "104": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 80
        }
      },
      "105": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 90,
          column: 69
        }
      },
      "106": {
        start: {
          line: 90,
          column: 45
        },
        end: {
          line: 90,
          column: 69
        }
      },
      "107": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 120
        }
      },
      "108": {
        start: {
          line: 91,
          column: 70
        },
        end: {
          line: 91,
          column: 115
        }
      },
      "109": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 93,
          column: 26
        }
      },
      "110": {
        start: {
          line: 94,
          column: 16
        },
        end: {
          line: 94,
          column: 29
        }
      },
      "111": {
        start: {
          line: 96,
          column: 16
        },
        end: {
          line: 98,
          column: 17
        }
      },
      "112": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 97,
          column: 67
        }
      },
      "113": {
        start: {
          line: 99,
          column: 16
        },
        end: {
          line: 99,
          column: 88
        }
      },
      "114": {
        start: {
          line: 99,
          column: 63
        },
        end: {
          line: 99,
          column: 88
        }
      },
      "115": {
        start: {
          line: 101,
          column: 16
        },
        end: {
          line: 103,
          column: 24
        }
      },
      "116": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 106,
          column: 26
        }
      },
      "117": {
        start: {
          line: 107,
          column: 16
        },
        end: {
          line: 107,
          column: 67
        }
      },
      "118": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 112,
          column: 17
        }
      },
      "119": {
        start: {
          line: 111,
          column: 20
        },
        end: {
          line: 111,
          column: 67
        }
      },
      "120": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 115,
          column: 24
        }
      },
      "121": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 33
        }
      },
      "122": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 120,
          column: 17
        }
      },
      "123": {
        start: {
          line: 119,
          column: 20
        },
        end: {
          line: 119,
          column: 55
        }
      },
      "124": {
        start: {
          line: 121,
          column: 16
        },
        end: {
          line: 121,
          column: 66
        }
      },
      "125": {
        start: {
          line: 121,
          column: 41
        },
        end: {
          line: 121,
          column: 66
        }
      },
      "126": {
        start: {
          line: 123,
          column: 16
        },
        end: {
          line: 125,
          column: 24
        }
      },
      "127": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 128,
          column: 26
        }
      },
      "128": {
        start: {
          line: 129,
          column: 16
        },
        end: {
          line: 132,
          column: 24
        }
      },
      "129": {
        start: {
          line: 135,
          column: 12
        },
        end: {
          line: 145,
          column: 20
        }
      },
      "130": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 148,
          column: 26
        }
      },
      "131": {
        start: {
          line: 149,
          column: 16
        },
        end: {
          line: 152,
          column: 24
        }
      },
      "132": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 36
        }
      },
      "133": {
        start: {
          line: 155,
          column: 16
        },
        end: {
          line: 155,
          column: 53
        }
      },
      "134": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 156,
          column: 80
        }
      },
      "135": {
        start: {
          line: 157,
          column: 16
        },
        end: {
          line: 157,
          column: 70
        }
      },
      "136": {
        start: {
          line: 157,
          column: 45
        },
        end: {
          line: 157,
          column: 70
        }
      },
      "137": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 158,
          column: 120
        }
      },
      "138": {
        start: {
          line: 158,
          column: 70
        },
        end: {
          line: 158,
          column: 115
        }
      },
      "139": {
        start: {
          line: 160,
          column: 16
        },
        end: {
          line: 160,
          column: 26
        }
      },
      "140": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 161,
          column: 30
        }
      },
      "141": {
        start: {
          line: 162,
          column: 21
        },
        end: {
          line: 162,
          column: 35
        }
      },
      "142": {
        start: {
          line: 163,
          column: 21
        },
        end: {
          line: 163,
          column: 43
        }
      },
      "143": {
        start: {
          line: 167,
          column: 0
        },
        end: {
          line: 247,
          column: 7
        }
      },
      "144": {
        start: {
          line: 167,
          column: 93
        },
        end: {
          line: 247,
          column: 3
        }
      },
      "145": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 246,
          column: 7
        }
      },
      "146": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 245,
          column: 9
        }
      },
      "147": {
        start: {
          line: 171,
          column: 20
        },
        end: {
          line: 171,
          column: 112
        }
      },
      "148": {
        start: {
          line: 173,
          column: 16
        },
        end: {
          line: 173,
          column: 44
        }
      },
      "149": {
        start: {
          line: 174,
          column: 16
        },
        end: {
          line: 179,
          column: 17
        }
      },
      "150": {
        start: {
          line: 175,
          column: 20
        },
        end: {
          line: 175,
          column: 109
        }
      },
      "151": {
        start: {
          line: 176,
          column: 20
        },
        end: {
          line: 176,
          column: 43
        }
      },
      "152": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 177,
          column: 60
        }
      },
      "153": {
        start: {
          line: 178,
          column: 20
        },
        end: {
          line: 178,
          column: 32
        }
      },
      "154": {
        start: {
          line: 180,
          column: 16
        },
        end: {
          line: 180,
          column: 39
        }
      },
      "155": {
        start: {
          line: 181,
          column: 16
        },
        end: {
          line: 181,
          column: 29
        }
      },
      "156": {
        start: {
          line: 183,
          column: 16
        },
        end: {
          line: 183,
          column: 43
        }
      },
      "157": {
        start: {
          line: 184,
          column: 16
        },
        end: {
          line: 184,
          column: 65
        }
      },
      "158": {
        start: {
          line: 185,
          column: 16
        },
        end: {
          line: 185,
          column: 50
        }
      },
      "159": {
        start: {
          line: 186,
          column: 16
        },
        end: {
          line: 186,
          column: 50
        }
      },
      "160": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 187,
          column: 65
        }
      },
      "161": {
        start: {
          line: 187,
          column: 41
        },
        end: {
          line: 187,
          column: 65
        }
      },
      "162": {
        start: {
          line: 188,
          column: 16
        },
        end: {
          line: 188,
          column: 54
        }
      },
      "163": {
        start: {
          line: 190,
          column: 16
        },
        end: {
          line: 190,
          column: 26
        }
      },
      "164": {
        start: {
          line: 191,
          column: 16
        },
        end: {
          line: 191,
          column: 65
        }
      },
      "165": {
        start: {
          line: 192,
          column: 20
        },
        end: {
          line: 196,
          column: 20
        }
      },
      "166": {
        start: {
          line: 198,
          column: 16
        },
        end: {
          line: 198,
          column: 46
        }
      },
      "167": {
        start: {
          line: 199,
          column: 16
        },
        end: {
          line: 199,
          column: 53
        }
      },
      "168": {
        start: {
          line: 200,
          column: 16
        },
        end: {
          line: 200,
          column: 80
        }
      },
      "169": {
        start: {
          line: 201,
          column: 16
        },
        end: {
          line: 201,
          column: 69
        }
      },
      "170": {
        start: {
          line: 201,
          column: 45
        },
        end: {
          line: 201,
          column: 69
        }
      },
      "171": {
        start: {
          line: 202,
          column: 16
        },
        end: {
          line: 202,
          column: 120
        }
      },
      "172": {
        start: {
          line: 202,
          column: 70
        },
        end: {
          line: 202,
          column: 115
        }
      },
      "173": {
        start: {
          line: 204,
          column: 16
        },
        end: {
          line: 204,
          column: 26
        }
      },
      "174": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 205,
          column: 29
        }
      },
      "175": {
        start: {
          line: 207,
          column: 16
        },
        end: {
          line: 209,
          column: 17
        }
      },
      "176": {
        start: {
          line: 208,
          column: 20
        },
        end: {
          line: 208,
          column: 67
        }
      },
      "177": {
        start: {
          line: 211,
          column: 16
        },
        end: {
          line: 213,
          column: 17
        }
      },
      "178": {
        start: {
          line: 212,
          column: 20
        },
        end: {
          line: 212,
          column: 71
        }
      },
      "179": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 217,
          column: 17
        }
      },
      "180": {
        start: {
          line: 216,
          column: 20
        },
        end: {
          line: 216,
          column: 67
        }
      },
      "181": {
        start: {
          line: 218,
          column: 16
        },
        end: {
          line: 220,
          column: 24
        }
      },
      "182": {
        start: {
          line: 222,
          column: 16
        },
        end: {
          line: 222,
          column: 33
        }
      },
      "183": {
        start: {
          line: 223,
          column: 16
        },
        end: {
          line: 225,
          column: 17
        }
      },
      "184": {
        start: {
          line: 224,
          column: 20
        },
        end: {
          line: 224,
          column: 55
        }
      },
      "185": {
        start: {
          line: 226,
          column: 16
        },
        end: {
          line: 233,
          column: 24
        }
      },
      "186": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 235,
          column: 36
        }
      },
      "187": {
        start: {
          line: 236,
          column: 16
        },
        end: {
          line: 236,
          column: 53
        }
      },
      "188": {
        start: {
          line: 237,
          column: 16
        },
        end: {
          line: 237,
          column: 80
        }
      },
      "189": {
        start: {
          line: 238,
          column: 16
        },
        end: {
          line: 238,
          column: 70
        }
      },
      "190": {
        start: {
          line: 238,
          column: 45
        },
        end: {
          line: 238,
          column: 70
        }
      },
      "191": {
        start: {
          line: 239,
          column: 16
        },
        end: {
          line: 239,
          column: 120
        }
      },
      "192": {
        start: {
          line: 239,
          column: 70
        },
        end: {
          line: 239,
          column: 115
        }
      },
      "193": {
        start: {
          line: 241,
          column: 16
        },
        end: {
          line: 241,
          column: 26
        }
      },
      "194": {
        start: {
          line: 242,
          column: 16
        },
        end: {
          line: 242,
          column: 30
        }
      },
      "195": {
        start: {
          line: 243,
          column: 21
        },
        end: {
          line: 243,
          column: 35
        }
      },
      "196": {
        start: {
          line: 244,
          column: 21
        },
        end: {
          line: 244,
          column: 43
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "securityDelay",
        decl: {
          start: {
            line: 49,
            column: 9
          },
          end: {
            line: 49,
            column: 22
          }
        },
        loc: {
          start: {
            line: 49,
            column: 25
          },
          end: {
            line: 55,
            column: 1
          }
        },
        line: 49
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 50,
            column: 44
          },
          end: {
            line: 50,
            column: 45
          }
        },
        loc: {
          start: {
            line: 50,
            column: 56
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 50
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 51,
            column: 33
          },
          end: {
            line: 51,
            column: 34
          }
        },
        loc: {
          start: {
            line: 51,
            column: 47
          },
          end: {
            line: 53,
            column: 9
          }
        },
        line: 51
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 52,
            column: 46
          },
          end: {
            line: 52,
            column: 47
          }
        },
        loc: {
          start: {
            line: 52,
            column: 65
          },
          end: {
            line: 52,
            column: 115
          }
        },
        line: 52
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 56,
            column: 73
          },
          end: {
            line: 56,
            column: 74
          }
        },
        loc: {
          start: {
            line: 56,
            column: 92
          },
          end: {
            line: 166,
            column: 5
          }
        },
        line: 56
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 56,
            column: 136
          },
          end: {
            line: 56,
            column: 137
          }
        },
        loc: {
          start: {
            line: 56,
            column: 148
          },
          end: {
            line: 166,
            column: 1
          }
        },
        line: 56
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 58,
            column: 29
          },
          end: {
            line: 58,
            column: 30
          }
        },
        loc: {
          start: {
            line: 58,
            column: 43
          },
          end: {
            line: 165,
            column: 5
          }
        },
        line: 58
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 91,
            column: 49
          },
          end: {
            line: 91,
            column: 50
          }
        },
        loc: {
          start: {
            line: 91,
            column: 68
          },
          end: {
            line: 91,
            column: 117
          }
        },
        line: 91
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 158,
            column: 49
          },
          end: {
            line: 158,
            column: 50
          }
        },
        loc: {
          start: {
            line: 158,
            column: 68
          },
          end: {
            line: 158,
            column: 117
          }
        },
        line: 158
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 167,
            column: 72
          },
          end: {
            line: 167,
            column: 73
          }
        },
        loc: {
          start: {
            line: 167,
            column: 91
          },
          end: {
            line: 247,
            column: 5
          }
        },
        line: 167
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 167,
            column: 135
          },
          end: {
            line: 167,
            column: 136
          }
        },
        loc: {
          start: {
            line: 167,
            column: 147
          },
          end: {
            line: 247,
            column: 1
          }
        },
        line: 167
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 169,
            column: 29
          },
          end: {
            line: 169,
            column: 30
          }
        },
        loc: {
          start: {
            line: 169,
            column: 43
          },
          end: {
            line: 246,
            column: 5
          }
        },
        line: 169
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 202,
            column: 49
          },
          end: {
            line: 202,
            column: 50
          }
        },
        loc: {
          start: {
            line: 202,
            column: 68
          },
          end: {
            line: 202,
            column: 117
          }
        },
        line: 202
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 239,
            column: 49
          },
          end: {
            line: 239,
            column: 50
          }
        },
        loc: {
          start: {
            line: 239,
            column: 68
          },
          end: {
            line: 239,
            column: 117
          }
        },
        line: 239
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 164,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 60,
            column: 12
          },
          end: {
            line: 60,
            column: 112
          }
        }, {
          start: {
            line: 61,
            column: 12
          },
          end: {
            line: 70,
            column: 29
          }
        }, {
          start: {
            line: 71,
            column: 12
          },
          end: {
            line: 73,
            column: 53
          }
        }, {
          start: {
            line: 74,
            column: 12
          },
          end: {
            line: 77,
            column: 54
          }
        }, {
          start: {
            line: 78,
            column: 12
          },
          end: {
            line: 80,
            column: 65
          }
        }, {
          start: {
            line: 81,
            column: 12
          },
          end: {
            line: 85,
            column: 20
          }
        }, {
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 91,
            column: 120
          }
        }, {
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 94,
            column: 29
          }
        }, {
          start: {
            line: 95,
            column: 12
          },
          end: {
            line: 103,
            column: 24
          }
        }, {
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 107,
            column: 67
          }
        }, {
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 115,
            column: 24
          }
        }, {
          start: {
            line: 116,
            column: 12
          },
          end: {
            line: 125,
            column: 24
          }
        }, {
          start: {
            line: 126,
            column: 12
          },
          end: {
            line: 132,
            column: 24
          }
        }, {
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 145,
            column: 20
          }
        }, {
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 152,
            column: 24
          }
        }, {
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 158,
            column: 120
          }
        }, {
          start: {
            line: 159,
            column: 12
          },
          end: {
            line: 161,
            column: 30
          }
        }, {
          start: {
            line: 162,
            column: 12
          },
          end: {
            line: 162,
            column: 35
          }
        }, {
          start: {
            line: 163,
            column: 12
          },
          end: {
            line: 163,
            column: 43
          }
        }],
        line: 59
      },
      "36": {
        loc: {
          start: {
            line: 63,
            column: 16
          },
          end: {
            line: 68,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 16
          },
          end: {
            line: 68,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "37": {
        loc: {
          start: {
            line: 76,
            column: 16
          },
          end: {
            line: 76,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 76,
            column: 16
          },
          end: {
            line: 76,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 76
      },
      "38": {
        loc: {
          start: {
            line: 76,
            column: 22
          },
          end: {
            line: 76,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 76,
            column: 22
          },
          end: {
            line: 76,
            column: 28
          }
        }, {
          start: {
            line: 76,
            column: 32
          },
          end: {
            line: 76,
            column: 38
          }
        }],
        line: 76
      },
      "39": {
        loc: {
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 90,
            column: 69
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 90,
            column: 69
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 90
      },
      "40": {
        loc: {
          start: {
            line: 96,
            column: 16
          },
          end: {
            line: 98,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 16
          },
          end: {
            line: 98,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "41": {
        loc: {
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 99,
            column: 88
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 99,
            column: 88
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 99
      },
      "42": {
        loc: {
          start: {
            line: 110,
            column: 16
          },
          end: {
            line: 112,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 16
          },
          end: {
            line: 112,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "43": {
        loc: {
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 120,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 120,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "44": {
        loc: {
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 121,
            column: 66
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 121,
            column: 66
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "45": {
        loc: {
          start: {
            line: 157,
            column: 16
          },
          end: {
            line: 157,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 16
          },
          end: {
            line: 157,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 157
      },
      "46": {
        loc: {
          start: {
            line: 170,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 171,
            column: 112
          }
        }, {
          start: {
            line: 172,
            column: 12
          },
          end: {
            line: 181,
            column: 29
          }
        }, {
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 188,
            column: 54
          }
        }, {
          start: {
            line: 189,
            column: 12
          },
          end: {
            line: 191,
            column: 65
          }
        }, {
          start: {
            line: 192,
            column: 12
          },
          end: {
            line: 196,
            column: 20
          }
        }, {
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 202,
            column: 120
          }
        }, {
          start: {
            line: 203,
            column: 12
          },
          end: {
            line: 205,
            column: 29
          }
        }, {
          start: {
            line: 206,
            column: 12
          },
          end: {
            line: 220,
            column: 24
          }
        }, {
          start: {
            line: 221,
            column: 12
          },
          end: {
            line: 233,
            column: 24
          }
        }, {
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 239,
            column: 120
          }
        }, {
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 242,
            column: 30
          }
        }, {
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 243,
            column: 35
          }
        }, {
          start: {
            line: 244,
            column: 12
          },
          end: {
            line: 244,
            column: 43
          }
        }],
        line: 170
      },
      "47": {
        loc: {
          start: {
            line: 174,
            column: 16
          },
          end: {
            line: 179,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 16
          },
          end: {
            line: 179,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "48": {
        loc: {
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 187,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 187,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 187
      },
      "49": {
        loc: {
          start: {
            line: 187,
            column: 22
          },
          end: {
            line: 187,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 187,
            column: 22
          },
          end: {
            line: 187,
            column: 28
          }
        }, {
          start: {
            line: 187,
            column: 32
          },
          end: {
            line: 187,
            column: 38
          }
        }],
        line: 187
      },
      "50": {
        loc: {
          start: {
            line: 201,
            column: 16
          },
          end: {
            line: 201,
            column: 69
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 16
          },
          end: {
            line: 201,
            column: 69
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "51": {
        loc: {
          start: {
            line: 207,
            column: 16
          },
          end: {
            line: 209,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 16
          },
          end: {
            line: 209,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "52": {
        loc: {
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 213,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 213,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "53": {
        loc: {
          start: {
            line: 215,
            column: 16
          },
          end: {
            line: 217,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 16
          },
          end: {
            line: 217,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 215
      },
      "54": {
        loc: {
          start: {
            line: 223,
            column: 16
          },
          end: {
            line: 225,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 16
          },
          end: {
            line: 225,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "55": {
        loc: {
          start: {
            line: 238,
            column: 16
          },
          end: {
            line: 238,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 16
          },
          end: {
            line: 238,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/verify-email/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,wDAAkC;AAClC,6EAAwF;AACxF,qEAAmE;AAEnE,gEAAgE;AAChE,IAAM,iBAAiB,GAAG,EAAE,CAAC;AAE7B,SAAe,aAAa;mCAAI,OAAO;;YACrC,sBAAO,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAtC,CAAsC,CAAC,EAAC;;;CACvE;AAEY,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;oBAExD,qBAAM,4CAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAA;;gBAArE,eAAe,GAAG,SAAmD;gBAE3E,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;oBACvB,KAAK,GAAG,IAAI,KAAK,CAAC,+DAA+D,CAAQ,CAAC;oBAChG,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;oBACxC,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEK,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;gBAGF,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAAvC,KAAmB,SAAoB,EAArC,KAAK,WAAA,EAAE,KAAK,WAAA;qBAEhB,CAAA,CAAC,KAAK,IAAI,CAAC,KAAK,CAAA,EAAhB,wBAAgB;gBAClB,qBAAM,aAAa,EAAE,EAAA;;gBAArB,SAAqB,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;oBAIzB,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;oBAClE,KAAK,EAAE;wBACL,KAAK,EAAE,KAAK;qBACb;iBACF,CAAC,EAAA;;gBAJI,iBAAiB,GAAG,SAIxB;gBAGI,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACrC,mBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,WAAW,CAAC,CAAC;qBAChE,CAAA,gBAAc,GAAG,CAAC,CAAA,EAAlB,wBAAkB;gBACpB,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,gBAAc,CAAC,EAAnC,CAAmC,CAAC,EAAA;;gBAAjE,SAAiE,CAAC;;;gBAGpE,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBACjD,CAAC;qBAGG,CAAA,iBAAiB,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAA,EAAtC,yBAAsC;gBACxC,yBAAyB;gBACzB,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;wBACpC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACxB,CAAC,EAAA;;gBAHF,yBAAyB;gBACzB,SAEE,CAAC;gBACH,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;;gBAGrD,6BAA6B;gBAC7B,IAAI,iBAAiB,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;oBAC3C,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBACjD,CAAC;gBAGY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACxB,CAAC,EAAA;;gBAFI,IAAI,GAAG,SAEX;gBAEF,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;gBACrC,CAAC;qBAGG,IAAI,CAAC,aAAa,EAAlB,yBAAkB;gBACpB,qBAAqB;gBACrB,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;wBACpC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACxB,CAAC,EAAA;;gBAHF,qBAAqB;gBACrB,SAEE,CAAC;gBACH,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE;qBAChD,CAAC,EAAC;;YAGL,6CAA6C;YAC7C,qBAAM,gBAAM,CAAC,YAAY,CAAC;oBACxB,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;wBACjB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;wBACtB,IAAI,EAAE;4BACJ,aAAa,EAAE,IAAI,IAAI,EAAE;yBAC1B;qBACF,CAAC;oBACF,gBAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;wBAC9B,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACxB,CAAC;iBACH,CAAC,EAAA;;gBAXF,6CAA6C;gBAC7C,SAUE,CAAC;gBAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE;qBAClD,CAAC,EAAC;;;gBAIG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACrC,mBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,WAAW,CAAC,CAAC;qBAChE,CAAA,gBAAc,GAAG,CAAC,CAAA,EAAlB,yBAAkB;gBACpB,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,gBAAc,CAAC,EAAnC,CAAmC,CAAC,EAAA;;gBAAjE,SAAiE,CAAC;;qBAEpE,MAAM,OAAK,CAAC;;;;KAEf,CAAC,CAAC;AAEU,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;oBAEvD,qBAAM,4CAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAA;;gBAArE,eAAe,GAAG,SAAmD;gBAE3E,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;oBACvB,KAAK,GAAG,IAAI,KAAK,CAAC,qEAAqE,CAAQ,CAAC;oBACtG,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;oBACxC,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEK,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;gBAGnB,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAClC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;qBAEpC,CAAA,CAAC,KAAK,IAAI,CAAC,KAAK,CAAA,EAAhB,wBAAgB;gBAClB,qBAAM,aAAa,EAAE,EAAA;;gBAArB,SAAqB,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;oBAIzB,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;oBAClE,KAAK,EAAE;wBACL,KAAK,EAAE,KAAK;qBACb;iBACF,CAAC,EAAA;;gBAJI,iBAAiB,GAAG,SAIxB;gBAGI,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACrC,mBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,WAAW,CAAC,CAAC;qBAChE,CAAA,gBAAc,GAAG,CAAC,CAAA,EAAlB,wBAAkB;gBACpB,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,gBAAc,CAAC,EAAnC,CAAmC,CAAC,EAAA;;gBAAjE,SAAiE,CAAC;;;gBAGpE,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBACjD,CAAC;gBAED,6BAA6B;gBAC7B,IAAI,iBAAiB,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;oBAC3C,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBACrD,CAAC;gBAED,6BAA6B;gBAC7B,IAAI,iBAAiB,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;oBAC3C,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBACjD,CAAC;gBAGY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACxB,CAAC,EAAA;;gBAFI,IAAI,GAAG,SAEX;gBAEF,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;gBACrC,CAAC;gBAED,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,KAAK,EAAE,IAAI;4BACX,KAAK,EAAE,KAAK;4BACZ,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa;yBACtC;qBACF,CAAC,EAAC;;;gBAIG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACrC,mBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,WAAW,CAAC,CAAC;qBAChE,CAAA,gBAAc,GAAG,CAAC,CAAA,EAAlB,yBAAkB;gBACpB,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,gBAAc,CAAC,EAAnC,CAAmC,CAAC,EAAA;;gBAAjE,SAAiE,CAAC;;qBAEpE,MAAM,OAAK,CAAC;;;;KAEf,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/verify-email/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';\n\n// Timing attack protection - consistent delay for all responses\nconst SECURITY_DELAY_MS = 50;\n\nasync function securityDelay(): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, SECURITY_DELAY_MS));\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> => {\n  // SECURITY FIX: Apply strict rate limiting for email verification attempts\n  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);\n\n  if (!rateLimitResult.allowed) {\n    const error = new Error('Too many email verification attempts. Please try again later.') as any;\n    error.statusCode = 429;\n    error.headers = rateLimitResult.headers;\n    throw error;\n  }\n\n  const startTime = Date.now();\n\n  try {\n    const { token, email } = await request.json();\n\n    if (!token || !email) {\n      await securityDelay();\n      throw new Error('Token and email are required.');\n    }\n\n    // SECURITY FIX: Always perform database lookup to prevent timing attacks\n    const verificationToken = await prisma.verificationToken.findUnique({\n      where: {\n        token: token,\n      },\n    });\n\n    // SECURITY FIX: Ensure consistent response time regardless of token validity\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n\n    if (!verificationToken) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Check if token has expired\n    if (verificationToken.expires < new Date()) {\n      // Clean up expired token\n      await prisma.verificationToken.delete({\n        where: { token: token },\n      });\n      throw new Error('Verification token has expired.');\n    }\n\n    // Check if the email matches\n    if (verificationToken.identifier !== email) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Find the user\n    const user = await prisma.user.findUnique({\n      where: { email: email },\n    });\n\n    if (!user) {\n      throw new Error('User not found.');\n    }\n\n    // Check if user is already verified\n    if (user.emailVerified) {\n      // Clean up the token\n      await prisma.verificationToken.delete({\n        where: { token: token },\n      });\n      return NextResponse.json({\n        success: true,\n        data: { message: 'Email is already verified.' }\n      });\n    }\n\n    // Update user as verified and clean up token\n    await prisma.$transaction([\n      prisma.user.update({\n        where: { id: user.id },\n        data: {\n          emailVerified: new Date(),\n        },\n      }),\n      prisma.verificationToken.delete({\n        where: { token: token },\n      }),\n    ]);\n\n    return NextResponse.json({\n      success: true,\n      data: { message: 'Email verified successfully.' }\n    });\n\n  } catch (error) {\n    // SECURITY FIX: Ensure consistent timing even for errors\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n    throw error;\n  }\n});\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ valid: boolean; email: string; alreadyVerified: boolean }>>> => {\n  // SECURITY FIX: Apply rate limiting to GET method as well\n  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);\n\n  if (!rateLimitResult.allowed) {\n    const error = new Error('Too many email verification check attempts. Please try again later.') as any;\n    error.statusCode = 429;\n    error.headers = rateLimitResult.headers;\n    throw error;\n  }\n\n  const startTime = Date.now();\n\n  try {\n    const { searchParams } = new URL(request.url);\n    const token = searchParams.get('token');\n    const email = searchParams.get('email');\n\n    if (!token || !email) {\n      await securityDelay();\n      throw new Error('Token and email are required.');\n    }\n\n    // SECURITY FIX: Always perform database lookup to prevent timing attacks\n    const verificationToken = await prisma.verificationToken.findUnique({\n      where: {\n        token: token,\n      },\n    });\n\n    // SECURITY FIX: Ensure consistent response time\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n\n    if (!verificationToken) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Check if token has expired\n    if (verificationToken.expires < new Date()) {\n      throw new Error('Verification token has expired.');\n    }\n\n    // Check if the email matches\n    if (verificationToken.identifier !== email) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Find the user\n    const user = await prisma.user.findUnique({\n      where: { email: email },\n    });\n\n    if (!user) {\n      throw new Error('User not found.');\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        valid: true,\n        email: email,\n        alreadyVerified: !!user.emailVerified\n      }\n    });\n\n  } catch (error) {\n    // SECURITY FIX: Ensure consistent timing even for errors\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n    throw error;\n  }\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "80677b1244234e42f749ba79cce9f09bfbd1458c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_oijs2ccho = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_oijs2ccho();
var __awaiter =
/* istanbul ignore next */
(cov_oijs2ccho().s[0]++,
/* istanbul ignore next */
(cov_oijs2ccho().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_oijs2ccho().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_oijs2ccho().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_oijs2ccho().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_oijs2ccho().f[1]++;
    cov_oijs2ccho().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_oijs2ccho().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_oijs2ccho().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[2]++;
      cov_oijs2ccho().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_oijs2ccho().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_oijs2ccho().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_oijs2ccho().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_oijs2ccho().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[4]++;
      cov_oijs2ccho().s[4]++;
      try {
        /* istanbul ignore next */
        cov_oijs2ccho().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_oijs2ccho().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[5]++;
      cov_oijs2ccho().s[7]++;
      try {
        /* istanbul ignore next */
        cov_oijs2ccho().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_oijs2ccho().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[6]++;
      cov_oijs2ccho().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_oijs2ccho().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_oijs2ccho().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_oijs2ccho().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_oijs2ccho().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_oijs2ccho().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_oijs2ccho().s[12]++,
/* istanbul ignore next */
(cov_oijs2ccho().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_oijs2ccho().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_oijs2ccho().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_oijs2ccho().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_oijs2ccho().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_oijs2ccho().f[8]++;
        cov_oijs2ccho().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_oijs2ccho().b[6][0]++;
          cov_oijs2ccho().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_oijs2ccho().b[6][1]++;
        }
        cov_oijs2ccho().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_oijs2ccho().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_oijs2ccho().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_oijs2ccho().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_oijs2ccho().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_oijs2ccho().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_oijs2ccho().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_oijs2ccho().f[9]++;
    cov_oijs2ccho().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_oijs2ccho().f[10]++;
    cov_oijs2ccho().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[11]++;
      cov_oijs2ccho().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_oijs2ccho().f[12]++;
    cov_oijs2ccho().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_oijs2ccho().b[9][0]++;
      cov_oijs2ccho().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_oijs2ccho().b[9][1]++;
    }
    cov_oijs2ccho().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_oijs2ccho().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_oijs2ccho().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_oijs2ccho().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_oijs2ccho().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_oijs2ccho().s[25]++;
      try {
        /* istanbul ignore next */
        cov_oijs2ccho().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_oijs2ccho().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_oijs2ccho().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_oijs2ccho().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_oijs2ccho().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_oijs2ccho().b[15][0]++,
        /* istanbul ignore next */
        (cov_oijs2ccho().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_oijs2ccho().b[16][1]++,
        /* istanbul ignore next */
        (cov_oijs2ccho().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_oijs2ccho().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_oijs2ccho().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_oijs2ccho().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_oijs2ccho().b[12][0]++;
          cov_oijs2ccho().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_oijs2ccho().b[12][1]++;
        }
        cov_oijs2ccho().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_oijs2ccho().b[18][0]++;
          cov_oijs2ccho().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_oijs2ccho().b[18][1]++;
        }
        cov_oijs2ccho().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_oijs2ccho().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_oijs2ccho().b[19][1]++;
            cov_oijs2ccho().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_oijs2ccho().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_oijs2ccho().b[19][2]++;
            cov_oijs2ccho().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_oijs2ccho().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_oijs2ccho().b[19][3]++;
            cov_oijs2ccho().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_oijs2ccho().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_oijs2ccho().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_oijs2ccho().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_oijs2ccho().b[19][4]++;
            cov_oijs2ccho().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_oijs2ccho().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_oijs2ccho().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_oijs2ccho().b[19][5]++;
            cov_oijs2ccho().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_oijs2ccho().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_oijs2ccho().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_oijs2ccho().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_oijs2ccho().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_oijs2ccho().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_oijs2ccho().b[20][0]++;
              cov_oijs2ccho().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_oijs2ccho().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_oijs2ccho().b[20][1]++;
            }
            cov_oijs2ccho().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_oijs2ccho().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_oijs2ccho().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_oijs2ccho().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_oijs2ccho().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_oijs2ccho().b[23][0]++;
              cov_oijs2ccho().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_oijs2ccho().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_oijs2ccho().b[23][1]++;
            }
            cov_oijs2ccho().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_oijs2ccho().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_oijs2ccho().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_oijs2ccho().b[25][0]++;
              cov_oijs2ccho().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_oijs2ccho().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_oijs2ccho().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_oijs2ccho().b[25][1]++;
            }
            cov_oijs2ccho().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_oijs2ccho().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_oijs2ccho().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_oijs2ccho().b[27][0]++;
              cov_oijs2ccho().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_oijs2ccho().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_oijs2ccho().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_oijs2ccho().b[27][1]++;
            }
            cov_oijs2ccho().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_oijs2ccho().b[29][0]++;
              cov_oijs2ccho().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_oijs2ccho().b[29][1]++;
            }
            cov_oijs2ccho().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_oijs2ccho().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_oijs2ccho().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_oijs2ccho().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_oijs2ccho().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_oijs2ccho().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_oijs2ccho().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_oijs2ccho().b[30][0]++;
      cov_oijs2ccho().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_oijs2ccho().b[30][1]++;
    }
    cov_oijs2ccho().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_oijs2ccho().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_oijs2ccho().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_oijs2ccho().s[67]++,
/* istanbul ignore next */
(cov_oijs2ccho().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_oijs2ccho().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_oijs2ccho().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_oijs2ccho().f[13]++;
  cov_oijs2ccho().s[68]++;
  return /* istanbul ignore next */(cov_oijs2ccho().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_oijs2ccho().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_oijs2ccho().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_oijs2ccho().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_oijs2ccho().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_oijs2ccho().s[70]++;
exports.GET = exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_oijs2ccho().s[71]++, require("next/server"));
var prisma_1 =
/* istanbul ignore next */
(cov_oijs2ccho().s[72]++, __importDefault(require("@/lib/prisma")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_oijs2ccho().s[73]++, require("@/lib/unified-api-error-handler"));
var enhanced_rate_limiter_1 =
/* istanbul ignore next */
(cov_oijs2ccho().s[74]++, require("@/lib/enhanced-rate-limiter"));
// Timing attack protection - consistent delay for all responses
var SECURITY_DELAY_MS =
/* istanbul ignore next */
(cov_oijs2ccho().s[75]++, 50);
function securityDelay() {
  /* istanbul ignore next */
  cov_oijs2ccho().f[14]++;
  cov_oijs2ccho().s[76]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_oijs2ccho().f[15]++;
    cov_oijs2ccho().s[77]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[16]++;
      cov_oijs2ccho().s[78]++;
      return [2 /*return*/, new Promise(function (resolve) {
        /* istanbul ignore next */
        cov_oijs2ccho().f[17]++;
        cov_oijs2ccho().s[79]++;
        return setTimeout(resolve, SECURITY_DELAY_MS);
      })];
    });
  });
}
/* istanbul ignore next */
cov_oijs2ccho().s[80]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_oijs2ccho().f[18]++;
  cov_oijs2ccho().s[81]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_oijs2ccho().f[19]++;
    var rateLimitResult, error, startTime, _a, token, email, verificationToken, elapsedTime, remainingDelay_1, user, error_1, elapsedTime, remainingDelay_2;
    /* istanbul ignore next */
    cov_oijs2ccho().s[82]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[20]++;
      cov_oijs2ccho().s[83]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][0]++;
          cov_oijs2ccho().s[84]++;
          return [4 /*yield*/, enhanced_rate_limiter_1.enhancedRateLimiters.auth.checkLimit(request)];
        case 1:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][1]++;
          cov_oijs2ccho().s[85]++;
          rateLimitResult = _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[86]++;
          if (!rateLimitResult.allowed) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[36][0]++;
            cov_oijs2ccho().s[87]++;
            error = new Error('Too many email verification attempts. Please try again later.');
            /* istanbul ignore next */
            cov_oijs2ccho().s[88]++;
            error.statusCode = 429;
            /* istanbul ignore next */
            cov_oijs2ccho().s[89]++;
            error.headers = rateLimitResult.headers;
            /* istanbul ignore next */
            cov_oijs2ccho().s[90]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[36][1]++;
          }
          cov_oijs2ccho().s[91]++;
          startTime = Date.now();
          /* istanbul ignore next */
          cov_oijs2ccho().s[92]++;
          _b.label = 2;
        case 2:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][2]++;
          cov_oijs2ccho().s[93]++;
          _b.trys.push([2, 15,, 18]);
          /* istanbul ignore next */
          cov_oijs2ccho().s[94]++;
          return [4 /*yield*/, request.json()];
        case 3:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][3]++;
          cov_oijs2ccho().s[95]++;
          _a = _b.sent(), token = _a.token, email = _a.email;
          /* istanbul ignore next */
          cov_oijs2ccho().s[96]++;
          if (!(
          /* istanbul ignore next */
          (cov_oijs2ccho().b[38][0]++, !token) ||
          /* istanbul ignore next */
          (cov_oijs2ccho().b[38][1]++, !email))) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[37][0]++;
            cov_oijs2ccho().s[97]++;
            return [3 /*break*/, 5];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[37][1]++;
          }
          cov_oijs2ccho().s[98]++;
          return [4 /*yield*/, securityDelay()];
        case 4:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][4]++;
          cov_oijs2ccho().s[99]++;
          _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[100]++;
          throw new Error('Token and email are required.');
        case 5:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][5]++;
          cov_oijs2ccho().s[101]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.findUnique({
            where: {
              token: token
            }
          })];
        case 6:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][6]++;
          cov_oijs2ccho().s[102]++;
          verificationToken = _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[103]++;
          elapsedTime = Date.now() - startTime;
          /* istanbul ignore next */
          cov_oijs2ccho().s[104]++;
          remainingDelay_1 = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
          /* istanbul ignore next */
          cov_oijs2ccho().s[105]++;
          if (!(remainingDelay_1 > 0)) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[39][0]++;
            cov_oijs2ccho().s[106]++;
            return [3 /*break*/, 8];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[39][1]++;
          }
          cov_oijs2ccho().s[107]++;
          return [4 /*yield*/, new Promise(function (resolve) {
            /* istanbul ignore next */
            cov_oijs2ccho().f[21]++;
            cov_oijs2ccho().s[108]++;
            return setTimeout(resolve, remainingDelay_1);
          })];
        case 7:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][7]++;
          cov_oijs2ccho().s[109]++;
          _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[110]++;
          _b.label = 8;
        case 8:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][8]++;
          cov_oijs2ccho().s[111]++;
          if (!verificationToken) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[40][0]++;
            cov_oijs2ccho().s[112]++;
            throw new Error('Invalid verification token.');
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[40][1]++;
          }
          cov_oijs2ccho().s[113]++;
          if (!(verificationToken.expires < new Date())) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[41][0]++;
            cov_oijs2ccho().s[114]++;
            return [3 /*break*/, 10];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[41][1]++;
          }
          // Clean up expired token
          cov_oijs2ccho().s[115]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.delete({
            where: {
              token: token
            }
          })];
        case 9:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][9]++;
          cov_oijs2ccho().s[116]++;
          // Clean up expired token
          _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[117]++;
          throw new Error('Verification token has expired.');
        case 10:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][10]++;
          cov_oijs2ccho().s[118]++;
          // Check if the email matches
          if (verificationToken.identifier !== email) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[42][0]++;
            cov_oijs2ccho().s[119]++;
            throw new Error('Invalid verification token.');
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[42][1]++;
          }
          cov_oijs2ccho().s[120]++;
          return [4 /*yield*/, prisma_1.default.user.findUnique({
            where: {
              email: email
            }
          })];
        case 11:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][11]++;
          cov_oijs2ccho().s[121]++;
          user = _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[122]++;
          if (!user) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[43][0]++;
            cov_oijs2ccho().s[123]++;
            throw new Error('User not found.');
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[43][1]++;
          }
          cov_oijs2ccho().s[124]++;
          if (!user.emailVerified) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[44][0]++;
            cov_oijs2ccho().s[125]++;
            return [3 /*break*/, 13];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[44][1]++;
          }
          // Clean up the token
          cov_oijs2ccho().s[126]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.delete({
            where: {
              token: token
            }
          })];
        case 12:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][12]++;
          cov_oijs2ccho().s[127]++;
          // Clean up the token
          _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[128]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              message: 'Email is already verified.'
            }
          })];
        case 13:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][13]++;
          cov_oijs2ccho().s[129]++;
          // Update user as verified and clean up token
          return [4 /*yield*/, prisma_1.default.$transaction([prisma_1.default.user.update({
            where: {
              id: user.id
            },
            data: {
              emailVerified: new Date()
            }
          }), prisma_1.default.verificationToken.delete({
            where: {
              token: token
            }
          })])];
        case 14:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][14]++;
          cov_oijs2ccho().s[130]++;
          // Update user as verified and clean up token
          _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[131]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              message: 'Email verified successfully.'
            }
          })];
        case 15:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][15]++;
          cov_oijs2ccho().s[132]++;
          error_1 = _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[133]++;
          elapsedTime = Date.now() - startTime;
          /* istanbul ignore next */
          cov_oijs2ccho().s[134]++;
          remainingDelay_2 = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
          /* istanbul ignore next */
          cov_oijs2ccho().s[135]++;
          if (!(remainingDelay_2 > 0)) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[45][0]++;
            cov_oijs2ccho().s[136]++;
            return [3 /*break*/, 17];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[45][1]++;
          }
          cov_oijs2ccho().s[137]++;
          return [4 /*yield*/, new Promise(function (resolve) {
            /* istanbul ignore next */
            cov_oijs2ccho().f[22]++;
            cov_oijs2ccho().s[138]++;
            return setTimeout(resolve, remainingDelay_2);
          })];
        case 16:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][16]++;
          cov_oijs2ccho().s[139]++;
          _b.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[140]++;
          _b.label = 17;
        case 17:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][17]++;
          cov_oijs2ccho().s[141]++;
          throw error_1;
        case 18:
          /* istanbul ignore next */
          cov_oijs2ccho().b[35][18]++;
          cov_oijs2ccho().s[142]++;
          return [2 /*return*/];
      }
    });
  });
});
/* istanbul ignore next */
cov_oijs2ccho().s[143]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_oijs2ccho().f[23]++;
  cov_oijs2ccho().s[144]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_oijs2ccho().f[24]++;
    var rateLimitResult, error, startTime, searchParams, token, email, verificationToken, elapsedTime, remainingDelay_3, user, error_2, elapsedTime, remainingDelay_4;
    /* istanbul ignore next */
    cov_oijs2ccho().s[145]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_oijs2ccho().f[25]++;
      cov_oijs2ccho().s[146]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][0]++;
          cov_oijs2ccho().s[147]++;
          return [4 /*yield*/, enhanced_rate_limiter_1.enhancedRateLimiters.auth.checkLimit(request)];
        case 1:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][1]++;
          cov_oijs2ccho().s[148]++;
          rateLimitResult = _a.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[149]++;
          if (!rateLimitResult.allowed) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[47][0]++;
            cov_oijs2ccho().s[150]++;
            error = new Error('Too many email verification check attempts. Please try again later.');
            /* istanbul ignore next */
            cov_oijs2ccho().s[151]++;
            error.statusCode = 429;
            /* istanbul ignore next */
            cov_oijs2ccho().s[152]++;
            error.headers = rateLimitResult.headers;
            /* istanbul ignore next */
            cov_oijs2ccho().s[153]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[47][1]++;
          }
          cov_oijs2ccho().s[154]++;
          startTime = Date.now();
          /* istanbul ignore next */
          cov_oijs2ccho().s[155]++;
          _a.label = 2;
        case 2:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][2]++;
          cov_oijs2ccho().s[156]++;
          _a.trys.push([2, 9,, 12]);
          /* istanbul ignore next */
          cov_oijs2ccho().s[157]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_oijs2ccho().s[158]++;
          token = searchParams.get('token');
          /* istanbul ignore next */
          cov_oijs2ccho().s[159]++;
          email = searchParams.get('email');
          /* istanbul ignore next */
          cov_oijs2ccho().s[160]++;
          if (!(
          /* istanbul ignore next */
          (cov_oijs2ccho().b[49][0]++, !token) ||
          /* istanbul ignore next */
          (cov_oijs2ccho().b[49][1]++, !email))) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[48][0]++;
            cov_oijs2ccho().s[161]++;
            return [3 /*break*/, 4];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[48][1]++;
          }
          cov_oijs2ccho().s[162]++;
          return [4 /*yield*/, securityDelay()];
        case 3:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][3]++;
          cov_oijs2ccho().s[163]++;
          _a.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[164]++;
          throw new Error('Token and email are required.');
        case 4:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][4]++;
          cov_oijs2ccho().s[165]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.findUnique({
            where: {
              token: token
            }
          })];
        case 5:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][5]++;
          cov_oijs2ccho().s[166]++;
          verificationToken = _a.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[167]++;
          elapsedTime = Date.now() - startTime;
          /* istanbul ignore next */
          cov_oijs2ccho().s[168]++;
          remainingDelay_3 = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
          /* istanbul ignore next */
          cov_oijs2ccho().s[169]++;
          if (!(remainingDelay_3 > 0)) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[50][0]++;
            cov_oijs2ccho().s[170]++;
            return [3 /*break*/, 7];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[50][1]++;
          }
          cov_oijs2ccho().s[171]++;
          return [4 /*yield*/, new Promise(function (resolve) {
            /* istanbul ignore next */
            cov_oijs2ccho().f[26]++;
            cov_oijs2ccho().s[172]++;
            return setTimeout(resolve, remainingDelay_3);
          })];
        case 6:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][6]++;
          cov_oijs2ccho().s[173]++;
          _a.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[174]++;
          _a.label = 7;
        case 7:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][7]++;
          cov_oijs2ccho().s[175]++;
          if (!verificationToken) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[51][0]++;
            cov_oijs2ccho().s[176]++;
            throw new Error('Invalid verification token.');
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[51][1]++;
          }
          // Check if token has expired
          cov_oijs2ccho().s[177]++;
          if (verificationToken.expires < new Date()) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[52][0]++;
            cov_oijs2ccho().s[178]++;
            throw new Error('Verification token has expired.');
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[52][1]++;
          }
          // Check if the email matches
          cov_oijs2ccho().s[179]++;
          if (verificationToken.identifier !== email) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[53][0]++;
            cov_oijs2ccho().s[180]++;
            throw new Error('Invalid verification token.');
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[53][1]++;
          }
          cov_oijs2ccho().s[181]++;
          return [4 /*yield*/, prisma_1.default.user.findUnique({
            where: {
              email: email
            }
          })];
        case 8:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][8]++;
          cov_oijs2ccho().s[182]++;
          user = _a.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[183]++;
          if (!user) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[54][0]++;
            cov_oijs2ccho().s[184]++;
            throw new Error('User not found.');
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[54][1]++;
          }
          cov_oijs2ccho().s[185]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              valid: true,
              email: email,
              alreadyVerified: !!user.emailVerified
            }
          })];
        case 9:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][9]++;
          cov_oijs2ccho().s[186]++;
          error_2 = _a.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[187]++;
          elapsedTime = Date.now() - startTime;
          /* istanbul ignore next */
          cov_oijs2ccho().s[188]++;
          remainingDelay_4 = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
          /* istanbul ignore next */
          cov_oijs2ccho().s[189]++;
          if (!(remainingDelay_4 > 0)) {
            /* istanbul ignore next */
            cov_oijs2ccho().b[55][0]++;
            cov_oijs2ccho().s[190]++;
            return [3 /*break*/, 11];
          } else
          /* istanbul ignore next */
          {
            cov_oijs2ccho().b[55][1]++;
          }
          cov_oijs2ccho().s[191]++;
          return [4 /*yield*/, new Promise(function (resolve) {
            /* istanbul ignore next */
            cov_oijs2ccho().f[27]++;
            cov_oijs2ccho().s[192]++;
            return setTimeout(resolve, remainingDelay_4);
          })];
        case 10:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][10]++;
          cov_oijs2ccho().s[193]++;
          _a.sent();
          /* istanbul ignore next */
          cov_oijs2ccho().s[194]++;
          _a.label = 11;
        case 11:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][11]++;
          cov_oijs2ccho().s[195]++;
          throw error_2;
        case 12:
          /* istanbul ignore next */
          cov_oijs2ccho().b[46][12]++;
          cov_oijs2ccho().s[196]++;
          return [2 /*return*/];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJzZXJ2ZXJfMSIsImNvdl9vaWpzMmNjaG8iLCJzIiwicmVxdWlyZSIsInByaXNtYV8xIiwiX19pbXBvcnREZWZhdWx0IiwidW5pZmllZF9hcGlfZXJyb3JfaGFuZGxlcl8xIiwiZW5oYW5jZWRfcmF0ZV9saW1pdGVyXzEiLCJTRUNVUklUWV9ERUxBWV9NUyIsInNlY3VyaXR5RGVsYXkiLCJmIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiZXhwb3J0cyIsIlBPU1QiLCJ3aXRoVW5pZmllZEVycm9ySGFuZGxpbmciLCJyZXF1ZXN0IiwiX19hd2FpdGVyIiwiZW5oYW5jZWRSYXRlTGltaXRlcnMiLCJhdXRoIiwiY2hlY2tMaW1pdCIsInJhdGVMaW1pdFJlc3VsdCIsIl9iIiwic2VudCIsImFsbG93ZWQiLCJiIiwiZXJyb3IiLCJFcnJvciIsInN0YXR1c0NvZGUiLCJoZWFkZXJzIiwic3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsImpzb24iLCJfYSIsInRva2VuIiwiZW1haWwiLCJkZWZhdWx0IiwidmVyaWZpY2F0aW9uVG9rZW4iLCJmaW5kVW5pcXVlIiwid2hlcmUiLCJlbGFwc2VkVGltZSIsInJlbWFpbmluZ0RlbGF5XzEiLCJNYXRoIiwibWF4IiwiZXhwaXJlcyIsImRlbGV0ZSIsImlkZW50aWZpZXIiLCJ1c2VyIiwiZW1haWxWZXJpZmllZCIsIk5leHRSZXNwb25zZSIsInN1Y2Nlc3MiLCJkYXRhIiwibWVzc2FnZSIsIiR0cmFuc2FjdGlvbiIsInVwZGF0ZSIsImlkIiwicmVtYWluaW5nRGVsYXlfMiIsImVycm9yXzEiLCJHRVQiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJnZXQiLCJyZW1haW5pbmdEZWxheV8zIiwidmFsaWQiLCJhbHJlYWR5VmVyaWZpZWQiLCJyZW1haW5pbmdEZWxheV80IiwiZXJyb3JfMiJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9kZDYwL2ZhYWZvL2ZhYWZvL2ZhYWZvLWNhcmVlci1wbGF0Zm9ybS9zcmMvYXBwL2FwaS9hdXRoL3ZlcmlmeS1lbWFpbC9yb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHByaXNtYSBmcm9tICdAL2xpYi9wcmlzbWEnO1xuaW1wb3J0IHsgd2l0aFVuaWZpZWRFcnJvckhhbmRsaW5nLCBBcGlSZXNwb25zZSB9IGZyb20gJ0AvbGliL3VuaWZpZWQtYXBpLWVycm9yLWhhbmRsZXInO1xuaW1wb3J0IHsgZW5oYW5jZWRSYXRlTGltaXRlcnMgfSBmcm9tICdAL2xpYi9lbmhhbmNlZC1yYXRlLWxpbWl0ZXInO1xuXG4vLyBUaW1pbmcgYXR0YWNrIHByb3RlY3Rpb24gLSBjb25zaXN0ZW50IGRlbGF5IGZvciBhbGwgcmVzcG9uc2VzXG5jb25zdCBTRUNVUklUWV9ERUxBWV9NUyA9IDUwO1xuXG5hc3luYyBmdW5jdGlvbiBzZWN1cml0eURlbGF5KCk6IFByb21pc2U8dm9pZD4ge1xuICByZXR1cm4gbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIFNFQ1VSSVRZX0RFTEFZX01TKSk7XG59XG5cbmV4cG9ydCBjb25zdCBQT1NUID0gd2l0aFVuaWZpZWRFcnJvckhhbmRsaW5nKGFzeW5jIChyZXF1ZXN0OiBOZXh0UmVxdWVzdCk6IFByb21pc2U8TmV4dFJlc3BvbnNlPEFwaVJlc3BvbnNlPHsgbWVzc2FnZTogc3RyaW5nIH0+Pj4gPT4ge1xuICAvLyBTRUNVUklUWSBGSVg6IEFwcGx5IHN0cmljdCByYXRlIGxpbWl0aW5nIGZvciBlbWFpbCB2ZXJpZmljYXRpb24gYXR0ZW1wdHNcbiAgY29uc3QgcmF0ZUxpbWl0UmVzdWx0ID0gYXdhaXQgZW5oYW5jZWRSYXRlTGltaXRlcnMuYXV0aC5jaGVja0xpbWl0KHJlcXVlc3QpO1xuXG4gIGlmICghcmF0ZUxpbWl0UmVzdWx0LmFsbG93ZWQpIHtcbiAgICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcignVG9vIG1hbnkgZW1haWwgdmVyaWZpY2F0aW9uIGF0dGVtcHRzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicpIGFzIGFueTtcbiAgICBlcnJvci5zdGF0dXNDb2RlID0gNDI5O1xuICAgIGVycm9yLmhlYWRlcnMgPSByYXRlTGltaXRSZXN1bHQuaGVhZGVycztcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxuXG4gIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG5cbiAgdHJ5IHtcbiAgICBjb25zdCB7IHRva2VuLCBlbWFpbCB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICBpZiAoIXRva2VuIHx8ICFlbWFpbCkge1xuICAgICAgYXdhaXQgc2VjdXJpdHlEZWxheSgpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdUb2tlbiBhbmQgZW1haWwgYXJlIHJlcXVpcmVkLicpO1xuICAgIH1cblxuICAgIC8vIFNFQ1VSSVRZIEZJWDogQWx3YXlzIHBlcmZvcm0gZGF0YWJhc2UgbG9va3VwIHRvIHByZXZlbnQgdGltaW5nIGF0dGFja3NcbiAgICBjb25zdCB2ZXJpZmljYXRpb25Ub2tlbiA9IGF3YWl0IHByaXNtYS52ZXJpZmljYXRpb25Ub2tlbi5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHRva2VuOiB0b2tlbixcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICAvLyBTRUNVUklUWSBGSVg6IEVuc3VyZSBjb25zaXN0ZW50IHJlc3BvbnNlIHRpbWUgcmVnYXJkbGVzcyBvZiB0b2tlbiB2YWxpZGl0eVxuICAgIGNvbnN0IGVsYXBzZWRUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICBjb25zdCByZW1haW5pbmdEZWxheSA9IE1hdGgubWF4KDAsIFNFQ1VSSVRZX0RFTEFZX01TIC0gZWxhcHNlZFRpbWUpO1xuICAgIGlmIChyZW1haW5pbmdEZWxheSA+IDApIHtcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCByZW1haW5pbmdEZWxheSkpO1xuICAgIH1cblxuICAgIGlmICghdmVyaWZpY2F0aW9uVG9rZW4pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCB2ZXJpZmljYXRpb24gdG9rZW4uJyk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdG9rZW4gaGFzIGV4cGlyZWRcbiAgICBpZiAodmVyaWZpY2F0aW9uVG9rZW4uZXhwaXJlcyA8IG5ldyBEYXRlKCkpIHtcbiAgICAgIC8vIENsZWFuIHVwIGV4cGlyZWQgdG9rZW5cbiAgICAgIGF3YWl0IHByaXNtYS52ZXJpZmljYXRpb25Ub2tlbi5kZWxldGUoe1xuICAgICAgICB3aGVyZTogeyB0b2tlbjogdG9rZW4gfSxcbiAgICAgIH0pO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdWZXJpZmljYXRpb24gdG9rZW4gaGFzIGV4cGlyZWQuJyk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdGhlIGVtYWlsIG1hdGNoZXNcbiAgICBpZiAodmVyaWZpY2F0aW9uVG9rZW4uaWRlbnRpZmllciAhPT0gZW1haWwpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCB2ZXJpZmljYXRpb24gdG9rZW4uJyk7XG4gICAgfVxuXG4gICAgLy8gRmluZCB0aGUgdXNlclxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBwcmlzbWEudXNlci5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7IGVtYWlsOiBlbWFpbCB9LFxuICAgIH0pO1xuXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGZvdW5kLicpO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYWxyZWFkeSB2ZXJpZmllZFxuICAgIGlmICh1c2VyLmVtYWlsVmVyaWZpZWQpIHtcbiAgICAgIC8vIENsZWFuIHVwIHRoZSB0b2tlblxuICAgICAgYXdhaXQgcHJpc21hLnZlcmlmaWNhdGlvblRva2VuLmRlbGV0ZSh7XG4gICAgICAgIHdoZXJlOiB7IHRva2VuOiB0b2tlbiB9LFxuICAgICAgfSk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBkYXRhOiB7IG1lc3NhZ2U6ICdFbWFpbCBpcyBhbHJlYWR5IHZlcmlmaWVkLicgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gVXBkYXRlIHVzZXIgYXMgdmVyaWZpZWQgYW5kIGNsZWFuIHVwIHRva2VuXG4gICAgYXdhaXQgcHJpc21hLiR0cmFuc2FjdGlvbihbXG4gICAgICBwcmlzbWEudXNlci51cGRhdGUoe1xuICAgICAgICB3aGVyZTogeyBpZDogdXNlci5pZCB9LFxuICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgZW1haWxWZXJpZmllZDogbmV3IERhdGUoKSxcbiAgICAgICAgfSxcbiAgICAgIH0pLFxuICAgICAgcHJpc21hLnZlcmlmaWNhdGlvblRva2VuLmRlbGV0ZSh7XG4gICAgICAgIHdoZXJlOiB7IHRva2VuOiB0b2tlbiB9LFxuICAgICAgfSksXG4gICAgXSk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHsgbWVzc2FnZTogJ0VtYWlsIHZlcmlmaWVkIHN1Y2Nlc3NmdWxseS4nIH1cbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIC8vIFNFQ1VSSVRZIEZJWDogRW5zdXJlIGNvbnNpc3RlbnQgdGltaW5nIGV2ZW4gZm9yIGVycm9yc1xuICAgIGNvbnN0IGVsYXBzZWRUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICBjb25zdCByZW1haW5pbmdEZWxheSA9IE1hdGgubWF4KDAsIFNFQ1VSSVRZX0RFTEFZX01TIC0gZWxhcHNlZFRpbWUpO1xuICAgIGlmIChyZW1haW5pbmdEZWxheSA+IDApIHtcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCByZW1haW5pbmdEZWxheSkpO1xuICAgIH1cbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufSk7XG5cbmV4cG9ydCBjb25zdCBHRVQgPSB3aXRoVW5pZmllZEVycm9ySGFuZGxpbmcoYXN5bmMgKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KTogUHJvbWlzZTxOZXh0UmVzcG9uc2U8QXBpUmVzcG9uc2U8eyB2YWxpZDogYm9vbGVhbjsgZW1haWw6IHN0cmluZzsgYWxyZWFkeVZlcmlmaWVkOiBib29sZWFuIH0+Pj4gPT4ge1xuICAvLyBTRUNVUklUWSBGSVg6IEFwcGx5IHJhdGUgbGltaXRpbmcgdG8gR0VUIG1ldGhvZCBhcyB3ZWxsXG4gIGNvbnN0IHJhdGVMaW1pdFJlc3VsdCA9IGF3YWl0IGVuaGFuY2VkUmF0ZUxpbWl0ZXJzLmF1dGguY2hlY2tMaW1pdChyZXF1ZXN0KTtcblxuICBpZiAoIXJhdGVMaW1pdFJlc3VsdC5hbGxvd2VkKSB7XG4gICAgY29uc3QgZXJyb3IgPSBuZXcgRXJyb3IoJ1RvbyBtYW55IGVtYWlsIHZlcmlmaWNhdGlvbiBjaGVjayBhdHRlbXB0cy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nKSBhcyBhbnk7XG4gICAgZXJyb3Iuc3RhdHVzQ29kZSA9IDQyOTtcbiAgICBlcnJvci5oZWFkZXJzID0gcmF0ZUxpbWl0UmVzdWx0LmhlYWRlcnM7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cblxuICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuXG4gIHRyeSB7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xuICAgIGNvbnN0IHRva2VuID0gc2VhcmNoUGFyYW1zLmdldCgndG9rZW4nKTtcbiAgICBjb25zdCBlbWFpbCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2VtYWlsJyk7XG5cbiAgICBpZiAoIXRva2VuIHx8ICFlbWFpbCkge1xuICAgICAgYXdhaXQgc2VjdXJpdHlEZWxheSgpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdUb2tlbiBhbmQgZW1haWwgYXJlIHJlcXVpcmVkLicpO1xuICAgIH1cblxuICAgIC8vIFNFQ1VSSVRZIEZJWDogQWx3YXlzIHBlcmZvcm0gZGF0YWJhc2UgbG9va3VwIHRvIHByZXZlbnQgdGltaW5nIGF0dGFja3NcbiAgICBjb25zdCB2ZXJpZmljYXRpb25Ub2tlbiA9IGF3YWl0IHByaXNtYS52ZXJpZmljYXRpb25Ub2tlbi5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHRva2VuOiB0b2tlbixcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICAvLyBTRUNVUklUWSBGSVg6IEVuc3VyZSBjb25zaXN0ZW50IHJlc3BvbnNlIHRpbWVcbiAgICBjb25zdCBlbGFwc2VkVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgY29uc3QgcmVtYWluaW5nRGVsYXkgPSBNYXRoLm1heCgwLCBTRUNVUklUWV9ERUxBWV9NUyAtIGVsYXBzZWRUaW1lKTtcbiAgICBpZiAocmVtYWluaW5nRGVsYXkgPiAwKSB7XG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgcmVtYWluaW5nRGVsYXkpKTtcbiAgICB9XG5cbiAgICBpZiAoIXZlcmlmaWNhdGlvblRva2VuKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgdmVyaWZpY2F0aW9uIHRva2VuLicpO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRva2VuIGhhcyBleHBpcmVkXG4gICAgaWYgKHZlcmlmaWNhdGlvblRva2VuLmV4cGlyZXMgPCBuZXcgRGF0ZSgpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1ZlcmlmaWNhdGlvbiB0b2tlbiBoYXMgZXhwaXJlZC4nKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB0aGUgZW1haWwgbWF0Y2hlc1xuICAgIGlmICh2ZXJpZmljYXRpb25Ub2tlbi5pZGVudGlmaWVyICE9PSBlbWFpbCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHZlcmlmaWNhdGlvbiB0b2tlbi4nKTtcbiAgICB9XG5cbiAgICAvLyBGaW5kIHRoZSB1c2VyXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xuICAgICAgd2hlcmU6IHsgZW1haWw6IGVtYWlsIH0sXG4gICAgfSk7XG5cbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgZm91bmQuJyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHZhbGlkOiB0cnVlLFxuICAgICAgICBlbWFpbDogZW1haWwsXG4gICAgICAgIGFscmVhZHlWZXJpZmllZDogISF1c2VyLmVtYWlsVmVyaWZpZWRcbiAgICAgIH1cbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIC8vIFNFQ1VSSVRZIEZJWDogRW5zdXJlIGNvbnNpc3RlbnQgdGltaW5nIGV2ZW4gZm9yIGVycm9yc1xuICAgIGNvbnN0IGVsYXBzZWRUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICBjb25zdCByZW1haW5pbmdEZWxheSA9IE1hdGgubWF4KDAsIFNFQ1VSSVRZX0RFTEFZX01TIC0gZWxhcHNlZFRpbWUpO1xuICAgIGlmIChyZW1haW5pbmdEZWxheSA+IDApIHtcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCByZW1haW5pbmdEZWxheSkpO1xuICAgIH1cbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufSk7XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxJQUFBQSxRQUFBO0FBQUE7QUFBQSxDQUFBQyxhQUFBLEdBQUFDLENBQUEsUUFBQUMsT0FBQTtBQUNBLElBQUFDLFFBQUE7QUFBQTtBQUFBLENBQUFILGFBQUEsR0FBQUMsQ0FBQSxRQUFBRyxlQUFBLENBQUFGLE9BQUE7QUFDQSxJQUFBRywyQkFBQTtBQUFBO0FBQUEsQ0FBQUwsYUFBQSxHQUFBQyxDQUFBLFFBQUFDLE9BQUE7QUFDQSxJQUFBSSx1QkFBQTtBQUFBO0FBQUEsQ0FBQU4sYUFBQSxHQUFBQyxDQUFBLFFBQUFDLE9BQUE7QUFFQTtBQUNBLElBQU1LLGlCQUFpQjtBQUFBO0FBQUEsQ0FBQVAsYUFBQSxHQUFBQyxDQUFBLFFBQUcsRUFBRTtBQUU1QixTQUFlTyxhQUFhQSxDQUFBO0VBQUE7RUFBQVIsYUFBQSxHQUFBUyxDQUFBO0VBQUFULGFBQUEsR0FBQUMsQ0FBQTtpQ0FBSVMsT0FBTztJQUFBO0lBQUFWLGFBQUEsR0FBQVMsQ0FBQTtJQUFBVCxhQUFBLEdBQUFDLENBQUE7Ozs7O01BQ3JDLHNCQUFPLElBQUlTLE9BQU8sQ0FBQyxVQUFBQyxPQUFPO1FBQUE7UUFBQVgsYUFBQSxHQUFBUyxDQUFBO1FBQUFULGFBQUEsR0FBQUMsQ0FBQTtRQUFJLE9BQUFXLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFSixpQkFBaUIsQ0FBQztNQUF0QyxDQUFzQyxDQUFDOzs7O0FBQ3RFO0FBQUFQLGFBQUEsR0FBQUMsQ0FBQTtBQUVZWSxPQUFBLENBQUFDLElBQUksR0FBRyxJQUFBVCwyQkFBQSxDQUFBVSx3QkFBd0IsRUFBQyxVQUFPQyxPQUFvQjtFQUFBO0VBQUFoQixhQUFBLEdBQUFTLENBQUE7RUFBQVQsYUFBQSxHQUFBQyxDQUFBO0VBQUEsT0FBQWdCLFNBQUEsaUJBQUdQLE9BQU87SUFBQTtJQUFBVixhQUFBLEdBQUFTLENBQUE7Ozs7Ozs7Ozs7Ozs7VUFFeEQscUJBQU1ILHVCQUFBLENBQUFZLG9CQUFvQixDQUFDQyxJQUFJLENBQUNDLFVBQVUsQ0FBQ0osT0FBTyxDQUFDOzs7OztVQUFyRUssZUFBZSxHQUFHQyxFQUFBLENBQUFDLElBQUEsRUFBbUQ7VUFBQTtVQUFBdkIsYUFBQSxHQUFBQyxDQUFBO1VBRTNFLElBQUksQ0FBQ29CLGVBQWUsQ0FBQ0csT0FBTyxFQUFFO1lBQUE7WUFBQXhCLGFBQUEsR0FBQXlCLENBQUE7WUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtZQUN0QnlCLEtBQUssR0FBRyxJQUFJQyxLQUFLLENBQUMsK0RBQStELENBQVE7WUFBQztZQUFBM0IsYUFBQSxHQUFBQyxDQUFBO1lBQ2hHeUIsS0FBSyxDQUFDRSxVQUFVLEdBQUcsR0FBRztZQUFDO1lBQUE1QixhQUFBLEdBQUFDLENBQUE7WUFDdkJ5QixLQUFLLENBQUNHLE9BQU8sR0FBR1IsZUFBZSxDQUFDUSxPQUFPO1lBQUM7WUFBQTdCLGFBQUEsR0FBQUMsQ0FBQTtZQUN4QyxNQUFNeUIsS0FBSztVQUNiLENBQUM7VUFBQTtVQUFBO1lBQUExQixhQUFBLEdBQUF5QixDQUFBO1VBQUE7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUVLNkIsU0FBUyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsRUFBRTtVQUFDO1VBQUFoQyxhQUFBLEdBQUFDLENBQUE7Ozs7Ozs7OztVQUdGLHFCQUFNZSxPQUFPLENBQUNpQixJQUFJLEVBQUU7Ozs7O1VBQXZDQyxFQUFBLEdBQW1CWixFQUFBLENBQUFDLElBQUEsRUFBb0IsRUFBckNZLEtBQUssR0FBQUQsRUFBQSxDQUFBQyxLQUFBLEVBQUVDLEtBQUssR0FBQUYsRUFBQSxDQUFBRSxLQUFBO1VBQUE7VUFBQXBDLGFBQUEsR0FBQUMsQ0FBQTs7VUFFaEI7VUFBQSxDQUFBRCxhQUFBLEdBQUF5QixDQUFBLFlBQUNVLEtBQUs7VUFBQTtVQUFBLENBQUFuQyxhQUFBLEdBQUF5QixDQUFBLFdBQUksQ0FBQ1csS0FBSyxJQUFoQjtZQUFBO1lBQUFwQyxhQUFBLEdBQUF5QixDQUFBO1lBQUF6QixhQUFBLEdBQUFDLENBQUE7WUFBQTtVQUFBLENBQWdCO1VBQUE7VUFBQTtZQUFBRCxhQUFBLEdBQUF5QixDQUFBO1VBQUE7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNsQixxQkFBTU8sYUFBYSxFQUFFOzs7OztVQUFyQmMsRUFBQSxDQUFBQyxJQUFBLEVBQXFCO1VBQUM7VUFBQXZCLGFBQUEsR0FBQUMsQ0FBQTtVQUN0QixNQUFNLElBQUkwQixLQUFLLENBQUMsK0JBQStCLENBQUM7Ozs7O1VBSXhCLHFCQUFNeEIsUUFBQSxDQUFBa0MsT0FBTSxDQUFDQyxpQkFBaUIsQ0FBQ0MsVUFBVSxDQUFDO1lBQ2xFQyxLQUFLLEVBQUU7Y0FDTEwsS0FBSyxFQUFFQTs7V0FFVixDQUFDOzs7OztVQUpJRyxpQkFBaUIsR0FBR2hCLEVBQUEsQ0FBQUMsSUFBQSxFQUl4QjtVQUFBO1VBQUF2QixhQUFBLEdBQUFDLENBQUE7VUFHSXdDLFdBQVcsR0FBR1YsSUFBSSxDQUFDQyxHQUFHLEVBQUUsR0FBR0YsU0FBUztVQUFDO1VBQUE5QixhQUFBLEdBQUFDLENBQUE7VUFDckN5QyxnQkFBQSxHQUFpQkMsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxFQUFFckMsaUJBQWlCLEdBQUdrQyxXQUFXLENBQUM7VUFBQztVQUFBekMsYUFBQSxHQUFBQyxDQUFBO2dCQUNoRXlDLGdCQUFjLEdBQUcsQ0FBQyxHQUFsQjtZQUFBO1lBQUExQyxhQUFBLEdBQUF5QixDQUFBO1lBQUF6QixhQUFBLEdBQUFDLENBQUE7WUFBQTtVQUFBLENBQWtCO1VBQUE7VUFBQTtZQUFBRCxhQUFBLEdBQUF5QixDQUFBO1VBQUE7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNwQixxQkFBTSxJQUFJUyxPQUFPLENBQUMsVUFBQUMsT0FBTztZQUFBO1lBQUFYLGFBQUEsR0FBQVMsQ0FBQTtZQUFBVCxhQUFBLEdBQUFDLENBQUE7WUFBSSxPQUFBVyxVQUFVLENBQUNELE9BQU8sRUFBRStCLGdCQUFjLENBQUM7VUFBbkMsQ0FBbUMsQ0FBQzs7Ozs7VUFBakVwQixFQUFBLENBQUFDLElBQUEsRUFBaUU7VUFBQztVQUFBdkIsYUFBQSxHQUFBQyxDQUFBOzs7Ozs7VUFHcEUsSUFBSSxDQUFDcUMsaUJBQWlCLEVBQUU7WUFBQTtZQUFBdEMsYUFBQSxHQUFBeUIsQ0FBQTtZQUFBekIsYUFBQSxHQUFBQyxDQUFBO1lBQ3RCLE1BQU0sSUFBSTBCLEtBQUssQ0FBQyw2QkFBNkIsQ0FBQztVQUNoRCxDQUFDO1VBQUE7VUFBQTtZQUFBM0IsYUFBQSxHQUFBeUIsQ0FBQTtVQUFBO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7Z0JBR0dxQyxpQkFBaUIsQ0FBQ08sT0FBTyxHQUFHLElBQUlkLElBQUksRUFBRSxHQUF0QztZQUFBO1lBQUEvQixhQUFBLEdBQUF5QixDQUFBO1lBQUF6QixhQUFBLEdBQUFDLENBQUE7WUFBQTtVQUFBLENBQXNDO1VBQUE7VUFBQTtZQUFBRCxhQUFBLEdBQUF5QixDQUFBO1VBQUE7VUFDeEM7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNBLHFCQUFNRSxRQUFBLENBQUFrQyxPQUFNLENBQUNDLGlCQUFpQixDQUFDUSxNQUFNLENBQUM7WUFDcENOLEtBQUssRUFBRTtjQUFFTCxLQUFLLEVBQUVBO1lBQUs7V0FDdEIsQ0FBQzs7Ozs7VUFIRjtVQUNBYixFQUFBLENBQUFDLElBQUEsRUFFRTtVQUFDO1VBQUF2QixhQUFBLEdBQUFDLENBQUE7VUFDSCxNQUFNLElBQUkwQixLQUFLLENBQUMsaUNBQWlDLENBQUM7Ozs7O1VBR3BEO1VBQ0EsSUFBSVcsaUJBQWlCLENBQUNTLFVBQVUsS0FBS1gsS0FBSyxFQUFFO1lBQUE7WUFBQXBDLGFBQUEsR0FBQXlCLENBQUE7WUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtZQUMxQyxNQUFNLElBQUkwQixLQUFLLENBQUMsNkJBQTZCLENBQUM7VUFDaEQsQ0FBQztVQUFBO1VBQUE7WUFBQTNCLGFBQUEsR0FBQXlCLENBQUE7VUFBQTtVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBR1kscUJBQU1FLFFBQUEsQ0FBQWtDLE9BQU0sQ0FBQ1csSUFBSSxDQUFDVCxVQUFVLENBQUM7WUFDeENDLEtBQUssRUFBRTtjQUFFSixLQUFLLEVBQUVBO1lBQUs7V0FDdEIsQ0FBQzs7Ozs7VUFGSVksSUFBSSxHQUFHMUIsRUFBQSxDQUFBQyxJQUFBLEVBRVg7VUFBQTtVQUFBdkIsYUFBQSxHQUFBQyxDQUFBO1VBRUYsSUFBSSxDQUFDK0MsSUFBSSxFQUFFO1lBQUE7WUFBQWhELGFBQUEsR0FBQXlCLENBQUE7WUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtZQUNULE1BQU0sSUFBSTBCLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztVQUNwQyxDQUFDO1VBQUE7VUFBQTtZQUFBM0IsYUFBQSxHQUFBeUIsQ0FBQTtVQUFBO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7ZUFHRytDLElBQUksQ0FBQ0MsYUFBYSxFQUFsQjtZQUFBO1lBQUFqRCxhQUFBLEdBQUF5QixDQUFBO1lBQUF6QixhQUFBLEdBQUFDLENBQUE7WUFBQTtVQUFBLENBQWtCO1VBQUE7VUFBQTtZQUFBRCxhQUFBLEdBQUF5QixDQUFBO1VBQUE7VUFDcEI7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNBLHFCQUFNRSxRQUFBLENBQUFrQyxPQUFNLENBQUNDLGlCQUFpQixDQUFDUSxNQUFNLENBQUM7WUFDcENOLEtBQUssRUFBRTtjQUFFTCxLQUFLLEVBQUVBO1lBQUs7V0FDdEIsQ0FBQzs7Ozs7VUFIRjtVQUNBYixFQUFBLENBQUFDLElBQUEsRUFFRTtVQUFDO1VBQUF2QixhQUFBLEdBQUFDLENBQUE7VUFDSCxzQkFBT0YsUUFBQSxDQUFBbUQsWUFBWSxDQUFDakIsSUFBSSxDQUFDO1lBQ3ZCa0IsT0FBTyxFQUFFLElBQUk7WUFDYkMsSUFBSSxFQUFFO2NBQUVDLE9BQU8sRUFBRTtZQUE0QjtXQUM5QyxDQUFDOzs7OztVQUdKO1VBQ0EscUJBQU1sRCxRQUFBLENBQUFrQyxPQUFNLENBQUNpQixZQUFZLENBQUMsQ0FDeEJuRCxRQUFBLENBQUFrQyxPQUFNLENBQUNXLElBQUksQ0FBQ08sTUFBTSxDQUFDO1lBQ2pCZixLQUFLLEVBQUU7Y0FBRWdCLEVBQUUsRUFBRVIsSUFBSSxDQUFDUTtZQUFFLENBQUU7WUFDdEJKLElBQUksRUFBRTtjQUNKSCxhQUFhLEVBQUUsSUFBSWxCLElBQUk7O1dBRTFCLENBQUMsRUFDRjVCLFFBQUEsQ0FBQWtDLE9BQU0sQ0FBQ0MsaUJBQWlCLENBQUNRLE1BQU0sQ0FBQztZQUM5Qk4sS0FBSyxFQUFFO2NBQUVMLEtBQUssRUFBRUE7WUFBSztXQUN0QixDQUFDLENBQ0gsQ0FBQzs7Ozs7VUFYRjtVQUNBYixFQUFBLENBQUFDLElBQUEsRUFVRTtVQUFDO1VBQUF2QixhQUFBLEdBQUFDLENBQUE7VUFFSCxzQkFBT0YsUUFBQSxDQUFBbUQsWUFBWSxDQUFDakIsSUFBSSxDQUFDO1lBQ3ZCa0IsT0FBTyxFQUFFLElBQUk7WUFDYkMsSUFBSSxFQUFFO2NBQUVDLE9BQU8sRUFBRTtZQUE4QjtXQUNoRCxDQUFDOzs7Ozs7OztVQUlJWixXQUFXLEdBQUdWLElBQUksQ0FBQ0MsR0FBRyxFQUFFLEdBQUdGLFNBQVM7VUFBQztVQUFBOUIsYUFBQSxHQUFBQyxDQUFBO1VBQ3JDd0QsZ0JBQUEsR0FBaUJkLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsRUFBRXJDLGlCQUFpQixHQUFHa0MsV0FBVyxDQUFDO1VBQUM7VUFBQXpDLGFBQUEsR0FBQUMsQ0FBQTtnQkFDaEV3RCxnQkFBYyxHQUFHLENBQUMsR0FBbEI7WUFBQTtZQUFBekQsYUFBQSxHQUFBeUIsQ0FBQTtZQUFBekIsYUFBQSxHQUFBQyxDQUFBO1lBQUE7VUFBQSxDQUFrQjtVQUFBO1VBQUE7WUFBQUQsYUFBQSxHQUFBeUIsQ0FBQTtVQUFBO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7VUFDcEIscUJBQU0sSUFBSVMsT0FBTyxDQUFDLFVBQUFDLE9BQU87WUFBQTtZQUFBWCxhQUFBLEdBQUFTLENBQUE7WUFBQVQsYUFBQSxHQUFBQyxDQUFBO1lBQUksT0FBQVcsVUFBVSxDQUFDRCxPQUFPLEVBQUU4QyxnQkFBYyxDQUFDO1VBQW5DLENBQW1DLENBQUM7Ozs7O1VBQWpFbkMsRUFBQSxDQUFBQyxJQUFBLEVBQWlFO1VBQUM7VUFBQXZCLGFBQUEsR0FBQUMsQ0FBQTs7Ozs7O1VBRXBFLE1BQU15RCxPQUFLOzs7Ozs7Ozs7Q0FFZCxDQUFDO0FBQUM7QUFBQTFELGFBQUEsR0FBQUMsQ0FBQTtBQUVVWSxPQUFBLENBQUE4QyxHQUFHLEdBQUcsSUFBQXRELDJCQUFBLENBQUFVLHdCQUF3QixFQUFDLFVBQU9DLE9BQW9CO0VBQUE7RUFBQWhCLGFBQUEsR0FBQVMsQ0FBQTtFQUFBVCxhQUFBLEdBQUFDLENBQUE7RUFBQSxPQUFBZ0IsU0FBQSxpQkFBR1AsT0FBTztJQUFBO0lBQUFWLGFBQUEsR0FBQVMsQ0FBQTs7Ozs7Ozs7Ozs7OztVQUV2RCxxQkFBTUgsdUJBQUEsQ0FBQVksb0JBQW9CLENBQUNDLElBQUksQ0FBQ0MsVUFBVSxDQUFDSixPQUFPLENBQUM7Ozs7O1VBQXJFSyxlQUFlLEdBQUdhLEVBQUEsQ0FBQVgsSUFBQSxFQUFtRDtVQUFBO1VBQUF2QixhQUFBLEdBQUFDLENBQUE7VUFFM0UsSUFBSSxDQUFDb0IsZUFBZSxDQUFDRyxPQUFPLEVBQUU7WUFBQTtZQUFBeEIsYUFBQSxHQUFBeUIsQ0FBQTtZQUFBekIsYUFBQSxHQUFBQyxDQUFBO1lBQ3RCeUIsS0FBSyxHQUFHLElBQUlDLEtBQUssQ0FBQyxxRUFBcUUsQ0FBUTtZQUFDO1lBQUEzQixhQUFBLEdBQUFDLENBQUE7WUFDdEd5QixLQUFLLENBQUNFLFVBQVUsR0FBRyxHQUFHO1lBQUM7WUFBQTVCLGFBQUEsR0FBQUMsQ0FBQTtZQUN2QnlCLEtBQUssQ0FBQ0csT0FBTyxHQUFHUixlQUFlLENBQUNRLE9BQU87WUFBQztZQUFBN0IsYUFBQSxHQUFBQyxDQUFBO1lBQ3hDLE1BQU15QixLQUFLO1VBQ2IsQ0FBQztVQUFBO1VBQUE7WUFBQTFCLGFBQUEsR0FBQXlCLENBQUE7VUFBQTtVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBRUs2QixTQUFTLEdBQUdDLElBQUksQ0FBQ0MsR0FBRyxFQUFFO1VBQUM7VUFBQWhDLGFBQUEsR0FBQUMsQ0FBQTs7Ozs7Ozs7O1VBR25CMkQsWUFBWSxHQUFLLElBQUlDLEdBQUcsQ0FBQzdDLE9BQU8sQ0FBQzhDLEdBQUcsQ0FBQyxDQUFBRixZQUF6QjtVQUEwQjtVQUFBNUQsYUFBQSxHQUFBQyxDQUFBO1VBQ3hDa0MsS0FBSyxHQUFHeUIsWUFBWSxDQUFDRyxHQUFHLENBQUMsT0FBTyxDQUFDO1VBQUM7VUFBQS9ELGFBQUEsR0FBQUMsQ0FBQTtVQUNsQ21DLEtBQUssR0FBR3dCLFlBQVksQ0FBQ0csR0FBRyxDQUFDLE9BQU8sQ0FBQztVQUFDO1VBQUEvRCxhQUFBLEdBQUFDLENBQUE7O1VBRXBDO1VBQUEsQ0FBQUQsYUFBQSxHQUFBeUIsQ0FBQSxZQUFDVSxLQUFLO1VBQUE7VUFBQSxDQUFBbkMsYUFBQSxHQUFBeUIsQ0FBQSxXQUFJLENBQUNXLEtBQUssSUFBaEI7WUFBQTtZQUFBcEMsYUFBQSxHQUFBeUIsQ0FBQTtZQUFBekIsYUFBQSxHQUFBQyxDQUFBO1lBQUE7VUFBQSxDQUFnQjtVQUFBO1VBQUE7WUFBQUQsYUFBQSxHQUFBeUIsQ0FBQTtVQUFBO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7VUFDbEIscUJBQU1PLGFBQWEsRUFBRTs7Ozs7VUFBckIwQixFQUFBLENBQUFYLElBQUEsRUFBcUI7VUFBQztVQUFBdkIsYUFBQSxHQUFBQyxDQUFBO1VBQ3RCLE1BQU0sSUFBSTBCLEtBQUssQ0FBQywrQkFBK0IsQ0FBQzs7Ozs7VUFJeEIscUJBQU14QixRQUFBLENBQUFrQyxPQUFNLENBQUNDLGlCQUFpQixDQUFDQyxVQUFVLENBQUM7WUFDbEVDLEtBQUssRUFBRTtjQUNMTCxLQUFLLEVBQUVBOztXQUVWLENBQUM7Ozs7O1VBSklHLGlCQUFpQixHQUFHSixFQUFBLENBQUFYLElBQUEsRUFJeEI7VUFBQTtVQUFBdkIsYUFBQSxHQUFBQyxDQUFBO1VBR0l3QyxXQUFXLEdBQUdWLElBQUksQ0FBQ0MsR0FBRyxFQUFFLEdBQUdGLFNBQVM7VUFBQztVQUFBOUIsYUFBQSxHQUFBQyxDQUFBO1VBQ3JDK0QsZ0JBQUEsR0FBaUJyQixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUVyQyxpQkFBaUIsR0FBR2tDLFdBQVcsQ0FBQztVQUFDO1VBQUF6QyxhQUFBLEdBQUFDLENBQUE7Z0JBQ2hFK0QsZ0JBQWMsR0FBRyxDQUFDLEdBQWxCO1lBQUE7WUFBQWhFLGFBQUEsR0FBQXlCLENBQUE7WUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtZQUFBO1VBQUEsQ0FBa0I7VUFBQTtVQUFBO1lBQUFELGFBQUEsR0FBQXlCLENBQUE7VUFBQTtVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBQ3BCLHFCQUFNLElBQUlTLE9BQU8sQ0FBQyxVQUFBQyxPQUFPO1lBQUE7WUFBQVgsYUFBQSxHQUFBUyxDQUFBO1lBQUFULGFBQUEsR0FBQUMsQ0FBQTtZQUFJLE9BQUFXLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFcUQsZ0JBQWMsQ0FBQztVQUFuQyxDQUFtQyxDQUFDOzs7OztVQUFqRTlCLEVBQUEsQ0FBQVgsSUFBQSxFQUFpRTtVQUFDO1VBQUF2QixhQUFBLEdBQUFDLENBQUE7Ozs7OztVQUdwRSxJQUFJLENBQUNxQyxpQkFBaUIsRUFBRTtZQUFBO1lBQUF0QyxhQUFBLEdBQUF5QixDQUFBO1lBQUF6QixhQUFBLEdBQUFDLENBQUE7WUFDdEIsTUFBTSxJQUFJMEIsS0FBSyxDQUFDLDZCQUE2QixDQUFDO1VBQ2hELENBQUM7VUFBQTtVQUFBO1lBQUEzQixhQUFBLEdBQUF5QixDQUFBO1VBQUE7VUFFRDtVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBQ0EsSUFBSXFDLGlCQUFpQixDQUFDTyxPQUFPLEdBQUcsSUFBSWQsSUFBSSxFQUFFLEVBQUU7WUFBQTtZQUFBL0IsYUFBQSxHQUFBeUIsQ0FBQTtZQUFBekIsYUFBQSxHQUFBQyxDQUFBO1lBQzFDLE1BQU0sSUFBSTBCLEtBQUssQ0FBQyxpQ0FBaUMsQ0FBQztVQUNwRCxDQUFDO1VBQUE7VUFBQTtZQUFBM0IsYUFBQSxHQUFBeUIsQ0FBQTtVQUFBO1VBRUQ7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNBLElBQUlxQyxpQkFBaUIsQ0FBQ1MsVUFBVSxLQUFLWCxLQUFLLEVBQUU7WUFBQTtZQUFBcEMsYUFBQSxHQUFBeUIsQ0FBQTtZQUFBekIsYUFBQSxHQUFBQyxDQUFBO1lBQzFDLE1BQU0sSUFBSTBCLEtBQUssQ0FBQyw2QkFBNkIsQ0FBQztVQUNoRCxDQUFDO1VBQUE7VUFBQTtZQUFBM0IsYUFBQSxHQUFBeUIsQ0FBQTtVQUFBO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7VUFHWSxxQkFBTUUsUUFBQSxDQUFBa0MsT0FBTSxDQUFDVyxJQUFJLENBQUNULFVBQVUsQ0FBQztZQUN4Q0MsS0FBSyxFQUFFO2NBQUVKLEtBQUssRUFBRUE7WUFBSztXQUN0QixDQUFDOzs7OztVQUZJWSxJQUFJLEdBQUdkLEVBQUEsQ0FBQVgsSUFBQSxFQUVYO1VBQUE7VUFBQXZCLGFBQUEsR0FBQUMsQ0FBQTtVQUVGLElBQUksQ0FBQytDLElBQUksRUFBRTtZQUFBO1lBQUFoRCxhQUFBLEdBQUF5QixDQUFBO1lBQUF6QixhQUFBLEdBQUFDLENBQUE7WUFDVCxNQUFNLElBQUkwQixLQUFLLENBQUMsaUJBQWlCLENBQUM7VUFDcEMsQ0FBQztVQUFBO1VBQUE7WUFBQTNCLGFBQUEsR0FBQXlCLENBQUE7VUFBQTtVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBRUQsc0JBQU9GLFFBQUEsQ0FBQW1ELFlBQVksQ0FBQ2pCLElBQUksQ0FBQztZQUN2QmtCLE9BQU8sRUFBRSxJQUFJO1lBQ2JDLElBQUksRUFBRTtjQUNKYSxLQUFLLEVBQUUsSUFBSTtjQUNYN0IsS0FBSyxFQUFFQSxLQUFLO2NBQ1o4QixlQUFlLEVBQUUsQ0FBQyxDQUFDbEIsSUFBSSxDQUFDQzs7V0FFM0IsQ0FBQzs7Ozs7Ozs7VUFJSVIsV0FBVyxHQUFHVixJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO1VBQUM7VUFBQTlCLGFBQUEsR0FBQUMsQ0FBQTtVQUNyQ2tFLGdCQUFBLEdBQWlCeEIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxFQUFFckMsaUJBQWlCLEdBQUdrQyxXQUFXLENBQUM7VUFBQztVQUFBekMsYUFBQSxHQUFBQyxDQUFBO2dCQUNoRWtFLGdCQUFjLEdBQUcsQ0FBQyxHQUFsQjtZQUFBO1lBQUFuRSxhQUFBLEdBQUF5QixDQUFBO1lBQUF6QixhQUFBLEdBQUFDLENBQUE7WUFBQTtVQUFBLENBQWtCO1VBQUE7VUFBQTtZQUFBRCxhQUFBLEdBQUF5QixDQUFBO1VBQUE7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNwQixxQkFBTSxJQUFJUyxPQUFPLENBQUMsVUFBQUMsT0FBTztZQUFBO1lBQUFYLGFBQUEsR0FBQVMsQ0FBQTtZQUFBVCxhQUFBLEdBQUFDLENBQUE7WUFBSSxPQUFBVyxVQUFVLENBQUNELE9BQU8sRUFBRXdELGdCQUFjLENBQUM7VUFBbkMsQ0FBbUMsQ0FBQzs7Ozs7VUFBakVqQyxFQUFBLENBQUFYLElBQUEsRUFBaUU7VUFBQztVQUFBdkIsYUFBQSxHQUFBQyxDQUFBOzs7Ozs7VUFFcEUsTUFBTW1FLE9BQUs7Ozs7Ozs7OztDQUVkLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=