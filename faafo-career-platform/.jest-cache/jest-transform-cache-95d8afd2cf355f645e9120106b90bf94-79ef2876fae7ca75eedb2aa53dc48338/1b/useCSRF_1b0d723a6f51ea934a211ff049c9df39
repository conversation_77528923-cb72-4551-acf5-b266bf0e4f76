119eb85d852ea1492235e0be17f0130a
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockGetHeaders = exports.useCSRF = void 0;
// Manual mock for useCSRF hook
var mockGetHeaders = jest.fn(function (additionalHeaders) {
    if (additionalHeaders === void 0) { additionalHeaders = {}; }
    return (__assign({ 'Content-Type': 'application/json', 'X-CSRF-Token': 'test-csrf-token' }, additionalHeaders));
});
exports.mockGetHeaders = mockGetHeaders;
exports.useCSRF = jest.fn(function () { return ({
    csrfToken: 'test-csrf-token',
    isLoading: false,
    error: null,
    getHeaders: mockGetHeaders,
}); });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9ob29rcy9fX21vY2tzX18vdXNlQ1NSRi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFBLCtCQUErQjtBQUMvQixJQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLFVBQUMsaUJBQXNCO0lBQXRCLGtDQUFBLEVBQUEsc0JBQXNCO0lBQUssT0FBQSxZQUN6RCxjQUFjLEVBQUUsa0JBQWtCLEVBQ2xDLGNBQWMsRUFBRSxpQkFBaUIsSUFDOUIsaUJBQWlCLEVBQ3BCO0FBSnlELENBSXpELENBQUMsQ0FBQztBQVVLLHdDQUFjO0FBUlYsUUFBQSxPQUFPLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxjQUFNLE9BQUEsQ0FBQztJQUNwQyxTQUFTLEVBQUUsaUJBQWlCO0lBQzVCLFNBQVMsRUFBRSxLQUFLO0lBQ2hCLEtBQUssRUFBRSxJQUFJO0lBQ1gsVUFBVSxFQUFFLGNBQWM7Q0FDM0IsQ0FBQyxFQUxtQyxDQUtuQyxDQUFDLENBQUMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9ob29rcy9fX21vY2tzX18vdXNlQ1NSRi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBNYW51YWwgbW9jayBmb3IgdXNlQ1NSRiBob29rXG5jb25zdCBtb2NrR2V0SGVhZGVycyA9IGplc3QuZm4oKGFkZGl0aW9uYWxIZWFkZXJzID0ge30pID0+ICh7XG4gICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICdYLUNTUkYtVG9rZW4nOiAndGVzdC1jc3JmLXRva2VuJyxcbiAgLi4uYWRkaXRpb25hbEhlYWRlcnMsXG59KSk7XG5cbmV4cG9ydCBjb25zdCB1c2VDU1JGID0gamVzdC5mbigoKSA9PiAoe1xuICBjc3JmVG9rZW46ICd0ZXN0LWNzcmYtdG9rZW4nLFxuICBpc0xvYWRpbmc6IGZhbHNlLFxuICBlcnJvcjogbnVsbCxcbiAgZ2V0SGVhZGVyczogbW9ja0dldEhlYWRlcnMsXG59KSk7XG5cbi8vIEV4cG9ydCB0aGUgbW9jayBmdW5jdGlvbiBmb3IgdGVzdCBhY2Nlc3NcbmV4cG9ydCB7IG1vY2tHZXRIZWFkZXJzIH07XG4iXSwidmVyc2lvbiI6M30=