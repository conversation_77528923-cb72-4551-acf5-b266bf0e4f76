{"version": 3, "names": ["server_1", "cov_w045g5qvg", "s", "require", "next_1", "auth_1", "prisma_1", "__importDefault", "unified_api_error_handler_1", "csrf_1", "rateLimit_1", "exports", "POST", "withUnifiedErrorHandling", "request_1", "_a", "f", "__awaiter", "request", "_b", "params", "withCSRFProtection", "withRateLimit", "windowMs", "maxRequests", "postId", "sent", "getServerSession", "authOptions", "session", "b", "user", "id", "error", "Error", "statusCode", "json", "content", "length", "default", "forumPost", "findUnique", "where", "post", "forumReply", "create", "data", "trim", "authorId", "include", "author", "select", "email", "name", "profile", "profilePictureUrl", "forumReputation", "forumPostCount", "forumReplyCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progressLevel", "newReply", "NextResponse", "success"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/replies/route.ts"], "sourcesContent": ["import { NextResponse, NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withRateLimit } from '@/lib/rateLimit';\n\ninterface ForumReplyCreateResponse {\n  id: string;\n  content: string;\n  authorId: string;\n  postId: string;\n  createdAt: Date;\n  updatedAt: Date;\n  author: any;\n}\n// POST handler to create a reply to a forum post\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest, { params }: { params: Promise<{ postId: string }> }) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 20 },\n      async () => {\n        const { postId } = await params;\n        const session = await getServerSession(authOptions);\n\n        if (!session || !session.user || !session.user.id) {\n          const error = new Error('Unauthorized') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const { content } = await request.json();\n\n        if (!content) {\n          const error = new Error('Content is required') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        if (content.length > 2000) {\n          const error = new Error('Reply content must be 2000 characters or less') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Check if the post exists\n        const post = await prisma.forumPost.findUnique({\n          where: { id: postId },\n        });\n\n        if (!post) {\n          const error = new Error('Post not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        const newReply = await prisma.forumReply.create({\n          data: {\n            content: content.trim(),\n            authorId: session.user.id,\n            postId: postId,\n          },\n          include: {\n            author: {\n              select: {\n                id: true,\n                email: true,\n                name: true,\n                profile: {\n                  select: {\n                    profilePictureUrl: true,\n                    forumReputation: true,\n                    forumPostCount: true,\n                    forumReplyCount: true,\n                    currentCareerPath: true,\n                    progressLevel: true,\n                  },\n                },\n              },\n            },\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: newReply\n        });\n      }\n    );\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAG,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AACA,IAAAK,2BAAA;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAM,MAAA;AAAA;AAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAO,WAAA;AAAA;AAAA,CAAAT,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAWA;AAAA;AAAAF,aAAA,GAAAC,CAAA;AACaS,OAAA,CAAAC,IAAI,GAAG,IAAAJ,2BAAA,CAAAK,wBAAwB,EAAC,UAAAC,SAAA,EAAAC,EAAA;EAAA;EAAAd,aAAA,GAAAe,CAAA;EAAAf,aAAA,GAAAC,CAAA;EAAA,OAAAe,SAAA,UAAAH,SAAA,EAAAC,EAAA,qBAAOG,OAAoB,EAAEC,EAAmD;IAAA;IAAAlB,aAAA,GAAAe,CAAA;QAAjDI,MAAM;IAAA;IAAA,CAAAnB,aAAA,GAAAC,CAAA,QAAAiB,EAAA,CAAAC,MAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;;;;;MAChF,sBAAO,IAAAO,MAAA,CAAAY,kBAAkB,EAACH,OAAO,EAAE;QAAA;QAAAjB,aAAA,GAAAe,CAAA;QAAAf,aAAA,GAAAC,CAAA;QAAA,OAAAe,SAAA;UAAA;UAAAhB,aAAA,GAAAe,CAAA;UAAAf,aAAA,GAAAC,CAAA;;;;;YACjC,sBAAO,IAAAQ,WAAA,CAAAY,aAAa,EAClBJ,OAAO,EACP;cAAEK,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;cAAEC,WAAW,EAAE;YAAE,CAAE,EAC7C;cAAA;cAAAvB,aAAA,GAAAe,CAAA;cAAAf,aAAA,GAAAC,CAAA;cAAA,OAAAe,SAAA;gBAAA;gBAAAhB,aAAA,GAAAe,CAAA;;;;;;;;;;;;;sBACqB,qBAAMI,MAAM;;;;;sBAAvBK,MAAM,GAAKV,EAAA,CAAAW,IAAA,EAAY,CAAAD,MAAjB;sBAAA;sBAAAxB,aAAA,GAAAC,CAAA;sBACE,qBAAM,IAAAE,MAAA,CAAAuB,gBAAgB,EAACtB,MAAA,CAAAuB,WAAW,CAAC;;;;;sBAA7CC,OAAO,GAAGd,EAAA,CAAAW,IAAA,EAAmC;sBAAA;sBAAAzB,aAAA,GAAAC,CAAA;sBAEnD;sBAAI;sBAAA,CAAAD,aAAA,GAAA6B,CAAA,YAACD,OAAO;sBAAA;sBAAA,CAAA5B,aAAA,GAAA6B,CAAA,WAAI,CAACD,OAAO,CAACE,IAAI;sBAAA;sBAAA,CAAA9B,aAAA,GAAA6B,CAAA,WAAI,CAACD,OAAO,CAACE,IAAI,CAACC,EAAE,GAAE;wBAAA;wBAAA/B,aAAA,GAAA6B,CAAA;wBAAA7B,aAAA,GAAAC,CAAA;wBAC3C+B,KAAK,GAAG,IAAIC,KAAK,CAAC,cAAc,CAAQ;wBAAC;wBAAAjC,aAAA,GAAAC,CAAA;wBAC/C+B,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAAlC,aAAA,GAAAC,CAAA;wBACvB,MAAM+B,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAhC,aAAA,GAAA6B,CAAA;sBAAA;sBAAA7B,aAAA,GAAAC,CAAA;sBAEmB,qBAAMgB,OAAO,CAACkB,IAAI,EAAE;;;;;sBAAhCC,OAAO,GAAKtB,EAAA,CAAAW,IAAA,EAAoB,CAAAW,OAAzB;sBAAA;sBAAApC,aAAA,GAAAC,CAAA;sBAEf,IAAI,CAACmC,OAAO,EAAE;wBAAA;wBAAApC,aAAA,GAAA6B,CAAA;wBAAA7B,aAAA,GAAAC,CAAA;wBACN+B,KAAK,GAAG,IAAIC,KAAK,CAAC,qBAAqB,CAAQ;wBAAC;wBAAAjC,aAAA,GAAAC,CAAA;wBACtD+B,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAAlC,aAAA,GAAAC,CAAA;wBACvB,MAAM+B,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAhC,aAAA,GAAA6B,CAAA;sBAAA;sBAAA7B,aAAA,GAAAC,CAAA;sBAED,IAAImC,OAAO,CAACC,MAAM,GAAG,IAAI,EAAE;wBAAA;wBAAArC,aAAA,GAAA6B,CAAA;wBAAA7B,aAAA,GAAAC,CAAA;wBACnB+B,KAAK,GAAG,IAAIC,KAAK,CAAC,+CAA+C,CAAQ;wBAAC;wBAAAjC,aAAA,GAAAC,CAAA;wBAChF+B,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAAlC,aAAA,GAAAC,CAAA;wBACvB,MAAM+B,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAhC,aAAA,GAAA6B,CAAA;sBAAA;sBAAA7B,aAAA,GAAAC,CAAA;sBAGY,qBAAMI,QAAA,CAAAiC,OAAM,CAACC,SAAS,CAACC,UAAU,CAAC;wBAC7CC,KAAK,EAAE;0BAAEV,EAAE,EAAEP;wBAAM;uBACpB,CAAC;;;;;sBAFIkB,IAAI,GAAG5B,EAAA,CAAAW,IAAA,EAEX;sBAAA;sBAAAzB,aAAA,GAAAC,CAAA;sBAEF,IAAI,CAACyC,IAAI,EAAE;wBAAA;wBAAA1C,aAAA,GAAA6B,CAAA;wBAAA7B,aAAA,GAAAC,CAAA;wBACH+B,KAAK,GAAG,IAAIC,KAAK,CAAC,gBAAgB,CAAQ;wBAAC;wBAAAjC,aAAA,GAAAC,CAAA;wBACjD+B,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAAlC,aAAA,GAAAC,CAAA;wBACvB,MAAM+B,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAhC,aAAA,GAAA6B,CAAA;sBAAA;sBAAA7B,aAAA,GAAAC,CAAA;sBAEgB,qBAAMI,QAAA,CAAAiC,OAAM,CAACK,UAAU,CAACC,MAAM,CAAC;wBAC9CC,IAAI,EAAE;0BACJT,OAAO,EAAEA,OAAO,CAACU,IAAI,EAAE;0BACvBC,QAAQ,EAAEnB,OAAO,CAACE,IAAI,CAACC,EAAE;0BACzBP,MAAM,EAAEA;yBACT;wBACDwB,OAAO,EAAE;0BACPC,MAAM,EAAE;4BACNC,MAAM,EAAE;8BACNnB,EAAE,EAAE,IAAI;8BACRoB,KAAK,EAAE,IAAI;8BACXC,IAAI,EAAE,IAAI;8BACVC,OAAO,EAAE;gCACPH,MAAM,EAAE;kCACNI,iBAAiB,EAAE,IAAI;kCACvBC,eAAe,EAAE,IAAI;kCACrBC,cAAc,EAAE,IAAI;kCACpBC,eAAe,EAAE,IAAI;kCACrBC,iBAAiB,EAAE,IAAI;kCACvBC,aAAa,EAAE;;;;;;uBAM1B,CAAC;;;;;sBAzBIC,QAAQ,GAAG9C,EAAA,CAAAW,IAAA,EAyBf;sBAAA;sBAAAzB,aAAA,GAAAC,CAAA;sBAEF,sBAAOF,QAAA,CAAA8D,YAAY,CAAC1B,IAAI,CAAC;wBACvB2B,OAAO,EAAE,IAAI;wBACbjB,IAAI,EAAEe;uBACP,CAAC;;;;aACH,CACF;;;OACF,CAAC;;;CACH,CAAC", "ignoreList": []}