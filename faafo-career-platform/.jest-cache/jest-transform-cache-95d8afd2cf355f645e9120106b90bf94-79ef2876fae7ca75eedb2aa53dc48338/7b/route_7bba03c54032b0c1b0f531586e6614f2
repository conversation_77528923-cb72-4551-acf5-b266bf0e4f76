1d25ba80ba746a2b13349aabe7e466b7
"use strict";

/* istanbul ignore next */
function cov_w045g5qvg() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/replies/route.ts";
  var hash = "56e84e6ab49f1a4fd2bf384571458f5afdd5d799";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/replies/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 22
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 13
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "73": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "74": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "75": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 76
        }
      },
      "76": {
        start: {
          line: 48,
          column: 13
        },
        end: {
          line: 48,
          column: 34
        }
      },
      "77": {
        start: {
          line: 49,
          column: 18
        },
        end: {
          line: 49,
          column: 44
        }
      },
      "78": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 132,
          column: 7
        }
      },
      "79": {
        start: {
          line: 51,
          column: 100
        },
        end: {
          line: 132,
          column: 3
        }
      },
      "80": {
        start: {
          line: 52,
          column: 17
        },
        end: {
          line: 52,
          column: 26
        }
      },
      "81": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 131,
          column: 7
        }
      },
      "82": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 130,
          column: 20
        }
      },
      "83": {
        start: {
          line: 54,
          column: 84
        },
        end: {
          line: 130,
          column: 15
        }
      },
      "84": {
        start: {
          line: 55,
          column: 16
        },
        end: {
          line: 129,
          column: 19
        }
      },
      "85": {
        start: {
          line: 56,
          column: 20
        },
        end: {
          line: 128,
          column: 32
        }
      },
      "86": {
        start: {
          line: 56,
          column: 143
        },
        end: {
          line: 128,
          column: 27
        }
      },
      "87": {
        start: {
          line: 58,
          column: 28
        },
        end: {
          line: 127,
          column: 31
        }
      },
      "88": {
        start: {
          line: 59,
          column: 32
        },
        end: {
          line: 126,
          column: 33
        }
      },
      "89": {
        start: {
          line: 60,
          column: 44
        },
        end: {
          line: 60,
          column: 73
        }
      },
      "90": {
        start: {
          line: 62,
          column: 40
        },
        end: {
          line: 62,
          column: 68
        }
      },
      "91": {
        start: {
          line: 63,
          column: 40
        },
        end: {
          line: 63,
          column: 111
        }
      },
      "92": {
        start: {
          line: 65,
          column: 40
        },
        end: {
          line: 65,
          column: 60
        }
      },
      "93": {
        start: {
          line: 66,
          column: 40
        },
        end: {
          line: 70,
          column: 41
        }
      },
      "94": {
        start: {
          line: 67,
          column: 44
        },
        end: {
          line: 67,
          column: 78
        }
      },
      "95": {
        start: {
          line: 68,
          column: 44
        },
        end: {
          line: 68,
          column: 67
        }
      },
      "96": {
        start: {
          line: 69,
          column: 44
        },
        end: {
          line: 69,
          column: 56
        }
      },
      "97": {
        start: {
          line: 71,
          column: 40
        },
        end: {
          line: 71,
          column: 77
        }
      },
      "98": {
        start: {
          line: 73,
          column: 40
        },
        end: {
          line: 73,
          column: 70
        }
      },
      "99": {
        start: {
          line: 74,
          column: 40
        },
        end: {
          line: 78,
          column: 41
        }
      },
      "100": {
        start: {
          line: 75,
          column: 44
        },
        end: {
          line: 75,
          column: 85
        }
      },
      "101": {
        start: {
          line: 76,
          column: 44
        },
        end: {
          line: 76,
          column: 67
        }
      },
      "102": {
        start: {
          line: 77,
          column: 44
        },
        end: {
          line: 77,
          column: 56
        }
      },
      "103": {
        start: {
          line: 79,
          column: 40
        },
        end: {
          line: 83,
          column: 41
        }
      },
      "104": {
        start: {
          line: 80,
          column: 44
        },
        end: {
          line: 80,
          column: 111
        }
      },
      "105": {
        start: {
          line: 81,
          column: 44
        },
        end: {
          line: 81,
          column: 67
        }
      },
      "106": {
        start: {
          line: 82,
          column: 44
        },
        end: {
          line: 82,
          column: 56
        }
      },
      "107": {
        start: {
          line: 84,
          column: 40
        },
        end: {
          line: 86,
          column: 48
        }
      },
      "108": {
        start: {
          line: 88,
          column: 40
        },
        end: {
          line: 88,
          column: 57
        }
      },
      "109": {
        start: {
          line: 89,
          column: 40
        },
        end: {
          line: 93,
          column: 41
        }
      },
      "110": {
        start: {
          line: 90,
          column: 44
        },
        end: {
          line: 90,
          column: 80
        }
      },
      "111": {
        start: {
          line: 91,
          column: 44
        },
        end: {
          line: 91,
          column: 67
        }
      },
      "112": {
        start: {
          line: 92,
          column: 44
        },
        end: {
          line: 92,
          column: 56
        }
      },
      "113": {
        start: {
          line: 94,
          column: 40
        },
        end: {
          line: 119,
          column: 48
        }
      },
      "114": {
        start: {
          line: 121,
          column: 40
        },
        end: {
          line: 121,
          column: 61
        }
      },
      "115": {
        start: {
          line: 122,
          column: 40
        },
        end: {
          line: 125,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 51,
            column: 73
          },
          end: {
            line: 51,
            column: 74
          }
        },
        loc: {
          start: {
            line: 51,
            column: 98
          },
          end: {
            line: 132,
            column: 5
          }
        },
        line: 51
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 51,
            column: 150
          },
          end: {
            line: 51,
            column: 151
          }
        },
        loc: {
          start: {
            line: 51,
            column: 173
          },
          end: {
            line: 132,
            column: 1
          }
        },
        line: 51
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 53,
            column: 29
          },
          end: {
            line: 53,
            column: 30
          }
        },
        loc: {
          start: {
            line: 53,
            column: 43
          },
          end: {
            line: 131,
            column: 5
          }
        },
        line: 53
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 54,
            column: 70
          },
          end: {
            line: 54,
            column: 71
          }
        },
        loc: {
          start: {
            line: 54,
            column: 82
          },
          end: {
            line: 130,
            column: 17
          }
        },
        line: 54
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 54,
            column: 125
          },
          end: {
            line: 54,
            column: 126
          }
        },
        loc: {
          start: {
            line: 54,
            column: 137
          },
          end: {
            line: 130,
            column: 13
          }
        },
        line: 54
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 55,
            column: 41
          },
          end: {
            line: 55,
            column: 42
          }
        },
        loc: {
          start: {
            line: 55,
            column: 55
          },
          end: {
            line: 129,
            column: 17
          }
        },
        line: 55
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 56,
            column: 129
          },
          end: {
            line: 56,
            column: 130
          }
        },
        loc: {
          start: {
            line: 56,
            column: 141
          },
          end: {
            line: 128,
            column: 29
          }
        },
        line: 56
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 56,
            column: 184
          },
          end: {
            line: 56,
            column: 185
          }
        },
        loc: {
          start: {
            line: 56,
            column: 196
          },
          end: {
            line: 128,
            column: 25
          }
        },
        line: 56
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 58,
            column: 53
          },
          end: {
            line: 58,
            column: 54
          }
        },
        loc: {
          start: {
            line: 58,
            column: 67
          },
          end: {
            line: 127,
            column: 29
          }
        },
        line: 58
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 59,
            column: 32
          },
          end: {
            line: 126,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 60,
            column: 36
          },
          end: {
            line: 60,
            column: 73
          }
        }, {
          start: {
            line: 61,
            column: 36
          },
          end: {
            line: 63,
            column: 111
          }
        }, {
          start: {
            line: 64,
            column: 36
          },
          end: {
            line: 71,
            column: 77
          }
        }, {
          start: {
            line: 72,
            column: 36
          },
          end: {
            line: 86,
            column: 48
          }
        }, {
          start: {
            line: 87,
            column: 36
          },
          end: {
            line: 119,
            column: 48
          }
        }, {
          start: {
            line: 120,
            column: 36
          },
          end: {
            line: 125,
            column: 48
          }
        }],
        line: 59
      },
      "36": {
        loc: {
          start: {
            line: 66,
            column: 40
          },
          end: {
            line: 70,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 66,
            column: 40
          },
          end: {
            line: 70,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 66
      },
      "37": {
        loc: {
          start: {
            line: 66,
            column: 44
          },
          end: {
            line: 66,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 66,
            column: 44
          },
          end: {
            line: 66,
            column: 52
          }
        }, {
          start: {
            line: 66,
            column: 56
          },
          end: {
            line: 66,
            column: 69
          }
        }, {
          start: {
            line: 66,
            column: 73
          },
          end: {
            line: 66,
            column: 89
          }
        }],
        line: 66
      },
      "38": {
        loc: {
          start: {
            line: 74,
            column: 40
          },
          end: {
            line: 78,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 40
          },
          end: {
            line: 78,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "39": {
        loc: {
          start: {
            line: 79,
            column: 40
          },
          end: {
            line: 83,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 40
          },
          end: {
            line: 83,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "40": {
        loc: {
          start: {
            line: 89,
            column: 40
          },
          end: {
            line: 93,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 40
          },
          end: {
            line: 93,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/replies/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAClC,6EAAwF;AACxF,mCAAgD;AAChD,6CAAgD;AAWhD,iDAAiD;AACpC,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,uFAAO,OAAoB,EAAE,EAAmD;QAAjD,MAAM,YAAA;;QAChF,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAC7C;;;;4CACqB,qBAAM,MAAM,EAAA;;wCAAvB,MAAM,GAAK,CAAA,SAAY,CAAA,OAAjB;wCACE,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCAEnD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;4CAC5C,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,CAAQ,CAAC;4CAC/C,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEmB,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAAhC,OAAO,GAAK,CAAA,SAAoB,CAAA,QAAzB;wCAEf,IAAI,CAAC,OAAO,EAAE,CAAC;4CACP,KAAK,GAAG,IAAI,KAAK,CAAC,qBAAqB,CAAQ,CAAC;4CACtD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAED,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;4CACpB,KAAK,GAAG,IAAI,KAAK,CAAC,+CAA+C,CAAQ,CAAC;4CAChF,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGY,qBAAM,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gDAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;6CACtB,CAAC,EAAA;;wCAFI,IAAI,GAAG,SAEX;wCAEF,IAAI,CAAC,IAAI,EAAE,CAAC;4CACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;4CACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEgB,qBAAM,gBAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gDAC9C,IAAI,EAAE;oDACJ,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;oDACvB,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;oDACzB,MAAM,EAAE,MAAM;iDACf;gDACD,OAAO,EAAE;oDACP,MAAM,EAAE;wDACN,MAAM,EAAE;4DACN,EAAE,EAAE,IAAI;4DACR,KAAK,EAAE,IAAI;4DACX,IAAI,EAAE,IAAI;4DACV,OAAO,EAAE;gEACP,MAAM,EAAE;oEACN,iBAAiB,EAAE,IAAI;oEACvB,eAAe,EAAE,IAAI;oEACrB,cAAc,EAAE,IAAI;oEACpB,eAAe,EAAE,IAAI;oEACrB,iBAAiB,EAAE,IAAI;oEACvB,aAAa,EAAE,IAAI;iEACpB;6DACF;yDACF;qDACF;iDACF;6CACF,CAAC,EAAA;;wCAzBI,QAAQ,GAAG,SAyBf;wCAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,QAAQ;6CACf,CAAC,EAAC;;;6BACJ,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/replies/route.ts"],
      sourcesContent: ["import { NextResponse, NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withRateLimit } from '@/lib/rateLimit';\n\ninterface ForumReplyCreateResponse {\n  id: string;\n  content: string;\n  authorId: string;\n  postId: string;\n  createdAt: Date;\n  updatedAt: Date;\n  author: any;\n}\n// POST handler to create a reply to a forum post\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest, { params }: { params: Promise<{ postId: string }> }) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 20 },\n      async () => {\n        const { postId } = await params;\n        const session = await getServerSession(authOptions);\n\n        if (!session || !session.user || !session.user.id) {\n          const error = new Error('Unauthorized') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const { content } = await request.json();\n\n        if (!content) {\n          const error = new Error('Content is required') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        if (content.length > 2000) {\n          const error = new Error('Reply content must be 2000 characters or less') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Check if the post exists\n        const post = await prisma.forumPost.findUnique({\n          where: { id: postId },\n        });\n\n        if (!post) {\n          const error = new Error('Post not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        const newReply = await prisma.forumReply.create({\n          data: {\n            content: content.trim(),\n            authorId: session.user.id,\n            postId: postId,\n          },\n          include: {\n            author: {\n              select: {\n                id: true,\n                email: true,\n                name: true,\n                profile: {\n                  select: {\n                    profilePictureUrl: true,\n                    forumReputation: true,\n                    forumPostCount: true,\n                    forumReplyCount: true,\n                    currentCareerPath: true,\n                    progressLevel: true,\n                  },\n                },\n              },\n            },\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: newReply\n        });\n      }\n    );\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "56e84e6ab49f1a4fd2bf384571458f5afdd5d799"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_w045g5qvg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_w045g5qvg();
var __awaiter =
/* istanbul ignore next */
(cov_w045g5qvg().s[0]++,
/* istanbul ignore next */
(cov_w045g5qvg().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_w045g5qvg().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_w045g5qvg().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_w045g5qvg().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_w045g5qvg().f[1]++;
    cov_w045g5qvg().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_w045g5qvg().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_w045g5qvg().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_w045g5qvg().f[2]++;
      cov_w045g5qvg().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_w045g5qvg().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_w045g5qvg().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_w045g5qvg().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_w045g5qvg().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_w045g5qvg().f[4]++;
      cov_w045g5qvg().s[4]++;
      try {
        /* istanbul ignore next */
        cov_w045g5qvg().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_w045g5qvg().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_w045g5qvg().f[5]++;
      cov_w045g5qvg().s[7]++;
      try {
        /* istanbul ignore next */
        cov_w045g5qvg().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_w045g5qvg().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_w045g5qvg().f[6]++;
      cov_w045g5qvg().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_w045g5qvg().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_w045g5qvg().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_w045g5qvg().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_w045g5qvg().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_w045g5qvg().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_w045g5qvg().s[12]++,
/* istanbul ignore next */
(cov_w045g5qvg().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_w045g5qvg().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_w045g5qvg().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_w045g5qvg().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_w045g5qvg().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_w045g5qvg().f[8]++;
        cov_w045g5qvg().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_w045g5qvg().b[6][0]++;
          cov_w045g5qvg().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_w045g5qvg().b[6][1]++;
        }
        cov_w045g5qvg().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_w045g5qvg().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_w045g5qvg().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_w045g5qvg().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_w045g5qvg().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_w045g5qvg().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_w045g5qvg().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_w045g5qvg().f[9]++;
    cov_w045g5qvg().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_w045g5qvg().f[10]++;
    cov_w045g5qvg().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_w045g5qvg().f[11]++;
      cov_w045g5qvg().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_w045g5qvg().f[12]++;
    cov_w045g5qvg().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_w045g5qvg().b[9][0]++;
      cov_w045g5qvg().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_w045g5qvg().b[9][1]++;
    }
    cov_w045g5qvg().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_w045g5qvg().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_w045g5qvg().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_w045g5qvg().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_w045g5qvg().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_w045g5qvg().s[25]++;
      try {
        /* istanbul ignore next */
        cov_w045g5qvg().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_w045g5qvg().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_w045g5qvg().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_w045g5qvg().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_w045g5qvg().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_w045g5qvg().b[15][0]++,
        /* istanbul ignore next */
        (cov_w045g5qvg().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_w045g5qvg().b[16][1]++,
        /* istanbul ignore next */
        (cov_w045g5qvg().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_w045g5qvg().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_w045g5qvg().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_w045g5qvg().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_w045g5qvg().b[12][0]++;
          cov_w045g5qvg().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_w045g5qvg().b[12][1]++;
        }
        cov_w045g5qvg().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_w045g5qvg().b[18][0]++;
          cov_w045g5qvg().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_w045g5qvg().b[18][1]++;
        }
        cov_w045g5qvg().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_w045g5qvg().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_w045g5qvg().b[19][1]++;
            cov_w045g5qvg().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_w045g5qvg().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_w045g5qvg().b[19][2]++;
            cov_w045g5qvg().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_w045g5qvg().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_w045g5qvg().b[19][3]++;
            cov_w045g5qvg().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_w045g5qvg().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_w045g5qvg().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_w045g5qvg().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_w045g5qvg().b[19][4]++;
            cov_w045g5qvg().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_w045g5qvg().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_w045g5qvg().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_w045g5qvg().b[19][5]++;
            cov_w045g5qvg().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_w045g5qvg().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_w045g5qvg().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_w045g5qvg().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_w045g5qvg().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_w045g5qvg().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_w045g5qvg().b[20][0]++;
              cov_w045g5qvg().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_w045g5qvg().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_w045g5qvg().b[20][1]++;
            }
            cov_w045g5qvg().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_w045g5qvg().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_w045g5qvg().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_w045g5qvg().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_w045g5qvg().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_w045g5qvg().b[23][0]++;
              cov_w045g5qvg().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_w045g5qvg().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_w045g5qvg().b[23][1]++;
            }
            cov_w045g5qvg().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_w045g5qvg().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_w045g5qvg().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_w045g5qvg().b[25][0]++;
              cov_w045g5qvg().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_w045g5qvg().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_w045g5qvg().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_w045g5qvg().b[25][1]++;
            }
            cov_w045g5qvg().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_w045g5qvg().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_w045g5qvg().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_w045g5qvg().b[27][0]++;
              cov_w045g5qvg().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_w045g5qvg().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_w045g5qvg().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_w045g5qvg().b[27][1]++;
            }
            cov_w045g5qvg().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_w045g5qvg().b[29][0]++;
              cov_w045g5qvg().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_w045g5qvg().b[29][1]++;
            }
            cov_w045g5qvg().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_w045g5qvg().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_w045g5qvg().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_w045g5qvg().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_w045g5qvg().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_w045g5qvg().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_w045g5qvg().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_w045g5qvg().b[30][0]++;
      cov_w045g5qvg().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_w045g5qvg().b[30][1]++;
    }
    cov_w045g5qvg().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_w045g5qvg().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_w045g5qvg().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_w045g5qvg().s[67]++,
/* istanbul ignore next */
(cov_w045g5qvg().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_w045g5qvg().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_w045g5qvg().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_w045g5qvg().f[13]++;
  cov_w045g5qvg().s[68]++;
  return /* istanbul ignore next */(cov_w045g5qvg().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_w045g5qvg().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_w045g5qvg().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_w045g5qvg().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_w045g5qvg().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_w045g5qvg().s[70]++;
exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_w045g5qvg().s[71]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_w045g5qvg().s[72]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_w045g5qvg().s[73]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_w045g5qvg().s[74]++, __importDefault(require("@/lib/prisma")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_w045g5qvg().s[75]++, require("@/lib/unified-api-error-handler"));
var csrf_1 =
/* istanbul ignore next */
(cov_w045g5qvg().s[76]++, require("@/lib/csrf"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_w045g5qvg().s[77]++, require("@/lib/rateLimit"));
// POST handler to create a reply to a forum post
/* istanbul ignore next */
cov_w045g5qvg().s[78]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_w045g5qvg().f[14]++;
  cov_w045g5qvg().s[79]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_w045g5qvg().f[15]++;
    var params =
    /* istanbul ignore next */
    (cov_w045g5qvg().s[80]++, _b.params);
    /* istanbul ignore next */
    cov_w045g5qvg().s[81]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_w045g5qvg().f[16]++;
      cov_w045g5qvg().s[82]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_w045g5qvg().f[17]++;
        cov_w045g5qvg().s[83]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_w045g5qvg().f[18]++;
          cov_w045g5qvg().s[84]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_w045g5qvg().f[19]++;
            cov_w045g5qvg().s[85]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 20
            }, function () {
              /* istanbul ignore next */
              cov_w045g5qvg().f[20]++;
              cov_w045g5qvg().s[86]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_w045g5qvg().f[21]++;
                var postId, session, error, content, error, error, post, error, newReply;
                /* istanbul ignore next */
                cov_w045g5qvg().s[87]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_w045g5qvg().f[22]++;
                  cov_w045g5qvg().s[88]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_w045g5qvg().b[35][0]++;
                      cov_w045g5qvg().s[89]++;
                      return [4 /*yield*/, params];
                    case 1:
                      /* istanbul ignore next */
                      cov_w045g5qvg().b[35][1]++;
                      cov_w045g5qvg().s[90]++;
                      postId = _a.sent().postId;
                      /* istanbul ignore next */
                      cov_w045g5qvg().s[91]++;
                      return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
                    case 2:
                      /* istanbul ignore next */
                      cov_w045g5qvg().b[35][2]++;
                      cov_w045g5qvg().s[92]++;
                      session = _a.sent();
                      /* istanbul ignore next */
                      cov_w045g5qvg().s[93]++;
                      if (
                      /* istanbul ignore next */
                      (cov_w045g5qvg().b[37][0]++, !session) ||
                      /* istanbul ignore next */
                      (cov_w045g5qvg().b[37][1]++, !session.user) ||
                      /* istanbul ignore next */
                      (cov_w045g5qvg().b[37][2]++, !session.user.id)) {
                        /* istanbul ignore next */
                        cov_w045g5qvg().b[36][0]++;
                        cov_w045g5qvg().s[94]++;
                        error = new Error('Unauthorized');
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[95]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[96]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_w045g5qvg().b[36][1]++;
                      }
                      cov_w045g5qvg().s[97]++;
                      return [4 /*yield*/, request.json()];
                    case 3:
                      /* istanbul ignore next */
                      cov_w045g5qvg().b[35][3]++;
                      cov_w045g5qvg().s[98]++;
                      content = _a.sent().content;
                      /* istanbul ignore next */
                      cov_w045g5qvg().s[99]++;
                      if (!content) {
                        /* istanbul ignore next */
                        cov_w045g5qvg().b[38][0]++;
                        cov_w045g5qvg().s[100]++;
                        error = new Error('Content is required');
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[101]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[102]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_w045g5qvg().b[38][1]++;
                      }
                      cov_w045g5qvg().s[103]++;
                      if (content.length > 2000) {
                        /* istanbul ignore next */
                        cov_w045g5qvg().b[39][0]++;
                        cov_w045g5qvg().s[104]++;
                        error = new Error('Reply content must be 2000 characters or less');
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[105]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[106]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_w045g5qvg().b[39][1]++;
                      }
                      cov_w045g5qvg().s[107]++;
                      return [4 /*yield*/, prisma_1.default.forumPost.findUnique({
                        where: {
                          id: postId
                        }
                      })];
                    case 4:
                      /* istanbul ignore next */
                      cov_w045g5qvg().b[35][4]++;
                      cov_w045g5qvg().s[108]++;
                      post = _a.sent();
                      /* istanbul ignore next */
                      cov_w045g5qvg().s[109]++;
                      if (!post) {
                        /* istanbul ignore next */
                        cov_w045g5qvg().b[40][0]++;
                        cov_w045g5qvg().s[110]++;
                        error = new Error('Post not found');
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[111]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_w045g5qvg().s[112]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_w045g5qvg().b[40][1]++;
                      }
                      cov_w045g5qvg().s[113]++;
                      return [4 /*yield*/, prisma_1.default.forumReply.create({
                        data: {
                          content: content.trim(),
                          authorId: session.user.id,
                          postId: postId
                        },
                        include: {
                          author: {
                            select: {
                              id: true,
                              email: true,
                              name: true,
                              profile: {
                                select: {
                                  profilePictureUrl: true,
                                  forumReputation: true,
                                  forumPostCount: true,
                                  forumReplyCount: true,
                                  currentCareerPath: true,
                                  progressLevel: true
                                }
                              }
                            }
                          }
                        }
                      })];
                    case 5:
                      /* istanbul ignore next */
                      cov_w045g5qvg().b[35][5]++;
                      cov_w045g5qvg().s[114]++;
                      newReply = _a.sent();
                      /* istanbul ignore next */
                      cov_w045g5qvg().s[115]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: newReply
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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