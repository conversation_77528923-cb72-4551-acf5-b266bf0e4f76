{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/accessibility-responsive.test.tsx", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,CAAC,IAAI,CAAC,mCAAmC,EAAE,cAAM,OAAA,CAAC;IACpD,wBAAwB,EAAE,cAAM,OAAA,uBAC3B,yBAAyB,KAC5B,eAAe,EAAE,KAAK,EACtB,mBAAmB,EAAE,KAAK,EAC1B,eAAe,EAAE,KAAK,IACtB,EAL8B,CAK9B;IACF,iBAAiB,EAAE,cAAM,OAAA,CAAC;QACxB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,gBAAgB,EAAE,cAAM,OAAA,eAAK,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAA3E,CAA2E;KACpG,CAAC,EAHuB,CAGvB;IACF,oBAAoB,EAAE,cAAM,OAAA,CAAC;QAC3B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,aAAa,EAAE,UAAC,IAAY,EAAE,KAAc,EAAE,WAAoB,IAAK,OAAA,CAAC;YACtE,EAAE,EAAE,IAAI;YACR,IAAI,MAAA;YACJ,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,CAAC,CAAC,KAAK;YACvB,kBAAkB,EAAE,KAAK,CAAC,CAAC,CAAC,UAAG,IAAI,WAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAG,IAAI,iBAAc,CAAC,CAAC,CAAC,SAAS;SAC9F,CAAC,EANqE,CAMrE;KACH,CAAC,EAV0B,CAU1B;CACH,CAAC,EAtBmD,CAsBnD,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE,cAAM,OAAA,CAAC;IAC/C,mBAAmB,EAAE,cAAM,OAAA,CAAC;QAC1B,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE;QACxC,iBAAiB,EAAE,IAAI;QACvB,QAAQ,EAAE,KAAK;QACf,QAAQ,EAAE,KAAK;QACf,SAAS,EAAE,IAAI;QACf,kBAAkB,EAAE,UAAC,MAAW,IAAK,OAAA,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,EAAhD,CAAgD;KACtF,CAAC,EAPyB,CAOzB;IACF,cAAc,EAAE,cAAM,OAAA,CAAC;QACrB,aAAa,EAAE,KAAK;QACpB,QAAQ,EAAE,IAAI;KACf,CAAC,EAHoB,CAGpB;CACH,CAAC,EAb8C,CAa9C,CAAC,CAAC;AAEJ,mCAAmC;AACnC,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE,cAAM,OAAA,CAAC;IAC/C,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,UAAC,SAAmB;QAC9C,IAAM,MAAM,GAAG,EAAE,CAAC;QAElB,oCAAoC;QACpC,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,aAAa,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,wBAAwB;gBACjC,OAAO,EAAE,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAC;aACnD,CAAC,CAAC;QACL,CAAC;QAED,yCAAyC;QACzC,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,aAAa,CAAC,gDAAgD,CAAC,EAAE,CAAC;YAC/E,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,uCAAuC;gBAChD,OAAO,EAAE,SAAS,CAAC,aAAa,CAAC,gDAAgD,CAAC;aACnF,CAAC,CAAC;QACL,CAAC;QAED,uDAAuD;QACvD,IAAI,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,aAAa,CAAC,IAAI,CAAC,MAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,aAAa,CAAC,IAAI,CAAC,CAAA,IAAI,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,aAAa,CAAC,IAAI,CAAC,CAAA,EAAE,CAAC;YACxG,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,mBAAmB;gBACzB,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,4BAA4B;gBACrC,OAAO,EAAE,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC;aACvC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAC1E,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC3B,MAAM,QAAA;YACN,OAAO,EAAE;gBACP,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,UAAU,EAAzB,CAAyB,CAAC,CAAC,MAAM;gBAC9D,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,SAAS,EAAxB,CAAwB,CAAC,CAAC,MAAM;gBAC5D,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,UAAU,EAAzB,CAAyB,CAAC,CAAC,MAAM;gBAC9D,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,OAAO,EAAtB,CAAsB,CAAC,CAAC,MAAM;aACzD;SACF,CAAC;IACJ,CAAC,CAAC;CACH,CAAC,EA9C8C,CA8C9C,CAAC,CAAC;AA9GJ;;GAEG;AAEH,gDAA4E;AAC5E,2EAAoD;AACpD,gDAA0B;AAE1B,8CAA8C;AAC9C,IAAM,yBAAyB,GAAG;IAChC,QAAQ,EAAE;QACR,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,KAAK;QACnB,SAAS,EAAE,KAAK;QAChB,gBAAgB,EAAE,KAAK;QACvB,kBAAkB,EAAE,IAAI;QACxB,YAAY,EAAE,IAAI;KACnB;IACD,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;IACxB,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;IACjC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC5B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;CACzB,CAAC;AA0FF,mEAA0G;AAC1G,uEAAyG;AAEzG,6BAA6B;AACrB,IAAA,kBAAkB,GAAK,OAAO,CAAC,8BAA8B,CAAC,mBAA5C,CAA6C;AAEvE,8CAA8C;AAC9C,IAAM,cAAc,GAAG,UAAC,KAAa,IAAK,OAAA,CAAC;IACzC,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,KAAK;IACZ,QAAQ,EAAE,IAAI;IACd,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;IACtB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;IACzB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC3B,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC9B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;CACzB,CAAC,EATwC,CASxC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,EAAE;IAC1C,QAAQ,EAAE,IAAI;IACd,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,cAAc,CAAC;CACpD,CAAC,CAAC;AAEH,sBAAsB;AACtB,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,cAAM,OAAA,CAAC;IAC1D,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;IACpB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;CACtB,CAAC,EAJyD,CAIzD,CAAC,CAAC;AAEJ,QAAQ,CAAC,4BAA4B,EAAE;IACrC,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,gDAAgD,EAAE;YACnD,IAAA,cAAM,EACJ,uBAAC,gCAAc,kBAAY,WAAW,YACpC,uBAAC,iCAAe,IACd,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,eAAe,EACrB,IAAI,EAAC,OAAO,EACZ,QAAQ,SACR,GACa,CAClB,CAAC;YAEF,IAAM,IAAI,GAAG,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAA,cAAM,EACJ,uBAAC,gCAAc,cACb,uBAAC,iCAAe,IACd,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,eAAe,EACrB,IAAI,EAAC,OAAO,EACZ,QAAQ,SACR,GACa,CAClB,CAAC;YAEF,IAAM,KAAK,GAAG,cAAM,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACtE,IAAM,KAAK,GAAG,cAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAEhD,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE;;;;;wBAC3D,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAE/B,IAAA,cAAM,EACJ,uBAAC,gCAAc,cACb,uBAAC,iCAAe,IACd,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,eAAe,EACrB,IAAI,EAAC,OAAO,EACZ,QAAQ,QACR,KAAK,EAAC,oCAAoC,GAC1C,GACa,CAClB,CAAC;wBAEI,KAAK,GAAG,cAAM,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;wBAEtE,8CAA8C;wBAC9C,qBAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAA;;wBADvB,8CAA8C;wBAC9C,SAAuB,CAAC;wBACxB,qBAAM,IAAI,CAAC,GAAG,EAAE,EAAA;;wBAAhB,SAAgB,CAAC;wBAEjB,qBAAM,IAAA,eAAO,EAAC;gCACZ,IAAM,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gCAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCACzC,MAAM,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;gCAC7E,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;4BACxD,CAAC,CAAC,EAAA;;wBALF,SAKE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAA,cAAM,EACJ,uBAAC,gCAAc,cACb,uBAAC,iCAAe,IACd,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,eAAe,EACrB,IAAI,EAAC,OAAO,EACZ,QAAQ,SACR,GACa,CAClB,CAAC;YAEF,IAAM,KAAK,GAAG,cAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,qDAAqD,EAAE;YACxD,IAAA,cAAM,EACJ,uBAAC,wCAAsB,IAAC,SAAS,QAAC,WAAW,EAAC,oBAAoB,uBAEzC,CAC1B,CAAC;YAEF,IAAM,MAAM,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAEvD,IAAM,aAAa,GAAG,cAAM,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;YAC/E,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;;;;;wBAC5B,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBACzB,WAAW,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;wBAE9B,IAAA,cAAM,EACJ,uBAAC,wCAAsB,IAAC,OAAO,EAAE,WAAW,uBAEnB,CAC1B,CAAC;wBAEI,MAAM,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;wBAE1C,8CAA8C;wBAC9C,qBAAM,IAAI,CAAC,GAAG,EAAE,EAAA;;wBADhB,8CAA8C;wBAC9C,SAAgB,CAAC;wBACjB,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAE7B,kCAAkC;wBAClC,qBAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAA;;wBADxB,kCAAkC;wBAClC,SAAwB,CAAC;wBACzB,MAAM,CAAC,WAAW,CAAC,CAAC,gBAAgB,EAAE,CAAC;;;;aACxC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,oDAAoD,EAAE;YACvD,IAAA,cAAM,EACJ,uBAAC,wCAAsB,yBAEE,CAC1B,CAAC;YAEF,IAAM,MAAM,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,gCAAgC,EAAE;IACzC,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAA,cAAM,EACJ,uBAAC,uCAAmB,IAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,EAAC,IAAI,YAC7C,sDAAkB,GACE,CACvB,CAAC;YAEF,IAAM,SAAS,GAAG,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;YACxD,uBAAuB;YACvB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,EAAE;gBAC1C,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,IAAA,cAAM,EACJ,uBAAC,uCAAmB,IAAC,OAAO,EAAC,IAAI,YAC/B,6DAAyB,GACL,CACvB,CAAC;YAEF,IAAM,SAAS,GAAG,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,aAAa,CAAC;YACnE,8DAA8D;YAC9D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,kBAAkB;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAA,cAAM,EACJ,wBAAC,kCAAc,IAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAC,IAAI,aACrD,qDAAiB,EACjB,qDAAiB,EACjB,qDAAiB,IACF,CAClB,CAAC;YAEF,IAAM,IAAI,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE;QAC1B,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAA,cAAM,EACJ,wBAAC,mCAAe,IAAC,SAAS,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,EAAC,IAAI,aAC5D,qDAAiB,EACjB,qDAAiB,IACD,CACnB,CAAC;YAEF,IAAM,KAAK,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,6BAA6B,EAAE;IACtC,EAAE,CAAC,0CAA0C,EAAE;QAC7C,IAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChD,SAAS,CAAC,SAAS,GAAG,wBAAwB,CAAC;QAE/C,IAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAClC,MAAM,CAAC,gBAAgB,CAAC;YACtB,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,SAAS;YACnB,OAAO,EAAE,wBAAwB;SAClC,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/C,IAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChD,SAAS,CAAC,SAAS,GAAG,uBAAuB,CAAC;QAE9C,IAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAClC,MAAM,CAAC,gBAAgB,CAAC;YACtB,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,uCAAuC;SACjD,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE;QAC7C,IAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChD,SAAS,CAAC,SAAS,GAAG,iCAAiC,CAAC;QAExD,IAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAClC,MAAM,CAAC,gBAAgB,CAAC;YACtB,IAAI,EAAE,mBAAmB;YACzB,QAAQ,EAAE,UAAU;SACrB,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE;QACzC,IAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChD,SAAS,CAAC,SAAS,GAAG,6CAA6C,CAAC;QAEpE,IAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE7C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,eAAe,EAAE;IACxB,EAAE,CAAC,2CAA2C,EAAE;QAC9C,IAAA,cAAM,EAAC,gCAAK,SAAS,EAAC,SAAS,wCAA8B,CAAC,CAAC;QAE/D,IAAM,OAAO,GAAG,cAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QAC5D,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE;QAC3C,IAAA,cAAM,EAAC,gCAAK,SAAS,EAAC,oBAAoB,gCAAsB,CAAC,CAAC;QAElE,IAAM,OAAO,GAAG,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE;QACxC,IAAA,cAAM,EAAC,mCAAQ,SAAS,EAAC,cAAc,6BAAsB,CAAC,CAAC;QAE/D,IAAM,MAAM,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,mBAAmB,EAAE;IAC5B,EAAE,CAAC,+DAA+D,EAAE;;;;;oBAC5D,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;oBAE/B,IAAA,cAAM,EACJ,uBAAC,uCAAmB,IAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,EAAC,IAAI,YAC7C,wBAAC,gCAAc,kBAAY,cAAc,aACvC,wBAAC,mCAAe,IAAC,SAAS,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,EAAC,IAAI,aAC5D,uBAAC,iCAAe,IACd,IAAI,EAAC,WAAW,EAChB,KAAK,EAAC,YAAY,EAClB,QAAQ,SACR,EACF,uBAAC,iCAAe,IACd,IAAI,EAAC,UAAU,EACf,KAAK,EAAC,WAAW,EACjB,QAAQ,SACR,IACc,EAElB,uBAAC,iCAAe,IACd,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,eAAe,EACrB,IAAI,EAAC,OAAO,EACZ,QAAQ,QACR,WAAW,EAAC,8BAA8B,GAC1C,EAEF,uBAAC,wCAAsB,+BAEE,IACV,GACG,CACvB,CAAC;oBAGI,IAAI,GAAG,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACtC,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;oBAGrD,cAAc,GAAG,cAAM,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;oBACtE,UAAU,GAAG,cAAM,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;oBAE3E,MAAM,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;oBAChE,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;oBAE5D,2BAA2B;oBAC3B,qBAAM,IAAI,CAAC,GAAG,EAAE,EAAA;;oBADhB,2BAA2B;oBAC3B,SAAgB,CAAC;oBACjB,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;oBAErC,qBAAM,IAAI,CAAC,GAAG,EAAE,EAAA;;oBAAhB,SAAgB,CAAC;oBACjB,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;oBAGpE,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;oBACnD,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;;;;SAC3C,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/accessibility-responsive.test.tsx"], "sourcesContent": ["/**\n * Tests for accessibility and responsive design improvements\n */\n\nimport { render, screen, fireEvent, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport React from 'react';\n\n// Mock the accessibility provider for testing\nconst mockAccessibilityProvider = {\n  settings: {\n    reducedMotion: false,\n    highContrast: false,\n    largeText: false,\n    screenReaderMode: false,\n    keyboardNavigation: true,\n    focusVisible: true,\n  },\n  updateSetting: jest.fn(),\n  announceToScreenReader: jest.fn(),\n  setFocusToElement: jest.fn(),\n  skipToContent: jest.fn(),\n};\n\njest.mock('../hooks/useAccessibilityEnhanced', () => ({\n  useAccessibilityEnhanced: () => ({\n    ...mockAccessibilityProvider,\n    isReducedMotion: false,\n    prefersHighContrast: false,\n    prefersDarkMode: false,\n  }),\n  useAriaLiveRegion: () => ({\n    announce: jest.fn(),\n    createLiveRegion: () => React.createElement('div', { 'aria-live': 'polite', className: 'sr-only' }),\n  }),\n  useFormAccessibility: () => ({\n    announceError: jest.fn(),\n    announceSuccess: jest.fn(),\n    getFieldProps: (name: string, error?: string, description?: string) => ({\n      id: name,\n      name,\n      'aria-required': true,\n      'aria-invalid': !!error,\n      'aria-describedby': error ? `${name}-error` : description ? `${name}-description` : undefined,\n    }),\n  }),\n}));\n\njest.mock('../hooks/useResponsiveDesign', () => ({\n  useResponsiveDesign: () => ({\n    windowSize: { width: 1024, height: 768 },\n    currentBreakpoint: 'lg',\n    isMobile: false,\n    isTablet: false,\n    isDesktop: true,\n    getResponsiveValue: (values: any) => values.lg || values.md || values.sm || values.xs,\n  }),\n  useTouchDevice: () => ({\n    isTouchDevice: false,\n    hasHover: true,\n  }),\n}));\n\n// Mock accessibility audit utility\njest.mock('../utils/accessibility-audit', () => ({\n  auditAccessibility: jest.fn((container?: Element) => {\n    const issues = [];\n\n    // Check for images without alt text\n    if (container?.querySelector('img:not([alt])')) {\n      issues.push({\n        rule: 'img-alt',\n        severity: 'serious',\n        message: 'Image missing alt text',\n        element: container.querySelector('img:not([alt])'),\n      });\n    }\n\n    // Check for form controls without labels\n    if (container?.querySelector('input:not([aria-label]):not([aria-labelledby])')) {\n      issues.push({\n        rule: 'form-label',\n        severity: 'critical',\n        message: 'Form control missing accessible label',\n        element: container.querySelector('input:not([aria-label]):not([aria-labelledby])'),\n      });\n    }\n\n    // Check for heading hierarchy - h3 after h1 without h2\n    if (container?.querySelector('h1') && container?.querySelector('h3') && !container?.querySelector('h2')) {\n      issues.push({\n        rule: 'heading-hierarchy',\n        severity: 'moderate',\n        message: 'Improper heading hierarchy',\n        element: container.querySelector('h3'),\n      });\n    }\n\n    return {\n      score: issues.length === 0 ? 100 : Math.max(0, 100 - (issues.length * 10)),\n      passed: issues.length === 0,\n      issues,\n      summary: {\n        critical: issues.filter(i => i.severity === 'critical').length,\n        serious: issues.filter(i => i.severity === 'serious').length,\n        moderate: issues.filter(i => i.severity === 'moderate').length,\n        minor: issues.filter(i => i.severity === 'minor').length,\n      },\n    };\n  }),\n}));\n\nimport { AccessibleForm, AccessibleField, AccessibleSubmitButton } from '@/components/ui/accessible-form';\nimport { ResponsiveContainer, ResponsiveGrid, ResponsiveStack } from '@/components/ui/responsive-layout';\n\n// Import the mocked function\nconst { auditAccessibility } = require('../utils/accessibility-audit');\n\n// Mock window.matchMedia for responsive tests\nconst mockMatchMedia = (query: string) => ({\n  matches: false,\n  media: query,\n  onchange: null,\n  addListener: jest.fn(),\n  removeListener: jest.fn(),\n  addEventListener: jest.fn(),\n  removeEventListener: jest.fn(),\n  dispatchEvent: jest.fn(),\n});\n\nObject.defineProperty(window, 'matchMedia', {\n  writable: true,\n  value: jest.fn().mockImplementation(mockMatchMedia),\n});\n\n// Mock ResizeObserver\nglobal.ResizeObserver = jest.fn().mockImplementation(() => ({\n  observe: jest.fn(),\n  unobserve: jest.fn(),\n  disconnect: jest.fn(),\n}));\n\ndescribe('Accessibility Improvements', () => {\n  describe('AccessibleForm', () => {\n    it('should render form with proper ARIA attributes', () => {\n      render(\n        <AccessibleForm aria-label=\"Test form\">\n          <AccessibleField\n            name=\"email\"\n            label=\"Email Address\"\n            type=\"email\"\n            required\n          />\n        </AccessibleForm>\n      );\n\n      const form = screen.getByRole('form');\n      expect(form).toHaveAttribute('aria-label', 'Test form');\n      expect(form).toHaveAttribute('novalidate');\n    });\n\n    it('should associate labels with form fields', () => {\n      render(\n        <AccessibleForm>\n          <AccessibleField\n            name=\"email\"\n            label=\"Email Address\"\n            type=\"email\"\n            required\n          />\n        </AccessibleForm>\n      );\n\n      const input = screen.getByRole('textbox', { name: /email address/i });\n      const label = screen.getByText('Email Address');\n      \n      expect(input).toBeInTheDocument();\n      expect(label).toBeInTheDocument();\n      expect(input).toHaveAttribute('aria-required', 'true');\n    });\n\n    it('should display validation errors with proper ARIA attributes', async () => {\n      const user = userEvent.setup();\n      \n      render(\n        <AccessibleForm>\n          <AccessibleField\n            name=\"email\"\n            label=\"Email Address\"\n            type=\"email\"\n            required\n            error=\"Please enter a valid email address\"\n          />\n        </AccessibleForm>\n      );\n\n      const input = screen.getByRole('textbox', { name: /email address/i });\n      \n      // Trigger validation by focusing and blurring\n      await user.click(input);\n      await user.tab();\n\n      await waitFor(() => {\n        const errorMessage = screen.getByRole('alert');\n        expect(errorMessage).toBeInTheDocument();\n        expect(errorMessage).toHaveTextContent('Please enter a valid email address');\n        expect(input).toHaveAttribute('aria-invalid', 'true');\n      });\n    });\n\n    it('should show required field indicators', () => {\n      render(\n        <AccessibleForm>\n          <AccessibleField\n            name=\"email\"\n            label=\"Email Address\"\n            type=\"email\"\n            required\n          />\n        </AccessibleForm>\n      );\n\n      const label = screen.getByText('Email Address');\n      expect(label).toHaveClass('after:content-[\\'*\\']');\n    });\n  });\n\n  describe('AccessibleSubmitButton', () => {\n    it('should show loading state with proper announcements', () => {\n      render(\n        <AccessibleSubmitButton isLoading loadingText=\"Submitting form...\">\n          Submit\n        </AccessibleSubmitButton>\n      );\n\n      const button = screen.getByRole('button');\n      expect(button).toBeDisabled();\n      expect(button).toHaveTextContent('Submitting form...');\n      \n      const loadingStatus = screen.getByText('Form is being submitted, please wait');\n      expect(loadingStatus).toHaveClass('sr-only');\n    });\n\n    it('should be keyboard accessible', async () => {\n      const user = userEvent.setup();\n      const handleClick = jest.fn();\n\n      render(\n        <AccessibleSubmitButton onClick={handleClick}>\n          Submit\n        </AccessibleSubmitButton>\n      );\n\n      const button = screen.getByRole('button');\n\n      // Test that button is focusable and clickable\n      await user.tab();\n      expect(button).toHaveFocus();\n\n      // Test button click functionality\n      await user.click(button);\n      expect(handleClick).toHaveBeenCalled();\n    });\n  });\n\n  describe('Touch Target Compliance', () => {\n    it('should meet minimum touch target size requirements', () => {\n      render(\n        <AccessibleSubmitButton>\n          Submit\n        </AccessibleSubmitButton>\n      );\n\n      const button = screen.getByRole('button');\n      expect(button).toHaveClass('min-h-[44px]');\n    });\n  });\n});\n\ndescribe('Responsive Design Improvements', () => {\n  describe('ResponsiveContainer', () => {\n    it('should render with responsive classes', () => {\n      render(\n        <ResponsiveContainer maxWidth=\"xl\" padding=\"md\">\n          <div>Content</div>\n        </ResponsiveContainer>\n      );\n\n      const container = screen.getByText('Content').parentElement;\n      expect(container).toHaveClass('mx-auto', 'w-full', 'max-w-xl');\n    });\n\n    it('should apply different padding based on screen size', () => {\n      // Mock mobile viewport\n      Object.defineProperty(window, 'innerWidth', {\n        writable: true,\n        configurable: true,\n        value: 375,\n      });\n\n      render(\n        <ResponsiveContainer padding=\"md\">\n          <div>Mobile Content</div>\n        </ResponsiveContainer>\n      );\n\n      const container = screen.getByText('Mobile Content').parentElement;\n      // Check for the actual classes applied by ResponsiveContainer\n      expect(container).toHaveClass('mx-auto', 'w-full');\n      expect(container).toHaveClass('px-8'); // Default padding\n    });\n  });\n\n  describe('ResponsiveGrid', () => {\n    it('should render with responsive grid classes', () => {\n      render(\n        <ResponsiveGrid cols={{ xs: 1, md: 2, lg: 3 }} gap=\"md\">\n          <div>Item 1</div>\n          <div>Item 2</div>\n          <div>Item 3</div>\n        </ResponsiveGrid>\n      );\n\n      const grid = screen.getByText('Item 1').parentElement;\n      expect(grid).toHaveClass('grid', 'gap-4');\n    });\n  });\n\n  describe('ResponsiveStack', () => {\n    it('should render with responsive flex classes', () => {\n      render(\n        <ResponsiveStack direction={{ xs: 'col', md: 'row' }} gap=\"lg\">\n          <div>Item 1</div>\n          <div>Item 2</div>\n        </ResponsiveStack>\n      );\n\n      const stack = screen.getByText('Item 1').parentElement;\n      expect(stack).toHaveClass('flex', 'gap-6');\n    });\n  });\n});\n\ndescribe('Accessibility Audit Utility', () => {\n  it('should detect missing alt text on images', () => {\n    const container = document.createElement('div');\n    container.innerHTML = '<img src=\"test.jpg\" />';\n    \n    const result = auditAccessibility(container);\n    \n    expect(result.issues).toContainEqual(\n      expect.objectContaining({\n        rule: 'img-alt',\n        severity: 'serious',\n        message: 'Image missing alt text'\n      })\n    );\n  });\n\n  it('should detect form controls without labels', () => {\n    const container = document.createElement('div');\n    container.innerHTML = '<input type=\"text\" />';\n    \n    const result = auditAccessibility(container);\n    \n    expect(result.issues).toContainEqual(\n      expect.objectContaining({\n        rule: 'form-label',\n        severity: 'critical',\n        message: 'Form control missing accessible label'\n      })\n    );\n  });\n\n  it('should detect improper heading hierarchy', () => {\n    const container = document.createElement('div');\n    container.innerHTML = '<h1>Title</h1><h3>Subtitle</h3>';\n    \n    const result = auditAccessibility(container);\n    \n    expect(result.issues).toContainEqual(\n      expect.objectContaining({\n        rule: 'heading-hierarchy',\n        severity: 'moderate'\n      })\n    );\n  });\n\n  it('should calculate accessibility score', () => {\n    const container = document.createElement('div');\n    container.innerHTML = '<h1>Perfect Page</h1><p>No issues here!</p>';\n    \n    const result = auditAccessibility(container);\n    \n    expect(result.score).toBeGreaterThan(90);\n    expect(result.passed).toBe(true);\n  });\n});\n\ndescribe('CSS Utilities', () => {\n  it('should include screen reader only classes', () => {\n    render(<div className=\"sr-only\">Screen reader only text</div>);\n    \n    const element = screen.getByText('Screen reader only text');\n    expect(element).toHaveClass('sr-only');\n  });\n\n  it('should include responsive text classes', () => {\n    render(<div className=\"text-responsive-lg\">Responsive text</div>);\n    \n    const element = screen.getByText('Responsive text');\n    expect(element).toHaveClass('text-responsive-lg');\n  });\n\n  it('should include touch target classes', () => {\n    render(<button className=\"touch-target\">Touch button</button>);\n    \n    const button = screen.getByRole('button');\n    expect(button).toHaveClass('touch-target');\n  });\n});\n\ndescribe('Integration Tests', () => {\n  it('should work together - accessible form with responsive layout', async () => {\n    const user = userEvent.setup();\n    \n    render(\n      <ResponsiveContainer maxWidth=\"md\" padding=\"lg\">\n        <AccessibleForm aria-label=\"Contact form\">\n          <ResponsiveStack direction={{ xs: 'col', md: 'row' }} gap=\"md\">\n            <AccessibleField\n              name=\"firstName\"\n              label=\"First Name\"\n              required\n            />\n            <AccessibleField\n              name=\"lastName\"\n              label=\"Last Name\"\n              required\n            />\n          </ResponsiveStack>\n          \n          <AccessibleField\n            name=\"email\"\n            label=\"Email Address\"\n            type=\"email\"\n            required\n            description=\"We'll never share your email\"\n          />\n          \n          <AccessibleSubmitButton>\n            Send Message\n          </AccessibleSubmitButton>\n        </AccessibleForm>\n      </ResponsiveContainer>\n    );\n\n    // Test form accessibility\n    const form = screen.getByRole('form');\n    expect(form).toHaveAttribute('aria-label', 'Contact form');\n\n    // Test field accessibility\n    const firstNameInput = screen.getByRole('textbox', { name: /first name/i });\n    const emailInput = screen.getByRole('textbox', { name: /email address/i });\n    \n    expect(firstNameInput).toHaveAttribute('aria-required', 'true');\n    expect(emailInput).toHaveAttribute('aria-required', 'true');\n\n    // Test keyboard navigation\n    await user.tab();\n    expect(firstNameInput).toHaveFocus();\n    \n    await user.tab();\n    expect(screen.getByRole('textbox', { name: /last name/i })).toHaveFocus();\n\n    // Test responsive layout classes\n    const container = form.closest('[class*=\"max-w\"]');\n    expect(container).toHaveClass('max-w-md');\n  });\n});\n"], "version": 3}