b1d726602a03a22c68355aa054628db1
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
jest.mock('../hooks/useAccessibilityEnhanced', function () { return ({
    useAccessibilityEnhanced: function () { return (__assign(__assign({}, mockAccessibilityProvider), { isReducedMotion: false, prefersHighContrast: false, prefersDarkMode: false })); },
    useAriaLiveRegion: function () { return ({
        announce: jest.fn(),
        createLiveRegion: function () { return react_2.default.createElement('div', { 'aria-live': 'polite', className: 'sr-only' }); },
    }); },
    useFormAccessibility: function () { return ({
        announceError: jest.fn(),
        announceSuccess: jest.fn(),
        getFieldProps: function (name, error, description) { return ({
            id: name,
            name: name,
            'aria-required': true,
            'aria-invalid': !!error,
            'aria-describedby': error ? "".concat(name, "-error") : description ? "".concat(name, "-description") : undefined,
        }); },
    }); },
}); });
jest.mock('../hooks/useResponsiveDesign', function () { return ({
    useResponsiveDesign: function () { return ({
        windowSize: { width: 1024, height: 768 },
        currentBreakpoint: 'lg',
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        getResponsiveValue: function (values) { return values.lg || values.md || values.sm || values.xs; },
    }); },
    useTouchDevice: function () { return ({
        isTouchDevice: false,
        hasHover: true,
    }); },
}); });
// Mock accessibility audit utility
jest.mock('../utils/accessibility-audit', function () { return ({
    auditAccessibility: jest.fn(function (container) {
        var issues = [];
        // Check for images without alt text
        if (container === null || container === void 0 ? void 0 : container.querySelector('img:not([alt])')) {
            issues.push({
                rule: 'img-alt',
                severity: 'serious',
                message: 'Image missing alt text',
                element: container.querySelector('img:not([alt])'),
            });
        }
        // Check for form controls without labels
        if (container === null || container === void 0 ? void 0 : container.querySelector('input:not([aria-label]):not([aria-labelledby])')) {
            issues.push({
                rule: 'form-label',
                severity: 'critical',
                message: 'Form control missing accessible label',
                element: container.querySelector('input:not([aria-label]):not([aria-labelledby])'),
            });
        }
        // Check for heading hierarchy - h3 after h1 without h2
        if ((container === null || container === void 0 ? void 0 : container.querySelector('h1')) && (container === null || container === void 0 ? void 0 : container.querySelector('h3')) && !(container === null || container === void 0 ? void 0 : container.querySelector('h2'))) {
            issues.push({
                rule: 'heading-hierarchy',
                severity: 'moderate',
                message: 'Improper heading hierarchy',
                element: container.querySelector('h3'),
            });
        }
        return {
            score: issues.length === 0 ? 100 : Math.max(0, 100 - (issues.length * 10)),
            passed: issues.length === 0,
            issues: issues,
            summary: {
                critical: issues.filter(function (i) { return i.severity === 'critical'; }).length,
                serious: issues.filter(function (i) { return i.severity === 'serious'; }).length,
                moderate: issues.filter(function (i) { return i.severity === 'moderate'; }).length,
                minor: issues.filter(function (i) { return i.severity === 'minor'; }).length,
            },
        };
    }),
}); });
/**
 * Tests for accessibility and responsive design improvements
 */
var react_1 = require("@testing-library/react");
var user_event_1 = __importDefault(require("@testing-library/user-event"));
var react_2 = __importDefault(require("react"));
// Mock the accessibility provider for testing
var mockAccessibilityProvider = {
    settings: {
        reducedMotion: false,
        highContrast: false,
        largeText: false,
        screenReaderMode: false,
        keyboardNavigation: true,
        focusVisible: true,
    },
    updateSetting: jest.fn(),
    announceToScreenReader: jest.fn(),
    setFocusToElement: jest.fn(),
    skipToContent: jest.fn(),
};
var accessible_form_1 = require("@/components/ui/accessible-form");
var responsive_layout_1 = require("@/components/ui/responsive-layout");
// Import the mocked function
var auditAccessibility = require('../utils/accessibility-audit').auditAccessibility;
// Mock window.matchMedia for responsive tests
var mockMatchMedia = function (query) { return ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
}); };
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(mockMatchMedia),
});
// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(function () { return ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}); });
describe('Accessibility Improvements', function () {
    describe('AccessibleForm', function () {
        it('should render form with proper ARIA attributes', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleForm, { "aria-label": "Test form", children: (0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleField, { name: "email", label: "Email Address", type: "email", required: true }) }));
            var form = react_1.screen.getByRole('form');
            expect(form).toHaveAttribute('aria-label', 'Test form');
            expect(form).toHaveAttribute('novalidate');
        });
        it('should associate labels with form fields', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleForm, { children: (0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleField, { name: "email", label: "Email Address", type: "email", required: true }) }));
            var input = react_1.screen.getByRole('textbox', { name: /email address/i });
            var label = react_1.screen.getByText('Email Address');
            expect(input).toBeInTheDocument();
            expect(label).toBeInTheDocument();
            expect(input).toHaveAttribute('aria-required', 'true');
        });
        it('should display validation errors with proper ARIA attributes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, input;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_1.render)((0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleForm, { children: (0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleField, { name: "email", label: "Email Address", type: "email", required: true, error: "Please enter a valid email address" }) }));
                        input = react_1.screen.getByRole('textbox', { name: /email address/i });
                        // Trigger validation by focusing and blurring
                        return [4 /*yield*/, user.click(input)];
                    case 1:
                        // Trigger validation by focusing and blurring
                        _a.sent();
                        return [4 /*yield*/, user.tab()];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, (0, react_1.waitFor)(function () {
                                var errorMessage = react_1.screen.getByRole('alert');
                                expect(errorMessage).toBeInTheDocument();
                                expect(errorMessage).toHaveTextContent('Please enter a valid email address');
                                expect(input).toHaveAttribute('aria-invalid', 'true');
                            })];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should show required field indicators', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleForm, { children: (0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleField, { name: "email", label: "Email Address", type: "email", required: true }) }));
            var label = react_1.screen.getByText('Email Address');
            expect(label).toHaveClass('after:content-[\'*\']');
        });
    });
    describe('AccessibleSubmitButton', function () {
        it('should show loading state with proper announcements', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleSubmitButton, { isLoading: true, loadingText: "Submitting form...", children: "Submit" }));
            var button = react_1.screen.getByRole('button');
            expect(button).toBeDisabled();
            expect(button).toHaveTextContent('Submitting form...');
            var loadingStatus = react_1.screen.getByText('Form is being submitted, please wait');
            expect(loadingStatus).toHaveClass('sr-only');
        });
        it('should be keyboard accessible', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, handleClick, button;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        handleClick = jest.fn();
                        (0, react_1.render)((0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleSubmitButton, { onClick: handleClick, children: "Submit" }));
                        button = react_1.screen.getByRole('button');
                        // Test that button is focusable and clickable
                        return [4 /*yield*/, user.tab()];
                    case 1:
                        // Test that button is focusable and clickable
                        _a.sent();
                        expect(button).toHaveFocus();
                        // Test button click functionality
                        return [4 /*yield*/, user.click(button)];
                    case 2:
                        // Test button click functionality
                        _a.sent();
                        expect(handleClick).toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Touch Target Compliance', function () {
        it('should meet minimum touch target size requirements', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleSubmitButton, { children: "Submit" }));
            var button = react_1.screen.getByRole('button');
            expect(button).toHaveClass('min-h-[44px]');
        });
    });
});
describe('Responsive Design Improvements', function () {
    describe('ResponsiveContainer', function () {
        it('should render with responsive classes', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsx)(responsive_layout_1.ResponsiveContainer, { maxWidth: "xl", padding: "md", children: (0, jsx_runtime_1.jsx)("div", { children: "Content" }) }));
            var container = react_1.screen.getByText('Content').parentElement;
            expect(container).toHaveClass('mx-auto', 'w-full', 'max-w-xl');
        });
        it('should apply different padding based on screen size', function () {
            // Mock mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375,
            });
            (0, react_1.render)((0, jsx_runtime_1.jsx)(responsive_layout_1.ResponsiveContainer, { padding: "md", children: (0, jsx_runtime_1.jsx)("div", { children: "Mobile Content" }) }));
            var container = react_1.screen.getByText('Mobile Content').parentElement;
            // Check for the actual classes applied by ResponsiveContainer
            expect(container).toHaveClass('mx-auto', 'w-full');
            expect(container).toHaveClass('px-8'); // Default padding
        });
    });
    describe('ResponsiveGrid', function () {
        it('should render with responsive grid classes', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsxs)(responsive_layout_1.ResponsiveGrid, { cols: { xs: 1, md: 2, lg: 3 }, gap: "md", children: [(0, jsx_runtime_1.jsx)("div", { children: "Item 1" }), (0, jsx_runtime_1.jsx)("div", { children: "Item 2" }), (0, jsx_runtime_1.jsx)("div", { children: "Item 3" })] }));
            var grid = react_1.screen.getByText('Item 1').parentElement;
            expect(grid).toHaveClass('grid', 'gap-4');
        });
    });
    describe('ResponsiveStack', function () {
        it('should render with responsive flex classes', function () {
            (0, react_1.render)((0, jsx_runtime_1.jsxs)(responsive_layout_1.ResponsiveStack, { direction: { xs: 'col', md: 'row' }, gap: "lg", children: [(0, jsx_runtime_1.jsx)("div", { children: "Item 1" }), (0, jsx_runtime_1.jsx)("div", { children: "Item 2" })] }));
            var stack = react_1.screen.getByText('Item 1').parentElement;
            expect(stack).toHaveClass('flex', 'gap-6');
        });
    });
});
describe('Accessibility Audit Utility', function () {
    it('should detect missing alt text on images', function () {
        var container = document.createElement('div');
        container.innerHTML = '<img src="test.jpg" />';
        var result = auditAccessibility(container);
        expect(result.issues).toContainEqual(expect.objectContaining({
            rule: 'img-alt',
            severity: 'serious',
            message: 'Image missing alt text'
        }));
    });
    it('should detect form controls without labels', function () {
        var container = document.createElement('div');
        container.innerHTML = '<input type="text" />';
        var result = auditAccessibility(container);
        expect(result.issues).toContainEqual(expect.objectContaining({
            rule: 'form-label',
            severity: 'critical',
            message: 'Form control missing accessible label'
        }));
    });
    it('should detect improper heading hierarchy', function () {
        var container = document.createElement('div');
        container.innerHTML = '<h1>Title</h1><h3>Subtitle</h3>';
        var result = auditAccessibility(container);
        expect(result.issues).toContainEqual(expect.objectContaining({
            rule: 'heading-hierarchy',
            severity: 'moderate'
        }));
    });
    it('should calculate accessibility score', function () {
        var container = document.createElement('div');
        container.innerHTML = '<h1>Perfect Page</h1><p>No issues here!</p>';
        var result = auditAccessibility(container);
        expect(result.score).toBeGreaterThan(90);
        expect(result.passed).toBe(true);
    });
});
describe('CSS Utilities', function () {
    it('should include screen reader only classes', function () {
        (0, react_1.render)((0, jsx_runtime_1.jsx)("div", { className: "sr-only", children: "Screen reader only text" }));
        var element = react_1.screen.getByText('Screen reader only text');
        expect(element).toHaveClass('sr-only');
    });
    it('should include responsive text classes', function () {
        (0, react_1.render)((0, jsx_runtime_1.jsx)("div", { className: "text-responsive-lg", children: "Responsive text" }));
        var element = react_1.screen.getByText('Responsive text');
        expect(element).toHaveClass('text-responsive-lg');
    });
    it('should include touch target classes', function () {
        (0, react_1.render)((0, jsx_runtime_1.jsx)("button", { className: "touch-target", children: "Touch button" }));
        var button = react_1.screen.getByRole('button');
        expect(button).toHaveClass('touch-target');
    });
});
describe('Integration Tests', function () {
    it('should work together - accessible form with responsive layout', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, form, firstNameInput, emailInput, container;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)((0, jsx_runtime_1.jsx)(responsive_layout_1.ResponsiveContainer, { maxWidth: "md", padding: "lg", children: (0, jsx_runtime_1.jsxs)(accessible_form_1.AccessibleForm, { "aria-label": "Contact form", children: [(0, jsx_runtime_1.jsxs)(responsive_layout_1.ResponsiveStack, { direction: { xs: 'col', md: 'row' }, gap: "md", children: [(0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleField, { name: "firstName", label: "First Name", required: true }), (0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleField, { name: "lastName", label: "Last Name", required: true })] }), (0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleField, { name: "email", label: "Email Address", type: "email", required: true, description: "We'll never share your email" }), (0, jsx_runtime_1.jsx)(accessible_form_1.AccessibleSubmitButton, { children: "Send Message" })] }) }));
                    form = react_1.screen.getByRole('form');
                    expect(form).toHaveAttribute('aria-label', 'Contact form');
                    firstNameInput = react_1.screen.getByRole('textbox', { name: /first name/i });
                    emailInput = react_1.screen.getByRole('textbox', { name: /email address/i });
                    expect(firstNameInput).toHaveAttribute('aria-required', 'true');
                    expect(emailInput).toHaveAttribute('aria-required', 'true');
                    // Test keyboard navigation
                    return [4 /*yield*/, user.tab()];
                case 1:
                    // Test keyboard navigation
                    _a.sent();
                    expect(firstNameInput).toHaveFocus();
                    return [4 /*yield*/, user.tab()];
                case 2:
                    _a.sent();
                    expect(react_1.screen.getByRole('textbox', { name: /last name/i })).toHaveFocus();
                    container = form.closest('[class*="max-w"]');
                    expect(container).toHaveClass('max-w-md');
                    return [2 /*return*/];
            }
        });
    }); });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL19fdGVzdHNfXy91bml0L2NvbXBvbmVudHMvYWNjZXNzaWJpbGl0eS1yZXNwb25zaXZlLnRlc3QudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBd0JBLElBQUksQ0FBQyxJQUFJLENBQUMsbUNBQW1DLEVBQUUsY0FBTSxPQUFBLENBQUM7SUFDcEQsd0JBQXdCLEVBQUUsY0FBTSxPQUFBLHVCQUMzQix5QkFBeUIsS0FDNUIsZUFBZSxFQUFFLEtBQUssRUFDdEIsbUJBQW1CLEVBQUUsS0FBSyxFQUMxQixlQUFlLEVBQUUsS0FBSyxJQUN0QixFQUw4QixDQUs5QjtJQUNGLGlCQUFpQixFQUFFLGNBQU0sT0FBQSxDQUFDO1FBQ3hCLFFBQVEsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ25CLGdCQUFnQixFQUFFLGNBQU0sT0FBQSxlQUFLLENBQUMsYUFBYSxDQUFDLEtBQUssRUFBRSxFQUFFLFdBQVcsRUFBRSxRQUFRLEVBQUUsU0FBUyxFQUFFLFNBQVMsRUFBRSxDQUFDLEVBQTNFLENBQTJFO0tBQ3BHLENBQUMsRUFIdUIsQ0FHdkI7SUFDRixvQkFBb0IsRUFBRSxjQUFNLE9BQUEsQ0FBQztRQUMzQixhQUFhLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUN4QixlQUFlLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUMxQixhQUFhLEVBQUUsVUFBQyxJQUFZLEVBQUUsS0FBYyxFQUFFLFdBQW9CLElBQUssT0FBQSxDQUFDO1lBQ3RFLEVBQUUsRUFBRSxJQUFJO1lBQ1IsSUFBSSxNQUFBO1lBQ0osZUFBZSxFQUFFLElBQUk7WUFDckIsY0FBYyxFQUFFLENBQUMsQ0FBQyxLQUFLO1lBQ3ZCLGtCQUFrQixFQUFFLEtBQUssQ0FBQyxDQUFDLENBQUMsVUFBRyxJQUFJLFdBQVEsQ0FBQyxDQUFDLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxVQUFHLElBQUksaUJBQWMsQ0FBQyxDQUFDLENBQUMsU0FBUztTQUM5RixDQUFDLEVBTnFFLENBTXJFO0tBQ0gsQ0FBQyxFQVYwQixDQVUxQjtDQUNILENBQUMsRUF0Qm1ELENBc0JuRCxDQUFDLENBQUM7QUFFSixJQUFJLENBQUMsSUFBSSxDQUFDLDhCQUE4QixFQUFFLGNBQU0sT0FBQSxDQUFDO0lBQy9DLG1CQUFtQixFQUFFLGNBQU0sT0FBQSxDQUFDO1FBQzFCLFVBQVUsRUFBRSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRTtRQUN4QyxpQkFBaUIsRUFBRSxJQUFJO1FBQ3ZCLFFBQVEsRUFBRSxLQUFLO1FBQ2YsUUFBUSxFQUFFLEtBQUs7UUFDZixTQUFTLEVBQUUsSUFBSTtRQUNmLGtCQUFrQixFQUFFLFVBQUMsTUFBVyxJQUFLLE9BQUEsTUFBTSxDQUFDLEVBQUUsSUFBSSxNQUFNLENBQUMsRUFBRSxJQUFJLE1BQU0sQ0FBQyxFQUFFLElBQUksTUFBTSxDQUFDLEVBQUUsRUFBaEQsQ0FBZ0Q7S0FDdEYsQ0FBQyxFQVB5QixDQU96QjtJQUNGLGNBQWMsRUFBRSxjQUFNLE9BQUEsQ0FBQztRQUNyQixhQUFhLEVBQUUsS0FBSztRQUNwQixRQUFRLEVBQUUsSUFBSTtLQUNmLENBQUMsRUFIb0IsQ0FHcEI7Q0FDSCxDQUFDLEVBYjhDLENBYTlDLENBQUMsQ0FBQztBQUVKLG1DQUFtQztBQUNuQyxJQUFJLENBQUMsSUFBSSxDQUFDLDhCQUE4QixFQUFFLGNBQU0sT0FBQSxDQUFDO0lBQy9DLGtCQUFrQixFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsVUFBQyxTQUFtQjtRQUM5QyxJQUFNLE1BQU0sR0FBRyxFQUFFLENBQUM7UUFFbEIsb0NBQW9DO1FBQ3BDLElBQUksU0FBUyxhQUFULFNBQVMsdUJBQVQsU0FBUyxDQUFFLGFBQWEsQ0FBQyxnQkFBZ0IsQ0FBQyxFQUFFLENBQUM7WUFDL0MsTUFBTSxDQUFDLElBQUksQ0FBQztnQkFDVixJQUFJLEVBQUUsU0FBUztnQkFDZixRQUFRLEVBQUUsU0FBUztnQkFDbkIsT0FBTyxFQUFFLHdCQUF3QjtnQkFDakMsT0FBTyxFQUFFLFNBQVMsQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUM7YUFDbkQsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztRQUVELHlDQUF5QztRQUN6QyxJQUFJLFNBQVMsYUFBVCxTQUFTLHVCQUFULFNBQVMsQ0FBRSxhQUFhLENBQUMsZ0RBQWdELENBQUMsRUFBRSxDQUFDO1lBQy9FLE1BQU0sQ0FBQyxJQUFJLENBQUM7Z0JBQ1YsSUFBSSxFQUFFLFlBQVk7Z0JBQ2xCLFFBQVEsRUFBRSxVQUFVO2dCQUNwQixPQUFPLEVBQUUsdUNBQXVDO2dCQUNoRCxPQUFPLEVBQUUsU0FBUyxDQUFDLGFBQWEsQ0FBQyxnREFBZ0QsQ0FBQzthQUNuRixDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsdURBQXVEO1FBQ3ZELElBQUksQ0FBQSxTQUFTLGFBQVQsU0FBUyx1QkFBVCxTQUFTLENBQUUsYUFBYSxDQUFDLElBQUksQ0FBQyxNQUFJLFNBQVMsYUFBVCxTQUFTLHVCQUFULFNBQVMsQ0FBRSxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUEsSUFBSSxDQUFDLENBQUEsU0FBUyxhQUFULFNBQVMsdUJBQVQsU0FBUyxDQUFFLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQSxFQUFFLENBQUM7WUFDeEcsTUFBTSxDQUFDLElBQUksQ0FBQztnQkFDVixJQUFJLEVBQUUsbUJBQW1CO2dCQUN6QixRQUFRLEVBQUUsVUFBVTtnQkFDcEIsT0FBTyxFQUFFLDRCQUE0QjtnQkFDckMsT0FBTyxFQUFFLFNBQVMsQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDO2FBQ3ZDLENBQUMsQ0FBQztRQUNMLENBQUM7UUFFRCxPQUFPO1lBQ0wsS0FBSyxFQUFFLE1BQU0sQ0FBQyxNQUFNLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLE1BQU0sQ0FBQyxNQUFNLEdBQUcsRUFBRSxDQUFDLENBQUM7WUFDMUUsTUFBTSxFQUFFLE1BQU0sQ0FBQyxNQUFNLEtBQUssQ0FBQztZQUMzQixNQUFNLFFBQUE7WUFDTixPQUFPLEVBQUU7Z0JBQ1AsUUFBUSxFQUFFLE1BQU0sQ0FBQyxNQUFNLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsUUFBUSxLQUFLLFVBQVUsRUFBekIsQ0FBeUIsQ0FBQyxDQUFDLE1BQU07Z0JBQzlELE9BQU8sRUFBRSxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLFFBQVEsS0FBSyxTQUFTLEVBQXhCLENBQXdCLENBQUMsQ0FBQyxNQUFNO2dCQUM1RCxRQUFRLEVBQUUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxRQUFRLEtBQUssVUFBVSxFQUF6QixDQUF5QixDQUFDLENBQUMsTUFBTTtnQkFDOUQsS0FBSyxFQUFFLE1BQU0sQ0FBQyxNQUFNLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsUUFBUSxLQUFLLE9BQU8sRUFBdEIsQ0FBc0IsQ0FBQyxDQUFDLE1BQU07YUFDekQ7U0FDRixDQUFDO0lBQ0osQ0FBQyxDQUFDO0NBQ0gsQ0FBQyxFQTlDOEMsQ0E4QzlDLENBQUMsQ0FBQztBQTlHSjs7R0FFRztBQUVILGdEQUE0RTtBQUM1RSwyRUFBb0Q7QUFDcEQsZ0RBQTBCO0FBRTFCLDhDQUE4QztBQUM5QyxJQUFNLHlCQUF5QixHQUFHO0lBQ2hDLFFBQVEsRUFBRTtRQUNSLGFBQWEsRUFBRSxLQUFLO1FBQ3BCLFlBQVksRUFBRSxLQUFLO1FBQ25CLFNBQVMsRUFBRSxLQUFLO1FBQ2hCLGdCQUFnQixFQUFFLEtBQUs7UUFDdkIsa0JBQWtCLEVBQUUsSUFBSTtRQUN4QixZQUFZLEVBQUUsSUFBSTtLQUNuQjtJQUNELGFBQWEsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO0lBQ3hCLHNCQUFzQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7SUFDakMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtJQUM1QixhQUFhLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtDQUN6QixDQUFDO0FBMEZGLG1FQUEwRztBQUMxRyx1RUFBeUc7QUFFekcsNkJBQTZCO0FBQ3JCLElBQUEsa0JBQWtCLEdBQUssT0FBTyxDQUFDLDhCQUE4QixDQUFDLG1CQUE1QyxDQUE2QztBQUV2RSw4Q0FBOEM7QUFDOUMsSUFBTSxjQUFjLEdBQUcsVUFBQyxLQUFhLElBQUssT0FBQSxDQUFDO0lBQ3pDLE9BQU8sRUFBRSxLQUFLO0lBQ2QsS0FBSyxFQUFFLEtBQUs7SUFDWixRQUFRLEVBQUUsSUFBSTtJQUNkLFdBQVcsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO0lBQ3RCLGNBQWMsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO0lBQ3pCLGdCQUFnQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7SUFDM0IsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtJQUM5QixhQUFhLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtDQUN6QixDQUFDLEVBVHdDLENBU3hDLENBQUM7QUFFSCxNQUFNLENBQUMsY0FBYyxDQUFDLE1BQU0sRUFBRSxZQUFZLEVBQUU7SUFDMUMsUUFBUSxFQUFFLElBQUk7SUFDZCxLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGtCQUFrQixDQUFDLGNBQWMsQ0FBQztDQUNwRCxDQUFDLENBQUM7QUFFSCxzQkFBc0I7QUFDdEIsTUFBTSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsa0JBQWtCLENBQUMsY0FBTSxPQUFBLENBQUM7SUFDMUQsT0FBTyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7SUFDbEIsU0FBUyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7SUFDcEIsVUFBVSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7Q0FDdEIsQ0FBQyxFQUp5RCxDQUl6RCxDQUFDLENBQUM7QUFFSixRQUFRLENBQUMsNEJBQTRCLEVBQUU7SUFDckMsUUFBUSxDQUFDLGdCQUFnQixFQUFFO1FBQ3pCLEVBQUUsQ0FBQyxnREFBZ0QsRUFBRTtZQUNuRCxJQUFBLGNBQU0sRUFDSix1QkFBQyxnQ0FBYyxrQkFBWSxXQUFXLFlBQ3BDLHVCQUFDLGlDQUFlLElBQ2QsSUFBSSxFQUFDLE9BQU8sRUFDWixLQUFLLEVBQUMsZUFBZSxFQUNyQixJQUFJLEVBQUMsT0FBTyxFQUNaLFFBQVEsU0FDUixHQUNhLENBQ2xCLENBQUM7WUFFRixJQUFNLElBQUksR0FBRyxjQUFNLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxlQUFlLENBQUMsWUFBWSxFQUFFLFdBQVcsQ0FBQyxDQUFDO1lBQ3hELE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxlQUFlLENBQUMsWUFBWSxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUU7WUFDN0MsSUFBQSxjQUFNLEVBQ0osdUJBQUMsZ0NBQWMsY0FDYix1QkFBQyxpQ0FBZSxJQUNkLElBQUksRUFBQyxPQUFPLEVBQ1osS0FBSyxFQUFDLGVBQWUsRUFDckIsSUFBSSxFQUFDLE9BQU8sRUFDWixRQUFRLFNBQ1IsR0FDYSxDQUNsQixDQUFDO1lBRUYsSUFBTSxLQUFLLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyxTQUFTLEVBQUUsRUFBRSxJQUFJLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDO1lBQ3RFLElBQU0sS0FBSyxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsZUFBZSxDQUFDLENBQUM7WUFFaEQsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDbEMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDbEMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLGVBQWUsQ0FBQyxlQUFlLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDekQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsOERBQThELEVBQUU7Ozs7O3dCQUMzRCxJQUFJLEdBQUcsb0JBQVMsQ0FBQyxLQUFLLEVBQUUsQ0FBQzt3QkFFL0IsSUFBQSxjQUFNLEVBQ0osdUJBQUMsZ0NBQWMsY0FDYix1QkFBQyxpQ0FBZSxJQUNkLElBQUksRUFBQyxPQUFPLEVBQ1osS0FBSyxFQUFDLGVBQWUsRUFDckIsSUFBSSxFQUFDLE9BQU8sRUFDWixRQUFRLFFBQ1IsS0FBSyxFQUFDLG9DQUFvQyxHQUMxQyxHQUNhLENBQ2xCLENBQUM7d0JBRUksS0FBSyxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsU0FBUyxFQUFFLEVBQUUsSUFBSSxFQUFFLGdCQUFnQixFQUFFLENBQUMsQ0FBQzt3QkFFdEUsOENBQThDO3dCQUM5QyxxQkFBTSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxFQUFBOzt3QkFEdkIsOENBQThDO3dCQUM5QyxTQUF1QixDQUFDO3dCQUN4QixxQkFBTSxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUE7O3dCQUFoQixTQUFnQixDQUFDO3dCQUVqQixxQkFBTSxJQUFBLGVBQU8sRUFBQztnQ0FDWixJQUFNLFlBQVksR0FBRyxjQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dDQUMvQyxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztnQ0FDekMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLGlCQUFpQixDQUFDLG9DQUFvQyxDQUFDLENBQUM7Z0NBQzdFLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxlQUFlLENBQUMsY0FBYyxFQUFFLE1BQU0sQ0FBQyxDQUFDOzRCQUN4RCxDQUFDLENBQUMsRUFBQTs7d0JBTEYsU0FLRSxDQUFDOzs7O2FBQ0osQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHVDQUF1QyxFQUFFO1lBQzFDLElBQUEsY0FBTSxFQUNKLHVCQUFDLGdDQUFjLGNBQ2IsdUJBQUMsaUNBQWUsSUFDZCxJQUFJLEVBQUMsT0FBTyxFQUNaLEtBQUssRUFBQyxlQUFlLEVBQ3JCLElBQUksRUFBQyxPQUFPLEVBQ1osUUFBUSxTQUNSLEdBQ2EsQ0FDbEIsQ0FBQztZQUVGLElBQU0sS0FBSyxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsZUFBZSxDQUFDLENBQUM7WUFDaEQsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLFdBQVcsQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDO1FBQ3JELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsd0JBQXdCLEVBQUU7UUFDakMsRUFBRSxDQUFDLHFEQUFxRCxFQUFFO1lBQ3hELElBQUEsY0FBTSxFQUNKLHVCQUFDLHdDQUFzQixJQUFDLFNBQVMsUUFBQyxXQUFXLEVBQUMsb0JBQW9CLHVCQUV6QyxDQUMxQixDQUFDO1lBRUYsSUFBTSxNQUFNLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxFQUFFLENBQUM7WUFDOUIsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGlCQUFpQixDQUFDLG9CQUFvQixDQUFDLENBQUM7WUFFdkQsSUFBTSxhQUFhLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO1lBQy9FLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQyxXQUFXLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDL0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsK0JBQStCLEVBQUU7Ozs7O3dCQUM1QixJQUFJLEdBQUcsb0JBQVMsQ0FBQyxLQUFLLEVBQUUsQ0FBQzt3QkFDekIsV0FBVyxHQUFHLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQzt3QkFFOUIsSUFBQSxjQUFNLEVBQ0osdUJBQUMsd0NBQXNCLElBQUMsT0FBTyxFQUFFLFdBQVcsdUJBRW5CLENBQzFCLENBQUM7d0JBRUksTUFBTSxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUM7d0JBRTFDLDhDQUE4Qzt3QkFDOUMscUJBQU0sSUFBSSxDQUFDLEdBQUcsRUFBRSxFQUFBOzt3QkFEaEIsOENBQThDO3dCQUM5QyxTQUFnQixDQUFDO3dCQUNqQixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7d0JBRTdCLGtDQUFrQzt3QkFDbEMscUJBQU0sSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsRUFBQTs7d0JBRHhCLGtDQUFrQzt3QkFDbEMsU0FBd0IsQ0FBQzt3QkFDekIsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDLGdCQUFnQixFQUFFLENBQUM7Ozs7YUFDeEMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMseUJBQXlCLEVBQUU7UUFDbEMsRUFBRSxDQUFDLG9EQUFvRCxFQUFFO1lBQ3ZELElBQUEsY0FBTSxFQUNKLHVCQUFDLHdDQUFzQix5QkFFRSxDQUMxQixDQUFDO1lBRUYsSUFBTSxNQUFNLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBQzdDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQztBQUVILFFBQVEsQ0FBQyxnQ0FBZ0MsRUFBRTtJQUN6QyxRQUFRLENBQUMscUJBQXFCLEVBQUU7UUFDOUIsRUFBRSxDQUFDLHVDQUF1QyxFQUFFO1lBQzFDLElBQUEsY0FBTSxFQUNKLHVCQUFDLHVDQUFtQixJQUFDLFFBQVEsRUFBQyxJQUFJLEVBQUMsT0FBTyxFQUFDLElBQUksWUFDN0Msc0RBQWtCLEdBQ0UsQ0FDdkIsQ0FBQztZQUVGLElBQU0sU0FBUyxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLENBQUMsYUFBYSxDQUFDO1lBQzVELE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxXQUFXLENBQUMsU0FBUyxFQUFFLFFBQVEsRUFBRSxVQUFVLENBQUMsQ0FBQztRQUNqRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxxREFBcUQsRUFBRTtZQUN4RCx1QkFBdUI7WUFDdkIsTUFBTSxDQUFDLGNBQWMsQ0FBQyxNQUFNLEVBQUUsWUFBWSxFQUFFO2dCQUMxQyxRQUFRLEVBQUUsSUFBSTtnQkFDZCxZQUFZLEVBQUUsSUFBSTtnQkFDbEIsS0FBSyxFQUFFLEdBQUc7YUFDWCxDQUFDLENBQUM7WUFFSCxJQUFBLGNBQU0sRUFDSix1QkFBQyx1Q0FBbUIsSUFBQyxPQUFPLEVBQUMsSUFBSSxZQUMvQiw2REFBeUIsR0FDTCxDQUN2QixDQUFDO1lBRUYsSUFBTSxTQUFTLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLGFBQWEsQ0FBQztZQUNuRSw4REFBOEQ7WUFDOUQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxTQUFTLEVBQUUsUUFBUSxDQUFDLENBQUM7WUFDbkQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLGtCQUFrQjtRQUMzRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGdCQUFnQixFQUFFO1FBQ3pCLEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRTtZQUMvQyxJQUFBLGNBQU0sRUFDSix3QkFBQyxrQ0FBYyxJQUFDLElBQUksRUFBRSxFQUFFLEVBQUUsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsRUFBRSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEVBQUUsR0FBRyxFQUFDLElBQUksYUFDckQscURBQWlCLEVBQ2pCLHFEQUFpQixFQUNqQixxREFBaUIsSUFDRixDQUNsQixDQUFDO1lBRUYsSUFBTSxJQUFJLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxhQUFhLENBQUM7WUFDdEQsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLFdBQVcsQ0FBQyxNQUFNLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFDNUMsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxpQkFBaUIsRUFBRTtRQUMxQixFQUFFLENBQUMsNENBQTRDLEVBQUU7WUFDL0MsSUFBQSxjQUFNLEVBQ0osd0JBQUMsbUNBQWUsSUFBQyxTQUFTLEVBQUUsRUFBRSxFQUFFLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRSxLQUFLLEVBQUUsRUFBRSxHQUFHLEVBQUMsSUFBSSxhQUM1RCxxREFBaUIsRUFDakIscURBQWlCLElBQ0QsQ0FDbkIsQ0FBQztZQUVGLElBQU0sS0FBSyxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUMsYUFBYSxDQUFDO1lBQ3ZELE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxXQUFXLENBQUMsTUFBTSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBQzdDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQztBQUVILFFBQVEsQ0FBQyw2QkFBNkIsRUFBRTtJQUN0QyxFQUFFLENBQUMsMENBQTBDLEVBQUU7UUFDN0MsSUFBTSxTQUFTLEdBQUcsUUFBUSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNoRCxTQUFTLENBQUMsU0FBUyxHQUFHLHdCQUF3QixDQUFDO1FBRS9DLElBQU0sTUFBTSxHQUFHLGtCQUFrQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRTdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUNsQyxNQUFNLENBQUMsZ0JBQWdCLENBQUM7WUFDdEIsSUFBSSxFQUFFLFNBQVM7WUFDZixRQUFRLEVBQUUsU0FBUztZQUNuQixPQUFPLEVBQUUsd0JBQXdCO1NBQ2xDLENBQUMsQ0FDSCxDQUFDO0lBQ0osQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsNENBQTRDLEVBQUU7UUFDL0MsSUFBTSxTQUFTLEdBQUcsUUFBUSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNoRCxTQUFTLENBQUMsU0FBUyxHQUFHLHVCQUF1QixDQUFDO1FBRTlDLElBQU0sTUFBTSxHQUFHLGtCQUFrQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRTdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUNsQyxNQUFNLENBQUMsZ0JBQWdCLENBQUM7WUFDdEIsSUFBSSxFQUFFLFlBQVk7WUFDbEIsUUFBUSxFQUFFLFVBQVU7WUFDcEIsT0FBTyxFQUFFLHVDQUF1QztTQUNqRCxDQUFDLENBQ0gsQ0FBQztJQUNKLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLDBDQUEwQyxFQUFFO1FBQzdDLElBQU0sU0FBUyxHQUFHLFFBQVEsQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDaEQsU0FBUyxDQUFDLFNBQVMsR0FBRyxpQ0FBaUMsQ0FBQztRQUV4RCxJQUFNLE1BQU0sR0FBRyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUU3QyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FDbEMsTUFBTSxDQUFDLGdCQUFnQixDQUFDO1lBQ3RCLElBQUksRUFBRSxtQkFBbUI7WUFDekIsUUFBUSxFQUFFLFVBQVU7U0FDckIsQ0FBQyxDQUNILENBQUM7SUFDSixDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxzQ0FBc0MsRUFBRTtRQUN6QyxJQUFNLFNBQVMsR0FBRyxRQUFRLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2hELFNBQVMsQ0FBQyxTQUFTLEdBQUcsNkNBQTZDLENBQUM7UUFFcEUsSUFBTSxNQUFNLEdBQUcsa0JBQWtCLENBQUMsU0FBUyxDQUFDLENBQUM7UUFFN0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDekMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDbkMsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQztBQUVILFFBQVEsQ0FBQyxlQUFlLEVBQUU7SUFDeEIsRUFBRSxDQUFDLDJDQUEyQyxFQUFFO1FBQzlDLElBQUEsY0FBTSxFQUFDLGdDQUFLLFNBQVMsRUFBQyxTQUFTLHdDQUE4QixDQUFDLENBQUM7UUFFL0QsSUFBTSxPQUFPLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyx5QkFBeUIsQ0FBQyxDQUFDO1FBQzVELE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxXQUFXLENBQUMsU0FBUyxDQUFDLENBQUM7SUFDekMsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsd0NBQXdDLEVBQUU7UUFDM0MsSUFBQSxjQUFNLEVBQUMsZ0NBQUssU0FBUyxFQUFDLG9CQUFvQixnQ0FBc0IsQ0FBQyxDQUFDO1FBRWxFLElBQU0sT0FBTyxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsaUJBQWlCLENBQUMsQ0FBQztRQUNwRCxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsV0FBVyxDQUFDLG9CQUFvQixDQUFDLENBQUM7SUFDcEQsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMscUNBQXFDLEVBQUU7UUFDeEMsSUFBQSxjQUFNLEVBQUMsbUNBQVEsU0FBUyxFQUFDLGNBQWMsNkJBQXNCLENBQUMsQ0FBQztRQUUvRCxJQUFNLE1BQU0sR0FBRyxjQUFNLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLENBQUMsY0FBYyxDQUFDLENBQUM7SUFDN0MsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQztBQUVILFFBQVEsQ0FBQyxtQkFBbUIsRUFBRTtJQUM1QixFQUFFLENBQUMsK0RBQStELEVBQUU7Ozs7O29CQUM1RCxJQUFJLEdBQUcsb0JBQVMsQ0FBQyxLQUFLLEVBQUUsQ0FBQztvQkFFL0IsSUFBQSxjQUFNLEVBQ0osdUJBQUMsdUNBQW1CLElBQUMsUUFBUSxFQUFDLElBQUksRUFBQyxPQUFPLEVBQUMsSUFBSSxZQUM3Qyx3QkFBQyxnQ0FBYyxrQkFBWSxjQUFjLGFBQ3ZDLHdCQUFDLG1DQUFlLElBQUMsU0FBUyxFQUFFLEVBQUUsRUFBRSxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsS0FBSyxFQUFFLEVBQUUsR0FBRyxFQUFDLElBQUksYUFDNUQsdUJBQUMsaUNBQWUsSUFDZCxJQUFJLEVBQUMsV0FBVyxFQUNoQixLQUFLLEVBQUMsWUFBWSxFQUNsQixRQUFRLFNBQ1IsRUFDRix1QkFBQyxpQ0FBZSxJQUNkLElBQUksRUFBQyxVQUFVLEVBQ2YsS0FBSyxFQUFDLFdBQVcsRUFDakIsUUFBUSxTQUNSLElBQ2MsRUFFbEIsdUJBQUMsaUNBQWUsSUFDZCxJQUFJLEVBQUMsT0FBTyxFQUNaLEtBQUssRUFBQyxlQUFlLEVBQ3JCLElBQUksRUFBQyxPQUFPLEVBQ1osUUFBUSxRQUNSLFdBQVcsRUFBQyw4QkFBOEIsR0FDMUMsRUFFRix1QkFBQyx3Q0FBc0IsK0JBRUUsSUFDVixHQUNHLENBQ3ZCLENBQUM7b0JBR0ksSUFBSSxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUM7b0JBQ3RDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxlQUFlLENBQUMsWUFBWSxFQUFFLGNBQWMsQ0FBQyxDQUFDO29CQUdyRCxjQUFjLEdBQUcsY0FBTSxDQUFDLFNBQVMsQ0FBQyxTQUFTLEVBQUUsRUFBRSxJQUFJLEVBQUUsYUFBYSxFQUFFLENBQUMsQ0FBQztvQkFDdEUsVUFBVSxHQUFHLGNBQU0sQ0FBQyxTQUFTLENBQUMsU0FBUyxFQUFFLEVBQUUsSUFBSSxFQUFFLGdCQUFnQixFQUFFLENBQUMsQ0FBQztvQkFFM0UsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxlQUFlLEVBQUUsTUFBTSxDQUFDLENBQUM7b0JBQ2hFLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxlQUFlLENBQUMsZUFBZSxFQUFFLE1BQU0sQ0FBQyxDQUFDO29CQUU1RCwyQkFBMkI7b0JBQzNCLHFCQUFNLElBQUksQ0FBQyxHQUFHLEVBQUUsRUFBQTs7b0JBRGhCLDJCQUEyQjtvQkFDM0IsU0FBZ0IsQ0FBQztvQkFDakIsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO29CQUVyQyxxQkFBTSxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUE7O29CQUFoQixTQUFnQixDQUFDO29CQUNqQixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxTQUFTLEVBQUUsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO29CQUdwRSxTQUFTLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDO29CQUNuRCxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsV0FBVyxDQUFDLFVBQVUsQ0FBQyxDQUFDOzs7O1NBQzNDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIi9Vc2Vycy9kZDYwL2ZhYWZvL2ZhYWZvL2ZhYWZvLWNhcmVlci1wbGF0Zm9ybS9fX3Rlc3RzX18vdW5pdC9jb21wb25lbnRzL2FjY2Vzc2liaWxpdHktcmVzcG9uc2l2ZS50ZXN0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRlc3RzIGZvciBhY2Nlc3NpYmlsaXR5IGFuZCByZXNwb25zaXZlIGRlc2lnbiBpbXByb3ZlbWVudHNcbiAqL1xuXG5pbXBvcnQgeyByZW5kZXIsIHNjcmVlbiwgZmlyZUV2ZW50LCB3YWl0Rm9yIH0gZnJvbSAnQHRlc3RpbmctbGlicmFyeS9yZWFjdCc7XG5pbXBvcnQgdXNlckV2ZW50IGZyb20gJ0B0ZXN0aW5nLWxpYnJhcnkvdXNlci1ldmVudCc7XG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG4vLyBNb2NrIHRoZSBhY2Nlc3NpYmlsaXR5IHByb3ZpZGVyIGZvciB0ZXN0aW5nXG5jb25zdCBtb2NrQWNjZXNzaWJpbGl0eVByb3ZpZGVyID0ge1xuICBzZXR0aW5nczoge1xuICAgIHJlZHVjZWRNb3Rpb246IGZhbHNlLFxuICAgIGhpZ2hDb250cmFzdDogZmFsc2UsXG4gICAgbGFyZ2VUZXh0OiBmYWxzZSxcbiAgICBzY3JlZW5SZWFkZXJNb2RlOiBmYWxzZSxcbiAgICBrZXlib2FyZE5hdmlnYXRpb246IHRydWUsXG4gICAgZm9jdXNWaXNpYmxlOiB0cnVlLFxuICB9LFxuICB1cGRhdGVTZXR0aW5nOiBqZXN0LmZuKCksXG4gIGFubm91bmNlVG9TY3JlZW5SZWFkZXI6IGplc3QuZm4oKSxcbiAgc2V0Rm9jdXNUb0VsZW1lbnQ6IGplc3QuZm4oKSxcbiAgc2tpcFRvQ29udGVudDogamVzdC5mbigpLFxufTtcblxuamVzdC5tb2NrKCcuLi9ob29rcy91c2VBY2Nlc3NpYmlsaXR5RW5oYW5jZWQnLCAoKSA9PiAoe1xuICB1c2VBY2Nlc3NpYmlsaXR5RW5oYW5jZWQ6ICgpID0+ICh7XG4gICAgLi4ubW9ja0FjY2Vzc2liaWxpdHlQcm92aWRlcixcbiAgICBpc1JlZHVjZWRNb3Rpb246IGZhbHNlLFxuICAgIHByZWZlcnNIaWdoQ29udHJhc3Q6IGZhbHNlLFxuICAgIHByZWZlcnNEYXJrTW9kZTogZmFsc2UsXG4gIH0pLFxuICB1c2VBcmlhTGl2ZVJlZ2lvbjogKCkgPT4gKHtcbiAgICBhbm5vdW5jZTogamVzdC5mbigpLFxuICAgIGNyZWF0ZUxpdmVSZWdpb246ICgpID0+IFJlYWN0LmNyZWF0ZUVsZW1lbnQoJ2RpdicsIHsgJ2FyaWEtbGl2ZSc6ICdwb2xpdGUnLCBjbGFzc05hbWU6ICdzci1vbmx5JyB9KSxcbiAgfSksXG4gIHVzZUZvcm1BY2Nlc3NpYmlsaXR5OiAoKSA9PiAoe1xuICAgIGFubm91bmNlRXJyb3I6IGplc3QuZm4oKSxcbiAgICBhbm5vdW5jZVN1Y2Nlc3M6IGplc3QuZm4oKSxcbiAgICBnZXRGaWVsZFByb3BzOiAobmFtZTogc3RyaW5nLCBlcnJvcj86IHN0cmluZywgZGVzY3JpcHRpb24/OiBzdHJpbmcpID0+ICh7XG4gICAgICBpZDogbmFtZSxcbiAgICAgIG5hbWUsXG4gICAgICAnYXJpYS1yZXF1aXJlZCc6IHRydWUsXG4gICAgICAnYXJpYS1pbnZhbGlkJzogISFlcnJvcixcbiAgICAgICdhcmlhLWRlc2NyaWJlZGJ5JzogZXJyb3IgPyBgJHtuYW1lfS1lcnJvcmAgOiBkZXNjcmlwdGlvbiA/IGAke25hbWV9LWRlc2NyaXB0aW9uYCA6IHVuZGVmaW5lZCxcbiAgICB9KSxcbiAgfSksXG59KSk7XG5cbmplc3QubW9jaygnLi4vaG9va3MvdXNlUmVzcG9uc2l2ZURlc2lnbicsICgpID0+ICh7XG4gIHVzZVJlc3BvbnNpdmVEZXNpZ246ICgpID0+ICh7XG4gICAgd2luZG93U2l6ZTogeyB3aWR0aDogMTAyNCwgaGVpZ2h0OiA3NjggfSxcbiAgICBjdXJyZW50QnJlYWtwb2ludDogJ2xnJyxcbiAgICBpc01vYmlsZTogZmFsc2UsXG4gICAgaXNUYWJsZXQ6IGZhbHNlLFxuICAgIGlzRGVza3RvcDogdHJ1ZSxcbiAgICBnZXRSZXNwb25zaXZlVmFsdWU6ICh2YWx1ZXM6IGFueSkgPT4gdmFsdWVzLmxnIHx8IHZhbHVlcy5tZCB8fCB2YWx1ZXMuc20gfHwgdmFsdWVzLnhzLFxuICB9KSxcbiAgdXNlVG91Y2hEZXZpY2U6ICgpID0+ICh7XG4gICAgaXNUb3VjaERldmljZTogZmFsc2UsXG4gICAgaGFzSG92ZXI6IHRydWUsXG4gIH0pLFxufSkpO1xuXG4vLyBNb2NrIGFjY2Vzc2liaWxpdHkgYXVkaXQgdXRpbGl0eVxuamVzdC5tb2NrKCcuLi91dGlscy9hY2Nlc3NpYmlsaXR5LWF1ZGl0JywgKCkgPT4gKHtcbiAgYXVkaXRBY2Nlc3NpYmlsaXR5OiBqZXN0LmZuKChjb250YWluZXI/OiBFbGVtZW50KSA9PiB7XG4gICAgY29uc3QgaXNzdWVzID0gW107XG5cbiAgICAvLyBDaGVjayBmb3IgaW1hZ2VzIHdpdGhvdXQgYWx0IHRleHRcbiAgICBpZiAoY29udGFpbmVyPy5xdWVyeVNlbGVjdG9yKCdpbWc6bm90KFthbHRdKScpKSB7XG4gICAgICBpc3N1ZXMucHVzaCh7XG4gICAgICAgIHJ1bGU6ICdpbWctYWx0JyxcbiAgICAgICAgc2V2ZXJpdHk6ICdzZXJpb3VzJyxcbiAgICAgICAgbWVzc2FnZTogJ0ltYWdlIG1pc3NpbmcgYWx0IHRleHQnLFxuICAgICAgICBlbGVtZW50OiBjb250YWluZXIucXVlcnlTZWxlY3RvcignaW1nOm5vdChbYWx0XSknKSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBmb3JtIGNvbnRyb2xzIHdpdGhvdXQgbGFiZWxzXG4gICAgaWYgKGNvbnRhaW5lcj8ucXVlcnlTZWxlY3RvcignaW5wdXQ6bm90KFthcmlhLWxhYmVsXSk6bm90KFthcmlhLWxhYmVsbGVkYnldKScpKSB7XG4gICAgICBpc3N1ZXMucHVzaCh7XG4gICAgICAgIHJ1bGU6ICdmb3JtLWxhYmVsJyxcbiAgICAgICAgc2V2ZXJpdHk6ICdjcml0aWNhbCcsXG4gICAgICAgIG1lc3NhZ2U6ICdGb3JtIGNvbnRyb2wgbWlzc2luZyBhY2Nlc3NpYmxlIGxhYmVsJyxcbiAgICAgICAgZWxlbWVudDogY29udGFpbmVyLnF1ZXJ5U2VsZWN0b3IoJ2lucHV0Om5vdChbYXJpYS1sYWJlbF0pOm5vdChbYXJpYS1sYWJlbGxlZGJ5XSknKSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBoZWFkaW5nIGhpZXJhcmNoeSAtIGgzIGFmdGVyIGgxIHdpdGhvdXQgaDJcbiAgICBpZiAoY29udGFpbmVyPy5xdWVyeVNlbGVjdG9yKCdoMScpICYmIGNvbnRhaW5lcj8ucXVlcnlTZWxlY3RvcignaDMnKSAmJiAhY29udGFpbmVyPy5xdWVyeVNlbGVjdG9yKCdoMicpKSB7XG4gICAgICBpc3N1ZXMucHVzaCh7XG4gICAgICAgIHJ1bGU6ICdoZWFkaW5nLWhpZXJhcmNoeScsXG4gICAgICAgIHNldmVyaXR5OiAnbW9kZXJhdGUnLFxuICAgICAgICBtZXNzYWdlOiAnSW1wcm9wZXIgaGVhZGluZyBoaWVyYXJjaHknLFxuICAgICAgICBlbGVtZW50OiBjb250YWluZXIucXVlcnlTZWxlY3RvcignaDMnKSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzY29yZTogaXNzdWVzLmxlbmd0aCA9PT0gMCA/IDEwMCA6IE1hdGgubWF4KDAsIDEwMCAtIChpc3N1ZXMubGVuZ3RoICogMTApKSxcbiAgICAgIHBhc3NlZDogaXNzdWVzLmxlbmd0aCA9PT0gMCxcbiAgICAgIGlzc3VlcyxcbiAgICAgIHN1bW1hcnk6IHtcbiAgICAgICAgY3JpdGljYWw6IGlzc3Vlcy5maWx0ZXIoaSA9PiBpLnNldmVyaXR5ID09PSAnY3JpdGljYWwnKS5sZW5ndGgsXG4gICAgICAgIHNlcmlvdXM6IGlzc3Vlcy5maWx0ZXIoaSA9PiBpLnNldmVyaXR5ID09PSAnc2VyaW91cycpLmxlbmd0aCxcbiAgICAgICAgbW9kZXJhdGU6IGlzc3Vlcy5maWx0ZXIoaSA9PiBpLnNldmVyaXR5ID09PSAnbW9kZXJhdGUnKS5sZW5ndGgsXG4gICAgICAgIG1pbm9yOiBpc3N1ZXMuZmlsdGVyKGkgPT4gaS5zZXZlcml0eSA9PT0gJ21pbm9yJykubGVuZ3RoLFxuICAgICAgfSxcbiAgICB9O1xuICB9KSxcbn0pKTtcblxuaW1wb3J0IHsgQWNjZXNzaWJsZUZvcm0sIEFjY2Vzc2libGVGaWVsZCwgQWNjZXNzaWJsZVN1Ym1pdEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hY2Nlc3NpYmxlLWZvcm0nO1xuaW1wb3J0IHsgUmVzcG9uc2l2ZUNvbnRhaW5lciwgUmVzcG9uc2l2ZUdyaWQsIFJlc3BvbnNpdmVTdGFjayB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9yZXNwb25zaXZlLWxheW91dCc7XG5cbi8vIEltcG9ydCB0aGUgbW9ja2VkIGZ1bmN0aW9uXG5jb25zdCB7IGF1ZGl0QWNjZXNzaWJpbGl0eSB9ID0gcmVxdWlyZSgnLi4vdXRpbHMvYWNjZXNzaWJpbGl0eS1hdWRpdCcpO1xuXG4vLyBNb2NrIHdpbmRvdy5tYXRjaE1lZGlhIGZvciByZXNwb25zaXZlIHRlc3RzXG5jb25zdCBtb2NrTWF0Y2hNZWRpYSA9IChxdWVyeTogc3RyaW5nKSA9PiAoe1xuICBtYXRjaGVzOiBmYWxzZSxcbiAgbWVkaWE6IHF1ZXJ5LFxuICBvbmNoYW5nZTogbnVsbCxcbiAgYWRkTGlzdGVuZXI6IGplc3QuZm4oKSxcbiAgcmVtb3ZlTGlzdGVuZXI6IGplc3QuZm4oKSxcbiAgYWRkRXZlbnRMaXN0ZW5lcjogamVzdC5mbigpLFxuICByZW1vdmVFdmVudExpc3RlbmVyOiBqZXN0LmZuKCksXG4gIGRpc3BhdGNoRXZlbnQ6IGplc3QuZm4oKSxcbn0pO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkod2luZG93LCAnbWF0Y2hNZWRpYScsIHtcbiAgd3JpdGFibGU6IHRydWUsXG4gIHZhbHVlOiBqZXN0LmZuKCkubW9ja0ltcGxlbWVudGF0aW9uKG1vY2tNYXRjaE1lZGlhKSxcbn0pO1xuXG4vLyBNb2NrIFJlc2l6ZU9ic2VydmVyXG5nbG9iYWwuUmVzaXplT2JzZXJ2ZXIgPSBqZXN0LmZuKCkubW9ja0ltcGxlbWVudGF0aW9uKCgpID0+ICh7XG4gIG9ic2VydmU6IGplc3QuZm4oKSxcbiAgdW5vYnNlcnZlOiBqZXN0LmZuKCksXG4gIGRpc2Nvbm5lY3Q6IGplc3QuZm4oKSxcbn0pKTtcblxuZGVzY3JpYmUoJ0FjY2Vzc2liaWxpdHkgSW1wcm92ZW1lbnRzJywgKCkgPT4ge1xuICBkZXNjcmliZSgnQWNjZXNzaWJsZUZvcm0nLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCByZW5kZXIgZm9ybSB3aXRoIHByb3BlciBBUklBIGF0dHJpYnV0ZXMnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoXG4gICAgICAgIDxBY2Nlc3NpYmxlRm9ybSBhcmlhLWxhYmVsPVwiVGVzdCBmb3JtXCI+XG4gICAgICAgICAgPEFjY2Vzc2libGVGaWVsZFxuICAgICAgICAgICAgbmFtZT1cImVtYWlsXCJcbiAgICAgICAgICAgIGxhYmVsPVwiRW1haWwgQWRkcmVzc1wiXG4gICAgICAgICAgICB0eXBlPVwiZW1haWxcIlxuICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAvPlxuICAgICAgICA8L0FjY2Vzc2libGVGb3JtPlxuICAgICAgKTtcblxuICAgICAgY29uc3QgZm9ybSA9IHNjcmVlbi5nZXRCeVJvbGUoJ2Zvcm0nKTtcbiAgICAgIGV4cGVjdChmb3JtKS50b0hhdmVBdHRyaWJ1dGUoJ2FyaWEtbGFiZWwnLCAnVGVzdCBmb3JtJyk7XG4gICAgICBleHBlY3QoZm9ybSkudG9IYXZlQXR0cmlidXRlKCdub3ZhbGlkYXRlJyk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGFzc29jaWF0ZSBsYWJlbHMgd2l0aCBmb3JtIGZpZWxkcycsICgpID0+IHtcbiAgICAgIHJlbmRlcihcbiAgICAgICAgPEFjY2Vzc2libGVGb3JtPlxuICAgICAgICAgIDxBY2Nlc3NpYmxlRmllbGRcbiAgICAgICAgICAgIG5hbWU9XCJlbWFpbFwiXG4gICAgICAgICAgICBsYWJlbD1cIkVtYWlsIEFkZHJlc3NcIlxuICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9BY2Nlc3NpYmxlRm9ybT5cbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IGlucHV0ID0gc2NyZWVuLmdldEJ5Um9sZSgndGV4dGJveCcsIHsgbmFtZTogL2VtYWlsIGFkZHJlc3MvaSB9KTtcbiAgICAgIGNvbnN0IGxhYmVsID0gc2NyZWVuLmdldEJ5VGV4dCgnRW1haWwgQWRkcmVzcycpO1xuICAgICAgXG4gICAgICBleHBlY3QoaW5wdXQpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3QobGFiZWwpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3QoaW5wdXQpLnRvSGF2ZUF0dHJpYnV0ZSgnYXJpYS1yZXF1aXJlZCcsICd0cnVlJyk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGRpc3BsYXkgdmFsaWRhdGlvbiBlcnJvcnMgd2l0aCBwcm9wZXIgQVJJQSBhdHRyaWJ1dGVzJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgdXNlciA9IHVzZXJFdmVudC5zZXR1cCgpO1xuICAgICAgXG4gICAgICByZW5kZXIoXG4gICAgICAgIDxBY2Nlc3NpYmxlRm9ybT5cbiAgICAgICAgICA8QWNjZXNzaWJsZUZpZWxkXG4gICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxuICAgICAgICAgICAgbGFiZWw9XCJFbWFpbCBBZGRyZXNzXCJcbiAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgZXJyb3I9XCJQbGVhc2UgZW50ZXIgYSB2YWxpZCBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L0FjY2Vzc2libGVGb3JtPlxuICAgICAgKTtcblxuICAgICAgY29uc3QgaW5wdXQgPSBzY3JlZW4uZ2V0QnlSb2xlKCd0ZXh0Ym94JywgeyBuYW1lOiAvZW1haWwgYWRkcmVzcy9pIH0pO1xuICAgICAgXG4gICAgICAvLyBUcmlnZ2VyIHZhbGlkYXRpb24gYnkgZm9jdXNpbmcgYW5kIGJsdXJyaW5nXG4gICAgICBhd2FpdCB1c2VyLmNsaWNrKGlucHV0KTtcbiAgICAgIGF3YWl0IHVzZXIudGFiKCk7XG5cbiAgICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBzY3JlZW4uZ2V0QnlSb2xlKCdhbGVydCcpO1xuICAgICAgICBleHBlY3QoZXJyb3JNZXNzYWdlKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgICBleHBlY3QoZXJyb3JNZXNzYWdlKS50b0hhdmVUZXh0Q29udGVudCgnUGxlYXNlIGVudGVyIGEgdmFsaWQgZW1haWwgYWRkcmVzcycpO1xuICAgICAgICBleHBlY3QoaW5wdXQpLnRvSGF2ZUF0dHJpYnV0ZSgnYXJpYS1pbnZhbGlkJywgJ3RydWUnKTtcbiAgICAgIH0pO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBzaG93IHJlcXVpcmVkIGZpZWxkIGluZGljYXRvcnMnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoXG4gICAgICAgIDxBY2Nlc3NpYmxlRm9ybT5cbiAgICAgICAgICA8QWNjZXNzaWJsZUZpZWxkXG4gICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxuICAgICAgICAgICAgbGFiZWw9XCJFbWFpbCBBZGRyZXNzXCJcbiAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgIC8+XG4gICAgICAgIDwvQWNjZXNzaWJsZUZvcm0+XG4gICAgICApO1xuXG4gICAgICBjb25zdCBsYWJlbCA9IHNjcmVlbi5nZXRCeVRleHQoJ0VtYWlsIEFkZHJlc3MnKTtcbiAgICAgIGV4cGVjdChsYWJlbCkudG9IYXZlQ2xhc3MoJ2FmdGVyOmNvbnRlbnQtW1xcJypcXCddJyk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdBY2Nlc3NpYmxlU3VibWl0QnV0dG9uJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgc2hvdyBsb2FkaW5nIHN0YXRlIHdpdGggcHJvcGVyIGFubm91bmNlbWVudHMnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoXG4gICAgICAgIDxBY2Nlc3NpYmxlU3VibWl0QnV0dG9uIGlzTG9hZGluZyBsb2FkaW5nVGV4dD1cIlN1Ym1pdHRpbmcgZm9ybS4uLlwiPlxuICAgICAgICAgIFN1Ym1pdFxuICAgICAgICA8L0FjY2Vzc2libGVTdWJtaXRCdXR0b24+XG4gICAgICApO1xuXG4gICAgICBjb25zdCBidXR0b24gPSBzY3JlZW4uZ2V0QnlSb2xlKCdidXR0b24nKTtcbiAgICAgIGV4cGVjdChidXR0b24pLnRvQmVEaXNhYmxlZCgpO1xuICAgICAgZXhwZWN0KGJ1dHRvbikudG9IYXZlVGV4dENvbnRlbnQoJ1N1Ym1pdHRpbmcgZm9ybS4uLicpO1xuICAgICAgXG4gICAgICBjb25zdCBsb2FkaW5nU3RhdHVzID0gc2NyZWVuLmdldEJ5VGV4dCgnRm9ybSBpcyBiZWluZyBzdWJtaXR0ZWQsIHBsZWFzZSB3YWl0Jyk7XG4gICAgICBleHBlY3QobG9hZGluZ1N0YXR1cykudG9IYXZlQ2xhc3MoJ3NyLW9ubHknKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgYmUga2V5Ym9hcmQgYWNjZXNzaWJsZScsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHVzZXIgPSB1c2VyRXZlbnQuc2V0dXAoKTtcbiAgICAgIGNvbnN0IGhhbmRsZUNsaWNrID0gamVzdC5mbigpO1xuXG4gICAgICByZW5kZXIoXG4gICAgICAgIDxBY2Nlc3NpYmxlU3VibWl0QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfT5cbiAgICAgICAgICBTdWJtaXRcbiAgICAgICAgPC9BY2Nlc3NpYmxlU3VibWl0QnV0dG9uPlxuICAgICAgKTtcblxuICAgICAgY29uc3QgYnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJyk7XG5cbiAgICAgIC8vIFRlc3QgdGhhdCBidXR0b24gaXMgZm9jdXNhYmxlIGFuZCBjbGlja2FibGVcbiAgICAgIGF3YWl0IHVzZXIudGFiKCk7XG4gICAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVGb2N1cygpO1xuXG4gICAgICAvLyBUZXN0IGJ1dHRvbiBjbGljayBmdW5jdGlvbmFsaXR5XG4gICAgICBhd2FpdCB1c2VyLmNsaWNrKGJ1dHRvbik7XG4gICAgICBleHBlY3QoaGFuZGxlQ2xpY2spLnRvSGF2ZUJlZW5DYWxsZWQoKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ1RvdWNoIFRhcmdldCBDb21wbGlhbmNlJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgbWVldCBtaW5pbXVtIHRvdWNoIHRhcmdldCBzaXplIHJlcXVpcmVtZW50cycsICgpID0+IHtcbiAgICAgIHJlbmRlcihcbiAgICAgICAgPEFjY2Vzc2libGVTdWJtaXRCdXR0b24+XG4gICAgICAgICAgU3VibWl0XG4gICAgICAgIDwvQWNjZXNzaWJsZVN1Ym1pdEJ1dHRvbj5cbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IGJ1dHRvbiA9IHNjcmVlbi5nZXRCeVJvbGUoJ2J1dHRvbicpO1xuICAgICAgZXhwZWN0KGJ1dHRvbikudG9IYXZlQ2xhc3MoJ21pbi1oLVs0NHB4XScpO1xuICAgIH0pO1xuICB9KTtcbn0pO1xuXG5kZXNjcmliZSgnUmVzcG9uc2l2ZSBEZXNpZ24gSW1wcm92ZW1lbnRzJywgKCkgPT4ge1xuICBkZXNjcmliZSgnUmVzcG9uc2l2ZUNvbnRhaW5lcicsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIHJlbmRlciB3aXRoIHJlc3BvbnNpdmUgY2xhc3NlcycsICgpID0+IHtcbiAgICAgIHJlbmRlcihcbiAgICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgbWF4V2lkdGg9XCJ4bFwiIHBhZGRpbmc9XCJtZFwiPlxuICAgICAgICAgIDxkaXY+Q29udGVudDwvZGl2PlxuICAgICAgICA8L1Jlc3BvbnNpdmVDb250YWluZXI+XG4gICAgICApO1xuXG4gICAgICBjb25zdCBjb250YWluZXIgPSBzY3JlZW4uZ2V0QnlUZXh0KCdDb250ZW50JykucGFyZW50RWxlbWVudDtcbiAgICAgIGV4cGVjdChjb250YWluZXIpLnRvSGF2ZUNsYXNzKCdteC1hdXRvJywgJ3ctZnVsbCcsICdtYXgtdy14bCcpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBhcHBseSBkaWZmZXJlbnQgcGFkZGluZyBiYXNlZCBvbiBzY3JlZW4gc2l6ZScsICgpID0+IHtcbiAgICAgIC8vIE1vY2sgbW9iaWxlIHZpZXdwb3J0XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkod2luZG93LCAnaW5uZXJXaWR0aCcsIHtcbiAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgdmFsdWU6IDM3NSxcbiAgICAgIH0pO1xuXG4gICAgICByZW5kZXIoXG4gICAgICAgIDxSZXNwb25zaXZlQ29udGFpbmVyIHBhZGRpbmc9XCJtZFwiPlxuICAgICAgICAgIDxkaXY+TW9iaWxlIENvbnRlbnQ8L2Rpdj5cbiAgICAgICAgPC9SZXNwb25zaXZlQ29udGFpbmVyPlxuICAgICAgKTtcblxuICAgICAgY29uc3QgY29udGFpbmVyID0gc2NyZWVuLmdldEJ5VGV4dCgnTW9iaWxlIENvbnRlbnQnKS5wYXJlbnRFbGVtZW50O1xuICAgICAgLy8gQ2hlY2sgZm9yIHRoZSBhY3R1YWwgY2xhc3NlcyBhcHBsaWVkIGJ5IFJlc3BvbnNpdmVDb250YWluZXJcbiAgICAgIGV4cGVjdChjb250YWluZXIpLnRvSGF2ZUNsYXNzKCdteC1hdXRvJywgJ3ctZnVsbCcpO1xuICAgICAgZXhwZWN0KGNvbnRhaW5lcikudG9IYXZlQ2xhc3MoJ3B4LTgnKTsgLy8gRGVmYXVsdCBwYWRkaW5nXG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdSZXNwb25zaXZlR3JpZCcsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIHJlbmRlciB3aXRoIHJlc3BvbnNpdmUgZ3JpZCBjbGFzc2VzJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKFxuICAgICAgICA8UmVzcG9uc2l2ZUdyaWQgY29scz17eyB4czogMSwgbWQ6IDIsIGxnOiAzIH19IGdhcD1cIm1kXCI+XG4gICAgICAgICAgPGRpdj5JdGVtIDE8L2Rpdj5cbiAgICAgICAgICA8ZGl2Pkl0ZW0gMjwvZGl2PlxuICAgICAgICAgIDxkaXY+SXRlbSAzPC9kaXY+XG4gICAgICAgIDwvUmVzcG9uc2l2ZUdyaWQ+XG4gICAgICApO1xuXG4gICAgICBjb25zdCBncmlkID0gc2NyZWVuLmdldEJ5VGV4dCgnSXRlbSAxJykucGFyZW50RWxlbWVudDtcbiAgICAgIGV4cGVjdChncmlkKS50b0hhdmVDbGFzcygnZ3JpZCcsICdnYXAtNCcpO1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnUmVzcG9uc2l2ZVN0YWNrJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgcmVuZGVyIHdpdGggcmVzcG9uc2l2ZSBmbGV4IGNsYXNzZXMnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoXG4gICAgICAgIDxSZXNwb25zaXZlU3RhY2sgZGlyZWN0aW9uPXt7IHhzOiAnY29sJywgbWQ6ICdyb3cnIH19IGdhcD1cImxnXCI+XG4gICAgICAgICAgPGRpdj5JdGVtIDE8L2Rpdj5cbiAgICAgICAgICA8ZGl2Pkl0ZW0gMjwvZGl2PlxuICAgICAgICA8L1Jlc3BvbnNpdmVTdGFjaz5cbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IHN0YWNrID0gc2NyZWVuLmdldEJ5VGV4dCgnSXRlbSAxJykucGFyZW50RWxlbWVudDtcbiAgICAgIGV4cGVjdChzdGFjaykudG9IYXZlQ2xhc3MoJ2ZsZXgnLCAnZ2FwLTYnKTtcbiAgICB9KTtcbiAgfSk7XG59KTtcblxuZGVzY3JpYmUoJ0FjY2Vzc2liaWxpdHkgQXVkaXQgVXRpbGl0eScsICgpID0+IHtcbiAgaXQoJ3Nob3VsZCBkZXRlY3QgbWlzc2luZyBhbHQgdGV4dCBvbiBpbWFnZXMnLCAoKSA9PiB7XG4gICAgY29uc3QgY29udGFpbmVyID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gICAgY29udGFpbmVyLmlubmVySFRNTCA9ICc8aW1nIHNyYz1cInRlc3QuanBnXCIgLz4nO1xuICAgIFxuICAgIGNvbnN0IHJlc3VsdCA9IGF1ZGl0QWNjZXNzaWJpbGl0eShjb250YWluZXIpO1xuICAgIFxuICAgIGV4cGVjdChyZXN1bHQuaXNzdWVzKS50b0NvbnRhaW5FcXVhbChcbiAgICAgIGV4cGVjdC5vYmplY3RDb250YWluaW5nKHtcbiAgICAgICAgcnVsZTogJ2ltZy1hbHQnLFxuICAgICAgICBzZXZlcml0eTogJ3NlcmlvdXMnLFxuICAgICAgICBtZXNzYWdlOiAnSW1hZ2UgbWlzc2luZyBhbHQgdGV4dCdcbiAgICAgIH0pXG4gICAgKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCBkZXRlY3QgZm9ybSBjb250cm9scyB3aXRob3V0IGxhYmVscycsICgpID0+IHtcbiAgICBjb25zdCBjb250YWluZXIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKTtcbiAgICBjb250YWluZXIuaW5uZXJIVE1MID0gJzxpbnB1dCB0eXBlPVwidGV4dFwiIC8+JztcbiAgICBcbiAgICBjb25zdCByZXN1bHQgPSBhdWRpdEFjY2Vzc2liaWxpdHkoY29udGFpbmVyKTtcbiAgICBcbiAgICBleHBlY3QocmVzdWx0Lmlzc3VlcykudG9Db250YWluRXF1YWwoXG4gICAgICBleHBlY3Qub2JqZWN0Q29udGFpbmluZyh7XG4gICAgICAgIHJ1bGU6ICdmb3JtLWxhYmVsJyxcbiAgICAgICAgc2V2ZXJpdHk6ICdjcml0aWNhbCcsXG4gICAgICAgIG1lc3NhZ2U6ICdGb3JtIGNvbnRyb2wgbWlzc2luZyBhY2Nlc3NpYmxlIGxhYmVsJ1xuICAgICAgfSlcbiAgICApO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIGRldGVjdCBpbXByb3BlciBoZWFkaW5nIGhpZXJhcmNoeScsICgpID0+IHtcbiAgICBjb25zdCBjb250YWluZXIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKTtcbiAgICBjb250YWluZXIuaW5uZXJIVE1MID0gJzxoMT5UaXRsZTwvaDE+PGgzPlN1YnRpdGxlPC9oMz4nO1xuICAgIFxuICAgIGNvbnN0IHJlc3VsdCA9IGF1ZGl0QWNjZXNzaWJpbGl0eShjb250YWluZXIpO1xuICAgIFxuICAgIGV4cGVjdChyZXN1bHQuaXNzdWVzKS50b0NvbnRhaW5FcXVhbChcbiAgICAgIGV4cGVjdC5vYmplY3RDb250YWluaW5nKHtcbiAgICAgICAgcnVsZTogJ2hlYWRpbmctaGllcmFyY2h5JyxcbiAgICAgICAgc2V2ZXJpdHk6ICdtb2RlcmF0ZSdcbiAgICAgIH0pXG4gICAgKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCBjYWxjdWxhdGUgYWNjZXNzaWJpbGl0eSBzY29yZScsICgpID0+IHtcbiAgICBjb25zdCBjb250YWluZXIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKTtcbiAgICBjb250YWluZXIuaW5uZXJIVE1MID0gJzxoMT5QZXJmZWN0IFBhZ2U8L2gxPjxwPk5vIGlzc3VlcyBoZXJlITwvcD4nO1xuICAgIFxuICAgIGNvbnN0IHJlc3VsdCA9IGF1ZGl0QWNjZXNzaWJpbGl0eShjb250YWluZXIpO1xuICAgIFxuICAgIGV4cGVjdChyZXN1bHQuc2NvcmUpLnRvQmVHcmVhdGVyVGhhbig5MCk7XG4gICAgZXhwZWN0KHJlc3VsdC5wYXNzZWQpLnRvQmUodHJ1ZSk7XG4gIH0pO1xufSk7XG5cbmRlc2NyaWJlKCdDU1MgVXRpbGl0aWVzJywgKCkgPT4ge1xuICBpdCgnc2hvdWxkIGluY2x1ZGUgc2NyZWVuIHJlYWRlciBvbmx5IGNsYXNzZXMnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxkaXYgY2xhc3NOYW1lPVwic3Itb25seVwiPlNjcmVlbiByZWFkZXIgb25seSB0ZXh0PC9kaXY+KTtcbiAgICBcbiAgICBjb25zdCBlbGVtZW50ID0gc2NyZWVuLmdldEJ5VGV4dCgnU2NyZWVuIHJlYWRlciBvbmx5IHRleHQnKTtcbiAgICBleHBlY3QoZWxlbWVudCkudG9IYXZlQ2xhc3MoJ3NyLW9ubHknKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCBpbmNsdWRlIHJlc3BvbnNpdmUgdGV4dCBjbGFzc2VzJywgKCkgPT4ge1xuICAgIHJlbmRlcig8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmVzcG9uc2l2ZS1sZ1wiPlJlc3BvbnNpdmUgdGV4dDwvZGl2Pik7XG4gICAgXG4gICAgY29uc3QgZWxlbWVudCA9IHNjcmVlbi5nZXRCeVRleHQoJ1Jlc3BvbnNpdmUgdGV4dCcpO1xuICAgIGV4cGVjdChlbGVtZW50KS50b0hhdmVDbGFzcygndGV4dC1yZXNwb25zaXZlLWxnJyk7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgaW5jbHVkZSB0b3VjaCB0YXJnZXQgY2xhc3NlcycsICgpID0+IHtcbiAgICByZW5kZXIoPGJ1dHRvbiBjbGFzc05hbWU9XCJ0b3VjaC10YXJnZXRcIj5Ub3VjaCBidXR0b248L2J1dHRvbj4pO1xuICAgIFxuICAgIGNvbnN0IGJ1dHRvbiA9IHNjcmVlbi5nZXRCeVJvbGUoJ2J1dHRvbicpO1xuICAgIGV4cGVjdChidXR0b24pLnRvSGF2ZUNsYXNzKCd0b3VjaC10YXJnZXQnKTtcbiAgfSk7XG59KTtcblxuZGVzY3JpYmUoJ0ludGVncmF0aW9uIFRlc3RzJywgKCkgPT4ge1xuICBpdCgnc2hvdWxkIHdvcmsgdG9nZXRoZXIgLSBhY2Nlc3NpYmxlIGZvcm0gd2l0aCByZXNwb25zaXZlIGxheW91dCcsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB1c2VyID0gdXNlckV2ZW50LnNldHVwKCk7XG4gICAgXG4gICAgcmVuZGVyKFxuICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgbWF4V2lkdGg9XCJtZFwiIHBhZGRpbmc9XCJsZ1wiPlxuICAgICAgICA8QWNjZXNzaWJsZUZvcm0gYXJpYS1sYWJlbD1cIkNvbnRhY3QgZm9ybVwiPlxuICAgICAgICAgIDxSZXNwb25zaXZlU3RhY2sgZGlyZWN0aW9uPXt7IHhzOiAnY29sJywgbWQ6ICdyb3cnIH19IGdhcD1cIm1kXCI+XG4gICAgICAgICAgICA8QWNjZXNzaWJsZUZpZWxkXG4gICAgICAgICAgICAgIG5hbWU9XCJmaXJzdE5hbWVcIlxuICAgICAgICAgICAgICBsYWJlbD1cIkZpcnN0IE5hbWVcIlxuICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxBY2Nlc3NpYmxlRmllbGRcbiAgICAgICAgICAgICAgbmFtZT1cImxhc3ROYW1lXCJcbiAgICAgICAgICAgICAgbGFiZWw9XCJMYXN0IE5hbWVcIlxuICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L1Jlc3BvbnNpdmVTdGFjaz5cbiAgICAgICAgICBcbiAgICAgICAgICA8QWNjZXNzaWJsZUZpZWxkXG4gICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxuICAgICAgICAgICAgbGFiZWw9XCJFbWFpbCBBZGRyZXNzXCJcbiAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgZGVzY3JpcHRpb249XCJXZSdsbCBuZXZlciBzaGFyZSB5b3VyIGVtYWlsXCJcbiAgICAgICAgICAvPlxuICAgICAgICAgIFxuICAgICAgICAgIDxBY2Nlc3NpYmxlU3VibWl0QnV0dG9uPlxuICAgICAgICAgICAgU2VuZCBNZXNzYWdlXG4gICAgICAgICAgPC9BY2Nlc3NpYmxlU3VibWl0QnV0dG9uPlxuICAgICAgICA8L0FjY2Vzc2libGVGb3JtPlxuICAgICAgPC9SZXNwb25zaXZlQ29udGFpbmVyPlxuICAgICk7XG5cbiAgICAvLyBUZXN0IGZvcm0gYWNjZXNzaWJpbGl0eVxuICAgIGNvbnN0IGZvcm0gPSBzY3JlZW4uZ2V0QnlSb2xlKCdmb3JtJyk7XG4gICAgZXhwZWN0KGZvcm0pLnRvSGF2ZUF0dHJpYnV0ZSgnYXJpYS1sYWJlbCcsICdDb250YWN0IGZvcm0nKTtcblxuICAgIC8vIFRlc3QgZmllbGQgYWNjZXNzaWJpbGl0eVxuICAgIGNvbnN0IGZpcnN0TmFtZUlucHV0ID0gc2NyZWVuLmdldEJ5Um9sZSgndGV4dGJveCcsIHsgbmFtZTogL2ZpcnN0IG5hbWUvaSB9KTtcbiAgICBjb25zdCBlbWFpbElucHV0ID0gc2NyZWVuLmdldEJ5Um9sZSgndGV4dGJveCcsIHsgbmFtZTogL2VtYWlsIGFkZHJlc3MvaSB9KTtcbiAgICBcbiAgICBleHBlY3QoZmlyc3ROYW1lSW5wdXQpLnRvSGF2ZUF0dHJpYnV0ZSgnYXJpYS1yZXF1aXJlZCcsICd0cnVlJyk7XG4gICAgZXhwZWN0KGVtYWlsSW5wdXQpLnRvSGF2ZUF0dHJpYnV0ZSgnYXJpYS1yZXF1aXJlZCcsICd0cnVlJyk7XG5cbiAgICAvLyBUZXN0IGtleWJvYXJkIG5hdmlnYXRpb25cbiAgICBhd2FpdCB1c2VyLnRhYigpO1xuICAgIGV4cGVjdChmaXJzdE5hbWVJbnB1dCkudG9IYXZlRm9jdXMoKTtcbiAgICBcbiAgICBhd2FpdCB1c2VyLnRhYigpO1xuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlSb2xlKCd0ZXh0Ym94JywgeyBuYW1lOiAvbGFzdCBuYW1lL2kgfSkpLnRvSGF2ZUZvY3VzKCk7XG5cbiAgICAvLyBUZXN0IHJlc3BvbnNpdmUgbGF5b3V0IGNsYXNzZXNcbiAgICBjb25zdCBjb250YWluZXIgPSBmb3JtLmNsb3Nlc3QoJ1tjbGFzcyo9XCJtYXgtd1wiXScpO1xuICAgIGV4cGVjdChjb250YWluZXIpLnRvSGF2ZUNsYXNzKCdtYXgtdy1tZCcpO1xuICB9KTtcbn0pO1xuIl0sInZlcnNpb24iOjN9