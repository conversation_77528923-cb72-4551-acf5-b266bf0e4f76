{"version": 3, "names": ["exports", "withErrorReporting", "cov_yk9vl9qyc", "s", "useErrorReporting", "react_1", "__importDefault", "require", "Sentry", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f", "captureError", "context", "b", "withScope", "scope", "userId", "setUser", "id", "email", "userEmail", "action", "setTag", "component", "additionalData", "setContext", "captureException", "console", "captureMessage", "message", "level", "log", "concat", "toUpperCase", "addBreadcrumb", "category", "data", "__assign", "clearUser", "startTransaction", "name", "operation", "startSpan", "op", "measureAsync", "fn", "Promise", "__awaiter", "_this", "result", "_a", "sent", "error_2", "error_1", "WrappedComponent", "componentName", "WithErrorReportingComponent", "props", "default", "useEffect", "createElement", "displayName", "reportError", "useCallback", "reportMessage"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/errorReporting.ts"], "sourcesContent": ["import React from 'react';\n\n// Conditional Sentry import\nlet Sentry: any = null;\ntry {\n  Sentry = require('@sentry/nextjs');\n} catch (error) {\n  // Sentry not available, will use console logging instead\n}\n\nexport interface ErrorContext {\n  userId?: string;\n  userEmail?: string;\n  action?: string;\n  component?: string;\n  additionalData?: Record<string, any>;\n}\n\nexport class ErrorReporter {\n  /**\n   * Report an error to <PERSON>try with additional context\n   */\n  static captureError(error: Error, context?: ErrorContext) {\n    if (Sentry) {\n      Sentry.withScope((scope: any) => {\n        if (context?.userId) {\n          scope.setUser({ id: context.userId, email: context.userEmail });\n        }\n\n        if (context?.action) {\n          scope.setTag('action', context.action);\n        }\n\n        if (context?.component) {\n          scope.setTag('component', context.component);\n        }\n\n        if (context?.additionalData) {\n          scope.setContext('additionalData', context.additionalData);\n        }\n\n        Sentry.captureException(error);\n      });\n    } else {\n      // Fallback to console logging when Sentry is not available\n      console.error('Error captured:', error);\n      if (context) {\n        console.error('Error context:', context);\n      }\n    }\n  }\n\n  /**\n   * Report a message to <PERSON><PERSON> (for non-error events)\n   */\n  static captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context?: ErrorContext) {\n    if (Sentry) {\n      Sentry.withScope((scope: any) => {\n        if (context?.userId) {\n          scope.setUser({ id: context.userId, email: context.userEmail });\n        }\n\n        if (context?.action) {\n          scope.setTag('action', context.action);\n        }\n\n        if (context?.component) {\n          scope.setTag('component', context.component);\n        }\n\n        if (context?.additionalData) {\n          scope.setContext('additionalData', context.additionalData);\n        }\n\n        Sentry.captureMessage(message, level);\n      });\n    } else {\n      // Fallback to console logging\n      console.log(`[${level.toUpperCase()}] ${message}`);\n      if (context) {\n        console.log('Message context:', context);\n      }\n    }\n  }\n\n  /**\n   * Add breadcrumb for debugging\n   */\n  static addBreadcrumb(message: string, category?: string, data?: Record<string, any>) {\n    if (Sentry) {\n      Sentry.addBreadcrumb({\n        message,\n        category: category || 'custom',\n        data,\n        level: 'info',\n      });\n    } else {\n      // Fallback to console logging\n      console.log(`[BREADCRUMB] ${category || 'custom'}: ${message}`, data);\n    }\n  }\n\n  /**\n   * Set user context for all subsequent error reports\n   */\n  static setUser(userId: string, email?: string, additionalData?: Record<string, any>) {\n    if (Sentry) {\n      Sentry.setUser({\n        id: userId,\n        email,\n        ...additionalData,\n      });\n    } else {\n      console.log(`[USER] Set user context: ${userId} (${email})`);\n    }\n  }\n\n  /**\n   * Clear user context\n   */\n  static clearUser() {\n    if (Sentry) {\n      Sentry.setUser(null);\n    } else {\n      console.log('[USER] Cleared user context');\n    }\n  }\n\n  /**\n   * Performance monitoring - start transaction\n   */\n  static startTransaction(name: string, operation: string) {\n    if (Sentry) {\n      // Note: startTransaction is deprecated in newer Sentry versions\n      // Using startSpan instead for performance monitoring\n      return Sentry.startSpan({ name, op: operation }, () => {});\n    } else {\n      console.log(`[PERFORMANCE] Start transaction: ${name} (${operation})`);\n      return null;\n    }\n  }\n\n  /**\n   * Performance monitoring - measure function execution\n   */\n  static async measureAsync<T>(\n    name: string,\n    operation: string,\n    fn: () => Promise<T>\n  ): Promise<T> {\n    if (Sentry) {\n      return Sentry.startSpan({ name, op: operation }, async () => {\n        try {\n          const result = await fn();\n          return result;\n        } catch (error) {\n          Sentry.captureException(error);\n          throw error;\n        }\n      });\n    } else {\n      console.log(`[PERFORMANCE] Measuring: ${name} (${operation})`);\n      try {\n        const result = await fn();\n        console.log(`[PERFORMANCE] Completed: ${name}`);\n        return result;\n      } catch (error) {\n        console.error(`[PERFORMANCE] Error in ${name}:`, error);\n        throw error;\n      }\n    }\n  }\n}\n\n/**\n * Higher-order component for automatic error boundary with Sentry reporting\n */\nexport function withErrorReporting<P extends object>(\n  WrappedComponent: React.ComponentType<P>,\n  componentName?: string\n): React.ComponentType<P> {\n  const WithErrorReportingComponent: React.ComponentType<P> = (props: P) => {\n    React.useEffect(() => {\n      ErrorReporter.addBreadcrumb(\n        `Component ${componentName || WrappedComponent.name} mounted`,\n        'component',\n        { componentName: componentName || WrappedComponent.name }\n      );\n    }, []);\n\n    return React.createElement(WrappedComponent, props);\n  };\n\n  WithErrorReportingComponent.displayName = `withErrorReporting(${componentName || WrappedComponent.name})`;\n\n  return WithErrorReportingComponent;\n}\n\n/**\n * Hook for error reporting in functional components\n */\nexport function useErrorReporting() {\n  const reportError = React.useCallback((error: Error, context?: ErrorContext) => {\n    ErrorReporter.captureError(error, context);\n  }, []);\n\n  const reportMessage = React.useCallback((\n    message: string, \n    level: 'info' | 'warning' | 'error' = 'info', \n    context?: ErrorContext\n  ) => {\n    ErrorReporter.captureMessage(message, level, context);\n  }, []);\n\n  const addBreadcrumb = React.useCallback((\n    message: string, \n    category?: string, \n    data?: Record<string, any>\n  ) => {\n    ErrorReporter.addBreadcrumb(message, category, data);\n  }, []);\n\n  return {\n    reportError,\n    reportMessage,\n    addBreadcrumb,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiLAA,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAmBC;AAAAC,aAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAI,iBAAA,GAAAA,iBAAA;AAzMA,IAAAC,OAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,eAAA,CAAAC,OAAA;AAEA;AACA,IAAIC,MAAM;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAQ,IAAI;AAAC;AAAAD,aAAA,GAAAC,CAAA;AACvB,IAAI;EAAA;EAAAD,aAAA,GAAAC,CAAA;EACFK,MAAM,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACpC,CAAC,CAAC,OAAOE,KAAK,EAAE;EACd;AAAA;AAWF,IAAAC,aAAA;AAAA;AAAA,cAAAR,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAS,CAAA;EAAA,SAAAD,cAAA;IAAA;IAAAR,aAAA,GAAAS,CAAA;EA0JA;EAzJE;;;EAAA;EAAAT,aAAA,GAAAC,CAAA;EAGOO,aAAA,CAAAE,YAAY,GAAnB,UAAoBH,KAAY,EAAEI,OAAsB;IAAA;IAAAX,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IACtD,IAAIK,MAAM,EAAE;MAAA;MAAAN,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACVK,MAAM,CAACO,SAAS,CAAC,UAACC,KAAU;QAAA;QAAAd,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAC,CAAA;QAC1B;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAEI,MAAM,GAAE;UAAA;UAAAf,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UACnBa,KAAK,CAACE,OAAO,CAAC;YAAEC,EAAE,EAAEN,OAAO,CAACI,MAAM;YAAEG,KAAK,EAAEP,OAAO,CAACQ;UAAS,CAAE,CAAC;QACjE,CAAC;QAAA;QAAA;UAAAnB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAED;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAES,MAAM,GAAE;UAAA;UAAApB,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UACnBa,KAAK,CAACO,MAAM,CAAC,QAAQ,EAAEV,OAAO,CAACS,MAAM,CAAC;QACxC,CAAC;QAAA;QAAA;UAAApB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAED;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAEW,SAAS,GAAE;UAAA;UAAAtB,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UACtBa,KAAK,CAACO,MAAM,CAAC,WAAW,EAAEV,OAAO,CAACW,SAAS,CAAC;QAC9C,CAAC;QAAA;QAAA;UAAAtB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAED;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAEY,cAAc,GAAE;UAAA;UAAAvB,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UAC3Ba,KAAK,CAACU,UAAU,CAAC,gBAAgB,EAAEb,OAAO,CAACY,cAAc,CAAC;QAC5D,CAAC;QAAA;QAAA;UAAAvB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAEDK,MAAM,CAACmB,gBAAgB,CAAClB,KAAK,CAAC;MAChC,CAAC,CAAC;IACJ,CAAC,MAAM;MAAA;MAAAP,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACL;MACAyB,OAAO,CAACnB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MAAC;MAAAP,aAAA,GAAAC,CAAA;MACxC,IAAIU,OAAO,EAAE;QAAA;QAAAX,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACXyB,OAAO,CAACnB,KAAK,CAAC,gBAAgB,EAAEI,OAAO,CAAC;MAC1C,CAAC;MAAA;MAAA;QAAAX,aAAA,GAAAY,CAAA;MAAA;IACH;EACF,CAAC;EAED;;;EAAA;EAAAZ,aAAA,GAAAC,CAAA;EAGOO,aAAA,CAAAmB,cAAc,GAArB,UAAsBC,OAAe,EAAEC,KAA4C,EAAElB,OAAsB;IAAA;IAAAX,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IAApE,IAAA4B,KAAA;MAAA;MAAA7B,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MAAA4B,KAAA,SAA4C;IAAA;IAAA;IAAA;MAAA7B,aAAA,GAAAY,CAAA;IAAA;IAAAZ,aAAA,GAAAC,CAAA;IACjF,IAAIK,MAAM,EAAE;MAAA;MAAAN,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACVK,MAAM,CAACO,SAAS,CAAC,UAACC,KAAU;QAAA;QAAAd,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAC,CAAA;QAC1B;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAEI,MAAM,GAAE;UAAA;UAAAf,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UACnBa,KAAK,CAACE,OAAO,CAAC;YAAEC,EAAE,EAAEN,OAAO,CAACI,MAAM;YAAEG,KAAK,EAAEP,OAAO,CAACQ;UAAS,CAAE,CAAC;QACjE,CAAC;QAAA;QAAA;UAAAnB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAED;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAES,MAAM,GAAE;UAAA;UAAApB,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UACnBa,KAAK,CAACO,MAAM,CAAC,QAAQ,EAAEV,OAAO,CAACS,MAAM,CAAC;QACxC,CAAC;QAAA;QAAA;UAAApB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAED;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAEW,SAAS,GAAE;UAAA;UAAAtB,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UACtBa,KAAK,CAACO,MAAM,CAAC,WAAW,EAAEV,OAAO,CAACW,SAAS,CAAC;QAC9C,CAAC;QAAA;QAAA;UAAAtB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAED;QAAI;QAAA,CAAAD,aAAA,GAAAY,CAAA,WAAAD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA,WAAPD,OAAO;QAAA;QAAA,CAAAX,aAAA,GAAAY,CAAA;QAAA;QAAA,CAAAZ,aAAA,GAAAY,CAAA,WAAPD,OAAO,CAAEY,cAAc,GAAE;UAAA;UAAAvB,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UAC3Ba,KAAK,CAACU,UAAU,CAAC,gBAAgB,EAAEb,OAAO,CAACY,cAAc,CAAC;QAC5D,CAAC;QAAA;QAAA;UAAAvB,aAAA,GAAAY,CAAA;QAAA;QAAAZ,aAAA,GAAAC,CAAA;QAEDK,MAAM,CAACqB,cAAc,CAACC,OAAO,EAAEC,KAAK,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM;MAAA;MAAA7B,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACL;MACAyB,OAAO,CAACI,GAAG,CAAC,IAAAC,MAAA,CAAIF,KAAK,CAACG,WAAW,EAAE,QAAAD,MAAA,CAAKH,OAAO,CAAE,CAAC;MAAC;MAAA5B,aAAA,GAAAC,CAAA;MACnD,IAAIU,OAAO,EAAE;QAAA;QAAAX,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACXyB,OAAO,CAACI,GAAG,CAAC,kBAAkB,EAAEnB,OAAO,CAAC;MAC1C,CAAC;MAAA;MAAA;QAAAX,aAAA,GAAAY,CAAA;MAAA;IACH;EACF,CAAC;EAED;;;EAAA;EAAAZ,aAAA,GAAAC,CAAA;EAGOO,aAAA,CAAAyB,aAAa,GAApB,UAAqBL,OAAe,EAAEM,QAAiB,EAAEC,IAA0B;IAAA;IAAAnC,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IACjF,IAAIK,MAAM,EAAE;MAAA;MAAAN,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACVK,MAAM,CAAC2B,aAAa,CAAC;QACnBL,OAAO,EAAAA,OAAA;QACPM,QAAQ;QAAE;QAAA,CAAAlC,aAAA,GAAAY,CAAA,WAAAsB,QAAQ;QAAA;QAAA,CAAAlC,aAAA,GAAAY,CAAA,WAAI,QAAQ;QAC9BuB,IAAI,EAAAA,IAAA;QACJN,KAAK,EAAE;OACR,CAAC;IACJ,CAAC,MAAM;MAAA;MAAA7B,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACL;MACAyB,OAAO,CAACI,GAAG,CAAC,gBAAAC,MAAA;MAAgB;MAAA,CAAA/B,aAAA,GAAAY,CAAA,WAAAsB,QAAQ;MAAA;MAAA,CAAAlC,aAAA,GAAAY,CAAA,WAAI,QAAQ,SAAAmB,MAAA,CAAKH,OAAO,CAAE,EAAEO,IAAI,CAAC;IACvE;EACF,CAAC;EAED;;;EAAA;EAAAnC,aAAA,GAAAC,CAAA;EAGOO,aAAA,CAAAQ,OAAO,GAAd,UAAeD,MAAc,EAAEG,KAAc,EAAEK,cAAoC;IAAA;IAAAvB,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IACjF,IAAIK,MAAM,EAAE;MAAA;MAAAN,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACVK,MAAM,CAACU,OAAO,CAAAoB,QAAA;QACZnB,EAAE,EAAEF,MAAM;QACVG,KAAK,EAAAA;MAAA,GACFK,cAAc,EACjB;IACJ,CAAC,MAAM;MAAA;MAAAvB,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACLyB,OAAO,CAACI,GAAG,CAAC,4BAAAC,MAAA,CAA4BhB,MAAM,QAAAgB,MAAA,CAAKb,KAAK,MAAG,CAAC;IAC9D;EACF,CAAC;EAED;;;EAAA;EAAAlB,aAAA,GAAAC,CAAA;EAGOO,aAAA,CAAA6B,SAAS,GAAhB;IAAA;IAAArC,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IACE,IAAIK,MAAM,EAAE;MAAA;MAAAN,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACVK,MAAM,CAACU,OAAO,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MAAA;MAAAhB,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACLyB,OAAO,CAACI,GAAG,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC;EAED;;;EAAA;EAAA9B,aAAA,GAAAC,CAAA;EAGOO,aAAA,CAAA8B,gBAAgB,GAAvB,UAAwBC,IAAY,EAAEC,SAAiB;IAAA;IAAAxC,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IACrD,IAAIK,MAAM,EAAE;MAAA;MAAAN,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACV;MACA;MACA,OAAOK,MAAM,CAACmC,SAAS,CAAC;QAAEF,IAAI,EAAAA,IAAA;QAAEG,EAAE,EAAEF;MAAS,CAAE,EAAE;QAAA;QAAAxC,aAAA,GAAAS,CAAA;MAAO,CAAC,CAAC;IAC5D,CAAC,MAAM;MAAA;MAAAT,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACLyB,OAAO,CAACI,GAAG,CAAC,oCAAAC,MAAA,CAAoCQ,IAAI,QAAAR,MAAA,CAAKS,SAAS,MAAG,CAAC;MAAC;MAAAxC,aAAA,GAAAC,CAAA;MACvE,OAAO,IAAI;IACb;EACF,CAAC;EAED;;;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAGaO,aAAA,CAAAmC,YAAY,GAAzB,UACEJ,IAAY,EACZC,SAAiB,EACjBI,EAAoB;IAAA;IAAA5C,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;mCACnB4C,OAAO;MAAA;MAAA7C,aAAA,GAAAS,CAAA;;;;;;;;;;;;;;;;iBACJH,MAAM,EAAN;cAAA;cAAAN,aAAA,GAAAY,CAAA;cAAAZ,aAAA,GAAAC,CAAA;cAAA;YAAA,CAAM;YAAA;YAAA;cAAAD,aAAA,GAAAY,CAAA;YAAA;YAAAZ,aAAA,GAAAC,CAAA;YACR,sBAAOK,MAAM,CAACmC,SAAS,CAAC;cAAEF,IAAI,EAAAA,IAAA;cAAEG,EAAE,EAAEF;YAAS,CAAE,EAAE;cAAA;cAAAxC,aAAA,GAAAS,CAAA;cAAAT,aAAA,GAAAC,CAAA;cAAA,OAAA6C,SAAA,CAAAC,KAAA;gBAAA;gBAAA/C,aAAA,GAAAS,CAAA;;;;;;;;;;;;;;;;sBAE9B,qBAAMmC,EAAE,EAAE;;;;;sBAAnBI,MAAM,GAAGC,EAAA,CAAAC,IAAA,EAAU;sBAAA;sBAAAlD,aAAA,GAAAC,CAAA;sBACzB,sBAAO+C,MAAM;;;;;;;;sBAEb1C,MAAM,CAACmB,gBAAgB,CAAC0B,OAAK,CAAC;sBAAC;sBAAAnD,aAAA,GAAAC,CAAA;sBAC/B,MAAMkD,OAAK;;;;;;;;;aAEd,CAAC;;;;;YAEFzB,OAAO,CAACI,GAAG,CAAC,4BAAAC,MAAA,CAA4BQ,IAAI,QAAAR,MAAA,CAAKS,SAAS,MAAG,CAAC;YAAC;YAAAxC,aAAA,GAAAC,CAAA;;;;;;;;;YAE9C,qBAAM2C,EAAE,EAAE;;;;;YAAnBI,MAAM,GAAGC,EAAA,CAAAC,IAAA,EAAU;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YACzByB,OAAO,CAACI,GAAG,CAAC,4BAAAC,MAAA,CAA4BQ,IAAI,CAAE,CAAC;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YAChD,sBAAO+C,MAAM;;;;;;;;YAEbtB,OAAO,CAACnB,KAAK,CAAC,0BAAAwB,MAAA,CAA0BQ,IAAI,MAAG,EAAEa,OAAK,CAAC;YAAC;YAAApD,aAAA,GAAAC,CAAA;YACxD,MAAMmD,OAAK;;;;;;;;;GAGhB;EAAA;EAAApD,aAAA,GAAAC,CAAA;EACH,OAAAO,aAAC;AAAD,CAAC,CA1JD;AA0JC;AAAAR,aAAA,GAAAC,CAAA;AA1JYH,OAAA,CAAAU,aAAA,GAAAA,aAAA;AA4Jb;;;AAGA,SAAgBT,kBAAkBA,CAChCsD,gBAAwC,EACxCC,aAAsB;EAAA;EAAAtD,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAC,CAAA;EAEtB,IAAMsD,2BAA2B,GAA2B,SAAAA,CAACC,KAAQ;IAAA;IAAAxD,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IACnEE,OAAA,CAAAsD,OAAK,CAACC,SAAS,CAAC;MAAA;MAAA1D,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAC,CAAA;MACdO,aAAa,CAACyB,aAAa,CACzB,aAAAF,MAAA;MAAa;MAAA,CAAA/B,aAAA,GAAAY,CAAA,WAAA0C,aAAa;MAAA;MAAA,CAAAtD,aAAA,GAAAY,CAAA,WAAIyC,gBAAgB,CAACd,IAAI,cAAU,EAC7D,WAAW,EACX;QAAEe,aAAa;QAAE;QAAA,CAAAtD,aAAA,GAAAY,CAAA,WAAA0C,aAAa;QAAA;QAAA,CAAAtD,aAAA,GAAAY,CAAA,WAAIyC,gBAAgB,CAACd,IAAI;MAAA,CAAE,CAC1D;IACH,CAAC,EAAE,EAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAC,CAAA;IAEP,OAAOE,OAAA,CAAAsD,OAAK,CAACE,aAAa,CAACN,gBAAgB,EAAEG,KAAK,CAAC;EACrD,CAAC;EAAC;EAAAxD,aAAA,GAAAC,CAAA;EAEFsD,2BAA2B,CAACK,WAAW,GAAG,sBAAA7B,MAAA;EAAsB;EAAA,CAAA/B,aAAA,GAAAY,CAAA,WAAA0C,aAAa;EAAA;EAAA,CAAAtD,aAAA,GAAAY,CAAA,WAAIyC,gBAAgB,CAACd,IAAI,OAAG;EAAC;EAAAvC,aAAA,GAAAC,CAAA;EAE1G,OAAOsD,2BAA2B;AACpC;AAEA;;;AAGA,SAAgBrD,iBAAiBA,CAAA;EAAA;EAAAF,aAAA,GAAAS,CAAA;EAC/B,IAAMoD,WAAW;EAAA;EAAA,CAAA7D,aAAA,GAAAC,CAAA,SAAGE,OAAA,CAAAsD,OAAK,CAACK,WAAW,CAAC,UAACvD,KAAY,EAAEI,OAAsB;IAAA;IAAAX,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IACzEO,aAAa,CAACE,YAAY,CAACH,KAAK,EAAEI,OAAO,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMoD,aAAa;EAAA;EAAA,CAAA/D,aAAA,GAAAC,CAAA,SAAGE,OAAA,CAAAsD,OAAK,CAACK,WAAW,CAAC,UACtClC,OAAe,EACfC,KAA4C,EAC5ClB,OAAsB;IAAA;IAAAX,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IADtB,IAAA4B,KAAA;MAAA;MAAA7B,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MAAA4B,KAAA,SAA4C;IAAA;IAAA;IAAA;MAAA7B,aAAA,GAAAY,CAAA;IAAA;IAAAZ,aAAA,GAAAC,CAAA;IAG5CO,aAAa,CAACmB,cAAc,CAACC,OAAO,EAAEC,KAAK,EAAElB,OAAO,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMsB,aAAa;EAAA;EAAA,CAAAjC,aAAA,GAAAC,CAAA,SAAGE,OAAA,CAAAsD,OAAK,CAACK,WAAW,CAAC,UACtClC,OAAe,EACfM,QAAiB,EACjBC,IAA0B;IAAA;IAAAnC,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;IAE1BO,aAAa,CAACyB,aAAa,CAACL,OAAO,EAAEM,QAAQ,EAAEC,IAAI,CAAC;EACtD,CAAC,EAAE,EAAE,CAAC;EAAC;EAAAnC,aAAA,GAAAC,CAAA;EAEP,OAAO;IACL4D,WAAW,EAAAA,WAAA;IACXE,aAAa,EAAAA,aAAA;IACb9B,aAAa,EAAAA;GACd;AACH", "ignoreList": []}