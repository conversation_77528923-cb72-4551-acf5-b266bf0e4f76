ea61ecf941440e2c7b7dd86fa2a4ac58
require('@testing-library/jest-dom');

// Import memory leak fixes
const { setupJestMemoryFixes } = require('./test-utils/memory-leak-fixes');

// Setup memory leak prevention
setupJestMemoryFixes();

// Load test environment variables from .env.test
const path = require('path');
const dotenv = require('dotenv');

// Load test environment
dotenv.config({ path: path.resolve(__dirname, '.env.test') });

// Mock NextAuth providers with proper default export pattern
jest.mock('next-auth/providers/credentials', () => {
  const mockCredentialsProvider = jest.fn(() => ({
    id: 'credentials',
    name: 'Credentials',
    type: 'credentials',
    credentials: {
      email: { label: 'Email', type: 'email' },
      password: { label: 'Password', type: 'password' }
    },
    authorize: jest.fn(),
  }));

  return mockCredentialsProvider;
});

jest.mock('next-auth/providers/email', () => {
  const mockEmailProvider = jest.fn(() => ({
    id: 'email',
    name: '<PERSON>ail',
    type: 'email',
  }));

  return mockEmailProvider;
});

// Mock NextAuth
jest.mock('next-auth', () => ({
  default: jest.fn(() => ({
    handlers: { GET: jest.fn(), POST: jest.fn() },
    auth: jest.fn(),
    signIn: jest.fn(),
    signOut: jest.fn(),
  })),
  getServerSession: jest.fn(),
}));

// Mock NextAuth React
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: null,
    status: 'unauthenticated',
    update: jest.fn(),
  })),
  signIn: jest.fn(),
  signOut: jest.fn(),
  getSession: jest.fn(),
  SessionProvider: ({ children }) => children,
}));

// Mock Prisma Client
jest.mock('@/lib/prisma', () => ({
  __esModule: true,
  default: {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    account: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    session: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    verificationToken: {
      findUnique: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
    skillAssessment: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    learningPath: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    learningResource: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    userProgress: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    forumPost: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    forumComment: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    $transaction: jest.fn(),
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  },
}));

// Mock bcrypt
jest.mock('bcrypt', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
  genSalt: jest.fn().mockResolvedValue('salt'),
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
    getAll: jest.fn(),
    has: jest.fn(),
    keys: jest.fn(),
    values: jest.fn(),
    entries: jest.fn(),
    forEach: jest.fn(),
    toString: jest.fn(),
  }),
  usePathname: () => '/',
  notFound: jest.fn(),
  redirect: jest.fn(),
}));

// Mock Radix UI components
const React = require('react');

jest.mock('@radix-ui/react-dialog', () => ({
  Root: ({ children }) => children,
  Trigger: ({ children, ...props }) => React.createElement('button', props, children),
  Portal: ({ children }) => children,
  Overlay: ({ children, ...props }) => React.createElement('div', props, children),
  Content: ({ children, ...props }) => React.createElement('div', props, children),
  Title: ({ children, ...props }) => React.createElement('h2', props, children),
  Description: ({ children, ...props }) => React.createElement('p', props, children),
  Close: ({ children, ...props }) => React.createElement('button', props, children),
}));



// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock fetch
global.fetch = jest.fn();

// Setup console error suppression for known issues
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
       args[0].includes('Warning: React.createFactory() is deprecated') ||
       args[0].includes('Warning: componentWillReceiveProps has been renamed'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
const fs = require('fs');

const envTestPath = path.join(__dirname, '.env.test');
if (fs.existsSync(envTestPath)) {
  require('dotenv').config({ path: envTestPath });
}

// Set up test environment variables with safe defaults
process.env.NODE_ENV = 'test';
process.env.NEXTAUTH_URL = process.env.NEXTAUTH_URL || 'http://localhost:3000';
process.env.NEXTAUTH_SECRET = process.env.TEST_NEXTAUTH_SECRET || process.env.NEXTAUTH_SECRET;
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL;

// Ensure test database URL includes 'test' for safety
if (process.env.DATABASE_URL && !process.env.DATABASE_URL.includes('test')) {
  console.warn('Warning: DATABASE_URL does not include "test" - using test database');
  process.env.DATABASE_URL = process.env.DATABASE_URL.replace(/\/[^\/]+$/, '/faafo_test');
}

// Database cleanup hooks for test safety
beforeEach(async () => {
  // Reset any global state before each test
  jest.clearAllMocks();
});

afterEach(async () => {
  // Clean up any test data or connections after each test
  if (global.mockPrisma) {
    // Reset all mock implementations
    Object.values(global.mockPrisma).forEach(model => {
      if (typeof model === 'object' && model !== null) {
        Object.values(model).forEach(method => {
          if (typeof method === 'function' && method.mockReset) {
            method.mockReset();
          }
        });
      }
    });
  }
});

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      pathname: '/',
      route: '/',
      query: {},
      asPath: '/',
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, ...props }) => {
    return React.createElement('a', { href, ...props }, children);
  };
});

// Mock Next.js Image component
jest.mock('next/image', () => {
  return ({ src, alt, ...props }) => {
    return React.createElement('img', { src, alt, ...props });
  };
});

// Mock NextAuth with flexible useSession
const mockUseSession = jest.fn(() => ({
  data: null,
  status: 'unauthenticated',
}));

jest.mock('next-auth/react', () => ({
  useSession: mockUseSession,
  signIn: jest.fn(() => Promise.resolve({ ok: true })),
  signOut: jest.fn(),
  getSession: jest.fn(() => Promise.resolve({ user: { id: '1', email: '<EMAIL>' } })),
  SessionProvider: ({ children, session }) => {
    // Update the mock based on the session prop
    if (session) {
      mockUseSession.mockReturnValue({
        data: session,
        status: 'authenticated',
      });
    } else {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });
    }
    return children;
  },
}));

// Make mockUseSession available globally for tests
global.mockUseSession = mockUseSession;

// Mock NextAuth core
jest.mock('next-auth', () => ({
  default: jest.fn(() => ({
    handlers: {
      GET: jest.fn(),
      POST: jest.fn(),
    },
  })),
  getServerSession: jest.fn(),
}));

// Mock NextAuth/next
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock NextAuth JWT
jest.mock('next-auth/jwt', () => ({
  getToken: jest.fn(),
}));

// Mock NextAuth providers
jest.mock('next-auth/providers/credentials', () => {
  const mockCredentialsProvider = jest.fn(() => ({
    id: 'credentials',
    name: 'Credentials',
    type: 'credentials',
    credentials: {
      email: { label: 'Email', type: 'email' },
      password: { label: 'Password', type: 'password' }
    },
    authorize: jest.fn(),
  }));

  return mockCredentialsProvider;
});

jest.mock('next-auth/providers/email', () => {
  const mockEmailProvider = jest.fn(() => ({
    id: 'email',
    name: 'Email',
    type: 'email',
  }));

  return mockEmailProvider;
});

// Mock NextAuth adapters
jest.mock('@auth/prisma-adapter', () => ({
  PrismaAdapter: jest.fn(() => ({
    createUser: jest.fn(),
    getUser: jest.fn(),
    getUserByEmail: jest.fn(),
    getUserByAccount: jest.fn(),
    updateUser: jest.fn(),
    deleteUser: jest.fn(),
    linkAccount: jest.fn(),
    unlinkAccount: jest.fn(),
    createSession: jest.fn(),
    getSessionAndUser: jest.fn(),
    updateSession: jest.fn(),
    deleteSession: jest.fn(),
    createVerificationToken: jest.fn(),
    useVerificationToken: jest.fn(),
  })),
}));

// Mock next-themes
jest.mock('next-themes', () => ({
  ThemeProvider: ({ children }) => children,
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
    resolvedTheme: 'light',
    themes: ['light', 'dark'],
    systemTheme: 'light',
  }),
}));

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-1234'),
  v1: jest.fn(() => 'test-uuid-v1-1234'),
  validate: jest.fn(() => true),
}));

// Create comprehensive Prisma mock
const createMockModel = () => ({
  findUnique: jest.fn(),
  findMany: jest.fn(),
  findFirst: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  deleteMany: jest.fn(),
  count: jest.fn(),
  upsert: jest.fn(),
  createMany: jest.fn(),
  updateMany: jest.fn(),
  aggregate: jest.fn(),
  groupBy: jest.fn(),
});

const mockPrismaClient = {
  user: createMockModel(),
  assessment: createMockModel(),
  assessmentResponse: createMockModel(),
  learningResource: createMockModel(),
  careerPath: createMockModel(),
  careerPathResource: createMockModel(),
  profile: createMockModel(),
  forumPost: createMockModel(),
  forumReply: createMockModel(),
  forumPostReaction: createMockModel(), // Added missing model
  userProgress: createMockModel(),
  userSkillProgress: createMockModel(),
  resourceRating: createMockModel(),
  skill: createMockModel(),
  userSkill: createMockModel(),
  industry: createMockModel(),
  notification: createMockModel(),
  userGoal: createMockModel(),
  achievement: createMockModel(),
  userAchievement: createMockModel(),
  postReaction: createMockModel(),
  moderatorAction: createMockModel(),
  report: createMockModel(),
  suggestionRule: createMockModel(),
  freedomFund: createMockModel(), // Added missing model
  verificationToken: createMockModel(), // Added missing model
  account: createMockModel(), // Added missing model
  session: createMockModel(), // Added missing model
  $connect: jest.fn().mockResolvedValue(undefined),
  $disconnect: jest.fn().mockResolvedValue(undefined),
  $transaction: jest.fn().mockImplementation((callback) => callback(mockPrismaClient)),
  $queryRaw: jest.fn(),
  $executeRaw: jest.fn(),
};

// Export to global for easy access in tests
global.mockPrisma = mockPrismaClient;

// Mock Prisma Client
jest.mock('@prisma/client', () => {
  return {
    PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient),
    Prisma: {
      PrismaClientKnownRequestError: class PrismaClientKnownRequestError extends Error {
        constructor(message, code, clientVersion) {
          super(message);
          this.code = code;
          this.clientVersion = clientVersion;
        }
      },
      PrismaClientUnknownRequestError: class PrismaClientUnknownRequestError extends Error {},
      PrismaClientRustPanicError: class PrismaClientRustPanicError extends Error {},
      PrismaClientInitializationError: class PrismaClientInitializationError extends Error {},
      PrismaClientValidationError: class PrismaClientValidationError extends Error {},
    },
  };
}, { virtual: true });

// Mock our Prisma lib
jest.mock('@/lib/prisma', () => ({
  default: mockPrismaClient,
  __esModule: true,
}));
// Mock analytics services
jest.mock('@/lib/analytics-service', () => ({
  analyticsService: {
    getUserEngagementMetrics: jest.fn().mockResolvedValue({
      totalUsers: 100,
      activeUsers: { daily: 10, weekly: 50, monthly: 80 },
      newUsers: { today: 2, thisWeek: 15, thisMonth: 25 },
      userRetention: { day1: 85, day7: 70, day30: 45 },
      sessionMetrics: { averageSessionDuration: 1200, totalSessions: 500, bounceRate: 0.3 },
      engagementTrends: []
    }),
    getLearningProgressMetrics: jest.fn().mockResolvedValue({
      totalResources: 50,
      completedResources: 200,
      inProgressResources: 75,
      averageCompletionTime: 3600,
      completionRate: 80,
      popularResources: [],
      learningTrends: [],
      categoryBreakdown: []
    }),
    getCareerPathMetrics: jest.fn().mockResolvedValue({
      totalPaths: 10,
      activePaths: 8,
      completionRates: [],
      pathPopularity: [],
      progressDistribution: []
    }),
    getCommunityMetrics: jest.fn().mockResolvedValue({
      totalPosts: 150,
      totalReplies: 300,
      activePosters: 25,
      engagementRate: 0.6,
      topContributors: [],
      categoryActivity: [],
      communityTrends: []
    }),
    getComprehensiveAnalytics: jest.fn().mockResolvedValue({
      userEngagement: {},
      learningProgress: {},
      careerPaths: {},
      community: {},
      generatedAt: new Date().toISOString(),
      timeRange: '30 days'
    })
  },
  __esModule: true,
}));

// Mock personal analytics service
jest.mock('@/lib/personal-analytics-service', () => ({
  personalAnalyticsService: {
    getLearningMetrics: jest.fn().mockResolvedValue({}),
    getCareerMetrics: jest.fn().mockResolvedValue({}),
    getCommunityMetrics: jest.fn().mockResolvedValue({}),
    getGoalsMetrics: jest.fn().mockResolvedValue({}),
    getComprehensivePersonalAnalytics: jest.fn().mockResolvedValue({})
  },
  __esModule: true,
}));

// Mock auth utils
jest.mock('@/lib/auth-utils', () => ({
  getCurrentUserAdminStatus: jest.fn().mockResolvedValue({
    isAuthenticated: true,
    isAdmin: true,
    userId: 'test-user-id'
  }),
  isUserAdmin: jest.fn().mockResolvedValue(true),
  requireAdmin: jest.fn().mockResolvedValue({ userId: 'test-user-id' }),
  __esModule: true,
}));

// Enhanced window.matchMedia mock for next-themes compatibility
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => {
    const mediaQuery = {
      matches: query === '(prefers-color-scheme: dark)' ? false : false, // Default to light mode
      media: query,
      onchange: null,
      addListener: jest.fn(), // Deprecated but still used by some libraries
      removeListener: jest.fn(), // Deprecated but still used by some libraries
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    };

    // Make it behave like a real MediaQueryList
    Object.defineProperty(mediaQuery, 'matches', {
      writable: true,
      value: query.includes('dark') ? false : true
    });

    return mediaQuery;
  }),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Global test utilities
global.React = require('react');

// Mock fetch globally
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({ isAdmin: false, role: null }),
    text: () => Promise.resolve(''),
    headers: new Headers(),
    redirected: false,
    statusText: 'OK',
    type: 'basic',
    url: '',
    clone: jest.fn(),
    body: null,
    bodyUsed: false,
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    blob: () => Promise.resolve(new Blob()),
    formData: () => Promise.resolve(new FormData()),
  })
);

// Mock Radix UI components
jest.mock('@radix-ui/react-select', () => {
  const React = require('react');

  const Root = ({ children, value, onValueChange, defaultValue, ...props }) => {
    const [internalValue, setInternalValue] = React.useState(defaultValue || value || '');

    const handleChange = (newValue) => {
      setInternalValue(newValue);
      if (onValueChange) {
        onValueChange(newValue);
      }
    };

    return React.createElement('div', {
      'data-testid': 'select-root',
      'data-value': internalValue,
      ...props
    }, React.Children.map(children, child =>
      React.isValidElement(child)
        ? React.cloneElement(child, { value: internalValue, onValueChange: handleChange })
        : child
    ));
  };

  const Trigger = React.forwardRef(({ children, className, ...props }, ref) =>
    React.createElement('button', {
      ref,
      type: 'button',
      role: 'combobox',
      'aria-expanded': 'false',
      className,
      'data-testid': 'select-trigger',
      ...props
    }, children)
  );

  const Value = ({ placeholder, children, ...props }) =>
    React.createElement('span', {
      'data-testid': 'select-value',
      ...props
    }, children || placeholder);

  const Content = ({ children, className, ...props }) =>
    React.createElement('div', {
      role: 'listbox',
      className,
      'data-testid': 'select-content',
      ...props
    }, children);

  const Item = React.forwardRef(({ children, value, onSelect, className, ...props }, ref) => {
    const handleClick = () => {
      if (onSelect) {
        onSelect(value);
      }
    };

    return React.createElement('div', {
      ref,
      role: 'option',
      className,
      'data-testid': 'select-item',
      'data-value': value,
      onClick: handleClick,
      ...props
    }, children);
  });

  const Label = ({ children, className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-label',
      ...props
    }, children);

  const Group = ({ children, ...props }) =>
    React.createElement('div', {
      'data-testid': 'select-group',
      ...props
    }, children);

  const Separator = ({ className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-separator',
      ...props
    });

  const ScrollUpButton = ({ className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-scroll-up-button',
      ...props
    });

  const ScrollDownButton = ({ className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-scroll-down-button',
      ...props
    });

  // Additional components used by shadcn/ui
  const Icon = ({ children, asChild, ...props }) =>
    asChild ? children : React.createElement('span', {
      'data-testid': 'select-icon',
      ...props
    }, children);

  const Portal = ({ children, ...props }) =>
    React.createElement('div', {
      'data-testid': 'select-portal',
      ...props
    }, children);

  const Viewport = ({ children, className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-viewport',
      ...props
    }, children);

  return {
    Root,
    Trigger,
    Value,
    Content,
    Item,
    Label,
    Group,
    Separator,
    ScrollUpButton,
    ScrollDownButton,
    Icon,
    Portal,
    Viewport,
  };
});

// Mock Radix UI Switch
jest.mock('@radix-ui/react-switch', () => {
  const React = require('react');

  const Root = React.forwardRef(({
    checked,
    defaultChecked,
    onCheckedChange,
    disabled,
    className,
    children,
    ...props
  }, ref) => {
    const [internalChecked, setInternalChecked] = React.useState(defaultChecked || checked || false);

    const handleChange = () => {
      if (disabled) return;

      const newChecked = !internalChecked;
      setInternalChecked(newChecked);

      if (onCheckedChange) {
        onCheckedChange(newChecked);
      }
    };

    return React.createElement('button', {
      ref,
      type: 'button',
      role: 'switch',
      'aria-checked': internalChecked,
      disabled,
      className,
      'data-testid': 'switch-root',
      'data-state': internalChecked ? 'checked' : 'unchecked',
      onClick: handleChange,
      ...props
    }, children);
  });

  const Thumb = React.forwardRef(({ className, ...props }, ref) =>
    React.createElement('span', {
      ref,
      className,
      'data-testid': 'switch-thumb',
      ...props
    })
  );

  return { Root, Thumb };
});

// Mock Radix UI Checkbox
jest.mock('@radix-ui/react-checkbox', () => {
  const React = require('react');

  const Root = React.forwardRef(({
    checked,
    defaultChecked,
    onCheckedChange,
    disabled,
    className,
    children,
    ...props
  }, ref) => {
    const [internalChecked, setInternalChecked] = React.useState(defaultChecked || checked || false);

    const handleChange = () => {
      if (disabled) return;

      const newChecked = !internalChecked;
      setInternalChecked(newChecked);

      if (onCheckedChange) {
        onCheckedChange(newChecked);
      }
    };

    return React.createElement('button', {
      ref,
      type: 'button',
      role: 'checkbox',
      'aria-checked': internalChecked,
      disabled,
      className,
      'data-testid': 'checkbox-root',
      'data-state': internalChecked ? 'checked' : 'unchecked',
      onClick: handleChange,
      ...props
    }, children);
  });

  const Indicator = React.forwardRef(({ className, children, ...props }, ref) =>
    React.createElement('span', {
      ref,
      className,
      'data-testid': 'checkbox-indicator',
      ...props
    }, children)
  );

  return { Root, Indicator };
});

// Mock Radix UI Separator
jest.mock('@radix-ui/react-separator', () => {
  const React = require('react');

  const Root = React.forwardRef(({
    orientation = 'horizontal',
    decorative = true,
    className,
    ...props
  }, ref) =>
    React.createElement('div', {
      ref,
      role: decorative ? 'none' : 'separator',
      'aria-orientation': orientation,
      className,
      'data-testid': 'separator-root',
      'data-orientation': orientation,
      ...props
    })
  );

  return { Root };
});

// Mock shadcn/ui components
jest.mock('@/components/ui/select', () => {
  const React = require('react');

  const Select = ({ children, value, onValueChange, defaultValue, 'data-testid': testId, ...props }) => {
    const [internalValue, setInternalValue] = React.useState(defaultValue || value || '');

    const handleChange = (newValue) => {
      setInternalValue(newValue);
      if (onValueChange) {
        onValueChange(newValue);
      }
    };

    return React.createElement('div', {
      'data-testid': testId || 'select',
      'data-value': internalValue,
      ...props
    }, React.Children.map(children, child => {
      if (React.isValidElement(child)) {
        // Pass trigger-specific test ID to SelectTrigger
        const childTestId = child.type === SelectTrigger && testId
          ? testId.replace('-select', '-trigger')
          : testId;
        return React.cloneElement(child, {
          value: internalValue,
          onValueChange: handleChange,
          'data-testid': childTestId
        });
      }
      return child;
    }));
  };

  const SelectGroup = ({ children, ...props }) =>
    React.createElement('div', { 'data-testid': 'select-group', ...props }, children);

  const SelectValue = ({ placeholder, children, ...props }) =>
    React.createElement('span', { 'data-testid': 'select-value', ...props }, children || placeholder);

  const SelectTrigger = React.forwardRef(({ children, className, onValueChange, value, 'data-testid': testId, ...props }, ref) => {
    // Extract the placeholder text from SelectValue children to use as accessible name
    let accessibleName = '';
    React.Children.forEach(children, child => {
      if (React.isValidElement(child) && child.props.placeholder) {
        accessibleName = child.props.placeholder;
      }
    });

    // Filter out onValueChange and value from button props to avoid React warnings
    const buttonProps = { ...props };
    delete buttonProps.onValueChange;
    delete buttonProps.value;
    delete buttonProps['data-testid'];

    return React.createElement('button', {
      ref,
      type: 'button',
      role: 'combobox',
      'aria-expanded': 'false',
      'aria-label': accessibleName || 'Select option',
      name: accessibleName || 'Select option',
      className,
      'data-testid': testId || 'select-trigger',
      value: value || '',
      ...buttonProps
    }, children);
  });

  const SelectContent = ({ children, className, onValueChange, value, ...props }) => {
    // Filter out onValueChange and value to avoid React warnings
    const filteredProps = { ...props };
    delete filteredProps.onValueChange;
    delete filteredProps.value;

    return React.createElement('div', {
      role: 'listbox',
      className,
      'data-testid': 'select-content',
      ...filteredProps
    }, children);
  };

  const SelectLabel = ({ children, className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-label',
      ...props
    }, children);

  const SelectItem = React.forwardRef(({ children, value, onSelect, className, ...props }, ref) => {
    const handleClick = () => {
      if (onSelect) {
        onSelect(value);
      }
    };

    return React.createElement('div', {
      ref,
      role: 'option',
      className,
      'data-testid': 'select-item',
      'data-value': value,
      onClick: handleClick,
      ...props
    }, children);
  });

  const SelectSeparator = ({ className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-separator',
      ...props
    });

  const SelectScrollUpButton = ({ className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-scroll-up-button',
      ...props
    });

  const SelectScrollDownButton = ({ className, ...props }) =>
    React.createElement('div', {
      className,
      'data-testid': 'select-scroll-down-button',
      ...props
    });

  return {
    Select,
    SelectGroup,
    SelectValue,
    SelectTrigger,
    SelectContent,
    SelectLabel,
    SelectItem,
    SelectSeparator,
    SelectScrollUpButton,
    SelectScrollDownButton,
  };
});

// Mock shadcn/ui switch
jest.mock('@/components/ui/switch', () => {
  const React = require('react');

  const Switch = React.forwardRef(({
    checked,
    defaultChecked,
    onCheckedChange,
    disabled,
    className,
    ...props
  }, ref) => {
    const [internalChecked, setInternalChecked] = React.useState(defaultChecked || checked || false);

    const handleChange = () => {
      if (disabled) return;

      const newChecked = !internalChecked;
      setInternalChecked(newChecked);

      if (onCheckedChange) {
        onCheckedChange(newChecked);
      }
    };

    return React.createElement('button', {
      ref,
      type: 'button',
      role: 'switch',
      'aria-checked': internalChecked,
      disabled,
      className,
      'data-testid': 'switch',
      'data-state': internalChecked ? 'checked' : 'unchecked',
      onClick: handleChange,
      ...props
    });
  });

  return { Switch };
});

// Mock PhotoUpload component
jest.mock('@/components/profile/PhotoUpload', () => {
  const React = require('react');

  const PhotoUpload = ({
    currentPhotoUrl,
    onPhotoUpdate,
    className,
    size = 'lg',
    disabled = false
  }) => {
    const handleUploadClick = () => {
      if (!disabled && onPhotoUpdate) {
        onPhotoUpdate('https://example.com/test-photo.jpg');
      }
    };

    const handleRemoveClick = () => {
      if (!disabled && onPhotoUpdate) {
        onPhotoUpdate(null);
      }
    };

    return React.createElement('div', {
      className,
      'data-testid': 'photo-upload'
    }, [
      React.createElement('div', {
        key: 'avatar',
        'data-testid': 'photo-avatar'
      }, currentPhotoUrl ? 'Photo' : 'No Photo'),
      React.createElement('button', {
        key: 'upload',
        type: 'button',
        onClick: handleUploadClick,
        disabled,
        'data-testid': 'upload-photo-button'
      }, currentPhotoUrl ? 'Change Photo' : 'Upload Photo'),
      currentPhotoUrl && React.createElement('button', {
        key: 'remove',
        type: 'button',
        onClick: handleRemoveClick,
        disabled,
        'data-testid': 'remove-photo-button'
      }, 'Remove'),
      React.createElement('div', {
        key: 'drop-text',
        'data-testid': 'photo-drop-area'
      }, 'Drop your photo here, or click to browse')
    ].filter(Boolean));
  };

  return PhotoUpload;
});