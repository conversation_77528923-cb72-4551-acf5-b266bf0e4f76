{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/comprehensive-validation.test.ts", "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,QAAQ,CAAC,iCAAiC,EAAE;IAC1C,QAAQ,CAAC,8BAA8B,EAAE;QACvC,EAAE,CAAC,yCAAyC,EAAE;YAC5C,IAAM,WAAW,GAAG;gBAClB,kBAAkB;gBAClB,yBAAyB;gBACzB,sBAAsB;aACvB,CAAC;YAEF,IAAM,aAAa,GAAG;gBACpB,eAAe;gBACf,cAAc;gBACd,OAAO;gBACP,wBAAwB;aACzB,CAAC;YAEF,IAAM,UAAU,GAAG,4BAA4B,CAAC;YAEhD,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;gBACvB,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,aAAa,CAAC,OAAO,CAAC,UAAA,KAAK;gBACzB,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAM,WAAW,GAAG;gBAClB,+BAA+B;gBAC/B,yBAAyB;gBACzB,kCAAkC;gBAClC,2BAA2B;gBAC3B,iCAAiC;aAClC,CAAC;YAEF,IAAM,UAAU,GAAG,4CAA4C,CAAC;YAEhE,WAAW,CAAC,OAAO,CAAC,UAAA,OAAO;gBACzB,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,gCAAgC;YAChC,IAAM,WAAW,GAAG,2BAA2B,CAAC;YAChD,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAM,oBAAoB,GAAG;gBAC3B,yBAAyB;gBACzB,aAAa;gBACb,gCAAgC;gBAChC,UAAU;gBACV,WAAW;aACZ,CAAC;YAEF,IAAM,UAAU,GAAG,kDAAkD,CAAC;YAEtE,oBAAoB,CAAC,OAAO,CAAC,UAAA,OAAO;gBAClC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,gCAAgC;YAChC,IAAM,SAAS,GAAG,oBAAoB,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;YACnD,IAAM,eAAe,GAAG;gBACtB,gBAAgB;gBAChB,uBAAuB;gBACvB,eAAe;aAChB,CAAC;YAEF,IAAM,aAAa,GAAG;gBACpB,KAAK;gBACL,UAAU;gBACV,KAAK;gBACL,UAAU;gBACV,UAAU,CAAC,kCAAkC;aAC9C,CAAC;YAEF,gFAAgF;YAChF,IAAM,mBAAmB,GAAG,sEAAsE,CAAC;YAEnG,eAAe,CAAC,OAAO,CAAC,UAAA,QAAQ;gBAC9B,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,aAAa,CAAC,OAAO,CAAC,UAAA,QAAQ;gBAC5B,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,kDAAkD,EAAE;YACrD,IAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,cAAc;gBAClB,KAAK,EAAE,6BAA6B;gBACpC,WAAW,EAAE,sCAAsC;gBACnD,GAAG,EAAE,4BAA4B;gBACjC,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,YAAY;gBACtB,UAAU,EAAE,UAAU;gBACtB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,SAAS;gBACnB,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,IAAI;aACf,CAAC;YAEF,6BAA6B;YAC7B,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACpD,MAAM,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5F,MAAM,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACrF,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAM,eAAe,GAAG;gBACtB,EAAE,EAAE,YAAY;gBAChB,KAAK,EAAE,sBAAsB;gBAC7B,WAAW,EAAE,2CAA2C;gBACxD,QAAQ,EAAE,YAAY;gBACtB,UAAU,EAAE,cAAc;gBAC1B,aAAa,EAAE,KAAK;gBACpB,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,IAAI;aACf,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5D,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE;YAC9C,IAAM,eAAe,GAAG;gBACtB,EAAE,EAAE,gBAAgB;gBACpB,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,aAAa;gBACrB,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE;oBACT,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,YAAY,EAAE,iBAAiB,CAAC,EAAE;oBACvD,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE;oBAC9C,KAAK,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE;iBACtC;aACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACvE,MAAM,CAAC,OAAO,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE;YAC9C,IAAM,SAAS,GAAG;gBAChB,EAAE,EAAE,UAAU;gBACd,KAAK,EAAE,6BAA6B;gBACpC,OAAO,EAAE,sDAAsD;gBAC/D,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,UAAU;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,CAAC,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACnC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAE7B,yBAAyB;wBACzB,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,EAAvB,CAAuB,CAAC,EAAA;;wBADrD,yBAAyB;wBACzB,SAAqD,CAAC;wBAEhD,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACrB,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;wBAErC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;wBACrC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;;;;aACvD,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAM,cAAc,GAAG,GAAG,CAAC;YAC3B,IAAM,oBAAoB,GAAG,IAAI,CAAC;YAClC,IAAM,gBAAgB,GAAG,KAAK,CAAC;YAE/B,IAAM,UAAU,GAAG,aAAa,CAAC;YACjC,IAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAEtC,IAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvC,IAAM,oBAAoB,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAC9D,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;YAC1E,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;YAElE,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAC9D,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;YAC1E,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;;;;;wBAClC,oBAAoB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC;4BAC3D,OAAA,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,CAAC,CAAC,EAAV,CAAU,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,EAAjD,CAAiD,CAAC;wBAAzE,CAAyE,CAC1E,CAAC;wBAEI,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACb,qBAAM,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAA;;wBAAjD,OAAO,GAAG,SAAuC;wBACjD,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAE3B,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;wBACjC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,wCAAwC;;;;aACxF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAM,aAAa,GAAG;gBACpB,IAAI;gBACJ,SAAS;gBACT,EAAE;gBACF,EAAE;gBACF,EAAE;gBACF,cAAc;aACf,CAAC;YAEF,aAAa,CAAC,OAAO,CAAC,UAAA,KAAK;gBACzB,+BAA+B;gBAC/B,IAAM,OAAO,GAAG,UAAC,IAAS;oBACxB,IAAI,CAAC,IAAI;wBAAE,OAAO,KAAK,CAAC;oBACxB,IAAI,OAAO,IAAI,KAAK,QAAQ;wBAAE,OAAO,KAAK,CAAC;oBAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;wBAAE,OAAO,KAAK,CAAC;oBACtC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;wBAAE,OAAO,KAAK,CAAC;oBACjD,OAAO,IAAI,CAAC;gBACd,CAAC,CAAC;gBAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,0BAA0B;YAC1B,IAAM,UAAU,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;YAChD,IAAM,OAAO,GAAG,UAAC,IAAS;gBACxB,OAAO,IAAI;oBACJ,OAAO,IAAI,KAAK,QAAQ;oBACxB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;oBACpB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACtC,CAAC,CAAC;YACF,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;;wBAErC,gBAAgB,GAAG,UAAO,UAAmB;;gCACjD,IAAI,UAAU,EAAE,CAAC;oCACf,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;gCACnC,CAAC;gCACD,sBAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,EAAC;;6BAC5C,CAAC;wBAGoB,qBAAM,gBAAgB,CAAC,KAAK,CAAC,EAAA;;wBAA7C,aAAa,GAAG,SAA6B;wBACnD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;wBAIvC,qBAAM,gBAAgB,CAAC,IAAI,CAAC,EAAA;;wBAA5B,SAA4B,CAAC;wBAC7B,IAAI,CAAC,6BAA6B,CAAC,CAAC;;;;wBAEpC,MAAM,CAAE,OAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;;;;;aAE1D,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;YACxC,IAAM,cAAc,GAAG,UAAC,KAAa,EAAE,GAAW,EAAE,GAAW;gBAC7D,OAAO,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC;YACtC,CAAC,CAAC;YAEF,wBAAwB;YACxB,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;YAC5D,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;YAE7D,0BAA0B;YAC1B,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY;YAC1D,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY;YAC3D,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE;QACpC,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,EAAE,EAAE,UAAU;oBACd,KAAK,EAAE,WAAW;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;gBACD,OAAO,EAAE,sBAAsB;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAClD,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAM,iBAAiB,GAAG;gBACxB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,wBAAwB;oBACjC,OAAO,EAAE,CAAC,mBAAmB,EAAE,sBAAsB,CAAC;iBACvD;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,iBAAiB,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAM,qBAAqB,GAAG;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;oBAC5B,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;iBAC7B;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,CAAC;oBACP,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,EAAE;oBACT,UAAU,EAAE,CAAC;oBACb,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,KAAK;iBACf;aACF,CAAC;YAEF,MAAM,CAAC,qBAAqB,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,2CAA2C,EAAE;YAC9C,0DAA0D;YAC1D,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAE3C,qEAAqE;YACrE,IAAM,eAAe,GAAG,CAAC,UAAU,CAAC,CAAC;YACrC,eAAe,CAAC,OAAO,CAAC,UAAA,MAAM;gBAC5B,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;YACxC,oDAAoD;YACpD,MAAM,CAAC,OAAO,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,mCAAmC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/B,MAAM,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACzB,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/comprehensive-validation.test.ts"], "sourcesContent": ["/**\n * Comprehensive Validation Tests\n * Tests core functionality, security, and data validation without database dependencies\n */\n\ndescribe('Comprehensive System Validation', () => {\n  describe('🔐 Authentication & Security', () => {\n    it('should validate email formats correctly', () => {\n      const validEmails = [\n        '<EMAIL>',\n        '<EMAIL>',\n        '<EMAIL>'\n      ];\n\n      const invalidEmails = [\n        'invalid-email',\n        '@example.com',\n        'user@',\n        'user <EMAIL>'\n      ];\n\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\n      validEmails.forEach(email => {\n        expect(email).toMatch(emailRegex);\n      });\n\n      invalidEmails.forEach(email => {\n        expect(email).not.toMatch(emailRegex);\n      });\n    });\n\n    it('should detect XSS attempts in user input', () => {\n      const xssPayloads = [\n        '<script>alert(\"xss\")</script>',\n        'javascript:alert(\"xss\")',\n        '<img src=x onerror=alert(\"xss\")>',\n        '<svg onload=alert(\"xss\")>',\n        '\"><script>alert(\"xss\")</script>'\n      ];\n\n      const xssPattern = /<script|javascript:|onerror=|onload=|<svg/i;\n\n      xssPayloads.forEach(payload => {\n        expect(payload).toMatch(xssPattern);\n      });\n\n      // Safe content should not match\n      const safeContent = 'This is safe user content';\n      expect(safeContent).not.toMatch(xssPattern);\n    });\n\n    it('should detect SQL injection attempts', () => {\n      const sqlInjectionPayloads = [\n        \"'; DROP TABLE users; --\",\n        \"' OR '1'='1\",\n        \"' UNION SELECT * FROM users --\",\n        \"admin'--\",\n        \"' OR 1=1#\"\n      ];\n\n      const sqlPattern = /('|;|--|union|drop|select|insert|update|delete)/i;\n\n      sqlInjectionPayloads.forEach(payload => {\n        expect(payload).toMatch(sqlPattern);\n      });\n\n      // Safe queries should not match\n      const safeQuery = 'normal search term';\n      expect(safeQuery).not.toMatch(sqlPattern);\n    });\n\n    it('should validate password strength requirements', () => {\n      const strongPasswords = [\n        'StrongPass123!',\n        'MySecure@Password2024',\n        'ComplexPass1!'\n      ];\n\n      const weakPasswords = [\n        '123',\n        'password',\n        'abc',\n        '12345678',\n        'Password' // Missing special char and number\n      ];\n\n      // Password should have: min 8 chars, uppercase, lowercase, number, special char\n      const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\n\n      strongPasswords.forEach(password => {\n        expect(password).toMatch(strongPasswordRegex);\n      });\n\n      weakPasswords.forEach(password => {\n        expect(password).not.toMatch(strongPasswordRegex);\n      });\n    });\n  });\n\n  describe('📊 Data Validation', () => {\n    it('should validate learning resource data structure', () => {\n      const validResource = {\n        id: 'resource-123',\n        title: 'Introduction to Programming',\n        description: 'A comprehensive course for beginners',\n        url: 'https://example.com/course',\n        type: 'COURSE',\n        category: 'TECHNOLOGY',\n        skillLevel: 'BEGINNER',\n        cost: 'FREE',\n        duration: '4 weeks',\n        author: 'John Doe',\n        isActive: true\n      };\n\n      // Required fields validation\n      expect(validResource).toHaveProperty('title');\n      expect(validResource).toHaveProperty('url');\n      expect(validResource).toHaveProperty('category');\n      expect(validResource.title.length).toBeGreaterThan(0);\n      expect(validResource.url).toMatch(/^https?:\\/\\/.+/);\n      expect(['TECHNOLOGY', 'BUSINESS', 'DESIGN', 'MARKETING']).toContain(validResource.category);\n      expect(['BEGINNER', 'INTERMEDIATE', 'ADVANCED']).toContain(validResource.skillLevel);\n      expect(['FREE', 'PAID', 'FREEMIUM']).toContain(validResource.cost);\n    });\n\n    it('should validate career path data structure', () => {\n      const validCareerPath = {\n        id: 'career-456',\n        title: 'Full Stack Developer',\n        description: 'Build web applications from front to back',\n        category: 'TECHNOLOGY',\n        skillLevel: 'INTERMEDIATE',\n        averageSalary: 85000,\n        jobGrowthRate: 22.8,\n        isActive: true\n      };\n\n      expect(validCareerPath).toHaveProperty('title');\n      expect(validCareerPath).toHaveProperty('description');\n      expect(typeof validCareerPath.averageSalary).toBe('number');\n      expect(typeof validCareerPath.jobGrowthRate).toBe('number');\n      expect(validCareerPath.averageSalary).toBeGreaterThan(0);\n      expect(validCareerPath.jobGrowthRate).toBeGreaterThanOrEqual(0);\n    });\n\n    it('should validate assessment data structure', () => {\n      const validAssessment = {\n        id: 'assessment-789',\n        userId: 'user-123',\n        status: 'IN_PROGRESS',\n        currentStep: 3,\n        responses: {\n          step1: { interests: ['technology', 'problem-solving'] },\n          step2: { skills: ['programming', 'analysis'] },\n          step3: { experience: 'intermediate' }\n        }\n      };\n\n      expect(validAssessment).toHaveProperty('userId');\n      expect(validAssessment).toHaveProperty('status');\n      expect(['IN_PROGRESS', 'COMPLETED']).toContain(validAssessment.status);\n      expect(typeof validAssessment.currentStep).toBe('number');\n      expect(validAssessment.currentStep).toBeGreaterThan(0);\n      expect(typeof validAssessment.responses).toBe('object');\n    });\n\n    it('should validate forum post data structure', () => {\n      const validPost = {\n        id: 'post-101',\n        title: 'Career Change Advice Needed',\n        content: 'I am looking to transition from marketing to tech...',\n        category: 'CAREER_ADVICE',\n        authorId: 'user-456',\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      expect(validPost).toHaveProperty('title');\n      expect(validPost).toHaveProperty('content');\n      expect(validPost).toHaveProperty('authorId');\n      expect(validPost.title.length).toBeGreaterThan(0);\n      expect(validPost.content.length).toBeGreaterThan(0);\n      expect(['GENERAL', 'CAREER_ADVICE', 'TECHNICAL_HELP', 'NETWORKING']).toContain(validPost.category);\n    });\n  });\n\n  describe('⚡ Performance Validation', () => {\n    it('should measure operation performance', async () => {\n      const startTime = Date.now();\n      \n      // Simulate API operation\n      await new Promise(resolve => setTimeout(resolve, 50));\n      \n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      expect(duration).toBeGreaterThan(40);\n      expect(duration).toBeLessThan(1000); // Should be fast\n    });\n\n    it('should validate data size limits', () => {\n      const maxTitleLength = 200;\n      const maxDescriptionLength = 2000;\n      const maxContentLength = 10000;\n\n      const validTitle = 'Valid Title';\n      const validDescription = 'A'.repeat(1000);\n      const validContent = 'B'.repeat(5000);\n\n      const oversizedTitle = 'C'.repeat(300);\n      const oversizedDescription = 'D'.repeat(3000);\n      const oversizedContent = 'E'.repeat(15000);\n\n      expect(validTitle.length).toBeLessThanOrEqual(maxTitleLength);\n      expect(validDescription.length).toBeLessThanOrEqual(maxDescriptionLength);\n      expect(validContent.length).toBeLessThanOrEqual(maxContentLength);\n\n      expect(oversizedTitle.length).toBeGreaterThan(maxTitleLength);\n      expect(oversizedDescription.length).toBeGreaterThan(maxDescriptionLength);\n      expect(oversizedContent.length).toBeGreaterThan(maxContentLength);\n    });\n\n    it('should handle concurrent operations', async () => {\n      const concurrentOperations = Array.from({ length: 10 }, (_, i) => \n        new Promise(resolve => setTimeout(() => resolve(i), Math.random() * 100))\n      );\n\n      const startTime = Date.now();\n      const results = await Promise.all(concurrentOperations);\n      const endTime = Date.now();\n\n      expect(results).toHaveLength(10);\n      expect(endTime - startTime).toBeLessThan(500); // Should handle concurrency efficiently\n    });\n  });\n\n  describe('🛡️ Error Handling', () => {\n    it('should handle invalid input gracefully', () => {\n      const invalidInputs = [\n        null,\n        undefined,\n        '',\n        {},\n        [],\n        'invalid-data'\n      ];\n\n      invalidInputs.forEach(input => {\n        // Simulate validation function\n        const isValid = (data: any) => {\n          if (!data) return false;\n          if (typeof data !== 'object') return false;\n          if (Array.isArray(data)) return false;\n          if (Object.keys(data).length === 0) return false;\n          return true;\n        };\n\n        expect(isValid(input)).toBe(false);\n      });\n\n      // Valid input should pass\n      const validInput = { name: 'test', value: 123 };\n      const isValid = (data: any) => {\n        return data && \n               typeof data === 'object' && \n               !Array.isArray(data) && \n               Object.keys(data).length > 0;\n      };\n      expect(isValid(validInput)).toBe(true);\n    });\n\n    it('should validate network error handling', async () => {\n      // Simulate network failure\n      const networkOperation = async (shouldFail: boolean) => {\n        if (shouldFail) {\n          throw new Error('Network Error');\n        }\n        return { success: true, data: 'response' };\n      };\n\n      // Should handle success\n      const successResult = await networkOperation(false);\n      expect(successResult.success).toBe(true);\n\n      // Should handle failure\n      try {\n        await networkOperation(true);\n        fail('Should have thrown an error');\n      } catch (error) {\n        expect((error as Error).message).toBe('Network Error');\n      }\n    });\n\n    it('should validate boundary conditions', () => {\n      const testBoundaries = (value: number, min: number, max: number) => {\n        return value >= min && value <= max;\n      };\n\n      // Test valid boundaries\n      expect(testBoundaries(5, 1, 10)).toBe(true);\n      expect(testBoundaries(1, 1, 10)).toBe(true); // Min boundary\n      expect(testBoundaries(10, 1, 10)).toBe(true); // Max boundary\n\n      // Test invalid boundaries\n      expect(testBoundaries(0, 1, 10)).toBe(false); // Below min\n      expect(testBoundaries(11, 1, 10)).toBe(false); // Above max\n      expect(testBoundaries(-5, 1, 10)).toBe(false); // Negative\n    });\n  });\n\n  describe('🔄 Integration Validation', () => {\n    it('should validate API response structure', () => {\n      const mockApiResponse = {\n        success: true,\n        data: {\n          id: 'item-123',\n          title: 'Test Item',\n          createdAt: new Date().toISOString()\n        },\n        message: 'Operation successful',\n        timestamp: new Date().toISOString()\n      };\n\n      expect(mockApiResponse).toHaveProperty('success');\n      expect(mockApiResponse).toHaveProperty('data');\n      expect(typeof mockApiResponse.success).toBe('boolean');\n      expect(typeof mockApiResponse.data).toBe('object');\n      expect(mockApiResponse.data).toHaveProperty('id');\n    });\n\n    it('should validate error response structure', () => {\n      const mockErrorResponse = {\n        success: false,\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Invalid input provided',\n          details: ['Email is required', 'Password is too weak']\n        },\n        timestamp: new Date().toISOString()\n      };\n\n      expect(mockErrorResponse.success).toBe(false);\n      expect(mockErrorResponse).toHaveProperty('error');\n      expect(mockErrorResponse.error).toHaveProperty('code');\n      expect(mockErrorResponse.error).toHaveProperty('message');\n      expect(Array.isArray(mockErrorResponse.error.details)).toBe(true);\n    });\n\n    it('should validate pagination structure', () => {\n      const mockPaginatedResponse = {\n        success: true,\n        data: [\n          { id: '1', title: 'Item 1' },\n          { id: '2', title: 'Item 2' }\n        ],\n        pagination: {\n          page: 1,\n          limit: 10,\n          total: 25,\n          totalPages: 3,\n          hasNext: true,\n          hasPrev: false\n        }\n      };\n\n      expect(mockPaginatedResponse).toHaveProperty('pagination');\n      expect(typeof mockPaginatedResponse.pagination.page).toBe('number');\n      expect(typeof mockPaginatedResponse.pagination.total).toBe('number');\n      expect(typeof mockPaginatedResponse.pagination.hasNext).toBe('boolean');\n      expect(mockPaginatedResponse.pagination.page).toBeGreaterThan(0);\n      expect(mockPaginatedResponse.pagination.total).toBeGreaterThanOrEqual(0);\n    });\n  });\n\n  describe('✅ System Health Checks', () => {\n    it('should validate environment configuration', () => {\n      // Check that required environment variables are available\n      expect(process.env.NODE_ENV).toBeDefined();\n      \n      // In a real test, you would check for required environment variables\n      const requiredEnvVars = ['NODE_ENV'];\n      requiredEnvVars.forEach(envVar => {\n        expect(process.env[envVar]).toBeDefined();\n      });\n    });\n\n    it('should validate system dependencies', () => {\n      // Check that core JavaScript features are available\n      expect(typeof Promise).toBe('function');\n      expect(typeof JSON.parse).toBe('function');\n      expect(typeof JSON.stringify).toBe('function');\n      expect(typeof setTimeout).toBe('function');\n      expect(typeof Date).toBe('function');\n    });\n\n    it('should validate test framework functionality', () => {\n      // Verify Jest is working correctly\n      expect(expect).toBeDefined();\n      expect(describe).toBeDefined();\n      expect(it).toBeDefined();\n      expect(beforeEach).toBeDefined();\n      expect(afterEach).toBeDefined();\n    });\n  });\n});\n"], "version": 3}