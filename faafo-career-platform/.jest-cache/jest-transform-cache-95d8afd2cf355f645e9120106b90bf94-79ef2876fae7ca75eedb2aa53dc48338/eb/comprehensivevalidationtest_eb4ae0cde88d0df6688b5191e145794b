01ac58ed17291b72f5c1041d5ceb881c
"use strict";
/**
 * Comprehensive Validation Tests
 * Tests core functionality, security, and data validation without database dependencies
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
describe('Comprehensive System Validation', function () {
    describe('🔐 Authentication & Security', function () {
        it('should validate email formats correctly', function () {
            var validEmails = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];
            var invalidEmails = [
                'invalid-email',
                '@example.com',
                'user@',
                'user <EMAIL>'
            ];
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            validEmails.forEach(function (email) {
                expect(email).toMatch(emailRegex);
            });
            invalidEmails.forEach(function (email) {
                expect(email).not.toMatch(emailRegex);
            });
        });
        it('should detect XSS attempts in user input', function () {
            var xssPayloads = [
                '<script>alert("xss")</script>',
                'javascript:alert("xss")',
                '<img src=x onerror=alert("xss")>',
                '<svg onload=alert("xss")>',
                '"><script>alert("xss")</script>'
            ];
            var xssPattern = /<script|javascript:|onerror=|onload=|<svg/i;
            xssPayloads.forEach(function (payload) {
                expect(payload).toMatch(xssPattern);
            });
            // Safe content should not match
            var safeContent = 'This is safe user content';
            expect(safeContent).not.toMatch(xssPattern);
        });
        it('should detect SQL injection attempts', function () {
            var sqlInjectionPayloads = [
                "'; DROP TABLE users; --",
                "' OR '1'='1",
                "' UNION SELECT * FROM users --",
                "admin'--",
                "' OR 1=1#"
            ];
            var sqlPattern = /('|;|--|union|drop|select|insert|update|delete)/i;
            sqlInjectionPayloads.forEach(function (payload) {
                expect(payload).toMatch(sqlPattern);
            });
            // Safe queries should not match
            var safeQuery = 'normal search term';
            expect(safeQuery).not.toMatch(sqlPattern);
        });
        it('should validate password strength requirements', function () {
            var strongPasswords = [
                'StrongPass123!',
                'MySecure@Password2024',
                'ComplexPass1!'
            ];
            var weakPasswords = [
                '123',
                'password',
                'abc',
                '12345678',
                'Password' // Missing special char and number
            ];
            // Password should have: min 8 chars, uppercase, lowercase, number, special char
            var strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
            strongPasswords.forEach(function (password) {
                expect(password).toMatch(strongPasswordRegex);
            });
            weakPasswords.forEach(function (password) {
                expect(password).not.toMatch(strongPasswordRegex);
            });
        });
    });
    describe('📊 Data Validation', function () {
        it('should validate learning resource data structure', function () {
            var validResource = {
                id: 'resource-123',
                title: 'Introduction to Programming',
                description: 'A comprehensive course for beginners',
                url: 'https://example.com/course',
                type: 'COURSE',
                category: 'TECHNOLOGY',
                skillLevel: 'BEGINNER',
                cost: 'FREE',
                duration: '4 weeks',
                author: 'John Doe',
                isActive: true
            };
            // Required fields validation
            expect(validResource).toHaveProperty('title');
            expect(validResource).toHaveProperty('url');
            expect(validResource).toHaveProperty('category');
            expect(validResource.title.length).toBeGreaterThan(0);
            expect(validResource.url).toMatch(/^https?:\/\/.+/);
            expect(['TECHNOLOGY', 'BUSINESS', 'DESIGN', 'MARKETING']).toContain(validResource.category);
            expect(['BEGINNER', 'INTERMEDIATE', 'ADVANCED']).toContain(validResource.skillLevel);
            expect(['FREE', 'PAID', 'FREEMIUM']).toContain(validResource.cost);
        });
        it('should validate career path data structure', function () {
            var validCareerPath = {
                id: 'career-456',
                title: 'Full Stack Developer',
                description: 'Build web applications from front to back',
                category: 'TECHNOLOGY',
                skillLevel: 'INTERMEDIATE',
                averageSalary: 85000,
                jobGrowthRate: 22.8,
                isActive: true
            };
            expect(validCareerPath).toHaveProperty('title');
            expect(validCareerPath).toHaveProperty('description');
            expect(typeof validCareerPath.averageSalary).toBe('number');
            expect(typeof validCareerPath.jobGrowthRate).toBe('number');
            expect(validCareerPath.averageSalary).toBeGreaterThan(0);
            expect(validCareerPath.jobGrowthRate).toBeGreaterThanOrEqual(0);
        });
        it('should validate assessment data structure', function () {
            var validAssessment = {
                id: 'assessment-789',
                userId: 'user-123',
                status: 'IN_PROGRESS',
                currentStep: 3,
                responses: {
                    step1: { interests: ['technology', 'problem-solving'] },
                    step2: { skills: ['programming', 'analysis'] },
                    step3: { experience: 'intermediate' }
                }
            };
            expect(validAssessment).toHaveProperty('userId');
            expect(validAssessment).toHaveProperty('status');
            expect(['IN_PROGRESS', 'COMPLETED']).toContain(validAssessment.status);
            expect(typeof validAssessment.currentStep).toBe('number');
            expect(validAssessment.currentStep).toBeGreaterThan(0);
            expect(typeof validAssessment.responses).toBe('object');
        });
        it('should validate forum post data structure', function () {
            var validPost = {
                id: 'post-101',
                title: 'Career Change Advice Needed',
                content: 'I am looking to transition from marketing to tech...',
                category: 'CAREER_ADVICE',
                authorId: 'user-456',
                createdAt: new Date(),
                updatedAt: new Date()
            };
            expect(validPost).toHaveProperty('title');
            expect(validPost).toHaveProperty('content');
            expect(validPost).toHaveProperty('authorId');
            expect(validPost.title.length).toBeGreaterThan(0);
            expect(validPost.content.length).toBeGreaterThan(0);
            expect(['GENERAL', 'CAREER_ADVICE', 'TECHNICAL_HELP', 'NETWORKING']).toContain(validPost.category);
        });
    });
    describe('⚡ Performance Validation', function () {
        it('should measure operation performance', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startTime, endTime, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startTime = Date.now();
                        // Simulate API operation
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 50); })];
                    case 1:
                        // Simulate API operation
                        _a.sent();
                        endTime = Date.now();
                        duration = endTime - startTime;
                        expect(duration).toBeGreaterThan(40);
                        expect(duration).toBeLessThan(1000); // Should be fast
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate data size limits', function () {
            var maxTitleLength = 200;
            var maxDescriptionLength = 2000;
            var maxContentLength = 10000;
            var validTitle = 'Valid Title';
            var validDescription = 'A'.repeat(1000);
            var validContent = 'B'.repeat(5000);
            var oversizedTitle = 'C'.repeat(300);
            var oversizedDescription = 'D'.repeat(3000);
            var oversizedContent = 'E'.repeat(15000);
            expect(validTitle.length).toBeLessThanOrEqual(maxTitleLength);
            expect(validDescription.length).toBeLessThanOrEqual(maxDescriptionLength);
            expect(validContent.length).toBeLessThanOrEqual(maxContentLength);
            expect(oversizedTitle.length).toBeGreaterThan(maxTitleLength);
            expect(oversizedDescription.length).toBeGreaterThan(maxDescriptionLength);
            expect(oversizedContent.length).toBeGreaterThan(maxContentLength);
        });
        it('should handle concurrent operations', function () { return __awaiter(void 0, void 0, void 0, function () {
            var concurrentOperations, startTime, results, endTime;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        concurrentOperations = Array.from({ length: 10 }, function (_, i) {
                            return new Promise(function (resolve) { return setTimeout(function () { return resolve(i); }, Math.random() * 100); });
                        });
                        startTime = Date.now();
                        return [4 /*yield*/, Promise.all(concurrentOperations)];
                    case 1:
                        results = _a.sent();
                        endTime = Date.now();
                        expect(results).toHaveLength(10);
                        expect(endTime - startTime).toBeLessThan(500); // Should handle concurrency efficiently
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('🛡️ Error Handling', function () {
        it('should handle invalid input gracefully', function () {
            var invalidInputs = [
                null,
                undefined,
                '',
                {},
                [],
                'invalid-data'
            ];
            invalidInputs.forEach(function (input) {
                // Simulate validation function
                var isValid = function (data) {
                    if (!data)
                        return false;
                    if (typeof data !== 'object')
                        return false;
                    if (Array.isArray(data))
                        return false;
                    if (Object.keys(data).length === 0)
                        return false;
                    return true;
                };
                expect(isValid(input)).toBe(false);
            });
            // Valid input should pass
            var validInput = { name: 'test', value: 123 };
            var isValid = function (data) {
                return data &&
                    typeof data === 'object' &&
                    !Array.isArray(data) &&
                    Object.keys(data).length > 0;
            };
            expect(isValid(validInput)).toBe(true);
        });
        it('should validate network error handling', function () { return __awaiter(void 0, void 0, void 0, function () {
            var networkOperation, successResult, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        networkOperation = function (shouldFail) { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                if (shouldFail) {
                                    throw new Error('Network Error');
                                }
                                return [2 /*return*/, { success: true, data: 'response' }];
                            });
                        }); };
                        return [4 /*yield*/, networkOperation(false)];
                    case 1:
                        successResult = _a.sent();
                        expect(successResult.success).toBe(true);
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, networkOperation(true)];
                    case 3:
                        _a.sent();
                        fail('Should have thrown an error');
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _a.sent();
                        expect(error_1.message).toBe('Network Error');
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        }); });
        it('should validate boundary conditions', function () {
            var testBoundaries = function (value, min, max) {
                return value >= min && value <= max;
            };
            // Test valid boundaries
            expect(testBoundaries(5, 1, 10)).toBe(true);
            expect(testBoundaries(1, 1, 10)).toBe(true); // Min boundary
            expect(testBoundaries(10, 1, 10)).toBe(true); // Max boundary
            // Test invalid boundaries
            expect(testBoundaries(0, 1, 10)).toBe(false); // Below min
            expect(testBoundaries(11, 1, 10)).toBe(false); // Above max
            expect(testBoundaries(-5, 1, 10)).toBe(false); // Negative
        });
    });
    describe('🔄 Integration Validation', function () {
        it('should validate API response structure', function () {
            var mockApiResponse = {
                success: true,
                data: {
                    id: 'item-123',
                    title: 'Test Item',
                    createdAt: new Date().toISOString()
                },
                message: 'Operation successful',
                timestamp: new Date().toISOString()
            };
            expect(mockApiResponse).toHaveProperty('success');
            expect(mockApiResponse).toHaveProperty('data');
            expect(typeof mockApiResponse.success).toBe('boolean');
            expect(typeof mockApiResponse.data).toBe('object');
            expect(mockApiResponse.data).toHaveProperty('id');
        });
        it('should validate error response structure', function () {
            var mockErrorResponse = {
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Invalid input provided',
                    details: ['Email is required', 'Password is too weak']
                },
                timestamp: new Date().toISOString()
            };
            expect(mockErrorResponse.success).toBe(false);
            expect(mockErrorResponse).toHaveProperty('error');
            expect(mockErrorResponse.error).toHaveProperty('code');
            expect(mockErrorResponse.error).toHaveProperty('message');
            expect(Array.isArray(mockErrorResponse.error.details)).toBe(true);
        });
        it('should validate pagination structure', function () {
            var mockPaginatedResponse = {
                success: true,
                data: [
                    { id: '1', title: 'Item 1' },
                    { id: '2', title: 'Item 2' }
                ],
                pagination: {
                    page: 1,
                    limit: 10,
                    total: 25,
                    totalPages: 3,
                    hasNext: true,
                    hasPrev: false
                }
            };
            expect(mockPaginatedResponse).toHaveProperty('pagination');
            expect(typeof mockPaginatedResponse.pagination.page).toBe('number');
            expect(typeof mockPaginatedResponse.pagination.total).toBe('number');
            expect(typeof mockPaginatedResponse.pagination.hasNext).toBe('boolean');
            expect(mockPaginatedResponse.pagination.page).toBeGreaterThan(0);
            expect(mockPaginatedResponse.pagination.total).toBeGreaterThanOrEqual(0);
        });
    });
    describe('✅ System Health Checks', function () {
        it('should validate environment configuration', function () {
            // Check that required environment variables are available
            expect(process.env.NODE_ENV).toBeDefined();
            // In a real test, you would check for required environment variables
            var requiredEnvVars = ['NODE_ENV'];
            requiredEnvVars.forEach(function (envVar) {
                expect(process.env[envVar]).toBeDefined();
            });
        });
        it('should validate system dependencies', function () {
            // Check that core JavaScript features are available
            expect(typeof Promise).toBe('function');
            expect(typeof JSON.parse).toBe('function');
            expect(typeof JSON.stringify).toBe('function');
            expect(typeof setTimeout).toBe('function');
            expect(typeof Date).toBe('function');
        });
        it('should validate test framework functionality', function () {
            // Verify Jest is working correctly
            expect(expect).toBeDefined();
            expect(describe).toBeDefined();
            expect(it).toBeDefined();
            expect(beforeEach).toBeDefined();
            expect(afterEach).toBeDefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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