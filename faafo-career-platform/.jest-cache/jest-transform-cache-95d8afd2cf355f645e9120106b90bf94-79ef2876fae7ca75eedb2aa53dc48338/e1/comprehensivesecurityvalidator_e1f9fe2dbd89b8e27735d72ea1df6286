7760198218bc1f0ead6dca61470bf66b
"use strict";

/**
 * Comprehensive Security Validator
 * Provides enhanced input validation, threat detection, and security checks
 */
/* istanbul ignore next */
function cov_vsgvdem0f() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/comprehensive-security-validator.ts";
  var hash = "924c67a5a8850fdd15a04d529f6b34864ea757e2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/comprehensive-security-validator.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 16
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "2": {
        start: {
          line: 7,
          column: 91
        },
        end: {
          line: 7,
          column: 106
        }
      },
      "3": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 13,
          column: 7
        }
      },
      "4": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "5": {
        start: {
          line: 9,
          column: 42
        },
        end: {
          line: 9,
          column: 70
        }
      },
      "6": {
        start: {
          line: 9,
          column: 85
        },
        end: {
          line: 9,
          column: 95
        }
      },
      "7": {
        start: {
          line: 10,
          column: 35
        },
        end: {
          line: 10,
          column: 100
        }
      },
      "8": {
        start: {
          line: 10,
          column: 41
        },
        end: {
          line: 10,
          column: 73
        }
      },
      "9": {
        start: {
          line: 10,
          column: 88
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "10": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 116
        }
      },
      "11": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 78
        }
      },
      "12": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "13": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 104
        }
      },
      "14": {
        start: {
          line: 16,
          column: 43
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "15": {
        start: {
          line: 16,
          column: 57
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "16": {
        start: {
          line: 16,
          column: 69
        },
        end: {
          line: 16,
          column: 81
        }
      },
      "17": {
        start: {
          line: 16,
          column: 119
        },
        end: {
          line: 16,
          column: 196
        }
      },
      "18": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 160
        }
      },
      "19": {
        start: {
          line: 17,
          column: 141
        },
        end: {
          line: 17,
          column: 153
        }
      },
      "20": {
        start: {
          line: 18,
          column: 23
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "21": {
        start: {
          line: 18,
          column: 45
        },
        end: {
          line: 18,
          column: 65
        }
      },
      "22": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "23": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "24": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "25": {
        start: {
          line: 21,
          column: 50
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "26": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "27": {
        start: {
          line: 22,
          column: 160
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "28": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "29": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "30": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 36,
          column: 13
        }
      },
      "31": {
        start: {
          line: 25,
          column: 32
        },
        end: {
          line: 25,
          column: 39
        }
      },
      "32": {
        start: {
          line: 25,
          column: 40
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "33": {
        start: {
          line: 26,
          column: 24
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "34": {
        start: {
          line: 26,
          column: 35
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "35": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "36": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 45
        }
      },
      "37": {
        start: {
          line: 27,
          column: 46
        },
        end: {
          line: 27,
          column: 55
        }
      },
      "38": {
        start: {
          line: 27,
          column: 56
        },
        end: {
          line: 27,
          column: 65
        }
      },
      "39": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "40": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "41": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "42": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 128
        }
      },
      "43": {
        start: {
          line: 30,
          column: 110
        },
        end: {
          line: 30,
          column: 116
        }
      },
      "44": {
        start: {
          line: 30,
          column: 117
        },
        end: {
          line: 30,
          column: 126
        }
      },
      "45": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 106
        }
      },
      "46": {
        start: {
          line: 31,
          column: 81
        },
        end: {
          line: 31,
          column: 97
        }
      },
      "47": {
        start: {
          line: 31,
          column: 98
        },
        end: {
          line: 31,
          column: 104
        }
      },
      "48": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 89
        }
      },
      "49": {
        start: {
          line: 32,
          column: 57
        },
        end: {
          line: 32,
          column: 72
        }
      },
      "50": {
        start: {
          line: 32,
          column: 73
        },
        end: {
          line: 32,
          column: 80
        }
      },
      "51": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "52": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "53": {
        start: {
          line: 33,
          column: 47
        },
        end: {
          line: 33,
          column: 62
        }
      },
      "54": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "55": {
        start: {
          line: 33,
          column: 79
        },
        end: {
          line: 33,
          column: 85
        }
      },
      "56": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "57": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "58": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 33
        }
      },
      "59": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 43
        }
      },
      "60": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "61": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "62": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 41
        }
      },
      "63": {
        start: {
          line: 38,
          column: 54
        },
        end: {
          line: 38,
          column: 64
        }
      },
      "64": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "65": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "66": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "67": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 62
        }
      },
      "68": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 48
        }
      },
      "69": {
        start: {
          line: 44,
          column: 18
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "70": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 30
        }
      },
      "71": {
        start: {
          line: 46,
          column: 52
        },
        end: {
          line: 388,
          column: 3
        }
      },
      "72": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 112,
          column: 6
        }
      },
      "73": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 111,
          column: 11
        }
      },
      "74": {
        start: {
          line: 55,
          column: 24
        },
        end: {
          line: 55,
          column: 28
        }
      },
      "75": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 110,
          column: 15
        }
      },
      "76": {
        start: {
          line: 57,
          column: 16
        },
        end: {
          line: 109,
          column: 17
        }
      },
      "77": {
        start: {
          line: 58,
          column: 28
        },
        end: {
          line: 58,
          column: 85
        }
      },
      "78": {
        start: {
          line: 60,
          column: 24
        },
        end: {
          line: 60,
          column: 44
        }
      },
      "79": {
        start: {
          line: 61,
          column: 24
        },
        end: {
          line: 61,
          column: 37
        }
      },
      "80": {
        start: {
          line: 62,
          column: 24
        },
        end: {
          line: 62,
          column: 38
        }
      },
      "81": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 107,
          column: 25
        }
      },
      "82": {
        start: {
          line: 65,
          column: 28
        },
        end: {
          line: 76,
          column: 29
        }
      },
      "83": {
        start: {
          line: 66,
          column: 32
        },
        end: {
          line: 66,
          column: 70
        }
      },
      "84": {
        start: {
          line: 67,
          column: 32
        },
        end: {
          line: 75,
          column: 33
        }
      },
      "85": {
        start: {
          line: 68,
          column: 36
        },
        end: {
          line: 73,
          column: 39
        }
      },
      "86": {
        start: {
          line: 74,
          column: 36
        },
        end: {
          line: 74,
          column: 52
        }
      },
      "87": {
        start: {
          line: 77,
          column: 28
        },
        end: {
          line: 77,
          column: 81
        }
      },
      "88": {
        start: {
          line: 78,
          column: 28
        },
        end: {
          line: 78,
          column: 73
        }
      },
      "89": {
        start: {
          line: 79,
          column: 28
        },
        end: {
          line: 81,
          column: 34
        }
      },
      "90": {
        start: {
          line: 80,
          column: 32
        },
        end: {
          line: 80,
          column: 83
        }
      },
      "91": {
        start: {
          line: 82,
          column: 28
        },
        end: {
          line: 82,
          column: 81
        }
      },
      "92": {
        start: {
          line: 83,
          column: 28
        },
        end: {
          line: 83,
          column: 72
        }
      },
      "93": {
        start: {
          line: 84,
          column: 28
        },
        end: {
          line: 86,
          column: 34
        }
      },
      "94": {
        start: {
          line: 85,
          column: 32
        },
        end: {
          line: 85,
          column: 83
        }
      },
      "95": {
        start: {
          line: 87,
          column: 28
        },
        end: {
          line: 87,
          column: 77
        }
      },
      "96": {
        start: {
          line: 88,
          column: 28
        },
        end: {
          line: 88,
          column: 122
        }
      },
      "97": {
        start: {
          line: 88,
          column: 85
        },
        end: {
          line: 88,
          column: 118
        }
      },
      "98": {
        start: {
          line: 89,
          column: 28
        },
        end: {
          line: 94,
          column: 35
        }
      },
      "99": {
        start: {
          line: 97,
          column: 28
        },
        end: {
          line: 97,
          column: 79
        }
      },
      "100": {
        start: {
          line: 98,
          column: 28
        },
        end: {
          line: 106,
          column: 35
        }
      },
      "101": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 46
        }
      },
      "102": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 182,
          column: 6
        }
      },
      "103": {
        start: {
          line: 117,
          column: 22
        },
        end: {
          line: 117,
          column: 24
        }
      },
      "104": {
        start: {
          line: 118,
          column: 25
        },
        end: {
          line: 118,
          column: 51
        }
      },
      "105": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 130,
          column: 9
        }
      },
      "106": {
        start: {
          line: 120,
          column: 22
        },
        end: {
          line: 120,
          column: 23
        }
      },
      "107": {
        start: {
          line: 120,
          column: 30
        },
        end: {
          line: 120,
          column: 57
        }
      },
      "108": {
        start: {
          line: 121,
          column: 26
        },
        end: {
          line: 121,
          column: 32
        }
      },
      "109": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 129,
          column: 13
        }
      },
      "110": {
        start: {
          line: 123,
          column: 16
        },
        end: {
          line: 128,
          column: 19
        }
      },
      "111": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 142,
          column: 9
        }
      },
      "112": {
        start: {
          line: 132,
          column: 22
        },
        end: {
          line: 132,
          column: 23
        }
      },
      "113": {
        start: {
          line: 132,
          column: 30
        },
        end: {
          line: 132,
          column: 47
        }
      },
      "114": {
        start: {
          line: 133,
          column: 26
        },
        end: {
          line: 133,
          column: 32
        }
      },
      "115": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 141,
          column: 13
        }
      },
      "116": {
        start: {
          line: 135,
          column: 16
        },
        end: {
          line: 140,
          column: 19
        }
      },
      "117": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 154,
          column: 9
        }
      },
      "118": {
        start: {
          line: 144,
          column: 22
        },
        end: {
          line: 144,
          column: 23
        }
      },
      "119": {
        start: {
          line: 144,
          column: 30
        },
        end: {
          line: 144,
          column: 61
        }
      },
      "120": {
        start: {
          line: 145,
          column: 26
        },
        end: {
          line: 145,
          column: 32
        }
      },
      "121": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 153,
          column: 13
        }
      },
      "122": {
        start: {
          line: 147,
          column: 16
        },
        end: {
          line: 152,
          column: 19
        }
      },
      "123": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 166,
          column: 9
        }
      },
      "124": {
        start: {
          line: 156,
          column: 22
        },
        end: {
          line: 156,
          column: 23
        }
      },
      "125": {
        start: {
          line: 156,
          column: 30
        },
        end: {
          line: 156,
          column: 58
        }
      },
      "126": {
        start: {
          line: 157,
          column: 26
        },
        end: {
          line: 157,
          column: 32
        }
      },
      "127": {
        start: {
          line: 158,
          column: 12
        },
        end: {
          line: 165,
          column: 13
        }
      },
      "128": {
        start: {
          line: 159,
          column: 16
        },
        end: {
          line: 164,
          column: 19
        }
      },
      "129": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 178,
          column: 9
        }
      },
      "130": {
        start: {
          line: 168,
          column: 22
        },
        end: {
          line: 168,
          column: 23
        }
      },
      "131": {
        start: {
          line: 168,
          column: 30
        },
        end: {
          line: 168,
          column: 58
        }
      },
      "132": {
        start: {
          line: 169,
          column: 26
        },
        end: {
          line: 169,
          column: 32
        }
      },
      "133": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 177,
          column: 13
        }
      },
      "134": {
        start: {
          line: 171,
          column: 16
        },
        end: {
          line: 176,
          column: 19
        }
      },
      "135": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 52
        }
      },
      "136": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 181,
          column: 23
        }
      },
      "137": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 213,
          column: 6
        }
      },
      "138": {
        start: {
          line: 187,
          column: 22
        },
        end: {
          line: 187,
          column: 24
        }
      },
      "139": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 195,
          column: 9
        }
      },
      "140": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 194,
          column: 15
        }
      },
      "141": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 203,
          column: 9
        }
      },
      "142": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 202,
          column: 15
        }
      },
      "143": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "144": {
        start: {
          line: 206,
          column: 12
        },
        end: {
          line: 210,
          column: 15
        }
      },
      "145": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 212,
          column: 23
        }
      },
      "146": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 238,
          column: 6
        }
      },
      "147": {
        start: {
          line: 218,
          column: 8
        },
        end: {
          line: 237,
          column: 11
        }
      },
      "148": {
        start: {
          line: 221,
          column: 12
        },
        end: {
          line: 236,
          column: 15
        }
      },
      "149": {
        start: {
          line: 222,
          column: 16
        },
        end: {
          line: 235,
          column: 17
        }
      },
      "150": {
        start: {
          line: 223,
          column: 28
        },
        end: {
          line: 223,
          column: 104
        }
      },
      "151": {
        start: {
          line: 225,
          column: 24
        },
        end: {
          line: 225,
          column: 44
        }
      },
      "152": {
        start: {
          line: 226,
          column: 24
        },
        end: {
          line: 234,
          column: 31
        }
      },
      "153": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 272,
          column: 6
        }
      },
      "154": {
        start: {
          line: 243,
          column: 20
        },
        end: {
          line: 243,
          column: 24
        }
      },
      "155": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 259,
          column: 9
        }
      },
      "156": {
        start: {
          line: 245,
          column: 28
        },
        end: {
          line: 245,
          column: 32
        }
      },
      "157": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 247,
          column: 78
        }
      },
      "158": {
        start: {
          line: 248,
          column: 12
        },
        end: {
          line: 248,
          column: 78
        }
      },
      "159": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 249,
          column: 63
        }
      },
      "160": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 250,
          column: 61
        }
      },
      "161": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 257,
          column: 41
        }
      },
      "162": {
        start: {
          line: 258,
          column: 12
        },
        end: {
          line: 258,
          column: 29
        }
      },
      "163": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "164": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 261,
          column: 91
        }
      },
      "165": {
        start: {
          line: 261,
          column: 46
        },
        end: {
          line: 261,
          column: 87
        }
      },
      "166": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 270,
          column: 9
        }
      },
      "167": {
        start: {
          line: 264,
          column: 28
        },
        end: {
          line: 264,
          column: 30
        }
      },
      "168": {
        start: {
          line: 265,
          column: 12
        },
        end: {
          line: 268,
          column: 13
        }
      },
      "169": {
        start: {
          line: 265,
          column: 26
        },
        end: {
          line: 265,
          column: 27
        }
      },
      "170": {
        start: {
          line: 265,
          column: 34
        },
        end: {
          line: 265,
          column: 54
        }
      },
      "171": {
        start: {
          line: 266,
          column: 25
        },
        end: {
          line: 266,
          column: 31
        }
      },
      "172": {
        start: {
          line: 266,
          column: 39
        },
        end: {
          line: 266,
          column: 44
        }
      },
      "173": {
        start: {
          line: 266,
          column: 54
        },
        end: {
          line: 266,
          column: 59
        }
      },
      "174": {
        start: {
          line: 267,
          column: 16
        },
        end: {
          line: 267,
          column: 67
        }
      },
      "175": {
        start: {
          line: 269,
          column: 12
        },
        end: {
          line: 269,
          column: 29
        }
      },
      "176": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 271,
          column: 20
        }
      },
      "177": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 288,
          column: 6
        }
      },
      "178": {
        start: {
          line: 277,
          column: 20
        },
        end: {
          line: 277,
          column: 24
        }
      },
      "179": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 279,
          column: 24
        }
      },
      "180": {
        start: {
          line: 279,
          column: 12
        },
        end: {
          line: 279,
          column: 24
        }
      },
      "181": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 281,
          column: 32
        }
      },
      "182": {
        start: {
          line: 281,
          column: 12
        },
        end: {
          line: 281,
          column: 32
        }
      },
      "183": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 283,
          column: 95
        }
      },
      "184": {
        start: {
          line: 283,
          column: 12
        },
        end: {
          line: 283,
          column: 95
        }
      },
      "185": {
        start: {
          line: 283,
          column: 46
        },
        end: {
          line: 283,
          column: 81
        }
      },
      "186": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "187": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 285,
          column: 112
        }
      },
      "188": {
        start: {
          line: 285,
          column: 62
        },
        end: {
          line: 285,
          column: 98
        }
      },
      "189": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 287,
          column: 18
        }
      },
      "190": {
        start: {
          line: 289,
          column: 4
        },
        end: {
          line: 297,
          column: 6
        }
      },
      "191": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 296,
          column: 9
        }
      },
      "192": {
        start: {
          line: 291,
          column: 29
        },
        end: {
          line: 291,
          column: 39
        }
      },
      "193": {
        start: {
          line: 292,
          column: 25
        },
        end: {
          line: 292,
          column: 35
        }
      },
      "194": {
        start: {
          line: 293,
          column: 27
        },
        end: {
          line: 293,
          column: 37
        }
      },
      "195": {
        start: {
          line: 294,
          column: 24
        },
        end: {
          line: 294,
          column: 33
        }
      },
      "196": {
        start: {
          line: 295,
          column: 21
        },
        end: {
          line: 295,
          column: 30
        }
      },
      "197": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 304,
          column: 6
        }
      },
      "198": {
        start: {
          line: 300,
          column: 8
        },
        end: {
          line: 303,
          column: 24
        }
      },
      "199": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 312,
          column: 6
        }
      },
      "200": {
        start: {
          line: 306,
          column: 33
        },
        end: {
          line: 310,
          column: 9
        }
      },
      "201": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 311,
          column: 95
        }
      },
      "202": {
        start: {
          line: 311,
          column: 60
        },
        end: {
          line: 311,
          column: 91
        }
      },
      "203": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 322,
          column: 6
        }
      },
      "204": {
        start: {
          line: 315,
          column: 23
        },
        end: {
          line: 315,
          column: 50
        }
      },
      "205": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 317,
          column: 24
        }
      },
      "206": {
        start: {
          line: 317,
          column: 12
        },
        end: {
          line: 317,
          column: 24
        }
      },
      "207": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 320,
          column: 24
        }
      },
      "208": {
        start: {
          line: 320,
          column: 12
        },
        end: {
          line: 320,
          column: 24
        }
      },
      "209": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 321,
          column: 21
        }
      },
      "210": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 330,
          column: 6
        }
      },
      "211": {
        start: {
          line: 324,
          column: 25
        },
        end: {
          line: 324,
          column: 51
        }
      },
      "212": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 328,
          column: 9
        }
      },
      "213": {
        start: {
          line: 327,
          column: 12
        },
        end: {
          line: 327,
          column: 24
        }
      },
      "214": {
        start: {
          line: 329,
          column: 8
        },
        end: {
          line: 329,
          column: 21
        }
      },
      "215": {
        start: {
          line: 331,
          column: 4
        },
        end: {
          line: 349,
          column: 6
        }
      },
      "216": {
        start: {
          line: 333,
          column: 31
        },
        end: {
          line: 333,
          column: 81
        }
      },
      "217": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 340,
          column: 9
        }
      },
      "218": {
        start: {
          line: 335,
          column: 12
        },
        end: {
          line: 339,
          column: 15
        }
      },
      "219": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 348,
          column: 9
        }
      },
      "220": {
        start: {
          line: 343,
          column: 12
        },
        end: {
          line: 347,
          column: 15
        }
      },
      "221": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 356,
          column: 6
        }
      },
      "222": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 365,
          column: 6
        }
      },
      "223": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 372,
          column: 6
        }
      },
      "224": {
        start: {
          line: 373,
          column: 4
        },
        end: {
          line: 380,
          column: 6
        }
      },
      "225": {
        start: {
          line: 381,
          column: 4
        },
        end: {
          line: 386,
          column: 6
        }
      },
      "226": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 387,
          column: 42
        }
      },
      "227": {
        start: {
          line: 389,
          column: 0
        },
        end: {
          line: 389,
          column: 72
        }
      },
      "228": {
        start: {
          line: 390,
          column: 0
        },
        end: {
          line: 390,
          column: 49
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 6,
            column: 45
          }
        },
        loc: {
          start: {
            line: 6,
            column: 89
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 7,
            column: 13
          },
          end: {
            line: 7,
            column: 18
          }
        },
        loc: {
          start: {
            line: 7,
            column: 26
          },
          end: {
            line: 7,
            column: 112
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 71
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 7,
            column: 108
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 63
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 8
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 26
          }
        },
        loc: {
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 99
          }
        },
        line: 9
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 33
          },
          end: {
            line: 10,
            column: 102
          }
        },
        line: 10
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 21
          }
        },
        loc: {
          start: {
            line: 11,
            column: 30
          },
          end: {
            line: 11,
            column: 118
          }
        },
        line: 11
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 49
          }
        },
        loc: {
          start: {
            line: 15,
            column: 73
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 15
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 16,
            column: 30
          },
          end: {
            line: 16,
            column: 31
          }
        },
        loc: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 83
          }
        },
        line: 16
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 17,
            column: 128
          },
          end: {
            line: 17,
            column: 129
          }
        },
        loc: {
          start: {
            line: 17,
            column: 139
          },
          end: {
            line: 17,
            column: 155
          }
        },
        line: 17
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 70
          }
        },
        line: 18
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 31
          }
        },
        loc: {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 67
          }
        },
        line: 18
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 19
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 53
          }
        },
        loc: {
          start: {
            line: 46,
            column: 64
          },
          end: {
            line: 388,
            column: 1
          }
        },
        line: 46
      },
      "14": {
        name: "ComprehensiveSecurityValidator",
        decl: {
          start: {
            line: 47,
            column: 13
          },
          end: {
            line: 47,
            column: 43
          }
        },
        loc: {
          start: {
            line: 47,
            column: 46
          },
          end: {
            line: 48,
            column: 5
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 52,
            column: 51
          },
          end: {
            line: 52,
            column: 52
          }
        },
        loc: {
          start: {
            line: 52,
            column: 84
          },
          end: {
            line: 112,
            column: 5
          }
        },
        line: 52
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 53,
            column: 48
          },
          end: {
            line: 53,
            column: 49
          }
        },
        loc: {
          start: {
            line: 53,
            column: 60
          },
          end: {
            line: 111,
            column: 9
          }
        },
        line: 53
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 56,
            column: 37
          },
          end: {
            line: 56,
            column: 38
          }
        },
        loc: {
          start: {
            line: 56,
            column: 51
          },
          end: {
            line: 110,
            column: 13
          }
        },
        line: 56
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 79,
            column: 64
          },
          end: {
            line: 79,
            column: 65
          }
        },
        loc: {
          start: {
            line: 79,
            column: 87
          },
          end: {
            line: 81,
            column: 29
          }
        },
        line: 79
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 84,
            column: 63
          },
          end: {
            line: 84,
            column: 64
          }
        },
        loc: {
          start: {
            line: 84,
            column: 86
          },
          end: {
            line: 86,
            column: 29
          }
        },
        line: 84
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 88,
            column: 70
          },
          end: {
            line: 88,
            column: 71
          }
        },
        loc: {
          start: {
            line: 88,
            column: 83
          },
          end: {
            line: 88,
            column: 120
          }
        },
        line: 88
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 116,
            column: 52
          },
          end: {
            line: 116,
            column: 53
          }
        },
        loc: {
          start: {
            line: 116,
            column: 77
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 116
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 186,
            column: 53
          },
          end: {
            line: 186,
            column: 54
          }
        },
        loc: {
          start: {
            line: 186,
            column: 78
          },
          end: {
            line: 213,
            column: 5
          }
        },
        line: 186
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 217,
            column: 58
          },
          end: {
            line: 217,
            column: 59
          }
        },
        loc: {
          start: {
            line: 217,
            column: 77
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 217
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 218,
            column: 48
          },
          end: {
            line: 218,
            column: 49
          }
        },
        loc: {
          start: {
            line: 218,
            column: 60
          },
          end: {
            line: 237,
            column: 9
          }
        },
        line: 218
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 221,
            column: 37
          },
          end: {
            line: 221,
            column: 38
          }
        },
        loc: {
          start: {
            line: 221,
            column: 51
          },
          end: {
            line: 236,
            column: 13
          }
        },
        line: 221
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 242,
            column: 50
          },
          end: {
            line: 242,
            column: 51
          }
        },
        loc: {
          start: {
            line: 242,
            column: 75
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 242
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 261,
            column: 28
          },
          end: {
            line: 261,
            column: 29
          }
        },
        loc: {
          start: {
            line: 261,
            column: 44
          },
          end: {
            line: 261,
            column: 89
          }
        },
        line: 261
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 276,
            column: 53
          },
          end: {
            line: 276,
            column: 54
          }
        },
        loc: {
          start: {
            line: 276,
            column: 69
          },
          end: {
            line: 288,
            column: 5
          }
        },
        line: 276
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 283,
            column: 28
          },
          end: {
            line: 283,
            column: 29
          }
        },
        loc: {
          start: {
            line: 283,
            column: 44
          },
          end: {
            line: 283,
            column: 83
          }
        },
        line: 283
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 285,
            column: 43
          },
          end: {
            line: 285,
            column: 44
          }
        },
        loc: {
          start: {
            line: 285,
            column: 60
          },
          end: {
            line: 285,
            column: 100
          }
        },
        line: 285
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 289,
            column: 52
          },
          end: {
            line: 289,
            column: 53
          }
        },
        loc: {
          start: {
            line: 289,
            column: 72
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 289
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 298,
            column: 49
          },
          end: {
            line: 298,
            column: 50
          }
        },
        loc: {
          start: {
            line: 298,
            column: 68
          },
          end: {
            line: 304,
            column: 5
          }
        },
        line: 298
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 305,
            column: 59
          },
          end: {
            line: 305,
            column: 60
          }
        },
        loc: {
          start: {
            line: 305,
            column: 80
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 305
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 311,
            column: 39
          },
          end: {
            line: 311,
            column: 40
          }
        },
        loc: {
          start: {
            line: 311,
            column: 58
          },
          end: {
            line: 311,
            column: 93
          }
        },
        line: 311
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 313,
            column: 61
          },
          end: {
            line: 313,
            column: 62
          }
        },
        loc: {
          start: {
            line: 313,
            column: 86
          },
          end: {
            line: 322,
            column: 5
          }
        },
        line: 313
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 323,
            column: 66
          },
          end: {
            line: 323,
            column: 67
          }
        },
        loc: {
          start: {
            line: 323,
            column: 91
          },
          end: {
            line: 330,
            column: 5
          }
        },
        line: 323
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 331,
            column: 55
          },
          end: {
            line: 331,
            column: 56
          }
        },
        loc: {
          start: {
            line: 331,
            column: 86
          },
          end: {
            line: 349,
            column: 5
          }
        },
        line: 331
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 21
          }
        }, {
          start: {
            line: 6,
            column: 25
          },
          end: {
            line: 6,
            column: 39
          }
        }, {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 56
          },
          end: {
            line: 7,
            column: 61
          }
        }, {
          start: {
            line: 7,
            column: 64
          },
          end: {
            line: 7,
            column: 109
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 17
          }
        }, {
          start: {
            line: 8,
            column: 22
          },
          end: {
            line: 8,
            column: 33
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 11,
            column: 32
          },
          end: {
            line: 11,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 11,
            column: 46
          },
          end: {
            line: 11,
            column: 67
          }
        }, {
          start: {
            line: 11,
            column: 70
          },
          end: {
            line: 11,
            column: 115
          }
        }],
        line: 11
      },
      "4": {
        loc: {
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 61
          }
        }, {
          start: {
            line: 12,
            column: 65
          },
          end: {
            line: 12,
            column: 67
          }
        }],
        line: 12
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 41,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 23
          }
        }, {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 43
          }
        }, {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 41,
            column: 1
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "7": {
        loc: {
          start: {
            line: 16,
            column: 134
          },
          end: {
            line: 16,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 167
          },
          end: {
            line: 16,
            column: 175
          }
        }, {
          start: {
            line: 16,
            column: 178
          },
          end: {
            line: 16,
            column: 184
          }
        }],
        line: 16
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 102
          }
        }, {
          start: {
            line: 17,
            column: 107
          },
          end: {
            line: 17,
            column: 155
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "10": {
        loc: {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 16
          }
        }, {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 43
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 125
          }
        }, {
          start: {
            line: 22,
            column: 130
          },
          end: {
            line: 22,
            column: 158
          }
        }],
        line: 22
      },
      "14": {
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 22,
            column: 56
          }
        }, {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "15": {
        loc: {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        }, {
          start: {
            line: 22,
            column: 119
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "16": {
        loc: {
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 77
          }
        }, {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "17": {
        loc: {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 83
          },
          end: {
            line: 22,
            column: 98
          }
        }, {
          start: {
            line: 22,
            column: 103
          },
          end: {
            line: 22,
            column: 112
          }
        }],
        line: 22
      },
      "18": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "19": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 23
          }
        }, {
          start: {
            line: 25,
            column: 24
          },
          end: {
            line: 25,
            column: 46
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 72
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 65
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 24
      },
      "20": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 74
          }
        }, {
          start: {
            line: 30,
            column: 79
          },
          end: {
            line: 30,
            column: 90
          }
        }, {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 30,
            column: 105
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 73
          }
        }],
        line: 30
      },
      "23": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "24": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 35
          }
        }, {
          start: {
            line: 31,
            column: 40
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: 31,
            column: 47
          },
          end: {
            line: 31,
            column: 59
          }
        }, {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 31,
            column: 75
          }
        }],
        line: 31
      },
      "25": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "26": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 39
          },
          end: {
            line: 32,
            column: 53
          }
        }],
        line: 32
      },
      "27": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "28": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 25
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "29": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "30": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "31": {
        loc: {
          start: {
            line: 39,
            column: 52
          },
          end: {
            line: 39,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 60
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 68
          },
          end: {
            line: 39,
            column: 74
          }
        }],
        line: 39
      },
      "32": {
        loc: {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 109,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 58,
            column: 20
          },
          end: {
            line: 58,
            column: 85
          }
        }, {
          start: {
            line: 59,
            column: 20
          },
          end: {
            line: 108,
            column: 46
          }
        }],
        line: 57
      },
      "33": {
        loc: {
          start: {
            line: 65,
            column: 28
          },
          end: {
            line: 76,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 65,
            column: 28
          },
          end: {
            line: 76,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 65
      },
      "34": {
        loc: {
          start: {
            line: 67,
            column: 32
          },
          end: {
            line: 75,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 32
          },
          end: {
            line: 75,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "35": {
        loc: {
          start: {
            line: 88,
            column: 38
          },
          end: {
            line: 88,
            column: 121
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 88,
            column: 38
          },
          end: {
            line: 88,
            column: 52
          }
        }, {
          start: {
            line: 88,
            column: 56
          },
          end: {
            line: 88,
            column: 121
          }
        }],
        line: 88
      },
      "36": {
        loc: {
          start: {
            line: 92,
            column: 51
          },
          end: {
            line: 92,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 92,
            column: 61
          },
          end: {
            line: 92,
            column: 74
          }
        }, {
          start: {
            line: 92,
            column: 77
          },
          end: {
            line: 92,
            column: 86
          }
        }],
        line: 92
      },
      "37": {
        loc: {
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 129,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 129,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "38": {
        loc: {
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "39": {
        loc: {
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 153,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 153,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 146
      },
      "40": {
        loc: {
          start: {
            line: 158,
            column: 12
          },
          end: {
            line: 165,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 12
          },
          end: {
            line: 165,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "41": {
        loc: {
          start: {
            line: 170,
            column: 12
          },
          end: {
            line: 177,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 12
          },
          end: {
            line: 177,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "42": {
        loc: {
          start: {
            line: 189,
            column: 8
          },
          end: {
            line: 195,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 8
          },
          end: {
            line: 195,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "43": {
        loc: {
          start: {
            line: 197,
            column: 8
          },
          end: {
            line: 203,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 8
          },
          end: {
            line: 203,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "44": {
        loc: {
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "45": {
        loc: {
          start: {
            line: 222,
            column: 16
          },
          end: {
            line: 235,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 223,
            column: 20
          },
          end: {
            line: 223,
            column: 104
          }
        }, {
          start: {
            line: 224,
            column: 20
          },
          end: {
            line: 234,
            column: 31
          }
        }],
        line: 222
      },
      "46": {
        loc: {
          start: {
            line: 227,
            column: 52
          },
          end: {
            line: 227,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 227,
            column: 93
          },
          end: {
            line: 227,
            column: 99
          }
        }, {
          start: {
            line: 227,
            column: 102
          },
          end: {
            line: 227,
            column: 114
          }
        }],
        line: 227
      },
      "47": {
        loc: {
          start: {
            line: 227,
            column: 52
          },
          end: {
            line: 227,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 52
          },
          end: {
            line: 227,
            column: 68
          }
        }, {
          start: {
            line: 227,
            column: 72
          },
          end: {
            line: 227,
            column: 90
          }
        }],
        line: 227
      },
      "48": {
        loc: {
          start: {
            line: 228,
            column: 40
          },
          end: {
            line: 228,
            column: 167
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 41
          },
          end: {
            line: 228,
            column: 153
          }
        }, {
          start: {
            line: 228,
            column: 158
          },
          end: {
            line: 228,
            column: 167
          }
        }],
        line: 228
      },
      "49": {
        loc: {
          start: {
            line: 228,
            column: 41
          },
          end: {
            line: 228,
            column: 153
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 228,
            column: 139
          },
          end: {
            line: 228,
            column: 145
          }
        }, {
          start: {
            line: 228,
            column: 148
          },
          end: {
            line: 228,
            column: 153
          }
        }],
        line: 228
      },
      "50": {
        loc: {
          start: {
            line: 228,
            column: 41
          },
          end: {
            line: 228,
            column: 136
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 41
          },
          end: {
            line: 228,
            column: 119
          }
        }, {
          start: {
            line: 228,
            column: 123
          },
          end: {
            line: 228,
            column: 136
          }
        }],
        line: 228
      },
      "51": {
        loc: {
          start: {
            line: 228,
            column: 47
          },
          end: {
            line: 228,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 228,
            column: 88
          },
          end: {
            line: 228,
            column: 94
          }
        }, {
          start: {
            line: 228,
            column: 97
          },
          end: {
            line: 228,
            column: 109
          }
        }],
        line: 228
      },
      "52": {
        loc: {
          start: {
            line: 228,
            column: 47
          },
          end: {
            line: 228,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 47
          },
          end: {
            line: 228,
            column: 63
          }
        }, {
          start: {
            line: 228,
            column: 67
          },
          end: {
            line: 228,
            column: 85
          }
        }],
        line: 228
      },
      "53": {
        loc: {
          start: {
            line: 229,
            column: 42
          },
          end: {
            line: 229,
            column: 168
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 43
          },
          end: {
            line: 229,
            column: 157
          }
        }, {
          start: {
            line: 229,
            column: 162
          },
          end: {
            line: 229,
            column: 168
          }
        }],
        line: 229
      },
      "54": {
        loc: {
          start: {
            line: 229,
            column: 43
          },
          end: {
            line: 229,
            column: 157
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 229,
            column: 141
          },
          end: {
            line: 229,
            column: 147
          }
        }, {
          start: {
            line: 229,
            column: 150
          },
          end: {
            line: 229,
            column: 157
          }
        }],
        line: 229
      },
      "55": {
        loc: {
          start: {
            line: 229,
            column: 43
          },
          end: {
            line: 229,
            column: 138
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 43
          },
          end: {
            line: 229,
            column: 121
          }
        }, {
          start: {
            line: 229,
            column: 125
          },
          end: {
            line: 229,
            column: 138
          }
        }],
        line: 229
      },
      "56": {
        loc: {
          start: {
            line: 229,
            column: 49
          },
          end: {
            line: 229,
            column: 111
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 229,
            column: 90
          },
          end: {
            line: 229,
            column: 96
          }
        }, {
          start: {
            line: 229,
            column: 99
          },
          end: {
            line: 229,
            column: 111
          }
        }],
        line: 229
      },
      "57": {
        loc: {
          start: {
            line: 229,
            column: 49
          },
          end: {
            line: 229,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 49
          },
          end: {
            line: 229,
            column: 65
          }
        }, {
          start: {
            line: 229,
            column: 69
          },
          end: {
            line: 229,
            column: 87
          }
        }],
        line: 229
      },
      "58": {
        loc: {
          start: {
            line: 231,
            column: 43
          },
          end: {
            line: 231,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 43
          },
          end: {
            line: 231,
            column: 76
          }
        }, {
          start: {
            line: 231,
            column: 80
          },
          end: {
            line: 231,
            column: 89
          }
        }],
        line: 231
      },
      "59": {
        loc: {
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 259,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 259,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 244
      },
      "60": {
        loc: {
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 260
      },
      "61": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 270,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 270,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "62": {
        loc: {
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 263,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 263,
            column: 36
          }
        }, {
          start: {
            line: 263,
            column: 40
          },
          end: {
            line: 263,
            column: 53
          }
        }],
        line: 263
      },
      "63": {
        loc: {
          start: {
            line: 278,
            column: 8
          },
          end: {
            line: 279,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 8
          },
          end: {
            line: 279,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 278
      },
      "64": {
        loc: {
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 281,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 281,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 280
      },
      "65": {
        loc: {
          start: {
            line: 280,
            column: 12
          },
          end: {
            line: 280,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 280,
            column: 12
          },
          end: {
            line: 280,
            column: 36
          }
        }, {
          start: {
            line: 280,
            column: 40
          },
          end: {
            line: 280,
            column: 65
          }
        }],
        line: 280
      },
      "66": {
        loc: {
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 283,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 283,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "67": {
        loc: {
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "68": {
        loc: {
          start: {
            line: 284,
            column: 12
          },
          end: {
            line: 284,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 284,
            column: 12
          },
          end: {
            line: 284,
            column: 36
          }
        }, {
          start: {
            line: 284,
            column: 40
          },
          end: {
            line: 284,
            column: 53
          }
        }],
        line: 284
      },
      "69": {
        loc: {
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 296,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 291,
            column: 12
          },
          end: {
            line: 291,
            column: 39
          }
        }, {
          start: {
            line: 292,
            column: 12
          },
          end: {
            line: 292,
            column: 35
          }
        }, {
          start: {
            line: 293,
            column: 12
          },
          end: {
            line: 293,
            column: 37
          }
        }, {
          start: {
            line: 294,
            column: 12
          },
          end: {
            line: 294,
            column: 33
          }
        }, {
          start: {
            line: 295,
            column: 12
          },
          end: {
            line: 295,
            column: 30
          }
        }],
        line: 290
      },
      "70": {
        loc: {
          start: {
            line: 300,
            column: 15
          },
          end: {
            line: 303,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 300,
            column: 122
          }
        }, {
          start: {
            line: 301,
            column: 12
          },
          end: {
            line: 301,
            column: 44
          }
        }, {
          start: {
            line: 302,
            column: 12
          },
          end: {
            line: 302,
            column: 51
          }
        }, {
          start: {
            line: 303,
            column: 12
          },
          end: {
            line: 303,
            column: 23
          }
        }],
        line: 300
      },
      "71": {
        loc: {
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 300,
            column: 122
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 300,
            column: 90
          },
          end: {
            line: 300,
            column: 96
          }
        }, {
          start: {
            line: 300,
            column: 99
          },
          end: {
            line: 300,
            column: 122
          }
        }],
        line: 300
      },
      "72": {
        loc: {
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 300,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 300,
            column: 70
          }
        }, {
          start: {
            line: 300,
            column: 74
          },
          end: {
            line: 300,
            column: 87
          }
        }],
        line: 300
      },
      "73": {
        loc: {
          start: {
            line: 316,
            column: 8
          },
          end: {
            line: 317,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 316,
            column: 8
          },
          end: {
            line: 317,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 316
      },
      "74": {
        loc: {
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 320,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 320,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "75": {
        loc: {
          start: {
            line: 319,
            column: 12
          },
          end: {
            line: 319,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 12
          },
          end: {
            line: 319,
            column: 46
          }
        }, {
          start: {
            line: 319,
            column: 50
          },
          end: {
            line: 319,
            column: 85
          }
        }],
        line: 319
      },
      "76": {
        loc: {
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 328,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 328,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 326
      },
      "77": {
        loc: {
          start: {
            line: 326,
            column: 12
          },
          end: {
            line: 326,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 326,
            column: 12
          },
          end: {
            line: 326,
            column: 40
          }
        }, {
          start: {
            line: 326,
            column: 44
          },
          end: {
            line: 326,
            column: 94
          }
        }],
        line: 326
      },
      "78": {
        loc: {
          start: {
            line: 333,
            column: 32
          },
          end: {
            line: 333,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 333,
            column: 32
          },
          end: {
            line: 333,
            column: 67
          }
        }, {
          start: {
            line: 333,
            column: 71
          },
          end: {
            line: 333,
            column: 73
          }
        }],
        line: 333
      },
      "79": {
        loc: {
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 340,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 340,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 334
      },
      "80": {
        loc: {
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 348,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 348,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "81": {
        loc: {
          start: {
            line: 342,
            column: 12
          },
          end: {
            line: 342,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 342,
            column: 12
          },
          end: {
            line: 342,
            column: 49
          }
        }, {
          start: {
            line: 342,
            column: 53
          },
          end: {
            line: 342,
            column: 75
          }
        }],
        line: 342
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0, 0, 0, 0],
      "70": [0, 0, 0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/comprehensive-security-validator.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,uCAA6C;AAC7C,+BAAqC;AA2BrC;IAAA;IAqWA,CAAC;IA1TC;;OAEG;IACU,4CAAa,GAA1B,UACE,OAAoB,EACpB,IAAS,EACT,MAAoB;uCACnB,OAAO;;;;;4BACQ,qBAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAA;;wBAAlD,OAAO,GAAG,SAAwC;wBAClD,OAAO,GAAqB,EAAE,CAAC;wBACjC,SAAS,GAAG,CAAC,CAAC;wBAElB,IAAI,CAAC;4BACH,mCAAmC;4BACnC,IAAI,MAAM,EAAE,CAAC;gCACL,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gCAC5C,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;oCAC1B,OAAO,CAAC,IAAI,CAAC;wCACX,IAAI,EAAE,mBAAmB;wCACzB,QAAQ,EAAE,QAAQ;wCAClB,WAAW,EAAE,sCAAsC;wCACnD,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,OAAO;qCACpC,CAAC,CAAC;oCACH,SAAS,IAAI,EAAE,CAAC;gCAClB,CAAC;4BACH,CAAC;4BAGK,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BAC3D,OAAO,CAAC,IAAI,OAAZ,OAAO,EAAS,eAAe,EAAE;4BACjC,SAAS,IAAI,eAAe,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,MAAM;gCAC9C,OAAO,GAAG,GAAG,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;4BACpD,CAAC,EAAE,CAAC,CAAC,CAAC;4BAGA,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;4BAC3D,OAAO,CAAC,IAAI,OAAZ,OAAO,EAAS,cAAc,EAAE;4BAChC,SAAS,IAAI,cAAc,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,MAAM;gCAC7C,OAAO,GAAG,GAAG,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;4BACpD,CAAC,EAAE,CAAC,CAAC,CAAC;4BAGA,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BAEjD,OAAO,GAAG,SAAS,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,UAAU,EAAzB,CAAyB,CAAC,CAAC;4BAEhF,sBAAO;oCACL,OAAO,SAAA;oCACP,OAAO,SAAA;oCACP,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;oCAClD,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC;iCACpC,EAAC;wBAEJ,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;4BACnD,sBAAO;oCACL,OAAO,EAAE,KAAK;oCACd,OAAO,EAAE,CAAC;4CACR,IAAI,EAAE,kBAAkB;4CACxB,QAAQ,EAAE,MAAM;4CAChB,WAAW,EAAE,kDAAkD;yCAChE,CAAC;oCACF,SAAS,EAAE,GAAG;iCACf,EAAC;wBACJ,CAAC;;;;;KACF;IAED;;OAEG;IACY,6CAAc,GAA7B,UAA8B,IAAS,EAAE,OAAwB;QAC/D,IAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,IAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE9C,0BAA0B;QAC1B,KAAsB,UAA2B,EAA3B,KAAA,IAAI,CAAC,sBAAsB,EAA3B,cAA2B,EAA3B,IAA2B,EAAE,CAAC;YAA/C,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,UAAU;oBACpB,WAAW,EAAE,0CAA0C;oBACvD,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,KAAsB,UAAiB,EAAjB,KAAA,IAAI,CAAC,YAAY,EAAjB,cAAiB,EAAjB,IAAiB,EAAE,CAAC;YAArC,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,KAAK;oBACX,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,+BAA+B;oBAC5C,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,KAAsB,UAA+B,EAA/B,KAAA,IAAI,CAAC,0BAA0B,EAA/B,cAA+B,EAA/B,IAA+B,EAAE,CAAC;YAAnD,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,mBAAmB;oBACzB,QAAQ,EAAE,UAAU;oBACpB,WAAW,EAAE,sCAAsC;oBACnD,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,KAAsB,UAA4B,EAA5B,KAAA,IAAI,CAAC,uBAAuB,EAA5B,cAA4B,EAA5B,IAA4B,EAAE,CAAC;YAAhD,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,gBAAgB;oBACtB,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,0CAA0C;oBACvD,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,KAAsB,UAA4B,EAA5B,KAAA,IAAI,CAAC,uBAAuB,EAA5B,cAA4B,EAA5B,IAA4B,EAAE,CAAC;YAAhD,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,gBAAgB;oBACtB,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,mCAAmC;oBAChD,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAE5C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACY,8CAAe,GAA9B,UAA+B,OAAwB,EAAE,IAAS;QAChE,IAAM,OAAO,GAAqB,EAAE,CAAC;QAErC,mCAAmC;QACnC,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,uBAAuB;gBAC7B,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,gCAAgC;aAC9C,CAAC,CAAC;QACL,CAAC;QAED,qCAAqC;QACrC,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,kCAAkC;aAChD,CAAC,CAAC;QACL,CAAC;QAED,0CAA0C;QAC1C,IAAI,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,sBAAsB;gBAC5B,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,wCAAwC;aACtD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACkB,mDAAoB,GAAzC,UAA0C,OAAoB;uCAAG,OAAO;;;;;4BACtD,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBAEnD,sBAAO;gCACL,eAAe,EAAE,CAAC,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAA;gCAChC,MAAM,EAAE,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,KAAI,SAAS;gCACtC,QAAQ,EAAE,CAAA,MAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAY,0CAAE,IAAI,KAAI,MAAM;gCAChD,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gCACpC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS;gCACzD,WAAW,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;gCAC1C,MAAM,EAAE,OAAO,CAAC,MAAM;6BACvB,EAAC;;;;KACH;IAED;;OAEG;IACY,2CAAY,GAA3B,UAA4B,IAAS,EAAE,OAAyB;QAAhE,iBAkCC;QAjCC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,SAAS,GAAG,IAAI,CAAC;YAErB,uBAAuB;YACvB,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAAC;YAClE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAAC;YAClE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;YACnD,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YAEjD,uBAAuB;YACvB,SAAS,GAAG,SAAS;iBAClB,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;iBACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;iBACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;iBACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;iBACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAE3B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAAhC,CAAgC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9C,IAAM,SAAS,GAAQ,EAAE,CAAC;YAC1B,KAA2B,UAAoB,EAApB,KAAA,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAApB,cAAoB,EAApB,IAAoB,EAAE,CAAC;gBAAvC,IAAA,WAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;gBACpB,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACY,8CAAe,GAA9B,UAA+B,IAAS;QAAxC,iBAQC;QAPC,IAAI,OAAO,IAAI,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAC;QAC1C,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QAC/E,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAA1B,CAA0B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvF,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9C,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAA3B,CAA2B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEc,6CAAc,GAA7B,UAA8B,QAAgB;QAC5C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;YAC3B,KAAK,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YACvB,KAAK,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;YACzB,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAEc,0CAAW,GAA1B,UAA2B,OAAoB;;QAC7C,OAAO,CAAA,MAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,0CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE;YAC5D,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;YAChC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;YACvC,WAAW,CAAC;IACrB,CAAC;IAEc,oDAAqB,GAApC,UAAqC,SAAiB;QACpD,IAAM,kBAAkB,GAAG;YACzB,6BAA6B;YAC7B,gCAAgC;YAChC,4BAA4B;SAC7B,CAAC;QAEF,OAAO,kBAAkB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAvB,CAAuB,CAAC,CAAC;IACrE,CAAC;IAEc,sDAAuB,GAAtC,UAAuC,OAAwB,EAAE,IAAS;QACxE,qCAAqC;QACrC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QAC7C,IAAI,QAAQ,GAAG,MAAM;YAAE,OAAO,IAAI,CAAC,CAAC,cAAc;QAElD,0BAA0B;QAC1B,IAAI,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE3F,OAAO,KAAK,CAAC;IACf,CAAC;IAEc,2DAA4B,GAA3C,UAA4C,OAAwB,EAAE,IAAS;QAC7E,IAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE9C,yDAAyD;QACzD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,iCAAiC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACvF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEc,gDAAiB,GAAhC,UAAiC,UAAkB,EAAE,OAAyB;QAC5E,yCAAyC;QACzC,IAAM,gBAAgB,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAC5E,IAAI,gBAAgB,GAAG,UAAU,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,yBAAyB;gBAC/B,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,uCAAuC;aACrD,CAAC,CAAC;QACL,CAAC;QAED,uDAAuD;QACvD,IAAI,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACpE,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,2CAA2C;aACzD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAnWuB,qDAAsB,GAAG;QAC/C,mEAAmE;QACnE,uBAAuB;QACvB,sBAAsB;QACtB,6CAA6C;QAC7C,4BAA4B;KAC7B,CAAC;IAEsB,2CAAY,GAAG;QACrC,8BAA8B;QAC9B,8BAA8B;QAC9B,eAAe;QACf,aAAa;QACb,uCAAuC;QACvC,aAAa;QACb,mBAAmB;KACpB,CAAC;IAEsB,yDAA0B,GAAG;QACnD,gBAAgB;QAChB,wFAAwF;QACxF,QAAQ;QACR,8CAA8C;QAC9C,kCAAkC;KACnC,CAAC;IAEsB,sDAAuB,GAAG;QAChD,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,UAAU;KACX,CAAC;IAEsB,sDAAuB,GAAG;QAChD,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,OAAO;KACR,CAAC;IA4TJ,qCAAC;CAAA,AArWD,IAqWC;AArWY,wEAA8B;AAuW3C,kBAAe,8BAA8B,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/comprehensive-security-validator.ts"],
      sourcesContent: ["/**\n * Comprehensive Security Validator\n * Provides enhanced input validation, threat detection, and security checks\n */\n\nimport { NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from './auth';\nimport { z } from 'zod';\n\ninterface SecurityThreat {\n  type: string;\n  severity: 'low' | 'medium' | 'high' | 'critical';\n  description: string;\n  pattern?: string;\n}\n\ninterface ValidationResult {\n  isValid: boolean;\n  threats: SecurityThreat[];\n  sanitizedData?: any;\n  riskScore: number;\n}\n\ninterface SecurityContext {\n  isAuthenticated: boolean;\n  userId?: string;\n  userRole?: string;\n  ipAddress: string;\n  userAgent: string;\n  requestPath: string;\n  method: string;\n}\n\nexport class ComprehensiveSecurityValidator {\n  private static readonly SQL_INJECTION_PATTERNS = [\n    /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\\b)/i,\n    /(--|\\/\\*|\\*\\/|;|\\||&)/,\n    /(\\b(OR|AND)\\b.*=.*)/i,\n    /(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)/i,\n    /(WAITFOR|DELAY|BENCHMARK)/i\n  ];\n\n  private static readonly XSS_PATTERNS = [\n    /<script[^>]*>.*?<\\/script>/gi,\n    /<iframe[^>]*>.*?<\\/iframe>/gi,\n    /javascript:/gi,\n    /on\\w+\\s*=/gi,\n    /<img[^>]*src\\s*=\\s*[\"']?javascript:/gi,\n    /eval\\s*\\(/gi,\n    /expression\\s*\\(/gi\n  ];\n\n  private static readonly COMMAND_INJECTION_PATTERNS = [\n    /[;&|`$(){}[\\]]/,\n    /\\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh|ftp)\\b/i,\n    /\\.\\.\\//,\n    /\\/etc\\/passwd|\\/etc\\/shadow|\\/proc\\/|\\/sys\\//,\n    /cmd\\.exe|powershell|bash|sh|zsh/i\n  ];\n\n  private static readonly PATH_TRAVERSAL_PATTERNS = [\n    /\\.\\.\\//,\n    /\\.\\.\\\\/,\n    /%2e%2e%2f/i,\n    /%2e%2e%5c/i,\n    /\\.\\.%2f/i,\n    /\\.\\.%5c/i\n  ];\n\n  private static readonly LDAP_INJECTION_PATTERNS = [\n    /[()&|!]/,\n    /\\*.*\\*/,\n    /\\(\\|\\(/,\n    /\\)&\\(/\n  ];\n\n  /**\n   * Comprehensive input validation with threat detection\n   */\n  static async validateInput(\n    request: NextRequest,\n    data: any,\n    schema?: z.ZodSchema\n  ): Promise<ValidationResult> {\n    const context = await this.buildSecurityContext(request);\n    const threats: SecurityThreat[] = [];\n    let riskScore = 0;\n\n    try {\n      // 1. Schema validation if provided\n      if (schema) {\n        const schemaResult = schema.safeParse(data);\n        if (!schemaResult.success) {\n          threats.push({\n            type: 'schema_validation',\n            severity: 'medium',\n            description: 'Input does not match expected schema',\n            pattern: schemaResult.error.message\n          });\n          riskScore += 30;\n        }\n      }\n\n      // 2. Deep security scanning\n      const securityThreats = this.scanForThreats(data, context);\n      threats.push(...securityThreats);\n      riskScore += securityThreats.reduce((sum, threat) => {\n        return sum + this.getThreatScore(threat.severity);\n      }, 0);\n\n      // 3. Context-based validation\n      const contextThreats = this.validateContext(context, data);\n      threats.push(...contextThreats);\n      riskScore += contextThreats.reduce((sum, threat) => {\n        return sum + this.getThreatScore(threat.severity);\n      }, 0);\n\n      // 4. Sanitize data if validation passes\n      const sanitizedData = this.sanitizeData(data, threats);\n\n      const isValid = riskScore < 50 && !threats.some(t => t.severity === 'critical');\n\n      return {\n        isValid,\n        threats,\n        sanitizedData: isValid ? sanitizedData : undefined,\n        riskScore: Math.min(riskScore, 100)\n      };\n\n    } catch (error) {\n      console.error('Security validation error:', error);\n      return {\n        isValid: false,\n        threats: [{\n          type: 'validation_error',\n          severity: 'high',\n          description: 'Security validation failed due to internal error'\n        }],\n        riskScore: 100\n      };\n    }\n  }\n\n  /**\n   * Scan for security threats in data\n   */\n  private static scanForThreats(data: any, context: SecurityContext): SecurityThreat[] {\n    const threats: SecurityThreat[] = [];\n    const dataString = this.flattenToString(data);\n\n    // SQL Injection detection\n    for (const pattern of this.SQL_INJECTION_PATTERNS) {\n      if (pattern.test(dataString)) {\n        threats.push({\n          type: 'sql_injection',\n          severity: 'critical',\n          description: 'Potential SQL injection attempt detected',\n          pattern: pattern.toString()\n        });\n      }\n    }\n\n    // XSS detection\n    for (const pattern of this.XSS_PATTERNS) {\n      if (pattern.test(dataString)) {\n        threats.push({\n          type: 'xss',\n          severity: 'high',\n          description: 'Potential XSS attack detected',\n          pattern: pattern.toString()\n        });\n      }\n    }\n\n    // Command injection detection\n    for (const pattern of this.COMMAND_INJECTION_PATTERNS) {\n      if (pattern.test(dataString)) {\n        threats.push({\n          type: 'command_injection',\n          severity: 'critical',\n          description: 'Potential command injection detected',\n          pattern: pattern.toString()\n        });\n      }\n    }\n\n    // Path traversal detection\n    for (const pattern of this.PATH_TRAVERSAL_PATTERNS) {\n      if (pattern.test(dataString)) {\n        threats.push({\n          type: 'path_traversal',\n          severity: 'high',\n          description: 'Potential path traversal attack detected',\n          pattern: pattern.toString()\n        });\n      }\n    }\n\n    // LDAP injection detection\n    for (const pattern of this.LDAP_INJECTION_PATTERNS) {\n      if (pattern.test(dataString)) {\n        threats.push({\n          type: 'ldap_injection',\n          severity: 'high',\n          description: 'Potential LDAP injection detected',\n          pattern: pattern.toString()\n        });\n      }\n    }\n\n    // Additional checks\n    this.checkForAnomalies(dataString, threats);\n\n    return threats;\n  }\n\n  /**\n   * Validate security context\n   */\n  private static validateContext(context: SecurityContext, data: any): SecurityThreat[] {\n    const threats: SecurityThreat[] = [];\n\n    // Check for suspicious user agents\n    if (this.isSuspiciousUserAgent(context.userAgent)) {\n      threats.push({\n        type: 'suspicious_user_agent',\n        severity: 'medium',\n        description: 'Suspicious user agent detected'\n      });\n    }\n\n    // Check for unusual request patterns\n    if (this.isUnusualRequestPattern(context, data)) {\n      threats.push({\n        type: 'unusual_pattern',\n        severity: 'medium',\n        description: 'Unusual request pattern detected'\n      });\n    }\n\n    // Check for privilege escalation attempts\n    if (this.isPrivilegeEscalationAttempt(context, data)) {\n      threats.push({\n        type: 'privilege_escalation',\n        severity: 'critical',\n        description: 'Potential privilege escalation attempt'\n      });\n    }\n\n    return threats;\n  }\n\n  /**\n   * Build security context from request\n   */\n  private static async buildSecurityContext(request: NextRequest): Promise<SecurityContext> {\n    const session = await getServerSession(authOptions);\n    \n    return {\n      isAuthenticated: !!session?.user,\n      userId: session?.user?.id || undefined,\n      userRole: (session?.user as any)?.role || 'user',\n      ipAddress: this.getClientIP(request),\n      userAgent: request.headers.get('user-agent') || 'unknown',\n      requestPath: new URL(request.url).pathname,\n      method: request.method\n    };\n  }\n\n  /**\n   * Sanitize data based on detected threats\n   */\n  private static sanitizeData(data: any, threats: SecurityThreat[]): any {\n    if (typeof data === 'string') {\n      let sanitized = data;\n      \n      // Remove potential XSS\n      sanitized = sanitized.replace(/<script[^>]*>.*?<\\/script>/gi, '');\n      sanitized = sanitized.replace(/<iframe[^>]*>.*?<\\/iframe>/gi, '');\n      sanitized = sanitized.replace(/javascript:/gi, '');\n      sanitized = sanitized.replace(/on\\w+\\s*=/gi, '');\n      \n      // Escape HTML entities\n      sanitized = sanitized\n        .replace(/&/g, '&amp;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\"/g, '&quot;')\n        .replace(/'/g, '&#x27;');\n      \n      return sanitized;\n    }\n    \n    if (Array.isArray(data)) {\n      return data.map(item => this.sanitizeData(item, threats));\n    }\n    \n    if (typeof data === 'object' && data !== null) {\n      const sanitized: any = {};\n      for (const [key, value] of Object.entries(data)) {\n        sanitized[key] = this.sanitizeData(value, threats);\n      }\n      return sanitized;\n    }\n    \n    return data;\n  }\n\n  /**\n   * Helper methods\n   */\n  private static flattenToString(data: any): string {\n    if (typeof data === 'string') return data;\n    if (typeof data === 'number' || typeof data === 'boolean') return String(data);\n    if (Array.isArray(data)) return data.map(item => this.flattenToString(item)).join(' ');\n    if (typeof data === 'object' && data !== null) {\n      return Object.values(data).map(value => this.flattenToString(value)).join(' ');\n    }\n    return '';\n  }\n\n  private static getThreatScore(severity: string): number {\n    switch (severity) {\n      case 'critical': return 50;\n      case 'high': return 30;\n      case 'medium': return 15;\n      case 'low': return 5;\n      default: return 0;\n    }\n  }\n\n  private static getClientIP(request: NextRequest): string {\n    return request.headers.get('x-forwarded-for')?.split(',')[0].trim() ||\n           request.headers.get('x-real-ip') ||\n           request.headers.get('cf-connecting-ip') ||\n           '127.0.0.1';\n  }\n\n  private static isSuspiciousUserAgent(userAgent: string): boolean {\n    const suspiciousPatterns = [\n      /bot|crawler|spider|scraper/i,\n      /curl|wget|python|java|go-http/i,\n      /sqlmap|nikto|nmap|masscan/i\n    ];\n    \n    return suspiciousPatterns.some(pattern => pattern.test(userAgent));\n  }\n\n  private static isUnusualRequestPattern(context: SecurityContext, data: any): boolean {\n    // Check for unusually large payloads\n    const dataSize = JSON.stringify(data).length;\n    if (dataSize > 100000) return true; // 100KB limit\n    \n    // Check for unusual paths\n    if (context.requestPath.includes('..') || context.requestPath.includes('%2e')) return true;\n    \n    return false;\n  }\n\n  private static isPrivilegeEscalationAttempt(context: SecurityContext, data: any): boolean {\n    const dataString = this.flattenToString(data);\n    \n    // Check for admin-related keywords in non-admin contexts\n    if (context.userRole !== 'admin' && /admin|root|superuser|privilege/i.test(dataString)) {\n      return true;\n    }\n    \n    return false;\n  }\n\n  private static checkForAnomalies(dataString: string, threats: SecurityThreat[]): void {\n    // Check for excessive special characters\n    const specialCharCount = (dataString.match(/[^a-zA-Z0-9\\s]/g) || []).length;\n    if (specialCharCount > dataString.length * 0.3) {\n      threats.push({\n        type: 'excessive_special_chars',\n        severity: 'medium',\n        description: 'Excessive special characters detected'\n      });\n    }\n    \n    // Check for base64 encoded content (potential payload)\n    if (/^[A-Za-z0-9+/]+=*$/.test(dataString) && dataString.length > 50) {\n      threats.push({\n        type: 'base64_payload',\n        severity: 'medium',\n        description: 'Potential base64 encoded payload detected'\n      });\n    }\n  }\n}\n\nexport default ComprehensiveSecurityValidator;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "924c67a5a8850fdd15a04d529f6b34864ea757e2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_vsgvdem0f = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_vsgvdem0f();
var __awaiter =
/* istanbul ignore next */
(cov_vsgvdem0f().s[0]++,
/* istanbul ignore next */
(cov_vsgvdem0f().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_vsgvdem0f().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_vsgvdem0f().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_vsgvdem0f().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[1]++;
    cov_vsgvdem0f().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[2]++;
      cov_vsgvdem0f().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_vsgvdem0f().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_vsgvdem0f().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_vsgvdem0f().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[4]++;
      cov_vsgvdem0f().s[4]++;
      try {
        /* istanbul ignore next */
        cov_vsgvdem0f().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_vsgvdem0f().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[5]++;
      cov_vsgvdem0f().s[7]++;
      try {
        /* istanbul ignore next */
        cov_vsgvdem0f().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_vsgvdem0f().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[6]++;
      cov_vsgvdem0f().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_vsgvdem0f().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_vsgvdem0f().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_vsgvdem0f().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_vsgvdem0f().s[12]++,
/* istanbul ignore next */
(cov_vsgvdem0f().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_vsgvdem0f().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_vsgvdem0f().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_vsgvdem0f().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_vsgvdem0f().f[8]++;
        cov_vsgvdem0f().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_vsgvdem0f().b[6][0]++;
          cov_vsgvdem0f().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_vsgvdem0f().b[6][1]++;
        }
        cov_vsgvdem0f().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_vsgvdem0f().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_vsgvdem0f().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_vsgvdem0f().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[9]++;
    cov_vsgvdem0f().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[10]++;
    cov_vsgvdem0f().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[11]++;
      cov_vsgvdem0f().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[12]++;
    cov_vsgvdem0f().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[9][0]++;
      cov_vsgvdem0f().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[9][1]++;
    }
    cov_vsgvdem0f().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_vsgvdem0f().s[25]++;
      try {
        /* istanbul ignore next */
        cov_vsgvdem0f().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[15][0]++,
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[16][1]++,
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_vsgvdem0f().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_vsgvdem0f().b[12][0]++;
          cov_vsgvdem0f().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_vsgvdem0f().b[12][1]++;
        }
        cov_vsgvdem0f().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_vsgvdem0f().b[18][0]++;
          cov_vsgvdem0f().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_vsgvdem0f().b[18][1]++;
        }
        cov_vsgvdem0f().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[19][1]++;
            cov_vsgvdem0f().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_vsgvdem0f().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[19][2]++;
            cov_vsgvdem0f().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_vsgvdem0f().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[19][3]++;
            cov_vsgvdem0f().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_vsgvdem0f().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_vsgvdem0f().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_vsgvdem0f().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[19][4]++;
            cov_vsgvdem0f().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_vsgvdem0f().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_vsgvdem0f().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[19][5]++;
            cov_vsgvdem0f().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_vsgvdem0f().b[20][0]++;
              cov_vsgvdem0f().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_vsgvdem0f().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_vsgvdem0f().b[20][1]++;
            }
            cov_vsgvdem0f().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_vsgvdem0f().b[23][0]++;
              cov_vsgvdem0f().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_vsgvdem0f().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_vsgvdem0f().b[23][1]++;
            }
            cov_vsgvdem0f().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_vsgvdem0f().b[25][0]++;
              cov_vsgvdem0f().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_vsgvdem0f().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_vsgvdem0f().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_vsgvdem0f().b[25][1]++;
            }
            cov_vsgvdem0f().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_vsgvdem0f().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_vsgvdem0f().b[27][0]++;
              cov_vsgvdem0f().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_vsgvdem0f().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_vsgvdem0f().b[27][1]++;
            }
            cov_vsgvdem0f().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_vsgvdem0f().b[29][0]++;
              cov_vsgvdem0f().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_vsgvdem0f().b[29][1]++;
            }
            cov_vsgvdem0f().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_vsgvdem0f().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_vsgvdem0f().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_vsgvdem0f().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_vsgvdem0f().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_vsgvdem0f().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_vsgvdem0f().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[30][0]++;
      cov_vsgvdem0f().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[30][1]++;
    }
    cov_vsgvdem0f().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_vsgvdem0f().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_vsgvdem0f().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_vsgvdem0f().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_vsgvdem0f().s[68]++;
exports.ComprehensiveSecurityValidator = void 0;
var next_auth_1 =
/* istanbul ignore next */
(cov_vsgvdem0f().s[69]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_vsgvdem0f().s[70]++, require("./auth"));
var ComprehensiveSecurityValidator =
/* istanbul ignore next */
(/** @class */cov_vsgvdem0f().s[71]++, function () {
  /* istanbul ignore next */
  cov_vsgvdem0f().f[13]++;
  function ComprehensiveSecurityValidator() {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[14]++;
  }
  /**
   * Comprehensive input validation with threat detection
   */
  /* istanbul ignore next */
  cov_vsgvdem0f().s[72]++;
  ComprehensiveSecurityValidator.validateInput = function (request, data, schema) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[15]++;
    cov_vsgvdem0f().s[73]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[16]++;
      var context, threats, riskScore, schemaResult, securityThreats, contextThreats, sanitizedData, isValid;
      var _this =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[74]++, this);
      /* istanbul ignore next */
      cov_vsgvdem0f().s[75]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_vsgvdem0f().f[17]++;
        cov_vsgvdem0f().s[76]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[32][0]++;
            cov_vsgvdem0f().s[77]++;
            return [4 /*yield*/, this.buildSecurityContext(request)];
          case 1:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[32][1]++;
            cov_vsgvdem0f().s[78]++;
            context = _a.sent();
            /* istanbul ignore next */
            cov_vsgvdem0f().s[79]++;
            threats = [];
            /* istanbul ignore next */
            cov_vsgvdem0f().s[80]++;
            riskScore = 0;
            /* istanbul ignore next */
            cov_vsgvdem0f().s[81]++;
            try {
              /* istanbul ignore next */
              cov_vsgvdem0f().s[82]++;
              // 1. Schema validation if provided
              if (schema) {
                /* istanbul ignore next */
                cov_vsgvdem0f().b[33][0]++;
                cov_vsgvdem0f().s[83]++;
                schemaResult = schema.safeParse(data);
                /* istanbul ignore next */
                cov_vsgvdem0f().s[84]++;
                if (!schemaResult.success) {
                  /* istanbul ignore next */
                  cov_vsgvdem0f().b[34][0]++;
                  cov_vsgvdem0f().s[85]++;
                  threats.push({
                    type: 'schema_validation',
                    severity: 'medium',
                    description: 'Input does not match expected schema',
                    pattern: schemaResult.error.message
                  });
                  /* istanbul ignore next */
                  cov_vsgvdem0f().s[86]++;
                  riskScore += 30;
                } else
                /* istanbul ignore next */
                {
                  cov_vsgvdem0f().b[34][1]++;
                }
              } else
              /* istanbul ignore next */
              {
                cov_vsgvdem0f().b[33][1]++;
              }
              cov_vsgvdem0f().s[87]++;
              securityThreats = this.scanForThreats(data, context);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[88]++;
              threats.push.apply(threats, securityThreats);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[89]++;
              riskScore += securityThreats.reduce(function (sum, threat) {
                /* istanbul ignore next */
                cov_vsgvdem0f().f[18]++;
                cov_vsgvdem0f().s[90]++;
                return sum + _this.getThreatScore(threat.severity);
              }, 0);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[91]++;
              contextThreats = this.validateContext(context, data);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[92]++;
              threats.push.apply(threats, contextThreats);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[93]++;
              riskScore += contextThreats.reduce(function (sum, threat) {
                /* istanbul ignore next */
                cov_vsgvdem0f().f[19]++;
                cov_vsgvdem0f().s[94]++;
                return sum + _this.getThreatScore(threat.severity);
              }, 0);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[95]++;
              sanitizedData = this.sanitizeData(data, threats);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[96]++;
              isValid =
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[35][0]++, riskScore < 50) &&
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[35][1]++, !threats.some(function (t) {
                /* istanbul ignore next */
                cov_vsgvdem0f().f[20]++;
                cov_vsgvdem0f().s[97]++;
                return t.severity === 'critical';
              }));
              /* istanbul ignore next */
              cov_vsgvdem0f().s[98]++;
              return [2 /*return*/, {
                isValid: isValid,
                threats: threats,
                sanitizedData: isValid ?
                /* istanbul ignore next */
                (cov_vsgvdem0f().b[36][0]++, sanitizedData) :
                /* istanbul ignore next */
                (cov_vsgvdem0f().b[36][1]++, undefined),
                riskScore: Math.min(riskScore, 100)
              }];
            } catch (error) {
              /* istanbul ignore next */
              cov_vsgvdem0f().s[99]++;
              console.error('Security validation error:', error);
              /* istanbul ignore next */
              cov_vsgvdem0f().s[100]++;
              return [2 /*return*/, {
                isValid: false,
                threats: [{
                  type: 'validation_error',
                  severity: 'high',
                  description: 'Security validation failed due to internal error'
                }],
                riskScore: 100
              }];
            }
            /* istanbul ignore next */
            cov_vsgvdem0f().s[101]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Scan for security threats in data
   */
  /* istanbul ignore next */
  cov_vsgvdem0f().s[102]++;
  ComprehensiveSecurityValidator.scanForThreats = function (data, context) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[21]++;
    var threats =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[103]++, []);
    var dataString =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[104]++, this.flattenToString(data));
    // SQL Injection detection
    /* istanbul ignore next */
    cov_vsgvdem0f().s[105]++;
    for (var _i =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[106]++, 0), _a =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[107]++, this.SQL_INJECTION_PATTERNS); _i < _a.length; _i++) {
      var pattern =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[108]++, _a[_i]);
      /* istanbul ignore next */
      cov_vsgvdem0f().s[109]++;
      if (pattern.test(dataString)) {
        /* istanbul ignore next */
        cov_vsgvdem0f().b[37][0]++;
        cov_vsgvdem0f().s[110]++;
        threats.push({
          type: 'sql_injection',
          severity: 'critical',
          description: 'Potential SQL injection attempt detected',
          pattern: pattern.toString()
        });
      } else
      /* istanbul ignore next */
      {
        cov_vsgvdem0f().b[37][1]++;
      }
    }
    // XSS detection
    /* istanbul ignore next */
    cov_vsgvdem0f().s[111]++;
    for (var _b =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[112]++, 0), _c =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[113]++, this.XSS_PATTERNS); _b < _c.length; _b++) {
      var pattern =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[114]++, _c[_b]);
      /* istanbul ignore next */
      cov_vsgvdem0f().s[115]++;
      if (pattern.test(dataString)) {
        /* istanbul ignore next */
        cov_vsgvdem0f().b[38][0]++;
        cov_vsgvdem0f().s[116]++;
        threats.push({
          type: 'xss',
          severity: 'high',
          description: 'Potential XSS attack detected',
          pattern: pattern.toString()
        });
      } else
      /* istanbul ignore next */
      {
        cov_vsgvdem0f().b[38][1]++;
      }
    }
    // Command injection detection
    /* istanbul ignore next */
    cov_vsgvdem0f().s[117]++;
    for (var _d =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[118]++, 0), _e =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[119]++, this.COMMAND_INJECTION_PATTERNS); _d < _e.length; _d++) {
      var pattern =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[120]++, _e[_d]);
      /* istanbul ignore next */
      cov_vsgvdem0f().s[121]++;
      if (pattern.test(dataString)) {
        /* istanbul ignore next */
        cov_vsgvdem0f().b[39][0]++;
        cov_vsgvdem0f().s[122]++;
        threats.push({
          type: 'command_injection',
          severity: 'critical',
          description: 'Potential command injection detected',
          pattern: pattern.toString()
        });
      } else
      /* istanbul ignore next */
      {
        cov_vsgvdem0f().b[39][1]++;
      }
    }
    // Path traversal detection
    /* istanbul ignore next */
    cov_vsgvdem0f().s[123]++;
    for (var _f =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[124]++, 0), _g =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[125]++, this.PATH_TRAVERSAL_PATTERNS); _f < _g.length; _f++) {
      var pattern =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[126]++, _g[_f]);
      /* istanbul ignore next */
      cov_vsgvdem0f().s[127]++;
      if (pattern.test(dataString)) {
        /* istanbul ignore next */
        cov_vsgvdem0f().b[40][0]++;
        cov_vsgvdem0f().s[128]++;
        threats.push({
          type: 'path_traversal',
          severity: 'high',
          description: 'Potential path traversal attack detected',
          pattern: pattern.toString()
        });
      } else
      /* istanbul ignore next */
      {
        cov_vsgvdem0f().b[40][1]++;
      }
    }
    // LDAP injection detection
    /* istanbul ignore next */
    cov_vsgvdem0f().s[129]++;
    for (var _h =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[130]++, 0), _j =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[131]++, this.LDAP_INJECTION_PATTERNS); _h < _j.length; _h++) {
      var pattern =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[132]++, _j[_h]);
      /* istanbul ignore next */
      cov_vsgvdem0f().s[133]++;
      if (pattern.test(dataString)) {
        /* istanbul ignore next */
        cov_vsgvdem0f().b[41][0]++;
        cov_vsgvdem0f().s[134]++;
        threats.push({
          type: 'ldap_injection',
          severity: 'high',
          description: 'Potential LDAP injection detected',
          pattern: pattern.toString()
        });
      } else
      /* istanbul ignore next */
      {
        cov_vsgvdem0f().b[41][1]++;
      }
    }
    // Additional checks
    /* istanbul ignore next */
    cov_vsgvdem0f().s[135]++;
    this.checkForAnomalies(dataString, threats);
    /* istanbul ignore next */
    cov_vsgvdem0f().s[136]++;
    return threats;
  };
  /**
   * Validate security context
   */
  /* istanbul ignore next */
  cov_vsgvdem0f().s[137]++;
  ComprehensiveSecurityValidator.validateContext = function (context, data) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[22]++;
    var threats =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[138]++, []);
    // Check for suspicious user agents
    /* istanbul ignore next */
    cov_vsgvdem0f().s[139]++;
    if (this.isSuspiciousUserAgent(context.userAgent)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[42][0]++;
      cov_vsgvdem0f().s[140]++;
      threats.push({
        type: 'suspicious_user_agent',
        severity: 'medium',
        description: 'Suspicious user agent detected'
      });
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[42][1]++;
    }
    // Check for unusual request patterns
    cov_vsgvdem0f().s[141]++;
    if (this.isUnusualRequestPattern(context, data)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[43][0]++;
      cov_vsgvdem0f().s[142]++;
      threats.push({
        type: 'unusual_pattern',
        severity: 'medium',
        description: 'Unusual request pattern detected'
      });
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[43][1]++;
    }
    // Check for privilege escalation attempts
    cov_vsgvdem0f().s[143]++;
    if (this.isPrivilegeEscalationAttempt(context, data)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[44][0]++;
      cov_vsgvdem0f().s[144]++;
      threats.push({
        type: 'privilege_escalation',
        severity: 'critical',
        description: 'Potential privilege escalation attempt'
      });
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[44][1]++;
    }
    cov_vsgvdem0f().s[145]++;
    return threats;
  };
  /**
   * Build security context from request
   */
  /* istanbul ignore next */
  cov_vsgvdem0f().s[146]++;
  ComprehensiveSecurityValidator.buildSecurityContext = function (request) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[23]++;
    cov_vsgvdem0f().s[147]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[24]++;
      var session;
      var _a, _b;
      /* istanbul ignore next */
      cov_vsgvdem0f().s[148]++;
      return __generator(this, function (_c) {
        /* istanbul ignore next */
        cov_vsgvdem0f().f[25]++;
        cov_vsgvdem0f().s[149]++;
        switch (_c.label) {
          case 0:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[45][0]++;
            cov_vsgvdem0f().s[150]++;
            return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
          case 1:
            /* istanbul ignore next */
            cov_vsgvdem0f().b[45][1]++;
            cov_vsgvdem0f().s[151]++;
            session = _c.sent();
            /* istanbul ignore next */
            cov_vsgvdem0f().s[152]++;
            return [2 /*return*/, {
              isAuthenticated: !!(
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[47][0]++, session === null) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[47][1]++, session === void 0) ?
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[46][0]++, void 0) :
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[46][1]++, session.user)),
              userId:
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[48][0]++,
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[50][0]++, (_a =
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[52][0]++, session === null) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[52][1]++, session === void 0) ?
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[51][0]++, void 0) :
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[51][1]++, session.user)) === null) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[50][1]++, _a === void 0) ?
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[49][0]++, void 0) :
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[49][1]++, _a.id)) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[48][1]++, undefined),
              userRole:
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[53][0]++,
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[55][0]++, (_b =
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[57][0]++, session === null) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[57][1]++, session === void 0) ?
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[56][0]++, void 0) :
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[56][1]++, session.user)) === null) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[55][1]++, _b === void 0) ?
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[54][0]++, void 0) :
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[54][1]++, _b.role)) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[53][1]++, 'user'),
              ipAddress: this.getClientIP(request),
              userAgent:
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[58][0]++, request.headers.get('user-agent')) ||
              /* istanbul ignore next */
              (cov_vsgvdem0f().b[58][1]++, 'unknown'),
              requestPath: new URL(request.url).pathname,
              method: request.method
            }];
        }
      });
    });
  };
  /**
   * Sanitize data based on detected threats
   */
  /* istanbul ignore next */
  cov_vsgvdem0f().s[153]++;
  ComprehensiveSecurityValidator.sanitizeData = function (data, threats) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[26]++;
    var _this =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[154]++, this);
    /* istanbul ignore next */
    cov_vsgvdem0f().s[155]++;
    if (typeof data === 'string') {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[59][0]++;
      var sanitized =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[156]++, data);
      // Remove potential XSS
      /* istanbul ignore next */
      cov_vsgvdem0f().s[157]++;
      sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
      /* istanbul ignore next */
      cov_vsgvdem0f().s[158]++;
      sanitized = sanitized.replace(/<iframe[^>]*>.*?<\/iframe>/gi, '');
      /* istanbul ignore next */
      cov_vsgvdem0f().s[159]++;
      sanitized = sanitized.replace(/javascript:/gi, '');
      /* istanbul ignore next */
      cov_vsgvdem0f().s[160]++;
      sanitized = sanitized.replace(/on\w+\s*=/gi, '');
      // Escape HTML entities
      /* istanbul ignore next */
      cov_vsgvdem0f().s[161]++;
      sanitized = sanitized.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;');
      /* istanbul ignore next */
      cov_vsgvdem0f().s[162]++;
      return sanitized;
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[59][1]++;
    }
    cov_vsgvdem0f().s[163]++;
    if (Array.isArray(data)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[60][0]++;
      cov_vsgvdem0f().s[164]++;
      return data.map(function (item) {
        /* istanbul ignore next */
        cov_vsgvdem0f().f[27]++;
        cov_vsgvdem0f().s[165]++;
        return _this.sanitizeData(item, threats);
      });
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[60][1]++;
    }
    cov_vsgvdem0f().s[166]++;
    if (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[62][0]++, typeof data === 'object') &&
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[62][1]++, data !== null)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[61][0]++;
      var sanitized =
      /* istanbul ignore next */
      (cov_vsgvdem0f().s[167]++, {});
      /* istanbul ignore next */
      cov_vsgvdem0f().s[168]++;
      for (var _i =
        /* istanbul ignore next */
        (cov_vsgvdem0f().s[169]++, 0), _a =
        /* istanbul ignore next */
        (cov_vsgvdem0f().s[170]++, Object.entries(data)); _i < _a.length; _i++) {
        var _b =
          /* istanbul ignore next */
          (cov_vsgvdem0f().s[171]++, _a[_i]),
          key =
          /* istanbul ignore next */
          (cov_vsgvdem0f().s[172]++, _b[0]),
          value =
          /* istanbul ignore next */
          (cov_vsgvdem0f().s[173]++, _b[1]);
        /* istanbul ignore next */
        cov_vsgvdem0f().s[174]++;
        sanitized[key] = this.sanitizeData(value, threats);
      }
      /* istanbul ignore next */
      cov_vsgvdem0f().s[175]++;
      return sanitized;
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[61][1]++;
    }
    cov_vsgvdem0f().s[176]++;
    return data;
  };
  /**
   * Helper methods
   */
  /* istanbul ignore next */
  cov_vsgvdem0f().s[177]++;
  ComprehensiveSecurityValidator.flattenToString = function (data) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[28]++;
    var _this =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[178]++, this);
    /* istanbul ignore next */
    cov_vsgvdem0f().s[179]++;
    if (typeof data === 'string') {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[63][0]++;
      cov_vsgvdem0f().s[180]++;
      return data;
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[63][1]++;
    }
    cov_vsgvdem0f().s[181]++;
    if (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[65][0]++, typeof data === 'number') ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[65][1]++, typeof data === 'boolean')) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[64][0]++;
      cov_vsgvdem0f().s[182]++;
      return String(data);
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[64][1]++;
    }
    cov_vsgvdem0f().s[183]++;
    if (Array.isArray(data)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[66][0]++;
      cov_vsgvdem0f().s[184]++;
      return data.map(function (item) {
        /* istanbul ignore next */
        cov_vsgvdem0f().f[29]++;
        cov_vsgvdem0f().s[185]++;
        return _this.flattenToString(item);
      }).join(' ');
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[66][1]++;
    }
    cov_vsgvdem0f().s[186]++;
    if (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[68][0]++, typeof data === 'object') &&
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[68][1]++, data !== null)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[67][0]++;
      cov_vsgvdem0f().s[187]++;
      return Object.values(data).map(function (value) {
        /* istanbul ignore next */
        cov_vsgvdem0f().f[30]++;
        cov_vsgvdem0f().s[188]++;
        return _this.flattenToString(value);
      }).join(' ');
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[67][1]++;
    }
    cov_vsgvdem0f().s[189]++;
    return '';
  };
  /* istanbul ignore next */
  cov_vsgvdem0f().s[190]++;
  ComprehensiveSecurityValidator.getThreatScore = function (severity) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[31]++;
    cov_vsgvdem0f().s[191]++;
    switch (severity) {
      case 'critical':
        /* istanbul ignore next */
        cov_vsgvdem0f().b[69][0]++;
        cov_vsgvdem0f().s[192]++;
        return 50;
      case 'high':
        /* istanbul ignore next */
        cov_vsgvdem0f().b[69][1]++;
        cov_vsgvdem0f().s[193]++;
        return 30;
      case 'medium':
        /* istanbul ignore next */
        cov_vsgvdem0f().b[69][2]++;
        cov_vsgvdem0f().s[194]++;
        return 15;
      case 'low':
        /* istanbul ignore next */
        cov_vsgvdem0f().b[69][3]++;
        cov_vsgvdem0f().s[195]++;
        return 5;
      default:
        /* istanbul ignore next */
        cov_vsgvdem0f().b[69][4]++;
        cov_vsgvdem0f().s[196]++;
        return 0;
    }
  };
  /* istanbul ignore next */
  cov_vsgvdem0f().s[197]++;
  ComprehensiveSecurityValidator.getClientIP = function (request) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[32]++;
    var _a;
    /* istanbul ignore next */
    cov_vsgvdem0f().s[198]++;
    return /* istanbul ignore next */(cov_vsgvdem0f().b[70][0]++,
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[72][0]++, (_a = request.headers.get('x-forwarded-for')) === null) ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[72][1]++, _a === void 0) ?
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[71][0]++, void 0) :
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[71][1]++, _a.split(',')[0].trim())) ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[70][1]++, request.headers.get('x-real-ip')) ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[70][2]++, request.headers.get('cf-connecting-ip')) ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[70][3]++, '127.0.0.1');
  };
  /* istanbul ignore next */
  cov_vsgvdem0f().s[199]++;
  ComprehensiveSecurityValidator.isSuspiciousUserAgent = function (userAgent) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[33]++;
    var suspiciousPatterns =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[200]++, [/bot|crawler|spider|scraper/i, /curl|wget|python|java|go-http/i, /sqlmap|nikto|nmap|masscan/i]);
    /* istanbul ignore next */
    cov_vsgvdem0f().s[201]++;
    return suspiciousPatterns.some(function (pattern) {
      /* istanbul ignore next */
      cov_vsgvdem0f().f[34]++;
      cov_vsgvdem0f().s[202]++;
      return pattern.test(userAgent);
    });
  };
  /* istanbul ignore next */
  cov_vsgvdem0f().s[203]++;
  ComprehensiveSecurityValidator.isUnusualRequestPattern = function (context, data) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[35]++;
    // Check for unusually large payloads
    var dataSize =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[204]++, JSON.stringify(data).length);
    /* istanbul ignore next */
    cov_vsgvdem0f().s[205]++;
    if (dataSize > 100000) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[73][0]++;
      cov_vsgvdem0f().s[206]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[73][1]++;
    } // 100KB limit
    // Check for unusual paths
    cov_vsgvdem0f().s[207]++;
    if (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[75][0]++, context.requestPath.includes('..')) ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[75][1]++, context.requestPath.includes('%2e'))) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[74][0]++;
      cov_vsgvdem0f().s[208]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[74][1]++;
    }
    cov_vsgvdem0f().s[209]++;
    return false;
  };
  /* istanbul ignore next */
  cov_vsgvdem0f().s[210]++;
  ComprehensiveSecurityValidator.isPrivilegeEscalationAttempt = function (context, data) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[36]++;
    var dataString =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[211]++, this.flattenToString(data));
    // Check for admin-related keywords in non-admin contexts
    /* istanbul ignore next */
    cov_vsgvdem0f().s[212]++;
    if (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[77][0]++, context.userRole !== 'admin') &&
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[77][1]++, /admin|root|superuser|privilege/i.test(dataString))) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[76][0]++;
      cov_vsgvdem0f().s[213]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[76][1]++;
    }
    cov_vsgvdem0f().s[214]++;
    return false;
  };
  /* istanbul ignore next */
  cov_vsgvdem0f().s[215]++;
  ComprehensiveSecurityValidator.checkForAnomalies = function (dataString, threats) {
    /* istanbul ignore next */
    cov_vsgvdem0f().f[37]++;
    // Check for excessive special characters
    var specialCharCount =
    /* istanbul ignore next */
    (cov_vsgvdem0f().s[216]++, (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[78][0]++, dataString.match(/[^a-zA-Z0-9\s]/g)) ||
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[78][1]++, [])).length);
    /* istanbul ignore next */
    cov_vsgvdem0f().s[217]++;
    if (specialCharCount > dataString.length * 0.3) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[79][0]++;
      cov_vsgvdem0f().s[218]++;
      threats.push({
        type: 'excessive_special_chars',
        severity: 'medium',
        description: 'Excessive special characters detected'
      });
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[79][1]++;
    }
    // Check for base64 encoded content (potential payload)
    cov_vsgvdem0f().s[219]++;
    if (
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[81][0]++, /^[A-Za-z0-9+/]+=*$/.test(dataString)) &&
    /* istanbul ignore next */
    (cov_vsgvdem0f().b[81][1]++, dataString.length > 50)) {
      /* istanbul ignore next */
      cov_vsgvdem0f().b[80][0]++;
      cov_vsgvdem0f().s[220]++;
      threats.push({
        type: 'base64_payload',
        severity: 'medium',
        description: 'Potential base64 encoded payload detected'
      });
    } else
    /* istanbul ignore next */
    {
      cov_vsgvdem0f().b[80][1]++;
    }
  };
  /* istanbul ignore next */
  cov_vsgvdem0f().s[221]++;
  ComprehensiveSecurityValidator.SQL_INJECTION_PATTERNS = [/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i, /(--|\/\*|\*\/|;|\||&)/, /(\b(OR|AND)\b.*=.*)/i, /(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)/i, /(WAITFOR|DELAY|BENCHMARK)/i];
  /* istanbul ignore next */
  cov_vsgvdem0f().s[222]++;
  ComprehensiveSecurityValidator.XSS_PATTERNS = [/<script[^>]*>.*?<\/script>/gi, /<iframe[^>]*>.*?<\/iframe>/gi, /javascript:/gi, /on\w+\s*=/gi, /<img[^>]*src\s*=\s*["']?javascript:/gi, /eval\s*\(/gi, /expression\s*\(/gi];
  /* istanbul ignore next */
  cov_vsgvdem0f().s[223]++;
  ComprehensiveSecurityValidator.COMMAND_INJECTION_PATTERNS = [/[;&|`$(){}[\]]/, /\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh|ftp)\b/i, /\.\.\//, /\/etc\/passwd|\/etc\/shadow|\/proc\/|\/sys\//, /cmd\.exe|powershell|bash|sh|zsh/i];
  /* istanbul ignore next */
  cov_vsgvdem0f().s[224]++;
  ComprehensiveSecurityValidator.PATH_TRAVERSAL_PATTERNS = [/\.\.\//, /\.\.\\/, /%2e%2e%2f/i, /%2e%2e%5c/i, /\.\.%2f/i, /\.\.%5c/i];
  /* istanbul ignore next */
  cov_vsgvdem0f().s[225]++;
  ComprehensiveSecurityValidator.LDAP_INJECTION_PATTERNS = [/[()&|!]/, /\*.*\*/, /\(\|\(/, /\)&\(/];
  /* istanbul ignore next */
  cov_vsgvdem0f().s[226]++;
  return ComprehensiveSecurityValidator;
}());
/* istanbul ignore next */
cov_vsgvdem0f().s[227]++;
exports.ComprehensiveSecurityValidator = ComprehensiveSecurityValidator;
/* istanbul ignore next */
cov_vsgvdem0f().s[228]++;
exports.default = ComprehensiveSecurityValidator;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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