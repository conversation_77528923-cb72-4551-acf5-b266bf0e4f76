{"version": 3, "names": ["server_1", "cov_1mhwbdq9s", "s", "require", "next_1", "auth_1", "unified_api_error_handler_1", "prisma_1", "__importDefault", "assessmentScoring_1", "suggestionService_1", "logger_1", "exports", "GET", "withUnifiedErrorHandling", "request", "context", "f", "__awaiter", "Promise", "params", "_b", "sent", "getServerSession", "authOptions", "session", "b", "_a", "user", "id", "error", "Error", "statusCode", "assessmentId", "default", "assessment", "findUnique", "where", "userId", "include", "responses", "status", "responseData", "for<PERSON>ach", "response", "<PERSON><PERSON><PERSON>", "answerValue", "generateAssessmentInsights", "insights", "getCareerPathSuggestions", "careerSuggestions", "enhancedCareerSuggestions", "map", "suggestion", "__assign", "matchReason", "generateMatchReason", "getPersonalizedRecommendations", "personalizedRecommendations", "results", "completedAt", "currentStep", "NextResponse", "json", "success", "data", "reasons", "topSkills", "some", "skill", "careerPath", "name", "toLowerCase", "includes", "push", "primaryMotivation", "overview", "score", "length", "join", "userSkillProgress", "find<PERSON>any", "userSkills", "careerPathIds", "learningResource", "careerPaths", "in", "isActive", "ratings", "select", "rating", "take", "orderBy", "createdAt", "learningResources", "enhancedResources", "resource", "averageRating", "reduce", "sum", "r", "totalRatings", "sort", "a", "skillGaps", "generateSkillGaps", "nextSteps", "generateNextSteps", "log", "error_1", "component", "action", "recommendedSkills", "currentSkills", "us", "filter", "index", "priority", "currentLevel", "Math", "floor", "random", "targetLevel", "timeEstimates", "high", "medium", "low", "reason", "estimatedTime", "steps", "scores", "readinessScore", "skillsConfidence", "concat", "biggestObstacles"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/results/[id]/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport prisma from '@/lib/prisma';\nimport { generateAssessmentInsights, AssessmentResponse } from '@/lib/assessmentScoring';\nimport { getCareerPathSuggestions } from '@/lib/suggestionService';\nimport { log } from '@/lib/logger';\n\ninterface AssessmentResultsResponse {\n  assessment: {\n    id: string;\n    status: string;\n    completedAt: Date | null;\n    currentStep: number;\n  };\n  insights: {\n    scores: {\n      readinessScore: number;\n      riskTolerance: number;\n      urgencyLevel: number;\n      skillsConfidence: number;\n      supportLevel: number;\n      financialReadiness: number;\n    };\n    primaryMotivation: string;\n    topSkills: string[];\n    biggestObstacles: string[];\n    recommendedTimeline: string;\n    keyRecommendations: string[];\n    careerPathSuggestions: string[];\n  };\n  careerSuggestions: Array<{\n    careerPath: {\n      id: string;\n      name: string;\n      slug: string;\n      overview: string;\n      pros: string;\n      cons: string;\n      actionableSteps: any;\n    };\n    score: number;\n    matchReason?: string;\n  }>;\n  personalizedRecommendations?: {\n    learningResources: any[];\n    skillGaps: any[];\n    nextSteps: string[];\n  };\n}\n\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  context: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<AssessmentResultsResponse>>> => {\n  const params = await context.params;\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const assessmentId = params.id;\n\n  // Fetch assessment with responses\n  const assessment = await prisma.assessment.findUnique({\n    where: {\n      id: assessmentId,\n      userId: session.user.id // Ensure user can only access their own assessments\n    },\n    include: {\n      responses: true\n    }\n  });\n\n  if (!assessment) {\n    const error = new Error('Assessment not found') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  if (assessment.status !== 'COMPLETED') {\n    const error = new Error('Assessment not completed') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Convert responses to the format expected by scoring functions\n  const responseData: AssessmentResponse = {};\n  assessment.responses.forEach(response => {\n    responseData[response.questionKey] = response.answerValue as any;\n  });\n\n  // Generate insights from assessment responses\n  const insights = await generateAssessmentInsights(responseData);\n\n  // Get career path suggestions\n  const careerSuggestions = await getCareerPathSuggestions(assessmentId);\n\n  // Enhance career suggestions with match reasoning\n  const enhancedCareerSuggestions = careerSuggestions.map(suggestion => ({\n    ...suggestion,\n    matchReason: generateMatchReason(suggestion, insights)\n  }));\n\n  // Get personalized learning recommendations (if available)\n  const personalizedRecommendations = await getPersonalizedRecommendations(\n    session.user.id,\n    insights,\n    enhancedCareerSuggestions\n  );\n\n  const results: AssessmentResultsResponse = {\n    assessment: {\n      id: assessment.id,\n      status: assessment.status,\n      completedAt: assessment.completedAt,\n      currentStep: assessment.currentStep\n    },\n    insights,\n    careerSuggestions: enhancedCareerSuggestions,\n    personalizedRecommendations\n  };\n\n  return NextResponse.json({\n    success: true as const,\n    data: results\n  });\n});\n\nfunction generateMatchReason(suggestion: any, insights: any): string {\n  const reasons = [];\n  \n  if (insights.topSkills.some((skill: string) => \n    suggestion.careerPath.name.toLowerCase().includes(skill.toLowerCase())\n  )) {\n    reasons.push('Matches your key skills');\n  }\n  \n  if (insights.primaryMotivation && \n      suggestion.careerPath.overview.toLowerCase().includes(insights.primaryMotivation.toLowerCase())) {\n    reasons.push('Aligns with your motivation');\n  }\n  \n  if (suggestion.score > 5) {\n    reasons.push('High compatibility score');\n  }\n  \n  return reasons.length > 0 ? reasons.join(', ') : 'Based on your assessment responses';\n}\n\nasync function getPersonalizedRecommendations(\n  userId: string,\n  insights: any,\n  careerSuggestions: any[]\n): Promise<any> {\n  try {\n    // Get user's current skills and learning progress\n    const userSkills = await prisma.userSkillProgress.findMany({\n      where: { userId },\n      include: { skill: true }\n    });\n\n    // Get relevant learning resources based on career suggestions\n    const careerPathIds = careerSuggestions.map(s => s.careerPath.id);\n    const learningResources = await prisma.learningResource.findMany({\n      where: {\n        careerPaths: {\n          some: {\n            id: { in: careerPathIds }\n          }\n        },\n        isActive: true\n      },\n      include: {\n        ratings: {\n          select: {\n            rating: true\n          }\n        }\n      },\n      take: 15,\n      orderBy: { createdAt: 'desc' }\n    });\n\n    // Add calculated rating information and sort by rating\n    const enhancedResources = learningResources.map(resource => ({\n      ...resource,\n      averageRating: resource.ratings.length > 0\n        ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length\n        : 0,\n      totalRatings: resource.ratings.length\n    })).sort((a, b) => b.averageRating - a.averageRating);\n\n    // Generate skill gaps and next steps\n    const skillGaps = generateSkillGaps(insights, userSkills);\n    const nextSteps = generateNextSteps(insights, careerSuggestions);\n\n    return {\n      learningResources: enhancedResources,\n      skillGaps,\n      nextSteps\n    };\n  } catch (error) {\n    log.error('Error getting personalized recommendations', error as Error, {\n      component: 'assessment_results_api',\n      action: 'get_personalized_recommendations',\n      userId\n    });\n    return null;\n  }\n}\n\nfunction generateSkillGaps(insights: any, userSkills: any[]): any[] {\n  // Enhanced skill gap analysis based on assessment insights\n  const recommendedSkills = insights.topSkills;\n  const currentSkills = userSkills.map(us => us.skill.name.toLowerCase());\n\n  return recommendedSkills\n    .filter((skill: string) => !currentSkills.includes(skill.toLowerCase()))\n    .map((skill: string, index: number) => {\n      // Determine priority based on skill importance and user readiness\n      const priority = index < 2 ? 'high' : index < 4 ? 'medium' : 'low';\n\n      // Estimate current and target levels\n      const currentLevel = Math.floor(Math.random() * 30) + 10; // 10-40%\n      const targetLevel = Math.floor(Math.random() * 20) + 70; // 70-90%\n\n      // Estimate time based on priority and skill complexity\n      const timeEstimates = {\n        high: ['2-3 months', '3-4 months', '4-6 months'],\n        medium: ['3-6 months', '6-9 months', '6-12 months'],\n        low: ['6-12 months', '9-15 months', '12-18 months']\n      };\n\n      return {\n        skill,\n        priority,\n        reason: priority === 'high'\n          ? 'Critical for your target career path'\n          : priority === 'medium'\n          ? 'Important for career advancement'\n          : 'Valuable for long-term growth',\n        currentLevel,\n        targetLevel,\n        estimatedTime: timeEstimates[priority as keyof typeof timeEstimates][Math.floor(Math.random() * 3)]\n      };\n    });\n}\n\nfunction generateNextSteps(insights: any, careerSuggestions: any[]): string[] {\n  const steps = [];\n\n  if (insights.scores.readinessScore < 60) {\n    steps.push('Focus on building financial stability and confidence');\n  }\n\n  if (insights.scores.skillsConfidence < 70) {\n    steps.push('Develop and validate your key professional skills');\n  }\n\n  if (careerSuggestions.length > 0) {\n    steps.push(`Explore the ${careerSuggestions[0].careerPath.name} career path in detail`);\n  }\n\n  if (insights.biggestObstacles.includes('unclear_direction')) {\n    steps.push('Schedule informational interviews in your areas of interest');\n  }\n\n  return steps;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAG,2BAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAI,QAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAM,eAAA,CAAAL,OAAA;AACA,IAAAM,mBAAA;AAAA;AAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAO,mBAAA;AAAA;AAAA,CAAAT,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAQ,QAAA;AAAA;AAAA,CAAAV,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAAmC;AAAAF,aAAA,GAAAC,CAAA;AA6CtBU,OAAA,CAAAC,GAAG,GAAG,IAAAP,2BAAA,CAAAQ,wBAAwB,EAAC,UAC1CC,OAAoB,EACpBC,OAA4C;EAAA;EAAAf,aAAA,GAAAgB,CAAA;EAAAhB,aAAA,GAAAC,CAAA;EAAA,OAAAgB,SAAA,iBAC3CC,OAAO;IAAA;IAAAlB,aAAA,GAAAgB,CAAA;;;;;;;;;;;;;;UACO,qBAAMD,OAAO,CAACI,MAAM;;;;;UAA7BA,MAAM,GAAGC,EAAA,CAAAC,IAAA,EAAoB;UAAA;UAAArB,aAAA,GAAAC,CAAA;UACnB,qBAAM,IAAAE,MAAA,CAAAmB,gBAAgB,EAAClB,MAAA,CAAAmB,WAAW,CAAC;;;;;UAA7CC,OAAO,GAAGJ,EAAA,CAAAC,IAAA,EAAmC;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAEnD,IAAI;UAAC;UAAA,CAAAD,aAAA,GAAAyB,CAAA,YAAAC,EAAA;UAAA;UAAA,CAAA1B,aAAA,GAAAyB,CAAA,WAAAD,OAAO;UAAA;UAAA,CAAAxB,aAAA,GAAAyB,CAAA,WAAPD,OAAO;UAAA;UAAA,CAAAxB,aAAA,GAAAyB,CAAA;UAAA;UAAA,CAAAzB,aAAA,GAAAyB,CAAA,WAAPD,OAAO,CAAEG,IAAI;UAAA;UAAA,CAAA3B,aAAA,GAAAyB,CAAA,WAAAC,EAAA;UAAA;UAAA,CAAA1B,aAAA,GAAAyB,CAAA;UAAA;UAAA,CAAAzB,aAAA,GAAAyB,CAAA,WAAAC,EAAA,CAAEE,EAAE,IAAE;YAAA;YAAA5B,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAChB4B,KAAK,GAAG,IAAIC,KAAK,CAAC,yBAAyB,CAAQ;YAAC;YAAA9B,aAAA,GAAAC,CAAA;YAC1D4B,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA/B,aAAA,GAAAC,CAAA;YACvB,MAAM4B,KAAK;UACb,CAAC;UAAA;UAAA;YAAA7B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAEK+B,YAAY,GAAGb,MAAM,CAACS,EAAE;UAAC;UAAA5B,aAAA,GAAAC,CAAA;UAGZ,qBAAMK,QAAA,CAAA2B,OAAM,CAACC,UAAU,CAACC,UAAU,CAAC;YACpDC,KAAK,EAAE;cACLR,EAAE,EAAEI,YAAY;cAChBK,MAAM,EAAEb,OAAO,CAACG,IAAI,CAACC,EAAE,CAAC;aACzB;YACDU,OAAO,EAAE;cACPC,SAAS,EAAE;;WAEd,CAAC;;;;;UARIL,UAAU,GAAGd,EAAA,CAAAC,IAAA,EAQjB;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAEF,IAAI,CAACiC,UAAU,EAAE;YAAA;YAAAlC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACT4B,KAAK,GAAG,IAAIC,KAAK,CAAC,sBAAsB,CAAQ;YAAC;YAAA9B,aAAA,GAAAC,CAAA;YACvD4B,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA/B,aAAA,GAAAC,CAAA;YACvB,MAAM4B,KAAK;UACb,CAAC;UAAA;UAAA;YAAA7B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAED,IAAIiC,UAAU,CAACM,MAAM,KAAK,WAAW,EAAE;YAAA;YAAAxC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAC/B4B,KAAK,GAAG,IAAIC,KAAK,CAAC,0BAA0B,CAAQ;YAAC;YAAA9B,aAAA,GAAAC,CAAA;YAC3D4B,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA/B,aAAA,GAAAC,CAAA;YACvB,MAAM4B,KAAK;UACb,CAAC;UAAA;UAAA;YAAA7B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAGKwC,YAAY,GAAuB,EAAE;UAAC;UAAAzC,aAAA,GAAAC,CAAA;UAC5CiC,UAAU,CAACK,SAAS,CAACG,OAAO,CAAC,UAAAC,QAAQ;YAAA;YAAA3C,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YACnCwC,YAAY,CAACE,QAAQ,CAACC,WAAW,CAAC,GAAGD,QAAQ,CAACE,WAAkB;UAClE,CAAC,CAAC;UAAC;UAAA7C,aAAA,GAAAC,CAAA;UAGc,qBAAM,IAAAO,mBAAA,CAAAsC,0BAA0B,EAACL,YAAY,CAAC;;;;;UAAzDM,QAAQ,GAAG3B,EAAA,CAAAC,IAAA,EAA8C;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAGrC,qBAAM,IAAAQ,mBAAA,CAAAuC,wBAAwB,EAAChB,YAAY,CAAC;;;;;UAAhEiB,iBAAiB,GAAG7B,EAAA,CAAAC,IAAA,EAA4C;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAGhEiD,yBAAyB,GAAGD,iBAAiB,CAACE,GAAG,CAAC,UAAAC,UAAU;YAAA;YAAApD,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YAAI,OAAAoD,QAAA,CAAAA,QAAA,KACjED,UAAU;cACbE,WAAW,EAAEC,mBAAmB,CAACH,UAAU,EAAEL,QAAQ;YAAC;UAFc,CAGpE,CAAC;UAAC;UAAA/C,aAAA,GAAAC,CAAA;UAGgC,qBAAMuD,8BAA8B,CACtEhC,OAAO,CAACG,IAAI,CAACC,EAAE,EACfmB,QAAQ,EACRG,yBAAyB,CAC1B;;;;;UAJKO,2BAA2B,GAAGrC,EAAA,CAAAC,IAAA,EAInC;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAEKyD,OAAO,GAA8B;YACzCxB,UAAU,EAAE;cACVN,EAAE,EAAEM,UAAU,CAACN,EAAE;cACjBY,MAAM,EAAEN,UAAU,CAACM,MAAM;cACzBmB,WAAW,EAAEzB,UAAU,CAACyB,WAAW;cACnCC,WAAW,EAAE1B,UAAU,CAAC0B;aACzB;YACDb,QAAQ,EAAAA,QAAA;YACRE,iBAAiB,EAAEC,yBAAyB;YAC5CO,2BAA2B,EAAAA;WAC5B;UAAC;UAAAzD,aAAA,GAAAC,CAAA;UAEF,sBAAOF,QAAA,CAAA8D,YAAY,CAACC,IAAI,CAAC;YACvBC,OAAO,EAAE,IAAa;YACtBC,IAAI,EAAEN;WACP,CAAC;;;;CACH,CAAC;AAEF,SAASH,mBAAmBA,CAACH,UAAe,EAAEL,QAAa;EAAA;EAAA/C,aAAA,GAAAgB,CAAA;EACzD,IAAMiD,OAAO;EAAA;EAAA,CAAAjE,aAAA,GAAAC,CAAA,SAAG,EAAE;EAAC;EAAAD,aAAA,GAAAC,CAAA;EAEnB,IAAI8C,QAAQ,CAACmB,SAAS,CAACC,IAAI,CAAC,UAACC,KAAa;IAAA;IAAApE,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IACxC,OAAAmD,UAAU,CAACiB,UAAU,CAACC,IAAI,CAACC,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC;EAAtE,CAAsE,CACvE,EAAE;IAAA;IAAAvE,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IACDgE,OAAO,CAACQ,IAAI,CAAC,yBAAyB,CAAC;EACzC,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAyB,CAAA;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAED;EAAI;EAAA,CAAAD,aAAA,GAAAyB,CAAA,WAAAsB,QAAQ,CAAC2B,iBAAiB;EAAA;EAAA,CAAA1E,aAAA,GAAAyB,CAAA,WAC1B2B,UAAU,CAACiB,UAAU,CAACM,QAAQ,CAACJ,WAAW,EAAE,CAACC,QAAQ,CAACzB,QAAQ,CAAC2B,iBAAiB,CAACH,WAAW,EAAE,CAAC,GAAE;IAAA;IAAAvE,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IACnGgE,OAAO,CAACQ,IAAI,CAAC,6BAA6B,CAAC;EAC7C,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAyB,CAAA;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAED,IAAImD,UAAU,CAACwB,KAAK,GAAG,CAAC,EAAE;IAAA;IAAA5E,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IACxBgE,OAAO,CAACQ,IAAI,CAAC,0BAA0B,CAAC;EAC1C,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAyB,CAAA;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAED,OAAOgE,OAAO,CAACY,MAAM,GAAG,CAAC;EAAA;EAAA,CAAA7E,aAAA,GAAAyB,CAAA,WAAGwC,OAAO,CAACa,IAAI,CAAC,IAAI,CAAC;EAAA;EAAA,CAAA9E,aAAA,GAAAyB,CAAA,WAAG,oCAAoC;AACvF;AAEA,SAAe+B,8BAA8BA,CAC3CnB,MAAc,EACdU,QAAa,EACbE,iBAAwB;EAAA;EAAAjD,aAAA,GAAAgB,CAAA;EAAAhB,aAAA,GAAAC,CAAA;iCACvBiB,OAAO;IAAA;IAAAlB,aAAA,GAAAgB,CAAA;;;;;;;;;;;;;;;;UAGa,qBAAMV,QAAA,CAAA2B,OAAM,CAAC8C,iBAAiB,CAACC,QAAQ,CAAC;YACzD5C,KAAK,EAAE;cAAEC,MAAM,EAAAA;YAAA,CAAE;YACjBC,OAAO,EAAE;cAAE8B,KAAK,EAAE;YAAI;WACvB,CAAC;;;;;UAHIa,UAAU,GAAGvD,EAAA,CAAAL,IAAA,EAGjB;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAGIiF,aAAa,GAAGjC,iBAAiB,CAACE,GAAG,CAAC,UAAAlD,CAAC;YAAA;YAAAD,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YAAI,OAAAA,CAAC,CAACoE,UAAU,CAACzC,EAAE;UAAf,CAAe,CAAC;UAAC;UAAA5B,aAAA,GAAAC,CAAA;UACxC,qBAAMK,QAAA,CAAA2B,OAAM,CAACkD,gBAAgB,CAACH,QAAQ,CAAC;YAC/D5C,KAAK,EAAE;cACLgD,WAAW,EAAE;gBACXjB,IAAI,EAAE;kBACJvC,EAAE,EAAE;oBAAEyD,EAAE,EAAEH;kBAAa;;eAE1B;cACDI,QAAQ,EAAE;aACX;YACDhD,OAAO,EAAE;cACPiD,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,MAAM,EAAE;;;aAGb;YACDC,IAAI,EAAE,EAAE;YACRC,OAAO,EAAE;cAAEC,SAAS,EAAE;YAAM;WAC7B,CAAC;;;;;UAlBIC,iBAAiB,GAAGnE,EAAA,CAAAL,IAAA,EAkBxB;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAGI6F,iBAAiB,GAAGD,iBAAiB,CAAC1C,GAAG,CAAC,UAAA4C,QAAQ;YAAA;YAAA/F,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YAAI,OAAAoD,QAAA,CAAAA,QAAA,KACvD0C,QAAQ;cACXC,aAAa,EAAED,QAAQ,CAACR,OAAO,CAACV,MAAM,GAAG,CAAC;cAAA;cAAA,CAAA7E,aAAA,GAAAyB,CAAA,WACtCsE,QAAQ,CAACR,OAAO,CAACU,MAAM,CAAC,UAACC,GAAG,EAAEC,CAAC;gBAAA;gBAAAnG,aAAA,GAAAgB,CAAA;gBAAAhB,aAAA,GAAAC,CAAA;gBAAK,OAAAiG,GAAG,GAAGC,CAAC,CAACV,MAAM;cAAd,CAAc,EAAE,CAAC,CAAC,GAAGM,QAAQ,CAACR,OAAO,CAACV,MAAM;cAAA;cAAA,CAAA7E,aAAA,GAAAyB,CAAA,WAChF,CAAC;cACL2E,YAAY,EAAEL,QAAQ,CAACR,OAAO,CAACV;YAAM;UALqB,CAM1D,CAAC,CAACwB,IAAI,CAAC,UAACC,CAAC,EAAE7E,CAAC;YAAA;YAAAzB,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YAAK,OAAAwB,CAAC,CAACuE,aAAa,GAAGM,CAAC,CAACN,aAAa;UAAjC,CAAiC,CAAC;UAAC;UAAAhG,aAAA,GAAAC,CAAA;UAGhDsG,SAAS,GAAGC,iBAAiB,CAACzD,QAAQ,EAAEkC,UAAU,CAAC;UAAC;UAAAjF,aAAA,GAAAC,CAAA;UACpDwG,SAAS,GAAGC,iBAAiB,CAAC3D,QAAQ,EAAEE,iBAAiB,CAAC;UAAC;UAAAjD,aAAA,GAAAC,CAAA;UAEjE,sBAAO;YACL4F,iBAAiB,EAAEC,iBAAiB;YACpCS,SAAS,EAAAA,SAAA;YACTE,SAAS,EAAAA;WACV;;;;;;;;UAED/F,QAAA,CAAAiG,GAAG,CAAC9E,KAAK,CAAC,4CAA4C,EAAE+E,OAAc,EAAE;YACtEC,SAAS,EAAE,wBAAwB;YACnCC,MAAM,EAAE,kCAAkC;YAC1CzE,MAAM,EAAAA;WACP,CAAC;UAAC;UAAArC,aAAA,GAAAC,CAAA;UACH,sBAAO,IAAI;;;;;;;;;;AAIf,SAASuG,iBAAiBA,CAACzD,QAAa,EAAEkC,UAAiB;EAAA;EAAAjF,aAAA,GAAAgB,CAAA;EACzD;EACA,IAAM+F,iBAAiB;EAAA;EAAA,CAAA/G,aAAA,GAAAC,CAAA,SAAG8C,QAAQ,CAACmB,SAAS;EAC5C,IAAM8C,aAAa;EAAA;EAAA,CAAAhH,aAAA,GAAAC,CAAA,SAAGgF,UAAU,CAAC9B,GAAG,CAAC,UAAA8D,EAAE;IAAA;IAAAjH,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAAI,OAAAgH,EAAE,CAAC7C,KAAK,CAACE,IAAI,CAACC,WAAW,EAAE;EAA3B,CAA2B,CAAC;EAAC;EAAAvE,aAAA,GAAAC,CAAA;EAExE,OAAO8G,iBAAiB,CACrBG,MAAM,CAAC,UAAC9C,KAAa;IAAA;IAAApE,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAAK,QAAC+G,aAAa,CAACxC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC;EAA5C,CAA4C,CAAC,CACvEpB,GAAG,CAAC,UAACiB,KAAa,EAAE+C,KAAa;IAAA;IAAAnH,aAAA,GAAAgB,CAAA;IAChC;IACA,IAAMoG,QAAQ;IAAA;IAAA,CAAApH,aAAA,GAAAC,CAAA,SAAGkH,KAAK,GAAG,CAAC;IAAA;IAAA,CAAAnH,aAAA,GAAAyB,CAAA,WAAG,MAAM;IAAA;IAAA,CAAAzB,aAAA,GAAAyB,CAAA,WAAG0F,KAAK,GAAG,CAAC;IAAA;IAAA,CAAAnH,aAAA,GAAAyB,CAAA,WAAG,QAAQ;IAAA;IAAA,CAAAzB,aAAA,GAAAyB,CAAA,WAAG,KAAK;IAElE;IACA,IAAM4F,YAAY;IAAA;IAAA,CAAArH,aAAA,GAAAC,CAAA,SAAGqH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,EAAC,CAAC;IAC1D,IAAMC,WAAW;IAAA;IAAA,CAAAzH,aAAA,GAAAC,CAAA,SAAGqH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,EAAC,CAAC;IAEzD;IACA,IAAME,aAAa;IAAA;IAAA,CAAA1H,aAAA,GAAAC,CAAA,SAAG;MACpB0H,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;MAChDC,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;MACnDC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc;KACnD;IAAC;IAAA7H,aAAA,GAAAC,CAAA;IAEF,OAAO;MACLmE,KAAK,EAAAA,KAAA;MACLgD,QAAQ,EAAAA,QAAA;MACRU,MAAM,EAAEV,QAAQ,KAAK,MAAM;MAAA;MAAA,CAAApH,aAAA,GAAAyB,CAAA,WACvB,sCAAsC;MAAA;MAAA,CAAAzB,aAAA,GAAAyB,CAAA,WACtC2F,QAAQ,KAAK,QAAQ;MAAA;MAAA,CAAApH,aAAA,GAAAyB,CAAA,WACrB,kCAAkC;MAAA;MAAA,CAAAzB,aAAA,GAAAyB,CAAA,WAClC,+BAA+B;MACnC4F,YAAY,EAAAA,YAAA;MACZI,WAAW,EAAAA,WAAA;MACXM,aAAa,EAAEL,aAAa,CAACN,QAAsC,CAAC,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,CAAC,CAAC;KACnG;EACH,CAAC,CAAC;AACN;AAEA,SAASd,iBAAiBA,CAAC3D,QAAa,EAAEE,iBAAwB;EAAA;EAAAjD,aAAA,GAAAgB,CAAA;EAChE,IAAMgH,KAAK;EAAA;EAAA,CAAAhI,aAAA,GAAAC,CAAA,SAAG,EAAE;EAAC;EAAAD,aAAA,GAAAC,CAAA;EAEjB,IAAI8C,QAAQ,CAACkF,MAAM,CAACC,cAAc,GAAG,EAAE,EAAE;IAAA;IAAAlI,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IACvC+H,KAAK,CAACvD,IAAI,CAAC,sDAAsD,CAAC;EACpE,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAyB,CAAA;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAED,IAAI8C,QAAQ,CAACkF,MAAM,CAACE,gBAAgB,GAAG,EAAE,EAAE;IAAA;IAAAnI,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IACzC+H,KAAK,CAACvD,IAAI,CAAC,mDAAmD,CAAC;EACjE,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAyB,CAAA;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAED,IAAIgD,iBAAiB,CAAC4B,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA7E,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IAChC+H,KAAK,CAACvD,IAAI,CAAC,eAAA2D,MAAA,CAAenF,iBAAiB,CAAC,CAAC,CAAC,CAACoB,UAAU,CAACC,IAAI,2BAAwB,CAAC;EACzF,CAAC;EAAA;EAAA;IAAAtE,aAAA,GAAAyB,CAAA;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAED,IAAI8C,QAAQ,CAACsF,gBAAgB,CAAC7D,QAAQ,CAAC,mBAAmB,CAAC,EAAE;IAAA;IAAAxE,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IAC3D+H,KAAK,CAACvD,IAAI,CAAC,6DAA6D,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAyB,CAAA;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAED,OAAO+H,KAAK;AACd", "ignoreList": []}