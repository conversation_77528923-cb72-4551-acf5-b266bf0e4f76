bd525541808bc2557b67b6b67f601826
"use strict";

/* istanbul ignore next */
function cov_1mhwbdq9s() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/results/[id]/route.ts";
  var hash = "c18d5df7beb687e39598585bf29898019abe441c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/results/[id]/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 21
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 13
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "85": {
        start: {
          line: 57,
          column: 34
        },
        end: {
          line: 57,
          column: 76
        }
      },
      "86": {
        start: {
          line: 58,
          column: 15
        },
        end: {
          line: 58,
          column: 55
        }
      },
      "87": {
        start: {
          line: 59,
          column: 26
        },
        end: {
          line: 59,
          column: 60
        }
      },
      "88": {
        start: {
          line: 60,
          column: 26
        },
        end: {
          line: 60,
          column: 60
        }
      },
      "89": {
        start: {
          line: 61,
          column: 15
        },
        end: {
          line: 61,
          column: 38
        }
      },
      "90": {
        start: {
          line: 62,
          column: 0
        },
        end: {
          line: 131,
          column: 7
        }
      },
      "91": {
        start: {
          line: 62,
          column: 102
        },
        end: {
          line: 131,
          column: 3
        }
      },
      "92": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 130,
          column: 7
        }
      },
      "93": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 129,
          column: 9
        }
      },
      "94": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 67,
          column: 57
        }
      },
      "95": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 35
        }
      },
      "96": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 70,
          column: 87
        }
      },
      "97": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 72,
          column: 36
        }
      },
      "98": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 77,
          column: 17
        }
      },
      "99": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 65
        }
      },
      "100": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 43
        }
      },
      "101": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 32
        }
      },
      "102": {
        start: {
          line: 78,
          column: 16
        },
        end: {
          line: 78,
          column: 41
        }
      },
      "103": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 87,
          column: 24
        }
      },
      "104": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 39
        }
      },
      "105": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 94,
          column: 17
        }
      },
      "106": {
        start: {
          line: 91,
          column: 20
        },
        end: {
          line: 91,
          column: 62
        }
      },
      "107": {
        start: {
          line: 92,
          column: 20
        },
        end: {
          line: 92,
          column: 43
        }
      },
      "108": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 93,
          column: 32
        }
      },
      "109": {
        start: {
          line: 95,
          column: 16
        },
        end: {
          line: 99,
          column: 17
        }
      },
      "110": {
        start: {
          line: 96,
          column: 20
        },
        end: {
          line: 96,
          column: 66
        }
      },
      "111": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 97,
          column: 43
        }
      },
      "112": {
        start: {
          line: 98,
          column: 20
        },
        end: {
          line: 98,
          column: 32
        }
      },
      "113": {
        start: {
          line: 100,
          column: 16
        },
        end: {
          line: 100,
          column: 34
        }
      },
      "114": {
        start: {
          line: 101,
          column: 16
        },
        end: {
          line: 103,
          column: 19
        }
      },
      "115": {
        start: {
          line: 102,
          column: 20
        },
        end: {
          line: 102,
          column: 78
        }
      },
      "116": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 104,
          column: 104
        }
      },
      "117": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 106,
          column: 37
        }
      },
      "118": {
        start: {
          line: 107,
          column: 16
        },
        end: {
          line: 107,
          column: 102
        }
      },
      "119": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 109,
          column: 46
        }
      },
      "120": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 110,
          column: 198
        }
      },
      "121": {
        start: {
          line: 110,
          column: 90
        },
        end: {
          line: 110,
          column: 194
        }
      },
      "122": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 111,
          column: 123
        }
      },
      "123": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 56
        }
      },
      "124": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 124,
          column: 18
        }
      },
      "125": {
        start: {
          line: 125,
          column: 16
        },
        end: {
          line: 128,
          column: 24
        }
      },
      "126": {
        start: {
          line: 133,
          column: 18
        },
        end: {
          line: 133,
          column: 20
        }
      },
      "127": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "128": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 135,
          column: 86
        }
      },
      "129": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 48
        }
      },
      "130": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 142,
          column: 5
        }
      },
      "131": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 52
        }
      },
      "132": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 145,
          column: 5
        }
      },
      "133": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 144,
          column: 49
        }
      },
      "134": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 146,
          column: 90
        }
      },
      "135": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 204,
          column: 7
        }
      },
      "136": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 203,
          column: 11
        }
      },
      "137": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 202,
          column: 13
        }
      },
      "138": {
        start: {
          line: 154,
          column: 20
        },
        end: {
          line: 154,
          column: 46
        }
      },
      "139": {
        start: {
          line: 155,
          column: 20
        },
        end: {
          line: 158,
          column: 28
        }
      },
      "140": {
        start: {
          line: 160,
          column: 20
        },
        end: {
          line: 160,
          column: 43
        }
      },
      "141": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 161,
          column: 100
        }
      },
      "142": {
        start: {
          line: 161,
          column: 73
        },
        end: {
          line: 161,
          column: 96
        }
      },
      "143": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 180,
          column: 28
        }
      },
      "144": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 182,
          column: 50
        }
      },
      "145": {
        start: {
          line: 183,
          column: 20
        },
        end: {
          line: 185,
          column: 147
        }
      },
      "146": {
        start: {
          line: 183,
          column: 84
        },
        end: {
          line: 185,
          column: 75
        }
      },
      "147": {
        start: {
          line: 184,
          column: 74
        },
        end: {
          line: 184,
          column: 96
        }
      },
      "148": {
        start: {
          line: 185,
          column: 102
        },
        end: {
          line: 185,
          column: 143
        }
      },
      "149": {
        start: {
          line: 186,
          column: 20
        },
        end: {
          line: 186,
          column: 72
        }
      },
      "150": {
        start: {
          line: 187,
          column: 20
        },
        end: {
          line: 187,
          column: 79
        }
      },
      "151": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 192,
          column: 27
        }
      },
      "152": {
        start: {
          line: 194,
          column: 20
        },
        end: {
          line: 194,
          column: 40
        }
      },
      "153": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 199,
          column: 23
        }
      },
      "154": {
        start: {
          line: 200,
          column: 20
        },
        end: {
          line: 200,
          column: 48
        }
      },
      "155": {
        start: {
          line: 201,
          column: 24
        },
        end: {
          line: 201,
          column: 46
        }
      },
      "156": {
        start: {
          line: 208,
          column: 28
        },
        end: {
          line: 208,
          column: 46
        }
      },
      "157": {
        start: {
          line: 209,
          column: 24
        },
        end: {
          line: 209,
          column: 93
        }
      },
      "158": {
        start: {
          line: 209,
          column: 55
        },
        end: {
          line: 209,
          column: 90
        }
      },
      "159": {
        start: {
          line: 210,
          column: 4
        },
        end: {
          line: 236,
          column: 7
        }
      },
      "160": {
        start: {
          line: 211,
          column: 35
        },
        end: {
          line: 211,
          column: 87
        }
      },
      "161": {
        start: {
          line: 214,
          column: 23
        },
        end: {
          line: 214,
          column: 72
        }
      },
      "162": {
        start: {
          line: 216,
          column: 27
        },
        end: {
          line: 216,
          column: 62
        }
      },
      "163": {
        start: {
          line: 217,
          column: 26
        },
        end: {
          line: 217,
          column: 61
        }
      },
      "164": {
        start: {
          line: 219,
          column: 28
        },
        end: {
          line: 223,
          column: 9
        }
      },
      "165": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 235,
          column: 10
        }
      },
      "166": {
        start: {
          line: 239,
          column: 16
        },
        end: {
          line: 239,
          column: 18
        }
      },
      "167": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 242,
          column: 5
        }
      },
      "168": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 241,
          column: 75
        }
      },
      "169": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 245,
          column: 5
        }
      },
      "170": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 244,
          column: 72
        }
      },
      "171": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 248,
          column: 5
        }
      },
      "172": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 106
        }
      },
      "173": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "174": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 250,
          column: 82
        }
      },
      "175": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 252,
          column: 17
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 62,
            column: 72
          },
          end: {
            line: 62,
            column: 73
          }
        },
        loc: {
          start: {
            line: 62,
            column: 100
          },
          end: {
            line: 131,
            column: 5
          }
        },
        line: 62
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 62,
            column: 144
          },
          end: {
            line: 62,
            column: 145
          }
        },
        loc: {
          start: {
            line: 62,
            column: 156
          },
          end: {
            line: 131,
            column: 1
          }
        },
        line: 62
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 65,
            column: 29
          },
          end: {
            line: 65,
            column: 30
          }
        },
        loc: {
          start: {
            line: 65,
            column: 43
          },
          end: {
            line: 130,
            column: 5
          }
        },
        line: 65
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 101,
            column: 45
          },
          end: {
            line: 101,
            column: 46
          }
        },
        loc: {
          start: {
            line: 101,
            column: 65
          },
          end: {
            line: 103,
            column: 17
          }
        },
        line: 101
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 110,
            column: 66
          },
          end: {
            line: 110,
            column: 67
          }
        },
        loc: {
          start: {
            line: 110,
            column: 88
          },
          end: {
            line: 110,
            column: 196
          }
        },
        line: 110
      },
      "21": {
        name: "generateMatchReason",
        decl: {
          start: {
            line: 132,
            column: 9
          },
          end: {
            line: 132,
            column: 28
          }
        },
        loc: {
          start: {
            line: 132,
            column: 51
          },
          end: {
            line: 147,
            column: 1
          }
        },
        line: 132
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 134,
            column: 32
          },
          end: {
            line: 134,
            column: 33
          }
        },
        loc: {
          start: {
            line: 134,
            column: 49
          },
          end: {
            line: 136,
            column: 5
          }
        },
        line: 134
      },
      "23": {
        name: "getPersonalizedRecommendations",
        decl: {
          start: {
            line: 148,
            column: 9
          },
          end: {
            line: 148,
            column: 39
          }
        },
        loc: {
          start: {
            line: 148,
            column: 77
          },
          end: {
            line: 205,
            column: 1
          }
        },
        line: 148
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 149,
            column: 44
          },
          end: {
            line: 149,
            column: 45
          }
        },
        loc: {
          start: {
            line: 149,
            column: 56
          },
          end: {
            line: 204,
            column: 5
          }
        },
        line: 149
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 151,
            column: 33
          },
          end: {
            line: 151,
            column: 34
          }
        },
        loc: {
          start: {
            line: 151,
            column: 47
          },
          end: {
            line: 203,
            column: 9
          }
        },
        line: 151
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 161,
            column: 58
          },
          end: {
            line: 161,
            column: 59
          }
        },
        loc: {
          start: {
            line: 161,
            column: 71
          },
          end: {
            line: 161,
            column: 98
          }
        },
        line: 161
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 183,
            column: 62
          },
          end: {
            line: 183,
            column: 63
          }
        },
        loc: {
          start: {
            line: 183,
            column: 82
          },
          end: {
            line: 185,
            column: 77
          }
        },
        line: 183
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 184,
            column: 54
          },
          end: {
            line: 184,
            column: 55
          }
        },
        loc: {
          start: {
            line: 184,
            column: 72
          },
          end: {
            line: 184,
            column: 98
          }
        },
        line: 184
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 185,
            column: 84
          },
          end: {
            line: 185,
            column: 85
          }
        },
        loc: {
          start: {
            line: 185,
            column: 100
          },
          end: {
            line: 185,
            column: 145
          }
        },
        line: 185
      },
      "30": {
        name: "generateSkillGaps",
        decl: {
          start: {
            line: 206,
            column: 9
          },
          end: {
            line: 206,
            column: 26
          }
        },
        loc: {
          start: {
            line: 206,
            column: 49
          },
          end: {
            line: 237,
            column: 1
          }
        },
        line: 206
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 209,
            column: 39
          },
          end: {
            line: 209,
            column: 40
          }
        },
        loc: {
          start: {
            line: 209,
            column: 53
          },
          end: {
            line: 209,
            column: 92
          }
        },
        line: 209
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 211,
            column: 17
          }
        },
        loc: {
          start: {
            line: 211,
            column: 33
          },
          end: {
            line: 211,
            column: 89
          }
        },
        line: 211
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 212,
            column: 13
          },
          end: {
            line: 212,
            column: 14
          }
        },
        loc: {
          start: {
            line: 212,
            column: 37
          },
          end: {
            line: 236,
            column: 5
          }
        },
        line: 212
      },
      "34": {
        name: "generateNextSteps",
        decl: {
          start: {
            line: 238,
            column: 9
          },
          end: {
            line: 238,
            column: 26
          }
        },
        loc: {
          start: {
            line: 238,
            column: 56
          },
          end: {
            line: 253,
            column: 1
          }
        },
        line: 238
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 66,
            column: 8
          },
          end: {
            line: 129,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 67,
            column: 57
          }
        }, {
          start: {
            line: 68,
            column: 12
          },
          end: {
            line: 70,
            column: 87
          }
        }, {
          start: {
            line: 71,
            column: 12
          },
          end: {
            line: 87,
            column: 24
          }
        }, {
          start: {
            line: 88,
            column: 12
          },
          end: {
            line: 104,
            column: 104
          }
        }, {
          start: {
            line: 105,
            column: 12
          },
          end: {
            line: 107,
            column: 102
          }
        }, {
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 111,
            column: 123
          }
        }, {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 128,
            column: 24
          }
        }],
        line: 66
      },
      "39": {
        loc: {
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 77,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 77,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 73
      },
      "40": {
        loc: {
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 120
          },
          end: {
            line: 73,
            column: 126
          }
        }, {
          start: {
            line: 73,
            column: 129
          },
          end: {
            line: 73,
            column: 134
          }
        }],
        line: 73
      },
      "41": {
        loc: {
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 100
          }
        }, {
          start: {
            line: 73,
            column: 104
          },
          end: {
            line: 73,
            column: 117
          }
        }],
        line: 73
      },
      "42": {
        loc: {
          start: {
            line: 73,
            column: 28
          },
          end: {
            line: 73,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 69
          },
          end: {
            line: 73,
            column: 75
          }
        }, {
          start: {
            line: 73,
            column: 78
          },
          end: {
            line: 73,
            column: 90
          }
        }],
        line: 73
      },
      "43": {
        loc: {
          start: {
            line: 73,
            column: 28
          },
          end: {
            line: 73,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 28
          },
          end: {
            line: 73,
            column: 44
          }
        }, {
          start: {
            line: 73,
            column: 48
          },
          end: {
            line: 73,
            column: 66
          }
        }],
        line: 73
      },
      "44": {
        loc: {
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 94,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 94,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 90
      },
      "45": {
        loc: {
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 99,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 99,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "46": {
        loc: {
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "47": {
        loc: {
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "48": {
        loc: {
          start: {
            line: 139,
            column: 8
          },
          end: {
            line: 140,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 8
          },
          end: {
            line: 139,
            column: 34
          }
        }, {
          start: {
            line: 140,
            column: 8
          },
          end: {
            line: 140,
            column: 103
          }
        }],
        line: 139
      },
      "49": {
        loc: {
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 145,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 145,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "50": {
        loc: {
          start: {
            line: 146,
            column: 11
          },
          end: {
            line: 146,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 146,
            column: 32
          },
          end: {
            line: 146,
            column: 50
          }
        }, {
          start: {
            line: 146,
            column: 53
          },
          end: {
            line: 146,
            column: 89
          }
        }],
        line: 146
      },
      "51": {
        loc: {
          start: {
            line: 152,
            column: 12
          },
          end: {
            line: 202,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 153,
            column: 16
          },
          end: {
            line: 158,
            column: 28
          }
        }, {
          start: {
            line: 159,
            column: 16
          },
          end: {
            line: 180,
            column: 28
          }
        }, {
          start: {
            line: 181,
            column: 16
          },
          end: {
            line: 192,
            column: 27
          }
        }, {
          start: {
            line: 193,
            column: 16
          },
          end: {
            line: 200,
            column: 48
          }
        }, {
          start: {
            line: 201,
            column: 16
          },
          end: {
            line: 201,
            column: 46
          }
        }],
        line: 152
      },
      "52": {
        loc: {
          start: {
            line: 183,
            column: 142
          },
          end: {
            line: 185,
            column: 31
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 30
          },
          end: {
            line: 184,
            column: 128
          }
        }, {
          start: {
            line: 185,
            column: 30
          },
          end: {
            line: 185,
            column: 31
          }
        }],
        line: 183
      },
      "53": {
        loc: {
          start: {
            line: 214,
            column: 23
          },
          end: {
            line: 214,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 35
          },
          end: {
            line: 214,
            column: 41
          }
        }, {
          start: {
            line: 214,
            column: 44
          },
          end: {
            line: 214,
            column: 72
          }
        }],
        line: 214
      },
      "54": {
        loc: {
          start: {
            line: 214,
            column: 44
          },
          end: {
            line: 214,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 56
          },
          end: {
            line: 214,
            column: 64
          }
        }, {
          start: {
            line: 214,
            column: 67
          },
          end: {
            line: 214,
            column: 72
          }
        }],
        line: 214
      },
      "55": {
        loc: {
          start: {
            line: 227,
            column: 20
          },
          end: {
            line: 231,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 228,
            column: 18
          },
          end: {
            line: 228,
            column: 56
          }
        }, {
          start: {
            line: 229,
            column: 18
          },
          end: {
            line: 231,
            column: 53
          }
        }],
        line: 227
      },
      "56": {
        loc: {
          start: {
            line: 229,
            column: 18
          },
          end: {
            line: 231,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 230,
            column: 22
          },
          end: {
            line: 230,
            column: 56
          }
        }, {
          start: {
            line: 231,
            column: 22
          },
          end: {
            line: 231,
            column: 53
          }
        }],
        line: 229
      },
      "57": {
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 242,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 242,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "58": {
        loc: {
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "59": {
        loc: {
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "60": {
        loc: {
          start: {
            line: 249,
            column: 4
          },
          end: {
            line: 251,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 4
          },
          end: {
            line: 251,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0, 0, 0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/results/[id]/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,6EAAwF;AACxF,wDAAkC;AAClC,6DAAyF;AACzF,6DAAmE;AACnE,uCAAmC;AA6CtB,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAC1C,OAAoB,EACpB,OAA4C,qCAC3C,OAAO;;;;;oBACO,qBAAM,OAAO,CAAC,MAAM,EAAA;;gBAA7B,MAAM,GAAG,SAAoB;gBACnB,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEK,YAAY,GAAG,MAAM,CAAC,EAAE,CAAC;gBAGZ,qBAAM,gBAAM,CAAC,UAAU,CAAC,UAAU,CAAC;wBACpD,KAAK,EAAE;4BACL,EAAE,EAAE,YAAY;4BAChB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,oDAAoD;yBAC7E;wBACD,OAAO,EAAE;4BACP,SAAS,EAAE,IAAI;yBAChB;qBACF,CAAC,EAAA;;gBARI,UAAU,GAAG,SAQjB;gBAEF,IAAI,CAAC,UAAU,EAAE,CAAC;oBACV,KAAK,GAAG,IAAI,KAAK,CAAC,sBAAsB,CAAQ,CAAC;oBACvD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBAChC,KAAK,GAAG,IAAI,KAAK,CAAC,0BAA0B,CAAQ,CAAC;oBAC3D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGK,YAAY,GAAuB,EAAE,CAAC;gBAC5C,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;oBACnC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,WAAkB,CAAC;gBACnE,CAAC,CAAC,CAAC;gBAGc,qBAAM,IAAA,8CAA0B,EAAC,YAAY,CAAC,EAAA;;gBAAzD,QAAQ,GAAG,SAA8C;gBAGrC,qBAAM,IAAA,4CAAwB,EAAC,YAAY,CAAC,EAAA;;gBAAhE,iBAAiB,GAAG,SAA4C;gBAGhE,yBAAyB,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAA,UAAU,IAAI,OAAA,uBACjE,UAAU,KACb,WAAW,EAAE,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,IACtD,EAHoE,CAGpE,CAAC,CAAC;gBAGgC,qBAAM,8BAA8B,CACtE,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,QAAQ,EACR,yBAAyB,CAC1B,EAAA;;gBAJK,2BAA2B,GAAG,SAInC;gBAEK,OAAO,GAA8B;oBACzC,UAAU,EAAE;wBACV,EAAE,EAAE,UAAU,CAAC,EAAE;wBACjB,MAAM,EAAE,UAAU,CAAC,MAAM;wBACzB,WAAW,EAAE,UAAU,CAAC,WAAW;wBACnC,WAAW,EAAE,UAAU,CAAC,WAAW;qBACpC;oBACD,QAAQ,UAAA;oBACR,iBAAiB,EAAE,yBAAyB;oBAC5C,2BAA2B,6BAAA;iBAC5B,CAAC;gBAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAa;wBACtB,IAAI,EAAE,OAAO;qBACd,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAEH,SAAS,mBAAmB,CAAC,UAAe,EAAE,QAAa;IACzD,IAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,UAAC,KAAa;QACxC,OAAA,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;IAAtE,CAAsE,CACvE,EAAE,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI,QAAQ,CAAC,iBAAiB;QAC1B,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;QACpG,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,oCAAoC,CAAC;AACxF,CAAC;AAED,SAAe,8BAA8B,CAC3C,MAAc,EACd,QAAa,EACb,iBAAwB;mCACvB,OAAO;;;;;;oBAGa,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;4BACzD,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE;4BACjB,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;yBACzB,CAAC,EAAA;;oBAHI,UAAU,GAAG,SAGjB;oBAGI,aAAa,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,CAAC,EAAE,EAAf,CAAe,CAAC,CAAC;oBACxC,qBAAM,gBAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;4BAC/D,KAAK,EAAE;gCACL,WAAW,EAAE;oCACX,IAAI,EAAE;wCACJ,EAAE,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;qCAC1B;iCACF;gCACD,QAAQ,EAAE,IAAI;6BACf;4BACD,OAAO,EAAE;gCACP,OAAO,EAAE;oCACP,MAAM,EAAE;wCACN,MAAM,EAAE,IAAI;qCACb;iCACF;6BACF;4BACD,IAAI,EAAE,EAAE;4BACR,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;yBAC/B,CAAC,EAAA;;oBAlBI,iBAAiB,GAAG,SAkBxB;oBAGI,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,uBACvD,QAAQ,KACX,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;4BACxC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,MAAM,EAAd,CAAc,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM;4BAClF,CAAC,CAAC,CAAC,EACL,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,IACrC,EAN0D,CAM1D,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,EAAjC,CAAiC,CAAC,CAAC;oBAGhD,SAAS,GAAG,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;oBACpD,SAAS,GAAG,iBAAiB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;oBAEjE,sBAAO;4BACL,iBAAiB,EAAE,iBAAiB;4BACpC,SAAS,WAAA;4BACT,SAAS,WAAA;yBACV,EAAC;;;oBAEF,YAAG,CAAC,KAAK,CAAC,4CAA4C,EAAE,OAAc,EAAE;wBACtE,SAAS,EAAE,wBAAwB;wBACnC,MAAM,EAAE,kCAAkC;wBAC1C,MAAM,QAAA;qBACP,CAAC,CAAC;oBACH,sBAAO,IAAI,EAAC;;;;;CAEf;AAED,SAAS,iBAAiB,CAAC,QAAa,EAAE,UAAiB;IACzD,2DAA2D;IAC3D,IAAM,iBAAiB,GAAG,QAAQ,CAAC,SAAS,CAAC;IAC7C,IAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,EAA3B,CAA2B,CAAC,CAAC;IAExE,OAAO,iBAAiB;SACrB,MAAM,CAAC,UAAC,KAAa,IAAK,OAAA,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAA5C,CAA4C,CAAC;SACvE,GAAG,CAAC,UAAC,KAAa,EAAE,KAAa;QAChC,kEAAkE;QAClE,IAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;QAEnE,qCAAqC;QACrC,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;QACnE,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;QAElE,uDAAuD;QACvD,IAAM,aAAa,GAAG;YACpB,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;YAChD,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;YACnD,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC;SACpD,CAAC;QAEF,OAAO;YACL,KAAK,OAAA;YACL,QAAQ,UAAA;YACR,MAAM,EAAE,QAAQ,KAAK,MAAM;gBACzB,CAAC,CAAC,sCAAsC;gBACxC,CAAC,CAAC,QAAQ,KAAK,QAAQ;oBACvB,CAAC,CAAC,kCAAkC;oBACpC,CAAC,CAAC,+BAA+B;YACnC,YAAY,cAAA;YACZ,WAAW,aAAA;YACX,aAAa,EAAE,aAAa,CAAC,QAAsC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;SACpG,CAAC;IACJ,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,iBAAiB,CAAC,QAAa,EAAE,iBAAwB;IAChE,IAAM,KAAK,GAAG,EAAE,CAAC;IAEjB,IAAI,QAAQ,CAAC,MAAM,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;QACxC,KAAK,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,CAAC,gBAAgB,GAAG,EAAE,EAAE,CAAC;QAC1C,KAAK,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;IAClE,CAAC;IAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjC,KAAK,CAAC,IAAI,CAAC,sBAAe,iBAAiB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,2BAAwB,CAAC,CAAC;IAC1F,CAAC;IAED,IAAI,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;QAC5D,KAAK,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;IAC5E,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/results/[id]/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport prisma from '@/lib/prisma';\nimport { generateAssessmentInsights, AssessmentResponse } from '@/lib/assessmentScoring';\nimport { getCareerPathSuggestions } from '@/lib/suggestionService';\nimport { log } from '@/lib/logger';\n\ninterface AssessmentResultsResponse {\n  assessment: {\n    id: string;\n    status: string;\n    completedAt: Date | null;\n    currentStep: number;\n  };\n  insights: {\n    scores: {\n      readinessScore: number;\n      riskTolerance: number;\n      urgencyLevel: number;\n      skillsConfidence: number;\n      supportLevel: number;\n      financialReadiness: number;\n    };\n    primaryMotivation: string;\n    topSkills: string[];\n    biggestObstacles: string[];\n    recommendedTimeline: string;\n    keyRecommendations: string[];\n    careerPathSuggestions: string[];\n  };\n  careerSuggestions: Array<{\n    careerPath: {\n      id: string;\n      name: string;\n      slug: string;\n      overview: string;\n      pros: string;\n      cons: string;\n      actionableSteps: any;\n    };\n    score: number;\n    matchReason?: string;\n  }>;\n  personalizedRecommendations?: {\n    learningResources: any[];\n    skillGaps: any[];\n    nextSteps: string[];\n  };\n}\n\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  context: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<AssessmentResultsResponse>>> => {\n  const params = await context.params;\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const assessmentId = params.id;\n\n  // Fetch assessment with responses\n  const assessment = await prisma.assessment.findUnique({\n    where: {\n      id: assessmentId,\n      userId: session.user.id // Ensure user can only access their own assessments\n    },\n    include: {\n      responses: true\n    }\n  });\n\n  if (!assessment) {\n    const error = new Error('Assessment not found') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  if (assessment.status !== 'COMPLETED') {\n    const error = new Error('Assessment not completed') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Convert responses to the format expected by scoring functions\n  const responseData: AssessmentResponse = {};\n  assessment.responses.forEach(response => {\n    responseData[response.questionKey] = response.answerValue as any;\n  });\n\n  // Generate insights from assessment responses\n  const insights = await generateAssessmentInsights(responseData);\n\n  // Get career path suggestions\n  const careerSuggestions = await getCareerPathSuggestions(assessmentId);\n\n  // Enhance career suggestions with match reasoning\n  const enhancedCareerSuggestions = careerSuggestions.map(suggestion => ({\n    ...suggestion,\n    matchReason: generateMatchReason(suggestion, insights)\n  }));\n\n  // Get personalized learning recommendations (if available)\n  const personalizedRecommendations = await getPersonalizedRecommendations(\n    session.user.id,\n    insights,\n    enhancedCareerSuggestions\n  );\n\n  const results: AssessmentResultsResponse = {\n    assessment: {\n      id: assessment.id,\n      status: assessment.status,\n      completedAt: assessment.completedAt,\n      currentStep: assessment.currentStep\n    },\n    insights,\n    careerSuggestions: enhancedCareerSuggestions,\n    personalizedRecommendations\n  };\n\n  return NextResponse.json({\n    success: true as const,\n    data: results\n  });\n});\n\nfunction generateMatchReason(suggestion: any, insights: any): string {\n  const reasons = [];\n  \n  if (insights.topSkills.some((skill: string) => \n    suggestion.careerPath.name.toLowerCase().includes(skill.toLowerCase())\n  )) {\n    reasons.push('Matches your key skills');\n  }\n  \n  if (insights.primaryMotivation && \n      suggestion.careerPath.overview.toLowerCase().includes(insights.primaryMotivation.toLowerCase())) {\n    reasons.push('Aligns with your motivation');\n  }\n  \n  if (suggestion.score > 5) {\n    reasons.push('High compatibility score');\n  }\n  \n  return reasons.length > 0 ? reasons.join(', ') : 'Based on your assessment responses';\n}\n\nasync function getPersonalizedRecommendations(\n  userId: string,\n  insights: any,\n  careerSuggestions: any[]\n): Promise<any> {\n  try {\n    // Get user's current skills and learning progress\n    const userSkills = await prisma.userSkillProgress.findMany({\n      where: { userId },\n      include: { skill: true }\n    });\n\n    // Get relevant learning resources based on career suggestions\n    const careerPathIds = careerSuggestions.map(s => s.careerPath.id);\n    const learningResources = await prisma.learningResource.findMany({\n      where: {\n        careerPaths: {\n          some: {\n            id: { in: careerPathIds }\n          }\n        },\n        isActive: true\n      },\n      include: {\n        ratings: {\n          select: {\n            rating: true\n          }\n        }\n      },\n      take: 15,\n      orderBy: { createdAt: 'desc' }\n    });\n\n    // Add calculated rating information and sort by rating\n    const enhancedResources = learningResources.map(resource => ({\n      ...resource,\n      averageRating: resource.ratings.length > 0\n        ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length\n        : 0,\n      totalRatings: resource.ratings.length\n    })).sort((a, b) => b.averageRating - a.averageRating);\n\n    // Generate skill gaps and next steps\n    const skillGaps = generateSkillGaps(insights, userSkills);\n    const nextSteps = generateNextSteps(insights, careerSuggestions);\n\n    return {\n      learningResources: enhancedResources,\n      skillGaps,\n      nextSteps\n    };\n  } catch (error) {\n    log.error('Error getting personalized recommendations', error as Error, {\n      component: 'assessment_results_api',\n      action: 'get_personalized_recommendations',\n      userId\n    });\n    return null;\n  }\n}\n\nfunction generateSkillGaps(insights: any, userSkills: any[]): any[] {\n  // Enhanced skill gap analysis based on assessment insights\n  const recommendedSkills = insights.topSkills;\n  const currentSkills = userSkills.map(us => us.skill.name.toLowerCase());\n\n  return recommendedSkills\n    .filter((skill: string) => !currentSkills.includes(skill.toLowerCase()))\n    .map((skill: string, index: number) => {\n      // Determine priority based on skill importance and user readiness\n      const priority = index < 2 ? 'high' : index < 4 ? 'medium' : 'low';\n\n      // Estimate current and target levels\n      const currentLevel = Math.floor(Math.random() * 30) + 10; // 10-40%\n      const targetLevel = Math.floor(Math.random() * 20) + 70; // 70-90%\n\n      // Estimate time based on priority and skill complexity\n      const timeEstimates = {\n        high: ['2-3 months', '3-4 months', '4-6 months'],\n        medium: ['3-6 months', '6-9 months', '6-12 months'],\n        low: ['6-12 months', '9-15 months', '12-18 months']\n      };\n\n      return {\n        skill,\n        priority,\n        reason: priority === 'high'\n          ? 'Critical for your target career path'\n          : priority === 'medium'\n          ? 'Important for career advancement'\n          : 'Valuable for long-term growth',\n        currentLevel,\n        targetLevel,\n        estimatedTime: timeEstimates[priority as keyof typeof timeEstimates][Math.floor(Math.random() * 3)]\n      };\n    });\n}\n\nfunction generateNextSteps(insights: any, careerSuggestions: any[]): string[] {\n  const steps = [];\n\n  if (insights.scores.readinessScore < 60) {\n    steps.push('Focus on building financial stability and confidence');\n  }\n\n  if (insights.scores.skillsConfidence < 70) {\n    steps.push('Develop and validate your key professional skills');\n  }\n\n  if (careerSuggestions.length > 0) {\n    steps.push(`Explore the ${careerSuggestions[0].careerPath.name} career path in detail`);\n  }\n\n  if (insights.biggestObstacles.includes('unclear_direction')) {\n    steps.push('Schedule informational interviews in your areas of interest');\n  }\n\n  return steps;\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c18d5df7beb687e39598585bf29898019abe441c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1mhwbdq9s = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1mhwbdq9s();
var __assign =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[0]++,
/* istanbul ignore next */
(cov_1mhwbdq9s().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1mhwbdq9s().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1mhwbdq9s().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[0]++;
  cov_1mhwbdq9s().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[1]++;
    cov_1mhwbdq9s().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1mhwbdq9s().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1mhwbdq9s().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1mhwbdq9s().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[2][0]++;
          cov_1mhwbdq9s().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1mhwbdq9s().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1mhwbdq9s().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1mhwbdq9s().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[11]++,
/* istanbul ignore next */
(cov_1mhwbdq9s().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1mhwbdq9s().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1mhwbdq9s().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[3]++;
    cov_1mhwbdq9s().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().f[4]++;
      cov_1mhwbdq9s().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1mhwbdq9s().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().f[6]++;
      cov_1mhwbdq9s().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().f[7]++;
      cov_1mhwbdq9s().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().f[8]++;
      cov_1mhwbdq9s().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1mhwbdq9s().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[23]++,
/* istanbul ignore next */
(cov_1mhwbdq9s().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1mhwbdq9s().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1mhwbdq9s().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1mhwbdq9s().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1mhwbdq9s().f[10]++;
        cov_1mhwbdq9s().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[9][0]++;
          cov_1mhwbdq9s().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1mhwbdq9s().b[9][1]++;
        }
        cov_1mhwbdq9s().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1mhwbdq9s().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1mhwbdq9s().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[11]++;
    cov_1mhwbdq9s().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[12]++;
    cov_1mhwbdq9s().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().f[13]++;
      cov_1mhwbdq9s().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[14]++;
    cov_1mhwbdq9s().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().b[12][0]++;
      cov_1mhwbdq9s().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1mhwbdq9s().b[12][1]++;
    }
    cov_1mhwbdq9s().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[18][0]++,
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[19][1]++,
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1mhwbdq9s().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[15][0]++;
          cov_1mhwbdq9s().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1mhwbdq9s().b[15][1]++;
        }
        cov_1mhwbdq9s().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[21][0]++;
          cov_1mhwbdq9s().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1mhwbdq9s().b[21][1]++;
        }
        cov_1mhwbdq9s().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[22][1]++;
            cov_1mhwbdq9s().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[22][2]++;
            cov_1mhwbdq9s().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[22][3]++;
            cov_1mhwbdq9s().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[22][4]++;
            cov_1mhwbdq9s().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[22][5]++;
            cov_1mhwbdq9s().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1mhwbdq9s().b[23][0]++;
              cov_1mhwbdq9s().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1mhwbdq9s().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1mhwbdq9s().b[23][1]++;
            }
            cov_1mhwbdq9s().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1mhwbdq9s().b[26][0]++;
              cov_1mhwbdq9s().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1mhwbdq9s().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1mhwbdq9s().b[26][1]++;
            }
            cov_1mhwbdq9s().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1mhwbdq9s().b[28][0]++;
              cov_1mhwbdq9s().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1mhwbdq9s().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1mhwbdq9s().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1mhwbdq9s().b[28][1]++;
            }
            cov_1mhwbdq9s().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1mhwbdq9s().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1mhwbdq9s().b[30][0]++;
              cov_1mhwbdq9s().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1mhwbdq9s().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1mhwbdq9s().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1mhwbdq9s().b[30][1]++;
            }
            cov_1mhwbdq9s().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1mhwbdq9s().b[32][0]++;
              cov_1mhwbdq9s().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1mhwbdq9s().b[32][1]++;
            }
            cov_1mhwbdq9s().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1mhwbdq9s().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1mhwbdq9s().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().b[33][0]++;
      cov_1mhwbdq9s().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1mhwbdq9s().b[33][1]++;
    }
    cov_1mhwbdq9s().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[78]++,
/* istanbul ignore next */
(cov_1mhwbdq9s().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_1mhwbdq9s().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1mhwbdq9s().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[15]++;
  cov_1mhwbdq9s().s[79]++;
  return /* istanbul ignore next */(cov_1mhwbdq9s().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1mhwbdq9s().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1mhwbdq9s().s[81]++;
exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[82]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[83]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[84]++, require("@/lib/auth"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[85]++, require("@/lib/unified-api-error-handler"));
var prisma_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[86]++, __importDefault(require("@/lib/prisma")));
var assessmentScoring_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[87]++, require("@/lib/assessmentScoring"));
var suggestionService_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[88]++, require("@/lib/suggestionService"));
var logger_1 =
/* istanbul ignore next */
(cov_1mhwbdq9s().s[89]++, require("@/lib/logger"));
/* istanbul ignore next */
cov_1mhwbdq9s().s[90]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request, context) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[16]++;
  cov_1mhwbdq9s().s[91]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[17]++;
    var params, session, error, assessmentId, assessment, error, error, responseData, insights, careerSuggestions, enhancedCareerSuggestions, personalizedRecommendations, results;
    var _a;
    /* istanbul ignore next */
    cov_1mhwbdq9s().s[92]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().f[18]++;
      cov_1mhwbdq9s().s[93]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[38][0]++;
          cov_1mhwbdq9s().s[94]++;
          return [4 /*yield*/, context.params];
        case 1:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[38][1]++;
          cov_1mhwbdq9s().s[95]++;
          params = _b.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[96]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 2:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[38][2]++;
          cov_1mhwbdq9s().s[97]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[98]++;
          if (!(
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[41][0]++, (_a =
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[43][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[43][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[42][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[42][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[41][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[40][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1mhwbdq9s().b[40][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[39][0]++;
            cov_1mhwbdq9s().s[99]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[100]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[101]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1mhwbdq9s().b[39][1]++;
          }
          cov_1mhwbdq9s().s[102]++;
          assessmentId = params.id;
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[103]++;
          return [4 /*yield*/, prisma_1.default.assessment.findUnique({
            where: {
              id: assessmentId,
              userId: session.user.id // Ensure user can only access their own assessments
            },
            include: {
              responses: true
            }
          })];
        case 3:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[38][3]++;
          cov_1mhwbdq9s().s[104]++;
          assessment = _b.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[105]++;
          if (!assessment) {
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[44][0]++;
            cov_1mhwbdq9s().s[106]++;
            error = new Error('Assessment not found');
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[107]++;
            error.statusCode = 404;
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[108]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1mhwbdq9s().b[44][1]++;
          }
          cov_1mhwbdq9s().s[109]++;
          if (assessment.status !== 'COMPLETED') {
            /* istanbul ignore next */
            cov_1mhwbdq9s().b[45][0]++;
            cov_1mhwbdq9s().s[110]++;
            error = new Error('Assessment not completed');
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[111]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_1mhwbdq9s().s[112]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1mhwbdq9s().b[45][1]++;
          }
          cov_1mhwbdq9s().s[113]++;
          responseData = {};
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[114]++;
          assessment.responses.forEach(function (response) {
            /* istanbul ignore next */
            cov_1mhwbdq9s().f[19]++;
            cov_1mhwbdq9s().s[115]++;
            responseData[response.questionKey] = response.answerValue;
          });
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[116]++;
          return [4 /*yield*/, (0, assessmentScoring_1.generateAssessmentInsights)(responseData)];
        case 4:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[38][4]++;
          cov_1mhwbdq9s().s[117]++;
          insights = _b.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[118]++;
          return [4 /*yield*/, (0, suggestionService_1.getCareerPathSuggestions)(assessmentId)];
        case 5:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[38][5]++;
          cov_1mhwbdq9s().s[119]++;
          careerSuggestions = _b.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[120]++;
          enhancedCareerSuggestions = careerSuggestions.map(function (suggestion) {
            /* istanbul ignore next */
            cov_1mhwbdq9s().f[20]++;
            cov_1mhwbdq9s().s[121]++;
            return __assign(__assign({}, suggestion), {
              matchReason: generateMatchReason(suggestion, insights)
            });
          });
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[122]++;
          return [4 /*yield*/, getPersonalizedRecommendations(session.user.id, insights, enhancedCareerSuggestions)];
        case 6:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[38][6]++;
          cov_1mhwbdq9s().s[123]++;
          personalizedRecommendations = _b.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[124]++;
          results = {
            assessment: {
              id: assessment.id,
              status: assessment.status,
              completedAt: assessment.completedAt,
              currentStep: assessment.currentStep
            },
            insights: insights,
            careerSuggestions: enhancedCareerSuggestions,
            personalizedRecommendations: personalizedRecommendations
          };
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[125]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: results
          })];
      }
    });
  });
});
function generateMatchReason(suggestion, insights) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[21]++;
  var reasons =
  /* istanbul ignore next */
  (cov_1mhwbdq9s().s[126]++, []);
  /* istanbul ignore next */
  cov_1mhwbdq9s().s[127]++;
  if (insights.topSkills.some(function (skill) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[22]++;
    cov_1mhwbdq9s().s[128]++;
    return suggestion.careerPath.name.toLowerCase().includes(skill.toLowerCase());
  })) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().b[46][0]++;
    cov_1mhwbdq9s().s[129]++;
    reasons.push('Matches your key skills');
  } else
  /* istanbul ignore next */
  {
    cov_1mhwbdq9s().b[46][1]++;
  }
  cov_1mhwbdq9s().s[130]++;
  if (
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[48][0]++, insights.primaryMotivation) &&
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[48][1]++, suggestion.careerPath.overview.toLowerCase().includes(insights.primaryMotivation.toLowerCase()))) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().b[47][0]++;
    cov_1mhwbdq9s().s[131]++;
    reasons.push('Aligns with your motivation');
  } else
  /* istanbul ignore next */
  {
    cov_1mhwbdq9s().b[47][1]++;
  }
  cov_1mhwbdq9s().s[132]++;
  if (suggestion.score > 5) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().b[49][0]++;
    cov_1mhwbdq9s().s[133]++;
    reasons.push('High compatibility score');
  } else
  /* istanbul ignore next */
  {
    cov_1mhwbdq9s().b[49][1]++;
  }
  cov_1mhwbdq9s().s[134]++;
  return reasons.length > 0 ?
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[50][0]++, reasons.join(', ')) :
  /* istanbul ignore next */
  (cov_1mhwbdq9s().b[50][1]++, 'Based on your assessment responses');
}
function getPersonalizedRecommendations(userId, insights, careerSuggestions) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[23]++;
  cov_1mhwbdq9s().s[135]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[24]++;
    var userSkills, careerPathIds, learningResources, enhancedResources, skillGaps, nextSteps, error_1;
    /* istanbul ignore next */
    cov_1mhwbdq9s().s[136]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1mhwbdq9s().f[25]++;
      cov_1mhwbdq9s().s[137]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[51][0]++;
          cov_1mhwbdq9s().s[138]++;
          _a.trys.push([0, 3,, 4]);
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[139]++;
          return [4 /*yield*/, prisma_1.default.userSkillProgress.findMany({
            where: {
              userId: userId
            },
            include: {
              skill: true
            }
          })];
        case 1:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[51][1]++;
          cov_1mhwbdq9s().s[140]++;
          userSkills = _a.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[141]++;
          careerPathIds = careerSuggestions.map(function (s) {
            /* istanbul ignore next */
            cov_1mhwbdq9s().f[26]++;
            cov_1mhwbdq9s().s[142]++;
            return s.careerPath.id;
          });
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[143]++;
          return [4 /*yield*/, prisma_1.default.learningResource.findMany({
            where: {
              careerPaths: {
                some: {
                  id: {
                    in: careerPathIds
                  }
                }
              },
              isActive: true
            },
            include: {
              ratings: {
                select: {
                  rating: true
                }
              }
            },
            take: 15,
            orderBy: {
              createdAt: 'desc'
            }
          })];
        case 2:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[51][2]++;
          cov_1mhwbdq9s().s[144]++;
          learningResources = _a.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[145]++;
          enhancedResources = learningResources.map(function (resource) {
            /* istanbul ignore next */
            cov_1mhwbdq9s().f[27]++;
            cov_1mhwbdq9s().s[146]++;
            return __assign(__assign({}, resource), {
              averageRating: resource.ratings.length > 0 ?
              /* istanbul ignore next */
              (cov_1mhwbdq9s().b[52][0]++, resource.ratings.reduce(function (sum, r) {
                /* istanbul ignore next */
                cov_1mhwbdq9s().f[28]++;
                cov_1mhwbdq9s().s[147]++;
                return sum + r.rating;
              }, 0) / resource.ratings.length) :
              /* istanbul ignore next */
              (cov_1mhwbdq9s().b[52][1]++, 0),
              totalRatings: resource.ratings.length
            });
          }).sort(function (a, b) {
            /* istanbul ignore next */
            cov_1mhwbdq9s().f[29]++;
            cov_1mhwbdq9s().s[148]++;
            return b.averageRating - a.averageRating;
          });
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[149]++;
          skillGaps = generateSkillGaps(insights, userSkills);
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[150]++;
          nextSteps = generateNextSteps(insights, careerSuggestions);
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[151]++;
          return [2 /*return*/, {
            learningResources: enhancedResources,
            skillGaps: skillGaps,
            nextSteps: nextSteps
          }];
        case 3:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[51][3]++;
          cov_1mhwbdq9s().s[152]++;
          error_1 = _a.sent();
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[153]++;
          logger_1.log.error('Error getting personalized recommendations', error_1, {
            component: 'assessment_results_api',
            action: 'get_personalized_recommendations',
            userId: userId
          });
          /* istanbul ignore next */
          cov_1mhwbdq9s().s[154]++;
          return [2 /*return*/, null];
        case 4:
          /* istanbul ignore next */
          cov_1mhwbdq9s().b[51][4]++;
          cov_1mhwbdq9s().s[155]++;
          return [2 /*return*/];
      }
    });
  });
}
function generateSkillGaps(insights, userSkills) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[30]++;
  // Enhanced skill gap analysis based on assessment insights
  var recommendedSkills =
  /* istanbul ignore next */
  (cov_1mhwbdq9s().s[156]++, insights.topSkills);
  var currentSkills =
  /* istanbul ignore next */
  (cov_1mhwbdq9s().s[157]++, userSkills.map(function (us) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[31]++;
    cov_1mhwbdq9s().s[158]++;
    return us.skill.name.toLowerCase();
  }));
  /* istanbul ignore next */
  cov_1mhwbdq9s().s[159]++;
  return recommendedSkills.filter(function (skill) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[32]++;
    cov_1mhwbdq9s().s[160]++;
    return !currentSkills.includes(skill.toLowerCase());
  }).map(function (skill, index) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().f[33]++;
    // Determine priority based on skill importance and user readiness
    var priority =
    /* istanbul ignore next */
    (cov_1mhwbdq9s().s[161]++, index < 2 ?
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[53][0]++, 'high') :
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[53][1]++, index < 4 ?
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[54][0]++, 'medium') :
    /* istanbul ignore next */
    (cov_1mhwbdq9s().b[54][1]++, 'low')));
    // Estimate current and target levels
    var currentLevel =
    /* istanbul ignore next */
    (cov_1mhwbdq9s().s[162]++, Math.floor(Math.random() * 30) + 10); // 10-40%
    var targetLevel =
    /* istanbul ignore next */
    (cov_1mhwbdq9s().s[163]++, Math.floor(Math.random() * 20) + 70); // 70-90%
    // Estimate time based on priority and skill complexity
    var timeEstimates =
    /* istanbul ignore next */
    (cov_1mhwbdq9s().s[164]++, {
      high: ['2-3 months', '3-4 months', '4-6 months'],
      medium: ['3-6 months', '6-9 months', '6-12 months'],
      low: ['6-12 months', '9-15 months', '12-18 months']
    });
    /* istanbul ignore next */
    cov_1mhwbdq9s().s[165]++;
    return {
      skill: skill,
      priority: priority,
      reason: priority === 'high' ?
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[55][0]++, 'Critical for your target career path') :
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[55][1]++, priority === 'medium' ?
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[56][0]++, 'Important for career advancement') :
      /* istanbul ignore next */
      (cov_1mhwbdq9s().b[56][1]++, 'Valuable for long-term growth')),
      currentLevel: currentLevel,
      targetLevel: targetLevel,
      estimatedTime: timeEstimates[priority][Math.floor(Math.random() * 3)]
    };
  });
}
function generateNextSteps(insights, careerSuggestions) {
  /* istanbul ignore next */
  cov_1mhwbdq9s().f[34]++;
  var steps =
  /* istanbul ignore next */
  (cov_1mhwbdq9s().s[166]++, []);
  /* istanbul ignore next */
  cov_1mhwbdq9s().s[167]++;
  if (insights.scores.readinessScore < 60) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().b[57][0]++;
    cov_1mhwbdq9s().s[168]++;
    steps.push('Focus on building financial stability and confidence');
  } else
  /* istanbul ignore next */
  {
    cov_1mhwbdq9s().b[57][1]++;
  }
  cov_1mhwbdq9s().s[169]++;
  if (insights.scores.skillsConfidence < 70) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().b[58][0]++;
    cov_1mhwbdq9s().s[170]++;
    steps.push('Develop and validate your key professional skills');
  } else
  /* istanbul ignore next */
  {
    cov_1mhwbdq9s().b[58][1]++;
  }
  cov_1mhwbdq9s().s[171]++;
  if (careerSuggestions.length > 0) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().b[59][0]++;
    cov_1mhwbdq9s().s[172]++;
    steps.push("Explore the ".concat(careerSuggestions[0].careerPath.name, " career path in detail"));
  } else
  /* istanbul ignore next */
  {
    cov_1mhwbdq9s().b[59][1]++;
  }
  cov_1mhwbdq9s().s[173]++;
  if (insights.biggestObstacles.includes('unclear_direction')) {
    /* istanbul ignore next */
    cov_1mhwbdq9s().b[60][0]++;
    cov_1mhwbdq9s().s[174]++;
    steps.push('Schedule informational interviews in your areas of interest');
  } else
  /* istanbul ignore next */
  {
    cov_1mhwbdq9s().b[60][1]++;
  }
  cov_1mhwbdq9s().s[175]++;
  return steps;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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