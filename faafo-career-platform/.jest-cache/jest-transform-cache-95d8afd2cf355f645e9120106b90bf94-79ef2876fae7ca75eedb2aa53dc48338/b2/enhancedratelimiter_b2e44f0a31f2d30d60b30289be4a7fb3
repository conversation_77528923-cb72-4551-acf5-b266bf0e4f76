6f1d3fe9cd6de26a96cea6c1b759c689
"use strict";

/**
 * Enhanced Rate Limiter with User-Based and IP-Based Limiting
 * Addresses shared network limitations and provides more granular control
 */
/* istanbul ignore next */
function cov_2etuhhs2ll() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhanced-rate-limiter.ts";
  var hash = "9166bb5c711b2d1f7c6ab6cc9b10ebdc4e3c9810";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhanced-rate-limiter.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 16
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "2": {
        start: {
          line: 7,
          column: 91
        },
        end: {
          line: 7,
          column: 106
        }
      },
      "3": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 13,
          column: 7
        }
      },
      "4": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "5": {
        start: {
          line: 9,
          column: 42
        },
        end: {
          line: 9,
          column: 70
        }
      },
      "6": {
        start: {
          line: 9,
          column: 85
        },
        end: {
          line: 9,
          column: 95
        }
      },
      "7": {
        start: {
          line: 10,
          column: 35
        },
        end: {
          line: 10,
          column: 100
        }
      },
      "8": {
        start: {
          line: 10,
          column: 41
        },
        end: {
          line: 10,
          column: 73
        }
      },
      "9": {
        start: {
          line: 10,
          column: 88
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "10": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 116
        }
      },
      "11": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 78
        }
      },
      "12": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "13": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 104
        }
      },
      "14": {
        start: {
          line: 16,
          column: 43
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "15": {
        start: {
          line: 16,
          column: 57
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "16": {
        start: {
          line: 16,
          column: 69
        },
        end: {
          line: 16,
          column: 81
        }
      },
      "17": {
        start: {
          line: 16,
          column: 119
        },
        end: {
          line: 16,
          column: 196
        }
      },
      "18": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 160
        }
      },
      "19": {
        start: {
          line: 17,
          column: 141
        },
        end: {
          line: 17,
          column: 153
        }
      },
      "20": {
        start: {
          line: 18,
          column: 23
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "21": {
        start: {
          line: 18,
          column: 45
        },
        end: {
          line: 18,
          column: 65
        }
      },
      "22": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "23": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "24": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "25": {
        start: {
          line: 21,
          column: 50
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "26": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "27": {
        start: {
          line: 22,
          column: 160
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "28": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "29": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "30": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 36,
          column: 13
        }
      },
      "31": {
        start: {
          line: 25,
          column: 32
        },
        end: {
          line: 25,
          column: 39
        }
      },
      "32": {
        start: {
          line: 25,
          column: 40
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "33": {
        start: {
          line: 26,
          column: 24
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "34": {
        start: {
          line: 26,
          column: 35
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "35": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "36": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 45
        }
      },
      "37": {
        start: {
          line: 27,
          column: 46
        },
        end: {
          line: 27,
          column: 55
        }
      },
      "38": {
        start: {
          line: 27,
          column: 56
        },
        end: {
          line: 27,
          column: 65
        }
      },
      "39": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "40": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "41": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "42": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 128
        }
      },
      "43": {
        start: {
          line: 30,
          column: 110
        },
        end: {
          line: 30,
          column: 116
        }
      },
      "44": {
        start: {
          line: 30,
          column: 117
        },
        end: {
          line: 30,
          column: 126
        }
      },
      "45": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 106
        }
      },
      "46": {
        start: {
          line: 31,
          column: 81
        },
        end: {
          line: 31,
          column: 97
        }
      },
      "47": {
        start: {
          line: 31,
          column: 98
        },
        end: {
          line: 31,
          column: 104
        }
      },
      "48": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 89
        }
      },
      "49": {
        start: {
          line: 32,
          column: 57
        },
        end: {
          line: 32,
          column: 72
        }
      },
      "50": {
        start: {
          line: 32,
          column: 73
        },
        end: {
          line: 32,
          column: 80
        }
      },
      "51": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "52": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "53": {
        start: {
          line: 33,
          column: 47
        },
        end: {
          line: 33,
          column: 62
        }
      },
      "54": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "55": {
        start: {
          line: 33,
          column: 79
        },
        end: {
          line: 33,
          column: 85
        }
      },
      "56": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "57": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "58": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 33
        }
      },
      "59": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 43
        }
      },
      "60": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "61": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "62": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 41
        }
      },
      "63": {
        start: {
          line: 38,
          column: 54
        },
        end: {
          line: 38,
          column: 64
        }
      },
      "64": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "65": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "66": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "67": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 62
        }
      },
      "68": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 68
        }
      },
      "69": {
        start: {
          line: 44,
          column: 18
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "70": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 30
        }
      },
      "71": {
        start: {
          line: 46,
          column: 41
        },
        end: {
          line: 274,
          column: 3
        }
      },
      "72": {
        start: {
          line: 48,
          column: 20
        },
        end: {
          line: 48,
          column: 24
        }
      },
      "73": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 29
        }
      },
      "74": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 33
        }
      },
      "75": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "76": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 36
        }
      },
      "77": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 48
        }
      },
      "78": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 76
        }
      },
      "79": {
        start: {
          line: 55,
          column: 34
        },
        end: {
          line: 55,
          column: 57
        }
      },
      "80": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 87,
          column: 6
        }
      },
      "81": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 86,
          column: 11
        }
      },
      "82": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 85,
          column: 15
        }
      },
      "83": {
        start: {
          line: 62,
          column: 16
        },
        end: {
          line: 84,
          column: 17
        }
      },
      "84": {
        start: {
          line: 63,
          column: 28
        },
        end: {
          line: 63,
          column: 104
        }
      },
      "85": {
        start: {
          line: 65,
          column: 24
        },
        end: {
          line: 65,
          column: 44
        }
      },
      "86": {
        start: {
          line: 66,
          column: 24
        },
        end: {
          line: 66,
          column: 55
        }
      },
      "87": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 67,
          column: 146
        }
      },
      "88": {
        start: {
          line: 68,
          column: 24
        },
        end: {
          line: 68,
          column: 83
        }
      },
      "89": {
        start: {
          line: 69,
          column: 24
        },
        end: {
          line: 69,
          column: 103
        }
      },
      "90": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 70,
          column: 84
        }
      },
      "91": {
        start: {
          line: 71,
          column: 24
        },
        end: {
          line: 73,
          column: 25
        }
      },
      "92": {
        start: {
          line: 72,
          column: 28
        },
        end: {
          line: 72,
          column: 63
        }
      },
      "93": {
        start: {
          line: 75,
          column: 24
        },
        end: {
          line: 80,
          column: 25
        }
      },
      "94": {
        start: {
          line: 76,
          column: 28
        },
        end: {
          line: 76,
          column: 86
        }
      },
      "95": {
        start: {
          line: 77,
          column: 28
        },
        end: {
          line: 79,
          column: 29
        }
      },
      "96": {
        start: {
          line: 78,
          column: 32
        },
        end: {
          line: 78,
          column: 66
        }
      },
      "97": {
        start: {
          line: 81,
          column: 24
        },
        end: {
          line: 81,
          column: 84
        }
      },
      "98": {
        start: {
          line: 83,
          column: 24
        },
        end: {
          line: 83,
          column: 56
        }
      },
      "99": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 119,
          column: 6
        }
      },
      "100": {
        start: {
          line: 89,
          column: 18
        },
        end: {
          line: 89,
          column: 40
        }
      },
      "101": {
        start: {
          line: 90,
          column: 18
        },
        end: {
          line: 90,
          column: 28
        }
      },
      "102": {
        start: {
          line: 91,
          column: 20
        },
        end: {
          line: 91,
          column: 43
        }
      },
      "103": {
        start: {
          line: 92,
          column: 23
        },
        end: {
          line: 92,
          column: 47
        }
      },
      "104": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 95,
          column: 38
        }
      },
      "105": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 104,
          column: 9
        }
      },
      "106": {
        start: {
          line: 97,
          column: 12
        },
        end: {
          line: 102,
          column: 14
        }
      },
      "107": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 103,
          column: 43
        }
      },
      "108": {
        start: {
          line: 105,
          column: 22
        },
        end: {
          line: 105,
          column: 41
        }
      },
      "109": {
        start: {
          line: 106,
          column: 24
        },
        end: {
          line: 106,
          column: 60
        }
      },
      "110": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 109,
          column: 9
        }
      },
      "111": {
        start: {
          line: 108,
          column: 12
        },
        end: {
          line: 108,
          column: 26
        }
      },
      "112": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 118,
          column: 10
        }
      },
      "113": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 158,
          column: 6
        }
      },
      "114": {
        start: {
          line: 121,
          column: 18
        },
        end: {
          line: 121,
          column: 34
        }
      },
      "115": {
        start: {
          line: 122,
          column: 18
        },
        end: {
          line: 122,
          column: 28
        }
      },
      "116": {
        start: {
          line: 123,
          column: 20
        },
        end: {
          line: 123,
          column: 41
        }
      },
      "117": {
        start: {
          line: 124,
          column: 23
        },
        end: {
          line: 124,
          column: 45
        }
      },
      "118": {
        start: {
          line: 126,
          column: 24
        },
        end: {
          line: 126,
          column: 46
        }
      },
      "119": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 130,
          column: 9
        }
      },
      "120": {
        start: {
          line: 129,
          column: 12
        },
        end: {
          line: 129,
          column: 84
        }
      },
      "121": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 134,
          column: 9
        }
      },
      "122": {
        start: {
          line: 133,
          column: 12
        },
        end: {
          line: 133,
          column: 52
        }
      },
      "123": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 143,
          column: 9
        }
      },
      "124": {
        start: {
          line: 136,
          column: 12
        },
        end: {
          line: 141,
          column: 14
        }
      },
      "125": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 41
        }
      },
      "126": {
        start: {
          line: 144,
          column: 22
        },
        end: {
          line: 144,
          column: 45
        }
      },
      "127": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 64
        }
      },
      "128": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 148,
          column: 9
        }
      },
      "129": {
        start: {
          line: 147,
          column: 12
        },
        end: {
          line: 147,
          column: 26
        }
      },
      "130": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 157,
          column: 10
        }
      },
      "131": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 187,
          column: 6
        }
      },
      "132": {
        start: {
          line: 160,
          column: 18
        },
        end: {
          line: 160,
          column: 80
        }
      },
      "133": {
        start: {
          line: 161,
          column: 18
        },
        end: {
          line: 161,
          column: 28
        }
      },
      "134": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 162,
          column: 44
        }
      },
      "135": {
        start: {
          line: 163,
          column: 23
        },
        end: {
          line: 163,
          column: 48
        }
      },
      "136": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 45
        }
      },
      "137": {
        start: {
          line: 165,
          column: 8
        },
        end: {
          line: 172,
          column: 9
        }
      },
      "138": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 170,
          column: 14
        }
      },
      "139": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 171,
          column: 44
        }
      },
      "140": {
        start: {
          line: 173,
          column: 22
        },
        end: {
          line: 173,
          column: 41
        }
      },
      "141": {
        start: {
          line: 174,
          column: 24
        },
        end: {
          line: 174,
          column: 60
        }
      },
      "142": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 177,
          column: 9
        }
      },
      "143": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 176,
          column: 26
        }
      },
      "144": {
        start: {
          line: 178,
          column: 8
        },
        end: {
          line: 186,
          column: 10
        }
      },
      "145": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 204,
          column: 6
        }
      },
      "146": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 190,
          column: 25
        }
      },
      "147": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 25
        }
      },
      "148": {
        start: {
          line: 191,
          column: 25
        },
        end: {
          line: 191,
          column: 47
        }
      },
      "149": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 194,
          column: 9
        }
      },
      "150": {
        start: {
          line: 193,
          column: 12
        },
        end: {
          line: 193,
          column: 67
        }
      },
      "151": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 195,
          column: 63
        }
      },
      "152": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 196,
          column: 26
        }
      },
      "153": {
        start: {
          line: 198,
          column: 23
        },
        end: {
          line: 198,
          column: 37
        }
      },
      "154": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 202,
          column: 9
        }
      },
      "155": {
        start: {
          line: 201,
          column: 12
        },
        end: {
          line: 201,
          column: 26
        }
      },
      "156": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 203,
          column: 24
        }
      },
      "157": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 218,
          column: 6
        }
      },
      "158": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 217,
          column: 9
        }
      },
      "159": {
        start: {
          line: 210,
          column: 24
        },
        end: {
          line: 210,
          column: 37
        }
      },
      "160": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 47
        }
      },
      "161": {
        start: {
          line: 215,
          column: 24
        },
        end: {
          line: 215,
          column: 37
        }
      },
      "162": {
        start: {
          line: 216,
          column: 12
        },
        end: {
          line: 216,
          column: 47
        }
      },
      "163": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 235,
          column: 6
        }
      },
      "164": {
        start: {
          line: 221,
          column: 24
        },
        end: {
          line: 221,
          column: 62
        }
      },
      "165": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 224,
          column: 9
        }
      },
      "166": {
        start: {
          line: 223,
          column: 12
        },
        end: {
          line: 223,
          column: 50
        }
      },
      "167": {
        start: {
          line: 225,
          column: 21
        },
        end: {
          line: 225,
          column: 53
        }
      },
      "168": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 228,
          column: 9
        }
      },
      "169": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 227,
          column: 26
        }
      },
      "170": {
        start: {
          line: 229,
          column: 29
        },
        end: {
          line: 229,
          column: 68
        }
      },
      "171": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 232,
          column: 9
        }
      },
      "172": {
        start: {
          line: 231,
          column: 12
        },
        end: {
          line: 231,
          column: 34
        }
      },
      "173": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 27
        }
      },
      "174": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 243,
          column: 6
        }
      },
      "175": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 242,
          column: 10
        }
      },
      "176": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 272,
          column: 6
        }
      },
      "177": {
        start: {
          line: 245,
          column: 20
        },
        end: {
          line: 245,
          column: 24
        }
      },
      "178": {
        start: {
          line: 246,
          column: 18
        },
        end: {
          line: 246,
          column: 28
        }
      },
      "179": {
        start: {
          line: 248,
          column: 29
        },
        end: {
          line: 248,
          column: 31
        }
      },
      "180": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 253,
          column: 11
        }
      },
      "181": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 252,
          column: 13
        }
      },
      "182": {
        start: {
          line: 251,
          column: 16
        },
        end: {
          line: 251,
          column: 41
        }
      },
      "183": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 85
        }
      },
      "184": {
        start: {
          line: 254,
          column: 48
        },
        end: {
          line: 254,
          column: 81
        }
      },
      "185": {
        start: {
          line: 256,
          column: 31
        },
        end: {
          line: 256,
          column: 33
        }
      },
      "186": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 261,
          column: 11
        }
      },
      "187": {
        start: {
          line: 258,
          column: 12
        },
        end: {
          line: 260,
          column: 13
        }
      },
      "188": {
        start: {
          line: 259,
          column: 16
        },
        end: {
          line: 259,
          column: 43
        }
      },
      "189": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 262,
          column: 89
        }
      },
      "190": {
        start: {
          line: 262,
          column: 50
        },
        end: {
          line: 262,
          column: 85
        }
      },
      "191": {
        start: {
          line: 264,
          column: 32
        },
        end: {
          line: 264,
          column: 34
        }
      },
      "192": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 269,
          column: 11
        }
      },
      "193": {
        start: {
          line: 266,
          column: 12
        },
        end: {
          line: 268,
          column: 13
        }
      },
      "194": {
        start: {
          line: 267,
          column: 16
        },
        end: {
          line: 267,
          column: 44
        }
      },
      "195": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 270,
          column: 91
        }
      },
      "196": {
        start: {
          line: 270,
          column: 51
        },
        end: {
          line: 270,
          column: 87
        }
      },
      "197": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 271,
          column: 182
        }
      },
      "198": {
        start: {
          line: 273,
          column: 4
        },
        end: {
          line: 273,
          column: 31
        }
      },
      "199": {
        start: {
          line: 275,
          column: 0
        },
        end: {
          line: 275,
          column: 50
        }
      },
      "200": {
        start: {
          line: 277,
          column: 0
        },
        end: {
          line: 305,
          column: 2
        }
      },
      "201": {
        start: {
          line: 306,
          column: 0
        },
        end: {
          line: 306,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 6,
            column: 45
          }
        },
        loc: {
          start: {
            line: 6,
            column: 89
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 7,
            column: 13
          },
          end: {
            line: 7,
            column: 18
          }
        },
        loc: {
          start: {
            line: 7,
            column: 26
          },
          end: {
            line: 7,
            column: 112
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 71
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 7,
            column: 108
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 63
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 8
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 26
          }
        },
        loc: {
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 99
          }
        },
        line: 9
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 33
          },
          end: {
            line: 10,
            column: 102
          }
        },
        line: 10
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 21
          }
        },
        loc: {
          start: {
            line: 11,
            column: 30
          },
          end: {
            line: 11,
            column: 118
          }
        },
        line: 11
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 49
          }
        },
        loc: {
          start: {
            line: 15,
            column: 73
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 15
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 16,
            column: 30
          },
          end: {
            line: 16,
            column: 31
          }
        },
        loc: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 83
          }
        },
        line: 16
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 17,
            column: 128
          },
          end: {
            line: 17,
            column: 129
          }
        },
        loc: {
          start: {
            line: 17,
            column: 139
          },
          end: {
            line: 17,
            column: 155
          }
        },
        line: 17
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 70
          }
        },
        line: 18
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 31
          }
        },
        loc: {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 67
          }
        },
        line: 18
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 19
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 46,
            column: 41
          },
          end: {
            line: 46,
            column: 42
          }
        },
        loc: {
          start: {
            line: 46,
            column: 53
          },
          end: {
            line: 274,
            column: 1
          }
        },
        line: 46
      },
      "14": {
        name: "EnhancedRateLimiter",
        decl: {
          start: {
            line: 47,
            column: 13
          },
          end: {
            line: 47,
            column: 32
          }
        },
        loc: {
          start: {
            line: 47,
            column: 41
          },
          end: {
            line: 56,
            column: 5
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 55,
            column: 20
          },
          end: {
            line: 55,
            column: 21
          }
        },
        loc: {
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 55,
            column: 59
          }
        },
        line: 55
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 57,
            column: 47
          },
          end: {
            line: 57,
            column: 48
          }
        },
        loc: {
          start: {
            line: 57,
            column: 66
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 57
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 58,
            column: 48
          },
          end: {
            line: 58,
            column: 49
          }
        },
        loc: {
          start: {
            line: 58,
            column: 60
          },
          end: {
            line: 86,
            column: 9
          }
        },
        line: 58
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 61,
            column: 37
          },
          end: {
            line: 61,
            column: 38
          }
        },
        loc: {
          start: {
            line: 61,
            column: 51
          },
          end: {
            line: 85,
            column: 13
          }
        },
        line: 61
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 88,
            column: 51
          },
          end: {
            line: 88,
            column: 52
          }
        },
        loc: {
          start: {
            line: 88,
            column: 86
          },
          end: {
            line: 119,
            column: 5
          }
        },
        line: 88
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 120,
            column: 49
          },
          end: {
            line: 120,
            column: 50
          }
        },
        loc: {
          start: {
            line: 120,
            column: 97
          },
          end: {
            line: 158,
            column: 5
          }
        },
        line: 120
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 159,
            column: 52
          },
          end: {
            line: 159,
            column: 53
          }
        },
        loc: {
          start: {
            line: 159,
            column: 74
          },
          end: {
            line: 187,
            column: 5
          }
        },
        line: 159
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 188,
            column: 56
          },
          end: {
            line: 188,
            column: 57
          }
        },
        loc: {
          start: {
            line: 188,
            column: 89
          },
          end: {
            line: 204,
            column: 5
          }
        },
        line: 188
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 205,
            column: 50
          },
          end: {
            line: 205,
            column: 51
          }
        },
        loc: {
          start: {
            line: 205,
            column: 64
          },
          end: {
            line: 218,
            column: 5
          }
        },
        line: 205
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 219,
            column: 48
          },
          end: {
            line: 219,
            column: 49
          }
        },
        loc: {
          start: {
            line: 219,
            column: 67
          },
          end: {
            line: 235,
            column: 5
          }
        },
        line: 219
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 236,
            column: 52
          },
          end: {
            line: 236,
            column: 53
          }
        },
        loc: {
          start: {
            line: 236,
            column: 97
          },
          end: {
            line: 243,
            column: 5
          }
        },
        line: 236
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 244,
            column: 44
          },
          end: {
            line: 244,
            column: 45
          }
        },
        loc: {
          start: {
            line: 244,
            column: 56
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 244
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 249,
            column: 29
          },
          end: {
            line: 249,
            column: 30
          }
        },
        loc: {
          start: {
            line: 249,
            column: 51
          },
          end: {
            line: 253,
            column: 9
          }
        },
        line: 249
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 254,
            column: 31
          },
          end: {
            line: 254,
            column: 32
          }
        },
        loc: {
          start: {
            line: 254,
            column: 46
          },
          end: {
            line: 254,
            column: 83
          }
        },
        line: 254
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 257,
            column: 31
          },
          end: {
            line: 257,
            column: 32
          }
        },
        loc: {
          start: {
            line: 257,
            column: 53
          },
          end: {
            line: 261,
            column: 9
          }
        },
        line: 257
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 262,
            column: 33
          },
          end: {
            line: 262,
            column: 34
          }
        },
        loc: {
          start: {
            line: 262,
            column: 48
          },
          end: {
            line: 262,
            column: 87
          }
        },
        line: 262
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 265,
            column: 32
          },
          end: {
            line: 265,
            column: 33
          }
        },
        loc: {
          start: {
            line: 265,
            column: 54
          },
          end: {
            line: 269,
            column: 9
          }
        },
        line: 265
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 270,
            column: 34
          },
          end: {
            line: 270,
            column: 35
          }
        },
        loc: {
          start: {
            line: 270,
            column: 49
          },
          end: {
            line: 270,
            column: 89
          }
        },
        line: 270
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 21
          }
        }, {
          start: {
            line: 6,
            column: 25
          },
          end: {
            line: 6,
            column: 39
          }
        }, {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 56
          },
          end: {
            line: 7,
            column: 61
          }
        }, {
          start: {
            line: 7,
            column: 64
          },
          end: {
            line: 7,
            column: 109
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 17
          }
        }, {
          start: {
            line: 8,
            column: 22
          },
          end: {
            line: 8,
            column: 33
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 11,
            column: 32
          },
          end: {
            line: 11,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 11,
            column: 46
          },
          end: {
            line: 11,
            column: 67
          }
        }, {
          start: {
            line: 11,
            column: 70
          },
          end: {
            line: 11,
            column: 115
          }
        }],
        line: 11
      },
      "4": {
        loc: {
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 61
          }
        }, {
          start: {
            line: 12,
            column: 65
          },
          end: {
            line: 12,
            column: 67
          }
        }],
        line: 12
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 41,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 23
          }
        }, {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 43
          }
        }, {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 41,
            column: 1
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "7": {
        loc: {
          start: {
            line: 16,
            column: 134
          },
          end: {
            line: 16,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 167
          },
          end: {
            line: 16,
            column: 175
          }
        }, {
          start: {
            line: 16,
            column: 178
          },
          end: {
            line: 16,
            column: 184
          }
        }],
        line: 16
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 102
          }
        }, {
          start: {
            line: 17,
            column: 107
          },
          end: {
            line: 17,
            column: 155
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "10": {
        loc: {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 16
          }
        }, {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 43
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 125
          }
        }, {
          start: {
            line: 22,
            column: 130
          },
          end: {
            line: 22,
            column: 158
          }
        }],
        line: 22
      },
      "14": {
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 22,
            column: 56
          }
        }, {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "15": {
        loc: {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        }, {
          start: {
            line: 22,
            column: 119
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "16": {
        loc: {
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 77
          }
        }, {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "17": {
        loc: {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 83
          },
          end: {
            line: 22,
            column: 98
          }
        }, {
          start: {
            line: 22,
            column: 103
          },
          end: {
            line: 22,
            column: 112
          }
        }],
        line: 22
      },
      "18": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "19": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 23
          }
        }, {
          start: {
            line: 25,
            column: 24
          },
          end: {
            line: 25,
            column: 46
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 72
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 65
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 24
      },
      "20": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 74
          }
        }, {
          start: {
            line: 30,
            column: 79
          },
          end: {
            line: 30,
            column: 90
          }
        }, {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 30,
            column: 105
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 73
          }
        }],
        line: 30
      },
      "23": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "24": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 35
          }
        }, {
          start: {
            line: 31,
            column: 40
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: 31,
            column: 47
          },
          end: {
            line: 31,
            column: 59
          }
        }, {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 31,
            column: 75
          }
        }],
        line: 31
      },
      "25": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "26": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 39
          },
          end: {
            line: 32,
            column: 53
          }
        }],
        line: 32
      },
      "27": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "28": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 25
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "29": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "30": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "31": {
        loc: {
          start: {
            line: 39,
            column: 52
          },
          end: {
            line: 39,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 60
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 68
          },
          end: {
            line: 39,
            column: 74
          }
        }],
        line: 39
      },
      "32": {
        loc: {
          start: {
            line: 62,
            column: 16
          },
          end: {
            line: 84,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 104
          }
        }, {
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 83,
            column: 56
          }
        }],
        line: 62
      },
      "33": {
        loc: {
          start: {
            line: 67,
            column: 33
          },
          end: {
            line: 67,
            column: 145
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 131
          },
          end: {
            line: 67,
            column: 137
          }
        }, {
          start: {
            line: 67,
            column: 140
          },
          end: {
            line: 67,
            column: 145
          }
        }],
        line: 67
      },
      "34": {
        loc: {
          start: {
            line: 67,
            column: 33
          },
          end: {
            line: 67,
            column: 128
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 33
          },
          end: {
            line: 67,
            column: 111
          }
        }, {
          start: {
            line: 67,
            column: 115
          },
          end: {
            line: 67,
            column: 128
          }
        }],
        line: 67
      },
      "35": {
        loc: {
          start: {
            line: 67,
            column: 39
          },
          end: {
            line: 67,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 80
          },
          end: {
            line: 67,
            column: 86
          }
        }, {
          start: {
            line: 67,
            column: 89
          },
          end: {
            line: 67,
            column: 101
          }
        }],
        line: 67
      },
      "36": {
        loc: {
          start: {
            line: 67,
            column: 39
          },
          end: {
            line: 67,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 39
          },
          end: {
            line: 67,
            column: 55
          }
        }, {
          start: {
            line: 67,
            column: 59
          },
          end: {
            line: 67,
            column: 77
          }
        }],
        line: 67
      },
      "37": {
        loc: {
          start: {
            line: 68,
            column: 36
          },
          end: {
            line: 68,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 36
          },
          end: {
            line: 68,
            column: 69
          }
        }, {
          start: {
            line: 68,
            column: 73
          },
          end: {
            line: 68,
            column: 82
          }
        }],
        line: 68
      },
      "38": {
        loc: {
          start: {
            line: 69,
            column: 71
          },
          end: {
            line: 69,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 71
          },
          end: {
            line: 69,
            column: 77
          }
        }, {
          start: {
            line: 69,
            column: 81
          },
          end: {
            line: 69,
            column: 90
          }
        }],
        line: 69
      },
      "39": {
        loc: {
          start: {
            line: 70,
            column: 63
          },
          end: {
            line: 70,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 63
          },
          end: {
            line: 70,
            column: 69
          }
        }, {
          start: {
            line: 70,
            column: 73
          },
          end: {
            line: 70,
            column: 82
          }
        }],
        line: 70
      },
      "40": {
        loc: {
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 73,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 73,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "41": {
        loc: {
          start: {
            line: 75,
            column: 24
          },
          end: {
            line: 80,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 24
          },
          end: {
            line: 80,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "42": {
        loc: {
          start: {
            line: 77,
            column: 28
          },
          end: {
            line: 79,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 28
          },
          end: {
            line: 79,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "43": {
        loc: {
          start: {
            line: 93,
            column: 20
          },
          end: {
            line: 95,
            column: 38
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 94,
            column: 14
          },
          end: {
            line: 94,
            column: 88
          }
        }, {
          start: {
            line: 95,
            column: 14
          },
          end: {
            line: 95,
            column: 38
          }
        }],
        line: 93
      },
      "44": {
        loc: {
          start: {
            line: 96,
            column: 8
          },
          end: {
            line: 104,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 8
          },
          end: {
            line: 104,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "45": {
        loc: {
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 96,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 96,
            column: 18
          }
        }, {
          start: {
            line: 96,
            column: 22
          },
          end: {
            line: 96,
            column: 43
          }
        }],
        line: 96
      },
      "46": {
        loc: {
          start: {
            line: 107,
            column: 8
          },
          end: {
            line: 109,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 8
          },
          end: {
            line: 109,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "47": {
        loc: {
          start: {
            line: 115,
            column: 24
          },
          end: {
            line: 115,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 115,
            column: 34
          },
          end: {
            line: 115,
            column: 43
          }
        }, {
          start: {
            line: 115,
            column: 46
          },
          end: {
            line: 115,
            column: 87
          }
        }],
        line: 115
      },
      "48": {
        loc: {
          start: {
            line: 117,
            column: 23
          },
          end: {
            line: 117,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 117,
            column: 41
          },
          end: {
            line: 117,
            column: 57
          }
        }, {
          start: {
            line: 117,
            column: 60
          },
          end: {
            line: 117,
            column: 66
          }
        }],
        line: 117
      },
      "49": {
        loc: {
          start: {
            line: 128,
            column: 8
          },
          end: {
            line: 130,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 8
          },
          end: {
            line: 130,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "50": {
        loc: {
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 128,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 128,
            column: 27
          }
        }, {
          start: {
            line: 128,
            column: 31
          },
          end: {
            line: 128,
            column: 46
          }
        }],
        line: 128
      },
      "51": {
        loc: {
          start: {
            line: 132,
            column: 8
          },
          end: {
            line: 134,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 132,
            column: 8
          },
          end: {
            line: 134,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 132
      },
      "52": {
        loc: {
          start: {
            line: 132,
            column: 12
          },
          end: {
            line: 132,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 132,
            column: 12
          },
          end: {
            line: 132,
            column: 28
          }
        }, {
          start: {
            line: 132,
            column: 32
          },
          end: {
            line: 132,
            column: 47
          }
        }],
        line: 132
      },
      "53": {
        loc: {
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 143,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 143,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "54": {
        loc: {
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 18
          }
        }, {
          start: {
            line: 135,
            column: 22
          },
          end: {
            line: 135,
            column: 43
          }
        }],
        line: 135
      },
      "55": {
        loc: {
          start: {
            line: 146,
            column: 8
          },
          end: {
            line: 148,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 8
          },
          end: {
            line: 148,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 146
      },
      "56": {
        loc: {
          start: {
            line: 154,
            column: 24
          },
          end: {
            line: 154,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 34
          },
          end: {
            line: 154,
            column: 43
          }
        }, {
          start: {
            line: 154,
            column: 46
          },
          end: {
            line: 154,
            column: 87
          }
        }],
        line: 154
      },
      "57": {
        loc: {
          start: {
            line: 156,
            column: 23
          },
          end: {
            line: 156,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 156,
            column: 41
          },
          end: {
            line: 156,
            column: 57
          }
        }, {
          start: {
            line: 156,
            column: 60
          },
          end: {
            line: 156,
            column: 64
          }
        }],
        line: 156
      },
      "58": {
        loc: {
          start: {
            line: 160,
            column: 18
          },
          end: {
            line: 160,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 160,
            column: 27
          },
          end: {
            line: 160,
            column: 55
          }
        }, {
          start: {
            line: 160,
            column: 58
          },
          end: {
            line: 160,
            column: 80
          }
        }],
        line: 160
      },
      "59": {
        loc: {
          start: {
            line: 165,
            column: 8
          },
          end: {
            line: 172,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 8
          },
          end: {
            line: 172,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "60": {
        loc: {
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 165,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 165,
            column: 18
          }
        }, {
          start: {
            line: 165,
            column: 22
          },
          end: {
            line: 165,
            column: 43
          }
        }],
        line: 165
      },
      "61": {
        loc: {
          start: {
            line: 175,
            column: 8
          },
          end: {
            line: 177,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 8
          },
          end: {
            line: 177,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "62": {
        loc: {
          start: {
            line: 183,
            column: 24
          },
          end: {
            line: 183,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 183,
            column: 34
          },
          end: {
            line: 183,
            column: 43
          }
        }, {
          start: {
            line: 183,
            column: 46
          },
          end: {
            line: 183,
            column: 87
          }
        }],
        line: 183
      },
      "63": {
        loc: {
          start: {
            line: 189,
            column: 8
          },
          end: {
            line: 190,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 8
          },
          end: {
            line: 190,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "64": {
        loc: {
          start: {
            line: 192,
            column: 8
          },
          end: {
            line: 194,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 8
          },
          end: {
            line: 194,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 192
      },
      "65": {
        loc: {
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 202,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 202,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "66": {
        loc: {
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 217,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 217,
            column: 9
          }
        }, {
          start: {
            line: 213,
            column: 13
          },
          end: {
            line: 217,
            column: 9
          }
        }],
        line: 208
      },
      "67": {
        loc: {
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 224,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 224,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "68": {
        loc: {
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 228,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 228,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "69": {
        loc: {
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 232,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 232,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "70": {
        loc: {
          start: {
            line: 250,
            column: 12
          },
          end: {
            line: 252,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 12
          },
          end: {
            line: 252,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "71": {
        loc: {
          start: {
            line: 258,
            column: 12
          },
          end: {
            line: 260,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 12
          },
          end: {
            line: 260,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "72": {
        loc: {
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 268,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 268,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhanced-rate-limiter.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,uCAA6C;AAC7C,+BAAqC;AAwCrC;IAME,6BAAoB,MAAuB;QAA3C,iBAGC;QAHmB,WAAM,GAAN,MAAM,CAAiB;QALnC,YAAO,GAAG,IAAI,GAAG,EAA0B,CAAC;QAC5C,cAAS,GAAG,IAAI,GAAG,EAA0B,CAAC;QAC9C,eAAU,GAAG,IAAI,GAAG,EAA0B,CAAC;QAC/C,2BAAsB,GAAG,IAAI,GAAG,EAAuB,CAAC;QAG9D,0CAA0C;QAC1C,WAAW,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,EAAE,EAAd,CAAc,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACnD,CAAC;IAEK,wCAAU,GAAhB,UAAiB,OAAoB;uCAAG,OAAO;;;;;4BAC7B,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBAC7C,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;wBAC/B,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAC;wBAC3B,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;wBAG3D,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,MAAM,IAAI,SAAS,EAAE,SAAS,CAAC,CAAC;wBAG/E,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;wBAClE,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;4BACzB,sBAAO,WAAW,EAAC;wBACrB,CAAC;wBAED,mDAAmD;wBACnD,IAAI,MAAM,EAAE,CAAC;4BACL,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;4BAChE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gCACxB,sBAAO,UAAU,EAAC;4BACpB,CAAC;wBACH,CAAC;wBAGK,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;wBAElE,qCAAqC;wBACrC,sBAAO,QAAQ,EAAC;;;;KACjB;IAEO,4CAAc,GAAtB,UAAuB,MAAc,EAAE,eAAwB;QAC7D,IAAM,GAAG,GAAG,eAAQ,MAAM,CAAE,CAAC;QAC7B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC1C,IAAM,KAAK,GAAG,eAAe;YAC3B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC;YAC5E,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAE7B,IAAI,CAAC,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACpC,KAAK,GAAG;gBACN,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;gBACzB,YAAY,EAAE,GAAG;gBACjB,eAAe,iBAAA;aAChB,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,CAAC;QAED,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACpC,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAEvD,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;QAED,OAAO;YACL,OAAO,SAAA;YACP,KAAK,OAAA;YACL,SAAS,WAAA;YACT,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;YAC3E,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC;YACxE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM;SACvD,CAAC;IACJ,CAAC;IAEO,0CAAY,GAApB,UAAqB,EAAU,EAAE,eAAwB,EAAE,eAAwB;QACjF,IAAM,GAAG,GAAG,aAAM,EAAE,CAAE,CAAC;QACvB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClC,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAExC,iCAAiC;QACjC,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAEvC,+DAA+D;QAC/D,IAAI,eAAe,IAAI,eAAe,EAAE,CAAC;YACvC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAC1E,CAAC;QAED,iEAAiE;QACjE,IAAI,CAAC,eAAe,IAAI,eAAe,EAAE,CAAC;YACxC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACpC,KAAK,GAAG;gBACN,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;gBACzB,YAAY,EAAE,GAAG;gBACjB,eAAe,iBAAA;aAChB,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/B,CAAC;QAED,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;QACxC,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAE3D,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;QAED,OAAO;YACL,OAAO,SAAA;YACP,KAAK,EAAE,SAAS;YAChB,SAAS,WAAA;YACT,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;YAC3E,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC;YAC1E,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI;SACrD,CAAC;IACJ,CAAC;IAEO,6CAAe,GAAvB,UAAwB,EAAU,EAAE,MAAe;QACjD,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,qBAAc,MAAM,CAAE,CAAC,CAAC,CAAC,mBAAY,EAAE,CAAE,CAAC;QAC/D,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrC,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAC3C,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAExC,IAAI,CAAC,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACpC,KAAK,GAAG;gBACN,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;gBACzB,YAAY,EAAE,GAAG;aAClB,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACpC,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAEvD,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;QAED,OAAO;YACL,OAAO,SAAA;YACP,KAAK,OAAA;YACL,SAAS,WAAA;YACT,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;YAC3E,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC;YACzE,SAAS,EAAE,OAAO;SACnB,CAAC;IACJ,CAAC;IAEO,iDAAmB,GAA3B,UAA4B,EAAU,EAAE,MAAe,EAAE,SAAkB;QACzE,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE1B,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAE1C,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;QAC3D,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAElB,2EAA2E;QAC3E,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QAEhC,oCAAoC;QACpC,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;YACrB,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,2CAAa,GAArB,UAAsB,EAAU;QAC9B,4CAA4C;QAC5C,4CAA4C;QAC5C,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO;YACP,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,OAAO;YACP,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,yCAAW,GAAnB,UAAoB,OAAoB;QACtC,iDAAiD;QACjD,IAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACzD,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACxC,CAAC;QAED,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC/D,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,2BAA2B;QAC3B,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,6CAAe,GAAvB,UAAwB,IAAY,EAAE,KAAa,EAAE,SAAiB,EAAE,SAAiB;QACvF,OAAO;YACL,mBAAmB,EAAE,KAAK,CAAC,QAAQ,EAAE;YACrC,uBAAuB,EAAE,SAAS,CAAC,QAAQ,EAAE;YAC7C,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;YAC3D,kBAAkB,EAAE,IAAI;SACzB,CAAC;IACJ,CAAC;IAEO,qCAAO,GAAf;QAAA,iBA+BC;QA9BC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,8BAA8B;QAC9B,IAAM,cAAc,GAAa,EAAE,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;YAC9B,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC1B,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAxB,CAAwB,CAAC,CAAC;QAExD,gCAAgC;QAChC,IAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;YAChC,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC1B,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,gBAAgB,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAC;QAE5D,iCAAiC;QACjC,IAAM,iBAAiB,GAAa,EAAE,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;YACjC,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC1B,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,iBAAiB,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAA3B,CAA2B,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,8DAAuD,IAAI,CAAC,OAAO,CAAC,IAAI,oBAAU,IAAI,CAAC,SAAS,CAAC,IAAI,qBAAW,IAAI,CAAC,UAAU,CAAC,IAAI,CAAE,CAAC,CAAC;IACtJ,CAAC;IACH,0BAAC;AAAD,CAAC,AArQD,IAqQC;AArQY,kDAAmB;AAuQhC,4DAA4D;AAC/C,QAAA,oBAAoB,GAAG;IAClC,GAAG,EAAE,IAAI,mBAAmB,CAAC;QAC3B,UAAU,EAAE,GAAG;QACf,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACzC,YAAY,EAAE,GAAG;QACjB,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QAC5B,aAAa,EAAE,EAAE;QACjB,aAAa,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QACrC,uBAAuB,EAAE,GAAG;KAC7B,CAAC;IAEF,IAAI,EAAE,IAAI,mBAAmB,CAAC;QAC5B,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QAC1B,YAAY,EAAE,EAAE;QAChB,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QAC5B,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,EAAE,GAAG,IAAI;QACxB,uBAAuB,EAAE,GAAG;KAC7B,CAAC;IAEF,KAAK,EAAE,IAAI,mBAAmB,CAAC;QAC7B,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QAC1B,YAAY,EAAE,GAAG;QACjB,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QAC5B,aAAa,EAAE,EAAE;QACjB,aAAa,EAAE,EAAE,GAAG,IAAI;QACxB,uBAAuB,EAAE,GAAG;KAC7B,CAAC;CACH,CAAC;AAEF,kBAAe,mBAAmB,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhanced-rate-limiter.ts"],
      sourcesContent: ["/**\n * Enhanced Rate Limiter with User-Based and IP-Based Limiting\n * Addresses shared network limitations and provides more granular control\n */\n\nimport { NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from './auth';\n\ninterface RateLimitConfig {\n  // IP-based limits (for unauthenticated users)\n  ipRequests: number;\n  ipWindowMs: number;\n  \n  // User-based limits (for authenticated users)\n  userRequests: number;\n  userWindowMs: number;\n  \n  // Burst limits (short-term)\n  burstRequests: number;\n  burstWindowMs: number;\n  \n  // Shared network protection\n  sharedNetworkMultiplier: number;\n  \n  // Skip limits for certain conditions\n  skipSuccessfulRequests?: boolean;\n  skipFailedRequests?: boolean;\n}\n\ninterface RateLimitEntry {\n  count: number;\n  resetTime: number;\n  firstRequest: number;\n  isSharedNetwork?: boolean;\n}\n\ninterface RateLimitResult {\n  allowed: boolean;\n  limit: number;\n  remaining: number;\n  resetTime: number;\n  retryAfter?: number;\n  headers: Record<string, string>;\n  limitType: 'ip' | 'user' | 'burst' | 'shared-network';\n}\n\nexport class EnhancedRateLimiter {\n  private ipStore = new Map<string, RateLimitEntry>();\n  private userStore = new Map<string, RateLimitEntry>();\n  private burstStore = new Map<string, RateLimitEntry>();\n  private sharedNetworkDetection = new Map<string, Set<string>>();\n  \n  constructor(private config: RateLimitConfig) {\n    // Cleanup expired entries every 5 minutes\n    setInterval(() => this.cleanup(), 5 * 60 * 1000);\n  }\n\n  async checkLimit(request: NextRequest): Promise<RateLimitResult> {\n    const session = await getServerSession(authOptions);\n    const ip = this.getClientIP(request);\n    const userId = session?.user?.id;\n    const userAgent = request.headers.get('user-agent') || 'unknown';\n\n    // Detect shared networks\n    const isSharedNetwork = this.detectSharedNetwork(ip, userId || undefined, userAgent);\n\n    // Check burst limits first (applies to all requests)\n    const burstResult = this.checkBurstLimit(ip, userId || undefined);\n    if (!burstResult.allowed) {\n      return burstResult;\n    }\n\n    // For authenticated users, use user-based limiting\n    if (userId) {\n      const userResult = this.checkUserLimit(userId, isSharedNetwork);\n      if (!userResult.allowed) {\n        return userResult;\n      }\n    }\n\n    // Always check IP-based limits as a fallback/additional protection\n    const ipResult = this.checkIPLimit(ip, isSharedNetwork, !!userId);\n    \n    // Return the most restrictive result\n    return ipResult;\n  }\n\n  private checkUserLimit(userId: string, isSharedNetwork: boolean): RateLimitResult {\n    const key = `user:${userId}`;\n    const now = Date.now();\n    \n    let entry = this.userStore.get(key);\n    const windowMs = this.config.userWindowMs;\n    const limit = isSharedNetwork \n      ? Math.floor(this.config.userRequests * this.config.sharedNetworkMultiplier)\n      : this.config.userRequests;\n\n    if (!entry || now > entry.resetTime) {\n      entry = {\n        count: 0,\n        resetTime: now + windowMs,\n        firstRequest: now,\n        isSharedNetwork\n      };\n      this.userStore.set(key, entry);\n    }\n\n    const allowed = entry.count < limit;\n    const remaining = Math.max(0, limit - entry.count - 1);\n\n    if (allowed) {\n      entry.count++;\n    }\n\n    return {\n      allowed,\n      limit,\n      remaining,\n      resetTime: entry.resetTime,\n      retryAfter: allowed ? undefined : Math.ceil((entry.resetTime - now) / 1000),\n      headers: this.generateHeaders('user', limit, remaining, entry.resetTime),\n      limitType: isSharedNetwork ? 'shared-network' : 'user'\n    };\n  }\n\n  private checkIPLimit(ip: string, isSharedNetwork: boolean, isAuthenticated: boolean): RateLimitResult {\n    const key = `ip:${ip}`;\n    const now = Date.now();\n    \n    let entry = this.ipStore.get(key);\n    const windowMs = this.config.ipWindowMs;\n    \n    // Adjust limits based on context\n    let baseLimit = this.config.ipRequests;\n    \n    // Increase limits for authenticated users from shared networks\n    if (isAuthenticated && isSharedNetwork) {\n      baseLimit = Math.floor(baseLimit * this.config.sharedNetworkMultiplier);\n    }\n    \n    // Decrease limits for unauthenticated users from shared networks\n    if (!isAuthenticated && isSharedNetwork) {\n      baseLimit = Math.floor(baseLimit * 0.5);\n    }\n\n    if (!entry || now > entry.resetTime) {\n      entry = {\n        count: 0,\n        resetTime: now + windowMs,\n        firstRequest: now,\n        isSharedNetwork\n      };\n      this.ipStore.set(key, entry);\n    }\n\n    const allowed = entry.count < baseLimit;\n    const remaining = Math.max(0, baseLimit - entry.count - 1);\n\n    if (allowed) {\n      entry.count++;\n    }\n\n    return {\n      allowed,\n      limit: baseLimit,\n      remaining,\n      resetTime: entry.resetTime,\n      retryAfter: allowed ? undefined : Math.ceil((entry.resetTime - now) / 1000),\n      headers: this.generateHeaders('ip', baseLimit, remaining, entry.resetTime),\n      limitType: isSharedNetwork ? 'shared-network' : 'ip'\n    };\n  }\n\n  private checkBurstLimit(ip: string, userId?: string): RateLimitResult {\n    const key = userId ? `burst:user:${userId}` : `burst:ip:${ip}`;\n    const now = Date.now();\n    \n    let entry = this.burstStore.get(key);\n    const windowMs = this.config.burstWindowMs;\n    const limit = this.config.burstRequests;\n\n    if (!entry || now > entry.resetTime) {\n      entry = {\n        count: 0,\n        resetTime: now + windowMs,\n        firstRequest: now\n      };\n      this.burstStore.set(key, entry);\n    }\n\n    const allowed = entry.count < limit;\n    const remaining = Math.max(0, limit - entry.count - 1);\n\n    if (allowed) {\n      entry.count++;\n    }\n\n    return {\n      allowed,\n      limit,\n      remaining,\n      resetTime: entry.resetTime,\n      retryAfter: allowed ? undefined : Math.ceil((entry.resetTime - now) / 1000),\n      headers: this.generateHeaders('burst', limit, remaining, entry.resetTime),\n      limitType: 'burst'\n    };\n  }\n\n  private detectSharedNetwork(ip: string, userId?: string, userAgent?: string): boolean {\n    if (!userId) return false;\n\n    const networkKey = this.getNetworkKey(ip);\n    \n    if (!this.sharedNetworkDetection.has(networkKey)) {\n      this.sharedNetworkDetection.set(networkKey, new Set());\n    }\n\n    const users = this.sharedNetworkDetection.get(networkKey)!;\n    users.add(userId);\n\n    // Consider it a shared network if more than 3 different users from same IP\n    const isShared = users.size > 3;\n\n    // Clean up old entries periodically\n    if (users.size > 100) {\n      users.clear();\n    }\n\n    return isShared;\n  }\n\n  private getNetworkKey(ip: string): string {\n    // For IPv4, use /24 subnet (first 3 octets)\n    // For IPv6, use /64 subnet (first 4 groups)\n    if (ip.includes(':')) {\n      // IPv6\n      const parts = ip.split(':');\n      return parts.slice(0, 4).join(':');\n    } else {\n      // IPv4\n      const parts = ip.split('.');\n      return parts.slice(0, 3).join('.');\n    }\n  }\n\n  private getClientIP(request: NextRequest): string {\n    // Try multiple headers to get the real client IP\n    const forwarded = request.headers.get('x-forwarded-for');\n    if (forwarded) {\n      return forwarded.split(',')[0].trim();\n    }\n\n    const realIP = request.headers.get('x-real-ip');\n    if (realIP) {\n      return realIP;\n    }\n\n    const cfConnectingIP = request.headers.get('cf-connecting-ip');\n    if (cfConnectingIP) {\n      return cfConnectingIP;\n    }\n\n    // Fallback to a default IP\n    return '127.0.0.1';\n  }\n\n  private generateHeaders(type: string, limit: number, remaining: number, resetTime: number): Record<string, string> {\n    return {\n      'X-RateLimit-Limit': limit.toString(),\n      'X-RateLimit-Remaining': remaining.toString(),\n      'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString(),\n      'X-RateLimit-Type': type\n    };\n  }\n\n  private cleanup(): void {\n    const now = Date.now();\n    \n    // Clean up expired IP entries\n    const ipKeysToDelete: string[] = [];\n    this.ipStore.forEach((entry, key) => {\n      if (now > entry.resetTime) {\n        ipKeysToDelete.push(key);\n      }\n    });\n    ipKeysToDelete.forEach(key => this.ipStore.delete(key));\n\n    // Clean up expired user entries\n    const userKeysToDelete: string[] = [];\n    this.userStore.forEach((entry, key) => {\n      if (now > entry.resetTime) {\n        userKeysToDelete.push(key);\n      }\n    });\n    userKeysToDelete.forEach(key => this.userStore.delete(key));\n\n    // Clean up expired burst entries\n    const burstKeysToDelete: string[] = [];\n    this.burstStore.forEach((entry, key) => {\n      if (now > entry.resetTime) {\n        burstKeysToDelete.push(key);\n      }\n    });\n    burstKeysToDelete.forEach(key => this.burstStore.delete(key));\n\n    console.log(`RateLimiter: Cleaned up expired entries. Active: IP=${this.ipStore.size}, User=${this.userStore.size}, Burst=${this.burstStore.size}`);\n  }\n}\n\n// Pre-configured rate limiters for different endpoint types\nexport const enhancedRateLimiters = {\n  api: new EnhancedRateLimiter({\n    ipRequests: 100,\n    ipWindowMs: 15 * 60 * 1000, // 15 minutes\n    userRequests: 200,\n    userWindowMs: 15 * 60 * 1000,\n    burstRequests: 20,\n    burstWindowMs: 60 * 1000, // 1 minute\n    sharedNetworkMultiplier: 2.0\n  }),\n\n  auth: new EnhancedRateLimiter({\n    ipRequests: 10,\n    ipWindowMs: 15 * 60 * 1000,\n    userRequests: 15,\n    userWindowMs: 15 * 60 * 1000,\n    burstRequests: 5,\n    burstWindowMs: 60 * 1000,\n    sharedNetworkMultiplier: 1.5\n  }),\n\n  write: new EnhancedRateLimiter({\n    ipRequests: 50,\n    ipWindowMs: 15 * 60 * 1000,\n    userRequests: 100,\n    userWindowMs: 15 * 60 * 1000,\n    burstRequests: 10,\n    burstWindowMs: 60 * 1000,\n    sharedNetworkMultiplier: 1.8\n  })\n};\n\nexport default EnhancedRateLimiter;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9166bb5c711b2d1f7c6ab6cc9b10ebdc4e3c9810"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2etuhhs2ll = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2etuhhs2ll();
var __awaiter =
/* istanbul ignore next */
(cov_2etuhhs2ll().s[0]++,
/* istanbul ignore next */
(cov_2etuhhs2ll().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2etuhhs2ll().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2etuhhs2ll().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2etuhhs2ll().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[1]++;
    cov_2etuhhs2ll().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[2]++;
      cov_2etuhhs2ll().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_2etuhhs2ll().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_2etuhhs2ll().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[4]++;
      cov_2etuhhs2ll().s[4]++;
      try {
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[5]++;
      cov_2etuhhs2ll().s[7]++;
      try {
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[6]++;
      cov_2etuhhs2ll().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2etuhhs2ll().s[12]++,
/* istanbul ignore next */
(cov_2etuhhs2ll().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_2etuhhs2ll().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2etuhhs2ll().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2etuhhs2ll().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2etuhhs2ll().f[8]++;
        cov_2etuhhs2ll().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2etuhhs2ll().b[6][0]++;
          cov_2etuhhs2ll().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2etuhhs2ll().b[6][1]++;
        }
        cov_2etuhhs2ll().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2etuhhs2ll().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2etuhhs2ll().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[9]++;
    cov_2etuhhs2ll().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[10]++;
    cov_2etuhhs2ll().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[11]++;
      cov_2etuhhs2ll().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[12]++;
    cov_2etuhhs2ll().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[9][0]++;
      cov_2etuhhs2ll().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[9][1]++;
    }
    cov_2etuhhs2ll().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().s[25]++;
      try {
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[15][0]++,
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[16][1]++,
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2etuhhs2ll().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2etuhhs2ll().b[12][0]++;
          cov_2etuhhs2ll().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2etuhhs2ll().b[12][1]++;
        }
        cov_2etuhhs2ll().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2etuhhs2ll().b[18][0]++;
          cov_2etuhhs2ll().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2etuhhs2ll().b[18][1]++;
        }
        cov_2etuhhs2ll().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[19][1]++;
            cov_2etuhhs2ll().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[19][2]++;
            cov_2etuhhs2ll().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[19][3]++;
            cov_2etuhhs2ll().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[19][4]++;
            cov_2etuhhs2ll().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[19][5]++;
            cov_2etuhhs2ll().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2etuhhs2ll().b[20][0]++;
              cov_2etuhhs2ll().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2etuhhs2ll().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2etuhhs2ll().b[20][1]++;
            }
            cov_2etuhhs2ll().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2etuhhs2ll().b[23][0]++;
              cov_2etuhhs2ll().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2etuhhs2ll().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2etuhhs2ll().b[23][1]++;
            }
            cov_2etuhhs2ll().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2etuhhs2ll().b[25][0]++;
              cov_2etuhhs2ll().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2etuhhs2ll().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_2etuhhs2ll().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2etuhhs2ll().b[25][1]++;
            }
            cov_2etuhhs2ll().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2etuhhs2ll().b[27][0]++;
              cov_2etuhhs2ll().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2etuhhs2ll().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2etuhhs2ll().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2etuhhs2ll().b[27][1]++;
            }
            cov_2etuhhs2ll().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2etuhhs2ll().b[29][0]++;
              cov_2etuhhs2ll().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2etuhhs2ll().b[29][1]++;
            }
            cov_2etuhhs2ll().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2etuhhs2ll().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[30][0]++;
      cov_2etuhhs2ll().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[30][1]++;
    }
    cov_2etuhhs2ll().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_2etuhhs2ll().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2etuhhs2ll().s[68]++;
exports.enhancedRateLimiters = exports.EnhancedRateLimiter = void 0;
var next_auth_1 =
/* istanbul ignore next */
(cov_2etuhhs2ll().s[69]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_2etuhhs2ll().s[70]++, require("./auth"));
var EnhancedRateLimiter =
/* istanbul ignore next */
(/** @class */cov_2etuhhs2ll().s[71]++, function () {
  /* istanbul ignore next */
  cov_2etuhhs2ll().f[13]++;
  function EnhancedRateLimiter(config) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[14]++;
    var _this =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[72]++, this);
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[73]++;
    this.config = config;
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[74]++;
    this.ipStore = new Map();
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[75]++;
    this.userStore = new Map();
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[76]++;
    this.burstStore = new Map();
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[77]++;
    this.sharedNetworkDetection = new Map();
    // Cleanup expired entries every 5 minutes
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[78]++;
    setInterval(function () {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[15]++;
      cov_2etuhhs2ll().s[79]++;
      return _this.cleanup();
    }, 5 * 60 * 1000);
  }
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[80]++;
  EnhancedRateLimiter.prototype.checkLimit = function (request) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[16]++;
    cov_2etuhhs2ll().s[81]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[17]++;
      var session, ip, userId, userAgent, isSharedNetwork, burstResult, userResult, ipResult;
      var _a;
      /* istanbul ignore next */
      cov_2etuhhs2ll().s[82]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_2etuhhs2ll().f[18]++;
        cov_2etuhhs2ll().s[83]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[32][0]++;
            cov_2etuhhs2ll().s[84]++;
            return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
          case 1:
            /* istanbul ignore next */
            cov_2etuhhs2ll().b[32][1]++;
            cov_2etuhhs2ll().s[85]++;
            session = _b.sent();
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[86]++;
            ip = this.getClientIP(request);
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[87]++;
            userId =
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[34][0]++, (_a =
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[36][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[36][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[35][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[35][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[34][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[33][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[33][1]++, _a.id);
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[88]++;
            userAgent =
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[37][0]++, request.headers.get('user-agent')) ||
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[37][1]++, 'unknown');
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[89]++;
            isSharedNetwork = this.detectSharedNetwork(ip,
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[38][0]++, userId) ||
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[38][1]++, undefined), userAgent);
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[90]++;
            burstResult = this.checkBurstLimit(ip,
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[39][0]++, userId) ||
            /* istanbul ignore next */
            (cov_2etuhhs2ll().b[39][1]++, undefined));
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[91]++;
            if (!burstResult.allowed) {
              /* istanbul ignore next */
              cov_2etuhhs2ll().b[40][0]++;
              cov_2etuhhs2ll().s[92]++;
              return [2 /*return*/, burstResult];
            } else
            /* istanbul ignore next */
            {
              cov_2etuhhs2ll().b[40][1]++;
            }
            // For authenticated users, use user-based limiting
            cov_2etuhhs2ll().s[93]++;
            if (userId) {
              /* istanbul ignore next */
              cov_2etuhhs2ll().b[41][0]++;
              cov_2etuhhs2ll().s[94]++;
              userResult = this.checkUserLimit(userId, isSharedNetwork);
              /* istanbul ignore next */
              cov_2etuhhs2ll().s[95]++;
              if (!userResult.allowed) {
                /* istanbul ignore next */
                cov_2etuhhs2ll().b[42][0]++;
                cov_2etuhhs2ll().s[96]++;
                return [2 /*return*/, userResult];
              } else
              /* istanbul ignore next */
              {
                cov_2etuhhs2ll().b[42][1]++;
              }
            } else
            /* istanbul ignore next */
            {
              cov_2etuhhs2ll().b[41][1]++;
            }
            cov_2etuhhs2ll().s[97]++;
            ipResult = this.checkIPLimit(ip, isSharedNetwork, !!userId);
            // Return the most restrictive result
            /* istanbul ignore next */
            cov_2etuhhs2ll().s[98]++;
            return [2 /*return*/, ipResult];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[99]++;
  EnhancedRateLimiter.prototype.checkUserLimit = function (userId, isSharedNetwork) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[19]++;
    var key =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[100]++, "user:".concat(userId));
    var now =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[101]++, Date.now());
    var entry =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[102]++, this.userStore.get(key));
    var windowMs =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[103]++, this.config.userWindowMs);
    var limit =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[104]++, isSharedNetwork ?
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[43][0]++, Math.floor(this.config.userRequests * this.config.sharedNetworkMultiplier)) :
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[43][1]++, this.config.userRequests));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[105]++;
    if (
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[45][0]++, !entry) ||
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[45][1]++, now > entry.resetTime)) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[44][0]++;
      cov_2etuhhs2ll().s[106]++;
      entry = {
        count: 0,
        resetTime: now + windowMs,
        firstRequest: now,
        isSharedNetwork: isSharedNetwork
      };
      /* istanbul ignore next */
      cov_2etuhhs2ll().s[107]++;
      this.userStore.set(key, entry);
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[44][1]++;
    }
    var allowed =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[108]++, entry.count < limit);
    var remaining =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[109]++, Math.max(0, limit - entry.count - 1));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[110]++;
    if (allowed) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[46][0]++;
      cov_2etuhhs2ll().s[111]++;
      entry.count++;
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[46][1]++;
    }
    cov_2etuhhs2ll().s[112]++;
    return {
      allowed: allowed,
      limit: limit,
      remaining: remaining,
      resetTime: entry.resetTime,
      retryAfter: allowed ?
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[47][0]++, undefined) :
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[47][1]++, Math.ceil((entry.resetTime - now) / 1000)),
      headers: this.generateHeaders('user', limit, remaining, entry.resetTime),
      limitType: isSharedNetwork ?
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[48][0]++, 'shared-network') :
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[48][1]++, 'user')
    };
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[113]++;
  EnhancedRateLimiter.prototype.checkIPLimit = function (ip, isSharedNetwork, isAuthenticated) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[20]++;
    var key =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[114]++, "ip:".concat(ip));
    var now =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[115]++, Date.now());
    var entry =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[116]++, this.ipStore.get(key));
    var windowMs =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[117]++, this.config.ipWindowMs);
    // Adjust limits based on context
    var baseLimit =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[118]++, this.config.ipRequests);
    // Increase limits for authenticated users from shared networks
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[119]++;
    if (
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[50][0]++, isAuthenticated) &&
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[50][1]++, isSharedNetwork)) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[49][0]++;
      cov_2etuhhs2ll().s[120]++;
      baseLimit = Math.floor(baseLimit * this.config.sharedNetworkMultiplier);
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[49][1]++;
    }
    // Decrease limits for unauthenticated users from shared networks
    cov_2etuhhs2ll().s[121]++;
    if (
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[52][0]++, !isAuthenticated) &&
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[52][1]++, isSharedNetwork)) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[51][0]++;
      cov_2etuhhs2ll().s[122]++;
      baseLimit = Math.floor(baseLimit * 0.5);
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[51][1]++;
    }
    cov_2etuhhs2ll().s[123]++;
    if (
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[54][0]++, !entry) ||
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[54][1]++, now > entry.resetTime)) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[53][0]++;
      cov_2etuhhs2ll().s[124]++;
      entry = {
        count: 0,
        resetTime: now + windowMs,
        firstRequest: now,
        isSharedNetwork: isSharedNetwork
      };
      /* istanbul ignore next */
      cov_2etuhhs2ll().s[125]++;
      this.ipStore.set(key, entry);
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[53][1]++;
    }
    var allowed =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[126]++, entry.count < baseLimit);
    var remaining =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[127]++, Math.max(0, baseLimit - entry.count - 1));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[128]++;
    if (allowed) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[55][0]++;
      cov_2etuhhs2ll().s[129]++;
      entry.count++;
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[55][1]++;
    }
    cov_2etuhhs2ll().s[130]++;
    return {
      allowed: allowed,
      limit: baseLimit,
      remaining: remaining,
      resetTime: entry.resetTime,
      retryAfter: allowed ?
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[56][0]++, undefined) :
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[56][1]++, Math.ceil((entry.resetTime - now) / 1000)),
      headers: this.generateHeaders('ip', baseLimit, remaining, entry.resetTime),
      limitType: isSharedNetwork ?
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[57][0]++, 'shared-network') :
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[57][1]++, 'ip')
    };
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[131]++;
  EnhancedRateLimiter.prototype.checkBurstLimit = function (ip, userId) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[21]++;
    var key =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[132]++, userId ?
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[58][0]++, "burst:user:".concat(userId)) :
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[58][1]++, "burst:ip:".concat(ip)));
    var now =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[133]++, Date.now());
    var entry =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[134]++, this.burstStore.get(key));
    var windowMs =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[135]++, this.config.burstWindowMs);
    var limit =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[136]++, this.config.burstRequests);
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[137]++;
    if (
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[60][0]++, !entry) ||
    /* istanbul ignore next */
    (cov_2etuhhs2ll().b[60][1]++, now > entry.resetTime)) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[59][0]++;
      cov_2etuhhs2ll().s[138]++;
      entry = {
        count: 0,
        resetTime: now + windowMs,
        firstRequest: now
      };
      /* istanbul ignore next */
      cov_2etuhhs2ll().s[139]++;
      this.burstStore.set(key, entry);
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[59][1]++;
    }
    var allowed =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[140]++, entry.count < limit);
    var remaining =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[141]++, Math.max(0, limit - entry.count - 1));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[142]++;
    if (allowed) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[61][0]++;
      cov_2etuhhs2ll().s[143]++;
      entry.count++;
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[61][1]++;
    }
    cov_2etuhhs2ll().s[144]++;
    return {
      allowed: allowed,
      limit: limit,
      remaining: remaining,
      resetTime: entry.resetTime,
      retryAfter: allowed ?
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[62][0]++, undefined) :
      /* istanbul ignore next */
      (cov_2etuhhs2ll().b[62][1]++, Math.ceil((entry.resetTime - now) / 1000)),
      headers: this.generateHeaders('burst', limit, remaining, entry.resetTime),
      limitType: 'burst'
    };
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[145]++;
  EnhancedRateLimiter.prototype.detectSharedNetwork = function (ip, userId, userAgent) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[22]++;
    cov_2etuhhs2ll().s[146]++;
    if (!userId) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[63][0]++;
      cov_2etuhhs2ll().s[147]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[63][1]++;
    }
    var networkKey =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[148]++, this.getNetworkKey(ip));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[149]++;
    if (!this.sharedNetworkDetection.has(networkKey)) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[64][0]++;
      cov_2etuhhs2ll().s[150]++;
      this.sharedNetworkDetection.set(networkKey, new Set());
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[64][1]++;
    }
    var users =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[151]++, this.sharedNetworkDetection.get(networkKey));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[152]++;
    users.add(userId);
    // Consider it a shared network if more than 3 different users from same IP
    var isShared =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[153]++, users.size > 3);
    // Clean up old entries periodically
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[154]++;
    if (users.size > 100) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[65][0]++;
      cov_2etuhhs2ll().s[155]++;
      users.clear();
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[65][1]++;
    }
    cov_2etuhhs2ll().s[156]++;
    return isShared;
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[157]++;
  EnhancedRateLimiter.prototype.getNetworkKey = function (ip) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[23]++;
    cov_2etuhhs2ll().s[158]++;
    // For IPv4, use /24 subnet (first 3 octets)
    // For IPv6, use /64 subnet (first 4 groups)
    if (ip.includes(':')) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[66][0]++;
      // IPv6
      var parts =
      /* istanbul ignore next */
      (cov_2etuhhs2ll().s[159]++, ip.split(':'));
      /* istanbul ignore next */
      cov_2etuhhs2ll().s[160]++;
      return parts.slice(0, 4).join(':');
    } else {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[66][1]++;
      // IPv4
      var parts =
      /* istanbul ignore next */
      (cov_2etuhhs2ll().s[161]++, ip.split('.'));
      /* istanbul ignore next */
      cov_2etuhhs2ll().s[162]++;
      return parts.slice(0, 3).join('.');
    }
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[163]++;
  EnhancedRateLimiter.prototype.getClientIP = function (request) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[24]++;
    // Try multiple headers to get the real client IP
    var forwarded =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[164]++, request.headers.get('x-forwarded-for'));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[165]++;
    if (forwarded) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[67][0]++;
      cov_2etuhhs2ll().s[166]++;
      return forwarded.split(',')[0].trim();
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[67][1]++;
    }
    var realIP =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[167]++, request.headers.get('x-real-ip'));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[168]++;
    if (realIP) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[68][0]++;
      cov_2etuhhs2ll().s[169]++;
      return realIP;
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[68][1]++;
    }
    var cfConnectingIP =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[170]++, request.headers.get('cf-connecting-ip'));
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[171]++;
    if (cfConnectingIP) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().b[69][0]++;
      cov_2etuhhs2ll().s[172]++;
      return cfConnectingIP;
    } else
    /* istanbul ignore next */
    {
      cov_2etuhhs2ll().b[69][1]++;
    }
    // Fallback to a default IP
    cov_2etuhhs2ll().s[173]++;
    return '127.0.0.1';
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[174]++;
  EnhancedRateLimiter.prototype.generateHeaders = function (type, limit, remaining, resetTime) {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[25]++;
    cov_2etuhhs2ll().s[175]++;
    return {
      'X-RateLimit-Limit': limit.toString(),
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString(),
      'X-RateLimit-Type': type
    };
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[176]++;
  EnhancedRateLimiter.prototype.cleanup = function () {
    /* istanbul ignore next */
    cov_2etuhhs2ll().f[26]++;
    var _this =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[177]++, this);
    var now =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[178]++, Date.now());
    // Clean up expired IP entries
    var ipKeysToDelete =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[179]++, []);
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[180]++;
    this.ipStore.forEach(function (entry, key) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[27]++;
      cov_2etuhhs2ll().s[181]++;
      if (now > entry.resetTime) {
        /* istanbul ignore next */
        cov_2etuhhs2ll().b[70][0]++;
        cov_2etuhhs2ll().s[182]++;
        ipKeysToDelete.push(key);
      } else
      /* istanbul ignore next */
      {
        cov_2etuhhs2ll().b[70][1]++;
      }
    });
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[183]++;
    ipKeysToDelete.forEach(function (key) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[28]++;
      cov_2etuhhs2ll().s[184]++;
      return _this.ipStore.delete(key);
    });
    // Clean up expired user entries
    var userKeysToDelete =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[185]++, []);
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[186]++;
    this.userStore.forEach(function (entry, key) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[29]++;
      cov_2etuhhs2ll().s[187]++;
      if (now > entry.resetTime) {
        /* istanbul ignore next */
        cov_2etuhhs2ll().b[71][0]++;
        cov_2etuhhs2ll().s[188]++;
        userKeysToDelete.push(key);
      } else
      /* istanbul ignore next */
      {
        cov_2etuhhs2ll().b[71][1]++;
      }
    });
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[189]++;
    userKeysToDelete.forEach(function (key) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[30]++;
      cov_2etuhhs2ll().s[190]++;
      return _this.userStore.delete(key);
    });
    // Clean up expired burst entries
    var burstKeysToDelete =
    /* istanbul ignore next */
    (cov_2etuhhs2ll().s[191]++, []);
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[192]++;
    this.burstStore.forEach(function (entry, key) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[31]++;
      cov_2etuhhs2ll().s[193]++;
      if (now > entry.resetTime) {
        /* istanbul ignore next */
        cov_2etuhhs2ll().b[72][0]++;
        cov_2etuhhs2ll().s[194]++;
        burstKeysToDelete.push(key);
      } else
      /* istanbul ignore next */
      {
        cov_2etuhhs2ll().b[72][1]++;
      }
    });
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[195]++;
    burstKeysToDelete.forEach(function (key) {
      /* istanbul ignore next */
      cov_2etuhhs2ll().f[32]++;
      cov_2etuhhs2ll().s[196]++;
      return _this.burstStore.delete(key);
    });
    /* istanbul ignore next */
    cov_2etuhhs2ll().s[197]++;
    console.log("RateLimiter: Cleaned up expired entries. Active: IP=".concat(this.ipStore.size, ", User=").concat(this.userStore.size, ", Burst=").concat(this.burstStore.size));
  };
  /* istanbul ignore next */
  cov_2etuhhs2ll().s[198]++;
  return EnhancedRateLimiter;
}());
/* istanbul ignore next */
cov_2etuhhs2ll().s[199]++;
exports.EnhancedRateLimiter = EnhancedRateLimiter;
// Pre-configured rate limiters for different endpoint types
/* istanbul ignore next */
cov_2etuhhs2ll().s[200]++;
exports.enhancedRateLimiters = {
  api: new EnhancedRateLimiter({
    ipRequests: 100,
    ipWindowMs: 15 * 60 * 1000,
    // 15 minutes
    userRequests: 200,
    userWindowMs: 15 * 60 * 1000,
    burstRequests: 20,
    burstWindowMs: 60 * 1000,
    // 1 minute
    sharedNetworkMultiplier: 2.0
  }),
  auth: new EnhancedRateLimiter({
    ipRequests: 10,
    ipWindowMs: 15 * 60 * 1000,
    userRequests: 15,
    userWindowMs: 15 * 60 * 1000,
    burstRequests: 5,
    burstWindowMs: 60 * 1000,
    sharedNetworkMultiplier: 1.5
  }),
  write: new EnhancedRateLimiter({
    ipRequests: 50,
    ipWindowMs: 15 * 60 * 1000,
    userRequests: 100,
    userWindowMs: 15 * 60 * 1000,
    burstRequests: 10,
    burstWindowMs: 60 * 1000,
    sharedNetworkMultiplier: 1.8
  })
};
/* istanbul ignore next */
cov_2etuhhs2ll().s[201]++;
exports.default = EnhancedRateLimiter;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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