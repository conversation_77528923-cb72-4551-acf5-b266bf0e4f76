{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/personalized-resources.test.tsx", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,+BAA+B;AAC/B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAM,OAAA,CAAC;IAC9B,SAAS,EAAE,cAAM,OAAA,4BAAc,EAAd,CAAc;CAChC,CAAC,EAF6B,CAE7B,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;IACrB,OAAO,UAAC,EAAiC;QAA/B,IAAA,QAAQ,cAAA,EAAE,IAAI,UAAA,EAAK,KAAK,cAA1B,oBAA4B,CAAF;QAAY,OAAA,CAC5C,uCAAG,IAAI,EAAE,IAAI,IAAM,KAAK,cAAG,QAAQ,IAAK,CACzC,CAAA;KAAA,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,gBAAgB;AAChB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAM,OAAA,CAAC;IAClC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;CACtB,CAAC,EAFiC,CAEjC,CAAC,CAAC;AAEJ,qBAAqB;AACrB,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,cAAM,OAAA,CAAC;IACjD,MAAM,EAAE,UAAC,EAAwD;QAAtD,IAAA,QAAQ,cAAA,EAAE,OAAO,aAAA,EAAE,SAAS,eAAA,EAAE,OAAO,aAAA,EAAK,KAAK,cAAjD,+CAAmD,CAAF;QACxD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,wDAAiB,QAAQ,EAAC,SAAS,EAAE,SAAS,IAAM,KAAK,cAAG,QAAQ,IAAO,CAAC;QACrF,CAAC;QACD,OAAO,2DAAoB,QAAQ,EAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,IAAM,KAAK,cAAG,QAAQ,IAAU,CAAC;IAC7G,CAAC;CACF,CAAC,EAPgD,CAOhD,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE,cAAM,OAAA,CAAC;IAChD,KAAK,EAAE,UAAC,EAAoC;QAAlC,IAAA,QAAQ,cAAA,EAAE,OAAO,aAAA,EAAK,KAAK,cAA7B,uBAA+B,CAAF;QAAY,OAAA,CAC/C,yDAAkB,OAAO,kBAAe,OAAO,IAAM,KAAK,cAAG,QAAQ,IAAQ,CAC9E,CAAA;KAAA;CACF,CAAC,EAJ+C,CAI/C,CAAC,CAAC;AAEJ,0BAA0B;AAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,CAAC;IAC/B,QAAQ,EAAE,cAAM,OAAA,+CAAiB,gBAAgB,GAAG,EAApC,CAAoC;IACpD,KAAK,EAAE,cAAM,OAAA,+CAAiB,YAAY,GAAG,EAAhC,CAAgC;IAC7C,IAAI,EAAE,cAAM,OAAA,+CAAiB,WAAW,GAAG,EAA/B,CAA+B;IAC3C,YAAY,EAAE,cAAM,OAAA,+CAAiB,oBAAoB,GAAG,EAAxC,CAAwC;IAC5D,QAAQ,EAAE,cAAM,OAAA,+CAAiB,eAAe,GAAG,EAAnC,CAAmC;IACnD,aAAa,EAAE,cAAM,OAAA,+CAAiB,qBAAqB,GAAG,EAAzC,CAAyC;IAC9D,OAAO,EAAE,cAAM,OAAA,+CAAiB,aAAa,GAAG,EAAjC,CAAiC;IAChD,UAAU,EAAE,cAAM,OAAA,+CAAiB,kBAAkB,GAAG,EAAtC,CAAsC;IACxD,IAAI,EAAE,cAAM,OAAA,+CAAiB,WAAW,GAAG,EAA/B,CAA+B;IAC3C,IAAI,EAAE,cAAM,OAAA,+CAAiB,WAAW,GAAG,EAA/B,CAA+B;CAC5C,CAAC,EAX8B,CAW9B,CAAC,CAAC;AA5DJ;;;;;;;GAOG;AAEH,gDAA0B;AAC1B,gDAA4E;AAC5E,yCAA6C;AAC7C,+GAAyF;AACzF,oDAAuE;AAiDvE,IAAM,cAAc,GAAG,kBAAoD,CAAC;AAE5E,QAAQ,CAAC,iCAAiC,EAAE;IAC1C,IAAM,QAAQ,GAAG;QACf,SAAS,EAAE;YACT;gBACE,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,8BAA8B;gBACrC,WAAW,EAAE,qCAAqC;gBAClD,GAAG,EAAE,qCAAqC;gBAC1C,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,eAAe;gBACzB,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,iBAAiB;gBACzB,QAAQ,EAAE,UAAU;gBACpB,IAAI,EAAE,MAAM;gBACZ,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,GAAG;gBACjB,WAAW,EAAE,EAAE;aAChB;YACD;gBACE,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,yBAAyB;gBAChC,WAAW,EAAE,2CAA2C;gBACxD,GAAG,EAAE,+BAA+B;gBACpC,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,cAAc;gBACxB,UAAU,EAAE,cAAc;gBAC1B,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,SAAS;gBACnB,IAAI,EAAE,MAAM;gBACZ,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAE,EAAE;aAChB;SACF;QACD,oBAAoB,EAAE,EAAE;QACxB,SAAS,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;QACrC,oBAAoB,EAAE,kCAAkC;KACzD,CAAC;IAEF,UAAU,CAAC;QACT,0BAA0B;QAC1B,cAAc,CAAC,eAAe,CAAC;YAC7B,IAAI,EAAE;gBACJ,IAAI,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE,kBAAkB,EAAE;gBACvD,OAAO,EAAE,YAAY;aACtB;YACD,MAAM,EAAE,eAAe;SACxB,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,CAAC,KAAK,GAAG,IAAA,6BAAe,EAAC;YAC7B;gBACE,GAAG,EAAE,6BAA6B;gBAClC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC5C;YACD;gBACE,GAAG,EAAE,wBAAwB;gBAC7B,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,kBAAkB,EAAE;aACzD;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC;QACR,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;YAElC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;;;;wBAC1C,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCAC7E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC1E,CAAC,CAAC,EAAA;;wBAHF,SAGE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE;;;;wBAC9C,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,uBAAuB;gCACvB,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCAC7E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCACpF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCAChE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCACzD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACtD,CAAC,CAAC,EAAA;;wBAPF,SAOE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;;;;wBACtD,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCAC7E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCACzD,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;4BACjF,CAAC,CAAC,EAAA;;wBAJF,SAIE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,+BAA+B,EAAE;;;;;wBAClC,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC/E,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEG,eAAe,GAAG,cAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;wBAClD,cAAc,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,MAAM;;4BAChD,OAAA,CAAA,MAAA,MAAM,CAAC,WAAW,0CAAE,QAAQ,CAAC,UAAU,CAAC;gCACxC,MAAM,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAA;yBAAA,CACtD,CAAC;6BAEE,cAAc,EAAd,wBAAc;wBAChB,iBAAS,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;wBAEhC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CACvC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,EACjD,MAAM,CAAC,gBAAgB,CAAC;oCACtB,MAAM,EAAE,MAAM;oCACd,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;wCAC/B,cAAc,EAAE,kBAAkB;qCACnC,CAAC;oCACF,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;iCAC5C,CAAC,CACH,CAAC;4BACJ,CAAC,CAAC,EAAA;;wBAXF,SAWE,CAAC;;;;;aAEN,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;;;;;wBACvC,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC/E,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEG,WAAW,GAAG,cAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;wBAC9C,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,UAAA,MAAM,YACxC,OAAA,MAAA,MAAM,CAAC,WAAW,0CAAE,QAAQ,CAAC,eAAe,CAAC,CAAA,EAAA,CAC9C,CAAC;wBAEF,IAAI,UAAU,EAAE,CAAC;4BACf,iBAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;4BAE5B,8CAA8C;4BAC9C,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,CAAC;wBACtC,CAAC;;;;aACF,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;;;;;wBACpC,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC/E,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEG,YAAY,GAAG,cAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;wBAC/C,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,UAAA,MAAM,YAC1C,OAAA,MAAA,MAAM,CAAC,WAAW,0CAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAA,EAAA,CAC/C,CAAC;6BAEE,WAAW,EAAX,wBAAW;wBACb,iBAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;wBAE7B,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CACvC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,EACjD,MAAM,CAAC,gBAAgB,CAAC;oCACtB,MAAM,EAAE,MAAM;oCACd,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC;iCAC7C,CAAC,CACH,CAAC;4BACJ,CAAC,CAAC,EAAA;;wBARF,SAQE,CAAC;;;;;aAEN,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,qCAAqC,EAAE;;;;wBACxC,MAAM,CAAC,KAAK,GAAG,IAAA,6BAAe,EAAC;4BAC7B;gCACE,GAAG,EAAE,6BAA6B;gCAClC,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE;gCAChE,MAAM,EAAE,GAAG;6BACZ;yBACF,CAAC,CAAC;wBAEH,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC1F,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE;;;;wBACjC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;wBAEvE,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACzD,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;wBACtC,MAAM,CAAC,KAAK,GAAG,IAAA,6BAAe,EAAC;4BAC7B;gCACE,GAAG,EAAE,6BAA6B;gCAClC,QAAQ,EAAE;oCACR,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE;wCACJ,SAAS,EAAE,EAAE;wCACb,oBAAoB,EAAE,EAAE;wCACxB,SAAS,EAAE,EAAE;wCACb,oBAAoB,EAAE,EAAE;qCACzB;iCACF;6BACF;yBACF,CAAC,CAAC;wBAEH,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC3F,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,EAAE,CAAC,oCAAoC,EAAE;YACvC,cAAc,CAAC,eAAe,CAAC;gBAC7B,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,iBAAiB;aAC1B,CAAC,CAAC;YAEH,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;YAElC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;YAC/C,cAAc,CAAC,eAAe,CAAC;gBAC7B,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;YAElC,iEAAiE;YACjE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,mCAAmC,EAAE;YACtC,uBAAuB;YACvB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,EAAE;gBAC1C,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;YAElC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;YACvC,wBAAwB;YACxB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,EAAE;gBAC1C,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YAEH,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;YAElC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,gCAAgC,EAAE;;;;;wBACnC,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC/E,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAGG,OAAO,GAAG,cAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;wBAChD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aAC3C,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;;;;;wBACvC,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;wBAElC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC/E,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEG,OAAO,GAAG,cAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;wBAChD,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;4BACpB,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;YACxC,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;YAElC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE;QACtB,EAAE,CAAC,+BAA+B,EAAE;;;;;wBAC1B,OAAO,GAAK,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,QAAtC,CAAuC;wBAEtD,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAClF,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEH,OAAO,EAAE,CAAC;wBAEV,mCAAmC;wBACnC,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,iCAAiC,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;;;;aACvF,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;gBAC9B,QAAQ,GAAK,IAAA,cAAM,EAAC,uBAAC,+BAAqB,KAAG,CAAC,SAAtC,CAAuC;gBAEvD,+BAA+B;gBAC/B,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC5B,cAAc,CAAC,eAAe,CAAC;wBAC7B,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,eAAQ,CAAC,CAAE,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE;wBAC1D,MAAM,EAAE,eAAe;qBACxB,CAAC,CAAC;oBAEH,QAAQ,CAAC,uBAAC,+BAAqB,KAAG,CAAC,CAAC;gBACtC,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;aACjF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/personalized-resources.test.tsx"], "sourcesContent": ["/**\n * Personalized Resources Tests\n * \n * Tests Personalized Resources component functionality, rendering, user interactions, and edge cases.\n * \n * @category unit\n * @requires React Testing Library, component mocking\n */\n\nimport React from 'react';\nimport { render, screen, fireEvent, waitFor } from '@testing-library/react';\nimport { useSession } from 'next-auth/react';\nimport PersonalizedResources from '../../src/components/dashboard/PersonalizedResources';\nimport { createMockFetch, mockNextRouter } from '../utils/testHelpers';\nimport { testLearningResources } from '../fixtures/testData';\n\n// Mock Next.js router and Link\njest.mock('next/router', () => ({\n  useRouter: () => mockNextRouter\n}));\n\njest.mock('next/link', () => {\n  return ({ children, href, ...props }: any) => (\n    <a href={href} {...props}>{children}</a>\n  );\n});\n\n// Mock NextAuth\njest.mock('next-auth/react', () => ({\n  useSession: jest.fn()\n}));\n\n// Mock UI components\njest.mock('../../src/components/ui/button', () => ({\n  Button: ({ children, onClick, className, asChild, ...props }: any) => {\n    if (asChild) {\n      return <div data-testid=\"button\" className={className} {...props}>{children}</div>;\n    }\n    return <button data-testid=\"button\" onClick={onClick} className={className} {...props}>{children}</button>;\n  },\n}));\n\njest.mock('../../src/components/ui/badge', () => ({\n  Badge: ({ children, variant, ...props }: any) => (\n    <span data-testid=\"badge\" data-variant={variant} {...props}>{children}</span>\n  ),\n}));\n\n// Mock Lucide React icons\njest.mock('lucide-react', () => ({\n  BookOpen: () => <div data-testid=\"book-open-icon\" />,\n  Clock: () => <div data-testid=\"clock-icon\" />,\n  Star: () => <div data-testid=\"star-icon\" />,\n  ExternalLink: () => <div data-testid=\"external-link-icon\" />,\n  Bookmark: () => <div data-testid=\"bookmark-icon\" />,\n  BookmarkCheck: () => <div data-testid=\"bookmark-check-icon\" />,\n  Loader2: () => <div data-testid=\"loader-icon\" />,\n  TrendingUp: () => <div data-testid=\"trending-up-icon\" />,\n  Play: () => <div data-testid=\"play-icon\" />,\n  User: () => <div data-testid=\"user-icon\" />,\n}));\n\nconst mockUseSession = useSession as jest.MockedFunction<typeof useSession>;\n\ndescribe('PersonalizedResources Component', () => {\n  const mockData = {\n    resources: [\n      {\n        id: '1',\n        title: 'Ethical Hacking Fundamentals',\n        description: 'Learn the basics of ethical hacking',\n        url: 'https://example.com/ethical-hacking',\n        type: 'COURSE',\n        category: 'CYBERSECURITY',\n        skillLevel: 'BEGINNER',\n        author: 'Security Expert',\n        duration: '40 hours',\n        cost: 'FREE',\n        averageRating: 4.5,\n        totalRatings: 120,\n        careerPaths: []\n      },\n      {\n        id: '2',\n        title: 'Machine Learning Basics',\n        description: 'Introduction to machine learning concepts',\n        url: 'https://example.com/ml-basics',\n        type: 'ARTICLE',\n        category: 'DATA_SCIENCE',\n        skillLevel: 'INTERMEDIATE',\n        author: 'Data Scientist',\n        duration: '2 hours',\n        cost: 'FREE',\n        averageRating: 4.2,\n        totalRatings: 85,\n        careerPaths: []\n      }\n    ],\n    suggestedCareerPaths: [],\n    interests: ['technology', 'security'],\n    recommendationReason: 'Based on your assessment results'\n  };\n\n  beforeEach(() => {\n    // Mock successful session\n    mockUseSession.mockReturnValue({\n      data: {\n        user: { id: 'test-user-id', email: '<EMAIL>' },\n        expires: '2024-12-31'\n      },\n      status: 'authenticated'\n    });\n\n    // Mock fetch for API calls\n    global.fetch = createMockFetch([\n      {\n        url: '/api/personalized-resources',\n        response: { success: true, data: mockData }\n      },\n      {\n        url: '/api/learning-progress',\n        response: { success: true, message: 'Progress updated' }\n      }\n    ]);\n  });\n\n  afterEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('Component Rendering', () => {\n    it('should render component with loading state', () => {\n      render(<PersonalizedResources />);\n\n      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();\n    });\n\n    it('should render resources after loading', async () => {\n      render(<PersonalizedResources />);\n      \n      await waitFor(() => {\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n        expect(screen.getByText('Machine Learning Basics')).toBeInTheDocument();\n      });\n    });\n\n    it('should display resource details correctly', async () => {\n      render(<PersonalizedResources />);\n\n      await waitFor(() => {\n        // Check first resource\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n        expect(screen.getByText('Learn the basics of ethical hacking')).toBeInTheDocument();\n        expect(screen.getByText(/Security Expert/)).toBeInTheDocument();\n        expect(screen.getByText(/40 hours/)).toBeInTheDocument();\n        expect(screen.getByText('4.5')).toBeInTheDocument();\n      });\n    });\n\n    it('should show appropriate badges for resource types', async () => {\n      render(<PersonalizedResources />);\n\n      await waitFor(() => {\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n        expect(screen.getByText('beginner')).toBeInTheDocument();\n        expect(screen.getAllByText('Free')).toHaveLength(2); // Both resources are free\n      });\n    });\n  });\n\n  describe('User Interactions', () => {\n    it('should handle bookmark toggle', async () => {\n      render(<PersonalizedResources />);\n      \n      await waitFor(() => {\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n      });\n\n      const bookmarkButtons = screen.getAllByTestId('button');\n      const bookmarkButton = bookmarkButtons.find(button => \n        button.textContent?.includes('Bookmark') || \n        button.querySelector('[data-testid=\"bookmark-icon\"]')\n      );\n\n      if (bookmarkButton) {\n        fireEvent.click(bookmarkButton);\n        \n        await waitFor(() => {\n          expect(global.fetch).toHaveBeenCalledWith(\n            expect.stringContaining('/api/learning-progress'),\n            expect.objectContaining({\n              method: 'POST',\n              headers: expect.objectContaining({\n                'Content-Type': 'application/json'\n              }),\n              body: expect.stringContaining('BOOKMARKED')\n            })\n          );\n        });\n      }\n    });\n\n    it('should handle external link clicks', async () => {\n      render(<PersonalizedResources />);\n      \n      await waitFor(() => {\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n      });\n\n      const viewButtons = screen.getAllByTestId('button');\n      const viewButton = viewButtons.find(button => \n        button.textContent?.includes('View Resource')\n      );\n\n      if (viewButton) {\n        fireEvent.click(viewButton);\n        \n        // Should open external link (mocked behavior)\n        expect(viewButton).toHaveBeenCalled;\n      }\n    });\n\n    it('should handle progress tracking', async () => {\n      render(<PersonalizedResources />);\n      \n      await waitFor(() => {\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n      });\n\n      const startButtons = screen.getAllByTestId('button');\n      const startButton = startButtons.find(button => \n        button.textContent?.includes('Start Learning')\n      );\n\n      if (startButton) {\n        fireEvent.click(startButton);\n        \n        await waitFor(() => {\n          expect(global.fetch).toHaveBeenCalledWith(\n            expect.stringContaining('/api/learning-progress'),\n            expect.objectContaining({\n              method: 'POST',\n              body: expect.stringContaining('IN_PROGRESS')\n            })\n          );\n        });\n      }\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle API errors gracefully', async () => {\n      global.fetch = createMockFetch([\n        {\n          url: '/api/personalized-resources',\n          response: { success: false, error: 'Failed to fetch resources' },\n          status: 500\n        }\n      ]);\n\n      render(<PersonalizedResources />);\n\n      await waitFor(() => {\n        expect(screen.getByText(/Failed to fetch personalized resources/i)).toBeInTheDocument();\n      });\n    });\n\n    it('should handle network errors', async () => {\n      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));\n\n      render(<PersonalizedResources />);\n      \n      await waitFor(() => {\n        expect(screen.getByText(/error/i)).toBeInTheDocument();\n      });\n    });\n\n    it('should handle empty resource list', async () => {\n      global.fetch = createMockFetch([\n        {\n          url: '/api/personalized-resources',\n          response: {\n            success: true,\n            data: {\n              resources: [],\n              suggestedCareerPaths: [],\n              interests: [],\n              recommendationReason: ''\n            }\n          }\n        }\n      ]);\n\n      render(<PersonalizedResources />);\n\n      await waitFor(() => {\n        expect(screen.getByText(/No personalized resources available yet/i)).toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('Authentication States', () => {\n    it('should handle unauthenticated user', () => {\n      mockUseSession.mockReturnValue({\n        data: null,\n        status: 'unauthenticated'\n      });\n\n      render(<PersonalizedResources />);\n\n      expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();\n    });\n\n    it('should handle loading authentication state', () => {\n      mockUseSession.mockReturnValue({\n        data: null,\n        status: 'loading'\n      });\n\n      render(<PersonalizedResources />);\n\n      // When session is loading, component shows unauthenticated state\n      expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();\n    });\n  });\n\n  describe('Responsive Design', () => {\n    it('should render correctly on mobile', () => {\n      // Mock mobile viewport\n      Object.defineProperty(window, 'innerWidth', {\n        writable: true,\n        configurable: true,\n        value: 375,\n      });\n\n      render(<PersonalizedResources />);\n      \n      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();\n    });\n\n    it('should render correctly on desktop', () => {\n      // Mock desktop viewport\n      Object.defineProperty(window, 'innerWidth', {\n        writable: true,\n        configurable: true,\n        value: 1920,\n      });\n\n      render(<PersonalizedResources />);\n      \n      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('should have proper ARIA labels', async () => {\n      render(<PersonalizedResources />);\n\n      await waitFor(() => {\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n      });\n\n      // Check that buttons are accessible\n      const buttons = screen.getAllByTestId('button');\n      expect(buttons.length).toBeGreaterThan(0);\n    });\n\n    it('should support keyboard navigation', async () => {\n      render(<PersonalizedResources />);\n      \n      await waitFor(() => {\n        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();\n      });\n\n      const buttons = screen.getAllByTestId('button');\n      buttons.forEach(button => {\n        expect(button).not.toHaveAttribute('tabIndex', '-1');\n      });\n    });\n\n    it('should have semantic HTML structure', () => {\n      render(<PersonalizedResources />);\n\n      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();\n    });\n  });\n\n  describe('Performance', () => {\n    it('should not cause memory leaks', async () => {\n      const { unmount } = render(<PersonalizedResources />);\n      \n      await waitFor(() => {\n        expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();\n      });\n\n      unmount();\n      \n      // Component should unmount cleanly\n      expect(screen.queryByText('Personalized Learning Resources')).not.toBeInTheDocument();\n    });\n\n    it('should handle rapid state changes', async () => {\n      const { rerender } = render(<PersonalizedResources />);\n      \n      // Rapidly change session state\n      for (let i = 0; i < 10; i++) {\n        mockUseSession.mockReturnValue({\n          data: { user: { id: `user-${i}` }, expires: '2024-12-31' },\n          status: 'authenticated'\n        });\n        \n        rerender(<PersonalizedResources />);\n      }\n      \n      // Should handle rapid changes without errors\n      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();\n    });\n  });\n});\n"], "version": 3}