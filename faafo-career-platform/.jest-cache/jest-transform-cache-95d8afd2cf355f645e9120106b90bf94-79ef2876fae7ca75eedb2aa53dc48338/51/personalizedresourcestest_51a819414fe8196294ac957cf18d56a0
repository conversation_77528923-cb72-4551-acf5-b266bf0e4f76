3cd71d48be186476351d6322d505f97c
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock Next.js router and Link
jest.mock('next/router', function () { return ({
    useRouter: function () { return testHelpers_1.mockNextRouter; }
}); });
jest.mock('next/link', function () {
    return function (_a) {
        var children = _a.children, href = _a.href, props = __rest(_a, ["children", "href"]);
        return ((0, jsx_runtime_1.jsx)("a", __assign({ href: href }, props, { children: children })));
    };
});
// Mock NextAuth
jest.mock('next-auth/react', function () { return ({
    useSession: jest.fn()
}); });
// Mock UI components
jest.mock('../../src/components/ui/button', function () { return ({
    Button: function (_a) {
        var children = _a.children, onClick = _a.onClick, className = _a.className, asChild = _a.asChild, props = __rest(_a, ["children", "onClick", "className", "asChild"]);
        if (asChild) {
            return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "button", className: className }, props, { children: children }));
        }
        return (0, jsx_runtime_1.jsx)("button", __assign({ "data-testid": "button", onClick: onClick, className: className }, props, { children: children }));
    },
}); });
jest.mock('../../src/components/ui/badge', function () { return ({
    Badge: function (_a) {
        var children = _a.children, variant = _a.variant, props = __rest(_a, ["children", "variant"]);
        return ((0, jsx_runtime_1.jsx)("span", __assign({ "data-testid": "badge", "data-variant": variant }, props, { children: children })));
    },
}); });
// Mock Lucide React icons
jest.mock('lucide-react', function () { return ({
    BookOpen: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "book-open-icon" }); },
    Clock: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "clock-icon" }); },
    Star: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "star-icon" }); },
    ExternalLink: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "external-link-icon" }); },
    Bookmark: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "bookmark-icon" }); },
    BookmarkCheck: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "bookmark-check-icon" }); },
    Loader2: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "loader-icon" }); },
    TrendingUp: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "trending-up-icon" }); },
    Play: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "play-icon" }); },
    User: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "user-icon" }); },
}); });
/**
 * Personalized Resources Tests
 *
 * Tests Personalized Resources component functionality, rendering, user interactions, and edge cases.
 *
 * @category unit
 * @requires React Testing Library, component mocking
 */
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
var react_3 = require("next-auth/react");
var PersonalizedResources_1 = __importDefault(require("../../src/components/dashboard/PersonalizedResources"));
var testHelpers_1 = require("../utils/testHelpers");
var mockUseSession = react_3.useSession;
describe('PersonalizedResources Component', function () {
    var mockData = {
        resources: [
            {
                id: '1',
                title: 'Ethical Hacking Fundamentals',
                description: 'Learn the basics of ethical hacking',
                url: 'https://example.com/ethical-hacking',
                type: 'COURSE',
                category: 'CYBERSECURITY',
                skillLevel: 'BEGINNER',
                author: 'Security Expert',
                duration: '40 hours',
                cost: 'FREE',
                averageRating: 4.5,
                totalRatings: 120,
                careerPaths: []
            },
            {
                id: '2',
                title: 'Machine Learning Basics',
                description: 'Introduction to machine learning concepts',
                url: 'https://example.com/ml-basics',
                type: 'ARTICLE',
                category: 'DATA_SCIENCE',
                skillLevel: 'INTERMEDIATE',
                author: 'Data Scientist',
                duration: '2 hours',
                cost: 'FREE',
                averageRating: 4.2,
                totalRatings: 85,
                careerPaths: []
            }
        ],
        suggestedCareerPaths: [],
        interests: ['technology', 'security'],
        recommendationReason: 'Based on your assessment results'
    };
    beforeEach(function () {
        // Mock successful session
        mockUseSession.mockReturnValue({
            data: {
                user: { id: 'test-user-id', email: '<EMAIL>' },
                expires: '2024-12-31'
            },
            status: 'authenticated'
        });
        // Mock fetch for API calls
        global.fetch = (0, testHelpers_1.createMockFetch)([
            {
                url: '/api/personalized-resources',
                response: { success: true, data: mockData }
            },
            {
                url: '/api/learning-progress',
                response: { success: true, message: 'Progress updated' }
            }
        ]);
    });
    afterEach(function () {
        jest.clearAllMocks();
    });
    describe('Component Rendering', function () {
        it('should render component with loading state', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
            expect(react_2.screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
        });
        it('should render resources after loading', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                                expect(react_2.screen.getByText('Machine Learning Basics')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should display resource details correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                // Check first resource
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                                expect(react_2.screen.getByText('Learn the basics of ethical hacking')).toBeInTheDocument();
                                expect(react_2.screen.getByText(/Security Expert/)).toBeInTheDocument();
                                expect(react_2.screen.getByText(/40 hours/)).toBeInTheDocument();
                                expect(react_2.screen.getByText('4.5')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should show appropriate badges for resource types', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                                expect(react_2.screen.getByText('beginner')).toBeInTheDocument();
                                expect(react_2.screen.getAllByText('Free')).toHaveLength(2); // Both resources are free
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('User Interactions', function () {
        it('should handle bookmark toggle', function () { return __awaiter(void 0, void 0, void 0, function () {
            var bookmarkButtons, bookmarkButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        bookmarkButtons = react_2.screen.getAllByTestId('button');
                        bookmarkButton = bookmarkButtons.find(function (button) {
                            var _a;
                            return ((_a = button.textContent) === null || _a === void 0 ? void 0 : _a.includes('Bookmark')) ||
                                button.querySelector('[data-testid="bookmark-icon"]');
                        });
                        if (!bookmarkButton) return [3 /*break*/, 3];
                        react_2.fireEvent.click(bookmarkButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(global.fetch).toHaveBeenCalledWith(expect.stringContaining('/api/learning-progress'), expect.objectContaining({
                                    method: 'POST',
                                    headers: expect.objectContaining({
                                        'Content-Type': 'application/json'
                                    }),
                                    body: expect.stringContaining('BOOKMARKED')
                                }));
                            })];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3: return [2 /*return*/];
                }
            });
        }); });
        it('should handle external link clicks', function () { return __awaiter(void 0, void 0, void 0, function () {
            var viewButtons, viewButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        viewButtons = react_2.screen.getAllByTestId('button');
                        viewButton = viewButtons.find(function (button) { var _a; return (_a = button.textContent) === null || _a === void 0 ? void 0 : _a.includes('View Resource'); });
                        if (viewButton) {
                            react_2.fireEvent.click(viewButton);
                            // Should open external link (mocked behavior)
                            expect(viewButton).toHaveBeenCalled;
                        }
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle progress tracking', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startButtons, startButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        startButtons = react_2.screen.getAllByTestId('button');
                        startButton = startButtons.find(function (button) { var _a; return (_a = button.textContent) === null || _a === void 0 ? void 0 : _a.includes('Start Learning'); });
                        if (!startButton) return [3 /*break*/, 3];
                        react_2.fireEvent.click(startButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(global.fetch).toHaveBeenCalledWith(expect.stringContaining('/api/learning-progress'), expect.objectContaining({
                                    method: 'POST',
                                    body: expect.stringContaining('IN_PROGRESS')
                                }));
                            })];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3: return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Error Handling', function () {
        it('should handle API errors gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        global.fetch = (0, testHelpers_1.createMockFetch)([
                            {
                                url: '/api/personalized-resources',
                                response: { success: false, error: 'Failed to fetch resources' },
                                status: 500
                            }
                        ]);
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText(/Failed to fetch personalized resources/i)).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle network errors', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText(/error/i)).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle empty resource list', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        global.fetch = (0, testHelpers_1.createMockFetch)([
                            {
                                url: '/api/personalized-resources',
                                response: {
                                    success: true,
                                    data: {
                                        resources: [],
                                        suggestedCareerPaths: [],
                                        interests: [],
                                        recommendationReason: ''
                                    }
                                }
                            }
                        ]);
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText(/No personalized resources available yet/i)).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Authentication States', function () {
        it('should handle unauthenticated user', function () {
            mockUseSession.mockReturnValue({
                data: null,
                status: 'unauthenticated'
            });
            (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
            expect(react_2.screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
        });
        it('should handle loading authentication state', function () {
            mockUseSession.mockReturnValue({
                data: null,
                status: 'loading'
            });
            (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
            // When session is loading, component shows unauthenticated state
            expect(react_2.screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
        });
    });
    describe('Responsive Design', function () {
        it('should render correctly on mobile', function () {
            // Mock mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375,
            });
            (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
            expect(react_2.screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
        });
        it('should render correctly on desktop', function () {
            // Mock desktop viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 1920,
            });
            (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
            expect(react_2.screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
        });
    });
    describe('Accessibility', function () {
        it('should have proper ARIA labels', function () { return __awaiter(void 0, void 0, void 0, function () {
            var buttons;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        buttons = react_2.screen.getAllByTestId('button');
                        expect(buttons.length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should support keyboard navigation', function () { return __awaiter(void 0, void 0, void 0, function () {
            var buttons;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        buttons = react_2.screen.getAllByTestId('button');
                        buttons.forEach(function (button) {
                            expect(button).not.toHaveAttribute('tabIndex', '-1');
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        it('should have semantic HTML structure', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
            expect(react_2.screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
        });
    });
    describe('Performance', function () {
        it('should not cause memory leaks', function () { return __awaiter(void 0, void 0, void 0, function () {
            var unmount;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        unmount = (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {})).unmount;
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        unmount();
                        // Component should unmount cleanly
                        expect(react_2.screen.queryByText('Personalized Learning Resources')).not.toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle rapid state changes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var rerender, i;
            return __generator(this, function (_a) {
                rerender = (0, react_2.render)((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {})).rerender;
                // Rapidly change session state
                for (i = 0; i < 10; i++) {
                    mockUseSession.mockReturnValue({
                        data: { user: { id: "user-".concat(i) }, expires: '2024-12-31' },
                        status: 'authenticated'
                    });
                    rerender((0, jsx_runtime_1.jsx)(PersonalizedResources_1.default, {}));
                }
                // Should handle rapid changes without errors
                expect(react_2.screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
                return [2 /*return*/];
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************