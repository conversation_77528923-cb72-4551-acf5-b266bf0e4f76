{"version": 3, "names": ["server_1", "cov_28w5c7bav7", "s", "require", "next_1", "auth_1", "prisma_1", "__importDefault", "unified_api_error_handler_1", "csrf_1", "rateLimit_1", "exports", "GET", "withUnifiedErrorHandling", "request", "f", "__awaiter", "getServerSession", "authOptions", "session", "_b", "sent", "b", "_a", "user", "id", "error", "Error", "statusCode", "searchParams", "URL", "url", "type", "get", "includeAll", "default", "achievement", "find<PERSON>any", "where", "isActive", "include", "userAchievements", "userId", "orderBy", "createdAt", "allAchievements", "achievementsWithStatus", "map", "__assign", "isUnlocked", "length", "unlockedAt", "progress", "NextResponse", "json", "success", "data", "achievements", "total", "unlocked", "filter", "a", "<PERSON><PERSON><PERSON><PERSON>", "userAchievement", "ua", "POST", "withCSRFProtection", "withRateLimit", "windowMs", "maxRequests", "body", "achievementId", "findUnique", "userId_achievementId", "existingUserAchievement", "create", "status", "PUT", "update", "updatedUserAchievement"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/achievements/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withRateLimit } from '@/lib/rateLimit';\n\ninterface AchievementsResponse {\n  achievements: any[];\n  total: number;\n  unlocked?: number;\n}\n\ninterface UserAchievementResponse {\n  id: string;\n  userId: string;\n  achievementId: string;\n  progress?: number;\n  unlockedAt: Date;\n  achievement: any;\n}\n// GET handler to retrieve user achievements\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Unauthorized') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const { searchParams } = new URL(request.url);\n  const type = searchParams.get('type');\n  const includeAll = searchParams.get('includeAll') === 'true';\n\n  if (includeAll) {\n    // Get all achievements with user's unlock status\n    const allAchievements = await prisma.achievement.findMany({\n      where: { isActive: true },\n      include: {\n        userAchievements: {\n          where: { userId: session.user.id },\n        },\n      },\n      orderBy: { createdAt: 'asc' },\n    });\n\n    const achievementsWithStatus = allAchievements.map(achievement => ({\n      ...achievement,\n      isUnlocked: achievement.userAchievements.length > 0,\n      unlockedAt: achievement.userAchievements[0]?.unlockedAt || null,\n      progress: achievement.userAchievements[0]?.progress || null,\n    }));\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        achievements: achievementsWithStatus,\n        total: allAchievements.length,\n        unlocked: achievementsWithStatus.filter(a => a.isUnlocked).length,\n      }\n    });\n  } else {\n    // Get only user's unlocked achievements\n    const whereClause: any = {\n      userId: session.user.id,\n    };\n\n    const userAchievements = await prisma.userAchievement.findMany({\n      where: whereClause,\n      include: {\n        achievement: true,\n      },\n      orderBy: { unlockedAt: 'desc' },\n    });\n\n    const achievements = userAchievements\n      .filter(ua => ua.achievement && (!type || ua.achievement.type === type))\n      .map(ua => ({\n        ...ua.achievement,\n        unlockedAt: ua.unlockedAt,\n        progress: ua.progress,\n      }));\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        achievements,\n        total: achievements.length,\n      }\n    });\n  }\n});\n\n// POST handler to unlock an achievement for a user\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 20 },\n      async () => {\n        const session = await getServerSession(authOptions);\n\n        if (!session?.user?.id) {\n          const error = new Error('Unauthorized') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const body = await request.json();\n        const { achievementId, progress } = body;\n\n        if (!achievementId) {\n          const error = new Error('Achievement ID is required') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Check if achievement exists\n        const achievement = await prisma.achievement.findUnique({\n          where: { id: achievementId, isActive: true },\n        });\n\n        if (!achievement) {\n          const error = new Error('Achievement not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        // Check if user already has this achievement\n        const existingUserAchievement = await prisma.userAchievement.findUnique({\n          where: {\n            userId_achievementId: {\n              userId: session.user.id,\n              achievementId,\n            },\n          },\n        });\n\n        if (existingUserAchievement) {\n          const error = new Error('Achievement already unlocked') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Create user achievement\n        const userAchievement = await prisma.userAchievement.create({\n          data: {\n            userId: session.user.id,\n            achievementId,\n            progress,\n          },\n          include: {\n            achievement: true,\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: userAchievement\n        }, { status: 201 });\n      }\n    );\n  });\n});\n\n// PUT handler to update achievement progress\nexport const PUT = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 30 },\n      async () => {\n        const session = await getServerSession(authOptions);\n\n        if (!session?.user?.id) {\n          const error = new Error('Unauthorized') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const body = await request.json();\n        const { achievementId, progress } = body;\n\n        if (!achievementId) {\n          const error = new Error('Achievement ID is required') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Check if user has this achievement\n        const userAchievement = await prisma.userAchievement.findUnique({\n          where: {\n            userId_achievementId: {\n              userId: session.user.id,\n              achievementId,\n            },\n          },\n        });\n\n        if (!userAchievement) {\n          const error = new Error('User achievement not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        // Update progress\n        const updatedUserAchievement = await prisma.userAchievement.update({\n          where: {\n            userId_achievementId: {\n              userId: session.user.id,\n              achievementId,\n            },\n          },\n          data: { progress },\n          include: {\n            achievement: true,\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: updatedUserAchievement\n        });\n      }\n    );\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAG,QAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AACA,IAAAK,2BAAA;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAM,MAAA;AAAA;AAAA,CAAAR,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAO,WAAA;AAAA;AAAA,CAAAT,cAAA,GAAAC,CAAA,QAAAC,OAAA;AAgBA;AAAA;AAAAF,cAAA,GAAAC,CAAA;AACaS,OAAA,CAAAC,GAAG,GAAG,IAAAJ,2BAAA,CAAAK,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAb,cAAA,GAAAc,CAAA;EAAAd,cAAA,GAAAC,CAAA;EAAA,OAAAc,SAAA;IAAA;IAAAf,cAAA,GAAAc,CAAA;;;;;;;;;;;;;;UACrD,qBAAM,IAAAX,MAAA,CAAAa,gBAAgB,EAACZ,MAAA,CAAAa,WAAW,CAAC;;;;;UAA7CC,OAAO,GAAGC,EAAA,CAAAC,IAAA,EAAmC;UAAA;UAAApB,cAAA,GAAAC,CAAA;UAEnD,IAAI;UAAC;UAAA,CAAAD,cAAA,GAAAqB,CAAA,YAAAC,EAAA;UAAA;UAAA,CAAAtB,cAAA,GAAAqB,CAAA,WAAAH,OAAO;UAAA;UAAA,CAAAlB,cAAA,GAAAqB,CAAA,WAAPH,OAAO;UAAA;UAAA,CAAAlB,cAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAPH,OAAO,CAAEK,IAAI;UAAA;UAAA,CAAAvB,cAAA,GAAAqB,CAAA,WAAAC,EAAA;UAAA;UAAA,CAAAtB,cAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAAC,EAAA,CAAEE,EAAE,IAAE;YAAA;YAAAxB,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAC,CAAA;YAChBwB,KAAK,GAAG,IAAIC,KAAK,CAAC,cAAc,CAAQ;YAAC;YAAA1B,cAAA,GAAAC,CAAA;YAC/CwB,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA3B,cAAA,GAAAC,CAAA;YACvB,MAAMwB,KAAK;UACb,CAAC;UAAA;UAAA;YAAAzB,cAAA,GAAAqB,CAAA;UAAA;UAAArB,cAAA,GAAAC,CAAA;UAEO2B,YAAY,GAAK,IAAIC,GAAG,CAAChB,OAAO,CAACiB,GAAG,CAAC,CAAAF,YAAzB;UAA0B;UAAA5B,cAAA,GAAAC,CAAA;UACxC8B,IAAI,GAAGH,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC;UAAC;UAAAhC,cAAA,GAAAC,CAAA;UAChCgC,UAAU,GAAGL,YAAY,CAACI,GAAG,CAAC,YAAY,CAAC,KAAK,MAAM;UAAC;UAAAhC,cAAA,GAAAC,CAAA;eAEzDgC,UAAU,EAAV;YAAA;YAAAjC,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAC,CAAA;YAAA;UAAA,CAAU;UAAA;UAAA;YAAAD,cAAA,GAAAqB,CAAA;UAAA;UAAArB,cAAA,GAAAC,CAAA;UAEY,qBAAMI,QAAA,CAAA6B,OAAM,CAACC,WAAW,CAACC,QAAQ,CAAC;YACxDC,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YACzBC,OAAO,EAAE;cACPC,gBAAgB,EAAE;gBAChBH,KAAK,EAAE;kBAAEI,MAAM,EAAEvB,OAAO,CAACK,IAAI,CAACC;gBAAE;;aAEnC;YACDkB,OAAO,EAAE;cAAEC,SAAS,EAAE;YAAK;WAC5B,CAAC;;;;;UARIC,eAAe,GAAGzB,EAAA,CAAAC,IAAA,EAQtB;UAAA;UAAApB,cAAA,GAAAC,CAAA;UAEI4C,sBAAsB,GAAGD,eAAe,CAACE,GAAG,CAAC,UAAAX,WAAW;YAAA;YAAAnC,cAAA,GAAAc,CAAA;;;;YAAI,OAAAiC,QAAA,CAAAA,QAAA,KAC7DZ,WAAW;cACda,UAAU,EAAEb,WAAW,CAACK,gBAAgB,CAACS,MAAM,GAAG,CAAC;cACnDC,UAAU;cAAE;cAAA,CAAAlD,cAAA,GAAAqB,CAAA;cAAA;cAAA,CAAArB,cAAA,GAAAqB,CAAA,YAAAC,EAAA,GAAAa,WAAW,CAACK,gBAAgB,CAAC,CAAC,CAAC;cAAA;cAAA,CAAAxC,cAAA,GAAAqB,CAAA,WAAAC,EAAA;cAAA;cAAA,CAAAtB,cAAA,GAAAqB,CAAA;cAAA;cAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAAC,EAAA,CAAE4B,UAAU;cAAA;cAAA,CAAAlD,cAAA,GAAAqB,CAAA,WAAI,IAAI;cAC/D8B,QAAQ;cAAE;cAAA,CAAAnD,cAAA,GAAAqB,CAAA;cAAA;cAAA,CAAArB,cAAA,GAAAqB,CAAA,YAAAF,EAAA,GAAAgB,WAAW,CAACK,gBAAgB,CAAC,CAAC,CAAC;cAAA;cAAA,CAAAxC,cAAA,GAAAqB,CAAA,WAAAF,EAAA;cAAA;cAAA,CAAAnB,cAAA,GAAAqB,CAAA;cAAA;cAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAAF,EAAA,CAAEgC,QAAQ;cAAA;cAAA,CAAAnD,cAAA,GAAAqB,CAAA,WAAI,IAAI;YAAA;WAC3D,CAAC;UAAC;UAAArB,cAAA,GAAAC,CAAA;UAEJ,sBAAOF,QAAA,CAAAqD,YAAY,CAACC,IAAI,CAAC;YACvBC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACJC,YAAY,EAAEX,sBAAsB;cACpCY,KAAK,EAAEb,eAAe,CAACK,MAAM;cAC7BS,QAAQ,EAAEb,sBAAsB,CAACc,MAAM,CAAC,UAAAC,CAAC;gBAAA;gBAAA5D,cAAA,GAAAc,CAAA;gBAAAd,cAAA,GAAAC,CAAA;gBAAI,OAAA2D,CAAC,CAACZ,UAAU;cAAZ,CAAY,CAAC,CAACC;;WAE9D,CAAC;;;;;UAGIY,WAAW,GAAQ;YACvBpB,MAAM,EAAEvB,OAAO,CAACK,IAAI,CAACC;WACtB;UAAC;UAAAxB,cAAA,GAAAC,CAAA;UAEuB,qBAAMI,QAAA,CAAA6B,OAAM,CAAC4B,eAAe,CAAC1B,QAAQ,CAAC;YAC7DC,KAAK,EAAEwB,WAAW;YAClBtB,OAAO,EAAE;cACPJ,WAAW,EAAE;aACd;YACDO,OAAO,EAAE;cAAEQ,UAAU,EAAE;YAAM;WAC9B,CAAC;;;;;UANIV,gBAAgB,GAAGrB,EAAA,CAAAC,IAAA,EAMvB;UAAA;UAAApB,cAAA,GAAAC,CAAA;UAEIuD,YAAY,GAAGhB,gBAAgB,CAClCmB,MAAM,CAAC,UAAAI,EAAE;YAAA;YAAA/D,cAAA,GAAAc,CAAA;YAAAd,cAAA,GAAAC,CAAA;YAAI,kCAAAD,cAAA,GAAAqB,CAAA,WAAA0C,EAAE,CAAC5B,WAAW;YAAK;YAAA,CAAAnC,cAAA,GAAAqB,CAAA,YAACU,IAAI;YAAA;YAAA,CAAA/B,cAAA,GAAAqB,CAAA,WAAI0C,EAAE,CAAC5B,WAAW,CAACJ,IAAI,KAAKA,IAAI,EAAC;UAAzD,CAAyD,CAAC,CACvEe,GAAG,CAAC,UAAAiB,EAAE;YAAA;YAAA/D,cAAA,GAAAc,CAAA;YAAAd,cAAA,GAAAC,CAAA;YAAI,OAAA8C,QAAA,CAAAA,QAAA,KACNgB,EAAE,CAAC5B,WAAW;cACjBe,UAAU,EAAEa,EAAE,CAACb,UAAU;cACzBC,QAAQ,EAAEY,EAAE,CAACZ;YAAQ;UAHZ,CAIT,CAAC;UAAC;UAAAnD,cAAA,GAAAC,CAAA;UAEN,sBAAOF,QAAA,CAAAqD,YAAY,CAACC,IAAI,CAAC;YACvBC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACJC,YAAY,EAAAA,YAAA;cACZC,KAAK,EAAED,YAAY,CAACP;;WAEvB,CAAC;;;;CAEL,CAAC;AAEF;AAAA;AAAAjD,cAAA,GAAAC,CAAA;AACaS,OAAA,CAAAsD,IAAI,GAAG,IAAAzD,2BAAA,CAAAK,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAb,cAAA,GAAAc,CAAA;EAAAd,cAAA,GAAAC,CAAA;EAAA,OAAAc,SAAA;IAAA;IAAAf,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;;;;;MACtE,sBAAO,IAAAO,MAAA,CAAAyD,kBAAkB,EAACpD,OAAO,EAAE;QAAA;QAAAb,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAAA,OAAAc,SAAA;UAAA;UAAAf,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;;;;;YACjC,sBAAO,IAAAQ,WAAA,CAAAyD,aAAa,EAClBrD,OAAO,EACP;cAAEsD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;cAAEC,WAAW,EAAE;YAAE,CAAE,EAC7C;cAAA;cAAApE,cAAA,GAAAc,CAAA;cAAAd,cAAA,GAAAC,CAAA;cAAA,OAAAc,SAAA;gBAAA;gBAAAf,cAAA,GAAAc,CAAA;;;;;;;;;;;;;;sBACkB,qBAAM,IAAAX,MAAA,CAAAa,gBAAgB,EAACZ,MAAA,CAAAa,WAAW,CAAC;;;;;sBAA7CC,OAAO,GAAGC,EAAA,CAAAC,IAAA,EAAmC;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBAEnD,IAAI;sBAAC;sBAAA,CAAAD,cAAA,GAAAqB,CAAA,YAAAC,EAAA;sBAAA;sBAAA,CAAAtB,cAAA,GAAAqB,CAAA,WAAAH,OAAO;sBAAA;sBAAA,CAAAlB,cAAA,GAAAqB,CAAA,WAAPH,OAAO;sBAAA;sBAAA,CAAAlB,cAAA,GAAAqB,CAAA;sBAAA;sBAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAPH,OAAO,CAAEK,IAAI;sBAAA;sBAAA,CAAAvB,cAAA,GAAAqB,CAAA,WAAAC,EAAA;sBAAA;sBAAA,CAAAtB,cAAA,GAAAqB,CAAA;sBAAA;sBAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAAC,EAAA,CAAEE,EAAE,IAAE;wBAAA;wBAAAxB,cAAA,GAAAqB,CAAA;wBAAArB,cAAA,GAAAC,CAAA;wBAChBwB,KAAK,GAAG,IAAIC,KAAK,CAAC,cAAc,CAAQ;wBAAC;wBAAA1B,cAAA,GAAAC,CAAA;wBAC/CwB,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAA3B,cAAA,GAAAC,CAAA;wBACvB,MAAMwB,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAzB,cAAA,GAAAqB,CAAA;sBAAA;sBAAArB,cAAA,GAAAC,CAAA;sBAEY,qBAAMY,OAAO,CAACwC,IAAI,EAAE;;;;;sBAA3BgB,IAAI,GAAGlD,EAAA,CAAAC,IAAA,EAAoB;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBACzBqE,aAAa,GAAeD,IAAI,CAAAC,aAAnB,EAAEnB,QAAQ,GAAKkB,IAAI,CAAAlB,QAAT;sBAAU;sBAAAnD,cAAA,GAAAC,CAAA;sBAEzC,IAAI,CAACqE,aAAa,EAAE;wBAAA;wBAAAtE,cAAA,GAAAqB,CAAA;wBAAArB,cAAA,GAAAC,CAAA;wBACZwB,KAAK,GAAG,IAAIC,KAAK,CAAC,4BAA4B,CAAQ;wBAAC;wBAAA1B,cAAA,GAAAC,CAAA;wBAC7DwB,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAA3B,cAAA,GAAAC,CAAA;wBACvB,MAAMwB,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAzB,cAAA,GAAAqB,CAAA;sBAAA;sBAAArB,cAAA,GAAAC,CAAA;sBAGmB,qBAAMI,QAAA,CAAA6B,OAAM,CAACC,WAAW,CAACoC,UAAU,CAAC;wBACtDlC,KAAK,EAAE;0BAAEb,EAAE,EAAE8C,aAAa;0BAAEhC,QAAQ,EAAE;wBAAI;uBAC3C,CAAC;;;;;sBAFIH,WAAW,GAAGhB,EAAA,CAAAC,IAAA,EAElB;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBAEF,IAAI,CAACkC,WAAW,EAAE;wBAAA;wBAAAnC,cAAA,GAAAqB,CAAA;wBAAArB,cAAA,GAAAC,CAAA;wBACVwB,KAAK,GAAG,IAAIC,KAAK,CAAC,uBAAuB,CAAQ;wBAAC;wBAAA1B,cAAA,GAAAC,CAAA;wBACxDwB,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAA3B,cAAA,GAAAC,CAAA;wBACvB,MAAMwB,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAzB,cAAA,GAAAqB,CAAA;sBAAA;sBAAArB,cAAA,GAAAC,CAAA;sBAG+B,qBAAMI,QAAA,CAAA6B,OAAM,CAAC4B,eAAe,CAACS,UAAU,CAAC;wBACtElC,KAAK,EAAE;0BACLmC,oBAAoB,EAAE;4BACpB/B,MAAM,EAAEvB,OAAO,CAACK,IAAI,CAACC,EAAE;4BACvB8C,aAAa,EAAAA;;;uBAGlB,CAAC;;;;;sBAPIG,uBAAuB,GAAGtD,EAAA,CAAAC,IAAA,EAO9B;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBAEF,IAAIwE,uBAAuB,EAAE;wBAAA;wBAAAzE,cAAA,GAAAqB,CAAA;wBAAArB,cAAA,GAAAC,CAAA;wBACrBwB,KAAK,GAAG,IAAIC,KAAK,CAAC,8BAA8B,CAAQ;wBAAC;wBAAA1B,cAAA,GAAAC,CAAA;wBAC/DwB,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAA3B,cAAA,GAAAC,CAAA;wBACvB,MAAMwB,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAzB,cAAA,GAAAqB,CAAA;sBAAA;sBAAArB,cAAA,GAAAC,CAAA;sBAGuB,qBAAMI,QAAA,CAAA6B,OAAM,CAAC4B,eAAe,CAACY,MAAM,CAAC;wBAC1DnB,IAAI,EAAE;0BACJd,MAAM,EAAEvB,OAAO,CAACK,IAAI,CAACC,EAAE;0BACvB8C,aAAa,EAAAA,aAAA;0BACbnB,QAAQ,EAAAA;yBACT;wBACDZ,OAAO,EAAE;0BACPJ,WAAW,EAAE;;uBAEhB,CAAC;;;;;sBATI2B,eAAe,GAAG3C,EAAA,CAAAC,IAAA,EAStB;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBAEF,sBAAOF,QAAA,CAAAqD,YAAY,CAACC,IAAI,CAAC;wBACvBC,OAAO,EAAE,IAAI;wBACbC,IAAI,EAAEO;uBACP,EAAE;wBAAEa,MAAM,EAAE;sBAAG,CAAE,CAAC;;;;aACpB,CACF;;;OACF,CAAC;;;CACH,CAAC;AAEF;AAAA;AAAA3E,cAAA,GAAAC,CAAA;AACaS,OAAA,CAAAkE,GAAG,GAAG,IAAArE,2BAAA,CAAAK,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAb,cAAA,GAAAc,CAAA;EAAAd,cAAA,GAAAC,CAAA;EAAA,OAAAc,SAAA;IAAA;IAAAf,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;;;;;MACrE,sBAAO,IAAAO,MAAA,CAAAyD,kBAAkB,EAACpD,OAAO,EAAE;QAAA;QAAAb,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAAA,OAAAc,SAAA;UAAA;UAAAf,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;;;;;YACjC,sBAAO,IAAAQ,WAAA,CAAAyD,aAAa,EAClBrD,OAAO,EACP;cAAEsD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;cAAEC,WAAW,EAAE;YAAE,CAAE,EAC7C;cAAA;cAAApE,cAAA,GAAAc,CAAA;cAAAd,cAAA,GAAAC,CAAA;cAAA,OAAAc,SAAA;gBAAA;gBAAAf,cAAA,GAAAc,CAAA;;;;;;;;;;;;;;sBACkB,qBAAM,IAAAX,MAAA,CAAAa,gBAAgB,EAACZ,MAAA,CAAAa,WAAW,CAAC;;;;;sBAA7CC,OAAO,GAAGC,EAAA,CAAAC,IAAA,EAAmC;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBAEnD,IAAI;sBAAC;sBAAA,CAAAD,cAAA,GAAAqB,CAAA,YAAAC,EAAA;sBAAA;sBAAA,CAAAtB,cAAA,GAAAqB,CAAA,WAAAH,OAAO;sBAAA;sBAAA,CAAAlB,cAAA,GAAAqB,CAAA,WAAPH,OAAO;sBAAA;sBAAA,CAAAlB,cAAA,GAAAqB,CAAA;sBAAA;sBAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAPH,OAAO,CAAEK,IAAI;sBAAA;sBAAA,CAAAvB,cAAA,GAAAqB,CAAA,WAAAC,EAAA;sBAAA;sBAAA,CAAAtB,cAAA,GAAAqB,CAAA;sBAAA;sBAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAAC,EAAA,CAAEE,EAAE,IAAE;wBAAA;wBAAAxB,cAAA,GAAAqB,CAAA;wBAAArB,cAAA,GAAAC,CAAA;wBAChBwB,KAAK,GAAG,IAAIC,KAAK,CAAC,cAAc,CAAQ;wBAAC;wBAAA1B,cAAA,GAAAC,CAAA;wBAC/CwB,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAA3B,cAAA,GAAAC,CAAA;wBACvB,MAAMwB,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAzB,cAAA,GAAAqB,CAAA;sBAAA;sBAAArB,cAAA,GAAAC,CAAA;sBAEY,qBAAMY,OAAO,CAACwC,IAAI,EAAE;;;;;sBAA3BgB,IAAI,GAAGlD,EAAA,CAAAC,IAAA,EAAoB;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBACzBqE,aAAa,GAAeD,IAAI,CAAAC,aAAnB,EAAEnB,QAAQ,GAAKkB,IAAI,CAAAlB,QAAT;sBAAU;sBAAAnD,cAAA,GAAAC,CAAA;sBAEzC,IAAI,CAACqE,aAAa,EAAE;wBAAA;wBAAAtE,cAAA,GAAAqB,CAAA;wBAAArB,cAAA,GAAAC,CAAA;wBACZwB,KAAK,GAAG,IAAIC,KAAK,CAAC,4BAA4B,CAAQ;wBAAC;wBAAA1B,cAAA,GAAAC,CAAA;wBAC7DwB,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAA3B,cAAA,GAAAC,CAAA;wBACvB,MAAMwB,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAzB,cAAA,GAAAqB,CAAA;sBAAA;sBAAArB,cAAA,GAAAC,CAAA;sBAGuB,qBAAMI,QAAA,CAAA6B,OAAM,CAAC4B,eAAe,CAACS,UAAU,CAAC;wBAC9DlC,KAAK,EAAE;0BACLmC,oBAAoB,EAAE;4BACpB/B,MAAM,EAAEvB,OAAO,CAACK,IAAI,CAACC,EAAE;4BACvB8C,aAAa,EAAAA;;;uBAGlB,CAAC;;;;;sBAPIR,eAAe,GAAG3C,EAAA,CAAAC,IAAA,EAOtB;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBAEF,IAAI,CAAC6D,eAAe,EAAE;wBAAA;wBAAA9D,cAAA,GAAAqB,CAAA;wBAAArB,cAAA,GAAAC,CAAA;wBACdwB,KAAK,GAAG,IAAIC,KAAK,CAAC,4BAA4B,CAAQ;wBAAC;wBAAA1B,cAAA,GAAAC,CAAA;wBAC7DwB,KAAK,CAACE,UAAU,GAAG,GAAG;wBAAC;wBAAA3B,cAAA,GAAAC,CAAA;wBACvB,MAAMwB,KAAK;sBACb,CAAC;sBAAA;sBAAA;wBAAAzB,cAAA,GAAAqB,CAAA;sBAAA;sBAAArB,cAAA,GAAAC,CAAA;sBAG8B,qBAAMI,QAAA,CAAA6B,OAAM,CAAC4B,eAAe,CAACe,MAAM,CAAC;wBACjExC,KAAK,EAAE;0BACLmC,oBAAoB,EAAE;4BACpB/B,MAAM,EAAEvB,OAAO,CAACK,IAAI,CAACC,EAAE;4BACvB8C,aAAa,EAAAA;;yBAEhB;wBACDf,IAAI,EAAE;0BAAEJ,QAAQ,EAAAA;wBAAA,CAAE;wBAClBZ,OAAO,EAAE;0BACPJ,WAAW,EAAE;;uBAEhB,CAAC;;;;;sBAXI2C,sBAAsB,GAAG3D,EAAA,CAAAC,IAAA,EAW7B;sBAAA;sBAAApB,cAAA,GAAAC,CAAA;sBAEF,sBAAOF,QAAA,CAAAqD,YAAY,CAACC,IAAI,CAAC;wBACvBC,OAAO,EAAE,IAAI;wBACbC,IAAI,EAAEuB;uBACP,CAAC;;;;aACH,CACF;;;OACF,CAAC;;;CACH,CAAC", "ignoreList": []}