5d247151988202c320bc81bb15f773f5
"use strict";

/* istanbul ignore next */
function cov_28w5c7bav7() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/achievements/route.ts";
  var hash = "275f3697c87181c481333838476d030f00500a5d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/achievements/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 50
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 13
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "85": {
        start: {
          line: 57,
          column: 15
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "86": {
        start: {
          line: 58,
          column: 34
        },
        end: {
          line: 58,
          column: 76
        }
      },
      "87": {
        start: {
          line: 59,
          column: 13
        },
        end: {
          line: 59,
          column: 34
        }
      },
      "88": {
        start: {
          line: 60,
          column: 18
        },
        end: {
          line: 60,
          column: 44
        }
      },
      "89": {
        start: {
          line: 62,
          column: 0
        },
        end: {
          line: 127,
          column: 7
        }
      },
      "90": {
        start: {
          line: 62,
          column: 93
        },
        end: {
          line: 127,
          column: 3
        }
      },
      "91": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 126,
          column: 7
        }
      },
      "92": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 125,
          column: 9
        }
      },
      "93": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 67,
          column: 91
        }
      },
      "94": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 36
        }
      },
      "95": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 74,
          column: 17
        }
      },
      "96": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 71,
          column: 54
        }
      },
      "97": {
        start: {
          line: 72,
          column: 20
        },
        end: {
          line: 72,
          column: 43
        }
      },
      "98": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 32
        }
      },
      "99": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 75,
          column: 65
        }
      },
      "100": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 76,
          column: 48
        }
      },
      "101": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 71
        }
      },
      "102": {
        start: {
          line: 78,
          column: 16
        },
        end: {
          line: 78,
          column: 57
        }
      },
      "103": {
        start: {
          line: 78,
          column: 33
        },
        end: {
          line: 78,
          column: 57
        }
      },
      "104": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 87,
          column: 24
        }
      },
      "105": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 44
        }
      },
      "106": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 93,
          column: 19
        }
      },
      "107": {
        start: {
          line: 92,
          column: 20
        },
        end: {
          line: 92,
          column: 344
        }
      },
      "108": {
        start: {
          line: 94,
          column: 16
        },
        end: {
          line: 101,
          column: 24
        }
      },
      "109": {
        start: {
          line: 99,
          column: 83
        },
        end: {
          line: 99,
          column: 103
        }
      },
      "110": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 105,
          column: 18
        }
      },
      "111": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 112,
          column: 24
        }
      },
      "112": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 114,
          column: 45
        }
      },
      "113": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 117,
          column: 147
        }
      },
      "114": {
        start: {
          line: 116,
          column: 44
        },
        end: {
          line: 116,
          column: 109
        }
      },
      "115": {
        start: {
          line: 117,
          column: 41
        },
        end: {
          line: 117,
          column: 143
        }
      },
      "116": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 124,
          column: 24
        }
      },
      "117": {
        start: {
          line: 129,
          column: 0
        },
        end: {
          line: 202,
          column: 7
        }
      },
      "118": {
        start: {
          line: 129,
          column: 94
        },
        end: {
          line: 202,
          column: 3
        }
      },
      "119": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 201,
          column: 7
        }
      },
      "120": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 200,
          column: 20
        }
      },
      "121": {
        start: {
          line: 131,
          column: 84
        },
        end: {
          line: 200,
          column: 15
        }
      },
      "122": {
        start: {
          line: 132,
          column: 16
        },
        end: {
          line: 199,
          column: 19
        }
      },
      "123": {
        start: {
          line: 133,
          column: 20
        },
        end: {
          line: 198,
          column: 32
        }
      },
      "124": {
        start: {
          line: 133,
          column: 143
        },
        end: {
          line: 198,
          column: 27
        }
      },
      "125": {
        start: {
          line: 136,
          column: 28
        },
        end: {
          line: 197,
          column: 31
        }
      },
      "126": {
        start: {
          line: 137,
          column: 32
        },
        end: {
          line: 196,
          column: 33
        }
      },
      "127": {
        start: {
          line: 138,
          column: 44
        },
        end: {
          line: 138,
          column: 115
        }
      },
      "128": {
        start: {
          line: 140,
          column: 40
        },
        end: {
          line: 140,
          column: 60
        }
      },
      "129": {
        start: {
          line: 141,
          column: 40
        },
        end: {
          line: 145,
          column: 41
        }
      },
      "130": {
        start: {
          line: 142,
          column: 44
        },
        end: {
          line: 142,
          column: 78
        }
      },
      "131": {
        start: {
          line: 143,
          column: 44
        },
        end: {
          line: 143,
          column: 67
        }
      },
      "132": {
        start: {
          line: 144,
          column: 44
        },
        end: {
          line: 144,
          column: 56
        }
      },
      "133": {
        start: {
          line: 146,
          column: 40
        },
        end: {
          line: 146,
          column: 77
        }
      },
      "134": {
        start: {
          line: 148,
          column: 40
        },
        end: {
          line: 148,
          column: 57
        }
      },
      "135": {
        start: {
          line: 149,
          column: 40
        },
        end: {
          line: 149,
          column: 101
        }
      },
      "136": {
        start: {
          line: 150,
          column: 40
        },
        end: {
          line: 154,
          column: 41
        }
      },
      "137": {
        start: {
          line: 151,
          column: 44
        },
        end: {
          line: 151,
          column: 92
        }
      },
      "138": {
        start: {
          line: 152,
          column: 44
        },
        end: {
          line: 152,
          column: 67
        }
      },
      "139": {
        start: {
          line: 153,
          column: 44
        },
        end: {
          line: 153,
          column: 56
        }
      },
      "140": {
        start: {
          line: 155,
          column: 40
        },
        end: {
          line: 157,
          column: 48
        }
      },
      "141": {
        start: {
          line: 159,
          column: 40
        },
        end: {
          line: 159,
          column: 64
        }
      },
      "142": {
        start: {
          line: 160,
          column: 40
        },
        end: {
          line: 164,
          column: 41
        }
      },
      "143": {
        start: {
          line: 161,
          column: 44
        },
        end: {
          line: 161,
          column: 87
        }
      },
      "144": {
        start: {
          line: 162,
          column: 44
        },
        end: {
          line: 162,
          column: 67
        }
      },
      "145": {
        start: {
          line: 163,
          column: 44
        },
        end: {
          line: 163,
          column: 56
        }
      },
      "146": {
        start: {
          line: 165,
          column: 40
        },
        end: {
          line: 172,
          column: 48
        }
      },
      "147": {
        start: {
          line: 174,
          column: 40
        },
        end: {
          line: 174,
          column: 76
        }
      },
      "148": {
        start: {
          line: 175,
          column: 40
        },
        end: {
          line: 179,
          column: 41
        }
      },
      "149": {
        start: {
          line: 176,
          column: 44
        },
        end: {
          line: 176,
          column: 94
        }
      },
      "150": {
        start: {
          line: 177,
          column: 44
        },
        end: {
          line: 177,
          column: 67
        }
      },
      "151": {
        start: {
          line: 178,
          column: 44
        },
        end: {
          line: 178,
          column: 56
        }
      },
      "152": {
        start: {
          line: 180,
          column: 40
        },
        end: {
          line: 189,
          column: 48
        }
      },
      "153": {
        start: {
          line: 191,
          column: 40
        },
        end: {
          line: 191,
          column: 68
        }
      },
      "154": {
        start: {
          line: 192,
          column: 40
        },
        end: {
          line: 195,
          column: 65
        }
      },
      "155": {
        start: {
          line: 204,
          column: 0
        },
        end: {
          line: 269,
          column: 7
        }
      },
      "156": {
        start: {
          line: 204,
          column: 93
        },
        end: {
          line: 269,
          column: 3
        }
      },
      "157": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 268,
          column: 7
        }
      },
      "158": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 267,
          column: 20
        }
      },
      "159": {
        start: {
          line: 206,
          column: 84
        },
        end: {
          line: 267,
          column: 15
        }
      },
      "160": {
        start: {
          line: 207,
          column: 16
        },
        end: {
          line: 266,
          column: 19
        }
      },
      "161": {
        start: {
          line: 208,
          column: 20
        },
        end: {
          line: 265,
          column: 32
        }
      },
      "162": {
        start: {
          line: 208,
          column: 143
        },
        end: {
          line: 265,
          column: 27
        }
      },
      "163": {
        start: {
          line: 211,
          column: 28
        },
        end: {
          line: 264,
          column: 31
        }
      },
      "164": {
        start: {
          line: 212,
          column: 32
        },
        end: {
          line: 263,
          column: 33
        }
      },
      "165": {
        start: {
          line: 213,
          column: 44
        },
        end: {
          line: 213,
          column: 115
        }
      },
      "166": {
        start: {
          line: 215,
          column: 40
        },
        end: {
          line: 215,
          column: 60
        }
      },
      "167": {
        start: {
          line: 216,
          column: 40
        },
        end: {
          line: 220,
          column: 41
        }
      },
      "168": {
        start: {
          line: 217,
          column: 44
        },
        end: {
          line: 217,
          column: 78
        }
      },
      "169": {
        start: {
          line: 218,
          column: 44
        },
        end: {
          line: 218,
          column: 67
        }
      },
      "170": {
        start: {
          line: 219,
          column: 44
        },
        end: {
          line: 219,
          column: 56
        }
      },
      "171": {
        start: {
          line: 221,
          column: 40
        },
        end: {
          line: 221,
          column: 77
        }
      },
      "172": {
        start: {
          line: 223,
          column: 40
        },
        end: {
          line: 223,
          column: 57
        }
      },
      "173": {
        start: {
          line: 224,
          column: 40
        },
        end: {
          line: 224,
          column: 101
        }
      },
      "174": {
        start: {
          line: 225,
          column: 40
        },
        end: {
          line: 229,
          column: 41
        }
      },
      "175": {
        start: {
          line: 226,
          column: 44
        },
        end: {
          line: 226,
          column: 92
        }
      },
      "176": {
        start: {
          line: 227,
          column: 44
        },
        end: {
          line: 227,
          column: 67
        }
      },
      "177": {
        start: {
          line: 228,
          column: 44
        },
        end: {
          line: 228,
          column: 56
        }
      },
      "178": {
        start: {
          line: 230,
          column: 40
        },
        end: {
          line: 237,
          column: 48
        }
      },
      "179": {
        start: {
          line: 239,
          column: 40
        },
        end: {
          line: 239,
          column: 68
        }
      },
      "180": {
        start: {
          line: 240,
          column: 40
        },
        end: {
          line: 244,
          column: 41
        }
      },
      "181": {
        start: {
          line: 241,
          column: 44
        },
        end: {
          line: 241,
          column: 92
        }
      },
      "182": {
        start: {
          line: 242,
          column: 44
        },
        end: {
          line: 242,
          column: 67
        }
      },
      "183": {
        start: {
          line: 243,
          column: 44
        },
        end: {
          line: 243,
          column: 56
        }
      },
      "184": {
        start: {
          line: 245,
          column: 40
        },
        end: {
          line: 256,
          column: 48
        }
      },
      "185": {
        start: {
          line: 258,
          column: 40
        },
        end: {
          line: 258,
          column: 75
        }
      },
      "186": {
        start: {
          line: 259,
          column: 40
        },
        end: {
          line: 262,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 62,
            column: 72
          },
          end: {
            line: 62,
            column: 73
          }
        },
        loc: {
          start: {
            line: 62,
            column: 91
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 62
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 62,
            column: 134
          },
          end: {
            line: 62,
            column: 135
          }
        },
        loc: {
          start: {
            line: 62,
            column: 146
          },
          end: {
            line: 127,
            column: 1
          }
        },
        line: 62
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 65,
            column: 29
          },
          end: {
            line: 65,
            column: 30
          }
        },
        loc: {
          start: {
            line: 65,
            column: 43
          },
          end: {
            line: 126,
            column: 5
          }
        },
        line: 65
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 90,
            column: 61
          },
          end: {
            line: 90,
            column: 62
          }
        },
        loc: {
          start: {
            line: 90,
            column: 84
          },
          end: {
            line: 93,
            column: 17
          }
        },
        line: 90
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 99,
            column: 68
          },
          end: {
            line: 99,
            column: 69
          }
        },
        loc: {
          start: {
            line: 99,
            column: 81
          },
          end: {
            line: 99,
            column: 105
          }
        },
        line: 99
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 116,
            column: 28
          },
          end: {
            line: 116,
            column: 29
          }
        },
        loc: {
          start: {
            line: 116,
            column: 42
          },
          end: {
            line: 116,
            column: 111
          }
        },
        line: 116
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 117,
            column: 25
          },
          end: {
            line: 117,
            column: 26
          }
        },
        loc: {
          start: {
            line: 117,
            column: 39
          },
          end: {
            line: 117,
            column: 145
          }
        },
        line: 117
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 129,
            column: 73
          },
          end: {
            line: 129,
            column: 74
          }
        },
        loc: {
          start: {
            line: 129,
            column: 92
          },
          end: {
            line: 202,
            column: 5
          }
        },
        line: 129
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 129,
            column: 135
          },
          end: {
            line: 129,
            column: 136
          }
        },
        loc: {
          start: {
            line: 129,
            column: 147
          },
          end: {
            line: 202,
            column: 1
          }
        },
        line: 129
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 130,
            column: 29
          },
          end: {
            line: 130,
            column: 30
          }
        },
        loc: {
          start: {
            line: 130,
            column: 43
          },
          end: {
            line: 201,
            column: 5
          }
        },
        line: 130
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 131,
            column: 70
          },
          end: {
            line: 131,
            column: 71
          }
        },
        loc: {
          start: {
            line: 131,
            column: 82
          },
          end: {
            line: 200,
            column: 17
          }
        },
        line: 131
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 131,
            column: 125
          },
          end: {
            line: 131,
            column: 126
          }
        },
        loc: {
          start: {
            line: 131,
            column: 137
          },
          end: {
            line: 200,
            column: 13
          }
        },
        line: 131
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 132,
            column: 41
          },
          end: {
            line: 132,
            column: 42
          }
        },
        loc: {
          start: {
            line: 132,
            column: 55
          },
          end: {
            line: 199,
            column: 17
          }
        },
        line: 132
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 133,
            column: 129
          },
          end: {
            line: 133,
            column: 130
          }
        },
        loc: {
          start: {
            line: 133,
            column: 141
          },
          end: {
            line: 198,
            column: 29
          }
        },
        line: 133
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 133,
            column: 184
          },
          end: {
            line: 133,
            column: 185
          }
        },
        loc: {
          start: {
            line: 133,
            column: 196
          },
          end: {
            line: 198,
            column: 25
          }
        },
        line: 133
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 136,
            column: 53
          },
          end: {
            line: 136,
            column: 54
          }
        },
        loc: {
          start: {
            line: 136,
            column: 67
          },
          end: {
            line: 197,
            column: 29
          }
        },
        line: 136
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 204,
            column: 72
          },
          end: {
            line: 204,
            column: 73
          }
        },
        loc: {
          start: {
            line: 204,
            column: 91
          },
          end: {
            line: 269,
            column: 5
          }
        },
        line: 204
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 204,
            column: 134
          },
          end: {
            line: 204,
            column: 135
          }
        },
        loc: {
          start: {
            line: 204,
            column: 146
          },
          end: {
            line: 269,
            column: 1
          }
        },
        line: 204
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 205,
            column: 29
          },
          end: {
            line: 205,
            column: 30
          }
        },
        loc: {
          start: {
            line: 205,
            column: 43
          },
          end: {
            line: 268,
            column: 5
          }
        },
        line: 205
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 206,
            column: 70
          },
          end: {
            line: 206,
            column: 71
          }
        },
        loc: {
          start: {
            line: 206,
            column: 82
          },
          end: {
            line: 267,
            column: 17
          }
        },
        line: 206
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 206,
            column: 125
          },
          end: {
            line: 206,
            column: 126
          }
        },
        loc: {
          start: {
            line: 206,
            column: 137
          },
          end: {
            line: 267,
            column: 13
          }
        },
        line: 206
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 207,
            column: 41
          },
          end: {
            line: 207,
            column: 42
          }
        },
        loc: {
          start: {
            line: 207,
            column: 55
          },
          end: {
            line: 266,
            column: 17
          }
        },
        line: 207
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 208,
            column: 129
          },
          end: {
            line: 208,
            column: 130
          }
        },
        loc: {
          start: {
            line: 208,
            column: 141
          },
          end: {
            line: 265,
            column: 29
          }
        },
        line: 208
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 208,
            column: 184
          },
          end: {
            line: 208,
            column: 185
          }
        },
        loc: {
          start: {
            line: 208,
            column: 196
          },
          end: {
            line: 265,
            column: 25
          }
        },
        line: 208
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 211,
            column: 53
          },
          end: {
            line: 211,
            column: 54
          }
        },
        loc: {
          start: {
            line: 211,
            column: 67
          },
          end: {
            line: 264,
            column: 29
          }
        },
        line: 211
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 66,
            column: 8
          },
          end: {
            line: 125,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 67,
            column: 91
          }
        }, {
          start: {
            line: 68,
            column: 12
          },
          end: {
            line: 87,
            column: 24
          }
        }, {
          start: {
            line: 88,
            column: 12
          },
          end: {
            line: 101,
            column: 24
          }
        }, {
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 112,
            column: 24
          }
        }, {
          start: {
            line: 113,
            column: 12
          },
          end: {
            line: 124,
            column: 24
          }
        }],
        line: 66
      },
      "39": {
        loc: {
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 74,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 74,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "40": {
        loc: {
          start: {
            line: 70,
            column: 22
          },
          end: {
            line: 70,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 120
          },
          end: {
            line: 70,
            column: 126
          }
        }, {
          start: {
            line: 70,
            column: 129
          },
          end: {
            line: 70,
            column: 134
          }
        }],
        line: 70
      },
      "41": {
        loc: {
          start: {
            line: 70,
            column: 22
          },
          end: {
            line: 70,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 22
          },
          end: {
            line: 70,
            column: 100
          }
        }, {
          start: {
            line: 70,
            column: 104
          },
          end: {
            line: 70,
            column: 117
          }
        }],
        line: 70
      },
      "42": {
        loc: {
          start: {
            line: 70,
            column: 28
          },
          end: {
            line: 70,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 69
          },
          end: {
            line: 70,
            column: 75
          }
        }, {
          start: {
            line: 70,
            column: 78
          },
          end: {
            line: 70,
            column: 90
          }
        }],
        line: 70
      },
      "43": {
        loc: {
          start: {
            line: 70,
            column: 28
          },
          end: {
            line: 70,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 28
          },
          end: {
            line: 70,
            column: 44
          }
        }, {
          start: {
            line: 70,
            column: 48
          },
          end: {
            line: 70,
            column: 66
          }
        }],
        line: 70
      },
      "44": {
        loc: {
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 78,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 78,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "45": {
        loc: {
          start: {
            line: 92,
            column: 131
          },
          end: {
            line: 92,
            column: 230
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 132
          },
          end: {
            line: 92,
            column: 221
          }
        }, {
          start: {
            line: 92,
            column: 226
          },
          end: {
            line: 92,
            column: 230
          }
        }],
        line: 92
      },
      "46": {
        loc: {
          start: {
            line: 92,
            column: 132
          },
          end: {
            line: 92,
            column: 221
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 92,
            column: 199
          },
          end: {
            line: 92,
            column: 205
          }
        }, {
          start: {
            line: 92,
            column: 208
          },
          end: {
            line: 92,
            column: 221
          }
        }],
        line: 92
      },
      "47": {
        loc: {
          start: {
            line: 92,
            column: 132
          },
          end: {
            line: 92,
            column: 196
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 132
          },
          end: {
            line: 92,
            column: 179
          }
        }, {
          start: {
            line: 92,
            column: 183
          },
          end: {
            line: 92,
            column: 196
          }
        }],
        line: 92
      },
      "48": {
        loc: {
          start: {
            line: 92,
            column: 242
          },
          end: {
            line: 92,
            column: 339
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 243
          },
          end: {
            line: 92,
            column: 330
          }
        }, {
          start: {
            line: 92,
            column: 335
          },
          end: {
            line: 92,
            column: 339
          }
        }],
        line: 92
      },
      "49": {
        loc: {
          start: {
            line: 92,
            column: 243
          },
          end: {
            line: 92,
            column: 330
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 92,
            column: 310
          },
          end: {
            line: 92,
            column: 316
          }
        }, {
          start: {
            line: 92,
            column: 319
          },
          end: {
            line: 92,
            column: 330
          }
        }],
        line: 92
      },
      "50": {
        loc: {
          start: {
            line: 92,
            column: 243
          },
          end: {
            line: 92,
            column: 307
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 243
          },
          end: {
            line: 92,
            column: 290
          }
        }, {
          start: {
            line: 92,
            column: 294
          },
          end: {
            line: 92,
            column: 307
          }
        }],
        line: 92
      },
      "51": {
        loc: {
          start: {
            line: 116,
            column: 51
          },
          end: {
            line: 116,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 51
          },
          end: {
            line: 116,
            column: 65
          }
        }, {
          start: {
            line: 116,
            column: 70
          },
          end: {
            line: 116,
            column: 75
          }
        }, {
          start: {
            line: 116,
            column: 79
          },
          end: {
            line: 116,
            column: 107
          }
        }],
        line: 116
      },
      "52": {
        loc: {
          start: {
            line: 137,
            column: 32
          },
          end: {
            line: 196,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 138,
            column: 36
          },
          end: {
            line: 138,
            column: 115
          }
        }, {
          start: {
            line: 139,
            column: 36
          },
          end: {
            line: 146,
            column: 77
          }
        }, {
          start: {
            line: 147,
            column: 36
          },
          end: {
            line: 157,
            column: 48
          }
        }, {
          start: {
            line: 158,
            column: 36
          },
          end: {
            line: 172,
            column: 48
          }
        }, {
          start: {
            line: 173,
            column: 36
          },
          end: {
            line: 189,
            column: 48
          }
        }, {
          start: {
            line: 190,
            column: 36
          },
          end: {
            line: 195,
            column: 65
          }
        }],
        line: 137
      },
      "53": {
        loc: {
          start: {
            line: 141,
            column: 40
          },
          end: {
            line: 145,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 141,
            column: 40
          },
          end: {
            line: 145,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 141
      },
      "54": {
        loc: {
          start: {
            line: 141,
            column: 46
          },
          end: {
            line: 141,
            column: 158
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 141,
            column: 144
          },
          end: {
            line: 141,
            column: 150
          }
        }, {
          start: {
            line: 141,
            column: 153
          },
          end: {
            line: 141,
            column: 158
          }
        }],
        line: 141
      },
      "55": {
        loc: {
          start: {
            line: 141,
            column: 46
          },
          end: {
            line: 141,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 141,
            column: 46
          },
          end: {
            line: 141,
            column: 124
          }
        }, {
          start: {
            line: 141,
            column: 128
          },
          end: {
            line: 141,
            column: 141
          }
        }],
        line: 141
      },
      "56": {
        loc: {
          start: {
            line: 141,
            column: 52
          },
          end: {
            line: 141,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 141,
            column: 93
          },
          end: {
            line: 141,
            column: 99
          }
        }, {
          start: {
            line: 141,
            column: 102
          },
          end: {
            line: 141,
            column: 114
          }
        }],
        line: 141
      },
      "57": {
        loc: {
          start: {
            line: 141,
            column: 52
          },
          end: {
            line: 141,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 141,
            column: 52
          },
          end: {
            line: 141,
            column: 68
          }
        }, {
          start: {
            line: 141,
            column: 72
          },
          end: {
            line: 141,
            column: 90
          }
        }],
        line: 141
      },
      "58": {
        loc: {
          start: {
            line: 150,
            column: 40
          },
          end: {
            line: 154,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 40
          },
          end: {
            line: 154,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "59": {
        loc: {
          start: {
            line: 160,
            column: 40
          },
          end: {
            line: 164,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 40
          },
          end: {
            line: 164,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "60": {
        loc: {
          start: {
            line: 175,
            column: 40
          },
          end: {
            line: 179,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 40
          },
          end: {
            line: 179,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "61": {
        loc: {
          start: {
            line: 212,
            column: 32
          },
          end: {
            line: 263,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 213,
            column: 36
          },
          end: {
            line: 213,
            column: 115
          }
        }, {
          start: {
            line: 214,
            column: 36
          },
          end: {
            line: 221,
            column: 77
          }
        }, {
          start: {
            line: 222,
            column: 36
          },
          end: {
            line: 237,
            column: 48
          }
        }, {
          start: {
            line: 238,
            column: 36
          },
          end: {
            line: 256,
            column: 48
          }
        }, {
          start: {
            line: 257,
            column: 36
          },
          end: {
            line: 262,
            column: 48
          }
        }],
        line: 212
      },
      "62": {
        loc: {
          start: {
            line: 216,
            column: 40
          },
          end: {
            line: 220,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 40
          },
          end: {
            line: 220,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 216
      },
      "63": {
        loc: {
          start: {
            line: 216,
            column: 46
          },
          end: {
            line: 216,
            column: 158
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 216,
            column: 144
          },
          end: {
            line: 216,
            column: 150
          }
        }, {
          start: {
            line: 216,
            column: 153
          },
          end: {
            line: 216,
            column: 158
          }
        }],
        line: 216
      },
      "64": {
        loc: {
          start: {
            line: 216,
            column: 46
          },
          end: {
            line: 216,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 46
          },
          end: {
            line: 216,
            column: 124
          }
        }, {
          start: {
            line: 216,
            column: 128
          },
          end: {
            line: 216,
            column: 141
          }
        }],
        line: 216
      },
      "65": {
        loc: {
          start: {
            line: 216,
            column: 52
          },
          end: {
            line: 216,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 216,
            column: 93
          },
          end: {
            line: 216,
            column: 99
          }
        }, {
          start: {
            line: 216,
            column: 102
          },
          end: {
            line: 216,
            column: 114
          }
        }],
        line: 216
      },
      "66": {
        loc: {
          start: {
            line: 216,
            column: 52
          },
          end: {
            line: 216,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 52
          },
          end: {
            line: 216,
            column: 68
          }
        }, {
          start: {
            line: 216,
            column: 72
          },
          end: {
            line: 216,
            column: 90
          }
        }],
        line: 216
      },
      "67": {
        loc: {
          start: {
            line: 225,
            column: 40
          },
          end: {
            line: 229,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 40
          },
          end: {
            line: 229,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      },
      "68": {
        loc: {
          start: {
            line: 240,
            column: 40
          },
          end: {
            line: 244,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 40
          },
          end: {
            line: 244,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0, 0],
      "52": [0, 0, 0, 0, 0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0, 0, 0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/achievements/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAClC,6EAAwF;AACxF,mCAAgD;AAChD,6CAAgD;AAgBhD,4CAA4C;AAC/B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;;;;oBACrD,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,CAAQ,CAAC;oBAC/C,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEO,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAChC,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,MAAM,CAAC;qBAEzD,UAAU,EAAV,wBAAU;gBAEY,qBAAM,gBAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;wBACxD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;wBACzB,OAAO,EAAE;4BACP,gBAAgB,EAAE;gCAChB,KAAK,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE;6BACnC;yBACF;wBACD,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;qBAC9B,CAAC,EAAA;;gBARI,eAAe,GAAG,SAQtB;gBAEI,sBAAsB,GAAG,eAAe,CAAC,GAAG,CAAC,UAAA,WAAW;;oBAAI,OAAA,uBAC7D,WAAW,KACd,UAAU,EAAE,WAAW,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EACnD,UAAU,EAAE,CAAA,MAAA,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,0CAAE,UAAU,KAAI,IAAI,EAC/D,QAAQ,EAAE,CAAA,MAAA,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,0CAAE,QAAQ,KAAI,IAAI,IAC3D,CAAA;iBAAA,CAAC,CAAC;gBAEJ,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,YAAY,EAAE,sBAAsB;4BACpC,KAAK,EAAE,eAAe,CAAC,MAAM;4BAC7B,QAAQ,EAAE,sBAAsB,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAZ,CAAY,CAAC,CAAC,MAAM;yBAClE;qBACF,CAAC,EAAC;;gBAGG,WAAW,GAAQ;oBACvB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;iBACxB,CAAC;gBAEuB,qBAAM,gBAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;wBAC7D,KAAK,EAAE,WAAW;wBAClB,OAAO,EAAE;4BACP,WAAW,EAAE,IAAI;yBAClB;wBACD,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;qBAChC,CAAC,EAAA;;gBANI,gBAAgB,GAAG,SAMvB;gBAEI,YAAY,GAAG,gBAAgB;qBAClC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,EAAzD,CAAyD,CAAC;qBACvE,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,uBACN,EAAE,CAAC,WAAW,KACjB,UAAU,EAAE,EAAE,CAAC,UAAU,EACzB,QAAQ,EAAE,EAAE,CAAC,QAAQ,IACrB,EAJS,CAIT,CAAC,CAAC;gBAEN,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,YAAY,cAAA;4BACZ,KAAK,EAAE,YAAY,CAAC,MAAM;yBAC3B;qBACF,CAAC,EAAC;;;KAEN,CAAC,CAAC;AAEH,mDAAmD;AACtC,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACtE,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAC7C;;;;;4CACkB,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;4CACjB,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,CAAQ,CAAC;4CAC/C,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAA3B,IAAI,GAAG,SAAoB;wCACzB,aAAa,GAAe,IAAI,cAAnB,EAAE,QAAQ,GAAK,IAAI,SAAT,CAAU;wCAEzC,IAAI,CAAC,aAAa,EAAE,CAAC;4CACb,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAQ,CAAC;4CAC7D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGmB,qBAAM,gBAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gDACtD,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE;6CAC7C,CAAC,EAAA;;wCAFI,WAAW,GAAG,SAElB;wCAEF,IAAI,CAAC,WAAW,EAAE,CAAC;4CACX,KAAK,GAAG,IAAI,KAAK,CAAC,uBAAuB,CAAQ,CAAC;4CACxD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAG+B,qBAAM,gBAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gDACtE,KAAK,EAAE;oDACL,oBAAoB,EAAE;wDACpB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;wDACvB,aAAa,eAAA;qDACd;iDACF;6CACF,CAAC,EAAA;;wCAPI,uBAAuB,GAAG,SAO9B;wCAEF,IAAI,uBAAuB,EAAE,CAAC;4CACtB,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,CAAQ,CAAC;4CAC/D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGuB,qBAAM,gBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gDAC1D,IAAI,EAAE;oDACJ,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;oDACvB,aAAa,eAAA;oDACb,QAAQ,UAAA;iDACT;gDACD,OAAO,EAAE;oDACP,WAAW,EAAE,IAAI;iDAClB;6CACF,CAAC,EAAA;;wCATI,eAAe,GAAG,SAStB;wCAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,eAAe;6CACtB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;;;6BACrB,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC;AAEH,6CAA6C;AAChC,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACrE,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAC7C;;;;;4CACkB,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;4CACjB,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,CAAQ,CAAC;4CAC/C,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAA3B,IAAI,GAAG,SAAoB;wCACzB,aAAa,GAAe,IAAI,cAAnB,EAAE,QAAQ,GAAK,IAAI,SAAT,CAAU;wCAEzC,IAAI,CAAC,aAAa,EAAE,CAAC;4CACb,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAQ,CAAC;4CAC7D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGuB,qBAAM,gBAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gDAC9D,KAAK,EAAE;oDACL,oBAAoB,EAAE;wDACpB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;wDACvB,aAAa,eAAA;qDACd;iDACF;6CACF,CAAC,EAAA;;wCAPI,eAAe,GAAG,SAOtB;wCAEF,IAAI,CAAC,eAAe,EAAE,CAAC;4CACf,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAQ,CAAC;4CAC7D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAG8B,qBAAM,gBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gDACjE,KAAK,EAAE;oDACL,oBAAoB,EAAE;wDACpB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;wDACvB,aAAa,eAAA;qDACd;iDACF;gDACD,IAAI,EAAE,EAAE,QAAQ,UAAA,EAAE;gDAClB,OAAO,EAAE;oDACP,WAAW,EAAE,IAAI;iDAClB;6CACF,CAAC,EAAA;;wCAXI,sBAAsB,GAAG,SAW7B;wCAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,sBAAsB;6CAC7B,CAAC,EAAC;;;6BACJ,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/achievements/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withRateLimit } from '@/lib/rateLimit';\n\ninterface AchievementsResponse {\n  achievements: any[];\n  total: number;\n  unlocked?: number;\n}\n\ninterface UserAchievementResponse {\n  id: string;\n  userId: string;\n  achievementId: string;\n  progress?: number;\n  unlockedAt: Date;\n  achievement: any;\n}\n// GET handler to retrieve user achievements\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Unauthorized') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const { searchParams } = new URL(request.url);\n  const type = searchParams.get('type');\n  const includeAll = searchParams.get('includeAll') === 'true';\n\n  if (includeAll) {\n    // Get all achievements with user's unlock status\n    const allAchievements = await prisma.achievement.findMany({\n      where: { isActive: true },\n      include: {\n        userAchievements: {\n          where: { userId: session.user.id },\n        },\n      },\n      orderBy: { createdAt: 'asc' },\n    });\n\n    const achievementsWithStatus = allAchievements.map(achievement => ({\n      ...achievement,\n      isUnlocked: achievement.userAchievements.length > 0,\n      unlockedAt: achievement.userAchievements[0]?.unlockedAt || null,\n      progress: achievement.userAchievements[0]?.progress || null,\n    }));\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        achievements: achievementsWithStatus,\n        total: allAchievements.length,\n        unlocked: achievementsWithStatus.filter(a => a.isUnlocked).length,\n      }\n    });\n  } else {\n    // Get only user's unlocked achievements\n    const whereClause: any = {\n      userId: session.user.id,\n    };\n\n    const userAchievements = await prisma.userAchievement.findMany({\n      where: whereClause,\n      include: {\n        achievement: true,\n      },\n      orderBy: { unlockedAt: 'desc' },\n    });\n\n    const achievements = userAchievements\n      .filter(ua => ua.achievement && (!type || ua.achievement.type === type))\n      .map(ua => ({\n        ...ua.achievement,\n        unlockedAt: ua.unlockedAt,\n        progress: ua.progress,\n      }));\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        achievements,\n        total: achievements.length,\n      }\n    });\n  }\n});\n\n// POST handler to unlock an achievement for a user\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 20 },\n      async () => {\n        const session = await getServerSession(authOptions);\n\n        if (!session?.user?.id) {\n          const error = new Error('Unauthorized') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const body = await request.json();\n        const { achievementId, progress } = body;\n\n        if (!achievementId) {\n          const error = new Error('Achievement ID is required') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Check if achievement exists\n        const achievement = await prisma.achievement.findUnique({\n          where: { id: achievementId, isActive: true },\n        });\n\n        if (!achievement) {\n          const error = new Error('Achievement not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        // Check if user already has this achievement\n        const existingUserAchievement = await prisma.userAchievement.findUnique({\n          where: {\n            userId_achievementId: {\n              userId: session.user.id,\n              achievementId,\n            },\n          },\n        });\n\n        if (existingUserAchievement) {\n          const error = new Error('Achievement already unlocked') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Create user achievement\n        const userAchievement = await prisma.userAchievement.create({\n          data: {\n            userId: session.user.id,\n            achievementId,\n            progress,\n          },\n          include: {\n            achievement: true,\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: userAchievement\n        }, { status: 201 });\n      }\n    );\n  });\n});\n\n// PUT handler to update achievement progress\nexport const PUT = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 30 },\n      async () => {\n        const session = await getServerSession(authOptions);\n\n        if (!session?.user?.id) {\n          const error = new Error('Unauthorized') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const body = await request.json();\n        const { achievementId, progress } = body;\n\n        if (!achievementId) {\n          const error = new Error('Achievement ID is required') as any;\n          error.statusCode = 400;\n          throw error;\n        }\n\n        // Check if user has this achievement\n        const userAchievement = await prisma.userAchievement.findUnique({\n          where: {\n            userId_achievementId: {\n              userId: session.user.id,\n              achievementId,\n            },\n          },\n        });\n\n        if (!userAchievement) {\n          const error = new Error('User achievement not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        // Update progress\n        const updatedUserAchievement = await prisma.userAchievement.update({\n          where: {\n            userId_achievementId: {\n              userId: session.user.id,\n              achievementId,\n            },\n          },\n          data: { progress },\n          include: {\n            achievement: true,\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: updatedUserAchievement\n        });\n      }\n    );\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "275f3697c87181c481333838476d030f00500a5d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_28w5c7bav7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_28w5c7bav7();
var __assign =
/* istanbul ignore next */
(cov_28w5c7bav7().s[0]++,
/* istanbul ignore next */
(cov_28w5c7bav7().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_28w5c7bav7().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_28w5c7bav7().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_28w5c7bav7().f[0]++;
  cov_28w5c7bav7().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[1]++;
    cov_28w5c7bav7().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_28w5c7bav7().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_28w5c7bav7().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_28w5c7bav7().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_28w5c7bav7().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_28w5c7bav7().b[2][0]++;
          cov_28w5c7bav7().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_28w5c7bav7().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_28w5c7bav7().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_28w5c7bav7().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_28w5c7bav7().s[11]++,
/* istanbul ignore next */
(cov_28w5c7bav7().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_28w5c7bav7().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_28w5c7bav7().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_28w5c7bav7().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[3]++;
    cov_28w5c7bav7().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[4]++;
      cov_28w5c7bav7().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_28w5c7bav7().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[6]++;
      cov_28w5c7bav7().s[15]++;
      try {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[7]++;
      cov_28w5c7bav7().s[18]++;
      try {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[8]++;
      cov_28w5c7bav7().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_28w5c7bav7().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_28w5c7bav7().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_28w5c7bav7().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_28w5c7bav7().s[23]++,
/* istanbul ignore next */
(cov_28w5c7bav7().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_28w5c7bav7().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_28w5c7bav7().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_28w5c7bav7().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_28w5c7bav7().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_28w5c7bav7().f[10]++;
        cov_28w5c7bav7().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_28w5c7bav7().b[9][0]++;
          cov_28w5c7bav7().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_28w5c7bav7().b[9][1]++;
        }
        cov_28w5c7bav7().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_28w5c7bav7().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_28w5c7bav7().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[11]++;
    cov_28w5c7bav7().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[12]++;
    cov_28w5c7bav7().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[13]++;
      cov_28w5c7bav7().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[14]++;
    cov_28w5c7bav7().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_28w5c7bav7().b[12][0]++;
      cov_28w5c7bav7().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_28w5c7bav7().b[12][1]++;
    }
    cov_28w5c7bav7().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_28w5c7bav7().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_28w5c7bav7().s[36]++;
      try {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[18][0]++,
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[19][1]++,
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_28w5c7bav7().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_28w5c7bav7().b[15][0]++;
          cov_28w5c7bav7().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_28w5c7bav7().b[15][1]++;
        }
        cov_28w5c7bav7().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_28w5c7bav7().b[21][0]++;
          cov_28w5c7bav7().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_28w5c7bav7().b[21][1]++;
        }
        cov_28w5c7bav7().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_28w5c7bav7().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_28w5c7bav7().b[22][1]++;
            cov_28w5c7bav7().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_28w5c7bav7().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_28w5c7bav7().b[22][2]++;
            cov_28w5c7bav7().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_28w5c7bav7().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_28w5c7bav7().b[22][3]++;
            cov_28w5c7bav7().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_28w5c7bav7().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_28w5c7bav7().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_28w5c7bav7().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_28w5c7bav7().b[22][4]++;
            cov_28w5c7bav7().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_28w5c7bav7().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_28w5c7bav7().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_28w5c7bav7().b[22][5]++;
            cov_28w5c7bav7().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_28w5c7bav7().b[23][0]++;
              cov_28w5c7bav7().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_28w5c7bav7().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_28w5c7bav7().b[23][1]++;
            }
            cov_28w5c7bav7().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_28w5c7bav7().b[26][0]++;
              cov_28w5c7bav7().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_28w5c7bav7().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_28w5c7bav7().b[26][1]++;
            }
            cov_28w5c7bav7().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_28w5c7bav7().b[28][0]++;
              cov_28w5c7bav7().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_28w5c7bav7().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_28w5c7bav7().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_28w5c7bav7().b[28][1]++;
            }
            cov_28w5c7bav7().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_28w5c7bav7().b[30][0]++;
              cov_28w5c7bav7().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_28w5c7bav7().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_28w5c7bav7().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_28w5c7bav7().b[30][1]++;
            }
            cov_28w5c7bav7().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_28w5c7bav7().b[32][0]++;
              cov_28w5c7bav7().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_28w5c7bav7().b[32][1]++;
            }
            cov_28w5c7bav7().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_28w5c7bav7().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_28w5c7bav7().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_28w5c7bav7().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_28w5c7bav7().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_28w5c7bav7().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_28w5c7bav7().b[33][0]++;
      cov_28w5c7bav7().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_28w5c7bav7().b[33][1]++;
    }
    cov_28w5c7bav7().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_28w5c7bav7().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_28w5c7bav7().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_28w5c7bav7().s[78]++,
/* istanbul ignore next */
(cov_28w5c7bav7().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_28w5c7bav7().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_28w5c7bav7().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_28w5c7bav7().f[15]++;
  cov_28w5c7bav7().s[79]++;
  return /* istanbul ignore next */(cov_28w5c7bav7().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_28w5c7bav7().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_28w5c7bav7().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_28w5c7bav7().s[81]++;
exports.PUT = exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_28w5c7bav7().s[82]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_28w5c7bav7().s[83]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_28w5c7bav7().s[84]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_28w5c7bav7().s[85]++, __importDefault(require("@/lib/prisma")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_28w5c7bav7().s[86]++, require("@/lib/unified-api-error-handler"));
var csrf_1 =
/* istanbul ignore next */
(cov_28w5c7bav7().s[87]++, require("@/lib/csrf"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_28w5c7bav7().s[88]++, require("@/lib/rateLimit"));
// GET handler to retrieve user achievements
/* istanbul ignore next */
cov_28w5c7bav7().s[89]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_28w5c7bav7().f[16]++;
  cov_28w5c7bav7().s[90]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[17]++;
    var session, error, searchParams, type, includeAll, allAchievements, achievementsWithStatus, whereClause, userAchievements, achievements;
    var _a;
    /* istanbul ignore next */
    cov_28w5c7bav7().s[91]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[18]++;
      cov_28w5c7bav7().s[92]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_28w5c7bav7().b[38][0]++;
          cov_28w5c7bav7().s[93]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_28w5c7bav7().b[38][1]++;
          cov_28w5c7bav7().s[94]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_28w5c7bav7().s[95]++;
          if (!(
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[41][0]++, (_a =
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[43][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[43][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[42][0]++, void 0) :
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[42][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[41][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[40][0]++, void 0) :
          /* istanbul ignore next */
          (cov_28w5c7bav7().b[40][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_28w5c7bav7().b[39][0]++;
            cov_28w5c7bav7().s[96]++;
            error = new Error('Unauthorized');
            /* istanbul ignore next */
            cov_28w5c7bav7().s[97]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_28w5c7bav7().s[98]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_28w5c7bav7().b[39][1]++;
          }
          cov_28w5c7bav7().s[99]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_28w5c7bav7().s[100]++;
          type = searchParams.get('type');
          /* istanbul ignore next */
          cov_28w5c7bav7().s[101]++;
          includeAll = searchParams.get('includeAll') === 'true';
          /* istanbul ignore next */
          cov_28w5c7bav7().s[102]++;
          if (!includeAll) {
            /* istanbul ignore next */
            cov_28w5c7bav7().b[44][0]++;
            cov_28w5c7bav7().s[103]++;
            return [3 /*break*/, 3];
          } else
          /* istanbul ignore next */
          {
            cov_28w5c7bav7().b[44][1]++;
          }
          cov_28w5c7bav7().s[104]++;
          return [4 /*yield*/, prisma_1.default.achievement.findMany({
            where: {
              isActive: true
            },
            include: {
              userAchievements: {
                where: {
                  userId: session.user.id
                }
              }
            },
            orderBy: {
              createdAt: 'asc'
            }
          })];
        case 2:
          /* istanbul ignore next */
          cov_28w5c7bav7().b[38][2]++;
          cov_28w5c7bav7().s[105]++;
          allAchievements = _b.sent();
          /* istanbul ignore next */
          cov_28w5c7bav7().s[106]++;
          achievementsWithStatus = allAchievements.map(function (achievement) {
            /* istanbul ignore next */
            cov_28w5c7bav7().f[19]++;
            var _a, _b;
            /* istanbul ignore next */
            cov_28w5c7bav7().s[107]++;
            return __assign(__assign({}, achievement), {
              isUnlocked: achievement.userAchievements.length > 0,
              unlockedAt:
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[45][0]++,
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[47][0]++, (_a = achievement.userAchievements[0]) === null) ||
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[47][1]++, _a === void 0) ?
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[46][0]++, void 0) :
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[46][1]++, _a.unlockedAt)) ||
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[45][1]++, null),
              progress:
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[48][0]++,
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[50][0]++, (_b = achievement.userAchievements[0]) === null) ||
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[50][1]++, _b === void 0) ?
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[49][0]++, void 0) :
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[49][1]++, _b.progress)) ||
              /* istanbul ignore next */
              (cov_28w5c7bav7().b[48][1]++, null)
            });
          });
          /* istanbul ignore next */
          cov_28w5c7bav7().s[108]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              achievements: achievementsWithStatus,
              total: allAchievements.length,
              unlocked: achievementsWithStatus.filter(function (a) {
                /* istanbul ignore next */
                cov_28w5c7bav7().f[20]++;
                cov_28w5c7bav7().s[109]++;
                return a.isUnlocked;
              }).length
            }
          })];
        case 3:
          /* istanbul ignore next */
          cov_28w5c7bav7().b[38][3]++;
          cov_28w5c7bav7().s[110]++;
          whereClause = {
            userId: session.user.id
          };
          /* istanbul ignore next */
          cov_28w5c7bav7().s[111]++;
          return [4 /*yield*/, prisma_1.default.userAchievement.findMany({
            where: whereClause,
            include: {
              achievement: true
            },
            orderBy: {
              unlockedAt: 'desc'
            }
          })];
        case 4:
          /* istanbul ignore next */
          cov_28w5c7bav7().b[38][4]++;
          cov_28w5c7bav7().s[112]++;
          userAchievements = _b.sent();
          /* istanbul ignore next */
          cov_28w5c7bav7().s[113]++;
          achievements = userAchievements.filter(function (ua) {
            /* istanbul ignore next */
            cov_28w5c7bav7().f[21]++;
            cov_28w5c7bav7().s[114]++;
            return /* istanbul ignore next */(cov_28w5c7bav7().b[51][0]++, ua.achievement) && (
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[51][1]++, !type) ||
            /* istanbul ignore next */
            (cov_28w5c7bav7().b[51][2]++, ua.achievement.type === type));
          }).map(function (ua) {
            /* istanbul ignore next */
            cov_28w5c7bav7().f[22]++;
            cov_28w5c7bav7().s[115]++;
            return __assign(__assign({}, ua.achievement), {
              unlockedAt: ua.unlockedAt,
              progress: ua.progress
            });
          });
          /* istanbul ignore next */
          cov_28w5c7bav7().s[116]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              achievements: achievements,
              total: achievements.length
            }
          })];
      }
    });
  });
});
// POST handler to unlock an achievement for a user
/* istanbul ignore next */
cov_28w5c7bav7().s[117]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_28w5c7bav7().f[23]++;
  cov_28w5c7bav7().s[118]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[24]++;
    cov_28w5c7bav7().s[119]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[25]++;
      cov_28w5c7bav7().s[120]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_28w5c7bav7().f[26]++;
        cov_28w5c7bav7().s[121]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_28w5c7bav7().f[27]++;
          cov_28w5c7bav7().s[122]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_28w5c7bav7().f[28]++;
            cov_28w5c7bav7().s[123]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 20
            }, function () {
              /* istanbul ignore next */
              cov_28w5c7bav7().f[29]++;
              cov_28w5c7bav7().s[124]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_28w5c7bav7().f[30]++;
                var session, error, body, achievementId, progress, error, achievement, error, existingUserAchievement, error, userAchievement;
                var _a;
                /* istanbul ignore next */
                cov_28w5c7bav7().s[125]++;
                return __generator(this, function (_b) {
                  /* istanbul ignore next */
                  cov_28w5c7bav7().f[31]++;
                  cov_28w5c7bav7().s[126]++;
                  switch (_b.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[52][0]++;
                      cov_28w5c7bav7().s[127]++;
                      return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
                    case 1:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[52][1]++;
                      cov_28w5c7bav7().s[128]++;
                      session = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[129]++;
                      if (!(
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[55][0]++, (_a =
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[57][0]++, session === null) ||
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[57][1]++, session === void 0) ?
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[56][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[56][1]++, session.user)) === null) ||
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[55][1]++, _a === void 0) ?
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[54][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[54][1]++, _a.id))) {
                        /* istanbul ignore next */
                        cov_28w5c7bav7().b[53][0]++;
                        cov_28w5c7bav7().s[130]++;
                        error = new Error('Unauthorized');
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[131]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[132]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_28w5c7bav7().b[53][1]++;
                      }
                      cov_28w5c7bav7().s[133]++;
                      return [4 /*yield*/, request.json()];
                    case 2:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[52][2]++;
                      cov_28w5c7bav7().s[134]++;
                      body = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[135]++;
                      achievementId = body.achievementId, progress = body.progress;
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[136]++;
                      if (!achievementId) {
                        /* istanbul ignore next */
                        cov_28w5c7bav7().b[58][0]++;
                        cov_28w5c7bav7().s[137]++;
                        error = new Error('Achievement ID is required');
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[138]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[139]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_28w5c7bav7().b[58][1]++;
                      }
                      cov_28w5c7bav7().s[140]++;
                      return [4 /*yield*/, prisma_1.default.achievement.findUnique({
                        where: {
                          id: achievementId,
                          isActive: true
                        }
                      })];
                    case 3:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[52][3]++;
                      cov_28w5c7bav7().s[141]++;
                      achievement = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[142]++;
                      if (!achievement) {
                        /* istanbul ignore next */
                        cov_28w5c7bav7().b[59][0]++;
                        cov_28w5c7bav7().s[143]++;
                        error = new Error('Achievement not found');
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[144]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[145]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_28w5c7bav7().b[59][1]++;
                      }
                      cov_28w5c7bav7().s[146]++;
                      return [4 /*yield*/, prisma_1.default.userAchievement.findUnique({
                        where: {
                          userId_achievementId: {
                            userId: session.user.id,
                            achievementId: achievementId
                          }
                        }
                      })];
                    case 4:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[52][4]++;
                      cov_28w5c7bav7().s[147]++;
                      existingUserAchievement = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[148]++;
                      if (existingUserAchievement) {
                        /* istanbul ignore next */
                        cov_28w5c7bav7().b[60][0]++;
                        cov_28w5c7bav7().s[149]++;
                        error = new Error('Achievement already unlocked');
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[150]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[151]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_28w5c7bav7().b[60][1]++;
                      }
                      cov_28w5c7bav7().s[152]++;
                      return [4 /*yield*/, prisma_1.default.userAchievement.create({
                        data: {
                          userId: session.user.id,
                          achievementId: achievementId,
                          progress: progress
                        },
                        include: {
                          achievement: true
                        }
                      })];
                    case 5:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[52][5]++;
                      cov_28w5c7bav7().s[153]++;
                      userAchievement = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[154]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: userAchievement
                      }, {
                        status: 201
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
// PUT handler to update achievement progress
/* istanbul ignore next */
cov_28w5c7bav7().s[155]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_28w5c7bav7().f[32]++;
  cov_28w5c7bav7().s[156]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_28w5c7bav7().f[33]++;
    cov_28w5c7bav7().s[157]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_28w5c7bav7().f[34]++;
      cov_28w5c7bav7().s[158]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_28w5c7bav7().f[35]++;
        cov_28w5c7bav7().s[159]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_28w5c7bav7().f[36]++;
          cov_28w5c7bav7().s[160]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_28w5c7bav7().f[37]++;
            cov_28w5c7bav7().s[161]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 30
            }, function () {
              /* istanbul ignore next */
              cov_28w5c7bav7().f[38]++;
              cov_28w5c7bav7().s[162]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_28w5c7bav7().f[39]++;
                var session, error, body, achievementId, progress, error, userAchievement, error, updatedUserAchievement;
                var _a;
                /* istanbul ignore next */
                cov_28w5c7bav7().s[163]++;
                return __generator(this, function (_b) {
                  /* istanbul ignore next */
                  cov_28w5c7bav7().f[40]++;
                  cov_28w5c7bav7().s[164]++;
                  switch (_b.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[61][0]++;
                      cov_28w5c7bav7().s[165]++;
                      return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
                    case 1:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[61][1]++;
                      cov_28w5c7bav7().s[166]++;
                      session = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[167]++;
                      if (!(
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[64][0]++, (_a =
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[66][0]++, session === null) ||
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[66][1]++, session === void 0) ?
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[65][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[65][1]++, session.user)) === null) ||
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[64][1]++, _a === void 0) ?
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[63][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_28w5c7bav7().b[63][1]++, _a.id))) {
                        /* istanbul ignore next */
                        cov_28w5c7bav7().b[62][0]++;
                        cov_28w5c7bav7().s[168]++;
                        error = new Error('Unauthorized');
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[169]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[170]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_28w5c7bav7().b[62][1]++;
                      }
                      cov_28w5c7bav7().s[171]++;
                      return [4 /*yield*/, request.json()];
                    case 2:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[61][2]++;
                      cov_28w5c7bav7().s[172]++;
                      body = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[173]++;
                      achievementId = body.achievementId, progress = body.progress;
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[174]++;
                      if (!achievementId) {
                        /* istanbul ignore next */
                        cov_28w5c7bav7().b[67][0]++;
                        cov_28w5c7bav7().s[175]++;
                        error = new Error('Achievement ID is required');
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[176]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[177]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_28w5c7bav7().b[67][1]++;
                      }
                      cov_28w5c7bav7().s[178]++;
                      return [4 /*yield*/, prisma_1.default.userAchievement.findUnique({
                        where: {
                          userId_achievementId: {
                            userId: session.user.id,
                            achievementId: achievementId
                          }
                        }
                      })];
                    case 3:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[61][3]++;
                      cov_28w5c7bav7().s[179]++;
                      userAchievement = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[180]++;
                      if (!userAchievement) {
                        /* istanbul ignore next */
                        cov_28w5c7bav7().b[68][0]++;
                        cov_28w5c7bav7().s[181]++;
                        error = new Error('User achievement not found');
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[182]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_28w5c7bav7().s[183]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_28w5c7bav7().b[68][1]++;
                      }
                      cov_28w5c7bav7().s[184]++;
                      return [4 /*yield*/, prisma_1.default.userAchievement.update({
                        where: {
                          userId_achievementId: {
                            userId: session.user.id,
                            achievementId: achievementId
                          }
                        },
                        data: {
                          progress: progress
                        },
                        include: {
                          achievement: true
                        }
                      })];
                    case 4:
                      /* istanbul ignore next */
                      cov_28w5c7bav7().b[61][4]++;
                      cov_28w5c7bav7().s[185]++;
                      updatedUserAchievement = _b.sent();
                      /* istanbul ignore next */
                      cov_28w5c7bav7().s[186]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: updatedUserAchievement
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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