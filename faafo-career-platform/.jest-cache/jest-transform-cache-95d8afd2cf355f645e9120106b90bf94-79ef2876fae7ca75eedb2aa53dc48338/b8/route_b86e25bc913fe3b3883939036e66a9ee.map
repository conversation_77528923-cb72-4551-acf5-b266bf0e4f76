{"version": 3, "names": ["server_1", "cov_oijs2ccho", "s", "require", "prisma_1", "__importDefault", "unified_api_error_handler_1", "enhanced_rate_limiter_1", "SECURITY_DELAY_MS", "securityDelay", "f", "Promise", "resolve", "setTimeout", "exports", "POST", "withUnifiedErrorHandling", "request", "__awaiter", "enhancedRateLimiters", "auth", "checkLimit", "rateLimitResult", "_b", "sent", "allowed", "b", "error", "Error", "statusCode", "headers", "startTime", "Date", "now", "json", "_a", "token", "email", "default", "verificationToken", "findUnique", "where", "elapsedTime", "remainingDelay_1", "Math", "max", "expires", "delete", "identifier", "user", "emailVerified", "NextResponse", "success", "data", "message", "$transaction", "update", "id", "remainingDelay_2", "error_1", "GET", "searchParams", "URL", "url", "get", "remainingDelay_3", "valid", "alreadyVerified", "remainingDelay_4", "error_2"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/verify-email/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';\n\n// Timing attack protection - consistent delay for all responses\nconst SECURITY_DELAY_MS = 50;\n\nasync function securityDelay(): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, SECURITY_DELAY_MS));\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> => {\n  // SECURITY FIX: Apply strict rate limiting for email verification attempts\n  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);\n\n  if (!rateLimitResult.allowed) {\n    const error = new Error('Too many email verification attempts. Please try again later.') as any;\n    error.statusCode = 429;\n    error.headers = rateLimitResult.headers;\n    throw error;\n  }\n\n  const startTime = Date.now();\n\n  try {\n    const { token, email } = await request.json();\n\n    if (!token || !email) {\n      await securityDelay();\n      throw new Error('Token and email are required.');\n    }\n\n    // SECURITY FIX: Always perform database lookup to prevent timing attacks\n    const verificationToken = await prisma.verificationToken.findUnique({\n      where: {\n        token: token,\n      },\n    });\n\n    // SECURITY FIX: Ensure consistent response time regardless of token validity\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n\n    if (!verificationToken) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Check if token has expired\n    if (verificationToken.expires < new Date()) {\n      // Clean up expired token\n      await prisma.verificationToken.delete({\n        where: { token: token },\n      });\n      throw new Error('Verification token has expired.');\n    }\n\n    // Check if the email matches\n    if (verificationToken.identifier !== email) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Find the user\n    const user = await prisma.user.findUnique({\n      where: { email: email },\n    });\n\n    if (!user) {\n      throw new Error('User not found.');\n    }\n\n    // Check if user is already verified\n    if (user.emailVerified) {\n      // Clean up the token\n      await prisma.verificationToken.delete({\n        where: { token: token },\n      });\n      return NextResponse.json({\n        success: true,\n        data: { message: 'Email is already verified.' }\n      });\n    }\n\n    // Update user as verified and clean up token\n    await prisma.$transaction([\n      prisma.user.update({\n        where: { id: user.id },\n        data: {\n          emailVerified: new Date(),\n        },\n      }),\n      prisma.verificationToken.delete({\n        where: { token: token },\n      }),\n    ]);\n\n    return NextResponse.json({\n      success: true,\n      data: { message: 'Email verified successfully.' }\n    });\n\n  } catch (error) {\n    // SECURITY FIX: Ensure consistent timing even for errors\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n    throw error;\n  }\n});\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ valid: boolean; email: string; alreadyVerified: boolean }>>> => {\n  // SECURITY FIX: Apply rate limiting to GET method as well\n  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);\n\n  if (!rateLimitResult.allowed) {\n    const error = new Error('Too many email verification check attempts. Please try again later.') as any;\n    error.statusCode = 429;\n    error.headers = rateLimitResult.headers;\n    throw error;\n  }\n\n  const startTime = Date.now();\n\n  try {\n    const { searchParams } = new URL(request.url);\n    const token = searchParams.get('token');\n    const email = searchParams.get('email');\n\n    if (!token || !email) {\n      await securityDelay();\n      throw new Error('Token and email are required.');\n    }\n\n    // SECURITY FIX: Always perform database lookup to prevent timing attacks\n    const verificationToken = await prisma.verificationToken.findUnique({\n      where: {\n        token: token,\n      },\n    });\n\n    // SECURITY FIX: Ensure consistent response time\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n\n    if (!verificationToken) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Check if token has expired\n    if (verificationToken.expires < new Date()) {\n      throw new Error('Verification token has expired.');\n    }\n\n    // Check if the email matches\n    if (verificationToken.identifier !== email) {\n      throw new Error('Invalid verification token.');\n    }\n\n    // Find the user\n    const user = await prisma.user.findUnique({\n      where: { email: email },\n    });\n\n    if (!user) {\n      throw new Error('User not found.');\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        valid: true,\n        email: email,\n        alreadyVerified: !!user.emailVerified\n      }\n    });\n\n  } catch (error) {\n    // SECURITY FIX: Ensure consistent timing even for errors\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n    throw error;\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,QAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,eAAA,CAAAF,OAAA;AACA,IAAAG,2BAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAI,uBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA;AACA,IAAMK,iBAAiB;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAG,EAAE;AAE5B,SAAeO,aAAaA,CAAA;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAC,CAAA;iCAAIS,OAAO;IAAA;IAAAV,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAC,CAAA;;;;;MACrC,sBAAO,IAAIS,OAAO,CAAC,UAAAC,OAAO;QAAA;QAAAX,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAC,CAAA;QAAI,OAAAW,UAAU,CAACD,OAAO,EAAEJ,iBAAiB,CAAC;MAAtC,CAAsC,CAAC;;;;AACtE;AAAAP,aAAA,GAAAC,CAAA;AAEYY,OAAA,CAAAC,IAAI,GAAG,IAAAT,2BAAA,CAAAU,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAhB,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAC,CAAA;EAAA,OAAAgB,SAAA,iBAAGP,OAAO;IAAA;IAAAV,aAAA,GAAAS,CAAA;;;;;;;;;;;;;UAExD,qBAAMH,uBAAA,CAAAY,oBAAoB,CAACC,IAAI,CAACC,UAAU,CAACJ,OAAO,CAAC;;;;;UAArEK,eAAe,GAAGC,EAAA,CAAAC,IAAA,EAAmD;UAAA;UAAAvB,aAAA,GAAAC,CAAA;UAE3E,IAAI,CAACoB,eAAe,CAACG,OAAO,EAAE;YAAA;YAAAxB,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACtByB,KAAK,GAAG,IAAIC,KAAK,CAAC,+DAA+D,CAAQ;YAAC;YAAA3B,aAAA,GAAAC,CAAA;YAChGyB,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA5B,aAAA,GAAAC,CAAA;YACvByB,KAAK,CAACG,OAAO,GAAGR,eAAe,CAACQ,OAAO;YAAC;YAAA7B,aAAA,GAAAC,CAAA;YACxC,MAAMyB,KAAK;UACb,CAAC;UAAA;UAAA;YAAA1B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAEK6B,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;UAAC;UAAAhC,aAAA,GAAAC,CAAA;;;;;;;;;UAGF,qBAAMe,OAAO,CAACiB,IAAI,EAAE;;;;;UAAvCC,EAAA,GAAmBZ,EAAA,CAAAC,IAAA,EAAoB,EAArCY,KAAK,GAAAD,EAAA,CAAAC,KAAA,EAAEC,KAAK,GAAAF,EAAA,CAAAE,KAAA;UAAA;UAAApC,aAAA,GAAAC,CAAA;;UAEhB;UAAA,CAAAD,aAAA,GAAAyB,CAAA,YAACU,KAAK;UAAA;UAAA,CAAAnC,aAAA,GAAAyB,CAAA,WAAI,CAACW,KAAK,IAAhB;YAAA;YAAApC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAgB;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAClB,qBAAMO,aAAa,EAAE;;;;;UAArBc,EAAA,CAAAC,IAAA,EAAqB;UAAC;UAAAvB,aAAA,GAAAC,CAAA;UACtB,MAAM,IAAI0B,KAAK,CAAC,+BAA+B,CAAC;;;;;UAIxB,qBAAMxB,QAAA,CAAAkC,OAAM,CAACC,iBAAiB,CAACC,UAAU,CAAC;YAClEC,KAAK,EAAE;cACLL,KAAK,EAAEA;;WAEV,CAAC;;;;;UAJIG,iBAAiB,GAAGhB,EAAA,CAAAC,IAAA,EAIxB;UAAA;UAAAvB,aAAA,GAAAC,CAAA;UAGIwC,WAAW,GAAGV,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;UAAC;UAAA9B,aAAA,GAAAC,CAAA;UACrCyC,gBAAA,GAAiBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErC,iBAAiB,GAAGkC,WAAW,CAAC;UAAC;UAAAzC,aAAA,GAAAC,CAAA;gBAChEyC,gBAAc,GAAG,CAAC,GAAlB;YAAA;YAAA1C,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAkB;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UACpB,qBAAM,IAAIS,OAAO,CAAC,UAAAC,OAAO;YAAA;YAAAX,aAAA,GAAAS,CAAA;YAAAT,aAAA,GAAAC,CAAA;YAAI,OAAAW,UAAU,CAACD,OAAO,EAAE+B,gBAAc,CAAC;UAAnC,CAAmC,CAAC;;;;;UAAjEpB,EAAA,CAAAC,IAAA,EAAiE;UAAC;UAAAvB,aAAA,GAAAC,CAAA;;;;;;UAGpE,IAAI,CAACqC,iBAAiB,EAAE;YAAA;YAAAtC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACtB,MAAM,IAAI0B,KAAK,CAAC,6BAA6B,CAAC;UAChD,CAAC;UAAA;UAAA;YAAA3B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;gBAGGqC,iBAAiB,CAACO,OAAO,GAAG,IAAId,IAAI,EAAE,GAAtC;YAAA;YAAA/B,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAsC;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UACxC;UAAAzB,aAAA,GAAAC,CAAA;UACA,qBAAME,QAAA,CAAAkC,OAAM,CAACC,iBAAiB,CAACQ,MAAM,CAAC;YACpCN,KAAK,EAAE;cAAEL,KAAK,EAAEA;YAAK;WACtB,CAAC;;;;;UAHF;UACAb,EAAA,CAAAC,IAAA,EAEE;UAAC;UAAAvB,aAAA,GAAAC,CAAA;UACH,MAAM,IAAI0B,KAAK,CAAC,iCAAiC,CAAC;;;;;UAGpD;UACA,IAAIW,iBAAiB,CAACS,UAAU,KAAKX,KAAK,EAAE;YAAA;YAAApC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAC1C,MAAM,IAAI0B,KAAK,CAAC,6BAA6B,CAAC;UAChD,CAAC;UAAA;UAAA;YAAA3B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAGY,qBAAME,QAAA,CAAAkC,OAAM,CAACW,IAAI,CAACT,UAAU,CAAC;YACxCC,KAAK,EAAE;cAAEJ,KAAK,EAAEA;YAAK;WACtB,CAAC;;;;;UAFIY,IAAI,GAAG1B,EAAA,CAAAC,IAAA,EAEX;UAAA;UAAAvB,aAAA,GAAAC,CAAA;UAEF,IAAI,CAAC+C,IAAI,EAAE;YAAA;YAAAhD,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACT,MAAM,IAAI0B,KAAK,CAAC,iBAAiB,CAAC;UACpC,CAAC;UAAA;UAAA;YAAA3B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;eAGG+C,IAAI,CAACC,aAAa,EAAlB;YAAA;YAAAjD,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAkB;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UACpB;UAAAzB,aAAA,GAAAC,CAAA;UACA,qBAAME,QAAA,CAAAkC,OAAM,CAACC,iBAAiB,CAACQ,MAAM,CAAC;YACpCN,KAAK,EAAE;cAAEL,KAAK,EAAEA;YAAK;WACtB,CAAC;;;;;UAHF;UACAb,EAAA,CAAAC,IAAA,EAEE;UAAC;UAAAvB,aAAA,GAAAC,CAAA;UACH,sBAAOF,QAAA,CAAAmD,YAAY,CAACjB,IAAI,CAAC;YACvBkB,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cAAEC,OAAO,EAAE;YAA4B;WAC9C,CAAC;;;;;UAGJ;UACA,qBAAMlD,QAAA,CAAAkC,OAAM,CAACiB,YAAY,CAAC,CACxBnD,QAAA,CAAAkC,OAAM,CAACW,IAAI,CAACO,MAAM,CAAC;YACjBf,KAAK,EAAE;cAAEgB,EAAE,EAAER,IAAI,CAACQ;YAAE,CAAE;YACtBJ,IAAI,EAAE;cACJH,aAAa,EAAE,IAAIlB,IAAI;;WAE1B,CAAC,EACF5B,QAAA,CAAAkC,OAAM,CAACC,iBAAiB,CAACQ,MAAM,CAAC;YAC9BN,KAAK,EAAE;cAAEL,KAAK,EAAEA;YAAK;WACtB,CAAC,CACH,CAAC;;;;;UAXF;UACAb,EAAA,CAAAC,IAAA,EAUE;UAAC;UAAAvB,aAAA,GAAAC,CAAA;UAEH,sBAAOF,QAAA,CAAAmD,YAAY,CAACjB,IAAI,CAAC;YACvBkB,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cAAEC,OAAO,EAAE;YAA8B;WAChD,CAAC;;;;;;;;UAIIZ,WAAW,GAAGV,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;UAAC;UAAA9B,aAAA,GAAAC,CAAA;UACrCwD,gBAAA,GAAiBd,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErC,iBAAiB,GAAGkC,WAAW,CAAC;UAAC;UAAAzC,aAAA,GAAAC,CAAA;gBAChEwD,gBAAc,GAAG,CAAC,GAAlB;YAAA;YAAAzD,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAkB;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UACpB,qBAAM,IAAIS,OAAO,CAAC,UAAAC,OAAO;YAAA;YAAAX,aAAA,GAAAS,CAAA;YAAAT,aAAA,GAAAC,CAAA;YAAI,OAAAW,UAAU,CAACD,OAAO,EAAE8C,gBAAc,CAAC;UAAnC,CAAmC,CAAC;;;;;UAAjEnC,EAAA,CAAAC,IAAA,EAAiE;UAAC;UAAAvB,aAAA,GAAAC,CAAA;;;;;;UAEpE,MAAMyD,OAAK;;;;;;;;;CAEd,CAAC;AAAC;AAAA1D,aAAA,GAAAC,CAAA;AAEUY,OAAA,CAAA8C,GAAG,GAAG,IAAAtD,2BAAA,CAAAU,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAhB,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAC,CAAA;EAAA,OAAAgB,SAAA,iBAAGP,OAAO;IAAA;IAAAV,aAAA,GAAAS,CAAA;;;;;;;;;;;;;UAEvD,qBAAMH,uBAAA,CAAAY,oBAAoB,CAACC,IAAI,CAACC,UAAU,CAACJ,OAAO,CAAC;;;;;UAArEK,eAAe,GAAGa,EAAA,CAAAX,IAAA,EAAmD;UAAA;UAAAvB,aAAA,GAAAC,CAAA;UAE3E,IAAI,CAACoB,eAAe,CAACG,OAAO,EAAE;YAAA;YAAAxB,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACtByB,KAAK,GAAG,IAAIC,KAAK,CAAC,qEAAqE,CAAQ;YAAC;YAAA3B,aAAA,GAAAC,CAAA;YACtGyB,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA5B,aAAA,GAAAC,CAAA;YACvByB,KAAK,CAACG,OAAO,GAAGR,eAAe,CAACQ,OAAO;YAAC;YAAA7B,aAAA,GAAAC,CAAA;YACxC,MAAMyB,KAAK;UACb,CAAC;UAAA;UAAA;YAAA1B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAEK6B,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;UAAC;UAAAhC,aAAA,GAAAC,CAAA;;;;;;;;;UAGnB2D,YAAY,GAAK,IAAIC,GAAG,CAAC7C,OAAO,CAAC8C,GAAG,CAAC,CAAAF,YAAzB;UAA0B;UAAA5D,aAAA,GAAAC,CAAA;UACxCkC,KAAK,GAAGyB,YAAY,CAACG,GAAG,CAAC,OAAO,CAAC;UAAC;UAAA/D,aAAA,GAAAC,CAAA;UAClCmC,KAAK,GAAGwB,YAAY,CAACG,GAAG,CAAC,OAAO,CAAC;UAAC;UAAA/D,aAAA,GAAAC,CAAA;;UAEpC;UAAA,CAAAD,aAAA,GAAAyB,CAAA,YAACU,KAAK;UAAA;UAAA,CAAAnC,aAAA,GAAAyB,CAAA,WAAI,CAACW,KAAK,IAAhB;YAAA;YAAApC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAgB;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAClB,qBAAMO,aAAa,EAAE;;;;;UAArB0B,EAAA,CAAAX,IAAA,EAAqB;UAAC;UAAAvB,aAAA,GAAAC,CAAA;UACtB,MAAM,IAAI0B,KAAK,CAAC,+BAA+B,CAAC;;;;;UAIxB,qBAAMxB,QAAA,CAAAkC,OAAM,CAACC,iBAAiB,CAACC,UAAU,CAAC;YAClEC,KAAK,EAAE;cACLL,KAAK,EAAEA;;WAEV,CAAC;;;;;UAJIG,iBAAiB,GAAGJ,EAAA,CAAAX,IAAA,EAIxB;UAAA;UAAAvB,aAAA,GAAAC,CAAA;UAGIwC,WAAW,GAAGV,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;UAAC;UAAA9B,aAAA,GAAAC,CAAA;UACrC+D,gBAAA,GAAiBrB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErC,iBAAiB,GAAGkC,WAAW,CAAC;UAAC;UAAAzC,aAAA,GAAAC,CAAA;gBAChE+D,gBAAc,GAAG,CAAC,GAAlB;YAAA;YAAAhE,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAkB;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UACpB,qBAAM,IAAIS,OAAO,CAAC,UAAAC,OAAO;YAAA;YAAAX,aAAA,GAAAS,CAAA;YAAAT,aAAA,GAAAC,CAAA;YAAI,OAAAW,UAAU,CAACD,OAAO,EAAEqD,gBAAc,CAAC;UAAnC,CAAmC,CAAC;;;;;UAAjE9B,EAAA,CAAAX,IAAA,EAAiE;UAAC;UAAAvB,aAAA,GAAAC,CAAA;;;;;;UAGpE,IAAI,CAACqC,iBAAiB,EAAE;YAAA;YAAAtC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACtB,MAAM,IAAI0B,KAAK,CAAC,6BAA6B,CAAC;UAChD,CAAC;UAAA;UAAA;YAAA3B,aAAA,GAAAyB,CAAA;UAAA;UAED;UAAAzB,aAAA,GAAAC,CAAA;UACA,IAAIqC,iBAAiB,CAACO,OAAO,GAAG,IAAId,IAAI,EAAE,EAAE;YAAA;YAAA/B,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAC1C,MAAM,IAAI0B,KAAK,CAAC,iCAAiC,CAAC;UACpD,CAAC;UAAA;UAAA;YAAA3B,aAAA,GAAAyB,CAAA;UAAA;UAED;UAAAzB,aAAA,GAAAC,CAAA;UACA,IAAIqC,iBAAiB,CAACS,UAAU,KAAKX,KAAK,EAAE;YAAA;YAAApC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAC1C,MAAM,IAAI0B,KAAK,CAAC,6BAA6B,CAAC;UAChD,CAAC;UAAA;UAAA;YAAA3B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAGY,qBAAME,QAAA,CAAAkC,OAAM,CAACW,IAAI,CAACT,UAAU,CAAC;YACxCC,KAAK,EAAE;cAAEJ,KAAK,EAAEA;YAAK;WACtB,CAAC;;;;;UAFIY,IAAI,GAAGd,EAAA,CAAAX,IAAA,EAEX;UAAA;UAAAvB,aAAA,GAAAC,CAAA;UAEF,IAAI,CAAC+C,IAAI,EAAE;YAAA;YAAAhD,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACT,MAAM,IAAI0B,KAAK,CAAC,iBAAiB,CAAC;UACpC,CAAC;UAAA;UAAA;YAAA3B,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAED,sBAAOF,QAAA,CAAAmD,YAAY,CAACjB,IAAI,CAAC;YACvBkB,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACJa,KAAK,EAAE,IAAI;cACX7B,KAAK,EAAEA,KAAK;cACZ8B,eAAe,EAAE,CAAC,CAAClB,IAAI,CAACC;;WAE3B,CAAC;;;;;;;;UAIIR,WAAW,GAAGV,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;UAAC;UAAA9B,aAAA,GAAAC,CAAA;UACrCkE,gBAAA,GAAiBxB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErC,iBAAiB,GAAGkC,WAAW,CAAC;UAAC;UAAAzC,aAAA,GAAAC,CAAA;gBAChEkE,gBAAc,GAAG,CAAC,GAAlB;YAAA;YAAAnE,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAkB;UAAA;UAAA;YAAAD,aAAA,GAAAyB,CAAA;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UACpB,qBAAM,IAAIS,OAAO,CAAC,UAAAC,OAAO;YAAA;YAAAX,aAAA,GAAAS,CAAA;YAAAT,aAAA,GAAAC,CAAA;YAAI,OAAAW,UAAU,CAACD,OAAO,EAAEwD,gBAAc,CAAC;UAAnC,CAAmC,CAAC;;;;;UAAjEjC,EAAA,CAAAX,IAAA,EAAiE;UAAC;UAAAvB,aAAA,GAAAC,CAAA;;;;;;UAEpE,MAAMmE,OAAK;;;;;;;;;CAEd,CAAC", "ignoreList": []}