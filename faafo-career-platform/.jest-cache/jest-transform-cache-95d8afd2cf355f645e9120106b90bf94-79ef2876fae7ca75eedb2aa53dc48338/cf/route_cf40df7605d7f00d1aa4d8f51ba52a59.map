{"version": 3, "names": ["server_1", "cov_162gp4ddpn", "s", "require", "unified_api_error_handler_1", "exports", "GET", "withUnifiedErrorHandling", "request", "f", "__awaiter", "Promise", "categories", "id", "name", "description", "postCount", "replyCount", "color", "children", "NextResponse", "json", "success", "data"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/categories/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface ForumCategoriesResponse {\n  id: string;\n  name: string;\n  description: string;\n  postCount: number;\n  replyCount: number;\n  color: string;\n  children: any[];\n}\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ForumCategoriesResponse[]>>> => {\n  // For now, return predefined categories since we don't have a categories table\n  const categories = [\n    {\n      id: 'career-advice',\n      name: 'Career Advice',\n      description: 'General career guidance and tips',\n      postCount: 0,\n      replyCount: 0,\n      color: 'blue',\n      children: []\n    },\n    {\n      id: 'job-search',\n      name: 'Job Search',\n      description: 'Job hunting strategies and experiences',\n      postCount: 0,\n      replyCount: 0,\n      color: 'green',\n      children: []\n    },\n    {\n      id: 'skill-development',\n      name: 'Skill Development',\n      description: 'Learning new skills and technologies',\n      postCount: 0,\n      replyCount: 0,\n      color: 'purple',\n      children: []\n    },\n    {\n      id: 'networking',\n      name: 'Networking',\n      description: 'Building professional connections',\n      postCount: 0,\n      replyCount: 0,\n      color: 'orange',\n      children: []\n    },\n    {\n      id: 'career-change',\n      name: 'Career Change',\n      description: 'Transitioning between careers',\n      postCount: 0,\n      replyCount: 0,\n      color: 'red',\n      children: []\n    },\n    {\n      id: 'freelancing',\n      name: 'Freelancing',\n      description: 'Independent work and consulting',\n      postCount: 0,\n      replyCount: 0,\n      color: 'teal',\n      children: []\n    }\n  ];\n\n  return NextResponse.json({\n    success: true,\n    data: categories\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,2BAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,QAAAC,OAAA;AAAwF;AAAAF,cAAA,GAAAC,CAAA;AAY3EG,OAAA,CAAAC,GAAG,GAAG,IAAAF,2BAAA,CAAAG,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAC,CAAA;EAAA,OAAAQ,SAAA,iBAAGC,OAAO;IAAA;IAAAV,cAAA,GAAAQ,CAAA;;;;;;;;MAEzEG,UAAU,GAAG,CACjB;QACEC,EAAE,EAAE,eAAe;QACnBC,IAAI,EAAE,eAAe;QACrBC,WAAW,EAAE,kCAAkC;QAC/CC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;OACX,EACD;QACEN,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,YAAY;QAClBC,WAAW,EAAE,wCAAwC;QACrDC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE;OACX,EACD;QACEN,EAAE,EAAE,mBAAmB;QACvBC,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,sCAAsC;QACnDC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE;OACX,EACD;QACEN,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,YAAY;QAClBC,WAAW,EAAE,mCAAmC;QAChDC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE;OACX,EACD;QACEN,EAAE,EAAE,eAAe;QACnBC,IAAI,EAAE,eAAe;QACrBC,WAAW,EAAE,+BAA+B;QAC5CC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,KAAK;QACZC,QAAQ,EAAE;OACX,EACD;QACEN,EAAE,EAAE,aAAa;QACjBC,IAAI,EAAE,aAAa;QACnBC,WAAW,EAAE,iCAAiC;QAC9CC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;OACX,CACF;MAAC;MAAAlB,cAAA,GAAAC,CAAA;MAEF,sBAAOF,QAAA,CAAAoB,YAAY,CAACC,IAAI,CAAC;QACvBC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEX;OACP,CAAC;;;CACH,CAAC", "ignoreList": []}