a883749b8dac2ba48eb508fb321498c4
"use strict";

/* istanbul ignore next */
function cov_162gp4ddpn() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/categories/route.ts";
  var hash = "114bd0ba274b00688a49e27fd3b3aaee2a4729c9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/categories/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "68": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 21
        }
      },
      "69": {
        start: {
          line: 40,
          column: 15
        },
        end: {
          line: 40,
          column: 37
        }
      },
      "70": {
        start: {
          line: 41,
          column: 34
        },
        end: {
          line: 41,
          column: 76
        }
      },
      "71": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 106,
          column: 7
        }
      },
      "72": {
        start: {
          line: 42,
          column: 93
        },
        end: {
          line: 106,
          column: 3
        }
      },
      "73": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 105,
          column: 7
        }
      },
      "74": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 100,
          column: 10
        }
      },
      "75": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 104,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 42,
            column: 72
          },
          end: {
            line: 42,
            column: 73
          }
        },
        loc: {
          start: {
            line: 42,
            column: 91
          },
          end: {
            line: 106,
            column: 5
          }
        },
        line: 42
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 42,
            column: 135
          },
          end: {
            line: 42,
            column: 136
          }
        },
        loc: {
          start: {
            line: 42,
            column: 147
          },
          end: {
            line: 106,
            column: 1
          }
        },
        line: 42
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 44,
            column: 29
          },
          end: {
            line: 44,
            column: 30
          }
        },
        loc: {
          start: {
            line: 44,
            column: 43
          },
          end: {
            line: 105,
            column: 5
          }
        },
        line: 44
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/categories/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,6EAAwF;AAY3E,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;QAEzE,UAAU,GAAG;YACjB;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,kCAAkC;gBAC/C,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,EAAE;aACb;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,wCAAwC;gBACrD,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,EAAE;aACb;YACD;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,sCAAsC;gBACnD,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,QAAQ;gBACf,QAAQ,EAAE,EAAE;aACb;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,mCAAmC;gBAChD,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,QAAQ;gBACf,QAAQ,EAAE,EAAE;aACb;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,+BAA+B;gBAC5C,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,EAAE;aACb;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,iCAAiC;gBAC9C,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,EAAE;aACb;SACF,CAAC;QAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,UAAU;aACjB,CAAC,EAAC;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/categories/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface ForumCategoriesResponse {\n  id: string;\n  name: string;\n  description: string;\n  postCount: number;\n  replyCount: number;\n  color: string;\n  children: any[];\n}\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ForumCategoriesResponse[]>>> => {\n  // For now, return predefined categories since we don't have a categories table\n  const categories = [\n    {\n      id: 'career-advice',\n      name: 'Career Advice',\n      description: 'General career guidance and tips',\n      postCount: 0,\n      replyCount: 0,\n      color: 'blue',\n      children: []\n    },\n    {\n      id: 'job-search',\n      name: 'Job Search',\n      description: 'Job hunting strategies and experiences',\n      postCount: 0,\n      replyCount: 0,\n      color: 'green',\n      children: []\n    },\n    {\n      id: 'skill-development',\n      name: 'Skill Development',\n      description: 'Learning new skills and technologies',\n      postCount: 0,\n      replyCount: 0,\n      color: 'purple',\n      children: []\n    },\n    {\n      id: 'networking',\n      name: 'Networking',\n      description: 'Building professional connections',\n      postCount: 0,\n      replyCount: 0,\n      color: 'orange',\n      children: []\n    },\n    {\n      id: 'career-change',\n      name: 'Career Change',\n      description: 'Transitioning between careers',\n      postCount: 0,\n      replyCount: 0,\n      color: 'red',\n      children: []\n    },\n    {\n      id: 'freelancing',\n      name: 'Freelancing',\n      description: 'Independent work and consulting',\n      postCount: 0,\n      replyCount: 0,\n      color: 'teal',\n      children: []\n    }\n  ];\n\n  return NextResponse.json({\n    success: true,\n    data: categories\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "114bd0ba274b00688a49e27fd3b3aaee2a4729c9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_162gp4ddpn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_162gp4ddpn();
var __awaiter =
/* istanbul ignore next */
(cov_162gp4ddpn().s[0]++,
/* istanbul ignore next */
(cov_162gp4ddpn().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_162gp4ddpn().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_162gp4ddpn().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_162gp4ddpn().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_162gp4ddpn().f[1]++;
    cov_162gp4ddpn().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_162gp4ddpn().f[2]++;
      cov_162gp4ddpn().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_162gp4ddpn().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_162gp4ddpn().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_162gp4ddpn().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_162gp4ddpn().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_162gp4ddpn().f[4]++;
      cov_162gp4ddpn().s[4]++;
      try {
        /* istanbul ignore next */
        cov_162gp4ddpn().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_162gp4ddpn().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_162gp4ddpn().f[5]++;
      cov_162gp4ddpn().s[7]++;
      try {
        /* istanbul ignore next */
        cov_162gp4ddpn().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_162gp4ddpn().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_162gp4ddpn().f[6]++;
      cov_162gp4ddpn().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_162gp4ddpn().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_162gp4ddpn().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_162gp4ddpn().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_162gp4ddpn().s[12]++,
/* istanbul ignore next */
(cov_162gp4ddpn().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_162gp4ddpn().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_162gp4ddpn().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_162gp4ddpn().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_162gp4ddpn().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_162gp4ddpn().f[8]++;
        cov_162gp4ddpn().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_162gp4ddpn().b[6][0]++;
          cov_162gp4ddpn().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_162gp4ddpn().b[6][1]++;
        }
        cov_162gp4ddpn().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_162gp4ddpn().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_162gp4ddpn().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_162gp4ddpn().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_162gp4ddpn().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_162gp4ddpn().f[9]++;
    cov_162gp4ddpn().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_162gp4ddpn().f[10]++;
    cov_162gp4ddpn().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_162gp4ddpn().f[11]++;
      cov_162gp4ddpn().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_162gp4ddpn().f[12]++;
    cov_162gp4ddpn().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_162gp4ddpn().b[9][0]++;
      cov_162gp4ddpn().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_162gp4ddpn().b[9][1]++;
    }
    cov_162gp4ddpn().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_162gp4ddpn().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_162gp4ddpn().s[25]++;
      try {
        /* istanbul ignore next */
        cov_162gp4ddpn().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[15][0]++,
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[16][1]++,
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_162gp4ddpn().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_162gp4ddpn().b[12][0]++;
          cov_162gp4ddpn().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_162gp4ddpn().b[12][1]++;
        }
        cov_162gp4ddpn().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_162gp4ddpn().b[18][0]++;
          cov_162gp4ddpn().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_162gp4ddpn().b[18][1]++;
        }
        cov_162gp4ddpn().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_162gp4ddpn().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_162gp4ddpn().b[19][1]++;
            cov_162gp4ddpn().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_162gp4ddpn().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_162gp4ddpn().b[19][2]++;
            cov_162gp4ddpn().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_162gp4ddpn().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_162gp4ddpn().b[19][3]++;
            cov_162gp4ddpn().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_162gp4ddpn().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_162gp4ddpn().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_162gp4ddpn().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_162gp4ddpn().b[19][4]++;
            cov_162gp4ddpn().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_162gp4ddpn().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_162gp4ddpn().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_162gp4ddpn().b[19][5]++;
            cov_162gp4ddpn().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_162gp4ddpn().b[20][0]++;
              cov_162gp4ddpn().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_162gp4ddpn().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_162gp4ddpn().b[20][1]++;
            }
            cov_162gp4ddpn().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_162gp4ddpn().b[23][0]++;
              cov_162gp4ddpn().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_162gp4ddpn().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_162gp4ddpn().b[23][1]++;
            }
            cov_162gp4ddpn().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_162gp4ddpn().b[25][0]++;
              cov_162gp4ddpn().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_162gp4ddpn().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_162gp4ddpn().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_162gp4ddpn().b[25][1]++;
            }
            cov_162gp4ddpn().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_162gp4ddpn().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_162gp4ddpn().b[27][0]++;
              cov_162gp4ddpn().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_162gp4ddpn().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_162gp4ddpn().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_162gp4ddpn().b[27][1]++;
            }
            cov_162gp4ddpn().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_162gp4ddpn().b[29][0]++;
              cov_162gp4ddpn().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_162gp4ddpn().b[29][1]++;
            }
            cov_162gp4ddpn().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_162gp4ddpn().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_162gp4ddpn().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_162gp4ddpn().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_162gp4ddpn().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_162gp4ddpn().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_162gp4ddpn().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_162gp4ddpn().b[30][0]++;
      cov_162gp4ddpn().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_162gp4ddpn().b[30][1]++;
    }
    cov_162gp4ddpn().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_162gp4ddpn().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_162gp4ddpn().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_162gp4ddpn().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_162gp4ddpn().s[68]++;
exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_162gp4ddpn().s[69]++, require("next/server"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_162gp4ddpn().s[70]++, require("@/lib/unified-api-error-handler"));
/* istanbul ignore next */
cov_162gp4ddpn().s[71]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_162gp4ddpn().f[13]++;
  cov_162gp4ddpn().s[72]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_162gp4ddpn().f[14]++;
    var categories;
    /* istanbul ignore next */
    cov_162gp4ddpn().s[73]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_162gp4ddpn().f[15]++;
      cov_162gp4ddpn().s[74]++;
      categories = [{
        id: 'career-advice',
        name: 'Career Advice',
        description: 'General career guidance and tips',
        postCount: 0,
        replyCount: 0,
        color: 'blue',
        children: []
      }, {
        id: 'job-search',
        name: 'Job Search',
        description: 'Job hunting strategies and experiences',
        postCount: 0,
        replyCount: 0,
        color: 'green',
        children: []
      }, {
        id: 'skill-development',
        name: 'Skill Development',
        description: 'Learning new skills and technologies',
        postCount: 0,
        replyCount: 0,
        color: 'purple',
        children: []
      }, {
        id: 'networking',
        name: 'Networking',
        description: 'Building professional connections',
        postCount: 0,
        replyCount: 0,
        color: 'orange',
        children: []
      }, {
        id: 'career-change',
        name: 'Career Change',
        description: 'Transitioning between careers',
        postCount: 0,
        replyCount: 0,
        color: 'red',
        children: []
      }, {
        id: 'freelancing',
        name: 'Freelancing',
        description: 'Independent work and consulting',
        postCount: 0,
        replyCount: 0,
        color: 'teal',
        children: []
      }];
      /* istanbul ignore next */
      cov_162gp4ddpn().s[75]++;
      return [2 /*return*/, server_1.NextResponse.json({
        success: true,
        data: categories
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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