{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useFormValidation.ts", "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCH,8CAuLC;AAKD,gDA0DC;AAKD,4CAgEC;AA1VD,+BAAyD;AACzD,6DAAgG;AA8BhG,SAAgB,iBAAiB,CAAC,OAAiC;IAAnE,iBAuLC;IArLG,IAAA,KAAK,GAKH,OAAO,MALJ,EACL,KAIE,OAAO,iBAJc,EAAvB,gBAAgB,mBAAG,IAAI,KAAA,EACvB,KAGE,OAAO,eAHY,EAArB,cAAc,mBAAG,IAAI,KAAA,EACrB,KAEE,OAAO,WAFO,EAAhB,UAAU,mBAAG,GAAG,KAAA,EAChB,kBAAkB,GAChB,OAAO,mBADS,CACR;IAEN,IAAA,KAAoB,IAAA,gBAAQ,EAAsB;QACtD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,IAAI;QACb,YAAY,EAAE,KAAK;QACnB,aAAa,EAAE,IAAI,GAAG,EAAE;KACzB,CAAC,EANK,KAAK,QAAA,EAAE,QAAQ,QAMpB,CAAC;IAEH,8BAA8B;IACxB,IAAA,KAA0C,IAAA,gBAAQ,EAAiC,EAAE,CAAC,EAArF,gBAAgB,QAAA,EAAE,mBAAmB,QAAgD,CAAC;IAE7F,0BAA0B;IAC1B,IAAA,iBAAS,EAAC;QACR,OAAO;YACL,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,YAAY,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,CAAC;QACxE,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEvB,gDAAgD;IAChD,IAAA,iBAAS,EAAC;QACR,IAAI,kBAAkB,EAAE,CAAC;YACvB,kBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAEtD,IAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,UAAO,SAAiB,EAAE,KAAU,oCAAG,OAAO;;;;YACxE,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YACpC,IAAI,CAAC,UAAU;gBAAE,sBAAO;YAExB,sCAAsC;YACtC,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;YAC5C,CAAC;YAED,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBAAM,IAAI,KAAE,YAAY,EAAE,IAAI,IAAG,EAAjC,CAAiC,CAAC,CAAC;YAG9C,KAAK,GAAG,UAAU,CAAC;;;oBACjB,MAAM,GAAG,uCAAmB,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;oBAE/E,QAAQ,CAAC,UAAA,IAAI;wBACX,IAAM,SAAS,gBAAQ,IAAI,CAAC,MAAM,CAAE,CAAC;wBACrC,IAAM,WAAW,qBAAO,IAAI,CAAC,QAAQ,OAAC,CAAC;wBAEvC,qBAAqB;wBACrB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;4BACjB,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;wBACtC,CAAC;6BAAM,CAAC;4BACN,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;wBAC9B,CAAC;wBAED,yBAAyB;wBACzB,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;4BAC5D,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;wBACnC,CAAC;wBAED,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;wBAEpD,6BACK,IAAI,KACP,MAAM,EAAE,SAAS,EACjB,QAAQ,EAAE,WAAW,EACrB,OAAO,SAAA,EACP,YAAY,EAAE,KAAK,IACnB;oBACJ,CAAC,CAAC,CAAC;oBAEH,iBAAiB;oBACjB,mBAAmB,CAAC,UAAA,IAAI;wBACtB,IAAM,SAAS,gBAAQ,IAAI,CAAE,CAAC;wBAC9B,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;wBAC5B,OAAO,SAAS,CAAC;oBACnB,CAAC,CAAC,CAAC;;;iBACJ,EAAE,UAAU,CAAC,CAAC;YAEf,mBAAmB,CAAC,UAAA,IAAI;;gBAAI,OAAA,uBAAM,IAAI,gBAAG,SAAS,IAAG,KAAK,OAAG;YAAjC,CAAiC,CAAC,CAAC;;;SAChE,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAE1C,IAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,UAAC,IAAyB;QACzD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBAAM,IAAI,KAAE,YAAY,EAAE,IAAI,IAAG,EAAjC,CAAiC,CAAC,CAAC;QAEpD,IAAM,MAAM,GAAG,uCAAmB,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAE7D,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ,EACzB,OAAO,EAAE,MAAM,CAAC,OAAO,EACvB,YAAY,EAAE,KAAK,IACnB,EANe,CAMf,CAAC,CAAC;QAEJ,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,IAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,UAAC,UAAqB;QACpD,QAAQ,CAAC,UAAA,IAAI;YACX,IAAM,SAAS,gBAAQ,IAAI,CAAC,MAAM,CAAE,CAAC;YAErC,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;oBAC1B,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,mBAAmB;gBACnB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;oBAChC,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;YAEpD,6BACK,IAAI,KACP,MAAM,EAAE,SAAS,EACjB,OAAO,SAAA,IACP;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,cAAc,GAAG,IAAA,mBAAW,EAAC;QACjC,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,MAAM,EAAE,EAAE,EACV,QAAQ,EAAE,EAAE,EACZ,OAAO,EAAE,IAAI,IACb,EALe,CAKf,CAAC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,gBAAgB,GAAG,IAAA,mBAAW,EAAC,UAAC,SAAiB;QACrD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,aAAa,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IACxE,EAHe,CAGf,CAAC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,eAAe,GAAG,IAAA,mBAAW,EAAC;QAClC,mBAAmB;QACnB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,YAAY,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,CAAC;QACtE,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAExB,QAAQ,CAAC;YACP,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,KAAK;YACnB,aAAa,EAAE,IAAI,GAAG,EAAE;SACzB,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEvB,IAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,UAAC,SAAiB;QAClD,OAAO,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnB,IAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,UAAC,SAAiB;QACjD,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnB,IAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,UAAC,SAAiB;QAClD,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnB,IAAM,OAAO,GAA0B;QACrC,aAAa,eAAA;QACb,YAAY,cAAA;QACZ,WAAW,aAAA;QACX,cAAc,gBAAA;QACd,gBAAgB,kBAAA;QAChB,eAAe,iBAAA;QACf,aAAa,eAAA;QACb,YAAY,cAAA;QACZ,aAAa,eAAA;KACd,CAAC;IAEF,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAChC,SAAiB,EACjB,KAAqB,EACrB,UAAwB;IAH1B,iBA0DC;IAvDC,2BAAA,EAAA,gBAAwB;IAElB,IAAA,KAAoB,IAAA,gBAAQ,GAAsB,EAAjD,KAAK,QAAA,EAAE,QAAQ,QAAkC,CAAC;IACnD,IAAA,KAAwB,IAAA,gBAAQ,GAAsB,EAArD,OAAO,QAAA,EAAE,UAAU,QAAkC,CAAC;IACvD,IAAA,KAAkC,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAAhD,YAAY,QAAA,EAAE,eAAe,QAAmB,CAAC;IAClD,IAAA,KAAoB,IAAA,gBAAQ,EAAwB,IAAI,CAAC,EAAxD,KAAK,QAAA,EAAE,QAAQ,QAAyC,CAAC;IAEhE,IAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,UAAO,KAAU;;;;YAC5C,uBAAuB;YACvB,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;YAED,eAAe,CAAC,IAAI,CAAC,CAAC;YAGhB,QAAQ,GAAG,UAAU,CAAC;;;oBACpB,MAAM,GAAG,uCAAmB,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;oBAE1E,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACvB,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC3B,eAAe,CAAC,KAAK,CAAC,CAAC;oBACvB,QAAQ,CAAC,IAAI,CAAC,CAAC;;;iBAChB,EAAE,UAAU,CAAC,CAAC;YAEf,QAAQ,CAAC,QAAQ,CAAC,CAAC;;;SACpB,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;IAE1C,IAAM,eAAe,GAAG,IAAA,mBAAW,EAAC;QAClC,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC;QACD,QAAQ,CAAC,SAAS,CAAC,CAAC;QACpB,UAAU,CAAC,SAAS,CAAC,CAAC;QACtB,eAAe,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,qBAAqB;IACrB,IAAA,iBAAS,EAAC;QACR,OAAO;YACL,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,OAAO;QACL,KAAK,OAAA;QACL,OAAO,SAAA;QACP,YAAY,cAAA;QACZ,OAAO,EAAE,CAAC,KAAK;QACf,QAAQ,UAAA;QACR,eAAe,iBAAA;KAChB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,WAAc,EACd,KAAqC,EACrC,QAA+E;IAHjF,iBAgEC;IA3DO,IAAA,KAAkB,IAAA,gBAAQ,EAAI,WAAW,CAAC,EAAzC,IAAI,QAAA,EAAE,OAAO,QAA4B,CAAC;IAC3C,IAAA,KAAkC,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAAhD,YAAY,QAAA,EAAE,eAAe,QAAmB,CAAC;IAClD,IAAA,KAAgC,IAAA,gBAAQ,GAAsB,EAA7D,WAAW,QAAA,EAAE,cAAc,QAAkC,CAAC;IAE/D,IAAA,KAAuC,iBAAiB,CAAC;QAC7D,KAAK,OAAA;QACL,gBAAgB,EAAE,IAAI;QACtB,cAAc,EAAE,IAAI;KACrB,CAAC,EAJK,eAAe,QAAA,EAAE,iBAAiB,QAIvC,CAAC;IAEH,IAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,UAAC,SAAkB,EAAE,KAAU;QAC7D,OAAO,CAAC,UAAA,IAAI;;YAAI,OAAA,uBAAM,IAAI,gBAAG,SAAS,IAAG,KAAK,OAAG;QAAjC,CAAiC,CAAC,CAAC;QACnD,iBAAiB,CAAC,aAAa,CAAC,SAAmB,EAAE,KAAK,CAAC,CAAC;QAC5D,iBAAiB,CAAC,gBAAgB,CAAC,SAAmB,CAAC,CAAC;IAC1D,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAExB,IAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,UAAO,CAAmB;;;;;oBACzD,IAAI,CAAC,EAAE,CAAC;wBACN,CAAC,CAAC,cAAc,EAAE,CAAC;oBACrB,CAAC;oBAED,cAAc,CAAC,SAAS,CAAC,CAAC;oBAC1B,eAAe,CAAC,IAAI,CAAC,CAAC;;;;oBAId,gBAAgB,GAAG,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAE9D,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;wBAC9B,eAAe,CAAC,KAAK,CAAC,CAAC;wBACvB,sBAAO;oBACT,CAAC;oBAED,6BAA6B;oBAC7B,qBAAM,QAAQ,CAAC,IAAI,EAAE,gBAAgB,CAAC,aAAa,IAAI,EAAE,CAAC,EAAA;;oBAD1D,6BAA6B;oBAC7B,SAA0D,CAAC;;;;oBAE3D,cAAc,CAAC,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC;;;oBAE7E,eAAe,CAAC,KAAK,CAAC,CAAC;;;;;SAE1B,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAExC,IAAM,SAAS,GAAG,IAAA,mBAAW,EAAC;QAC5B,OAAO,CAAC,WAAW,CAAC,CAAC;QACrB,cAAc,CAAC,SAAS,CAAC,CAAC;QAC1B,eAAe,CAAC,KAAK,CAAC,CAAC;QACvB,iBAAiB,CAAC,eAAe,EAAE,CAAC;IACtC,CAAC,EAAE,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAErC,OAAO;QACL,IAAI,MAAA;QACJ,WAAW,aAAA;QACX,YAAY,cAAA;QACZ,SAAS,WAAA;QACT,YAAY,cAAA;QACZ,WAAW,aAAA;QACX,UAAU,EAAE,eAAe;QAC3B,iBAAiB,mBAAA;KAClB,CAAC;AACJ,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useFormValidation.ts"], "sourcesContent": ["/**\n * React hook for comprehensive form validation\n * Provides real-time validation, error handling, and sanitization\n */\n\nimport { useState, useCallback, useEffect } from 'react';\nimport { ClientSideValidator, ValidationRule, ValidationResult } from '@/lib/client-validation';\n\nexport interface UseFormValidationOptions {\n  rules: Record<string, ValidationRule>;\n  validateOnChange?: boolean;\n  validateOnBlur?: boolean;\n  debounceMs?: number;\n  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;\n}\n\nexport interface FormValidationState {\n  errors: Record<string, string>;\n  warnings: string[];\n  isValid: boolean;\n  isValidating: boolean;\n  touchedFields: Set<string>;\n}\n\nexport interface FormValidationActions {\n  validateField: (fieldName: string, value: any) => Promise<void>;\n  validateForm: (data: Record<string, any>) => ValidationResult;\n  clearErrors: (fieldNames?: string[]) => void;\n  clearAllErrors: () => void;\n  markFieldTouched: (fieldName: string) => void;\n  resetValidation: () => void;\n  getFieldError: (fieldName: string) => string | undefined;\n  isFieldValid: (fieldName: string) => boolean;\n  hasFieldError: (fieldName: string) => boolean;\n}\n\nexport function useFormValidation(options: UseFormValidationOptions): [FormValidationState, FormValidationActions] {\n  const {\n    rules,\n    validateOnChange = true,\n    validateOnBlur = true,\n    debounceMs = 300,\n    onValidationChange\n  } = options;\n\n  const [state, setState] = useState<FormValidationState>({\n    errors: {},\n    warnings: [],\n    isValid: true,\n    isValidating: false,\n    touchedFields: new Set()\n  });\n\n  // Debounced validation timers\n  const [validationTimers, setValidationTimers] = useState<Record<string, NodeJS.Timeout>>({});\n\n  // Clear timers on unmount\n  useEffect(() => {\n    return () => {\n      Object.values(validationTimers).forEach(timer => clearTimeout(timer));\n    };\n  }, [validationTimers]);\n\n  // Notify parent component of validation changes\n  useEffect(() => {\n    if (onValidationChange) {\n      onValidationChange(state.isValid, state.errors);\n    }\n  }, [state.isValid, state.errors, onValidationChange]);\n\n  const validateField = useCallback(async (fieldName: string, value: any): Promise<void> => {\n    const fieldRules = rules[fieldName];\n    if (!fieldRules) return;\n\n    // Clear existing timer for this field\n    if (validationTimers[fieldName]) {\n      clearTimeout(validationTimers[fieldName]);\n    }\n\n    setState(prev => ({ ...prev, isValidating: true }));\n\n    // Set new timer for debounced validation\n    const timer = setTimeout(async () => {\n      const result = ClientSideValidator.validateField(fieldName, value, fieldRules);\n\n      setState(prev => {\n        const newErrors = { ...prev.errors };\n        const newWarnings = [...prev.warnings];\n\n        // Update field error\n        if (result.error) {\n          newErrors[fieldName] = result.error;\n        } else {\n          delete newErrors[fieldName];\n        }\n\n        // Add warning if present\n        if (result.warning && !newWarnings.includes(result.warning)) {\n          newWarnings.push(result.warning);\n        }\n\n        const isValid = Object.keys(newErrors).length === 0;\n\n        return {\n          ...prev,\n          errors: newErrors,\n          warnings: newWarnings,\n          isValid,\n          isValidating: false\n        };\n      });\n\n      // Clean up timer\n      setValidationTimers(prev => {\n        const newTimers = { ...prev };\n        delete newTimers[fieldName];\n        return newTimers;\n      });\n    }, debounceMs);\n\n    setValidationTimers(prev => ({ ...prev, [fieldName]: timer }));\n  }, [rules, debounceMs, validationTimers]);\n\n  const validateForm = useCallback((data: Record<string, any>): ValidationResult => {\n    setState(prev => ({ ...prev, isValidating: true }));\n\n    const result = ClientSideValidator.validateForm(data, rules);\n\n    setState(prev => ({\n      ...prev,\n      errors: result.errors,\n      warnings: result.warnings,\n      isValid: result.isValid,\n      isValidating: false\n    }));\n\n    return result;\n  }, [rules]);\n\n  const clearErrors = useCallback((fieldNames?: string[]) => {\n    setState(prev => {\n      const newErrors = { ...prev.errors };\n      \n      if (fieldNames) {\n        fieldNames.forEach(fieldName => {\n          delete newErrors[fieldName];\n        });\n      } else {\n        // Clear all errors\n        Object.keys(newErrors).forEach(key => {\n          delete newErrors[key];\n        });\n      }\n\n      const isValid = Object.keys(newErrors).length === 0;\n\n      return {\n        ...prev,\n        errors: newErrors,\n        isValid\n      };\n    });\n  }, []);\n\n  const clearAllErrors = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      errors: {},\n      warnings: [],\n      isValid: true\n    }));\n  }, []);\n\n  const markFieldTouched = useCallback((fieldName: string) => {\n    setState(prev => ({\n      ...prev,\n      touchedFields: new Set(Array.from(prev.touchedFields).concat(fieldName))\n    }));\n  }, []);\n\n  const resetValidation = useCallback(() => {\n    // Clear all timers\n    Object.values(validationTimers).forEach(timer => clearTimeout(timer));\n    setValidationTimers({});\n\n    setState({\n      errors: {},\n      warnings: [],\n      isValid: true,\n      isValidating: false,\n      touchedFields: new Set()\n    });\n  }, [validationTimers]);\n\n  const getFieldError = useCallback((fieldName: string): string | undefined => {\n    return state.errors[fieldName];\n  }, [state.errors]);\n\n  const isFieldValid = useCallback((fieldName: string): boolean => {\n    return !state.errors[fieldName];\n  }, [state.errors]);\n\n  const hasFieldError = useCallback((fieldName: string): boolean => {\n    return !!state.errors[fieldName];\n  }, [state.errors]);\n\n  const actions: FormValidationActions = {\n    validateField,\n    validateForm,\n    clearErrors,\n    clearAllErrors,\n    markFieldTouched,\n    resetValidation,\n    getFieldError,\n    isFieldValid,\n    hasFieldError\n  };\n\n  return [state, actions];\n}\n\n/**\n * Hook for validating a single field with real-time feedback\n */\nexport function useFieldValidation(\n  fieldName: string,\n  rules: ValidationRule,\n  debounceMs: number = 300\n) {\n  const [error, setError] = useState<string | undefined>();\n  const [warning, setWarning] = useState<string | undefined>();\n  const [isValidating, setIsValidating] = useState(false);\n  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);\n\n  const validate = useCallback(async (value: any) => {\n    // Clear existing timer\n    if (timer) {\n      clearTimeout(timer);\n    }\n\n    setIsValidating(true);\n\n    // Set new timer for debounced validation\n    const newTimer = setTimeout(async () => {\n      const result = ClientSideValidator.validateField(fieldName, value, rules);\n      \n      setError(result.error);\n      setWarning(result.warning);\n      setIsValidating(false);\n      setTimer(null);\n    }, debounceMs);\n\n    setTimer(newTimer);\n  }, [fieldName, rules, debounceMs, timer]);\n\n  const clearValidation = useCallback(() => {\n    if (timer) {\n      clearTimeout(timer);\n      setTimer(null);\n    }\n    setError(undefined);\n    setWarning(undefined);\n    setIsValidating(false);\n  }, [timer]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    };\n  }, [timer]);\n\n  return {\n    error,\n    warning,\n    isValidating,\n    isValid: !error,\n    validate,\n    clearValidation\n  };\n}\n\n/**\n * Hook for form submission with validation\n */\nexport function useValidatedForm<T extends Record<string, any>>(\n  initialData: T,\n  rules: Record<string, ValidationRule>,\n  onSubmit: (data: T, sanitizedData: Record<string, any>) => Promise<void> | void\n) {\n  const [data, setData] = useState<T>(initialData);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitError, setSubmitError] = useState<string | undefined>();\n\n  const [validationState, validationActions] = useFormValidation({\n    rules,\n    validateOnChange: true,\n    validateOnBlur: true\n  });\n\n  const updateField = useCallback((fieldName: keyof T, value: any) => {\n    setData(prev => ({ ...prev, [fieldName]: value }));\n    validationActions.validateField(fieldName as string, value);\n    validationActions.markFieldTouched(fieldName as string);\n  }, [validationActions]);\n\n  const handleSubmit = useCallback(async (e?: React.FormEvent) => {\n    if (e) {\n      e.preventDefault();\n    }\n\n    setSubmitError(undefined);\n    setIsSubmitting(true);\n\n    try {\n      // Validate entire form\n      const validationResult = validationActions.validateForm(data);\n\n      if (!validationResult.isValid) {\n        setIsSubmitting(false);\n        return;\n      }\n\n      // Submit with sanitized data\n      await onSubmit(data, validationResult.sanitizedData || {});\n    } catch (error) {\n      setSubmitError(error instanceof Error ? error.message : 'An error occurred');\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [data, validationActions, onSubmit]);\n\n  const resetForm = useCallback(() => {\n    setData(initialData);\n    setSubmitError(undefined);\n    setIsSubmitting(false);\n    validationActions.resetValidation();\n  }, [initialData, validationActions]);\n\n  return {\n    data,\n    updateField,\n    handleSubmit,\n    resetForm,\n    isSubmitting,\n    submitError,\n    validation: validationState,\n    validationActions\n  };\n}\n"], "version": 3}