9873a2e98429ca06505792fd07eb2601
"use strict";
/**
 * React hook for comprehensive form validation
 * Provides real-time validation, error handling, and sanitization
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useFormValidation = useFormValidation;
exports.useFieldValidation = useFieldValidation;
exports.useValidatedForm = useValidatedForm;
var react_1 = require("react");
var client_validation_1 = require("@/lib/client-validation");
function useFormValidation(options) {
    var _this = this;
    var rules = options.rules, _a = options.validateOnChange, validateOnChange = _a === void 0 ? true : _a, _b = options.validateOnBlur, validateOnBlur = _b === void 0 ? true : _b, _c = options.debounceMs, debounceMs = _c === void 0 ? 300 : _c, onValidationChange = options.onValidationChange;
    var _d = (0, react_1.useState)({
        errors: {},
        warnings: [],
        isValid: true,
        isValidating: false,
        touchedFields: new Set()
    }), state = _d[0], setState = _d[1];
    // Debounced validation timers
    var _e = (0, react_1.useState)({}), validationTimers = _e[0], setValidationTimers = _e[1];
    // Clear timers on unmount
    (0, react_1.useEffect)(function () {
        return function () {
            Object.values(validationTimers).forEach(function (timer) { return clearTimeout(timer); });
        };
    }, [validationTimers]);
    // Notify parent component of validation changes
    (0, react_1.useEffect)(function () {
        if (onValidationChange) {
            onValidationChange(state.isValid, state.errors);
        }
    }, [state.isValid, state.errors, onValidationChange]);
    var validateField = (0, react_1.useCallback)(function (fieldName, value) { return __awaiter(_this, void 0, Promise, function () {
        var fieldRules, timer;
        var _this = this;
        return __generator(this, function (_a) {
            fieldRules = rules[fieldName];
            if (!fieldRules)
                return [2 /*return*/];
            // Clear existing timer for this field
            if (validationTimers[fieldName]) {
                clearTimeout(validationTimers[fieldName]);
            }
            setState(function (prev) { return (__assign(__assign({}, prev), { isValidating: true })); });
            timer = setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                var result;
                return __generator(this, function (_a) {
                    result = client_validation_1.ClientSideValidator.validateField(fieldName, value, fieldRules);
                    setState(function (prev) {
                        var newErrors = __assign({}, prev.errors);
                        var newWarnings = __spreadArray([], prev.warnings, true);
                        // Update field error
                        if (result.error) {
                            newErrors[fieldName] = result.error;
                        }
                        else {
                            delete newErrors[fieldName];
                        }
                        // Add warning if present
                        if (result.warning && !newWarnings.includes(result.warning)) {
                            newWarnings.push(result.warning);
                        }
                        var isValid = Object.keys(newErrors).length === 0;
                        return __assign(__assign({}, prev), { errors: newErrors, warnings: newWarnings, isValid: isValid, isValidating: false });
                    });
                    // Clean up timer
                    setValidationTimers(function (prev) {
                        var newTimers = __assign({}, prev);
                        delete newTimers[fieldName];
                        return newTimers;
                    });
                    return [2 /*return*/];
                });
            }); }, debounceMs);
            setValidationTimers(function (prev) {
                var _a;
                return (__assign(__assign({}, prev), (_a = {}, _a[fieldName] = timer, _a)));
            });
            return [2 /*return*/];
        });
    }); }, [rules, debounceMs, validationTimers]);
    var validateForm = (0, react_1.useCallback)(function (data) {
        setState(function (prev) { return (__assign(__assign({}, prev), { isValidating: true })); });
        var result = client_validation_1.ClientSideValidator.validateForm(data, rules);
        setState(function (prev) { return (__assign(__assign({}, prev), { errors: result.errors, warnings: result.warnings, isValid: result.isValid, isValidating: false })); });
        return result;
    }, [rules]);
    var clearErrors = (0, react_1.useCallback)(function (fieldNames) {
        setState(function (prev) {
            var newErrors = __assign({}, prev.errors);
            if (fieldNames) {
                fieldNames.forEach(function (fieldName) {
                    delete newErrors[fieldName];
                });
            }
            else {
                // Clear all errors
                Object.keys(newErrors).forEach(function (key) {
                    delete newErrors[key];
                });
            }
            var isValid = Object.keys(newErrors).length === 0;
            return __assign(__assign({}, prev), { errors: newErrors, isValid: isValid });
        });
    }, []);
    var clearAllErrors = (0, react_1.useCallback)(function () {
        setState(function (prev) { return (__assign(__assign({}, prev), { errors: {}, warnings: [], isValid: true })); });
    }, []);
    var markFieldTouched = (0, react_1.useCallback)(function (fieldName) {
        setState(function (prev) { return (__assign(__assign({}, prev), { touchedFields: new Set(Array.from(prev.touchedFields).concat(fieldName)) })); });
    }, []);
    var resetValidation = (0, react_1.useCallback)(function () {
        // Clear all timers
        Object.values(validationTimers).forEach(function (timer) { return clearTimeout(timer); });
        setValidationTimers({});
        setState({
            errors: {},
            warnings: [],
            isValid: true,
            isValidating: false,
            touchedFields: new Set()
        });
    }, [validationTimers]);
    var getFieldError = (0, react_1.useCallback)(function (fieldName) {
        return state.errors[fieldName];
    }, [state.errors]);
    var isFieldValid = (0, react_1.useCallback)(function (fieldName) {
        return !state.errors[fieldName];
    }, [state.errors]);
    var hasFieldError = (0, react_1.useCallback)(function (fieldName) {
        return !!state.errors[fieldName];
    }, [state.errors]);
    var actions = {
        validateField: validateField,
        validateForm: validateForm,
        clearErrors: clearErrors,
        clearAllErrors: clearAllErrors,
        markFieldTouched: markFieldTouched,
        resetValidation: resetValidation,
        getFieldError: getFieldError,
        isFieldValid: isFieldValid,
        hasFieldError: hasFieldError
    };
    return [state, actions];
}
/**
 * Hook for validating a single field with real-time feedback
 */
function useFieldValidation(fieldName, rules, debounceMs) {
    var _this = this;
    if (debounceMs === void 0) { debounceMs = 300; }
    var _a = (0, react_1.useState)(), error = _a[0], setError = _a[1];
    var _b = (0, react_1.useState)(), warning = _b[0], setWarning = _b[1];
    var _c = (0, react_1.useState)(false), isValidating = _c[0], setIsValidating = _c[1];
    var _d = (0, react_1.useState)(null), timer = _d[0], setTimer = _d[1];
    var validate = (0, react_1.useCallback)(function (value) { return __awaiter(_this, void 0, void 0, function () {
        var newTimer;
        var _this = this;
        return __generator(this, function (_a) {
            // Clear existing timer
            if (timer) {
                clearTimeout(timer);
            }
            setIsValidating(true);
            newTimer = setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {
                var result;
                return __generator(this, function (_a) {
                    result = client_validation_1.ClientSideValidator.validateField(fieldName, value, rules);
                    setError(result.error);
                    setWarning(result.warning);
                    setIsValidating(false);
                    setTimer(null);
                    return [2 /*return*/];
                });
            }); }, debounceMs);
            setTimer(newTimer);
            return [2 /*return*/];
        });
    }); }, [fieldName, rules, debounceMs, timer]);
    var clearValidation = (0, react_1.useCallback)(function () {
        if (timer) {
            clearTimeout(timer);
            setTimer(null);
        }
        setError(undefined);
        setWarning(undefined);
        setIsValidating(false);
    }, [timer]);
    // Cleanup on unmount
    (0, react_1.useEffect)(function () {
        return function () {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [timer]);
    return {
        error: error,
        warning: warning,
        isValidating: isValidating,
        isValid: !error,
        validate: validate,
        clearValidation: clearValidation
    };
}
/**
 * Hook for form submission with validation
 */
function useValidatedForm(initialData, rules, onSubmit) {
    var _this = this;
    var _a = (0, react_1.useState)(initialData), data = _a[0], setData = _a[1];
    var _b = (0, react_1.useState)(false), isSubmitting = _b[0], setIsSubmitting = _b[1];
    var _c = (0, react_1.useState)(), submitError = _c[0], setSubmitError = _c[1];
    var _d = useFormValidation({
        rules: rules,
        validateOnChange: true,
        validateOnBlur: true
    }), validationState = _d[0], validationActions = _d[1];
    var updateField = (0, react_1.useCallback)(function (fieldName, value) {
        setData(function (prev) {
            var _a;
            return (__assign(__assign({}, prev), (_a = {}, _a[fieldName] = value, _a)));
        });
        validationActions.validateField(fieldName, value);
        validationActions.markFieldTouched(fieldName);
    }, [validationActions]);
    var handleSubmit = (0, react_1.useCallback)(function (e) { return __awaiter(_this, void 0, void 0, function () {
        var validationResult, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (e) {
                        e.preventDefault();
                    }
                    setSubmitError(undefined);
                    setIsSubmitting(true);
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 5]);
                    validationResult = validationActions.validateForm(data);
                    if (!validationResult.isValid) {
                        setIsSubmitting(false);
                        return [2 /*return*/];
                    }
                    // Submit with sanitized data
                    return [4 /*yield*/, onSubmit(data, validationResult.sanitizedData || {})];
                case 2:
                    // Submit with sanitized data
                    _a.sent();
                    return [3 /*break*/, 5];
                case 3:
                    error_1 = _a.sent();
                    setSubmitError(error_1 instanceof Error ? error_1.message : 'An error occurred');
                    return [3 /*break*/, 5];
                case 4:
                    setIsSubmitting(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); }, [data, validationActions, onSubmit]);
    var resetForm = (0, react_1.useCallback)(function () {
        setData(initialData);
        setSubmitError(undefined);
        setIsSubmitting(false);
        validationActions.resetValidation();
    }, [initialData, validationActions]);
    return {
        data: data,
        updateField: updateField,
        handleSubmit: handleSubmit,
        resetForm: resetForm,
        isSubmitting: isSubmitting,
        submitError: submitError,
        validation: validationState,
        validationActions: validationActions
    };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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