b39ccdd55af29e38c47685b97805adb8
"use strict";
/**
 * AI Service Duplication and Architecture Issues Tests
 *
 * These tests prove critical problems with multiple overlapping AI services
 * that create conflicts, inconsistencies, and architectural complexity.
 *
 * EXPECTED TO FAIL - These tests demonstrate service architecture flaws that need fixing.
 */
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
(0, globals_1.describe)('AI Service Duplication and Architecture Issues', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Multiple Overlapping AI Services', function () {
        (0, globals_1.it)('should fail - too many AI services with overlapping functionality', function () {
            // We have multiple AI services with overlapping functionality:
            // 1. SecureAIService (secure-ai-service.ts)
            // 2. SelfHealingAIService (self-healing-ai-service.ts) 
            // 3. OptimizedAIService (optimized-ai-service.ts)
            // 4. AIService (ai-service.ts)
            // 5. Enhanced AI functionality in question generators
            var secureAIServiceExists = true;
            var selfHealingAIServiceExists = true;
            var optimizedAIServiceExists = true;
            var basicAIServiceExists = true;
            var enhancedAIInGeneratorsExists = true;
            var totalAIServices = [
                secureAIServiceExists,
                selfHealingAIServiceExists,
                optimizedAIServiceExists,
                basicAIServiceExists,
                enhancedAIInGeneratorsExists
            ].filter(Boolean).length;
            // EXPECTED TO FAIL: Should have ONE unified AI service, not multiple overlapping ones
            (0, globals_1.expect)(totalAIServices).toBe(1);
        });
        (0, globals_1.it)('should fail - AI services have conflicting method signatures', function () {
            // Different AI services have different method signatures for similar functionality
            var secureAIMethodSignature = ['prompt', 'options', 'securityContext'];
            var selfHealingAIMethodSignature = ['prompt', 'config', 'retryOptions'];
            var optimizedAIMethodSignature = ['prompt', 'settings', 'cacheKey'];
            var basicAIMethodSignature = ['prompt', 'model'];
            // EXPECTED TO FAIL: All AI services should have consistent method signatures
            (0, globals_1.expect)(secureAIMethodSignature).toEqual(selfHealingAIMethodSignature);
            (0, globals_1.expect)(selfHealingAIMethodSignature).toEqual(optimizedAIMethodSignature);
            (0, globals_1.expect)(optimizedAIMethodSignature).toEqual(basicAIMethodSignature);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Inconsistent AI Provider Management', function () {
        (0, globals_1.it)('should fail - different AI services use different providers inconsistently', function () {
            // Different services support different AI providers
            var secureAIProviders = ['openai', 'anthropic', 'google'];
            var selfHealingAIProviders = ['openai', 'google', 'azure'];
            var optimizedAIProviders = ['google', 'anthropic'];
            var basicAIProviders = ['openai'];
            // EXPECTED TO FAIL: All AI services should support the same set of providers
            (0, globals_1.expect)(secureAIProviders).toEqual(selfHealingAIProviders);
            (0, globals_1.expect)(selfHealingAIProviders).toEqual(optimizedAIProviders);
            (0, globals_1.expect)(optimizedAIProviders).toEqual(basicAIProviders);
        });
        (0, globals_1.it)('should fail - AI provider fallback logic is inconsistent', function () {
            // Different services have different fallback strategies
            var secureAIFallbackOrder = ['openai', 'anthropic', 'google'];
            var selfHealingAIFallbackOrder = ['google', 'openai', 'azure'];
            var optimizedAIFallbackOrder = ['google', 'anthropic'];
            // Mock provider failure scenarios
            var openaiDown = true;
            var googleDown = false;
            var anthropicDown = false;
            // EXPECTED TO FAIL: All services should use the same fallback strategy
            (0, globals_1.expect)(secureAIFallbackOrder).toEqual(selfHealingAIFallbackOrder);
            (0, globals_1.expect)(selfHealingAIFallbackOrder).toEqual(optimizedAIFallbackOrder);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Conflicting Caching Strategies', function () {
        (0, globals_1.it)('should fail - AI services use different caching keys for same requests', function () {
            // Different services generate different cache keys for identical requests
            var prompt = 'Generate interview questions for React developer';
            var model = 'gpt-4';
            var secureAICacheKey = "secure_".concat(prompt, "_").concat(model);
            var selfHealingAICacheKey = "healing_".concat(prompt.toLowerCase(), "_").concat(model);
            var optimizedAICacheKey = "opt_".concat(Buffer.from(prompt).toString('base64'), "_").concat(model);
            // EXPECTED TO FAIL: Same requests should generate same cache keys across all services
            (0, globals_1.expect)(secureAICacheKey).toBe(selfHealingAICacheKey);
            (0, globals_1.expect)(selfHealingAICacheKey).toBe(optimizedAICacheKey);
        });
        (0, globals_1.it)('should fail - AI services have conflicting cache TTL values', function () {
            // Different services use different cache expiration times
            var secureAICacheTTL = 3600; // 1 hour
            var selfHealingAICacheTTL = 7200; // 2 hours
            var optimizedAICacheTTL = 1800; // 30 minutes
            var basicAICacheTTL = 0; // No caching
            // EXPECTED TO FAIL: All AI services should use consistent cache TTL
            (0, globals_1.expect)(secureAICacheTTL).toBe(selfHealingAICacheTTL);
            (0, globals_1.expect)(selfHealingAICacheTTL).toBe(optimizedAICacheTTL);
            (0, globals_1.expect)(optimizedAICacheTTL).toBeGreaterThan(basicAICacheTTL);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Inconsistent Error Handling and Retry Logic', function () {
        (0, globals_1.it)('should fail - AI services have different retry strategies', function () {
            // Different services use different retry configurations
            var secureAIRetryConfig = {
                maxRetries: 3,
                backoffMultiplier: 2,
                initialDelay: 1000
            };
            var selfHealingAIRetryConfig = {
                maxRetries: 5,
                backoffMultiplier: 1.5,
                initialDelay: 500
            };
            var optimizedAIRetryConfig = {
                maxRetries: 2,
                backoffMultiplier: 3,
                initialDelay: 2000
            };
            // EXPECTED TO FAIL: All AI services should use consistent retry strategies
            (0, globals_1.expect)(secureAIRetryConfig).toEqual(selfHealingAIRetryConfig);
            (0, globals_1.expect)(selfHealingAIRetryConfig).toEqual(optimizedAIRetryConfig);
        });
        (0, globals_1.it)('should fail - AI services return different error formats', function () {
            // Different services return different error structures
            var secureAIError = {
                success: false,
                error: 'AI request failed',
                code: 'AI_ERROR',
                retryable: true
            };
            var selfHealingAIError = {
                isSuccess: false,
                message: 'Request failed',
                errorType: 'PROVIDER_ERROR',
                canRetry: true,
                attempts: 3
            };
            var optimizedAIError = {
                ok: false,
                err: 'Failed to process',
                status: 500,
                retry: true
            };
            // EXPECTED TO FAIL: All AI services should return consistent error formats
            (0, globals_1.expect)(Object.keys(secureAIError)).toEqual(Object.keys(selfHealingAIError));
            (0, globals_1.expect)(Object.keys(selfHealingAIError)).toEqual(Object.keys(optimizedAIError));
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: Resource Management Conflicts', function () {
        (0, globals_1.it)('should fail - AI services can create resource conflicts', function () {
            // Multiple AI services might try to use the same resources simultaneously
            var concurrentRequests = [
                { service: 'SecureAI', provider: 'openai', timestamp: Date.now() },
                { service: 'SelfHealingAI', provider: 'openai', timestamp: Date.now() },
                { service: 'OptimizedAI', provider: 'openai', timestamp: Date.now() }
            ];
            // Mock rate limit: only 2 concurrent requests allowed per provider
            var maxConcurrentPerProvider = 2;
            var openaiRequests = concurrentRequests.filter(function (req) { return req.provider === 'openai'; });
            // EXPECTED TO FAIL: Should not exceed rate limits due to multiple services
            (0, globals_1.expect)(openaiRequests.length).toBeLessThanOrEqual(maxConcurrentPerProvider);
        });
        (0, globals_1.it)('should fail - AI services dont coordinate API quota usage', function () {
            // Different services don't share quota information
            var secureAIQuotaUsed = 1000; // tokens used
            var selfHealingAIQuotaUsed = 800; // tokens used
            var optimizedAIQuotaUsed = 500; // tokens used
            var totalQuotaUsed = secureAIQuotaUsed + selfHealingAIQuotaUsed + optimizedAIQuotaUsed;
            var dailyQuotaLimit = 2000; // total daily limit
            // EXPECTED TO FAIL: Services should coordinate to stay within quota
            (0, globals_1.expect)(totalQuotaUsed).toBeLessThanOrEqual(dailyQuotaLimit);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 6: Configuration Management Problems', function () {
        (0, globals_1.it)('should fail - AI services use different configuration sources', function () {
            // Different services read configuration from different places
            var secureAIConfigSource = 'environment variables';
            var selfHealingAIConfigSource = 'config file';
            var optimizedAIConfigSource = 'database';
            var basicAIConfigSource = 'hardcoded values';
            // EXPECTED TO FAIL: All AI services should use the same configuration source
            (0, globals_1.expect)(secureAIConfigSource).toBe(selfHealingAIConfigSource);
            (0, globals_1.expect)(selfHealingAIConfigSource).toBe(optimizedAIConfigSource);
            (0, globals_1.expect)(optimizedAIConfigSource).toBe(basicAIConfigSource);
        });
        (0, globals_1.it)('should fail - AI services have inconsistent model selection logic', function () {
            // Different services use different logic to select AI models
            var secureAIModelSelection = {
                default: 'gpt-4',
                fallback: 'gpt-3.5-turbo',
                criteria: 'security-first'
            };
            var selfHealingAIModelSelection = {
                primary: 'gemini-pro',
                secondary: 'gpt-4',
                strategy: 'reliability-first'
            };
            var optimizedAIModelSelection = {
                preferred: 'claude-3',
                backup: 'gemini-pro',
                optimization: 'speed-first'
            };
            // EXPECTED TO FAIL: All services should use consistent model selection logic
            (0, globals_1.expect)(secureAIModelSelection.default).toBe(selfHealingAIModelSelection.primary);
            (0, globals_1.expect)(selfHealingAIModelSelection.primary).toBe(optimizedAIModelSelection.preferred);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 7: Monitoring and Observability Gaps', function () {
        (0, globals_1.it)('should fail - AI services have inconsistent logging formats', function () {
            // Different services log in different formats
            var secureAILogFormat = {
                timestamp: '2024-01-01T00:00:00Z',
                level: 'INFO',
                service: 'SecureAI',
                message: 'Request processed',
                metadata: { requestId: 'req-123' }
            };
            var selfHealingAILogFormat = {
                time: 1704067200000,
                severity: 'info',
                component: 'SelfHealingAI',
                msg: 'Request completed',
                context: { id: 'req-123' }
            };
            // EXPECTED TO FAIL: All services should use consistent logging formats
            (0, globals_1.expect)(Object.keys(secureAILogFormat)).toEqual(Object.keys(selfHealingAILogFormat));
        });
        (0, globals_1.it)('should fail - AI services dont provide unified metrics', function () {
            // Different services track different metrics
            var secureAIMetrics = ['request_count', 'response_time', 'error_rate', 'security_score'];
            var selfHealingAIMetrics = ['total_requests', 'avg_latency', 'failure_rate', 'retry_count'];
            var optimizedAIMetrics = ['throughput', 'cache_hit_rate', 'cost_per_request'];
            // EXPECTED TO FAIL: All services should track the same core metrics
            (0, globals_1.expect)(secureAIMetrics).toEqual(selfHealingAIMetrics);
            (0, globals_1.expect)(selfHealingAIMetrics).toEqual(optimizedAIMetrics);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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