fba52f5e962bc9070f773e5fe178c30c
"use strict";
/**
 * Test Performance and Scalability Issues Tests
 *
 * These tests prove test performance problems including slow execution times,
 * memory leaks, and unbounded resource usage during test runs.
 *
 * EXPECTED TO FAIL - These tests demonstrate performance issues that need fixing.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
(0, globals_1.describe)('Test Performance and Scalability Issues', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Slow Test Execution Times', function () {
        (0, globals_1.it)('should fail - test suite execution time exceeds acceptable limits', function () { return __awaiter(void 0, void 0, void 0, function () {
            function findTestFiles(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        findTestFiles(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        testFiles.push(filePath);
                    }
                });
            }
            var startTime, testFiles, testDirectory, slowTestFiles, executionTime;
            return __generator(this, function (_a) {
                startTime = Date.now();
                testFiles = [];
                testDirectory = path_1.default.join(process.cwd(), '__tests__');
                findTestFiles(testDirectory);
                slowTestFiles = [];
                testFiles.forEach(function (testFile) {
                    try {
                        var content_1 = fs_1.default.readFileSync(testFile, 'utf8');
                        // Count potentially slow operations
                        var slowOperations = [
                            /setTimeout|setInterval/gi,
                            /fetch\(/gi,
                            /prisma\./gi,
                            /database/gi,
                            /ai-service|gemini/gi
                        ];
                        var slowOpCount_1 = 0;
                        slowOperations.forEach(function (pattern) {
                            slowOpCount_1 += (content_1.match(pattern) || []).length;
                        });
                        var testCount = (content_1.match(/it\(/g) || []).length;
                        var slowOpsPerTest = testCount > 0 ? slowOpCount_1 / testCount : 0;
                        // If more than 3 slow operations per test, it's likely slow
                        if (slowOpsPerTest > 3) {
                            slowTestFiles.push({
                                file: testFile,
                                slowOpCount: slowOpCount_1,
                                testCount: testCount,
                                slowOpsPerTest: Math.round(slowOpsPerTest * 100) / 100
                            });
                        }
                    }
                    catch (error) {
                        // Skip files that can't be read
                    }
                });
                executionTime = Date.now() - startTime;
                // EXPECTED TO FAIL: Test execution should be fast (under 100ms for analysis)
                (0, globals_1.expect)(executionTime).toBeLessThan(100);
                // EXPECTED TO FAIL: Should not have many slow test files
                (0, globals_1.expect)(slowTestFiles.length).toBeLessThan(5);
                return [2 /*return*/];
            });
        }); });
        (0, globals_1.it)('should fail - individual tests take too long to execute', function () {
            // Check for tests with excessive timeouts or slow operations
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var slowIndividualTests = [];
            function analyzeTestSpeed(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestSpeed(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_2 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Look for tests with long timeouts
                            var timeoutMatches = content_2.match(/\.timeout\((\d+)\)/g) || [];
                            timeoutMatches.forEach(function (timeoutMatch) {
                                var timeout = parseInt(timeoutMatch.match(/\d+/)[0]);
                                if (timeout > 30000) { // More than 30 seconds
                                    slowIndividualTests.push({
                                        file: filePath,
                                        issue: "Excessive timeout: ".concat(timeout, "ms")
                                    });
                                }
                            });
                            // Look for synchronous operations that should be async
                            var syncOperations = [
                                /fs\.readFileSync/gi,
                                /fs\.writeFileSync/gi,
                                /JSON\.parse.*fs\./gi
                            ];
                            var syncOpCount_1 = 0;
                            syncOperations.forEach(function (pattern) {
                                syncOpCount_1 += (content_2.match(pattern) || []).length;
                            });
                            if (syncOpCount_1 > 5) {
                                slowIndividualTests.push({
                                    file: filePath,
                                    issue: "Too many synchronous operations: ".concat(syncOpCount_1)
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestSpeed(testDirectory);
            // EXPECTED TO FAIL: Individual tests should not be slow
            (0, globals_1.expect)(slowIndividualTests.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Memory Leaks in Tests', function () {
        (0, globals_1.it)('should fail - tests create memory leaks through unclosed resources', function () {
            // Check for potential memory leak patterns
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var memoryLeakIssues = [];
            function analyzeMemoryLeaks(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeMemoryLeaks(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_3 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for unclosed resources
                            var resourceCreation = [
                                /new PrismaClient/gi,
                                /createConnection/gi,
                                /setInterval/gi,
                                /setTimeout.*\d{4,}/gi, // Long timeouts
                                /addEventListener/gi
                            ];
                            var resourceCleanup = [
                                /disconnect|close|clear|remove/gi,
                                /afterEach|afterAll/gi
                            ];
                            var creationCount_1 = 0;
                            resourceCreation.forEach(function (pattern) {
                                creationCount_1 += (content_3.match(pattern) || []).length;
                            });
                            var cleanupCount_1 = 0;
                            resourceCleanup.forEach(function (pattern) {
                                cleanupCount_1 += (content_3.match(pattern) || []).length;
                            });
                            // If creating resources without cleanup
                            if (creationCount_1 > 2 && cleanupCount_1 === 0) {
                                memoryLeakIssues.push({
                                    file: filePath,
                                    creationCount: creationCount_1,
                                    cleanupCount: cleanupCount_1,
                                    issue: 'Resources created without cleanup'
                                });
                            }
                            // Check for global variable accumulation
                            var globalVarAssignments = (content_3.match(/global\.\w+\s*=/g) || []).length;
                            if (globalVarAssignments > 3) {
                                memoryLeakIssues.push({
                                    file: filePath,
                                    issue: "Too many global variable assignments: ".concat(globalVarAssignments)
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeMemoryLeaks(testDirectory);
            // EXPECTED TO FAIL: Tests should not create memory leaks
            (0, globals_1.expect)(memoryLeakIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test data accumulates without proper cleanup', function () {
            // Check for test data accumulation issues
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var dataAccumulationIssues = [];
            function analyzeDataAccumulation(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeDataAccumulation(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_4 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for data creation without cleanup
                            var dataCreation = [
                                /\.create\(/gi,
                                /\.insert\(/gi,
                                /\.save\(/gi,
                                /push\(/gi
                            ];
                            var dataCleanup = [
                                /\.delete\(/gi,
                                /\.remove\(/gi,
                                /\.clear\(/gi,
                                /\.truncate\(/gi,
                                /\.pop\(/gi,
                                /\.splice\(/gi
                            ];
                            var creationOps_1 = 0;
                            dataCreation.forEach(function (pattern) {
                                creationOps_1 += (content_4.match(pattern) || []).length;
                            });
                            var cleanupOps_1 = 0;
                            dataCleanup.forEach(function (pattern) {
                                cleanupOps_1 += (content_4.match(pattern) || []).length;
                            });
                            // If creating much more data than cleaning up
                            if (creationOps_1 > 5 && cleanupOps_1 < creationOps_1 * 0.5) {
                                dataAccumulationIssues.push({
                                    file: filePath,
                                    creationOps: creationOps_1,
                                    cleanupOps: cleanupOps_1,
                                    ratio: Math.round((cleanupOps_1 / creationOps_1) * 100) / 100
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeDataAccumulation(testDirectory);
            // EXPECTED TO FAIL: Test data should be properly cleaned up
            (0, globals_1.expect)(dataAccumulationIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Unbounded Resource Usage', function () {
        (0, globals_1.it)('should fail - tests use unbounded arrays or objects that grow indefinitely', function () {
            // Check for unbounded data structures
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var unboundedResourceIssues = [];
            function analyzeUnboundedResources(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeUnboundedResources(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_5 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Look for potentially unbounded operations
                            var unboundedPatterns = [
                                /while\s*\(true\)/gi,
                                /for\s*\(\s*;\s*;\s*\)/gi,
                                /setInterval(?!.*clear)/gi,
                                /\.push\(.*\).*for|while/gi,
                                /Array\(\d{4,}\)/gi // Large array creation
                            ];
                            var unboundedCount_1 = 0;
                            unboundedPatterns.forEach(function (pattern) {
                                unboundedCount_1 += (content_5.match(pattern) || []).length;
                            });
                            // Check for large test data generation
                            var largeDataMatches = content_5.match(/Array\.from.*\d{3,}|new Array\(\d{3,}\)/gi) || [];
                            if (unboundedCount_1 > 0 || largeDataMatches.length > 2) {
                                unboundedResourceIssues.push({
                                    file: filePath,
                                    unboundedCount: unboundedCount_1,
                                    largeDataCount: largeDataMatches.length,
                                    issues: largeDataMatches
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeUnboundedResources(testDirectory);
            // EXPECTED TO FAIL: Tests should not use unbounded resources
            (0, globals_1.expect)(unboundedResourceIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - concurrent test execution causes resource contention', function () {
            // Check for resource contention issues
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            var resourceContentionIssues = [];
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                // Check maxWorkers configuration
                var maxWorkersMatch = configContent.match(/maxWorkers:\s*['"`]?([^'"`\s,}]+)['"`]?/);
                var maxWorkers = maxWorkersMatch ? maxWorkersMatch[1] : '50%';
                // Check if maxWorkers is too high for resource-intensive tests
                if (maxWorkers === '100%' || (typeof maxWorkers === 'string' && maxWorkers.includes('%') && parseInt(maxWorkers) > 75)) {
                    resourceContentionIssues.push('maxWorkers too high for resource-intensive tests');
                }
                // Check for database connection pooling issues
                var testDirectory = path_1.default.join(process.cwd(), '__tests__');
                var dbTestCount_1 = 0;
                function countDbTests(dir) {
                    if (!fs_1.default.existsSync(dir))
                        return;
                    var files = fs_1.default.readdirSync(dir);
                    files.forEach(function (file) {
                        var filePath = path_1.default.join(dir, file);
                        var stat = fs_1.default.statSync(filePath);
                        if (stat.isDirectory()) {
                            countDbTests(filePath);
                        }
                        else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                            try {
                                var content = fs_1.default.readFileSync(filePath, 'utf8');
                                if (content.includes('prisma') || content.includes('database')) {
                                    dbTestCount_1++;
                                }
                            }
                            catch (error) {
                                // Skip files that can't be read
                            }
                        }
                    });
                }
                countDbTests(testDirectory);
                // If many database tests with high concurrency
                if (dbTestCount_1 > 5 && (maxWorkers === '100%' || parseInt(maxWorkers) > 50)) {
                    resourceContentionIssues.push("".concat(dbTestCount_1, " database tests with high concurrency (").concat(maxWorkers, ")"));
                }
            }
            // EXPECTED TO FAIL: Should not have resource contention issues
            (0, globals_1.expect)(resourceContentionIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Test Suite Scalability Problems', function () {
        (0, globals_1.it)('should fail - test suite does not scale with codebase growth', function () {
            // Analyze test suite scalability
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var srcDirectory = path_1.default.join(process.cwd(), 'src');
            var testFileCount = 0;
            var srcFileCount = 0;
            function countFiles(dir, isTestDir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        countFiles(filePath, isTestDir);
                    }
                    else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
                        if (isTestDir && (file.includes('.test.') || file.includes('.spec.'))) {
                            testFileCount++;
                        }
                        else if (!isTestDir && !file.includes('.test.') && !file.includes('.spec.')) {
                            srcFileCount++;
                        }
                    }
                });
            }
            countFiles(testDirectory, true);
            countFiles(srcDirectory, false);
            var testCoverageRatio = srcFileCount > 0 ? testFileCount / srcFileCount : 0;
            // EXPECTED TO FAIL: Should have reasonable test coverage ratio (at least 0.5)
            (0, globals_1.expect)(testCoverageRatio).toBeGreaterThan(0.5);
            // Check for test organization scalability
            var scalabilityIssues = [];
            // Tests should be organized in directories
            if (testFileCount > 20) {
                var testSubdirs = fs_1.default.readdirSync(testDirectory).filter(function (item) {
                    var itemPath = path_1.default.join(testDirectory, item);
                    return fs_1.default.statSync(itemPath).isDirectory();
                });
                if (testSubdirs.length < 3) {
                    scalabilityIssues.push('Large test suite not properly organized into subdirectories');
                }
            }
            // EXPECTED TO FAIL: Should not have scalability issues
            (0, globals_1.expect)(scalabilityIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test execution time grows non-linearly with test count', function () {
            // Analyze test execution time scaling
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var performanceIssues = [];
            function analyzeTestComplexity(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestComplexity(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_6 = fs_1.default.readFileSync(filePath, 'utf8');
                            var testCount = (content_6.match(/it\(/g) || []).length;
                            var complexOperations = [
                                /nested.*describe/gi,
                                /for.*for/gi, // Nested loops
                                /while.*while/gi,
                                /\.map.*\.filter.*\.reduce/gi // Chained operations
                            ];
                            var complexityScore_1 = 0;
                            complexOperations.forEach(function (pattern) {
                                complexityScore_1 += (content_6.match(pattern) || []).length * 2;
                            });
                            // Add base complexity for each test
                            complexityScore_1 += testCount;
                            var complexityPerTest = testCount > 0 ? complexityScore_1 / testCount : 0;
                            // If complexity per test is too high
                            if (complexityPerTest > 5) {
                                performanceIssues.push({
                                    file: filePath,
                                    testCount: testCount,
                                    complexityScore: complexityScore_1,
                                    complexityPerTest: Math.round(complexityPerTest * 100) / 100
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestComplexity(testDirectory);
            // EXPECTED TO FAIL: Test complexity should be manageable
            (0, globals_1.expect)(performanceIssues.length).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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