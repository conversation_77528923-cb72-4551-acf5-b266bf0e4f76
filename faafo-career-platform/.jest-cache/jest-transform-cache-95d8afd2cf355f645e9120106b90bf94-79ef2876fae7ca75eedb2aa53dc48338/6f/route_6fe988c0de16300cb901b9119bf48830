ebd970ce02bf822a834cce924dd85b70
"use strict";

/* istanbul ignore next */
function cov_1fogj4o552() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/route.ts";
  var hash = "49bd72f882b9527672e9d453e702e90fce66adeb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "68": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 52
        }
      },
      "69": {
        start: {
          line: 40,
          column: 15
        },
        end: {
          line: 40,
          column: 37
        }
      },
      "70": {
        start: {
          line: 41,
          column: 18
        },
        end: {
          line: 41,
          column: 38
        }
      },
      "71": {
        start: {
          line: 42,
          column: 13
        },
        end: {
          line: 42,
          column: 34
        }
      },
      "72": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 38
        }
      },
      "73": {
        start: {
          line: 44,
          column: 34
        },
        end: {
          line: 44,
          column: 76
        }
      },
      "74": {
        start: {
          line: 45,
          column: 18
        },
        end: {
          line: 45,
          column: 44
        }
      },
      "75": {
        start: {
          line: 46,
          column: 19
        },
        end: {
          line: 46,
          column: 46
        }
      },
      "76": {
        start: {
          line: 48,
          column: 0
        },
        end: {
          line: 201,
          column: 7
        }
      },
      "77": {
        start: {
          line: 48,
          column: 99
        },
        end: {
          line: 201,
          column: 3
        }
      },
      "78": {
        start: {
          line: 49,
          column: 17
        },
        end: {
          line: 49,
          column: 26
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "80": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 199,
          column: 20
        }
      },
      "81": {
        start: {
          line: 51,
          column: 132
        },
        end: {
          line: 199,
          column: 15
        }
      },
      "82": {
        start: {
          line: 54,
          column: 16
        },
        end: {
          line: 198,
          column: 19
        }
      },
      "83": {
        start: {
          line: 55,
          column: 20
        },
        end: {
          line: 197,
          column: 21
        }
      },
      "84": {
        start: {
          line: 56,
          column: 32
        },
        end: {
          line: 56,
          column: 61
        }
      },
      "85": {
        start: {
          line: 58,
          column: 28
        },
        end: {
          line: 58,
          column: 56
        }
      },
      "86": {
        start: {
          line: 59,
          column: 28
        },
        end: {
          line: 59,
          column: 104
        }
      },
      "87": {
        start: {
          line: 61,
          column: 28
        },
        end: {
          line: 61,
          column: 48
        }
      },
      "88": {
        start: {
          line: 62,
          column: 28
        },
        end: {
          line: 62,
          column: 150
        }
      },
      "89": {
        start: {
          line: 63,
          column: 28
        },
        end: {
          line: 68,
          column: 29
        }
      },
      "90": {
        start: {
          line: 64,
          column: 32
        },
        end: {
          line: 67,
          column: 57
        }
      },
      "91": {
        start: {
          line: 69,
          column: 28
        },
        end: {
          line: 175,
          column: 36
        }
      },
      "92": {
        start: {
          line: 177,
          column: 28
        },
        end: {
          line: 177,
          column: 45
        }
      },
      "93": {
        start: {
          line: 178,
          column: 28
        },
        end: {
          line: 183,
          column: 29
        }
      },
      "94": {
        start: {
          line: 179,
          column: 32
        },
        end: {
          line: 182,
          column: 57
        }
      },
      "95": {
        start: {
          line: 185,
          column: 28
        },
        end: {
          line: 192,
          column: 29
        }
      },
      "96": {
        start: {
          line: 186,
          column: 32
        },
        end: {
          line: 191,
          column: 35
        }
      },
      "97": {
        start: {
          line: 190,
          column: 36
        },
        end: {
          line: 190,
          column: 92
        }
      },
      "98": {
        start: {
          line: 193,
          column: 28
        },
        end: {
          line: 196,
          column: 36
        }
      },
      "99": {
        start: {
          line: 203,
          column: 0
        },
        end: {
          line: 300,
          column: 7
        }
      },
      "100": {
        start: {
          line: 203,
          column: 99
        },
        end: {
          line: 300,
          column: 3
        }
      },
      "101": {
        start: {
          line: 204,
          column: 17
        },
        end: {
          line: 204,
          column: 26
        }
      },
      "102": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "103": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 298,
          column: 20
        }
      },
      "104": {
        start: {
          line: 206,
          column: 131
        },
        end: {
          line: 298,
          column: 15
        }
      },
      "105": {
        start: {
          line: 209,
          column: 16
        },
        end: {
          line: 297,
          column: 19
        }
      },
      "106": {
        start: {
          line: 210,
          column: 20
        },
        end: {
          line: 296,
          column: 21
        }
      },
      "107": {
        start: {
          line: 211,
          column: 32
        },
        end: {
          line: 211,
          column: 108
        }
      },
      "108": {
        start: {
          line: 213,
          column: 28
        },
        end: {
          line: 213,
          column: 48
        }
      },
      "109": {
        start: {
          line: 214,
          column: 28
        },
        end: {
          line: 219,
          column: 29
        }
      },
      "110": {
        start: {
          line: 215,
          column: 32
        },
        end: {
          line: 218,
          column: 57
        }
      },
      "111": {
        start: {
          line: 220,
          column: 28
        },
        end: {
          line: 220,
          column: 57
        }
      },
      "112": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 56
        }
      },
      "113": {
        start: {
          line: 223,
          column: 28
        },
        end: {
          line: 223,
          column: 65
        }
      },
      "114": {
        start: {
          line: 225,
          column: 28
        },
        end: {
          line: 225,
          column: 45
        }
      },
      "115": {
        start: {
          line: 226,
          column: 28
        },
        end: {
          line: 226,
          column: 101
        }
      },
      "116": {
        start: {
          line: 227,
          column: 28
        },
        end: {
          line: 232,
          column: 29
        }
      },
      "117": {
        start: {
          line: 228,
          column: 32
        },
        end: {
          line: 231,
          column: 57
        }
      },
      "118": {
        start: {
          line: 233,
          column: 28
        },
        end: {
          line: 236,
          column: 36
        }
      },
      "119": {
        start: {
          line: 238,
          column: 28
        },
        end: {
          line: 238,
          column: 53
        }
      },
      "120": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 244,
          column: 29
        }
      },
      "121": {
        start: {
          line: 240,
          column: 32
        },
        end: {
          line: 243,
          column: 57
        }
      },
      "122": {
        start: {
          line: 245,
          column: 28
        },
        end: {
          line: 250,
          column: 29
        }
      },
      "123": {
        start: {
          line: 246,
          column: 32
        },
        end: {
          line: 249,
          column: 57
        }
      },
      "124": {
        start: {
          line: 251,
          column: 28
        },
        end: {
          line: 288,
          column: 36
        }
      },
      "125": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 52
        }
      },
      "126": {
        start: {
          line: 291,
          column: 28
        },
        end: {
          line: 295,
          column: 36
        }
      },
      "127": {
        start: {
          line: 302,
          column: 0
        },
        end: {
          line: 365,
          column: 7
        }
      },
      "128": {
        start: {
          line: 302,
          column: 102
        },
        end: {
          line: 365,
          column: 3
        }
      },
      "129": {
        start: {
          line: 303,
          column: 17
        },
        end: {
          line: 303,
          column: 26
        }
      },
      "130": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 364,
          column: 7
        }
      },
      "131": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 363,
          column: 20
        }
      },
      "132": {
        start: {
          line: 305,
          column: 130
        },
        end: {
          line: 363,
          column: 15
        }
      },
      "133": {
        start: {
          line: 308,
          column: 16
        },
        end: {
          line: 362,
          column: 19
        }
      },
      "134": {
        start: {
          line: 309,
          column: 20
        },
        end: {
          line: 361,
          column: 21
        }
      },
      "135": {
        start: {
          line: 310,
          column: 32
        },
        end: {
          line: 310,
          column: 108
        }
      },
      "136": {
        start: {
          line: 312,
          column: 28
        },
        end: {
          line: 312,
          column: 48
        }
      },
      "137": {
        start: {
          line: 313,
          column: 28
        },
        end: {
          line: 318,
          column: 29
        }
      },
      "138": {
        start: {
          line: 314,
          column: 32
        },
        end: {
          line: 317,
          column: 57
        }
      },
      "139": {
        start: {
          line: 319,
          column: 28
        },
        end: {
          line: 319,
          column: 57
        }
      },
      "140": {
        start: {
          line: 321,
          column: 28
        },
        end: {
          line: 321,
          column: 56
        }
      },
      "141": {
        start: {
          line: 322,
          column: 28
        },
        end: {
          line: 327,
          column: 36
        }
      },
      "142": {
        start: {
          line: 329,
          column: 28
        },
        end: {
          line: 329,
          column: 53
        }
      },
      "143": {
        start: {
          line: 330,
          column: 28
        },
        end: {
          line: 335,
          column: 29
        }
      },
      "144": {
        start: {
          line: 331,
          column: 32
        },
        end: {
          line: 334,
          column: 57
        }
      },
      "145": {
        start: {
          line: 336,
          column: 28
        },
        end: {
          line: 336,
          column: 97
        }
      },
      "146": {
        start: {
          line: 338,
          column: 28
        },
        end: {
          line: 338,
          column: 48
        }
      },
      "147": {
        start: {
          line: 339,
          column: 28
        },
        end: {
          line: 339,
          column: 80
        }
      },
      "148": {
        start: {
          line: 340,
          column: 28
        },
        end: {
          line: 345,
          column: 29
        }
      },
      "149": {
        start: {
          line: 341,
          column: 32
        },
        end: {
          line: 344,
          column: 57
        }
      },
      "150": {
        start: {
          line: 347,
          column: 28
        },
        end: {
          line: 353,
          column: 36
        }
      },
      "151": {
        start: {
          line: 356,
          column: 28
        },
        end: {
          line: 356,
          column: 38
        }
      },
      "152": {
        start: {
          line: 357,
          column: 28
        },
        end: {
          line: 360,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 48,
            column: 72
          },
          end: {
            line: 48,
            column: 73
          }
        },
        loc: {
          start: {
            line: 48,
            column: 97
          },
          end: {
            line: 201,
            column: 5
          }
        },
        line: 48
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 48,
            column: 149
          },
          end: {
            line: 48,
            column: 150
          }
        },
        loc: {
          start: {
            line: 48,
            column: 172
          },
          end: {
            line: 201,
            column: 1
          }
        },
        line: 48
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 50,
            column: 29
          },
          end: {
            line: 50,
            column: 30
          }
        },
        loc: {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 200,
            column: 5
          }
        },
        line: 50
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 51,
            column: 118
          },
          end: {
            line: 51,
            column: 119
          }
        },
        loc: {
          start: {
            line: 51,
            column: 130
          },
          end: {
            line: 199,
            column: 17
          }
        },
        line: 51
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 51,
            column: 173
          },
          end: {
            line: 51,
            column: 174
          }
        },
        loc: {
          start: {
            line: 51,
            column: 185
          },
          end: {
            line: 199,
            column: 13
          }
        },
        line: 51
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 54,
            column: 41
          },
          end: {
            line: 54,
            column: 42
          }
        },
        loc: {
          start: {
            line: 54,
            column: 55
          },
          end: {
            line: 198,
            column: 17
          }
        },
        line: 54
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 189,
            column: 41
          },
          end: {
            line: 189,
            column: 42
          }
        },
        loc: {
          start: {
            line: 189,
            column: 58
          },
          end: {
            line: 191,
            column: 33
          }
        },
        line: 189
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 203,
            column: 72
          },
          end: {
            line: 203,
            column: 73
          }
        },
        loc: {
          start: {
            line: 203,
            column: 97
          },
          end: {
            line: 300,
            column: 5
          }
        },
        line: 203
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 203,
            column: 149
          },
          end: {
            line: 203,
            column: 150
          }
        },
        loc: {
          start: {
            line: 203,
            column: 172
          },
          end: {
            line: 300,
            column: 1
          }
        },
        line: 203
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 205,
            column: 29
          },
          end: {
            line: 205,
            column: 30
          }
        },
        loc: {
          start: {
            line: 205,
            column: 43
          },
          end: {
            line: 299,
            column: 5
          }
        },
        line: 205
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 206,
            column: 117
          },
          end: {
            line: 206,
            column: 118
          }
        },
        loc: {
          start: {
            line: 206,
            column: 129
          },
          end: {
            line: 298,
            column: 17
          }
        },
        line: 206
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 206,
            column: 172
          },
          end: {
            line: 206,
            column: 173
          }
        },
        loc: {
          start: {
            line: 206,
            column: 184
          },
          end: {
            line: 298,
            column: 13
          }
        },
        line: 206
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 209,
            column: 41
          },
          end: {
            line: 209,
            column: 42
          }
        },
        loc: {
          start: {
            line: 209,
            column: 55
          },
          end: {
            line: 297,
            column: 17
          }
        },
        line: 209
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 302,
            column: 75
          },
          end: {
            line: 302,
            column: 76
          }
        },
        loc: {
          start: {
            line: 302,
            column: 100
          },
          end: {
            line: 365,
            column: 5
          }
        },
        line: 302
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 302,
            column: 152
          },
          end: {
            line: 302,
            column: 153
          }
        },
        loc: {
          start: {
            line: 302,
            column: 175
          },
          end: {
            line: 365,
            column: 1
          }
        },
        line: 302
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 304,
            column: 29
          },
          end: {
            line: 304,
            column: 30
          }
        },
        loc: {
          start: {
            line: 304,
            column: 43
          },
          end: {
            line: 364,
            column: 5
          }
        },
        line: 304
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 305,
            column: 116
          },
          end: {
            line: 305,
            column: 117
          }
        },
        loc: {
          start: {
            line: 305,
            column: 128
          },
          end: {
            line: 363,
            column: 17
          }
        },
        line: 305
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 305,
            column: 171
          },
          end: {
            line: 305,
            column: 172
          }
        },
        loc: {
          start: {
            line: 305,
            column: 183
          },
          end: {
            line: 363,
            column: 13
          }
        },
        line: 305
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 308,
            column: 41
          },
          end: {
            line: 308,
            column: 42
          }
        },
        loc: {
          start: {
            line: 308,
            column: 55
          },
          end: {
            line: 362,
            column: 17
          }
        },
        line: 308
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 55,
            column: 20
          },
          end: {
            line: 197,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 56,
            column: 24
          },
          end: {
            line: 56,
            column: 61
          }
        }, {
          start: {
            line: 57,
            column: 24
          },
          end: {
            line: 59,
            column: 104
          }
        }, {
          start: {
            line: 60,
            column: 24
          },
          end: {
            line: 175,
            column: 36
          }
        }, {
          start: {
            line: 176,
            column: 24
          },
          end: {
            line: 196,
            column: 36
          }
        }],
        line: 55
      },
      "33": {
        loc: {
          start: {
            line: 62,
            column: 37
          },
          end: {
            line: 62,
            column: 149
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 62,
            column: 135
          },
          end: {
            line: 62,
            column: 141
          }
        }, {
          start: {
            line: 62,
            column: 144
          },
          end: {
            line: 62,
            column: 149
          }
        }],
        line: 62
      },
      "34": {
        loc: {
          start: {
            line: 62,
            column: 37
          },
          end: {
            line: 62,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 37
          },
          end: {
            line: 62,
            column: 115
          }
        }, {
          start: {
            line: 62,
            column: 119
          },
          end: {
            line: 62,
            column: 132
          }
        }],
        line: 62
      },
      "35": {
        loc: {
          start: {
            line: 62,
            column: 43
          },
          end: {
            line: 62,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 62,
            column: 84
          },
          end: {
            line: 62,
            column: 90
          }
        }, {
          start: {
            line: 62,
            column: 93
          },
          end: {
            line: 62,
            column: 105
          }
        }],
        line: 62
      },
      "36": {
        loc: {
          start: {
            line: 62,
            column: 43
          },
          end: {
            line: 62,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 43
          },
          end: {
            line: 62,
            column: 59
          }
        }, {
          start: {
            line: 62,
            column: 63
          },
          end: {
            line: 62,
            column: 81
          }
        }],
        line: 62
      },
      "37": {
        loc: {
          start: {
            line: 63,
            column: 28
          },
          end: {
            line: 68,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 28
          },
          end: {
            line: 68,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "38": {
        loc: {
          start: {
            line: 109,
            column: 51
          },
          end: {
            line: 121,
            column: 41
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 109,
            column: 60
          },
          end: {
            line: 116,
            column: 41
          }
        }, {
          start: {
            line: 116,
            column: 44
          },
          end: {
            line: 121,
            column: 41
          }
        }],
        line: 109
      },
      "39": {
        loc: {
          start: {
            line: 122,
            column: 51
          },
          end: {
            line: 129,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 122,
            column: 60
          },
          end: {
            line: 129,
            column: 41
          }
        }, {
          start: {
            line: 129,
            column: 44
          },
          end: {
            line: 129,
            column: 49
          }
        }],
        line: 122
      },
      "40": {
        loc: {
          start: {
            line: 155,
            column: 59
          },
          end: {
            line: 167,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 155,
            column: 68
          },
          end: {
            line: 162,
            column: 49
          }
        }, {
          start: {
            line: 162,
            column: 52
          },
          end: {
            line: 167,
            column: 49
          }
        }],
        line: 155
      },
      "41": {
        loc: {
          start: {
            line: 178,
            column: 28
          },
          end: {
            line: 183,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 28
          },
          end: {
            line: 183,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 178
      },
      "42": {
        loc: {
          start: {
            line: 185,
            column: 28
          },
          end: {
            line: 192,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 28
          },
          end: {
            line: 192,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "43": {
        loc: {
          start: {
            line: 185,
            column: 32
          },
          end: {
            line: 185,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 32
          },
          end: {
            line: 185,
            column: 38
          }
        }, {
          start: {
            line: 185,
            column: 42
          },
          end: {
            line: 185,
            column: 66
          }
        }],
        line: 185
      },
      "44": {
        loc: {
          start: {
            line: 210,
            column: 20
          },
          end: {
            line: 296,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 211,
            column: 24
          },
          end: {
            line: 211,
            column: 108
          }
        }, {
          start: {
            line: 212,
            column: 24
          },
          end: {
            line: 220,
            column: 57
          }
        }, {
          start: {
            line: 221,
            column: 24
          },
          end: {
            line: 223,
            column: 65
          }
        }, {
          start: {
            line: 224,
            column: 24
          },
          end: {
            line: 236,
            column: 36
          }
        }, {
          start: {
            line: 237,
            column: 24
          },
          end: {
            line: 288,
            column: 36
          }
        }, {
          start: {
            line: 289,
            column: 24
          },
          end: {
            line: 295,
            column: 36
          }
        }],
        line: 210
      },
      "45": {
        loc: {
          start: {
            line: 214,
            column: 28
          },
          end: {
            line: 219,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 28
          },
          end: {
            line: 219,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "46": {
        loc: {
          start: {
            line: 214,
            column: 34
          },
          end: {
            line: 214,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 132
          },
          end: {
            line: 214,
            column: 138
          }
        }, {
          start: {
            line: 214,
            column: 141
          },
          end: {
            line: 214,
            column: 146
          }
        }],
        line: 214
      },
      "47": {
        loc: {
          start: {
            line: 214,
            column: 34
          },
          end: {
            line: 214,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 214,
            column: 34
          },
          end: {
            line: 214,
            column: 112
          }
        }, {
          start: {
            line: 214,
            column: 116
          },
          end: {
            line: 214,
            column: 129
          }
        }],
        line: 214
      },
      "48": {
        loc: {
          start: {
            line: 214,
            column: 40
          },
          end: {
            line: 214,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 81
          },
          end: {
            line: 214,
            column: 87
          }
        }, {
          start: {
            line: 214,
            column: 90
          },
          end: {
            line: 214,
            column: 102
          }
        }],
        line: 214
      },
      "49": {
        loc: {
          start: {
            line: 214,
            column: 40
          },
          end: {
            line: 214,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 214,
            column: 40
          },
          end: {
            line: 214,
            column: 56
          }
        }, {
          start: {
            line: 214,
            column: 60
          },
          end: {
            line: 214,
            column: 78
          }
        }],
        line: 214
      },
      "50": {
        loc: {
          start: {
            line: 227,
            column: 28
          },
          end: {
            line: 232,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 28
          },
          end: {
            line: 232,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 227
      },
      "51": {
        loc: {
          start: {
            line: 227,
            column: 32
          },
          end: {
            line: 227,
            column: 164
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 32
          },
          end: {
            line: 227,
            column: 93
          }
        }, {
          start: {
            line: 227,
            column: 97
          },
          end: {
            line: 227,
            column: 164
          }
        }],
        line: 227
      },
      "52": {
        loc: {
          start: {
            line: 227,
            column: 34
          },
          end: {
            line: 227,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 227,
            column: 71
          },
          end: {
            line: 227,
            column: 77
          }
        }, {
          start: {
            line: 227,
            column: 80
          },
          end: {
            line: 227,
            column: 92
          }
        }],
        line: 227
      },
      "53": {
        loc: {
          start: {
            line: 227,
            column: 34
          },
          end: {
            line: 227,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 34
          },
          end: {
            line: 227,
            column: 48
          }
        }, {
          start: {
            line: 227,
            column: 52
          },
          end: {
            line: 227,
            column: 68
          }
        }],
        line: 227
      },
      "54": {
        loc: {
          start: {
            line: 227,
            column: 99
          },
          end: {
            line: 227,
            column: 163
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 227,
            column: 140
          },
          end: {
            line: 227,
            column: 146
          }
        }, {
          start: {
            line: 227,
            column: 149
          },
          end: {
            line: 227,
            column: 163
          }
        }],
        line: 227
      },
      "55": {
        loc: {
          start: {
            line: 227,
            column: 99
          },
          end: {
            line: 227,
            column: 137
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 99
          },
          end: {
            line: 227,
            column: 115
          }
        }, {
          start: {
            line: 227,
            column: 119
          },
          end: {
            line: 227,
            column: 137
          }
        }],
        line: 227
      },
      "56": {
        loc: {
          start: {
            line: 239,
            column: 28
          },
          end: {
            line: 244,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 239,
            column: 28
          },
          end: {
            line: 244,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 239
      },
      "57": {
        loc: {
          start: {
            line: 245,
            column: 28
          },
          end: {
            line: 250,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 28
          },
          end: {
            line: 250,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "58": {
        loc: {
          start: {
            line: 256,
            column: 52
          },
          end: {
            line: 256,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 256,
            column: 52
          },
          end: {
            line: 256,
            column: 62
          }
        }, {
          start: {
            line: 256,
            column: 66
          },
          end: {
            line: 256,
            column: 70
          }
        }],
        line: 256
      },
      "59": {
        loc: {
          start: {
            line: 309,
            column: 20
          },
          end: {
            line: 361,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 310,
            column: 24
          },
          end: {
            line: 310,
            column: 108
          }
        }, {
          start: {
            line: 311,
            column: 24
          },
          end: {
            line: 319,
            column: 57
          }
        }, {
          start: {
            line: 320,
            column: 24
          },
          end: {
            line: 327,
            column: 36
          }
        }, {
          start: {
            line: 328,
            column: 24
          },
          end: {
            line: 336,
            column: 97
          }
        }, {
          start: {
            line: 337,
            column: 24
          },
          end: {
            line: 353,
            column: 36
          }
        }, {
          start: {
            line: 354,
            column: 24
          },
          end: {
            line: 360,
            column: 36
          }
        }],
        line: 309
      },
      "60": {
        loc: {
          start: {
            line: 313,
            column: 28
          },
          end: {
            line: 318,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 313,
            column: 28
          },
          end: {
            line: 318,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 313
      },
      "61": {
        loc: {
          start: {
            line: 313,
            column: 34
          },
          end: {
            line: 313,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 313,
            column: 132
          },
          end: {
            line: 313,
            column: 138
          }
        }, {
          start: {
            line: 313,
            column: 141
          },
          end: {
            line: 313,
            column: 146
          }
        }],
        line: 313
      },
      "62": {
        loc: {
          start: {
            line: 313,
            column: 34
          },
          end: {
            line: 313,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 34
          },
          end: {
            line: 313,
            column: 112
          }
        }, {
          start: {
            line: 313,
            column: 116
          },
          end: {
            line: 313,
            column: 129
          }
        }],
        line: 313
      },
      "63": {
        loc: {
          start: {
            line: 313,
            column: 40
          },
          end: {
            line: 313,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 313,
            column: 81
          },
          end: {
            line: 313,
            column: 87
          }
        }, {
          start: {
            line: 313,
            column: 90
          },
          end: {
            line: 313,
            column: 102
          }
        }],
        line: 313
      },
      "64": {
        loc: {
          start: {
            line: 313,
            column: 40
          },
          end: {
            line: 313,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 40
          },
          end: {
            line: 313,
            column: 56
          }
        }, {
          start: {
            line: 313,
            column: 60
          },
          end: {
            line: 313,
            column: 78
          }
        }],
        line: 313
      },
      "65": {
        loc: {
          start: {
            line: 330,
            column: 28
          },
          end: {
            line: 335,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 330,
            column: 28
          },
          end: {
            line: 335,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 330
      },
      "66": {
        loc: {
          start: {
            line: 340,
            column: 28
          },
          end: {
            line: 345,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 340,
            column: 28
          },
          end: {
            line: 345,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 340
      },
      "67": {
        loc: {
          start: {
            line: 340,
            column: 32
          },
          end: {
            line: 340,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 340,
            column: 32
          },
          end: {
            line: 340,
            column: 40
          }
        }, {
          start: {
            line: 340,
            column: 44
          },
          end: {
            line: 340,
            column: 52
          }
        }],
        line: 340
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0, 0, 0, 0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0, 0, 0, 0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,uCAAsC;AACtC,6EAA2E;AAC3E,6CAAgD;AAChD,+CAA+C;AAS/C,sEAAsE;AACzD,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAAmD;QAAjD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAC9C;;;;;gCACqB,qBAAM,MAAM,EAAA;;4BAAvB,MAAM,GAAK,CAAA,SAAY,CAAA,OAAjB;4BACE,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAC7C,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAC;4BAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;gCACZ,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,qBAAqB;qCAC7B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAGY,qBAAM,eAAM,CAAC,SAAS,CAAC,UAAU,CAAC;oCAC7C,KAAK,EAAE;wCACL,EAAE,EAAE,MAAM;wCACV,QAAQ,EAAE,KAAK,CAAC,0BAA0B;qCAC3C;oCACD,OAAO,EAAE;wCACP,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,KAAK,EAAE,IAAI;gDACX,IAAI,EAAE,IAAI;gDACV,KAAK,EAAE,IAAI;gDACX,OAAO,EAAE;oDACP,MAAM,EAAE;wDACN,iBAAiB,EAAE,IAAI;wDACvB,eAAe,EAAE,IAAI;wDACrB,cAAc,EAAE,IAAI;wDACpB,eAAe,EAAE,IAAI;wDACrB,iBAAiB,EAAE,IAAI;wDACvB,aAAa,EAAE,IAAI;qDACpB;iDACF;6CACF;yCACF;wCACD,QAAQ,EAAE;4CACR,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,IAAI,EAAE,IAAI;gDACV,IAAI,EAAE,IAAI;gDACV,KAAK,EAAE,IAAI;6CACZ;yCACF;wCACD,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,OAAO,EAAE,IAAI;gDACb,SAAS,EAAE,IAAI;gDACf,SAAS,EAAE,IAAI;6CAChB;yCACF;wCACD,gEAAgE;wCAChE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;4CAClB,KAAK,EAAE;gDACL,MAAM,EAAE,MAAM;6CACf;4CACD,MAAM,EAAE;gDACN,IAAI,EAAE,IAAI;6CACX;yCACF,CAAC,CAAC,CAAC;4CACF,MAAM,EAAE;gDACN,IAAI,EAAE,IAAI;gDACV,MAAM,EAAE,IAAI;6CACb;yCACF;wCACD,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;4CAClB,KAAK,EAAE;gDACL,MAAM,EAAE,MAAM;6CACf;4CACD,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;6CACT;yCACF,CAAC,CAAC,CAAC,KAAK;wCACT,8CAA8C;wCAC9C,OAAO,EAAE;4CACP,KAAK,EAAE;gDACL,QAAQ,EAAE,KAAK;6CAChB;4CACD,OAAO,EAAE;gDACP,MAAM,EAAE;oDACN,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,IAAI,EAAE,IAAI;wDACV,KAAK,EAAE,IAAI;wDACX,KAAK,EAAE,IAAI;wDACX,OAAO,EAAE;4DACP,MAAM,EAAE;gEACN,iBAAiB,EAAE,IAAI;gEACvB,eAAe,EAAE,IAAI;6DACtB;yDACF;qDACF;iDACF;gDACD,MAAM,EAAE;oDACN,MAAM,EAAE;wDACN,SAAS,EAAE,IAAI;qDAChB;iDACF;gDACD,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;oDAClB,KAAK,EAAE;wDACL,MAAM,EAAE,MAAM;qDACf;oDACD,MAAM,EAAE;wDACN,IAAI,EAAE,IAAI;qDACX;iDACF,CAAC,CAAC,CAAC;oDACF,MAAM,EAAE;wDACN,IAAI,EAAE,IAAI;wDACV,MAAM,EAAE,IAAI;qDACb;iDACF;6CACF;4CACD,OAAO,EAAE;gDACP,SAAS,EAAE,KAAK;6CACjB;4CACD,IAAI,EAAE,EAAE,EAAE,yCAAyC;yCACpD;qCACF;iCACF,CAAC,EAAA;;4BA1GI,IAAI,GAAG,SA0GX;4BAEF,IAAI,CAAC,IAAI,EAAE,CAAC;gCACV,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,gBAAgB;qCACxB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAED,0DAA0D;4BAC1D,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gCACvC,eAAM,CAAC,SAAS,CAAC,MAAM,CAAC;oCACtB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;oCACrB,IAAI,EAAE,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE;iCACtC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;oCACZ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gCAC1D,CAAC,CAAC,CAAC;4BACL,CAAC;4BAED,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,IAAI;iCACX,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,0BAA0B;AACb,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAAmD;QAAjD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAC7C;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,yBAAyB;qCACjC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAEkB,qBAAM,MAAM,EAAA;;4BAAvB,MAAM,GAAK,CAAA,SAAY,CAAA,OAAjB;4BACD,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;4BAA3B,IAAI,GAAG,SAAoB;4BACzB,KAAK,GAA0B,IAAI,MAA9B,EAAE,OAAO,GAAiB,IAAI,QAArB,EAAE,UAAU,GAAK,IAAI,WAAT,CAAU;4BAE5C,IAAI,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,EAAE,CAAA,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,EAAE,CAAA,EAAE,CAAC;gCACvC,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,gCAAgC;qCACxC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAGoB,qBAAM,eAAM,CAAC,SAAS,CAAC,UAAU,CAAC;oCACrD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;oCACrB,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;iCAC3B,CAAC,EAAA;;4BAHI,YAAY,GAAG,SAGnB;4BAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gCAClB,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,gBAAgB;qCACxB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAED,IAAI,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gCAC9C,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,kCAAkC;qCAC1C,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAGmB,qBAAM,eAAM,CAAC,SAAS,CAAC,MAAM,CAAC;oCAChD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;oCACrB,IAAI,EAAE;wCACJ,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;wCACnB,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;wCACvB,UAAU,EAAE,UAAU,IAAI,IAAI;wCAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;qCACtB;oCACD,OAAO,EAAE;wCACP,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,KAAK,EAAE,IAAI;gDACX,IAAI,EAAE,IAAI;gDACV,OAAO,EAAE;oDACP,MAAM,EAAE;wDACN,iBAAiB,EAAE,IAAI;wDACvB,eAAe,EAAE,IAAI;qDACtB;iDACF;6CACF;yCACF;wCACD,QAAQ,EAAE;4CACR,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,IAAI,EAAE,IAAI;gDACV,IAAI,EAAE,IAAI;6CACX;yCACF;wCACD,MAAM,EAAE;4CACN,MAAM,EAAE;gDACN,OAAO,EAAE,IAAI;gDACb,SAAS,EAAE,IAAI;gDACf,SAAS,EAAE,IAAI;6CAChB;yCACF;qCACF;iCACF,CAAC,EAAA;;4BArCI,WAAW,GAAG,SAqClB;4BAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,WAAW;oCACjB,OAAO,EAAE,2BAA2B;iCACrC,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,6BAA6B;AAChB,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,uFAC7C,OAAoB,EACpB,EAAmD;QAAjD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,EAC5C;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,yBAAyB;qCACjC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAEkB,qBAAM,MAAM,EAAA;;4BAAvB,MAAM,GAAK,CAAA,SAAY,CAAA,OAAjB;4BAGO,qBAAM,eAAM,CAAC,SAAS,CAAC,UAAU,CAAC;oCACrD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;oCACrB,MAAM,EAAE;wCACN,QAAQ,EAAE,IAAI;qCACf;iCACF,CAAC,EAAA;;4BALI,YAAY,GAAG,SAKnB;4BAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gCAClB,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,gBAAgB;qCACxB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAEe,qBAAM,IAAA,wBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;4BAA5C,OAAO,GAAG,SAAkC;4BAC5C,OAAO,GAAG,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BAE1D,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;gCACzB,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,oCAAoC;qCAC5C,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAED,iCAAiC;4BACjC,qBAAM,eAAM,CAAC,SAAS,CAAC,MAAM,CAAC;oCAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;oCACrB,IAAI,EAAE;wCACJ,QAAQ,EAAE,IAAI;wCACd,SAAS,EAAE,IAAI,IAAI,EAAE;qCACtB;iCACF,CAAC,EAAA;;4BAPF,iCAAiC;4BACjC,SAME,CAAC;4BAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,OAAO,EAAE,2BAA2B;iCACrC,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/posts/[postId]/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { isUserAdmin } from '@/lib/auth-utils';\n\ninterface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\n// GET - Retrieve specific forum post with optimized query (fixes N+1)\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ postId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 100 },\n    async () => {\n      const { postId } = await params;\n      const session = await getServerSession(authOptions);\n      const userId = session?.user?.id;\n\n      if (!postId) {\n        return NextResponse.json({\n          success: false,\n          error: 'Post ID is required'\n        }, { status: 400 });\n      }\n\n      // Optimized single query with all necessary includes (prevents N+1)\n      const post = await prisma.forumPost.findUnique({\n        where: { \n          id: postId,\n          isHidden: false // Only show visible posts\n        },\n        include: {\n          author: {\n            select: {\n              id: true,\n              email: true,\n              name: true,\n              image: true,\n              profile: {\n                select: {\n                  profilePictureUrl: true,\n                  forumReputation: true,\n                  forumPostCount: true,\n                  forumReplyCount: true,\n                  currentCareerPath: true,\n                  progressLevel: true,\n                },\n              },\n            },\n          },\n          category: {\n            select: {\n              id: true,\n              name: true,\n              slug: true,\n              color: true,\n            },\n          },\n          _count: {\n            select: {\n              replies: true,\n              reactions: true,\n              bookmarks: true,\n            },\n          },\n          // Load user's specific reactions and bookmarks if authenticated\n          reactions: userId ? {\n            where: {\n              userId: userId,\n            },\n            select: {\n              type: true,\n            },\n          } : {\n            select: {\n              type: true,\n              userId: true,\n            },\n          },\n          bookmarks: userId ? {\n            where: {\n              userId: userId,\n            },\n            select: {\n              id: true,\n            },\n          } : false,\n          // Load recent replies with optimized includes\n          replies: {\n            where: {\n              isHidden: false,\n            },\n            include: {\n              author: {\n                select: {\n                  id: true,\n                  name: true,\n                  email: true,\n                  image: true,\n                  profile: {\n                    select: {\n                      profilePictureUrl: true,\n                      forumReputation: true,\n                    },\n                  },\n                },\n              },\n              _count: {\n                select: {\n                  reactions: true,\n                },\n              },\n              reactions: userId ? {\n                where: {\n                  userId: userId,\n                },\n                select: {\n                  type: true,\n                },\n              } : {\n                select: {\n                  type: true,\n                  userId: true,\n                },\n              },\n            },\n            orderBy: {\n              createdAt: 'asc',\n            },\n            take: 50, // Limit replies to prevent huge payloads\n          },\n        },\n      });\n\n      if (!post) {\n        return NextResponse.json({\n          success: false,\n          error: 'Post not found'\n        }, { status: 404 });\n      }\n\n      // Increment view count asynchronously (don't wait for it)\n      if (userId && userId !== post.authorId) {\n        prisma.forumPost.update({\n          where: { id: postId },\n          data: { viewCount: { increment: 1 } }\n        }).catch(error => {\n          console.error('Failed to increment view count:', error);\n        });\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: post\n      });\n    }\n  );\n});\n\n// PUT - Update forum post\nexport const PUT = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ postId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 10 },\n    async () => {\n      const session = await getServerSession(authOptions);\n      \n      if (!session?.user?.id) {\n        return NextResponse.json({\n          success: false,\n          error: 'Authentication required'\n        }, { status: 401 });\n      }\n\n      const { postId } = await params;\n      const body = await request.json();\n      const { title, content, categoryId } = body;\n\n      if (!title?.trim() || !content?.trim()) {\n        return NextResponse.json({\n          success: false,\n          error: 'Title and content are required'\n        }, { status: 400 });\n      }\n\n      // Check if user owns the post\n      const existingPost = await prisma.forumPost.findUnique({\n        where: { id: postId },\n        select: { authorId: true }\n      });\n\n      if (!existingPost) {\n        return NextResponse.json({\n          success: false,\n          error: 'Post not found'\n        }, { status: 404 });\n      }\n\n      if (existingPost.authorId !== session.user.id) {\n        return NextResponse.json({\n          success: false,\n          error: 'You can only edit your own posts'\n        }, { status: 403 });\n      }\n\n      // Update the post\n      const updatedPost = await prisma.forumPost.update({\n        where: { id: postId },\n        data: {\n          title: title.trim(),\n          content: content.trim(),\n          categoryId: categoryId || null,\n          updatedAt: new Date(),\n        },\n        include: {\n          author: {\n            select: {\n              id: true,\n              email: true,\n              name: true,\n              profile: {\n                select: {\n                  profilePictureUrl: true,\n                  forumReputation: true,\n                },\n              },\n            },\n          },\n          category: {\n            select: {\n              id: true,\n              name: true,\n              slug: true,\n            },\n          },\n          _count: {\n            select: {\n              replies: true,\n              reactions: true,\n              bookmarks: true,\n            },\n          },\n        },\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: updatedPost,\n        message: 'Post updated successfully'\n      });\n    }\n  );\n});\n\n// DELETE - Delete forum post\nexport const DELETE = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ postId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 5 },\n    async () => {\n      const session = await getServerSession(authOptions);\n      \n      if (!session?.user?.id) {\n        return NextResponse.json({\n          success: false,\n          error: 'Authentication required'\n        }, { status: 401 });\n      }\n\n      const { postId } = await params;\n\n      // Check if user owns the post or is admin\n      const existingPost = await prisma.forumPost.findUnique({\n        where: { id: postId },\n        select: {\n          authorId: true\n        }\n      });\n\n      if (!existingPost) {\n        return NextResponse.json({\n          success: false,\n          error: 'Post not found'\n        }, { status: 404 });\n      }\n\n      const isAdmin = await isUserAdmin(session.user.id);\n      const isOwner = existingPost.authorId === session.user.id;\n\n      if (!isOwner && !isAdmin) {\n        return NextResponse.json({\n          success: false,\n          error: 'You can only delete your own posts'\n        }, { status: 403 });\n      }\n\n      // Soft delete by hiding the post\n      await prisma.forumPost.update({\n        where: { id: postId },\n        data: { \n          isHidden: true,\n          updatedAt: new Date(),\n        },\n      });\n\n      return NextResponse.json({\n        success: true,\n        message: 'Post deleted successfully'\n      });\n    }\n  );\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "49bd72f882b9527672e9d453e702e90fce66adeb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1fogj4o552 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1fogj4o552();
var __awaiter =
/* istanbul ignore next */
(cov_1fogj4o552().s[0]++,
/* istanbul ignore next */
(cov_1fogj4o552().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1fogj4o552().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1fogj4o552().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1fogj4o552().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1fogj4o552().f[1]++;
    cov_1fogj4o552().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1fogj4o552().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1fogj4o552().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[2]++;
      cov_1fogj4o552().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1fogj4o552().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1fogj4o552().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1fogj4o552().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1fogj4o552().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[4]++;
      cov_1fogj4o552().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1fogj4o552().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1fogj4o552().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[5]++;
      cov_1fogj4o552().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1fogj4o552().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1fogj4o552().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[6]++;
      cov_1fogj4o552().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1fogj4o552().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1fogj4o552().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1fogj4o552().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1fogj4o552().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1fogj4o552().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1fogj4o552().s[12]++,
/* istanbul ignore next */
(cov_1fogj4o552().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1fogj4o552().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1fogj4o552().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1fogj4o552().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1fogj4o552().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1fogj4o552().f[8]++;
        cov_1fogj4o552().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1fogj4o552().b[6][0]++;
          cov_1fogj4o552().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1fogj4o552().b[6][1]++;
        }
        cov_1fogj4o552().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1fogj4o552().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1fogj4o552().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1fogj4o552().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1fogj4o552().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1fogj4o552().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1fogj4o552().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1fogj4o552().f[9]++;
    cov_1fogj4o552().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1fogj4o552().f[10]++;
    cov_1fogj4o552().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[11]++;
      cov_1fogj4o552().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1fogj4o552().f[12]++;
    cov_1fogj4o552().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1fogj4o552().b[9][0]++;
      cov_1fogj4o552().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1fogj4o552().b[9][1]++;
    }
    cov_1fogj4o552().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1fogj4o552().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1fogj4o552().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1fogj4o552().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1fogj4o552().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1fogj4o552().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1fogj4o552().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1fogj4o552().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1fogj4o552().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1fogj4o552().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1fogj4o552().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1fogj4o552().b[15][0]++,
        /* istanbul ignore next */
        (cov_1fogj4o552().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1fogj4o552().b[16][1]++,
        /* istanbul ignore next */
        (cov_1fogj4o552().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1fogj4o552().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1fogj4o552().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1fogj4o552().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1fogj4o552().b[12][0]++;
          cov_1fogj4o552().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1fogj4o552().b[12][1]++;
        }
        cov_1fogj4o552().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1fogj4o552().b[18][0]++;
          cov_1fogj4o552().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1fogj4o552().b[18][1]++;
        }
        cov_1fogj4o552().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1fogj4o552().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1fogj4o552().b[19][1]++;
            cov_1fogj4o552().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1fogj4o552().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1fogj4o552().b[19][2]++;
            cov_1fogj4o552().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1fogj4o552().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1fogj4o552().b[19][3]++;
            cov_1fogj4o552().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1fogj4o552().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1fogj4o552().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1fogj4o552().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1fogj4o552().b[19][4]++;
            cov_1fogj4o552().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1fogj4o552().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1fogj4o552().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1fogj4o552().b[19][5]++;
            cov_1fogj4o552().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1fogj4o552().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1fogj4o552().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1fogj4o552().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1fogj4o552().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1fogj4o552().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1fogj4o552().b[20][0]++;
              cov_1fogj4o552().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1fogj4o552().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1fogj4o552().b[20][1]++;
            }
            cov_1fogj4o552().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1fogj4o552().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1fogj4o552().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1fogj4o552().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1fogj4o552().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1fogj4o552().b[23][0]++;
              cov_1fogj4o552().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1fogj4o552().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1fogj4o552().b[23][1]++;
            }
            cov_1fogj4o552().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1fogj4o552().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1fogj4o552().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1fogj4o552().b[25][0]++;
              cov_1fogj4o552().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1fogj4o552().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1fogj4o552().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1fogj4o552().b[25][1]++;
            }
            cov_1fogj4o552().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1fogj4o552().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1fogj4o552().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1fogj4o552().b[27][0]++;
              cov_1fogj4o552().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1fogj4o552().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1fogj4o552().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1fogj4o552().b[27][1]++;
            }
            cov_1fogj4o552().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1fogj4o552().b[29][0]++;
              cov_1fogj4o552().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1fogj4o552().b[29][1]++;
            }
            cov_1fogj4o552().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1fogj4o552().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1fogj4o552().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1fogj4o552().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1fogj4o552().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1fogj4o552().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1fogj4o552().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1fogj4o552().b[30][0]++;
      cov_1fogj4o552().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1fogj4o552().b[30][1]++;
    }
    cov_1fogj4o552().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1fogj4o552().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1fogj4o552().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1fogj4o552().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1fogj4o552().s[68]++;
exports.DELETE = exports.PUT = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1fogj4o552().s[69]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_1fogj4o552().s[70]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_1fogj4o552().s[71]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_1fogj4o552().s[72]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1fogj4o552().s[73]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_1fogj4o552().s[74]++, require("@/lib/rateLimit"));
var auth_utils_1 =
/* istanbul ignore next */
(cov_1fogj4o552().s[75]++, require("@/lib/auth-utils"));
// GET - Retrieve specific forum post with optimized query (fixes N+1)
/* istanbul ignore next */
cov_1fogj4o552().s[76]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1fogj4o552().f[13]++;
  cov_1fogj4o552().s[77]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_1fogj4o552().f[14]++;
    var params =
    /* istanbul ignore next */
    (cov_1fogj4o552().s[78]++, _b.params);
    /* istanbul ignore next */
    cov_1fogj4o552().s[79]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[15]++;
      cov_1fogj4o552().s[80]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 100
      }, function () {
        /* istanbul ignore next */
        cov_1fogj4o552().f[16]++;
        cov_1fogj4o552().s[81]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1fogj4o552().f[17]++;
          var postId, session, userId, post;
          var _a;
          /* istanbul ignore next */
          cov_1fogj4o552().s[82]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_1fogj4o552().f[18]++;
            cov_1fogj4o552().s[83]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_1fogj4o552().b[32][0]++;
                cov_1fogj4o552().s[84]++;
                return [4 /*yield*/, params];
              case 1:
                /* istanbul ignore next */
                cov_1fogj4o552().b[32][1]++;
                cov_1fogj4o552().s[85]++;
                postId = _b.sent().postId;
                /* istanbul ignore next */
                cov_1fogj4o552().s[86]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 2:
                /* istanbul ignore next */
                cov_1fogj4o552().b[32][2]++;
                cov_1fogj4o552().s[87]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[88]++;
                userId =
                /* istanbul ignore next */
                (cov_1fogj4o552().b[34][0]++, (_a =
                /* istanbul ignore next */
                (cov_1fogj4o552().b[36][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[36][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[35][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[35][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[34][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[33][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[33][1]++, _a.id);
                /* istanbul ignore next */
                cov_1fogj4o552().s[89]++;
                if (!postId) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[37][0]++;
                  cov_1fogj4o552().s[90]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Post ID is required'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[37][1]++;
                }
                cov_1fogj4o552().s[91]++;
                return [4 /*yield*/, prisma_1.prisma.forumPost.findUnique({
                  where: {
                    id: postId,
                    isHidden: false // Only show visible posts
                  },
                  include: {
                    author: {
                      select: {
                        id: true,
                        email: true,
                        name: true,
                        image: true,
                        profile: {
                          select: {
                            profilePictureUrl: true,
                            forumReputation: true,
                            forumPostCount: true,
                            forumReplyCount: true,
                            currentCareerPath: true,
                            progressLevel: true
                          }
                        }
                      }
                    },
                    category: {
                      select: {
                        id: true,
                        name: true,
                        slug: true,
                        color: true
                      }
                    },
                    _count: {
                      select: {
                        replies: true,
                        reactions: true,
                        bookmarks: true
                      }
                    },
                    // Load user's specific reactions and bookmarks if authenticated
                    reactions: userId ?
                    /* istanbul ignore next */
                    (cov_1fogj4o552().b[38][0]++, {
                      where: {
                        userId: userId
                      },
                      select: {
                        type: true
                      }
                    }) :
                    /* istanbul ignore next */
                    (cov_1fogj4o552().b[38][1]++, {
                      select: {
                        type: true,
                        userId: true
                      }
                    }),
                    bookmarks: userId ?
                    /* istanbul ignore next */
                    (cov_1fogj4o552().b[39][0]++, {
                      where: {
                        userId: userId
                      },
                      select: {
                        id: true
                      }
                    }) :
                    /* istanbul ignore next */
                    (cov_1fogj4o552().b[39][1]++, false),
                    // Load recent replies with optimized includes
                    replies: {
                      where: {
                        isHidden: false
                      },
                      include: {
                        author: {
                          select: {
                            id: true,
                            name: true,
                            email: true,
                            image: true,
                            profile: {
                              select: {
                                profilePictureUrl: true,
                                forumReputation: true
                              }
                            }
                          }
                        },
                        _count: {
                          select: {
                            reactions: true
                          }
                        },
                        reactions: userId ?
                        /* istanbul ignore next */
                        (cov_1fogj4o552().b[40][0]++, {
                          where: {
                            userId: userId
                          },
                          select: {
                            type: true
                          }
                        }) :
                        /* istanbul ignore next */
                        (cov_1fogj4o552().b[40][1]++, {
                          select: {
                            type: true,
                            userId: true
                          }
                        })
                      },
                      orderBy: {
                        createdAt: 'asc'
                      },
                      take: 50 // Limit replies to prevent huge payloads
                    }
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_1fogj4o552().b[32][3]++;
                cov_1fogj4o552().s[92]++;
                post = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[93]++;
                if (!post) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[41][0]++;
                  cov_1fogj4o552().s[94]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Post not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[41][1]++;
                }
                // Increment view count asynchronously (don't wait for it)
                cov_1fogj4o552().s[95]++;
                if (
                /* istanbul ignore next */
                (cov_1fogj4o552().b[43][0]++, userId) &&
                /* istanbul ignore next */
                (cov_1fogj4o552().b[43][1]++, userId !== post.authorId)) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[42][0]++;
                  cov_1fogj4o552().s[96]++;
                  prisma_1.prisma.forumPost.update({
                    where: {
                      id: postId
                    },
                    data: {
                      viewCount: {
                        increment: 1
                      }
                    }
                  }).catch(function (error) {
                    /* istanbul ignore next */
                    cov_1fogj4o552().f[19]++;
                    cov_1fogj4o552().s[97]++;
                    console.error('Failed to increment view count:', error);
                  });
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[42][1]++;
                }
                cov_1fogj4o552().s[98]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: post
                })];
            }
          });
        });
      })];
    });
  });
});
// PUT - Update forum post
/* istanbul ignore next */
cov_1fogj4o552().s[99]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1fogj4o552().f[20]++;
  cov_1fogj4o552().s[100]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_1fogj4o552().f[21]++;
    var params =
    /* istanbul ignore next */
    (cov_1fogj4o552().s[101]++, _b.params);
    /* istanbul ignore next */
    cov_1fogj4o552().s[102]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[22]++;
      cov_1fogj4o552().s[103]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 10
      }, function () {
        /* istanbul ignore next */
        cov_1fogj4o552().f[23]++;
        cov_1fogj4o552().s[104]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1fogj4o552().f[24]++;
          var session, postId, body, title, content, categoryId, existingPost, updatedPost;
          var _a;
          /* istanbul ignore next */
          cov_1fogj4o552().s[105]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_1fogj4o552().f[25]++;
            cov_1fogj4o552().s[106]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_1fogj4o552().b[44][0]++;
                cov_1fogj4o552().s[107]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1fogj4o552().b[44][1]++;
                cov_1fogj4o552().s[108]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[109]++;
                if (!(
                /* istanbul ignore next */
                (cov_1fogj4o552().b[47][0]++, (_a =
                /* istanbul ignore next */
                (cov_1fogj4o552().b[49][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[49][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[48][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[48][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[47][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[46][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[46][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[45][0]++;
                  cov_1fogj4o552().s[110]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[45][1]++;
                }
                cov_1fogj4o552().s[111]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_1fogj4o552().b[44][2]++;
                cov_1fogj4o552().s[112]++;
                postId = _b.sent().postId;
                /* istanbul ignore next */
                cov_1fogj4o552().s[113]++;
                return [4 /*yield*/, request.json()];
              case 3:
                /* istanbul ignore next */
                cov_1fogj4o552().b[44][3]++;
                cov_1fogj4o552().s[114]++;
                body = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[115]++;
                title = body.title, content = body.content, categoryId = body.categoryId;
                /* istanbul ignore next */
                cov_1fogj4o552().s[116]++;
                if (
                /* istanbul ignore next */
                (cov_1fogj4o552().b[51][0]++, !(
                /* istanbul ignore next */
                (cov_1fogj4o552().b[53][0]++, title === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[53][1]++, title === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[52][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[52][1]++, title.trim()))) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[51][1]++, !(
                /* istanbul ignore next */
                (cov_1fogj4o552().b[55][0]++, content === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[55][1]++, content === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[54][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[54][1]++, content.trim())))) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[50][0]++;
                  cov_1fogj4o552().s[117]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Title and content are required'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[50][1]++;
                }
                cov_1fogj4o552().s[118]++;
                return [4 /*yield*/, prisma_1.prisma.forumPost.findUnique({
                  where: {
                    id: postId
                  },
                  select: {
                    authorId: true
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_1fogj4o552().b[44][4]++;
                cov_1fogj4o552().s[119]++;
                existingPost = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[120]++;
                if (!existingPost) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[56][0]++;
                  cov_1fogj4o552().s[121]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Post not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[56][1]++;
                }
                cov_1fogj4o552().s[122]++;
                if (existingPost.authorId !== session.user.id) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[57][0]++;
                  cov_1fogj4o552().s[123]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'You can only edit your own posts'
                  }, {
                    status: 403
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[57][1]++;
                }
                cov_1fogj4o552().s[124]++;
                return [4 /*yield*/, prisma_1.prisma.forumPost.update({
                  where: {
                    id: postId
                  },
                  data: {
                    title: title.trim(),
                    content: content.trim(),
                    categoryId:
                    /* istanbul ignore next */
                    (cov_1fogj4o552().b[58][0]++, categoryId) ||
                    /* istanbul ignore next */
                    (cov_1fogj4o552().b[58][1]++, null),
                    updatedAt: new Date()
                  },
                  include: {
                    author: {
                      select: {
                        id: true,
                        email: true,
                        name: true,
                        profile: {
                          select: {
                            profilePictureUrl: true,
                            forumReputation: true
                          }
                        }
                      }
                    },
                    category: {
                      select: {
                        id: true,
                        name: true,
                        slug: true
                      }
                    },
                    _count: {
                      select: {
                        replies: true,
                        reactions: true,
                        bookmarks: true
                      }
                    }
                  }
                })];
              case 5:
                /* istanbul ignore next */
                cov_1fogj4o552().b[44][5]++;
                cov_1fogj4o552().s[125]++;
                updatedPost = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[126]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: updatedPost,
                  message: 'Post updated successfully'
                })];
            }
          });
        });
      })];
    });
  });
});
// DELETE - Delete forum post
/* istanbul ignore next */
cov_1fogj4o552().s[127]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1fogj4o552().f[26]++;
  cov_1fogj4o552().s[128]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_1fogj4o552().f[27]++;
    var params =
    /* istanbul ignore next */
    (cov_1fogj4o552().s[129]++, _b.params);
    /* istanbul ignore next */
    cov_1fogj4o552().s[130]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1fogj4o552().f[28]++;
      cov_1fogj4o552().s[131]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 5
      }, function () {
        /* istanbul ignore next */
        cov_1fogj4o552().f[29]++;
        cov_1fogj4o552().s[132]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1fogj4o552().f[30]++;
          var session, postId, existingPost, isAdmin, isOwner;
          var _a;
          /* istanbul ignore next */
          cov_1fogj4o552().s[133]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_1fogj4o552().f[31]++;
            cov_1fogj4o552().s[134]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_1fogj4o552().b[59][0]++;
                cov_1fogj4o552().s[135]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1fogj4o552().b[59][1]++;
                cov_1fogj4o552().s[136]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[137]++;
                if (!(
                /* istanbul ignore next */
                (cov_1fogj4o552().b[62][0]++, (_a =
                /* istanbul ignore next */
                (cov_1fogj4o552().b[64][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[64][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[63][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[63][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1fogj4o552().b[62][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1fogj4o552().b[61][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1fogj4o552().b[61][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[60][0]++;
                  cov_1fogj4o552().s[138]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[60][1]++;
                }
                cov_1fogj4o552().s[139]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_1fogj4o552().b[59][2]++;
                cov_1fogj4o552().s[140]++;
                postId = _b.sent().postId;
                /* istanbul ignore next */
                cov_1fogj4o552().s[141]++;
                return [4 /*yield*/, prisma_1.prisma.forumPost.findUnique({
                  where: {
                    id: postId
                  },
                  select: {
                    authorId: true
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_1fogj4o552().b[59][3]++;
                cov_1fogj4o552().s[142]++;
                existingPost = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[143]++;
                if (!existingPost) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[65][0]++;
                  cov_1fogj4o552().s[144]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Post not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[65][1]++;
                }
                cov_1fogj4o552().s[145]++;
                return [4 /*yield*/, (0, auth_utils_1.isUserAdmin)(session.user.id)];
              case 4:
                /* istanbul ignore next */
                cov_1fogj4o552().b[59][4]++;
                cov_1fogj4o552().s[146]++;
                isAdmin = _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[147]++;
                isOwner = existingPost.authorId === session.user.id;
                /* istanbul ignore next */
                cov_1fogj4o552().s[148]++;
                if (
                /* istanbul ignore next */
                (cov_1fogj4o552().b[67][0]++, !isOwner) &&
                /* istanbul ignore next */
                (cov_1fogj4o552().b[67][1]++, !isAdmin)) {
                  /* istanbul ignore next */
                  cov_1fogj4o552().b[66][0]++;
                  cov_1fogj4o552().s[149]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'You can only delete your own posts'
                  }, {
                    status: 403
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1fogj4o552().b[66][1]++;
                }
                // Soft delete by hiding the post
                cov_1fogj4o552().s[150]++;
                return [4 /*yield*/, prisma_1.prisma.forumPost.update({
                  where: {
                    id: postId
                  },
                  data: {
                    isHidden: true,
                    updatedAt: new Date()
                  }
                })];
              case 5:
                /* istanbul ignore next */
                cov_1fogj4o552().b[59][5]++;
                cov_1fogj4o552().s[151]++;
                // Soft delete by hiding the post
                _b.sent();
                /* istanbul ignore next */
                cov_1fogj4o552().s[152]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  message: 'Post deleted successfully'
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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