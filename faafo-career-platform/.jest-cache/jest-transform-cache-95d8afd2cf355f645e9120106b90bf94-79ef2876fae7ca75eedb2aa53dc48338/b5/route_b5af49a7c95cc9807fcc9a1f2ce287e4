56479a05fc480fa6d3118364780392d0
"use strict";

/* istanbul ignore next */
function cov_1ylss5jcej() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/[id]/progress/route.ts";
  var hash = "c25a26fa4a5b0c372b005a55f070dc45b52cf305";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/[id]/progress/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 20
        },
        end: {
          line: 57,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 55,
          column: 5
        }
      },
      "80": {
        start: {
          line: 50,
          column: 40
        },
        end: {
          line: 55,
          column: 5
        }
      },
      "81": {
        start: {
          line: 50,
          column: 53
        },
        end: {
          line: 50,
          column: 54
        }
      },
      "82": {
        start: {
          line: 50,
          column: 60
        },
        end: {
          line: 50,
          column: 71
        }
      },
      "83": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 54,
          column: 9
        }
      },
      "84": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 65
        }
      },
      "85": {
        start: {
          line: 52,
          column: 21
        },
        end: {
          line: 52,
          column: 65
        }
      },
      "86": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 28
        }
      },
      "87": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 61
        }
      },
      "88": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 62
        }
      },
      "89": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 59,
          column: 21
        }
      },
      "90": {
        start: {
          line: 60,
          column: 15
        },
        end: {
          line: 60,
          column: 37
        }
      },
      "91": {
        start: {
          line: 61,
          column: 18
        },
        end: {
          line: 61,
          column: 38
        }
      },
      "92": {
        start: {
          line: 62,
          column: 13
        },
        end: {
          line: 62,
          column: 34
        }
      },
      "93": {
        start: {
          line: 63,
          column: 34
        },
        end: {
          line: 63,
          column: 76
        }
      },
      "94": {
        start: {
          line: 64,
          column: 15
        },
        end: {
          line: 64,
          column: 38
        }
      },
      "95": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 26
        }
      },
      "96": {
        start: {
          line: 66,
          column: 27
        },
        end: {
          line: 70,
          column: 2
        }
      },
      "97": {
        start: {
          line: 71,
          column: 0
        },
        end: {
          line: 232,
          column: 7
        }
      },
      "98": {
        start: {
          line: 71,
          column: 99
        },
        end: {
          line: 232,
          column: 3
        }
      },
      "99": {
        start: {
          line: 74,
          column: 17
        },
        end: {
          line: 74,
          column: 26
        }
      },
      "100": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 231,
          column: 7
        }
      },
      "101": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 230,
          column: 9
        }
      },
      "102": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 77,
          column: 96
        }
      },
      "103": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 79,
          column: 36
        }
      },
      "104": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 84,
          column: 17
        }
      },
      "105": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 81,
          column: 65
        }
      },
      "106": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 82,
          column: 43
        }
      },
      "107": {
        start: {
          line: 83,
          column: 20
        },
        end: {
          line: 83,
          column: 32
        }
      },
      "108": {
        start: {
          line: 85,
          column: 16
        },
        end: {
          line: 85,
          column: 41
        }
      },
      "109": {
        start: {
          line: 86,
          column: 16
        },
        end: {
          line: 86,
          column: 45
        }
      },
      "110": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 44
        }
      },
      "111": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 53
        }
      },
      "112": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 33
        }
      },
      "113": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 66
        }
      },
      "114": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 98,
          column: 17
        }
      },
      "115": {
        start: {
          line: 94,
          column: 20
        },
        end: {
          line: 94,
          column: 70
        }
      },
      "116": {
        start: {
          line: 95,
          column: 20
        },
        end: {
          line: 95,
          column: 43
        }
      },
      "117": {
        start: {
          line: 96,
          column: 20
        },
        end: {
          line: 96,
          column: 60
        }
      },
      "118": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 97,
          column: 32
        }
      },
      "119": {
        start: {
          line: 99,
          column: 16
        },
        end: {
          line: 99,
          column: 123
        }
      },
      "120": {
        start: {
          line: 100,
          column: 16
        },
        end: {
          line: 105,
          column: 24
        }
      },
      "121": {
        start: {
          line: 107,
          column: 16
        },
        end: {
          line: 107,
          column: 37
        }
      },
      "122": {
        start: {
          line: 108,
          column: 16
        },
        end: {
          line: 112,
          column: 17
        }
      },
      "123": {
        start: {
          line: 109,
          column: 20
        },
        end: {
          line: 109,
          column: 64
        }
      },
      "124": {
        start: {
          line: 110,
          column: 20
        },
        end: {
          line: 110,
          column: 43
        }
      },
      "125": {
        start: {
          line: 111,
          column: 20
        },
        end: {
          line: 111,
          column: 32
        }
      },
      "126": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 117,
          column: 17
        }
      },
      "127": {
        start: {
          line: 114,
          column: 20
        },
        end: {
          line: 114,
          column: 73
        }
      },
      "128": {
        start: {
          line: 115,
          column: 20
        },
        end: {
          line: 115,
          column: 43
        }
      },
      "129": {
        start: {
          line: 116,
          column: 20
        },
        end: {
          line: 116,
          column: 32
        }
      },
      "130": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 123,
          column: 18
        }
      },
      "131": {
        start: {
          line: 124,
          column: 16
        },
        end: {
          line: 124,
          column: 162
        }
      },
      "132": {
        start: {
          line: 125,
          column: 16
        },
        end: {
          line: 127,
          column: 28
        }
      },
      "133": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 128,
          column: 99
        }
      },
      "134": {
        start: {
          line: 129,
          column: 16
        },
        end: {
          line: 131,
          column: 24
        }
      },
      "135": {
        start: {
          line: 132,
          column: 16
        },
        end: {
          line: 136,
          column: 63
        }
      },
      "136": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 137,
          column: 63
        }
      },
      "137": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 140,
          column: 19
        }
      },
      "138": {
        start: {
          line: 139,
          column: 20
        },
        end: {
          line: 139,
          column: 77
        }
      },
      "139": {
        start: {
          line: 141,
          column: 16
        },
        end: {
          line: 149,
          column: 24
        }
      },
      "140": {
        start: {
          line: 151,
          column: 16
        },
        end: {
          line: 151,
          column: 44
        }
      },
      "141": {
        start: {
          line: 152,
          column: 16
        },
        end: {
          line: 152,
          column: 34
        }
      },
      "142": {
        start: {
          line: 153,
          column: 16
        },
        end: {
          line: 153,
          column: 60
        }
      },
      "143": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 29
        }
      },
      "144": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 156,
          column: 79
        }
      },
      "145": {
        start: {
          line: 156,
          column: 54
        },
        end: {
          line: 156,
          column: 79
        }
      },
      "146": {
        start: {
          line: 157,
          column: 16
        },
        end: {
          line: 157,
          column: 50
        }
      },
      "147": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 158,
          column: 29
        }
      },
      "148": {
        start: {
          line: 160,
          column: 16
        },
        end: {
          line: 160,
          column: 44
        }
      },
      "149": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 163,
          column: 24
        }
      },
      "150": {
        start: {
          line: 165,
          column: 16
        },
        end: {
          line: 165,
          column: 34
        }
      },
      "151": {
        start: {
          line: 166,
          column: 16
        },
        end: {
          line: 166,
          column: 53
        }
      },
      "152": {
        start: {
          line: 166,
          column: 28
        },
        end: {
          line: 166,
          column: 53
        }
      },
      "153": {
        start: {
          line: 168,
          column: 16
        },
        end: {
          line: 186,
          column: 24
        }
      },
      "154": {
        start: {
          line: 189,
          column: 16
        },
        end: {
          line: 189,
          column: 26
        }
      },
      "155": {
        start: {
          line: 190,
          column: 16
        },
        end: {
          line: 194,
          column: 19
        }
      },
      "156": {
        start: {
          line: 195,
          column: 16
        },
        end: {
          line: 195,
          column: 30
        }
      },
      "157": {
        start: {
          line: 196,
          column: 21
        },
        end: {
          line: 196,
          column: 46
        }
      },
      "158": {
        start: {
          line: 198,
          column: 16
        },
        end: {
          line: 198,
          column: 36
        }
      },
      "159": {
        start: {
          line: 199,
          column: 16
        },
        end: {
          line: 199,
          column: 100
        }
      },
      "160": {
        start: {
          line: 200,
          column: 16
        },
        end: {
          line: 200,
          column: 41
        }
      },
      "161": {
        start: {
          line: 202,
          column: 16
        },
        end: {
          line: 202,
          column: 21
        }
      },
      "162": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 203,
          column: 40
        }
      },
      "163": {
        start: {
          line: 206,
          column: 16
        },
        end: {
          line: 210,
          column: 19
        }
      },
      "164": {
        start: {
          line: 211,
          column: 16
        },
        end: {
          line: 225,
          column: 18
        }
      },
      "165": {
        start: {
          line: 226,
          column: 16
        },
        end: {
          line: 229,
          column: 24
        }
      },
      "166": {
        start: {
          line: 234,
          column: 18
        },
        end: {
          line: 234,
          column: 37
        }
      },
      "167": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 235,
          column: 58
        }
      },
      "168": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 236,
          column: 33
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 52
          },
          end: {
            line: 49,
            column: 53
          }
        },
        loc: {
          start: {
            line: 49,
            column: 78
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 71,
            column: 72
          },
          end: {
            line: 71,
            column: 73
          }
        },
        loc: {
          start: {
            line: 71,
            column: 97
          },
          end: {
            line: 232,
            column: 5
          }
        },
        line: 71
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 71,
            column: 150
          },
          end: {
            line: 71,
            column: 151
          }
        },
        loc: {
          start: {
            line: 71,
            column: 173
          },
          end: {
            line: 232,
            column: 1
          }
        },
        line: 71
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 75,
            column: 29
          },
          end: {
            line: 75,
            column: 30
          }
        },
        loc: {
          start: {
            line: 75,
            column: 43
          },
          end: {
            line: 231,
            column: 5
          }
        },
        line: 75
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 138,
            column: 48
          },
          end: {
            line: 138,
            column: 49
          }
        },
        loc: {
          start: {
            line: 138,
            column: 61
          },
          end: {
            line: 140,
            column: 17
          }
        },
        line: 138
      },
      "20": {
        name: "calculateMilestoneDueDate",
        decl: {
          start: {
            line: 233,
            column: 9
          },
          end: {
            line: 233,
            column: 34
          }
        },
        loc: {
          start: {
            line: 233,
            column: 62
          },
          end: {
            line: 237,
            column: 1
          }
        },
        line: 233
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 20
          },
          end: {
            line: 57,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 25
          }
        }, {
          start: {
            line: 49,
            column: 29
          },
          end: {
            line: 49,
            column: 47
          }
        }, {
          start: {
            line: 49,
            column: 52
          },
          end: {
            line: 57,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 55,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 55,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 12
          }
        }, {
          start: {
            line: 50,
            column: 16
          },
          end: {
            line: 50,
            column: 38
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 54,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 54,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "39": {
        loc: {
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 51,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 51,
            column: 14
          }
        }, {
          start: {
            line: 51,
            column: 18
          },
          end: {
            line: 51,
            column: 30
          }
        }],
        line: 51
      },
      "40": {
        loc: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "41": {
        loc: {
          start: {
            line: 56,
            column: 21
          },
          end: {
            line: 56,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 21
          },
          end: {
            line: 56,
            column: 23
          }
        }, {
          start: {
            line: 56,
            column: 27
          },
          end: {
            line: 56,
            column: 59
          }
        }],
        line: 56
      },
      "42": {
        loc: {
          start: {
            line: 76,
            column: 8
          },
          end: {
            line: 230,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 77,
            column: 12
          },
          end: {
            line: 77,
            column: 96
          }
        }, {
          start: {
            line: 78,
            column: 12
          },
          end: {
            line: 86,
            column: 45
          }
        }, {
          start: {
            line: 87,
            column: 12
          },
          end: {
            line: 89,
            column: 53
          }
        }, {
          start: {
            line: 90,
            column: 12
          },
          end: {
            line: 105,
            column: 24
          }
        }, {
          start: {
            line: 106,
            column: 12
          },
          end: {
            line: 149,
            column: 24
          }
        }, {
          start: {
            line: 150,
            column: 12
          },
          end: {
            line: 154,
            column: 29
          }
        }, {
          start: {
            line: 155,
            column: 12
          },
          end: {
            line: 158,
            column: 29
          }
        }, {
          start: {
            line: 159,
            column: 12
          },
          end: {
            line: 163,
            column: 24
          }
        }, {
          start: {
            line: 164,
            column: 12
          },
          end: {
            line: 186,
            column: 24
          }
        }, {
          start: {
            line: 187,
            column: 12
          },
          end: {
            line: 195,
            column: 30
          }
        }, {
          start: {
            line: 196,
            column: 12
          },
          end: {
            line: 196,
            column: 46
          }
        }, {
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 200,
            column: 41
          }
        }, {
          start: {
            line: 201,
            column: 12
          },
          end: {
            line: 203,
            column: 40
          }
        }, {
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 229,
            column: 24
          }
        }],
        line: 76
      },
      "43": {
        loc: {
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 84,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 84,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "44": {
        loc: {
          start: {
            line: 80,
            column: 22
          },
          end: {
            line: 80,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 120
          },
          end: {
            line: 80,
            column: 126
          }
        }, {
          start: {
            line: 80,
            column: 129
          },
          end: {
            line: 80,
            column: 134
          }
        }],
        line: 80
      },
      "45": {
        loc: {
          start: {
            line: 80,
            column: 22
          },
          end: {
            line: 80,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 22
          },
          end: {
            line: 80,
            column: 100
          }
        }, {
          start: {
            line: 80,
            column: 104
          },
          end: {
            line: 80,
            column: 117
          }
        }],
        line: 80
      },
      "46": {
        loc: {
          start: {
            line: 80,
            column: 28
          },
          end: {
            line: 80,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 69
          },
          end: {
            line: 80,
            column: 75
          }
        }, {
          start: {
            line: 80,
            column: 78
          },
          end: {
            line: 80,
            column: 90
          }
        }],
        line: 80
      },
      "47": {
        loc: {
          start: {
            line: 80,
            column: 28
          },
          end: {
            line: 80,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 28
          },
          end: {
            line: 80,
            column: 44
          }
        }, {
          start: {
            line: 80,
            column: 48
          },
          end: {
            line: 80,
            column: 66
          }
        }],
        line: 80
      },
      "48": {
        loc: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 98,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 98,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "49": {
        loc: {
          start: {
            line: 108,
            column: 16
          },
          end: {
            line: 112,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 16
          },
          end: {
            line: 112,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "50": {
        loc: {
          start: {
            line: 113,
            column: 16
          },
          end: {
            line: 117,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 16
          },
          end: {
            line: 117,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "51": {
        loc: {
          start: {
            line: 118,
            column: 35
          },
          end: {
            line: 123,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 118,
            column: 35
          },
          end: {
            line: 118,
            column: 60
          }
        }, {
          start: {
            line: 118,
            column: 64
          },
          end: {
            line: 123,
            column: 17
          }
        }],
        line: 118
      },
      "52": {
        loc: {
          start: {
            line: 124,
            column: 89
          },
          end: {
            line: 124,
            column: 127
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 124,
            column: 89
          },
          end: {
            line: 124,
            column: 121
          }
        }, {
          start: {
            line: 124,
            column: 125
          },
          end: {
            line: 124,
            column: 127
          }
        }],
        line: 124
      },
      "53": {
        loc: {
          start: {
            line: 125,
            column: 90
          },
          end: {
            line: 125,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 125,
            column: 90
          },
          end: {
            line: 125,
            column: 126
          }
        }, {
          start: {
            line: 125,
            column: 130
          },
          end: {
            line: 125,
            column: 132
          }
        }],
        line: 125
      },
      "54": {
        loc: {
          start: {
            line: 128,
            column: 33
          },
          end: {
            line: 128,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 128,
            column: 69
          },
          end: {
            line: 128,
            column: 94
          }
        }, {
          start: {
            line: 128,
            column: 97
          },
          end: {
            line: 128,
            column: 98
          }
        }],
        line: 128
      },
      "55": {
        loc: {
          start: {
            line: 129,
            column: 39
          },
          end: {
            line: 131,
            column: 23
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 130,
            column: 22
          },
          end: {
            line: 130,
            column: 84
          }
        }, {
          start: {
            line: 131,
            column: 22
          },
          end: {
            line: 131,
            column: 23
          }
        }],
        line: 129
      },
      "56": {
        loc: {
          start: {
            line: 132,
            column: 210
          },
          end: {
            line: 136,
            column: 59
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 132,
            column: 218
          },
          end: {
            line: 136,
            column: 34
          }
        }, {
          start: {
            line: 136,
            column: 37
          },
          end: {
            line: 136,
            column: 59
          }
        }],
        line: 132
      },
      "57": {
        loc: {
          start: {
            line: 132,
            column: 251
          },
          end: {
            line: 132,
            column: 279
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 132,
            column: 251
          },
          end: {
            line: 132,
            column: 273
          }
        }, {
          start: {
            line: 132,
            column: 277
          },
          end: {
            line: 132,
            column: 279
          }
        }],
        line: 132
      },
      "58": {
        loc: {
          start: {
            line: 137,
            column: 29
          },
          end: {
            line: 137,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 29
          },
          end: {
            line: 137,
            column: 56
          }
        }, {
          start: {
            line: 137,
            column: 60
          },
          end: {
            line: 137,
            column: 62
          }
        }],
        line: 137
      },
      "59": {
        loc: {
          start: {
            line: 147,
            column: 36
          },
          end: {
            line: 147,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 147,
            column: 66
          },
          end: {
            line: 147,
            column: 77
          }
        }, {
          start: {
            line: 147,
            column: 80
          },
          end: {
            line: 147,
            column: 88
          }
        }],
        line: 147
      },
      "60": {
        loc: {
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 156,
            column: 79
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 156,
            column: 79
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "61": {
        loc: {
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 166,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 166,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "62": {
        loc: {
          start: {
            line: 214,
            column: 39
          },
          end: {
            line: 222,
            column: 25
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 55
          },
          end: {
            line: 218,
            column: 25
          }
        }, {
          start: {
            line: 218,
            column: 28
          },
          end: {
            line: 222,
            column: 25
          }
        }],
        line: 214
      },
      "63": {
        loc: {
          start: {
            line: 215,
            column: 36
          },
          end: {
            line: 215,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 36
          },
          end: {
            line: 215,
            column: 56
          }
        }, {
          start: {
            line: 215,
            column: 60
          },
          end: {
            line: 215,
            column: 62
          }
        }],
        line: 215
      },
      "64": {
        loc: {
          start: {
            line: 216,
            column: 44
          },
          end: {
            line: 216,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 44
          },
          end: {
            line: 216,
            column: 72
          }
        }, {
          start: {
            line: 216,
            column: 76
          },
          end: {
            line: 216,
            column: 77
          }
        }],
        line: 216
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/[id]/progress/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,6EAAwF;AAGxF,uCAAsC;AACtC,2BAAwB;AAExB,IAAM,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,sCAAsC,CAAC;IACnF,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IAC1D,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC,QAAQ,EAAE;CACzD,CAAC,CAAC;AA2BU,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,qEAGzC,OAAO,YAFR,OAAoB,EACpB,EAA+C;;;QAA7C,MAAM,YAAA;;;oBAEQ,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACJ,qBAAM,MAAM,EAAA;;gBAA3B,UAAU,GAAK,CAAA,SAAY,CAAA,GAAjB;gBAET,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAA3B,IAAI,GAAG,SAAoB;gBAC3B,UAAU,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAExD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBAClB,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,CAAQ,CAAC;oBAC/D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC;oBACxC,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEK,KAA0C,UAAU,CAAC,IAAI,EAAvD,eAAe,qBAAA,EAAE,WAAW,iBAAA,EAAE,KAAK,WAAA,CAAqB;gBAE/C,qBAAM,eAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;wBACvD,KAAK,EAAE;4BACL,EAAE,EAAE,UAAU;4BACd,MAAM,QAAA;yBACP;qBACF,CAAC,EAAA;;gBALI,QAAQ,GAAG,SAKf;gBAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACR,KAAK,GAAG,IAAI,KAAK,CAAC,wBAAwB,CAAQ,CAAC;oBACzD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBAC3B,KAAK,GAAG,IAAI,KAAK,CAAC,iCAAiC,CAAQ,CAAC;oBAClE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGK,gBAAgB,GAAG,QAAQ,CAAC,gBAAuB,IAAI;oBAC3D,UAAU,EAAE,EAAE;oBACd,mBAAmB,EAAE,EAAE;oBACvB,eAAe,EAAE,EAAE;oBACnB,YAAY,EAAE,UAAU;iBACzB,CAAC;gBAGI,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,iCACxC,CAAC,gBAAgB,CAAC,eAAe,IAAI,EAAE,CAAC,SACxC,eAAe,QAClB,CAAC,CAAC;gBAGE,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,iCACzC,CAAC,gBAAgB,CAAC,mBAAmB,IAAI,EAAE,CAAC;oBAC/C,WAAW;0BACX,CAAC,CAAC;gBAGE,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,oBAAoB,GAAG,cAAc,GAAG,CAAC;oBAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC;oBAChE,CAAC,CAAC,CAAC,CAAC;gBAGA,uBAAuB,yBACxB,gBAAgB,KACnB,eAAe,EAAE,kBAAkB,EACnC,mBAAmB,qBAAA,EACnB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EACrC,KAAK,EAAE,KAAK,CAAC,CAAC,iCAAK,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE,CAAC,UAAE;4BACjD,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;4BAC9B,SAAS,EAAE,WAAW;4BACtB,IAAI,EAAE,KAAK;yBACZ,UAAE,CAAC,CAAC,gBAAgB,CAAC,KAAK,GAC5B,CAAC;gBAGI,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,EAAE,CAAC;gBAC/C,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,UAAC,CAAM;oBAC3C,OAAA,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAAjD,CAAiD,CAClD,CAAC;gBAGsB,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;wBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;wBACzB,IAAI,EAAE;4BACJ,gBAAgB,EAAE,uBAAuB;4BACzC,oBAAoB,sBAAA;4BACpB,WAAW,EAAE,IAAI,IAAI,EAAE;4BACvB,MAAM,EAAE,oBAAoB,IAAI,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ;yBAC7D;qBACF,CAAC,EAAA;;gBARI,eAAe,GAAG,SAQtB;gBAGI,YAAY,GAAG,EAAE,CAAC;sBACe,EAAf,mCAAe;;;qBAAf,CAAA,6BAAe,CAAA;gBAA5B,SAAS;;;;gBAGF,qBAAM,eAAM,CAAC,KAAK,CAAC,SAAS,CAAC;wBACzC,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;qBAC9D,CAAC,EAAA;;gBAFI,KAAK,GAAG,SAEZ;qBAEE,KAAK,EAAL,yBAAK;gBACP,6BAA6B;gBAC7B,qBAAM,eAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;wBACpC,KAAK,EAAE;4BACL,cAAc,EAAE;gCACd,MAAM,QAAA;gCACN,OAAO,EAAE,KAAK,CAAC,EAAE;6BAClB;yBACF;wBACD,MAAM,EAAE;4BACN,cAAc,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,2CAA2C;4BAC9E,aAAa,EAAE,IAAI,IAAI,EAAE;yBAC1B;wBACD,MAAM,EAAE;4BACN,MAAM,QAAA;4BACN,OAAO,EAAE,KAAK,CAAC,EAAE;4BACjB,YAAY,EAAE,cAAc;4BAC5B,cAAc,EAAE,EAAE;4BAClB,aAAa,EAAE,IAAI,IAAI,EAAE;yBAC1B;qBACF,CAAC,EAAA;;gBAnBF,6BAA6B;gBAC7B,SAkBE,CAAC;gBAEH,YAAY,CAAC,IAAI,CAAC;oBAChB,IAAI,EAAE,gBAAgB;oBACtB,KAAK,EAAE,UAAG,SAAS,cAAW;oBAC9B,MAAM,EAAE,EAAE;iBACX,CAAC,CAAC;;;;;gBAGL,OAAO,CAAC,KAAK,CAAC,4CAAqC,SAAS,MAAG,EAAE,OAAK,CAAC,CAAC;;;gBApCpD,IAAe,CAAA;;;gBAwCvC,uCAAuC;gBACvC,YAAY,CAAC,IAAI,CAAC;oBAChB,IAAI,EAAE,qBAAqB;oBAC3B,KAAK,EAAE,oBAAa,WAAW,eAAY;oBAC3C,MAAM,EAAE,EAAE;iBACX,CAAC,CAAC;gBAGG,YAAY,GAAG;oBACnB,eAAe,EAAE;wBACf,oBAAoB,sBAAA;wBACpB,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;4BAC7B,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,EAAE;4BAClC,cAAc,EAAE,aAAa,CAAC,cAAc,IAAI,CAAC;4BACjD,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC;yBAC5E,CAAC,CAAC,CAAC;4BACF,MAAM,EAAE,EAAE;4BACV,cAAc,EAAE,CAAC;4BACjB,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBAClC;qBACF;oBACD,YAAY,cAAA;iBACb,CAAC;gBAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAa;wBACtB,IAAI,EAAE,YAAY;qBACnB,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAEH,SAAS,yBAAyB,CAAC,SAAe,EAAE,cAAsB;IACxE,IAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,cAAc,CAAC,CAAC;IACtD,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC;AAC/B,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/gap-analysis/[id]/progress/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { prisma } from '@/lib/prisma';\nimport { z } from 'zod';\n\nconst updateProgressSchema = z.object({\n  completedSkills: z.array(z.string()).min(1, 'At least one skill must be completed'),\n  milestoneId: z.string().min(1, 'Milestone ID is required'),\n  notes: z.string().max(1000, 'Notes too long').optional(),\n});\n\ninterface UpdateGapAnalysisProgressRequest {\n  completedSkills: string[];\n  milestoneId: string;\n  notes?: string;\n}\n\ninterface UpdateGapAnalysisProgressResponse {\n  success: boolean;\n  data: {\n    updatedAnalysis: {\n      completionPercentage: number;\n      nextMilestone: {\n        skills: string[];\n        estimatedHours: number;\n        dueDate: string;\n      };\n    };\n    achievements: Array<{\n      type: string;\n      title: string;\n      points: number;\n    }>;\n  };\n}\n\nexport const PUT = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<UpdateGapAnalysisProgressResponse['data']>>> => {\n  const session = await getServerSession(authOptions);\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const userId = session.user.id;\n  const { id: analysisId } = await params;\n\n  const body = await request.json();\n  const validation = updateProgressSchema.safeParse(body);\n\n  if (!validation.success) {\n    const error = new Error('Invalid progress update data') as any;\n    error.statusCode = 400;\n    error.details = validation.error.errors;\n    throw error;\n  }\n\n  const { completedSkills, milestoneId, notes } = validation.data;\n  // Get the gap analysis\n  const analysis = await prisma.skillGapAnalysis.findFirst({\n    where: {\n      id: analysisId,\n      userId,\n    },\n  });\n\n  if (!analysis) {\n    const error = new Error('Gap analysis not found') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  if (analysis.status !== 'ACTIVE') {\n    const error = new Error('Cannot update inactive analysis') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Parse current progress tracking\n  const progressTracking = analysis.progressTracking as any || {\n    milestones: [],\n    completedMilestones: [],\n    completedSkills: [],\n    currentPhase: 'planning',\n  };\n\n  // Update completed skills\n  const newCompletedSkills = Array.from(new Set([\n    ...(progressTracking.completedSkills || []),\n    ...completedSkills,\n  ]));\n\n  // Mark milestone as completed\n  const completedMilestones = Array.from(new Set([\n    ...(progressTracking.completedMilestones || []),\n    milestoneId,\n  ]));\n\n  // Calculate completion percentage\n  const totalSkillGaps = Array.isArray(analysis.skillGaps) ? analysis.skillGaps.length : 0;\n  const completionPercentage = totalSkillGaps > 0\n    ? Math.round((newCompletedSkills.length / totalSkillGaps) * 100)\n    : 0;\n\n  // Update progress tracking\n  const updatedProgressTracking = {\n    ...progressTracking,\n    completedSkills: newCompletedSkills,\n    completedMilestones,\n    lastUpdated: new Date().toISOString(),\n    notes: notes ? [...(progressTracking.notes || []), {\n      date: new Date().toISOString(),\n      milestone: milestoneId,\n      note: notes,\n    }] : progressTracking.notes,\n  };\n\n  // Find next milestone\n  const milestones = progressTracking.milestones || [];\n  const nextMilestone = milestones.find((m: any) =>\n    !completedMilestones.includes(m.month.toString())\n  );\n\n  // Update the analysis\n  const updatedAnalysis = await prisma.skillGapAnalysis.update({\n    where: { id: analysisId },\n    data: {\n      progressTracking: updatedProgressTracking,\n      completionPercentage,\n      lastUpdated: new Date(),\n      status: completionPercentage >= 100 ? 'COMPLETED' : 'ACTIVE',\n    },\n  });\n\n  // Update user skill progress for completed skills\n  const achievements = [];\n  for (const skillName of completedSkills) {\n    try {\n      // Find skill by name\n      const skill = await prisma.skill.findFirst({\n        where: { name: { contains: skillName, mode: 'insensitive' } },\n      });\n\n      if (skill) {\n        // Update user skill progress\n        await prisma.userSkillProgress.upsert({\n          where: {\n            userId_skillId: {\n              userId,\n              skillId: skill.id,\n            },\n          },\n          update: {\n            progressPoints: { increment: 25 }, // Bonus points for gap analysis completion\n            lastPracticed: new Date(),\n          },\n          create: {\n            userId,\n            skillId: skill.id,\n            currentLevel: 'INTERMEDIATE',\n            progressPoints: 25,\n            lastPracticed: new Date(),\n          },\n        });\n\n        achievements.push({\n          type: 'SKILL_PROGRESS',\n          title: `${skillName} Progress`,\n          points: 25,\n        });\n      }\n    } catch (error) {\n      console.error(`Error updating progress for skill ${skillName}:`, error);\n    }\n  }\n\n  // Add milestone completion achievement\n  achievements.push({\n    type: 'MILESTONE_COMPLETED',\n    title: `Milestone ${milestoneId} Completed`,\n    points: 50,\n  });\n\n  // Prepare response data\n  const responseData = {\n    updatedAnalysis: {\n      completionPercentage,\n      nextMilestone: nextMilestone ? {\n        skills: nextMilestone.skills || [],\n        estimatedHours: nextMilestone.estimatedHours || 0,\n        dueDate: calculateMilestoneDueDate(analysis.createdAt, nextMilestone.month),\n      } : {\n        skills: [],\n        estimatedHours: 0,\n        dueDate: new Date().toISOString(),\n      },\n    },\n    achievements,\n  };\n\n  return NextResponse.json({\n    success: true as const,\n    data: responseData\n  });\n});\n\nfunction calculateMilestoneDueDate(startDate: Date, milestoneMonth: number): string {\n  const dueDate = new Date(startDate);\n  dueDate.setMonth(dueDate.getMonth() + milestoneMonth);\n  return dueDate.toISOString();\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c25a26fa4a5b0c372b005a55f070dc45b52cf305"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ylss5jcej = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ylss5jcej();
var __assign =
/* istanbul ignore next */
(cov_1ylss5jcej().s[0]++,
/* istanbul ignore next */
(cov_1ylss5jcej().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1ylss5jcej().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1ylss5jcej().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1ylss5jcej().f[0]++;
  cov_1ylss5jcej().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1ylss5jcej().f[1]++;
    cov_1ylss5jcej().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1ylss5jcej().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1ylss5jcej().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1ylss5jcej().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1ylss5jcej().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1ylss5jcej().b[2][0]++;
          cov_1ylss5jcej().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1ylss5jcej().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1ylss5jcej().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1ylss5jcej().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1ylss5jcej().s[11]++,
/* istanbul ignore next */
(cov_1ylss5jcej().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1ylss5jcej().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1ylss5jcej().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1ylss5jcej().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1ylss5jcej().f[3]++;
    cov_1ylss5jcej().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1ylss5jcej().f[4]++;
      cov_1ylss5jcej().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1ylss5jcej().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1ylss5jcej().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1ylss5jcej().f[6]++;
      cov_1ylss5jcej().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1ylss5jcej().f[7]++;
      cov_1ylss5jcej().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1ylss5jcej().f[8]++;
      cov_1ylss5jcej().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1ylss5jcej().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1ylss5jcej().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1ylss5jcej().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1ylss5jcej().s[23]++,
/* istanbul ignore next */
(cov_1ylss5jcej().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1ylss5jcej().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1ylss5jcej().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1ylss5jcej().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1ylss5jcej().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1ylss5jcej().f[10]++;
        cov_1ylss5jcej().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1ylss5jcej().b[9][0]++;
          cov_1ylss5jcej().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1ylss5jcej().b[9][1]++;
        }
        cov_1ylss5jcej().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1ylss5jcej().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1ylss5jcej().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1ylss5jcej().f[11]++;
    cov_1ylss5jcej().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1ylss5jcej().f[12]++;
    cov_1ylss5jcej().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1ylss5jcej().f[13]++;
      cov_1ylss5jcej().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1ylss5jcej().f[14]++;
    cov_1ylss5jcej().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1ylss5jcej().b[12][0]++;
      cov_1ylss5jcej().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1ylss5jcej().b[12][1]++;
    }
    cov_1ylss5jcej().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1ylss5jcej().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1ylss5jcej().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[18][0]++,
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[19][1]++,
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1ylss5jcej().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1ylss5jcej().b[15][0]++;
          cov_1ylss5jcej().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1ylss5jcej().b[15][1]++;
        }
        cov_1ylss5jcej().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1ylss5jcej().b[21][0]++;
          cov_1ylss5jcej().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1ylss5jcej().b[21][1]++;
        }
        cov_1ylss5jcej().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1ylss5jcej().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1ylss5jcej().b[22][1]++;
            cov_1ylss5jcej().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1ylss5jcej().b[22][2]++;
            cov_1ylss5jcej().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1ylss5jcej().b[22][3]++;
            cov_1ylss5jcej().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1ylss5jcej().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1ylss5jcej().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1ylss5jcej().b[22][4]++;
            cov_1ylss5jcej().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1ylss5jcej().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1ylss5jcej().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1ylss5jcej().b[22][5]++;
            cov_1ylss5jcej().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1ylss5jcej().b[23][0]++;
              cov_1ylss5jcej().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1ylss5jcej().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1ylss5jcej().b[23][1]++;
            }
            cov_1ylss5jcej().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1ylss5jcej().b[26][0]++;
              cov_1ylss5jcej().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1ylss5jcej().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ylss5jcej().b[26][1]++;
            }
            cov_1ylss5jcej().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1ylss5jcej().b[28][0]++;
              cov_1ylss5jcej().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1ylss5jcej().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1ylss5jcej().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ylss5jcej().b[28][1]++;
            }
            cov_1ylss5jcej().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1ylss5jcej().b[30][0]++;
              cov_1ylss5jcej().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1ylss5jcej().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1ylss5jcej().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ylss5jcej().b[30][1]++;
            }
            cov_1ylss5jcej().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1ylss5jcej().b[32][0]++;
              cov_1ylss5jcej().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1ylss5jcej().b[32][1]++;
            }
            cov_1ylss5jcej().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1ylss5jcej().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1ylss5jcej().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1ylss5jcej().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1ylss5jcej().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1ylss5jcej().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1ylss5jcej().b[33][0]++;
      cov_1ylss5jcej().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1ylss5jcej().b[33][1]++;
    }
    cov_1ylss5jcej().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1ylss5jcej().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1ylss5jcej().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_1ylss5jcej().s[78]++,
/* istanbul ignore next */
(cov_1ylss5jcej().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_1ylss5jcej().b[35][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_1ylss5jcej().b[35][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_1ylss5jcej().f[15]++;
  cov_1ylss5jcej().s[79]++;
  if (
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[37][0]++, pack) ||
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[37][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_1ylss5jcej().b[36][0]++;
    cov_1ylss5jcej().s[80]++;
    for (var i =
      /* istanbul ignore next */
      (cov_1ylss5jcej().s[81]++, 0), l =
      /* istanbul ignore next */
      (cov_1ylss5jcej().s[82]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_1ylss5jcej().s[83]++;
      if (
      /* istanbul ignore next */
      (cov_1ylss5jcej().b[39][0]++, ar) ||
      /* istanbul ignore next */
      (cov_1ylss5jcej().b[39][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_1ylss5jcej().b[38][0]++;
        cov_1ylss5jcej().s[84]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_1ylss5jcej().b[40][0]++;
          cov_1ylss5jcej().s[85]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_1ylss5jcej().b[40][1]++;
        }
        cov_1ylss5jcej().s[86]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_1ylss5jcej().b[38][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_1ylss5jcej().b[36][1]++;
  }
  cov_1ylss5jcej().s[87]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[41][0]++, ar) ||
  /* istanbul ignore next */
  (cov_1ylss5jcej().b[41][1]++, Array.prototype.slice.call(from)));
}));
/* istanbul ignore next */
cov_1ylss5jcej().s[88]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1ylss5jcej().s[89]++;
exports.PUT = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1ylss5jcej().s[90]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_1ylss5jcej().s[91]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_1ylss5jcej().s[92]++, require("@/lib/auth"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1ylss5jcej().s[93]++, require("@/lib/unified-api-error-handler"));
var prisma_1 =
/* istanbul ignore next */
(cov_1ylss5jcej().s[94]++, require("@/lib/prisma"));
var zod_1 =
/* istanbul ignore next */
(cov_1ylss5jcej().s[95]++, require("zod"));
var updateProgressSchema =
/* istanbul ignore next */
(cov_1ylss5jcej().s[96]++, zod_1.z.object({
  completedSkills: zod_1.z.array(zod_1.z.string()).min(1, 'At least one skill must be completed'),
  milestoneId: zod_1.z.string().min(1, 'Milestone ID is required'),
  notes: zod_1.z.string().max(1000, 'Notes too long').optional()
}));
/* istanbul ignore next */
cov_1ylss5jcej().s[97]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1ylss5jcej().f[16]++;
  cov_1ylss5jcej().s[98]++;
  return __awaiter(void 0, [request_1, _a], Promise, function (request, _b) {
    /* istanbul ignore next */
    cov_1ylss5jcej().f[17]++;
    var session, error, userId, analysisId, body, validation, error, _c, completedSkills, milestoneId, notes, analysis, error, error, progressTracking, newCompletedSkills, completedMilestones, totalSkillGaps, completionPercentage, updatedProgressTracking, milestones, nextMilestone, updatedAnalysis, achievements, _i, completedSkills_1, skillName, skill, error_1, responseData;
    var _d;
    var params =
    /* istanbul ignore next */
    (cov_1ylss5jcej().s[99]++, _b.params);
    /* istanbul ignore next */
    cov_1ylss5jcej().s[100]++;
    return __generator(this, function (_e) {
      /* istanbul ignore next */
      cov_1ylss5jcej().f[18]++;
      cov_1ylss5jcej().s[101]++;
      switch (_e.label) {
        case 0:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][0]++;
          cov_1ylss5jcej().s[102]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][1]++;
          cov_1ylss5jcej().s[103]++;
          session = _e.sent();
          /* istanbul ignore next */
          cov_1ylss5jcej().s[104]++;
          if (!(
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[45][0]++, (_d =
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[47][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[47][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[46][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[46][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[45][1]++, _d === void 0) ?
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[44][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[44][1]++, _d.id))) {
            /* istanbul ignore next */
            cov_1ylss5jcej().b[43][0]++;
            cov_1ylss5jcej().s[105]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_1ylss5jcej().s[106]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[107]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1ylss5jcej().b[43][1]++;
          }
          cov_1ylss5jcej().s[108]++;
          userId = session.user.id;
          /* istanbul ignore next */
          cov_1ylss5jcej().s[109]++;
          return [4 /*yield*/, params];
        case 2:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][2]++;
          cov_1ylss5jcej().s[110]++;
          analysisId = _e.sent().id;
          /* istanbul ignore next */
          cov_1ylss5jcej().s[111]++;
          return [4 /*yield*/, request.json()];
        case 3:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][3]++;
          cov_1ylss5jcej().s[112]++;
          body = _e.sent();
          /* istanbul ignore next */
          cov_1ylss5jcej().s[113]++;
          validation = updateProgressSchema.safeParse(body);
          /* istanbul ignore next */
          cov_1ylss5jcej().s[114]++;
          if (!validation.success) {
            /* istanbul ignore next */
            cov_1ylss5jcej().b[48][0]++;
            cov_1ylss5jcej().s[115]++;
            error = new Error('Invalid progress update data');
            /* istanbul ignore next */
            cov_1ylss5jcej().s[116]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[117]++;
            error.details = validation.error.errors;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[118]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1ylss5jcej().b[48][1]++;
          }
          cov_1ylss5jcej().s[119]++;
          _c = validation.data, completedSkills = _c.completedSkills, milestoneId = _c.milestoneId, notes = _c.notes;
          /* istanbul ignore next */
          cov_1ylss5jcej().s[120]++;
          return [4 /*yield*/, prisma_1.prisma.skillGapAnalysis.findFirst({
            where: {
              id: analysisId,
              userId: userId
            }
          })];
        case 4:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][4]++;
          cov_1ylss5jcej().s[121]++;
          analysis = _e.sent();
          /* istanbul ignore next */
          cov_1ylss5jcej().s[122]++;
          if (!analysis) {
            /* istanbul ignore next */
            cov_1ylss5jcej().b[49][0]++;
            cov_1ylss5jcej().s[123]++;
            error = new Error('Gap analysis not found');
            /* istanbul ignore next */
            cov_1ylss5jcej().s[124]++;
            error.statusCode = 404;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[125]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1ylss5jcej().b[49][1]++;
          }
          cov_1ylss5jcej().s[126]++;
          if (analysis.status !== 'ACTIVE') {
            /* istanbul ignore next */
            cov_1ylss5jcej().b[50][0]++;
            cov_1ylss5jcej().s[127]++;
            error = new Error('Cannot update inactive analysis');
            /* istanbul ignore next */
            cov_1ylss5jcej().s[128]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_1ylss5jcej().s[129]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1ylss5jcej().b[50][1]++;
          }
          cov_1ylss5jcej().s[130]++;
          progressTracking =
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[51][0]++, analysis.progressTracking) ||
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[51][1]++, {
            milestones: [],
            completedMilestones: [],
            completedSkills: [],
            currentPhase: 'planning'
          });
          /* istanbul ignore next */
          cov_1ylss5jcej().s[131]++;
          newCompletedSkills = Array.from(new Set(__spreadArray(__spreadArray([],
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[52][0]++, progressTracking.completedSkills) ||
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[52][1]++, []), true), completedSkills, true)));
          /* istanbul ignore next */
          cov_1ylss5jcej().s[132]++;
          completedMilestones = Array.from(new Set(__spreadArray(__spreadArray([],
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[53][0]++, progressTracking.completedMilestones) ||
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[53][1]++, []), true), [milestoneId], false)));
          /* istanbul ignore next */
          cov_1ylss5jcej().s[133]++;
          totalSkillGaps = Array.isArray(analysis.skillGaps) ?
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[54][0]++, analysis.skillGaps.length) :
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[54][1]++, 0);
          /* istanbul ignore next */
          cov_1ylss5jcej().s[134]++;
          completionPercentage = totalSkillGaps > 0 ?
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[55][0]++, Math.round(newCompletedSkills.length / totalSkillGaps * 100)) :
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[55][1]++, 0);
          /* istanbul ignore next */
          cov_1ylss5jcej().s[135]++;
          updatedProgressTracking = __assign(__assign({}, progressTracking), {
            completedSkills: newCompletedSkills,
            completedMilestones: completedMilestones,
            lastUpdated: new Date().toISOString(),
            notes: notes ?
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[56][0]++, __spreadArray(__spreadArray([],
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[57][0]++, progressTracking.notes) ||
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[57][1]++, []), true), [{
              date: new Date().toISOString(),
              milestone: milestoneId,
              note: notes
            }], false)) :
            /* istanbul ignore next */
            (cov_1ylss5jcej().b[56][1]++, progressTracking.notes)
          });
          /* istanbul ignore next */
          cov_1ylss5jcej().s[136]++;
          milestones =
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[58][0]++, progressTracking.milestones) ||
          /* istanbul ignore next */
          (cov_1ylss5jcej().b[58][1]++, []);
          /* istanbul ignore next */
          cov_1ylss5jcej().s[137]++;
          nextMilestone = milestones.find(function (m) {
            /* istanbul ignore next */
            cov_1ylss5jcej().f[19]++;
            cov_1ylss5jcej().s[138]++;
            return !completedMilestones.includes(m.month.toString());
          });
          /* istanbul ignore next */
          cov_1ylss5jcej().s[139]++;
          return [4 /*yield*/, prisma_1.prisma.skillGapAnalysis.update({
            where: {
              id: analysisId
            },
            data: {
              progressTracking: updatedProgressTracking,
              completionPercentage: completionPercentage,
              lastUpdated: new Date(),
              status: completionPercentage >= 100 ?
              /* istanbul ignore next */
              (cov_1ylss5jcej().b[59][0]++, 'COMPLETED') :
              /* istanbul ignore next */
              (cov_1ylss5jcej().b[59][1]++, 'ACTIVE')
            }
          })];
        case 5:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][5]++;
          cov_1ylss5jcej().s[140]++;
          updatedAnalysis = _e.sent();
          /* istanbul ignore next */
          cov_1ylss5jcej().s[141]++;
          achievements = [];
          /* istanbul ignore next */
          cov_1ylss5jcej().s[142]++;
          _i = 0, completedSkills_1 = completedSkills;
          /* istanbul ignore next */
          cov_1ylss5jcej().s[143]++;
          _e.label = 6;
        case 6:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][6]++;
          cov_1ylss5jcej().s[144]++;
          if (!(_i < completedSkills_1.length)) {
            /* istanbul ignore next */
            cov_1ylss5jcej().b[60][0]++;
            cov_1ylss5jcej().s[145]++;
            return [3 /*break*/, 13];
          } else
          /* istanbul ignore next */
          {
            cov_1ylss5jcej().b[60][1]++;
          }
          cov_1ylss5jcej().s[146]++;
          skillName = completedSkills_1[_i];
          /* istanbul ignore next */
          cov_1ylss5jcej().s[147]++;
          _e.label = 7;
        case 7:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][7]++;
          cov_1ylss5jcej().s[148]++;
          _e.trys.push([7, 11,, 12]);
          /* istanbul ignore next */
          cov_1ylss5jcej().s[149]++;
          return [4 /*yield*/, prisma_1.prisma.skill.findFirst({
            where: {
              name: {
                contains: skillName,
                mode: 'insensitive'
              }
            }
          })];
        case 8:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][8]++;
          cov_1ylss5jcej().s[150]++;
          skill = _e.sent();
          /* istanbul ignore next */
          cov_1ylss5jcej().s[151]++;
          if (!skill) {
            /* istanbul ignore next */
            cov_1ylss5jcej().b[61][0]++;
            cov_1ylss5jcej().s[152]++;
            return [3 /*break*/, 10];
          } else
          /* istanbul ignore next */
          {
            cov_1ylss5jcej().b[61][1]++;
          }
          // Update user skill progress
          cov_1ylss5jcej().s[153]++;
          return [4 /*yield*/, prisma_1.prisma.userSkillProgress.upsert({
            where: {
              userId_skillId: {
                userId: userId,
                skillId: skill.id
              }
            },
            update: {
              progressPoints: {
                increment: 25
              },
              // Bonus points for gap analysis completion
              lastPracticed: new Date()
            },
            create: {
              userId: userId,
              skillId: skill.id,
              currentLevel: 'INTERMEDIATE',
              progressPoints: 25,
              lastPracticed: new Date()
            }
          })];
        case 9:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][9]++;
          cov_1ylss5jcej().s[154]++;
          // Update user skill progress
          _e.sent();
          /* istanbul ignore next */
          cov_1ylss5jcej().s[155]++;
          achievements.push({
            type: 'SKILL_PROGRESS',
            title: "".concat(skillName, " Progress"),
            points: 25
          });
          /* istanbul ignore next */
          cov_1ylss5jcej().s[156]++;
          _e.label = 10;
        case 10:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][10]++;
          cov_1ylss5jcej().s[157]++;
          return [3 /*break*/, 12];
        case 11:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][11]++;
          cov_1ylss5jcej().s[158]++;
          error_1 = _e.sent();
          /* istanbul ignore next */
          cov_1ylss5jcej().s[159]++;
          console.error("Error updating progress for skill ".concat(skillName, ":"), error_1);
          /* istanbul ignore next */
          cov_1ylss5jcej().s[160]++;
          return [3 /*break*/, 12];
        case 12:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][12]++;
          cov_1ylss5jcej().s[161]++;
          _i++;
          /* istanbul ignore next */
          cov_1ylss5jcej().s[162]++;
          return [3 /*break*/, 6];
        case 13:
          /* istanbul ignore next */
          cov_1ylss5jcej().b[42][13]++;
          cov_1ylss5jcej().s[163]++;
          // Add milestone completion achievement
          achievements.push({
            type: 'MILESTONE_COMPLETED',
            title: "Milestone ".concat(milestoneId, " Completed"),
            points: 50
          });
          /* istanbul ignore next */
          cov_1ylss5jcej().s[164]++;
          responseData = {
            updatedAnalysis: {
              completionPercentage: completionPercentage,
              nextMilestone: nextMilestone ?
              /* istanbul ignore next */
              (cov_1ylss5jcej().b[62][0]++, {
                skills:
                /* istanbul ignore next */
                (cov_1ylss5jcej().b[63][0]++, nextMilestone.skills) ||
                /* istanbul ignore next */
                (cov_1ylss5jcej().b[63][1]++, []),
                estimatedHours:
                /* istanbul ignore next */
                (cov_1ylss5jcej().b[64][0]++, nextMilestone.estimatedHours) ||
                /* istanbul ignore next */
                (cov_1ylss5jcej().b[64][1]++, 0),
                dueDate: calculateMilestoneDueDate(analysis.createdAt, nextMilestone.month)
              }) :
              /* istanbul ignore next */
              (cov_1ylss5jcej().b[62][1]++, {
                skills: [],
                estimatedHours: 0,
                dueDate: new Date().toISOString()
              })
            },
            achievements: achievements
          };
          /* istanbul ignore next */
          cov_1ylss5jcej().s[165]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: responseData
          })];
      }
    });
  });
});
function calculateMilestoneDueDate(startDate, milestoneMonth) {
  /* istanbul ignore next */
  cov_1ylss5jcej().f[20]++;
  var dueDate =
  /* istanbul ignore next */
  (cov_1ylss5jcej().s[166]++, new Date(startDate));
  /* istanbul ignore next */
  cov_1ylss5jcej().s[167]++;
  dueDate.setMonth(dueDate.getMonth() + milestoneMonth);
  /* istanbul ignore next */
  cov_1ylss5jcej().s[168]++;
  return dueDate.toISOString();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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