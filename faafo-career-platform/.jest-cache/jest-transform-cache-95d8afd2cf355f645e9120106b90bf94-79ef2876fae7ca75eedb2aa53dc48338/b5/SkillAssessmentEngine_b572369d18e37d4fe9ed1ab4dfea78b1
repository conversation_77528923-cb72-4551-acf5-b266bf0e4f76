53f61f171b5b4172eacfa8192901beef
"use strict";

/* istanbul ignore next */
function cov_mwcv5ulzt() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillAssessmentEngine.ts";
  var hash = "53de580f14d59d2083abad6435ed7b3748b6788a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillAssessmentEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 39
        }
      },
      "80": {
        start: {
          line: 51,
          column: 13
        },
        end: {
          line: 51,
          column: 28
        }
      },
      "81": {
        start: {
          line: 52,
          column: 43
        },
        end: {
          line: 458,
          column: 3
        }
      },
      "82": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 65,
          column: 10
        }
      },
      "84": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 67,
          column: 36
        }
      },
      "85": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 71,
          column: 6
        }
      },
      "86": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 70,
          column: 39
        }
      },
      "87": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 90,
          column: 6
        }
      },
      "88": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 75,
          column: 9
        }
      },
      "89": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 74,
          column: 61
        }
      },
      "90": {
        start: {
          line: 76,
          column: 25
        },
        end: {
          line: 87,
          column: 9
        }
      },
      "91": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 56
        }
      },
      "92": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 26
        }
      },
      "93": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 101,
          column: 6
        }
      },
      "94": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 100,
          column: 11
        }
      },
      "95": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 96,
          column: 53
        }
      },
      "96": {
        start: {
          line: 96,
          column: 38
        },
        end: {
          line: 96,
          column: 51
        }
      },
      "97": {
        start: {
          line: 97,
          column: 12
        },
        end: {
          line: 99,
          column: 15
        }
      },
      "98": {
        start: {
          line: 98,
          column: 16
        },
        end: {
          line: 98,
          column: 99
        }
      },
      "99": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 147,
          column: 6
        }
      },
      "100": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 146,
          column: 11
        }
      },
      "101": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 145,
          column: 15
        }
      },
      "102": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 144,
          column: 17
        }
      },
      "103": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 70
        }
      },
      "104": {
        start: {
          line: 109,
          column: 24
        },
        end: {
          line: 111,
          column: 25
        }
      },
      "105": {
        start: {
          line: 110,
          column: 28
        },
        end: {
          line: 110,
          column: 68
        }
      },
      "106": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 112,
          column: 39
        }
      },
      "107": {
        start: {
          line: 113,
          column: 24
        },
        end: {
          line: 113,
          column: 85
        }
      },
      "108": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 122
        }
      },
      "109": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 115,
          column: 57
        }
      },
      "110": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 37
        }
      },
      "111": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 71
        }
      },
      "112": {
        start: {
          line: 118,
          column: 47
        },
        end: {
          line: 118,
          column: 71
        }
      },
      "113": {
        start: {
          line: 119,
          column: 24
        },
        end: {
          line: 119,
          column: 41
        }
      },
      "114": {
        start: {
          line: 120,
          column: 24
        },
        end: {
          line: 120,
          column: 30
        }
      },
      "115": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 121,
          column: 37
        }
      },
      "116": {
        start: {
          line: 123,
          column: 24
        },
        end: {
          line: 123,
          column: 78
        }
      },
      "117": {
        start: {
          line: 123,
          column: 54
        },
        end: {
          line: 123,
          column: 78
        }
      },
      "118": {
        start: {
          line: 124,
          column: 24
        },
        end: {
          line: 124,
          column: 83
        }
      },
      "119": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 125,
          column: 97
        }
      },
      "120": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 127,
          column: 45
        }
      },
      "121": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 128,
          column: 49
        }
      },
      "122": {
        start: {
          line: 129,
          column: 24
        },
        end: {
          line: 129,
          column: 37
        }
      },
      "123": {
        start: {
          line: 131,
          column: 24
        },
        end: {
          line: 131,
          column: 28
        }
      },
      "124": {
        start: {
          line: 132,
          column: 24
        },
        end: {
          line: 132,
          column: 48
        }
      },
      "125": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 29
        }
      },
      "126": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 135,
          column: 48
        }
      },
      "127": {
        start: {
          line: 137,
          column: 24
        },
        end: {
          line: 139,
          column: 25
        }
      },
      "128": {
        start: {
          line: 138,
          column: 28
        },
        end: {
          line: 138,
          column: 57
        }
      },
      "129": {
        start: {
          line: 140,
          column: 24
        },
        end: {
          line: 140,
          column: 57
        }
      },
      "130": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 141,
          column: 58
        }
      },
      "131": {
        start: {
          line: 142,
          column: 24
        },
        end: {
          line: 142,
          column: 71
        }
      },
      "132": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 143,
          column: 57
        }
      },
      "133": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 187,
          column: 6
        }
      },
      "134": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 186,
          column: 11
        }
      },
      "135": {
        start: {
          line: 151,
          column: 12
        },
        end: {
          line: 185,
          column: 15
        }
      },
      "136": {
        start: {
          line: 152,
          column: 16
        },
        end: {
          line: 152,
          column: 62
        }
      },
      "137": {
        start: {
          line: 153,
          column: 16
        },
        end: {
          line: 155,
          column: 17
        }
      },
      "138": {
        start: {
          line: 154,
          column: 20
        },
        end: {
          line: 154,
          column: 60
        }
      },
      "139": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 158,
          column: 17
        }
      },
      "140": {
        start: {
          line: 157,
          column: 20
        },
        end: {
          line: 157,
          column: 74
        }
      },
      "141": {
        start: {
          line: 159,
          column: 16
        },
        end: {
          line: 159,
          column: 106
        }
      },
      "142": {
        start: {
          line: 159,
          column: 68
        },
        end: {
          line: 159,
          column: 102
        }
      },
      "143": {
        start: {
          line: 160,
          column: 16
        },
        end: {
          line: 162,
          column: 17
        }
      },
      "144": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 161,
          column: 59
        }
      },
      "145": {
        start: {
          line: 163,
          column: 16
        },
        end: {
          line: 165,
          column: 17
        }
      },
      "146": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 64
        }
      },
      "147": {
        start: {
          line: 166,
          column: 16
        },
        end: {
          line: 166,
          column: 122
        }
      },
      "148": {
        start: {
          line: 166,
          column: 76
        },
        end: {
          line: 166,
          column: 118
        }
      },
      "149": {
        start: {
          line: 167,
          column: 16
        },
        end: {
          line: 169,
          column: 17
        }
      },
      "150": {
        start: {
          line: 168,
          column: 20
        },
        end: {
          line: 168,
          column: 65
        }
      },
      "151": {
        start: {
          line: 170,
          column: 16
        },
        end: {
          line: 177,
          column: 18
        }
      },
      "152": {
        start: {
          line: 178,
          column: 16
        },
        end: {
          line: 178,
          column: 52
        }
      },
      "153": {
        start: {
          line: 179,
          column: 16
        },
        end: {
          line: 179,
          column: 50
        }
      },
      "154": {
        start: {
          line: 180,
          column: 16
        },
        end: {
          line: 182,
          column: 17
        }
      },
      "155": {
        start: {
          line: 181,
          column: 20
        },
        end: {
          line: 181,
          column: 54
        }
      },
      "156": {
        start: {
          line: 183,
          column: 16
        },
        end: {
          line: 183,
          column: 63
        }
      },
      "157": {
        start: {
          line: 184,
          column: 16
        },
        end: {
          line: 184,
          column: 48
        }
      },
      "158": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 220,
          column: 6
        }
      },
      "159": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 219,
          column: 11
        }
      },
      "160": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 194,
          column: 53
        }
      },
      "161": {
        start: {
          line: 194,
          column: 38
        },
        end: {
          line: 194,
          column: 51
        }
      },
      "162": {
        start: {
          line: 195,
          column: 12
        },
        end: {
          line: 218,
          column: 15
        }
      },
      "163": {
        start: {
          line: 196,
          column: 16
        },
        end: {
          line: 217,
          column: 17
        }
      },
      "164": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 50
        }
      },
      "165": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 88
        }
      },
      "166": {
        start: {
          line: 201,
          column: 24
        },
        end: {
          line: 201,
          column: 45
        }
      },
      "167": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 206,
          column: 31
        }
      },
      "168": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 44
        }
      },
      "169": {
        start: {
          line: 209,
          column: 24
        },
        end: {
          line: 215,
          column: 31
        }
      },
      "170": {
        start: {
          line: 216,
          column: 28
        },
        end: {
          line: 216,
          column: 50
        }
      },
      "171": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 284,
          column: 6
        }
      },
      "172": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 283,
          column: 11
        }
      },
      "173": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 282,
          column: 15
        }
      },
      "174": {
        start: {
          line: 225,
          column: 16
        },
        end: {
          line: 225,
          column: 62
        }
      },
      "175": {
        start: {
          line: 226,
          column: 16
        },
        end: {
          line: 228,
          column: 17
        }
      },
      "176": {
        start: {
          line: 227,
          column: 20
        },
        end: {
          line: 227,
          column: 60
        }
      },
      "177": {
        start: {
          line: 229,
          column: 16
        },
        end: {
          line: 229,
          column: 33
        }
      },
      "178": {
        start: {
          line: 230,
          column: 16
        },
        end: {
          line: 230,
          column: 112
        }
      },
      "179": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 251,
          column: 18
        }
      },
      "180": {
        start: {
          line: 232,
          column: 41
        },
        end: {
          line: 232,
          column: 116
        }
      },
      "181": {
        start: {
          line: 232,
          column: 84
        },
        end: {
          line: 232,
          column: 113
        }
      },
      "182": {
        start: {
          line: 233,
          column: 41
        },
        end: {
          line: 235,
          column: 22
        }
      },
      "183": {
        start: {
          line: 234,
          column: 24
        },
        end: {
          line: 234,
          column: 99
        }
      },
      "184": {
        start: {
          line: 234,
          column: 66
        },
        end: {
          line: 234,
          column: 95
        }
      },
      "185": {
        start: {
          line: 236,
          column: 38
        },
        end: {
          line: 236,
          column: 39
        }
      },
      "186": {
        start: {
          line: 237,
          column: 40
        },
        end: {
          line: 237,
          column: 41
        }
      },
      "187": {
        start: {
          line: 238,
          column: 34
        },
        end: {
          line: 245,
          column: 21
        }
      },
      "188": {
        start: {
          line: 239,
          column: 39
        },
        end: {
          line: 239,
          column: 113
        }
      },
      "189": {
        start: {
          line: 239,
          column: 74
        },
        end: {
          line: 239,
          column: 110
        }
      },
      "190": {
        start: {
          line: 240,
          column: 37
        },
        end: {
          line: 240,
          column: 80
        }
      },
      "191": {
        start: {
          line: 241,
          column: 24
        },
        end: {
          line: 241,
          column: 46
        }
      },
      "192": {
        start: {
          line: 242,
          column: 24
        },
        end: {
          line: 244,
          column: 25
        }
      },
      "193": {
        start: {
          line: 243,
          column: 28
        },
        end: {
          line: 243,
          column: 52
        }
      },
      "194": {
        start: {
          line: 246,
          column: 20
        },
        end: {
          line: 249,
          column: 21
        }
      },
      "195": {
        start: {
          line: 246,
          column: 34
        },
        end: {
          line: 246,
          column: 35
        }
      },
      "196": {
        start: {
          line: 246,
          column: 56
        },
        end: {
          line: 246,
          column: 70
        }
      },
      "197": {
        start: {
          line: 247,
          column: 39
        },
        end: {
          line: 247,
          column: 59
        }
      },
      "198": {
        start: {
          line: 248,
          column: 24
        },
        end: {
          line: 248,
          column: 42
        }
      },
      "199": {
        start: {
          line: 250,
          column: 20
        },
        end: {
          line: 250,
          column: 101
        }
      },
      "200": {
        start: {
          line: 253,
          column: 16
        },
        end: {
          line: 256,
          column: 17
        }
      },
      "201": {
        start: {
          line: 254,
          column: 20
        },
        end: {
          line: 254,
          column: 37
        }
      },
      "202": {
        start: {
          line: 255,
          column: 20
        },
        end: {
          line: 255,
          column: 37
        }
      },
      "203": {
        start: {
          line: 257,
          column: 16
        },
        end: {
          line: 257,
          column: 149
        }
      },
      "204": {
        start: {
          line: 257,
          column: 89
        },
        end: {
          line: 257,
          column: 108
        }
      },
      "205": {
        start: {
          line: 258,
          column: 16
        },
        end: {
          line: 258,
          column: 159
        }
      },
      "206": {
        start: {
          line: 258,
          column: 78
        },
        end: {
          line: 258,
          column: 112
        }
      },
      "207": {
        start: {
          line: 258,
          column: 135
        },
        end: {
          line: 258,
          column: 155
        }
      },
      "208": {
        start: {
          line: 259,
          column: 16
        },
        end: {
          line: 261,
          column: 32
        }
      },
      "209": {
        start: {
          line: 260,
          column: 69
        },
        end: {
          line: 260,
          column: 87
        }
      },
      "210": {
        start: {
          line: 262,
          column: 16
        },
        end: {
          line: 262,
          column: 113
        }
      },
      "211": {
        start: {
          line: 262,
          column: 81
        },
        end: {
          line: 262,
          column: 106
        }
      },
      "212": {
        start: {
          line: 263,
          column: 16
        },
        end: {
          line: 265,
          column: 24
        }
      },
      "213": {
        start: {
          line: 266,
          column: 16
        },
        end: {
          line: 275,
          column: 18
        }
      },
      "214": {
        start: {
          line: 277,
          column: 16
        },
        end: {
          line: 277,
          column: 48
        }
      },
      "215": {
        start: {
          line: 278,
          column: 16
        },
        end: {
          line: 278,
          column: 61
        }
      },
      "216": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 50
        }
      },
      "217": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 280,
          column: 63
        }
      },
      "218": {
        start: {
          line: 281,
          column: 16
        },
        end: {
          line: 281,
          column: 47
        }
      },
      "219": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 287,
          column: 6
        }
      },
      "220": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 286,
          column: 58
        }
      },
      "221": {
        start: {
          line: 288,
          column: 4
        },
        end: {
          line: 290,
          column: 6
        }
      },
      "222": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 289,
          column: 106
        }
      },
      "223": {
        start: {
          line: 289,
          column: 75
        },
        end: {
          line: 289,
          column: 102
        }
      },
      "224": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 294,
          column: 6
        }
      },
      "225": {
        start: {
          line: 293,
          column: 8
        },
        end: {
          line: 293,
          column: 53
        }
      },
      "226": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 309,
          column: 6
        }
      },
      "227": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 308,
          column: 11
        }
      },
      "228": {
        start: {
          line: 298,
          column: 12
        },
        end: {
          line: 307,
          column: 15
        }
      },
      "229": {
        start: {
          line: 299,
          column: 16
        },
        end: {
          line: 299,
          column: 62
        }
      },
      "230": {
        start: {
          line: 300,
          column: 16
        },
        end: {
          line: 302,
          column: 17
        }
      },
      "231": {
        start: {
          line: 301,
          column: 20
        },
        end: {
          line: 301,
          column: 60
        }
      },
      "232": {
        start: {
          line: 303,
          column: 16
        },
        end: {
          line: 303,
          column: 43
        }
      },
      "233": {
        start: {
          line: 304,
          column: 16
        },
        end: {
          line: 304,
          column: 50
        }
      },
      "234": {
        start: {
          line: 305,
          column: 16
        },
        end: {
          line: 305,
          column: 63
        }
      },
      "235": {
        start: {
          line: 306,
          column: 16
        },
        end: {
          line: 306,
          column: 38
        }
      },
      "236": {
        start: {
          line: 310,
          column: 4
        },
        end: {
          line: 327,
          column: 6
        }
      },
      "237": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 326,
          column: 11
        }
      },
      "238": {
        start: {
          line: 313,
          column: 12
        },
        end: {
          line: 325,
          column: 15
        }
      },
      "239": {
        start: {
          line: 314,
          column: 16
        },
        end: {
          line: 314,
          column: 83
        }
      },
      "240": {
        start: {
          line: 315,
          column: 16
        },
        end: {
          line: 315,
          column: 99
        }
      },
      "241": {
        start: {
          line: 316,
          column: 16
        },
        end: {
          line: 324,
          column: 23
        }
      },
      "242": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 426,
          column: 6
        }
      },
      "243": {
        start: {
          line: 330,
          column: 24
        },
        end: {
          line: 415,
          column: 9
        }
      },
      "244": {
        start: {
          line: 416,
          column: 29
        },
        end: {
          line: 416,
          column: 47
        }
      },
      "245": {
        start: {
          line: 417,
          column: 8
        },
        end: {
          line: 424,
          column: 9
        }
      },
      "246": {
        start: {
          line: 418,
          column: 12
        },
        end: {
          line: 423,
          column: 19
        }
      },
      "247": {
        start: {
          line: 425,
          column: 8
        },
        end: {
          line: 425,
          column: 69
        }
      },
      "248": {
        start: {
          line: 427,
          column: 4
        },
        end: {
          line: 433,
          column: 6
        }
      },
      "249": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 432,
          column: 9
        }
      },
      "250": {
        start: {
          line: 429,
          column: 21
        },
        end: {
          line: 429,
          column: 37
        }
      },
      "251": {
        start: {
          line: 430,
          column: 20
        },
        end: {
          line: 430,
          column: 55
        }
      },
      "252": {
        start: {
          line: 431,
          column: 12
        },
        end: {
          line: 431,
          column: 74
        }
      },
      "253": {
        start: {
          line: 434,
          column: 4
        },
        end: {
          line: 456,
          column: 6
        }
      },
      "254": {
        start: {
          line: 435,
          column: 30
        },
        end: {
          line: 435,
          column: 32
        }
      },
      "255": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 444,
          column: 9
        }
      },
      "256": {
        start: {
          line: 437,
          column: 12
        },
        end: {
          line: 437,
          column: 119
        }
      },
      "257": {
        start: {
          line: 439,
          column: 13
        },
        end: {
          line: 444,
          column: 9
        }
      },
      "258": {
        start: {
          line: 440,
          column: 12
        },
        end: {
          line: 440,
          column: 97
        }
      },
      "259": {
        start: {
          line: 443,
          column: 12
        },
        end: {
          line: 443,
          column: 97
        }
      },
      "260": {
        start: {
          line: 446,
          column: 8
        },
        end: {
          line: 454,
          column: 9
        }
      },
      "261": {
        start: {
          line: 446,
          column: 22
        },
        end: {
          line: 446,
          column: 23
        }
      },
      "262": {
        start: {
          line: 446,
          column: 30
        },
        end: {
          line: 446,
          column: 57
        }
      },
      "263": {
        start: {
          line: 447,
          column: 21
        },
        end: {
          line: 447,
          column: 27
        }
      },
      "264": {
        start: {
          line: 447,
          column: 37
        },
        end: {
          line: 447,
          column: 42
        }
      },
      "265": {
        start: {
          line: 447,
          column: 52
        },
        end: {
          line: 447,
          column: 57
        }
      },
      "266": {
        start: {
          line: 448,
          column: 12
        },
        end: {
          line: 453,
          column: 13
        }
      },
      "267": {
        start: {
          line: 449,
          column: 16
        },
        end: {
          line: 449,
          column: 107
        }
      },
      "268": {
        start: {
          line: 451,
          column: 17
        },
        end: {
          line: 453,
          column: 13
        }
      },
      "269": {
        start: {
          line: 452,
          column: 16
        },
        end: {
          line: 452,
          column: 131
        }
      },
      "270": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 455,
          column: 31
        }
      },
      "271": {
        start: {
          line: 457,
          column: 4
        },
        end: {
          line: 457,
          column: 33
        }
      },
      "272": {
        start: {
          line: 459,
          column: 0
        },
        end: {
          line: 459,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 52,
            column: 43
          },
          end: {
            line: 52,
            column: 44
          }
        },
        loc: {
          start: {
            line: 52,
            column: 55
          },
          end: {
            line: 458,
            column: 1
          }
        },
        line: 52
      },
      "16": {
        name: "SkillAssessmentEngine",
        decl: {
          start: {
            line: 53,
            column: 13
          },
          end: {
            line: 53,
            column: 34
          }
        },
        loc: {
          start: {
            line: 53,
            column: 37
          },
          end: {
            line: 68,
            column: 5
          }
        },
        line: 53
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 69,
            column: 57
          },
          end: {
            line: 69,
            column: 58
          }
        },
        loc: {
          start: {
            line: 69,
            column: 76
          },
          end: {
            line: 71,
            column: 5
          }
        },
        line: 69
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 72,
            column: 55
          },
          end: {
            line: 72,
            column: 56
          }
        },
        loc: {
          start: {
            line: 72,
            column: 73
          },
          end: {
            line: 90,
            column: 5
          }
        },
        line: 72
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 94,
            column: 71
          },
          end: {
            line: 94,
            column: 72
          }
        },
        loc: {
          start: {
            line: 94,
            column: 91
          },
          end: {
            line: 101,
            column: 5
          }
        },
        line: 94
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 95,
            column: 51
          },
          end: {
            line: 95,
            column: 52
          }
        },
        loc: {
          start: {
            line: 95,
            column: 78
          },
          end: {
            line: 100,
            column: 9
          }
        },
        line: 95
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 97,
            column: 37
          },
          end: {
            line: 97,
            column: 38
          }
        },
        loc: {
          start: {
            line: 97,
            column: 51
          },
          end: {
            line: 99,
            column: 13
          }
        },
        line: 97
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 102,
            column: 56
          },
          end: {
            line: 102,
            column: 57
          }
        },
        loc: {
          start: {
            line: 102,
            column: 80
          },
          end: {
            line: 147,
            column: 5
          }
        },
        line: 102
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 103,
            column: 48
          },
          end: {
            line: 103,
            column: 49
          }
        },
        loc: {
          start: {
            line: 103,
            column: 60
          },
          end: {
            line: 146,
            column: 9
          }
        },
        line: 103
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 105,
            column: 37
          },
          end: {
            line: 105,
            column: 38
          }
        },
        loc: {
          start: {
            line: 105,
            column: 51
          },
          end: {
            line: 145,
            column: 13
          }
        },
        line: 105
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 148,
            column: 53
          },
          end: {
            line: 148,
            column: 54
          }
        },
        loc: {
          start: {
            line: 148,
            column: 85
          },
          end: {
            line: 187,
            column: 5
          }
        },
        line: 148
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 149,
            column: 48
          },
          end: {
            line: 149,
            column: 49
          }
        },
        loc: {
          start: {
            line: 149,
            column: 60
          },
          end: {
            line: 186,
            column: 9
          }
        },
        line: 149
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 151,
            column: 37
          },
          end: {
            line: 151,
            column: 38
          }
        },
        loc: {
          start: {
            line: 151,
            column: 51
          },
          end: {
            line: 185,
            column: 13
          }
        },
        line: 151
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 159,
            column: 53
          },
          end: {
            line: 159,
            column: 54
          }
        },
        loc: {
          start: {
            line: 159,
            column: 66
          },
          end: {
            line: 159,
            column: 104
          }
        },
        line: 159
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 166,
            column: 61
          },
          end: {
            line: 166,
            column: 62
          }
        },
        loc: {
          start: {
            line: 166,
            column: 74
          },
          end: {
            line: 166,
            column: 120
          }
        },
        line: 166
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 191,
            column: 69
          },
          end: {
            line: 191,
            column: 70
          }
        },
        loc: {
          start: {
            line: 191,
            column: 105
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 191
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 192,
            column: 51
          },
          end: {
            line: 192,
            column: 52
          }
        },
        loc: {
          start: {
            line: 192,
            column: 92
          },
          end: {
            line: 219,
            column: 9
          }
        },
        line: 192
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 195,
            column: 37
          },
          end: {
            line: 195,
            column: 38
          }
        },
        loc: {
          start: {
            line: 195,
            column: 51
          },
          end: {
            line: 218,
            column: 13
          }
        },
        line: 195
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 221,
            column: 55
          },
          end: {
            line: 221,
            column: 56
          }
        },
        loc: {
          start: {
            line: 221,
            column: 79
          },
          end: {
            line: 284,
            column: 5
          }
        },
        line: 221
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 222,
            column: 48
          },
          end: {
            line: 222,
            column: 49
          }
        },
        loc: {
          start: {
            line: 222,
            column: 60
          },
          end: {
            line: 283,
            column: 9
          }
        },
        line: 222
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 224,
            column: 37
          },
          end: {
            line: 224,
            column: 38
          }
        },
        loc: {
          start: {
            line: 224,
            column: 51
          },
          end: {
            line: 282,
            column: 13
          }
        },
        line: 224
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 231,
            column: 26
          },
          end: {
            line: 231,
            column: 27
          }
        },
        loc: {
          start: {
            line: 231,
            column: 45
          },
          end: {
            line: 251,
            column: 17
          }
        },
        line: 231
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 232,
            column: 69
          },
          end: {
            line: 232,
            column: 70
          }
        },
        loc: {
          start: {
            line: 232,
            column: 82
          },
          end: {
            line: 232,
            column: 115
          }
        },
        line: 232
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 233,
            column: 69
          },
          end: {
            line: 233,
            column: 70
          }
        },
        loc: {
          start: {
            line: 233,
            column: 82
          },
          end: {
            line: 235,
            column: 21
          }
        },
        line: 233
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 234,
            column: 51
          },
          end: {
            line: 234,
            column: 52
          }
        },
        loc: {
          start: {
            line: 234,
            column: 64
          },
          end: {
            line: 234,
            column: 97
          }
        },
        line: 234
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 238,
            column: 34
          },
          end: {
            line: 238,
            column: 35
          }
        },
        loc: {
          start: {
            line: 238,
            column: 54
          },
          end: {
            line: 245,
            column: 21
          }
        },
        line: 238
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 239,
            column: 59
          },
          end: {
            line: 239,
            column: 60
          }
        },
        loc: {
          start: {
            line: 239,
            column: 72
          },
          end: {
            line: 239,
            column: 112
          }
        },
        line: 239
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 257,
            column: 65
          },
          end: {
            line: 257,
            column: 66
          }
        },
        loc: {
          start: {
            line: 257,
            column: 87
          },
          end: {
            line: 257,
            column: 110
          }
        },
        line: 257
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 258,
            column: 63
          },
          end: {
            line: 258,
            column: 64
          }
        },
        loc: {
          start: {
            line: 258,
            column: 76
          },
          end: {
            line: 258,
            column: 114
          }
        },
        line: 258
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 258,
            column: 120
          },
          end: {
            line: 258,
            column: 121
          }
        },
        loc: {
          start: {
            line: 258,
            column: 133
          },
          end: {
            line: 258,
            column: 157
          }
        },
        line: 258
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 260,
            column: 46
          },
          end: {
            line: 260,
            column: 47
          }
        },
        loc: {
          start: {
            line: 260,
            column: 67
          },
          end: {
            line: 260,
            column: 89
          }
        },
        line: 260
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 262,
            column: 61
          },
          end: {
            line: 262,
            column: 62
          }
        },
        loc: {
          start: {
            line: 262,
            column: 79
          },
          end: {
            line: 262,
            column: 108
          }
        },
        line: 262
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 285,
            column: 52
          },
          end: {
            line: 285,
            column: 53
          }
        },
        loc: {
          start: {
            line: 285,
            column: 76
          },
          end: {
            line: 287,
            column: 5
          }
        },
        line: 285
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 288,
            column: 59
          },
          end: {
            line: 288,
            column: 60
          }
        },
        loc: {
          start: {
            line: 288,
            column: 77
          },
          end: {
            line: 290,
            column: 5
          }
        },
        line: 288
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 289,
            column: 60
          },
          end: {
            line: 289,
            column: 61
          }
        },
        loc: {
          start: {
            line: 289,
            column: 73
          },
          end: {
            line: 289,
            column: 104
          }
        },
        line: 289
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 292,
            column: 56
          },
          end: {
            line: 292,
            column: 57
          }
        },
        loc: {
          start: {
            line: 292,
            column: 68
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 292
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 295,
            column: 61
          },
          end: {
            line: 295,
            column: 62
          }
        },
        loc: {
          start: {
            line: 295,
            column: 93
          },
          end: {
            line: 309,
            column: 5
          }
        },
        line: 295
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 296,
            column: 48
          },
          end: {
            line: 296,
            column: 49
          }
        },
        loc: {
          start: {
            line: 296,
            column: 60
          },
          end: {
            line: 308,
            column: 9
          }
        },
        line: 296
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 298,
            column: 37
          },
          end: {
            line: 298,
            column: 38
          }
        },
        loc: {
          start: {
            line: 298,
            column: 51
          },
          end: {
            line: 307,
            column: 13
          }
        },
        line: 298
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 310,
            column: 63
          },
          end: {
            line: 310,
            column: 64
          }
        },
        loc: {
          start: {
            line: 310,
            column: 94
          },
          end: {
            line: 327,
            column: 5
          }
        },
        line: 310
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 311,
            column: 48
          },
          end: {
            line: 311,
            column: 49
          }
        },
        loc: {
          start: {
            line: 311,
            column: 60
          },
          end: {
            line: 326,
            column: 9
          }
        },
        line: 311
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 313,
            column: 37
          },
          end: {
            line: 313,
            column: 38
          }
        },
        loc: {
          start: {
            line: 313,
            column: 51
          },
          end: {
            line: 325,
            column: 13
          }
        },
        line: 313
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 328,
            column: 59
          },
          end: {
            line: 328,
            column: 60
          }
        },
        loc: {
          start: {
            line: 328,
            column: 90
          },
          end: {
            line: 426,
            column: 5
          }
        },
        line: 328
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 427,
            column: 51
          },
          end: {
            line: 427,
            column: 52
          }
        },
        loc: {
          start: {
            line: 427,
            column: 68
          },
          end: {
            line: 433,
            column: 5
          }
        },
        line: 427
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 434,
            column: 62
          },
          end: {
            line: 434,
            column: 63
          }
        },
        loc: {
          start: {
            line: 434,
            column: 99
          },
          end: {
            line: 456,
            column: 5
          }
        },
        line: 434
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 73,
            column: 8
          },
          end: {
            line: 75,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 8
          },
          end: {
            line: 75,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 73
      },
      "36": {
        loc: {
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 73,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 73,
            column: 26
          }
        }, {
          start: {
            line: 73,
            column: 30
          },
          end: {
            line: 73,
            column: 50
          }
        }, {
          start: {
            line: 73,
            column: 54
          },
          end: {
            line: 73,
            column: 77
          }
        }],
        line: 73
      },
      "37": {
        loc: {
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 96,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 96,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "38": {
        loc: {
          start: {
            line: 106,
            column: 16
          },
          end: {
            line: 144,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 107,
            column: 20
          },
          end: {
            line: 116,
            column: 37
          }
        }, {
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 121,
            column: 37
          }
        }, {
          start: {
            line: 122,
            column: 20
          },
          end: {
            line: 125,
            column: 97
          }
        }, {
          start: {
            line: 126,
            column: 20
          },
          end: {
            line: 129,
            column: 37
          }
        }, {
          start: {
            line: 130,
            column: 20
          },
          end: {
            line: 132,
            column: 48
          }
        }, {
          start: {
            line: 133,
            column: 20
          },
          end: {
            line: 135,
            column: 48
          }
        }, {
          start: {
            line: 136,
            column: 20
          },
          end: {
            line: 143,
            column: 57
          }
        }],
        line: 106
      },
      "39": {
        loc: {
          start: {
            line: 109,
            column: 24
          },
          end: {
            line: 111,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 24
          },
          end: {
            line: 111,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "40": {
        loc: {
          start: {
            line: 113,
            column: 44
          },
          end: {
            line: 113,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 44
          },
          end: {
            line: 113,
            column: 79
          }
        }, {
          start: {
            line: 113,
            column: 83
          },
          end: {
            line: 113,
            column: 84
          }
        }],
        line: 113
      },
      "41": {
        loc: {
          start: {
            line: 114,
            column: 43
          },
          end: {
            line: 114,
            column: 121
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 114,
            column: 43
          },
          end: {
            line: 114,
            column: 77
          }
        }, {
          start: {
            line: 114,
            column: 81
          },
          end: {
            line: 114,
            column: 121
          }
        }],
        line: 114
      },
      "42": {
        loc: {
          start: {
            line: 118,
            column: 24
          },
          end: {
            line: 118,
            column: 71
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 24
          },
          end: {
            line: 118,
            column: 71
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "43": {
        loc: {
          start: {
            line: 123,
            column: 24
          },
          end: {
            line: 123,
            column: 78
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 24
          },
          end: {
            line: 123,
            column: 78
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "44": {
        loc: {
          start: {
            line: 137,
            column: 24
          },
          end: {
            line: 139,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 24
          },
          end: {
            line: 139,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "45": {
        loc: {
          start: {
            line: 153,
            column: 16
          },
          end: {
            line: 155,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 16
          },
          end: {
            line: 155,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "46": {
        loc: {
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 158,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 158,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "47": {
        loc: {
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 162,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 162,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "48": {
        loc: {
          start: {
            line: 163,
            column: 16
          },
          end: {
            line: 165,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 16
          },
          end: {
            line: 165,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "49": {
        loc: {
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 163,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 163,
            column: 45
          }
        }, {
          start: {
            line: 163,
            column: 49
          },
          end: {
            line: 163,
            column: 97
          }
        }],
        line: 163
      },
      "50": {
        loc: {
          start: {
            line: 167,
            column: 16
          },
          end: {
            line: 169,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 16
          },
          end: {
            line: 169,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "51": {
        loc: {
          start: {
            line: 180,
            column: 16
          },
          end: {
            line: 182,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 180,
            column: 16
          },
          end: {
            line: 182,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 180
      },
      "52": {
        loc: {
          start: {
            line: 194,
            column: 12
          },
          end: {
            line: 194,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 12
          },
          end: {
            line: 194,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "53": {
        loc: {
          start: {
            line: 196,
            column: 16
          },
          end: {
            line: 217,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 199,
            column: 88
          }
        }, {
          start: {
            line: 200,
            column: 20
          },
          end: {
            line: 206,
            column: 31
          }
        }, {
          start: {
            line: 207,
            column: 20
          },
          end: {
            line: 215,
            column: 31
          }
        }, {
          start: {
            line: 216,
            column: 20
          },
          end: {
            line: 216,
            column: 50
          }
        }],
        line: 196
      },
      "54": {
        loc: {
          start: {
            line: 211,
            column: 39
          },
          end: {
            line: 211,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 211,
            column: 66
          },
          end: {
            line: 211,
            column: 81
          }
        }, {
          start: {
            line: 211,
            column: 84
          },
          end: {
            line: 211,
            column: 99
          }
        }],
        line: 211
      },
      "55": {
        loc: {
          start: {
            line: 226,
            column: 16
          },
          end: {
            line: 228,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 16
          },
          end: {
            line: 228,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "56": {
        loc: {
          start: {
            line: 230,
            column: 36
          },
          end: {
            line: 230,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 230,
            column: 36
          },
          end: {
            line: 230,
            column: 71
          }
        }, {
          start: {
            line: 230,
            column: 75
          },
          end: {
            line: 230,
            column: 111
          }
        }],
        line: 230
      },
      "57": {
        loc: {
          start: {
            line: 240,
            column: 37
          },
          end: {
            line: 240,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 240,
            column: 37
          },
          end: {
            line: 240,
            column: 75
          }
        }, {
          start: {
            line: 240,
            column: 79
          },
          end: {
            line: 240,
            column: 80
          }
        }],
        line: 240
      },
      "58": {
        loc: {
          start: {
            line: 242,
            column: 24
          },
          end: {
            line: 244,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 24
          },
          end: {
            line: 244,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "59": {
        loc: {
          start: {
            line: 250,
            column: 43
          },
          end: {
            line: 250,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 250,
            column: 61
          },
          end: {
            line: 250,
            column: 96
          }
        }, {
          start: {
            line: 250,
            column: 99
          },
          end: {
            line: 250,
            column: 100
          }
        }],
        line: 250
      },
      "60": {
        loc: {
          start: {
            line: 259,
            column: 36
          },
          end: {
            line: 261,
            column: 31
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 260,
            column: 22
          },
          end: {
            line: 260,
            column: 119
          }
        }, {
          start: {
            line: 261,
            column: 22
          },
          end: {
            line: 261,
            column: 31
          }
        }],
        line: 259
      },
      "61": {
        loc: {
          start: {
            line: 263,
            column: 41
          },
          end: {
            line: 265,
            column: 23
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 264,
            column: 22
          },
          end: {
            line: 264,
            column: 66
          }
        }, {
          start: {
            line: 265,
            column: 22
          },
          end: {
            line: 265,
            column: 23
          }
        }],
        line: 263
      },
      "62": {
        loc: {
          start: {
            line: 286,
            column: 15
          },
          end: {
            line: 286,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 286,
            column: 15
          },
          end: {
            line: 286,
            column: 49
          }
        }, {
          start: {
            line: 286,
            column: 53
          },
          end: {
            line: 286,
            column: 57
          }
        }],
        line: 286
      },
      "63": {
        loc: {
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 302,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 302,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 300
      },
      "64": {
        loc: {
          start: {
            line: 417,
            column: 8
          },
          end: {
            line: 424,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 417,
            column: 8
          },
          end: {
            line: 424,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 417
      },
      "65": {
        loc: {
          start: {
            line: 425,
            column: 15
          },
          end: {
            line: 425,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 425,
            column: 15
          },
          end: {
            line: 425,
            column: 41
          }
        }, {
          start: {
            line: 425,
            column: 45
          },
          end: {
            line: 425,
            column: 68
          }
        }],
        line: 425
      },
      "66": {
        loc: {
          start: {
            line: 436,
            column: 8
          },
          end: {
            line: 444,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 436,
            column: 8
          },
          end: {
            line: 444,
            column: 9
          }
        }, {
          start: {
            line: 439,
            column: 13
          },
          end: {
            line: 444,
            column: 9
          }
        }],
        line: 436
      },
      "67": {
        loc: {
          start: {
            line: 439,
            column: 13
          },
          end: {
            line: 444,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 439,
            column: 13
          },
          end: {
            line: 444,
            column: 9
          }
        }, {
          start: {
            line: 442,
            column: 13
          },
          end: {
            line: 444,
            column: 9
          }
        }],
        line: 439
      },
      "68": {
        loc: {
          start: {
            line: 448,
            column: 12
          },
          end: {
            line: 453,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 448,
            column: 12
          },
          end: {
            line: 453,
            column: 13
          }
        }, {
          start: {
            line: 451,
            column: 17
          },
          end: {
            line: 453,
            column: 13
          }
        }],
        line: 448
      },
      "69": {
        loc: {
          start: {
            line: 451,
            column: 17
          },
          end: {
            line: 453,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 451,
            column: 17
          },
          end: {
            line: 453,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 451
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0, 0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillAssessmentEngine.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6BAAoC;AAqEpC;IAeE;QAdQ,gBAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;QAEjD,kBAAa,GAAqB;YACxC,iBAAiB,EAAE,CAAC;YACpB,gBAAgB,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;YAC1D,SAAS,EAAE,IAAI,EAAE,aAAa;YAC9B,kBAAkB,EAAE,KAAK;YACzB,iBAAiB,EAAE;gBACjB,QAAQ,EAAE,CAAC;gBACX,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,CAAC;aACZ;SACF,CAAC;QAGA,wEAAwE;QACxE,IAAI,CAAC,eAAe,GAAG,IAAW,CAAC;IACrC,CAAC;IAED,kDAAkB,GAAlB,UAAmB,OAAwB;QACzC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;IACjC,CAAC;IAED,gDAAgB,GAAhB,UAAiB,MAA8B;QAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACtE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAM,UAAU,GAAe;YAC7B,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,MAAM,EAAE,SAAS;YACjB,MAAM,wBAAO,IAAI,CAAC,aAAa,GAAK,MAAM,CAAC,MAAM,CAAE;YACnD,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAChD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACG,gEAAgC,GAAtC;0CAAuG,OAAO,YAAvE,MAA8B,EAAE,OAA6B;YAA7B,wBAAA,EAAA,YAA6B;;gBAClG,sBAAO,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,EAAC;;;KACpE;IAEK,iDAAiB,GAAvB,UAAwB,YAAoB;uCAAG,OAAO;;;;;wBAC9C,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;wBACpD,IAAI,CAAC,UAAU,EAAE,CAAC;4BAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;wBAC1C,CAAC;wBAEK,SAAS,GAAyB,EAAE,CAAC;wBACrC,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAC;wBAC7D,gBAAgB,GAAG,UAAU,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;8BAE/D,EAAnB,KAAA,UAAU,CAAC,QAAQ;;;6BAAnB,CAAA,cAAmB,CAAA;wBAA9B,OAAO;wBACP,CAAC,GAAG,CAAC;;;6BAAE,CAAA,CAAC,GAAG,iBAAiB,CAAA;wBAC7B,UAAU,GAAG,gBAAgB,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;wBAChD,qBAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,UAAU,CAAC,EAAA;;wBAAnE,QAAQ,GAAG,SAAwD;wBACzE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;;wBAHY,CAAC,EAAE,CAAA;;;wBADtB,IAAmB,CAAA;;;wBAQzC,IAAI,UAAU,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;4BACzC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;wBAED,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;wBACjC,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;wBAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;wBAE/C,sBAAO,SAAS,EAAC;;;;KAClB;IAEK,8CAAc,GAApB,UAAqB,YAAoB,EAAE,MAA4B;uCAAG,OAAO;;;gBACzE,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,CAAC;gBAED,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBACtC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBACxD,CAAC;gBAEK,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,UAAU,EAA1B,CAA0B,CAAC,CAAC;gBAC5E,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;gBACzC,CAAC;gBAED,IAAI,MAAM,CAAC,cAAc,GAAG,CAAC,IAAI,MAAM,CAAC,cAAc,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBAClF,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC9C,CAAC;gBAEK,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,KAAK,MAAM,CAAC,UAAU,EAAlC,CAAkC,CAAC,CAAC;gBAC5F,IAAI,gBAAgB,EAAE,CAAC;oBACrB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAC/C,CAAC;gBAEK,QAAQ,GAAuB;oBACnC,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,cAAc,EAAE,MAAM,CAAC,cAAc;oBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,SAAS,EAAE,MAAM,CAAC,cAAc,KAAK,QAAQ,CAAC,aAAa;oBAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpC,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBAElC,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBACpC,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC;gBACpC,CAAC;gBAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAC/C,sBAAO,QAAQ,EAAC;;;KACjB;IAED;;OAEG;IACG,8DAA8B,GAApC;0CAAyH,OAAO,YAA3F,YAAoB,EAAE,MAA4B,EAAE,OAA6B;;YAA7B,wBAAA,EAAA,YAA6B;;;;;wBAEjG,qBAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC,EAAA;;wBAA1D,QAAQ,GAAG,SAA+C;wBAChE,sBAAO;gCACL,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE,QAAQ;gCACd,cAAc,EAAE,MAAM;6BACvB,EAAC;;;wBAEF,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gCAC/D,SAAS,EAAE,cAAc;gCACzB,SAAS,EAAE,IAAI;gCACf,YAAY,EAAE,IAAI;6BACnB,EAAC;;;;;KAEL;IAEK,gDAAgB,GAAtB,UAAuB,YAAoB;uCAAG,OAAO;;;gBAC7C,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,CAAC;gBAEK,WAAW,GAA2B,EAAE,CAAC;gBACzC,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,iBAAiB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAkB,CAAC;oCAG5F,OAAO;oBAChB,IAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,KAAK,OAAO,EAArB,CAAqB,CAAC,CAAC;oBAC/E,IAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC;wBAClD,OAAA,cAAc,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,UAAU,EAArB,CAAqB,CAAC;oBAA/C,CAA+C,CAChD,CAAC;oBAEF,IAAI,WAAW,GAAG,CAAC,CAAC;oBACpB,IAAI,aAAa,GAAG,CAAC,CAAC;4CAEX,QAAQ;wBACjB,IAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,UAAU,EAA5B,CAA4B,CAAE,CAAC;wBACzE,IAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;wBAC3D,WAAW,IAAI,MAAM,CAAC;wBACtB,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;4BACvB,aAAa,IAAI,MAAM,CAAC;wBAC1B,CAAC;;oBANH,KAAuB,UAAc,EAAd,iCAAc,EAAd,4BAAc,EAAd,IAAc;wBAAhC,IAAM,QAAQ,uBAAA;gCAAR,QAAQ;qBAOlB;oBAED,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAnBnF,kCAAkC;gBAClC,WAAyC,EAAnB,KAAA,UAAU,CAAC,QAAQ,EAAnB,cAAmB,EAAnB,IAAmB;oBAA9B,OAAO;4BAAP,OAAO;iBAmBjB;gBAGK,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,EAAX,CAAW,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;gBAGnH,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,KAAK,SAAS,EAA1B,CAA0B,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAW,EAAb,CAAa,CAAC,CAAC;gBACxG,iBAAiB,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC;oBACnD,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI,IAAK,OAAA,GAAG,GAAG,IAAI,EAAV,CAAU,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM;oBACjF,CAAC,CAAC,SAAS,CAAC;gBAER,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,SAAS,EAAjB,CAAiB,EAAE,CAAC,CAAC,CAAC;gBAC/E,sBAAsB,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;oBAC5D,CAAC,CAAC,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM;oBAC9C,CAAC,CAAC,CAAC,CAAC;gBAEA,OAAO,GAAsB;oBACjC,YAAY,cAAA;oBACZ,WAAW,aAAA;oBACX,YAAY,cAAA;oBACZ,iBAAiB,mBAAA;oBACjB,sBAAsB,wBAAA;oBACtB,cAAc,gBAAA;oBACd,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,YAAY,CAAC;iBACzE,CAAC;gBAEF,2BAA2B;gBAC3B,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;gBAChC,UAAU,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;gBAC7C,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAE/C,sBAAO,OAAO,EAAC;;;KAChB;IAED,6CAAa,GAAb,UAAc,YAAoB;QAChC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;IACpD,CAAC;IAED,oDAAoB,GAApB,UAAqB,MAAc;QACjC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,MAAM,EAAnB,CAAmB,CAAC,CAAC;IAChF,CAAC;IAED,2BAA2B;IAC3B,iDAAiB,GAAjB;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEK,sDAAsB,GAA5B,UAA6B,YAAoB,EAAE,MAA4B;uCAAG,OAAO;;;gBACjF,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,CAAC;gBAED,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC3B,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;;;;KAChD;IAEa,wDAAwB,GAAtC,UAAuC,OAAe,EAAE,UAAkB;uCAAG,OAAO;;;gBAG5E,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBACnE,QAAQ,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;gBAEzF,sBAAO;wBACL,EAAE,EAAE,UAAG,IAAA,SAAM,GAAE,cAAI,OAAO,cAAI,UAAU,cAAI,IAAI,CAAC,GAAG,EAAE,cAAI,IAAI,CAAC,MAAM,EAAE,CAAE;wBACzE,OAAO,SAAA;wBACP,QAAQ,EAAE,QAAQ,CAAC,QAAQ;wBAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;wBACzB,aAAa,EAAE,QAAQ,CAAC,aAAa;wBACrC,UAAU,YAAA;wBACV,WAAW,EAAE,QAAQ,CAAC,WAAW;qBAClC,EAAC;;;KACH;IAEO,oDAAoB,GAA5B,UAA6B,OAAe,EAAE,UAAkB;QAC9D,4CAA4C;QAC5C,IAAM,SAAS,GAAG;YAChB,UAAU,EAAE;gBACV,QAAQ,EAAE;oBACR;wBACE,QAAQ,EAAE,8DAA8D;wBACxE,OAAO,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,gBAAgB,CAAC;wBACxE,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,+DAA+D;qBAC7E;oBACD;wBACE,QAAQ,EAAE,mDAAmD;wBAC7D,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,kBAAkB,CAAC;wBAC5D,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,oFAAoF;qBAClG;iBACF;gBACD,YAAY,EAAE;oBACZ;wBACE,QAAQ,EAAE,sDAAsD;wBAChE,OAAO,EAAE,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,iCAAiC,EAAE,mBAAmB,CAAC;wBAC9G,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,kEAAkE;qBAChF;iBACF;gBACD,QAAQ,EAAE;oBACR;wBACE,QAAQ,EAAE,kCAAkC;wBAC5C,OAAO,EAAE,CAAC,oCAAoC,EAAE,0BAA0B,EAAE,2CAA2C,EAAE,gBAAgB,CAAC;wBAC1I,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,iIAAiI;qBAC/I;iBACF;aACF;YACD,KAAK,EAAE;gBACL,QAAQ,EAAE;oBACR;wBACE,QAAQ,EAAE,cAAc;wBACxB,OAAO,EAAE,CAAC,sBAAsB,EAAE,mCAAmC,EAAE,iBAAiB,EAAE,YAAY,CAAC;wBACvG,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,4GAA4G;qBAC1H;iBACF;gBACD,YAAY,EAAE;oBACZ;wBACE,QAAQ,EAAE,uCAAuC;wBACjD,OAAO,EAAE,CAAC,2BAA2B,EAAE,wBAAwB,EAAE,yBAAyB,EAAE,sBAAsB,CAAC;wBACnH,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,iFAAiF;qBAC/F;iBACF;gBACD,QAAQ,EAAE;oBACR;wBACE,QAAQ,EAAE,+DAA+D;wBACzE,OAAO,EAAE,CAAC,eAAe,EAAE,oCAAoC,EAAE,8BAA8B,EAAE,+BAA+B,CAAC;wBACjI,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,2FAA2F;qBACzG;iBACF;aACF;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE;oBACR;wBACE,QAAQ,EAAE,kBAAkB;wBAC5B,OAAO,EAAE,CAAC,eAAe,EAAE,sBAAsB,EAAE,YAAY,EAAE,iBAAiB,CAAC;wBACnF,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,yEAAyE;qBACvF;iBACF;gBACD,YAAY,EAAE;oBACZ;wBACE,QAAQ,EAAE,cAAc;wBACxB,OAAO,EAAE,CAAC,sBAAsB,EAAE,wBAAwB,EAAE,0BAA0B,EAAE,sBAAsB,CAAC;wBAC/G,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,qFAAqF;qBACnG;iBACF;gBACD,QAAQ,EAAE;oBACR;wBACE,QAAQ,EAAE,oCAAoC;wBAC9C,OAAO,EAAE,CAAC,4BAA4B,EAAE,kCAAkC,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;wBACxH,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,wHAAwH;qBACtI;iBACF;aACF;SACF,CAAC;QAEF,IAAM,cAAc,GAAG,SAAS,CAAC,OAAiC,CAAC,CAAC;QACpE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,CAAC;oBACN,QAAQ,EAAE,iBAAU,UAAU,2BAAiB,OAAO,CAAE;oBACxD,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;oBACzD,aAAa,EAAE,CAAC;oBAChB,WAAW,EAAE,4BAA4B;iBAC1C,CAAC,CAAC;QACL,CAAC;QAED,OAAO,cAAc,CAAC,UAAyC,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC;IAC9F,CAAC;IAEO,4CAAY,GAApB,UAAwB,KAAU;;QAChC,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9C,KAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAA1C,KAAK,CAAC,CAAC,CAAC,QAAA,EAAE,KAAK,CAAC,CAAC,CAAC,QAAA,CAAyB;QAC9C,CAAC;IACH,CAAC;IAEO,uDAAuB,GAA/B,UAAgC,WAAmC,EAAE,YAAoB;QACvF,IAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;YACtB,eAAe,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;QAC7G,CAAC;aAAM,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QACvF,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QACvF,CAAC;QAED,qCAAqC;QACrC,KAA6B,UAA2B,EAA3B,KAAA,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAA3B,cAA2B,EAA3B,IAA2B,EAAE,CAAC;YAAhD,IAAA,WAAc,EAAb,KAAK,QAAA,EAAE,KAAK,QAAA;YACtB,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBACf,eAAe,CAAC,IAAI,CAAC,4CAAqC,KAAK,mBAAgB,CAAC,CAAC;YACnF,CAAC;iBAAM,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBACtB,eAAe,CAAC,IAAI,CAAC,iBAAU,KAAK,sEAAmE,CAAC,CAAC;YAC3G,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IACH,4BAAC;AAAD,CAAC,AAjYD,IAiYC;AAjYY,sDAAqB",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillAssessmentEngine.ts"],
      sourcesContent: ["import { v4 as uuidv4 } from 'uuid';\nimport { EdgeCaseHandler, EdgeCaseResult, EdgeCaseOptions } from './EdgeCaseHandler';\n\nexport interface AssessmentConfig {\n  questionsPerSkill?: number;\n  difficultyLevels?: string[];\n  timeLimit?: number;\n  randomizeQuestions?: boolean;\n  difficultyWeights?: Record<string, number>;\n}\n\nexport interface AssessmentQuestion {\n  id: string;\n  skillId: string;\n  question: string;\n  options: string[];\n  correctAnswer: number;\n  difficulty: string;\n  explanation?: string;\n}\n\nexport interface AssessmentResponse {\n  questionId: string;\n  selectedAnswer: number;\n  timeSpent: number;\n  confidence?: number;\n  isCorrect: boolean;\n  timestamp: Date;\n}\n\nexport interface Assessment {\n  id: string;\n  userId: string;\n  careerPathId: string;\n  skillIds: string[];\n  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';\n  config: AssessmentConfig;\n  questions: AssessmentQuestion[];\n  responses: AssessmentResponse[];\n  createdAt: Date;\n  updatedAt: Date;\n  completedAt?: Date;\n}\n\nexport interface AssessmentResults {\n  assessmentId: string;\n  skillScores: Record<string, number>;\n  overallScore: number;\n  averageConfidence?: number;\n  averageTimePerQuestion: number;\n  totalTimeSpent: number;\n  completedAt: Date;\n  recommendations: string[];\n}\n\nexport interface CreateAssessmentParams {\n  userId: string;\n  careerPathId: string;\n  skillIds: string[];\n  config?: AssessmentConfig;\n}\n\nexport interface RecordResponseParams {\n  questionId: string;\n  selectedAnswer: number;\n  timeSpent: number;\n  confidence?: number;\n}\n\nexport class SkillAssessmentEngine {\n  private assessments: Map<string, Assessment> = new Map();\n  private edgeCaseHandler: EdgeCaseHandler;\n  private defaultConfig: AssessmentConfig = {\n    questionsPerSkill: 5,\n    difficultyLevels: ['beginner', 'intermediate', 'advanced'],\n    timeLimit: 1800, // 30 minutes\n    randomizeQuestions: false,\n    difficultyWeights: {\n      beginner: 1,\n      intermediate: 2,\n      advanced: 3,\n    },\n  };\n\n  constructor() {\n    // EdgeCaseHandler will be initialized later when services are available\n    this.edgeCaseHandler = null as any;\n  }\n\n  setEdgeCaseHandler(handler: EdgeCaseHandler) {\n    this.edgeCaseHandler = handler;\n  }\n\n  createAssessment(params: CreateAssessmentParams): Assessment {\n    if (!params.userId || !params.careerPathId || !params.skillIds.length) {\n      throw new Error('Invalid assessment parameters');\n    }\n\n    const assessment: Assessment = {\n      id: uuidv4(),\n      userId: params.userId,\n      careerPathId: params.careerPathId,\n      skillIds: params.skillIds,\n      status: 'pending',\n      config: { ...this.defaultConfig, ...params.config },\n      questions: [],\n      responses: [],\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    this.assessments.set(assessment.id, assessment);\n    return assessment;\n  }\n\n  /**\n   * Create assessment with comprehensive edge case handling\n   */\n  async createAssessmentWithEdgeHandling(params: CreateAssessmentParams, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult<Assessment>> {\n    return this.edgeCaseHandler.handleSkillAssessment(params, options);\n  }\n\n  async generateQuestions(assessmentId: string): Promise<AssessmentQuestion[]> {\n    const assessment = this.getAssessment(assessmentId);\n    if (!assessment) {\n      throw new Error('Assessment not found');\n    }\n\n    const questions: AssessmentQuestion[] = [];\n    const questionsPerSkill = assessment.config.questionsPerSkill || 5;\n    const difficultyLevels = assessment.config.difficultyLevels || ['beginner', 'intermediate', 'advanced'];\n\n    for (const skillId of assessment.skillIds) {\n      for (let i = 0; i < questionsPerSkill; i++) {\n        const difficulty = difficultyLevels[i % difficultyLevels.length];\n        const question = await this.generateQuestionForSkill(skillId, difficulty);\n        questions.push(question);\n      }\n    }\n\n    if (assessment.config.randomizeQuestions) {\n      this.shuffleArray(questions);\n    }\n\n    assessment.questions = questions;\n    assessment.updatedAt = new Date();\n    this.assessments.set(assessmentId, assessment);\n\n    return questions;\n  }\n\n  async recordResponse(assessmentId: string, params: RecordResponseParams): Promise<AssessmentResponse> {\n    const assessment = this.getAssessment(assessmentId);\n    if (!assessment) {\n      throw new Error('Assessment not found');\n    }\n\n    if (assessment.status === 'completed') {\n      throw new Error('Cannot modify completed assessment');\n    }\n\n    const question = assessment.questions.find(q => q.id === params.questionId);\n    if (!question) {\n      throw new Error('Invalid question ID');\n    }\n\n    if (params.selectedAnswer < 0 || params.selectedAnswer >= question.options.length) {\n      throw new Error('Invalid answer selection');\n    }\n\n    const existingResponse = assessment.responses.find(r => r.questionId === params.questionId);\n    if (existingResponse) {\n      throw new Error('Question already answered');\n    }\n\n    const response: AssessmentResponse = {\n      questionId: params.questionId,\n      selectedAnswer: params.selectedAnswer,\n      timeSpent: params.timeSpent,\n      confidence: params.confidence,\n      isCorrect: params.selectedAnswer === question.correctAnswer,\n      timestamp: new Date(),\n    };\n\n    assessment.responses.push(response);\n    assessment.updatedAt = new Date();\n\n    if (assessment.status === 'pending') {\n      assessment.status = 'in_progress';\n    }\n\n    this.assessments.set(assessmentId, assessment);\n    return response;\n  }\n\n  /**\n   * Record response with comprehensive edge case handling\n   */\n  async recordResponseWithEdgeHandling(assessmentId: string, params: RecordResponseParams, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult<AssessmentResponse>> {\n    try {\n      const response = await this.recordResponse(assessmentId, params);\n      return {\n        success: true,\n        data: response,\n        sanitizedInput: params\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        errorType: 'SYSTEM_ERROR',\n        retryable: true,\n        fallbackData: null\n      };\n    }\n  }\n\n  async calculateResults(assessmentId: string): Promise<AssessmentResults> {\n    const assessment = this.getAssessment(assessmentId);\n    if (!assessment) {\n      throw new Error('Assessment not found');\n    }\n\n    const skillScores: Record<string, number> = {};\n    const difficultyWeights = assessment.config.difficultyWeights || this.defaultConfig.difficultyWeights!;\n\n    // Calculate scores for each skill\n    for (const skillId of assessment.skillIds) {\n      const skillQuestions = assessment.questions.filter(q => q.skillId === skillId);\n      const skillResponses = assessment.responses.filter(r => \n        skillQuestions.some(q => q.id === r.questionId)\n      );\n\n      let totalWeight = 0;\n      let weightedScore = 0;\n\n      for (const response of skillResponses) {\n        const question = skillQuestions.find(q => q.id === response.questionId)!;\n        const weight = difficultyWeights[question.difficulty] || 1;\n        totalWeight += weight;\n        if (response.isCorrect) {\n          weightedScore += weight;\n        }\n      }\n\n      skillScores[skillId] = totalWeight > 0 ? (weightedScore / totalWeight) * 100 : 0;\n    }\n\n    // Calculate overall score\n    const overallScore = Object.values(skillScores).reduce((sum, score) => sum + score, 0) / Object.keys(skillScores).length;\n\n    // Calculate confidence and time metrics\n    const confidenceValues = assessment.responses.filter(r => r.confidence !== undefined).map(r => r.confidence!);\n    const averageConfidence = confidenceValues.length > 0 \n      ? confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length \n      : undefined;\n\n    const totalTimeSpent = assessment.responses.reduce((sum, r) => sum + r.timeSpent, 0);\n    const averageTimePerQuestion = assessment.responses.length > 0 \n      ? totalTimeSpent / assessment.responses.length \n      : 0;\n\n    const results: AssessmentResults = {\n      assessmentId,\n      skillScores,\n      overallScore,\n      averageConfidence,\n      averageTimePerQuestion,\n      totalTimeSpent,\n      completedAt: new Date(),\n      recommendations: this.generateRecommendations(skillScores, overallScore),\n    };\n\n    // Update assessment status\n    assessment.status = 'completed';\n    assessment.completedAt = results.completedAt;\n    assessment.updatedAt = new Date();\n    this.assessments.set(assessmentId, assessment);\n\n    return results;\n  }\n\n  getAssessment(assessmentId: string): Assessment | null {\n    return this.assessments.get(assessmentId) || null;\n  }\n\n  getAssessmentsByUser(userId: string): Assessment[] {\n    return Array.from(this.assessments.values()).filter(a => a.userId === userId);\n  }\n\n  // Debug method for testing\n  getAllAssessments(): Assessment[] {\n    return Array.from(this.assessments.values());\n  }\n\n  async updateAssessmentStatus(assessmentId: string, status: Assessment['status']): Promise<void> {\n    const assessment = this.getAssessment(assessmentId);\n    if (!assessment) {\n      throw new Error('Assessment not found');\n    }\n\n    assessment.status = status;\n    assessment.updatedAt = new Date();\n    this.assessments.set(assessmentId, assessment);\n  }\n\n  private async generateQuestionForSkill(skillId: string, difficulty: string): Promise<AssessmentQuestion> {\n    // This is a simplified implementation. In a real system, this would\n    // fetch questions from a database or generate them using AI\n    const questionTemplates = this.getQuestionTemplates(skillId, difficulty);\n    const template = questionTemplates[Math.floor(Math.random() * questionTemplates.length)];\n\n    return {\n      id: `${uuidv4()}-${skillId}-${difficulty}-${Date.now()}-${Math.random()}`,\n      skillId,\n      question: template.question,\n      options: template.options,\n      correctAnswer: template.correctAnswer,\n      difficulty,\n      explanation: template.explanation,\n    };\n  }\n\n  private getQuestionTemplates(skillId: string, difficulty: string) {\n    // Simplified question templates for testing\n    const templates = {\n      javascript: {\n        beginner: [\n          {\n            question: \"What is the correct way to declare a variable in JavaScript?\",\n            options: [\"var x = 5;\", \"variable x = 5;\", \"v x = 5;\", \"declare x = 5;\"],\n            correctAnswer: 0,\n            explanation: \"The 'var' keyword is used to declare variables in JavaScript.\"\n          },\n          {\n            question: \"Which of the following is a JavaScript data type?\",\n            options: [\"string\", \"boolean\", \"number\", \"all of the above\"],\n            correctAnswer: 3,\n            explanation: \"JavaScript has several primitive data types including string, boolean, and number.\"\n          }\n        ],\n        intermediate: [\n          {\n            question: \"What does the 'this' keyword refer to in JavaScript?\",\n            options: [\"The current function\", \"The global object\", \"The object that owns the method\", \"The parent object\"],\n            correctAnswer: 2,\n            explanation: \"'this' refers to the object that owns the method being executed.\"\n          }\n        ],\n        advanced: [\n          {\n            question: \"What is a closure in JavaScript?\",\n            options: [\"A function inside another function\", \"A way to close a program\", \"A function that has access to outer scope\", \"A type of loop\"],\n            correctAnswer: 2,\n            explanation: \"A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.\"\n          }\n        ]\n      },\n      react: {\n        beginner: [\n          {\n            question: \"What is JSX?\",\n            options: [\"A JavaScript library\", \"A syntax extension for JavaScript\", \"A CSS framework\", \"A database\"],\n            correctAnswer: 1,\n            explanation: \"JSX is a syntax extension for JavaScript that allows you to write HTML-like code in your JavaScript files.\"\n          }\n        ],\n        intermediate: [\n          {\n            question: \"What is the purpose of useState hook?\",\n            options: [\"To manage component state\", \"To handle side effects\", \"To optimize performance\", \"To create components\"],\n            correctAnswer: 0,\n            explanation: \"useState is a React hook that allows you to add state to functional components.\"\n          }\n        ],\n        advanced: [\n          {\n            question: \"What is the difference between useEffect and useLayoutEffect?\",\n            options: [\"No difference\", \"useLayoutEffect runs synchronously\", \"useEffect runs synchronously\", \"useLayoutEffect is deprecated\"],\n            correctAnswer: 1,\n            explanation: \"useLayoutEffect runs synchronously after all DOM mutations but before the browser paints.\"\n          }\n        ]\n      },\n      nodejs: {\n        beginner: [\n          {\n            question: \"What is Node.js?\",\n            options: [\"A web browser\", \"A JavaScript runtime\", \"A database\", \"A CSS framework\"],\n            correctAnswer: 1,\n            explanation: \"Node.js is a JavaScript runtime built on Chrome's V8 JavaScript engine.\"\n          }\n        ],\n        intermediate: [\n          {\n            question: \"What is npm?\",\n            options: [\"Node Package Manager\", \"New Programming Method\", \"Network Protocol Manager\", \"Node Process Manager\"],\n            correctAnswer: 0,\n            explanation: \"npm stands for Node Package Manager and is the default package manager for Node.js.\"\n          }\n        ],\n        advanced: [\n          {\n            question: \"What is the event loop in Node.js?\",\n            options: [\"A loop that handles events\", \"A mechanism for non-blocking I/O\", \"A way to create loops\", \"A debugging tool\"],\n            correctAnswer: 1,\n            explanation: \"The event loop is what allows Node.js to perform non-blocking I/O operations despite JavaScript being single-threaded.\"\n          }\n        ]\n      }\n    };\n\n    const skillTemplates = templates[skillId as keyof typeof templates];\n    if (!skillTemplates) {\n      return [{\n        question: `Sample ${difficulty} question for ${skillId}`,\n        options: [\"Option A\", \"Option B\", \"Option C\", \"Option D\"],\n        correctAnswer: 0,\n        explanation: \"This is a sample question.\"\n      }];\n    }\n\n    return skillTemplates[difficulty as keyof typeof skillTemplates] || skillTemplates.beginner;\n  }\n\n  private shuffleArray<T>(array: T[]): void {\n    for (let i = array.length - 1; i > 0; i--) {\n      const j = Math.floor(Math.random() * (i + 1));\n      [array[i], array[j]] = [array[j], array[i]];\n    }\n  }\n\n  private generateRecommendations(skillScores: Record<string, number>, overallScore: number): string[] {\n    const recommendations: string[] = [];\n\n    if (overallScore < 50) {\n      recommendations.push(\"Consider focusing on fundamental concepts before advancing to more complex topics.\");\n    } else if (overallScore < 75) {\n      recommendations.push(\"Good foundation! Focus on practicing more advanced concepts.\");\n    } else {\n      recommendations.push(\"Excellent performance! You're ready for advanced challenges.\");\n    }\n\n    // Add skill-specific recommendations\n    for (const [skill, score] of Object.entries(skillScores)) {\n      if (score < 60) {\n        recommendations.push(`Consider additional practice with ${skill} fundamentals.`);\n      } else if (score > 85) {\n        recommendations.push(`Strong ${skill} skills! Consider mentoring others or taking on leadership roles.`);\n      }\n    }\n\n    return recommendations;\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "53de580f14d59d2083abad6435ed7b3748b6788a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_mwcv5ulzt = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_mwcv5ulzt();
var __assign =
/* istanbul ignore next */
(cov_mwcv5ulzt().s[0]++,
/* istanbul ignore next */
(cov_mwcv5ulzt().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_mwcv5ulzt().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_mwcv5ulzt().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_mwcv5ulzt().f[0]++;
  cov_mwcv5ulzt().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_mwcv5ulzt().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_mwcv5ulzt().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[1]++;
    cov_mwcv5ulzt().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_mwcv5ulzt().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_mwcv5ulzt().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[2][0]++;
          cov_mwcv5ulzt().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_mwcv5ulzt().s[11]++,
/* istanbul ignore next */
(cov_mwcv5ulzt().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_mwcv5ulzt().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_mwcv5ulzt().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_mwcv5ulzt().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[3]++;
    cov_mwcv5ulzt().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[4]++;
      cov_mwcv5ulzt().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_mwcv5ulzt().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_mwcv5ulzt().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[6]++;
      cov_mwcv5ulzt().s[15]++;
      try {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[7]++;
      cov_mwcv5ulzt().s[18]++;
      try {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[8]++;
      cov_mwcv5ulzt().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_mwcv5ulzt().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_mwcv5ulzt().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_mwcv5ulzt().s[23]++,
/* istanbul ignore next */
(cov_mwcv5ulzt().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_mwcv5ulzt().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_mwcv5ulzt().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_mwcv5ulzt().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_mwcv5ulzt().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[10]++;
        cov_mwcv5ulzt().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[9][0]++;
          cov_mwcv5ulzt().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[9][1]++;
        }
        cov_mwcv5ulzt().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_mwcv5ulzt().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_mwcv5ulzt().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_mwcv5ulzt().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[11]++;
    cov_mwcv5ulzt().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[12]++;
    cov_mwcv5ulzt().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[13]++;
      cov_mwcv5ulzt().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[14]++;
    cov_mwcv5ulzt().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().b[12][0]++;
      cov_mwcv5ulzt().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_mwcv5ulzt().b[12][1]++;
    }
    cov_mwcv5ulzt().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[36]++;
      try {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[18][0]++,
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[19][1]++,
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[15][0]++;
          cov_mwcv5ulzt().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[15][1]++;
        }
        cov_mwcv5ulzt().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[21][0]++;
          cov_mwcv5ulzt().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[21][1]++;
        }
        cov_mwcv5ulzt().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[22][1]++;
            cov_mwcv5ulzt().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[22][2]++;
            cov_mwcv5ulzt().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[22][3]++;
            cov_mwcv5ulzt().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[22][4]++;
            cov_mwcv5ulzt().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[22][5]++;
            cov_mwcv5ulzt().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[23][0]++;
              cov_mwcv5ulzt().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_mwcv5ulzt().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[23][1]++;
            }
            cov_mwcv5ulzt().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[26][0]++;
              cov_mwcv5ulzt().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_mwcv5ulzt().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[26][1]++;
            }
            cov_mwcv5ulzt().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[28][0]++;
              cov_mwcv5ulzt().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_mwcv5ulzt().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_mwcv5ulzt().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[28][1]++;
            }
            cov_mwcv5ulzt().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[30][0]++;
              cov_mwcv5ulzt().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_mwcv5ulzt().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_mwcv5ulzt().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[30][1]++;
            }
            cov_mwcv5ulzt().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[32][0]++;
              cov_mwcv5ulzt().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[32][1]++;
            }
            cov_mwcv5ulzt().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().b[33][0]++;
      cov_mwcv5ulzt().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_mwcv5ulzt().b[33][1]++;
    }
    cov_mwcv5ulzt().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_mwcv5ulzt().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_mwcv5ulzt().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_mwcv5ulzt().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_mwcv5ulzt().s[79]++;
exports.SkillAssessmentEngine = void 0;
var uuid_1 =
/* istanbul ignore next */
(cov_mwcv5ulzt().s[80]++, require("uuid"));
var SkillAssessmentEngine =
/* istanbul ignore next */
(/** @class */cov_mwcv5ulzt().s[81]++, function () {
  /* istanbul ignore next */
  cov_mwcv5ulzt().f[15]++;
  function SkillAssessmentEngine() {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[16]++;
    cov_mwcv5ulzt().s[82]++;
    this.assessments = new Map();
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[83]++;
    this.defaultConfig = {
      questionsPerSkill: 5,
      difficultyLevels: ['beginner', 'intermediate', 'advanced'],
      timeLimit: 1800,
      // 30 minutes
      randomizeQuestions: false,
      difficultyWeights: {
        beginner: 1,
        intermediate: 2,
        advanced: 3
      }
    };
    // EdgeCaseHandler will be initialized later when services are available
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[84]++;
    this.edgeCaseHandler = null;
  }
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[85]++;
  SkillAssessmentEngine.prototype.setEdgeCaseHandler = function (handler) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[17]++;
    cov_mwcv5ulzt().s[86]++;
    this.edgeCaseHandler = handler;
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[87]++;
  SkillAssessmentEngine.prototype.createAssessment = function (params) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[18]++;
    cov_mwcv5ulzt().s[88]++;
    if (
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[36][0]++, !params.userId) ||
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[36][1]++, !params.careerPathId) ||
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[36][2]++, !params.skillIds.length)) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().b[35][0]++;
      cov_mwcv5ulzt().s[89]++;
      throw new Error('Invalid assessment parameters');
    } else
    /* istanbul ignore next */
    {
      cov_mwcv5ulzt().b[35][1]++;
    }
    var assessment =
    /* istanbul ignore next */
    (cov_mwcv5ulzt().s[90]++, {
      id: (0, uuid_1.v4)(),
      userId: params.userId,
      careerPathId: params.careerPathId,
      skillIds: params.skillIds,
      status: 'pending',
      config: __assign(__assign({}, this.defaultConfig), params.config),
      questions: [],
      responses: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[91]++;
    this.assessments.set(assessment.id, assessment);
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[92]++;
    return assessment;
  };
  /**
   * Create assessment with comprehensive edge case handling
   */
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[93]++;
  SkillAssessmentEngine.prototype.createAssessmentWithEdgeHandling = function (params_1) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[19]++;
    cov_mwcv5ulzt().s[94]++;
    return __awaiter(this, arguments, Promise, function (params, options) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[20]++;
      cov_mwcv5ulzt().s[95]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().b[37][0]++;
        cov_mwcv5ulzt().s[96]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_mwcv5ulzt().b[37][1]++;
      }
      cov_mwcv5ulzt().s[97]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[21]++;
        cov_mwcv5ulzt().s[98]++;
        return [2 /*return*/, this.edgeCaseHandler.handleSkillAssessment(params, options)];
      });
    });
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[99]++;
  SkillAssessmentEngine.prototype.generateQuestions = function (assessmentId) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[22]++;
    cov_mwcv5ulzt().s[100]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[23]++;
      var assessment, questions, questionsPerSkill, difficultyLevels, _i, _a, skillId, i, difficulty, question;
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[101]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[24]++;
        cov_mwcv5ulzt().s[102]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[38][0]++;
            cov_mwcv5ulzt().s[103]++;
            assessment = this.getAssessment(assessmentId);
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[104]++;
            if (!assessment) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[39][0]++;
              cov_mwcv5ulzt().s[105]++;
              throw new Error('Assessment not found');
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[39][1]++;
            }
            cov_mwcv5ulzt().s[106]++;
            questions = [];
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[107]++;
            questionsPerSkill =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[40][0]++, assessment.config.questionsPerSkill) ||
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[40][1]++, 5);
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[108]++;
            difficultyLevels =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[41][0]++, assessment.config.difficultyLevels) ||
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[41][1]++, ['beginner', 'intermediate', 'advanced']);
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[109]++;
            _i = 0, _a = assessment.skillIds;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[110]++;
            _b.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[38][1]++;
            cov_mwcv5ulzt().s[111]++;
            if (!(_i < _a.length)) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[42][0]++;
              cov_mwcv5ulzt().s[112]++;
              return [3 /*break*/, 6];
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[42][1]++;
            }
            cov_mwcv5ulzt().s[113]++;
            skillId = _a[_i];
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[114]++;
            i = 0;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[115]++;
            _b.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[38][2]++;
            cov_mwcv5ulzt().s[116]++;
            if (!(i < questionsPerSkill)) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[43][0]++;
              cov_mwcv5ulzt().s[117]++;
              return [3 /*break*/, 5];
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[43][1]++;
            }
            cov_mwcv5ulzt().s[118]++;
            difficulty = difficultyLevels[i % difficultyLevels.length];
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[119]++;
            return [4 /*yield*/, this.generateQuestionForSkill(skillId, difficulty)];
          case 3:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[38][3]++;
            cov_mwcv5ulzt().s[120]++;
            question = _b.sent();
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[121]++;
            questions.push(question);
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[122]++;
            _b.label = 4;
          case 4:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[38][4]++;
            cov_mwcv5ulzt().s[123]++;
            i++;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[124]++;
            return [3 /*break*/, 2];
          case 5:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[38][5]++;
            cov_mwcv5ulzt().s[125]++;
            _i++;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[126]++;
            return [3 /*break*/, 1];
          case 6:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[38][6]++;
            cov_mwcv5ulzt().s[127]++;
            if (assessment.config.randomizeQuestions) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[44][0]++;
              cov_mwcv5ulzt().s[128]++;
              this.shuffleArray(questions);
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[44][1]++;
            }
            cov_mwcv5ulzt().s[129]++;
            assessment.questions = questions;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[130]++;
            assessment.updatedAt = new Date();
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[131]++;
            this.assessments.set(assessmentId, assessment);
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[132]++;
            return [2 /*return*/, questions];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[133]++;
  SkillAssessmentEngine.prototype.recordResponse = function (assessmentId, params) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[25]++;
    cov_mwcv5ulzt().s[134]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[26]++;
      var assessment, question, existingResponse, response;
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[135]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[27]++;
        cov_mwcv5ulzt().s[136]++;
        assessment = this.getAssessment(assessmentId);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[137]++;
        if (!assessment) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[45][0]++;
          cov_mwcv5ulzt().s[138]++;
          throw new Error('Assessment not found');
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[45][1]++;
        }
        cov_mwcv5ulzt().s[139]++;
        if (assessment.status === 'completed') {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[46][0]++;
          cov_mwcv5ulzt().s[140]++;
          throw new Error('Cannot modify completed assessment');
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[46][1]++;
        }
        cov_mwcv5ulzt().s[141]++;
        question = assessment.questions.find(function (q) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[28]++;
          cov_mwcv5ulzt().s[142]++;
          return q.id === params.questionId;
        });
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[143]++;
        if (!question) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[47][0]++;
          cov_mwcv5ulzt().s[144]++;
          throw new Error('Invalid question ID');
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[47][1]++;
        }
        cov_mwcv5ulzt().s[145]++;
        if (
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[49][0]++, params.selectedAnswer < 0) ||
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[49][1]++, params.selectedAnswer >= question.options.length)) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[48][0]++;
          cov_mwcv5ulzt().s[146]++;
          throw new Error('Invalid answer selection');
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[48][1]++;
        }
        cov_mwcv5ulzt().s[147]++;
        existingResponse = assessment.responses.find(function (r) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[29]++;
          cov_mwcv5ulzt().s[148]++;
          return r.questionId === params.questionId;
        });
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[149]++;
        if (existingResponse) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[50][0]++;
          cov_mwcv5ulzt().s[150]++;
          throw new Error('Question already answered');
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[50][1]++;
        }
        cov_mwcv5ulzt().s[151]++;
        response = {
          questionId: params.questionId,
          selectedAnswer: params.selectedAnswer,
          timeSpent: params.timeSpent,
          confidence: params.confidence,
          isCorrect: params.selectedAnswer === question.correctAnswer,
          timestamp: new Date()
        };
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[152]++;
        assessment.responses.push(response);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[153]++;
        assessment.updatedAt = new Date();
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[154]++;
        if (assessment.status === 'pending') {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[51][0]++;
          cov_mwcv5ulzt().s[155]++;
          assessment.status = 'in_progress';
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[51][1]++;
        }
        cov_mwcv5ulzt().s[156]++;
        this.assessments.set(assessmentId, assessment);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[157]++;
        return [2 /*return*/, response];
      });
    });
  };
  /**
   * Record response with comprehensive edge case handling
   */
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[158]++;
  SkillAssessmentEngine.prototype.recordResponseWithEdgeHandling = function (assessmentId_1, params_1) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[30]++;
    cov_mwcv5ulzt().s[159]++;
    return __awaiter(this, arguments, Promise, function (assessmentId, params, options) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[31]++;
      var response, error_1;
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[160]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().b[52][0]++;
        cov_mwcv5ulzt().s[161]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_mwcv5ulzt().b[52][1]++;
      }
      cov_mwcv5ulzt().s[162]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[32]++;
        cov_mwcv5ulzt().s[163]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[53][0]++;
            cov_mwcv5ulzt().s[164]++;
            _a.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[165]++;
            return [4 /*yield*/, this.recordResponse(assessmentId, params)];
          case 1:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[53][1]++;
            cov_mwcv5ulzt().s[166]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[167]++;
            return [2 /*return*/, {
              success: true,
              data: response,
              sanitizedInput: params
            }];
          case 2:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[53][2]++;
            cov_mwcv5ulzt().s[168]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[169]++;
            return [2 /*return*/, {
              success: false,
              error: error_1 instanceof Error ?
              /* istanbul ignore next */
              (cov_mwcv5ulzt().b[54][0]++, error_1.message) :
              /* istanbul ignore next */
              (cov_mwcv5ulzt().b[54][1]++, 'Unknown error'),
              errorType: 'SYSTEM_ERROR',
              retryable: true,
              fallbackData: null
            }];
          case 3:
            /* istanbul ignore next */
            cov_mwcv5ulzt().b[53][3]++;
            cov_mwcv5ulzt().s[170]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[171]++;
  SkillAssessmentEngine.prototype.calculateResults = function (assessmentId) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[33]++;
    cov_mwcv5ulzt().s[172]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[34]++;
      var assessment, skillScores, difficultyWeights, _loop_1, _i, _a, skillId, overallScore, confidenceValues, averageConfidence, totalTimeSpent, averageTimePerQuestion, results;
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[173]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[35]++;
        cov_mwcv5ulzt().s[174]++;
        assessment = this.getAssessment(assessmentId);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[175]++;
        if (!assessment) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[55][0]++;
          cov_mwcv5ulzt().s[176]++;
          throw new Error('Assessment not found');
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[55][1]++;
        }
        cov_mwcv5ulzt().s[177]++;
        skillScores = {};
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[178]++;
        difficultyWeights =
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[56][0]++, assessment.config.difficultyWeights) ||
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[56][1]++, this.defaultConfig.difficultyWeights);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[179]++;
        _loop_1 = function (skillId) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[36]++;
          var skillQuestions =
          /* istanbul ignore next */
          (cov_mwcv5ulzt().s[180]++, assessment.questions.filter(function (q) {
            /* istanbul ignore next */
            cov_mwcv5ulzt().f[37]++;
            cov_mwcv5ulzt().s[181]++;
            return q.skillId === skillId;
          }));
          var skillResponses =
          /* istanbul ignore next */
          (cov_mwcv5ulzt().s[182]++, assessment.responses.filter(function (r) {
            /* istanbul ignore next */
            cov_mwcv5ulzt().f[38]++;
            cov_mwcv5ulzt().s[183]++;
            return skillQuestions.some(function (q) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().f[39]++;
              cov_mwcv5ulzt().s[184]++;
              return q.id === r.questionId;
            });
          }));
          var totalWeight =
          /* istanbul ignore next */
          (cov_mwcv5ulzt().s[185]++, 0);
          var weightedScore =
          /* istanbul ignore next */
          (cov_mwcv5ulzt().s[186]++, 0);
          /* istanbul ignore next */
          cov_mwcv5ulzt().s[187]++;
          var _loop_2 = function (response) {
            /* istanbul ignore next */
            cov_mwcv5ulzt().f[40]++;
            var question =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().s[188]++, skillQuestions.find(function (q) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().f[41]++;
              cov_mwcv5ulzt().s[189]++;
              return q.id === response.questionId;
            }));
            var weight =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().s[190]++,
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[57][0]++, difficultyWeights[question.difficulty]) ||
            /* istanbul ignore next */
            (cov_mwcv5ulzt().b[57][1]++, 1));
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[191]++;
            totalWeight += weight;
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[192]++;
            if (response.isCorrect) {
              /* istanbul ignore next */
              cov_mwcv5ulzt().b[58][0]++;
              cov_mwcv5ulzt().s[193]++;
              weightedScore += weight;
            } else
            /* istanbul ignore next */
            {
              cov_mwcv5ulzt().b[58][1]++;
            }
          };
          /* istanbul ignore next */
          cov_mwcv5ulzt().s[194]++;
          for (var _c =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().s[195]++, 0), skillResponses_1 =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().s[196]++, skillResponses); _c < skillResponses_1.length; _c++) {
            var response =
            /* istanbul ignore next */
            (cov_mwcv5ulzt().s[197]++, skillResponses_1[_c]);
            /* istanbul ignore next */
            cov_mwcv5ulzt().s[198]++;
            _loop_2(response);
          }
          /* istanbul ignore next */
          cov_mwcv5ulzt().s[199]++;
          skillScores[skillId] = totalWeight > 0 ?
          /* istanbul ignore next */
          (cov_mwcv5ulzt().b[59][0]++, weightedScore / totalWeight * 100) :
          /* istanbul ignore next */
          (cov_mwcv5ulzt().b[59][1]++, 0);
        };
        // Calculate scores for each skill
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[200]++;
        for (_i = 0, _a = assessment.skillIds; _i < _a.length; _i++) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().s[201]++;
          skillId = _a[_i];
          /* istanbul ignore next */
          cov_mwcv5ulzt().s[202]++;
          _loop_1(skillId);
        }
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[203]++;
        overallScore = Object.values(skillScores).reduce(function (sum, score) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[42]++;
          cov_mwcv5ulzt().s[204]++;
          return sum + score;
        }, 0) / Object.keys(skillScores).length;
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[205]++;
        confidenceValues = assessment.responses.filter(function (r) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[43]++;
          cov_mwcv5ulzt().s[206]++;
          return r.confidence !== undefined;
        }).map(function (r) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[44]++;
          cov_mwcv5ulzt().s[207]++;
          return r.confidence;
        });
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[208]++;
        averageConfidence = confidenceValues.length > 0 ?
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[60][0]++, confidenceValues.reduce(function (sum, conf) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[45]++;
          cov_mwcv5ulzt().s[209]++;
          return sum + conf;
        }, 0) / confidenceValues.length) :
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[60][1]++, undefined);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[210]++;
        totalTimeSpent = assessment.responses.reduce(function (sum, r) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().f[46]++;
          cov_mwcv5ulzt().s[211]++;
          return sum + r.timeSpent;
        }, 0);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[212]++;
        averageTimePerQuestion = assessment.responses.length > 0 ?
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[61][0]++, totalTimeSpent / assessment.responses.length) :
        /* istanbul ignore next */
        (cov_mwcv5ulzt().b[61][1]++, 0);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[213]++;
        results = {
          assessmentId: assessmentId,
          skillScores: skillScores,
          overallScore: overallScore,
          averageConfidence: averageConfidence,
          averageTimePerQuestion: averageTimePerQuestion,
          totalTimeSpent: totalTimeSpent,
          completedAt: new Date(),
          recommendations: this.generateRecommendations(skillScores, overallScore)
        };
        // Update assessment status
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[214]++;
        assessment.status = 'completed';
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[215]++;
        assessment.completedAt = results.completedAt;
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[216]++;
        assessment.updatedAt = new Date();
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[217]++;
        this.assessments.set(assessmentId, assessment);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[218]++;
        return [2 /*return*/, results];
      });
    });
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[219]++;
  SkillAssessmentEngine.prototype.getAssessment = function (assessmentId) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[47]++;
    cov_mwcv5ulzt().s[220]++;
    return /* istanbul ignore next */(cov_mwcv5ulzt().b[62][0]++, this.assessments.get(assessmentId)) ||
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[62][1]++, null);
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[221]++;
  SkillAssessmentEngine.prototype.getAssessmentsByUser = function (userId) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[48]++;
    cov_mwcv5ulzt().s[222]++;
    return Array.from(this.assessments.values()).filter(function (a) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[49]++;
      cov_mwcv5ulzt().s[223]++;
      return a.userId === userId;
    });
  };
  // Debug method for testing
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[224]++;
  SkillAssessmentEngine.prototype.getAllAssessments = function () {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[50]++;
    cov_mwcv5ulzt().s[225]++;
    return Array.from(this.assessments.values());
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[226]++;
  SkillAssessmentEngine.prototype.updateAssessmentStatus = function (assessmentId, status) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[51]++;
    cov_mwcv5ulzt().s[227]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[52]++;
      var assessment;
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[228]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[53]++;
        cov_mwcv5ulzt().s[229]++;
        assessment = this.getAssessment(assessmentId);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[230]++;
        if (!assessment) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[63][0]++;
          cov_mwcv5ulzt().s[231]++;
          throw new Error('Assessment not found');
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[63][1]++;
        }
        cov_mwcv5ulzt().s[232]++;
        assessment.status = status;
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[233]++;
        assessment.updatedAt = new Date();
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[234]++;
        this.assessments.set(assessmentId, assessment);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[235]++;
        return [2 /*return*/];
      });
    });
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[236]++;
  SkillAssessmentEngine.prototype.generateQuestionForSkill = function (skillId, difficulty) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[54]++;
    cov_mwcv5ulzt().s[237]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_mwcv5ulzt().f[55]++;
      var questionTemplates, template;
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[238]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().f[56]++;
        cov_mwcv5ulzt().s[239]++;
        questionTemplates = this.getQuestionTemplates(skillId, difficulty);
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[240]++;
        template = questionTemplates[Math.floor(Math.random() * questionTemplates.length)];
        /* istanbul ignore next */
        cov_mwcv5ulzt().s[241]++;
        return [2 /*return*/, {
          id: "".concat((0, uuid_1.v4)(), "-").concat(skillId, "-").concat(difficulty, "-").concat(Date.now(), "-").concat(Math.random()),
          skillId: skillId,
          question: template.question,
          options: template.options,
          correctAnswer: template.correctAnswer,
          difficulty: difficulty,
          explanation: template.explanation
        }];
      });
    });
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[242]++;
  SkillAssessmentEngine.prototype.getQuestionTemplates = function (skillId, difficulty) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[57]++;
    // Simplified question templates for testing
    var templates =
    /* istanbul ignore next */
    (cov_mwcv5ulzt().s[243]++, {
      javascript: {
        beginner: [{
          question: "What is the correct way to declare a variable in JavaScript?",
          options: ["var x = 5;", "variable x = 5;", "v x = 5;", "declare x = 5;"],
          correctAnswer: 0,
          explanation: "The 'var' keyword is used to declare variables in JavaScript."
        }, {
          question: "Which of the following is a JavaScript data type?",
          options: ["string", "boolean", "number", "all of the above"],
          correctAnswer: 3,
          explanation: "JavaScript has several primitive data types including string, boolean, and number."
        }],
        intermediate: [{
          question: "What does the 'this' keyword refer to in JavaScript?",
          options: ["The current function", "The global object", "The object that owns the method", "The parent object"],
          correctAnswer: 2,
          explanation: "'this' refers to the object that owns the method being executed."
        }],
        advanced: [{
          question: "What is a closure in JavaScript?",
          options: ["A function inside another function", "A way to close a program", "A function that has access to outer scope", "A type of loop"],
          correctAnswer: 2,
          explanation: "A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned."
        }]
      },
      react: {
        beginner: [{
          question: "What is JSX?",
          options: ["A JavaScript library", "A syntax extension for JavaScript", "A CSS framework", "A database"],
          correctAnswer: 1,
          explanation: "JSX is a syntax extension for JavaScript that allows you to write HTML-like code in your JavaScript files."
        }],
        intermediate: [{
          question: "What is the purpose of useState hook?",
          options: ["To manage component state", "To handle side effects", "To optimize performance", "To create components"],
          correctAnswer: 0,
          explanation: "useState is a React hook that allows you to add state to functional components."
        }],
        advanced: [{
          question: "What is the difference between useEffect and useLayoutEffect?",
          options: ["No difference", "useLayoutEffect runs synchronously", "useEffect runs synchronously", "useLayoutEffect is deprecated"],
          correctAnswer: 1,
          explanation: "useLayoutEffect runs synchronously after all DOM mutations but before the browser paints."
        }]
      },
      nodejs: {
        beginner: [{
          question: "What is Node.js?",
          options: ["A web browser", "A JavaScript runtime", "A database", "A CSS framework"],
          correctAnswer: 1,
          explanation: "Node.js is a JavaScript runtime built on Chrome's V8 JavaScript engine."
        }],
        intermediate: [{
          question: "What is npm?",
          options: ["Node Package Manager", "New Programming Method", "Network Protocol Manager", "Node Process Manager"],
          correctAnswer: 0,
          explanation: "npm stands for Node Package Manager and is the default package manager for Node.js."
        }],
        advanced: [{
          question: "What is the event loop in Node.js?",
          options: ["A loop that handles events", "A mechanism for non-blocking I/O", "A way to create loops", "A debugging tool"],
          correctAnswer: 1,
          explanation: "The event loop is what allows Node.js to perform non-blocking I/O operations despite JavaScript being single-threaded."
        }]
      }
    });
    var skillTemplates =
    /* istanbul ignore next */
    (cov_mwcv5ulzt().s[244]++, templates[skillId]);
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[245]++;
    if (!skillTemplates) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().b[64][0]++;
      cov_mwcv5ulzt().s[246]++;
      return [{
        question: "Sample ".concat(difficulty, " question for ").concat(skillId),
        options: ["Option A", "Option B", "Option C", "Option D"],
        correctAnswer: 0,
        explanation: "This is a sample question."
      }];
    } else
    /* istanbul ignore next */
    {
      cov_mwcv5ulzt().b[64][1]++;
    }
    cov_mwcv5ulzt().s[247]++;
    return /* istanbul ignore next */(cov_mwcv5ulzt().b[65][0]++, skillTemplates[difficulty]) ||
    /* istanbul ignore next */
    (cov_mwcv5ulzt().b[65][1]++, skillTemplates.beginner);
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[248]++;
  SkillAssessmentEngine.prototype.shuffleArray = function (array) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[58]++;
    var _a;
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[249]++;
    for (var i =
    /* istanbul ignore next */
    (cov_mwcv5ulzt().s[250]++, array.length - 1); i > 0; i--) {
      var j =
      /* istanbul ignore next */
      (cov_mwcv5ulzt().s[251]++, Math.floor(Math.random() * (i + 1)));
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[252]++;
      _a = [array[j], array[i]], array[i] = _a[0], array[j] = _a[1];
    }
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[253]++;
  SkillAssessmentEngine.prototype.generateRecommendations = function (skillScores, overallScore) {
    /* istanbul ignore next */
    cov_mwcv5ulzt().f[59]++;
    var recommendations =
    /* istanbul ignore next */
    (cov_mwcv5ulzt().s[254]++, []);
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[255]++;
    if (overallScore < 50) {
      /* istanbul ignore next */
      cov_mwcv5ulzt().b[66][0]++;
      cov_mwcv5ulzt().s[256]++;
      recommendations.push("Consider focusing on fundamental concepts before advancing to more complex topics.");
    } else {
      /* istanbul ignore next */
      cov_mwcv5ulzt().b[66][1]++;
      cov_mwcv5ulzt().s[257]++;
      if (overallScore < 75) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().b[67][0]++;
        cov_mwcv5ulzt().s[258]++;
        recommendations.push("Good foundation! Focus on practicing more advanced concepts.");
      } else {
        /* istanbul ignore next */
        cov_mwcv5ulzt().b[67][1]++;
        cov_mwcv5ulzt().s[259]++;
        recommendations.push("Excellent performance! You're ready for advanced challenges.");
      }
    }
    // Add skill-specific recommendations
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[260]++;
    for (var _i =
      /* istanbul ignore next */
      (cov_mwcv5ulzt().s[261]++, 0), _a =
      /* istanbul ignore next */
      (cov_mwcv5ulzt().s[262]++, Object.entries(skillScores)); _i < _a.length; _i++) {
      var _b =
        /* istanbul ignore next */
        (cov_mwcv5ulzt().s[263]++, _a[_i]),
        skill =
        /* istanbul ignore next */
        (cov_mwcv5ulzt().s[264]++, _b[0]),
        score =
        /* istanbul ignore next */
        (cov_mwcv5ulzt().s[265]++, _b[1]);
      /* istanbul ignore next */
      cov_mwcv5ulzt().s[266]++;
      if (score < 60) {
        /* istanbul ignore next */
        cov_mwcv5ulzt().b[68][0]++;
        cov_mwcv5ulzt().s[267]++;
        recommendations.push("Consider additional practice with ".concat(skill, " fundamentals."));
      } else {
        /* istanbul ignore next */
        cov_mwcv5ulzt().b[68][1]++;
        cov_mwcv5ulzt().s[268]++;
        if (score > 85) {
          /* istanbul ignore next */
          cov_mwcv5ulzt().b[69][0]++;
          cov_mwcv5ulzt().s[269]++;
          recommendations.push("Strong ".concat(skill, " skills! Consider mentoring others or taking on leadership roles."));
        } else
        /* istanbul ignore next */
        {
          cov_mwcv5ulzt().b[69][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_mwcv5ulzt().s[270]++;
    return recommendations;
  };
  /* istanbul ignore next */
  cov_mwcv5ulzt().s[271]++;
  return SkillAssessmentEngine;
}());
/* istanbul ignore next */
cov_mwcv5ulzt().s[272]++;
exports.SkillAssessmentEngine = SkillAssessmentEngine;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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