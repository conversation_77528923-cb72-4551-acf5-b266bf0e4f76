97a98f580ba37d9d825b2e84bf26509c
"use strict";

/* istanbul ignore next */
function cov_808r7p3i1() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/enhanced-results/route.ts";
  var hash = "1c87f3af3c6659e488e493fd8c82daff7cd5dafb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/enhanced-results/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "85": {
        start: {
          line: 57,
          column: 34
        },
        end: {
          line: 57,
          column: 76
        }
      },
      "86": {
        start: {
          line: 58,
          column: 15
        },
        end: {
          line: 58,
          column: 55
        }
      },
      "87": {
        start: {
          line: 59,
          column: 34
        },
        end: {
          line: 59,
          column: 76
        }
      },
      "88": {
        start: {
          line: 60,
          column: 0
        },
        end: {
          line: 126,
          column: 7
        }
      },
      "89": {
        start: {
          line: 60,
          column: 99
        },
        end: {
          line: 126,
          column: 3
        }
      },
      "90": {
        start: {
          line: 63,
          column: 17
        },
        end: {
          line: 63,
          column: 26
        }
      },
      "91": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 125,
          column: 7
        }
      },
      "92": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 124,
          column: 9
        }
      },
      "93": {
        start: {
          line: 66,
          column: 20
        },
        end: {
          line: 66,
          column: 96
        }
      },
      "94": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 68,
          column: 36
        }
      },
      "95": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 45
        }
      },
      "96": {
        start: {
          line: 71,
          column: 16
        },
        end: {
          line: 71,
          column: 46
        }
      },
      "97": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 76,
          column: 17
        }
      },
      "98": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 65
        }
      },
      "99": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 43
        }
      },
      "100": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 32
        }
      },
      "101": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 81,
          column: 17
        }
      },
      "102": {
        start: {
          line: 78,
          column: 20
        },
        end: {
          line: 78,
          column: 67
        }
      },
      "103": {
        start: {
          line: 79,
          column: 20
        },
        end: {
          line: 79,
          column: 43
        }
      },
      "104": {
        start: {
          line: 80,
          column: 20
        },
        end: {
          line: 80,
          column: 32
        }
      },
      "105": {
        start: {
          line: 82,
          column: 16
        },
        end: {
          line: 90,
          column: 24
        }
      },
      "106": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 39
        }
      },
      "107": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 97,
          column: 17
        }
      },
      "108": {
        start: {
          line: 94,
          column: 20
        },
        end: {
          line: 94,
          column: 79
        }
      },
      "109": {
        start: {
          line: 95,
          column: 20
        },
        end: {
          line: 95,
          column: 43
        }
      },
      "110": {
        start: {
          line: 96,
          column: 20
        },
        end: {
          line: 96,
          column: 32
        }
      },
      "111": {
        start: {
          line: 98,
          column: 16
        },
        end: {
          line: 102,
          column: 17
        }
      },
      "112": {
        start: {
          line: 99,
          column: 20
        },
        end: {
          line: 99,
          column: 69
        }
      },
      "113": {
        start: {
          line: 100,
          column: 20
        },
        end: {
          line: 100,
          column: 43
        }
      },
      "114": {
        start: {
          line: 101,
          column: 20
        },
        end: {
          line: 101,
          column: 32
        }
      },
      "115": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 103,
          column: 34
        }
      },
      "116": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 114,
          column: 19
        }
      },
      "117": {
        start: {
          line: 105,
          column: 20
        },
        end: {
          line: 113,
          column: 21
        }
      },
      "118": {
        start: {
          line: 106,
          column: 36
        },
        end: {
          line: 108,
          column: 50
        }
      },
      "119": {
        start: {
          line: 109,
          column: 24
        },
        end: {
          line: 109,
          column: 67
        }
      },
      "120": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 112,
          column: 82
        }
      },
      "121": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 115,
          column: 144
        }
      },
      "122": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 44
        }
      },
      "123": {
        start: {
          line: 119,
          column: 16
        },
        end: {
          line: 119,
          column: 141
        }
      },
      "124": {
        start: {
          line: 120,
          column: 16
        },
        end: {
          line: 123,
          column: 24
        }
      },
      "125": {
        start: {
          line: 128,
          column: 0
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "126": {
        start: {
          line: 128,
          column: 100
        },
        end: {
          line: 200,
          column: 3
        }
      },
      "127": {
        start: {
          line: 131,
          column: 17
        },
        end: {
          line: 131,
          column: 26
        }
      },
      "128": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 199,
          column: 7
        }
      },
      "129": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 198,
          column: 9
        }
      },
      "130": {
        start: {
          line: 134,
          column: 20
        },
        end: {
          line: 134,
          column: 96
        }
      },
      "131": {
        start: {
          line: 136,
          column: 16
        },
        end: {
          line: 136,
          column: 36
        }
      },
      "132": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 137,
          column: 45
        }
      },
      "133": {
        start: {
          line: 139,
          column: 16
        },
        end: {
          line: 139,
          column: 46
        }
      },
      "134": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 140,
          column: 53
        }
      },
      "135": {
        start: {
          line: 142,
          column: 16
        },
        end: {
          line: 142,
          column: 33
        }
      },
      "136": {
        start: {
          line: 143,
          column: 16
        },
        end: {
          line: 147,
          column: 17
        }
      },
      "137": {
        start: {
          line: 144,
          column: 20
        },
        end: {
          line: 144,
          column: 65
        }
      },
      "138": {
        start: {
          line: 145,
          column: 20
        },
        end: {
          line: 145,
          column: 43
        }
      },
      "139": {
        start: {
          line: 146,
          column: 20
        },
        end: {
          line: 146,
          column: 32
        }
      },
      "140": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 152,
          column: 17
        }
      },
      "141": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 149,
          column: 67
        }
      },
      "142": {
        start: {
          line: 150,
          column: 20
        },
        end: {
          line: 150,
          column: 43
        }
      },
      "143": {
        start: {
          line: 151,
          column: 20
        },
        end: {
          line: 151,
          column: 32
        }
      },
      "144": {
        start: {
          line: 153,
          column: 16
        },
        end: {
          line: 161,
          column: 24
        }
      },
      "145": {
        start: {
          line: 163,
          column: 16
        },
        end: {
          line: 163,
          column: 39
        }
      },
      "146": {
        start: {
          line: 164,
          column: 16
        },
        end: {
          line: 168,
          column: 17
        }
      },
      "147": {
        start: {
          line: 165,
          column: 20
        },
        end: {
          line: 165,
          column: 79
        }
      },
      "148": {
        start: {
          line: 166,
          column: 20
        },
        end: {
          line: 166,
          column: 43
        }
      },
      "149": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 167,
          column: 32
        }
      },
      "150": {
        start: {
          line: 169,
          column: 16
        },
        end: {
          line: 169,
          column: 308
        }
      },
      "151": {
        start: {
          line: 170,
          column: 16
        },
        end: {
          line: 170,
          column: 34
        }
      },
      "152": {
        start: {
          line: 171,
          column: 16
        },
        end: {
          line: 181,
          column: 19
        }
      },
      "153": {
        start: {
          line: 172,
          column: 20
        },
        end: {
          line: 180,
          column: 21
        }
      },
      "154": {
        start: {
          line: 173,
          column: 36
        },
        end: {
          line: 175,
          column: 50
        }
      },
      "155": {
        start: {
          line: 176,
          column: 24
        },
        end: {
          line: 176,
          column: 67
        }
      },
      "156": {
        start: {
          line: 179,
          column: 24
        },
        end: {
          line: 179,
          column: 82
        }
      },
      "157": {
        start: {
          line: 183,
          column: 16
        },
        end: {
          line: 188,
          column: 18
        }
      },
      "158": {
        start: {
          line: 189,
          column: 16
        },
        end: {
          line: 189,
          column: 144
        }
      },
      "159": {
        start: {
          line: 191,
          column: 16
        },
        end: {
          line: 191,
          column: 44
        }
      },
      "160": {
        start: {
          line: 193,
          column: 16
        },
        end: {
          line: 193,
          column: 160
        }
      },
      "161": {
        start: {
          line: 194,
          column: 16
        },
        end: {
          line: 197,
          column: 24
        }
      },
      "162": {
        start: {
          line: 202,
          column: 0
        },
        end: {
          line: 251,
          column: 7
        }
      },
      "163": {
        start: {
          line: 202,
          column: 101
        },
        end: {
          line: 251,
          column: 3
        }
      },
      "164": {
        start: {
          line: 205,
          column: 17
        },
        end: {
          line: 205,
          column: 26
        }
      },
      "165": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 250,
          column: 7
        }
      },
      "166": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 249,
          column: 9
        }
      },
      "167": {
        start: {
          line: 208,
          column: 20
        },
        end: {
          line: 208,
          column: 96
        }
      },
      "168": {
        start: {
          line: 210,
          column: 16
        },
        end: {
          line: 210,
          column: 36
        }
      },
      "169": {
        start: {
          line: 211,
          column: 16
        },
        end: {
          line: 211,
          column: 45
        }
      },
      "170": {
        start: {
          line: 213,
          column: 16
        },
        end: {
          line: 213,
          column: 46
        }
      },
      "171": {
        start: {
          line: 214,
          column: 16
        },
        end: {
          line: 214,
          column: 53
        }
      },
      "172": {
        start: {
          line: 216,
          column: 16
        },
        end: {
          line: 216,
          column: 33
        }
      },
      "173": {
        start: {
          line: 217,
          column: 16
        },
        end: {
          line: 221,
          column: 17
        }
      },
      "174": {
        start: {
          line: 218,
          column: 20
        },
        end: {
          line: 218,
          column: 65
        }
      },
      "175": {
        start: {
          line: 219,
          column: 20
        },
        end: {
          line: 219,
          column: 43
        }
      },
      "176": {
        start: {
          line: 220,
          column: 20
        },
        end: {
          line: 220,
          column: 32
        }
      },
      "177": {
        start: {
          line: 222,
          column: 16
        },
        end: {
          line: 222,
          column: 225
        }
      },
      "178": {
        start: {
          line: 225,
          column: 16
        },
        end: {
          line: 231,
          column: 19
        }
      },
      "179": {
        start: {
          line: 243,
          column: 16
        },
        end: {
          line: 248,
          column: 24
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 60,
            column: 72
          },
          end: {
            line: 60,
            column: 73
          }
        },
        loc: {
          start: {
            line: 60,
            column: 97
          },
          end: {
            line: 126,
            column: 5
          }
        },
        line: 60
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 60,
            column: 150
          },
          end: {
            line: 60,
            column: 151
          }
        },
        loc: {
          start: {
            line: 60,
            column: 173
          },
          end: {
            line: 126,
            column: 1
          }
        },
        line: 60
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 64,
            column: 29
          },
          end: {
            line: 64,
            column: 30
          }
        },
        loc: {
          start: {
            line: 64,
            column: 43
          },
          end: {
            line: 125,
            column: 5
          }
        },
        line: 64
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 104,
            column: 45
          },
          end: {
            line: 104,
            column: 46
          }
        },
        loc: {
          start: {
            line: 104,
            column: 65
          },
          end: {
            line: 114,
            column: 17
          }
        },
        line: 104
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 128,
            column: 73
          },
          end: {
            line: 128,
            column: 74
          }
        },
        loc: {
          start: {
            line: 128,
            column: 98
          },
          end: {
            line: 200,
            column: 5
          }
        },
        line: 128
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 128,
            column: 151
          },
          end: {
            line: 128,
            column: 152
          }
        },
        loc: {
          start: {
            line: 128,
            column: 174
          },
          end: {
            line: 200,
            column: 1
          }
        },
        line: 128
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 132,
            column: 29
          },
          end: {
            line: 132,
            column: 30
          }
        },
        loc: {
          start: {
            line: 132,
            column: 43
          },
          end: {
            line: 199,
            column: 5
          }
        },
        line: 132
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 171,
            column: 45
          },
          end: {
            line: 171,
            column: 46
          }
        },
        loc: {
          start: {
            line: 171,
            column: 65
          },
          end: {
            line: 181,
            column: 17
          }
        },
        line: 171
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 202,
            column: 74
          },
          end: {
            line: 202,
            column: 75
          }
        },
        loc: {
          start: {
            line: 202,
            column: 99
          },
          end: {
            line: 251,
            column: 5
          }
        },
        line: 202
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 202,
            column: 152
          },
          end: {
            line: 202,
            column: 153
          }
        },
        loc: {
          start: {
            line: 202,
            column: 175
          },
          end: {
            line: 251,
            column: 1
          }
        },
        line: 202
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 206,
            column: 29
          },
          end: {
            line: 206,
            column: 30
          }
        },
        loc: {
          start: {
            line: 206,
            column: 43
          },
          end: {
            line: 250,
            column: 5
          }
        },
        line: 206
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 65,
            column: 8
          },
          end: {
            line: 124,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 66,
            column: 12
          },
          end: {
            line: 66,
            column: 96
          }
        }, {
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 69,
            column: 45
          }
        }, {
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 90,
            column: 24
          }
        }, {
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 115,
            column: 144
          }
        }, {
          start: {
            line: 116,
            column: 12
          },
          end: {
            line: 123,
            column: 24
          }
        }],
        line: 65
      },
      "39": {
        loc: {
          start: {
            line: 72,
            column: 16
          },
          end: {
            line: 76,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 16
          },
          end: {
            line: 76,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "40": {
        loc: {
          start: {
            line: 72,
            column: 22
          },
          end: {
            line: 72,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 120
          },
          end: {
            line: 72,
            column: 126
          }
        }, {
          start: {
            line: 72,
            column: 129
          },
          end: {
            line: 72,
            column: 134
          }
        }],
        line: 72
      },
      "41": {
        loc: {
          start: {
            line: 72,
            column: 22
          },
          end: {
            line: 72,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 22
          },
          end: {
            line: 72,
            column: 100
          }
        }, {
          start: {
            line: 72,
            column: 104
          },
          end: {
            line: 72,
            column: 117
          }
        }],
        line: 72
      },
      "42": {
        loc: {
          start: {
            line: 72,
            column: 28
          },
          end: {
            line: 72,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 69
          },
          end: {
            line: 72,
            column: 75
          }
        }, {
          start: {
            line: 72,
            column: 78
          },
          end: {
            line: 72,
            column: 90
          }
        }],
        line: 72
      },
      "43": {
        loc: {
          start: {
            line: 72,
            column: 28
          },
          end: {
            line: 72,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 28
          },
          end: {
            line: 72,
            column: 44
          }
        }, {
          start: {
            line: 72,
            column: 48
          },
          end: {
            line: 72,
            column: 66
          }
        }],
        line: 72
      },
      "44": {
        loc: {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 81,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 81,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "45": {
        loc: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 97,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 97,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "46": {
        loc: {
          start: {
            line: 98,
            column: 16
          },
          end: {
            line: 102,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 16
          },
          end: {
            line: 102,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "47": {
        loc: {
          start: {
            line: 106,
            column: 36
          },
          end: {
            line: 108,
            column: 50
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 107,
            column: 30
          },
          end: {
            line: 107,
            column: 62
          }
        }, {
          start: {
            line: 108,
            column: 30
          },
          end: {
            line: 108,
            column: 50
          }
        }],
        line: 106
      },
      "48": {
        loc: {
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 198,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 134,
            column: 96
          }
        }, {
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 137,
            column: 45
          }
        }, {
          start: {
            line: 138,
            column: 12
          },
          end: {
            line: 140,
            column: 53
          }
        }, {
          start: {
            line: 141,
            column: 12
          },
          end: {
            line: 161,
            column: 24
          }
        }, {
          start: {
            line: 162,
            column: 12
          },
          end: {
            line: 189,
            column: 144
          }
        }, {
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 197,
            column: 24
          }
        }],
        line: 133
      },
      "49": {
        loc: {
          start: {
            line: 143,
            column: 16
          },
          end: {
            line: 147,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 16
          },
          end: {
            line: 147,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "50": {
        loc: {
          start: {
            line: 143,
            column: 22
          },
          end: {
            line: 143,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 143,
            column: 120
          },
          end: {
            line: 143,
            column: 126
          }
        }, {
          start: {
            line: 143,
            column: 129
          },
          end: {
            line: 143,
            column: 134
          }
        }],
        line: 143
      },
      "51": {
        loc: {
          start: {
            line: 143,
            column: 22
          },
          end: {
            line: 143,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 22
          },
          end: {
            line: 143,
            column: 100
          }
        }, {
          start: {
            line: 143,
            column: 104
          },
          end: {
            line: 143,
            column: 117
          }
        }],
        line: 143
      },
      "52": {
        loc: {
          start: {
            line: 143,
            column: 28
          },
          end: {
            line: 143,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 143,
            column: 69
          },
          end: {
            line: 143,
            column: 75
          }
        }, {
          start: {
            line: 143,
            column: 78
          },
          end: {
            line: 143,
            column: 90
          }
        }],
        line: 143
      },
      "53": {
        loc: {
          start: {
            line: 143,
            column: 28
          },
          end: {
            line: 143,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 28
          },
          end: {
            line: 143,
            column: 44
          }
        }, {
          start: {
            line: 143,
            column: 48
          },
          end: {
            line: 143,
            column: 66
          }
        }],
        line: 143
      },
      "54": {
        loc: {
          start: {
            line: 148,
            column: 16
          },
          end: {
            line: 152,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 16
          },
          end: {
            line: 152,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 148
      },
      "55": {
        loc: {
          start: {
            line: 164,
            column: 16
          },
          end: {
            line: 168,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 16
          },
          end: {
            line: 168,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "56": {
        loc: {
          start: {
            line: 169,
            column: 51
          },
          end: {
            line: 169,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 67
          },
          end: {
            line: 169,
            column: 69
          }
        }, {
          start: {
            line: 169,
            column: 72
          },
          end: {
            line: 169,
            column: 74
          }
        }],
        line: 169
      },
      "57": {
        loc: {
          start: {
            line: 169,
            column: 119
          },
          end: {
            line: 169,
            column: 150
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 135
          },
          end: {
            line: 169,
            column: 145
          }
        }, {
          start: {
            line: 169,
            column: 148
          },
          end: {
            line: 169,
            column: 150
          }
        }],
        line: 169
      },
      "58": {
        loc: {
          start: {
            line: 169,
            column: 199
          },
          end: {
            line: 169,
            column: 236
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 215
          },
          end: {
            line: 169,
            column: 231
          }
        }, {
          start: {
            line: 169,
            column: 234
          },
          end: {
            line: 169,
            column: 236
          }
        }],
        line: 169
      },
      "59": {
        loc: {
          start: {
            line: 169,
            column: 279
          },
          end: {
            line: 169,
            column: 307
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 295
          },
          end: {
            line: 169,
            column: 302
          }
        }, {
          start: {
            line: 169,
            column: 305
          },
          end: {
            line: 169,
            column: 307
          }
        }],
        line: 169
      },
      "60": {
        loc: {
          start: {
            line: 173,
            column: 36
          },
          end: {
            line: 175,
            column: 50
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 174,
            column: 30
          },
          end: {
            line: 174,
            column: 62
          }
        }, {
          start: {
            line: 175,
            column: 30
          },
          end: {
            line: 175,
            column: 50
          }
        }],
        line: 173
      },
      "61": {
        loc: {
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 249,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 208,
            column: 12
          },
          end: {
            line: 208,
            column: 96
          }
        }, {
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 211,
            column: 45
          }
        }, {
          start: {
            line: 212,
            column: 12
          },
          end: {
            line: 214,
            column: 53
          }
        }, {
          start: {
            line: 215,
            column: 12
          },
          end: {
            line: 248,
            column: 24
          }
        }],
        line: 207
      },
      "62": {
        loc: {
          start: {
            line: 217,
            column: 16
          },
          end: {
            line: 221,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 16
          },
          end: {
            line: 221,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "63": {
        loc: {
          start: {
            line: 217,
            column: 22
          },
          end: {
            line: 217,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 217,
            column: 120
          },
          end: {
            line: 217,
            column: 126
          }
        }, {
          start: {
            line: 217,
            column: 129
          },
          end: {
            line: 217,
            column: 134
          }
        }],
        line: 217
      },
      "64": {
        loc: {
          start: {
            line: 217,
            column: 22
          },
          end: {
            line: 217,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 217,
            column: 22
          },
          end: {
            line: 217,
            column: 100
          }
        }, {
          start: {
            line: 217,
            column: 104
          },
          end: {
            line: 217,
            column: 117
          }
        }],
        line: 217
      },
      "65": {
        loc: {
          start: {
            line: 217,
            column: 28
          },
          end: {
            line: 217,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 217,
            column: 69
          },
          end: {
            line: 217,
            column: 75
          }
        }, {
          start: {
            line: 217,
            column: 78
          },
          end: {
            line: 217,
            column: 90
          }
        }],
        line: 217
      },
      "66": {
        loc: {
          start: {
            line: 217,
            column: 28
          },
          end: {
            line: 217,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 217,
            column: 28
          },
          end: {
            line: 217,
            column: 44
          }
        }, {
          start: {
            line: 217,
            column: 48
          },
          end: {
            line: 217,
            column: 66
          }
        }],
        line: 217
      },
      "67": {
        loc: {
          start: {
            line: 222,
            column: 67
          },
          end: {
            line: 222,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 222,
            column: 83
          },
          end: {
            line: 222,
            column: 85
          }
        }, {
          start: {
            line: 222,
            column: 88
          },
          end: {
            line: 222,
            column: 90
          }
        }],
        line: 222
      },
      "68": {
        loc: {
          start: {
            line: 222,
            column: 139
          },
          end: {
            line: 222,
            column: 162
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 222,
            column: 155
          },
          end: {
            line: 222,
            column: 157
          }
        }, {
          start: {
            line: 222,
            column: 160
          },
          end: {
            line: 222,
            column: 162
          }
        }],
        line: 222
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0, 0, 0, 0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0, 0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/enhanced-results/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,6EAAwF;AACxF,wDAAkC;AAClC,6EAA4E;AAY/D,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,qEAGzC,OAAO,YAFR,OAAoB,EACpB,EAA+C;;;QAA7C,MAAM,YAAA;;;oBAEQ,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACtB,qBAAM,MAAM,EAAA;;gBAA7B,YAAY,GAAK,CAAA,SAAY,CAAA,GAAjB;gBAExB,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,IAAI,CAAC,YAAY,EAAE,CAAC;oBACZ,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAQ,CAAC;oBAC5D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGkB,qBAAM,gBAAM,CAAC,UAAU,CAAC,SAAS,CAAC;wBACnD,KAAK,EAAE;4BACL,EAAE,EAAE,YAAY;4BAChB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;yBACxB;wBACD,OAAO,EAAE;4BACP,SAAS,EAAE,IAAI;yBAChB;qBACF,CAAC,EAAA;;gBARI,UAAU,GAAG,SAQjB;gBAEF,IAAI,CAAC,UAAU,EAAE,CAAC;oBACV,KAAK,GAAG,IAAI,KAAK,CAAC,uCAAuC,CAAQ,CAAC;oBACxE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBAChC,KAAK,GAAG,IAAI,KAAK,CAAC,6BAA6B,CAAQ,CAAC;oBAC9D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGK,YAAY,GAAuB,EAAE,CAAC;gBAC5C,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;oBACnC,IAAI,CAAC;wBACH,IAAM,KAAK,GAAG,OAAO,QAAQ,CAAC,WAAW,KAAK,QAAQ;4BACpD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;4BAClC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;wBACzB,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,KAA0C,CAAC;oBAClF,CAAC;oBAAC,WAAM,CAAC;wBACP,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,WAAgD,CAAC;oBACjG,CAAC;gBACH,CAAC,CAAC,CAAC;gBAGqB,qBAAM,qDAAyB,CAAC,uBAAuB,CAC7E,YAAY,EACZ,YAAY,CACb,EAAA;;gBAHK,eAAe,GAAG,SAGvB;gBAED,4BAA4B;gBAC5B,OAAO,CAAC,GAAG,CAAC,yDAAkD,OAAO,CAAC,IAAI,CAAC,EAAE,0BAAgB,YAAY,CAAE,CAAC,CAAC;gBAE7G,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAa;wBACtB,IAAI,EAAE,eAAe;qBACtB,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAWH,+DAA+D;AAClD,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,qEAG1C,OAAO,YAFR,OAAoB,EACpB,EAA+C;;;QAA7C,MAAM,YAAA;;;oBAEQ,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACtB,qBAAM,MAAM,EAAA;;gBAA7B,YAAY,GAAK,CAAA,SAAY,CAAA,GAAjB;gBACX,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAA3B,IAAI,GAAG,SAAoB;gBAEjC,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,IAAI,CAAC,YAAY,EAAE,CAAC;oBACZ,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAQ,CAAC;oBAC5D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGkB,qBAAM,gBAAM,CAAC,UAAU,CAAC,SAAS,CAAC;wBACnD,KAAK,EAAE;4BACL,EAAE,EAAE,YAAY;4BAChB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;yBACxB;wBACD,OAAO,EAAE;4BACP,SAAS,EAAE,IAAI;yBAChB;qBACF,CAAC,EAAA;;gBARI,UAAU,GAAG,SAQjB;gBAEF,IAAI,CAAC,UAAU,EAAE,CAAC;oBACV,KAAK,GAAG,IAAI,KAAK,CAAC,uCAAuC,CAAQ,CAAC;oBACxE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAIC,KAIE,IAAI,WAJS,EAAf,UAAU,mBAAG,EAAE,KAAA,EACf,KAGE,IAAI,eAHqB,EAA3B,cAAc,mBAAG,UAAU,KAAA,EAC3B,KAEE,IAAI,iBAF6B,EAAnC,gBAAgB,mBAAG,gBAAgB,KAAA,EACnC,KACE,IAAI,cADiB,EAAvB,aAAa,mBAAG,OAAO,KAAA,CAChB;gBAGH,YAAY,GAAuB,EAAE,CAAC;gBAC5C,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;oBACnC,IAAI,CAAC;wBACH,IAAM,KAAK,GAAG,OAAO,QAAQ,CAAC,WAAW,KAAK,QAAQ;4BACpD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;4BAClC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;wBACzB,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,KAA0C,CAAC;oBAClF,CAAC;oBAAC,WAAM,CAAC;wBACP,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,WAAgD,CAAC;oBACjG,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,wCAAwC;gBACvC,YAAoB,CAAC,gBAAgB,GAAG;oBACvC,UAAU,YAAA;oBACV,cAAc,gBAAA;oBACd,gBAAgB,kBAAA;oBAChB,aAAa,eAAA;iBACd,CAAC;gBAGsB,qBAAM,qDAAyB,CAAC,uBAAuB,CAC7E,YAAY,EACZ,YAAY,CACb,EAAA;;gBAHK,eAAe,GAAG,SAGvB;gBAED,8BAA8B;gBAC9B,OAAO,CAAC,GAAG,CAAC,4EAAqE,OAAO,CAAC,IAAI,CAAC,EAAE,0BAAgB,YAAY,CAAE,CAAC,CAAC;gBAEhI,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAa;wBACtB,IAAI,wBACC,eAAe,KAClB,OAAO,EAAE,uCAAuC,GACjD;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAOH,0DAA0D;AAC7C,QAAA,KAAK,GAAG,IAAA,oDAAwB,EAAC,qEAG3C,OAAO,YAFR,OAAoB,EACpB,EAA+C;;;QAA7C,MAAM,YAAA;;;oBAEQ,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACtB,qBAAM,MAAM,EAAA;;gBAA7B,YAAY,GAAK,CAAA,SAAY,CAAA,GAAjB;gBACX,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAA3B,IAAI,GAAG,SAAoB;gBAEjC,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGC,KAIE,IAAI,mBAJiB,EAAvB,kBAAkB,mBAAG,EAAE,KAAA,EACvB,KAGE,IAAI,iBAHe,EAArB,gBAAgB,mBAAG,EAAE,KAAA,EACrB,aAAa,GAEX,IAAI,cAFO,EACb,QAAQ,GACN,IAAI,SADE,CACD;gBAET,wEAAwE;gBACxE,2CAA2C;gBAC3C,OAAO,CAAC,GAAG,CAAC,iDAA0C,OAAO,CAAC,IAAI,CAAC,EAAE,MAAG,EAAE;oBACxE,YAAY,cAAA;oBACZ,kBAAkB,oBAAA;oBAClB,gBAAgB,kBAAA;oBAChB,aAAa,eAAA;oBACb,QAAQ,UAAA;iBACT,CAAC,CAAC;gBAEH,kFAAkF;gBAClF,2CAA2C;gBAC3C,YAAY;gBACZ,oBAAoB;gBACpB,+BAA+B;gBAC/B,0BAA0B;gBAC1B,wBAAwB;gBACxB,qBAAqB;gBACrB,eAAe;gBACf,MAAM;gBACN,MAAM;gBAEN,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAa;wBACtB,IAAI,EAAE;4BACJ,OAAO,EAAE,yEAAyE;yBACnF;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/enhanced-results/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport prisma from '@/lib/prisma';\nimport { EnhancedAssessmentService } from '@/lib/enhancedAssessmentService';\nimport { AssessmentResponse } from '@/lib/assessmentScoring';\nimport { withCSRFProtection } from '@/lib/csrf';\n\ninterface EnhancedResultsResponse {\n  insights: any;\n  careerPathRecommendations: any[];\n  learningPath: any;\n  skillDevelopmentPlan: any;\n  nextSteps: any[];\n}\n\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<EnhancedResultsResponse>>> => {\n  const session = await getServerSession(authOptions);\n  const { id: assessmentId } = await params;\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  if (!assessmentId) {\n    const error = new Error('Assessment ID is required') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Verify assessment belongs to user\n  const assessment = await prisma.assessment.findFirst({\n    where: {\n      id: assessmentId,\n      userId: session.user.id\n    },\n    include: {\n      responses: true\n    }\n  });\n\n  if (!assessment) {\n    const error = new Error('Assessment not found or access denied') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  if (assessment.status !== 'COMPLETED') {\n    const error = new Error('Assessment is not completed') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Convert assessment responses to the expected format\n  const responseData: AssessmentResponse = {};\n  assessment.responses.forEach(response => {\n    try {\n      const value = typeof response.answerValue === 'string'\n        ? JSON.parse(response.answerValue)\n        : response.answerValue;\n      responseData[response.questionKey] = value as string | string[] | number | null;\n    } catch {\n      responseData[response.questionKey] = response.answerValue as string | string[] | number | null;\n    }\n  });\n\n  // Generate enhanced results\n  const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(\n    assessmentId,\n    responseData\n  );\n\n  // Log successful generation\n  console.log(`Enhanced assessment results generated for user ${session.user.id}, assessment ${assessmentId}`);\n\n  return NextResponse.json({\n    success: true as const,\n    data: enhancedResults\n  });\n});\n\ninterface RegenerateResultsResponse {\n  insights: any;\n  careerPathRecommendations: any[];\n  learningPath: any;\n  skillDevelopmentPlan: any;\n  nextSteps: any[];\n  message: string;\n}\n\n// POST endpoint to regenerate results with updated preferences\nexport const POST = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<RegenerateResultsResponse>>> => {\n  const session = await getServerSession(authOptions);\n  const { id: assessmentId } = await params;\n  const body = await request.json();\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  if (!assessmentId) {\n    const error = new Error('Assessment ID is required') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Verify assessment belongs to user\n  const assessment = await prisma.assessment.findFirst({\n    where: {\n      id: assessmentId,\n      userId: session.user.id\n    },\n    include: {\n      responses: true\n    }\n  });\n\n  if (!assessment) {\n    const error = new Error('Assessment not found or access denied') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  // Extract preferences from request body\n  const {\n    focusAreas = [],\n    timeCommitment = 'MODERATE',\n    budgetPreference = 'FREE_PREFERRED',\n    learningStyle = 'MIXED'\n  } = body;\n\n  // Convert assessment responses to the expected format\n  const responseData: AssessmentResponse = {};\n  assessment.responses.forEach(response => {\n    try {\n      const value = typeof response.answerValue === 'string'\n        ? JSON.parse(response.answerValue)\n        : response.answerValue;\n      responseData[response.questionKey] = value as string | string[] | number | null;\n    } catch {\n      responseData[response.questionKey] = response.answerValue as string | string[] | number | null;\n    }\n  });\n\n  // Add user preferences to response data\n  (responseData as any).user_preferences = {\n    focusAreas,\n    timeCommitment,\n    budgetPreference,\n    learningStyle\n  };\n\n  // Generate enhanced results with preferences\n  const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(\n    assessmentId,\n    responseData\n  );\n\n  // Log successful regeneration\n  console.log(`Enhanced assessment results regenerated with preferences for user ${session.user.id}, assessment ${assessmentId}`);\n\n  return NextResponse.json({\n    success: true as const,\n    data: {\n      ...enhancedResults,\n      message: 'Results updated with your preferences'\n    }\n  });\n});\n\ninterface FeedbackResponse {\n  message: string;\n  feedbackId?: string;\n}\n\n// PATCH endpoint to save user feedback on recommendations\nexport const PATCH = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<FeedbackResponse>>> => {\n  const session = await getServerSession(authOptions);\n  const { id: assessmentId } = await params;\n  const body = await request.json();\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const {\n    careerPathFeedback = {},\n    resourceFeedback = {},\n    overallRating,\n    comments\n  } = body;\n\n  // Save feedback to database (you might want to create a feedback table)\n  // For now, we'll log it and return success\n  console.log(`Assessment feedback received from user ${session.user.id}:`, {\n    assessmentId,\n    careerPathFeedback,\n    resourceFeedback,\n    overallRating,\n    comments\n  });\n\n  // In a production system, you would save this feedback to improve recommendations\n  // await prisma.assessmentFeedback.create({\n  //   data: {\n  //     assessmentId,\n  //     userId: session.user.id,\n  //     careerPathFeedback,\n  //     resourceFeedback,\n  //     overallRating,\n  //     comments\n  //   }\n  // });\n\n  return NextResponse.json({\n    success: true as const,\n    data: {\n      message: 'Thank you for your feedback! This helps us improve our recommendations.'\n    }\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1c87f3af3c6659e488e493fd8c82daff7cd5dafb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_808r7p3i1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_808r7p3i1();
var __assign =
/* istanbul ignore next */
(cov_808r7p3i1().s[0]++,
/* istanbul ignore next */
(cov_808r7p3i1().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_808r7p3i1().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_808r7p3i1().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_808r7p3i1().f[0]++;
  cov_808r7p3i1().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_808r7p3i1().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_808r7p3i1().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[1]++;
    cov_808r7p3i1().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_808r7p3i1().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_808r7p3i1().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_808r7p3i1().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_808r7p3i1().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_808r7p3i1().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_808r7p3i1().b[2][0]++;
          cov_808r7p3i1().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_808r7p3i1().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_808r7p3i1().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_808r7p3i1().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_808r7p3i1().s[11]++,
/* istanbul ignore next */
(cov_808r7p3i1().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_808r7p3i1().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_808r7p3i1().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_808r7p3i1().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[3]++;
    cov_808r7p3i1().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_808r7p3i1().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_808r7p3i1().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[4]++;
      cov_808r7p3i1().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_808r7p3i1().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_808r7p3i1().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_808r7p3i1().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[6]++;
      cov_808r7p3i1().s[15]++;
      try {
        /* istanbul ignore next */
        cov_808r7p3i1().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_808r7p3i1().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[7]++;
      cov_808r7p3i1().s[18]++;
      try {
        /* istanbul ignore next */
        cov_808r7p3i1().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_808r7p3i1().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[8]++;
      cov_808r7p3i1().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_808r7p3i1().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_808r7p3i1().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_808r7p3i1().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_808r7p3i1().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_808r7p3i1().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_808r7p3i1().s[23]++,
/* istanbul ignore next */
(cov_808r7p3i1().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_808r7p3i1().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_808r7p3i1().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_808r7p3i1().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_808r7p3i1().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_808r7p3i1().f[10]++;
        cov_808r7p3i1().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_808r7p3i1().b[9][0]++;
          cov_808r7p3i1().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_808r7p3i1().b[9][1]++;
        }
        cov_808r7p3i1().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_808r7p3i1().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_808r7p3i1().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_808r7p3i1().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_808r7p3i1().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_808r7p3i1().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_808r7p3i1().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_808r7p3i1().f[11]++;
    cov_808r7p3i1().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[12]++;
    cov_808r7p3i1().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[13]++;
      cov_808r7p3i1().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[14]++;
    cov_808r7p3i1().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_808r7p3i1().b[12][0]++;
      cov_808r7p3i1().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_808r7p3i1().b[12][1]++;
    }
    cov_808r7p3i1().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_808r7p3i1().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_808r7p3i1().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_808r7p3i1().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_808r7p3i1().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_808r7p3i1().s[36]++;
      try {
        /* istanbul ignore next */
        cov_808r7p3i1().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_808r7p3i1().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_808r7p3i1().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_808r7p3i1().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_808r7p3i1().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_808r7p3i1().b[18][0]++,
        /* istanbul ignore next */
        (cov_808r7p3i1().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_808r7p3i1().b[19][1]++,
        /* istanbul ignore next */
        (cov_808r7p3i1().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_808r7p3i1().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_808r7p3i1().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_808r7p3i1().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_808r7p3i1().b[15][0]++;
          cov_808r7p3i1().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_808r7p3i1().b[15][1]++;
        }
        cov_808r7p3i1().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_808r7p3i1().b[21][0]++;
          cov_808r7p3i1().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_808r7p3i1().b[21][1]++;
        }
        cov_808r7p3i1().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_808r7p3i1().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_808r7p3i1().b[22][1]++;
            cov_808r7p3i1().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_808r7p3i1().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_808r7p3i1().b[22][2]++;
            cov_808r7p3i1().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_808r7p3i1().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_808r7p3i1().b[22][3]++;
            cov_808r7p3i1().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_808r7p3i1().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_808r7p3i1().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_808r7p3i1().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_808r7p3i1().b[22][4]++;
            cov_808r7p3i1().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_808r7p3i1().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_808r7p3i1().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_808r7p3i1().b[22][5]++;
            cov_808r7p3i1().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_808r7p3i1().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_808r7p3i1().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_808r7p3i1().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_808r7p3i1().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_808r7p3i1().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_808r7p3i1().b[23][0]++;
              cov_808r7p3i1().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_808r7p3i1().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_808r7p3i1().b[23][1]++;
            }
            cov_808r7p3i1().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_808r7p3i1().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_808r7p3i1().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_808r7p3i1().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_808r7p3i1().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_808r7p3i1().b[26][0]++;
              cov_808r7p3i1().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_808r7p3i1().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_808r7p3i1().b[26][1]++;
            }
            cov_808r7p3i1().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_808r7p3i1().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_808r7p3i1().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_808r7p3i1().b[28][0]++;
              cov_808r7p3i1().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_808r7p3i1().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_808r7p3i1().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_808r7p3i1().b[28][1]++;
            }
            cov_808r7p3i1().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_808r7p3i1().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_808r7p3i1().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_808r7p3i1().b[30][0]++;
              cov_808r7p3i1().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_808r7p3i1().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_808r7p3i1().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_808r7p3i1().b[30][1]++;
            }
            cov_808r7p3i1().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_808r7p3i1().b[32][0]++;
              cov_808r7p3i1().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_808r7p3i1().b[32][1]++;
            }
            cov_808r7p3i1().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_808r7p3i1().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_808r7p3i1().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_808r7p3i1().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_808r7p3i1().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_808r7p3i1().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_808r7p3i1().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_808r7p3i1().b[33][0]++;
      cov_808r7p3i1().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_808r7p3i1().b[33][1]++;
    }
    cov_808r7p3i1().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_808r7p3i1().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_808r7p3i1().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_808r7p3i1().s[78]++,
/* istanbul ignore next */
(cov_808r7p3i1().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_808r7p3i1().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_808r7p3i1().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_808r7p3i1().f[15]++;
  cov_808r7p3i1().s[79]++;
  return /* istanbul ignore next */(cov_808r7p3i1().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_808r7p3i1().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_808r7p3i1().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_808r7p3i1().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_808r7p3i1().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_808r7p3i1().s[81]++;
exports.PATCH = exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_808r7p3i1().s[82]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_808r7p3i1().s[83]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_808r7p3i1().s[84]++, require("@/lib/auth"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_808r7p3i1().s[85]++, require("@/lib/unified-api-error-handler"));
var prisma_1 =
/* istanbul ignore next */
(cov_808r7p3i1().s[86]++, __importDefault(require("@/lib/prisma")));
var enhancedAssessmentService_1 =
/* istanbul ignore next */
(cov_808r7p3i1().s[87]++, require("@/lib/enhancedAssessmentService"));
/* istanbul ignore next */
cov_808r7p3i1().s[88]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_808r7p3i1().f[16]++;
  cov_808r7p3i1().s[89]++;
  return __awaiter(void 0, [request_1, _a], Promise, function (request, _b) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[17]++;
    var session, assessmentId, error, error, assessment, error, error, responseData, enhancedResults;
    var _c;
    var params =
    /* istanbul ignore next */
    (cov_808r7p3i1().s[90]++, _b.params);
    /* istanbul ignore next */
    cov_808r7p3i1().s[91]++;
    return __generator(this, function (_d) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[18]++;
      cov_808r7p3i1().s[92]++;
      switch (_d.label) {
        case 0:
          /* istanbul ignore next */
          cov_808r7p3i1().b[38][0]++;
          cov_808r7p3i1().s[93]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_808r7p3i1().b[38][1]++;
          cov_808r7p3i1().s[94]++;
          session = _d.sent();
          /* istanbul ignore next */
          cov_808r7p3i1().s[95]++;
          return [4 /*yield*/, params];
        case 2:
          /* istanbul ignore next */
          cov_808r7p3i1().b[38][2]++;
          cov_808r7p3i1().s[96]++;
          assessmentId = _d.sent().id;
          /* istanbul ignore next */
          cov_808r7p3i1().s[97]++;
          if (!(
          /* istanbul ignore next */
          (cov_808r7p3i1().b[41][0]++, (_c =
          /* istanbul ignore next */
          (cov_808r7p3i1().b[43][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_808r7p3i1().b[43][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[42][0]++, void 0) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[42][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_808r7p3i1().b[41][1]++, _c === void 0) ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[40][0]++, void 0) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[40][1]++, _c.id))) {
            /* istanbul ignore next */
            cov_808r7p3i1().b[39][0]++;
            cov_808r7p3i1().s[98]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_808r7p3i1().s[99]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_808r7p3i1().s[100]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[39][1]++;
          }
          cov_808r7p3i1().s[101]++;
          if (!assessmentId) {
            /* istanbul ignore next */
            cov_808r7p3i1().b[44][0]++;
            cov_808r7p3i1().s[102]++;
            error = new Error('Assessment ID is required');
            /* istanbul ignore next */
            cov_808r7p3i1().s[103]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_808r7p3i1().s[104]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[44][1]++;
          }
          cov_808r7p3i1().s[105]++;
          return [4 /*yield*/, prisma_1.default.assessment.findFirst({
            where: {
              id: assessmentId,
              userId: session.user.id
            },
            include: {
              responses: true
            }
          })];
        case 3:
          /* istanbul ignore next */
          cov_808r7p3i1().b[38][3]++;
          cov_808r7p3i1().s[106]++;
          assessment = _d.sent();
          /* istanbul ignore next */
          cov_808r7p3i1().s[107]++;
          if (!assessment) {
            /* istanbul ignore next */
            cov_808r7p3i1().b[45][0]++;
            cov_808r7p3i1().s[108]++;
            error = new Error('Assessment not found or access denied');
            /* istanbul ignore next */
            cov_808r7p3i1().s[109]++;
            error.statusCode = 404;
            /* istanbul ignore next */
            cov_808r7p3i1().s[110]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[45][1]++;
          }
          cov_808r7p3i1().s[111]++;
          if (assessment.status !== 'COMPLETED') {
            /* istanbul ignore next */
            cov_808r7p3i1().b[46][0]++;
            cov_808r7p3i1().s[112]++;
            error = new Error('Assessment is not completed');
            /* istanbul ignore next */
            cov_808r7p3i1().s[113]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_808r7p3i1().s[114]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[46][1]++;
          }
          cov_808r7p3i1().s[115]++;
          responseData = {};
          /* istanbul ignore next */
          cov_808r7p3i1().s[116]++;
          assessment.responses.forEach(function (response) {
            /* istanbul ignore next */
            cov_808r7p3i1().f[19]++;
            cov_808r7p3i1().s[117]++;
            try {
              var value =
              /* istanbul ignore next */
              (cov_808r7p3i1().s[118]++, typeof response.answerValue === 'string' ?
              /* istanbul ignore next */
              (cov_808r7p3i1().b[47][0]++, JSON.parse(response.answerValue)) :
              /* istanbul ignore next */
              (cov_808r7p3i1().b[47][1]++, response.answerValue));
              /* istanbul ignore next */
              cov_808r7p3i1().s[119]++;
              responseData[response.questionKey] = value;
            } catch (_a) {
              /* istanbul ignore next */
              cov_808r7p3i1().s[120]++;
              responseData[response.questionKey] = response.answerValue;
            }
          });
          /* istanbul ignore next */
          cov_808r7p3i1().s[121]++;
          return [4 /*yield*/, enhancedAssessmentService_1.EnhancedAssessmentService.generateEnhancedResults(assessmentId, responseData)];
        case 4:
          /* istanbul ignore next */
          cov_808r7p3i1().b[38][4]++;
          cov_808r7p3i1().s[122]++;
          enhancedResults = _d.sent();
          // Log successful generation
          /* istanbul ignore next */
          cov_808r7p3i1().s[123]++;
          console.log("Enhanced assessment results generated for user ".concat(session.user.id, ", assessment ").concat(assessmentId));
          /* istanbul ignore next */
          cov_808r7p3i1().s[124]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: enhancedResults
          })];
      }
    });
  });
});
// POST endpoint to regenerate results with updated preferences
/* istanbul ignore next */
cov_808r7p3i1().s[125]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_808r7p3i1().f[20]++;
  cov_808r7p3i1().s[126]++;
  return __awaiter(void 0, [request_1, _a], Promise, function (request, _b) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[21]++;
    var session, assessmentId, body, error, error, assessment, error, _c, focusAreas, _d, timeCommitment, _e, budgetPreference, _f, learningStyle, responseData, enhancedResults;
    var _g;
    var params =
    /* istanbul ignore next */
    (cov_808r7p3i1().s[127]++, _b.params);
    /* istanbul ignore next */
    cov_808r7p3i1().s[128]++;
    return __generator(this, function (_h) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[22]++;
      cov_808r7p3i1().s[129]++;
      switch (_h.label) {
        case 0:
          /* istanbul ignore next */
          cov_808r7p3i1().b[48][0]++;
          cov_808r7p3i1().s[130]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_808r7p3i1().b[48][1]++;
          cov_808r7p3i1().s[131]++;
          session = _h.sent();
          /* istanbul ignore next */
          cov_808r7p3i1().s[132]++;
          return [4 /*yield*/, params];
        case 2:
          /* istanbul ignore next */
          cov_808r7p3i1().b[48][2]++;
          cov_808r7p3i1().s[133]++;
          assessmentId = _h.sent().id;
          /* istanbul ignore next */
          cov_808r7p3i1().s[134]++;
          return [4 /*yield*/, request.json()];
        case 3:
          /* istanbul ignore next */
          cov_808r7p3i1().b[48][3]++;
          cov_808r7p3i1().s[135]++;
          body = _h.sent();
          /* istanbul ignore next */
          cov_808r7p3i1().s[136]++;
          if (!(
          /* istanbul ignore next */
          (cov_808r7p3i1().b[51][0]++, (_g =
          /* istanbul ignore next */
          (cov_808r7p3i1().b[53][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_808r7p3i1().b[53][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[52][0]++, void 0) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[52][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_808r7p3i1().b[51][1]++, _g === void 0) ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[50][0]++, void 0) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[50][1]++, _g.id))) {
            /* istanbul ignore next */
            cov_808r7p3i1().b[49][0]++;
            cov_808r7p3i1().s[137]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_808r7p3i1().s[138]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_808r7p3i1().s[139]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[49][1]++;
          }
          cov_808r7p3i1().s[140]++;
          if (!assessmentId) {
            /* istanbul ignore next */
            cov_808r7p3i1().b[54][0]++;
            cov_808r7p3i1().s[141]++;
            error = new Error('Assessment ID is required');
            /* istanbul ignore next */
            cov_808r7p3i1().s[142]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_808r7p3i1().s[143]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[54][1]++;
          }
          cov_808r7p3i1().s[144]++;
          return [4 /*yield*/, prisma_1.default.assessment.findFirst({
            where: {
              id: assessmentId,
              userId: session.user.id
            },
            include: {
              responses: true
            }
          })];
        case 4:
          /* istanbul ignore next */
          cov_808r7p3i1().b[48][4]++;
          cov_808r7p3i1().s[145]++;
          assessment = _h.sent();
          /* istanbul ignore next */
          cov_808r7p3i1().s[146]++;
          if (!assessment) {
            /* istanbul ignore next */
            cov_808r7p3i1().b[55][0]++;
            cov_808r7p3i1().s[147]++;
            error = new Error('Assessment not found or access denied');
            /* istanbul ignore next */
            cov_808r7p3i1().s[148]++;
            error.statusCode = 404;
            /* istanbul ignore next */
            cov_808r7p3i1().s[149]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[55][1]++;
          }
          cov_808r7p3i1().s[150]++;
          _c = body.focusAreas, focusAreas = _c === void 0 ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[56][0]++, []) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[56][1]++, _c), _d = body.timeCommitment, timeCommitment = _d === void 0 ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[57][0]++, 'MODERATE') :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[57][1]++, _d), _e = body.budgetPreference, budgetPreference = _e === void 0 ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[58][0]++, 'FREE_PREFERRED') :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[58][1]++, _e), _f = body.learningStyle, learningStyle = _f === void 0 ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[59][0]++, 'MIXED') :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[59][1]++, _f);
          /* istanbul ignore next */
          cov_808r7p3i1().s[151]++;
          responseData = {};
          /* istanbul ignore next */
          cov_808r7p3i1().s[152]++;
          assessment.responses.forEach(function (response) {
            /* istanbul ignore next */
            cov_808r7p3i1().f[23]++;
            cov_808r7p3i1().s[153]++;
            try {
              var value =
              /* istanbul ignore next */
              (cov_808r7p3i1().s[154]++, typeof response.answerValue === 'string' ?
              /* istanbul ignore next */
              (cov_808r7p3i1().b[60][0]++, JSON.parse(response.answerValue)) :
              /* istanbul ignore next */
              (cov_808r7p3i1().b[60][1]++, response.answerValue));
              /* istanbul ignore next */
              cov_808r7p3i1().s[155]++;
              responseData[response.questionKey] = value;
            } catch (_a) {
              /* istanbul ignore next */
              cov_808r7p3i1().s[156]++;
              responseData[response.questionKey] = response.answerValue;
            }
          });
          // Add user preferences to response data
          /* istanbul ignore next */
          cov_808r7p3i1().s[157]++;
          responseData.user_preferences = {
            focusAreas: focusAreas,
            timeCommitment: timeCommitment,
            budgetPreference: budgetPreference,
            learningStyle: learningStyle
          };
          /* istanbul ignore next */
          cov_808r7p3i1().s[158]++;
          return [4 /*yield*/, enhancedAssessmentService_1.EnhancedAssessmentService.generateEnhancedResults(assessmentId, responseData)];
        case 5:
          /* istanbul ignore next */
          cov_808r7p3i1().b[48][5]++;
          cov_808r7p3i1().s[159]++;
          enhancedResults = _h.sent();
          // Log successful regeneration
          /* istanbul ignore next */
          cov_808r7p3i1().s[160]++;
          console.log("Enhanced assessment results regenerated with preferences for user ".concat(session.user.id, ", assessment ").concat(assessmentId));
          /* istanbul ignore next */
          cov_808r7p3i1().s[161]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: __assign(__assign({}, enhancedResults), {
              message: 'Results updated with your preferences'
            })
          })];
      }
    });
  });
});
// PATCH endpoint to save user feedback on recommendations
/* istanbul ignore next */
cov_808r7p3i1().s[162]++;
exports.PATCH = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_808r7p3i1().f[24]++;
  cov_808r7p3i1().s[163]++;
  return __awaiter(void 0, [request_1, _a], Promise, function (request, _b) {
    /* istanbul ignore next */
    cov_808r7p3i1().f[25]++;
    var session, assessmentId, body, error, _c, careerPathFeedback, _d, resourceFeedback, overallRating, comments;
    var _e;
    var params =
    /* istanbul ignore next */
    (cov_808r7p3i1().s[164]++, _b.params);
    /* istanbul ignore next */
    cov_808r7p3i1().s[165]++;
    return __generator(this, function (_f) {
      /* istanbul ignore next */
      cov_808r7p3i1().f[26]++;
      cov_808r7p3i1().s[166]++;
      switch (_f.label) {
        case 0:
          /* istanbul ignore next */
          cov_808r7p3i1().b[61][0]++;
          cov_808r7p3i1().s[167]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_808r7p3i1().b[61][1]++;
          cov_808r7p3i1().s[168]++;
          session = _f.sent();
          /* istanbul ignore next */
          cov_808r7p3i1().s[169]++;
          return [4 /*yield*/, params];
        case 2:
          /* istanbul ignore next */
          cov_808r7p3i1().b[61][2]++;
          cov_808r7p3i1().s[170]++;
          assessmentId = _f.sent().id;
          /* istanbul ignore next */
          cov_808r7p3i1().s[171]++;
          return [4 /*yield*/, request.json()];
        case 3:
          /* istanbul ignore next */
          cov_808r7p3i1().b[61][3]++;
          cov_808r7p3i1().s[172]++;
          body = _f.sent();
          /* istanbul ignore next */
          cov_808r7p3i1().s[173]++;
          if (!(
          /* istanbul ignore next */
          (cov_808r7p3i1().b[64][0]++, (_e =
          /* istanbul ignore next */
          (cov_808r7p3i1().b[66][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_808r7p3i1().b[66][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[65][0]++, void 0) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[65][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_808r7p3i1().b[64][1]++, _e === void 0) ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[63][0]++, void 0) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[63][1]++, _e.id))) {
            /* istanbul ignore next */
            cov_808r7p3i1().b[62][0]++;
            cov_808r7p3i1().s[174]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_808r7p3i1().s[175]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_808r7p3i1().s[176]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_808r7p3i1().b[62][1]++;
          }
          cov_808r7p3i1().s[177]++;
          _c = body.careerPathFeedback, careerPathFeedback = _c === void 0 ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[67][0]++, {}) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[67][1]++, _c), _d = body.resourceFeedback, resourceFeedback = _d === void 0 ?
          /* istanbul ignore next */
          (cov_808r7p3i1().b[68][0]++, {}) :
          /* istanbul ignore next */
          (cov_808r7p3i1().b[68][1]++, _d), overallRating = body.overallRating, comments = body.comments;
          // Save feedback to database (you might want to create a feedback table)
          // For now, we'll log it and return success
          /* istanbul ignore next */
          cov_808r7p3i1().s[178]++;
          console.log("Assessment feedback received from user ".concat(session.user.id, ":"), {
            assessmentId: assessmentId,
            careerPathFeedback: careerPathFeedback,
            resourceFeedback: resourceFeedback,
            overallRating: overallRating,
            comments: comments
          });
          // In a production system, you would save this feedback to improve recommendations
          // await prisma.assessmentFeedback.create({
          //   data: {
          //     assessmentId,
          //     userId: session.user.id,
          //     careerPathFeedback,
          //     resourceFeedback,
          //     overallRating,
          //     comments
          //   }
          // });
          /* istanbul ignore next */
          cov_808r7p3i1().s[179]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              message: 'Thank you for your feedback! This helps us improve our recommendations.'
            }
          })];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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