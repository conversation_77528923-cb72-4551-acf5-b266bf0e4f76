ac500e8e40385b18ad08fcf635d01c4b
"use strict";
/**
 * Test Environment Configuration Issues Tests
 *
 * These tests prove test environment issues including inconsistent environment
 * variables, unsafe database configurations, and missing cleanup hooks.
 *
 * EXPECTED TO FAIL - These tests demonstrate environment configuration issues that need fixing.
 */
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
(0, globals_1.describe)('Test Environment Configuration Issues', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Inconsistent Environment Variables', function () {
        (0, globals_1.it)('should fail - test environment variables are inconsistent across setup files', function () {
            // Check jest.setup.js for proper environment configuration
            var setupFiles = [
                'jest.setup.js',
                'jest.setup.integration.js',
                'jest.polyfills.js'
            ];
            var envVarIssues = [];
            setupFiles.forEach(function (setupFile) {
                var setupPath = path_1.default.join(process.cwd(), setupFile);
                if (fs_1.default.existsSync(setupPath)) {
                    var setupContent_1 = fs_1.default.readFileSync(setupPath, 'utf8');
                    // Required environment variables for testing
                    var requiredEnvVars = [
                        'NEXTAUTH_URL',
                        'NEXTAUTH_SECRET',
                        'DATABASE_URL',
                        'NODE_ENV'
                    ];
                    var missingEnvVars = requiredEnvVars.filter(function (envVar) {
                        return !setupContent_1.includes("process.env.".concat(envVar));
                    });
                    if (missingEnvVars.length > 0) {
                        envVarIssues.push({ file: setupFile, missingEnvVars: missingEnvVars });
                    }
                    // Check for hardcoded test values that might cause issues
                    var hasHardcodedSecrets = setupContent_1.includes('test-secret') ||
                        setupContent_1.includes('localhost:5432');
                    if (hasHardcodedSecrets) {
                        envVarIssues.push({ file: setupFile, issue: 'Contains hardcoded secrets' });
                    }
                }
                else {
                    envVarIssues.push({ file: setupFile, issue: 'Setup file does not exist' });
                }
            });
            // EXPECTED TO FAIL: All setup files should have consistent environment configuration
            (0, globals_1.expect)(envVarIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - environment variables have different values across test configurations', function () {
            // Check for environment variable consistency
            var configFiles = [
                'jest.config.js',
                'jest.setup.js',
                'jest.setup.integration.js',
                '.env.test',
                '.env.local'
            ];
            var envValues = {};
            var inconsistencies = [];
            configFiles.forEach(function (configFile) {
                var configPath = path_1.default.join(process.cwd(), configFile);
                if (fs_1.default.existsSync(configPath)) {
                    var content = fs_1.default.readFileSync(configPath, 'utf8');
                    // Extract environment variable assignments
                    var envMatches = content.match(/process\.env\.(\w+)\s*=\s*['"`]([^'"`]+)['"`]/g) || [];
                    envMatches.forEach(function (match) {
                        var _a = match.match(/process\.env\.(\w+)\s*=\s*['"`]([^'"`]+)['"`]/), varName = _a[1], value = _a[2];
                        if (!envValues[varName]) {
                            envValues[varName] = [];
                        }
                        envValues[varName].push({ file: configFile, value: value });
                    });
                }
            });
            // Check for inconsistent values
            Object.entries(envValues).forEach(function (_a) {
                var varName = _a[0], assignments = _a[1];
                if (assignments.length > 1) {
                    var uniqueValues = __spreadArray([], new Set(assignments.map(function (a) { return a.value; })), true);
                    if (uniqueValues.length > 1) {
                        inconsistencies.push({ variable: varName, assignments: assignments });
                    }
                }
            });
            // EXPECTED TO FAIL: Environment variables should have consistent values
            (0, globals_1.expect)(inconsistencies.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Unsafe Database Configuration', function () {
        (0, globals_1.it)('should fail - test database configuration is unsafe for production', function () {
            // Check for proper test database isolation
            var setupFiles = [
                'jest.setup.js',
                'jest.setup.integration.js'
            ];
            var unsafeConfigurations = [];
            setupFiles.forEach(function (setupFile) {
                var setupPath = path_1.default.join(process.cwd(), setupFile);
                if (fs_1.default.existsSync(setupPath)) {
                    var content = fs_1.default.readFileSync(setupPath, 'utf8');
                    // Check for unsafe database configurations
                    if (content.includes('DATABASE_URL') && !content.includes('test')) {
                        unsafeConfigurations.push("".concat(setupFile, ": DATABASE_URL doesn't include 'test'"));
                    }
                    // Check for production database references
                    if (content.includes('production') || content.includes('prod')) {
                        unsafeConfigurations.push("".concat(setupFile, ": Contains production references"));
                    }
                    // Check for real database URLs
                    var dbUrlMatches = content.match(/postgresql:\/\/[^'"`\s]+/g) || [];
                    dbUrlMatches.forEach(function (url) {
                        if (!url.includes('test') && !url.includes('localhost')) {
                            unsafeConfigurations.push("".concat(setupFile, ": Real database URL detected: ").concat(url));
                        }
                    });
                }
            });
            // EXPECTED TO FAIL: Test database configuration should be safe
            (0, globals_1.expect)(unsafeConfigurations.length).toBe(0);
        });
        (0, globals_1.it)('should fail - database cleanup hooks are missing or inadequate', function () {
            // Check for database cleanup in test files
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var missingCleanupTests = [];
            function checkDatabaseCleanup(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        checkDatabaseCleanup(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check if test uses database operations
                            var hasDbOperations = content.includes('prisma') ||
                                content.includes('database') ||
                                content.includes('PrismaClient');
                            if (hasDbOperations) {
                                // Check for cleanup hooks
                                var hasBeforeEach = content.includes('beforeEach');
                                var hasAfterEach = content.includes('afterEach');
                                var hasCleanup = content.includes('cleanup') ||
                                    content.includes('clear') ||
                                    content.includes('reset') ||
                                    content.includes('truncate');
                                if (!hasAfterEach && !hasCleanup) {
                                    missingCleanupTests.push({
                                        file: filePath,
                                        hasBeforeEach: hasBeforeEach,
                                        hasAfterEach: hasAfterEach,
                                        hasCleanup: hasCleanup
                                    });
                                }
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            checkDatabaseCleanup(testDirectory);
            // EXPECTED TO FAIL: Database tests should have proper cleanup
            (0, globals_1.expect)(missingCleanupTests.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Test Isolation Problems', function () {
        (0, globals_1.it)('should fail - tests share state and interfere with each other', function () {
            // Check for global state sharing issues
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var stateInterferenceIssues = [];
            function checkStateIsolation(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        checkStateIsolation(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for global variable usage
                            var globalVarUsage = (content.match(/global\.|window\.|process\.env\./g) || []).length;
                            var hasStateReset = content.includes('beforeEach') ||
                                content.includes('afterEach') ||
                                content.includes('jest.clearAllMocks');
                            // Check for shared test data
                            var hasSharedData = content.includes('let ') &&
                                !content.includes('beforeEach') &&
                                (content.match(/it\(/g) || []).length > 1;
                            if ((globalVarUsage > 5 && !hasStateReset) || hasSharedData) {
                                stateInterferenceIssues.push({
                                    file: filePath,
                                    globalVarUsage: globalVarUsage,
                                    hasStateReset: hasStateReset,
                                    hasSharedData: hasSharedData
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            checkStateIsolation(testDirectory);
            // EXPECTED TO FAIL: Tests should not share state
            (0, globals_1.expect)(stateInterferenceIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - mock configurations leak between tests', function () {
            // Check for mock leakage issues
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var mockLeakageIssues = [];
            function checkMockLeakage(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        checkMockLeakage(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            var mockCount = (content.match(/jest\.mock|mockImplementation|mockReturnValue/g) || []).length;
                            var hasMockClear = content.includes('clearAllMocks') ||
                                content.includes('resetAllMocks') ||
                                content.includes('restoreAllMocks');
                            var testCount = (content.match(/it\(/g) || []).length;
                            // If there are multiple tests with mocks but no mock clearing
                            if (mockCount > 3 && testCount > 1 && !hasMockClear) {
                                mockLeakageIssues.push({
                                    file: filePath,
                                    mockCount: mockCount,
                                    testCount: testCount,
                                    hasMockClear: hasMockClear
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            checkMockLeakage(testDirectory);
            // EXPECTED TO FAIL: Mocks should not leak between tests
            (0, globals_1.expect)(mockLeakageIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Test Configuration Inconsistencies', function () {
        (0, globals_1.it)('should fail - Jest configuration has conflicting settings', function () {
            // Check Jest configuration for conflicts
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                var configIssues_1 = [];
                // Check for conflicting test environments
                var testEnvMatches = configContent.match(/testEnvironment:\s*['"`]([^'"`]+)['"`]/);
                var testEnv = testEnvMatches ? testEnvMatches[1] : 'node';
                // Check for DOM-related tests with node environment
                if (testEnv === 'node' && configContent.includes('jsdom')) {
                    configIssues_1.push('Conflicting test environment: node vs jsdom');
                }
                // Check for module name mapping conflicts
                var moduleMapMatches = configContent.match(/moduleNameMapper:\s*{([^}]+)}/s);
                if (moduleMapMatches) {
                    var moduleMap = moduleMapMatches[1];
                    var mappings = moduleMap.split(',');
                    // Check for conflicting path mappings
                    var pathMappings_1 = {};
                    mappings.forEach(function (mapping) {
                        var match = mapping.match(/['"`]([^'"`]+)['"`]:\s*['"`]([^'"`]+)['"`]/);
                        if (match) {
                            var pattern = match[1], replacement = match[2];
                            if (pathMappings_1[pattern] && pathMappings_1[pattern] !== replacement) {
                                configIssues_1.push("Conflicting module mapping for ".concat(pattern));
                            }
                            pathMappings_1[pattern] = replacement;
                        }
                    });
                }
                // Check for coverage threshold conflicts
                var coverageMatches = configContent.match(/coverageThreshold:\s*{[^}]+global:\s*{([^}]+)}/s);
                if (coverageMatches) {
                    var thresholds = coverageMatches[1];
                    var branchesMatch = thresholds.match(/branches:\s*(\d+)/);
                    var linesMatch = thresholds.match(/lines:\s*(\d+)/);
                    if (branchesMatch && linesMatch) {
                        var branches = parseInt(branchesMatch[1]);
                        var lines = parseInt(linesMatch[1]);
                        // Branches threshold should not be higher than lines threshold
                        if (branches > lines) {
                            configIssues_1.push('Branch coverage threshold higher than line coverage');
                        }
                    }
                }
                // EXPECTED TO FAIL: Jest configuration should not have conflicts
                (0, globals_1.expect)(configIssues_1.length).toBe(0);
            }
            else {
                // EXPECTED TO FAIL: Jest config should exist
                (0, globals_1.expect)(fs_1.default.existsSync(jestConfigPath)).toBe(true);
            }
        });
        (0, globals_1.it)('should fail - test scripts in package.json are incomplete or conflicting', function () {
            var packageJsonPath = path_1.default.join(process.cwd(), 'package.json');
            if (fs_1.default.existsSync(packageJsonPath)) {
                var packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf8'));
                var scripts_1 = packageJson.scripts || {};
                var scriptIssues = [];
                // Check for required test scripts
                var requiredTestScripts = [
                    'test',
                    'test:coverage',
                    'test:watch',
                    'test:ci'
                ];
                var missingScripts = requiredTestScripts.filter(function (script) { return !scripts_1[script]; });
                if (missingScripts.length > 0) {
                    scriptIssues.push("Missing required scripts: ".concat(missingScripts.join(', ')));
                }
                // Check for conflicting test configurations
                var testScript = scripts_1['test'];
                var testCiScript = scripts_1['test:ci'];
                if (testScript && testCiScript) {
                    // CI script should disable watch mode
                    if (testCiScript.includes('--watch') && !testCiScript.includes('--watchAll=false')) {
                        scriptIssues.push('CI test script should disable watch mode');
                    }
                    // CI script should include coverage
                    if (!testCiScript.includes('--coverage')) {
                        scriptIssues.push('CI test script should include coverage reporting');
                    }
                }
                // EXPECTED TO FAIL: Package.json test scripts should be complete and consistent
                (0, globals_1.expect)(scriptIssues.length).toBe(0);
            }
            else {
                // EXPECTED TO FAIL: package.json should exist
                (0, globals_1.expect)(fs_1.default.existsSync(packageJsonPath)).toBe(true);
            }
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: External Service Configuration Problems', function () {
        (0, globals_1.it)('should fail - external service mocks are not properly configured for testing', function () {
            // Check for external service mock configuration
            var setupFiles = [
                'jest.setup.js',
                'jest.setup.integration.js',
                '__mocks__'
            ];
            var externalServiceIssues = [];
            // External services that should be mocked in tests
            var externalServices = [
                'fetch',
                'gemini',
                'openai',
                'resend',
                'sentry'
            ];
            setupFiles.forEach(function (setupFile) {
                var setupPath = path_1.default.join(process.cwd(), setupFile);
                if (fs_1.default.existsSync(setupPath)) {
                    var isDirectory = fs_1.default.statSync(setupPath).isDirectory();
                    if (isDirectory) {
                        // Check __mocks__ directory
                        var mockFiles = fs_1.default.readdirSync(setupPath);
                        var mockedServices_1 = mockFiles.map(function (file) { return file.replace(/\.(js|ts)$/, ''); });
                        var unmockedServices = externalServices.filter(function (service) {
                            return !mockedServices_1.includes(service);
                        });
                        if (unmockedServices.length > 2) {
                            externalServiceIssues.push({
                                location: setupFile,
                                issue: "Unmocked external services: ".concat(unmockedServices.join(', '))
                            });
                        }
                    }
                    else {
                        // Check setup file content
                        var content_1 = fs_1.default.readFileSync(setupPath, 'utf8');
                        var mockedInSetup = externalServices.filter(function (service) {
                            return content_1.includes("jest.mock") && content_1.includes(service);
                        });
                        // Should mock critical external services
                        if (mockedInSetup.length < 2) {
                            externalServiceIssues.push({
                                location: setupFile,
                                issue: 'Insufficient external service mocking'
                            });
                        }
                    }
                }
            });
            // EXPECTED TO FAIL: External services should be properly mocked
            (0, globals_1.expect)(externalServiceIssues.length).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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