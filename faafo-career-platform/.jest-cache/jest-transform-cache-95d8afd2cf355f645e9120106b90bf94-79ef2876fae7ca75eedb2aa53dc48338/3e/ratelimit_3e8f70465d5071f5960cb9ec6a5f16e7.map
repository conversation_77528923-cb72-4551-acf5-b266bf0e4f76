{"version": 3, "names": ["cov_6rrxujlk7", "actualCoverage", "exports", "withRateLimit", "s", "getClientIP", "createUserRateLimiter", "cleanupRateLimitStore", "config_1", "require", "rateLimitStore", "Map", "RateLimiter", "f", "options", "b", "requests", "CONFIG", "API", "RATE_LIMIT_REQUESTS", "windowMs", "RATE_LIMIT_WINDOW_MS", "keyGenerator", "defaultKeyGenerator", "skipSuccessfulRequests", "skipFailedRequests", "prototype", "request", "cleanupExpiredEntries", "now", "Date", "keysToDelete", "for<PERSON>ach", "value", "key", "resetTime", "push", "delete", "check", "windowStart", "windowEnd", "entry", "get", "count", "set", "allowed", "remaining", "Math", "max", "limit", "increment", "rateLimiters", "api", "auth", "passwordReset", "search", "write", "rateLimiter", "result", "__assign", "headers", "toString", "toISOString", "forwarded", "split", "trim", "realIP", "cleaned", "console", "log", "concat", "window", "setInterval"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rate-limit.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { CONFIG } from './config';\n\n// In-memory store for rate limiting (in production, use Redis)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\ninterface RateLimitOptions {\n  requests: number;\n  windowMs: number;\n  keyGenerator?: (request: NextRequest) => string;\n  skipSuccessfulRequests?: boolean;\n  skipFailedRequests?: boolean;\n}\n\nexport class RateLimiter {\n  private options: Required<RateLimitOptions>;\n\n  constructor(options: Partial<RateLimitOptions> = {}) {\n    this.options = {\n      requests: options.requests || CONFIG.API.RATE_LIMIT_REQUESTS,\n      windowMs: options.windowMs || CONFIG.API.RATE_LIMIT_WINDOW_MS,\n      keyGenerator: options.keyGenerator || this.defaultKeyGenerator,\n      skipSuccessfulRequests: options.skipSuccessfulRequests || false,\n      skipFailedRequests: options.skipFailedRequests || false,\n    };\n  }\n\n  private defaultKeyGenerator(request: NextRequest): string {\n    // Use IP address as default key\n    return getClientIP(request);\n  }\n\n  private cleanupExpiredEntries(): void {\n    const now = Date.now();\n    const keysToDelete: string[] = [];\n\n    rateLimitStore.forEach((value, key) => {\n      if (now > value.resetTime) {\n        keysToDelete.push(key);\n      }\n    });\n\n    keysToDelete.forEach(key => rateLimitStore.delete(key));\n  }\n\n  public check(request: NextRequest): {\n    allowed: boolean;\n    limit: number;\n    remaining: number;\n    resetTime: number;\n  } {\n    this.cleanupExpiredEntries();\n\n    const key = this.options.keyGenerator(request);\n    const now = Date.now();\n    const windowStart = now;\n    const windowEnd = windowStart + this.options.windowMs;\n\n    let entry = rateLimitStore.get(key);\n\n    if (!entry || now > entry.resetTime) {\n      // Create new entry or reset expired entry\n      entry = {\n        count: 0,\n        resetTime: windowEnd,\n      };\n      rateLimitStore.set(key, entry);\n    }\n\n    const allowed = entry.count < this.options.requests;\n    const remaining = Math.max(0, this.options.requests - entry.count - 1);\n\n    if (allowed) {\n      entry.count++;\n    }\n\n    return {\n      allowed,\n      limit: this.options.requests,\n      remaining,\n      resetTime: entry.resetTime,\n    };\n  }\n\n  public increment(request: NextRequest): void {\n    const key = this.options.keyGenerator(request);\n    const entry = rateLimitStore.get(key);\n    if (entry) {\n      entry.count++;\n    }\n  }\n}\n\n// Pre-configured rate limiters for different endpoints\nexport const rateLimiters = {\n  // General API rate limiter\n  api: new RateLimiter({\n    requests: 100,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n\n  // Stricter rate limiter for authentication endpoints\n  auth: new RateLimiter({\n    requests: 10,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n\n  // Very strict rate limiter for password reset\n  passwordReset: new RateLimiter({\n    requests: 3,\n    windowMs: 60 * 60 * 1000, // 1 hour\n  }),\n\n  // Moderate rate limiter for search/read operations\n  search: new RateLimiter({\n    requests: 200,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n\n  // Strict rate limiter for write operations\n  write: new RateLimiter({\n    requests: 50,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n};\n\n// Middleware function to apply rate limiting\nexport function withRateLimit(rateLimiter: RateLimiter) {\n  return (request: NextRequest) => {\n    const result = rateLimiter.check(request);\n    \n    return {\n      ...result,\n      headers: {\n        'X-RateLimit-Limit': result.limit.toString(),\n        'X-RateLimit-Remaining': result.remaining.toString(),\n        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),\n      },\n    };\n  };\n}\n\n// Helper function to get client IP\nexport function getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for');\n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n  \n  const realIP = request.headers.get('x-real-ip');\n  if (realIP) {\n    return realIP;\n  }\n  \n  return 'unknown';\n}\n\n// Rate limit by user ID for authenticated requests\nexport function createUserRateLimiter(options: Partial<RateLimitOptions> = {}) {\n  return new RateLimiter({\n    ...options,\n    keyGenerator: (request: NextRequest) => {\n      // This would need to be implemented based on your auth system\n      // For now, fall back to IP-based limiting\n      return getClientIP(request);\n    },\n  });\n}\n\n// Cleanup function to be called periodically\nexport function cleanupRateLimitStore(): void {\n  const now = Date.now();\n  let cleaned = 0;\n  const keysToDelete: string[] = [];\n\n  rateLimitStore.forEach((value, key) => {\n    if (now > value.resetTime) {\n      keysToDelete.push(key);\n    }\n  });\n\n  keysToDelete.forEach(key => {\n    rateLimitStore.delete(key);\n    cleaned++;\n  });\n\n  console.log(`Cleaned up ${cleaned} expired rate limit entries`);\n}\n\n// Auto-cleanup every 5 minutes\nif (typeof window === 'undefined') {\n  setInterval(cleanupRateLimitStore, 5 * 60 * 1000);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+IA;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBAE,OAAA,CAAAC,aAAA,GAAAA,aAAA;AAaC;AAAAH,aAAA,GAAAI,CAAA;AAGDF,OAAA,CAAAG,WAAA,GAAAA,WAAA;AAYC;AAAAL,aAAA,GAAAI,CAAA;AAGDF,OAAA,CAAAI,qBAAA,GAAAA,qBAAA;AASC;AAAAN,aAAA,GAAAI,CAAA;AAGDF,OAAA,CAAAK,qBAAA,GAAAA,qBAAA;AAzKA,IAAAC,QAAA;AAAA;AAAA,CAAAR,aAAA,GAAAI,CAAA,QAAAK,OAAA;AAEA;AACA,IAAMC,cAAc;AAAA;AAAA,CAAAV,aAAA,GAAAI,CAAA,QAAG,IAAIO,GAAG,EAAgD;AAU9E,IAAAC,WAAA;AAAA;AAAA,cAAAZ,aAAA,GAAAI,CAAA;EAAA;EAAAJ,aAAA,GAAAa,CAAA;EAGE,SAAAD,YAAYE,OAAuC;IAAA;IAAAd,aAAA,GAAAa,CAAA;IAAAb,aAAA,GAAAI,CAAA;IAAvC,IAAAU,OAAA;MAAA;MAAAd,aAAA,GAAAe,CAAA;MAAAf,aAAA,GAAAI,CAAA;MAAAU,OAAA,KAAuC;IAAA;IAAA;IAAA;MAAAd,aAAA,GAAAe,CAAA;IAAA;IAAAf,aAAA,GAAAI,CAAA;IACjD,IAAI,CAACU,OAAO,GAAG;MACbE,QAAQ;MAAE;MAAA,CAAAhB,aAAA,GAAAe,CAAA,UAAAD,OAAO,CAACE,QAAQ;MAAA;MAAA,CAAAhB,aAAA,GAAAe,CAAA,UAAIP,QAAA,CAAAS,MAAM,CAACC,GAAG,CAACC,mBAAmB;MAC5DC,QAAQ;MAAE;MAAA,CAAApB,aAAA,GAAAe,CAAA,UAAAD,OAAO,CAACM,QAAQ;MAAA;MAAA,CAAApB,aAAA,GAAAe,CAAA,UAAIP,QAAA,CAAAS,MAAM,CAACC,GAAG,CAACG,oBAAoB;MAC7DC,YAAY;MAAE;MAAA,CAAAtB,aAAA,GAAAe,CAAA,UAAAD,OAAO,CAACQ,YAAY;MAAA;MAAA,CAAAtB,aAAA,GAAAe,CAAA,UAAI,IAAI,CAACQ,mBAAmB;MAC9DC,sBAAsB;MAAE;MAAA,CAAAxB,aAAA,GAAAe,CAAA,UAAAD,OAAO,CAACU,sBAAsB;MAAA;MAAA,CAAAxB,aAAA,GAAAe,CAAA,UAAI,KAAK;MAC/DU,kBAAkB;MAAE;MAAA,CAAAzB,aAAA,GAAAe,CAAA,UAAAD,OAAO,CAACW,kBAAkB;MAAA;MAAA,CAAAzB,aAAA,GAAAe,CAAA,UAAI,KAAK;KACxD;EACH;EAAC;EAAAf,aAAA,GAAAI,CAAA;EAEOQ,WAAA,CAAAc,SAAA,CAAAH,mBAAmB,GAA3B,UAA4BI,OAAoB;IAAA;IAAA3B,aAAA,GAAAa,CAAA;IAAAb,aAAA,GAAAI,CAAA;IAC9C;IACA,OAAOC,WAAW,CAACsB,OAAO,CAAC;EAC7B,CAAC;EAAA;EAAA3B,aAAA,GAAAI,CAAA;EAEOQ,WAAA,CAAAc,SAAA,CAAAE,qBAAqB,GAA7B;IAAA;IAAA5B,aAAA,GAAAa,CAAA;IACE,IAAMgB,GAAG;IAAA;IAAA,CAAA7B,aAAA,GAAAI,CAAA,QAAG0B,IAAI,CAACD,GAAG,EAAE;IACtB,IAAME,YAAY;IAAA;IAAA,CAAA/B,aAAA,GAAAI,CAAA,QAAa,EAAE;IAAC;IAAAJ,aAAA,GAAAI,CAAA;IAElCM,cAAc,CAACsB,OAAO,CAAC,UAACC,KAAK,EAAEC,GAAG;MAAA;MAAAlC,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAI,CAAA;MAChC,IAAIyB,GAAG,GAAGI,KAAK,CAACE,SAAS,EAAE;QAAA;QAAAnC,aAAA,GAAAe,CAAA;QAAAf,aAAA,GAAAI,CAAA;QACzB2B,YAAY,CAACK,IAAI,CAACF,GAAG,CAAC;MACxB,CAAC;MAAA;MAAA;QAAAlC,aAAA,GAAAe,CAAA;MAAA;IACH,CAAC,CAAC;IAAC;IAAAf,aAAA,GAAAI,CAAA;IAEH2B,YAAY,CAACC,OAAO,CAAC,UAAAE,GAAG;MAAA;MAAAlC,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAI,CAAA;MAAI,OAAAM,cAAc,CAAC2B,MAAM,CAACH,GAAG,CAAC;IAA1B,CAA0B,CAAC;EACzD,CAAC;EAAA;EAAAlC,aAAA,GAAAI,CAAA;EAEMQ,WAAA,CAAAc,SAAA,CAAAY,KAAK,GAAZ,UAAaX,OAAoB;IAAA;IAAA3B,aAAA,GAAAa,CAAA;IAAAb,aAAA,GAAAI,CAAA;IAM/B,IAAI,CAACwB,qBAAqB,EAAE;IAE5B,IAAMM,GAAG;IAAA;IAAA,CAAAlC,aAAA,GAAAI,CAAA,QAAG,IAAI,CAACU,OAAO,CAACQ,YAAY,CAACK,OAAO,CAAC;IAC9C,IAAME,GAAG;IAAA;IAAA,CAAA7B,aAAA,GAAAI,CAAA,QAAG0B,IAAI,CAACD,GAAG,EAAE;IACtB,IAAMU,WAAW;IAAA;IAAA,CAAAvC,aAAA,GAAAI,CAAA,QAAGyB,GAAG;IACvB,IAAMW,SAAS;IAAA;IAAA,CAAAxC,aAAA,GAAAI,CAAA,QAAGmC,WAAW,GAAG,IAAI,CAACzB,OAAO,CAACM,QAAQ;IAErD,IAAIqB,KAAK;IAAA;IAAA,CAAAzC,aAAA,GAAAI,CAAA,QAAGM,cAAc,CAACgC,GAAG,CAACR,GAAG,CAAC;IAAC;IAAAlC,aAAA,GAAAI,CAAA;IAEpC;IAAI;IAAA,CAAAJ,aAAA,GAAAe,CAAA,YAAC0B,KAAK;IAAA;IAAA,CAAAzC,aAAA,GAAAe,CAAA,WAAIc,GAAG,GAAGY,KAAK,CAACN,SAAS,GAAE;MAAA;MAAAnC,aAAA,GAAAe,CAAA;MAAAf,aAAA,GAAAI,CAAA;MACnC;MACAqC,KAAK,GAAG;QACNE,KAAK,EAAE,CAAC;QACRR,SAAS,EAAEK;OACZ;MAAC;MAAAxC,aAAA,GAAAI,CAAA;MACFM,cAAc,CAACkC,GAAG,CAACV,GAAG,EAAEO,KAAK,CAAC;IAChC,CAAC;IAAA;IAAA;MAAAzC,aAAA,GAAAe,CAAA;IAAA;IAED,IAAM8B,OAAO;IAAA;IAAA,CAAA7C,aAAA,GAAAI,CAAA,QAAGqC,KAAK,CAACE,KAAK,GAAG,IAAI,CAAC7B,OAAO,CAACE,QAAQ;IACnD,IAAM8B,SAAS;IAAA;IAAA,CAAA9C,aAAA,GAAAI,CAAA,QAAG2C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClC,OAAO,CAACE,QAAQ,GAAGyB,KAAK,CAACE,KAAK,GAAG,CAAC,CAAC;IAAC;IAAA3C,aAAA,GAAAI,CAAA;IAEvE,IAAIyC,OAAO,EAAE;MAAA;MAAA7C,aAAA,GAAAe,CAAA;MAAAf,aAAA,GAAAI,CAAA;MACXqC,KAAK,CAACE,KAAK,EAAE;IACf,CAAC;IAAA;IAAA;MAAA3C,aAAA,GAAAe,CAAA;IAAA;IAAAf,aAAA,GAAAI,CAAA;IAED,OAAO;MACLyC,OAAO,EAAAA,OAAA;MACPI,KAAK,EAAE,IAAI,CAACnC,OAAO,CAACE,QAAQ;MAC5B8B,SAAS,EAAAA,SAAA;MACTX,SAAS,EAAEM,KAAK,CAACN;KAClB;EACH,CAAC;EAAA;EAAAnC,aAAA,GAAAI,CAAA;EAEMQ,WAAA,CAAAc,SAAA,CAAAwB,SAAS,GAAhB,UAAiBvB,OAAoB;IAAA;IAAA3B,aAAA,GAAAa,CAAA;IACnC,IAAMqB,GAAG;IAAA;IAAA,CAAAlC,aAAA,GAAAI,CAAA,QAAG,IAAI,CAACU,OAAO,CAACQ,YAAY,CAACK,OAAO,CAAC;IAC9C,IAAMc,KAAK;IAAA;IAAA,CAAAzC,aAAA,GAAAI,CAAA,QAAGM,cAAc,CAACgC,GAAG,CAACR,GAAG,CAAC;IAAC;IAAAlC,aAAA,GAAAI,CAAA;IACtC,IAAIqC,KAAK,EAAE;MAAA;MAAAzC,aAAA,GAAAe,CAAA;MAAAf,aAAA,GAAAI,CAAA;MACTqC,KAAK,CAACE,KAAK,EAAE;IACf,CAAC;IAAA;IAAA;MAAA3C,aAAA,GAAAe,CAAA;IAAA;EACH,CAAC;EAAA;EAAAf,aAAA,GAAAI,CAAA;EACH,OAAAQ,WAAC;AAAD,CAAC,CA7ED;AA6EC;AAAAZ,aAAA,GAAAI,CAAA;AA7EYF,OAAA,CAAAU,WAAA,GAAAA,WAAA;AA+Eb;AAAA;AAAAZ,aAAA,GAAAI,CAAA;AACaF,OAAA,CAAAiD,YAAY,GAAG;EAC1B;EACAC,GAAG,EAAE,IAAIxC,WAAW,CAAC;IACnBI,QAAQ,EAAE,GAAG;IACbI,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;GAC3B,CAAC;EAEF;EACAiC,IAAI,EAAE,IAAIzC,WAAW,CAAC;IACpBI,QAAQ,EAAE,EAAE;IACZI,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;GAC3B,CAAC;EAEF;EACAkC,aAAa,EAAE,IAAI1C,WAAW,CAAC;IAC7BI,QAAQ,EAAE,CAAC;IACXI,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;GAC3B,CAAC;EAEF;EACAmC,MAAM,EAAE,IAAI3C,WAAW,CAAC;IACtBI,QAAQ,EAAE,GAAG;IACbI,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;GAC3B,CAAC;EAEF;EACAoC,KAAK,EAAE,IAAI5C,WAAW,CAAC;IACrBI,QAAQ,EAAE,EAAE;IACZI,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;GAC3B;CACF;AAED;AACA,SAAgBjB,aAAaA,CAACsD,WAAwB;EAAA;EAAAzD,aAAA,GAAAa,CAAA;EAAAb,aAAA,GAAAI,CAAA;EACpD,OAAO,UAACuB,OAAoB;IAAA;IAAA3B,aAAA,GAAAa,CAAA;IAC1B,IAAM6C,MAAM;IAAA;IAAA,CAAA1D,aAAA,GAAAI,CAAA,QAAGqD,WAAW,CAACnB,KAAK,CAACX,OAAO,CAAC;IAAC;IAAA3B,aAAA,GAAAI,CAAA;IAE1C,OAAAuD,QAAA,CAAAA,QAAA,KACKD,MAAM;MACTE,OAAO,EAAE;QACP,mBAAmB,EAAEF,MAAM,CAACT,KAAK,CAACY,QAAQ,EAAE;QAC5C,uBAAuB,EAAEH,MAAM,CAACZ,SAAS,CAACe,QAAQ,EAAE;QACpD,mBAAmB,EAAE,IAAI/B,IAAI,CAAC4B,MAAM,CAACvB,SAAS,CAAC,CAAC2B,WAAW;;IAC5D;EAEL,CAAC;AACH;AAEA;AACA,SAAgBzD,WAAWA,CAACsB,OAAoB;EAAA;EAAA3B,aAAA,GAAAa,CAAA;EAC9C,IAAMkD,SAAS;EAAA;EAAA,CAAA/D,aAAA,GAAAI,CAAA,QAAGuB,OAAO,CAACiC,OAAO,CAAClB,GAAG,CAAC,iBAAiB,CAAC;EAAC;EAAA1C,aAAA,GAAAI,CAAA;EACzD,IAAI2D,SAAS,EAAE;IAAA;IAAA/D,aAAA,GAAAe,CAAA;IAAAf,aAAA,GAAAI,CAAA;IACb,OAAO2D,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;EACvC,CAAC;EAAA;EAAA;IAAAjE,aAAA,GAAAe,CAAA;EAAA;EAED,IAAMmD,MAAM;EAAA;EAAA,CAAAlE,aAAA,GAAAI,CAAA,QAAGuB,OAAO,CAACiC,OAAO,CAAClB,GAAG,CAAC,WAAW,CAAC;EAAC;EAAA1C,aAAA,GAAAI,CAAA;EAChD,IAAI8D,MAAM,EAAE;IAAA;IAAAlE,aAAA,GAAAe,CAAA;IAAAf,aAAA,GAAAI,CAAA;IACV,OAAO8D,MAAM;EACf,CAAC;EAAA;EAAA;IAAAlE,aAAA,GAAAe,CAAA;EAAA;EAAAf,aAAA,GAAAI,CAAA;EAED,OAAO,SAAS;AAClB;AAEA;AACA,SAAgBE,qBAAqBA,CAACQ,OAAuC;EAAA;EAAAd,aAAA,GAAAa,CAAA;EAAAb,aAAA,GAAAI,CAAA;EAAvC,IAAAU,OAAA;IAAA;IAAAd,aAAA,GAAAe,CAAA;IAAAf,aAAA,GAAAI,CAAA;IAAAU,OAAA,KAAuC;EAAA;EAAA;EAAA;IAAAd,aAAA,GAAAe,CAAA;EAAA;EAAAf,aAAA,GAAAI,CAAA;EAC3E,OAAO,IAAIQ,WAAW,CAAA+C,QAAA,CAAAA,QAAA,KACjB7C,OAAO;IACVQ,YAAY,EAAE,SAAAA,CAACK,OAAoB;MAAA;MAAA3B,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAI,CAAA;MACjC;MACA;MACA,OAAOC,WAAW,CAACsB,OAAO,CAAC;IAC7B;EAAC,GACD;AACJ;AAEA;AACA,SAAgBpB,qBAAqBA,CAAA;EAAA;EAAAP,aAAA,GAAAa,CAAA;EACnC,IAAMgB,GAAG;EAAA;EAAA,CAAA7B,aAAA,GAAAI,CAAA,QAAG0B,IAAI,CAACD,GAAG,EAAE;EACtB,IAAIsC,OAAO;EAAA;EAAA,CAAAnE,aAAA,GAAAI,CAAA,QAAG,CAAC;EACf,IAAM2B,YAAY;EAAA;EAAA,CAAA/B,aAAA,GAAAI,CAAA,QAAa,EAAE;EAAC;EAAAJ,aAAA,GAAAI,CAAA;EAElCM,cAAc,CAACsB,OAAO,CAAC,UAACC,KAAK,EAAEC,GAAG;IAAA;IAAAlC,aAAA,GAAAa,CAAA;IAAAb,aAAA,GAAAI,CAAA;IAChC,IAAIyB,GAAG,GAAGI,KAAK,CAACE,SAAS,EAAE;MAAA;MAAAnC,aAAA,GAAAe,CAAA;MAAAf,aAAA,GAAAI,CAAA;MACzB2B,YAAY,CAACK,IAAI,CAACF,GAAG,CAAC;IACxB,CAAC;IAAA;IAAA;MAAAlC,aAAA,GAAAe,CAAA;IAAA;EACH,CAAC,CAAC;EAAC;EAAAf,aAAA,GAAAI,CAAA;EAEH2B,YAAY,CAACC,OAAO,CAAC,UAAAE,GAAG;IAAA;IAAAlC,aAAA,GAAAa,CAAA;IAAAb,aAAA,GAAAI,CAAA;IACtBM,cAAc,CAAC2B,MAAM,CAACH,GAAG,CAAC;IAAC;IAAAlC,aAAA,GAAAI,CAAA;IAC3B+D,OAAO,EAAE;EACX,CAAC,CAAC;EAAC;EAAAnE,aAAA,GAAAI,CAAA;EAEHgE,OAAO,CAACC,GAAG,CAAC,cAAAC,MAAA,CAAcH,OAAO,gCAA6B,CAAC;AACjE;AAEA;AAAA;AAAAnE,aAAA,GAAAI,CAAA;AACA,IAAI,OAAOmE,MAAM,KAAK,WAAW,EAAE;EAAA;EAAAvE,aAAA,GAAAe,CAAA;EAAAf,aAAA,GAAAI,CAAA;EACjCoE,WAAW,CAACjE,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AACnD,CAAC;AAAA;AAAA;EAAAP,aAAA,GAAAe,CAAA;AAAA", "ignoreList": []}