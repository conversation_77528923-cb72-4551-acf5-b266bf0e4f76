39df031abd2f4daef260533feee4f396
"use strict";

/* istanbul ignore next */
function cov_6rrxujlk7() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rate-limit.ts";
  var hash = "103c5071c82e0f5217c61a441338698a713965d8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rate-limit.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "12": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 52
        }
      },
      "13": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 38
        }
      },
      "14": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 34
        }
      },
      "15": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 54
        }
      },
      "16": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 54
        }
      },
      "17": {
        start: {
          line: 19,
          column: 15
        },
        end: {
          line: 19,
          column: 34
        }
      },
      "18": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 21,
          column: 30
        }
      },
      "19": {
        start: {
          line: 22,
          column: 33
        },
        end: {
          line: 82,
          column: 3
        }
      },
      "20": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 49
        }
      },
      "21": {
        start: {
          line: 24,
          column: 34
        },
        end: {
          line: 24,
          column: 47
        }
      },
      "22": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 31,
          column: 10
        }
      },
      "23": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 36,
          column: 6
        }
      },
      "24": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 36
        }
      },
      "25": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 46,
          column: 6
        }
      },
      "26": {
        start: {
          line: 38,
          column: 18
        },
        end: {
          line: 38,
          column: 28
        }
      },
      "27": {
        start: {
          line: 39,
          column: 27
        },
        end: {
          line: 39,
          column: 29
        }
      },
      "28": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 44,
          column: 11
        }
      },
      "29": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "30": {
        start: {
          line: 42,
          column: 16
        },
        end: {
          line: 42,
          column: 39
        }
      },
      "31": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 84
        }
      },
      "32": {
        start: {
          line: 45,
          column: 46
        },
        end: {
          line: 45,
          column: 80
        }
      },
      "33": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 73,
          column: 6
        }
      },
      "34": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 48,
          column: 37
        }
      },
      "35": {
        start: {
          line: 49,
          column: 18
        },
        end: {
          line: 49,
          column: 52
        }
      },
      "36": {
        start: {
          line: 50,
          column: 18
        },
        end: {
          line: 50,
          column: 28
        }
      },
      "37": {
        start: {
          line: 51,
          column: 26
        },
        end: {
          line: 51,
          column: 29
        }
      },
      "38": {
        start: {
          line: 52,
          column: 24
        },
        end: {
          line: 52,
          column: 59
        }
      },
      "39": {
        start: {
          line: 53,
          column: 20
        },
        end: {
          line: 53,
          column: 43
        }
      },
      "40": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 61,
          column: 9
        }
      },
      "41": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 59,
          column: 14
        }
      },
      "42": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 60,
          column: 43
        }
      },
      "43": {
        start: {
          line: 62,
          column: 22
        },
        end: {
          line: 62,
          column: 57
        }
      },
      "44": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 63,
          column: 76
        }
      },
      "45": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "46": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 26
        }
      },
      "47": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 72,
          column: 10
        }
      },
      "48": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 80,
          column: 6
        }
      },
      "49": {
        start: {
          line: 75,
          column: 18
        },
        end: {
          line: 75,
          column: 52
        }
      },
      "50": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 43
        }
      },
      "51": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 79,
          column: 9
        }
      },
      "52": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 26
        }
      },
      "53": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 81,
          column: 23
        }
      },
      "54": {
        start: {
          line: 83,
          column: 0
        },
        end: {
          line: 83,
          column: 34
        }
      },
      "55": {
        start: {
          line: 85,
          column: 0
        },
        end: {
          line: 111,
          column: 2
        }
      },
      "56": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 121,
          column: 6
        }
      },
      "57": {
        start: {
          line: 115,
          column: 21
        },
        end: {
          line: 115,
          column: 47
        }
      },
      "58": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 120,
          column: 17
        }
      },
      "59": {
        start: {
          line: 125,
          column: 20
        },
        end: {
          line: 125,
          column: 58
        }
      },
      "60": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 128,
          column: 5
        }
      },
      "61": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 46
        }
      },
      "62": {
        start: {
          line: 129,
          column: 17
        },
        end: {
          line: 129,
          column: 49
        }
      },
      "63": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "64": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 22
        }
      },
      "65": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 133,
          column: 21
        }
      },
      "66": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 137,
          column: 45
        }
      },
      "67": {
        start: {
          line: 137,
          column: 30
        },
        end: {
          line: 137,
          column: 43
        }
      },
      "68": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 142,
          column: 14
        }
      },
      "69": {
        start: {
          line: 141,
          column: 12
        },
        end: {
          line: 141,
          column: 40
        }
      },
      "70": {
        start: {
          line: 146,
          column: 14
        },
        end: {
          line: 146,
          column: 24
        }
      },
      "71": {
        start: {
          line: 147,
          column: 18
        },
        end: {
          line: 147,
          column: 19
        }
      },
      "72": {
        start: {
          line: 148,
          column: 23
        },
        end: {
          line: 148,
          column: 25
        }
      },
      "73": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 153,
          column: 7
        }
      },
      "74": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 152,
          column: 9
        }
      },
      "75": {
        start: {
          line: 151,
          column: 12
        },
        end: {
          line: 151,
          column: 35
        }
      },
      "76": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 157,
          column: 7
        }
      },
      "77": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 155,
          column: 35
        }
      },
      "78": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 156,
          column: 18
        }
      },
      "79": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 158,
          column: 78
        }
      },
      "80": {
        start: {
          line: 161,
          column: 0
        },
        end: {
          line: 163,
          column: 1
        }
      },
      "81": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 162,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 34
          }
        },
        loc: {
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 82,
            column: 1
          }
        },
        line: 22
      },
      "3": {
        name: "RateLimiter",
        decl: {
          start: {
            line: 23,
            column: 13
          },
          end: {
            line: 23,
            column: 24
          }
        },
        loc: {
          start: {
            line: 23,
            column: 34
          },
          end: {
            line: 32,
            column: 5
          }
        },
        line: 23
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 33,
            column: 48
          },
          end: {
            line: 33,
            column: 49
          }
        },
        loc: {
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 33
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 37,
            column: 50
          },
          end: {
            line: 37,
            column: 51
          }
        },
        loc: {
          start: {
            line: 37,
            column: 62
          },
          end: {
            line: 46,
            column: 5
          }
        },
        line: 37
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 40,
            column: 31
          },
          end: {
            line: 40,
            column: 32
          }
        },
        loc: {
          start: {
            line: 40,
            column: 53
          },
          end: {
            line: 44,
            column: 9
          }
        },
        line: 40
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 45,
            column: 29
          },
          end: {
            line: 45,
            column: 30
          }
        },
        loc: {
          start: {
            line: 45,
            column: 44
          },
          end: {
            line: 45,
            column: 82
          }
        },
        line: 45
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 47,
            column: 34
          },
          end: {
            line: 47,
            column: 35
          }
        },
        loc: {
          start: {
            line: 47,
            column: 53
          },
          end: {
            line: 73,
            column: 5
          }
        },
        line: 47
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 74,
            column: 38
          },
          end: {
            line: 74,
            column: 39
          }
        },
        loc: {
          start: {
            line: 74,
            column: 57
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 74
      },
      "10": {
        name: "withRateLimit",
        decl: {
          start: {
            line: 113,
            column: 9
          },
          end: {
            line: 113,
            column: 22
          }
        },
        loc: {
          start: {
            line: 113,
            column: 36
          },
          end: {
            line: 122,
            column: 1
          }
        },
        line: 113
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 114,
            column: 11
          },
          end: {
            line: 114,
            column: 12
          }
        },
        loc: {
          start: {
            line: 114,
            column: 30
          },
          end: {
            line: 121,
            column: 5
          }
        },
        line: 114
      },
      "12": {
        name: "getClientIP",
        decl: {
          start: {
            line: 124,
            column: 9
          },
          end: {
            line: 124,
            column: 20
          }
        },
        loc: {
          start: {
            line: 124,
            column: 30
          },
          end: {
            line: 134,
            column: 1
          }
        },
        line: 124
      },
      "13": {
        name: "createUserRateLimiter",
        decl: {
          start: {
            line: 136,
            column: 9
          },
          end: {
            line: 136,
            column: 30
          }
        },
        loc: {
          start: {
            line: 136,
            column: 40
          },
          end: {
            line: 143,
            column: 1
          }
        },
        line: 136
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 138,
            column: 75
          },
          end: {
            line: 138,
            column: 76
          }
        },
        loc: {
          start: {
            line: 138,
            column: 94
          },
          end: {
            line: 142,
            column: 9
          }
        },
        line: 138
      },
      "15": {
        name: "cleanupRateLimitStore",
        decl: {
          start: {
            line: 145,
            column: 9
          },
          end: {
            line: 145,
            column: 30
          }
        },
        loc: {
          start: {
            line: 145,
            column: 33
          },
          end: {
            line: 159,
            column: 1
          }
        },
        line: 145
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 149,
            column: 27
          },
          end: {
            line: 149,
            column: 28
          }
        },
        loc: {
          start: {
            line: 149,
            column: 49
          },
          end: {
            line: 153,
            column: 5
          }
        },
        line: 149
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 154,
            column: 25
          },
          end: {
            line: 154,
            column: 26
          }
        },
        loc: {
          start: {
            line: 154,
            column: 40
          },
          end: {
            line: 157,
            column: 5
          }
        },
        line: 154
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 24,
            column: 8
          },
          end: {
            line: 24,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 8
          },
          end: {
            line: 24,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "4": {
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 26,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 26,
            column: 38
          }
        }, {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 81
          }
        }],
        line: 26
      },
      "5": {
        loc: {
          start: {
            line: 27,
            column: 22
          },
          end: {
            line: 27,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 22
          },
          end: {
            line: 27,
            column: 38
          }
        }, {
          start: {
            line: 27,
            column: 42
          },
          end: {
            line: 27,
            column: 82
          }
        }],
        line: 27
      },
      "6": {
        loc: {
          start: {
            line: 28,
            column: 26
          },
          end: {
            line: 28,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 26
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: 28,
            column: 50
          },
          end: {
            line: 28,
            column: 74
          }
        }],
        line: 28
      },
      "7": {
        loc: {
          start: {
            line: 29,
            column: 36
          },
          end: {
            line: 29,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 36
          },
          end: {
            line: 29,
            column: 66
          }
        }, {
          start: {
            line: 29,
            column: 70
          },
          end: {
            line: 29,
            column: 75
          }
        }],
        line: 29
      },
      "8": {
        loc: {
          start: {
            line: 30,
            column: 32
          },
          end: {
            line: 30,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 32
          },
          end: {
            line: 30,
            column: 58
          }
        }, {
          start: {
            line: 30,
            column: 62
          },
          end: {
            line: 30,
            column: 67
          }
        }],
        line: 30
      },
      "9": {
        loc: {
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "10": {
        loc: {
          start: {
            line: 54,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 54,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 54
      },
      "11": {
        loc: {
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 18
          }
        }, {
          start: {
            line: 54,
            column: 22
          },
          end: {
            line: 54,
            column: 43
          }
        }],
        line: 54
      },
      "12": {
        loc: {
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 66,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 66,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "13": {
        loc: {
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 79,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 79,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "14": {
        loc: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "15": {
        loc: {
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "16": {
        loc: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "17": {
        loc: {
          start: {
            line: 150,
            column: 8
          },
          end: {
            line: 152,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 8
          },
          end: {
            line: 152,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "18": {
        loc: {
          start: {
            line: 161,
            column: 0
          },
          end: {
            line: 163,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 0
          },
          end: {
            line: 163,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rate-limit.ts",
      mappings: ";;;;;;;;;;;;;;AA+HA,sCAaC;AAGD,kCAYC;AAGD,sDASC;AAGD,sDAiBC;AA1LD,mCAAkC;AAElC,+DAA+D;AAC/D,IAAM,cAAc,GAAG,IAAI,GAAG,EAAgD,CAAC;AAU/E;IAGE,qBAAY,OAAuC;QAAvC,wBAAA,EAAA,YAAuC;QACjD,IAAI,CAAC,OAAO,GAAG;YACb,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,eAAM,CAAC,GAAG,CAAC,mBAAmB;YAC5D,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,eAAM,CAAC,GAAG,CAAC,oBAAoB;YAC7D,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB;YAC9D,sBAAsB,EAAE,OAAO,CAAC,sBAAsB,IAAI,KAAK;YAC/D,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,IAAI,KAAK;SACxD,CAAC;IACJ,CAAC;IAEO,yCAAmB,GAA3B,UAA4B,OAAoB;QAC9C,gCAAgC;QAChC,OAAO,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEO,2CAAqB,GAA7B;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAM,YAAY,GAAa,EAAE,CAAC;QAElC,cAAc,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;YAChC,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC1B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,YAAY,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAC;IAC1D,CAAC;IAEM,2BAAK,GAAZ,UAAa,OAAoB;QAM/B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAM,WAAW,GAAG,GAAG,CAAC;QACxB,IAAM,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAEtD,IAAI,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,CAAC,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACpC,0CAA0C;YAC1C,KAAK,GAAG;gBACN,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,SAAS;aACrB,CAAC;YACF,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,CAAC;QAED,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACpD,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAEvE,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;QAED,OAAO;YACL,OAAO,SAAA;YACP,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC5B,SAAS,WAAA;YACT,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CAAC;IACJ,CAAC;IAEM,+BAAS,GAAhB,UAAiB,OAAoB;QACnC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAM,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;IACH,kBAAC;AAAD,CAAC,AA7ED,IA6EC;AA7EY,kCAAW;AA+ExB,uDAAuD;AAC1C,QAAA,YAAY,GAAG;IAC1B,2BAA2B;IAC3B,GAAG,EAAE,IAAI,WAAW,CAAC;QACnB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;KACxC,CAAC;IAEF,qDAAqD;IACrD,IAAI,EAAE,IAAI,WAAW,CAAC;QACpB,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;KACxC,CAAC;IAEF,8CAA8C;IAC9C,aAAa,EAAE,IAAI,WAAW,CAAC;QAC7B,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;KACpC,CAAC;IAEF,mDAAmD;IACnD,MAAM,EAAE,IAAI,WAAW,CAAC;QACtB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;KACxC,CAAC;IAEF,2CAA2C;IAC3C,KAAK,EAAE,IAAI,WAAW,CAAC;QACrB,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;KACxC,CAAC;CACH,CAAC;AAEF,6CAA6C;AAC7C,SAAgB,aAAa,CAAC,WAAwB;IACpD,OAAO,UAAC,OAAoB;QAC1B,IAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE1C,6BACK,MAAM,KACT,OAAO,EAAE;gBACP,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC5C,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACpD,mBAAmB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;aAC9D,IACD;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,mCAAmC;AACnC,SAAgB,WAAW,CAAC,OAAoB;IAC9C,IAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACzD,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;IAED,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAChD,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,mDAAmD;AACnD,SAAgB,qBAAqB,CAAC,OAAuC;IAAvC,wBAAA,EAAA,YAAuC;IAC3E,OAAO,IAAI,WAAW,uBACjB,OAAO,KACV,YAAY,EAAE,UAAC,OAAoB;YACjC,8DAA8D;YAC9D,0CAA0C;YAC1C,OAAO,WAAW,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC,IACD,CAAC;AACL,CAAC;AAED,6CAA6C;AAC7C,SAAgB,qBAAqB;IACnC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAM,YAAY,GAAa,EAAE,CAAC;IAElC,cAAc,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;QAChC,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YAC1B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,YAAY,CAAC,OAAO,CAAC,UAAA,GAAG;QACtB,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3B,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,qBAAc,OAAO,gCAA6B,CAAC,CAAC;AAClE,CAAC;AAED,+BAA+B;AAC/B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAClC,WAAW,CAAC,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AACpD,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rate-limit.ts"],
      sourcesContent: ["import { NextRequest } from 'next/server';\nimport { CONFIG } from './config';\n\n// In-memory store for rate limiting (in production, use Redis)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\ninterface RateLimitOptions {\n  requests: number;\n  windowMs: number;\n  keyGenerator?: (request: NextRequest) => string;\n  skipSuccessfulRequests?: boolean;\n  skipFailedRequests?: boolean;\n}\n\nexport class RateLimiter {\n  private options: Required<RateLimitOptions>;\n\n  constructor(options: Partial<RateLimitOptions> = {}) {\n    this.options = {\n      requests: options.requests || CONFIG.API.RATE_LIMIT_REQUESTS,\n      windowMs: options.windowMs || CONFIG.API.RATE_LIMIT_WINDOW_MS,\n      keyGenerator: options.keyGenerator || this.defaultKeyGenerator,\n      skipSuccessfulRequests: options.skipSuccessfulRequests || false,\n      skipFailedRequests: options.skipFailedRequests || false,\n    };\n  }\n\n  private defaultKeyGenerator(request: NextRequest): string {\n    // Use IP address as default key\n    return getClientIP(request);\n  }\n\n  private cleanupExpiredEntries(): void {\n    const now = Date.now();\n    const keysToDelete: string[] = [];\n\n    rateLimitStore.forEach((value, key) => {\n      if (now > value.resetTime) {\n        keysToDelete.push(key);\n      }\n    });\n\n    keysToDelete.forEach(key => rateLimitStore.delete(key));\n  }\n\n  public check(request: NextRequest): {\n    allowed: boolean;\n    limit: number;\n    remaining: number;\n    resetTime: number;\n  } {\n    this.cleanupExpiredEntries();\n\n    const key = this.options.keyGenerator(request);\n    const now = Date.now();\n    const windowStart = now;\n    const windowEnd = windowStart + this.options.windowMs;\n\n    let entry = rateLimitStore.get(key);\n\n    if (!entry || now > entry.resetTime) {\n      // Create new entry or reset expired entry\n      entry = {\n        count: 0,\n        resetTime: windowEnd,\n      };\n      rateLimitStore.set(key, entry);\n    }\n\n    const allowed = entry.count < this.options.requests;\n    const remaining = Math.max(0, this.options.requests - entry.count - 1);\n\n    if (allowed) {\n      entry.count++;\n    }\n\n    return {\n      allowed,\n      limit: this.options.requests,\n      remaining,\n      resetTime: entry.resetTime,\n    };\n  }\n\n  public increment(request: NextRequest): void {\n    const key = this.options.keyGenerator(request);\n    const entry = rateLimitStore.get(key);\n    if (entry) {\n      entry.count++;\n    }\n  }\n}\n\n// Pre-configured rate limiters for different endpoints\nexport const rateLimiters = {\n  // General API rate limiter\n  api: new RateLimiter({\n    requests: 100,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n\n  // Stricter rate limiter for authentication endpoints\n  auth: new RateLimiter({\n    requests: 10,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n\n  // Very strict rate limiter for password reset\n  passwordReset: new RateLimiter({\n    requests: 3,\n    windowMs: 60 * 60 * 1000, // 1 hour\n  }),\n\n  // Moderate rate limiter for search/read operations\n  search: new RateLimiter({\n    requests: 200,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n\n  // Strict rate limiter for write operations\n  write: new RateLimiter({\n    requests: 50,\n    windowMs: 15 * 60 * 1000, // 15 minutes\n  }),\n};\n\n// Middleware function to apply rate limiting\nexport function withRateLimit(rateLimiter: RateLimiter) {\n  return (request: NextRequest) => {\n    const result = rateLimiter.check(request);\n    \n    return {\n      ...result,\n      headers: {\n        'X-RateLimit-Limit': result.limit.toString(),\n        'X-RateLimit-Remaining': result.remaining.toString(),\n        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),\n      },\n    };\n  };\n}\n\n// Helper function to get client IP\nexport function getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for');\n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n  \n  const realIP = request.headers.get('x-real-ip');\n  if (realIP) {\n    return realIP;\n  }\n  \n  return 'unknown';\n}\n\n// Rate limit by user ID for authenticated requests\nexport function createUserRateLimiter(options: Partial<RateLimitOptions> = {}) {\n  return new RateLimiter({\n    ...options,\n    keyGenerator: (request: NextRequest) => {\n      // This would need to be implemented based on your auth system\n      // For now, fall back to IP-based limiting\n      return getClientIP(request);\n    },\n  });\n}\n\n// Cleanup function to be called periodically\nexport function cleanupRateLimitStore(): void {\n  const now = Date.now();\n  let cleaned = 0;\n  const keysToDelete: string[] = [];\n\n  rateLimitStore.forEach((value, key) => {\n    if (now > value.resetTime) {\n      keysToDelete.push(key);\n    }\n  });\n\n  keysToDelete.forEach(key => {\n    rateLimitStore.delete(key);\n    cleaned++;\n  });\n\n  console.log(`Cleaned up ${cleaned} expired rate limit entries`);\n}\n\n// Auto-cleanup every 5 minutes\nif (typeof window === 'undefined') {\n  setInterval(cleanupRateLimitStore, 5 * 60 * 1000);\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "103c5071c82e0f5217c61a441338698a713965d8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_6rrxujlk7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_6rrxujlk7();
var __assign =
/* istanbul ignore next */
(cov_6rrxujlk7().s[0]++,
/* istanbul ignore next */
(cov_6rrxujlk7().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_6rrxujlk7().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_6rrxujlk7().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_6rrxujlk7().f[0]++;
  cov_6rrxujlk7().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_6rrxujlk7().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_6rrxujlk7().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[1]++;
    cov_6rrxujlk7().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_6rrxujlk7().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_6rrxujlk7().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_6rrxujlk7().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_6rrxujlk7().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_6rrxujlk7().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_6rrxujlk7().b[2][0]++;
          cov_6rrxujlk7().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_6rrxujlk7().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_6rrxujlk7().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_6rrxujlk7().s[10]++;
  return __assign.apply(this, arguments);
}));
/* istanbul ignore next */
cov_6rrxujlk7().s[11]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_6rrxujlk7().s[12]++;
exports.rateLimiters = exports.RateLimiter = void 0;
/* istanbul ignore next */
cov_6rrxujlk7().s[13]++;
exports.withRateLimit = withRateLimit;
/* istanbul ignore next */
cov_6rrxujlk7().s[14]++;
exports.getClientIP = getClientIP;
/* istanbul ignore next */
cov_6rrxujlk7().s[15]++;
exports.createUserRateLimiter = createUserRateLimiter;
/* istanbul ignore next */
cov_6rrxujlk7().s[16]++;
exports.cleanupRateLimitStore = cleanupRateLimitStore;
var config_1 =
/* istanbul ignore next */
(cov_6rrxujlk7().s[17]++, require("./config"));
// In-memory store for rate limiting (in production, use Redis)
var rateLimitStore =
/* istanbul ignore next */
(cov_6rrxujlk7().s[18]++, new Map());
var RateLimiter =
/* istanbul ignore next */
(/** @class */cov_6rrxujlk7().s[19]++, function () {
  /* istanbul ignore next */
  cov_6rrxujlk7().f[2]++;
  function RateLimiter(options) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[3]++;
    cov_6rrxujlk7().s[20]++;
    if (options === void 0) {
      /* istanbul ignore next */
      cov_6rrxujlk7().b[3][0]++;
      cov_6rrxujlk7().s[21]++;
      options = {};
    } else
    /* istanbul ignore next */
    {
      cov_6rrxujlk7().b[3][1]++;
    }
    cov_6rrxujlk7().s[22]++;
    this.options = {
      requests:
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[4][0]++, options.requests) ||
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[4][1]++, config_1.CONFIG.API.RATE_LIMIT_REQUESTS),
      windowMs:
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[5][0]++, options.windowMs) ||
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[5][1]++, config_1.CONFIG.API.RATE_LIMIT_WINDOW_MS),
      keyGenerator:
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[6][0]++, options.keyGenerator) ||
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[6][1]++, this.defaultKeyGenerator),
      skipSuccessfulRequests:
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[7][0]++, options.skipSuccessfulRequests) ||
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[7][1]++, false),
      skipFailedRequests:
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[8][0]++, options.skipFailedRequests) ||
      /* istanbul ignore next */
      (cov_6rrxujlk7().b[8][1]++, false)
    };
  }
  /* istanbul ignore next */
  cov_6rrxujlk7().s[23]++;
  RateLimiter.prototype.defaultKeyGenerator = function (request) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[4]++;
    cov_6rrxujlk7().s[24]++;
    // Use IP address as default key
    return getClientIP(request);
  };
  /* istanbul ignore next */
  cov_6rrxujlk7().s[25]++;
  RateLimiter.prototype.cleanupExpiredEntries = function () {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[5]++;
    var now =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[26]++, Date.now());
    var keysToDelete =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[27]++, []);
    /* istanbul ignore next */
    cov_6rrxujlk7().s[28]++;
    rateLimitStore.forEach(function (value, key) {
      /* istanbul ignore next */
      cov_6rrxujlk7().f[6]++;
      cov_6rrxujlk7().s[29]++;
      if (now > value.resetTime) {
        /* istanbul ignore next */
        cov_6rrxujlk7().b[9][0]++;
        cov_6rrxujlk7().s[30]++;
        keysToDelete.push(key);
      } else
      /* istanbul ignore next */
      {
        cov_6rrxujlk7().b[9][1]++;
      }
    });
    /* istanbul ignore next */
    cov_6rrxujlk7().s[31]++;
    keysToDelete.forEach(function (key) {
      /* istanbul ignore next */
      cov_6rrxujlk7().f[7]++;
      cov_6rrxujlk7().s[32]++;
      return rateLimitStore.delete(key);
    });
  };
  /* istanbul ignore next */
  cov_6rrxujlk7().s[33]++;
  RateLimiter.prototype.check = function (request) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[8]++;
    cov_6rrxujlk7().s[34]++;
    this.cleanupExpiredEntries();
    var key =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[35]++, this.options.keyGenerator(request));
    var now =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[36]++, Date.now());
    var windowStart =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[37]++, now);
    var windowEnd =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[38]++, windowStart + this.options.windowMs);
    var entry =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[39]++, rateLimitStore.get(key));
    /* istanbul ignore next */
    cov_6rrxujlk7().s[40]++;
    if (
    /* istanbul ignore next */
    (cov_6rrxujlk7().b[11][0]++, !entry) ||
    /* istanbul ignore next */
    (cov_6rrxujlk7().b[11][1]++, now > entry.resetTime)) {
      /* istanbul ignore next */
      cov_6rrxujlk7().b[10][0]++;
      cov_6rrxujlk7().s[41]++;
      // Create new entry or reset expired entry
      entry = {
        count: 0,
        resetTime: windowEnd
      };
      /* istanbul ignore next */
      cov_6rrxujlk7().s[42]++;
      rateLimitStore.set(key, entry);
    } else
    /* istanbul ignore next */
    {
      cov_6rrxujlk7().b[10][1]++;
    }
    var allowed =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[43]++, entry.count < this.options.requests);
    var remaining =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[44]++, Math.max(0, this.options.requests - entry.count - 1));
    /* istanbul ignore next */
    cov_6rrxujlk7().s[45]++;
    if (allowed) {
      /* istanbul ignore next */
      cov_6rrxujlk7().b[12][0]++;
      cov_6rrxujlk7().s[46]++;
      entry.count++;
    } else
    /* istanbul ignore next */
    {
      cov_6rrxujlk7().b[12][1]++;
    }
    cov_6rrxujlk7().s[47]++;
    return {
      allowed: allowed,
      limit: this.options.requests,
      remaining: remaining,
      resetTime: entry.resetTime
    };
  };
  /* istanbul ignore next */
  cov_6rrxujlk7().s[48]++;
  RateLimiter.prototype.increment = function (request) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[9]++;
    var key =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[49]++, this.options.keyGenerator(request));
    var entry =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[50]++, rateLimitStore.get(key));
    /* istanbul ignore next */
    cov_6rrxujlk7().s[51]++;
    if (entry) {
      /* istanbul ignore next */
      cov_6rrxujlk7().b[13][0]++;
      cov_6rrxujlk7().s[52]++;
      entry.count++;
    } else
    /* istanbul ignore next */
    {
      cov_6rrxujlk7().b[13][1]++;
    }
  };
  /* istanbul ignore next */
  cov_6rrxujlk7().s[53]++;
  return RateLimiter;
}());
/* istanbul ignore next */
cov_6rrxujlk7().s[54]++;
exports.RateLimiter = RateLimiter;
// Pre-configured rate limiters for different endpoints
/* istanbul ignore next */
cov_6rrxujlk7().s[55]++;
exports.rateLimiters = {
  // General API rate limiter
  api: new RateLimiter({
    requests: 100,
    windowMs: 15 * 60 * 1000 // 15 minutes
  }),
  // Stricter rate limiter for authentication endpoints
  auth: new RateLimiter({
    requests: 10,
    windowMs: 15 * 60 * 1000 // 15 minutes
  }),
  // Very strict rate limiter for password reset
  passwordReset: new RateLimiter({
    requests: 3,
    windowMs: 60 * 60 * 1000 // 1 hour
  }),
  // Moderate rate limiter for search/read operations
  search: new RateLimiter({
    requests: 200,
    windowMs: 15 * 60 * 1000 // 15 minutes
  }),
  // Strict rate limiter for write operations
  write: new RateLimiter({
    requests: 50,
    windowMs: 15 * 60 * 1000 // 15 minutes
  })
};
// Middleware function to apply rate limiting
function withRateLimit(rateLimiter) {
  /* istanbul ignore next */
  cov_6rrxujlk7().f[10]++;
  cov_6rrxujlk7().s[56]++;
  return function (request) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[11]++;
    var result =
    /* istanbul ignore next */
    (cov_6rrxujlk7().s[57]++, rateLimiter.check(request));
    /* istanbul ignore next */
    cov_6rrxujlk7().s[58]++;
    return __assign(__assign({}, result), {
      headers: {
        'X-RateLimit-Limit': result.limit.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
      }
    });
  };
}
// Helper function to get client IP
function getClientIP(request) {
  /* istanbul ignore next */
  cov_6rrxujlk7().f[12]++;
  var forwarded =
  /* istanbul ignore next */
  (cov_6rrxujlk7().s[59]++, request.headers.get('x-forwarded-for'));
  /* istanbul ignore next */
  cov_6rrxujlk7().s[60]++;
  if (forwarded) {
    /* istanbul ignore next */
    cov_6rrxujlk7().b[14][0]++;
    cov_6rrxujlk7().s[61]++;
    return forwarded.split(',')[0].trim();
  } else
  /* istanbul ignore next */
  {
    cov_6rrxujlk7().b[14][1]++;
  }
  var realIP =
  /* istanbul ignore next */
  (cov_6rrxujlk7().s[62]++, request.headers.get('x-real-ip'));
  /* istanbul ignore next */
  cov_6rrxujlk7().s[63]++;
  if (realIP) {
    /* istanbul ignore next */
    cov_6rrxujlk7().b[15][0]++;
    cov_6rrxujlk7().s[64]++;
    return realIP;
  } else
  /* istanbul ignore next */
  {
    cov_6rrxujlk7().b[15][1]++;
  }
  cov_6rrxujlk7().s[65]++;
  return 'unknown';
}
// Rate limit by user ID for authenticated requests
function createUserRateLimiter(options) {
  /* istanbul ignore next */
  cov_6rrxujlk7().f[13]++;
  cov_6rrxujlk7().s[66]++;
  if (options === void 0) {
    /* istanbul ignore next */
    cov_6rrxujlk7().b[16][0]++;
    cov_6rrxujlk7().s[67]++;
    options = {};
  } else
  /* istanbul ignore next */
  {
    cov_6rrxujlk7().b[16][1]++;
  }
  cov_6rrxujlk7().s[68]++;
  return new RateLimiter(__assign(__assign({}, options), {
    keyGenerator: function (request) {
      /* istanbul ignore next */
      cov_6rrxujlk7().f[14]++;
      cov_6rrxujlk7().s[69]++;
      // This would need to be implemented based on your auth system
      // For now, fall back to IP-based limiting
      return getClientIP(request);
    }
  }));
}
// Cleanup function to be called periodically
function cleanupRateLimitStore() {
  /* istanbul ignore next */
  cov_6rrxujlk7().f[15]++;
  var now =
  /* istanbul ignore next */
  (cov_6rrxujlk7().s[70]++, Date.now());
  var cleaned =
  /* istanbul ignore next */
  (cov_6rrxujlk7().s[71]++, 0);
  var keysToDelete =
  /* istanbul ignore next */
  (cov_6rrxujlk7().s[72]++, []);
  /* istanbul ignore next */
  cov_6rrxujlk7().s[73]++;
  rateLimitStore.forEach(function (value, key) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[16]++;
    cov_6rrxujlk7().s[74]++;
    if (now > value.resetTime) {
      /* istanbul ignore next */
      cov_6rrxujlk7().b[17][0]++;
      cov_6rrxujlk7().s[75]++;
      keysToDelete.push(key);
    } else
    /* istanbul ignore next */
    {
      cov_6rrxujlk7().b[17][1]++;
    }
  });
  /* istanbul ignore next */
  cov_6rrxujlk7().s[76]++;
  keysToDelete.forEach(function (key) {
    /* istanbul ignore next */
    cov_6rrxujlk7().f[17]++;
    cov_6rrxujlk7().s[77]++;
    rateLimitStore.delete(key);
    /* istanbul ignore next */
    cov_6rrxujlk7().s[78]++;
    cleaned++;
  });
  /* istanbul ignore next */
  cov_6rrxujlk7().s[79]++;
  console.log("Cleaned up ".concat(cleaned, " expired rate limit entries"));
}
// Auto-cleanup every 5 minutes
/* istanbul ignore next */
cov_6rrxujlk7().s[80]++;
if (typeof window === 'undefined') {
  /* istanbul ignore next */
  cov_6rrxujlk7().b[18][0]++;
  cov_6rrxujlk7().s[81]++;
  setInterval(cleanupRateLimitStore, 5 * 60 * 1000);
} else
/* istanbul ignore next */
{
  cov_6rrxujlk7().b[18][1]++;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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