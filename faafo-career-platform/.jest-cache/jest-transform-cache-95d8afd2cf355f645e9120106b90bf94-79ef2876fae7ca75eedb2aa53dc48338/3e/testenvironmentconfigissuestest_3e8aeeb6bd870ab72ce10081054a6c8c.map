{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-environment-config-issues.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;AAEH,yCAAuE;AACvE,0CAAoB;AACpB,8CAAwB;AAExB,IAAA,kBAAQ,EAAC,uCAAuC,EAAE;IAChD,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,sDAAsD,EAAE;QAC/D,IAAA,YAAE,EAAC,8EAA8E,EAAE;YACjF,2DAA2D;YAC3D,IAAM,UAAU,GAAG;gBACjB,eAAe;gBACf,2BAA2B;gBAC3B,mBAAmB;aACpB,CAAC;YAEF,IAAM,YAAY,GAAG,EAAE,CAAC;YAExB,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;gBAC1B,IAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;gBAEtD,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAM,cAAY,GAAG,YAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBAExD,6CAA6C;oBAC7C,IAAM,eAAe,GAAG;wBACtB,cAAc;wBACd,iBAAiB;wBACjB,cAAc;wBACd,UAAU;qBACX,CAAC;oBAEF,IAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,UAAA,MAAM;wBAClD,OAAA,CAAC,cAAY,CAAC,QAAQ,CAAC,sBAAe,MAAM,CAAE,CAAC;oBAA/C,CAA+C,CAChD,CAAC;oBAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC9B,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,gBAAA,EAAE,CAAC,CAAC;oBACzD,CAAC;oBAED,0DAA0D;oBAC1D,IAAM,mBAAmB,GAAG,cAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;wBACrC,cAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;oBAEnE,IAAI,mBAAmB,EAAE,CAAC;wBACxB,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,4BAA4B,EAAE,CAAC,CAAC;oBAC9E,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC,CAAC;gBAC7E,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,qFAAqF;YACrF,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sFAAsF,EAAE;YACzF,6CAA6C;YAC7C,IAAM,WAAW,GAAG;gBAClB,gBAAgB;gBAChB,eAAe;gBACf,2BAA2B;gBAC3B,WAAW;gBACX,YAAY;aACb,CAAC;YAEF,IAAM,SAAS,GAAG,EAAE,CAAC;YACrB,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,WAAW,CAAC,OAAO,CAAC,UAAA,UAAU;gBAC5B,IAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;gBAExD,IAAI,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC9B,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;oBAEpD,2CAA2C;oBAC3C,IAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,gDAAgD,CAAC,IAAI,EAAE,CAAC;oBAEzF,UAAU,CAAC,OAAO,CAAC,UAAA,KAAK;wBAChB,IAAA,KAAqB,KAAK,CAAC,KAAK,CAAC,+CAA+C,CAAC,EAA9E,OAAO,QAAA,EAAE,KAAK,QAAgE,CAAC;wBAExF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;4BACxB,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;wBAC1B,CAAC;wBACD,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC;oBACvD,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAC,EAAsB;oBAArB,OAAO,QAAA,EAAE,WAAW,QAAA;gBACtD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,IAAM,YAAY,qBAAO,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,CAAC,OAAC,CAAC;oBACjE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,eAAe,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;oBAC3D,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,wEAAwE;YACxE,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,iDAAiD,EAAE;QAC1D,IAAA,YAAE,EAAC,oEAAoE,EAAE;YACvE,2CAA2C;YAC3C,IAAM,UAAU,GAAG;gBACjB,eAAe;gBACf,2BAA2B;aAC5B,CAAC;YAEF,IAAM,oBAAoB,GAAG,EAAE,CAAC;YAEhC,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;gBAC1B,IAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;gBAEtD,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBAEnD,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBAClE,oBAAoB,CAAC,IAAI,CAAC,UAAG,SAAS,0CAAuC,CAAC,CAAC;oBACjF,CAAC;oBAED,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC/D,oBAAoB,CAAC,IAAI,CAAC,UAAG,SAAS,qCAAkC,CAAC,CAAC;oBAC5E,CAAC;oBAED,+BAA+B;oBAC/B,IAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC,IAAI,EAAE,CAAC;oBACtE,YAAY,CAAC,OAAO,CAAC,UAAA,GAAG;wBACtB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;4BACxD,oBAAoB,CAAC,IAAI,CAAC,UAAG,SAAS,2CAAiC,GAAG,CAAE,CAAC,CAAC;wBAChF,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,+DAA+D;YAC/D,IAAA,gBAAM,EAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,gEAAgE,EAAE;YACnE,2CAA2C;YAC3C,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,mBAAmB,GAAG,EAAE,CAAC;YAE/B,SAAS,oBAAoB,CAAC,GAAW;gBACvC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,oBAAoB,CAAC,QAAQ,CAAC,CAAC;oBACjC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,yCAAyC;4BACzC,IAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;gCAC5B,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;gCAC5B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;4BAEvD,IAAI,eAAe,EAAE,CAAC;gCACpB,0BAA0B;gCAC1B,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gCACrD,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gCACnD,IAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;oCAC7B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;oCACzB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;oCACzB,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gCAE9C,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,EAAE,CAAC;oCACjC,mBAAmB,CAAC,IAAI,CAAC;wCACvB,IAAI,EAAE,QAAQ;wCACd,aAAa,eAAA;wCACb,YAAY,cAAA;wCACZ,UAAU,YAAA;qCACX,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAEpC,8DAA8D;YAC9D,IAAA,gBAAM,EAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2CAA2C,EAAE;QACpD,IAAA,YAAE,EAAC,+DAA+D,EAAE;YAClE,wCAAwC;YACxC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,uBAAuB,GAAG,EAAE,CAAC;YAEnC,SAAS,mBAAmB,CAAC,GAAW;gBACtC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,mBAAmB,CAAC,QAAQ,CAAC,CAAC;oBAChC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,kCAAkC;4BAClC,IAAM,cAAc,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACzF,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;gCAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;gCAC7B,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;4BAE3D,6BAA6B;4BAC7B,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;gCAC1B,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;gCAC/B,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;4BAE9D,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,aAAa,EAAE,CAAC;gCAC5D,uBAAuB,CAAC,IAAI,CAAC;oCAC3B,IAAI,EAAE,QAAQ;oCACd,cAAc,gBAAA;oCACd,aAAa,eAAA;oCACb,aAAa,eAAA;iCACd,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAEnC,iDAAiD;YACjD,IAAA,gBAAM,EAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sDAAsD,EAAE;YACzD,gCAAgC;YAChC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,SAAS,gBAAgB,CAAC,GAAW;gBACnC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,gBAAgB,CAAC,QAAQ,CAAC,CAAC;oBAC7B,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,gDAAgD,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACjG,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;gCACnC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;gCACjC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;4BACvD,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAExD,8DAA8D;4BAC9D,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gCACpD,iBAAiB,CAAC,IAAI,CAAC;oCACrB,IAAI,EAAE,QAAQ;oCACd,SAAS,WAAA;oCACT,SAAS,WAAA;oCACT,YAAY,cAAA;iCACb,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEhC,wDAAwD;YACxD,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,sDAAsD,EAAE;QAC/D,IAAA,YAAE,EAAC,2DAA2D,EAAE;YAC9D,yCAAyC;YACzC,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAElE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAC9D,IAAM,cAAY,GAAG,EAAE,CAAC;gBAExB,0CAA0C;gBAC1C,IAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;gBACrF,IAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAE5D,oDAAoD;gBACpD,IAAI,OAAO,KAAK,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC1D,cAAY,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gBACnE,CAAC;gBAED,0CAA0C;gBAC1C,IAAM,gBAAgB,GAAG,aAAa,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBAC/E,IAAI,gBAAgB,EAAE,CAAC;oBACrB,IAAM,SAAS,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;oBACtC,IAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAEtC,sCAAsC;oBACtC,IAAM,cAAY,GAAG,EAAE,CAAC;oBACxB,QAAQ,CAAC,OAAO,CAAC,UAAA,OAAO;wBACtB,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;wBAC1E,IAAI,KAAK,EAAE,CAAC;4BACD,IAAA,OAAO,GAAiB,KAAK,GAAtB,EAAE,WAAW,GAAI,KAAK,GAAT,CAAU;4BACvC,IAAI,cAAY,CAAC,OAAO,CAAC,IAAI,cAAY,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;gCACnE,cAAY,CAAC,IAAI,CAAC,yCAAkC,OAAO,CAAE,CAAC,CAAC;4BACjE,CAAC;4BACD,cAAY,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC;wBACtC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,yCAAyC;gBACzC,IAAM,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;gBAC/F,IAAI,eAAe,EAAE,CAAC;oBACpB,IAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBACtC,IAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;oBAC5D,IAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBAEtD,IAAI,aAAa,IAAI,UAAU,EAAE,CAAC;wBAChC,IAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5C,IAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;wBAEtC,+DAA+D;wBAC/D,IAAI,QAAQ,GAAG,KAAK,EAAE,CAAC;4BACrB,cAAY,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;wBAC3E,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,iEAAiE;gBACjE,IAAA,gBAAM,EAAC,cAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,6CAA6C;gBAC7C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,0EAA0E,EAAE;YAC7E,IAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,CAAC;YAEjE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;gBACzE,IAAM,SAAO,GAAG,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC;gBAC1C,IAAM,YAAY,GAAG,EAAE,CAAC;gBAExB,kCAAkC;gBAClC,IAAM,mBAAmB,GAAG;oBAC1B,MAAM;oBACN,eAAe;oBACf,YAAY;oBACZ,SAAS;iBACV,CAAC;gBAEF,IAAM,cAAc,GAAG,mBAAmB,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,CAAC,SAAO,CAAC,MAAM,CAAC,EAAhB,CAAgB,CAAC,CAAC;gBAC9E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC9B,YAAY,CAAC,IAAI,CAAC,oCAA6B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;gBAC9E,CAAC;gBAED,4CAA4C;gBAC5C,IAAM,UAAU,GAAG,SAAO,CAAC,MAAM,CAAC,CAAC;gBACnC,IAAM,YAAY,GAAG,SAAO,CAAC,SAAS,CAAC,CAAC;gBAExC,IAAI,UAAU,IAAI,YAAY,EAAE,CAAC;oBAC/B,sCAAsC;oBACtC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBACnF,YAAY,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;oBAChE,CAAC;oBAED,oCAAoC;oBACpC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;wBACzC,YAAY,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;oBACxE,CAAC;gBACH,CAAC;gBAED,gFAAgF;gBAChF,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,8CAA8C;gBAC9C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2DAA2D,EAAE;QACpE,IAAA,YAAE,EAAC,8EAA8E,EAAE;YACjF,gDAAgD;YAChD,IAAM,UAAU,GAAG;gBACjB,eAAe;gBACf,2BAA2B;gBAC3B,WAAW;aACZ,CAAC;YAEF,IAAM,qBAAqB,GAAG,EAAE,CAAC;YAEjC,mDAAmD;YACnD,IAAM,gBAAgB,GAAG;gBACvB,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;aACT,CAAC;YAEF,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;gBAC1B,IAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;gBAEtD,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAM,WAAW,GAAG,YAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;oBAEzD,IAAI,WAAW,EAAE,CAAC;wBAChB,4BAA4B;wBAC5B,IAAM,SAAS,GAAG,YAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;wBAC5C,IAAM,gBAAc,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAA9B,CAA8B,CAAC,CAAC;wBAE7E,IAAM,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAA,OAAO;4BACtD,OAAA,CAAC,gBAAc,CAAC,QAAQ,CAAC,OAAO,CAAC;wBAAjC,CAAiC,CAClC,CAAC;wBAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAChC,qBAAqB,CAAC,IAAI,CAAC;gCACzB,QAAQ,EAAE,SAAS;gCACnB,KAAK,EAAE,sCAA+B,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE;6BACpE,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,2BAA2B;wBAC3B,IAAM,SAAO,GAAG,YAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;wBAEnD,IAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAA,OAAO;4BACnD,OAAA,SAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,SAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;wBAA1D,CAA0D,CAC3D,CAAC;wBAEF,yCAAyC;wBACzC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC7B,qBAAqB,CAAC,IAAI,CAAC;gCACzB,QAAQ,EAAE,SAAS;gCACnB,KAAK,EAAE,uCAAuC;6BAC/C,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,gEAAgE;YAChE,IAAA,gBAAM,EAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-environment-config-issues.test.ts"], "sourcesContent": ["/**\n * Test Environment Configuration Issues Tests\n * \n * These tests prove test environment issues including inconsistent environment\n * variables, unsafe database configurations, and missing cleanup hooks.\n * \n * EXPECTED TO FAIL - These tests demonstrate environment configuration issues that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\nimport fs from 'fs';\nimport path from 'path';\n\ndescribe('Test Environment Configuration Issues', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Inconsistent Environment Variables', () => {\n    it('should fail - test environment variables are inconsistent across setup files', () => {\n      // Check jest.setup.js for proper environment configuration\n      const setupFiles = [\n        'jest.setup.js',\n        'jest.setup.integration.js',\n        'jest.polyfills.js'\n      ];\n      \n      const envVarIssues = [];\n      \n      setupFiles.forEach(setupFile => {\n        const setupPath = path.join(process.cwd(), setupFile);\n        \n        if (fs.existsSync(setupPath)) {\n          const setupContent = fs.readFileSync(setupPath, 'utf8');\n          \n          // Required environment variables for testing\n          const requiredEnvVars = [\n            'NEXTAUTH_URL',\n            'NEXTAUTH_SECRET',\n            'DATABASE_URL',\n            'NODE_ENV'\n          ];\n          \n          const missingEnvVars = requiredEnvVars.filter(envVar => \n            !setupContent.includes(`process.env.${envVar}`)\n          );\n          \n          if (missingEnvVars.length > 0) {\n            envVarIssues.push({ file: setupFile, missingEnvVars });\n          }\n          \n          // Check for hardcoded test values that might cause issues\n          const hasHardcodedSecrets = setupContent.includes('test-secret') || \n                                     setupContent.includes('localhost:5432');\n          \n          if (hasHardcodedSecrets) {\n            envVarIssues.push({ file: setupFile, issue: 'Contains hardcoded secrets' });\n          }\n        } else {\n          envVarIssues.push({ file: setupFile, issue: 'Setup file does not exist' });\n        }\n      });\n      \n      // EXPECTED TO FAIL: All setup files should have consistent environment configuration\n      expect(envVarIssues.length).toBe(0);\n    });\n\n    it('should fail - environment variables have different values across test configurations', () => {\n      // Check for environment variable consistency\n      const configFiles = [\n        'jest.config.js',\n        'jest.setup.js',\n        'jest.setup.integration.js',\n        '.env.test',\n        '.env.local'\n      ];\n      \n      const envValues = {};\n      const inconsistencies = [];\n      \n      configFiles.forEach(configFile => {\n        const configPath = path.join(process.cwd(), configFile);\n        \n        if (fs.existsSync(configPath)) {\n          const content = fs.readFileSync(configPath, 'utf8');\n          \n          // Extract environment variable assignments\n          const envMatches = content.match(/process\\.env\\.(\\w+)\\s*=\\s*['\"`]([^'\"`]+)['\"`]/g) || [];\n          \n          envMatches.forEach(match => {\n            const [, varName, value] = match.match(/process\\.env\\.(\\w+)\\s*=\\s*['\"`]([^'\"`]+)['\"`]/);\n            \n            if (!envValues[varName]) {\n              envValues[varName] = [];\n            }\n            envValues[varName].push({ file: configFile, value });\n          });\n        }\n      });\n      \n      // Check for inconsistent values\n      Object.entries(envValues).forEach(([varName, assignments]) => {\n        if (assignments.length > 1) {\n          const uniqueValues = [...new Set(assignments.map(a => a.value))];\n          if (uniqueValues.length > 1) {\n            inconsistencies.push({ variable: varName, assignments });\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: Environment variables should have consistent values\n      expect(inconsistencies.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: Unsafe Database Configuration', () => {\n    it('should fail - test database configuration is unsafe for production', () => {\n      // Check for proper test database isolation\n      const setupFiles = [\n        'jest.setup.js',\n        'jest.setup.integration.js'\n      ];\n      \n      const unsafeConfigurations = [];\n      \n      setupFiles.forEach(setupFile => {\n        const setupPath = path.join(process.cwd(), setupFile);\n        \n        if (fs.existsSync(setupPath)) {\n          const content = fs.readFileSync(setupPath, 'utf8');\n          \n          // Check for unsafe database configurations\n          if (content.includes('DATABASE_URL') && !content.includes('test')) {\n            unsafeConfigurations.push(`${setupFile}: DATABASE_URL doesn't include 'test'`);\n          }\n          \n          // Check for production database references\n          if (content.includes('production') || content.includes('prod')) {\n            unsafeConfigurations.push(`${setupFile}: Contains production references`);\n          }\n          \n          // Check for real database URLs\n          const dbUrlMatches = content.match(/postgresql:\\/\\/[^'\"`\\s]+/g) || [];\n          dbUrlMatches.forEach(url => {\n            if (!url.includes('test') && !url.includes('localhost')) {\n              unsafeConfigurations.push(`${setupFile}: Real database URL detected: ${url}`);\n            }\n          });\n        }\n      });\n      \n      // EXPECTED TO FAIL: Test database configuration should be safe\n      expect(unsafeConfigurations.length).toBe(0);\n    });\n\n    it('should fail - database cleanup hooks are missing or inadequate', () => {\n      // Check for database cleanup in test files\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const missingCleanupTests = [];\n      \n      function checkDatabaseCleanup(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            checkDatabaseCleanup(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check if test uses database operations\n              const hasDbOperations = content.includes('prisma') || \n                                    content.includes('database') ||\n                                    content.includes('PrismaClient');\n              \n              if (hasDbOperations) {\n                // Check for cleanup hooks\n                const hasBeforeEach = content.includes('beforeEach');\n                const hasAfterEach = content.includes('afterEach');\n                const hasCleanup = content.includes('cleanup') || \n                                 content.includes('clear') || \n                                 content.includes('reset') ||\n                                 content.includes('truncate');\n                \n                if (!hasAfterEach && !hasCleanup) {\n                  missingCleanupTests.push({ \n                    file: filePath, \n                    hasBeforeEach, \n                    hasAfterEach, \n                    hasCleanup \n                  });\n                }\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      checkDatabaseCleanup(testDirectory);\n      \n      // EXPECTED TO FAIL: Database tests should have proper cleanup\n      expect(missingCleanupTests.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: Test Isolation Problems', () => {\n    it('should fail - tests share state and interfere with each other', () => {\n      // Check for global state sharing issues\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const stateInterferenceIssues = [];\n      \n      function checkStateIsolation(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            checkStateIsolation(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for global variable usage\n              const globalVarUsage = (content.match(/global\\.|window\\.|process\\.env\\./g) || []).length;\n              const hasStateReset = content.includes('beforeEach') || \n                                  content.includes('afterEach') ||\n                                  content.includes('jest.clearAllMocks');\n              \n              // Check for shared test data\n              const hasSharedData = content.includes('let ') && \n                                  !content.includes('beforeEach') &&\n                                  (content.match(/it\\(/g) || []).length > 1;\n              \n              if ((globalVarUsage > 5 && !hasStateReset) || hasSharedData) {\n                stateInterferenceIssues.push({ \n                  file: filePath, \n                  globalVarUsage, \n                  hasStateReset, \n                  hasSharedData \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      checkStateIsolation(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should not share state\n      expect(stateInterferenceIssues.length).toBe(0);\n    });\n\n    it('should fail - mock configurations leak between tests', () => {\n      // Check for mock leakage issues\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const mockLeakageIssues = [];\n      \n      function checkMockLeakage(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            checkMockLeakage(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              const mockCount = (content.match(/jest\\.mock|mockImplementation|mockReturnValue/g) || []).length;\n              const hasMockClear = content.includes('clearAllMocks') || \n                                 content.includes('resetAllMocks') ||\n                                 content.includes('restoreAllMocks');\n              const testCount = (content.match(/it\\(/g) || []).length;\n              \n              // If there are multiple tests with mocks but no mock clearing\n              if (mockCount > 3 && testCount > 1 && !hasMockClear) {\n                mockLeakageIssues.push({ \n                  file: filePath, \n                  mockCount, \n                  testCount, \n                  hasMockClear \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      checkMockLeakage(testDirectory);\n      \n      // EXPECTED TO FAIL: Mocks should not leak between tests\n      expect(mockLeakageIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Test Configuration Inconsistencies', () => {\n    it('should fail - Jest configuration has conflicting settings', () => {\n      // Check Jest configuration for conflicts\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      \n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        const configIssues = [];\n        \n        // Check for conflicting test environments\n        const testEnvMatches = configContent.match(/testEnvironment:\\s*['\"`]([^'\"`]+)['\"`]/);\n        const testEnv = testEnvMatches ? testEnvMatches[1] : 'node';\n        \n        // Check for DOM-related tests with node environment\n        if (testEnv === 'node' && configContent.includes('jsdom')) {\n          configIssues.push('Conflicting test environment: node vs jsdom');\n        }\n        \n        // Check for module name mapping conflicts\n        const moduleMapMatches = configContent.match(/moduleNameMapper:\\s*{([^}]+)}/s);\n        if (moduleMapMatches) {\n          const moduleMap = moduleMapMatches[1];\n          const mappings = moduleMap.split(',');\n          \n          // Check for conflicting path mappings\n          const pathMappings = {};\n          mappings.forEach(mapping => {\n            const match = mapping.match(/['\"`]([^'\"`]+)['\"`]:\\s*['\"`]([^'\"`]+)['\"`]/);\n            if (match) {\n              const [, pattern, replacement] = match;\n              if (pathMappings[pattern] && pathMappings[pattern] !== replacement) {\n                configIssues.push(`Conflicting module mapping for ${pattern}`);\n              }\n              pathMappings[pattern] = replacement;\n            }\n          });\n        }\n        \n        // Check for coverage threshold conflicts\n        const coverageMatches = configContent.match(/coverageThreshold:\\s*{[^}]+global:\\s*{([^}]+)}/s);\n        if (coverageMatches) {\n          const thresholds = coverageMatches[1];\n          const branchesMatch = thresholds.match(/branches:\\s*(\\d+)/);\n          const linesMatch = thresholds.match(/lines:\\s*(\\d+)/);\n          \n          if (branchesMatch && linesMatch) {\n            const branches = parseInt(branchesMatch[1]);\n            const lines = parseInt(linesMatch[1]);\n            \n            // Branches threshold should not be higher than lines threshold\n            if (branches > lines) {\n              configIssues.push('Branch coverage threshold higher than line coverage');\n            }\n          }\n        }\n        \n        // EXPECTED TO FAIL: Jest configuration should not have conflicts\n        expect(configIssues.length).toBe(0);\n      } else {\n        // EXPECTED TO FAIL: Jest config should exist\n        expect(fs.existsSync(jestConfigPath)).toBe(true);\n      }\n    });\n\n    it('should fail - test scripts in package.json are incomplete or conflicting', () => {\n      const packageJsonPath = path.join(process.cwd(), 'package.json');\n      \n      if (fs.existsSync(packageJsonPath)) {\n        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));\n        const scripts = packageJson.scripts || {};\n        const scriptIssues = [];\n        \n        // Check for required test scripts\n        const requiredTestScripts = [\n          'test',\n          'test:coverage',\n          'test:watch',\n          'test:ci'\n        ];\n        \n        const missingScripts = requiredTestScripts.filter(script => !scripts[script]);\n        if (missingScripts.length > 0) {\n          scriptIssues.push(`Missing required scripts: ${missingScripts.join(', ')}`);\n        }\n        \n        // Check for conflicting test configurations\n        const testScript = scripts['test'];\n        const testCiScript = scripts['test:ci'];\n        \n        if (testScript && testCiScript) {\n          // CI script should disable watch mode\n          if (testCiScript.includes('--watch') && !testCiScript.includes('--watchAll=false')) {\n            scriptIssues.push('CI test script should disable watch mode');\n          }\n          \n          // CI script should include coverage\n          if (!testCiScript.includes('--coverage')) {\n            scriptIssues.push('CI test script should include coverage reporting');\n          }\n        }\n        \n        // EXPECTED TO FAIL: Package.json test scripts should be complete and consistent\n        expect(scriptIssues.length).toBe(0);\n      } else {\n        // EXPECTED TO FAIL: package.json should exist\n        expect(fs.existsSync(packageJsonPath)).toBe(true);\n      }\n    });\n  });\n\n  describe('CRITICAL ISSUE 5: External Service Configuration Problems', () => {\n    it('should fail - external service mocks are not properly configured for testing', () => {\n      // Check for external service mock configuration\n      const setupFiles = [\n        'jest.setup.js',\n        'jest.setup.integration.js',\n        '__mocks__'\n      ];\n      \n      const externalServiceIssues = [];\n      \n      // External services that should be mocked in tests\n      const externalServices = [\n        'fetch',\n        'gemini',\n        'openai',\n        'resend',\n        'sentry'\n      ];\n      \n      setupFiles.forEach(setupFile => {\n        const setupPath = path.join(process.cwd(), setupFile);\n        \n        if (fs.existsSync(setupPath)) {\n          const isDirectory = fs.statSync(setupPath).isDirectory();\n          \n          if (isDirectory) {\n            // Check __mocks__ directory\n            const mockFiles = fs.readdirSync(setupPath);\n            const mockedServices = mockFiles.map(file => file.replace(/\\.(js|ts)$/, ''));\n            \n            const unmockedServices = externalServices.filter(service => \n              !mockedServices.includes(service)\n            );\n            \n            if (unmockedServices.length > 2) {\n              externalServiceIssues.push({ \n                location: setupFile, \n                issue: `Unmocked external services: ${unmockedServices.join(', ')}` \n              });\n            }\n          } else {\n            // Check setup file content\n            const content = fs.readFileSync(setupPath, 'utf8');\n            \n            const mockedInSetup = externalServices.filter(service => \n              content.includes(`jest.mock`) && content.includes(service)\n            );\n            \n            // Should mock critical external services\n            if (mockedInSetup.length < 2) {\n              externalServiceIssues.push({ \n                location: setupFile, \n                issue: 'Insufficient external service mocking' \n              });\n            }\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: External services should be properly mocked\n      expect(externalServiceIssues.length).toBe(0);\n    });\n  });\n});\n"], "version": 3}