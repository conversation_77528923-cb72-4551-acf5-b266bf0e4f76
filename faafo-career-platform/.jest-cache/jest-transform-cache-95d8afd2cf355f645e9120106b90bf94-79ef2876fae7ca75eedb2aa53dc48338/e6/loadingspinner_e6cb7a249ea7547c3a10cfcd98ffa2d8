f4dfac98895288fa2e7367e55051dabd
"use strict";

/* istanbul ignore next */
function cov_vy4pm8hw6() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/loading-spinner.tsx";
  var hash = "b6eea4a7ef67082544080bbc0b64a1119ee609fe";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/loading-spinner.tsx",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 40
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 36
        }
      },
      "5": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 48
        }
      },
      "6": {
        start: {
          line: 9,
          column: 20
        },
        end: {
          line: 9,
          column: 48
        }
      },
      "7": {
        start: {
          line: 10,
          column: 14
        },
        end: {
          line: 10,
          column: 47
        }
      },
      "8": {
        start: {
          line: 12,
          column: 13
        },
        end: {
          line: 12,
          column: 20
        }
      },
      "9": {
        start: {
          line: 12,
          column: 29
        },
        end: {
          line: 12,
          column: 54
        }
      },
      "10": {
        start: {
          line: 12,
          column: 61
        },
        end: {
          line: 12,
          column: 73
        }
      },
      "11": {
        start: {
          line: 12,
          column: 87
        },
        end: {
          line: 12,
          column: 110
        }
      },
      "12": {
        start: {
          line: 13,
          column: 22
        },
        end: {
          line: 17,
          column: 5
        }
      },
      "13": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 272
        }
      },
      "14": {
        start: {
          line: 21,
          column: 13
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "15": {
        start: {
          line: 21,
          column: 35
        },
        end: {
          line: 21,
          column: 68
        }
      },
      "16": {
        start: {
          line: 21,
          column: 75
        },
        end: {
          line: 21,
          column: 82
        }
      },
      "17": {
        start: {
          line: 21,
          column: 91
        },
        end: {
          line: 21,
          column: 116
        }
      },
      "18": {
        start: {
          line: 21,
          column: 123
        },
        end: {
          line: 21,
          column: 135
        }
      },
      "19": {
        start: {
          line: 21,
          column: 149
        },
        end: {
          line: 21,
          column: 172
        }
      },
      "20": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 294
        }
      },
      "21": {
        start: {
          line: 25,
          column: 13
        },
        end: {
          line: 25,
          column: 23
        }
      },
      "22": {
        start: {
          line: 25,
          column: 35
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "23": {
        start: {
          line: 25,
          column: 83
        },
        end: {
          line: 25,
          column: 96
        }
      },
      "24": {
        start: {
          line: 25,
          column: 109
        },
        end: {
          line: 25,
          column: 120
        }
      },
      "25": {
        start: {
          line: 25,
          column: 127
        },
        end: {
          line: 25,
          column: 139
        }
      },
      "26": {
        start: {
          line: 25,
          column: 153
        },
        end: {
          line: 25,
          column: 176
        }
      },
      "27": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 859
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "LoadingSpinner",
        decl: {
          start: {
            line: 11,
            column: 9
          },
          end: {
            line: 11,
            column: 23
          }
        },
        loc: {
          start: {
            line: 11,
            column: 28
          },
          end: {
            line: 19,
            column: 1
          }
        },
        line: 11
      },
      "2": {
        name: "LoadingState",
        decl: {
          start: {
            line: 20,
            column: 9
          },
          end: {
            line: 20,
            column: 21
          }
        },
        loc: {
          start: {
            line: 20,
            column: 26
          },
          end: {
            line: 23,
            column: 1
          }
        },
        line: 20
      },
      "3": {
        name: "ProgressiveLoading",
        decl: {
          start: {
            line: 24,
            column: 9
          },
          end: {
            line: 24,
            column: 27
          }
        },
        loc: {
          start: {
            line: 24,
            column: 32
          },
          end: {
            line: 27,
            column: 1
          }
        },
        line: 24
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 12,
            column: 29
          },
          end: {
            line: 12,
            column: 54
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 45
          },
          end: {
            line: 12,
            column: 49
          }
        }, {
          start: {
            line: 12,
            column: 52
          },
          end: {
            line: 12,
            column: 54
          }
        }],
        line: 12
      },
      "4": {
        loc: {
          start: {
            line: 12,
            column: 87
          },
          end: {
            line: 12,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 103
          },
          end: {
            line: 12,
            column: 105
          }
        }, {
          start: {
            line: 12,
            column: 108
          },
          end: {
            line: 12,
            column: 110
          }
        }],
        line: 12
      },
      "5": {
        loc: {
          start: {
            line: 21,
            column: 35
          },
          end: {
            line: 21,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 21,
            column: 51
          },
          end: {
            line: 21,
            column: 63
          }
        }, {
          start: {
            line: 21,
            column: 66
          },
          end: {
            line: 21,
            column: 68
          }
        }],
        line: 21
      },
      "6": {
        loc: {
          start: {
            line: 21,
            column: 91
          },
          end: {
            line: 21,
            column: 116
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 21,
            column: 107
          },
          end: {
            line: 21,
            column: 111
          }
        }, {
          start: {
            line: 21,
            column: 114
          },
          end: {
            line: 21,
            column: 116
          }
        }],
        line: 21
      },
      "7": {
        loc: {
          start: {
            line: 21,
            column: 149
          },
          end: {
            line: 21,
            column: 172
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 21,
            column: 165
          },
          end: {
            line: 21,
            column: 167
          }
        }, {
          start: {
            line: 21,
            column: 170
          },
          end: {
            line: 21,
            column: 172
          }
        }],
        line: 21
      },
      "8": {
        loc: {
          start: {
            line: 25,
            column: 35
          },
          end: {
            line: 25,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 51
          },
          end: {
            line: 25,
            column: 63
          }
        }, {
          start: {
            line: 25,
            column: 66
          },
          end: {
            line: 25,
            column: 68
          }
        }],
        line: 25
      },
      "9": {
        loc: {
          start: {
            line: 25,
            column: 153
          },
          end: {
            line: 25,
            column: 176
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 169
          },
          end: {
            line: 25,
            column: 171
          }
        }, {
          start: {
            line: 25,
            column: 174
          },
          end: {
            line: 25,
            column: 176
          }
        }],
        line: 25
      },
      "10": {
        loc: {
          start: {
            line: 26,
            column: 390
          },
          end: {
            line: 26,
            column: 519
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 390
          },
          end: {
            line: 26,
            column: 400
          }
        }, {
          start: {
            line: 26,
            column: 405
          },
          end: {
            line: 26,
            column: 518
          }
        }],
        line: 26
      },
      "11": {
        loc: {
          start: {
            line: 26,
            column: 521
          },
          end: {
            line: 26,
            column: 849
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 521
          },
          end: {
            line: 26,
            column: 543
          }
        }, {
          start: {
            line: 26,
            column: 548
          },
          end: {
            line: 26,
            column: 848
          }
        }],
        line: 26
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/loading-spinner.tsx",
      mappings: ";;;;;AAOA,wCAYC;AAQD,oCAOC;AASD,gDAyBC;;AApED,gDAA0B;AAO1B,SAAgB,cAAc,CAAC,EAAoD;QAAlD,YAAW,EAAX,IAAI,mBAAG,IAAI,KAAA,EAAE,iBAAc,EAAd,SAAS,mBAAG,EAAE,KAAA;IAC1D,IAAM,WAAW,GAAG;QAClB,EAAE,EAAE,SAAS;QACb,EAAE,EAAE,SAAS;QACb,EAAE,EAAE,SAAS;KACd,CAAC;IAEF,OAAO,CACL,gCAAK,SAAS,EAAE,+EAAwE,WAAW,CAAC,IAAI,CAAC,cAAI,SAAS,CAAE,YACtH,iCAAM,SAAS,EAAC,SAAS,2BAAkB,GACvC,CACP,CAAC;AACJ,CAAC;AAQD,SAAgB,YAAY,CAAC,EAA0E;QAAxE,eAAsB,EAAtB,OAAO,mBAAG,YAAY,KAAA,EAAE,YAAW,EAAX,IAAI,mBAAG,IAAI,KAAA,EAAE,iBAAc,EAAd,SAAS,mBAAG,EAAE,KAAA;IAChF,OAAO,CACL,iCAAK,SAAS,EAAE,qDAA8C,SAAS,CAAE,aACvE,uBAAC,cAAc,IAAC,IAAI,EAAE,IAAI,GAAI,EAC9B,iCAAM,SAAS,EAAC,kCAAkC,YAAE,OAAO,GAAQ,IAC/D,CACP,CAAC;AACJ,CAAC;AASD,SAAgB,kBAAkB,CAAC,EAKT;QAJxB,eAAsB,EAAtB,OAAO,mBAAG,YAAY,KAAA,EACtB,UAAU,gBAAA,EACV,QAAQ,cAAA,EACR,iBAAc,EAAd,SAAS,mBAAG,EAAE,KAAA;IAEd,OAAO,CACL,iCAAK,SAAS,EAAE,kEAA2D,SAAS,CAAE,aACpF,uBAAC,cAAc,IAAC,IAAI,EAAC,IAAI,GAAG,EAC5B,iCAAK,SAAS,EAAC,aAAa,aAC1B,8BAAG,SAAS,EAAC,sDAAsD,YAAE,OAAO,GAAK,EAChF,UAAU,IAAI,CACb,8BAAG,SAAS,EAAC,+CAA+C,YAAE,UAAU,GAAK,CAC9E,EACA,QAAQ,KAAK,SAAS,IAAI,CACzB,gCAAK,SAAS,EAAC,yDAAyD,YACtE,gCACE,SAAS,EAAC,mEAAmE,EAC7E,KAAK,EAAE,EAAE,KAAK,EAAE,UAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,MAAG,EAAE,GAC5D,GACE,CACP,IACG,IACF,CACP,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/loading-spinner.tsx"],
      sourcesContent: ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-6 w-6',\n    lg: 'h-8 w-8'\n  };\n\n  return (\n    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}>\n      <span className=\"sr-only\">Loading...</span>\n    </div>\n  );\n}\n\ninterface LoadingStateProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport function LoadingState({ message = 'Loading...', size = 'md', className = '' }: LoadingStateProps) {\n  return (\n    <div className={`flex items-center justify-center space-x-2 ${className}`}>\n      <LoadingSpinner size={size} />\n      <span className=\"text-gray-600 dark:text-gray-400\">{message}</span>\n    </div>\n  );\n}\n\ninterface ProgressiveLoadingProps {\n  message?: string;\n  subMessage?: string;\n  progress?: number;\n  className?: string;\n}\n\nexport function ProgressiveLoading({\n  message = 'Loading...',\n  subMessage,\n  progress,\n  className = ''\n}: ProgressiveLoadingProps) {\n  return (\n    <div className={`flex flex-col items-center justify-center space-y-4 p-8 ${className}`}>\n      <LoadingSpinner size=\"lg\" />\n      <div className=\"text-center\">\n        <p className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">{message}</p>\n        {subMessage && (\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">{subMessage}</p>\n        )}\n        {progress !== undefined && (\n          <div className=\"w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3\">\n            <div\n              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out\"\n              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b6eea4a7ef67082544080bbc0b64a1119ee609fe"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_vy4pm8hw6 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_vy4pm8hw6();
var __importDefault =
/* istanbul ignore next */
(cov_vy4pm8hw6().s[0]++,
/* istanbul ignore next */
(cov_vy4pm8hw6().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_vy4pm8hw6().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_vy4pm8hw6().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_vy4pm8hw6().f[0]++;
  cov_vy4pm8hw6().s[1]++;
  return /* istanbul ignore next */(cov_vy4pm8hw6().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_vy4pm8hw6().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_vy4pm8hw6().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_vy4pm8hw6().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_vy4pm8hw6().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_vy4pm8hw6().s[3]++;
exports.LoadingSpinner = LoadingSpinner;
/* istanbul ignore next */
cov_vy4pm8hw6().s[4]++;
exports.LoadingState = LoadingState;
/* istanbul ignore next */
cov_vy4pm8hw6().s[5]++;
exports.ProgressiveLoading = ProgressiveLoading;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_vy4pm8hw6().s[6]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_vy4pm8hw6().s[7]++, __importDefault(require("react")));
function LoadingSpinner(_a) {
  /* istanbul ignore next */
  cov_vy4pm8hw6().f[1]++;
  var _b =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[8]++, _a.size),
    size =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[9]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[3][0]++, 'md') :
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[3][1]++, _b)),
    _c =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[10]++, _a.className),
    className =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[11]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[4][0]++, '') :
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[4][1]++, _c));
  var sizeClasses =
  /* istanbul ignore next */
  (cov_vy4pm8hw6().s[12]++, {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  });
  /* istanbul ignore next */
  cov_vy4pm8hw6().s[13]++;
  return (0, jsx_runtime_1.jsx)("div", {
    className: "animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ".concat(sizeClasses[size], " ").concat(className),
    children: (0, jsx_runtime_1.jsx)("span", {
      className: "sr-only",
      children: "Loading..."
    })
  });
}
function LoadingState(_a) {
  /* istanbul ignore next */
  cov_vy4pm8hw6().f[2]++;
  var _b =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[14]++, _a.message),
    message =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[15]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[5][0]++, 'Loading...') :
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[5][1]++, _b)),
    _c =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[16]++, _a.size),
    size =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[17]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[6][0]++, 'md') :
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[6][1]++, _c)),
    _d =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[18]++, _a.className),
    className =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[19]++, _d === void 0 ?
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[7][0]++, '') :
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[7][1]++, _d));
  /* istanbul ignore next */
  cov_vy4pm8hw6().s[20]++;
  return (0, jsx_runtime_1.jsxs)("div", {
    className: "flex items-center justify-center space-x-2 ".concat(className),
    children: [(0, jsx_runtime_1.jsx)(LoadingSpinner, {
      size: size
    }), (0, jsx_runtime_1.jsx)("span", {
      className: "text-gray-600 dark:text-gray-400",
      children: message
    })]
  });
}
function ProgressiveLoading(_a) {
  /* istanbul ignore next */
  cov_vy4pm8hw6().f[3]++;
  var _b =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[21]++, _a.message),
    message =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[22]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[8][0]++, 'Loading...') :
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[8][1]++, _b)),
    subMessage =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[23]++, _a.subMessage),
    progress =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[24]++, _a.progress),
    _c =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[25]++, _a.className),
    className =
    /* istanbul ignore next */
    (cov_vy4pm8hw6().s[26]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[9][0]++, '') :
    /* istanbul ignore next */
    (cov_vy4pm8hw6().b[9][1]++, _c));
  /* istanbul ignore next */
  cov_vy4pm8hw6().s[27]++;
  return (0, jsx_runtime_1.jsxs)("div", {
    className: "flex flex-col items-center justify-center space-y-4 p-8 ".concat(className),
    children: [(0, jsx_runtime_1.jsx)(LoadingSpinner, {
      size: "lg"
    }), (0, jsx_runtime_1.jsxs)("div", {
      className: "text-center",
      children: [(0, jsx_runtime_1.jsx)("p", {
        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
        children: message
      }),
      /* istanbul ignore next */
      (cov_vy4pm8hw6().b[10][0]++, subMessage) &&
      /* istanbul ignore next */
      (cov_vy4pm8hw6().b[10][1]++, (0, jsx_runtime_1.jsx)("p", {
        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
        children: subMessage
      })),
      /* istanbul ignore next */
      (cov_vy4pm8hw6().b[11][0]++, progress !== undefined) &&
      /* istanbul ignore next */
      (cov_vy4pm8hw6().b[11][1]++, (0, jsx_runtime_1.jsx)("div", {
        className: "w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3",
        children: (0, jsx_runtime_1.jsx)("div", {
          className: "bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out",
          style: {
            width: "".concat(Math.min(100, Math.max(0, progress)), "%")
          }
        })
      }))]
    })]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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