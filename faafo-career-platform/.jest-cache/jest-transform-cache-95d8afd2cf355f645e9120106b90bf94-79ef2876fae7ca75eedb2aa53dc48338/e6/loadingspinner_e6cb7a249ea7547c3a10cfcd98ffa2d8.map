{"version": 3, "names": ["cov_vy4pm8hw6", "actualCoverage", "exports", "LoadingSpinner", "s", "LoadingState", "ProgressiveLoading", "react_1", "__importDefault", "require", "_a", "f", "_b", "size", "b", "_c", "className", "sizeClasses", "sm", "md", "lg", "jsx_runtime_1", "jsx", "concat", "children", "message", "_d", "jsxs", "subMessage", "progress", "undefined", "style", "width", "Math", "min", "max"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-6 w-6',\n    lg: 'h-8 w-8'\n  };\n\n  return (\n    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}>\n      <span className=\"sr-only\">Loading...</span>\n    </div>\n  );\n}\n\ninterface LoadingStateProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport function LoadingState({ message = 'Loading...', size = 'md', className = '' }: LoadingStateProps) {\n  return (\n    <div className={`flex items-center justify-center space-x-2 ${className}`}>\n      <LoadingSpinner size={size} />\n      <span className=\"text-gray-600 dark:text-gray-400\">{message}</span>\n    </div>\n  );\n}\n\ninterface ProgressiveLoadingProps {\n  message?: string;\n  subMessage?: string;\n  progress?: number;\n  className?: string;\n}\n\nexport function ProgressiveLoading({\n  message = 'Loading...',\n  subMessage,\n  progress,\n  className = ''\n}: ProgressiveLoadingProps) {\n  return (\n    <div className={`flex flex-col items-center justify-center space-y-4 p-8 ${className}`}>\n      <LoadingSpinner size=\"lg\" />\n      <div className=\"text-center\">\n        <p className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">{message}</p>\n        {subMessage && (\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">{subMessage}</p>\n        )}\n        {progress !== undefined && (\n          <div className=\"w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3\">\n            <div\n              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out\"\n              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWQ;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJRE,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAYC;AAAAH,aAAA,GAAAI,CAAA;AAQDF,OAAA,CAAAG,YAAA,GAAAA,YAAA;AAOC;AAAAL,aAAA,GAAAI,CAAA;AASDF,OAAA,CAAAI,kBAAA,GAAAA,kBAAA;;;;AA3CA,IAAAC,OAAA;AAAA;AAAA,CAAAP,aAAA,GAAAI,CAAA,OAAAI,eAAA,CAAAC,OAAA;AAOA,SAAgBN,cAAcA,CAACO,EAAoD;EAAA;EAAAV,aAAA,GAAAW,CAAA;MAAlDC,EAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAI,CAAA,OAAAM,EAAA,CAAAG,IAAW;IAAXA,IAAI;IAAA;IAAA,CAAAb,aAAA,GAAAI,CAAA,OAAAQ,EAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAc,CAAA,UAAG,IAAI;IAAA;IAAA,CAAAd,aAAA,GAAAc,CAAA,UAAAF,EAAA;IAAEG,EAAA;IAAA;IAAA,CAAAf,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAM,SAAc;IAAdA,SAAS;IAAA;IAAA,CAAAhB,aAAA,GAAAI,CAAA,QAAAW,EAAA;IAAA;IAAA,CAAAf,aAAA,GAAAc,CAAA,UAAG,EAAE;IAAA;IAAA,CAAAd,aAAA,GAAAc,CAAA,UAAAC,EAAA;EAC1D,IAAME,WAAW;EAAA;EAAA,CAAAjB,aAAA,GAAAI,CAAA,QAAG;IAClBc,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;GACL;EAAC;EAAApB,aAAA,GAAAI,CAAA;EAEF,OACE,IAAAiB,aAAA,CAAAC,GAAA;IAAKN,SAAS,EAAE,wEAAAO,MAAA,CAAwEN,WAAW,CAACJ,IAAI,CAAC,OAAAU,MAAA,CAAIP,SAAS,CAAE;IAAAQ,QAAA,EACtH,IAAAH,aAAA,CAAAC,GAAA;MAAMN,SAAS,EAAC,SAAS;MAAAQ,QAAA;IAAA;EAAkB,EACvC;AAEV;AAQA,SAAgBnB,YAAYA,CAACK,EAA0E;EAAA;EAAAV,aAAA,GAAAW,CAAA;MAAxEC,EAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAe,OAAsB;IAAtBA,OAAO;IAAA;IAAA,CAAAzB,aAAA,GAAAI,CAAA,QAAAQ,EAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAc,CAAA,UAAG,YAAY;IAAA;IAAA,CAAAd,aAAA,GAAAc,CAAA,UAAAF,EAAA;IAAEG,EAAA;IAAA;IAAA,CAAAf,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAG,IAAW;IAAXA,IAAI;IAAA;IAAA,CAAAb,aAAA,GAAAI,CAAA,QAAAW,EAAA;IAAA;IAAA,CAAAf,aAAA,GAAAc,CAAA,UAAG,IAAI;IAAA;IAAA,CAAAd,aAAA,GAAAc,CAAA,UAAAC,EAAA;IAAEW,EAAA;IAAA;IAAA,CAAA1B,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAM,SAAc;IAAdA,SAAS;IAAA;IAAA,CAAAhB,aAAA,GAAAI,CAAA,QAAAsB,EAAA;IAAA;IAAA,CAAA1B,aAAA,GAAAc,CAAA,UAAG,EAAE;IAAA;IAAA,CAAAd,aAAA,GAAAc,CAAA,UAAAY,EAAA;EAAA;EAAA1B,aAAA,GAAAI,CAAA;EAChF,OACE,IAAAiB,aAAA,CAAAM,IAAA;IAAKX,SAAS,EAAE,8CAAAO,MAAA,CAA8CP,SAAS,CAAE;IAAAQ,QAAA,GACvE,IAAAH,aAAA,CAAAC,GAAA,EAACnB,cAAc;MAACU,IAAI,EAAEA;IAAI,EAAI,EAC9B,IAAAQ,aAAA,CAAAC,GAAA;MAAMN,SAAS,EAAC,kCAAkC;MAAAQ,QAAA,EAAEC;IAAO,EAAQ;EAAA,EAC/D;AAEV;AASA,SAAgBnB,kBAAkBA,CAACI,EAKT;EAAA;EAAAV,aAAA,GAAAW,CAAA;MAJxBC,EAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAe,OAAsB;IAAtBA,OAAO;IAAA;IAAA,CAAAzB,aAAA,GAAAI,CAAA,QAAAQ,EAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAc,CAAA,UAAG,YAAY;IAAA;IAAA,CAAAd,aAAA,GAAAc,CAAA,UAAAF,EAAA;IACtBgB,UAAU;IAAA;IAAA,CAAA5B,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAkB,UAAA;IACVC,QAAQ;IAAA;IAAA,CAAA7B,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAmB,QAAA;IACRd,EAAA;IAAA;IAAA,CAAAf,aAAA,GAAAI,CAAA,QAAAM,EAAA,CAAAM,SAAc;IAAdA,SAAS;IAAA;IAAA,CAAAhB,aAAA,GAAAI,CAAA,QAAAW,EAAA;IAAA;IAAA,CAAAf,aAAA,GAAAc,CAAA,UAAG,EAAE;IAAA;IAAA,CAAAd,aAAA,GAAAc,CAAA,UAAAC,EAAA;EAAA;EAAAf,aAAA,GAAAI,CAAA;EAEd,OACE,IAAAiB,aAAA,CAAAM,IAAA;IAAKX,SAAS,EAAE,2DAAAO,MAAA,CAA2DP,SAAS,CAAE;IAAAQ,QAAA,GACpF,IAAAH,aAAA,CAAAC,GAAA,EAACnB,cAAc;MAACU,IAAI,EAAC;IAAI,EAAG,EAC5B,IAAAQ,aAAA,CAAAM,IAAA;MAAKX,SAAS,EAAC,aAAa;MAAAQ,QAAA,GAC1B,IAAAH,aAAA,CAAAC,GAAA;QAAGN,SAAS,EAAC,sDAAsD;QAAAQ,QAAA,EAAEC;MAAO,EAAK;MAChF;MAAA,CAAAzB,aAAA,GAAAc,CAAA,WAAAc,UAAU;MAAA;MAAA,CAAA5B,aAAA,GAAAc,CAAA,WACT,IAAAO,aAAA,CAAAC,GAAA;QAAGN,SAAS,EAAC,+CAA+C;QAAAQ,QAAA,EAAEI;MAAU,EAAK,CAC9E;MACA;MAAA,CAAA5B,aAAA,GAAAc,CAAA,WAAAe,QAAQ,KAAKC,SAAS;MAAA;MAAA,CAAA9B,aAAA,GAAAc,CAAA,WACrB,IAAAO,aAAA,CAAAC,GAAA;QAAKN,SAAS,EAAC,yDAAyD;QAAAQ,QAAA,EACtE,IAAAH,aAAA,CAAAC,GAAA;UACEN,SAAS,EAAC,mEAAmE;UAC7Ee,KAAK,EAAE;YAAEC,KAAK,EAAE,GAAAT,MAAA,CAAGU,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEN,QAAQ,CAAC,CAAC;UAAG;QAAE;MAC5D,EACE,CACP;IAAA,EACG;EAAA,EACF;AAEV", "ignoreList": []}