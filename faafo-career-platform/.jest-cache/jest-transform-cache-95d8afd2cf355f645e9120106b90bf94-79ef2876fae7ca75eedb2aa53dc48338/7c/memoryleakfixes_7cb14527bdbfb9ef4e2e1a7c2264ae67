eb64be622d665309dbc1c73402aeb893
/**
 * Memory Leak Fixes and Resource Management
 * 
 * This module provides comprehensive memory leak detection and fixes
 * for the Jest test environment to address the critical memory issues
 * identified in Priority 2 testing.
 */

// Track all timers and intervals for cleanup
const activeTimers = new Set();
const activeIntervals = new Set();
const activeTimeouts = new Set();

// Original timer functions
const originalSetTimeout = global.setTimeout;
const originalSetInterval = global.setInterval;
const originalClearTimeout = global.clearTimeout;
const originalClearInterval = global.clearInterval;

/**
 * Enhanced setTimeout that tracks timers for cleanup
 */
global.setTimeout = function(callback, delay, ...args) {
  const timerId = originalSetTimeout.call(this, (...callbackArgs) => {
    activeTimeouts.delete(timerId);
    return callback(...callbackArgs);
  }, delay, ...args);
  
  activeTimeouts.add(timerId);
  return timerId;
};

/**
 * Enhanced setInterval that tracks intervals for cleanup
 */
global.setInterval = function(callback, delay, ...args) {
  const intervalId = originalSetInterval.call(this, callback, delay, ...args);
  activeIntervals.add(intervalId);
  return intervalId;
};

/**
 * Enhanced clearTimeout that removes from tracking
 */
global.clearTimeout = function(timerId) {
  activeTimeouts.delete(timerId);
  return originalClearTimeout.call(this, timerId);
};

/**
 * Enhanced clearInterval that removes from tracking
 */
global.clearInterval = function(intervalId) {
  activeIntervals.delete(intervalId);
  return originalClearInterval.call(this, intervalId);
};

/**
 * Clean up all active timers and intervals
 */
function cleanupTimers() {
  // Clear all active timeouts
  for (const timerId of activeTimeouts) {
    originalClearTimeout(timerId);
  }
  activeTimeouts.clear();

  // Clear all active intervals
  for (const intervalId of activeIntervals) {
    originalClearInterval(intervalId);
  }
  activeIntervals.clear();
}

/**
 * Memory usage monitoring
 */
function getMemoryUsage() {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const usage = process.memoryUsage();
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
      external: Math.round(usage.external / 1024 / 1024), // MB
      rss: Math.round(usage.rss / 1024 / 1024) // MB
    };
  }
  return null;
}

/**
 * Force garbage collection if available
 */
function forceGarbageCollection() {
  if (typeof global.gc === 'function') {
    global.gc();
  }
}

/**
 * Memory leak detection
 */
function detectMemoryLeaks() {
  const usage = getMemoryUsage();
  if (!usage) return { hasLeaks: false };

  const hasLeaks = usage.heapUsed > 400; // 400MB threshold
  const hasTimerLeaks = activeTimeouts.size > 0 || activeIntervals.size > 0;

  return {
    hasLeaks: hasLeaks || hasTimerLeaks,
    memoryUsage: usage,
    activeTimers: {
      timeouts: activeTimeouts.size,
      intervals: activeIntervals.size
    },
    warnings: [
      ...(hasLeaks ? [`High memory usage: ${usage.heapUsed}MB`] : []),
      ...(hasTimerLeaks ? [`Active timers: ${activeTimeouts.size} timeouts, ${activeIntervals.size} intervals`] : [])
    ]
  };
}

/**
 * Comprehensive cleanup function
 */
function performCleanup() {
  // Clean up timers
  cleanupTimers();

  // Force garbage collection
  forceGarbageCollection();

  // Clear any global state that might be leaking
  if (typeof global !== 'undefined') {
    // Clear Jest mocks
    if (global.jest && global.jest.clearAllMocks) {
      global.jest.clearAllMocks();
    }

    // Clear any cached modules that might be holding references
    if (global.jest && global.jest.resetModules) {
      global.jest.resetModules();
    }
  }

  // Clear DOM if in browser environment
  if (typeof document !== 'undefined') {
    // Remove all event listeners
    const elements = document.querySelectorAll('*');
    elements.forEach(element => {
      const clone = element.cloneNode(true);
      if (element.parentNode) {
        element.parentNode.replaceChild(clone, element);
      }
    });
  }
}

/**
 * Setup memory monitoring for tests
 */
function setupMemoryMonitoring() {
  let initialMemory = null;

  return {
    start() {
      initialMemory = getMemoryUsage();
      return initialMemory;
    },

    check() {
      const current = getMemoryUsage();
      if (!initialMemory || !current) return null;

      const increase = current.heapUsed - initialMemory.heapUsed;
      return {
        initial: initialMemory,
        current,
        increase,
        isLeaking: increase > 50 // 50MB increase threshold
      };
    },

    cleanup() {
      performCleanup();
      const afterCleanup = getMemoryUsage();
      return afterCleanup;
    }
  };
}

/**
 * Jest setup for memory leak prevention
 */
function setupJestMemoryFixes() {
  // Before each test
  beforeEach(() => {
    // Clear any existing timers
    cleanupTimers();
    
    // Force garbage collection
    forceGarbageCollection();
  });

  // After each test
  afterEach(() => {
    // Perform comprehensive cleanup
    performCleanup();

    // Check for memory leaks
    const leakCheck = detectMemoryLeaks();
    if (leakCheck.hasLeaks) {
      console.warn('⚠️ Memory leak detected:', leakCheck.warnings.join(', '));
    }
  });

  // After all tests
  afterAll(() => {
    // Final cleanup
    performCleanup();
  });
}

/**
 * Mock factory with automatic cleanup
 */
function createCleanMock(mockImplementation) {
  const mock = jest.fn(mockImplementation);
  
  // Store original clear function
  const originalClear = mock.mockClear.bind(mock);
  
  // Enhanced clear that also cleans up references
  mock.mockClear = function() {
    originalClear();
    // Clear any stored references in the mock
    if (this.mock && this.mock.instances) {
      this.mock.instances.length = 0;
    }
    if (this.mock && this.mock.calls) {
      this.mock.calls.length = 0;
    }
    return this;
  };

  return mock;
}

/**
 * Resource pool for reusing objects
 */
class ResourcePool {
  constructor(createFn, resetFn, maxSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.maxSize = maxSize;
    this.pool = [];
  }

  acquire() {
    if (this.pool.length > 0) {
      const resource = this.pool.pop();
      return this.resetFn ? this.resetFn(resource) : resource;
    }
    return this.createFn();
  }

  release(resource) {
    if (this.pool.length < this.maxSize) {
      this.pool.push(resource);
    }
  }

  clear() {
    this.pool.length = 0;
  }
}

module.exports = {
  cleanupTimers,
  getMemoryUsage,
  forceGarbageCollection,
  detectMemoryLeaks,
  performCleanup,
  setupMemoryMonitoring,
  setupJestMemoryFixes,
  createCleanMock,
  ResourcePool
};
