41e598b02306bca5007a42eb5ebe5c60
"use strict";
'use client';

/* istanbul ignore next */
function cov_285cpx7d66() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/dashboard/PersonalizedResources.tsx";
  var hash = "092215ecec5c3f1b2d6d8f9b0ba23a4911fa3a67";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/dashboard/PersonalizedResources.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "2": {
        start: {
          line: 4,
          column: 26
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 52
        }
      },
      "4": {
        start: {
          line: 6,
          column: 4
        },
        end: {
          line: 8,
          column: 5
        }
      },
      "5": {
        start: {
          line: 7,
          column: 6
        },
        end: {
          line: 7,
          column: 68
        }
      },
      "6": {
        start: {
          line: 7,
          column: 51
        },
        end: {
          line: 7,
          column: 63
        }
      },
      "7": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 39
        }
      },
      "8": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "9": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "10": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 17
        }
      },
      "11": {
        start: {
          line: 14,
          column: 25
        },
        end: {
          line: 18,
          column: 2
        }
      },
      "12": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 72
        }
      },
      "13": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 21
        }
      },
      "14": {
        start: {
          line: 19,
          column: 19
        },
        end: {
          line: 35,
          column: 4
        }
      },
      "15": {
        start: {
          line: 20,
          column: 18
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "16": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 25,
          column: 10
        }
      },
      "17": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 23
        }
      },
      "18": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "19": {
        start: {
          line: 23,
          column: 29
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "20": {
        start: {
          line: 23,
          column: 77
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "21": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 24,
          column: 22
        }
      },
      "22": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 26
        }
      },
      "23": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 34,
          column: 6
        }
      },
      "24": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "25": {
        start: {
          line: 29,
          column: 35
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "26": {
        start: {
          line: 30,
          column: 21
        },
        end: {
          line: 30,
          column: 23
        }
      },
      "27": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "28": {
        start: {
          line: 31,
          column: 25
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "29": {
        start: {
          line: 31,
          column: 38
        },
        end: {
          line: 31,
          column: 50
        }
      },
      "30": {
        start: {
          line: 31,
          column: 56
        },
        end: {
          line: 31,
          column: 57
        }
      },
      "31": {
        start: {
          line: 31,
          column: 78
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "32": {
        start: {
          line: 31,
          column: 102
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "33": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 40
        }
      },
      "34": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 22
        }
      },
      "35": {
        start: {
          line: 36,
          column: 16
        },
        end: {
          line: 44,
          column: 1
        }
      },
      "36": {
        start: {
          line: 37,
          column: 28
        },
        end: {
          line: 37,
          column: 110
        }
      },
      "37": {
        start: {
          line: 37,
          column: 91
        },
        end: {
          line: 37,
          column: 106
        }
      },
      "38": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 43,
          column: 7
        }
      },
      "39": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 97
        }
      },
      "40": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 39,
          column: 70
        }
      },
      "41": {
        start: {
          line: 39,
          column: 85
        },
        end: {
          line: 39,
          column: 95
        }
      },
      "42": {
        start: {
          line: 40,
          column: 35
        },
        end: {
          line: 40,
          column: 100
        }
      },
      "43": {
        start: {
          line: 40,
          column: 41
        },
        end: {
          line: 40,
          column: 73
        }
      },
      "44": {
        start: {
          line: 40,
          column: 88
        },
        end: {
          line: 40,
          column: 98
        }
      },
      "45": {
        start: {
          line: 41,
          column: 32
        },
        end: {
          line: 41,
          column: 116
        }
      },
      "46": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 78
        }
      },
      "47": {
        start: {
          line: 45,
          column: 18
        },
        end: {
          line: 71,
          column: 1
        }
      },
      "48": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 46,
          column: 104
        }
      },
      "49": {
        start: {
          line: 46,
          column: 43
        },
        end: {
          line: 46,
          column: 68
        }
      },
      "50": {
        start: {
          line: 46,
          column: 57
        },
        end: {
          line: 46,
          column: 68
        }
      },
      "51": {
        start: {
          line: 46,
          column: 69
        },
        end: {
          line: 46,
          column: 81
        }
      },
      "52": {
        start: {
          line: 46,
          column: 119
        },
        end: {
          line: 46,
          column: 196
        }
      },
      "53": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 160
        }
      },
      "54": {
        start: {
          line: 47,
          column: 141
        },
        end: {
          line: 47,
          column: 153
        }
      },
      "55": {
        start: {
          line: 48,
          column: 23
        },
        end: {
          line: 48,
          column: 68
        }
      },
      "56": {
        start: {
          line: 48,
          column: 45
        },
        end: {
          line: 48,
          column: 65
        }
      },
      "57": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 70
        }
      },
      "58": {
        start: {
          line: 50,
          column: 15
        },
        end: {
          line: 50,
          column: 70
        }
      },
      "59": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 68,
          column: 66
        }
      },
      "60": {
        start: {
          line: 51,
          column: 50
        },
        end: {
          line: 68,
          column: 66
        }
      },
      "61": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 169
        }
      },
      "62": {
        start: {
          line: 52,
          column: 160
        },
        end: {
          line: 52,
          column: 169
        }
      },
      "63": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "64": {
        start: {
          line: 53,
          column: 26
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "65": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 66,
          column: 13
        }
      },
      "66": {
        start: {
          line: 55,
          column: 32
        },
        end: {
          line: 55,
          column: 39
        }
      },
      "67": {
        start: {
          line: 55,
          column: 40
        },
        end: {
          line: 55,
          column: 46
        }
      },
      "68": {
        start: {
          line: 56,
          column: 24
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "69": {
        start: {
          line: 56,
          column: 35
        },
        end: {
          line: 56,
          column: 72
        }
      },
      "70": {
        start: {
          line: 57,
          column: 24
        },
        end: {
          line: 57,
          column: 34
        }
      },
      "71": {
        start: {
          line: 57,
          column: 35
        },
        end: {
          line: 57,
          column: 45
        }
      },
      "72": {
        start: {
          line: 57,
          column: 46
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "73": {
        start: {
          line: 57,
          column: 56
        },
        end: {
          line: 57,
          column: 65
        }
      },
      "74": {
        start: {
          line: 58,
          column: 24
        },
        end: {
          line: 58,
          column: 41
        }
      },
      "75": {
        start: {
          line: 58,
          column: 42
        },
        end: {
          line: 58,
          column: 55
        }
      },
      "76": {
        start: {
          line: 58,
          column: 56
        },
        end: {
          line: 58,
          column: 65
        }
      },
      "77": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 128
        }
      },
      "78": {
        start: {
          line: 60,
          column: 110
        },
        end: {
          line: 60,
          column: 116
        }
      },
      "79": {
        start: {
          line: 60,
          column: 117
        },
        end: {
          line: 60,
          column: 126
        }
      },
      "80": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 61,
          column: 106
        }
      },
      "81": {
        start: {
          line: 61,
          column: 81
        },
        end: {
          line: 61,
          column: 97
        }
      },
      "82": {
        start: {
          line: 61,
          column: 98
        },
        end: {
          line: 61,
          column: 104
        }
      },
      "83": {
        start: {
          line: 62,
          column: 20
        },
        end: {
          line: 62,
          column: 89
        }
      },
      "84": {
        start: {
          line: 62,
          column: 57
        },
        end: {
          line: 62,
          column: 72
        }
      },
      "85": {
        start: {
          line: 62,
          column: 73
        },
        end: {
          line: 62,
          column: 80
        }
      },
      "86": {
        start: {
          line: 62,
          column: 81
        },
        end: {
          line: 62,
          column: 87
        }
      },
      "87": {
        start: {
          line: 63,
          column: 20
        },
        end: {
          line: 63,
          column: 87
        }
      },
      "88": {
        start: {
          line: 63,
          column: 47
        },
        end: {
          line: 63,
          column: 62
        }
      },
      "89": {
        start: {
          line: 63,
          column: 63
        },
        end: {
          line: 63,
          column: 78
        }
      },
      "90": {
        start: {
          line: 63,
          column: 79
        },
        end: {
          line: 63,
          column: 85
        }
      },
      "91": {
        start: {
          line: 64,
          column: 20
        },
        end: {
          line: 64,
          column: 42
        }
      },
      "92": {
        start: {
          line: 64,
          column: 30
        },
        end: {
          line: 64,
          column: 42
        }
      },
      "93": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 65,
          column: 33
        }
      },
      "94": {
        start: {
          line: 65,
          column: 34
        },
        end: {
          line: 65,
          column: 43
        }
      },
      "95": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 67,
          column: 39
        }
      },
      "96": {
        start: {
          line: 68,
          column: 22
        },
        end: {
          line: 68,
          column: 34
        }
      },
      "97": {
        start: {
          line: 68,
          column: 35
        },
        end: {
          line: 68,
          column: 41
        }
      },
      "98": {
        start: {
          line: 68,
          column: 54
        },
        end: {
          line: 68,
          column: 64
        }
      },
      "99": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 35
        }
      },
      "100": {
        start: {
          line: 69,
          column: 23
        },
        end: {
          line: 69,
          column: 35
        }
      },
      "101": {
        start: {
          line: 69,
          column: 36
        },
        end: {
          line: 69,
          column: 89
        }
      },
      "102": {
        start: {
          line: 72,
          column: 22
        },
        end: {
          line: 74,
          column: 1
        }
      },
      "103": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 62
        }
      },
      "104": {
        start: {
          line: 75,
          column: 0
        },
        end: {
          line: 75,
          column: 62
        }
      },
      "105": {
        start: {
          line: 76,
          column: 0
        },
        end: {
          line: 76,
          column: 40
        }
      },
      "106": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 77,
          column: 48
        }
      },
      "107": {
        start: {
          line: 78,
          column: 14
        },
        end: {
          line: 78,
          column: 44
        }
      },
      "108": {
        start: {
          line: 79,
          column: 13
        },
        end: {
          line: 79,
          column: 50
        }
      },
      "109": {
        start: {
          line: 80,
          column: 14
        },
        end: {
          line: 80,
          column: 40
        }
      },
      "110": {
        start: {
          line: 81,
          column: 21
        },
        end: {
          line: 81,
          column: 44
        }
      },
      "111": {
        start: {
          line: 82,
          column: 15
        },
        end: {
          line: 82,
          column: 48
        }
      },
      "112": {
        start: {
          line: 84,
          column: 16
        },
        end: {
          line: 84,
          column: 20
        }
      },
      "113": {
        start: {
          line: 85,
          column: 18
        },
        end: {
          line: 85,
          column: 48
        }
      },
      "114": {
        start: {
          line: 86,
          column: 13
        },
        end: {
          line: 86,
          column: 40
        }
      },
      "115": {
        start: {
          line: 86,
          column: 49
        },
        end: {
          line: 86,
          column: 54
        }
      },
      "116": {
        start: {
          line: 86,
          column: 66
        },
        end: {
          line: 86,
          column: 71
        }
      },
      "117": {
        start: {
          line: 87,
          column: 13
        },
        end: {
          line: 87,
          column: 40
        }
      },
      "118": {
        start: {
          line: 87,
          column: 52
        },
        end: {
          line: 87,
          column: 57
        }
      },
      "119": {
        start: {
          line: 87,
          column: 72
        },
        end: {
          line: 87,
          column: 77
        }
      },
      "120": {
        start: {
          line: 88,
          column: 13
        },
        end: {
          line: 88,
          column: 40
        }
      },
      "121": {
        start: {
          line: 88,
          column: 50
        },
        end: {
          line: 88,
          column: 55
        }
      },
      "122": {
        start: {
          line: 88,
          column: 68
        },
        end: {
          line: 88,
          column: 73
        }
      },
      "123": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 94,
          column: 18
        }
      },
      "124": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 93,
          column: 9
        }
      },
      "125": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 41
        }
      },
      "126": {
        start: {
          line: 95,
          column: 37
        },
        end: {
          line: 142,
          column: 21
        }
      },
      "127": {
        start: {
          line: 95,
          column: 76
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "128": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 141,
          column: 11
        }
      },
      "129": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 140,
          column: 13
        }
      },
      "130": {
        start: {
          line: 100,
          column: 20
        },
        end: {
          line: 100,
          column: 47
        }
      },
      "131": {
        start: {
          line: 101,
          column: 20
        },
        end: {
          line: 101,
          column: 37
        }
      },
      "132": {
        start: {
          line: 102,
          column: 20
        },
        end: {
          line: 102,
          column: 87
        }
      },
      "133": {
        start: {
          line: 104,
          column: 20
        },
        end: {
          line: 104,
          column: 41
        }
      },
      "134": {
        start: {
          line: 105,
          column: 20
        },
        end: {
          line: 117,
          column: 21
        }
      },
      "135": {
        start: {
          line: 107,
          column: 24
        },
        end: {
          line: 115,
          column: 25
        }
      },
      "136": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 113,
          column: 31
        }
      },
      "137": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 114,
          column: 50
        }
      },
      "138": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 82
        }
      },
      "139": {
        start: {
          line: 118,
          column: 20
        },
        end: {
          line: 118,
          column: 58
        }
      },
      "140": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 39
        }
      },
      "141": {
        start: {
          line: 121,
          column: 20
        },
        end: {
          line: 126,
          column: 21
        }
      },
      "142": {
        start: {
          line: 122,
          column: 24
        },
        end: {
          line: 122,
          column: 45
        }
      },
      "143": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 125,
          column: 85
        }
      },
      "144": {
        start: {
          line: 127,
          column: 20
        },
        end: {
          line: 127,
          column: 44
        }
      },
      "145": {
        start: {
          line: 129,
          column: 20
        },
        end: {
          line: 129,
          column: 38
        }
      },
      "146": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 133,
          column: 21
        }
      },
      "147": {
        start: {
          line: 132,
          column: 24
        },
        end: {
          line: 132,
          column: 87
        }
      },
      "148": {
        start: {
          line: 134,
          column: 20
        },
        end: {
          line: 134,
          column: 91
        }
      },
      "149": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 135,
          column: 44
        }
      },
      "150": {
        start: {
          line: 137,
          column: 20
        },
        end: {
          line: 137,
          column: 38
        }
      },
      "151": {
        start: {
          line: 138,
          column: 20
        },
        end: {
          line: 138,
          column: 46
        }
      },
      "152": {
        start: {
          line: 139,
          column: 24
        },
        end: {
          line: 139,
          column: 46
        }
      },
      "153": {
        start: {
          line: 143,
          column: 33
        },
        end: {
          line: 172,
          column: 14
        }
      },
      "154": {
        start: {
          line: 143,
          column: 90
        },
        end: {
          line: 172,
          column: 7
        }
      },
      "155": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 171,
          column: 11
        }
      },
      "156": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 170,
          column: 13
        }
      },
      "157": {
        start: {
          line: 148,
          column: 20
        },
        end: {
          line: 148,
          column: 46
        }
      },
      "158": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 158,
          column: 28
        }
      },
      "159": {
        start: {
          line: 160,
          column: 20
        },
        end: {
          line: 160,
          column: 41
        }
      },
      "160": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 163,
          column: 21
        }
      },
      "161": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 78
        }
      },
      "162": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 44
        }
      },
      "163": {
        start: {
          line: 166,
          column: 20
        },
        end: {
          line: 166,
          column: 40
        }
      },
      "164": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 167,
          column: 80
        }
      },
      "165": {
        start: {
          line: 168,
          column: 20
        },
        end: {
          line: 168,
          column: 44
        }
      },
      "166": {
        start: {
          line: 169,
          column: 24
        },
        end: {
          line: 169,
          column: 46
        }
      },
      "167": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "168": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 174,
          column: 883
        }
      },
      "169": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 178,
          column: 5
        }
      },
      "170": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 177,
          column: 751
        }
      },
      "171": {
        start: {
          line: 177,
          column: 455
        },
        end: {
          line: 177,
          column: 739
        }
      },
      "172": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 181,
          column: 5
        }
      },
      "173": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 711
        }
      },
      "174": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 184,
          column: 3439
        }
      },
      "175": {
        start: {
          line: 182,
          column: 923
        },
        end: {
          line: 184,
          column: 1860
        }
      },
      "176": {
        start: {
          line: 184,
          column: 1671
        },
        end: {
          line: 184,
          column: 1728
        }
      },
      "177": {
        start: {
          line: 184,
          column: 2859
        },
        end: {
          line: 184,
          column: 3147
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 3,
            column: 75
          }
        },
        loc: {
          start: {
            line: 3,
            column: 96
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 38
          },
          end: {
            line: 7,
            column: 39
          }
        },
        loc: {
          start: {
            line: 7,
            column: 49
          },
          end: {
            line: 7,
            column: 65
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 10,
            column: 7
          }
        },
        loc: {
          start: {
            line: 10,
            column: 28
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 10
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 14,
            column: 81
          }
        },
        loc: {
          start: {
            line: 14,
            column: 95
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 16,
            column: 6
          }
        },
        loc: {
          start: {
            line: 16,
            column: 20
          },
          end: {
            line: 18,
            column: 1
          }
        },
        line: 16
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 52
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 19
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 19
          }
        },
        loc: {
          start: {
            line: 20,
            column: 30
          },
          end: {
            line: 27,
            column: 5
          }
        },
        line: 20
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 21,
            column: 49
          }
        },
        loc: {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 25,
            column: 9
          }
        },
        line: 21
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 28,
            column: 11
          },
          end: {
            line: 28,
            column: 12
          }
        },
        loc: {
          start: {
            line: 28,
            column: 26
          },
          end: {
            line: 34,
            column: 5
          }
        },
        line: 28
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 36,
            column: 44
          },
          end: {
            line: 36,
            column: 45
          }
        },
        loc: {
          start: {
            line: 36,
            column: 89
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 36
      },
      "10": {
        name: "adopt",
        decl: {
          start: {
            line: 37,
            column: 13
          },
          end: {
            line: 37,
            column: 18
          }
        },
        loc: {
          start: {
            line: 37,
            column: 26
          },
          end: {
            line: 37,
            column: 112
          }
        },
        line: 37
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 37,
            column: 70
          },
          end: {
            line: 37,
            column: 71
          }
        },
        loc: {
          start: {
            line: 37,
            column: 89
          },
          end: {
            line: 37,
            column: 108
          }
        },
        line: 37
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 38,
            column: 36
          },
          end: {
            line: 38,
            column: 37
          }
        },
        loc: {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 43,
            column: 5
          }
        },
        line: 38
      },
      "13": {
        name: "fulfilled",
        decl: {
          start: {
            line: 39,
            column: 17
          },
          end: {
            line: 39,
            column: 26
          }
        },
        loc: {
          start: {
            line: 39,
            column: 34
          },
          end: {
            line: 39,
            column: 99
          }
        },
        line: 39
      },
      "14": {
        name: "rejected",
        decl: {
          start: {
            line: 40,
            column: 17
          },
          end: {
            line: 40,
            column: 25
          }
        },
        loc: {
          start: {
            line: 40,
            column: 33
          },
          end: {
            line: 40,
            column: 102
          }
        },
        line: 40
      },
      "15": {
        name: "step",
        decl: {
          start: {
            line: 41,
            column: 17
          },
          end: {
            line: 41,
            column: 21
          }
        },
        loc: {
          start: {
            line: 41,
            column: 30
          },
          end: {
            line: 41,
            column: 118
          }
        },
        line: 41
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 45,
            column: 48
          },
          end: {
            line: 45,
            column: 49
          }
        },
        loc: {
          start: {
            line: 45,
            column: 73
          },
          end: {
            line: 71,
            column: 1
          }
        },
        line: 45
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 46,
            column: 30
          },
          end: {
            line: 46,
            column: 31
          }
        },
        loc: {
          start: {
            line: 46,
            column: 41
          },
          end: {
            line: 46,
            column: 83
          }
        },
        line: 46
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 47,
            column: 128
          },
          end: {
            line: 47,
            column: 129
          }
        },
        loc: {
          start: {
            line: 47,
            column: 139
          },
          end: {
            line: 47,
            column: 155
          }
        },
        line: 47
      },
      "19": {
        name: "verb",
        decl: {
          start: {
            line: 48,
            column: 13
          },
          end: {
            line: 48,
            column: 17
          }
        },
        loc: {
          start: {
            line: 48,
            column: 21
          },
          end: {
            line: 48,
            column: 70
          }
        },
        line: 48
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 48,
            column: 30
          },
          end: {
            line: 48,
            column: 31
          }
        },
        loc: {
          start: {
            line: 48,
            column: 43
          },
          end: {
            line: 48,
            column: 67
          }
        },
        line: 48
      },
      "21": {
        name: "step",
        decl: {
          start: {
            line: 49,
            column: 13
          },
          end: {
            line: 49,
            column: 17
          }
        },
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 49
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 72,
            column: 56
          },
          end: {
            line: 72,
            column: 57
          }
        },
        loc: {
          start: {
            line: 72,
            column: 71
          },
          end: {
            line: 74,
            column: 1
          }
        },
        line: 72
      },
      "23": {
        name: "PersonalizedResources",
        decl: {
          start: {
            line: 83,
            column: 9
          },
          end: {
            line: 83,
            column: 30
          }
        },
        loc: {
          start: {
            line: 83,
            column: 33
          },
          end: {
            line: 185,
            column: 1
          }
        },
        line: 83
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 89,
            column: 27
          },
          end: {
            line: 89,
            column: 28
          }
        },
        loc: {
          start: {
            line: 89,
            column: 39
          },
          end: {
            line: 94,
            column: 5
          }
        },
        line: 89
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 95,
            column: 62
          },
          end: {
            line: 95,
            column: 63
          }
        },
        loc: {
          start: {
            line: 95,
            column: 74
          },
          end: {
            line: 142,
            column: 9
          }
        },
        line: 95
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 95,
            column: 116
          },
          end: {
            line: 95,
            column: 117
          }
        },
        loc: {
          start: {
            line: 95,
            column: 128
          },
          end: {
            line: 142,
            column: 5
          }
        },
        line: 95
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 97,
            column: 33
          },
          end: {
            line: 97,
            column: 34
          }
        },
        loc: {
          start: {
            line: 97,
            column: 47
          },
          end: {
            line: 141,
            column: 9
          }
        },
        line: 97
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 143,
            column: 58
          },
          end: {
            line: 143,
            column: 59
          }
        },
        loc: {
          start: {
            line: 143,
            column: 88
          },
          end: {
            line: 172,
            column: 9
          }
        },
        line: 143
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 143,
            column: 130
          },
          end: {
            line: 143,
            column: 131
          }
        },
        loc: {
          start: {
            line: 143,
            column: 142
          },
          end: {
            line: 172,
            column: 5
          }
        },
        line: 143
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 145,
            column: 33
          },
          end: {
            line: 145,
            column: 34
          }
        },
        loc: {
          start: {
            line: 145,
            column: 47
          },
          end: {
            line: 171,
            column: 9
          }
        },
        line: 145
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 177,
            column: 440
          },
          end: {
            line: 177,
            column: 441
          }
        },
        loc: {
          start: {
            line: 177,
            column: 453
          },
          end: {
            line: 177,
            column: 741
          }
        },
        line: 177
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 182,
            column: 901
          },
          end: {
            line: 182,
            column: 902
          }
        },
        loc: {
          start: {
            line: 182,
            column: 921
          },
          end: {
            line: 184,
            column: 1862
          }
        },
        line: 182
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 184,
            column: 1657
          },
          end: {
            line: 184,
            column: 1658
          }
        },
        loc: {
          start: {
            line: 184,
            column: 1669
          },
          end: {
            line: 184,
            column: 1730
          }
        },
        line: 184
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 184,
            column: 2841
          },
          end: {
            line: 184,
            column: 2842
          }
        },
        loc: {
          start: {
            line: 184,
            column: 2857
          },
          end: {
            line: 184,
            column: 3149
          }
        },
        line: 184
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 13,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 27
          }
        }, {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 3,
            column: 51
          }
        }, {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 10,
            column: 1
          }
        }, {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 13,
            column: 1
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "4": {
        loc: {
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 13
          }
        }, {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "5": {
        loc: {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 6,
            column: 34
          },
          end: {
            line: 6,
            column: 47
          }
        }, {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "6": {
        loc: {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 63
          }
        }, {
          start: {
            line: 6,
            column: 67
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "7": {
        loc: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "8": {
        loc: {
          start: {
            line: 14,
            column: 25
          },
          end: {
            line: 18,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 30
          }
        }, {
          start: {
            line: 14,
            column: 34
          },
          end: {
            line: 14,
            column: 57
          }
        }, {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "9": {
        loc: {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 16,
            column: 1
          }
        }, {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "10": {
        loc: {
          start: {
            line: 19,
            column: 19
          },
          end: {
            line: 35,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 20
          },
          end: {
            line: 19,
            column: 24
          }
        }, {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 19,
            column: 45
          }
        }, {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 35,
            column: 4
          }
        }],
        line: 19
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 25,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 21,
            column: 44
          }
        }, {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 25,
            column: 9
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "13": {
        loc: {
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "14": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 15
          }
        }, {
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 29,
            column: 33
          }
        }],
        line: 29
      },
      "15": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "16": {
        loc: {
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "17": {
        loc: {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 44,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 17
          },
          end: {
            line: 36,
            column: 21
          }
        }, {
          start: {
            line: 36,
            column: 25
          },
          end: {
            line: 36,
            column: 39
          }
        }, {
          start: {
            line: 36,
            column: 44
          },
          end: {
            line: 44,
            column: 1
          }
        }],
        line: 36
      },
      "18": {
        loc: {
          start: {
            line: 37,
            column: 35
          },
          end: {
            line: 37,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 56
          },
          end: {
            line: 37,
            column: 61
          }
        }, {
          start: {
            line: 37,
            column: 64
          },
          end: {
            line: 37,
            column: 109
          }
        }],
        line: 37
      },
      "19": {
        loc: {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 17
          }
        }, {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 38,
            column: 33
          }
        }],
        line: 38
      },
      "20": {
        loc: {
          start: {
            line: 41,
            column: 32
          },
          end: {
            line: 41,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 41,
            column: 46
          },
          end: {
            line: 41,
            column: 67
          }
        }, {
          start: {
            line: 41,
            column: 70
          },
          end: {
            line: 41,
            column: 115
          }
        }],
        line: 41
      },
      "21": {
        loc: {
          start: {
            line: 42,
            column: 51
          },
          end: {
            line: 42,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 51
          },
          end: {
            line: 42,
            column: 61
          }
        }, {
          start: {
            line: 42,
            column: 65
          },
          end: {
            line: 42,
            column: 67
          }
        }],
        line: 42
      },
      "22": {
        loc: {
          start: {
            line: 45,
            column: 18
          },
          end: {
            line: 71,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 19
          },
          end: {
            line: 45,
            column: 23
          }
        }, {
          start: {
            line: 45,
            column: 27
          },
          end: {
            line: 45,
            column: 43
          }
        }, {
          start: {
            line: 45,
            column: 48
          },
          end: {
            line: 71,
            column: 1
          }
        }],
        line: 45
      },
      "23": {
        loc: {
          start: {
            line: 46,
            column: 43
          },
          end: {
            line: 46,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 43
          },
          end: {
            line: 46,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "24": {
        loc: {
          start: {
            line: 46,
            column: 134
          },
          end: {
            line: 46,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 167
          },
          end: {
            line: 46,
            column: 175
          }
        }, {
          start: {
            line: 46,
            column: 178
          },
          end: {
            line: 46,
            column: 184
          }
        }],
        line: 46
      },
      "25": {
        loc: {
          start: {
            line: 47,
            column: 74
          },
          end: {
            line: 47,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 74
          },
          end: {
            line: 47,
            column: 102
          }
        }, {
          start: {
            line: 47,
            column: 107
          },
          end: {
            line: 47,
            column: 155
          }
        }],
        line: 47
      },
      "26": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "27": {
        loc: {
          start: {
            line: 51,
            column: 15
          },
          end: {
            line: 51,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 15
          },
          end: {
            line: 51,
            column: 16
          }
        }, {
          start: {
            line: 51,
            column: 21
          },
          end: {
            line: 51,
            column: 44
          }
        }],
        line: 51
      },
      "28": {
        loc: {
          start: {
            line: 51,
            column: 28
          },
          end: {
            line: 51,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 28
          },
          end: {
            line: 51,
            column: 33
          }
        }, {
          start: {
            line: 51,
            column: 38
          },
          end: {
            line: 51,
            column: 43
          }
        }],
        line: 51
      },
      "29": {
        loc: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "30": {
        loc: {
          start: {
            line: 52,
            column: 23
          },
          end: {
            line: 52,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 23
          },
          end: {
            line: 52,
            column: 24
          }
        }, {
          start: {
            line: 52,
            column: 29
          },
          end: {
            line: 52,
            column: 125
          }
        }, {
          start: {
            line: 52,
            column: 130
          },
          end: {
            line: 52,
            column: 158
          }
        }],
        line: 52
      },
      "31": {
        loc: {
          start: {
            line: 52,
            column: 33
          },
          end: {
            line: 52,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 45
          },
          end: {
            line: 52,
            column: 56
          }
        }, {
          start: {
            line: 52,
            column: 59
          },
          end: {
            line: 52,
            column: 125
          }
        }],
        line: 52
      },
      "32": {
        loc: {
          start: {
            line: 52,
            column: 59
          },
          end: {
            line: 52,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 67
          },
          end: {
            line: 52,
            column: 116
          }
        }, {
          start: {
            line: 52,
            column: 119
          },
          end: {
            line: 52,
            column: 125
          }
        }],
        line: 52
      },
      "33": {
        loc: {
          start: {
            line: 52,
            column: 67
          },
          end: {
            line: 52,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 67
          },
          end: {
            line: 52,
            column: 77
          }
        }, {
          start: {
            line: 52,
            column: 82
          },
          end: {
            line: 52,
            column: 115
          }
        }],
        line: 52
      },
      "34": {
        loc: {
          start: {
            line: 52,
            column: 82
          },
          end: {
            line: 52,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 83
          },
          end: {
            line: 52,
            column: 98
          }
        }, {
          start: {
            line: 52,
            column: 103
          },
          end: {
            line: 52,
            column: 112
          }
        }],
        line: 52
      },
      "35": {
        loc: {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "36": {
        loc: {
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 66,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 55,
            column: 16
          },
          end: {
            line: 55,
            column: 23
          }
        }, {
          start: {
            line: 55,
            column: 24
          },
          end: {
            line: 55,
            column: 46
          }
        }, {
          start: {
            line: 56,
            column: 16
          },
          end: {
            line: 56,
            column: 72
          }
        }, {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 57,
            column: 65
          }
        }, {
          start: {
            line: 58,
            column: 16
          },
          end: {
            line: 58,
            column: 65
          }
        }, {
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 65,
            column: 43
          }
        }],
        line: 54
      },
      "37": {
        loc: {
          start: {
            line: 60,
            column: 20
          },
          end: {
            line: 60,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 60,
            column: 20
          },
          end: {
            line: 60,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 60
      },
      "38": {
        loc: {
          start: {
            line: 60,
            column: 24
          },
          end: {
            line: 60,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 24
          },
          end: {
            line: 60,
            column: 74
          }
        }, {
          start: {
            line: 60,
            column: 79
          },
          end: {
            line: 60,
            column: 90
          }
        }, {
          start: {
            line: 60,
            column: 94
          },
          end: {
            line: 60,
            column: 105
          }
        }],
        line: 60
      },
      "39": {
        loc: {
          start: {
            line: 60,
            column: 42
          },
          end: {
            line: 60,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 42
          },
          end: {
            line: 60,
            column: 54
          }
        }, {
          start: {
            line: 60,
            column: 58
          },
          end: {
            line: 60,
            column: 73
          }
        }],
        line: 60
      },
      "40": {
        loc: {
          start: {
            line: 61,
            column: 20
          },
          end: {
            line: 61,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 20
          },
          end: {
            line: 61,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "41": {
        loc: {
          start: {
            line: 61,
            column: 24
          },
          end: {
            line: 61,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 24
          },
          end: {
            line: 61,
            column: 35
          }
        }, {
          start: {
            line: 61,
            column: 40
          },
          end: {
            line: 61,
            column: 42
          }
        }, {
          start: {
            line: 61,
            column: 47
          },
          end: {
            line: 61,
            column: 59
          }
        }, {
          start: {
            line: 61,
            column: 63
          },
          end: {
            line: 61,
            column: 75
          }
        }],
        line: 61
      },
      "42": {
        loc: {
          start: {
            line: 62,
            column: 20
          },
          end: {
            line: 62,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 62,
            column: 20
          },
          end: {
            line: 62,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 62
      },
      "43": {
        loc: {
          start: {
            line: 62,
            column: 24
          },
          end: {
            line: 62,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 24
          },
          end: {
            line: 62,
            column: 35
          }
        }, {
          start: {
            line: 62,
            column: 39
          },
          end: {
            line: 62,
            column: 53
          }
        }],
        line: 62
      },
      "44": {
        loc: {
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "45": {
        loc: {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 25
          }
        }, {
          start: {
            line: 63,
            column: 29
          },
          end: {
            line: 63,
            column: 43
          }
        }],
        line: 63
      },
      "46": {
        loc: {
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 64,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 64,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "47": {
        loc: {
          start: {
            line: 69,
            column: 8
          },
          end: {
            line: 69,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 8
          },
          end: {
            line: 69,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "48": {
        loc: {
          start: {
            line: 69,
            column: 52
          },
          end: {
            line: 69,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 60
          },
          end: {
            line: 69,
            column: 65
          }
        }, {
          start: {
            line: 69,
            column: 68
          },
          end: {
            line: 69,
            column: 74
          }
        }],
        line: 69
      },
      "49": {
        loc: {
          start: {
            line: 72,
            column: 22
          },
          end: {
            line: 74,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 23
          },
          end: {
            line: 72,
            column: 27
          }
        }, {
          start: {
            line: 72,
            column: 31
          },
          end: {
            line: 72,
            column: 51
          }
        }, {
          start: {
            line: 72,
            column: 56
          },
          end: {
            line: 74,
            column: 1
          }
        }],
        line: 72
      },
      "50": {
        loc: {
          start: {
            line: 73,
            column: 11
          },
          end: {
            line: 73,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 37
          },
          end: {
            line: 73,
            column: 40
          }
        }, {
          start: {
            line: 73,
            column: 43
          },
          end: {
            line: 73,
            column: 61
          }
        }],
        line: 73
      },
      "51": {
        loc: {
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 73,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 73,
            column: 15
          }
        }, {
          start: {
            line: 73,
            column: 19
          },
          end: {
            line: 73,
            column: 33
          }
        }],
        line: 73
      },
      "52": {
        loc: {
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 93,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 93,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "53": {
        loc: {
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 91,
            column: 124
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 91,
            column: 110
          },
          end: {
            line: 91,
            column: 116
          }
        }, {
          start: {
            line: 91,
            column: 119
          },
          end: {
            line: 91,
            column: 124
          }
        }],
        line: 91
      },
      "54": {
        loc: {
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 91,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 91,
            column: 90
          }
        }, {
          start: {
            line: 91,
            column: 94
          },
          end: {
            line: 91,
            column: 107
          }
        }],
        line: 91
      },
      "55": {
        loc: {
          start: {
            line: 91,
            column: 18
          },
          end: {
            line: 91,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 91,
            column: 59
          },
          end: {
            line: 91,
            column: 65
          }
        }, {
          start: {
            line: 91,
            column: 68
          },
          end: {
            line: 91,
            column: 80
          }
        }],
        line: 91
      },
      "56": {
        loc: {
          start: {
            line: 91,
            column: 18
          },
          end: {
            line: 91,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 18
          },
          end: {
            line: 91,
            column: 34
          }
        }, {
          start: {
            line: 91,
            column: 38
          },
          end: {
            line: 91,
            column: 56
          }
        }],
        line: 91
      },
      "57": {
        loc: {
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 140,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 102,
            column: 87
          }
        }, {
          start: {
            line: 103,
            column: 16
          },
          end: {
            line: 118,
            column: 58
          }
        }, {
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 127,
            column: 44
          }
        }, {
          start: {
            line: 128,
            column: 16
          },
          end: {
            line: 135,
            column: 44
          }
        }, {
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 138,
            column: 46
          }
        }, {
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 139,
            column: 46
          }
        }],
        line: 98
      },
      "58": {
        loc: {
          start: {
            line: 105,
            column: 20
          },
          end: {
            line: 117,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 20
          },
          end: {
            line: 117,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "59": {
        loc: {
          start: {
            line: 107,
            column: 24
          },
          end: {
            line: 115,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 24
          },
          end: {
            line: 115,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "60": {
        loc: {
          start: {
            line: 121,
            column: 20
          },
          end: {
            line: 126,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 20
          },
          end: {
            line: 126,
            column: 21
          }
        }, {
          start: {
            line: 124,
            column: 25
          },
          end: {
            line: 126,
            column: 21
          }
        }],
        line: 121
      },
      "61": {
        loc: {
          start: {
            line: 125,
            column: 40
          },
          end: {
            line: 125,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 125,
            column: 40
          },
          end: {
            line: 125,
            column: 52
          }
        }, {
          start: {
            line: 125,
            column: 56
          },
          end: {
            line: 125,
            column: 83
          }
        }],
        line: 125
      },
      "62": {
        loc: {
          start: {
            line: 131,
            column: 20
          },
          end: {
            line: 133,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 20
          },
          end: {
            line: 133,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "63": {
        loc: {
          start: {
            line: 131,
            column: 24
          },
          end: {
            line: 131,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 131,
            column: 24
          },
          end: {
            line: 131,
            column: 46
          }
        }, {
          start: {
            line: 131,
            column: 50
          },
          end: {
            line: 131,
            column: 80
          }
        }],
        line: 131
      },
      "64": {
        loc: {
          start: {
            line: 134,
            column: 29
          },
          end: {
            line: 134,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 134,
            column: 54
          },
          end: {
            line: 134,
            column: 67
          }
        }, {
          start: {
            line: 134,
            column: 70
          },
          end: {
            line: 134,
            column: 89
          }
        }],
        line: 134
      },
      "65": {
        loc: {
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 170,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 158,
            column: 28
          }
        }, {
          start: {
            line: 159,
            column: 16
          },
          end: {
            line: 164,
            column: 44
          }
        }, {
          start: {
            line: 165,
            column: 16
          },
          end: {
            line: 168,
            column: 44
          }
        }, {
          start: {
            line: 169,
            column: 16
          },
          end: {
            line: 169,
            column: 46
          }
        }],
        line: 146
      },
      "66": {
        loc: {
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 163,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 163,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "67": {
        loc: {
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "68": {
        loc: {
          start: {
            line: 176,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "69": {
        loc: {
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "70": {
        loc: {
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 22
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: 179,
            column: 17
          },
          end: {
            line: 179,
            column: 22
          }
        }],
        line: 179
      },
      "71": {
        loc: {
          start: {
            line: 180,
            column: 525
          },
          end: {
            line: 180,
            column: 573
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 180,
            column: 525
          },
          end: {
            line: 180,
            column: 530
          }
        }, {
          start: {
            line: 180,
            column: 534
          },
          end: {
            line: 180,
            column: 573
          }
        }],
        line: 180
      },
      "72": {
        loc: {
          start: {
            line: 182,
            column: 824
          },
          end: {
            line: 184,
            column: 2412
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 182,
            column: 870
          },
          end: {
            line: 184,
            column: 1863
          }
        }, {
          start: {
            line: 184,
            column: 1867
          },
          end: {
            line: 184,
            column: 2411
          }
        }],
        line: 182
      },
      "73": {
        loc: {
          start: {
            line: 182,
            column: 824
          },
          end: {
            line: 182,
            column: 867
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 824
          },
          end: {
            line: 182,
            column: 838
          }
        }, {
          start: {
            line: 182,
            column: 842
          },
          end: {
            line: 182,
            column: 867
          }
        }],
        line: 182
      },
      "74": {
        loc: {
          start: {
            line: 182,
            column: 1452
          },
          end: {
            line: 184,
            column: 165
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 1452
          },
          end: {
            line: 182,
            column: 1471
          }
        }, {
          start: {
            line: 182,
            column: 1476
          },
          end: {
            line: 184,
            column: 164
          }
        }],
        line: 182
      },
      "75": {
        loc: {
          start: {
            line: 182,
            column: 1568
          },
          end: {
            line: 184,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 182,
            column: 1605
          },
          end: {
            line: 182,
            column: 1672
          }
        }, {
          start: {
            line: 183,
            column: 52
          },
          end: {
            line: 184,
            column: 115
          }
        }],
        line: 182
      },
      "76": {
        loc: {
          start: {
            line: 183,
            column: 52
          },
          end: {
            line: 184,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 183,
            column: 93
          },
          end: {
            line: 183,
            column: 164
          }
        }, {
          start: {
            line: 184,
            column: 56
          },
          end: {
            line: 184,
            column: 115
          }
        }],
        line: 183
      },
      "77": {
        loc: {
          start: {
            line: 184,
            column: 167
          },
          end: {
            line: 184,
            column: 363
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 167
          },
          end: {
            line: 184,
            column: 191
          }
        }, {
          start: {
            line: 184,
            column: 196
          },
          end: {
            line: 184,
            column: 362
          }
        }],
        line: 184
      },
      "78": {
        loc: {
          start: {
            line: 184,
            column: 365
          },
          end: {
            line: 184,
            column: 717
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 365
          },
          end: {
            line: 184,
            column: 391
          }
        }, {
          start: {
            line: 184,
            column: 396
          },
          end: {
            line: 184,
            column: 716
          }
        }],
        line: 184
      },
      "79": {
        loc: {
          start: {
            line: 184,
            column: 723
          },
          end: {
            line: 184,
            column: 931
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 723
          },
          end: {
            line: 184,
            column: 738
          }
        }, {
          start: {
            line: 184,
            column: 743
          },
          end: {
            line: 184,
            column: 930
          }
        }],
        line: 184
      },
      "80": {
        loc: {
          start: {
            line: 184,
            column: 869
          },
          end: {
            line: 184,
            column: 926
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 869
          },
          end: {
            line: 184,
            column: 886
          }
        }, {
          start: {
            line: 184,
            column: 890
          },
          end: {
            line: 184,
            column: 926
          }
        }],
        line: 184
      },
      "81": {
        loc: {
          start: {
            line: 184,
            column: 2417
          },
          end: {
            line: 184,
            column: 3158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 2417
          },
          end: {
            line: 184,
            column: 2442
          }
        }, {
          start: {
            line: 184,
            column: 2446
          },
          end: {
            line: 184,
            column: 2482
          }
        }, {
          start: {
            line: 184,
            column: 2487
          },
          end: {
            line: 184,
            column: 3157
          }
        }],
        line: 184
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0, 0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0, 0, 0, 0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0, 0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/dashboard/PersonalizedResources.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCb,wCAoQC;;AAzSD,6CAAgE;AAChE,mDAA6B;AAC7B,yCAA6C;AAC7C,6CAAqG;AACrG,iDAAgD;AAiChD,SAAwB,qBAAqB;IAA7C,iBAoQC;IAnQS,IAAM,OAAO,GAAK,IAAA,kBAAU,GAAE,KAAjB,CAAkB;IACjC,IAAA,KAAkB,IAAA,gBAAQ,EAAmC,IAAI,CAAC,EAAjE,IAAI,QAAA,EAAE,OAAO,QAAoD,CAAC;IACnE,IAAA,KAAwB,IAAA,gBAAQ,EAAC,IAAI,CAAC,EAArC,OAAO,QAAA,EAAE,UAAU,QAAkB,CAAC;IACvC,IAAA,KAAoB,IAAA,gBAAQ,EAAgB,IAAI,CAAC,EAAhD,KAAK,QAAA,EAAE,QAAQ,QAAiC,CAAC;IAExD,IAAA,iBAAS,EAAC;;QACR,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,EAAE,CAAC;YACtB,0BAA0B,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEd,IAAM,0BAA0B,GAAG,IAAA,mBAAW,EAAC;;;;;;oBAE3C,UAAU,CAAC,IAAI,CAAC,CAAC;oBACA,qBAAM,KAAK,CAAC,qCAAqC,CAAC,EAAA;;oBAA7D,QAAQ,GAAG,SAAkD;oBAEnE,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACjB,0DAA0D;wBAC1D,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;4BAC5B,OAAO,CAAC;gCACN,SAAS,EAAE,EAAE;gCACb,oBAAoB,EAAE,EAAE;gCACxB,SAAS,EAAE,EAAE;gCACb,oBAAoB,EAAE,4DAA4D;6BACnF,CAAC,CAAC;4BACH,sBAAO;wBACT,CAAC;wBACD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;oBAC5D,CAAC;oBAEc,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAA9B,MAAM,GAAG,SAAqB;oBACpC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wBACnB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACvB,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,2BAA2B,CAAC,CAAC;oBAC/D,CAAC;;;;oBAED,kDAAkD;oBAClD,IAAI,KAAG,YAAY,KAAK,IAAI,CAAC,KAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;wBACzD,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAG,CAAC,CAAC;oBAC/D,CAAC;oBACD,QAAQ,CAAC,KAAG,YAAY,KAAK,CAAC,CAAC,CAAC,KAAG,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC;;;oBAEnE,UAAU,CAAC,KAAK,CAAC,CAAC;;;;;SAErB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEd,IAAM,sBAAsB,GAAG,IAAA,mBAAW,EAAC,UAAO,UAAkB,EAAE,MAAc;;;;;;oBAE/D,qBAAM,KAAK,CAAC,wBAAwB,EAAE;4BACrD,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE;gCACP,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,UAAU,YAAA;gCACV,MAAM,QAAA;6BACP,CAAC;yBACH,CAAC,EAAA;;oBATI,QAAQ,GAAG,SASf;oBAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACjB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;oBACxD,CAAC;;;;oBAED,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,OAAK,CAAC,CAAC;;;;;SAE7D,EAAE,EAAE,CAAC,CAAC;IAEP,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,CACL,iCAAK,SAAS,EAAC,iDAAiD,aAC9D,gCAAI,SAAS,EAAC,oDAAoD,aAChE,uBAAC,yBAAU,IAAC,SAAS,EAAC,uBAAuB,GAAG,uCAE7C,EACL,iCAAK,SAAS,EAAC,kBAAkB,aAC/B,uBAAC,uBAAQ,IAAC,SAAS,EAAC,sCAAsC,GAAG,EAC7D,8BAAG,SAAS,EAAC,uCAAuC,uGAEhD,EACJ,uBAAC,eAAM,IAAC,OAAO,kBACb,uBAAC,cAAI,IAAC,IAAI,EAAC,QAAQ,wBAAe,GAC3B,IACL,IACF,CACP,CAAC;IACJ,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CACL,iCAAK,SAAS,EAAC,iDAAiD,aAC9D,gCAAI,SAAS,EAAC,oDAAoD,aAChE,uBAAC,yBAAU,IAAC,SAAS,EAAC,uBAAuB,GAAG,uCAE7C,EACL,gCAAK,SAAS,EAAC,WAAW,YACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CACpB,iCAAa,SAAS,EAAC,eAAe,aACpC,gCAAK,SAAS,EAAC,qDAAqD,GAAO,EAC3E,gCAAK,SAAS,EAAC,gDAAgD,GAAO,KAF9D,CAAC,CAGL,CACP,EALqB,CAKrB,CAAC,GACE,IACF,CACP,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QACnB,OAAO,CACL,iCAAK,SAAS,EAAC,iDAAiD,aAC9D,gCAAI,SAAS,EAAC,oDAAoD,aAChE,uBAAC,yBAAU,IAAC,SAAS,EAAC,uBAAuB,GAAG,uCAE7C,EACL,iCAAK,SAAS,EAAC,kBAAkB,aAC/B,8BAAG,SAAS,EAAC,qCAAqC,YAC/C,KAAK,IAAI,uCAAuC,GAC/C,EACJ,uBAAC,eAAM,IAAC,OAAO,EAAE,0BAA0B,EAAE,OAAO,EAAC,SAAS,0BAErD,IACL,IACF,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,iCAAK,SAAS,EAAC,iDAAiD,aAC9D,iCAAK,SAAS,EAAC,wCAAwC,aACrD,gCAAI,SAAS,EAAC,+CAA+C,aAC3D,uBAAC,yBAAU,IAAC,SAAS,EAAC,uBAAuB,GAAG,2BAE7C,EACL,uBAAC,eAAM,IAAC,OAAO,QAAC,OAAO,EAAC,SAAS,EAAC,IAAI,EAAC,IAAI,YACzC,uBAAC,cAAI,IAAC,IAAI,EAAC,YAAY,yBAAgB,GAChC,IACL,EAEN,8BAAG,SAAS,EAAC,+CAA+C,YACzD,IAAI,CAAC,oBAAoB,GACxB,EAEJ,gCAAK,SAAS,EAAC,WAAW,YACvB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAC,QAAQ,IAAK,OAAA,CAC1F,iCAEE,SAAS,EAAC,8FAA8F,aAExG,gCAAK,SAAS,EAAC,uCAAuC,YACpD,iCAAK,SAAS,EAAC,QAAQ,aACrB,+BAAI,SAAS,EAAC,mDAAmD,YAC9D,QAAQ,CAAC,KAAK,GACZ,EACL,iCAAK,SAAS,EAAC,8BAA8B,aAC1C,QAAQ,CAAC,UAAU,IAAI,CACtB,iCAAM,SAAS,EAAE,gDACf,QAAQ,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC,CAAC,mEAAmE,CAAC,CAAC;oDAC1G,QAAQ,CAAC,UAAU,KAAK,cAAc,CAAC,CAAC,CAAC,uEAAuE,CAAC,CAAC;wDAClH,2DAA2D,CAC3D,YACC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,GAC7B,CACR,EACA,QAAQ,CAAC,IAAI,KAAK,MAAM,IAAI,CAC3B,iCAAM,SAAS,EAAC,qGAAqG,qBAE9G,CACR,EACA,QAAQ,CAAC,aAAa,GAAG,CAAC,IAAI,CAC7B,iCAAK,SAAS,EAAC,yBAAyB,aACtC,uBAAC,mBAAI,IAAC,SAAS,EAAC,sCAAsC,GAAG,EACzD,iCAAM,SAAS,EAAC,0CAA0C,YACvD,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAC7B,IACH,CACP,IACG,EACL,QAAQ,CAAC,MAAM,IAAI,CAClB,+BAAG,SAAS,EAAC,+CAA+C,oBACtD,QAAQ,CAAC,MAAM,EAClB,QAAQ,CAAC,QAAQ,IAAI,kBAAM,QAAQ,CAAC,QAAQ,CAAE,IAC7C,CACL,IACG,GACF,EAEN,8BAAG,SAAS,EAAC,4DAA4D,YACtE,QAAQ,CAAC,WAAW,GACnB,EAEJ,iCAAK,SAAS,EAAC,YAAY,aACzB,uBAAC,eAAM,IACL,OAAO,QACP,IAAI,EAAC,IAAI,EACT,SAAS,EAAC,QAAQ,YAElB,+BACE,IAAI,EAAE,QAAQ,CAAC,GAAG,EAClB,MAAM,EAAC,QAAQ,EACf,GAAG,EAAC,qBAAqB,EACzB,SAAS,EAAC,wCAAwC,aAElD,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,WAE5B,uBAAC,2BAAY,IAAC,SAAS,EAAC,SAAS,GAAG,IAClC,GACG,EAET,uBAAC,eAAM,IACL,OAAO,EAAC,SAAS,EACjB,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,cAAM,OAAA,sBAAsB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAjD,CAAiD,EAChE,SAAS,EAAC,MAAM,YAEhB,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,GACzB,IACL,KAvED,QAAQ,CAAC,EAAE,CAwEZ,CACP,EA3E2F,CA2E3F,CAAC,CAAC,CAAC,CAAC,CACH,iCAAK,SAAS,EAAC,kBAAkB,aAC/B,uBAAC,uBAAQ,IAAC,SAAS,EAAC,sCAAsC,GAAG,EAC7D,8BAAG,SAAS,EAAC,uCAAuC,qHAEhD,EACJ,uBAAC,eAAM,IAAC,OAAO,kBACb,uBAAC,cAAI,IAAC,IAAI,EAAC,aAAa,gCAAuB,GACxC,IACL,CACP,GACG,EAEL,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,IAAI,CACpE,iCAAK,SAAS,EAAC,yDAAyD,aACtE,8BAAG,SAAS,EAAC,+CAA+C,2CAExD,EACJ,gCAAK,SAAS,EAAC,sBAAsB,YAClC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,CACvC,uBAAC,cAAI,IAEH,IAAI,EAAE,wBAAiB,IAAI,CAAC,EAAE,CAAE,EAChC,SAAS,EAAC,yJAAyJ,YAElK,IAAI,CAAC,IAAI,IAJL,IAAI,CAAC,EAAE,CAKP,CACR,EARwC,CAQxC,CAAC,GACE,IACF,CACP,EAED,gCAAK,SAAS,EAAC,kBAAkB,YAC/B,uBAAC,eAAM,IAAC,OAAO,QAAC,OAAO,EAAC,SAAS,EAAC,IAAI,EAAC,IAAI,YACzC,uBAAC,cAAI,IAAC,IAAI,EAAC,aAAa,mCAEjB,GACA,GACL,IACF,CACP,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/dashboard/PersonalizedResources.tsx"],
      sourcesContent: ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport Link from 'next/link';\nimport { useSession } from 'next-auth/react';\nimport { BookOpen, Star, ExternalLink, Play, Bookmark, Clock, User, TrendingUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\ninterface LearningResource {\n  id: string;\n  title: string;\n  description: string;\n  url: string;\n  type: string;\n  category: string;\n  skillLevel: string;\n  author?: string;\n  duration?: string;\n  cost: string;\n  averageRating: number;\n  totalRatings: number;\n  careerPaths: {\n    id: string;\n    name: string;\n    slug: string;\n  }[];\n}\n\ninterface PersonalizedResourcesData {\n  resources: LearningResource[];\n  suggestedCareerPaths: {\n    id: string;\n    name: string;\n    slug: string;\n  }[];\n  interests: string[];\n  recommendationReason: string;\n}\n\nexport default function PersonalizedResources() {\n  const { data: session } = useSession();\n  const [data, setData] = useState<PersonalizedResourcesData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (session?.user?.id) {\n      fetchPersonalizedResources();\n    }\n  }, [session]);\n\n  const fetchPersonalizedResources = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/personalized-resources?limit=6');\n\n      if (!response.ok) {\n        // Handle 404 gracefully for new users without assessments\n        if (response.status === 404) {\n          setData({\n            resources: [],\n            suggestedCareerPaths: [],\n            interests: [],\n            recommendationReason: 'Complete an assessment to get personalized recommendations'\n          });\n          return;\n        }\n        throw new Error('Failed to fetch personalized resources');\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        setData(result.data);\n      } else {\n        throw new Error(result.error || 'Failed to fetch resources');\n      }\n    } catch (err) {\n      // Only log non-404 errors to reduce console noise\n      if (err instanceof Error && !err.message.includes('404')) {\n        console.error('Error fetching personalized resources:', err);\n      }\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  }, [session]);\n\n  const handleResourceProgress = useCallback(async (resourceId: string, status: string) => {\n    try {\n      const response = await fetch('/api/learning-progress', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          resourceId,\n          status\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to update resource progress');\n      }\n    } catch (error) {\n      console.error('Error updating resource progress:', error);\n    }\n  }, []);\n\n  if (!session) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h2 className=\"text-xl font-semibold mb-4 flex items-center gap-2\">\n          <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n          Personalized Learning Resources\n        </h2>\n        <div className=\"text-center py-8\">\n          <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            Sign in to get personalized learning recommendations based on your assessment results.\n          </p>\n          <Button asChild>\n            <Link href=\"/login\">Sign In</Link>\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h2 className=\"text-xl font-semibold mb-4 flex items-center gap-2\">\n          <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n          Personalized Learning Resources\n        </h2>\n        <div className=\"space-y-4\">\n          {[1, 2, 3].map((i) => (\n            <div key={i} className=\"animate-pulse\">\n              <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2\"></div>\n              <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !data) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h2 className=\"text-xl font-semibold mb-4 flex items-center gap-2\">\n          <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n          Personalized Learning Resources\n        </h2>\n        <div className=\"text-center py-8\">\n          <p className=\"text-red-600 dark:text-red-400 mb-4\">\n            {error || 'Failed to load personalized resources'}\n          </p>\n          <Button onClick={fetchPersonalizedResources} variant=\"outline\">\n            Try Again\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-xl font-semibold flex items-center gap-2\">\n          <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n          Recommended for You\n        </h2>\n        <Button asChild variant=\"outline\" size=\"sm\">\n          <Link href=\"/resources\">View All</Link>\n        </Button>\n      </div>\n\n      <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\n        {data.recommendationReason}\n      </p>\n\n      <div className=\"space-y-4\">\n        {data.resources && data.resources.length > 0 ? data.resources.slice(0, 3).map((resource) => (\n          <div\n            key={resource.id}\n            className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow\"\n          >\n            <div className=\"flex items-start justify-between mb-2\">\n              <div className=\"flex-1\">\n                <h3 className=\"font-medium text-gray-900 dark:text-gray-100 mb-1\">\n                  {resource.title}\n                </h3>\n                <div className=\"flex items-center gap-2 mb-2\">\n                  {resource.skillLevel && (\n                    <span className={`px-2 py-1 rounded text-xs font-medium ${\n                      resource.skillLevel === 'BEGINNER' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                      resource.skillLevel === 'INTERMEDIATE' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :\n                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n                    }`}>\n                      {resource.skillLevel.toLowerCase()}\n                    </span>\n                  )}\n                  {resource.cost === 'FREE' && (\n                    <span className=\"px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs font-medium\">\n                      Free\n                    </span>\n                  )}\n                  {resource.averageRating > 0 && (\n                    <div className=\"flex items-center gap-1\">\n                      <Star className=\"h-3 w-3 text-yellow-500 fill-current\" />\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        {resource.averageRating.toFixed(1)}\n                      </span>\n                    </div>\n                  )}\n                </div>\n                {resource.author && (\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">\n                    by {resource.author}\n                    {resource.duration && ` \u2022 ${resource.duration}`}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-3 line-clamp-2\">\n              {resource.description}\n            </p>\n\n            <div className=\"flex gap-2\">\n              <Button\n                asChild\n                size=\"sm\"\n                className=\"flex-1\"\n              >\n                <a\n                  href={resource.url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center justify-center gap-2\"\n                >\n                  <Play className=\"h-3 w-3\" />\n                  Start\n                  <ExternalLink className=\"h-3 w-3\" />\n                </a>\n              </Button>\n              \n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleResourceProgress(resource.id, 'BOOKMARKED')}\n                className=\"px-3\"\n              >\n                <Bookmark className=\"h-3 w-3\" />\n              </Button>\n            </div>\n          </div>\n        )) : (\n          <div className=\"text-center py-8\">\n            <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              No personalized resources available yet. Complete an assessment to get personalized recommendations.\n            </p>\n            <Button asChild>\n              <Link href=\"/assessment\">Take Assessment</Link>\n            </Button>\n          </div>\n        )}\n      </div>\n\n      {data.suggestedCareerPaths && data.suggestedCareerPaths.length > 0 && (\n        <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n            Based on your interest in:\n          </p>\n          <div className=\"flex flex-wrap gap-2\">\n            {data.suggestedCareerPaths.map((path) => (\n              <Link\n                key={path.id}\n                href={`/career-paths/${path.id}`}\n                className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-xs hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors\"\n              >\n                {path.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n\n      <div className=\"mt-4 text-center\">\n        <Button asChild variant=\"outline\" size=\"sm\">\n          <Link href=\"/assessment\">\n            Update Preferences\n          </Link>\n        </Button>\n      </div>\n    </div>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "092215ecec5c3f1b2d6d8f9b0ba23a4911fa3a67"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_285cpx7d66 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_285cpx7d66();
var __createBinding =
/* istanbul ignore next */
(cov_285cpx7d66().s[0]++,
/* istanbul ignore next */
(cov_285cpx7d66().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_285cpx7d66().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_285cpx7d66().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_285cpx7d66().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_285cpx7d66().f[0]++;
  cov_285cpx7d66().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_285cpx7d66().b[2][0]++;
    cov_285cpx7d66().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_285cpx7d66().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_285cpx7d66().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_285cpx7d66().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_285cpx7d66().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_285cpx7d66().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_285cpx7d66().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_285cpx7d66().b[5][1]++,
  /* istanbul ignore next */
  (cov_285cpx7d66().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_285cpx7d66().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_285cpx7d66().b[3][0]++;
    cov_285cpx7d66().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_285cpx7d66().f[1]++;
        cov_285cpx7d66().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_285cpx7d66().b[3][1]++;
  }
  cov_285cpx7d66().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_285cpx7d66().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_285cpx7d66().f[2]++;
  cov_285cpx7d66().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_285cpx7d66().b[7][0]++;
    cov_285cpx7d66().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_285cpx7d66().b[7][1]++;
  }
  cov_285cpx7d66().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_285cpx7d66().s[11]++,
/* istanbul ignore next */
(cov_285cpx7d66().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_285cpx7d66().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_285cpx7d66().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_285cpx7d66().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_285cpx7d66().f[3]++;
  cov_285cpx7d66().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_285cpx7d66().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_285cpx7d66().f[4]++;
  cov_285cpx7d66().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_285cpx7d66().s[14]++,
/* istanbul ignore next */
(cov_285cpx7d66().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_285cpx7d66().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_285cpx7d66().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_285cpx7d66().f[5]++;
  cov_285cpx7d66().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_285cpx7d66().f[6]++;
    cov_285cpx7d66().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_285cpx7d66().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_285cpx7d66().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_285cpx7d66().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_285cpx7d66().s[17]++, []);
      /* istanbul ignore next */
      cov_285cpx7d66().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_285cpx7d66().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_285cpx7d66().b[12][0]++;
          cov_285cpx7d66().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_285cpx7d66().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_285cpx7d66().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_285cpx7d66().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_285cpx7d66().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_285cpx7d66().f[8]++;
    cov_285cpx7d66().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_285cpx7d66().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_285cpx7d66().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_285cpx7d66().b[13][0]++;
      cov_285cpx7d66().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_285cpx7d66().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[26]++, {});
    /* istanbul ignore next */
    cov_285cpx7d66().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_285cpx7d66().b[15][0]++;
      cov_285cpx7d66().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_285cpx7d66().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_285cpx7d66().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_285cpx7d66().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_285cpx7d66().b[16][0]++;
          cov_285cpx7d66().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_285cpx7d66().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_285cpx7d66().b[15][1]++;
    }
    cov_285cpx7d66().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_285cpx7d66().s[34]++;
    return result;
  };
}()));
var __awaiter =
/* istanbul ignore next */
(cov_285cpx7d66().s[35]++,
/* istanbul ignore next */
(cov_285cpx7d66().b[17][0]++, this) &&
/* istanbul ignore next */
(cov_285cpx7d66().b[17][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_285cpx7d66().b[17][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_285cpx7d66().f[9]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_285cpx7d66().f[10]++;
    cov_285cpx7d66().s[36]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_285cpx7d66().b[18][0]++, value) :
    /* istanbul ignore next */
    (cov_285cpx7d66().b[18][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_285cpx7d66().f[11]++;
      cov_285cpx7d66().s[37]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_285cpx7d66().s[38]++;
  return new (
  /* istanbul ignore next */
  (cov_285cpx7d66().b[19][0]++, P) ||
  /* istanbul ignore next */
  (cov_285cpx7d66().b[19][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_285cpx7d66().f[12]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_285cpx7d66().f[13]++;
      cov_285cpx7d66().s[39]++;
      try {
        /* istanbul ignore next */
        cov_285cpx7d66().s[40]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_285cpx7d66().s[41]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_285cpx7d66().f[14]++;
      cov_285cpx7d66().s[42]++;
      try {
        /* istanbul ignore next */
        cov_285cpx7d66().s[43]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_285cpx7d66().s[44]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_285cpx7d66().f[15]++;
      cov_285cpx7d66().s[45]++;
      result.done ?
      /* istanbul ignore next */
      (cov_285cpx7d66().b[20][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_285cpx7d66().b[20][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_285cpx7d66().s[46]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_285cpx7d66().b[21][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_285cpx7d66().b[21][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_285cpx7d66().s[47]++,
/* istanbul ignore next */
(cov_285cpx7d66().b[22][0]++, this) &&
/* istanbul ignore next */
(cov_285cpx7d66().b[22][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_285cpx7d66().b[22][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_285cpx7d66().f[16]++;
  var _ =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[48]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_285cpx7d66().f[17]++;
        cov_285cpx7d66().s[49]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_285cpx7d66().b[23][0]++;
          cov_285cpx7d66().s[50]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_285cpx7d66().b[23][1]++;
        }
        cov_285cpx7d66().s[51]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[52]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_285cpx7d66().b[24][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_285cpx7d66().b[24][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_285cpx7d66().s[53]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_285cpx7d66().b[25][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_285cpx7d66().b[25][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_285cpx7d66().f[18]++;
    cov_285cpx7d66().s[54]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_285cpx7d66().f[19]++;
    cov_285cpx7d66().s[55]++;
    return function (v) {
      /* istanbul ignore next */
      cov_285cpx7d66().f[20]++;
      cov_285cpx7d66().s[56]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_285cpx7d66().f[21]++;
    cov_285cpx7d66().s[57]++;
    if (f) {
      /* istanbul ignore next */
      cov_285cpx7d66().b[26][0]++;
      cov_285cpx7d66().s[58]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_285cpx7d66().b[26][1]++;
    }
    cov_285cpx7d66().s[59]++;
    while (
    /* istanbul ignore next */
    (cov_285cpx7d66().b[27][0]++, g) &&
    /* istanbul ignore next */
    (cov_285cpx7d66().b[27][1]++, g = 0,
    /* istanbul ignore next */
    (cov_285cpx7d66().b[28][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_285cpx7d66().b[28][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_285cpx7d66().s[60]++;
      try {
        /* istanbul ignore next */
        cov_285cpx7d66().s[61]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_285cpx7d66().b[30][0]++, y) &&
        /* istanbul ignore next */
        (cov_285cpx7d66().b[30][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_285cpx7d66().b[31][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_285cpx7d66().b[31][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_285cpx7d66().b[32][0]++,
        /* istanbul ignore next */
        (cov_285cpx7d66().b[33][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_285cpx7d66().b[33][1]++,
        /* istanbul ignore next */
        (cov_285cpx7d66().b[34][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_285cpx7d66().b[34][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_285cpx7d66().b[32][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_285cpx7d66().b[30][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_285cpx7d66().b[29][0]++;
          cov_285cpx7d66().s[62]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_285cpx7d66().b[29][1]++;
        }
        cov_285cpx7d66().s[63]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_285cpx7d66().b[35][0]++;
          cov_285cpx7d66().s[64]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_285cpx7d66().b[35][1]++;
        }
        cov_285cpx7d66().s[65]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_285cpx7d66().b[36][0]++;
          case 1:
            /* istanbul ignore next */
            cov_285cpx7d66().b[36][1]++;
            cov_285cpx7d66().s[66]++;
            t = op;
            /* istanbul ignore next */
            cov_285cpx7d66().s[67]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_285cpx7d66().b[36][2]++;
            cov_285cpx7d66().s[68]++;
            _.label++;
            /* istanbul ignore next */
            cov_285cpx7d66().s[69]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_285cpx7d66().b[36][3]++;
            cov_285cpx7d66().s[70]++;
            _.label++;
            /* istanbul ignore next */
            cov_285cpx7d66().s[71]++;
            y = op[1];
            /* istanbul ignore next */
            cov_285cpx7d66().s[72]++;
            op = [0];
            /* istanbul ignore next */
            cov_285cpx7d66().s[73]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_285cpx7d66().b[36][4]++;
            cov_285cpx7d66().s[74]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_285cpx7d66().s[75]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_285cpx7d66().s[76]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_285cpx7d66().b[36][5]++;
            cov_285cpx7d66().s[77]++;
            if (
            /* istanbul ignore next */
            (cov_285cpx7d66().b[38][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_285cpx7d66().b[39][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_285cpx7d66().b[39][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_285cpx7d66().b[38][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_285cpx7d66().b[38][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[37][0]++;
              cov_285cpx7d66().s[78]++;
              _ = 0;
              /* istanbul ignore next */
              cov_285cpx7d66().s[79]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[37][1]++;
            }
            cov_285cpx7d66().s[80]++;
            if (
            /* istanbul ignore next */
            (cov_285cpx7d66().b[41][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_285cpx7d66().b[41][1]++, !t) ||
            /* istanbul ignore next */
            (cov_285cpx7d66().b[41][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_285cpx7d66().b[41][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[40][0]++;
              cov_285cpx7d66().s[81]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_285cpx7d66().s[82]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[40][1]++;
            }
            cov_285cpx7d66().s[83]++;
            if (
            /* istanbul ignore next */
            (cov_285cpx7d66().b[43][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_285cpx7d66().b[43][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[42][0]++;
              cov_285cpx7d66().s[84]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_285cpx7d66().s[85]++;
              t = op;
              /* istanbul ignore next */
              cov_285cpx7d66().s[86]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[42][1]++;
            }
            cov_285cpx7d66().s[87]++;
            if (
            /* istanbul ignore next */
            (cov_285cpx7d66().b[45][0]++, t) &&
            /* istanbul ignore next */
            (cov_285cpx7d66().b[45][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[44][0]++;
              cov_285cpx7d66().s[88]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_285cpx7d66().s[89]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_285cpx7d66().s[90]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[44][1]++;
            }
            cov_285cpx7d66().s[91]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[46][0]++;
              cov_285cpx7d66().s[92]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[46][1]++;
            }
            cov_285cpx7d66().s[93]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_285cpx7d66().s[94]++;
            continue;
        }
        /* istanbul ignore next */
        cov_285cpx7d66().s[95]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_285cpx7d66().s[96]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_285cpx7d66().s[97]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_285cpx7d66().s[98]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_285cpx7d66().s[99]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_285cpx7d66().b[47][0]++;
      cov_285cpx7d66().s[100]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_285cpx7d66().b[47][1]++;
    }
    cov_285cpx7d66().s[101]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_285cpx7d66().b[48][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_285cpx7d66().b[48][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_285cpx7d66().s[102]++,
/* istanbul ignore next */
(cov_285cpx7d66().b[49][0]++, this) &&
/* istanbul ignore next */
(cov_285cpx7d66().b[49][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_285cpx7d66().b[49][2]++, function (mod) {
  /* istanbul ignore next */
  cov_285cpx7d66().f[22]++;
  cov_285cpx7d66().s[103]++;
  return /* istanbul ignore next */(cov_285cpx7d66().b[51][0]++, mod) &&
  /* istanbul ignore next */
  (cov_285cpx7d66().b[51][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_285cpx7d66().b[50][0]++, mod) :
  /* istanbul ignore next */
  (cov_285cpx7d66().b[50][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_285cpx7d66().s[104]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_285cpx7d66().s[105]++;
exports.default = PersonalizedResources;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_285cpx7d66().s[106]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_285cpx7d66().s[107]++, __importStar(require("react")));
var link_1 =
/* istanbul ignore next */
(cov_285cpx7d66().s[108]++, __importDefault(require("next/link")));
var react_2 =
/* istanbul ignore next */
(cov_285cpx7d66().s[109]++, require("next-auth/react"));
var lucide_react_1 =
/* istanbul ignore next */
(cov_285cpx7d66().s[110]++, require("lucide-react"));
var button_1 =
/* istanbul ignore next */
(cov_285cpx7d66().s[111]++, require("@/components/ui/button"));
function PersonalizedResources() {
  /* istanbul ignore next */
  cov_285cpx7d66().f[23]++;
  var _this =
  /* istanbul ignore next */
  (cov_285cpx7d66().s[112]++, this);
  var session =
  /* istanbul ignore next */
  (cov_285cpx7d66().s[113]++, (0, react_2.useSession)().data);
  var _a =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[114]++, (0, react_1.useState)(null)),
    data =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[115]++, _a[0]),
    setData =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[116]++, _a[1]);
  var _b =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[117]++, (0, react_1.useState)(true)),
    loading =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[118]++, _b[0]),
    setLoading =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[119]++, _b[1]);
  var _c =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[120]++, (0, react_1.useState)(null)),
    error =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[121]++, _c[0]),
    setError =
    /* istanbul ignore next */
    (cov_285cpx7d66().s[122]++, _c[1]);
  /* istanbul ignore next */
  cov_285cpx7d66().s[123]++;
  (0, react_1.useEffect)(function () {
    /* istanbul ignore next */
    cov_285cpx7d66().f[24]++;
    var _a;
    /* istanbul ignore next */
    cov_285cpx7d66().s[124]++;
    if (
    /* istanbul ignore next */
    (cov_285cpx7d66().b[54][0]++, (_a =
    /* istanbul ignore next */
    (cov_285cpx7d66().b[56][0]++, session === null) ||
    /* istanbul ignore next */
    (cov_285cpx7d66().b[56][1]++, session === void 0) ?
    /* istanbul ignore next */
    (cov_285cpx7d66().b[55][0]++, void 0) :
    /* istanbul ignore next */
    (cov_285cpx7d66().b[55][1]++, session.user)) === null) ||
    /* istanbul ignore next */
    (cov_285cpx7d66().b[54][1]++, _a === void 0) ?
    /* istanbul ignore next */
    (cov_285cpx7d66().b[53][0]++, void 0) :
    /* istanbul ignore next */
    (cov_285cpx7d66().b[53][1]++, _a.id)) {
      /* istanbul ignore next */
      cov_285cpx7d66().b[52][0]++;
      cov_285cpx7d66().s[125]++;
      fetchPersonalizedResources();
    } else
    /* istanbul ignore next */
    {
      cov_285cpx7d66().b[52][1]++;
    }
  }, [session]);
  var fetchPersonalizedResources =
  /* istanbul ignore next */
  (cov_285cpx7d66().s[126]++, (0, react_1.useCallback)(function () {
    /* istanbul ignore next */
    cov_285cpx7d66().f[25]++;
    cov_285cpx7d66().s[127]++;
    return __awaiter(_this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_285cpx7d66().f[26]++;
      var response, result, err_1;
      /* istanbul ignore next */
      cov_285cpx7d66().s[128]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_285cpx7d66().f[27]++;
        cov_285cpx7d66().s[129]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_285cpx7d66().b[57][0]++;
            cov_285cpx7d66().s[130]++;
            _a.trys.push([0, 3, 4, 5]);
            /* istanbul ignore next */
            cov_285cpx7d66().s[131]++;
            setLoading(true);
            /* istanbul ignore next */
            cov_285cpx7d66().s[132]++;
            return [4 /*yield*/, fetch('/api/personalized-resources?limit=6')];
          case 1:
            /* istanbul ignore next */
            cov_285cpx7d66().b[57][1]++;
            cov_285cpx7d66().s[133]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_285cpx7d66().s[134]++;
            if (!response.ok) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[58][0]++;
              cov_285cpx7d66().s[135]++;
              // Handle 404 gracefully for new users without assessments
              if (response.status === 404) {
                /* istanbul ignore next */
                cov_285cpx7d66().b[59][0]++;
                cov_285cpx7d66().s[136]++;
                setData({
                  resources: [],
                  suggestedCareerPaths: [],
                  interests: [],
                  recommendationReason: 'Complete an assessment to get personalized recommendations'
                });
                /* istanbul ignore next */
                cov_285cpx7d66().s[137]++;
                return [2 /*return*/];
              } else
              /* istanbul ignore next */
              {
                cov_285cpx7d66().b[59][1]++;
              }
              cov_285cpx7d66().s[138]++;
              throw new Error('Failed to fetch personalized resources');
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[58][1]++;
            }
            cov_285cpx7d66().s[139]++;
            return [4 /*yield*/, response.json()];
          case 2:
            /* istanbul ignore next */
            cov_285cpx7d66().b[57][2]++;
            cov_285cpx7d66().s[140]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_285cpx7d66().s[141]++;
            if (result.success) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[60][0]++;
              cov_285cpx7d66().s[142]++;
              setData(result.data);
            } else {
              /* istanbul ignore next */
              cov_285cpx7d66().b[60][1]++;
              cov_285cpx7d66().s[143]++;
              throw new Error(
              /* istanbul ignore next */
              (cov_285cpx7d66().b[61][0]++, result.error) ||
              /* istanbul ignore next */
              (cov_285cpx7d66().b[61][1]++, 'Failed to fetch resources'));
            }
            /* istanbul ignore next */
            cov_285cpx7d66().s[144]++;
            return [3 /*break*/, 5];
          case 3:
            /* istanbul ignore next */
            cov_285cpx7d66().b[57][3]++;
            cov_285cpx7d66().s[145]++;
            err_1 = _a.sent();
            // Only log non-404 errors to reduce console noise
            /* istanbul ignore next */
            cov_285cpx7d66().s[146]++;
            if (
            /* istanbul ignore next */
            (cov_285cpx7d66().b[63][0]++, err_1 instanceof Error) &&
            /* istanbul ignore next */
            (cov_285cpx7d66().b[63][1]++, !err_1.message.includes('404'))) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[62][0]++;
              cov_285cpx7d66().s[147]++;
              console.error('Error fetching personalized resources:', err_1);
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[62][1]++;
            }
            cov_285cpx7d66().s[148]++;
            setError(err_1 instanceof Error ?
            /* istanbul ignore next */
            (cov_285cpx7d66().b[64][0]++, err_1.message) :
            /* istanbul ignore next */
            (cov_285cpx7d66().b[64][1]++, 'An error occurred'));
            /* istanbul ignore next */
            cov_285cpx7d66().s[149]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_285cpx7d66().b[57][4]++;
            cov_285cpx7d66().s[150]++;
            setLoading(false);
            /* istanbul ignore next */
            cov_285cpx7d66().s[151]++;
            return [7 /*endfinally*/];
          case 5:
            /* istanbul ignore next */
            cov_285cpx7d66().b[57][5]++;
            cov_285cpx7d66().s[152]++;
            return [2 /*return*/];
        }
      });
    });
  }, [session]));
  var handleResourceProgress =
  /* istanbul ignore next */
  (cov_285cpx7d66().s[153]++, (0, react_1.useCallback)(function (resourceId, status) {
    /* istanbul ignore next */
    cov_285cpx7d66().f[28]++;
    cov_285cpx7d66().s[154]++;
    return __awaiter(_this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_285cpx7d66().f[29]++;
      var response, error_1;
      /* istanbul ignore next */
      cov_285cpx7d66().s[155]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_285cpx7d66().f[30]++;
        cov_285cpx7d66().s[156]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_285cpx7d66().b[65][0]++;
            cov_285cpx7d66().s[157]++;
            _a.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_285cpx7d66().s[158]++;
            return [4 /*yield*/, fetch('/api/learning-progress', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                resourceId: resourceId,
                status: status
              })
            })];
          case 1:
            /* istanbul ignore next */
            cov_285cpx7d66().b[65][1]++;
            cov_285cpx7d66().s[159]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_285cpx7d66().s[160]++;
            if (!response.ok) {
              /* istanbul ignore next */
              cov_285cpx7d66().b[66][0]++;
              cov_285cpx7d66().s[161]++;
              throw new Error('Failed to update resource progress');
            } else
            /* istanbul ignore next */
            {
              cov_285cpx7d66().b[66][1]++;
            }
            cov_285cpx7d66().s[162]++;
            return [3 /*break*/, 3];
          case 2:
            /* istanbul ignore next */
            cov_285cpx7d66().b[65][2]++;
            cov_285cpx7d66().s[163]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_285cpx7d66().s[164]++;
            console.error('Error updating resource progress:', error_1);
            /* istanbul ignore next */
            cov_285cpx7d66().s[165]++;
            return [3 /*break*/, 3];
          case 3:
            /* istanbul ignore next */
            cov_285cpx7d66().b[65][3]++;
            cov_285cpx7d66().s[166]++;
            return [2 /*return*/];
        }
      });
    });
  }, []));
  /* istanbul ignore next */
  cov_285cpx7d66().s[167]++;
  if (!session) {
    /* istanbul ignore next */
    cov_285cpx7d66().b[67][0]++;
    cov_285cpx7d66().s[168]++;
    return (0, jsx_runtime_1.jsxs)("div", {
      className: "bg-white dark:bg-gray-800 rounded-lg shadow p-6",
      children: [(0, jsx_runtime_1.jsxs)("h2", {
        className: "text-xl font-semibold mb-4 flex items-center gap-2",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, {
          className: "h-5 w-5 text-blue-600"
        }), "Personalized Learning Resources"]
      }), (0, jsx_runtime_1.jsxs)("div", {
        className: "text-center py-8",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.BookOpen, {
          className: "h-12 w-12 text-gray-400 mx-auto mb-4"
        }), (0, jsx_runtime_1.jsx)("p", {
          className: "text-gray-600 dark:text-gray-400 mb-4",
          children: "Sign in to get personalized learning recommendations based on your assessment results."
        }), (0, jsx_runtime_1.jsx)(button_1.Button, {
          asChild: true,
          children: (0, jsx_runtime_1.jsx)(link_1.default, {
            href: "/login",
            children: "Sign In"
          })
        })]
      })]
    });
  } else
  /* istanbul ignore next */
  {
    cov_285cpx7d66().b[67][1]++;
  }
  cov_285cpx7d66().s[169]++;
  if (loading) {
    /* istanbul ignore next */
    cov_285cpx7d66().b[68][0]++;
    cov_285cpx7d66().s[170]++;
    return (0, jsx_runtime_1.jsxs)("div", {
      className: "bg-white dark:bg-gray-800 rounded-lg shadow p-6",
      children: [(0, jsx_runtime_1.jsxs)("h2", {
        className: "text-xl font-semibold mb-4 flex items-center gap-2",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, {
          className: "h-5 w-5 text-blue-600"
        }), "Personalized Learning Resources"]
      }), (0, jsx_runtime_1.jsx)("div", {
        className: "space-y-4",
        children: [1, 2, 3].map(function (i) {
          /* istanbul ignore next */
          cov_285cpx7d66().f[31]++;
          cov_285cpx7d66().s[171]++;
          return (0, jsx_runtime_1.jsxs)("div", {
            className: "animate-pulse",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"
            })]
          }, i);
        })
      })]
    });
  } else
  /* istanbul ignore next */
  {
    cov_285cpx7d66().b[68][1]++;
  }
  cov_285cpx7d66().s[172]++;
  if (
  /* istanbul ignore next */
  (cov_285cpx7d66().b[70][0]++, error) ||
  /* istanbul ignore next */
  (cov_285cpx7d66().b[70][1]++, !data)) {
    /* istanbul ignore next */
    cov_285cpx7d66().b[69][0]++;
    cov_285cpx7d66().s[173]++;
    return (0, jsx_runtime_1.jsxs)("div", {
      className: "bg-white dark:bg-gray-800 rounded-lg shadow p-6",
      children: [(0, jsx_runtime_1.jsxs)("h2", {
        className: "text-xl font-semibold mb-4 flex items-center gap-2",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, {
          className: "h-5 w-5 text-blue-600"
        }), "Personalized Learning Resources"]
      }), (0, jsx_runtime_1.jsxs)("div", {
        className: "text-center py-8",
        children: [(0, jsx_runtime_1.jsx)("p", {
          className: "text-red-600 dark:text-red-400 mb-4",
          children:
          /* istanbul ignore next */
          (cov_285cpx7d66().b[71][0]++, error) ||
          /* istanbul ignore next */
          (cov_285cpx7d66().b[71][1]++, 'Failed to load personalized resources')
        }), (0, jsx_runtime_1.jsx)(button_1.Button, {
          onClick: fetchPersonalizedResources,
          variant: "outline",
          children: "Try Again"
        })]
      })]
    });
  } else
  /* istanbul ignore next */
  {
    cov_285cpx7d66().b[69][1]++;
  }
  cov_285cpx7d66().s[174]++;
  return (0, jsx_runtime_1.jsxs)("div", {
    className: "bg-white dark:bg-gray-800 rounded-lg shadow p-6",
    children: [(0, jsx_runtime_1.jsxs)("div", {
      className: "flex items-center justify-between mb-4",
      children: [(0, jsx_runtime_1.jsxs)("h2", {
        className: "text-xl font-semibold flex items-center gap-2",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, {
          className: "h-5 w-5 text-blue-600"
        }), "Recommended for You"]
      }), (0, jsx_runtime_1.jsx)(button_1.Button, {
        asChild: true,
        variant: "outline",
        size: "sm",
        children: (0, jsx_runtime_1.jsx)(link_1.default, {
          href: "/resources",
          children: "View All"
        })
      })]
    }), (0, jsx_runtime_1.jsx)("p", {
      className: "text-sm text-gray-600 dark:text-gray-400 mb-6",
      children: data.recommendationReason
    }), (0, jsx_runtime_1.jsx)("div", {
      className: "space-y-4",
      children:
      /* istanbul ignore next */
      (cov_285cpx7d66().b[73][0]++, data.resources) &&
      /* istanbul ignore next */
      (cov_285cpx7d66().b[73][1]++, data.resources.length > 0) ?
      /* istanbul ignore next */
      (cov_285cpx7d66().b[72][0]++, data.resources.slice(0, 3).map(function (resource) {
        /* istanbul ignore next */
        cov_285cpx7d66().f[32]++;
        cov_285cpx7d66().s[175]++;
        return (0, jsx_runtime_1.jsxs)("div", {
          className: "border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "flex items-start justify-between mb-2",
            children: (0, jsx_runtime_1.jsxs)("div", {
              className: "flex-1",
              children: [(0, jsx_runtime_1.jsx)("h3", {
                className: "font-medium text-gray-900 dark:text-gray-100 mb-1",
                children: resource.title
              }), (0, jsx_runtime_1.jsxs)("div", {
                className: "flex items-center gap-2 mb-2",
                children: [
                /* istanbul ignore next */
                (cov_285cpx7d66().b[74][0]++, resource.skillLevel) &&
                /* istanbul ignore next */
                (cov_285cpx7d66().b[74][1]++, (0, jsx_runtime_1.jsx)("span", {
                  className: "px-2 py-1 rounded text-xs font-medium ".concat(resource.skillLevel === 'BEGINNER' ?
                  /* istanbul ignore next */
                  (cov_285cpx7d66().b[75][0]++, 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200') :
                  /* istanbul ignore next */
                  (cov_285cpx7d66().b[75][1]++, resource.skillLevel === 'INTERMEDIATE' ?
                  /* istanbul ignore next */
                  (cov_285cpx7d66().b[76][0]++, 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200') :
                  /* istanbul ignore next */
                  (cov_285cpx7d66().b[76][1]++, 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'))),
                  children: resource.skillLevel.toLowerCase()
                })),
                /* istanbul ignore next */
                (cov_285cpx7d66().b[77][0]++, resource.cost === 'FREE') &&
                /* istanbul ignore next */
                (cov_285cpx7d66().b[77][1]++, (0, jsx_runtime_1.jsx)("span", {
                  className: "px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs font-medium",
                  children: "Free"
                })),
                /* istanbul ignore next */
                (cov_285cpx7d66().b[78][0]++, resource.averageRating > 0) &&
                /* istanbul ignore next */
                (cov_285cpx7d66().b[78][1]++, (0, jsx_runtime_1.jsxs)("div", {
                  className: "flex items-center gap-1",
                  children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Star, {
                    className: "h-3 w-3 text-yellow-500 fill-current"
                  }), (0, jsx_runtime_1.jsx)("span", {
                    className: "text-xs text-gray-600 dark:text-gray-400",
                    children: resource.averageRating.toFixed(1)
                  })]
                }))]
              }),
              /* istanbul ignore next */
              (cov_285cpx7d66().b[79][0]++, resource.author) &&
              /* istanbul ignore next */
              (cov_285cpx7d66().b[79][1]++, (0, jsx_runtime_1.jsxs)("p", {
                className: "text-xs text-gray-500 dark:text-gray-400 mb-2",
                children: ["by ", resource.author,
                /* istanbul ignore next */
                (cov_285cpx7d66().b[80][0]++, resource.duration) &&
                /* istanbul ignore next */
                (cov_285cpx7d66().b[80][1]++, " \u2022 ".concat(resource.duration))]
              }))]
            })
          }), (0, jsx_runtime_1.jsx)("p", {
            className: "text-sm text-gray-700 dark:text-gray-300 mb-3 line-clamp-2",
            children: resource.description
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "flex gap-2",
            children: [(0, jsx_runtime_1.jsx)(button_1.Button, {
              asChild: true,
              size: "sm",
              className: "flex-1",
              children: (0, jsx_runtime_1.jsxs)("a", {
                href: resource.url,
                target: "_blank",
                rel: "noopener noreferrer",
                className: "flex items-center justify-center gap-2",
                children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Play, {
                  className: "h-3 w-3"
                }), "Start", (0, jsx_runtime_1.jsx)(lucide_react_1.ExternalLink, {
                  className: "h-3 w-3"
                })]
              })
            }), (0, jsx_runtime_1.jsx)(button_1.Button, {
              variant: "outline",
              size: "sm",
              onClick: function () {
                /* istanbul ignore next */
                cov_285cpx7d66().f[33]++;
                cov_285cpx7d66().s[176]++;
                return handleResourceProgress(resource.id, 'BOOKMARKED');
              },
              className: "px-3",
              children: (0, jsx_runtime_1.jsx)(lucide_react_1.Bookmark, {
                className: "h-3 w-3"
              })
            })]
          })]
        }, resource.id);
      })) :
      /* istanbul ignore next */
      (cov_285cpx7d66().b[72][1]++, (0, jsx_runtime_1.jsxs)("div", {
        className: "text-center py-8",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.BookOpen, {
          className: "h-12 w-12 text-gray-400 mx-auto mb-4"
        }), (0, jsx_runtime_1.jsx)("p", {
          className: "text-gray-600 dark:text-gray-400 mb-4",
          children: "No personalized resources available yet. Complete an assessment to get personalized recommendations."
        }), (0, jsx_runtime_1.jsx)(button_1.Button, {
          asChild: true,
          children: (0, jsx_runtime_1.jsx)(link_1.default, {
            href: "/assessment",
            children: "Take Assessment"
          })
        })]
      }))
    }),
    /* istanbul ignore next */
    (cov_285cpx7d66().b[81][0]++, data.suggestedCareerPaths) &&
    /* istanbul ignore next */
    (cov_285cpx7d66().b[81][1]++, data.suggestedCareerPaths.length > 0) &&
    /* istanbul ignore next */
    (cov_285cpx7d66().b[81][2]++, (0, jsx_runtime_1.jsxs)("div", {
      className: "mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",
      children: [(0, jsx_runtime_1.jsx)("p", {
        className: "text-sm text-gray-600 dark:text-gray-400 mb-2",
        children: "Based on your interest in:"
      }), (0, jsx_runtime_1.jsx)("div", {
        className: "flex flex-wrap gap-2",
        children: data.suggestedCareerPaths.map(function (path) {
          /* istanbul ignore next */
          cov_285cpx7d66().f[34]++;
          cov_285cpx7d66().s[177]++;
          return (0, jsx_runtime_1.jsx)(link_1.default, {
            href: "/career-paths/".concat(path.id),
            className: "px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-xs hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",
            children: path.name
          }, path.id);
        })
      })]
    })), (0, jsx_runtime_1.jsx)("div", {
      className: "mt-4 text-center",
      children: (0, jsx_runtime_1.jsx)(button_1.Button, {
        asChild: true,
        variant: "outline",
        size: "sm",
        children: (0, jsx_runtime_1.jsx)(link_1.default, {
          href: "/assessment",
          children: "Update Preferences"
        })
      })
    })]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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