45d75a618ffccff1e47e175ad0ef61be
"use strict";

/**
 * Secure Cache Service with User Session Validation
 * Prevents cache key collisions and unauthorized access to cached data
 */
/* istanbul ignore next */
function cov_13l0ggms5q() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/secure-cache-service.ts";
  var hash = "d521e93df6937aa1e01796569d873811efe069c2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/secure-cache-service.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 16
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "2": {
        start: {
          line: 7,
          column: 91
        },
        end: {
          line: 7,
          column: 106
        }
      },
      "3": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 13,
          column: 7
        }
      },
      "4": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "5": {
        start: {
          line: 9,
          column: 42
        },
        end: {
          line: 9,
          column: 70
        }
      },
      "6": {
        start: {
          line: 9,
          column: 85
        },
        end: {
          line: 9,
          column: 95
        }
      },
      "7": {
        start: {
          line: 10,
          column: 35
        },
        end: {
          line: 10,
          column: 100
        }
      },
      "8": {
        start: {
          line: 10,
          column: 41
        },
        end: {
          line: 10,
          column: 73
        }
      },
      "9": {
        start: {
          line: 10,
          column: 88
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "10": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 116
        }
      },
      "11": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 78
        }
      },
      "12": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "13": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 104
        }
      },
      "14": {
        start: {
          line: 16,
          column: 43
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "15": {
        start: {
          line: 16,
          column: 57
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "16": {
        start: {
          line: 16,
          column: 69
        },
        end: {
          line: 16,
          column: 81
        }
      },
      "17": {
        start: {
          line: 16,
          column: 119
        },
        end: {
          line: 16,
          column: 196
        }
      },
      "18": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 160
        }
      },
      "19": {
        start: {
          line: 17,
          column: 141
        },
        end: {
          line: 17,
          column: 153
        }
      },
      "20": {
        start: {
          line: 18,
          column: 23
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "21": {
        start: {
          line: 18,
          column: 45
        },
        end: {
          line: 18,
          column: 65
        }
      },
      "22": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "23": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "24": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "25": {
        start: {
          line: 21,
          column: 50
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "26": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "27": {
        start: {
          line: 22,
          column: 160
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "28": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "29": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "30": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 36,
          column: 13
        }
      },
      "31": {
        start: {
          line: 25,
          column: 32
        },
        end: {
          line: 25,
          column: 39
        }
      },
      "32": {
        start: {
          line: 25,
          column: 40
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "33": {
        start: {
          line: 26,
          column: 24
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "34": {
        start: {
          line: 26,
          column: 35
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "35": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "36": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 45
        }
      },
      "37": {
        start: {
          line: 27,
          column: 46
        },
        end: {
          line: 27,
          column: 55
        }
      },
      "38": {
        start: {
          line: 27,
          column: 56
        },
        end: {
          line: 27,
          column: 65
        }
      },
      "39": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "40": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "41": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "42": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 128
        }
      },
      "43": {
        start: {
          line: 30,
          column: 110
        },
        end: {
          line: 30,
          column: 116
        }
      },
      "44": {
        start: {
          line: 30,
          column: 117
        },
        end: {
          line: 30,
          column: 126
        }
      },
      "45": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 106
        }
      },
      "46": {
        start: {
          line: 31,
          column: 81
        },
        end: {
          line: 31,
          column: 97
        }
      },
      "47": {
        start: {
          line: 31,
          column: 98
        },
        end: {
          line: 31,
          column: 104
        }
      },
      "48": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 89
        }
      },
      "49": {
        start: {
          line: 32,
          column: 57
        },
        end: {
          line: 32,
          column: 72
        }
      },
      "50": {
        start: {
          line: 32,
          column: 73
        },
        end: {
          line: 32,
          column: 80
        }
      },
      "51": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "52": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "53": {
        start: {
          line: 33,
          column: 47
        },
        end: {
          line: 33,
          column: 62
        }
      },
      "54": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "55": {
        start: {
          line: 33,
          column: 79
        },
        end: {
          line: 33,
          column: 85
        }
      },
      "56": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "57": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "58": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 33
        }
      },
      "59": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 43
        }
      },
      "60": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "61": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "62": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 41
        }
      },
      "63": {
        start: {
          line: 38,
          column: 54
        },
        end: {
          line: 38,
          column: 64
        }
      },
      "64": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "65": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "66": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "67": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 50,
          column: 1
        }
      },
      "68": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "69": {
        start: {
          line: 43,
          column: 40
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "70": {
        start: {
          line: 43,
          column: 53
        },
        end: {
          line: 43,
          column: 54
        }
      },
      "71": {
        start: {
          line: 43,
          column: 60
        },
        end: {
          line: 43,
          column: 71
        }
      },
      "72": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 47,
          column: 9
        }
      },
      "73": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 65
        }
      },
      "74": {
        start: {
          line: 45,
          column: 21
        },
        end: {
          line: 45,
          column: 65
        }
      },
      "75": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 46,
          column: 28
        }
      },
      "76": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 61
        }
      },
      "77": {
        start: {
          line: 51,
          column: 22
        },
        end: {
          line: 53,
          column: 1
        }
      },
      "78": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "79": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 62
        }
      },
      "80": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 36
        }
      },
      "81": {
        start: {
          line: 56,
          column: 18
        },
        end: {
          line: 56,
          column: 38
        }
      },
      "82": {
        start: {
          line: 57,
          column: 13
        },
        end: {
          line: 57,
          column: 30
        }
      },
      "83": {
        start: {
          line: 58,
          column: 35
        },
        end: {
          line: 58,
          column: 83
        }
      },
      "84": {
        start: {
          line: 59,
          column: 15
        },
        end: {
          line: 59,
          column: 49
        }
      },
      "85": {
        start: {
          line: 60,
          column: 40
        },
        end: {
          line: 303,
          column: 3
        }
      },
      "86": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 84,
          column: 6
        }
      },
      "87": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 67,
          column: 24
        }
      },
      "88": {
        start: {
          line: 68,
          column: 21
        },
        end: {
          line: 68,
          column: 23
        }
      },
      "89": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 71,
          column: 9
        }
      },
      "90": {
        start: {
          line: 69,
          column: 22
        },
        end: {
          line: 69,
          column: 23
        }
      },
      "91": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 70,
          column: 43
        }
      },
      "92": {
        start: {
          line: 73,
          column: 28
        },
        end: {
          line: 73,
          column: 52
        }
      },
      "93": {
        start: {
          line: 74,
          column: 30
        },
        end: {
          line: 74,
          column: 56
        }
      },
      "94": {
        start: {
          line: 75,
          column: 33
        },
        end: {
          line: 75,
          column: 89
        }
      },
      "95": {
        start: {
          line: 76,
          column: 30
        },
        end: {
          line: 76,
          column: 89
        }
      },
      "96": {
        start: {
          line: 76,
          column: 56
        },
        end: {
          line: 76,
          column: 86
        }
      },
      "97": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 34
        }
      },
      "98": {
        start: {
          line: 79,
          column: 20
        },
        end: {
          line: 79,
          column: 67
        }
      },
      "99": {
        start: {
          line: 81,
          column: 25
        },
        end: {
          line: 81,
          column: 125
        }
      },
      "100": {
        start: {
          line: 82,
          column: 23
        },
        end: {
          line: 82,
          column: 110
        }
      },
      "101": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 181
        }
      },
      "102": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 136,
          column: 6
        }
      },
      "103": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 135,
          column: 11
        }
      },
      "104": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 51
        }
      },
      "105": {
        start: {
          line: 92,
          column: 37
        },
        end: {
          line: 92,
          column: 49
        }
      },
      "106": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 93,
          column: 53
        }
      },
      "107": {
        start: {
          line: 93,
          column: 38
        },
        end: {
          line: 93,
          column: 51
        }
      },
      "108": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 134,
          column: 15
        }
      },
      "109": {
        start: {
          line: 95,
          column: 16
        },
        end: {
          line: 133,
          column: 17
        }
      },
      "110": {
        start: {
          line: 97,
          column: 24
        },
        end: {
          line: 97,
          column: 50
        }
      },
      "111": {
        start: {
          line: 98,
          column: 24
        },
        end: {
          line: 98,
          column: 100
        }
      },
      "112": {
        start: {
          line: 100,
          column: 24
        },
        end: {
          line: 100,
          column: 44
        }
      },
      "113": {
        start: {
          line: 101,
          column: 24
        },
        end: {
          line: 104,
          column: 25
        }
      },
      "114": {
        start: {
          line: 102,
          column: 28
        },
        end: {
          line: 102,
          column: 103
        }
      },
      "115": {
        start: {
          line: 103,
          column: 28
        },
        end: {
          line: 103,
          column: 57
        }
      },
      "116": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 105,
          column: 163
        }
      },
      "117": {
        start: {
          line: 106,
          column: 24
        },
        end: {
          line: 106,
          column: 101
        }
      },
      "118": {
        start: {
          line: 107,
          column: 24
        },
        end: {
          line: 107,
          column: 127
        }
      },
      "119": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 114,
          column: 26
        }
      },
      "120": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 118,
          column: 26
        }
      },
      "121": {
        start: {
          line: 119,
          column: 24
        },
        end: {
          line: 119,
          column: 202
        }
      },
      "122": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 121,
          column: 34
        }
      },
      "123": {
        start: {
          line: 122,
          column: 24
        },
        end: {
          line: 126,
          column: 27
        }
      },
      "124": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 127,
          column: 52
        }
      },
      "125": {
        start: {
          line: 129,
          column: 24
        },
        end: {
          line: 129,
          column: 44
        }
      },
      "126": {
        start: {
          line: 130,
          column: 24
        },
        end: {
          line: 130,
          column: 88
        }
      },
      "127": {
        start: {
          line: 131,
          column: 24
        },
        end: {
          line: 131,
          column: 53
        }
      },
      "128": {
        start: {
          line: 132,
          column: 28
        },
        end: {
          line: 132,
          column: 50
        }
      },
      "129": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 194,
          column: 6
        }
      },
      "130": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 193,
          column: 11
        }
      },
      "131": {
        start: {
          line: 144,
          column: 12
        },
        end: {
          line: 144,
          column: 51
        }
      },
      "132": {
        start: {
          line: 144,
          column: 37
        },
        end: {
          line: 144,
          column: 49
        }
      },
      "133": {
        start: {
          line: 145,
          column: 12
        },
        end: {
          line: 145,
          column: 53
        }
      },
      "134": {
        start: {
          line: 145,
          column: 38
        },
        end: {
          line: 145,
          column: 51
        }
      },
      "135": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 192,
          column: 15
        }
      },
      "136": {
        start: {
          line: 147,
          column: 16
        },
        end: {
          line: 191,
          column: 17
        }
      },
      "137": {
        start: {
          line: 149,
          column: 24
        },
        end: {
          line: 149,
          column: 50
        }
      },
      "138": {
        start: {
          line: 150,
          column: 24
        },
        end: {
          line: 150,
          column: 100
        }
      },
      "139": {
        start: {
          line: 152,
          column: 24
        },
        end: {
          line: 152,
          column: 44
        }
      },
      "140": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 155,
          column: 25
        }
      },
      "141": {
        start: {
          line: 154,
          column: 28
        },
        end: {
          line: 154,
          column: 56
        }
      },
      "142": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 163
        }
      },
      "143": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 157,
          column: 101
        }
      },
      "144": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 127
        }
      },
      "145": {
        start: {
          line: 159,
          column: 24
        },
        end: {
          line: 159,
          column: 107
        }
      },
      "146": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 161,
          column: 47
        }
      },
      "147": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 164,
          column: 25
        }
      },
      "148": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 163,
          column: 56
        }
      },
      "149": {
        start: {
          line: 165,
          column: 24
        },
        end: {
          line: 165,
          column: 126
        }
      },
      "150": {
        start: {
          line: 165,
          column: 102
        },
        end: {
          line: 165,
          column: 126
        }
      },
      "151": {
        start: {
          line: 166,
          column: 24
        },
        end: {
          line: 166,
          column: 110
        }
      },
      "152": {
        start: {
          line: 167,
          column: 24
        },
        end: {
          line: 167,
          column: 110
        }
      },
      "153": {
        start: {
          line: 169,
          column: 24
        },
        end: {
          line: 169,
          column: 34
        }
      },
      "154": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 170,
          column: 52
        }
      },
      "155": {
        start: {
          line: 172,
          column: 24
        },
        end: {
          line: 172,
          column: 86
        }
      },
      "156": {
        start: {
          line: 173,
          column: 24
        },
        end: {
          line: 173,
          column: 106
        }
      },
      "157": {
        start: {
          line: 173,
          column: 82
        },
        end: {
          line: 173,
          column: 106
        }
      },
      "158": {
        start: {
          line: 174,
          column: 24
        },
        end: {
          line: 174,
          column: 113
        }
      },
      "159": {
        start: {
          line: 175,
          column: 24
        },
        end: {
          line: 175,
          column: 110
        }
      },
      "160": {
        start: {
          line: 177,
          column: 24
        },
        end: {
          line: 177,
          column: 34
        }
      },
      "161": {
        start: {
          line: 178,
          column: 24
        },
        end: {
          line: 178,
          column: 52
        }
      },
      "162": {
        start: {
          line: 180,
          column: 24
        },
        end: {
          line: 184,
          column: 27
        }
      },
      "163": {
        start: {
          line: 185,
          column: 24
        },
        end: {
          line: 185,
          column: 63
        }
      },
      "164": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 187,
          column: 44
        }
      },
      "165": {
        start: {
          line: 188,
          column: 24
        },
        end: {
          line: 188,
          column: 88
        }
      },
      "166": {
        start: {
          line: 189,
          column: 24
        },
        end: {
          line: 189,
          column: 52
        }
      },
      "167": {
        start: {
          line: 190,
          column: 28
        },
        end: {
          line: 190,
          column: 50
        }
      },
      "168": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 226,
          column: 6
        }
      },
      "169": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 225,
          column: 11
        }
      },
      "170": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 202,
          column: 51
        }
      },
      "171": {
        start: {
          line: 202,
          column: 37
        },
        end: {
          line: 202,
          column: 49
        }
      },
      "172": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 203,
          column: 53
        }
      },
      "173": {
        start: {
          line: 203,
          column: 38
        },
        end: {
          line: 203,
          column: 51
        }
      },
      "174": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 224,
          column: 15
        }
      },
      "175": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 223,
          column: 17
        }
      },
      "176": {
        start: {
          line: 207,
          column: 24
        },
        end: {
          line: 207,
          column: 50
        }
      },
      "177": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 100
        }
      },
      "178": {
        start: {
          line: 210,
          column: 24
        },
        end: {
          line: 210,
          column: 44
        }
      },
      "179": {
        start: {
          line: 211,
          column: 24
        },
        end: {
          line: 211,
          column: 163
        }
      },
      "180": {
        start: {
          line: 212,
          column: 24
        },
        end: {
          line: 212,
          column: 101
        }
      },
      "181": {
        start: {
          line: 213,
          column: 24
        },
        end: {
          line: 213,
          column: 127
        }
      },
      "182": {
        start: {
          line: 214,
          column: 24
        },
        end: {
          line: 214,
          column: 110
        }
      },
      "183": {
        start: {
          line: 216,
          column: 24
        },
        end: {
          line: 216,
          column: 34
        }
      },
      "184": {
        start: {
          line: 217,
          column: 24
        },
        end: {
          line: 217,
          column: 52
        }
      },
      "185": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 44
        }
      },
      "186": {
        start: {
          line: 220,
          column: 24
        },
        end: {
          line: 220,
          column: 91
        }
      },
      "187": {
        start: {
          line: 221,
          column: 24
        },
        end: {
          line: 221,
          column: 53
        }
      },
      "188": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 50
        }
      },
      "189": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 235,
          column: 6
        }
      },
      "190": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 234,
          column: 27
        }
      },
      "191": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 249,
          column: 6
        }
      },
      "192": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 243,
          column: 122
        }
      },
      "193": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 247,
          column: 9
        }
      },
      "194": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 246,
          column: 110
        }
      },
      "195": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 248,
          column: 20
        }
      },
      "196": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 256,
          column: 6
        }
      },
      "197": {
        start: {
          line: 254,
          column: 25
        },
        end: {
          line: 254,
          column: 45
        }
      },
      "198": {
        start: {
          line: 255,
          column: 8
        },
        end: {
          line: 255,
          column: 103
        }
      },
      "199": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 279,
          column: 6
        }
      },
      "200": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 264,
          column: 9
        }
      },
      "201": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 263,
          column: 25
        }
      },
      "202": {
        start: {
          line: 266,
          column: 8
        },
        end: {
          line: 268,
          column: 9
        }
      },
      "203": {
        start: {
          line: 267,
          column: 12
        },
        end: {
          line: 267,
          column: 25
        }
      },
      "204": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 272,
          column: 9
        }
      },
      "205": {
        start: {
          line: 271,
          column: 12
        },
        end: {
          line: 271,
          column: 25
        }
      },
      "206": {
        start: {
          line: 274,
          column: 21
        },
        end: {
          line: 274,
          column: 40
        }
      },
      "207": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 277,
          column: 9
        }
      },
      "208": {
        start: {
          line: 276,
          column: 12
        },
        end: {
          line: 276,
          column: 25
        }
      },
      "209": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 278,
          column: 20
        }
      },
      "210": {
        start: {
          line: 283,
          column: 4
        },
        end: {
          line: 299,
          column: 6
        }
      },
      "211": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 298,
          column: 11
        }
      },
      "212": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 297,
          column: 15
        }
      },
      "213": {
        start: {
          line: 286,
          column: 16
        },
        end: {
          line: 295,
          column: 17
        }
      },
      "214": {
        start: {
          line: 289,
          column: 20
        },
        end: {
          line: 289,
          column: 117
        }
      },
      "215": {
        start: {
          line: 294,
          column: 20
        },
        end: {
          line: 294,
          column: 84
        }
      },
      "216": {
        start: {
          line: 296,
          column: 16
        },
        end: {
          line: 296,
          column: 38
        }
      },
      "217": {
        start: {
          line: 300,
          column: 4
        },
        end: {
          line: 300,
          column: 46
        }
      },
      "218": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 301,
          column: 42
        }
      },
      "219": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 302,
          column: 30
        }
      },
      "220": {
        start: {
          line: 304,
          column: 0
        },
        end: {
          line: 304,
          column: 48
        }
      },
      "221": {
        start: {
          line: 305,
          column: 0
        },
        end: {
          line: 305,
          column: 37
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 6,
            column: 45
          }
        },
        loc: {
          start: {
            line: 6,
            column: 89
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 7,
            column: 13
          },
          end: {
            line: 7,
            column: 18
          }
        },
        loc: {
          start: {
            line: 7,
            column: 26
          },
          end: {
            line: 7,
            column: 112
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 71
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 7,
            column: 108
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 63
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 8
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 26
          }
        },
        loc: {
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 99
          }
        },
        line: 9
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 33
          },
          end: {
            line: 10,
            column: 102
          }
        },
        line: 10
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 21
          }
        },
        loc: {
          start: {
            line: 11,
            column: 30
          },
          end: {
            line: 11,
            column: 118
          }
        },
        line: 11
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 49
          }
        },
        loc: {
          start: {
            line: 15,
            column: 73
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 15
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 16,
            column: 30
          },
          end: {
            line: 16,
            column: 31
          }
        },
        loc: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 83
          }
        },
        line: 16
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 17,
            column: 128
          },
          end: {
            line: 17,
            column: 129
          }
        },
        loc: {
          start: {
            line: 17,
            column: 139
          },
          end: {
            line: 17,
            column: 155
          }
        },
        line: 17
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 70
          }
        },
        line: 18
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 31
          }
        },
        loc: {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 67
          }
        },
        line: 18
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 19
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 42,
            column: 52
          },
          end: {
            line: 42,
            column: 53
          }
        },
        loc: {
          start: {
            line: 42,
            column: 78
          },
          end: {
            line: 50,
            column: 1
          }
        },
        line: 42
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 51,
            column: 56
          },
          end: {
            line: 51,
            column: 57
          }
        },
        loc: {
          start: {
            line: 51,
            column: 71
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 51
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 60,
            column: 40
          },
          end: {
            line: 60,
            column: 41
          }
        },
        loc: {
          start: {
            line: 60,
            column: 52
          },
          end: {
            line: 303,
            column: 1
          }
        },
        line: 60
      },
      "16": {
        name: "SecureCacheService",
        decl: {
          start: {
            line: 61,
            column: 13
          },
          end: {
            line: 61,
            column: 31
          }
        },
        loc: {
          start: {
            line: 61,
            column: 34
          },
          end: {
            line: 62,
            column: 5
          }
        },
        line: 61
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 66,
            column: 43
          },
          end: {
            line: 66,
            column: 44
          }
        },
        loc: {
          start: {
            line: 66,
            column: 78
          },
          end: {
            line: 84,
            column: 5
          }
        },
        line: 66
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 76,
            column: 41
          },
          end: {
            line: 76,
            column: 42
          }
        },
        loc: {
          start: {
            line: 76,
            column: 54
          },
          end: {
            line: 76,
            column: 88
          }
        },
        line: 76
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 88,
            column: 35
          },
          end: {
            line: 88,
            column: 36
          }
        },
        loc: {
          start: {
            line: 88,
            column: 72
          },
          end: {
            line: 136,
            column: 5
          }
        },
        line: 88
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 89,
            column: 51
          },
          end: {
            line: 89,
            column: 52
          }
        },
        loc: {
          start: {
            line: 89,
            column: 99
          },
          end: {
            line: 135,
            column: 9
          }
        },
        line: 89
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 94,
            column: 37
          },
          end: {
            line: 94,
            column: 38
          }
        },
        loc: {
          start: {
            line: 94,
            column: 51
          },
          end: {
            line: 134,
            column: 13
          }
        },
        line: 94
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 140,
            column: 35
          },
          end: {
            line: 140,
            column: 36
          }
        },
        loc: {
          start: {
            line: 140,
            column: 64
          },
          end: {
            line: 194,
            column: 5
          }
        },
        line: 140
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 141,
            column: 51
          },
          end: {
            line: 141,
            column: 52
          }
        },
        loc: {
          start: {
            line: 141,
            column: 93
          },
          end: {
            line: 193,
            column: 9
          }
        },
        line: 141
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 146,
            column: 37
          },
          end: {
            line: 146,
            column: 38
          }
        },
        loc: {
          start: {
            line: 146,
            column: 51
          },
          end: {
            line: 192,
            column: 13
          }
        },
        line: 146
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 198,
            column: 38
          },
          end: {
            line: 198,
            column: 39
          }
        },
        loc: {
          start: {
            line: 198,
            column: 67
          },
          end: {
            line: 226,
            column: 5
          }
        },
        line: 198
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 199,
            column: 51
          },
          end: {
            line: 199,
            column: 52
          }
        },
        loc: {
          start: {
            line: 199,
            column: 93
          },
          end: {
            line: 225,
            column: 9
          }
        },
        line: 199
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 204,
            column: 37
          },
          end: {
            line: 204,
            column: 38
          }
        },
        loc: {
          start: {
            line: 204,
            column: 51
          },
          end: {
            line: 224,
            column: 13
          }
        },
        line: 204
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 230,
            column: 39
          },
          end: {
            line: 230,
            column: 40
          }
        },
        loc: {
          start: {
            line: 230,
            column: 56
          },
          end: {
            line: 235,
            column: 5
          }
        },
        line: 230
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 239,
            column: 42
          },
          end: {
            line: 239,
            column: 43
          }
        },
        loc: {
          start: {
            line: 239,
            column: 61
          },
          end: {
            line: 249,
            column: 5
          }
        },
        line: 239
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 253,
            column: 46
          },
          end: {
            line: 253,
            column: 47
          }
        },
        loc: {
          start: {
            line: 253,
            column: 62
          },
          end: {
            line: 256,
            column: 5
          }
        },
        line: 253
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 260,
            column: 42
          },
          end: {
            line: 260,
            column: 43
          }
        },
        loc: {
          start: {
            line: 260,
            column: 90
          },
          end: {
            line: 279,
            column: 5
          }
        },
        line: 260
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 283,
            column: 40
          },
          end: {
            line: 283,
            column: 41
          }
        },
        loc: {
          start: {
            line: 283,
            column: 58
          },
          end: {
            line: 299,
            column: 5
          }
        },
        line: 283
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 284,
            column: 48
          },
          end: {
            line: 284,
            column: 49
          }
        },
        loc: {
          start: {
            line: 284,
            column: 60
          },
          end: {
            line: 298,
            column: 9
          }
        },
        line: 284
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 285,
            column: 37
          },
          end: {
            line: 285,
            column: 38
          }
        },
        loc: {
          start: {
            line: 285,
            column: 51
          },
          end: {
            line: 297,
            column: 13
          }
        },
        line: 285
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 21
          }
        }, {
          start: {
            line: 6,
            column: 25
          },
          end: {
            line: 6,
            column: 39
          }
        }, {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 56
          },
          end: {
            line: 7,
            column: 61
          }
        }, {
          start: {
            line: 7,
            column: 64
          },
          end: {
            line: 7,
            column: 109
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 17
          }
        }, {
          start: {
            line: 8,
            column: 22
          },
          end: {
            line: 8,
            column: 33
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 11,
            column: 32
          },
          end: {
            line: 11,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 11,
            column: 46
          },
          end: {
            line: 11,
            column: 67
          }
        }, {
          start: {
            line: 11,
            column: 70
          },
          end: {
            line: 11,
            column: 115
          }
        }],
        line: 11
      },
      "4": {
        loc: {
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 61
          }
        }, {
          start: {
            line: 12,
            column: 65
          },
          end: {
            line: 12,
            column: 67
          }
        }],
        line: 12
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 41,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 23
          }
        }, {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 43
          }
        }, {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 41,
            column: 1
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "7": {
        loc: {
          start: {
            line: 16,
            column: 134
          },
          end: {
            line: 16,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 167
          },
          end: {
            line: 16,
            column: 175
          }
        }, {
          start: {
            line: 16,
            column: 178
          },
          end: {
            line: 16,
            column: 184
          }
        }],
        line: 16
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 102
          }
        }, {
          start: {
            line: 17,
            column: 107
          },
          end: {
            line: 17,
            column: 155
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "10": {
        loc: {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 16
          }
        }, {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 43
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 125
          }
        }, {
          start: {
            line: 22,
            column: 130
          },
          end: {
            line: 22,
            column: 158
          }
        }],
        line: 22
      },
      "14": {
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 22,
            column: 56
          }
        }, {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "15": {
        loc: {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        }, {
          start: {
            line: 22,
            column: 119
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "16": {
        loc: {
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 77
          }
        }, {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "17": {
        loc: {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 83
          },
          end: {
            line: 22,
            column: 98
          }
        }, {
          start: {
            line: 22,
            column: 103
          },
          end: {
            line: 22,
            column: 112
          }
        }],
        line: 22
      },
      "18": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "19": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 23
          }
        }, {
          start: {
            line: 25,
            column: 24
          },
          end: {
            line: 25,
            column: 46
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 72
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 65
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 24
      },
      "20": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 74
          }
        }, {
          start: {
            line: 30,
            column: 79
          },
          end: {
            line: 30,
            column: 90
          }
        }, {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 30,
            column: 105
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 73
          }
        }],
        line: 30
      },
      "23": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "24": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 35
          }
        }, {
          start: {
            line: 31,
            column: 40
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: 31,
            column: 47
          },
          end: {
            line: 31,
            column: 59
          }
        }, {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 31,
            column: 75
          }
        }],
        line: 31
      },
      "25": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "26": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 39
          },
          end: {
            line: 32,
            column: 53
          }
        }],
        line: 32
      },
      "27": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "28": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 25
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "29": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "30": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "31": {
        loc: {
          start: {
            line: 39,
            column: 52
          },
          end: {
            line: 39,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 60
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 68
          },
          end: {
            line: 39,
            column: 74
          }
        }],
        line: 39
      },
      "32": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 50,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 21
          },
          end: {
            line: 42,
            column: 25
          }
        }, {
          start: {
            line: 42,
            column: 29
          },
          end: {
            line: 42,
            column: 47
          }
        }, {
          start: {
            line: 42,
            column: 52
          },
          end: {
            line: 50,
            column: 1
          }
        }],
        line: 42
      },
      "33": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "34": {
        loc: {
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 12
          }
        }, {
          start: {
            line: 43,
            column: 16
          },
          end: {
            line: 43,
            column: 38
          }
        }],
        line: 43
      },
      "35": {
        loc: {
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 47,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 47,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "36": {
        loc: {
          start: {
            line: 44,
            column: 12
          },
          end: {
            line: 44,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 12
          },
          end: {
            line: 44,
            column: 14
          }
        }, {
          start: {
            line: 44,
            column: 18
          },
          end: {
            line: 44,
            column: 30
          }
        }],
        line: 44
      },
      "37": {
        loc: {
          start: {
            line: 45,
            column: 12
          },
          end: {
            line: 45,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 12
          },
          end: {
            line: 45,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "38": {
        loc: {
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 23
          }
        }, {
          start: {
            line: 49,
            column: 27
          },
          end: {
            line: 49,
            column: 59
          }
        }],
        line: 49
      },
      "39": {
        loc: {
          start: {
            line: 51,
            column: 22
          },
          end: {
            line: 53,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 23
          },
          end: {
            line: 51,
            column: 27
          }
        }, {
          start: {
            line: 51,
            column: 31
          },
          end: {
            line: 51,
            column: 51
          }
        }, {
          start: {
            line: 51,
            column: 56
          },
          end: {
            line: 53,
            column: 1
          }
        }],
        line: 51
      },
      "40": {
        loc: {
          start: {
            line: 52,
            column: 11
          },
          end: {
            line: 52,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 37
          },
          end: {
            line: 52,
            column: 40
          }
        }, {
          start: {
            line: 52,
            column: 43
          },
          end: {
            line: 52,
            column: 61
          }
        }],
        line: 52
      },
      "41": {
        loc: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 15
          }
        }, {
          start: {
            line: 52,
            column: 19
          },
          end: {
            line: 52,
            column: 33
          }
        }],
        line: 52
      },
      "42": {
        loc: {
          start: {
            line: 75,
            column: 33
          },
          end: {
            line: 75,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 75,
            column: 45
          },
          end: {
            line: 75,
            column: 74
          }
        }, {
          start: {
            line: 75,
            column: 77
          },
          end: {
            line: 75,
            column: 89
          }
        }],
        line: 75
      },
      "43": {
        loc: {
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 92,
            column: 51
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 92,
            column: 51
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "44": {
        loc: {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 93,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 93,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "45": {
        loc: {
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 133,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 96,
            column: 20
          },
          end: {
            line: 98,
            column: 100
          }
        }, {
          start: {
            line: 99,
            column: 20
          },
          end: {
            line: 119,
            column: 202
          }
        }, {
          start: {
            line: 120,
            column: 20
          },
          end: {
            line: 127,
            column: 52
          }
        }, {
          start: {
            line: 128,
            column: 20
          },
          end: {
            line: 131,
            column: 53
          }
        }, {
          start: {
            line: 132,
            column: 20
          },
          end: {
            line: 132,
            column: 50
          }
        }],
        line: 95
      },
      "46": {
        loc: {
          start: {
            line: 101,
            column: 24
          },
          end: {
            line: 104,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 101,
            column: 24
          },
          end: {
            line: 104,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 101
      },
      "47": {
        loc: {
          start: {
            line: 101,
            column: 28
          },
          end: {
            line: 101,
            column: 179
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 101,
            column: 28
          },
          end: {
            line: 101,
            column: 60
          }
        }, {
          start: {
            line: 101,
            column: 64
          },
          end: {
            line: 101,
            column: 179
          }
        }],
        line: 101
      },
      "48": {
        loc: {
          start: {
            line: 101,
            column: 66
          },
          end: {
            line: 101,
            column: 178
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 101,
            column: 164
          },
          end: {
            line: 101,
            column: 170
          }
        }, {
          start: {
            line: 101,
            column: 173
          },
          end: {
            line: 101,
            column: 178
          }
        }],
        line: 101
      },
      "49": {
        loc: {
          start: {
            line: 101,
            column: 66
          },
          end: {
            line: 101,
            column: 161
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 101,
            column: 66
          },
          end: {
            line: 101,
            column: 144
          }
        }, {
          start: {
            line: 101,
            column: 148
          },
          end: {
            line: 101,
            column: 161
          }
        }],
        line: 101
      },
      "50": {
        loc: {
          start: {
            line: 101,
            column: 72
          },
          end: {
            line: 101,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 101,
            column: 113
          },
          end: {
            line: 101,
            column: 119
          }
        }, {
          start: {
            line: 101,
            column: 122
          },
          end: {
            line: 101,
            column: 134
          }
        }],
        line: 101
      },
      "51": {
        loc: {
          start: {
            line: 101,
            column: 72
          },
          end: {
            line: 101,
            column: 110
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 101,
            column: 72
          },
          end: {
            line: 101,
            column: 88
          }
        }, {
          start: {
            line: 101,
            column: 92
          },
          end: {
            line: 101,
            column: 110
          }
        }],
        line: 101
      },
      "52": {
        loc: {
          start: {
            line: 105,
            column: 33
          },
          end: {
            line: 105,
            column: 162
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 34
          },
          end: {
            line: 105,
            column: 146
          }
        }, {
          start: {
            line: 105,
            column: 151
          },
          end: {
            line: 105,
            column: 162
          }
        }],
        line: 105
      },
      "53": {
        loc: {
          start: {
            line: 105,
            column: 34
          },
          end: {
            line: 105,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 105,
            column: 132
          },
          end: {
            line: 105,
            column: 138
          }
        }, {
          start: {
            line: 105,
            column: 141
          },
          end: {
            line: 105,
            column: 146
          }
        }],
        line: 105
      },
      "54": {
        loc: {
          start: {
            line: 105,
            column: 34
          },
          end: {
            line: 105,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 34
          },
          end: {
            line: 105,
            column: 112
          }
        }, {
          start: {
            line: 105,
            column: 116
          },
          end: {
            line: 105,
            column: 129
          }
        }],
        line: 105
      },
      "55": {
        loc: {
          start: {
            line: 105,
            column: 40
          },
          end: {
            line: 105,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 105,
            column: 81
          },
          end: {
            line: 105,
            column: 87
          }
        }, {
          start: {
            line: 105,
            column: 90
          },
          end: {
            line: 105,
            column: 102
          }
        }],
        line: 105
      },
      "56": {
        loc: {
          start: {
            line: 105,
            column: 40
          },
          end: {
            line: 105,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 40
          },
          end: {
            line: 105,
            column: 56
          }
        }, {
          start: {
            line: 105,
            column: 60
          },
          end: {
            line: 105,
            column: 78
          }
        }],
        line: 105
      },
      "57": {
        loc: {
          start: {
            line: 106,
            column: 36
          },
          end: {
            line: 106,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 63
          },
          end: {
            line: 106,
            column: 93
          }
        }, {
          start: {
            line: 106,
            column: 96
          },
          end: {
            line: 106,
            column: 100
          }
        }],
        line: 106
      },
      "58": {
        loc: {
          start: {
            line: 110,
            column: 39
          },
          end: {
            line: 110,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 110,
            column: 39
          },
          end: {
            line: 110,
            column: 48
          }
        }, {
          start: {
            line: 110,
            column: 52
          },
          end: {
            line: 110,
            column: 61
          }
        }],
        line: 110
      },
      "59": {
        loc: {
          start: {
            line: 119,
            column: 126
          },
          end: {
            line: 119,
            column: 157
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 126
          },
          end: {
            line: 119,
            column: 137
          }
        }, {
          start: {
            line: 119,
            column: 141
          },
          end: {
            line: 119,
            column: 157
          }
        }],
        line: 119
      },
      "60": {
        loc: {
          start: {
            line: 144,
            column: 12
          },
          end: {
            line: 144,
            column: 51
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 144,
            column: 12
          },
          end: {
            line: 144,
            column: 51
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 144
      },
      "61": {
        loc: {
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 145,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 145,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 145
      },
      "62": {
        loc: {
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 191,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 148,
            column: 20
          },
          end: {
            line: 150,
            column: 100
          }
        }, {
          start: {
            line: 151,
            column: 20
          },
          end: {
            line: 159,
            column: 107
          }
        }, {
          start: {
            line: 160,
            column: 20
          },
          end: {
            line: 167,
            column: 110
          }
        }, {
          start: {
            line: 168,
            column: 20
          },
          end: {
            line: 170,
            column: 52
          }
        }, {
          start: {
            line: 171,
            column: 20
          },
          end: {
            line: 175,
            column: 110
          }
        }, {
          start: {
            line: 176,
            column: 20
          },
          end: {
            line: 178,
            column: 52
          }
        }, {
          start: {
            line: 179,
            column: 20
          },
          end: {
            line: 185,
            column: 63
          }
        }, {
          start: {
            line: 186,
            column: 20
          },
          end: {
            line: 189,
            column: 52
          }
        }, {
          start: {
            line: 190,
            column: 20
          },
          end: {
            line: 190,
            column: 50
          }
        }],
        line: 147
      },
      "63": {
        loc: {
          start: {
            line: 153,
            column: 24
          },
          end: {
            line: 155,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 24
          },
          end: {
            line: 155,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "64": {
        loc: {
          start: {
            line: 153,
            column: 28
          },
          end: {
            line: 153,
            column: 179
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 28
          },
          end: {
            line: 153,
            column: 60
          }
        }, {
          start: {
            line: 153,
            column: 64
          },
          end: {
            line: 153,
            column: 179
          }
        }],
        line: 153
      },
      "65": {
        loc: {
          start: {
            line: 153,
            column: 66
          },
          end: {
            line: 153,
            column: 178
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 153,
            column: 164
          },
          end: {
            line: 153,
            column: 170
          }
        }, {
          start: {
            line: 153,
            column: 173
          },
          end: {
            line: 153,
            column: 178
          }
        }],
        line: 153
      },
      "66": {
        loc: {
          start: {
            line: 153,
            column: 66
          },
          end: {
            line: 153,
            column: 161
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 66
          },
          end: {
            line: 153,
            column: 144
          }
        }, {
          start: {
            line: 153,
            column: 148
          },
          end: {
            line: 153,
            column: 161
          }
        }],
        line: 153
      },
      "67": {
        loc: {
          start: {
            line: 153,
            column: 72
          },
          end: {
            line: 153,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 153,
            column: 113
          },
          end: {
            line: 153,
            column: 119
          }
        }, {
          start: {
            line: 153,
            column: 122
          },
          end: {
            line: 153,
            column: 134
          }
        }],
        line: 153
      },
      "68": {
        loc: {
          start: {
            line: 153,
            column: 72
          },
          end: {
            line: 153,
            column: 110
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 72
          },
          end: {
            line: 153,
            column: 88
          }
        }, {
          start: {
            line: 153,
            column: 92
          },
          end: {
            line: 153,
            column: 110
          }
        }],
        line: 153
      },
      "69": {
        loc: {
          start: {
            line: 156,
            column: 33
          },
          end: {
            line: 156,
            column: 162
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 156,
            column: 34
          },
          end: {
            line: 156,
            column: 146
          }
        }, {
          start: {
            line: 156,
            column: 151
          },
          end: {
            line: 156,
            column: 162
          }
        }],
        line: 156
      },
      "70": {
        loc: {
          start: {
            line: 156,
            column: 34
          },
          end: {
            line: 156,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 156,
            column: 132
          },
          end: {
            line: 156,
            column: 138
          }
        }, {
          start: {
            line: 156,
            column: 141
          },
          end: {
            line: 156,
            column: 146
          }
        }],
        line: 156
      },
      "71": {
        loc: {
          start: {
            line: 156,
            column: 34
          },
          end: {
            line: 156,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 156,
            column: 34
          },
          end: {
            line: 156,
            column: 112
          }
        }, {
          start: {
            line: 156,
            column: 116
          },
          end: {
            line: 156,
            column: 129
          }
        }],
        line: 156
      },
      "72": {
        loc: {
          start: {
            line: 156,
            column: 40
          },
          end: {
            line: 156,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 156,
            column: 81
          },
          end: {
            line: 156,
            column: 87
          }
        }, {
          start: {
            line: 156,
            column: 90
          },
          end: {
            line: 156,
            column: 102
          }
        }],
        line: 156
      },
      "73": {
        loc: {
          start: {
            line: 156,
            column: 40
          },
          end: {
            line: 156,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 156,
            column: 40
          },
          end: {
            line: 156,
            column: 56
          }
        }, {
          start: {
            line: 156,
            column: 60
          },
          end: {
            line: 156,
            column: 78
          }
        }],
        line: 156
      },
      "74": {
        loc: {
          start: {
            line: 157,
            column: 36
          },
          end: {
            line: 157,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 157,
            column: 63
          },
          end: {
            line: 157,
            column: 93
          }
        }, {
          start: {
            line: 157,
            column: 96
          },
          end: {
            line: 157,
            column: 100
          }
        }],
        line: 157
      },
      "75": {
        loc: {
          start: {
            line: 162,
            column: 24
          },
          end: {
            line: 164,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 24
          },
          end: {
            line: 164,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "76": {
        loc: {
          start: {
            line: 165,
            column: 24
          },
          end: {
            line: 165,
            column: 126
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 24
          },
          end: {
            line: 165,
            column: 126
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "77": {
        loc: {
          start: {
            line: 173,
            column: 24
          },
          end: {
            line: 173,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 24
          },
          end: {
            line: 173,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "78": {
        loc: {
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 202,
            column: 51
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 202,
            column: 51
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "79": {
        loc: {
          start: {
            line: 203,
            column: 12
          },
          end: {
            line: 203,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 12
          },
          end: {
            line: 203,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      },
      "80": {
        loc: {
          start: {
            line: 205,
            column: 16
          },
          end: {
            line: 223,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 206,
            column: 20
          },
          end: {
            line: 208,
            column: 100
          }
        }, {
          start: {
            line: 209,
            column: 20
          },
          end: {
            line: 214,
            column: 110
          }
        }, {
          start: {
            line: 215,
            column: 20
          },
          end: {
            line: 217,
            column: 52
          }
        }, {
          start: {
            line: 218,
            column: 20
          },
          end: {
            line: 221,
            column: 53
          }
        }, {
          start: {
            line: 222,
            column: 20
          },
          end: {
            line: 222,
            column: 50
          }
        }],
        line: 205
      },
      "81": {
        loc: {
          start: {
            line: 211,
            column: 33
          },
          end: {
            line: 211,
            column: 162
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 34
          },
          end: {
            line: 211,
            column: 146
          }
        }, {
          start: {
            line: 211,
            column: 151
          },
          end: {
            line: 211,
            column: 162
          }
        }],
        line: 211
      },
      "82": {
        loc: {
          start: {
            line: 211,
            column: 34
          },
          end: {
            line: 211,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 211,
            column: 132
          },
          end: {
            line: 211,
            column: 138
          }
        }, {
          start: {
            line: 211,
            column: 141
          },
          end: {
            line: 211,
            column: 146
          }
        }],
        line: 211
      },
      "83": {
        loc: {
          start: {
            line: 211,
            column: 34
          },
          end: {
            line: 211,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 34
          },
          end: {
            line: 211,
            column: 112
          }
        }, {
          start: {
            line: 211,
            column: 116
          },
          end: {
            line: 211,
            column: 129
          }
        }],
        line: 211
      },
      "84": {
        loc: {
          start: {
            line: 211,
            column: 40
          },
          end: {
            line: 211,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 211,
            column: 81
          },
          end: {
            line: 211,
            column: 87
          }
        }, {
          start: {
            line: 211,
            column: 90
          },
          end: {
            line: 211,
            column: 102
          }
        }],
        line: 211
      },
      "85": {
        loc: {
          start: {
            line: 211,
            column: 40
          },
          end: {
            line: 211,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 40
          },
          end: {
            line: 211,
            column: 56
          }
        }, {
          start: {
            line: 211,
            column: 60
          },
          end: {
            line: 211,
            column: 78
          }
        }],
        line: 211
      },
      "86": {
        loc: {
          start: {
            line: 212,
            column: 36
          },
          end: {
            line: 212,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 212,
            column: 63
          },
          end: {
            line: 212,
            column: 93
          }
        }, {
          start: {
            line: 212,
            column: 96
          },
          end: {
            line: 212,
            column: 100
          }
        }],
        line: 212
      },
      "87": {
        loc: {
          start: {
            line: 242,
            column: 28
          },
          end: {
            line: 243,
            column: 122
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 242,
            column: 29
          },
          end: {
            line: 242,
            column: 128
          }
        }, {
          start: {
            line: 243,
            column: 13
          },
          end: {
            line: 243,
            column: 121
          }
        }],
        line: 242
      },
      "88": {
        loc: {
          start: {
            line: 242,
            column: 29
          },
          end: {
            line: 242,
            column: 128
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 242,
            column: 111
          },
          end: {
            line: 242,
            column: 117
          }
        }, {
          start: {
            line: 242,
            column: 120
          },
          end: {
            line: 242,
            column: 128
          }
        }],
        line: 242
      },
      "89": {
        loc: {
          start: {
            line: 242,
            column: 29
          },
          end: {
            line: 242,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 242,
            column: 29
          },
          end: {
            line: 242,
            column: 91
          }
        }, {
          start: {
            line: 242,
            column: 95
          },
          end: {
            line: 242,
            column: 108
          }
        }],
        line: 242
      },
      "90": {
        loc: {
          start: {
            line: 243,
            column: 13
          },
          end: {
            line: 243,
            column: 121
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 243,
            column: 104
          },
          end: {
            line: 243,
            column: 110
          }
        }, {
          start: {
            line: 243,
            column: 113
          },
          end: {
            line: 243,
            column: 121
          }
        }],
        line: 243
      },
      "91": {
        loc: {
          start: {
            line: 243,
            column: 13
          },
          end: {
            line: 243,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 13
          },
          end: {
            line: 243,
            column: 84
          }
        }, {
          start: {
            line: 243,
            column: 88
          },
          end: {
            line: 243,
            column: 101
          }
        }],
        line: 243
      },
      "92": {
        loc: {
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 244
      },
      "93": {
        loc: {
          start: {
            line: 262,
            column: 8
          },
          end: {
            line: 264,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 262,
            column: 8
          },
          end: {
            line: 264,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 262
      },
      "94": {
        loc: {
          start: {
            line: 266,
            column: 8
          },
          end: {
            line: 268,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 8
          },
          end: {
            line: 268,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "95": {
        loc: {
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 266,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 266,
            column: 48
          }
        }, {
          start: {
            line: 266,
            column: 52
          },
          end: {
            line: 266,
            column: 78
          }
        }],
        line: 266
      },
      "96": {
        loc: {
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 272,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 272,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "97": {
        loc: {
          start: {
            line: 270,
            column: 12
          },
          end: {
            line: 270,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 12
          },
          end: {
            line: 270,
            column: 36
          }
        }, {
          start: {
            line: 270,
            column: 40
          },
          end: {
            line: 270,
            column: 72
          }
        }],
        line: 270
      },
      "98": {
        loc: {
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 277,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 277,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 275
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0, 0, 0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0, 0, 0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0],
      "94": [0, 0],
      "95": [0, 0],
      "96": [0, 0],
      "97": [0, 0],
      "98": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/secure-cache-service.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,uCAA6C;AAC7C,+BAAqC;AACrC,oFAA0E;AAC1E,kDAA4B;AAiB5B;IAAA;IA0PA,CAAC;IAtPC;;OAEG;IACI,oCAAiB,GAAxB,UACE,IAAY,EACZ,MAAc,EACd,SAAwB;QAH1B,iBAqBC;QAjBC,gBAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,+BAAmB;;QAEnB,sBAAsB;QACtB,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACnD,IAAM,kBAAkB,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QACpF,IAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAArB,CAAqB,CAAC,CAAC;QAE/D,6BAA6B;QAC7B,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAM,KAAK,GAAG,gBAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEpD,kCAAkC;QAClC,IAAM,UAAU,GAAG,eAAC,aAAa,EAAE,eAAe,EAAE,kBAAkB,GAAK,eAAe,QAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACtG,IAAM,QAAQ,GAAG,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAE/F,OAAO,iBAAU,IAAI,CAAC,aAAa,cAAI,aAAa,cAAI,eAAe,cAAI,kBAAkB,cAAI,QAAQ,cAAI,KAAK,CAAE,CAAC;IACvH,CAAC;IAED;;OAEG;IACU,4BAAS,GAAtB;0CAMG,OAAO,YALR,OAAoB,EACpB,IAAY,EACZ,IAAO,EACP,MAAqB,EACrB,OAAgC;;;YADhC,uBAAA,EAAA,WAAqB;YACrB,wBAAA,EAAA,YAAgC;;;;;wBAGd,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBAEnD,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;4BAC3D,OAAO,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;4BAC3E,sBAAO,KAAK,EAAC;wBACf,CAAC;wBAEK,MAAM,GAAG,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,KAAI,WAAW,CAAC;wBAC1C,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAG7E,QAAQ,GAAG,IAAI,CAAC,iBAAiB,OAAtB,IAAI,iBAAmB,IAAI,EAAE,MAAM,EAAE,SAAS,GAAK,MAAM,SAAC,CAAC;wBAGtE,QAAQ,GAAkB;4BAC9B,MAAM,QAAA;4BACN,SAAS,EAAE,SAAS,IAAI,SAAS;4BACjC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;4BACrB,OAAO,EAAE,IAAI,CAAC,aAAa;4BAC3B,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;yBAC1C,CAAC;wBAGI,UAAU,GAAG;4BACjB,QAAQ,UAAA;4BACR,IAAI,MAAA;yBACL,CAAC;wBAEF,qBAAM,8CAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,EAAE,IAAI,EAAE,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,CAAC,EAAA;;wBAApI,SAAoI,CAAC;wBAErI,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE;4BACnD,IAAI,MAAA;4BACJ,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;4BACvC,SAAS,EAAE,QAAQ,CAAC,MAAM;yBAC3B,CAAC,CAAC;wBAEH,sBAAO,IAAI,EAAC;;;wBAEZ,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,OAAK,CAAC,CAAC;wBAC9D,sBAAO,KAAK,EAAC;;;;;KAEhB;IAED;;OAEG;IACU,4BAAS,GAAtB;0CAKG,OAAO,YAJR,OAAoB,EACpB,IAAY,EACZ,MAAqB,EACrB,OAAgC;;;YADhC,uBAAA,EAAA,WAAqB;YACrB,wBAAA,EAAA,YAAgC;;;;;wBAGd,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBAEnD,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;4BAC3D,sBAAO,IAAI,EAAC;wBACd,CAAC;wBAEK,MAAM,GAAG,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,KAAI,WAAW,CAAC;wBAC1C,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAG7E,QAAQ,GAAG,IAAI,CAAC,iBAAiB,OAAtB,IAAI,iBAAmB,IAAI,EAAE,MAAM,EAAE,SAAS,GAAK,MAAM,SAAC,CAAC;wBAGzD,qBAAM,8CAAiB,CAAC,GAAG,CAG3C,QAAQ,CAAC,EAAA;;wBAHN,UAAU,GAAG,SAGP;wBAEZ,IAAI,CAAC,UAAU,EAAE,CAAC;4BAChB,sBAAO,IAAI,EAAC;wBACd,CAAC;6BAGG,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,EAAvE,wBAAuE;wBACzE,OAAO,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;wBACtF,qBAAM,8CAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAA;;wBAAxC,SAAwC,CAAC;wBACzC,sBAAO,IAAI,EAAC;;wBAIR,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;6BAChE,CAAA,UAAU,CAAC,QAAQ,CAAC,QAAQ,KAAK,gBAAgB,CAAA,EAAjD,wBAAiD;wBACnD,OAAO,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;wBACzF,qBAAM,8CAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAA;;wBAAxC,SAAwC,CAAC;wBACzC,sBAAO,IAAI,EAAC;;wBAGd,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE;4BACtD,IAAI,MAAA;4BACJ,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;4BACvC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,SAAS;yBAChD,CAAC,CAAC;wBAEH,sBAAO,UAAU,CAAC,IAAI,EAAC;;;wBAEvB,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,OAAK,CAAC,CAAC;wBAC9D,sBAAO,IAAI,EAAC;;;;;KAEf;IAED;;OAEG;IACU,+BAAY,GAAzB;0CAKG,OAAO,YAJR,OAAoB,EACpB,IAAY,EACZ,MAAqB,EACrB,OAAgC;;;YADhC,uBAAA,EAAA,WAAqB;YACrB,wBAAA,EAAA,YAAgC;;;;;wBAGd,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBAC7C,MAAM,GAAG,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,KAAI,WAAW,CAAC;wBAC1C,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAE7E,QAAQ,GAAG,IAAI,CAAC,iBAAiB,OAAtB,IAAI,iBAAmB,IAAI,EAAE,MAAM,EAAE,SAAS,GAAK,MAAM,SAAC,CAAC;wBAC5E,qBAAM,8CAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAA;;wBAAxC,SAAwC,CAAC;wBAEzC,sBAAO,IAAI,EAAC;;;wBAEZ,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,OAAK,CAAC,CAAC;wBACjE,sBAAO,KAAK,EAAC;;;;;KAEhB;IAED;;OAEG;IACY,gCAAa,GAA5B,UAA6B,KAAa;QACxC,OAAO,KAAK;aACT,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC;aAC/B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;aACjB,WAAW,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG;IACY,mCAAgB,GAA/B,UAAgC,OAAoB;;QAClD,6CAA6C;QAC7C,IAAM,aAAa,GAAG,CAAA,MAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,0CAAE,KAAK;aACtD,MAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,0CAAE,KAAK,CAAA,CAAC;QAEpF,IAAI,aAAa,EAAE,CAAC;YAClB,yDAAyD;YACzD,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACY,uCAAoB,GAAnC,UAAuC,IAAO;QAC5C,IAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACxC,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACY,mCAAgB,GAA/B,UACE,QAAuB,EACvB,MAAc,EACd,SAAwB,EACxB,OAA2B;QAE3B,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,gBAAgB,IAAI,QAAQ,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6CAA6C;QAC7C,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAC/C,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACU,iCAAc,GAA3B,UAA4B,MAAc;uCAAG,OAAO;;gBAClD,IAAI,CAAC;oBACH,+DAA+D;oBAC/D,wDAAwD;oBACxD,OAAO,CAAC,GAAG,CAAC,kDAAkD,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;oBAEjG,+CAA+C;oBAC/C,yDAAyD;gBAC3D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBAClE,CAAC;;;;KACF;IAxPuB,gCAAa,GAAG,MAAM,CAAC;IACvB,8BAAW,GAAG,IAAI,CAAC,CAAC,SAAS;IAwPvD,yBAAC;CAAA,AA1PD,IA0PC;AA1PY,gDAAkB;AA4P/B,kBAAe,kBAAkB,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/secure-cache-service.ts"],
      sourcesContent: ["/**\n * Secure Cache Service with User Session Validation\n * Prevents cache key collisions and unauthorized access to cached data\n */\n\nimport { NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from './auth';\nimport { consolidatedCache } from './services/consolidated-cache-service';\nimport crypto from 'crypto';\n\ninterface SecureCacheOptions {\n  ttl?: number;\n  requireSession?: boolean;\n  includeSessionId?: boolean;\n  validateUserAccess?: boolean;\n}\n\ninterface CacheMetadata {\n  userId: string;\n  sessionId?: string;\n  timestamp: number;\n  version: string;\n  checksum: string;\n}\n\nexport class SecureCacheService {\n  private static readonly CACHE_VERSION = 'v2.0';\n  private static readonly DEFAULT_TTL = 3600; // 1 hour\n\n  /**\n   * Generate a secure cache key with user isolation and collision prevention\n   */\n  static generateSecureKey(\n    type: string,\n    userId: string,\n    sessionId: string | null,\n    ...params: string[]\n  ): string {\n    // Sanitize all inputs\n    const sanitizedType = this.sanitizeInput(type);\n    const sanitizedUserId = this.sanitizeInput(userId);\n    const sanitizedSessionId = sessionId ? this.sanitizeInput(sessionId) : 'no-session';\n    const sanitizedParams = params.map(p => this.sanitizeInput(p));\n\n    // Create a unique identifier\n    const timestamp = Date.now();\n    const nonce = crypto.randomBytes(4).toString('hex');\n    \n    // Generate checksum for integrity\n    const dataToHash = [sanitizedType, sanitizedUserId, sanitizedSessionId, ...sanitizedParams].join('|');\n    const checksum = crypto.createHash('sha256').update(dataToHash).digest('hex').substring(0, 16);\n\n    return `secure:${this.CACHE_VERSION}:${sanitizedType}:${sanitizedUserId}:${sanitizedSessionId}:${checksum}:${nonce}`;\n  }\n\n  /**\n   * Set data in cache with security metadata\n   */\n  static async setSecure<T>(\n    request: NextRequest,\n    type: string,\n    data: T,\n    params: string[] = [],\n    options: SecureCacheOptions = {}\n  ): Promise<boolean> {\n    try {\n      const session = await getServerSession(authOptions);\n      \n      if (options.requireSession !== false && !session?.user?.id) {\n        console.warn('SecureCache: Attempted to cache data without valid session');\n        return false;\n      }\n\n      const userId = session?.user?.id || 'anonymous';\n      const sessionId = options.includeSessionId ? this.extractSessionId(request) : null;\n      \n      // Generate secure cache key\n      const cacheKey = this.generateSecureKey(type, userId, sessionId, ...params);\n      \n      // Create metadata for validation\n      const metadata: CacheMetadata = {\n        userId,\n        sessionId: sessionId || undefined,\n        timestamp: Date.now(),\n        version: this.CACHE_VERSION,\n        checksum: this.generateDataChecksum(data)\n      };\n\n      // Wrap data with metadata\n      const secureData = {\n        metadata,\n        data\n      };\n\n      await consolidatedCache.set(cacheKey, secureData, { ttl: (options.ttl || this.DEFAULT_TTL) * 1000, tags: ['secure_cache', userId] });\n      \n      console.log('SecureCache: Data cached successfully', {\n        type,\n        userId: userId.substring(0, 10) + '...',\n        keyLength: cacheKey.length\n      });\n\n      return true;\n    } catch (error) {\n      console.error('SecureCache: Failed to set cache data', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get data from cache with security validation\n   */\n  static async getSecure<T>(\n    request: NextRequest,\n    type: string,\n    params: string[] = [],\n    options: SecureCacheOptions = {}\n  ): Promise<T | null> {\n    try {\n      const session = await getServerSession(authOptions);\n      \n      if (options.requireSession !== false && !session?.user?.id) {\n        return null;\n      }\n\n      const userId = session?.user?.id || 'anonymous';\n      const sessionId = options.includeSessionId ? this.extractSessionId(request) : null;\n      \n      // Generate the same secure cache key\n      const cacheKey = this.generateSecureKey(type, userId, sessionId, ...params);\n      \n      // Retrieve cached data\n      const cachedData = await consolidatedCache.get<{\n        metadata: CacheMetadata;\n        data: T;\n      }>(cacheKey);\n\n      if (!cachedData) {\n        return null;\n      }\n\n      // Validate metadata\n      if (!this.validateMetadata(cachedData.metadata, userId, sessionId, options)) {\n        console.warn('SecureCache: Metadata validation failed, removing invalid cache entry');\n        await consolidatedCache.delete(cacheKey);\n        return null;\n      }\n\n      // Validate data integrity\n      const expectedChecksum = this.generateDataChecksum(cachedData.data);\n      if (cachedData.metadata.checksum !== expectedChecksum) {\n        console.warn('SecureCache: Data integrity check failed, removing corrupted cache entry');\n        await consolidatedCache.delete(cacheKey);\n        return null;\n      }\n\n      console.log('SecureCache: Data retrieved successfully', {\n        type,\n        userId: userId.substring(0, 10) + '...',\n        age: Date.now() - cachedData.metadata.timestamp\n      });\n\n      return cachedData.data;\n    } catch (error) {\n      console.error('SecureCache: Failed to get cache data', error);\n      return null;\n    }\n  }\n\n  /**\n   * Delete cached data\n   */\n  static async deleteSecure(\n    request: NextRequest,\n    type: string,\n    params: string[] = [],\n    options: SecureCacheOptions = {}\n  ): Promise<boolean> {\n    try {\n      const session = await getServerSession(authOptions);\n      const userId = session?.user?.id || 'anonymous';\n      const sessionId = options.includeSessionId ? this.extractSessionId(request) : null;\n      \n      const cacheKey = this.generateSecureKey(type, userId, sessionId, ...params);\n      await consolidatedCache.delete(cacheKey);\n      \n      return true;\n    } catch (error) {\n      console.error('SecureCache: Failed to delete cache data', error);\n      return false;\n    }\n  }\n\n  /**\n   * Sanitize input to prevent cache key injection\n   */\n  private static sanitizeInput(input: string): string {\n    return input\n      .replace(/[^a-zA-Z0-9_-]/g, '_')\n      .substring(0, 100)\n      .toLowerCase();\n  }\n\n  /**\n   * Extract session ID from request\n   */\n  private static extractSessionId(request: NextRequest): string | null {\n    // Try to get session ID from various sources\n    const sessionCookie = request.cookies.get('next-auth.session-token')?.value ||\n                         request.cookies.get('__Secure-next-auth.session-token')?.value;\n    \n    if (sessionCookie) {\n      // Use first 16 characters of session token as session ID\n      return crypto.createHash('sha256').update(sessionCookie).digest('hex').substring(0, 16);\n    }\n\n    return null;\n  }\n\n  /**\n   * Generate checksum for data integrity\n   */\n  private static generateDataChecksum<T>(data: T): string {\n    const dataString = JSON.stringify(data);\n    return crypto.createHash('sha256').update(dataString).digest('hex').substring(0, 16);\n  }\n\n  /**\n   * Validate cache metadata\n   */\n  private static validateMetadata(\n    metadata: CacheMetadata,\n    userId: string,\n    sessionId: string | null,\n    options: SecureCacheOptions\n  ): boolean {\n    // Check version compatibility\n    if (metadata.version !== this.CACHE_VERSION) {\n      return false;\n    }\n\n    // Check user access\n    if (options.validateUserAccess !== false && metadata.userId !== userId) {\n      return false;\n    }\n\n    // Check session if required\n    if (options.includeSessionId && metadata.sessionId !== sessionId) {\n      return false;\n    }\n\n    // Check age (max 24 hours regardless of TTL)\n    const maxAge = 24 * 60 * 60 * 1000; // 24 hours\n    if (Date.now() - metadata.timestamp > maxAge) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Clear all cache entries for a user (for logout/security)\n   */\n  static async clearUserCache(userId: string): Promise<void> {\n    try {\n      // This would require a more sophisticated cache implementation\n      // For now, we'll implement this as a future enhancement\n      console.log('SecureCache: User cache clear requested for user', userId.substring(0, 10) + '...');\n      \n      // TODO: Implement pattern-based cache clearing\n      // This would require Redis SCAN or similar functionality\n    } catch (error) {\n      console.error('SecureCache: Failed to clear user cache', error);\n    }\n  }\n}\n\nexport default SecureCacheService;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d521e93df6937aa1e01796569d873811efe069c2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_13l0ggms5q = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_13l0ggms5q();
var __awaiter =
/* istanbul ignore next */
(cov_13l0ggms5q().s[0]++,
/* istanbul ignore next */
(cov_13l0ggms5q().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_13l0ggms5q().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_13l0ggms5q().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_13l0ggms5q().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[1]++;
    cov_13l0ggms5q().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[2]++;
      cov_13l0ggms5q().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_13l0ggms5q().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[4]++;
      cov_13l0ggms5q().s[4]++;
      try {
        /* istanbul ignore next */
        cov_13l0ggms5q().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_13l0ggms5q().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[5]++;
      cov_13l0ggms5q().s[7]++;
      try {
        /* istanbul ignore next */
        cov_13l0ggms5q().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_13l0ggms5q().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[6]++;
      cov_13l0ggms5q().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_13l0ggms5q().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_13l0ggms5q().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_13l0ggms5q().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_13l0ggms5q().s[12]++,
/* istanbul ignore next */
(cov_13l0ggms5q().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_13l0ggms5q().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_13l0ggms5q().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_13l0ggms5q().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_13l0ggms5q().f[8]++;
        cov_13l0ggms5q().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_13l0ggms5q().b[6][0]++;
          cov_13l0ggms5q().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_13l0ggms5q().b[6][1]++;
        }
        cov_13l0ggms5q().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_13l0ggms5q().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[9]++;
    cov_13l0ggms5q().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[10]++;
    cov_13l0ggms5q().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[11]++;
      cov_13l0ggms5q().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[12]++;
    cov_13l0ggms5q().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_13l0ggms5q().b[9][0]++;
      cov_13l0ggms5q().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_13l0ggms5q().b[9][1]++;
    }
    cov_13l0ggms5q().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_13l0ggms5q().s[25]++;
      try {
        /* istanbul ignore next */
        cov_13l0ggms5q().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[15][0]++,
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[16][1]++,
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_13l0ggms5q().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_13l0ggms5q().b[12][0]++;
          cov_13l0ggms5q().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_13l0ggms5q().b[12][1]++;
        }
        cov_13l0ggms5q().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_13l0ggms5q().b[18][0]++;
          cov_13l0ggms5q().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_13l0ggms5q().b[18][1]++;
        }
        cov_13l0ggms5q().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[19][1]++;
            cov_13l0ggms5q().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_13l0ggms5q().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[19][2]++;
            cov_13l0ggms5q().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_13l0ggms5q().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[19][3]++;
            cov_13l0ggms5q().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_13l0ggms5q().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_13l0ggms5q().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_13l0ggms5q().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[19][4]++;
            cov_13l0ggms5q().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[19][5]++;
            cov_13l0ggms5q().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[20][0]++;
              cov_13l0ggms5q().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_13l0ggms5q().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[20][1]++;
            }
            cov_13l0ggms5q().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[23][0]++;
              cov_13l0ggms5q().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_13l0ggms5q().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[23][1]++;
            }
            cov_13l0ggms5q().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[25][0]++;
              cov_13l0ggms5q().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_13l0ggms5q().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_13l0ggms5q().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[25][1]++;
            }
            cov_13l0ggms5q().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[27][0]++;
              cov_13l0ggms5q().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_13l0ggms5q().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_13l0ggms5q().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[27][1]++;
            }
            cov_13l0ggms5q().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[29][0]++;
              cov_13l0ggms5q().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[29][1]++;
            }
            cov_13l0ggms5q().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_13l0ggms5q().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_13l0ggms5q().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_13l0ggms5q().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_13l0ggms5q().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_13l0ggms5q().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_13l0ggms5q().b[30][0]++;
      cov_13l0ggms5q().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_13l0ggms5q().b[30][1]++;
    }
    cov_13l0ggms5q().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_13l0ggms5q().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_13l0ggms5q().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_13l0ggms5q().s[67]++,
/* istanbul ignore next */
(cov_13l0ggms5q().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_13l0ggms5q().b[32][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_13l0ggms5q().b[32][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_13l0ggms5q().f[13]++;
  cov_13l0ggms5q().s[68]++;
  if (
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[34][0]++, pack) ||
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[34][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_13l0ggms5q().b[33][0]++;
    cov_13l0ggms5q().s[69]++;
    for (var i =
      /* istanbul ignore next */
      (cov_13l0ggms5q().s[70]++, 0), l =
      /* istanbul ignore next */
      (cov_13l0ggms5q().s[71]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_13l0ggms5q().s[72]++;
      if (
      /* istanbul ignore next */
      (cov_13l0ggms5q().b[36][0]++, ar) ||
      /* istanbul ignore next */
      (cov_13l0ggms5q().b[36][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_13l0ggms5q().b[35][0]++;
        cov_13l0ggms5q().s[73]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_13l0ggms5q().b[37][0]++;
          cov_13l0ggms5q().s[74]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_13l0ggms5q().b[37][1]++;
        }
        cov_13l0ggms5q().s[75]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_13l0ggms5q().b[35][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_13l0ggms5q().b[33][1]++;
  }
  cov_13l0ggms5q().s[76]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[38][0]++, ar) ||
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[38][1]++, Array.prototype.slice.call(from)));
}));
var __importDefault =
/* istanbul ignore next */
(cov_13l0ggms5q().s[77]++,
/* istanbul ignore next */
(cov_13l0ggms5q().b[39][0]++, this) &&
/* istanbul ignore next */
(cov_13l0ggms5q().b[39][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_13l0ggms5q().b[39][2]++, function (mod) {
  /* istanbul ignore next */
  cov_13l0ggms5q().f[14]++;
  cov_13l0ggms5q().s[78]++;
  return /* istanbul ignore next */(cov_13l0ggms5q().b[41][0]++, mod) &&
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[41][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[40][0]++, mod) :
  /* istanbul ignore next */
  (cov_13l0ggms5q().b[40][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_13l0ggms5q().s[79]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_13l0ggms5q().s[80]++;
exports.SecureCacheService = void 0;
var next_auth_1 =
/* istanbul ignore next */
(cov_13l0ggms5q().s[81]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_13l0ggms5q().s[82]++, require("./auth"));
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_13l0ggms5q().s[83]++, require("./services/consolidated-cache-service"));
var crypto_1 =
/* istanbul ignore next */
(cov_13l0ggms5q().s[84]++, __importDefault(require("crypto")));
var SecureCacheService =
/* istanbul ignore next */
(/** @class */cov_13l0ggms5q().s[85]++, function () {
  /* istanbul ignore next */
  cov_13l0ggms5q().f[15]++;
  function SecureCacheService() {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[16]++;
  }
  /**
   * Generate a secure cache key with user isolation and collision prevention
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[86]++;
  SecureCacheService.generateSecureKey = function (type, userId, sessionId) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[17]++;
    var _this =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[87]++, this);
    var params =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[88]++, []);
    /* istanbul ignore next */
    cov_13l0ggms5q().s[89]++;
    for (var _i =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[90]++, 3); _i < arguments.length; _i++) {
      /* istanbul ignore next */
      cov_13l0ggms5q().s[91]++;
      params[_i - 3] = arguments[_i];
    }
    // Sanitize all inputs
    var sanitizedType =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[92]++, this.sanitizeInput(type));
    var sanitizedUserId =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[93]++, this.sanitizeInput(userId));
    var sanitizedSessionId =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[94]++, sessionId ?
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[42][0]++, this.sanitizeInput(sessionId)) :
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[42][1]++, 'no-session'));
    var sanitizedParams =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[95]++, params.map(function (p) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[18]++;
      cov_13l0ggms5q().s[96]++;
      return _this.sanitizeInput(p);
    }));
    // Create a unique identifier
    var timestamp =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[97]++, Date.now());
    var nonce =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[98]++, crypto_1.default.randomBytes(4).toString('hex'));
    // Generate checksum for integrity
    var dataToHash =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[99]++, __spreadArray([sanitizedType, sanitizedUserId, sanitizedSessionId], sanitizedParams, true).join('|'));
    var checksum =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[100]++, crypto_1.default.createHash('sha256').update(dataToHash).digest('hex').substring(0, 16));
    /* istanbul ignore next */
    cov_13l0ggms5q().s[101]++;
    return "secure:".concat(this.CACHE_VERSION, ":").concat(sanitizedType, ":").concat(sanitizedUserId, ":").concat(sanitizedSessionId, ":").concat(checksum, ":").concat(nonce);
  };
  /**
   * Set data in cache with security metadata
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[102]++;
  SecureCacheService.setSecure = function (request_1, type_1, data_1) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[19]++;
    cov_13l0ggms5q().s[103]++;
    return __awaiter(this, arguments, Promise, function (request, type, data, params, options) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[20]++;
      var session, userId, sessionId, cacheKey, metadata, secureData, error_1;
      var _a, _b;
      /* istanbul ignore next */
      cov_13l0ggms5q().s[104]++;
      if (params === void 0) {
        /* istanbul ignore next */
        cov_13l0ggms5q().b[43][0]++;
        cov_13l0ggms5q().s[105]++;
        params = [];
      } else
      /* istanbul ignore next */
      {
        cov_13l0ggms5q().b[43][1]++;
      }
      cov_13l0ggms5q().s[106]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_13l0ggms5q().b[44][0]++;
        cov_13l0ggms5q().s[107]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_13l0ggms5q().b[44][1]++;
      }
      cov_13l0ggms5q().s[108]++;
      return __generator(this, function (_c) {
        /* istanbul ignore next */
        cov_13l0ggms5q().f[21]++;
        cov_13l0ggms5q().s[109]++;
        switch (_c.label) {
          case 0:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[45][0]++;
            cov_13l0ggms5q().s[110]++;
            _c.trys.push([0, 3,, 4]);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[111]++;
            return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
          case 1:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[45][1]++;
            cov_13l0ggms5q().s[112]++;
            session = _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[113]++;
            if (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[47][0]++, options.requireSession !== false) &&
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[47][1]++, !(
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[49][0]++, (_a =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[51][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[51][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[50][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[50][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[49][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[48][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[48][1]++, _a.id)))) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[46][0]++;
              cov_13l0ggms5q().s[114]++;
              console.warn('SecureCache: Attempted to cache data without valid session');
              /* istanbul ignore next */
              cov_13l0ggms5q().s[115]++;
              return [2 /*return*/, false];
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[46][1]++;
            }
            cov_13l0ggms5q().s[116]++;
            userId =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[52][0]++,
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[54][0]++, (_b =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[56][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[56][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[55][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[55][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[54][1]++, _b === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[53][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[53][1]++, _b.id)) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[52][1]++, 'anonymous');
            /* istanbul ignore next */
            cov_13l0ggms5q().s[117]++;
            sessionId = options.includeSessionId ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[57][0]++, this.extractSessionId(request)) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[57][1]++, null);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[118]++;
            cacheKey = this.generateSecureKey.apply(this, __spreadArray([type, userId, sessionId], params, false));
            /* istanbul ignore next */
            cov_13l0ggms5q().s[119]++;
            metadata = {
              userId: userId,
              sessionId:
              /* istanbul ignore next */
              (cov_13l0ggms5q().b[58][0]++, sessionId) ||
              /* istanbul ignore next */
              (cov_13l0ggms5q().b[58][1]++, undefined),
              timestamp: Date.now(),
              version: this.CACHE_VERSION,
              checksum: this.generateDataChecksum(data)
            };
            /* istanbul ignore next */
            cov_13l0ggms5q().s[120]++;
            secureData = {
              metadata: metadata,
              data: data
            };
            /* istanbul ignore next */
            cov_13l0ggms5q().s[121]++;
            return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.set(cacheKey, secureData, {
              ttl: (
              /* istanbul ignore next */
              (cov_13l0ggms5q().b[59][0]++, options.ttl) ||
              /* istanbul ignore next */
              (cov_13l0ggms5q().b[59][1]++, this.DEFAULT_TTL)) * 1000,
              tags: ['secure_cache', userId]
            })];
          case 2:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[45][2]++;
            cov_13l0ggms5q().s[122]++;
            _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[123]++;
            console.log('SecureCache: Data cached successfully', {
              type: type,
              userId: userId.substring(0, 10) + '...',
              keyLength: cacheKey.length
            });
            /* istanbul ignore next */
            cov_13l0ggms5q().s[124]++;
            return [2 /*return*/, true];
          case 3:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[45][3]++;
            cov_13l0ggms5q().s[125]++;
            error_1 = _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[126]++;
            console.error('SecureCache: Failed to set cache data', error_1);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[127]++;
            return [2 /*return*/, false];
          case 4:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[45][4]++;
            cov_13l0ggms5q().s[128]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Get data from cache with security validation
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[129]++;
  SecureCacheService.getSecure = function (request_1, type_1) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[22]++;
    cov_13l0ggms5q().s[130]++;
    return __awaiter(this, arguments, Promise, function (request, type, params, options) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[23]++;
      var session, userId, sessionId, cacheKey, cachedData, expectedChecksum, error_2;
      var _a, _b;
      /* istanbul ignore next */
      cov_13l0ggms5q().s[131]++;
      if (params === void 0) {
        /* istanbul ignore next */
        cov_13l0ggms5q().b[60][0]++;
        cov_13l0ggms5q().s[132]++;
        params = [];
      } else
      /* istanbul ignore next */
      {
        cov_13l0ggms5q().b[60][1]++;
      }
      cov_13l0ggms5q().s[133]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_13l0ggms5q().b[61][0]++;
        cov_13l0ggms5q().s[134]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_13l0ggms5q().b[61][1]++;
      }
      cov_13l0ggms5q().s[135]++;
      return __generator(this, function (_c) {
        /* istanbul ignore next */
        cov_13l0ggms5q().f[24]++;
        cov_13l0ggms5q().s[136]++;
        switch (_c.label) {
          case 0:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][0]++;
            cov_13l0ggms5q().s[137]++;
            _c.trys.push([0, 7,, 8]);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[138]++;
            return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
          case 1:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][1]++;
            cov_13l0ggms5q().s[139]++;
            session = _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[140]++;
            if (
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[64][0]++, options.requireSession !== false) &&
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[64][1]++, !(
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[66][0]++, (_a =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[68][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[68][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[67][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[67][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[66][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[65][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[65][1]++, _a.id)))) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[63][0]++;
              cov_13l0ggms5q().s[141]++;
              return [2 /*return*/, null];
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[63][1]++;
            }
            cov_13l0ggms5q().s[142]++;
            userId =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[69][0]++,
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[71][0]++, (_b =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[73][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[73][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[72][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[72][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[71][1]++, _b === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[70][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[70][1]++, _b.id)) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[69][1]++, 'anonymous');
            /* istanbul ignore next */
            cov_13l0ggms5q().s[143]++;
            sessionId = options.includeSessionId ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[74][0]++, this.extractSessionId(request)) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[74][1]++, null);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[144]++;
            cacheKey = this.generateSecureKey.apply(this, __spreadArray([type, userId, sessionId], params, false));
            /* istanbul ignore next */
            cov_13l0ggms5q().s[145]++;
            return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.get(cacheKey)];
          case 2:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][2]++;
            cov_13l0ggms5q().s[146]++;
            cachedData = _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[147]++;
            if (!cachedData) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[75][0]++;
              cov_13l0ggms5q().s[148]++;
              return [2 /*return*/, null];
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[75][1]++;
            }
            cov_13l0ggms5q().s[149]++;
            if (!!this.validateMetadata(cachedData.metadata, userId, sessionId, options)) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[76][0]++;
              cov_13l0ggms5q().s[150]++;
              return [3 /*break*/, 4];
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[76][1]++;
            }
            cov_13l0ggms5q().s[151]++;
            console.warn('SecureCache: Metadata validation failed, removing invalid cache entry');
            /* istanbul ignore next */
            cov_13l0ggms5q().s[152]++;
            return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.delete(cacheKey)];
          case 3:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][3]++;
            cov_13l0ggms5q().s[153]++;
            _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[154]++;
            return [2 /*return*/, null];
          case 4:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][4]++;
            cov_13l0ggms5q().s[155]++;
            expectedChecksum = this.generateDataChecksum(cachedData.data);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[156]++;
            if (!(cachedData.metadata.checksum !== expectedChecksum)) {
              /* istanbul ignore next */
              cov_13l0ggms5q().b[77][0]++;
              cov_13l0ggms5q().s[157]++;
              return [3 /*break*/, 6];
            } else
            /* istanbul ignore next */
            {
              cov_13l0ggms5q().b[77][1]++;
            }
            cov_13l0ggms5q().s[158]++;
            console.warn('SecureCache: Data integrity check failed, removing corrupted cache entry');
            /* istanbul ignore next */
            cov_13l0ggms5q().s[159]++;
            return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.delete(cacheKey)];
          case 5:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][5]++;
            cov_13l0ggms5q().s[160]++;
            _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[161]++;
            return [2 /*return*/, null];
          case 6:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][6]++;
            cov_13l0ggms5q().s[162]++;
            console.log('SecureCache: Data retrieved successfully', {
              type: type,
              userId: userId.substring(0, 10) + '...',
              age: Date.now() - cachedData.metadata.timestamp
            });
            /* istanbul ignore next */
            cov_13l0ggms5q().s[163]++;
            return [2 /*return*/, cachedData.data];
          case 7:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][7]++;
            cov_13l0ggms5q().s[164]++;
            error_2 = _c.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[165]++;
            console.error('SecureCache: Failed to get cache data', error_2);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[166]++;
            return [2 /*return*/, null];
          case 8:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[62][8]++;
            cov_13l0ggms5q().s[167]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Delete cached data
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[168]++;
  SecureCacheService.deleteSecure = function (request_1, type_1) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[25]++;
    cov_13l0ggms5q().s[169]++;
    return __awaiter(this, arguments, Promise, function (request, type, params, options) {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[26]++;
      var session, userId, sessionId, cacheKey, error_3;
      var _a;
      /* istanbul ignore next */
      cov_13l0ggms5q().s[170]++;
      if (params === void 0) {
        /* istanbul ignore next */
        cov_13l0ggms5q().b[78][0]++;
        cov_13l0ggms5q().s[171]++;
        params = [];
      } else
      /* istanbul ignore next */
      {
        cov_13l0ggms5q().b[78][1]++;
      }
      cov_13l0ggms5q().s[172]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_13l0ggms5q().b[79][0]++;
        cov_13l0ggms5q().s[173]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_13l0ggms5q().b[79][1]++;
      }
      cov_13l0ggms5q().s[174]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_13l0ggms5q().f[27]++;
        cov_13l0ggms5q().s[175]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[80][0]++;
            cov_13l0ggms5q().s[176]++;
            _b.trys.push([0, 3,, 4]);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[177]++;
            return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
          case 1:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[80][1]++;
            cov_13l0ggms5q().s[178]++;
            session = _b.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[179]++;
            userId =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[81][0]++,
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[83][0]++, (_a =
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[85][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[85][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[84][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[84][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[83][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[82][0]++, void 0) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[82][1]++, _a.id)) ||
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[81][1]++, 'anonymous');
            /* istanbul ignore next */
            cov_13l0ggms5q().s[180]++;
            sessionId = options.includeSessionId ?
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[86][0]++, this.extractSessionId(request)) :
            /* istanbul ignore next */
            (cov_13l0ggms5q().b[86][1]++, null);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[181]++;
            cacheKey = this.generateSecureKey.apply(this, __spreadArray([type, userId, sessionId], params, false));
            /* istanbul ignore next */
            cov_13l0ggms5q().s[182]++;
            return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.delete(cacheKey)];
          case 2:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[80][2]++;
            cov_13l0ggms5q().s[183]++;
            _b.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[184]++;
            return [2 /*return*/, true];
          case 3:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[80][3]++;
            cov_13l0ggms5q().s[185]++;
            error_3 = _b.sent();
            /* istanbul ignore next */
            cov_13l0ggms5q().s[186]++;
            console.error('SecureCache: Failed to delete cache data', error_3);
            /* istanbul ignore next */
            cov_13l0ggms5q().s[187]++;
            return [2 /*return*/, false];
          case 4:
            /* istanbul ignore next */
            cov_13l0ggms5q().b[80][4]++;
            cov_13l0ggms5q().s[188]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Sanitize input to prevent cache key injection
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[189]++;
  SecureCacheService.sanitizeInput = function (input) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[28]++;
    cov_13l0ggms5q().s[190]++;
    return input.replace(/[^a-zA-Z0-9_-]/g, '_').substring(0, 100).toLowerCase();
  };
  /**
   * Extract session ID from request
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[191]++;
  SecureCacheService.extractSessionId = function (request) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[29]++;
    var _a, _b;
    // Try to get session ID from various sources
    var sessionCookie =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[192]++,
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[87][0]++,
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[89][0]++, (_a = request.cookies.get('next-auth.session-token')) === null) ||
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[89][1]++, _a === void 0) ?
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[88][0]++, void 0) :
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[88][1]++, _a.value)) ||
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[87][1]++,
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[91][0]++, (_b = request.cookies.get('__Secure-next-auth.session-token')) === null) ||
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[91][1]++, _b === void 0) ?
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[90][0]++, void 0) :
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[90][1]++, _b.value)));
    /* istanbul ignore next */
    cov_13l0ggms5q().s[193]++;
    if (sessionCookie) {
      /* istanbul ignore next */
      cov_13l0ggms5q().b[92][0]++;
      cov_13l0ggms5q().s[194]++;
      // Use first 16 characters of session token as session ID
      return crypto_1.default.createHash('sha256').update(sessionCookie).digest('hex').substring(0, 16);
    } else
    /* istanbul ignore next */
    {
      cov_13l0ggms5q().b[92][1]++;
    }
    cov_13l0ggms5q().s[195]++;
    return null;
  };
  /**
   * Generate checksum for data integrity
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[196]++;
  SecureCacheService.generateDataChecksum = function (data) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[30]++;
    var dataString =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[197]++, JSON.stringify(data));
    /* istanbul ignore next */
    cov_13l0ggms5q().s[198]++;
    return crypto_1.default.createHash('sha256').update(dataString).digest('hex').substring(0, 16);
  };
  /**
   * Validate cache metadata
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[199]++;
  SecureCacheService.validateMetadata = function (metadata, userId, sessionId, options) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[31]++;
    cov_13l0ggms5q().s[200]++;
    // Check version compatibility
    if (metadata.version !== this.CACHE_VERSION) {
      /* istanbul ignore next */
      cov_13l0ggms5q().b[93][0]++;
      cov_13l0ggms5q().s[201]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_13l0ggms5q().b[93][1]++;
    }
    // Check user access
    cov_13l0ggms5q().s[202]++;
    if (
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[95][0]++, options.validateUserAccess !== false) &&
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[95][1]++, metadata.userId !== userId)) {
      /* istanbul ignore next */
      cov_13l0ggms5q().b[94][0]++;
      cov_13l0ggms5q().s[203]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_13l0ggms5q().b[94][1]++;
    }
    // Check session if required
    cov_13l0ggms5q().s[204]++;
    if (
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[97][0]++, options.includeSessionId) &&
    /* istanbul ignore next */
    (cov_13l0ggms5q().b[97][1]++, metadata.sessionId !== sessionId)) {
      /* istanbul ignore next */
      cov_13l0ggms5q().b[96][0]++;
      cov_13l0ggms5q().s[205]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_13l0ggms5q().b[96][1]++;
    }
    // Check age (max 24 hours regardless of TTL)
    var maxAge =
    /* istanbul ignore next */
    (cov_13l0ggms5q().s[206]++, 24 * 60 * 60 * 1000); // 24 hours
    /* istanbul ignore next */
    cov_13l0ggms5q().s[207]++;
    if (Date.now() - metadata.timestamp > maxAge) {
      /* istanbul ignore next */
      cov_13l0ggms5q().b[98][0]++;
      cov_13l0ggms5q().s[208]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_13l0ggms5q().b[98][1]++;
    }
    cov_13l0ggms5q().s[209]++;
    return true;
  };
  /**
   * Clear all cache entries for a user (for logout/security)
   */
  /* istanbul ignore next */
  cov_13l0ggms5q().s[210]++;
  SecureCacheService.clearUserCache = function (userId) {
    /* istanbul ignore next */
    cov_13l0ggms5q().f[32]++;
    cov_13l0ggms5q().s[211]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_13l0ggms5q().f[33]++;
      cov_13l0ggms5q().s[212]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_13l0ggms5q().f[34]++;
        cov_13l0ggms5q().s[213]++;
        try {
          /* istanbul ignore next */
          cov_13l0ggms5q().s[214]++;
          // This would require a more sophisticated cache implementation
          // For now, we'll implement this as a future enhancement
          console.log('SecureCache: User cache clear requested for user', userId.substring(0, 10) + '...');
          // TODO: Implement pattern-based cache clearing
          // This would require Redis SCAN or similar functionality
        } catch (error) {
          /* istanbul ignore next */
          cov_13l0ggms5q().s[215]++;
          console.error('SecureCache: Failed to clear user cache', error);
        }
        /* istanbul ignore next */
        cov_13l0ggms5q().s[216]++;
        return [2 /*return*/];
      });
    });
  };
  /* istanbul ignore next */
  cov_13l0ggms5q().s[217]++;
  SecureCacheService.CACHE_VERSION = 'v2.0';
  /* istanbul ignore next */
  cov_13l0ggms5q().s[218]++;
  SecureCacheService.DEFAULT_TTL = 3600; // 1 hour
  /* istanbul ignore next */
  cov_13l0ggms5q().s[219]++;
  return SecureCacheService;
}());
/* istanbul ignore next */
cov_13l0ggms5q().s[220]++;
exports.SecureCacheService = SecureCacheService;
/* istanbul ignore next */
cov_13l0ggms5q().s[221]++;
exports.default = SecureCacheService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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