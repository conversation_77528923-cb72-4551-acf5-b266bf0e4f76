d37069480d21822d79b8d4e0e33f46a8
// Polyfills for Next.js 15 and Jest environment
const { TextEncoder, TextDecoder } = require('util');

// Web APIs polyfills
if (!global.TextEncoder) {
  global.TextEncoder = TextEncoder;
}

if (!global.TextDecoder) {
  global.TextDecoder = TextDecoder;
}

// Use Node.js built-in URL and URLSearchParams
const { URL: NodeURL, URLSearchParams: NodeURLSearchParams } = require('url');
global.URL = NodeURL;
global.URLSearchParams = NodeURLSearchParams;

// Fetch API polyfill for tests
if (!global.fetch) {
  global.fetch = jest.fn();
}

// Request/Response polyfills compatible with Next.js 15
if (!global.Request) {
  global.Request = class Request {
    constructor(input, init = {}) {
      // Use defineProperty to handle read-only url property in NextRequest
      const url = typeof input === 'string' ? input : input.url;
      Object.defineProperty(this, 'url', {
        value: url,
        writable: false,
        enumerable: true,
        configurable: true
      });

      this.method = init.method || 'GET';
      this.headers = new Headers(init.headers || {});
      this.body = init.body;
      this._bodyInit = init.body;
      this.cache = init.cache || 'default';
      this.credentials = init.credentials || 'same-origin';
      this.mode = init.mode || 'cors';
      this.redirect = init.redirect || 'follow';
      this.referrer = init.referrer || '';
      this.referrerPolicy = init.referrerPolicy || '';
    }

    async json() {
      if (typeof this._bodyInit === 'string') {
        return JSON.parse(this._bodyInit);
      }
      return this._bodyInit;
    }

    async text() {
      return this._bodyInit || '';
    }

    async arrayBuffer() {
      return new ArrayBuffer(0);
    }

    async blob() {
      return new Blob([this._bodyInit || '']);
    }

    clone() {
      return new Request(this.url, {
        method: this.method,
        headers: this.headers,
        body: this.body,
        cache: this.cache,
        credentials: this.credentials,
        mode: this.mode,
        redirect: this.redirect,
        referrer: this.referrer,
        referrerPolicy: this.referrerPolicy,
      });
    }
  };
}

if (!global.Response) {
  global.Response = class Response {
    constructor(body, init = {}) {
      this.body = body;
      this.status = init.status || 200;
      this.statusText = init.statusText || 'OK';
      this.headers = new Headers(init.headers || {});
      this.ok = this.status >= 200 && this.status < 300;
      this.redirected = false;
      this.type = 'basic';
      this.url = '';
    }

    async json() {
      if (typeof this.body === 'string') {
        return JSON.parse(this.body);
      }
      return this.body;
    }

    async text() {
      return this.body || '';
    }

    async arrayBuffer() {
      return new ArrayBuffer(0);
    }

    async blob() {
      return new Blob([this.body || '']);
    }

    clone() {
      return new Response(this.body, {
        status: this.status,
        statusText: this.statusText,
        headers: this.headers,
      });
    }

    static json(data, init = {}) {
      return new Response(JSON.stringify(data), {
        ...init,
        headers: {
          'Content-Type': 'application/json',
          ...init.headers,
        },
      });
    }

    static redirect(url, status = 302) {
      return new Response(null, {
        status,
        headers: {
          Location: url,
        },
      });
    }
  };
}

if (!global.Headers) {
  global.Headers = class Headers extends Map {
    constructor(init) {
      super();
      if (init) {
        if (Array.isArray(init)) {
          init.forEach(([key, value]) => this.set(key, value));
        } else if (typeof init === 'object') {
          Object.entries(init).forEach(([key, value]) => this.set(key, value));
        }
      }
    }

    get(name) {
      return super.get(name.toLowerCase());
    }

    set(name, value) {
      return super.set(name.toLowerCase(), String(value));
    }

    has(name) {
      return super.has(name.toLowerCase());
    }

    delete(name) {
      return super.delete(name.toLowerCase());
    }

    append(name, value) {
      const existing = this.get(name);
      if (existing) {
        this.set(name, `${existing}, ${value}`);
      } else {
        this.set(name, value);
      }
    }

    forEach(callback, thisArg) {
      for (const [key, value] of this) {
        callback.call(thisArg, value, key, this);
      }
    }

    keys() {
      return super.keys();
    }

    values() {
      return super.values();
    }

    entries() {
      return super.entries();
    }

    [Symbol.iterator]() {
      return super.entries();
    }
  };
}

// Crypto API polyfill
if (!global.crypto) {
  const { webcrypto } = require('crypto');
  global.crypto = webcrypto || {
    getRandomValues: (arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
    randomUUID: () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    subtle: {
      digest: async (algorithm, data) => {
        // Simple hash implementation for testing
        const crypto = require('crypto');
        const hash = crypto.createHash('sha256');
        hash.update(data);
        const buffer = hash.digest();
        // Convert Buffer to ArrayBuffer for compatibility
        return buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength);
      }
    }
  };
}

// Ensure crypto.subtle exists even if webcrypto is available but incomplete
if (global.crypto && !global.crypto.subtle) {
  global.crypto.subtle = {
    digest: async (algorithm, data) => {
      const crypto = require('crypto');
      const hash = crypto.createHash('sha256');
      hash.update(data);
      const buffer = hash.digest();
      // Convert Buffer to ArrayBuffer for compatibility
      return buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength);
    }
  };
}

// Performance API polyfill
if (!global.performance) {
  global.performance = {
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
  };
}

// AbortController polyfill
if (!global.AbortController) {
  global.AbortController = class AbortController {
    constructor() {
      this.signal = {
        aborted: false,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      };
    }
    abort() {
      this.signal.aborted = true;
    }
  };
}

// FormData polyfill
if (!global.FormData) {
  global.FormData = class FormData {
    constructor() {
      this._data = new Map();
    }
    append(name, value) {
      this._data.set(name, value);
    }
    get(name) {
      return this._data.get(name);
    }
    has(name) {
      return this._data.has(name);
    }
    delete(name) {
      this._data.delete(name);
    }
    entries() {
      return this._data.entries();
    }
  };
}

// Blob polyfill
if (!global.Blob) {
  global.Blob = class Blob {
    constructor(parts = [], options = {}) {
      this.parts = parts;
      this.type = options.type || '';
      this.size = parts.reduce((size, part) => size + (part.length || 0), 0);
    }

    async text() {
      return this.parts.join('');
    }

    async arrayBuffer() {
      return new ArrayBuffer(this.size);
    }
  };
}

// File polyfill
if (!global.File) {
  global.File = class File extends global.Blob {
    constructor(parts, name, options = {}) {
      super(parts, options);
      this.name = name;
      this.lastModified = options.lastModified || Date.now();
    }
  };
}

// Mock console methods to reduce noise in tests (optional)
if (process.env.NODE_ENV === 'test') {
  const originalConsole = global.console;
  global.console = {
    ...originalConsole,
    // Keep error and warn for debugging
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
  };
}
