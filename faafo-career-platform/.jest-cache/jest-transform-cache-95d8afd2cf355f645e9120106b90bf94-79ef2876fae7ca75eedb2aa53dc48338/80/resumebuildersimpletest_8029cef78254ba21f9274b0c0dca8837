00899eea40b2c3e1e241378f0f2e5fb7
"use strict";
/**
 * Simple Resume Builder Tests
 *
 * Basic tests for Resume Builder functionality without complex dependencies
 */
Object.defineProperty(exports, "__esModule", { value: true });
var zod_1 = require("zod");
// Define the validation schemas (copied from API route)
var personalInfoSchema = zod_1.z.object({
    firstName: zod_1.z.string().min(1, 'First name is required'),
    lastName: zod_1.z.string().min(1, 'Last name is required'),
    email: zod_1.z.string().email('Valid email is required'),
    phone: zod_1.z.string().optional(),
    location: zod_1.z.string().optional(),
    website: zod_1.z.string().url().optional().or(zod_1.z.literal('')),
    linkedIn: zod_1.z.string().url().optional().or(zod_1.z.literal(''))
});
var experienceSchema = zod_1.z.object({
    company: zod_1.z.string().min(1, 'Company name is required'),
    position: zod_1.z.string().min(1, 'Position is required'),
    startDate: zod_1.z.string().min(1, 'Start date is required'),
    endDate: zod_1.z.string().optional(),
    description: zod_1.z.string().optional(),
    achievements: zod_1.z.array(zod_1.z.string()).optional()
});
var educationSchema = zod_1.z.object({
    institution: zod_1.z.string().min(1, 'Institution is required'),
    degree: zod_1.z.string().min(1, 'Degree is required'),
    field: zod_1.z.string().optional(),
    startDate: zod_1.z.string().optional(),
    endDate: zod_1.z.string().optional(),
    gpa: zod_1.z.string().optional(),
    honors: zod_1.z.string().optional()
});
var skillSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Skill name is required'),
    level: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
    category: zod_1.z.string().optional()
});
var resumeValidationSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Resume title is required'),
    personalInfo: personalInfoSchema,
    summary: zod_1.z.string().optional(),
    experience: zod_1.z.array(experienceSchema).optional(),
    education: zod_1.z.array(educationSchema).optional(),
    skills: zod_1.z.array(skillSchema).optional(),
    sections: zod_1.z.record(zod_1.z.any()).optional(),
    template: zod_1.z.enum(['modern', 'classic', 'minimal', 'creative']).default('modern'),
    isPublic: zod_1.z.boolean().default(false)
});
describe('Resume Builder - Simple Tests', function () {
    describe('Resume Validation Schema', function () {
        it('should validate a complete resume object', function () {
            var validResume = {
                title: 'Software Engineer Resume',
                personalInfo: {
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    phone: '******-0123',
                    location: 'San Francisco, CA',
                    website: 'https://johndoe.com',
                    linkedIn: 'https://linkedin.com/in/johndoe'
                },
                summary: 'Experienced software engineer with 5+ years of experience.',
                experience: [
                    {
                        company: 'Tech Corp',
                        position: 'Senior Software Engineer',
                        startDate: '2020-01',
                        endDate: '2023-12',
                        description: 'Led development of web applications.',
                        achievements: ['Improved performance by 40%', 'Mentored 3 developers']
                    }
                ],
                education: [
                    {
                        institution: 'University of California',
                        degree: 'Bachelor of Science',
                        field: 'Computer Science',
                        startDate: '2016-09',
                        endDate: '2020-05',
                        gpa: '3.8'
                    }
                ],
                skills: [
                    {
                        name: 'JavaScript',
                        level: 'ADVANCED',
                        category: 'Programming Languages'
                    }
                ],
                template: 'modern',
                isPublic: false
            };
            var result = resumeValidationSchema.safeParse(validResume);
            expect(result.success).toBe(true);
        });
        it('should reject resume with missing required fields', function () {
            var invalidResume = {
                // Missing title
                personalInfo: {
                    firstName: 'John',
                    lastName: 'Doe',
                    email: 'invalid-email' // Invalid email format
                },
                experience: [],
                education: [],
                skills: []
            };
            var result = resumeValidationSchema.safeParse(invalidResume);
            expect(result.success).toBe(false);
            if (!result.success) {
                var errors = result.error.issues;
                expect(errors.some(function (error) { return error.path.includes('title'); })).toBe(true);
                expect(errors.some(function (error) { return error.path.includes('email'); })).toBe(true);
            }
        });
        it('should validate personal info fields correctly', function () {
            var validPersonalInfo = {
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
                phone: '******-0456',
                location: 'New York, NY',
                website: 'https://janesmith.dev',
                linkedIn: 'https://linkedin.com/in/janesmith'
            };
            // Test as part of a minimal resume
            var resume = {
                title: 'Test Resume',
                personalInfo: validPersonalInfo,
                experience: [],
                education: [],
                skills: []
            };
            var result = resumeValidationSchema.safeParse(resume);
            expect(result.success).toBe(true);
        });
        it('should validate experience entries correctly', function () {
            var validExperience = [
                {
                    company: 'StartupCo',
                    position: 'Full Stack Developer',
                    startDate: '2021-06',
                    endDate: '2023-12',
                    description: 'Developed web applications using React and Node.js.',
                    achievements: [
                        'Built authentication system for 10,000+ users',
                        'Reduced page load times by 60%'
                    ]
                },
                {
                    company: 'Current Company',
                    position: 'Senior Developer',
                    startDate: '2024-01',
                    // No end date for current position
                    description: 'Leading frontend development team.'
                }
            ];
            var resume = {
                title: 'Developer Resume',
                personalInfo: {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>'
                },
                experience: validExperience,
                education: [],
                skills: []
            };
            var result = resumeValidationSchema.safeParse(resume);
            expect(result.success).toBe(true);
        });
        it('should validate education entries correctly', function () {
            var validEducation = [
                {
                    institution: 'State University',
                    degree: 'Bachelor of Science',
                    field: 'Computer Science',
                    startDate: '2017-09',
                    endDate: '2021-05',
                    gpa: '3.7',
                    honors: 'Magna Cum Laude'
                },
                {
                    institution: 'Online Academy',
                    degree: 'Certificate',
                    field: 'Web Development',
                    startDate: '2021-01',
                    endDate: '2021-03'
                }
            ];
            var resume = {
                title: 'Graduate Resume',
                personalInfo: {
                    firstName: 'Test',
                    lastName: 'Graduate',
                    email: '<EMAIL>'
                },
                experience: [],
                education: validEducation,
                skills: []
            };
            var result = resumeValidationSchema.safeParse(resume);
            expect(result.success).toBe(true);
        });
        it('should validate skills entries correctly', function () {
            var validSkills = [
                {
                    name: 'React',
                    level: 'ADVANCED',
                    category: 'Frontend Frameworks'
                },
                {
                    name: 'Node.js',
                    level: 'INTERMEDIATE',
                    category: 'Backend Technologies'
                },
                {
                    name: 'PostgreSQL',
                    level: 'INTERMEDIATE',
                    category: 'Databases'
                },
                {
                    name: 'Communication',
                    level: 'ADVANCED',
                    category: 'Soft Skills'
                }
            ];
            var resume = {
                title: 'Skills-focused Resume',
                personalInfo: {
                    firstName: 'Skilled',
                    lastName: 'Developer',
                    email: '<EMAIL>'
                },
                experience: [],
                education: [],
                skills: validSkills
            };
            var result = resumeValidationSchema.safeParse(resume);
            expect(result.success).toBe(true);
        });
        it('should validate template options', function () {
            var templates = ['modern', 'classic', 'minimal', 'creative'];
            templates.forEach(function (template) {
                var resume = {
                    title: "".concat(template, " Resume"),
                    personalInfo: {
                        firstName: 'Test',
                        lastName: 'User',
                        email: '<EMAIL>'
                    },
                    experience: [],
                    education: [],
                    skills: [],
                    template: template
                };
                var result = resumeValidationSchema.safeParse(resume);
                expect(result.success).toBe(true);
            });
        });
        it('should reject invalid template options', function () {
            var resume = {
                title: 'Invalid Template Resume',
                personalInfo: {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>'
                },
                experience: [],
                education: [],
                skills: [],
                template: 'invalid-template'
            };
            var result = resumeValidationSchema.safeParse(resume);
            expect(result.success).toBe(false);
        });
        it('should validate skill levels correctly', function () {
            var validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
            validLevels.forEach(function (level) {
                var resume = {
                    title: 'Skill Level Test',
                    personalInfo: {
                        firstName: 'Test',
                        lastName: 'User',
                        email: '<EMAIL>'
                    },
                    experience: [],
                    education: [],
                    skills: [
                        {
                            name: 'Test Skill',
                            level: level,
                            category: 'Test Category'
                        }
                    ]
                };
                var result = resumeValidationSchema.safeParse(resume);
                expect(result.success).toBe(true);
            });
        });
        it('should handle optional fields correctly', function () {
            var minimalResume = {
                title: 'Minimal Resume',
                personalInfo: {
                    firstName: 'Min',
                    lastName: 'User',
                    email: '<EMAIL>'
                },
                experience: [],
                education: [],
                skills: []
            };
            var result = resumeValidationSchema.safeParse(minimalResume);
            expect(result.success).toBe(true);
        });
        it('should validate URL formats for website and LinkedIn', function () {
            var resumeWithUrls = {
                title: 'URL Test Resume',
                personalInfo: {
                    firstName: 'URL',
                    lastName: 'Tester',
                    email: '<EMAIL>',
                    website: 'https://example.com',
                    linkedIn: 'https://linkedin.com/in/urltester'
                },
                experience: [],
                education: [],
                skills: []
            };
            var result = resumeValidationSchema.safeParse(resumeWithUrls);
            expect(result.success).toBe(true);
        });
        it('should reject invalid URL formats', function () {
            var resumeWithInvalidUrls = {
                title: 'Invalid URL Resume',
                personalInfo: {
                    firstName: 'Invalid',
                    lastName: 'URL',
                    email: '<EMAIL>',
                    website: 'not-a-url',
                    linkedIn: 'also-not-a-url'
                },
                experience: [],
                education: [],
                skills: []
            };
            var result = resumeValidationSchema.safeParse(resumeWithInvalidUrls);
            expect(result.success).toBe(false);
            if (!result.success) {
                var errors = result.error.issues;
                expect(errors.some(function (error) { return error.path.includes('website'); })).toBe(true);
                expect(errors.some(function (error) { return error.path.includes('linkedIn'); })).toBe(true);
            }
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL19fdGVzdHNfXy91bml0L2NvbXBvbmVudHMvcmVzdW1lLWJ1aWxkZXItc2ltcGxlLnRlc3QudHMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7O0dBSUc7O0FBRUgsMkJBQXdCO0FBRXhCLHdEQUF3RDtBQUN4RCxJQUFNLGtCQUFrQixHQUFHLE9BQUMsQ0FBQyxNQUFNLENBQUM7SUFDbEMsU0FBUyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLHdCQUF3QixDQUFDO0lBQ3RELFFBQVEsRUFBRSxPQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSx1QkFBdUIsQ0FBQztJQUNwRCxLQUFLLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLEtBQUssQ0FBQyx5QkFBeUIsQ0FBQztJQUNsRCxLQUFLLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsRUFBRTtJQUM1QixRQUFRLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsRUFBRTtJQUMvQixPQUFPLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLFFBQVEsRUFBRSxDQUFDLEVBQUUsQ0FBQyxPQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxDQUFDO0lBQ3RELFFBQVEsRUFBRSxPQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsUUFBUSxFQUFFLENBQUMsRUFBRSxDQUFDLE9BQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDLENBQUM7Q0FDeEQsQ0FBQyxDQUFDO0FBRUgsSUFBTSxnQkFBZ0IsR0FBRyxPQUFDLENBQUMsTUFBTSxDQUFDO0lBQ2hDLE9BQU8sRUFBRSxPQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSwwQkFBMEIsQ0FBQztJQUN0RCxRQUFRLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsc0JBQXNCLENBQUM7SUFDbkQsU0FBUyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLHdCQUF3QixDQUFDO0lBQ3RELE9BQU8sRUFBRSxPQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxFQUFFO0lBQzlCLFdBQVcsRUFBRSxPQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxFQUFFO0lBQ2xDLFlBQVksRUFBRSxPQUFDLENBQUMsS0FBSyxDQUFDLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRTtDQUM3QyxDQUFDLENBQUM7QUFFSCxJQUFNLGVBQWUsR0FBRyxPQUFDLENBQUMsTUFBTSxDQUFDO0lBQy9CLFdBQVcsRUFBRSxPQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSx5QkFBeUIsQ0FBQztJQUN6RCxNQUFNLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsb0JBQW9CLENBQUM7SUFDL0MsS0FBSyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLEVBQUU7SUFDNUIsU0FBUyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLEVBQUU7SUFDaEMsT0FBTyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLEVBQUU7SUFDOUIsR0FBRyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLEVBQUU7SUFDMUIsTUFBTSxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLEVBQUU7Q0FDOUIsQ0FBQyxDQUFDO0FBRUgsSUFBTSxXQUFXLEdBQUcsT0FBQyxDQUFDLE1BQU0sQ0FBQztJQUMzQixJQUFJLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsd0JBQXdCLENBQUM7SUFDakQsS0FBSyxFQUFFLE9BQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxVQUFVLEVBQUUsY0FBYyxFQUFFLFVBQVUsRUFBRSxRQUFRLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRTtJQUM1RSxRQUFRLEVBQUUsT0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsRUFBRTtDQUNoQyxDQUFDLENBQUM7QUFFSCxJQUFNLHNCQUFzQixHQUFHLE9BQUMsQ0FBQyxNQUFNLENBQUM7SUFDdEMsS0FBSyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLDBCQUEwQixDQUFDO0lBQ3BELFlBQVksRUFBRSxrQkFBa0I7SUFDaEMsT0FBTyxFQUFFLE9BQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLEVBQUU7SUFDOUIsVUFBVSxFQUFFLE9BQUMsQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxRQUFRLEVBQUU7SUFDaEQsU0FBUyxFQUFFLE9BQUMsQ0FBQyxLQUFLLENBQUMsZUFBZSxDQUFDLENBQUMsUUFBUSxFQUFFO0lBQzlDLE1BQU0sRUFBRSxPQUFDLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFDLFFBQVEsRUFBRTtJQUN2QyxRQUFRLEVBQUUsT0FBQyxDQUFDLE1BQU0sQ0FBQyxPQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQyxRQUFRLEVBQUU7SUFDdEMsUUFBUSxFQUFFLE9BQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxRQUFRLEVBQUUsU0FBUyxFQUFFLFNBQVMsRUFBRSxVQUFVLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUM7SUFDaEYsUUFBUSxFQUFFLE9BQUMsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDO0NBQ3JDLENBQUMsQ0FBQztBQUVILFFBQVEsQ0FBQywrQkFBK0IsRUFBRTtJQUN4QyxRQUFRLENBQUMsMEJBQTBCLEVBQUU7UUFDbkMsRUFBRSxDQUFDLDBDQUEwQyxFQUFFO1lBQzdDLElBQU0sV0FBVyxHQUFHO2dCQUNsQixLQUFLLEVBQUUsMEJBQTBCO2dCQUNqQyxZQUFZLEVBQUU7b0JBQ1osU0FBUyxFQUFFLE1BQU07b0JBQ2pCLFFBQVEsRUFBRSxLQUFLO29CQUNmLEtBQUssRUFBRSxzQkFBc0I7b0JBQzdCLEtBQUssRUFBRSxhQUFhO29CQUNwQixRQUFRLEVBQUUsbUJBQW1CO29CQUM3QixPQUFPLEVBQUUscUJBQXFCO29CQUM5QixRQUFRLEVBQUUsaUNBQWlDO2lCQUM1QztnQkFDRCxPQUFPLEVBQUUsNERBQTREO2dCQUNyRSxVQUFVLEVBQUU7b0JBQ1Y7d0JBQ0UsT0FBTyxFQUFFLFdBQVc7d0JBQ3BCLFFBQVEsRUFBRSwwQkFBMEI7d0JBQ3BDLFNBQVMsRUFBRSxTQUFTO3dCQUNwQixPQUFPLEVBQUUsU0FBUzt3QkFDbEIsV0FBVyxFQUFFLHNDQUFzQzt3QkFDbkQsWUFBWSxFQUFFLENBQUMsNkJBQTZCLEVBQUUsdUJBQXVCLENBQUM7cUJBQ3ZFO2lCQUNGO2dCQUNELFNBQVMsRUFBRTtvQkFDVDt3QkFDRSxXQUFXLEVBQUUsMEJBQTBCO3dCQUN2QyxNQUFNLEVBQUUscUJBQXFCO3dCQUM3QixLQUFLLEVBQUUsa0JBQWtCO3dCQUN6QixTQUFTLEVBQUUsU0FBUzt3QkFDcEIsT0FBTyxFQUFFLFNBQVM7d0JBQ2xCLEdBQUcsRUFBRSxLQUFLO3FCQUNYO2lCQUNGO2dCQUNELE1BQU0sRUFBRTtvQkFDTjt3QkFDRSxJQUFJLEVBQUUsWUFBWTt3QkFDbEIsS0FBSyxFQUFFLFVBQVU7d0JBQ2pCLFFBQVEsRUFBRSx1QkFBdUI7cUJBQ2xDO2lCQUNGO2dCQUNELFFBQVEsRUFBRSxRQUFRO2dCQUNsQixRQUFRLEVBQUUsS0FBSzthQUNoQixDQUFDO1lBRUYsSUFBTSxNQUFNLEdBQUcsc0JBQXNCLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBQzdELE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3BDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG1EQUFtRCxFQUFFO1lBQ3RELElBQU0sYUFBYSxHQUFHO2dCQUNwQixnQkFBZ0I7Z0JBQ2hCLFlBQVksRUFBRTtvQkFDWixTQUFTLEVBQUUsTUFBTTtvQkFDakIsUUFBUSxFQUFFLEtBQUs7b0JBQ2YsS0FBSyxFQUFFLGVBQWUsQ0FBQyx1QkFBdUI7aUJBQy9DO2dCQUNELFVBQVUsRUFBRSxFQUFFO2dCQUNkLFNBQVMsRUFBRSxFQUFFO2dCQUNiLE1BQU0sRUFBRSxFQUFFO2FBQ1gsQ0FBQztZQUVGLElBQU0sTUFBTSxHQUFHLHNCQUFzQixDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUMvRCxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUVuQyxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUNwQixJQUFNLE1BQU0sR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQztnQkFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBQSxLQUFLLElBQUksT0FBQSxLQUFLLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsRUFBNUIsQ0FBNEIsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUN0RSxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFBLEtBQUssSUFBSSxPQUFBLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUE1QixDQUE0QixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDeEUsQ0FBQztRQUNILENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdEQUFnRCxFQUFFO1lBQ25ELElBQU0saUJBQWlCLEdBQUc7Z0JBQ3hCLFNBQVMsRUFBRSxNQUFNO2dCQUNqQixRQUFRLEVBQUUsT0FBTztnQkFDakIsS0FBSyxFQUFFLHdCQUF3QjtnQkFDL0IsS0FBSyxFQUFFLGFBQWE7Z0JBQ3BCLFFBQVEsRUFBRSxjQUFjO2dCQUN4QixPQUFPLEVBQUUsdUJBQXVCO2dCQUNoQyxRQUFRLEVBQUUsbUNBQW1DO2FBQzlDLENBQUM7WUFFRixtQ0FBbUM7WUFDbkMsSUFBTSxNQUFNLEdBQUc7Z0JBQ2IsS0FBSyxFQUFFLGFBQWE7Z0JBQ3BCLFlBQVksRUFBRSxpQkFBaUI7Z0JBQy9CLFVBQVUsRUFBRSxFQUFFO2dCQUNkLFNBQVMsRUFBRSxFQUFFO2dCQUNiLE1BQU0sRUFBRSxFQUFFO2FBQ1gsQ0FBQztZQUVGLElBQU0sTUFBTSxHQUFHLHNCQUFzQixDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN4RCxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNwQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRTtZQUNqRCxJQUFNLGVBQWUsR0FBRztnQkFDdEI7b0JBQ0UsT0FBTyxFQUFFLFdBQVc7b0JBQ3BCLFFBQVEsRUFBRSxzQkFBc0I7b0JBQ2hDLFNBQVMsRUFBRSxTQUFTO29CQUNwQixPQUFPLEVBQUUsU0FBUztvQkFDbEIsV0FBVyxFQUFFLHFEQUFxRDtvQkFDbEUsWUFBWSxFQUFFO3dCQUNaLCtDQUErQzt3QkFDL0MsZ0NBQWdDO3FCQUNqQztpQkFDRjtnQkFDRDtvQkFDRSxPQUFPLEVBQUUsaUJBQWlCO29CQUMxQixRQUFRLEVBQUUsa0JBQWtCO29CQUM1QixTQUFTLEVBQUUsU0FBUztvQkFDcEIsbUNBQW1DO29CQUNuQyxXQUFXLEVBQUUsb0NBQW9DO2lCQUNsRDthQUNGLENBQUM7WUFFRixJQUFNLE1BQU0sR0FBRztnQkFDYixLQUFLLEVBQUUsa0JBQWtCO2dCQUN6QixZQUFZLEVBQUU7b0JBQ1osU0FBUyxFQUFFLE1BQU07b0JBQ2pCLFFBQVEsRUFBRSxNQUFNO29CQUNoQixLQUFLLEVBQUUsa0JBQWtCO2lCQUMxQjtnQkFDRCxVQUFVLEVBQUUsZUFBZTtnQkFDM0IsU0FBUyxFQUFFLEVBQUU7Z0JBQ2IsTUFBTSxFQUFFLEVBQUU7YUFDWCxDQUFDO1lBRUYsSUFBTSxNQUFNLEdBQUcsc0JBQXNCLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3hELE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3BDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDZDQUE2QyxFQUFFO1lBQ2hELElBQU0sY0FBYyxHQUFHO2dCQUNyQjtvQkFDRSxXQUFXLEVBQUUsa0JBQWtCO29CQUMvQixNQUFNLEVBQUUscUJBQXFCO29CQUM3QixLQUFLLEVBQUUsa0JBQWtCO29CQUN6QixTQUFTLEVBQUUsU0FBUztvQkFDcEIsT0FBTyxFQUFFLFNBQVM7b0JBQ2xCLEdBQUcsRUFBRSxLQUFLO29CQUNWLE1BQU0sRUFBRSxpQkFBaUI7aUJBQzFCO2dCQUNEO29CQUNFLFdBQVcsRUFBRSxnQkFBZ0I7b0JBQzdCLE1BQU0sRUFBRSxhQUFhO29CQUNyQixLQUFLLEVBQUUsaUJBQWlCO29CQUN4QixTQUFTLEVBQUUsU0FBUztvQkFDcEIsT0FBTyxFQUFFLFNBQVM7aUJBQ25CO2FBQ0YsQ0FBQztZQUVGLElBQU0sTUFBTSxHQUFHO2dCQUNiLEtBQUssRUFBRSxpQkFBaUI7Z0JBQ3hCLFlBQVksRUFBRTtvQkFDWixTQUFTLEVBQUUsTUFBTTtvQkFDakIsUUFBUSxFQUFFLFVBQVU7b0JBQ3BCLEtBQUssRUFBRSxzQkFBc0I7aUJBQzlCO2dCQUNELFVBQVUsRUFBRSxFQUFFO2dCQUNkLFNBQVMsRUFBRSxjQUFjO2dCQUN6QixNQUFNLEVBQUUsRUFBRTthQUNYLENBQUM7WUFFRixJQUFNLE1BQU0sR0FBRyxzQkFBc0IsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDeEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDcEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUU7WUFDN0MsSUFBTSxXQUFXLEdBQUc7Z0JBQ2xCO29CQUNFLElBQUksRUFBRSxPQUFPO29CQUNiLEtBQUssRUFBRSxVQUFVO29CQUNqQixRQUFRLEVBQUUscUJBQXFCO2lCQUNoQztnQkFDRDtvQkFDRSxJQUFJLEVBQUUsU0FBUztvQkFDZixLQUFLLEVBQUUsY0FBYztvQkFDckIsUUFBUSxFQUFFLHNCQUFzQjtpQkFDakM7Z0JBQ0Q7b0JBQ0UsSUFBSSxFQUFFLFlBQVk7b0JBQ2xCLEtBQUssRUFBRSxjQUFjO29CQUNyQixRQUFRLEVBQUUsV0FBVztpQkFDdEI7Z0JBQ0Q7b0JBQ0UsSUFBSSxFQUFFLGVBQWU7b0JBQ3JCLEtBQUssRUFBRSxVQUFVO29CQUNqQixRQUFRLEVBQUUsYUFBYTtpQkFDeEI7YUFDRixDQUFDO1lBRUYsSUFBTSxNQUFNLEdBQUc7Z0JBQ2IsS0FBSyxFQUFFLHVCQUF1QjtnQkFDOUIsWUFBWSxFQUFFO29CQUNaLFNBQVMsRUFBRSxTQUFTO29CQUNwQixRQUFRLEVBQUUsV0FBVztvQkFDckIsS0FBSyxFQUFFLHFCQUFxQjtpQkFDN0I7Z0JBQ0QsVUFBVSxFQUFFLEVBQUU7Z0JBQ2QsU0FBUyxFQUFFLEVBQUU7Z0JBQ2IsTUFBTSxFQUFFLFdBQVc7YUFDcEIsQ0FBQztZQUVGLElBQU0sTUFBTSxHQUFHLHNCQUFzQixDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN4RCxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNwQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRTtZQUNyQyxJQUFNLFNBQVMsR0FBRyxDQUFDLFFBQVEsRUFBRSxTQUFTLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQyxDQUFDO1lBRS9ELFNBQVMsQ0FBQyxPQUFPLENBQUMsVUFBQSxRQUFRO2dCQUN4QixJQUFNLE1BQU0sR0FBRztvQkFDYixLQUFLLEVBQUUsVUFBRyxRQUFRLFlBQVM7b0JBQzNCLFlBQVksRUFBRTt3QkFDWixTQUFTLEVBQUUsTUFBTTt3QkFDakIsUUFBUSxFQUFFLE1BQU07d0JBQ2hCLEtBQUssRUFBRSxrQkFBa0I7cUJBQzFCO29CQUNELFVBQVUsRUFBRSxFQUFFO29CQUNkLFNBQVMsRUFBRSxFQUFFO29CQUNiLE1BQU0sRUFBRSxFQUFFO29CQUNWLFFBQVEsVUFBQTtpQkFDVCxDQUFDO2dCQUVGLElBQU0sTUFBTSxHQUFHLHNCQUFzQixDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztnQkFDeEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDcEMsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx3Q0FBd0MsRUFBRTtZQUMzQyxJQUFNLE1BQU0sR0FBRztnQkFDYixLQUFLLEVBQUUseUJBQXlCO2dCQUNoQyxZQUFZLEVBQUU7b0JBQ1osU0FBUyxFQUFFLE1BQU07b0JBQ2pCLFFBQVEsRUFBRSxNQUFNO29CQUNoQixLQUFLLEVBQUUsa0JBQWtCO2lCQUMxQjtnQkFDRCxVQUFVLEVBQUUsRUFBRTtnQkFDZCxTQUFTLEVBQUUsRUFBRTtnQkFDYixNQUFNLEVBQUUsRUFBRTtnQkFDVixRQUFRLEVBQUUsa0JBQWtCO2FBQzdCLENBQUM7WUFFRixJQUFNLE1BQU0sR0FBRyxzQkFBc0IsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDeEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDckMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsd0NBQXdDLEVBQUU7WUFDM0MsSUFBTSxXQUFXLEdBQUcsQ0FBQyxVQUFVLEVBQUUsY0FBYyxFQUFFLFVBQVUsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUV2RSxXQUFXLENBQUMsT0FBTyxDQUFDLFVBQUEsS0FBSztnQkFDdkIsSUFBTSxNQUFNLEdBQUc7b0JBQ2IsS0FBSyxFQUFFLGtCQUFrQjtvQkFDekIsWUFBWSxFQUFFO3dCQUNaLFNBQVMsRUFBRSxNQUFNO3dCQUNqQixRQUFRLEVBQUUsTUFBTTt3QkFDaEIsS0FBSyxFQUFFLGtCQUFrQjtxQkFDMUI7b0JBQ0QsVUFBVSxFQUFFLEVBQUU7b0JBQ2QsU0FBUyxFQUFFLEVBQUU7b0JBQ2IsTUFBTSxFQUFFO3dCQUNOOzRCQUNFLElBQUksRUFBRSxZQUFZOzRCQUNsQixLQUFLLE9BQUE7NEJBQ0wsUUFBUSxFQUFFLGVBQWU7eUJBQzFCO3FCQUNGO2lCQUNGLENBQUM7Z0JBRUYsSUFBTSxNQUFNLEdBQUcsc0JBQXNCLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDO2dCQUN4RCxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNwQyxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHlDQUF5QyxFQUFFO1lBQzVDLElBQU0sYUFBYSxHQUFHO2dCQUNwQixLQUFLLEVBQUUsZ0JBQWdCO2dCQUN2QixZQUFZLEVBQUU7b0JBQ1osU0FBUyxFQUFFLEtBQUs7b0JBQ2hCLFFBQVEsRUFBRSxNQUFNO29CQUNoQixLQUFLLEVBQUUsaUJBQWlCO2lCQUN6QjtnQkFDRCxVQUFVLEVBQUUsRUFBRTtnQkFDZCxTQUFTLEVBQUUsRUFBRTtnQkFDYixNQUFNLEVBQUUsRUFBRTthQUNYLENBQUM7WUFFRixJQUFNLE1BQU0sR0FBRyxzQkFBc0IsQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDL0QsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDcEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0RBQXNELEVBQUU7WUFDekQsSUFBTSxjQUFjLEdBQUc7Z0JBQ3JCLEtBQUssRUFBRSxpQkFBaUI7Z0JBQ3hCLFlBQVksRUFBRTtvQkFDWixTQUFTLEVBQUUsS0FBSztvQkFDaEIsUUFBUSxFQUFFLFFBQVE7b0JBQ2xCLEtBQUssRUFBRSxpQkFBaUI7b0JBQ3hCLE9BQU8sRUFBRSxxQkFBcUI7b0JBQzlCLFFBQVEsRUFBRSxtQ0FBbUM7aUJBQzlDO2dCQUNELFVBQVUsRUFBRSxFQUFFO2dCQUNkLFNBQVMsRUFBRSxFQUFFO2dCQUNiLE1BQU0sRUFBRSxFQUFFO2FBQ1gsQ0FBQztZQUVGLElBQU0sTUFBTSxHQUFHLHNCQUFzQixDQUFDLFNBQVMsQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUNoRSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNwQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRTtZQUN0QyxJQUFNLHFCQUFxQixHQUFHO2dCQUM1QixLQUFLLEVBQUUsb0JBQW9CO2dCQUMzQixZQUFZLEVBQUU7b0JBQ1osU0FBUyxFQUFFLFNBQVM7b0JBQ3BCLFFBQVEsRUFBRSxLQUFLO29CQUNmLEtBQUssRUFBRSxxQkFBcUI7b0JBQzVCLE9BQU8sRUFBRSxXQUFXO29CQUNwQixRQUFRLEVBQUUsZ0JBQWdCO2lCQUMzQjtnQkFDRCxVQUFVLEVBQUUsRUFBRTtnQkFDZCxTQUFTLEVBQUUsRUFBRTtnQkFDYixNQUFNLEVBQUUsRUFBRTthQUNYLENBQUM7WUFFRixJQUFNLE1BQU0sR0FBRyxzQkFBc0IsQ0FBQyxTQUFTLENBQUMscUJBQXFCLENBQUMsQ0FBQztZQUN2RSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUVuQyxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUNwQixJQUFNLE1BQU0sR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQztnQkFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBQSxLQUFLLElBQUksT0FBQSxLQUFLLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBOUIsQ0FBOEIsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUN4RSxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFBLEtBQUssSUFBSSxPQUFBLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxFQUEvQixDQUErQixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDM0UsQ0FBQztRQUNILENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyIvVXNlcnMvZGQ2MC9mYWFmby9mYWFmby9mYWFmby1jYXJlZXItcGxhdGZvcm0vX190ZXN0c19fL3VuaXQvY29tcG9uZW50cy9yZXN1bWUtYnVpbGRlci1zaW1wbGUudGVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFNpbXBsZSBSZXN1bWUgQnVpbGRlciBUZXN0c1xuICpcbiAqIEJhc2ljIHRlc3RzIGZvciBSZXN1bWUgQnVpbGRlciBmdW5jdGlvbmFsaXR5IHdpdGhvdXQgY29tcGxleCBkZXBlbmRlbmNpZXNcbiAqL1xuXG5pbXBvcnQgeyB6IH0gZnJvbSAnem9kJztcblxuLy8gRGVmaW5lIHRoZSB2YWxpZGF0aW9uIHNjaGVtYXMgKGNvcGllZCBmcm9tIEFQSSByb3V0ZSlcbmNvbnN0IHBlcnNvbmFsSW5mb1NjaGVtYSA9IHoub2JqZWN0KHtcbiAgZmlyc3ROYW1lOiB6LnN0cmluZygpLm1pbigxLCAnRmlyc3QgbmFtZSBpcyByZXF1aXJlZCcpLFxuICBsYXN0TmFtZTogei5zdHJpbmcoKS5taW4oMSwgJ0xhc3QgbmFtZSBpcyByZXF1aXJlZCcpLFxuICBlbWFpbDogei5zdHJpbmcoKS5lbWFpbCgnVmFsaWQgZW1haWwgaXMgcmVxdWlyZWQnKSxcbiAgcGhvbmU6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgbG9jYXRpb246IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgd2Vic2l0ZTogei5zdHJpbmcoKS51cmwoKS5vcHRpb25hbCgpLm9yKHoubGl0ZXJhbCgnJykpLFxuICBsaW5rZWRJbjogei5zdHJpbmcoKS51cmwoKS5vcHRpb25hbCgpLm9yKHoubGl0ZXJhbCgnJykpXG59KTtcblxuY29uc3QgZXhwZXJpZW5jZVNjaGVtYSA9IHoub2JqZWN0KHtcbiAgY29tcGFueTogei5zdHJpbmcoKS5taW4oMSwgJ0NvbXBhbnkgbmFtZSBpcyByZXF1aXJlZCcpLFxuICBwb3NpdGlvbjogei5zdHJpbmcoKS5taW4oMSwgJ1Bvc2l0aW9uIGlzIHJlcXVpcmVkJyksXG4gIHN0YXJ0RGF0ZTogei5zdHJpbmcoKS5taW4oMSwgJ1N0YXJ0IGRhdGUgaXMgcmVxdWlyZWQnKSxcbiAgZW5kRGF0ZTogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBkZXNjcmlwdGlvbjogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBhY2hpZXZlbWVudHM6IHouYXJyYXkoei5zdHJpbmcoKSkub3B0aW9uYWwoKVxufSk7XG5cbmNvbnN0IGVkdWNhdGlvblNjaGVtYSA9IHoub2JqZWN0KHtcbiAgaW5zdGl0dXRpb246IHouc3RyaW5nKCkubWluKDEsICdJbnN0aXR1dGlvbiBpcyByZXF1aXJlZCcpLFxuICBkZWdyZWU6IHouc3RyaW5nKCkubWluKDEsICdEZWdyZWUgaXMgcmVxdWlyZWQnKSxcbiAgZmllbGQ6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgc3RhcnREYXRlOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIGVuZERhdGU6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgZ3BhOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIGhvbm9yczogei5zdHJpbmcoKS5vcHRpb25hbCgpXG59KTtcblxuY29uc3Qgc2tpbGxTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIG5hbWU6IHouc3RyaW5nKCkubWluKDEsICdTa2lsbCBuYW1lIGlzIHJlcXVpcmVkJyksXG4gIGxldmVsOiB6LmVudW0oWydCRUdJTk5FUicsICdJTlRFUk1FRElBVEUnLCAnQURWQU5DRUQnLCAnRVhQRVJUJ10pLm9wdGlvbmFsKCksXG4gIGNhdGVnb3J5OiB6LnN0cmluZygpLm9wdGlvbmFsKClcbn0pO1xuXG5jb25zdCByZXN1bWVWYWxpZGF0aW9uU2NoZW1hID0gei5vYmplY3Qoe1xuICB0aXRsZTogei5zdHJpbmcoKS5taW4oMSwgJ1Jlc3VtZSB0aXRsZSBpcyByZXF1aXJlZCcpLFxuICBwZXJzb25hbEluZm86IHBlcnNvbmFsSW5mb1NjaGVtYSxcbiAgc3VtbWFyeTogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBleHBlcmllbmNlOiB6LmFycmF5KGV4cGVyaWVuY2VTY2hlbWEpLm9wdGlvbmFsKCksXG4gIGVkdWNhdGlvbjogei5hcnJheShlZHVjYXRpb25TY2hlbWEpLm9wdGlvbmFsKCksXG4gIHNraWxsczogei5hcnJheShza2lsbFNjaGVtYSkub3B0aW9uYWwoKSxcbiAgc2VjdGlvbnM6IHoucmVjb3JkKHouYW55KCkpLm9wdGlvbmFsKCksXG4gIHRlbXBsYXRlOiB6LmVudW0oWydtb2Rlcm4nLCAnY2xhc3NpYycsICdtaW5pbWFsJywgJ2NyZWF0aXZlJ10pLmRlZmF1bHQoJ21vZGVybicpLFxuICBpc1B1YmxpYzogei5ib29sZWFuKCkuZGVmYXVsdChmYWxzZSlcbn0pO1xuXG5kZXNjcmliZSgnUmVzdW1lIEJ1aWxkZXIgLSBTaW1wbGUgVGVzdHMnLCAoKSA9PiB7XG4gIGRlc2NyaWJlKCdSZXN1bWUgVmFsaWRhdGlvbiBTY2hlbWEnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCB2YWxpZGF0ZSBhIGNvbXBsZXRlIHJlc3VtZSBvYmplY3QnLCAoKSA9PiB7XG4gICAgICBjb25zdCB2YWxpZFJlc3VtZSA9IHtcbiAgICAgICAgdGl0bGU6ICdTb2Z0d2FyZSBFbmdpbmVlciBSZXN1bWUnLFxuICAgICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgICBmaXJzdE5hbWU6ICdKb2huJyxcbiAgICAgICAgICBsYXN0TmFtZTogJ0RvZScsXG4gICAgICAgICAgZW1haWw6ICdqb2huLmRvZUBleGFtcGxlLmNvbScsXG4gICAgICAgICAgcGhvbmU6ICcrMS01NTUtMDEyMycsXG4gICAgICAgICAgbG9jYXRpb246ICdTYW4gRnJhbmNpc2NvLCBDQScsXG4gICAgICAgICAgd2Vic2l0ZTogJ2h0dHBzOi8vam9obmRvZS5jb20nLFxuICAgICAgICAgIGxpbmtlZEluOiAnaHR0cHM6Ly9saW5rZWRpbi5jb20vaW4vam9obmRvZSdcbiAgICAgICAgfSxcbiAgICAgICAgc3VtbWFyeTogJ0V4cGVyaWVuY2VkIHNvZnR3YXJlIGVuZ2luZWVyIHdpdGggNSsgeWVhcnMgb2YgZXhwZXJpZW5jZS4nLFxuICAgICAgICBleHBlcmllbmNlOiBbXG4gICAgICAgICAge1xuICAgICAgICAgICAgY29tcGFueTogJ1RlY2ggQ29ycCcsXG4gICAgICAgICAgICBwb3NpdGlvbjogJ1NlbmlvciBTb2Z0d2FyZSBFbmdpbmVlcicsXG4gICAgICAgICAgICBzdGFydERhdGU6ICcyMDIwLTAxJyxcbiAgICAgICAgICAgIGVuZERhdGU6ICcyMDIzLTEyJyxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnTGVkIGRldmVsb3BtZW50IG9mIHdlYiBhcHBsaWNhdGlvbnMuJyxcbiAgICAgICAgICAgIGFjaGlldmVtZW50czogWydJbXByb3ZlZCBwZXJmb3JtYW5jZSBieSA0MCUnLCAnTWVudG9yZWQgMyBkZXZlbG9wZXJzJ11cbiAgICAgICAgICB9XG4gICAgICAgIF0sXG4gICAgICAgIGVkdWNhdGlvbjogW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIGluc3RpdHV0aW9uOiAnVW5pdmVyc2l0eSBvZiBDYWxpZm9ybmlhJyxcbiAgICAgICAgICAgIGRlZ3JlZTogJ0JhY2hlbG9yIG9mIFNjaWVuY2UnLFxuICAgICAgICAgICAgZmllbGQ6ICdDb21wdXRlciBTY2llbmNlJyxcbiAgICAgICAgICAgIHN0YXJ0RGF0ZTogJzIwMTYtMDknLFxuICAgICAgICAgICAgZW5kRGF0ZTogJzIwMjAtMDUnLFxuICAgICAgICAgICAgZ3BhOiAnMy44J1xuICAgICAgICAgIH1cbiAgICAgICAgXSxcbiAgICAgICAgc2tpbGxzOiBbXG4gICAgICAgICAge1xuICAgICAgICAgICAgbmFtZTogJ0phdmFTY3JpcHQnLFxuICAgICAgICAgICAgbGV2ZWw6ICdBRFZBTkNFRCcsXG4gICAgICAgICAgICBjYXRlZ29yeTogJ1Byb2dyYW1taW5nIExhbmd1YWdlcydcbiAgICAgICAgICB9XG4gICAgICAgIF0sXG4gICAgICAgIHRlbXBsYXRlOiAnbW9kZXJuJyxcbiAgICAgICAgaXNQdWJsaWM6IGZhbHNlXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSByZXN1bWVWYWxpZGF0aW9uU2NoZW1hLnNhZmVQYXJzZSh2YWxpZFJlc3VtZSk7XG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3MpLnRvQmUodHJ1ZSk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHJlamVjdCByZXN1bWUgd2l0aCBtaXNzaW5nIHJlcXVpcmVkIGZpZWxkcycsICgpID0+IHtcbiAgICAgIGNvbnN0IGludmFsaWRSZXN1bWUgPSB7XG4gICAgICAgIC8vIE1pc3NpbmcgdGl0bGVcbiAgICAgICAgcGVyc29uYWxJbmZvOiB7XG4gICAgICAgICAgZmlyc3ROYW1lOiAnSm9obicsXG4gICAgICAgICAgbGFzdE5hbWU6ICdEb2UnLFxuICAgICAgICAgIGVtYWlsOiAnaW52YWxpZC1lbWFpbCcgLy8gSW52YWxpZCBlbWFpbCBmb3JtYXRcbiAgICAgICAgfSxcbiAgICAgICAgZXhwZXJpZW5jZTogW10sXG4gICAgICAgIGVkdWNhdGlvbjogW10sXG4gICAgICAgIHNraWxsczogW11cbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IHJlc3VtZVZhbGlkYXRpb25TY2hlbWEuc2FmZVBhcnNlKGludmFsaWRSZXN1bWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIFxuICAgICAgaWYgKCFyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBjb25zdCBlcnJvcnMgPSByZXN1bHQuZXJyb3IuaXNzdWVzO1xuICAgICAgICBleHBlY3QoZXJyb3JzLnNvbWUoZXJyb3IgPT4gZXJyb3IucGF0aC5pbmNsdWRlcygndGl0bGUnKSkpLnRvQmUodHJ1ZSk7XG4gICAgICAgIGV4cGVjdChlcnJvcnMuc29tZShlcnJvciA9PiBlcnJvci5wYXRoLmluY2x1ZGVzKCdlbWFpbCcpKSkudG9CZSh0cnVlKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgcGVyc29uYWwgaW5mbyBmaWVsZHMgY29ycmVjdGx5JywgKCkgPT4ge1xuICAgICAgY29uc3QgdmFsaWRQZXJzb25hbEluZm8gPSB7XG4gICAgICAgIGZpcnN0TmFtZTogJ0phbmUnLFxuICAgICAgICBsYXN0TmFtZTogJ1NtaXRoJyxcbiAgICAgICAgZW1haWw6ICdqYW5lLnNtaXRoQGV4YW1wbGUuY29tJyxcbiAgICAgICAgcGhvbmU6ICcrMS01NTUtMDQ1NicsXG4gICAgICAgIGxvY2F0aW9uOiAnTmV3IFlvcmssIE5ZJyxcbiAgICAgICAgd2Vic2l0ZTogJ2h0dHBzOi8vamFuZXNtaXRoLmRldicsXG4gICAgICAgIGxpbmtlZEluOiAnaHR0cHM6Ly9saW5rZWRpbi5jb20vaW4vamFuZXNtaXRoJ1xuICAgICAgfTtcblxuICAgICAgLy8gVGVzdCBhcyBwYXJ0IG9mIGEgbWluaW1hbCByZXN1bWVcbiAgICAgIGNvbnN0IHJlc3VtZSA9IHtcbiAgICAgICAgdGl0bGU6ICdUZXN0IFJlc3VtZScsXG4gICAgICAgIHBlcnNvbmFsSW5mbzogdmFsaWRQZXJzb25hbEluZm8sXG4gICAgICAgIGV4cGVyaWVuY2U6IFtdLFxuICAgICAgICBlZHVjYXRpb246IFtdLFxuICAgICAgICBza2lsbHM6IFtdXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSByZXN1bWVWYWxpZGF0aW9uU2NoZW1hLnNhZmVQYXJzZShyZXN1bWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCB2YWxpZGF0ZSBleHBlcmllbmNlIGVudHJpZXMgY29ycmVjdGx5JywgKCkgPT4ge1xuICAgICAgY29uc3QgdmFsaWRFeHBlcmllbmNlID0gW1xuICAgICAgICB7XG4gICAgICAgICAgY29tcGFueTogJ1N0YXJ0dXBDbycsXG4gICAgICAgICAgcG9zaXRpb246ICdGdWxsIFN0YWNrIERldmVsb3BlcicsXG4gICAgICAgICAgc3RhcnREYXRlOiAnMjAyMS0wNicsXG4gICAgICAgICAgZW5kRGF0ZTogJzIwMjMtMTInLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRGV2ZWxvcGVkIHdlYiBhcHBsaWNhdGlvbnMgdXNpbmcgUmVhY3QgYW5kIE5vZGUuanMuJyxcbiAgICAgICAgICBhY2hpZXZlbWVudHM6IFtcbiAgICAgICAgICAgICdCdWlsdCBhdXRoZW50aWNhdGlvbiBzeXN0ZW0gZm9yIDEwLDAwMCsgdXNlcnMnLFxuICAgICAgICAgICAgJ1JlZHVjZWQgcGFnZSBsb2FkIHRpbWVzIGJ5IDYwJSdcbiAgICAgICAgICBdXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBjb21wYW55OiAnQ3VycmVudCBDb21wYW55JyxcbiAgICAgICAgICBwb3NpdGlvbjogJ1NlbmlvciBEZXZlbG9wZXInLFxuICAgICAgICAgIHN0YXJ0RGF0ZTogJzIwMjQtMDEnLFxuICAgICAgICAgIC8vIE5vIGVuZCBkYXRlIGZvciBjdXJyZW50IHBvc2l0aW9uXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdMZWFkaW5nIGZyb250ZW5kIGRldmVsb3BtZW50IHRlYW0uJ1xuICAgICAgICB9XG4gICAgICBdO1xuXG4gICAgICBjb25zdCByZXN1bWUgPSB7XG4gICAgICAgIHRpdGxlOiAnRGV2ZWxvcGVyIFJlc3VtZScsXG4gICAgICAgIHBlcnNvbmFsSW5mbzoge1xuICAgICAgICAgIGZpcnN0TmFtZTogJ1Rlc3QnLFxuICAgICAgICAgIGxhc3ROYW1lOiAnVXNlcicsXG4gICAgICAgICAgZW1haWw6ICd0ZXN0QGV4YW1wbGUuY29tJ1xuICAgICAgICB9LFxuICAgICAgICBleHBlcmllbmNlOiB2YWxpZEV4cGVyaWVuY2UsXG4gICAgICAgIGVkdWNhdGlvbjogW10sXG4gICAgICAgIHNraWxsczogW11cbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IHJlc3VtZVZhbGlkYXRpb25TY2hlbWEuc2FmZVBhcnNlKHJlc3VtZSk7XG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3MpLnRvQmUodHJ1ZSk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIGVkdWNhdGlvbiBlbnRyaWVzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IHZhbGlkRWR1Y2F0aW9uID0gW1xuICAgICAgICB7XG4gICAgICAgICAgaW5zdGl0dXRpb246ICdTdGF0ZSBVbml2ZXJzaXR5JyxcbiAgICAgICAgICBkZWdyZWU6ICdCYWNoZWxvciBvZiBTY2llbmNlJyxcbiAgICAgICAgICBmaWVsZDogJ0NvbXB1dGVyIFNjaWVuY2UnLFxuICAgICAgICAgIHN0YXJ0RGF0ZTogJzIwMTctMDknLFxuICAgICAgICAgIGVuZERhdGU6ICcyMDIxLTA1JyxcbiAgICAgICAgICBncGE6ICczLjcnLFxuICAgICAgICAgIGhvbm9yczogJ01hZ25hIEN1bSBMYXVkZSdcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGluc3RpdHV0aW9uOiAnT25saW5lIEFjYWRlbXknLFxuICAgICAgICAgIGRlZ3JlZTogJ0NlcnRpZmljYXRlJyxcbiAgICAgICAgICBmaWVsZDogJ1dlYiBEZXZlbG9wbWVudCcsXG4gICAgICAgICAgc3RhcnREYXRlOiAnMjAyMS0wMScsXG4gICAgICAgICAgZW5kRGF0ZTogJzIwMjEtMDMnXG4gICAgICAgIH1cbiAgICAgIF07XG5cbiAgICAgIGNvbnN0IHJlc3VtZSA9IHtcbiAgICAgICAgdGl0bGU6ICdHcmFkdWF0ZSBSZXN1bWUnLFxuICAgICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgICBmaXJzdE5hbWU6ICdUZXN0JyxcbiAgICAgICAgICBsYXN0TmFtZTogJ0dyYWR1YXRlJyxcbiAgICAgICAgICBlbWFpbDogJ2dyYWR1YXRlQGV4YW1wbGUuY29tJ1xuICAgICAgICB9LFxuICAgICAgICBleHBlcmllbmNlOiBbXSxcbiAgICAgICAgZWR1Y2F0aW9uOiB2YWxpZEVkdWNhdGlvbixcbiAgICAgICAgc2tpbGxzOiBbXVxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gcmVzdW1lVmFsaWRhdGlvblNjaGVtYS5zYWZlUGFyc2UocmVzdW1lKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VjY2VzcykudG9CZSh0cnVlKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgc2tpbGxzIGVudHJpZXMgY29ycmVjdGx5JywgKCkgPT4ge1xuICAgICAgY29uc3QgdmFsaWRTa2lsbHMgPSBbXG4gICAgICAgIHtcbiAgICAgICAgICBuYW1lOiAnUmVhY3QnLFxuICAgICAgICAgIGxldmVsOiAnQURWQU5DRUQnLFxuICAgICAgICAgIGNhdGVnb3J5OiAnRnJvbnRlbmQgRnJhbWV3b3JrcydcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIG5hbWU6ICdOb2RlLmpzJyxcbiAgICAgICAgICBsZXZlbDogJ0lOVEVSTUVESUFURScsXG4gICAgICAgICAgY2F0ZWdvcnk6ICdCYWNrZW5kIFRlY2hub2xvZ2llcydcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIG5hbWU6ICdQb3N0Z3JlU1FMJyxcbiAgICAgICAgICBsZXZlbDogJ0lOVEVSTUVESUFURScsXG4gICAgICAgICAgY2F0ZWdvcnk6ICdEYXRhYmFzZXMnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBuYW1lOiAnQ29tbXVuaWNhdGlvbicsXG4gICAgICAgICAgbGV2ZWw6ICdBRFZBTkNFRCcsXG4gICAgICAgICAgY2F0ZWdvcnk6ICdTb2Z0IFNraWxscydcbiAgICAgICAgfVxuICAgICAgXTtcblxuICAgICAgY29uc3QgcmVzdW1lID0ge1xuICAgICAgICB0aXRsZTogJ1NraWxscy1mb2N1c2VkIFJlc3VtZScsXG4gICAgICAgIHBlcnNvbmFsSW5mbzoge1xuICAgICAgICAgIGZpcnN0TmFtZTogJ1NraWxsZWQnLFxuICAgICAgICAgIGxhc3ROYW1lOiAnRGV2ZWxvcGVyJyxcbiAgICAgICAgICBlbWFpbDogJ3NraWxsZWRAZXhhbXBsZS5jb20nXG4gICAgICAgIH0sXG4gICAgICAgIGV4cGVyaWVuY2U6IFtdLFxuICAgICAgICBlZHVjYXRpb246IFtdLFxuICAgICAgICBza2lsbHM6IHZhbGlkU2tpbGxzXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSByZXN1bWVWYWxpZGF0aW9uU2NoZW1hLnNhZmVQYXJzZShyZXN1bWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCB2YWxpZGF0ZSB0ZW1wbGF0ZSBvcHRpb25zJywgKCkgPT4ge1xuICAgICAgY29uc3QgdGVtcGxhdGVzID0gWydtb2Rlcm4nLCAnY2xhc3NpYycsICdtaW5pbWFsJywgJ2NyZWF0aXZlJ107XG4gICAgICBcbiAgICAgIHRlbXBsYXRlcy5mb3JFYWNoKHRlbXBsYXRlID0+IHtcbiAgICAgICAgY29uc3QgcmVzdW1lID0ge1xuICAgICAgICAgIHRpdGxlOiBgJHt0ZW1wbGF0ZX0gUmVzdW1lYCxcbiAgICAgICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgICAgIGZpcnN0TmFtZTogJ1Rlc3QnLFxuICAgICAgICAgICAgbGFzdE5hbWU6ICdVc2VyJyxcbiAgICAgICAgICAgIGVtYWlsOiAndGVzdEBleGFtcGxlLmNvbSdcbiAgICAgICAgICB9LFxuICAgICAgICAgIGV4cGVyaWVuY2U6IFtdLFxuICAgICAgICAgIGVkdWNhdGlvbjogW10sXG4gICAgICAgICAgc2tpbGxzOiBbXSxcbiAgICAgICAgICB0ZW1wbGF0ZVxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IHJlc3VtZVZhbGlkYXRpb25TY2hlbWEuc2FmZVBhcnNlKHJlc3VtZSk7XG4gICAgICAgIGV4cGVjdChyZXN1bHQuc3VjY2VzcykudG9CZSh0cnVlKTtcbiAgICAgIH0pO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCByZWplY3QgaW52YWxpZCB0ZW1wbGF0ZSBvcHRpb25zJywgKCkgPT4ge1xuICAgICAgY29uc3QgcmVzdW1lID0ge1xuICAgICAgICB0aXRsZTogJ0ludmFsaWQgVGVtcGxhdGUgUmVzdW1lJyxcbiAgICAgICAgcGVyc29uYWxJbmZvOiB7XG4gICAgICAgICAgZmlyc3ROYW1lOiAnVGVzdCcsXG4gICAgICAgICAgbGFzdE5hbWU6ICdVc2VyJyxcbiAgICAgICAgICBlbWFpbDogJ3Rlc3RAZXhhbXBsZS5jb20nXG4gICAgICAgIH0sXG4gICAgICAgIGV4cGVyaWVuY2U6IFtdLFxuICAgICAgICBlZHVjYXRpb246IFtdLFxuICAgICAgICBza2lsbHM6IFtdLFxuICAgICAgICB0ZW1wbGF0ZTogJ2ludmFsaWQtdGVtcGxhdGUnXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSByZXN1bWVWYWxpZGF0aW9uU2NoZW1hLnNhZmVQYXJzZShyZXN1bWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgc2tpbGwgbGV2ZWxzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IHZhbGlkTGV2ZWxzID0gWydCRUdJTk5FUicsICdJTlRFUk1FRElBVEUnLCAnQURWQU5DRUQnLCAnRVhQRVJUJ107XG4gICAgICBcbiAgICAgIHZhbGlkTGV2ZWxzLmZvckVhY2gobGV2ZWwgPT4ge1xuICAgICAgICBjb25zdCByZXN1bWUgPSB7XG4gICAgICAgICAgdGl0bGU6ICdTa2lsbCBMZXZlbCBUZXN0JyxcbiAgICAgICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgICAgIGZpcnN0TmFtZTogJ1Rlc3QnLFxuICAgICAgICAgICAgbGFzdE5hbWU6ICdVc2VyJyxcbiAgICAgICAgICAgIGVtYWlsOiAndGVzdEBleGFtcGxlLmNvbSdcbiAgICAgICAgICB9LFxuICAgICAgICAgIGV4cGVyaWVuY2U6IFtdLFxuICAgICAgICAgIGVkdWNhdGlvbjogW10sXG4gICAgICAgICAgc2tpbGxzOiBbXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIG5hbWU6ICdUZXN0IFNraWxsJyxcbiAgICAgICAgICAgICAgbGV2ZWwsXG4gICAgICAgICAgICAgIGNhdGVnb3J5OiAnVGVzdCBDYXRlZ29yeSdcbiAgICAgICAgICAgIH1cbiAgICAgICAgICBdXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgcmVzdWx0ID0gcmVzdW1lVmFsaWRhdGlvblNjaGVtYS5zYWZlUGFyc2UocmVzdW1lKTtcbiAgICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgICAgfSk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBvcHRpb25hbCBmaWVsZHMgY29ycmVjdGx5JywgKCkgPT4ge1xuICAgICAgY29uc3QgbWluaW1hbFJlc3VtZSA9IHtcbiAgICAgICAgdGl0bGU6ICdNaW5pbWFsIFJlc3VtZScsXG4gICAgICAgIHBlcnNvbmFsSW5mbzoge1xuICAgICAgICAgIGZpcnN0TmFtZTogJ01pbicsXG4gICAgICAgICAgbGFzdE5hbWU6ICdVc2VyJyxcbiAgICAgICAgICBlbWFpbDogJ21pbkBleGFtcGxlLmNvbSdcbiAgICAgICAgfSxcbiAgICAgICAgZXhwZXJpZW5jZTogW10sXG4gICAgICAgIGVkdWNhdGlvbjogW10sXG4gICAgICAgIHNraWxsczogW11cbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IHJlc3VtZVZhbGlkYXRpb25TY2hlbWEuc2FmZVBhcnNlKG1pbmltYWxSZXN1bWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCB2YWxpZGF0ZSBVUkwgZm9ybWF0cyBmb3Igd2Vic2l0ZSBhbmQgTGlua2VkSW4nLCAoKSA9PiB7XG4gICAgICBjb25zdCByZXN1bWVXaXRoVXJscyA9IHtcbiAgICAgICAgdGl0bGU6ICdVUkwgVGVzdCBSZXN1bWUnLFxuICAgICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgICBmaXJzdE5hbWU6ICdVUkwnLFxuICAgICAgICAgIGxhc3ROYW1lOiAnVGVzdGVyJyxcbiAgICAgICAgICBlbWFpbDogJ3VybEBleGFtcGxlLmNvbScsXG4gICAgICAgICAgd2Vic2l0ZTogJ2h0dHBzOi8vZXhhbXBsZS5jb20nLFxuICAgICAgICAgIGxpbmtlZEluOiAnaHR0cHM6Ly9saW5rZWRpbi5jb20vaW4vdXJsdGVzdGVyJ1xuICAgICAgICB9LFxuICAgICAgICBleHBlcmllbmNlOiBbXSxcbiAgICAgICAgZWR1Y2F0aW9uOiBbXSxcbiAgICAgICAgc2tpbGxzOiBbXVxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gcmVzdW1lVmFsaWRhdGlvblNjaGVtYS5zYWZlUGFyc2UocmVzdW1lV2l0aFVybHMpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCByZWplY3QgaW52YWxpZCBVUkwgZm9ybWF0cycsICgpID0+IHtcbiAgICAgIGNvbnN0IHJlc3VtZVdpdGhJbnZhbGlkVXJscyA9IHtcbiAgICAgICAgdGl0bGU6ICdJbnZhbGlkIFVSTCBSZXN1bWUnLFxuICAgICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgICBmaXJzdE5hbWU6ICdJbnZhbGlkJyxcbiAgICAgICAgICBsYXN0TmFtZTogJ1VSTCcsXG4gICAgICAgICAgZW1haWw6ICdpbnZhbGlkQGV4YW1wbGUuY29tJyxcbiAgICAgICAgICB3ZWJzaXRlOiAnbm90LWEtdXJsJyxcbiAgICAgICAgICBsaW5rZWRJbjogJ2Fsc28tbm90LWEtdXJsJ1xuICAgICAgICB9LFxuICAgICAgICBleHBlcmllbmNlOiBbXSxcbiAgICAgICAgZWR1Y2F0aW9uOiBbXSxcbiAgICAgICAgc2tpbGxzOiBbXVxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gcmVzdW1lVmFsaWRhdGlvblNjaGVtYS5zYWZlUGFyc2UocmVzdW1lV2l0aEludmFsaWRVcmxzKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VjY2VzcykudG9CZShmYWxzZSk7XG4gICAgICBcbiAgICAgIGlmICghcmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgY29uc3QgZXJyb3JzID0gcmVzdWx0LmVycm9yLmlzc3VlcztcbiAgICAgICAgZXhwZWN0KGVycm9ycy5zb21lKGVycm9yID0+IGVycm9yLnBhdGguaW5jbHVkZXMoJ3dlYnNpdGUnKSkpLnRvQmUodHJ1ZSk7XG4gICAgICAgIGV4cGVjdChlcnJvcnMuc29tZShlcnJvciA9PiBlcnJvci5wYXRoLmluY2x1ZGVzKCdsaW5rZWRJbicpKSkudG9CZSh0cnVlKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfSk7XG59KTtcbiJdLCJ2ZXJzaW9uIjozfQ==