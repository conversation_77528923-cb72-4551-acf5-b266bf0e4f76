8eac2314f976d45ddee34a1e10a47aac
"use strict";
/**
 * Skill Assessment Engine Tests
 *
 * Tests Skill Assessment Engine functionality, business logic, and edge cases.
 *
 * @category unit
 * @requires Unit testing utilities, mocking
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var SkillAssessmentEngine_1 = require("@/lib/skills/SkillAssessmentEngine");
describe('SkillAssessmentEngine', function () {
    var engine;
    beforeEach(function () {
        engine = new SkillAssessmentEngine_1.SkillAssessmentEngine();
    });
    describe('Assessment Creation', function () {
        it('should create a new assessment with default settings', function () {
            var assessment = engine.createAssessment({
                userId: 'user-123',
                careerPathId: 'path-456',
                skillIds: ['skill-1', 'skill-2', 'skill-3'],
            });
            expect(assessment).toBeDefined();
            expect(assessment.id).toBeDefined();
            expect(assessment.userId).toBe('user-123');
            expect(assessment.careerPathId).toBe('path-456');
            expect(assessment.skillIds).toEqual(['skill-1', 'skill-2', 'skill-3']);
            expect(assessment.status).toBe('pending');
            expect(assessment.createdAt).toBeInstanceOf(Date);
            expect(assessment.questions).toHaveLength(0);
            expect(assessment.responses).toHaveLength(0);
        });
        it('should create assessment with custom configuration', function () {
            var config = {
                questionsPerSkill: 5,
                difficultyLevels: ['beginner', 'intermediate', 'advanced'],
                timeLimit: 3600, // 1 hour
                randomizeQuestions: true,
            };
            var assessment = engine.createAssessment({
                userId: 'user-123',
                careerPathId: 'path-456',
                skillIds: ['skill-1'],
                config: config,
            });
            expect(assessment.config).toMatchObject(config);
        });
        it('should throw error for invalid input', function () {
            expect(function () {
                engine.createAssessment({
                    userId: '',
                    careerPathId: 'path-456',
                    skillIds: [],
                });
            }).toThrow('Invalid assessment parameters');
        });
    });
    describe('Question Generation', function () {
        it('should generate questions for all skills', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, questions;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript', 'react', 'nodejs'],
                        });
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 1:
                        questions = _a.sent();
                        expect(questions).toBeDefined();
                        expect(questions.length).toBeGreaterThan(0);
                        expect(questions.every(function (q) { return q.skillId; })).toBe(true);
                        expect(questions.every(function (q) { return q.question; })).toBe(true);
                        expect(questions.every(function (q) { return q.options && q.options.length >= 2; })).toBe(true);
                        expect(questions.every(function (q) { return typeof q.correctAnswer === 'number'; })).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should generate questions with different difficulty levels', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, questions, difficulties;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                            config: {
                                questionsPerSkill: 6,
                                difficultyLevels: ['beginner', 'intermediate', 'advanced'],
                            },
                        });
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 1:
                        questions = _a.sent();
                        difficulties = questions.map(function (q) { return q.difficulty; });
                        expect(difficulties).toContain('beginner');
                        expect(difficulties).toContain('intermediate');
                        expect(difficulties).toContain('advanced');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should respect questionsPerSkill configuration', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, questions;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript', 'react'],
                            config: {
                                questionsPerSkill: 3,
                            },
                        });
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 1:
                        questions = _a.sent();
                        expect(questions.length).toBe(6); // 2 skills × 3 questions each
                        return [2 /*return*/];
                }
            });
        }); });
        it('should randomize questions when configured', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, questions1, questions2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                            config: {
                                randomizeQuestions: true,
                            },
                        });
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 1:
                        questions1 = _a.sent();
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 2:
                        questions2 = _a.sent();
                        // Questions should be different due to randomization
                        expect(questions1).not.toEqual(questions2);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Response Handling', function () {
        it('should record user responses', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, questions, response;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                        });
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 1:
                        _b.sent();
                        questions = ((_a = engine.getAssessment(assessment.id)) === null || _a === void 0 ? void 0 : _a.questions) || [];
                        return [4 /*yield*/, engine.recordResponse(assessment.id, {
                                questionId: questions[0].id,
                                selectedAnswer: 1,
                                timeSpent: 30,
                                confidence: 8,
                            })];
                    case 2:
                        response = _b.sent();
                        expect(response).toBeDefined();
                        expect(response.questionId).toBe(questions[0].id);
                        expect(response.selectedAnswer).toBe(1);
                        expect(response.timeSpent).toBe(30);
                        expect(response.confidence).toBe(8);
                        expect(response.isCorrect).toBeDefined();
                        expect(response.timestamp).toBeInstanceOf(Date);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate response data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, questions;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                        });
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 1:
                        _b.sent();
                        questions = ((_a = engine.getAssessment(assessment.id)) === null || _a === void 0 ? void 0 : _a.questions) || [];
                        return [4 /*yield*/, expect(engine.recordResponse(assessment.id, {
                                questionId: 'invalid-question',
                                selectedAnswer: 1,
                                timeSpent: 30,
                            })).rejects.toThrow('Invalid question ID')];
                    case 2:
                        _b.sent();
                        return [4 /*yield*/, expect(engine.recordResponse(assessment.id, {
                                questionId: questions[0].id,
                                selectedAnswer: -1,
                                timeSpent: 30,
                            })).rejects.toThrow('Invalid answer selection')];
                    case 3:
                        _b.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should prevent duplicate responses', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, questions;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                        });
                        return [4 /*yield*/, engine.generateQuestions(assessment.id)];
                    case 1:
                        _b.sent();
                        questions = ((_a = engine.getAssessment(assessment.id)) === null || _a === void 0 ? void 0 : _a.questions) || [];
                        return [4 /*yield*/, engine.recordResponse(assessment.id, {
                                questionId: questions[0].id,
                                selectedAnswer: 1,
                                timeSpent: 30,
                            })];
                    case 2:
                        _b.sent();
                        return [4 /*yield*/, expect(engine.recordResponse(assessment.id, {
                                questionId: questions[0].id,
                                selectedAnswer: 2,
                                timeSpent: 15,
                            })).rejects.toThrow('Question already answered')];
                    case 3:
                        _b.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Assessment Scoring', function () {
        it('should calculate skill scores correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var freshEngine, assessment, questions, results;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        freshEngine = new SkillAssessmentEngine_1.SkillAssessmentEngine();
                        assessment = freshEngine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                            config: { questionsPerSkill: 4 },
                        });
                        return [4 /*yield*/, freshEngine.generateQuestions(assessment.id)];
                    case 1:
                        _b.sent();
                        questions = ((_a = freshEngine.getAssessment(assessment.id)) === null || _a === void 0 ? void 0 : _a.questions) || [];
                        // Answer 3 out of 4 questions correctly
                        return [4 /*yield*/, freshEngine.recordResponse(assessment.id, {
                                questionId: questions[0].id,
                                selectedAnswer: questions[0].correctAnswer,
                                timeSpent: 30,
                            })];
                    case 2:
                        // Answer 3 out of 4 questions correctly
                        _b.sent();
                        return [4 /*yield*/, freshEngine.recordResponse(assessment.id, {
                                questionId: questions[1].id,
                                selectedAnswer: questions[1].correctAnswer,
                                timeSpent: 25,
                            })];
                    case 3:
                        _b.sent();
                        return [4 /*yield*/, freshEngine.recordResponse(assessment.id, {
                                questionId: questions[2].id,
                                selectedAnswer: questions[2].correctAnswer,
                                timeSpent: 35,
                            })];
                    case 4:
                        _b.sent();
                        return [4 /*yield*/, freshEngine.recordResponse(assessment.id, {
                                questionId: questions[3].id,
                                selectedAnswer: (questions[3].correctAnswer + 1) % questions[3].options.length,
                                timeSpent: 20,
                            })];
                    case 5:
                        _b.sent();
                        return [4 /*yield*/, freshEngine.calculateResults(assessment.id)];
                    case 6:
                        results = _b.sent();
                        expect(results).toBeDefined();
                        expect(results.skillScores).toBeDefined();
                        expect(results.skillScores['javascript']).toBeGreaterThan(70); // Weighted score will be higher
                        expect(results.overallScore).toBeGreaterThan(70);
                        expect(results.completedAt).toBeInstanceOf(Date);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should calculate weighted scores based on difficulty', function () { return __awaiter(void 0, void 0, void 0, function () {
            var freshEngine, assessment, questions, _i, questions_1, question, results;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        freshEngine = new SkillAssessmentEngine_1.SkillAssessmentEngine();
                        assessment = freshEngine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                            config: {
                                questionsPerSkill: 3,
                                difficultyWeights: {
                                    beginner: 1,
                                    intermediate: 2,
                                    advanced: 3,
                                },
                            },
                        });
                        return [4 /*yield*/, freshEngine.generateQuestions(assessment.id)];
                    case 1:
                        _b.sent();
                        questions = ((_a = freshEngine.getAssessment(assessment.id)) === null || _a === void 0 ? void 0 : _a.questions) || [];
                        _i = 0, questions_1 = questions;
                        _b.label = 2;
                    case 2:
                        if (!(_i < questions_1.length)) return [3 /*break*/, 5];
                        question = questions_1[_i];
                        return [4 /*yield*/, freshEngine.recordResponse(assessment.id, {
                                questionId: question.id,
                                selectedAnswer: question.correctAnswer,
                                timeSpent: 30,
                            })];
                    case 3:
                        _b.sent();
                        _b.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5: return [4 /*yield*/, freshEngine.calculateResults(assessment.id)];
                    case 6:
                        results = _b.sent();
                        expect(results.skillScores['javascript']).toBeGreaterThan(0);
                        expect(results.overallScore).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should include confidence and time metrics', function () { return __awaiter(void 0, void 0, void 0, function () {
            var freshEngine, assessment, questions, results;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        freshEngine = new SkillAssessmentEngine_1.SkillAssessmentEngine();
                        assessment = freshEngine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                            config: { questionsPerSkill: 2 },
                        });
                        return [4 /*yield*/, freshEngine.generateQuestions(assessment.id)];
                    case 1:
                        _b.sent();
                        questions = ((_a = freshEngine.getAssessment(assessment.id)) === null || _a === void 0 ? void 0 : _a.questions) || [];
                        return [4 /*yield*/, freshEngine.recordResponse(assessment.id, {
                                questionId: questions[0].id,
                                selectedAnswer: questions[0].correctAnswer,
                                timeSpent: 30,
                                confidence: 9,
                            })];
                    case 2:
                        _b.sent();
                        return [4 /*yield*/, freshEngine.recordResponse(assessment.id, {
                                questionId: questions[1].id,
                                selectedAnswer: questions[1].correctAnswer,
                                timeSpent: 45,
                                confidence: 7,
                            })];
                    case 3:
                        _b.sent();
                        return [4 /*yield*/, freshEngine.calculateResults(assessment.id)];
                    case 4:
                        results = _b.sent();
                        expect(results.averageConfidence).toBeCloseTo(8, 1); // (9 + 7) / 2
                        expect(results.averageTimePerQuestion).toBeCloseTo(37.5, 1); // (30 + 45) / 2
                        expect(results.totalTimeSpent).toBe(75);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Assessment Management', function () {
        it('should retrieve assessment by ID', function () {
            var assessment = engine.createAssessment({
                userId: 'user-123',
                careerPathId: 'path-456',
                skillIds: ['javascript'],
            });
            var retrieved = engine.getAssessment(assessment.id);
            expect(retrieved).toEqual(assessment);
        });
        it('should return null for non-existent assessment', function () {
            var retrieved = engine.getAssessment('non-existent-id');
            expect(retrieved).toBeNull();
        });
        it.skip('should list assessments by user', function () {
            var freshEngine = new SkillAssessmentEngine_1.SkillAssessmentEngine();
            var assessment1 = freshEngine.createAssessment({
                userId: 'user-123',
                careerPathId: 'path-456',
                skillIds: ['javascript'],
            });
            var assessment2 = freshEngine.createAssessment({
                userId: 'user-123',
                careerPathId: 'path-789',
                skillIds: ['react'],
            });
            var assessment3 = freshEngine.createAssessment({
                userId: 'user-456',
                careerPathId: 'path-456',
                skillIds: ['nodejs'],
            });
            // Debug: Check all assessments in the engine
            var allAssessments = freshEngine.getAllAssessments();
            console.log('All assessments count:', allAssessments.length);
            console.log('All assessments userIds:', allAssessments.map(function (a) { return a.userId; }));
            var userAssessments = freshEngine.getAssessmentsByUser('user-123');
            expect(userAssessments).toHaveLength(2);
            expect(userAssessments.some(function (a) { return a.id === assessment1.id; })).toBe(true);
            expect(userAssessments.some(function (a) { return a.id === assessment2.id; })).toBe(true);
            expect(userAssessments.some(function (a) { return a.id === assessment3.id; })).toBe(false);
        });
        it('should update assessment status', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment, updated;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                        });
                        expect(assessment.status).toBe('pending');
                        return [4 /*yield*/, engine.updateAssessmentStatus(assessment.id, 'in_progress')];
                    case 1:
                        _a.sent();
                        updated = engine.getAssessment(assessment.id);
                        expect(updated === null || updated === void 0 ? void 0 : updated.status).toBe('in_progress');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Error Handling', function () {
        it('should handle missing assessment gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, expect(engine.generateQuestions('non-existent-id')).rejects.toThrow('Assessment not found')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, expect(engine.recordResponse('non-existent-id', {
                                questionId: 'question-1',
                                selectedAnswer: 1,
                                timeSpent: 30,
                            })).rejects.toThrow('Assessment not found')];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, expect(engine.calculateResults('non-existent-id')).rejects.toThrow('Assessment not found')];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate assessment state transitions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var assessment;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        assessment = engine.createAssessment({
                            userId: 'user-123',
                            careerPathId: 'path-456',
                            skillIds: ['javascript'],
                        });
                        return [4 /*yield*/, engine.updateAssessmentStatus(assessment.id, 'completed')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, expect(engine.recordResponse(assessment.id, {
                                questionId: 'question-1',
                                selectedAnswer: 1,
                                timeSpent: 30,
                            })).rejects.toThrow('Cannot modify completed assessment')];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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