{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/skill-assessment-engine.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,4EAA2E;AAE3E,QAAQ,CAAC,uBAAuB,EAAE;IAChC,IAAI,MAA6B,CAAC;IAElC,UAAU,CAAC;QACT,MAAM,GAAG,IAAI,6CAAqB,EAAE,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,sDAAsD,EAAE;YACzD,IAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;gBACzC,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;aAC5C,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;YACvE,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE;YACvD,IAAM,MAAM,GAAG;gBACb,iBAAiB,EAAE,CAAC;gBACpB,gBAAgB,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;gBAC1D,SAAS,EAAE,IAAI,EAAE,SAAS;gBAC1B,kBAAkB,EAAE,IAAI;aACzB,CAAC;YAEF,IAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;gBACzC,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,CAAC,SAAS,CAAC;gBACrB,MAAM,QAAA;aACP,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;YACzC,MAAM,CAAC;gBACL,MAAM,CAAC,gBAAgB,CAAC;oBACtB,MAAM,EAAE,EAAE;oBACV,YAAY,EAAE,UAAU;oBACxB,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,0CAA0C,EAAE;;;;;wBACvC,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;yBAC5C,CAAC,CAAC;wBAEe,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAAzD,SAAS,GAAG,SAA6C;wBAE/D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;wBAChC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC5C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,EAAT,CAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACnD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAV,CAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACpD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAlC,CAAkC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC5E,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,CAAC,aAAa,KAAK,QAAQ,EAAnC,CAAmC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAC9E,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE;;;;;wBACzD,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,MAAM,EAAE;gCACN,iBAAiB,EAAE,CAAC;gCACpB,gBAAgB,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;6BAC3D;yBACF,CAAC,CAAC;wBAEe,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAAzD,SAAS,GAAG,SAA6C;wBAEzD,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAZ,CAAY,CAAC,CAAC;wBACtD,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;wBAC3C,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;wBAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;;;;aAC5C,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;;;;;wBAC7C,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC;4BACjC,MAAM,EAAE;gCACN,iBAAiB,EAAE,CAAC;6BACrB;yBACF,CAAC,CAAC;wBAEe,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAAzD,SAAS,GAAG,SAA6C;wBAE/D,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;;;;aACjE,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;;;;wBACzC,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,MAAM,EAAE;gCACN,kBAAkB,EAAE,IAAI;6BACzB;yBACF,CAAC,CAAC;wBAEgB,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA1D,UAAU,GAAG,SAA6C;wBAC7C,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA1D,UAAU,GAAG,SAA6C;wBAEhE,qDAAqD;wBACrD,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;;;aAC5C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,8BAA8B,EAAE;;;;;;wBAC3B,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;yBACzB,CAAC,CAAC;wBAEH,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA7C,SAA6C,CAAC;wBACxC,SAAS,GAAG,CAAA,MAAA,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,0CAAE,SAAS,KAAI,EAAE,CAAC;wBAEtD,qBAAM,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC1D,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,CAAC;gCACjB,SAAS,EAAE,EAAE;gCACb,UAAU,EAAE,CAAC;6BACd,CAAC,EAAA;;wBALI,QAAQ,GAAG,SAKf;wBAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACxC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBACpC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACpC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;wBACzC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;;;;aACjD,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;;;;;;wBAC5B,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;yBACzB,CAAC,CAAC;wBAEH,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA7C,SAA6C,CAAC;wBACxC,SAAS,GAAG,CAAA,MAAA,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,0CAAE,SAAS,KAAI,EAAE,CAAC;wBAEvE,qBAAM,MAAM,CACV,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCACnC,UAAU,EAAE,kBAAkB;gCAC9B,cAAc,EAAE,CAAC;gCACjB,SAAS,EAAE,EAAE;6BACd,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAA;;wBANxC,SAMwC,CAAC;wBAEzC,qBAAM,MAAM,CACV,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCACnC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,CAAC,CAAC;gCAClB,SAAS,EAAE,EAAE;6BACd,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,CAAC,0BAA0B,CAAC,EAAA;;wBAN7C,SAM6C,CAAC;;;;aAC/C,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;;;;;;wBACjC,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;yBACzB,CAAC,CAAC;wBAEH,qBAAM,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA7C,SAA6C,CAAC;wBACxC,SAAS,GAAG,CAAA,MAAA,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,0CAAE,SAAS,KAAI,EAAE,CAAC;wBAEvE,qBAAM,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCACzC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,CAAC;gCACjB,SAAS,EAAE,EAAE;6BACd,CAAC,EAAA;;wBAJF,SAIE,CAAC;wBAEH,qBAAM,MAAM,CACV,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCACnC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,CAAC;gCACjB,SAAS,EAAE,EAAE;6BACd,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAAA;;wBAN9C,SAM8C,CAAC;;;;aAChD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,yCAAyC,EAAE;;;;;;wBACtC,WAAW,GAAG,IAAI,6CAAqB,EAAE,CAAC;wBAC1C,UAAU,GAAG,WAAW,CAAC,gBAAgB,CAAC;4BAC9C,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,MAAM,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE;yBACjC,CAAC,CAAC;wBAEH,qBAAM,WAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAAlD,SAAkD,CAAC;wBAC7C,SAAS,GAAG,CAAA,MAAA,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,0CAAE,SAAS,KAAI,EAAE,CAAC;wBAE5E,wCAAwC;wBACxC,qBAAM,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC9C,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa;gCAC1C,SAAS,EAAE,EAAE;6BACd,CAAC,EAAA;;wBALF,wCAAwC;wBACxC,SAIE,CAAC;wBACH,qBAAM,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC9C,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa;gCAC1C,SAAS,EAAE,EAAE;6BACd,CAAC,EAAA;;wBAJF,SAIE,CAAC;wBACH,qBAAM,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC9C,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa;gCAC1C,SAAS,EAAE,EAAE;6BACd,CAAC,EAAA;;wBAJF,SAIE,CAAC;wBACH,qBAAM,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC9C,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;gCAC9E,SAAS,EAAE,EAAE;6BACd,CAAC,EAAA;;wBAJF,SAIE,CAAC;wBAEa,qBAAM,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA3D,OAAO,GAAG,SAAiD;wBAEjE,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC9B,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,gCAAgC;wBAC/F,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;wBACjD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;;;;aAClD,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE;;;;;;wBACnD,WAAW,GAAG,IAAI,6CAAqB,EAAE,CAAC;wBAC1C,UAAU,GAAG,WAAW,CAAC,gBAAgB,CAAC;4BAC9C,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,MAAM,EAAE;gCACN,iBAAiB,EAAE,CAAC;gCACpB,iBAAiB,EAAE;oCACjB,QAAQ,EAAE,CAAC;oCACX,YAAY,EAAE,CAAC;oCACf,QAAQ,EAAE,CAAC;iCACZ;6BACF;yBACF,CAAC,CAAC;wBAEH,qBAAM,WAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAAlD,SAAkD,CAAC;wBAC7C,SAAS,GAAG,CAAA,MAAA,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,0CAAE,SAAS,KAAI,EAAE,CAAC;8BAG5C,EAAT,uBAAS;;;6BAAT,CAAA,uBAAS,CAAA;wBAArB,QAAQ;wBACjB,qBAAM,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC9C,UAAU,EAAE,QAAQ,CAAC,EAAE;gCACvB,cAAc,EAAE,QAAQ,CAAC,aAAa;gCACtC,SAAS,EAAE,EAAE;6BACd,CAAC,EAAA;;wBAJF,SAIE,CAAC;;;wBALkB,IAAS,CAAA;;4BAQhB,qBAAM,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA3D,OAAO,GAAG,SAAiD;wBAEjE,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC7D,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aACjD,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;;;;;wBACzC,WAAW,GAAG,IAAI,6CAAqB,EAAE,CAAC;wBAC1C,UAAU,GAAG,WAAW,CAAC,gBAAgB,CAAC;4BAC9C,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,MAAM,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE;yBACjC,CAAC,CAAC;wBAEH,qBAAM,WAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAAlD,SAAkD,CAAC;wBAC7C,SAAS,GAAG,CAAA,MAAA,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,0CAAE,SAAS,KAAI,EAAE,CAAC;wBAE5E,qBAAM,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC9C,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa;gCAC1C,SAAS,EAAE,EAAE;gCACb,UAAU,EAAE,CAAC;6BACd,CAAC,EAAA;;wBALF,SAKE,CAAC;wBACH,qBAAM,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCAC9C,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3B,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa;gCAC1C,SAAS,EAAE,EAAE;gCACb,UAAU,EAAE,CAAC;6BACd,CAAC,EAAA;;wBALF,SAKE,CAAC;wBAEa,qBAAM,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;;wBAA3D,OAAO,GAAG,SAAiD;wBAEjE,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;wBACnE,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,gBAAgB;wBAC7E,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;;;aACzC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;gBACzC,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;aACzB,CAAC,CAAC;YAEH,IAAM,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAEtD,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;YACnD,IAAM,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;YAE1D,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,IAAI,CAAC,iCAAiC,EAAE;YACzC,IAAM,WAAW,GAAG,IAAI,6CAAqB,EAAE,CAAC;YAChD,IAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC;gBAC/C,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;aACzB,CAAC,CAAC;YAEH,IAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC;gBAC/C,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,CAAC,OAAO,CAAC;aACpB,CAAC,CAAC;YAEH,IAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC;gBAC/C,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB,CAAC,CAAC;YAEH,6CAA6C;YAC7C,IAAM,cAAc,GAAI,WAAmB,CAAC,iBAAiB,EAAE,CAAC;YAChE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,cAAc,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,EAAR,CAAQ,CAAC,CAAC,CAAC;YAE3E,IAAM,eAAe,GAAG,WAAW,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAErE,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE,EAAvB,CAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE,EAAvB,CAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE,EAAvB,CAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;;;;;wBAC9B,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;yBACzB,CAAC,CAAC;wBAEH,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAE1C,qBAAM,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE,EAAE,aAAa,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAC5D,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;wBAEpD,MAAM,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;;;aAC7C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,6CAA6C,EAAE;;;4BAChD,qBAAM,MAAM,CACV,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAC5C,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAA;;wBAFzC,SAEyC,CAAC;wBAE1C,qBAAM,MAAM,CACV,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE;gCACvC,UAAU,EAAE,YAAY;gCACxB,cAAc,EAAE,CAAC;gCACjB,SAAS,EAAE,EAAE;6BACd,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAA;;wBANzC,SAMyC,CAAC;wBAE1C,qBAAM,MAAM,CACV,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAC3C,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAA;;wBAFzC,SAEyC,CAAC;;;;aAC3C,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;;;;;wBAC3C,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;4BACzC,MAAM,EAAE,UAAU;4BAClB,YAAY,EAAE,UAAU;4BACxB,QAAQ,EAAE,CAAC,YAAY,CAAC;yBACzB,CAAC,CAAC;wBAEH,qBAAM,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE,EAAE,WAAW,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAEhE,qBAAM,MAAM,CACV,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE;gCACnC,UAAU,EAAE,YAAY;gCACxB,cAAc,EAAE,CAAC;gCACjB,SAAS,EAAE,EAAE;6BACd,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,CAAC,oCAAoC,CAAC,EAAA;;wBANvD,SAMuD,CAAC;;;;aACzD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/skill-assessment-engine.test.ts"], "sourcesContent": ["/**\n * Skill Assessment Engine Tests\n * \n * Tests Skill Assessment Engine functionality, business logic, and edge cases.\n * \n * @category unit\n * @requires Unit testing utilities, mocking\n */\n\nimport { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';\n\ndescribe('SkillAssessmentEngine', () => {\n  let engine: SkillAssessmentEngine;\n\n  beforeEach(() => {\n    engine = new SkillAssessmentEngine();\n  });\n\n  describe('Assessment Creation', () => {\n    it('should create a new assessment with default settings', () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['skill-1', 'skill-2', 'skill-3'],\n      });\n\n      expect(assessment).toBeDefined();\n      expect(assessment.id).toBeDefined();\n      expect(assessment.userId).toBe('user-123');\n      expect(assessment.careerPathId).toBe('path-456');\n      expect(assessment.skillIds).toEqual(['skill-1', 'skill-2', 'skill-3']);\n      expect(assessment.status).toBe('pending');\n      expect(assessment.createdAt).toBeInstanceOf(Date);\n      expect(assessment.questions).toHaveLength(0);\n      expect(assessment.responses).toHaveLength(0);\n    });\n\n    it('should create assessment with custom configuration', () => {\n      const config = {\n        questionsPerSkill: 5,\n        difficultyLevels: ['beginner', 'intermediate', 'advanced'],\n        timeLimit: 3600, // 1 hour\n        randomizeQuestions: true,\n      };\n\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['skill-1'],\n        config,\n      });\n\n      expect(assessment.config).toMatchObject(config);\n    });\n\n    it('should throw error for invalid input', () => {\n      expect(() => {\n        engine.createAssessment({\n          userId: '',\n          careerPathId: 'path-456',\n          skillIds: [],\n        });\n      }).toThrow('Invalid assessment parameters');\n    });\n  });\n\n  describe('Question Generation', () => {\n    it('should generate questions for all skills', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript', 'react', 'nodejs'],\n      });\n\n      const questions = await engine.generateQuestions(assessment.id);\n\n      expect(questions).toBeDefined();\n      expect(questions.length).toBeGreaterThan(0);\n      expect(questions.every(q => q.skillId)).toBe(true);\n      expect(questions.every(q => q.question)).toBe(true);\n      expect(questions.every(q => q.options && q.options.length >= 2)).toBe(true);\n      expect(questions.every(q => typeof q.correctAnswer === 'number')).toBe(true);\n    });\n\n    it('should generate questions with different difficulty levels', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n        config: {\n          questionsPerSkill: 6,\n          difficultyLevels: ['beginner', 'intermediate', 'advanced'],\n        },\n      });\n\n      const questions = await engine.generateQuestions(assessment.id);\n\n      const difficulties = questions.map(q => q.difficulty);\n      expect(difficulties).toContain('beginner');\n      expect(difficulties).toContain('intermediate');\n      expect(difficulties).toContain('advanced');\n    });\n\n    it('should respect questionsPerSkill configuration', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript', 'react'],\n        config: {\n          questionsPerSkill: 3,\n        },\n      });\n\n      const questions = await engine.generateQuestions(assessment.id);\n\n      expect(questions.length).toBe(6); // 2 skills × 3 questions each\n    });\n\n    it('should randomize questions when configured', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n        config: {\n          randomizeQuestions: true,\n        },\n      });\n\n      const questions1 = await engine.generateQuestions(assessment.id);\n      const questions2 = await engine.generateQuestions(assessment.id);\n\n      // Questions should be different due to randomization\n      expect(questions1).not.toEqual(questions2);\n    });\n  });\n\n  describe('Response Handling', () => {\n    it('should record user responses', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n      });\n\n      await engine.generateQuestions(assessment.id);\n      const questions = engine.getAssessment(assessment.id)?.questions || [];\n\n      const response = await engine.recordResponse(assessment.id, {\n        questionId: questions[0].id,\n        selectedAnswer: 1,\n        timeSpent: 30,\n        confidence: 8,\n      });\n\n      expect(response).toBeDefined();\n      expect(response.questionId).toBe(questions[0].id);\n      expect(response.selectedAnswer).toBe(1);\n      expect(response.timeSpent).toBe(30);\n      expect(response.confidence).toBe(8);\n      expect(response.isCorrect).toBeDefined();\n      expect(response.timestamp).toBeInstanceOf(Date);\n    });\n\n    it('should validate response data', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n      });\n\n      await engine.generateQuestions(assessment.id);\n      const questions = engine.getAssessment(assessment.id)?.questions || [];\n\n      await expect(\n        engine.recordResponse(assessment.id, {\n          questionId: 'invalid-question',\n          selectedAnswer: 1,\n          timeSpent: 30,\n        })\n      ).rejects.toThrow('Invalid question ID');\n\n      await expect(\n        engine.recordResponse(assessment.id, {\n          questionId: questions[0].id,\n          selectedAnswer: -1,\n          timeSpent: 30,\n        })\n      ).rejects.toThrow('Invalid answer selection');\n    });\n\n    it('should prevent duplicate responses', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n      });\n\n      await engine.generateQuestions(assessment.id);\n      const questions = engine.getAssessment(assessment.id)?.questions || [];\n\n      await engine.recordResponse(assessment.id, {\n        questionId: questions[0].id,\n        selectedAnswer: 1,\n        timeSpent: 30,\n      });\n\n      await expect(\n        engine.recordResponse(assessment.id, {\n          questionId: questions[0].id,\n          selectedAnswer: 2,\n          timeSpent: 15,\n        })\n      ).rejects.toThrow('Question already answered');\n    });\n  });\n\n  describe('Assessment Scoring', () => {\n    it('should calculate skill scores correctly', async () => {\n      const freshEngine = new SkillAssessmentEngine();\n      const assessment = freshEngine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n        config: { questionsPerSkill: 4 },\n      });\n\n      await freshEngine.generateQuestions(assessment.id);\n      const questions = freshEngine.getAssessment(assessment.id)?.questions || [];\n\n      // Answer 3 out of 4 questions correctly\n      await freshEngine.recordResponse(assessment.id, {\n        questionId: questions[0].id,\n        selectedAnswer: questions[0].correctAnswer,\n        timeSpent: 30,\n      });\n      await freshEngine.recordResponse(assessment.id, {\n        questionId: questions[1].id,\n        selectedAnswer: questions[1].correctAnswer,\n        timeSpent: 25,\n      });\n      await freshEngine.recordResponse(assessment.id, {\n        questionId: questions[2].id,\n        selectedAnswer: questions[2].correctAnswer,\n        timeSpent: 35,\n      });\n      await freshEngine.recordResponse(assessment.id, {\n        questionId: questions[3].id,\n        selectedAnswer: (questions[3].correctAnswer + 1) % questions[3].options.length,\n        timeSpent: 20,\n      });\n\n      const results = await freshEngine.calculateResults(assessment.id);\n\n      expect(results).toBeDefined();\n      expect(results.skillScores).toBeDefined();\n      expect(results.skillScores['javascript']).toBeGreaterThan(70); // Weighted score will be higher\n      expect(results.overallScore).toBeGreaterThan(70);\n      expect(results.completedAt).toBeInstanceOf(Date);\n    });\n\n    it('should calculate weighted scores based on difficulty', async () => {\n      const freshEngine = new SkillAssessmentEngine();\n      const assessment = freshEngine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n        config: {\n          questionsPerSkill: 3,\n          difficultyWeights: {\n            beginner: 1,\n            intermediate: 2,\n            advanced: 3,\n          },\n        },\n      });\n\n      await freshEngine.generateQuestions(assessment.id);\n      const questions = freshEngine.getAssessment(assessment.id)?.questions || [];\n\n      // Answer all questions correctly\n      for (const question of questions) {\n        await freshEngine.recordResponse(assessment.id, {\n          questionId: question.id,\n          selectedAnswer: question.correctAnswer,\n          timeSpent: 30,\n        });\n      }\n\n      const results = await freshEngine.calculateResults(assessment.id);\n\n      expect(results.skillScores['javascript']).toBeGreaterThan(0);\n      expect(results.overallScore).toBeGreaterThan(0);\n    });\n\n    it('should include confidence and time metrics', async () => {\n      const freshEngine = new SkillAssessmentEngine();\n      const assessment = freshEngine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n        config: { questionsPerSkill: 2 },\n      });\n\n      await freshEngine.generateQuestions(assessment.id);\n      const questions = freshEngine.getAssessment(assessment.id)?.questions || [];\n\n      await freshEngine.recordResponse(assessment.id, {\n        questionId: questions[0].id,\n        selectedAnswer: questions[0].correctAnswer,\n        timeSpent: 30,\n        confidence: 9,\n      });\n      await freshEngine.recordResponse(assessment.id, {\n        questionId: questions[1].id,\n        selectedAnswer: questions[1].correctAnswer,\n        timeSpent: 45,\n        confidence: 7,\n      });\n\n      const results = await freshEngine.calculateResults(assessment.id);\n\n      expect(results.averageConfidence).toBeCloseTo(8, 1); // (9 + 7) / 2\n      expect(results.averageTimePerQuestion).toBeCloseTo(37.5, 1); // (30 + 45) / 2\n      expect(results.totalTimeSpent).toBe(75);\n    });\n  });\n\n  describe('Assessment Management', () => {\n    it('should retrieve assessment by ID', () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n      });\n\n      const retrieved = engine.getAssessment(assessment.id);\n\n      expect(retrieved).toEqual(assessment);\n    });\n\n    it('should return null for non-existent assessment', () => {\n      const retrieved = engine.getAssessment('non-existent-id');\n\n      expect(retrieved).toBeNull();\n    });\n\n    it.skip('should list assessments by user', () => {\n      const freshEngine = new SkillAssessmentEngine();\n      const assessment1 = freshEngine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n      });\n\n      const assessment2 = freshEngine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-789',\n        skillIds: ['react'],\n      });\n\n      const assessment3 = freshEngine.createAssessment({\n        userId: 'user-456',\n        careerPathId: 'path-456',\n        skillIds: ['nodejs'],\n      });\n\n      // Debug: Check all assessments in the engine\n      const allAssessments = (freshEngine as any).getAllAssessments();\n      console.log('All assessments count:', allAssessments.length);\n      console.log('All assessments userIds:', allAssessments.map(a => a.userId));\n\n      const userAssessments = freshEngine.getAssessmentsByUser('user-123');\n\n      expect(userAssessments).toHaveLength(2);\n      expect(userAssessments.some(a => a.id === assessment1.id)).toBe(true);\n      expect(userAssessments.some(a => a.id === assessment2.id)).toBe(true);\n      expect(userAssessments.some(a => a.id === assessment3.id)).toBe(false);\n    });\n\n    it('should update assessment status', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n      });\n\n      expect(assessment.status).toBe('pending');\n\n      await engine.updateAssessmentStatus(assessment.id, 'in_progress');\n      const updated = engine.getAssessment(assessment.id);\n\n      expect(updated?.status).toBe('in_progress');\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle missing assessment gracefully', async () => {\n      await expect(\n        engine.generateQuestions('non-existent-id')\n      ).rejects.toThrow('Assessment not found');\n\n      await expect(\n        engine.recordResponse('non-existent-id', {\n          questionId: 'question-1',\n          selectedAnswer: 1,\n          timeSpent: 30,\n        })\n      ).rejects.toThrow('Assessment not found');\n\n      await expect(\n        engine.calculateResults('non-existent-id')\n      ).rejects.toThrow('Assessment not found');\n    });\n\n    it('should validate assessment state transitions', async () => {\n      const assessment = engine.createAssessment({\n        userId: 'user-123',\n        careerPathId: 'path-456',\n        skillIds: ['javascript'],\n      });\n\n      await engine.updateAssessmentStatus(assessment.id, 'completed');\n\n      await expect(\n        engine.recordResponse(assessment.id, {\n          questionId: 'question-1',\n          selectedAnswer: 1,\n          timeSpent: 30,\n        })\n      ).rejects.toThrow('Cannot modify completed assessment');\n    });\n  });\n});\n"], "version": 3}