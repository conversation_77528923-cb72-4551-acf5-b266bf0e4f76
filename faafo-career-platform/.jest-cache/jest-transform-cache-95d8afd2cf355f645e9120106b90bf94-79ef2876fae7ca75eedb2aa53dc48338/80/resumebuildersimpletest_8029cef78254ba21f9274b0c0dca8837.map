{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/resume-builder-simple.test.ts", "mappings": ";AAAA;;;;GAIG;;AAEH,2BAAwB;AAExB,wDAAwD;AACxD,IAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACtD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;IACpD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,yBAAyB,CAAC;IAClD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACtD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;CACxD,CAAC,CAAC;AAEH,IAAM,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IACtD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;IACnD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACtD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,YAAY,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;CAC7C,CAAC,CAAC;AAEH,IAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC;IACzD,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC;IAC/C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC9B,CAAC,CAAC;AAEH,IAAM,WAAW,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACjD,KAAK,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC5E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAChC,CAAC,CAAC;AAEH,IAAM,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IACpD,YAAY,EAAE,kBAAkB;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE;IAChD,SAAS,EAAE,OAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE;IAC9C,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;IACvC,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACtC,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IAChF,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACrC,CAAC,CAAC;AAEH,QAAQ,CAAC,+BAA+B,EAAE;IACxC,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,0BAA0B;gBACjC,YAAY,EAAE;oBACZ,SAAS,EAAE,MAAM;oBACjB,QAAQ,EAAE,KAAK;oBACf,KAAK,EAAE,sBAAsB;oBAC7B,KAAK,EAAE,aAAa;oBACpB,QAAQ,EAAE,mBAAmB;oBAC7B,OAAO,EAAE,qBAAqB;oBAC9B,QAAQ,EAAE,iCAAiC;iBAC5C;gBACD,OAAO,EAAE,4DAA4D;gBACrE,UAAU,EAAE;oBACV;wBACE,OAAO,EAAE,WAAW;wBACpB,QAAQ,EAAE,0BAA0B;wBACpC,SAAS,EAAE,SAAS;wBACpB,OAAO,EAAE,SAAS;wBAClB,WAAW,EAAE,sCAAsC;wBACnD,YAAY,EAAE,CAAC,6BAA6B,EAAE,uBAAuB,CAAC;qBACvE;iBACF;gBACD,SAAS,EAAE;oBACT;wBACE,WAAW,EAAE,0BAA0B;wBACvC,MAAM,EAAE,qBAAqB;wBAC7B,KAAK,EAAE,kBAAkB;wBACzB,SAAS,EAAE,SAAS;wBACpB,OAAO,EAAE,SAAS;wBAClB,GAAG,EAAE,KAAK;qBACX;iBACF;gBACD,MAAM,EAAE;oBACN;wBACE,IAAI,EAAE,YAAY;wBAClB,KAAK,EAAE,UAAU;wBACjB,QAAQ,EAAE,uBAAuB;qBAClC;iBACF;gBACD,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;YACtD,IAAM,aAAa,GAAG;gBACpB,gBAAgB;gBAChB,YAAY,EAAE;oBACZ,SAAS,EAAE,MAAM;oBACjB,QAAQ,EAAE,KAAK;oBACf,KAAK,EAAE,eAAe,CAAC,uBAAuB;iBAC/C;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAA5B,CAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAA5B,CAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;YACnD,IAAM,iBAAiB,GAAG;gBACxB,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE,wBAAwB;gBAC/B,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,cAAc;gBACxB,OAAO,EAAE,uBAAuB;gBAChC,QAAQ,EAAE,mCAAmC;aAC9C,CAAC;YAEF,mCAAmC;YACnC,IAAM,MAAM,GAAG;gBACb,KAAK,EAAE,aAAa;gBACpB,YAAY,EAAE,iBAAiB;gBAC/B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAM,eAAe,GAAG;gBACtB;oBACE,OAAO,EAAE,WAAW;oBACpB,QAAQ,EAAE,sBAAsB;oBAChC,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,SAAS;oBAClB,WAAW,EAAE,qDAAqD;oBAClE,YAAY,EAAE;wBACZ,+CAA+C;wBAC/C,gCAAgC;qBACjC;iBACF;gBACD;oBACE,OAAO,EAAE,iBAAiB;oBAC1B,QAAQ,EAAE,kBAAkB;oBAC5B,SAAS,EAAE,SAAS;oBACpB,mCAAmC;oBACnC,WAAW,EAAE,oCAAoC;iBAClD;aACF,CAAC;YAEF,IAAM,MAAM,GAAG;gBACb,KAAK,EAAE,kBAAkB;gBACzB,YAAY,EAAE;oBACZ,SAAS,EAAE,MAAM;oBACjB,QAAQ,EAAE,MAAM;oBAChB,KAAK,EAAE,kBAAkB;iBAC1B;gBACD,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE;YAChD,IAAM,cAAc,GAAG;gBACrB;oBACE,WAAW,EAAE,kBAAkB;oBAC/B,MAAM,EAAE,qBAAqB;oBAC7B,KAAK,EAAE,kBAAkB;oBACzB,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,SAAS;oBAClB,GAAG,EAAE,KAAK;oBACV,MAAM,EAAE,iBAAiB;iBAC1B;gBACD;oBACE,WAAW,EAAE,gBAAgB;oBAC7B,MAAM,EAAE,aAAa;oBACrB,KAAK,EAAE,iBAAiB;oBACxB,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,SAAS;iBACnB;aACF,CAAC;YAEF,IAAM,MAAM,GAAG;gBACb,KAAK,EAAE,iBAAiB;gBACxB,YAAY,EAAE;oBACZ,SAAS,EAAE,MAAM;oBACjB,QAAQ,EAAE,UAAU;oBACpB,KAAK,EAAE,sBAAsB;iBAC9B;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,cAAc;gBACzB,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAM,WAAW,GAAG;gBAClB;oBACE,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,UAAU;oBACjB,QAAQ,EAAE,qBAAqB;iBAChC;gBACD;oBACE,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,cAAc;oBACrB,QAAQ,EAAE,sBAAsB;iBACjC;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,cAAc;oBACrB,QAAQ,EAAE,WAAW;iBACtB;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,UAAU;oBACjB,QAAQ,EAAE,aAAa;iBACxB;aACF,CAAC;YAEF,IAAM,MAAM,GAAG;gBACb,KAAK,EAAE,uBAAuB;gBAC9B,YAAY,EAAE;oBACZ,SAAS,EAAE,SAAS;oBACpB,QAAQ,EAAE,WAAW;oBACrB,KAAK,EAAE,qBAAqB;iBAC7B;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,WAAW;aACpB,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YAE/D,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;gBACxB,IAAM,MAAM,GAAG;oBACb,KAAK,EAAE,UAAG,QAAQ,YAAS;oBAC3B,YAAY,EAAE;wBACZ,SAAS,EAAE,MAAM;wBACjB,QAAQ,EAAE,MAAM;wBAChB,KAAK,EAAE,kBAAkB;qBAC1B;oBACD,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE,EAAE;oBACV,QAAQ,UAAA;iBACT,CAAC;gBAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACxD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAM,MAAM,GAAG;gBACb,KAAK,EAAE,yBAAyB;gBAChC,YAAY,EAAE;oBACZ,SAAS,EAAE,MAAM;oBACjB,QAAQ,EAAE,MAAM;oBAChB,KAAK,EAAE,kBAAkB;iBAC1B;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,kBAAkB;aAC7B,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAM,WAAW,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEvE,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;gBACvB,IAAM,MAAM,GAAG;oBACb,KAAK,EAAE,kBAAkB;oBACzB,YAAY,EAAE;wBACZ,SAAS,EAAE,MAAM;wBACjB,QAAQ,EAAE,MAAM;wBAChB,KAAK,EAAE,kBAAkB;qBAC1B;oBACD,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE;wBACN;4BACE,IAAI,EAAE,YAAY;4BAClB,KAAK,OAAA;4BACL,QAAQ,EAAE,eAAe;yBAC1B;qBACF;iBACF,CAAC;gBAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACxD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;YAC5C,IAAM,aAAa,GAAG;gBACpB,KAAK,EAAE,gBAAgB;gBACvB,YAAY,EAAE;oBACZ,SAAS,EAAE,KAAK;oBAChB,QAAQ,EAAE,MAAM;oBAChB,KAAK,EAAE,iBAAiB;iBACzB;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE;YACzD,IAAM,cAAc,GAAG;gBACrB,KAAK,EAAE,iBAAiB;gBACxB,YAAY,EAAE;oBACZ,SAAS,EAAE,KAAK;oBAChB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,iBAAiB;oBACxB,OAAO,EAAE,qBAAqB;oBAC9B,QAAQ,EAAE,mCAAmC;iBAC9C;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;YACtC,IAAM,qBAAqB,GAAG;gBAC5B,KAAK,EAAE,oBAAoB;gBAC3B,YAAY,EAAE;oBACZ,SAAS,EAAE,SAAS;oBACpB,QAAQ,EAAE,KAAK;oBACf,KAAK,EAAE,qBAAqB;oBAC5B,OAAO,EAAE,WAAW;oBACpB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,IAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAA9B,CAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAA/B,CAA+B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/resume-builder-simple.test.ts"], "sourcesContent": ["/**\n * Simple Resume Builder Tests\n *\n * Basic tests for Resume Builder functionality without complex dependencies\n */\n\nimport { z } from 'zod';\n\n// Define the validation schemas (copied from API route)\nconst personalInfoSchema = z.object({\n  firstName: z.string().min(1, 'First name is required'),\n  lastName: z.string().min(1, 'Last name is required'),\n  email: z.string().email('Valid email is required'),\n  phone: z.string().optional(),\n  location: z.string().optional(),\n  website: z.string().url().optional().or(z.literal('')),\n  linkedIn: z.string().url().optional().or(z.literal(''))\n});\n\nconst experienceSchema = z.object({\n  company: z.string().min(1, 'Company name is required'),\n  position: z.string().min(1, 'Position is required'),\n  startDate: z.string().min(1, 'Start date is required'),\n  endDate: z.string().optional(),\n  description: z.string().optional(),\n  achievements: z.array(z.string()).optional()\n});\n\nconst educationSchema = z.object({\n  institution: z.string().min(1, 'Institution is required'),\n  degree: z.string().min(1, 'Degree is required'),\n  field: z.string().optional(),\n  startDate: z.string().optional(),\n  endDate: z.string().optional(),\n  gpa: z.string().optional(),\n  honors: z.string().optional()\n});\n\nconst skillSchema = z.object({\n  name: z.string().min(1, 'Skill name is required'),\n  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),\n  category: z.string().optional()\n});\n\nconst resumeValidationSchema = z.object({\n  title: z.string().min(1, 'Resume title is required'),\n  personalInfo: personalInfoSchema,\n  summary: z.string().optional(),\n  experience: z.array(experienceSchema).optional(),\n  education: z.array(educationSchema).optional(),\n  skills: z.array(skillSchema).optional(),\n  sections: z.record(z.any()).optional(),\n  template: z.enum(['modern', 'classic', 'minimal', 'creative']).default('modern'),\n  isPublic: z.boolean().default(false)\n});\n\ndescribe('Resume Builder - Simple Tests', () => {\n  describe('Resume Validation Schema', () => {\n    it('should validate a complete resume object', () => {\n      const validResume = {\n        title: 'Software Engineer Resume',\n        personalInfo: {\n          firstName: 'John',\n          lastName: 'Doe',\n          email: '<EMAIL>',\n          phone: '******-0123',\n          location: 'San Francisco, CA',\n          website: 'https://johndoe.com',\n          linkedIn: 'https://linkedin.com/in/johndoe'\n        },\n        summary: 'Experienced software engineer with 5+ years of experience.',\n        experience: [\n          {\n            company: 'Tech Corp',\n            position: 'Senior Software Engineer',\n            startDate: '2020-01',\n            endDate: '2023-12',\n            description: 'Led development of web applications.',\n            achievements: ['Improved performance by 40%', 'Mentored 3 developers']\n          }\n        ],\n        education: [\n          {\n            institution: 'University of California',\n            degree: 'Bachelor of Science',\n            field: 'Computer Science',\n            startDate: '2016-09',\n            endDate: '2020-05',\n            gpa: '3.8'\n          }\n        ],\n        skills: [\n          {\n            name: 'JavaScript',\n            level: 'ADVANCED',\n            category: 'Programming Languages'\n          }\n        ],\n        template: 'modern',\n        isPublic: false\n      };\n\n      const result = resumeValidationSchema.safeParse(validResume);\n      expect(result.success).toBe(true);\n    });\n\n    it('should reject resume with missing required fields', () => {\n      const invalidResume = {\n        // Missing title\n        personalInfo: {\n          firstName: 'John',\n          lastName: 'Doe',\n          email: 'invalid-email' // Invalid email format\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const result = resumeValidationSchema.safeParse(invalidResume);\n      expect(result.success).toBe(false);\n      \n      if (!result.success) {\n        const errors = result.error.issues;\n        expect(errors.some(error => error.path.includes('title'))).toBe(true);\n        expect(errors.some(error => error.path.includes('email'))).toBe(true);\n      }\n    });\n\n    it('should validate personal info fields correctly', () => {\n      const validPersonalInfo = {\n        firstName: 'Jane',\n        lastName: 'Smith',\n        email: '<EMAIL>',\n        phone: '******-0456',\n        location: 'New York, NY',\n        website: 'https://janesmith.dev',\n        linkedIn: 'https://linkedin.com/in/janesmith'\n      };\n\n      // Test as part of a minimal resume\n      const resume = {\n        title: 'Test Resume',\n        personalInfo: validPersonalInfo,\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const result = resumeValidationSchema.safeParse(resume);\n      expect(result.success).toBe(true);\n    });\n\n    it('should validate experience entries correctly', () => {\n      const validExperience = [\n        {\n          company: 'StartupCo',\n          position: 'Full Stack Developer',\n          startDate: '2021-06',\n          endDate: '2023-12',\n          description: 'Developed web applications using React and Node.js.',\n          achievements: [\n            'Built authentication system for 10,000+ users',\n            'Reduced page load times by 60%'\n          ]\n        },\n        {\n          company: 'Current Company',\n          position: 'Senior Developer',\n          startDate: '2024-01',\n          // No end date for current position\n          description: 'Leading frontend development team.'\n        }\n      ];\n\n      const resume = {\n        title: 'Developer Resume',\n        personalInfo: {\n          firstName: 'Test',\n          lastName: 'User',\n          email: '<EMAIL>'\n        },\n        experience: validExperience,\n        education: [],\n        skills: []\n      };\n\n      const result = resumeValidationSchema.safeParse(resume);\n      expect(result.success).toBe(true);\n    });\n\n    it('should validate education entries correctly', () => {\n      const validEducation = [\n        {\n          institution: 'State University',\n          degree: 'Bachelor of Science',\n          field: 'Computer Science',\n          startDate: '2017-09',\n          endDate: '2021-05',\n          gpa: '3.7',\n          honors: 'Magna Cum Laude'\n        },\n        {\n          institution: 'Online Academy',\n          degree: 'Certificate',\n          field: 'Web Development',\n          startDate: '2021-01',\n          endDate: '2021-03'\n        }\n      ];\n\n      const resume = {\n        title: 'Graduate Resume',\n        personalInfo: {\n          firstName: 'Test',\n          lastName: 'Graduate',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: validEducation,\n        skills: []\n      };\n\n      const result = resumeValidationSchema.safeParse(resume);\n      expect(result.success).toBe(true);\n    });\n\n    it('should validate skills entries correctly', () => {\n      const validSkills = [\n        {\n          name: 'React',\n          level: 'ADVANCED',\n          category: 'Frontend Frameworks'\n        },\n        {\n          name: 'Node.js',\n          level: 'INTERMEDIATE',\n          category: 'Backend Technologies'\n        },\n        {\n          name: 'PostgreSQL',\n          level: 'INTERMEDIATE',\n          category: 'Databases'\n        },\n        {\n          name: 'Communication',\n          level: 'ADVANCED',\n          category: 'Soft Skills'\n        }\n      ];\n\n      const resume = {\n        title: 'Skills-focused Resume',\n        personalInfo: {\n          firstName: 'Skilled',\n          lastName: 'Developer',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: [],\n        skills: validSkills\n      };\n\n      const result = resumeValidationSchema.safeParse(resume);\n      expect(result.success).toBe(true);\n    });\n\n    it('should validate template options', () => {\n      const templates = ['modern', 'classic', 'minimal', 'creative'];\n      \n      templates.forEach(template => {\n        const resume = {\n          title: `${template} Resume`,\n          personalInfo: {\n            firstName: 'Test',\n            lastName: 'User',\n            email: '<EMAIL>'\n          },\n          experience: [],\n          education: [],\n          skills: [],\n          template\n        };\n\n        const result = resumeValidationSchema.safeParse(resume);\n        expect(result.success).toBe(true);\n      });\n    });\n\n    it('should reject invalid template options', () => {\n      const resume = {\n        title: 'Invalid Template Resume',\n        personalInfo: {\n          firstName: 'Test',\n          lastName: 'User',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: [],\n        skills: [],\n        template: 'invalid-template'\n      };\n\n      const result = resumeValidationSchema.safeParse(resume);\n      expect(result.success).toBe(false);\n    });\n\n    it('should validate skill levels correctly', () => {\n      const validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];\n      \n      validLevels.forEach(level => {\n        const resume = {\n          title: 'Skill Level Test',\n          personalInfo: {\n            firstName: 'Test',\n            lastName: 'User',\n            email: '<EMAIL>'\n          },\n          experience: [],\n          education: [],\n          skills: [\n            {\n              name: 'Test Skill',\n              level,\n              category: 'Test Category'\n            }\n          ]\n        };\n\n        const result = resumeValidationSchema.safeParse(resume);\n        expect(result.success).toBe(true);\n      });\n    });\n\n    it('should handle optional fields correctly', () => {\n      const minimalResume = {\n        title: 'Minimal Resume',\n        personalInfo: {\n          firstName: 'Min',\n          lastName: 'User',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const result = resumeValidationSchema.safeParse(minimalResume);\n      expect(result.success).toBe(true);\n    });\n\n    it('should validate URL formats for website and LinkedIn', () => {\n      const resumeWithUrls = {\n        title: 'URL Test Resume',\n        personalInfo: {\n          firstName: 'URL',\n          lastName: 'Tester',\n          email: '<EMAIL>',\n          website: 'https://example.com',\n          linkedIn: 'https://linkedin.com/in/urltester'\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const result = resumeValidationSchema.safeParse(resumeWithUrls);\n      expect(result.success).toBe(true);\n    });\n\n    it('should reject invalid URL formats', () => {\n      const resumeWithInvalidUrls = {\n        title: 'Invalid URL Resume',\n        personalInfo: {\n          firstName: 'Invalid',\n          lastName: 'URL',\n          email: '<EMAIL>',\n          website: 'not-a-url',\n          linkedIn: 'also-not-a-url'\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const result = resumeValidationSchema.safeParse(resumeWithInvalidUrls);\n      expect(result.success).toBe(false);\n      \n      if (!result.success) {\n        const errors = result.error.issues;\n        expect(errors.some(error => error.path.includes('website'))).toBe(true);\n        expect(errors.some(error => error.path.includes('linkedIn'))).toBe(true);\n      }\n    });\n  });\n});\n"], "version": 3}