9858c0f61376b9c404ea91091aa9a861
"use strict";

/* istanbul ignore next */
function cov_2ha0euo22() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/reset-password/route.ts";
  var hash = "4171daa00e05c6bbc7b3d7a8781774dc18357d15";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/reset-password/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 22
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 15
        },
        end: {
          line: 44,
          column: 55
        }
      },
      "73": {
        start: {
          line: 45,
          column: 17
        },
        end: {
          line: 45,
          column: 53
        }
      },
      "74": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 76
        }
      },
      "75": {
        start: {
          line: 47,
          column: 30
        },
        end: {
          line: 47,
          column: 68
        }
      },
      "76": {
        start: {
          line: 48,
          column: 18
        },
        end: {
          line: 48,
          column: 44
        }
      },
      "77": {
        start: {
          line: 50,
          column: 24
        },
        end: {
          line: 50,
          column: 27
        }
      },
      "78": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 56,
          column: 7
        }
      },
      "79": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 55,
          column: 11
        }
      },
      "80": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 54,
          column: 118
        }
      },
      "81": {
        start: {
          line: 54,
          column: 67
        },
        end: {
          line: 54,
          column: 113
        }
      },
      "82": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 156,
          column: 7
        }
      },
      "83": {
        start: {
          line: 58,
          column: 94
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "84": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "85": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 154,
          column: 9
        }
      },
      "86": {
        start: {
          line: 62,
          column: 20
        },
        end: {
          line: 62,
          column: 112
        }
      },
      "87": {
        start: {
          line: 64,
          column: 16
        },
        end: {
          line: 64,
          column: 44
        }
      },
      "88": {
        start: {
          line: 65,
          column: 16
        },
        end: {
          line: 70,
          column: 17
        }
      },
      "89": {
        start: {
          line: 66,
          column: 20
        },
        end: {
          line: 66,
          column: 99
        }
      },
      "90": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 67,
          column: 43
        }
      },
      "91": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 68,
          column: 60
        }
      },
      "92": {
        start: {
          line: 69,
          column: 20
        },
        end: {
          line: 69,
          column: 32
        }
      },
      "93": {
        start: {
          line: 71,
          column: 16
        },
        end: {
          line: 71,
          column: 39
        }
      },
      "94": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 72,
          column: 29
        }
      },
      "95": {
        start: {
          line: 74,
          column: 16
        },
        end: {
          line: 74,
          column: 44
        }
      },
      "96": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 75,
          column: 53
        }
      },
      "97": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 73
        }
      },
      "98": {
        start: {
          line: 78,
          column: 16
        },
        end: {
          line: 78,
          column: 68
        }
      },
      "99": {
        start: {
          line: 78,
          column: 44
        },
        end: {
          line: 78,
          column: 68
        }
      },
      "100": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 79,
          column: 54
        }
      },
      "101": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 81,
          column: 26
        }
      },
      "102": {
        start: {
          line: 82,
          column: 16
        },
        end: {
          line: 82,
          column: 72
        }
      },
      "103": {
        start: {
          line: 84,
          column: 16
        },
        end: {
          line: 84,
          column: 89
        }
      },
      "104": {
        start: {
          line: 85,
          column: 16
        },
        end: {
          line: 85,
          column: 74
        }
      },
      "105": {
        start: {
          line: 85,
          column: 50
        },
        end: {
          line: 85,
          column: 74
        }
      },
      "106": {
        start: {
          line: 86,
          column: 16
        },
        end: {
          line: 86,
          column: 54
        }
      },
      "107": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 26
        }
      },
      "108": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 83
        }
      },
      "109": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 90,
          column: 39
        }
      },
      "110": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 68
        }
      },
      "111": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 28
        }
      },
      "112": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 97,
          column: 20
        }
      },
      "113": {
        start: {
          line: 99,
          column: 16
        },
        end: {
          line: 99,
          column: 33
        }
      },
      "114": {
        start: {
          line: 100,
          column: 16
        },
        end: {
          line: 100,
          column: 37
        }
      },
      "115": {
        start: {
          line: 101,
          column: 16
        },
        end: {
          line: 101,
          column: 36
        }
      },
      "116": {
        start: {
          line: 102,
          column: 16
        },
        end: {
          line: 102,
          column: 117
        }
      },
      "117": {
        start: {
          line: 102,
          column: 92
        },
        end: {
          line: 102,
          column: 117
        }
      },
      "118": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 103,
          column: 97
        }
      },
      "119": {
        start: {
          line: 105,
          column: 16
        },
        end: {
          line: 105,
          column: 41
        }
      },
      "120": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 106,
          column: 41
        }
      },
      "121": {
        start: {
          line: 109,
          column: 12
        },
        end: {
          line: 109,
          column: 142
        }
      },
      "122": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 112,
          column: 26
        }
      },
      "123": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 30
        }
      },
      "124": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 115,
          column: 53
        }
      },
      "125": {
        start: {
          line: 116,
          column: 16
        },
        end: {
          line: 116,
          column: 80
        }
      },
      "126": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 70
        }
      },
      "127": {
        start: {
          line: 117,
          column: 45
        },
        end: {
          line: 117,
          column: 70
        }
      },
      "128": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 118,
          column: 120
        }
      },
      "129": {
        start: {
          line: 118,
          column: 70
        },
        end: {
          line: 118,
          column: 115
        }
      },
      "130": {
        start: {
          line: 120,
          column: 16
        },
        end: {
          line: 120,
          column: 26
        }
      },
      "131": {
        start: {
          line: 121,
          column: 16
        },
        end: {
          line: 121,
          column: 30
        }
      },
      "132": {
        start: {
          line: 123,
          column: 16
        },
        end: {
          line: 125,
          column: 17
        }
      },
      "133": {
        start: {
          line: 124,
          column: 20
        },
        end: {
          line: 124,
          column: 80
        }
      },
      "134": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 126,
          column: 76
        }
      },
      "135": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 128,
          column: 43
        }
      },
      "136": {
        start: {
          line: 129,
          column: 16
        },
        end: {
          line: 136,
          column: 24
        }
      },
      "137": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 138,
          column: 26
        }
      },
      "138": {
        start: {
          line: 139,
          column: 16
        },
        end: {
          line: 142,
          column: 24
        }
      },
      "139": {
        start: {
          line: 144,
          column: 16
        },
        end: {
          line: 144,
          column: 36
        }
      },
      "140": {
        start: {
          line: 145,
          column: 16
        },
        end: {
          line: 145,
          column: 53
        }
      },
      "141": {
        start: {
          line: 146,
          column: 16
        },
        end: {
          line: 146,
          column: 80
        }
      },
      "142": {
        start: {
          line: 147,
          column: 16
        },
        end: {
          line: 147,
          column: 70
        }
      },
      "143": {
        start: {
          line: 147,
          column: 45
        },
        end: {
          line: 147,
          column: 70
        }
      },
      "144": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 148,
          column: 120
        }
      },
      "145": {
        start: {
          line: 148,
          column: 70
        },
        end: {
          line: 148,
          column: 115
        }
      },
      "146": {
        start: {
          line: 150,
          column: 16
        },
        end: {
          line: 150,
          column: 26
        }
      },
      "147": {
        start: {
          line: 151,
          column: 16
        },
        end: {
          line: 151,
          column: 30
        }
      },
      "148": {
        start: {
          line: 152,
          column: 21
        },
        end: {
          line: 152,
          column: 35
        }
      },
      "149": {
        start: {
          line: 153,
          column: 21
        },
        end: {
          line: 153,
          column: 43
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "securityDelay",
        decl: {
          start: {
            line: 51,
            column: 9
          },
          end: {
            line: 51,
            column: 22
          }
        },
        loc: {
          start: {
            line: 51,
            column: 25
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 51
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 52,
            column: 44
          },
          end: {
            line: 52,
            column: 45
          }
        },
        loc: {
          start: {
            line: 52,
            column: 56
          },
          end: {
            line: 56,
            column: 5
          }
        },
        line: 52
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 53,
            column: 33
          },
          end: {
            line: 53,
            column: 34
          }
        },
        loc: {
          start: {
            line: 53,
            column: 47
          },
          end: {
            line: 55,
            column: 9
          }
        },
        line: 53
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 54,
            column: 46
          },
          end: {
            line: 54,
            column: 47
          }
        },
        loc: {
          start: {
            line: 54,
            column: 65
          },
          end: {
            line: 54,
            column: 115
          }
        },
        line: 54
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 58,
            column: 73
          },
          end: {
            line: 58,
            column: 74
          }
        },
        loc: {
          start: {
            line: 58,
            column: 92
          },
          end: {
            line: 156,
            column: 5
          }
        },
        line: 58
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 58,
            column: 136
          },
          end: {
            line: 58,
            column: 137
          }
        },
        loc: {
          start: {
            line: 58,
            column: 148
          },
          end: {
            line: 156,
            column: 1
          }
        },
        line: 58
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 60,
            column: 29
          },
          end: {
            line: 60,
            column: 30
          }
        },
        loc: {
          start: {
            line: 60,
            column: 43
          },
          end: {
            line: 155,
            column: 5
          }
        },
        line: 60
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 118,
            column: 49
          },
          end: {
            line: 118,
            column: 50
          }
        },
        loc: {
          start: {
            line: 118,
            column: 68
          },
          end: {
            line: 118,
            column: 117
          }
        },
        line: 118
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 148,
            column: 49
          },
          end: {
            line: 148,
            column: 50
          }
        },
        loc: {
          start: {
            line: 148,
            column: 68
          },
          end: {
            line: 148,
            column: 117
          }
        },
        line: 148
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 154,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 62,
            column: 12
          },
          end: {
            line: 62,
            column: 112
          }
        }, {
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 72,
            column: 29
          }
        }, {
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 75,
            column: 53
          }
        }, {
          start: {
            line: 76,
            column: 12
          },
          end: {
            line: 79,
            column: 54
          }
        }, {
          start: {
            line: 80,
            column: 12
          },
          end: {
            line: 82,
            column: 72
          }
        }, {
          start: {
            line: 83,
            column: 12
          },
          end: {
            line: 86,
            column: 54
          }
        }, {
          start: {
            line: 87,
            column: 12
          },
          end: {
            line: 92,
            column: 28
          }
        }, {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 97,
            column: 20
          }
        }, {
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 103,
            column: 97
          }
        }, {
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 106,
            column: 41
          }
        }, {
          start: {
            line: 107,
            column: 12
          },
          end: {
            line: 109,
            column: 142
          }
        }, {
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 113,
            column: 30
          }
        }, {
          start: {
            line: 114,
            column: 12
          },
          end: {
            line: 118,
            column: 120
          }
        }, {
          start: {
            line: 119,
            column: 12
          },
          end: {
            line: 121,
            column: 30
          }
        }, {
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 126,
            column: 76
          }
        }, {
          start: {
            line: 127,
            column: 12
          },
          end: {
            line: 136,
            column: 24
          }
        }, {
          start: {
            line: 137,
            column: 12
          },
          end: {
            line: 142,
            column: 24
          }
        }, {
          start: {
            line: 143,
            column: 12
          },
          end: {
            line: 148,
            column: 120
          }
        }, {
          start: {
            line: 149,
            column: 12
          },
          end: {
            line: 151,
            column: 30
          }
        }, {
          start: {
            line: 152,
            column: 12
          },
          end: {
            line: 152,
            column: 35
          }
        }, {
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 153,
            column: 43
          }
        }],
        line: 61
      },
      "36": {
        loc: {
          start: {
            line: 65,
            column: 16
          },
          end: {
            line: 70,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 65,
            column: 16
          },
          end: {
            line: 70,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 65
      },
      "37": {
        loc: {
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 78,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 78,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "38": {
        loc: {
          start: {
            line: 78,
            column: 22
          },
          end: {
            line: 78,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 78,
            column: 22
          },
          end: {
            line: 78,
            column: 28
          }
        }, {
          start: {
            line: 78,
            column: 32
          },
          end: {
            line: 78,
            column: 41
          }
        }],
        line: 78
      },
      "39": {
        loc: {
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 85,
            column: 74
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 85,
            column: 74
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "40": {
        loc: {
          start: {
            line: 102,
            column: 16
          },
          end: {
            line: 102,
            column: 117
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 16
          },
          end: {
            line: 102,
            column: 117
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "41": {
        loc: {
          start: {
            line: 102,
            column: 22
          },
          end: {
            line: 102,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 102,
            column: 57
          },
          end: {
            line: 102,
            column: 63
          }
        }, {
          start: {
            line: 102,
            column: 66
          },
          end: {
            line: 102,
            column: 89
          }
        }],
        line: 102
      },
      "42": {
        loc: {
          start: {
            line: 102,
            column: 22
          },
          end: {
            line: 102,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 22
          },
          end: {
            line: 102,
            column: 35
          }
        }, {
          start: {
            line: 102,
            column: 39
          },
          end: {
            line: 102,
            column: 54
          }
        }],
        line: 102
      },
      "43": {
        loc: {
          start: {
            line: 117,
            column: 16
          },
          end: {
            line: 117,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 16
          },
          end: {
            line: 117,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "44": {
        loc: {
          start: {
            line: 123,
            column: 16
          },
          end: {
            line: 125,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 16
          },
          end: {
            line: 125,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "45": {
        loc: {
          start: {
            line: 123,
            column: 20
          },
          end: {
            line: 123,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 20
          },
          end: {
            line: 123,
            column: 25
          }
        }, {
          start: {
            line: 123,
            column: 29
          },
          end: {
            line: 123,
            column: 42
          }
        }],
        line: 123
      },
      "46": {
        loc: {
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 147,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 147,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/reset-password/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,wDAAkC;AAClC,sDAA8B;AAC9B,6EAAwF;AACxF,qEAAmE;AACnE,6CAA2D;AAE3D,gEAAgE;AAChE,IAAM,iBAAiB,GAAG,GAAG,CAAC;AAE9B,SAAe,aAAa;mCAAI,OAAO;;YACrC,sBAAO,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAtC,CAAsC,CAAC,EAAC;;;CACvE;AAEY,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;oBAExD,qBAAM,4CAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAA;;gBAArE,eAAe,GAAG,SAAmD;gBAE3E,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;oBACvB,KAAK,GAAG,IAAI,KAAK,CAAC,2DAA2D,CAAQ,CAAC;oBAC5F,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;oBACxC,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEK,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;gBAGC,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAA1C,KAAsB,SAAoB,EAAxC,KAAK,WAAA,EAAE,QAAQ,cAAA;qBAEnB,CAAA,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAA,EAAnB,wBAAmB;gBACrB,qBAAM,aAAa,EAAE,EAAA;;gBAArB,SAAqB,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;;gBAIpD,kBAAkB,GAAG,IAAA,oCAAwB,EAAC,QAAQ,CAAC,CAAC;qBAC1D,CAAC,kBAAkB,CAAC,OAAO,EAA3B,wBAA2B;gBAC7B,qBAAM,aAAa,EAAE,EAAA;;gBAArB,SAAqB,CAAC;gBAChB,KAAK,GAAG,IAAI,KAAK,CAAC,+CAA+C,CAAQ,CAAC;gBAChF,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBACvB,KAAK,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBACpD,MAAM,KAAK,CAAC;oBAID,qBAAM,gBAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,KAAK,EAAE;wBACL,oBAAoB,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,4BAA4B;qBACvE;iBACF,CAAC,EAAA;;gBAJI,IAAI,GAAG,SAIX;gBAEE,YAAY,GAAG,KAAK,CAAC;gBACrB,cAAc,GAAG,EAAE,CAAC;qBAGpB,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,kBAAkB,CAAA,EAAxB,yBAAwB;gBACX,qBAAM,kBAAM,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAA;;gBAAnE,YAAY,GAAG,SAAoD,CAAC;;;YAEpE,+DAA+D;YAC/D,qBAAM,kBAAM,CAAC,OAAO,CAAC,KAAK,EAAE,wEAAwE,CAAC,EAAA;;gBADrG,+DAA+D;gBAC/D,SAAqG,CAAC;;;gBAIlG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACrC,mBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,WAAW,CAAC,CAAC;qBAChE,CAAA,gBAAc,GAAG,CAAC,CAAA,EAAlB,yBAAkB;gBACpB,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,gBAAc,CAAC,EAAnC,CAAmC,CAAC,EAAA;;gBAAjE,SAAiE,CAAC;;;gBAGpE,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBAC3B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBAC9D,CAAC;gBAEgB,qBAAM,kBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAA;;gBAAhD,cAAc,GAAG,SAA+B,CAAC;gBAEjD,qBAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;wBACvB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;wBACtB,IAAI,EAAE;4BACJ,QAAQ,EAAE,cAAc;4BACxB,kBAAkB,EAAE,IAAI;4BACxB,oBAAoB,EAAE,IAAI;yBAC3B;qBACF,CAAC,EAAA;;gBAPF,SAOE,CAAC;gBAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,EAAE,OAAO,EAAE,4CAA4C,EAAE;qBAChE,CAAC,EAAC;;;gBAIG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACrC,mBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,WAAW,CAAC,CAAC;qBAChE,CAAA,gBAAc,GAAG,CAAC,CAAA,EAAlB,yBAAkB;gBACpB,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,gBAAc,CAAC,EAAnC,CAAmC,CAAC,EAAA;;gBAAjE,SAAiE,CAAC;;qBAEpE,MAAM,OAAK,CAAC;;;;KAEf,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/reset-password/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport prisma from '@/lib/prisma';\nimport bcrypt from 'bcryptjs';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';\nimport { validatePasswordStrength } from '@/lib/rateLimit';\n\n// Timing attack protection - consistent delay for all responses\nconst SECURITY_DELAY_MS = 100;\n\nasync function securityDelay(): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, SECURITY_DELAY_MS));\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> => {\n  // SECURITY FIX: Apply strict rate limiting for password reset attempts\n  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);\n\n  if (!rateLimitResult.allowed) {\n    const error = new Error('Too many password reset attempts. Please try again later.') as any;\n    error.statusCode = 429;\n    error.headers = rateLimitResult.headers;\n    throw error;\n  }\n\n  const startTime = Date.now();\n\n  try {\n    const { token, password } = await request.json();\n\n    if (!token || !password) {\n      await securityDelay();\n      throw new Error('Token and new password are required.');\n    }\n\n    // SECURITY FIX: Validate password strength\n    const passwordValidation = validatePasswordStrength(password);\n    if (!passwordValidation.isValid) {\n      await securityDelay();\n      const error = new Error('Password does not meet security requirements.') as any;\n      error.statusCode = 400;\n      error.data = { details: passwordValidation.errors };\n      throw error;\n    }\n\n    // SECURITY FIX: Always perform database lookup and token comparison to prevent timing attacks\n    const user = await prisma.user.findFirst({\n      where: {\n        passwordResetExpires: { gt: new Date() }, // Token must not be expired\n      },\n    });\n\n    let isTokenValid = false;\n    let hashedPassword = '';\n\n    // Always perform bcrypt comparison even if user is null (timing attack protection)\n    if (user?.passwordResetToken) {\n      isTokenValid = await bcrypt.compare(token, user.passwordResetToken);\n    } else {\n      // Perform dummy bcrypt operation to maintain consistent timing\n      await bcrypt.compare(token, '$2a$12$dummy.hash.to.prevent.timing.attacks.abcdefghijklmnopqrstuvwxyz');\n    }\n\n    // SECURITY FIX: Ensure consistent response time regardless of token validity\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n\n    if (!user || !isTokenValid) {\n      throw new Error('Invalid or expired password reset token.');\n    }\n\n    hashedPassword = await bcrypt.hash(password, 12);\n\n    await prisma.user.update({\n      where: { id: user.id },\n      data: {\n        password: hashedPassword,\n        passwordResetToken: null,\n        passwordResetExpires: null,\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: { message: 'Your password has been reset successfully.' }\n    });\n\n  } catch (error) {\n    // SECURITY FIX: Ensure consistent timing even for errors\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n    throw error;\n  }\n});"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4171daa00e05c6bbc7b3d7a8781774dc18357d15"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2ha0euo22 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ha0euo22();
var __awaiter =
/* istanbul ignore next */
(cov_2ha0euo22().s[0]++,
/* istanbul ignore next */
(cov_2ha0euo22().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2ha0euo22().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2ha0euo22().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2ha0euo22().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2ha0euo22().f[1]++;
    cov_2ha0euo22().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2ha0euo22().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_2ha0euo22().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2ha0euo22().f[2]++;
      cov_2ha0euo22().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2ha0euo22().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_2ha0euo22().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_2ha0euo22().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2ha0euo22().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2ha0euo22().f[4]++;
      cov_2ha0euo22().s[4]++;
      try {
        /* istanbul ignore next */
        cov_2ha0euo22().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2ha0euo22().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2ha0euo22().f[5]++;
      cov_2ha0euo22().s[7]++;
      try {
        /* istanbul ignore next */
        cov_2ha0euo22().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2ha0euo22().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2ha0euo22().f[6]++;
      cov_2ha0euo22().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2ha0euo22().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2ha0euo22().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2ha0euo22().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2ha0euo22().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2ha0euo22().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2ha0euo22().s[12]++,
/* istanbul ignore next */
(cov_2ha0euo22().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_2ha0euo22().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2ha0euo22().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2ha0euo22().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_2ha0euo22().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2ha0euo22().f[8]++;
        cov_2ha0euo22().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2ha0euo22().b[6][0]++;
          cov_2ha0euo22().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2ha0euo22().b[6][1]++;
        }
        cov_2ha0euo22().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2ha0euo22().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2ha0euo22().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2ha0euo22().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2ha0euo22().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2ha0euo22().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2ha0euo22().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2ha0euo22().f[9]++;
    cov_2ha0euo22().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2ha0euo22().f[10]++;
    cov_2ha0euo22().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2ha0euo22().f[11]++;
      cov_2ha0euo22().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2ha0euo22().f[12]++;
    cov_2ha0euo22().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_2ha0euo22().b[9][0]++;
      cov_2ha0euo22().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2ha0euo22().b[9][1]++;
    }
    cov_2ha0euo22().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_2ha0euo22().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_2ha0euo22().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2ha0euo22().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2ha0euo22().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2ha0euo22().s[25]++;
      try {
        /* istanbul ignore next */
        cov_2ha0euo22().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2ha0euo22().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_2ha0euo22().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2ha0euo22().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2ha0euo22().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2ha0euo22().b[15][0]++,
        /* istanbul ignore next */
        (cov_2ha0euo22().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2ha0euo22().b[16][1]++,
        /* istanbul ignore next */
        (cov_2ha0euo22().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2ha0euo22().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2ha0euo22().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2ha0euo22().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2ha0euo22().b[12][0]++;
          cov_2ha0euo22().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2ha0euo22().b[12][1]++;
        }
        cov_2ha0euo22().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2ha0euo22().b[18][0]++;
          cov_2ha0euo22().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2ha0euo22().b[18][1]++;
        }
        cov_2ha0euo22().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2ha0euo22().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2ha0euo22().b[19][1]++;
            cov_2ha0euo22().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_2ha0euo22().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2ha0euo22().b[19][2]++;
            cov_2ha0euo22().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_2ha0euo22().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2ha0euo22().b[19][3]++;
            cov_2ha0euo22().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_2ha0euo22().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2ha0euo22().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_2ha0euo22().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2ha0euo22().b[19][4]++;
            cov_2ha0euo22().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2ha0euo22().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2ha0euo22().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2ha0euo22().b[19][5]++;
            cov_2ha0euo22().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_2ha0euo22().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2ha0euo22().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2ha0euo22().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2ha0euo22().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2ha0euo22().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2ha0euo22().b[20][0]++;
              cov_2ha0euo22().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2ha0euo22().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2ha0euo22().b[20][1]++;
            }
            cov_2ha0euo22().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_2ha0euo22().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2ha0euo22().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2ha0euo22().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2ha0euo22().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2ha0euo22().b[23][0]++;
              cov_2ha0euo22().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2ha0euo22().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2ha0euo22().b[23][1]++;
            }
            cov_2ha0euo22().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_2ha0euo22().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2ha0euo22().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2ha0euo22().b[25][0]++;
              cov_2ha0euo22().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2ha0euo22().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_2ha0euo22().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2ha0euo22().b[25][1]++;
            }
            cov_2ha0euo22().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_2ha0euo22().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_2ha0euo22().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2ha0euo22().b[27][0]++;
              cov_2ha0euo22().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2ha0euo22().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2ha0euo22().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2ha0euo22().b[27][1]++;
            }
            cov_2ha0euo22().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2ha0euo22().b[29][0]++;
              cov_2ha0euo22().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2ha0euo22().b[29][1]++;
            }
            cov_2ha0euo22().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2ha0euo22().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2ha0euo22().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2ha0euo22().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2ha0euo22().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2ha0euo22().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2ha0euo22().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2ha0euo22().b[30][0]++;
      cov_2ha0euo22().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2ha0euo22().b[30][1]++;
    }
    cov_2ha0euo22().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2ha0euo22().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2ha0euo22().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_2ha0euo22().s[67]++,
/* istanbul ignore next */
(cov_2ha0euo22().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_2ha0euo22().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2ha0euo22().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2ha0euo22().f[13]++;
  cov_2ha0euo22().s[68]++;
  return /* istanbul ignore next */(cov_2ha0euo22().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2ha0euo22().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2ha0euo22().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_2ha0euo22().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2ha0euo22().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2ha0euo22().s[70]++;
exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_2ha0euo22().s[71]++, require("next/server"));
var prisma_1 =
/* istanbul ignore next */
(cov_2ha0euo22().s[72]++, __importDefault(require("@/lib/prisma")));
var bcryptjs_1 =
/* istanbul ignore next */
(cov_2ha0euo22().s[73]++, __importDefault(require("bcryptjs")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_2ha0euo22().s[74]++, require("@/lib/unified-api-error-handler"));
var enhanced_rate_limiter_1 =
/* istanbul ignore next */
(cov_2ha0euo22().s[75]++, require("@/lib/enhanced-rate-limiter"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_2ha0euo22().s[76]++, require("@/lib/rateLimit"));
// Timing attack protection - consistent delay for all responses
var SECURITY_DELAY_MS =
/* istanbul ignore next */
(cov_2ha0euo22().s[77]++, 100);
function securityDelay() {
  /* istanbul ignore next */
  cov_2ha0euo22().f[14]++;
  cov_2ha0euo22().s[78]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2ha0euo22().f[15]++;
    cov_2ha0euo22().s[79]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2ha0euo22().f[16]++;
      cov_2ha0euo22().s[80]++;
      return [2 /*return*/, new Promise(function (resolve) {
        /* istanbul ignore next */
        cov_2ha0euo22().f[17]++;
        cov_2ha0euo22().s[81]++;
        return setTimeout(resolve, SECURITY_DELAY_MS);
      })];
    });
  });
}
/* istanbul ignore next */
cov_2ha0euo22().s[82]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_2ha0euo22().f[18]++;
  cov_2ha0euo22().s[83]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2ha0euo22().f[19]++;
    var rateLimitResult, error, startTime, _a, token, password, passwordValidation, error, user, isTokenValid, hashedPassword, elapsedTime, remainingDelay_1, error_1, elapsedTime, remainingDelay_2;
    /* istanbul ignore next */
    cov_2ha0euo22().s[84]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_2ha0euo22().f[20]++;
      cov_2ha0euo22().s[85]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][0]++;
          cov_2ha0euo22().s[86]++;
          return [4 /*yield*/, enhanced_rate_limiter_1.enhancedRateLimiters.auth.checkLimit(request)];
        case 1:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][1]++;
          cov_2ha0euo22().s[87]++;
          rateLimitResult = _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[88]++;
          if (!rateLimitResult.allowed) {
            /* istanbul ignore next */
            cov_2ha0euo22().b[36][0]++;
            cov_2ha0euo22().s[89]++;
            error = new Error('Too many password reset attempts. Please try again later.');
            /* istanbul ignore next */
            cov_2ha0euo22().s[90]++;
            error.statusCode = 429;
            /* istanbul ignore next */
            cov_2ha0euo22().s[91]++;
            error.headers = rateLimitResult.headers;
            /* istanbul ignore next */
            cov_2ha0euo22().s[92]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_2ha0euo22().b[36][1]++;
          }
          cov_2ha0euo22().s[93]++;
          startTime = Date.now();
          /* istanbul ignore next */
          cov_2ha0euo22().s[94]++;
          _b.label = 2;
        case 2:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][2]++;
          cov_2ha0euo22().s[95]++;
          _b.trys.push([2, 17,, 20]);
          /* istanbul ignore next */
          cov_2ha0euo22().s[96]++;
          return [4 /*yield*/, request.json()];
        case 3:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][3]++;
          cov_2ha0euo22().s[97]++;
          _a = _b.sent(), token = _a.token, password = _a.password;
          /* istanbul ignore next */
          cov_2ha0euo22().s[98]++;
          if (!(
          /* istanbul ignore next */
          (cov_2ha0euo22().b[38][0]++, !token) ||
          /* istanbul ignore next */
          (cov_2ha0euo22().b[38][1]++, !password))) {
            /* istanbul ignore next */
            cov_2ha0euo22().b[37][0]++;
            cov_2ha0euo22().s[99]++;
            return [3 /*break*/, 5];
          } else
          /* istanbul ignore next */
          {
            cov_2ha0euo22().b[37][1]++;
          }
          cov_2ha0euo22().s[100]++;
          return [4 /*yield*/, securityDelay()];
        case 4:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][4]++;
          cov_2ha0euo22().s[101]++;
          _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[102]++;
          throw new Error('Token and new password are required.');
        case 5:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][5]++;
          cov_2ha0euo22().s[103]++;
          passwordValidation = (0, rateLimit_1.validatePasswordStrength)(password);
          /* istanbul ignore next */
          cov_2ha0euo22().s[104]++;
          if (!!passwordValidation.isValid) {
            /* istanbul ignore next */
            cov_2ha0euo22().b[39][0]++;
            cov_2ha0euo22().s[105]++;
            return [3 /*break*/, 7];
          } else
          /* istanbul ignore next */
          {
            cov_2ha0euo22().b[39][1]++;
          }
          cov_2ha0euo22().s[106]++;
          return [4 /*yield*/, securityDelay()];
        case 6:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][6]++;
          cov_2ha0euo22().s[107]++;
          _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[108]++;
          error = new Error('Password does not meet security requirements.');
          /* istanbul ignore next */
          cov_2ha0euo22().s[109]++;
          error.statusCode = 400;
          /* istanbul ignore next */
          cov_2ha0euo22().s[110]++;
          error.data = {
            details: passwordValidation.errors
          };
          /* istanbul ignore next */
          cov_2ha0euo22().s[111]++;
          throw error;
        case 7:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][7]++;
          cov_2ha0euo22().s[112]++;
          return [4 /*yield*/, prisma_1.default.user.findFirst({
            where: {
              passwordResetExpires: {
                gt: new Date()
              } // Token must not be expired
            }
          })];
        case 8:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][8]++;
          cov_2ha0euo22().s[113]++;
          user = _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[114]++;
          isTokenValid = false;
          /* istanbul ignore next */
          cov_2ha0euo22().s[115]++;
          hashedPassword = '';
          /* istanbul ignore next */
          cov_2ha0euo22().s[116]++;
          if (!(
          /* istanbul ignore next */
          (cov_2ha0euo22().b[42][0]++, user === null) ||
          /* istanbul ignore next */
          (cov_2ha0euo22().b[42][1]++, user === void 0) ?
          /* istanbul ignore next */
          (cov_2ha0euo22().b[41][0]++, void 0) :
          /* istanbul ignore next */
          (cov_2ha0euo22().b[41][1]++, user.passwordResetToken))) {
            /* istanbul ignore next */
            cov_2ha0euo22().b[40][0]++;
            cov_2ha0euo22().s[117]++;
            return [3 /*break*/, 10];
          } else
          /* istanbul ignore next */
          {
            cov_2ha0euo22().b[40][1]++;
          }
          cov_2ha0euo22().s[118]++;
          return [4 /*yield*/, bcryptjs_1.default.compare(token, user.passwordResetToken)];
        case 9:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][9]++;
          cov_2ha0euo22().s[119]++;
          isTokenValid = _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[120]++;
          return [3 /*break*/, 12];
        case 10:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][10]++;
          cov_2ha0euo22().s[121]++;
          // Perform dummy bcrypt operation to maintain consistent timing
          return [4 /*yield*/, bcryptjs_1.default.compare(token, '$2a$12$dummy.hash.to.prevent.timing.attacks.abcdefghijklmnopqrstuvwxyz')];
        case 11:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][11]++;
          cov_2ha0euo22().s[122]++;
          // Perform dummy bcrypt operation to maintain consistent timing
          _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[123]++;
          _b.label = 12;
        case 12:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][12]++;
          cov_2ha0euo22().s[124]++;
          elapsedTime = Date.now() - startTime;
          /* istanbul ignore next */
          cov_2ha0euo22().s[125]++;
          remainingDelay_1 = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
          /* istanbul ignore next */
          cov_2ha0euo22().s[126]++;
          if (!(remainingDelay_1 > 0)) {
            /* istanbul ignore next */
            cov_2ha0euo22().b[43][0]++;
            cov_2ha0euo22().s[127]++;
            return [3 /*break*/, 14];
          } else
          /* istanbul ignore next */
          {
            cov_2ha0euo22().b[43][1]++;
          }
          cov_2ha0euo22().s[128]++;
          return [4 /*yield*/, new Promise(function (resolve) {
            /* istanbul ignore next */
            cov_2ha0euo22().f[21]++;
            cov_2ha0euo22().s[129]++;
            return setTimeout(resolve, remainingDelay_1);
          })];
        case 13:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][13]++;
          cov_2ha0euo22().s[130]++;
          _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[131]++;
          _b.label = 14;
        case 14:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][14]++;
          cov_2ha0euo22().s[132]++;
          if (
          /* istanbul ignore next */
          (cov_2ha0euo22().b[45][0]++, !user) ||
          /* istanbul ignore next */
          (cov_2ha0euo22().b[45][1]++, !isTokenValid)) {
            /* istanbul ignore next */
            cov_2ha0euo22().b[44][0]++;
            cov_2ha0euo22().s[133]++;
            throw new Error('Invalid or expired password reset token.');
          } else
          /* istanbul ignore next */
          {
            cov_2ha0euo22().b[44][1]++;
          }
          cov_2ha0euo22().s[134]++;
          return [4 /*yield*/, bcryptjs_1.default.hash(password, 12)];
        case 15:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][15]++;
          cov_2ha0euo22().s[135]++;
          hashedPassword = _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[136]++;
          return [4 /*yield*/, prisma_1.default.user.update({
            where: {
              id: user.id
            },
            data: {
              password: hashedPassword,
              passwordResetToken: null,
              passwordResetExpires: null
            }
          })];
        case 16:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][16]++;
          cov_2ha0euo22().s[137]++;
          _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[138]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              message: 'Your password has been reset successfully.'
            }
          })];
        case 17:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][17]++;
          cov_2ha0euo22().s[139]++;
          error_1 = _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[140]++;
          elapsedTime = Date.now() - startTime;
          /* istanbul ignore next */
          cov_2ha0euo22().s[141]++;
          remainingDelay_2 = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
          /* istanbul ignore next */
          cov_2ha0euo22().s[142]++;
          if (!(remainingDelay_2 > 0)) {
            /* istanbul ignore next */
            cov_2ha0euo22().b[46][0]++;
            cov_2ha0euo22().s[143]++;
            return [3 /*break*/, 19];
          } else
          /* istanbul ignore next */
          {
            cov_2ha0euo22().b[46][1]++;
          }
          cov_2ha0euo22().s[144]++;
          return [4 /*yield*/, new Promise(function (resolve) {
            /* istanbul ignore next */
            cov_2ha0euo22().f[22]++;
            cov_2ha0euo22().s[145]++;
            return setTimeout(resolve, remainingDelay_2);
          })];
        case 18:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][18]++;
          cov_2ha0euo22().s[146]++;
          _b.sent();
          /* istanbul ignore next */
          cov_2ha0euo22().s[147]++;
          _b.label = 19;
        case 19:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][19]++;
          cov_2ha0euo22().s[148]++;
          throw error_1;
        case 20:
          /* istanbul ignore next */
          cov_2ha0euo22().b[35][20]++;
          cov_2ha0euo22().s[149]++;
          return [2 /*return*/];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJzZXJ2ZXJfMSIsImNvdl8yaGEwZXVvMjIiLCJzIiwicmVxdWlyZSIsInByaXNtYV8xIiwiX19pbXBvcnREZWZhdWx0IiwiYmNyeXB0anNfMSIsInVuaWZpZWRfYXBpX2Vycm9yX2hhbmRsZXJfMSIsImVuaGFuY2VkX3JhdGVfbGltaXRlcl8xIiwicmF0ZUxpbWl0XzEiLCJTRUNVUklUWV9ERUxBWV9NUyIsInNlY3VyaXR5RGVsYXkiLCJmIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiZXhwb3J0cyIsIlBPU1QiLCJ3aXRoVW5pZmllZEVycm9ySGFuZGxpbmciLCJyZXF1ZXN0IiwiX19hd2FpdGVyIiwiZW5oYW5jZWRSYXRlTGltaXRlcnMiLCJhdXRoIiwiY2hlY2tMaW1pdCIsInJhdGVMaW1pdFJlc3VsdCIsIl9iIiwic2VudCIsImFsbG93ZWQiLCJiIiwiZXJyb3IiLCJFcnJvciIsInN0YXR1c0NvZGUiLCJoZWFkZXJzIiwic3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsImpzb24iLCJfYSIsInRva2VuIiwicGFzc3dvcmQiLCJwYXNzd29yZFZhbGlkYXRpb24iLCJ2YWxpZGF0ZVBhc3N3b3JkU3RyZW5ndGgiLCJpc1ZhbGlkIiwiZGF0YSIsImRldGFpbHMiLCJlcnJvcnMiLCJkZWZhdWx0IiwidXNlciIsImZpbmRGaXJzdCIsIndoZXJlIiwicGFzc3dvcmRSZXNldEV4cGlyZXMiLCJndCIsImlzVG9rZW5WYWxpZCIsImhhc2hlZFBhc3N3b3JkIiwicGFzc3dvcmRSZXNldFRva2VuIiwiY29tcGFyZSIsImVsYXBzZWRUaW1lIiwicmVtYWluaW5nRGVsYXlfMSIsIk1hdGgiLCJtYXgiLCJoYXNoIiwidXBkYXRlIiwiaWQiLCJOZXh0UmVzcG9uc2UiLCJzdWNjZXNzIiwibWVzc2FnZSIsInJlbWFpbmluZ0RlbGF5XzIiLCJlcnJvcl8xIl0sInNvdXJjZXMiOlsiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9hcHAvYXBpL2F1dGgvcmVzZXQtcGFzc3dvcmQvcm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCBwcmlzbWEgZnJvbSAnQC9saWIvcHJpc21hJztcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnO1xuaW1wb3J0IHsgd2l0aFVuaWZpZWRFcnJvckhhbmRsaW5nLCBBcGlSZXNwb25zZSB9IGZyb20gJ0AvbGliL3VuaWZpZWQtYXBpLWVycm9yLWhhbmRsZXInO1xuaW1wb3J0IHsgZW5oYW5jZWRSYXRlTGltaXRlcnMgfSBmcm9tICdAL2xpYi9lbmhhbmNlZC1yYXRlLWxpbWl0ZXInO1xuaW1wb3J0IHsgdmFsaWRhdGVQYXNzd29yZFN0cmVuZ3RoIH0gZnJvbSAnQC9saWIvcmF0ZUxpbWl0JztcblxuLy8gVGltaW5nIGF0dGFjayBwcm90ZWN0aW9uIC0gY29uc2lzdGVudCBkZWxheSBmb3IgYWxsIHJlc3BvbnNlc1xuY29uc3QgU0VDVVJJVFlfREVMQVlfTVMgPSAxMDA7XG5cbmFzeW5jIGZ1bmN0aW9uIHNlY3VyaXR5RGVsYXkoKTogUHJvbWlzZTx2b2lkPiB7XG4gIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgU0VDVVJJVFlfREVMQVlfTVMpKTtcbn1cblxuZXhwb3J0IGNvbnN0IFBPU1QgPSB3aXRoVW5pZmllZEVycm9ySGFuZGxpbmcoYXN5bmMgKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KTogUHJvbWlzZTxOZXh0UmVzcG9uc2U8QXBpUmVzcG9uc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4+PiA9PiB7XG4gIC8vIFNFQ1VSSVRZIEZJWDogQXBwbHkgc3RyaWN0IHJhdGUgbGltaXRpbmcgZm9yIHBhc3N3b3JkIHJlc2V0IGF0dGVtcHRzXG4gIGNvbnN0IHJhdGVMaW1pdFJlc3VsdCA9IGF3YWl0IGVuaGFuY2VkUmF0ZUxpbWl0ZXJzLmF1dGguY2hlY2tMaW1pdChyZXF1ZXN0KTtcblxuICBpZiAoIXJhdGVMaW1pdFJlc3VsdC5hbGxvd2VkKSB7XG4gICAgY29uc3QgZXJyb3IgPSBuZXcgRXJyb3IoJ1RvbyBtYW55IHBhc3N3b3JkIHJlc2V0IGF0dGVtcHRzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicpIGFzIGFueTtcbiAgICBlcnJvci5zdGF0dXNDb2RlID0gNDI5O1xuICAgIGVycm9yLmhlYWRlcnMgPSByYXRlTGltaXRSZXN1bHQuaGVhZGVycztcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxuXG4gIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG5cbiAgdHJ5IHtcbiAgICBjb25zdCB7IHRva2VuLCBwYXNzd29yZCB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICBpZiAoIXRva2VuIHx8ICFwYXNzd29yZCkge1xuICAgICAgYXdhaXQgc2VjdXJpdHlEZWxheSgpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdUb2tlbiBhbmQgbmV3IHBhc3N3b3JkIGFyZSByZXF1aXJlZC4nKTtcbiAgICB9XG5cbiAgICAvLyBTRUNVUklUWSBGSVg6IFZhbGlkYXRlIHBhc3N3b3JkIHN0cmVuZ3RoXG4gICAgY29uc3QgcGFzc3dvcmRWYWxpZGF0aW9uID0gdmFsaWRhdGVQYXNzd29yZFN0cmVuZ3RoKHBhc3N3b3JkKTtcbiAgICBpZiAoIXBhc3N3b3JkVmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICBhd2FpdCBzZWN1cml0eURlbGF5KCk7XG4gICAgICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcignUGFzc3dvcmQgZG9lcyBub3QgbWVldCBzZWN1cml0eSByZXF1aXJlbWVudHMuJykgYXMgYW55O1xuICAgICAgZXJyb3Iuc3RhdHVzQ29kZSA9IDQwMDtcbiAgICAgIGVycm9yLmRhdGEgPSB7IGRldGFpbHM6IHBhc3N3b3JkVmFsaWRhdGlvbi5lcnJvcnMgfTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIC8vIFNFQ1VSSVRZIEZJWDogQWx3YXlzIHBlcmZvcm0gZGF0YWJhc2UgbG9va3VwIGFuZCB0b2tlbiBjb21wYXJpc29uIHRvIHByZXZlbnQgdGltaW5nIGF0dGFja3NcbiAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZEZpcnN0KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHBhc3N3b3JkUmVzZXRFeHBpcmVzOiB7IGd0OiBuZXcgRGF0ZSgpIH0sIC8vIFRva2VuIG11c3Qgbm90IGJlIGV4cGlyZWRcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBsZXQgaXNUb2tlblZhbGlkID0gZmFsc2U7XG4gICAgbGV0IGhhc2hlZFBhc3N3b3JkID0gJyc7XG5cbiAgICAvLyBBbHdheXMgcGVyZm9ybSBiY3J5cHQgY29tcGFyaXNvbiBldmVuIGlmIHVzZXIgaXMgbnVsbCAodGltaW5nIGF0dGFjayBwcm90ZWN0aW9uKVxuICAgIGlmICh1c2VyPy5wYXNzd29yZFJlc2V0VG9rZW4pIHtcbiAgICAgIGlzVG9rZW5WYWxpZCA9IGF3YWl0IGJjcnlwdC5jb21wYXJlKHRva2VuLCB1c2VyLnBhc3N3b3JkUmVzZXRUb2tlbik7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFBlcmZvcm0gZHVtbXkgYmNyeXB0IG9wZXJhdGlvbiB0byBtYWludGFpbiBjb25zaXN0ZW50IHRpbWluZ1xuICAgICAgYXdhaXQgYmNyeXB0LmNvbXBhcmUodG9rZW4sICckMmEkMTIkZHVtbXkuaGFzaC50by5wcmV2ZW50LnRpbWluZy5hdHRhY2tzLmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6Jyk7XG4gICAgfVxuXG4gICAgLy8gU0VDVVJJVFkgRklYOiBFbnN1cmUgY29uc2lzdGVudCByZXNwb25zZSB0aW1lIHJlZ2FyZGxlc3Mgb2YgdG9rZW4gdmFsaWRpdHlcbiAgICBjb25zdCBlbGFwc2VkVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgY29uc3QgcmVtYWluaW5nRGVsYXkgPSBNYXRoLm1heCgwLCBTRUNVUklUWV9ERUxBWV9NUyAtIGVsYXBzZWRUaW1lKTtcbiAgICBpZiAocmVtYWluaW5nRGVsYXkgPiAwKSB7XG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgcmVtYWluaW5nRGVsYXkpKTtcbiAgICB9XG5cbiAgICBpZiAoIXVzZXIgfHwgIWlzVG9rZW5WYWxpZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIG9yIGV4cGlyZWQgcGFzc3dvcmQgcmVzZXQgdG9rZW4uJyk7XG4gICAgfVxuXG4gICAgaGFzaGVkUGFzc3dvcmQgPSBhd2FpdCBiY3J5cHQuaGFzaChwYXNzd29yZCwgMTIpO1xuXG4gICAgYXdhaXQgcHJpc21hLnVzZXIudXBkYXRlKHtcbiAgICAgIHdoZXJlOiB7IGlkOiB1c2VyLmlkIH0sXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHBhc3N3b3JkOiBoYXNoZWRQYXNzd29yZCxcbiAgICAgICAgcGFzc3dvcmRSZXNldFRva2VuOiBudWxsLFxuICAgICAgICBwYXNzd29yZFJlc2V0RXhwaXJlczogbnVsbCxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHsgbWVzc2FnZTogJ1lvdXIgcGFzc3dvcmQgaGFzIGJlZW4gcmVzZXQgc3VjY2Vzc2Z1bGx5LicgfVxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgLy8gU0VDVVJJVFkgRklYOiBFbnN1cmUgY29uc2lzdGVudCB0aW1pbmcgZXZlbiBmb3IgZXJyb3JzXG4gICAgY29uc3QgZWxhcHNlZFRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuICAgIGNvbnN0IHJlbWFpbmluZ0RlbGF5ID0gTWF0aC5tYXgoMCwgU0VDVVJJVFlfREVMQVlfTVMgLSBlbGFwc2VkVGltZSk7XG4gICAgaWYgKHJlbWFpbmluZ0RlbGF5ID4gMCkge1xuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIHJlbWFpbmluZ0RlbGF5KSk7XG4gICAgfVxuICAgIHRocm93IGVycm9yO1xuICB9XG59KTsiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxJQUFBQSxRQUFBO0FBQUE7QUFBQSxDQUFBQyxhQUFBLEdBQUFDLENBQUEsUUFBQUMsT0FBQTtBQUNBLElBQUFDLFFBQUE7QUFBQTtBQUFBLENBQUFILGFBQUEsR0FBQUMsQ0FBQSxRQUFBRyxlQUFBLENBQUFGLE9BQUE7QUFDQSxJQUFBRyxVQUFBO0FBQUE7QUFBQSxDQUFBTCxhQUFBLEdBQUFDLENBQUEsUUFBQUcsZUFBQSxDQUFBRixPQUFBO0FBQ0EsSUFBQUksMkJBQUE7QUFBQTtBQUFBLENBQUFOLGFBQUEsR0FBQUMsQ0FBQSxRQUFBQyxPQUFBO0FBQ0EsSUFBQUssdUJBQUE7QUFBQTtBQUFBLENBQUFQLGFBQUEsR0FBQUMsQ0FBQSxRQUFBQyxPQUFBO0FBQ0EsSUFBQU0sV0FBQTtBQUFBO0FBQUEsQ0FBQVIsYUFBQSxHQUFBQyxDQUFBLFFBQUFDLE9BQUE7QUFFQTtBQUNBLElBQU1PLGlCQUFpQjtBQUFBO0FBQUEsQ0FBQVQsYUFBQSxHQUFBQyxDQUFBLFFBQUcsR0FBRztBQUU3QixTQUFlUyxhQUFhQSxDQUFBO0VBQUE7RUFBQVYsYUFBQSxHQUFBVyxDQUFBO0VBQUFYLGFBQUEsR0FBQUMsQ0FBQTtpQ0FBSVcsT0FBTztJQUFBO0lBQUFaLGFBQUEsR0FBQVcsQ0FBQTtJQUFBWCxhQUFBLEdBQUFDLENBQUE7Ozs7O01BQ3JDLHNCQUFPLElBQUlXLE9BQU8sQ0FBQyxVQUFBQyxPQUFPO1FBQUE7UUFBQWIsYUFBQSxHQUFBVyxDQUFBO1FBQUFYLGFBQUEsR0FBQUMsQ0FBQTtRQUFJLE9BQUFhLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFSixpQkFBaUIsQ0FBQztNQUF0QyxDQUFzQyxDQUFDOzs7O0FBQ3RFO0FBQUFULGFBQUEsR0FBQUMsQ0FBQTtBQUVZYyxPQUFBLENBQUFDLElBQUksR0FBRyxJQUFBViwyQkFBQSxDQUFBVyx3QkFBd0IsRUFBQyxVQUFPQyxPQUFvQjtFQUFBO0VBQUFsQixhQUFBLEdBQUFXLENBQUE7RUFBQVgsYUFBQSxHQUFBQyxDQUFBO0VBQUEsT0FBQWtCLFNBQUEsaUJBQUdQLE9BQU87SUFBQTtJQUFBWixhQUFBLEdBQUFXLENBQUE7Ozs7Ozs7Ozs7Ozs7VUFFeEQscUJBQU1KLHVCQUFBLENBQUFhLG9CQUFvQixDQUFDQyxJQUFJLENBQUNDLFVBQVUsQ0FBQ0osT0FBTyxDQUFDOzs7OztVQUFyRUssZUFBZSxHQUFHQyxFQUFBLENBQUFDLElBQUEsRUFBbUQ7VUFBQTtVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBRTNFLElBQUksQ0FBQ3NCLGVBQWUsQ0FBQ0csT0FBTyxFQUFFO1lBQUE7WUFBQTFCLGFBQUEsR0FBQTJCLENBQUE7WUFBQTNCLGFBQUEsR0FBQUMsQ0FBQTtZQUN0QjJCLEtBQUssR0FBRyxJQUFJQyxLQUFLLENBQUMsMkRBQTJELENBQVE7WUFBQztZQUFBN0IsYUFBQSxHQUFBQyxDQUFBO1lBQzVGMkIsS0FBSyxDQUFDRSxVQUFVLEdBQUcsR0FBRztZQUFDO1lBQUE5QixhQUFBLEdBQUFDLENBQUE7WUFDdkIyQixLQUFLLENBQUNHLE9BQU8sR0FBR1IsZUFBZSxDQUFDUSxPQUFPO1lBQUM7WUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtZQUN4QyxNQUFNMkIsS0FBSztVQUNiLENBQUM7VUFBQTtVQUFBO1lBQUE1QixhQUFBLEdBQUEyQixDQUFBO1VBQUE7VUFBQTNCLGFBQUEsR0FBQUMsQ0FBQTtVQUVLK0IsU0FBUyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsRUFBRTtVQUFDO1VBQUFsQyxhQUFBLEdBQUFDLENBQUE7Ozs7Ozs7OztVQUdDLHFCQUFNaUIsT0FBTyxDQUFDaUIsSUFBSSxFQUFFOzs7OztVQUExQ0MsRUFBQSxHQUFzQlosRUFBQSxDQUFBQyxJQUFBLEVBQW9CLEVBQXhDWSxLQUFLLEdBQUFELEVBQUEsQ0FBQUMsS0FBQSxFQUFFQyxRQUFRLEdBQUFGLEVBQUEsQ0FBQUUsUUFBQTtVQUFBO1VBQUF0QyxhQUFBLEdBQUFDLENBQUE7O1VBRW5CO1VBQUEsQ0FBQUQsYUFBQSxHQUFBMkIsQ0FBQSxZQUFDVSxLQUFLO1VBQUE7VUFBQSxDQUFBckMsYUFBQSxHQUFBMkIsQ0FBQSxXQUFJLENBQUNXLFFBQVEsSUFBbkI7WUFBQTtZQUFBdEMsYUFBQSxHQUFBMkIsQ0FBQTtZQUFBM0IsYUFBQSxHQUFBQyxDQUFBO1lBQUE7VUFBQSxDQUFtQjtVQUFBO1VBQUE7WUFBQUQsYUFBQSxHQUFBMkIsQ0FBQTtVQUFBO1VBQUEzQixhQUFBLEdBQUFDLENBQUE7VUFDckIscUJBQU1TLGFBQWEsRUFBRTs7Ozs7VUFBckJjLEVBQUEsQ0FBQUMsSUFBQSxFQUFxQjtVQUFDO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7VUFDdEIsTUFBTSxJQUFJNEIsS0FBSyxDQUFDLHNDQUFzQyxDQUFDOzs7OztVQUluRFUsa0JBQWtCLEdBQUcsSUFBQS9CLFdBQUEsQ0FBQWdDLHdCQUF3QixFQUFDRixRQUFRLENBQUM7VUFBQztVQUFBdEMsYUFBQSxHQUFBQyxDQUFBO2VBQzFELENBQUNzQyxrQkFBa0IsQ0FBQ0UsT0FBTyxFQUEzQjtZQUFBO1lBQUF6QyxhQUFBLEdBQUEyQixDQUFBO1lBQUEzQixhQUFBLEdBQUFDLENBQUE7WUFBQTtVQUFBLENBQTJCO1VBQUE7VUFBQTtZQUFBRCxhQUFBLEdBQUEyQixDQUFBO1VBQUE7VUFBQTNCLGFBQUEsR0FBQUMsQ0FBQTtVQUM3QixxQkFBTVMsYUFBYSxFQUFFOzs7OztVQUFyQmMsRUFBQSxDQUFBQyxJQUFBLEVBQXFCO1VBQUM7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNoQjJCLEtBQUssR0FBRyxJQUFJQyxLQUFLLENBQUMsK0NBQStDLENBQVE7VUFBQztVQUFBN0IsYUFBQSxHQUFBQyxDQUFBO1VBQ2hGMkIsS0FBSyxDQUFDRSxVQUFVLEdBQUcsR0FBRztVQUFDO1VBQUE5QixhQUFBLEdBQUFDLENBQUE7VUFDdkIyQixLQUFLLENBQUNjLElBQUksR0FBRztZQUFFQyxPQUFPLEVBQUVKLGtCQUFrQixDQUFDSztVQUFNLENBQUU7VUFBQztVQUFBNUMsYUFBQSxHQUFBQyxDQUFBO1VBQ3BELE1BQU0yQixLQUFLOzs7OztVQUlBLHFCQUFNekIsUUFBQSxDQUFBMEMsT0FBTSxDQUFDQyxJQUFJLENBQUNDLFNBQVMsQ0FBQztZQUN2Q0MsS0FBSyxFQUFFO2NBQ0xDLG9CQUFvQixFQUFFO2dCQUFFQyxFQUFFLEVBQUUsSUFBSWpCLElBQUk7Y0FBRSxDQUFFLENBQUU7O1dBRTdDLENBQUM7Ozs7O1VBSklhLElBQUksR0FBR3RCLEVBQUEsQ0FBQUMsSUFBQSxFQUlYO1VBQUE7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUVFa0QsWUFBWSxHQUFHLEtBQUs7VUFBQztVQUFBbkQsYUFBQSxHQUFBQyxDQUFBO1VBQ3JCbUQsY0FBYyxHQUFHLEVBQUU7VUFBQztVQUFBcEQsYUFBQSxHQUFBQyxDQUFBOztVQUdwQjtVQUFBLENBQUFELGFBQUEsR0FBQTJCLENBQUEsV0FBQW1CLElBQUk7VUFBQTtVQUFBLENBQUE5QyxhQUFBLEdBQUEyQixDQUFBLFdBQUptQixJQUFJO1VBQUE7VUFBQSxDQUFBOUMsYUFBQSxHQUFBMkIsQ0FBQTtVQUFBO1VBQUEsQ0FBQTNCLGFBQUEsR0FBQTJCLENBQUEsV0FBSm1CLElBQUksQ0FBRU8sa0JBQWtCLElBQXhCO1lBQUE7WUFBQXJELGFBQUEsR0FBQTJCLENBQUE7WUFBQTNCLGFBQUEsR0FBQUMsQ0FBQTtZQUFBO1VBQUEsQ0FBd0I7VUFBQTtVQUFBO1lBQUFELGFBQUEsR0FBQTJCLENBQUE7VUFBQTtVQUFBM0IsYUFBQSxHQUFBQyxDQUFBO1VBQ1gscUJBQU1JLFVBQUEsQ0FBQXdDLE9BQU0sQ0FBQ1MsT0FBTyxDQUFDakIsS0FBSyxFQUFFUyxJQUFJLENBQUNPLGtCQUFrQixDQUFDOzs7OztVQUFuRUYsWUFBWSxHQUFHM0IsRUFBQSxDQUFBQyxJQUFBLEVBQW9EO1VBQUM7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTs7Ozs7O1VBRXBFO1VBQ0EscUJBQU1JLFVBQUEsQ0FBQXdDLE9BQU0sQ0FBQ1MsT0FBTyxDQUFDakIsS0FBSyxFQUFFLHdFQUF3RSxDQUFDOzs7OztVQURyRztVQUNBYixFQUFBLENBQUFDLElBQUEsRUFBcUc7VUFBQztVQUFBekIsYUFBQSxHQUFBQyxDQUFBOzs7Ozs7VUFJbEdzRCxXQUFXLEdBQUd0QixJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO1VBQUM7VUFBQWhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNyQ3VELGdCQUFBLEdBQWlCQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUVqRCxpQkFBaUIsR0FBRzhDLFdBQVcsQ0FBQztVQUFDO1VBQUF2RCxhQUFBLEdBQUFDLENBQUE7Z0JBQ2hFdUQsZ0JBQWMsR0FBRyxDQUFDLEdBQWxCO1lBQUE7WUFBQXhELGFBQUEsR0FBQTJCLENBQUE7WUFBQTNCLGFBQUEsR0FBQUMsQ0FBQTtZQUFBO1VBQUEsQ0FBa0I7VUFBQTtVQUFBO1lBQUFELGFBQUEsR0FBQTJCLENBQUE7VUFBQTtVQUFBM0IsYUFBQSxHQUFBQyxDQUFBO1VBQ3BCLHFCQUFNLElBQUlXLE9BQU8sQ0FBQyxVQUFBQyxPQUFPO1lBQUE7WUFBQWIsYUFBQSxHQUFBVyxDQUFBO1lBQUFYLGFBQUEsR0FBQUMsQ0FBQTtZQUFJLE9BQUFhLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFMkMsZ0JBQWMsQ0FBQztVQUFuQyxDQUFtQyxDQUFDOzs7OztVQUFqRWhDLEVBQUEsQ0FBQUMsSUFBQSxFQUFpRTtVQUFDO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7Ozs7OztVQUdwRTtVQUFJO1VBQUEsQ0FBQUQsYUFBQSxHQUFBMkIsQ0FBQSxZQUFDbUIsSUFBSTtVQUFBO1VBQUEsQ0FBQTlDLGFBQUEsR0FBQTJCLENBQUEsV0FBSSxDQUFDd0IsWUFBWSxHQUFFO1lBQUE7WUFBQW5ELGFBQUEsR0FBQTJCLENBQUE7WUFBQTNCLGFBQUEsR0FBQUMsQ0FBQTtZQUMxQixNQUFNLElBQUk0QixLQUFLLENBQUMsMENBQTBDLENBQUM7VUFDN0QsQ0FBQztVQUFBO1VBQUE7WUFBQTdCLGFBQUEsR0FBQTJCLENBQUE7VUFBQTtVQUFBM0IsYUFBQSxHQUFBQyxDQUFBO1VBRWdCLHFCQUFNSSxVQUFBLENBQUF3QyxPQUFNLENBQUNjLElBQUksQ0FBQ3JCLFFBQVEsRUFBRSxFQUFFLENBQUM7Ozs7O1VBQWhEYyxjQUFjLEdBQUc1QixFQUFBLENBQUFDLElBQUEsRUFBK0I7VUFBQztVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBRWpELHFCQUFNRSxRQUFBLENBQUEwQyxPQUFNLENBQUNDLElBQUksQ0FBQ2MsTUFBTSxDQUFDO1lBQ3ZCWixLQUFLLEVBQUU7Y0FBRWEsRUFBRSxFQUFFZixJQUFJLENBQUNlO1lBQUUsQ0FBRTtZQUN0Qm5CLElBQUksRUFBRTtjQUNKSixRQUFRLEVBQUVjLGNBQWM7Y0FDeEJDLGtCQUFrQixFQUFFLElBQUk7Y0FDeEJKLG9CQUFvQixFQUFFOztXQUV6QixDQUFDOzs7OztVQVBGekIsRUFBQSxDQUFBQyxJQUFBLEVBT0U7VUFBQztVQUFBekIsYUFBQSxHQUFBQyxDQUFBO1VBRUgsc0JBQU9GLFFBQUEsQ0FBQStELFlBQVksQ0FBQzNCLElBQUksQ0FBQztZQUN2QjRCLE9BQU8sRUFBRSxJQUFJO1lBQ2JyQixJQUFJLEVBQUU7Y0FBRXNCLE9BQU8sRUFBRTtZQUE0QztXQUM5RCxDQUFDOzs7Ozs7OztVQUlJVCxXQUFXLEdBQUd0QixJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO1VBQUM7VUFBQWhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNyQ2dFLGdCQUFBLEdBQWlCUixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUVqRCxpQkFBaUIsR0FBRzhDLFdBQVcsQ0FBQztVQUFDO1VBQUF2RCxhQUFBLEdBQUFDLENBQUE7Z0JBQ2hFZ0UsZ0JBQWMsR0FBRyxDQUFDLEdBQWxCO1lBQUE7WUFBQWpFLGFBQUEsR0FBQTJCLENBQUE7WUFBQTNCLGFBQUEsR0FBQUMsQ0FBQTtZQUFBO1VBQUEsQ0FBa0I7VUFBQTtVQUFBO1lBQUFELGFBQUEsR0FBQTJCLENBQUE7VUFBQTtVQUFBM0IsYUFBQSxHQUFBQyxDQUFBO1VBQ3BCLHFCQUFNLElBQUlXLE9BQU8sQ0FBQyxVQUFBQyxPQUFPO1lBQUE7WUFBQWIsYUFBQSxHQUFBVyxDQUFBO1lBQUFYLGFBQUEsR0FBQUMsQ0FBQTtZQUFJLE9BQUFhLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFb0QsZ0JBQWMsQ0FBQztVQUFuQyxDQUFtQyxDQUFDOzs7OztVQUFqRXpDLEVBQUEsQ0FBQUMsSUFBQSxFQUFpRTtVQUFDO1VBQUF6QixhQUFBLEdBQUFDLENBQUE7Ozs7OztVQUVwRSxNQUFNaUUsT0FBSzs7Ozs7Ozs7O0NBRWQsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==