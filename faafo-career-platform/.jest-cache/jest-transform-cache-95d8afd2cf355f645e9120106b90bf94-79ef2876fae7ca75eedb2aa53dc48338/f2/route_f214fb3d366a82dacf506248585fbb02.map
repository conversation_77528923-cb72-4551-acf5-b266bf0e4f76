{"version": 3, "names": ["server_1", "cov_2ha0euo22", "s", "require", "prisma_1", "__importDefault", "bcryptjs_1", "unified_api_error_handler_1", "enhanced_rate_limiter_1", "rateLimit_1", "SECURITY_DELAY_MS", "securityDelay", "f", "Promise", "resolve", "setTimeout", "exports", "POST", "withUnifiedErrorHandling", "request", "__awaiter", "enhancedRateLimiters", "auth", "checkLimit", "rateLimitResult", "_b", "sent", "allowed", "b", "error", "Error", "statusCode", "headers", "startTime", "Date", "now", "json", "_a", "token", "password", "passwordValidation", "validatePasswordStrength", "<PERSON><PERSON><PERSON><PERSON>", "data", "details", "errors", "default", "user", "<PERSON><PERSON><PERSON><PERSON>", "where", "passwordResetExpires", "gt", "isTokenValid", "hashedPassword", "passwordResetToken", "compare", "elapsedTime", "remainingDelay_1", "Math", "max", "hash", "update", "id", "NextResponse", "success", "message", "remainingDelay_2", "error_1"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/reset-password/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport prisma from '@/lib/prisma';\nimport bcrypt from 'bcryptjs';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';\nimport { validatePasswordStrength } from '@/lib/rateLimit';\n\n// Timing attack protection - consistent delay for all responses\nconst SECURITY_DELAY_MS = 100;\n\nasync function securityDelay(): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, SECURITY_DELAY_MS));\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> => {\n  // SECURITY FIX: Apply strict rate limiting for password reset attempts\n  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);\n\n  if (!rateLimitResult.allowed) {\n    const error = new Error('Too many password reset attempts. Please try again later.') as any;\n    error.statusCode = 429;\n    error.headers = rateLimitResult.headers;\n    throw error;\n  }\n\n  const startTime = Date.now();\n\n  try {\n    const { token, password } = await request.json();\n\n    if (!token || !password) {\n      await securityDelay();\n      throw new Error('Token and new password are required.');\n    }\n\n    // SECURITY FIX: Validate password strength\n    const passwordValidation = validatePasswordStrength(password);\n    if (!passwordValidation.isValid) {\n      await securityDelay();\n      const error = new Error('Password does not meet security requirements.') as any;\n      error.statusCode = 400;\n      error.data = { details: passwordValidation.errors };\n      throw error;\n    }\n\n    // SECURITY FIX: Always perform database lookup and token comparison to prevent timing attacks\n    const user = await prisma.user.findFirst({\n      where: {\n        passwordResetExpires: { gt: new Date() }, // Token must not be expired\n      },\n    });\n\n    let isTokenValid = false;\n    let hashedPassword = '';\n\n    // Always perform bcrypt comparison even if user is null (timing attack protection)\n    if (user?.passwordResetToken) {\n      isTokenValid = await bcrypt.compare(token, user.passwordResetToken);\n    } else {\n      // Perform dummy bcrypt operation to maintain consistent timing\n      await bcrypt.compare(token, '$2a$12$dummy.hash.to.prevent.timing.attacks.abcdefghijklmnopqrstuvwxyz');\n    }\n\n    // SECURITY FIX: Ensure consistent response time regardless of token validity\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n\n    if (!user || !isTokenValid) {\n      throw new Error('Invalid or expired password reset token.');\n    }\n\n    hashedPassword = await bcrypt.hash(password, 12);\n\n    await prisma.user.update({\n      where: { id: user.id },\n      data: {\n        password: hashedPassword,\n        passwordResetToken: null,\n        passwordResetExpires: null,\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: { message: 'Your password has been reset successfully.' }\n    });\n\n  } catch (error) {\n    // SECURITY FIX: Ensure consistent timing even for errors\n    const elapsedTime = Date.now() - startTime;\n    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);\n    if (remainingDelay > 0) {\n      await new Promise(resolve => setTimeout(resolve, remainingDelay));\n    }\n    throw error;\n  }\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,QAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,eAAA,CAAAF,OAAA;AACA,IAAAG,UAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAG,eAAA,CAAAF,OAAA;AACA,IAAAI,2BAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAK,uBAAA;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAM,WAAA;AAAA;AAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA;AACA,IAAMO,iBAAiB;AAAA;AAAA,CAAAT,aAAA,GAAAC,CAAA,QAAG,GAAG;AAE7B,SAAeS,aAAaA,CAAA;EAAA;EAAAV,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAC,CAAA;iCAAIW,OAAO;IAAA;IAAAZ,aAAA,GAAAW,CAAA;IAAAX,aAAA,GAAAC,CAAA;;;;;MACrC,sBAAO,IAAIW,OAAO,CAAC,UAAAC,OAAO;QAAA;QAAAb,aAAA,GAAAW,CAAA;QAAAX,aAAA,GAAAC,CAAA;QAAI,OAAAa,UAAU,CAACD,OAAO,EAAEJ,iBAAiB,CAAC;MAAtC,CAAsC,CAAC;;;;AACtE;AAAAT,aAAA,GAAAC,CAAA;AAEYc,OAAA,CAAAC,IAAI,GAAG,IAAAV,2BAAA,CAAAW,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAlB,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAC,CAAA;EAAA,OAAAkB,SAAA,iBAAGP,OAAO;IAAA;IAAAZ,aAAA,GAAAW,CAAA;;;;;;;;;;;;;UAExD,qBAAMJ,uBAAA,CAAAa,oBAAoB,CAACC,IAAI,CAACC,UAAU,CAACJ,OAAO,CAAC;;;;;UAArEK,eAAe,GAAGC,EAAA,CAAAC,IAAA,EAAmD;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAE3E,IAAI,CAACsB,eAAe,CAACG,OAAO,EAAE;YAAA;YAAA1B,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAC,CAAA;YACtB2B,KAAK,GAAG,IAAIC,KAAK,CAAC,2DAA2D,CAAQ;YAAC;YAAA7B,aAAA,GAAAC,CAAA;YAC5F2B,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA9B,aAAA,GAAAC,CAAA;YACvB2B,KAAK,CAACG,OAAO,GAAGR,eAAe,CAACQ,OAAO;YAAC;YAAA/B,aAAA,GAAAC,CAAA;YACxC,MAAM2B,KAAK;UACb,CAAC;UAAA;UAAA;YAAA5B,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAC,CAAA;UAEK+B,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;UAAC;UAAAlC,aAAA,GAAAC,CAAA;;;;;;;;;UAGC,qBAAMiB,OAAO,CAACiB,IAAI,EAAE;;;;;UAA1CC,EAAA,GAAsBZ,EAAA,CAAAC,IAAA,EAAoB,EAAxCY,KAAK,GAAAD,EAAA,CAAAC,KAAA,EAAEC,QAAQ,GAAAF,EAAA,CAAAE,QAAA;UAAA;UAAAtC,aAAA,GAAAC,CAAA;;UAEnB;UAAA,CAAAD,aAAA,GAAA2B,CAAA,YAACU,KAAK;UAAA;UAAA,CAAArC,aAAA,GAAA2B,CAAA,WAAI,CAACW,QAAQ,IAAnB;YAAA;YAAAtC,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAmB;UAAA;UAAA;YAAAD,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAC,CAAA;UACrB,qBAAMS,aAAa,EAAE;;;;;UAArBc,EAAA,CAAAC,IAAA,EAAqB;UAAC;UAAAzB,aAAA,GAAAC,CAAA;UACtB,MAAM,IAAI4B,KAAK,CAAC,sCAAsC,CAAC;;;;;UAInDU,kBAAkB,GAAG,IAAA/B,WAAA,CAAAgC,wBAAwB,EAACF,QAAQ,CAAC;UAAC;UAAAtC,aAAA,GAAAC,CAAA;eAC1D,CAACsC,kBAAkB,CAACE,OAAO,EAA3B;YAAA;YAAAzC,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAC,CAAA;YAAA;UAAA,CAA2B;UAAA;UAAA;YAAAD,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAC,CAAA;UAC7B,qBAAMS,aAAa,EAAE;;;;;UAArBc,EAAA,CAAAC,IAAA,EAAqB;UAAC;UAAAzB,aAAA,GAAAC,CAAA;UAChB2B,KAAK,GAAG,IAAIC,KAAK,CAAC,+CAA+C,CAAQ;UAAC;UAAA7B,aAAA,GAAAC,CAAA;UAChF2B,KAAK,CAACE,UAAU,GAAG,GAAG;UAAC;UAAA9B,aAAA,GAAAC,CAAA;UACvB2B,KAAK,CAACc,IAAI,GAAG;YAAEC,OAAO,EAAEJ,kBAAkB,CAACK;UAAM,CAAE;UAAC;UAAA5C,aAAA,GAAAC,CAAA;UACpD,MAAM2B,KAAK;;;;;UAIA,qBAAMzB,QAAA,CAAA0C,OAAM,CAACC,IAAI,CAACC,SAAS,CAAC;YACvCC,KAAK,EAAE;cACLC,oBAAoB,EAAE;gBAAEC,EAAE,EAAE,IAAIjB,IAAI;cAAE,CAAE,CAAE;;WAE7C,CAAC;;;;;UAJIa,IAAI,GAAGtB,EAAA,CAAAC,IAAA,EAIX;UAAA;UAAAzB,aAAA,GAAAC,CAAA;UAEEkD,YAAY,GAAG,KAAK;UAAC;UAAAnD,aAAA,GAAAC,CAAA;UACrBmD,cAAc,GAAG,EAAE;UAAC;UAAApD,aAAA,GAAAC,CAAA;;UAGpB;UAAA,CAAAD,aAAA,GAAA2B,CAAA,WAAAmB,IAAI;UAAA;UAAA,CAAA9C,aAAA,GAAA2B,CAAA,WAAJmB,IAAI;UAAA;UAAA,CAAA9C,aAAA,GAAA2B,CAAA;UAAA;UAAA,CAAA3B,aAAA,GAAA2B,CAAA,WAAJmB,IAAI,CAAEO,kBAAkB,IAAxB;YAAA;YAAArD,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAwB;UAAA;UAAA;YAAAD,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAC,CAAA;UACX,qBAAMI,UAAA,CAAAwC,OAAM,CAACS,OAAO,CAACjB,KAAK,EAAES,IAAI,CAACO,kBAAkB,CAAC;;;;;UAAnEF,YAAY,GAAG3B,EAAA,CAAAC,IAAA,EAAoD;UAAC;UAAAzB,aAAA,GAAAC,CAAA;;;;;;UAEpE;UACA,qBAAMI,UAAA,CAAAwC,OAAM,CAACS,OAAO,CAACjB,KAAK,EAAE,wEAAwE,CAAC;;;;;UADrG;UACAb,EAAA,CAAAC,IAAA,EAAqG;UAAC;UAAAzB,aAAA,GAAAC,CAAA;;;;;;UAIlGsD,WAAW,GAAGtB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;UAAC;UAAAhC,aAAA,GAAAC,CAAA;UACrCuD,gBAAA,GAAiBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,iBAAiB,GAAG8C,WAAW,CAAC;UAAC;UAAAvD,aAAA,GAAAC,CAAA;gBAChEuD,gBAAc,GAAG,CAAC,GAAlB;YAAA;YAAAxD,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAkB;UAAA;UAAA;YAAAD,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAC,CAAA;UACpB,qBAAM,IAAIW,OAAO,CAAC,UAAAC,OAAO;YAAA;YAAAb,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAAI,OAAAa,UAAU,CAACD,OAAO,EAAE2C,gBAAc,CAAC;UAAnC,CAAmC,CAAC;;;;;UAAjEhC,EAAA,CAAAC,IAAA,EAAiE;UAAC;UAAAzB,aAAA,GAAAC,CAAA;;;;;;UAGpE;UAAI;UAAA,CAAAD,aAAA,GAAA2B,CAAA,YAACmB,IAAI;UAAA;UAAA,CAAA9C,aAAA,GAAA2B,CAAA,WAAI,CAACwB,YAAY,GAAE;YAAA;YAAAnD,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAC,CAAA;YAC1B,MAAM,IAAI4B,KAAK,CAAC,0CAA0C,CAAC;UAC7D,CAAC;UAAA;UAAA;YAAA7B,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAC,CAAA;UAEgB,qBAAMI,UAAA,CAAAwC,OAAM,CAACc,IAAI,CAACrB,QAAQ,EAAE,EAAE,CAAC;;;;;UAAhDc,cAAc,GAAG5B,EAAA,CAAAC,IAAA,EAA+B;UAAC;UAAAzB,aAAA,GAAAC,CAAA;UAEjD,qBAAME,QAAA,CAAA0C,OAAM,CAACC,IAAI,CAACc,MAAM,CAAC;YACvBZ,KAAK,EAAE;cAAEa,EAAE,EAAEf,IAAI,CAACe;YAAE,CAAE;YACtBnB,IAAI,EAAE;cACJJ,QAAQ,EAAEc,cAAc;cACxBC,kBAAkB,EAAE,IAAI;cACxBJ,oBAAoB,EAAE;;WAEzB,CAAC;;;;;UAPFzB,EAAA,CAAAC,IAAA,EAOE;UAAC;UAAAzB,aAAA,GAAAC,CAAA;UAEH,sBAAOF,QAAA,CAAA+D,YAAY,CAAC3B,IAAI,CAAC;YACvB4B,OAAO,EAAE,IAAI;YACbrB,IAAI,EAAE;cAAEsB,OAAO,EAAE;YAA4C;WAC9D,CAAC;;;;;;;;UAIIT,WAAW,GAAGtB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;UAAC;UAAAhC,aAAA,GAAAC,CAAA;UACrCgE,gBAAA,GAAiBR,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,iBAAiB,GAAG8C,WAAW,CAAC;UAAC;UAAAvD,aAAA,GAAAC,CAAA;gBAChEgE,gBAAc,GAAG,CAAC,GAAlB;YAAA;YAAAjE,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAkB;UAAA;UAAA;YAAAD,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAC,CAAA;UACpB,qBAAM,IAAIW,OAAO,CAAC,UAAAC,OAAO;YAAA;YAAAb,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAAI,OAAAa,UAAU,CAACD,OAAO,EAAEoD,gBAAc,CAAC;UAAnC,CAAmC,CAAC;;;;;UAAjEzC,EAAA,CAAAC,IAAA,EAAiE;UAAC;UAAAzB,aAAA,GAAAC,CAAA;;;;;;UAEpE,MAAMiE,OAAK;;;;;;;;;CAEd,CAAC", "ignoreList": []}