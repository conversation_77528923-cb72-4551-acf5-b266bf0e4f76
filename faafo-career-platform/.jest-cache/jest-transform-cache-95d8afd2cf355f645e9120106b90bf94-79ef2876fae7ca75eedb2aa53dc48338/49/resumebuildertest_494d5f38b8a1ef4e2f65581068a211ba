e300cfd50194e9347dfc13e2c6281af5
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock dependencies
jest.mock('next-auth/react');
jest.mock('next/navigation', function () { return ({
    useRouter: function () { return ({
        push: jest.fn(),
        replace: jest.fn(),
    }); },
    useSearchParams: function () { return ({
        get: jest.fn(),
    }); },
}); });
/**
 * Resume Builder Component Tests
 *
 * Tests for the main ResumeBuilder component including form interactions,
 * data management, and user workflows.
 */
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
var user_event_1 = __importDefault(require("@testing-library/user-event"));
var react_3 = require("next-auth/react");
var ResumeBuilder_1 = require("@/components/resume-builder/ResumeBuilder");
var mockUseSession = react_3.useSession;
// Mock fetch globally
global.fetch = jest.fn();
var mockFetch = fetch;
describe('ResumeBuilder Component', function () {
    beforeEach(function () {
        jest.clearAllMocks();
        mockUseSession.mockReturnValue({
            data: {
                user: {
                    email: '<EMAIL>',
                    name: 'Test User'
                }
            },
            status: 'authenticated'
        });
    });
    afterEach(function () {
        jest.resetAllMocks();
    });
    describe('Initial Render', function () {
        it('should render the resume builder form', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
            expect(react_2.screen.getByText('Resume Builder')).toBeInTheDocument();
            expect(react_2.screen.getByText('Create and customize your professional resume')).toBeInTheDocument();
            expect(react_2.screen.getByLabelText('Resume Title')).toBeInTheDocument();
            expect(react_2.screen.getByDisplayValue('My Resume')).toBeInTheDocument();
        });
        it('should render all form tabs', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
            expect(react_2.screen.getByRole('tab', { name: 'Personal' })).toBeInTheDocument();
            expect(react_2.screen.getByRole('tab', { name: 'Experience' })).toBeInTheDocument();
            expect(react_2.screen.getByRole('tab', { name: 'Education' })).toBeInTheDocument();
            expect(react_2.screen.getByRole('tab', { name: 'Skills' })).toBeInTheDocument();
        });
        it('should show personal info tab by default', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
            expect(react_2.screen.getByLabelText('First Name *')).toBeInTheDocument();
            expect(react_2.screen.getByLabelText('Last Name *')).toBeInTheDocument();
            expect(react_2.screen.getByLabelText('Email Address *')).toBeInTheDocument();
        });
    });
    describe('Form Interactions', function () {
        it('should update resume title when input changes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, titleInput;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        titleInput = react_2.screen.getByLabelText('Resume Title');
                        return [4 /*yield*/, user.clear(titleInput)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, user.type(titleInput, 'Software Engineer Resume')];
                    case 2:
                        _a.sent();
                        expect(titleInput).toHaveValue('Software Engineer Resume');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update template selection', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, templateSelect, classicOption;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        templateSelect = react_2.screen.getByRole('combobox');
                        return [4 /*yield*/, user.click(templateSelect)];
                    case 1:
                        _a.sent();
                        classicOption = react_2.screen.getByText('Classic');
                        return [4 /*yield*/, user.click(classicOption)];
                    case 2:
                        _a.sent();
                        expect(react_2.screen.getByDisplayValue('classic')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should switch between tabs', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, experienceTab;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        experienceTab = react_2.screen.getByRole('tab', { name: 'Experience' });
                        return [4 /*yield*/, user.click(experienceTab)];
                    case 1:
                        _a.sent();
                        expect(react_2.screen.getByText('Work Experience')).toBeInTheDocument();
                        expect(react_2.screen.getByText('Add your professional work experience, starting with the most recent')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update personal information', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, firstNameInput, lastNameInput;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        firstNameInput = react_2.screen.getByLabelText('First Name *');
                        lastNameInput = react_2.screen.getByLabelText('Last Name *');
                        return [4 /*yield*/, user.type(firstNameInput, 'John')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, user.type(lastNameInput, 'Doe')];
                    case 2:
                        _a.sent();
                        expect(firstNameInput).toHaveValue('John');
                        expect(lastNameInput).toHaveValue('Doe');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update professional summary', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, summaryTextarea;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        summaryTextarea = react_2.screen.getByPlaceholderText(/Experienced software engineer/);
                        return [4 /*yield*/, user.type(summaryTextarea, 'Passionate developer with 5 years of experience')];
                    case 1:
                        _a.sent();
                        expect(summaryTextarea).toHaveValue('Passionate developer with 5 years of experience');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Data Loading', function () {
        it('should load existing resume when resumeId is provided', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockResumeData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockResumeData = {
                            id: 'resume-1',
                            title: 'Existing Resume',
                            personalInfo: {
                                firstName: 'Jane',
                                lastName: 'Smith',
                                email: '<EMAIL>'
                            },
                            summary: 'Existing summary',
                            experience: [],
                            education: [],
                            skills: [],
                            template: 'classic',
                            isPublic: false
                        };
                        mockFetch.mockResolvedValueOnce({
                            ok: true,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            success: true,
                                            data: mockResumeData
                                        })];
                                });
                            }); }
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, { resumeId: "resume-1" }));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByDisplayValue('Existing Resume')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder/resume-1', {
                            headers: {
                                'Content-Type': 'application/json',
                            },
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        it('should show loading spinner while loading resume', function () {
            mockFetch.mockImplementation(function () { return new Promise(function () { }); }); // Never resolves
            (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, { resumeId: "resume-1" }));
            expect(react_2.screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
        });
        it('should show error when loading fails', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockFetch.mockResolvedValueOnce({
                            ok: false,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            success: false,
                                            error: 'Resume not found'
                                        })];
                                });
                            }); }
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, { resumeId: "resume-1" }));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Resume not found')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Save Functionality', function () {
        it('should save new resume when save button is clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, mockOnSave, saveButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        mockOnSave = jest.fn();
                        mockFetch.mockResolvedValueOnce({
                            ok: true,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            success: true,
                                            data: {
                                                id: 'new-resume-1',
                                                title: 'My Resume',
                                                personalInfo: {
                                                    firstName: '',
                                                    lastName: '',
                                                    email: '<EMAIL>'
                                                },
                                                summary: '',
                                                experience: [],
                                                education: [],
                                                skills: [],
                                                template: 'modern',
                                                isPublic: false
                                            }
                                        })];
                                });
                            }); }
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, { onSave: mockOnSave }));
                        saveButton = react_2.screen.getByRole('button', { name: /save/i });
                        return [4 /*yield*/, user.click(saveButton)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: expect.stringContaining('"title":"My Resume"')
                                });
                            })];
                    case 2:
                        _a.sent();
                        expect(mockOnSave).toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update existing resume when save button is clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, mockOnSave, saveButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        mockOnSave = jest.fn();
                        // Mock initial load
                        mockFetch.mockResolvedValueOnce({
                            ok: true,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            success: true,
                                            data: {
                                                id: 'resume-1',
                                                title: 'Existing Resume',
                                                personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
                                                template: 'modern'
                                            }
                                        })];
                                });
                            }); }
                        });
                        // Mock save
                        mockFetch.mockResolvedValueOnce({
                            ok: true,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            success: true,
                                            data: { id: 'resume-1', title: 'Updated Resume' }
                                        })];
                                });
                            }); }
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, { resumeId: "resume-1", onSave: mockOnSave }));
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByDisplayValue('Existing Resume')).toBeInTheDocument();
                            })];
                    case 1:
                        _a.sent();
                        saveButton = react_2.screen.getByRole('button', { name: /save/i });
                        return [4 /*yield*/, user.click(saveButton)];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder/resume-1', {
                                    method: 'PUT',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: expect.any(String)
                                });
                            })];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should show error when save fails', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, saveButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        mockFetch.mockResolvedValueOnce({
                            ok: false,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            success: false,
                                            error: 'Failed to save resume'
                                        })];
                                });
                            }); }
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        saveButton = react_2.screen.getByRole('button', { name: /save/i });
                        return [4 /*yield*/, user.click(saveButton)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Failed to save resume')).toBeInTheDocument();
                            })];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Preview Functionality', function () {
        it('should show preview when preview button is clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, previewButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        previewButton = react_2.screen.getByRole('button', { name: /preview/i });
                        return [4 /*yield*/, user.click(previewButton)];
                    case 1:
                        _a.sent();
                        expect(react_2.screen.getByText('Resume Preview')).toBeInTheDocument();
                        expect(react_2.screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return to edit mode from preview', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, previewButton, editButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        previewButton = react_2.screen.getByRole('button', { name: /preview/i });
                        return [4 /*yield*/, user.click(previewButton)];
                    case 1:
                        _a.sent();
                        editButton = react_2.screen.getByRole('button', { name: /edit/i });
                        return [4 /*yield*/, user.click(editButton)];
                    case 2:
                        _a.sent();
                        expect(react_2.screen.getByText('Resume Builder')).toBeInTheDocument();
                        expect(react_2.screen.getByLabelText('Resume Title')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Cancel Functionality', function () {
        it('should call onCancel when cancel button is clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, mockOnCancel, cancelButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        mockOnCancel = jest.fn();
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, { onCancel: mockOnCancel }));
                        cancelButton = react_2.screen.getByRole('button', { name: /cancel/i });
                        return [4 /*yield*/, user.click(cancelButton)];
                    case 1:
                        _a.sent();
                        expect(mockOnCancel).toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Authentication', function () {
        it('should show error when user is not authenticated', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user, saveButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = user_event_1.default.setup();
                        mockUseSession.mockReturnValue({
                            data: null,
                            status: 'unauthenticated'
                        });
                        mockFetch.mockResolvedValueOnce({
                            ok: false,
                            status: 401,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            success: false,
                                            error: 'You must be logged in to save a resume'
                                        })];
                                });
                            }); }
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(ResumeBuilder_1.ResumeBuilder, {}));
                        saveButton = react_2.screen.getByRole('button', { name: /save/i });
                        return [4 /*yield*/, user.click(saveButton)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('You must be logged in to save a resume')).toBeInTheDocument();
                            })];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************