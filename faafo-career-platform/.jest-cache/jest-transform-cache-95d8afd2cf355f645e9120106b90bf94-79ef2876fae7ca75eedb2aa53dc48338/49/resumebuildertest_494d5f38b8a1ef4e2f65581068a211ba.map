{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/resume-builder/resume-builder.test.tsx", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAM,OAAA,CAAC;IAClC,SAAS,EAAE,cAAM,OAAA,CAAC;QAChB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;KACnB,CAAC,EAHe,CAGf;IACF,eAAe,EAAE,cAAM,OAAA,CAAC;QACtB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;KACf,CAAC,EAFqB,CAErB;CACH,CAAC,EARiC,CAQjC,CAAC,CAAC;AAvBJ;;;;;GAKG;AAEH,gDAA0B;AAC1B,gDAA4E;AAC5E,2EAAoD;AACpD,yCAA6C;AAC7C,2EAA0E;AAc1E,IAAM,cAAc,GAAG,kBAAoD,CAAC;AAE5E,sBAAsB;AACtB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;AACzB,IAAM,SAAS,GAAG,KAA0C,CAAC;AAE7D,QAAQ,CAAC,yBAAyB,EAAE;IAClC,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,cAAc,CAAC,eAAe,CAAC;YAC7B,IAAI,EAAE;gBACJ,IAAI,EAAE;oBACJ,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,MAAM,EAAE,eAAe;SACxB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC;QACR,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;YAE1B,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC/D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC9F,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAClE,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;YAE1B,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC1E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;YAE1B,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAClE,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACjE,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,+CAA+C,EAAE;;;;;wBAC5C,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAC/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAEpB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;wBACzD,qBAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAA;;wBAA5B,SAA4B,CAAC;wBAC7B,qBAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,0BAA0B,CAAC,EAAA;;wBAAvD,SAAuD,CAAC;wBAExD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAC;;;;aAC5D,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;;;;;wBAC/B,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAC/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAEpB,cAAc,GAAG,cAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;wBACpD,qBAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAA;;wBAAhC,SAAgC,CAAC;wBAE3B,aAAa,GAAG,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;wBAClD,qBAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAA;;wBAA/B,SAA+B,CAAC;wBAEhC,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aACjE,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE;;;;;wBACzB,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAC/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAGpB,aAAa,GAAG,cAAM,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;wBACtE,qBAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAA;;wBAA/B,SAA+B,CAAC;wBAEhC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAChE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,sEAAsE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aACtH,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;;;;;wBACjC,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAC/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAEpB,cAAc,GAAG,cAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;wBACvD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;wBAE3D,qBAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,EAAA;;wBAAvC,SAAuC,CAAC;wBACxC,qBAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,EAAA;;wBAArC,SAAqC,CAAC;wBAEtC,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;wBAC3C,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;;;aAC1C,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;;;;;wBACjC,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAC/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAEpB,eAAe,GAAG,cAAM,CAAC,oBAAoB,CAAC,+BAA+B,CAAC,CAAC;wBACrF,qBAAM,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,iDAAiD,CAAC,EAAA;;wBAAnF,SAAmF,CAAC;wBAEpF,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,iDAAiD,CAAC,CAAC;;;;aACxF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE;QACvB,EAAE,CAAC,uDAAuD,EAAE;;;;;wBACpD,cAAc,GAAG;4BACrB,EAAE,EAAE,UAAU;4BACd,KAAK,EAAE,iBAAiB;4BACxB,YAAY,EAAE;gCACZ,SAAS,EAAE,MAAM;gCACjB,QAAQ,EAAE,OAAO;gCACjB,KAAK,EAAE,kBAAkB;6BAC1B;4BACD,OAAO,EAAE,kBAAkB;4BAC3B,UAAU,EAAE,EAAE;4BACd,SAAS,EAAE,EAAE;4BACb,MAAM,EAAE,EAAE;4BACV,QAAQ,EAAE,SAAS;4BACnB,QAAQ,EAAE,KAAK;yBAChB,CAAC;wBAEF,SAAS,CAAC,qBAAqB,CAAC;4BAC9B,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,IAAI;4CACb,IAAI,EAAE,cAAc;yCACrB,CAAC,EAAA;;iCAAA;yBACS,CAAC,CAAC;wBAEf,IAAA,cAAM,EAAC,uBAAC,6BAAa,IAAC,QAAQ,EAAC,UAAU,GAAG,CAAC,CAAC;wBAE9C,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC1E,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEH,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,8BAA8B,EAAE;4BACrE,OAAO,EAAE;gCACP,cAAc,EAAE,kBAAkB;6BACnC;yBACF,CAAC,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE;YACrD,SAAS,CAAC,kBAAkB,CAAC,cAAM,OAAA,IAAI,OAAO,CAAC,cAAO,CAAC,CAAC,EAArB,CAAqB,CAAC,CAAC,CAAC,iBAAiB;YAE5E,IAAA,cAAM,EAAC,uBAAC,6BAAa,IAAC,QAAQ,EAAC,UAAU,GAAG,CAAC,CAAC;YAE9C,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,kBAAkB;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;;;wBACzC,SAAS,CAAC,qBAAqB,CAAC;4BAC9B,EAAE,EAAE,KAAK;4BACT,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,KAAK;4CACd,KAAK,EAAE,kBAAkB;yCAC1B,CAAC,EAAA;;iCAAA;yBACS,CAAC,CAAC;wBAEf,IAAA,cAAM,EAAC,uBAAC,6BAAa,IAAC,QAAQ,EAAC,UAAU,GAAG,CAAC,CAAC;wBAE9C,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACnE,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,oDAAoD,EAAE;;;;;wBACjD,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBACzB,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;wBAE7B,SAAS,CAAC,qBAAqB,CAAC;4BAC9B,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,IAAI;4CACb,IAAI,EAAE;gDACJ,EAAE,EAAE,cAAc;gDAClB,KAAK,EAAE,WAAW;gDAClB,YAAY,EAAE;oDACZ,SAAS,EAAE,EAAE;oDACb,QAAQ,EAAE,EAAE;oDACZ,KAAK,EAAE,kBAAkB;iDAC1B;gDACD,OAAO,EAAE,EAAE;gDACX,UAAU,EAAE,EAAE;gDACd,SAAS,EAAE,EAAE;gDACb,MAAM,EAAE,EAAE;gDACV,QAAQ,EAAE,QAAQ;gDAClB,QAAQ,EAAE,KAAK;6CAChB;yCACF,CAAC,EAAA;;iCAAA;yBACS,CAAC,CAAC;wBAEf,IAAA,cAAM,EAAC,uBAAC,6BAAa,IAAC,MAAM,EAAE,UAAU,GAAI,CAAC,CAAC;wBAExC,UAAU,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;wBACjE,qBAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAA;;wBAA5B,SAA4B,CAAC;wBAE7B,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,qBAAqB,EAAE;oCAC5D,MAAM,EAAE,MAAM;oCACd,OAAO,EAAE;wCACP,cAAc,EAAE,kBAAkB;qCACnC;oCACD,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,CAAC;iCACrD,CAAC,CAAC;4BACL,CAAC,CAAC,EAAA;;wBARF,SAQE,CAAC;wBAEH,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE;;;;;wBACxD,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBACzB,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;wBAE7B,oBAAoB;wBACpB,SAAS,CAAC,qBAAqB,CAAC;4BAC9B,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,IAAI;4CACb,IAAI,EAAE;gDACJ,EAAE,EAAE,UAAU;gDACd,KAAK,EAAE,iBAAiB;gDACxB,YAAY,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE;gDAC/E,QAAQ,EAAE,QAAQ;6CACnB;yCACF,CAAC,EAAA;;iCAAA;yBACS,CAAC,CAAC;wBAEf,YAAY;wBACZ,SAAS,CAAC,qBAAqB,CAAC;4BAC9B,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,IAAI;4CACb,IAAI,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,gBAAgB,EAAE;yCAClD,CAAC,EAAA;;iCAAA;yBACS,CAAC,CAAC;wBAEf,IAAA,cAAM,EAAC,uBAAC,6BAAa,IAAC,QAAQ,EAAC,UAAU,EAAC,MAAM,EAAE,UAAU,GAAI,CAAC,CAAC;wBAElE,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC1E,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEG,UAAU,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;wBACjE,qBAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAA;;wBAA5B,SAA4B,CAAC;wBAE7B,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,8BAA8B,EAAE;oCACrE,MAAM,EAAE,KAAK;oCACb,OAAO,EAAE;wCACP,cAAc,EAAE,kBAAkB;qCACnC;oCACD,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iCACzB,CAAC,CAAC;4BACL,CAAC,CAAC,EAAA;;wBARF,SAQE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAE/B,SAAS,CAAC,qBAAqB,CAAC;4BAC9B,EAAE,EAAE,KAAK;4BACT,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,KAAK;4CACd,KAAK,EAAE,uBAAuB;yCAC/B,CAAC,EAAA;;iCAAA;yBACS,CAAC,CAAC;wBAEf,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAEpB,UAAU,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;wBACjE,qBAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAA;;wBAA5B,SAA4B,CAAC;wBAE7B,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACxE,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,EAAE,CAAC,oDAAoD,EAAE;;;;;wBACjD,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAC/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAEpB,aAAa,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;wBACvE,qBAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAA;;wBAA/B,SAA+B,CAAC;wBAEhC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC/D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aAC3E,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;wBACtC,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAC/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAGpB,aAAa,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;wBACvE,qBAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAA;;wBAA/B,SAA+B,CAAC;wBAG1B,UAAU,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;wBACjE,qBAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAA;;wBAA5B,SAA4B,CAAC;wBAE7B,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC/D,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aACnE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE;QAC/B,EAAE,CAAC,oDAAoD,EAAE;;;;;wBACjD,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBACzB,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;wBAE/B,IAAA,cAAM,EAAC,uBAAC,6BAAa,IAAC,QAAQ,EAAE,YAAY,GAAI,CAAC,CAAC;wBAE5C,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;wBACrE,qBAAM,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAA;;wBAA9B,SAA8B,CAAC;wBAE/B,MAAM,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;;;;aACzC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,kDAAkD,EAAE;;;;;wBAC/C,IAAI,GAAG,oBAAS,CAAC,KAAK,EAAE,CAAC;wBAE/B,cAAc,CAAC,eAAe,CAAC;4BAC7B,IAAI,EAAE,IAAI;4BACV,MAAM,EAAE,iBAAiB;yBAC1B,CAAC,CAAC;wBAEH,SAAS,CAAC,qBAAqB,CAAC;4BAC9B,EAAE,EAAE,KAAK;4BACT,MAAM,EAAE,GAAG;4BACX,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,KAAK;4CACd,KAAK,EAAE,wCAAwC;yCAChD,CAAC,EAAA;;iCAAA;yBACS,CAAC,CAAC;wBAEf,IAAA,cAAM,EAAC,uBAAC,6BAAa,KAAG,CAAC,CAAC;wBAEpB,UAAU,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;wBACjE,qBAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAA;;wBAA5B,SAA4B,CAAC;wBAE7B,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACzF,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/resume-builder/resume-builder.test.tsx"], "sourcesContent": ["/**\n * Resume Builder Component Tests\n * \n * Tests for the main ResumeBuilder component including form interactions,\n * data management, and user workflows.\n */\n\nimport React from 'react';\nimport { render, screen, fireEvent, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport { useSession } from 'next-auth/react';\nimport { ResumeBuilder } from '@/components/resume-builder/ResumeBuilder';\n\n// Mock dependencies\njest.mock('next-auth/react');\njest.mock('next/navigation', () => ({\n  useRouter: () => ({\n    push: jest.fn(),\n    replace: jest.fn(),\n  }),\n  useSearchParams: () => ({\n    get: jest.fn(),\n  }),\n}));\n\nconst mockUseSession = useSession as jest.MockedFunction<typeof useSession>;\n\n// Mock fetch globally\nglobal.fetch = jest.fn();\nconst mockFetch = fetch as jest.MockedFunction<typeof fetch>;\n\ndescribe('ResumeBuilder Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n    mockUseSession.mockReturnValue({\n      data: {\n        user: {\n          email: '<EMAIL>',\n          name: 'Test User'\n        }\n      },\n      status: 'authenticated'\n    });\n  });\n\n  afterEach(() => {\n    jest.resetAllMocks();\n  });\n\n  describe('Initial Render', () => {\n    it('should render the resume builder form', () => {\n      render(<ResumeBuilder />);\n      \n      expect(screen.getByText('Resume Builder')).toBeInTheDocument();\n      expect(screen.getByText('Create and customize your professional resume')).toBeInTheDocument();\n      expect(screen.getByLabelText('Resume Title')).toBeInTheDocument();\n      expect(screen.getByDisplayValue('My Resume')).toBeInTheDocument();\n    });\n\n    it('should render all form tabs', () => {\n      render(<ResumeBuilder />);\n      \n      expect(screen.getByRole('tab', { name: 'Personal' })).toBeInTheDocument();\n      expect(screen.getByRole('tab', { name: 'Experience' })).toBeInTheDocument();\n      expect(screen.getByRole('tab', { name: 'Education' })).toBeInTheDocument();\n      expect(screen.getByRole('tab', { name: 'Skills' })).toBeInTheDocument();\n    });\n\n    it('should show personal info tab by default', () => {\n      render(<ResumeBuilder />);\n      \n      expect(screen.getByLabelText('First Name *')).toBeInTheDocument();\n      expect(screen.getByLabelText('Last Name *')).toBeInTheDocument();\n      expect(screen.getByLabelText('Email Address *')).toBeInTheDocument();\n    });\n  });\n\n  describe('Form Interactions', () => {\n    it('should update resume title when input changes', async () => {\n      const user = userEvent.setup();\n      render(<ResumeBuilder />);\n      \n      const titleInput = screen.getByLabelText('Resume Title');\n      await user.clear(titleInput);\n      await user.type(titleInput, 'Software Engineer Resume');\n      \n      expect(titleInput).toHaveValue('Software Engineer Resume');\n    });\n\n    it('should update template selection', async () => {\n      const user = userEvent.setup();\n      render(<ResumeBuilder />);\n      \n      const templateSelect = screen.getByRole('combobox');\n      await user.click(templateSelect);\n      \n      const classicOption = screen.getByText('Classic');\n      await user.click(classicOption);\n      \n      expect(screen.getByDisplayValue('classic')).toBeInTheDocument();\n    });\n\n    it('should switch between tabs', async () => {\n      const user = userEvent.setup();\n      render(<ResumeBuilder />);\n      \n      // Click on Experience tab\n      const experienceTab = screen.getByRole('tab', { name: 'Experience' });\n      await user.click(experienceTab);\n      \n      expect(screen.getByText('Work Experience')).toBeInTheDocument();\n      expect(screen.getByText('Add your professional work experience, starting with the most recent')).toBeInTheDocument();\n    });\n\n    it('should update personal information', async () => {\n      const user = userEvent.setup();\n      render(<ResumeBuilder />);\n      \n      const firstNameInput = screen.getByLabelText('First Name *');\n      const lastNameInput = screen.getByLabelText('Last Name *');\n      \n      await user.type(firstNameInput, 'John');\n      await user.type(lastNameInput, 'Doe');\n      \n      expect(firstNameInput).toHaveValue('John');\n      expect(lastNameInput).toHaveValue('Doe');\n    });\n\n    it('should update professional summary', async () => {\n      const user = userEvent.setup();\n      render(<ResumeBuilder />);\n      \n      const summaryTextarea = screen.getByPlaceholderText(/Experienced software engineer/);\n      await user.type(summaryTextarea, 'Passionate developer with 5 years of experience');\n      \n      expect(summaryTextarea).toHaveValue('Passionate developer with 5 years of experience');\n    });\n  });\n\n  describe('Data Loading', () => {\n    it('should load existing resume when resumeId is provided', async () => {\n      const mockResumeData = {\n        id: 'resume-1',\n        title: 'Existing Resume',\n        personalInfo: {\n          firstName: 'Jane',\n          lastName: 'Smith',\n          email: '<EMAIL>'\n        },\n        summary: 'Existing summary',\n        experience: [],\n        education: [],\n        skills: [],\n        template: 'classic',\n        isPublic: false\n      };\n\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          success: true,\n          data: mockResumeData\n        })\n      } as Response);\n\n      render(<ResumeBuilder resumeId=\"resume-1\" />);\n\n      await waitFor(() => {\n        expect(screen.getByDisplayValue('Existing Resume')).toBeInTheDocument();\n      });\n\n      expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder/resume-1', {\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n    });\n\n    it('should show loading spinner while loading resume', () => {\n      mockFetch.mockImplementation(() => new Promise(() => {})); // Never resolves\n      \n      render(<ResumeBuilder resumeId=\"resume-1\" />);\n      \n      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner\n    });\n\n    it('should show error when loading fails', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        json: async () => ({\n          success: false,\n          error: 'Resume not found'\n        })\n      } as Response);\n\n      render(<ResumeBuilder resumeId=\"resume-1\" />);\n\n      await waitFor(() => {\n        expect(screen.getByText('Resume not found')).toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('Save Functionality', () => {\n    it('should save new resume when save button is clicked', async () => {\n      const user = userEvent.setup();\n      const mockOnSave = jest.fn();\n\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          success: true,\n          data: {\n            id: 'new-resume-1',\n            title: 'My Resume',\n            personalInfo: {\n              firstName: '',\n              lastName: '',\n              email: '<EMAIL>'\n            },\n            summary: '',\n            experience: [],\n            education: [],\n            skills: [],\n            template: 'modern',\n            isPublic: false\n          }\n        })\n      } as Response);\n\n      render(<ResumeBuilder onSave={mockOnSave} />);\n      \n      const saveButton = screen.getByRole('button', { name: /save/i });\n      await user.click(saveButton);\n\n      await waitFor(() => {\n        expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: expect.stringContaining('\"title\":\"My Resume\"')\n        });\n      });\n\n      expect(mockOnSave).toHaveBeenCalled();\n    });\n\n    it('should update existing resume when save button is clicked', async () => {\n      const user = userEvent.setup();\n      const mockOnSave = jest.fn();\n\n      // Mock initial load\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          success: true,\n          data: {\n            id: 'resume-1',\n            title: 'Existing Resume',\n            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },\n            template: 'modern'\n          }\n        })\n      } as Response);\n\n      // Mock save\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          success: true,\n          data: { id: 'resume-1', title: 'Updated Resume' }\n        })\n      } as Response);\n\n      render(<ResumeBuilder resumeId=\"resume-1\" onSave={mockOnSave} />);\n\n      await waitFor(() => {\n        expect(screen.getByDisplayValue('Existing Resume')).toBeInTheDocument();\n      });\n\n      const saveButton = screen.getByRole('button', { name: /save/i });\n      await user.click(saveButton);\n\n      await waitFor(() => {\n        expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder/resume-1', {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: expect.any(String)\n        });\n      });\n    });\n\n    it('should show error when save fails', async () => {\n      const user = userEvent.setup();\n\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        json: async () => ({\n          success: false,\n          error: 'Failed to save resume'\n        })\n      } as Response);\n\n      render(<ResumeBuilder />);\n      \n      const saveButton = screen.getByRole('button', { name: /save/i });\n      await user.click(saveButton);\n\n      await waitFor(() => {\n        expect(screen.getByText('Failed to save resume')).toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('Preview Functionality', () => {\n    it('should show preview when preview button is clicked', async () => {\n      const user = userEvent.setup();\n      render(<ResumeBuilder />);\n      \n      const previewButton = screen.getByRole('button', { name: /preview/i });\n      await user.click(previewButton);\n      \n      expect(screen.getByText('Resume Preview')).toBeInTheDocument();\n      expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();\n    });\n\n    it('should return to edit mode from preview', async () => {\n      const user = userEvent.setup();\n      render(<ResumeBuilder />);\n      \n      // Go to preview\n      const previewButton = screen.getByRole('button', { name: /preview/i });\n      await user.click(previewButton);\n      \n      // Return to edit\n      const editButton = screen.getByRole('button', { name: /edit/i });\n      await user.click(editButton);\n      \n      expect(screen.getByText('Resume Builder')).toBeInTheDocument();\n      expect(screen.getByLabelText('Resume Title')).toBeInTheDocument();\n    });\n  });\n\n  describe('Cancel Functionality', () => {\n    it('should call onCancel when cancel button is clicked', async () => {\n      const user = userEvent.setup();\n      const mockOnCancel = jest.fn();\n      \n      render(<ResumeBuilder onCancel={mockOnCancel} />);\n      \n      const cancelButton = screen.getByRole('button', { name: /cancel/i });\n      await user.click(cancelButton);\n      \n      expect(mockOnCancel).toHaveBeenCalled();\n    });\n  });\n\n  describe('Authentication', () => {\n    it('should show error when user is not authenticated', async () => {\n      const user = userEvent.setup();\n      \n      mockUseSession.mockReturnValue({\n        data: null,\n        status: 'unauthenticated'\n      });\n\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 401,\n        json: async () => ({\n          success: false,\n          error: 'You must be logged in to save a resume'\n        })\n      } as Response);\n\n      render(<ResumeBuilder />);\n      \n      const saveButton = screen.getByRole('button', { name: /save/i });\n      await user.click(saveButton);\n\n      await waitFor(() => {\n        expect(screen.getByText('You must be logged in to save a resume')).toBeInTheDocument();\n      });\n    });\n  });\n});\n"], "version": 3}