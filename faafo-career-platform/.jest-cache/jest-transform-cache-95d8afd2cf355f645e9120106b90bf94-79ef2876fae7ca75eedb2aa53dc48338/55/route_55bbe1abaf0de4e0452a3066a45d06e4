e6afae657de8086c17c3568f06b7e995
"use strict";

/* istanbul ignore next */
function cov_1fhzcattg() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/search/route.ts";
  var hash = "8a4e3f3b6d2b0e29b28ad828e084109efcb0d7cc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/search/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "68": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 21
        }
      },
      "69": {
        start: {
          line: 40,
          column: 15
        },
        end: {
          line: 40,
          column: 37
        }
      },
      "70": {
        start: {
          line: 41,
          column: 34
        },
        end: {
          line: 41,
          column: 76
        }
      },
      "71": {
        start: {
          line: 42,
          column: 15
        },
        end: {
          line: 42,
          column: 38
        }
      },
      "72": {
        start: {
          line: 43,
          column: 30
        },
        end: {
          line: 43,
          column: 80
        }
      },
      "73": {
        start: {
          line: 44,
          column: 13
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "74": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "75": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 160,
          column: 7
        }
      },
      "76": {
        start: {
          line: 46,
          column: 93
        },
        end: {
          line: 160,
          column: 3
        }
      },
      "77": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 159,
          column: 7
        }
      },
      "78": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 158,
          column: 9
        }
      },
      "79": {
        start: {
          line: 51,
          column: 20
        },
        end: {
          line: 51,
          column: 91
        }
      },
      "80": {
        start: {
          line: 53,
          column: 16
        },
        end: {
          line: 53,
          column: 36
        }
      },
      "81": {
        start: {
          line: 54,
          column: 16
        },
        end: {
          line: 54,
          column: 138
        }
      },
      "82": {
        start: {
          line: 55,
          column: 16
        },
        end: {
          line: 55,
          column: 65
        }
      },
      "83": {
        start: {
          line: 56,
          column: 16
        },
        end: {
          line: 56,
          column: 100
        }
      },
      "84": {
        start: {
          line: 57,
          column: 16
        },
        end: {
          line: 57,
          column: 82
        }
      },
      "85": {
        start: {
          line: 58,
          column: 16
        },
        end: {
          line: 58,
          column: 56
        }
      },
      "86": {
        start: {
          line: 59,
          column: 16
        },
        end: {
          line: 69,
          column: 17
        }
      },
      "87": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 68,
          column: 28
        }
      },
      "88": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 91,
          column: 18
        }
      },
      "89": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 102,
          column: 17
        }
      },
      "90": {
        start: {
          line: 94,
          column: 20
        },
        end: {
          line: 101,
          column: 22
        }
      },
      "91": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 146,
          column: 49
        }
      },
      "92": {
        start: {
          line: 103,
          column: 128
        },
        end: {
          line: 146,
          column: 23
        }
      },
      "93": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 145,
          column: 27
        }
      },
      "94": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 144,
          column: 29
        }
      },
      "95": {
        start: {
          line: 107,
          column: 40
        },
        end: {
          line: 122,
          column: 40
        }
      },
      "96": {
        start: {
          line: 124,
          column: 36
        },
        end: {
          line: 124,
          column: 55
        }
      },
      "97": {
        start: {
          line: 125,
          column: 36
        },
        end: {
          line: 138,
          column: 39
        }
      },
      "98": {
        start: {
          line: 127,
          column: 40
        },
        end: {
          line: 137,
          column: 43
        }
      },
      "99": {
        start: {
          line: 140,
          column: 36
        },
        end: {
          line: 142,
          column: 37
        }
      },
      "100": {
        start: {
          line: 141,
          column: 40
        },
        end: {
          line: 141,
          column: 96
        }
      },
      "101": {
        start: {
          line: 143,
          column: 36
        },
        end: {
          line: 143,
          column: 75
        }
      },
      "102": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 148,
          column: 35
        }
      },
      "103": {
        start: {
          line: 149,
          column: 16
        },
        end: {
          line: 157,
          column: 24
        }
      },
      "104": {
        start: {
          line: 163,
          column: 23
        },
        end: {
          line: 184,
          column: 5
        }
      },
      "105": {
        start: {
          line: 185,
          column: 21
        },
        end: {
          line: 185,
          column: 40
        }
      },
      "106": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 197,
          column: 11
        }
      },
      "107": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 189,
          column: 62
        }
      },
      "108": {
        start: {
          line: 192,
          column: 39
        },
        end: {
          line: 197,
          column: 7
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 46,
            column: 72
          },
          end: {
            line: 46,
            column: 73
          }
        },
        loc: {
          start: {
            line: 46,
            column: 91
          },
          end: {
            line: 160,
            column: 5
          }
        },
        line: 46
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 46,
            column: 135
          },
          end: {
            line: 46,
            column: 136
          }
        },
        loc: {
          start: {
            line: 46,
            column: 147
          },
          end: {
            line: 160,
            column: 1
          }
        },
        line: 46
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 29
          },
          end: {
            line: 49,
            column: 30
          }
        },
        loc: {
          start: {
            line: 49,
            column: 43
          },
          end: {
            line: 159,
            column: 5
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 103,
            column: 114
          },
          end: {
            line: 103,
            column: 115
          }
        },
        loc: {
          start: {
            line: 103,
            column: 126
          },
          end: {
            line: 146,
            column: 25
          }
        },
        line: 103
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 103,
            column: 169
          },
          end: {
            line: 103,
            column: 170
          }
        },
        loc: {
          start: {
            line: 103,
            column: 181
          },
          end: {
            line: 146,
            column: 21
          }
        },
        line: 103
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 105,
            column: 49
          },
          end: {
            line: 105,
            column: 50
          }
        },
        loc: {
          start: {
            line: 105,
            column: 63
          },
          end: {
            line: 145,
            column: 25
          }
        },
        line: 105
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 125,
            column: 65
          },
          end: {
            line: 125,
            column: 66
          }
        },
        loc: {
          start: {
            line: 125,
            column: 82
          },
          end: {
            line: 138,
            column: 37
          }
        },
        line: 125
      },
      "20": {
        name: "getCommonSkillSuggestions",
        decl: {
          start: {
            line: 162,
            column: 9
          },
          end: {
            line: 162,
            column: 34
          }
        },
        loc: {
          start: {
            line: 162,
            column: 42
          },
          end: {
            line: 198,
            column: 1
          }
        },
        line: 162
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 187,
            column: 17
          }
        },
        loc: {
          start: {
            line: 187,
            column: 33
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 187
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 192,
            column: 13
          },
          end: {
            line: 192,
            column: 14
          }
        },
        loc: {
          start: {
            line: 192,
            column: 37
          },
          end: {
            line: 197,
            column: 9
          }
        },
        line: 192
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 158,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 51,
            column: 91
          }
        }, {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 146,
            column: 49
          }
        }, {
          start: {
            line: 147,
            column: 12
          },
          end: {
            line: 157,
            column: 24
          }
        }],
        line: 50
      },
      "33": {
        loc: {
          start: {
            line: 54,
            column: 25
          },
          end: {
            line: 54,
            column: 137
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 54,
            column: 123
          },
          end: {
            line: 54,
            column: 129
          }
        }, {
          start: {
            line: 54,
            column: 132
          },
          end: {
            line: 54,
            column: 137
          }
        }],
        line: 54
      },
      "34": {
        loc: {
          start: {
            line: 54,
            column: 25
          },
          end: {
            line: 54,
            column: 120
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 25
          },
          end: {
            line: 54,
            column: 103
          }
        }, {
          start: {
            line: 54,
            column: 107
          },
          end: {
            line: 54,
            column: 120
          }
        }],
        line: 54
      },
      "35": {
        loc: {
          start: {
            line: 54,
            column: 31
          },
          end: {
            line: 54,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 54,
            column: 72
          },
          end: {
            line: 54,
            column: 78
          }
        }, {
          start: {
            line: 54,
            column: 81
          },
          end: {
            line: 54,
            column: 93
          }
        }],
        line: 54
      },
      "36": {
        loc: {
          start: {
            line: 54,
            column: 31
          },
          end: {
            line: 54,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 31
          },
          end: {
            line: 54,
            column: 47
          }
        }, {
          start: {
            line: 54,
            column: 51
          },
          end: {
            line: 54,
            column: 69
          }
        }],
        line: 54
      },
      "37": {
        loc: {
          start: {
            line: 56,
            column: 24
          },
          end: {
            line: 56,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 81
          },
          end: {
            line: 56,
            column: 87
          }
        }, {
          start: {
            line: 56,
            column: 90
          },
          end: {
            line: 56,
            column: 99
          }
        }],
        line: 56
      },
      "38": {
        loc: {
          start: {
            line: 56,
            column: 24
          },
          end: {
            line: 56,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 24
          },
          end: {
            line: 56,
            column: 61
          }
        }, {
          start: {
            line: 56,
            column: 65
          },
          end: {
            line: 56,
            column: 78
          }
        }],
        line: 56
      },
      "39": {
        loc: {
          start: {
            line: 57,
            column: 42
          },
          end: {
            line: 57,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 42
          },
          end: {
            line: 57,
            column: 67
          }
        }, {
          start: {
            line: 57,
            column: 71
          },
          end: {
            line: 57,
            column: 75
          }
        }],
        line: 57
      },
      "40": {
        loc: {
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 69,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 69,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "41": {
        loc: {
          start: {
            line: 59,
            column: 20
          },
          end: {
            line: 59,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 20
          },
          end: {
            line: 59,
            column: 26
          }
        }, {
          start: {
            line: 59,
            column: 30
          },
          end: {
            line: 59,
            column: 46
          }
        }],
        line: 59
      },
      "42": {
        loc: {
          start: {
            line: 66,
            column: 39
          },
          end: {
            line: 66,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 66,
            column: 39
          },
          end: {
            line: 66,
            column: 44
          }
        }, {
          start: {
            line: 66,
            column: 48
          },
          end: {
            line: 66,
            column: 50
          }
        }],
        line: 66
      },
      "43": {
        loc: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 102,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 102,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "44": {
        loc: {
          start: {
            line: 106,
            column: 28
          },
          end: {
            line: 144,
            column: 29
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 107,
            column: 32
          },
          end: {
            line: 122,
            column: 40
          }
        }, {
          start: {
            line: 123,
            column: 32
          },
          end: {
            line: 143,
            column: 75
          }
        }],
        line: 106
      },
      "45": {
        loc: {
          start: {
            line: 130,
            column: 54
          },
          end: {
            line: 130,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 54
          },
          end: {
            line: 130,
            column: 68
          }
        }, {
          start: {
            line: 130,
            column: 72
          },
          end: {
            line: 130,
            column: 81
          }
        }],
        line: 130
      },
      "46": {
        loc: {
          start: {
            line: 131,
            column: 57
          },
          end: {
            line: 131,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 131,
            column: 57
          },
          end: {
            line: 131,
            column: 74
          }
        }, {
          start: {
            line: 131,
            column: 78
          },
          end: {
            line: 131,
            column: 87
          }
        }],
        line: 131
      },
      "47": {
        loc: {
          start: {
            line: 132,
            column: 56
          },
          end: {
            line: 136,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 132,
            column: 78
          },
          end: {
            line: 136,
            column: 45
          }
        }, {
          start: {
            line: 136,
            column: 48
          },
          end: {
            line: 136,
            column: 57
          }
        }],
        line: 132
      },
      "48": {
        loc: {
          start: {
            line: 133,
            column: 63
          },
          end: {
            line: 133,
            column: 115
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 63
          },
          end: {
            line: 133,
            column: 102
          }
        }, {
          start: {
            line: 133,
            column: 106
          },
          end: {
            line: 133,
            column: 115
          }
        }],
        line: 133
      },
      "49": {
        loc: {
          start: {
            line: 134,
            column: 61
          },
          end: {
            line: 134,
            column: 165
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 134,
            column: 62
          },
          end: {
            line: 134,
            column: 151
          }
        }, {
          start: {
            line: 134,
            column: 156
          },
          end: {
            line: 134,
            column: 165
          }
        }],
        line: 134
      },
      "50": {
        loc: {
          start: {
            line: 134,
            column: 62
          },
          end: {
            line: 134,
            column: 151
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 134,
            column: 129
          },
          end: {
            line: 134,
            column: 135
          }
        }, {
          start: {
            line: 134,
            column: 138
          },
          end: {
            line: 134,
            column: 151
          }
        }],
        line: 134
      },
      "51": {
        loc: {
          start: {
            line: 134,
            column: 62
          },
          end: {
            line: 134,
            column: 126
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 134,
            column: 62
          },
          end: {
            line: 134,
            column: 109
          }
        }, {
          start: {
            line: 134,
            column: 113
          },
          end: {
            line: 134,
            column: 126
          }
        }],
        line: 134
      },
      "52": {
        loc: {
          start: {
            line: 140,
            column: 36
          },
          end: {
            line: 142,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 36
          },
          end: {
            line: 142,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "53": {
        loc: {
          start: {
            line: 146,
            column: 27
          },
          end: {
            line: 146,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 146,
            column: 27
          },
          end: {
            line: 146,
            column: 33
          }
        }, {
          start: {
            line: 146,
            column: 37
          },
          end: {
            line: 146,
            column: 46
          }
        }],
        line: 146
      },
      "54": {
        loc: {
          start: {
            line: 188,
            column: 15
          },
          end: {
            line: 189,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 15
          },
          end: {
            line: 188,
            column: 60
          }
        }, {
          start: {
            line: 189,
            column: 12
          },
          end: {
            line: 189,
            column: 61
          }
        }],
        line: 188
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/search/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,6EAAwF;AAExF,uCAAsC;AACtC,iFAAqF;AACrF,uCAAkD;AAClD,mCAAyC;AAqB5B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;;oBAE/D,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAC7C,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAC;gBAEzB,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,KAAK,GAAG,MAAA,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,0CAAE,IAAI,EAAE,CAAC;gBACtC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBAClE,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAE9C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/B,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAa;4BACtB,IAAI,EAAE;gCACJ,OAAO,EAAE,IAAI;gCACb,MAAM,EAAE,EAAE;gCACV,KAAK,EAAE,CAAC;gCACR,KAAK,EAAE,KAAK,IAAI,EAAE;6BACnB;yBACF,CAAC,EAAC;gBACL,CAAC;gBAGK,gBAAgB,GAAQ;oBAC5B,EAAE,EAAE;wBACF;4BACE,IAAI,EAAE;gCACJ,QAAQ,EAAE,KAAK;gCACf,IAAI,EAAE,aAAa;6BACpB;yBACF;wBACD;4BACE,WAAW,EAAE;gCACX,QAAQ,EAAE,KAAK;gCACf,IAAI,EAAE,aAAa;6BACpB;yBACF;wBACD;4BACE,QAAQ,EAAE;gCACR,QAAQ,EAAE,KAAK;gCACf,IAAI,EAAE,aAAa;6BACpB;yBACF;qBACF;iBACF,CAAC;gBAEF,mCAAmC;gBACnC,IAAI,QAAQ,EAAE,CAAC;oBACb,gBAAgB,CAAC,GAAG,GAAG;wBACrB;4BACE,QAAQ,EAAE;gCACR,MAAM,EAAE,QAAQ;gCAChB,IAAI,EAAE,aAAa;6BACpB;yBACF;qBACF,CAAC;gBACJ,CAAC;gBAGc,qBAAM,kDAA0B,CAAC,kBAAkB,CAChE,KAAK,EACL;;;;wCAEiB,qBAAM,eAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;wCACzC,KAAK,EAAE,gBAAgB;wCACvB,OAAO,EAAE;4CACP,UAAU,EAAE;gDACV,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gDACzB,OAAO,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;gDAC7B,IAAI,EAAE,CAAC;6CACR;yCACF;wCACD,IAAI,EAAE,KAAK;wCACX,OAAO,EAAE;4CACP;gDACE,IAAI,EAAE,KAAK;6CACZ;yCACF;qCACF,CAAC,EAAA;;oCAfI,MAAM,GAAG,SAeb;oCAGI,eAAe,GAAwB,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK;;wCAAI,OAAA,CAAC;4CAChE,EAAE,EAAE,KAAK,CAAC,EAAE;4CACZ,IAAI,EAAE,KAAK,CAAC,IAAI;4CAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,SAAS;4CACrC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,SAAS;4CAC3C,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gDAChC,aAAa,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,mBAAmB,IAAI,SAAS;gDACnE,WAAW,EAAE,CAAA,MAAA,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,0CAAE,QAAQ,EAAE,KAAI,SAAS;gDACrE,UAAU,EAAE,SAAS,EAAE,uCAAuC;6CAC/D,CAAC,CAAC,CAAC,SAAS;yCACd,CAAC,CAAA;qCAAA,CAAC,CAAC;oCAEJ,0EAA0E;oCAC1E,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wCACjC,sBAAO,yBAAyB,CAAC,KAAK,CAAC,EAAC;oCAC1C,CAAC;oCAED,sBAAO,eAAe,EAAC;;;yBACxB,EACD,MAAM,IAAI,SAAS,CACpB,EAAA;;gBA1CK,MAAM,GAAG,SA0Cd;gBAED,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAa;wBACtB,IAAI,EAAE;4BACJ,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,MAAM;4BACd,KAAK,EAAE,MAAM,CAAC,MAAM;4BACpB,KAAK,OAAA;yBACN;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAEH,iEAAiE;AACjE,SAAS,yBAAyB,CAAC,KAAa;IAC9C,IAAM,YAAY,GAAG;QACnB,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,uBAAuB,EAAE;QACzD,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,uBAAuB,EAAE;QACrD,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,qBAAqB,EAAE;QAClD,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,sBAAsB,EAAE;QACrD,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,uBAAuB,EAAE;QACzD,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,uBAAuB,EAAE;QAClD,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,EAAE;QAC9C,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE;QAC5C,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,iBAAiB,EAAE;QAC5C,EAAE,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,aAAa,EAAE;QACvD,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE;QAClD,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE;QAC/C,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,aAAa,EAAE;QACpD,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE;QAChD,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC/C,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE;QAC5C,EAAE,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,oBAAoB,EAAE;QAC7D,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,sBAAsB,EAAE;QACvD,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,uBAAuB,EAAE;QACtD,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,uBAAuB,EAAE;KACnD,CAAC;IAEF,IAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IACvC,OAAO,YAAY;SAChB,MAAM,CAAC,UAAA,KAAK;QACX,OAAA,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7C,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;IADjD,CACiD,CAClD;SACA,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;SACZ,GAAG,CAAC,UAAC,KAAK,EAAE,KAAK,IAAK,OAAA,CAAC;QACtB,EAAE,EAAE,iBAAU,KAAK,CAAE;QACrB,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,WAAW,EAAE,UAAG,KAAK,CAAC,IAAI,gBAAM,KAAK,CAAC,QAAQ,CAAE;KACjD,CAAC,EALqB,CAKrB,CAAC,CAAC;AACR,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/skills/search/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { prisma } from '@/lib/prisma';\nimport { skillGapPerformanceMonitor } from '@/lib/performance/skill-gap-performance';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\n\ninterface SkillSearchResult {\n  id: string;\n  name: string;\n  category: string;\n  description?: string;\n  marketData?: {\n    averageSalary?: number;\n    demandLevel?: string;\n    growthRate?: number;\n  };\n}\n\ninterface SkillSearchResponse {\n  success: boolean;\n  skills: SkillSearchResult[];\n  total: number;\n  query: string;\n}\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<SkillSearchResponse>>> => {\n  // Get user session for performance tracking\n  const session = await getServerSession(authOptions);\n  const userId = session?.user?.id;\n\n  const { searchParams } = new URL(request.url);\n  const query = searchParams.get('q')?.trim();\n  const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);\n  const category = searchParams.get('category');\n\n  if (!query || query.length < 2) {\n    return NextResponse.json({\n      success: true as const,\n      data: {\n        success: true,\n        skills: [],\n        total: 0,\n        query: query || '',\n      }\n    });\n  }\n\n  // Build search conditions\n  const searchConditions: any = {\n    OR: [\n      {\n        name: {\n          contains: query,\n          mode: 'insensitive',\n        },\n      },\n      {\n        description: {\n          contains: query,\n          mode: 'insensitive',\n        },\n      },\n      {\n        category: {\n          contains: query,\n          mode: 'insensitive',\n        },\n      },\n    ],\n  };\n\n  // Add category filter if specified\n  if (category) {\n    searchConditions.AND = [\n      {\n        category: {\n          equals: category,\n          mode: 'insensitive',\n        },\n      },\n    ];\n  }\n\n  // Use performance monitoring for skill search\n  const result = await skillGapPerformanceMonitor.monitorSkillSearch(\n    query,\n    async () => {\n      // Search skills in database\n      const skills = await prisma.skill.findMany({\n        where: searchConditions,\n        include: {\n          marketData: {\n            where: { isActive: true },\n            orderBy: { dataDate: 'desc' },\n            take: 1,\n          },\n        },\n        take: limit,\n        orderBy: [\n          {\n            name: 'asc',\n          },\n        ],\n      });\n\n      // Format response\n      const formattedSkills: SkillSearchResult[] = skills.map(skill => ({\n        id: skill.id,\n        name: skill.name,\n        category: skill.category || 'General',\n        description: skill.description || undefined,\n        marketData: skill.marketData[0] ? {\n          averageSalary: skill.marketData[0].averageSalaryImpact || undefined,\n          demandLevel: skill.marketData[0].demandLevel?.toString() || undefined,\n          growthRate: undefined, // growthTrend is an enum, not a number\n        } : undefined,\n      }));\n\n      // If no results found in database, provide some common skills as fallback\n      if (formattedSkills.length === 0) {\n        return getCommonSkillSuggestions(query);\n      }\n\n      return formattedSkills;\n    },\n    userId || undefined\n  );\n\n  return NextResponse.json({\n    success: true as const,\n    data: {\n      success: true,\n      skills: result,\n      total: result.length,\n      query,\n    }\n  });\n});\n\n// Fallback common skills when database search returns no results\nfunction getCommonSkillSuggestions(query: string): SkillSearchResult[] {\n  const commonSkills = [\n    { name: 'JavaScript', category: 'Programming Languages' },\n    { name: 'Python', category: 'Programming Languages' },\n    { name: 'React', category: 'Frontend Frameworks' },\n    { name: 'Node.js', category: 'Backend Technologies' },\n    { name: 'TypeScript', category: 'Programming Languages' },\n    { name: 'SQL', category: 'Database Technologies' },\n    { name: 'Git', category: 'Development Tools' },\n    { name: 'Docker', category: 'DevOps Tools' },\n    { name: 'AWS', category: 'Cloud Platforms' },\n    { name: 'Project Management', category: 'Soft Skills' },\n    { name: 'Communication', category: 'Soft Skills' },\n    { name: 'Leadership', category: 'Soft Skills' },\n    { name: 'Problem Solving', category: 'Soft Skills' },\n    { name: 'Data Analysis', category: 'Analytics' },\n    { name: 'Machine Learning', category: 'AI/ML' },\n    { name: 'UI/UX Design', category: 'Design' },\n    { name: 'Agile Methodology', category: 'Project Management' },\n    { name: 'REST APIs', category: 'Backend Technologies' },\n    { name: 'MongoDB', category: 'Database Technologies' },\n    { name: 'CSS', category: 'Frontend Technologies' },\n  ];\n\n  const queryLower = query.toLowerCase();\n  return commonSkills\n    .filter(skill => \n      skill.name.toLowerCase().includes(queryLower) ||\n      skill.category.toLowerCase().includes(queryLower)\n    )\n    .slice(0, 10)\n    .map((skill, index) => ({\n      id: `common-${index}`,\n      name: skill.name,\n      category: skill.category,\n      description: `${skill.name} - ${skill.category}`,\n    }));\n}\n\n\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8a4e3f3b6d2b0e29b28ad828e084109efcb0d7cc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1fhzcattg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1fhzcattg();
var __awaiter =
/* istanbul ignore next */
(cov_1fhzcattg().s[0]++,
/* istanbul ignore next */
(cov_1fhzcattg().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1fhzcattg().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1fhzcattg().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1fhzcattg().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1fhzcattg().f[1]++;
    cov_1fhzcattg().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1fhzcattg().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1fhzcattg().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1fhzcattg().f[2]++;
      cov_1fhzcattg().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1fhzcattg().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1fhzcattg().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1fhzcattg().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1fhzcattg().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1fhzcattg().f[4]++;
      cov_1fhzcattg().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1fhzcattg().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1fhzcattg().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1fhzcattg().f[5]++;
      cov_1fhzcattg().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1fhzcattg().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1fhzcattg().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1fhzcattg().f[6]++;
      cov_1fhzcattg().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1fhzcattg().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1fhzcattg().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1fhzcattg().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1fhzcattg().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1fhzcattg().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1fhzcattg().s[12]++,
/* istanbul ignore next */
(cov_1fhzcattg().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1fhzcattg().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1fhzcattg().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1fhzcattg().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1fhzcattg().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1fhzcattg().f[8]++;
        cov_1fhzcattg().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1fhzcattg().b[6][0]++;
          cov_1fhzcattg().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1fhzcattg().b[6][1]++;
        }
        cov_1fhzcattg().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1fhzcattg().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1fhzcattg().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1fhzcattg().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1fhzcattg().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1fhzcattg().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1fhzcattg().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1fhzcattg().f[9]++;
    cov_1fhzcattg().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1fhzcattg().f[10]++;
    cov_1fhzcattg().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1fhzcattg().f[11]++;
      cov_1fhzcattg().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1fhzcattg().f[12]++;
    cov_1fhzcattg().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1fhzcattg().b[9][0]++;
      cov_1fhzcattg().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1fhzcattg().b[9][1]++;
    }
    cov_1fhzcattg().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1fhzcattg().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1fhzcattg().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1fhzcattg().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1fhzcattg().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1fhzcattg().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1fhzcattg().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1fhzcattg().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1fhzcattg().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1fhzcattg().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1fhzcattg().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1fhzcattg().b[15][0]++,
        /* istanbul ignore next */
        (cov_1fhzcattg().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1fhzcattg().b[16][1]++,
        /* istanbul ignore next */
        (cov_1fhzcattg().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1fhzcattg().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1fhzcattg().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1fhzcattg().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1fhzcattg().b[12][0]++;
          cov_1fhzcattg().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1fhzcattg().b[12][1]++;
        }
        cov_1fhzcattg().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1fhzcattg().b[18][0]++;
          cov_1fhzcattg().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1fhzcattg().b[18][1]++;
        }
        cov_1fhzcattg().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1fhzcattg().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1fhzcattg().b[19][1]++;
            cov_1fhzcattg().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1fhzcattg().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1fhzcattg().b[19][2]++;
            cov_1fhzcattg().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1fhzcattg().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1fhzcattg().b[19][3]++;
            cov_1fhzcattg().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1fhzcattg().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1fhzcattg().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1fhzcattg().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1fhzcattg().b[19][4]++;
            cov_1fhzcattg().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1fhzcattg().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1fhzcattg().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1fhzcattg().b[19][5]++;
            cov_1fhzcattg().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1fhzcattg().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1fhzcattg().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1fhzcattg().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1fhzcattg().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1fhzcattg().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1fhzcattg().b[20][0]++;
              cov_1fhzcattg().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1fhzcattg().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1fhzcattg().b[20][1]++;
            }
            cov_1fhzcattg().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1fhzcattg().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1fhzcattg().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1fhzcattg().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1fhzcattg().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1fhzcattg().b[23][0]++;
              cov_1fhzcattg().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1fhzcattg().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1fhzcattg().b[23][1]++;
            }
            cov_1fhzcattg().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1fhzcattg().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1fhzcattg().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1fhzcattg().b[25][0]++;
              cov_1fhzcattg().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1fhzcattg().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1fhzcattg().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1fhzcattg().b[25][1]++;
            }
            cov_1fhzcattg().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1fhzcattg().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1fhzcattg().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1fhzcattg().b[27][0]++;
              cov_1fhzcattg().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1fhzcattg().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1fhzcattg().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1fhzcattg().b[27][1]++;
            }
            cov_1fhzcattg().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1fhzcattg().b[29][0]++;
              cov_1fhzcattg().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1fhzcattg().b[29][1]++;
            }
            cov_1fhzcattg().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1fhzcattg().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1fhzcattg().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1fhzcattg().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1fhzcattg().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1fhzcattg().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1fhzcattg().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1fhzcattg().b[30][0]++;
      cov_1fhzcattg().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1fhzcattg().b[30][1]++;
    }
    cov_1fhzcattg().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1fhzcattg().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1fhzcattg().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1fhzcattg().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1fhzcattg().s[68]++;
exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1fhzcattg().s[69]++, require("next/server"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1fhzcattg().s[70]++, require("@/lib/unified-api-error-handler"));
var prisma_1 =
/* istanbul ignore next */
(cov_1fhzcattg().s[71]++, require("@/lib/prisma"));
var skill_gap_performance_1 =
/* istanbul ignore next */
(cov_1fhzcattg().s[72]++, require("@/lib/performance/skill-gap-performance"));
var next_1 =
/* istanbul ignore next */
(cov_1fhzcattg().s[73]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_1fhzcattg().s[74]++, require("@/lib/auth"));
/* istanbul ignore next */
cov_1fhzcattg().s[75]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1fhzcattg().f[13]++;
  cov_1fhzcattg().s[76]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1fhzcattg().f[14]++;
    var session, userId, searchParams, query, limit, category, searchConditions, result;
    var _a, _b;
    /* istanbul ignore next */
    cov_1fhzcattg().s[77]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1fhzcattg().f[15]++;
      cov_1fhzcattg().s[78]++;
      switch (_c.label) {
        case 0:
          /* istanbul ignore next */
          cov_1fhzcattg().b[32][0]++;
          cov_1fhzcattg().s[79]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_1fhzcattg().b[32][1]++;
          cov_1fhzcattg().s[80]++;
          session = _c.sent();
          /* istanbul ignore next */
          cov_1fhzcattg().s[81]++;
          userId =
          /* istanbul ignore next */
          (cov_1fhzcattg().b[34][0]++, (_a =
          /* istanbul ignore next */
          (cov_1fhzcattg().b[36][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_1fhzcattg().b[36][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_1fhzcattg().b[35][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1fhzcattg().b[35][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_1fhzcattg().b[34][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_1fhzcattg().b[33][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1fhzcattg().b[33][1]++, _a.id);
          /* istanbul ignore next */
          cov_1fhzcattg().s[82]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_1fhzcattg().s[83]++;
          query =
          /* istanbul ignore next */
          (cov_1fhzcattg().b[38][0]++, (_b = searchParams.get('q')) === null) ||
          /* istanbul ignore next */
          (cov_1fhzcattg().b[38][1]++, _b === void 0) ?
          /* istanbul ignore next */
          (cov_1fhzcattg().b[37][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1fhzcattg().b[37][1]++, _b.trim());
          /* istanbul ignore next */
          cov_1fhzcattg().s[84]++;
          limit = Math.min(parseInt(
          /* istanbul ignore next */
          (cov_1fhzcattg().b[39][0]++, searchParams.get('limit')) ||
          /* istanbul ignore next */
          (cov_1fhzcattg().b[39][1]++, '10')), 50);
          /* istanbul ignore next */
          cov_1fhzcattg().s[85]++;
          category = searchParams.get('category');
          /* istanbul ignore next */
          cov_1fhzcattg().s[86]++;
          if (
          /* istanbul ignore next */
          (cov_1fhzcattg().b[41][0]++, !query) ||
          /* istanbul ignore next */
          (cov_1fhzcattg().b[41][1]++, query.length < 2)) {
            /* istanbul ignore next */
            cov_1fhzcattg().b[40][0]++;
            cov_1fhzcattg().s[87]++;
            return [2 /*return*/, server_1.NextResponse.json({
              success: true,
              data: {
                success: true,
                skills: [],
                total: 0,
                query:
                /* istanbul ignore next */
                (cov_1fhzcattg().b[42][0]++, query) ||
                /* istanbul ignore next */
                (cov_1fhzcattg().b[42][1]++, '')
              }
            })];
          } else
          /* istanbul ignore next */
          {
            cov_1fhzcattg().b[40][1]++;
          }
          cov_1fhzcattg().s[88]++;
          searchConditions = {
            OR: [{
              name: {
                contains: query,
                mode: 'insensitive'
              }
            }, {
              description: {
                contains: query,
                mode: 'insensitive'
              }
            }, {
              category: {
                contains: query,
                mode: 'insensitive'
              }
            }]
          };
          // Add category filter if specified
          /* istanbul ignore next */
          cov_1fhzcattg().s[89]++;
          if (category) {
            /* istanbul ignore next */
            cov_1fhzcattg().b[43][0]++;
            cov_1fhzcattg().s[90]++;
            searchConditions.AND = [{
              category: {
                equals: category,
                mode: 'insensitive'
              }
            }];
          } else
          /* istanbul ignore next */
          {
            cov_1fhzcattg().b[43][1]++;
          }
          cov_1fhzcattg().s[91]++;
          return [4 /*yield*/, skill_gap_performance_1.skillGapPerformanceMonitor.monitorSkillSearch(query, function () {
            /* istanbul ignore next */
            cov_1fhzcattg().f[16]++;
            cov_1fhzcattg().s[92]++;
            return __awaiter(void 0, void 0, void 0, function () {
              /* istanbul ignore next */
              cov_1fhzcattg().f[17]++;
              var skills, formattedSkills;
              /* istanbul ignore next */
              cov_1fhzcattg().s[93]++;
              return __generator(this, function (_a) {
                /* istanbul ignore next */
                cov_1fhzcattg().f[18]++;
                cov_1fhzcattg().s[94]++;
                switch (_a.label) {
                  case 0:
                    /* istanbul ignore next */
                    cov_1fhzcattg().b[44][0]++;
                    cov_1fhzcattg().s[95]++;
                    return [4 /*yield*/, prisma_1.prisma.skill.findMany({
                      where: searchConditions,
                      include: {
                        marketData: {
                          where: {
                            isActive: true
                          },
                          orderBy: {
                            dataDate: 'desc'
                          },
                          take: 1
                        }
                      },
                      take: limit,
                      orderBy: [{
                        name: 'asc'
                      }]
                    })];
                  case 1:
                    /* istanbul ignore next */
                    cov_1fhzcattg().b[44][1]++;
                    cov_1fhzcattg().s[96]++;
                    skills = _a.sent();
                    /* istanbul ignore next */
                    cov_1fhzcattg().s[97]++;
                    formattedSkills = skills.map(function (skill) {
                      /* istanbul ignore next */
                      cov_1fhzcattg().f[19]++;
                      var _a;
                      /* istanbul ignore next */
                      cov_1fhzcattg().s[98]++;
                      return {
                        id: skill.id,
                        name: skill.name,
                        category:
                        /* istanbul ignore next */
                        (cov_1fhzcattg().b[45][0]++, skill.category) ||
                        /* istanbul ignore next */
                        (cov_1fhzcattg().b[45][1]++, 'General'),
                        description:
                        /* istanbul ignore next */
                        (cov_1fhzcattg().b[46][0]++, skill.description) ||
                        /* istanbul ignore next */
                        (cov_1fhzcattg().b[46][1]++, undefined),
                        marketData: skill.marketData[0] ?
                        /* istanbul ignore next */
                        (cov_1fhzcattg().b[47][0]++, {
                          averageSalary:
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[48][0]++, skill.marketData[0].averageSalaryImpact) ||
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[48][1]++, undefined),
                          demandLevel:
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[49][0]++,
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[51][0]++, (_a = skill.marketData[0].demandLevel) === null) ||
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[51][1]++, _a === void 0) ?
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[50][0]++, void 0) :
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[50][1]++, _a.toString())) ||
                          /* istanbul ignore next */
                          (cov_1fhzcattg().b[49][1]++, undefined),
                          growthRate: undefined // growthTrend is an enum, not a number
                        }) :
                        /* istanbul ignore next */
                        (cov_1fhzcattg().b[47][1]++, undefined)
                      };
                    });
                    // If no results found in database, provide some common skills as fallback
                    /* istanbul ignore next */
                    cov_1fhzcattg().s[99]++;
                    if (formattedSkills.length === 0) {
                      /* istanbul ignore next */
                      cov_1fhzcattg().b[52][0]++;
                      cov_1fhzcattg().s[100]++;
                      return [2 /*return*/, getCommonSkillSuggestions(query)];
                    } else
                    /* istanbul ignore next */
                    {
                      cov_1fhzcattg().b[52][1]++;
                    }
                    cov_1fhzcattg().s[101]++;
                    return [2 /*return*/, formattedSkills];
                }
              });
            });
          },
          /* istanbul ignore next */
          (cov_1fhzcattg().b[53][0]++, userId) ||
          /* istanbul ignore next */
          (cov_1fhzcattg().b[53][1]++, undefined))];
        case 2:
          /* istanbul ignore next */
          cov_1fhzcattg().b[32][2]++;
          cov_1fhzcattg().s[102]++;
          result = _c.sent();
          /* istanbul ignore next */
          cov_1fhzcattg().s[103]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              success: true,
              skills: result,
              total: result.length,
              query: query
            }
          })];
      }
    });
  });
});
// Fallback common skills when database search returns no results
function getCommonSkillSuggestions(query) {
  /* istanbul ignore next */
  cov_1fhzcattg().f[20]++;
  var commonSkills =
  /* istanbul ignore next */
  (cov_1fhzcattg().s[104]++, [{
    name: 'JavaScript',
    category: 'Programming Languages'
  }, {
    name: 'Python',
    category: 'Programming Languages'
  }, {
    name: 'React',
    category: 'Frontend Frameworks'
  }, {
    name: 'Node.js',
    category: 'Backend Technologies'
  }, {
    name: 'TypeScript',
    category: 'Programming Languages'
  }, {
    name: 'SQL',
    category: 'Database Technologies'
  }, {
    name: 'Git',
    category: 'Development Tools'
  }, {
    name: 'Docker',
    category: 'DevOps Tools'
  }, {
    name: 'AWS',
    category: 'Cloud Platforms'
  }, {
    name: 'Project Management',
    category: 'Soft Skills'
  }, {
    name: 'Communication',
    category: 'Soft Skills'
  }, {
    name: 'Leadership',
    category: 'Soft Skills'
  }, {
    name: 'Problem Solving',
    category: 'Soft Skills'
  }, {
    name: 'Data Analysis',
    category: 'Analytics'
  }, {
    name: 'Machine Learning',
    category: 'AI/ML'
  }, {
    name: 'UI/UX Design',
    category: 'Design'
  }, {
    name: 'Agile Methodology',
    category: 'Project Management'
  }, {
    name: 'REST APIs',
    category: 'Backend Technologies'
  }, {
    name: 'MongoDB',
    category: 'Database Technologies'
  }, {
    name: 'CSS',
    category: 'Frontend Technologies'
  }]);
  var queryLower =
  /* istanbul ignore next */
  (cov_1fhzcattg().s[105]++, query.toLowerCase());
  /* istanbul ignore next */
  cov_1fhzcattg().s[106]++;
  return commonSkills.filter(function (skill) {
    /* istanbul ignore next */
    cov_1fhzcattg().f[21]++;
    cov_1fhzcattg().s[107]++;
    return /* istanbul ignore next */(cov_1fhzcattg().b[54][0]++, skill.name.toLowerCase().includes(queryLower)) ||
    /* istanbul ignore next */
    (cov_1fhzcattg().b[54][1]++, skill.category.toLowerCase().includes(queryLower));
  }).slice(0, 10).map(function (skill, index) {
    /* istanbul ignore next */
    cov_1fhzcattg().f[22]++;
    cov_1fhzcattg().s[108]++;
    return {
      id: "common-".concat(index),
      name: skill.name,
      category: skill.category,
      description: "".concat(skill.name, " - ").concat(skill.category)
    };
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJzZXJ2ZXJfMSIsImNvdl8xZmh6Y2F0dGciLCJzIiwicmVxdWlyZSIsInVuaWZpZWRfYXBpX2Vycm9yX2hhbmRsZXJfMSIsInByaXNtYV8xIiwic2tpbGxfZ2FwX3BlcmZvcm1hbmNlXzEiLCJuZXh0XzEiLCJhdXRoXzEiLCJleHBvcnRzIiwiR0VUIiwid2l0aFVuaWZpZWRFcnJvckhhbmRsaW5nIiwicmVxdWVzdCIsImYiLCJfX2F3YWl0ZXIiLCJQcm9taXNlIiwiZ2V0U2VydmVyU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwic2Vzc2lvbiIsIl9jIiwic2VudCIsInVzZXJJZCIsImIiLCJfYSIsInVzZXIiLCJpZCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsInF1ZXJ5IiwiX2IiLCJnZXQiLCJ0cmltIiwibGltaXQiLCJNYXRoIiwibWluIiwicGFyc2VJbnQiLCJjYXRlZ29yeSIsImxlbmd0aCIsIk5leHRSZXNwb25zZSIsImpzb24iLCJzdWNjZXNzIiwiZGF0YSIsInNraWxscyIsInRvdGFsIiwic2VhcmNoQ29uZGl0aW9ucyIsIk9SIiwibmFtZSIsImNvbnRhaW5zIiwibW9kZSIsImRlc2NyaXB0aW9uIiwiQU5EIiwiZXF1YWxzIiwic2tpbGxHYXBQZXJmb3JtYW5jZU1vbml0b3IiLCJtb25pdG9yU2tpbGxTZWFyY2giLCJwcmlzbWEiLCJza2lsbCIsImZpbmRNYW55Iiwid2hlcmUiLCJpbmNsdWRlIiwibWFya2V0RGF0YSIsImlzQWN0aXZlIiwib3JkZXJCeSIsImRhdGFEYXRlIiwidGFrZSIsImZvcm1hdHRlZFNraWxscyIsIm1hcCIsInVuZGVmaW5lZCIsImF2ZXJhZ2VTYWxhcnkiLCJhdmVyYWdlU2FsYXJ5SW1wYWN0IiwiZGVtYW5kTGV2ZWwiLCJ0b1N0cmluZyIsImdyb3d0aFJhdGUiLCJnZXRDb21tb25Ta2lsbFN1Z2dlc3Rpb25zIiwicmVzdWx0IiwiY29tbW9uU2tpbGxzIiwicXVlcnlMb3dlciIsInRvTG93ZXJDYXNlIiwiZmlsdGVyIiwiaW5jbHVkZXMiLCJzbGljZSIsImluZGV4IiwiY29uY2F0Il0sInNvdXJjZXMiOlsiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9hcHAvYXBpL3NraWxscy9zZWFyY2gvcm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IHdpdGhVbmlmaWVkRXJyb3JIYW5kbGluZywgQXBpUmVzcG9uc2UgfSBmcm9tICdAL2xpYi91bmlmaWVkLWFwaS1lcnJvci1oYW5kbGVyJztcbmltcG9ydCB7IHdpdGhSYXRlTGltaXQgfSBmcm9tICdAL2xpYi9yYXRlTGltaXQnO1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnQC9saWIvcHJpc21hJztcbmltcG9ydCB7IHNraWxsR2FwUGVyZm9ybWFuY2VNb25pdG9yIH0gZnJvbSAnQC9saWIvcGVyZm9ybWFuY2Uvc2tpbGwtZ2FwLXBlcmZvcm1hbmNlJztcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvbmV4dCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgnO1xuXG5pbnRlcmZhY2UgU2tpbGxTZWFyY2hSZXN1bHQge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xuICBtYXJrZXREYXRhPzoge1xuICAgIGF2ZXJhZ2VTYWxhcnk/OiBudW1iZXI7XG4gICAgZGVtYW5kTGV2ZWw/OiBzdHJpbmc7XG4gICAgZ3Jvd3RoUmF0ZT86IG51bWJlcjtcbiAgfTtcbn1cblxuaW50ZXJmYWNlIFNraWxsU2VhcmNoUmVzcG9uc2Uge1xuICBzdWNjZXNzOiBib29sZWFuO1xuICBza2lsbHM6IFNraWxsU2VhcmNoUmVzdWx0W107XG4gIHRvdGFsOiBudW1iZXI7XG4gIHF1ZXJ5OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBHRVQgPSB3aXRoVW5pZmllZEVycm9ySGFuZGxpbmcoYXN5bmMgKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KTogUHJvbWlzZTxOZXh0UmVzcG9uc2U8QXBpUmVzcG9uc2U8U2tpbGxTZWFyY2hSZXNwb25zZT4+PiA9PiB7XG4gIC8vIEdldCB1c2VyIHNlc3Npb24gZm9yIHBlcmZvcm1hbmNlIHRyYWNraW5nXG4gIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKTtcbiAgY29uc3QgdXNlcklkID0gc2Vzc2lvbj8udXNlcj8uaWQ7XG5cbiAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xuICBjb25zdCBxdWVyeSA9IHNlYXJjaFBhcmFtcy5nZXQoJ3EnKT8udHJpbSgpO1xuICBjb25zdCBsaW1pdCA9IE1hdGgubWluKHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoJ2xpbWl0JykgfHwgJzEwJyksIDUwKTtcbiAgY29uc3QgY2F0ZWdvcnkgPSBzZWFyY2hQYXJhbXMuZ2V0KCdjYXRlZ29yeScpO1xuXG4gIGlmICghcXVlcnkgfHwgcXVlcnkubGVuZ3RoIDwgMikge1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlIGFzIGNvbnN0LFxuICAgICAgZGF0YToge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBza2lsbHM6IFtdLFxuICAgICAgICB0b3RhbDogMCxcbiAgICAgICAgcXVlcnk6IHF1ZXJ5IHx8ICcnLFxuICAgICAgfVxuICAgIH0pO1xuICB9XG5cbiAgLy8gQnVpbGQgc2VhcmNoIGNvbmRpdGlvbnNcbiAgY29uc3Qgc2VhcmNoQ29uZGl0aW9uczogYW55ID0ge1xuICAgIE9SOiBbXG4gICAgICB7XG4gICAgICAgIG5hbWU6IHtcbiAgICAgICAgICBjb250YWluczogcXVlcnksXG4gICAgICAgICAgbW9kZTogJ2luc2Vuc2l0aXZlJyxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGRlc2NyaXB0aW9uOiB7XG4gICAgICAgICAgY29udGFpbnM6IHF1ZXJ5LFxuICAgICAgICAgIG1vZGU6ICdpbnNlbnNpdGl2ZScsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBjYXRlZ29yeToge1xuICAgICAgICAgIGNvbnRhaW5zOiBxdWVyeSxcbiAgICAgICAgICBtb2RlOiAnaW5zZW5zaXRpdmUnLFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICBdLFxuICB9O1xuXG4gIC8vIEFkZCBjYXRlZ29yeSBmaWx0ZXIgaWYgc3BlY2lmaWVkXG4gIGlmIChjYXRlZ29yeSkge1xuICAgIHNlYXJjaENvbmRpdGlvbnMuQU5EID0gW1xuICAgICAge1xuICAgICAgICBjYXRlZ29yeToge1xuICAgICAgICAgIGVxdWFsczogY2F0ZWdvcnksXG4gICAgICAgICAgbW9kZTogJ2luc2Vuc2l0aXZlJyxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgXTtcbiAgfVxuXG4gIC8vIFVzZSBwZXJmb3JtYW5jZSBtb25pdG9yaW5nIGZvciBza2lsbCBzZWFyY2hcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2tpbGxHYXBQZXJmb3JtYW5jZU1vbml0b3IubW9uaXRvclNraWxsU2VhcmNoKFxuICAgIHF1ZXJ5LFxuICAgIGFzeW5jICgpID0+IHtcbiAgICAgIC8vIFNlYXJjaCBza2lsbHMgaW4gZGF0YWJhc2VcbiAgICAgIGNvbnN0IHNraWxscyA9IGF3YWl0IHByaXNtYS5za2lsbC5maW5kTWFueSh7XG4gICAgICAgIHdoZXJlOiBzZWFyY2hDb25kaXRpb25zLFxuICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgbWFya2V0RGF0YToge1xuICAgICAgICAgICAgd2hlcmU6IHsgaXNBY3RpdmU6IHRydWUgfSxcbiAgICAgICAgICAgIG9yZGVyQnk6IHsgZGF0YURhdGU6ICdkZXNjJyB9LFxuICAgICAgICAgICAgdGFrZTogMSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgICB0YWtlOiBsaW1pdCxcbiAgICAgICAgb3JkZXJCeTogW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdhc2MnLFxuICAgICAgICAgIH0sXG4gICAgICAgIF0sXG4gICAgICB9KTtcblxuICAgICAgLy8gRm9ybWF0IHJlc3BvbnNlXG4gICAgICBjb25zdCBmb3JtYXR0ZWRTa2lsbHM6IFNraWxsU2VhcmNoUmVzdWx0W10gPSBza2lsbHMubWFwKHNraWxsID0+ICh7XG4gICAgICAgIGlkOiBza2lsbC5pZCxcbiAgICAgICAgbmFtZTogc2tpbGwubmFtZSxcbiAgICAgICAgY2F0ZWdvcnk6IHNraWxsLmNhdGVnb3J5IHx8ICdHZW5lcmFsJyxcbiAgICAgICAgZGVzY3JpcHRpb246IHNraWxsLmRlc2NyaXB0aW9uIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgbWFya2V0RGF0YTogc2tpbGwubWFya2V0RGF0YVswXSA/IHtcbiAgICAgICAgICBhdmVyYWdlU2FsYXJ5OiBza2lsbC5tYXJrZXREYXRhWzBdLmF2ZXJhZ2VTYWxhcnlJbXBhY3QgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIGRlbWFuZExldmVsOiBza2lsbC5tYXJrZXREYXRhWzBdLmRlbWFuZExldmVsPy50b1N0cmluZygpIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICBncm93dGhSYXRlOiB1bmRlZmluZWQsIC8vIGdyb3d0aFRyZW5kIGlzIGFuIGVudW0sIG5vdCBhIG51bWJlclxuICAgICAgICB9IDogdW5kZWZpbmVkLFxuICAgICAgfSkpO1xuXG4gICAgICAvLyBJZiBubyByZXN1bHRzIGZvdW5kIGluIGRhdGFiYXNlLCBwcm92aWRlIHNvbWUgY29tbW9uIHNraWxscyBhcyBmYWxsYmFja1xuICAgICAgaWYgKGZvcm1hdHRlZFNraWxscy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgcmV0dXJuIGdldENvbW1vblNraWxsU3VnZ2VzdGlvbnMocXVlcnkpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gZm9ybWF0dGVkU2tpbGxzO1xuICAgIH0sXG4gICAgdXNlcklkIHx8IHVuZGVmaW5lZFxuICApO1xuXG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgc3VjY2VzczogdHJ1ZSBhcyBjb25zdCxcbiAgICBkYXRhOiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgc2tpbGxzOiByZXN1bHQsXG4gICAgICB0b3RhbDogcmVzdWx0Lmxlbmd0aCxcbiAgICAgIHF1ZXJ5LFxuICAgIH1cbiAgfSk7XG59KTtcblxuLy8gRmFsbGJhY2sgY29tbW9uIHNraWxscyB3aGVuIGRhdGFiYXNlIHNlYXJjaCByZXR1cm5zIG5vIHJlc3VsdHNcbmZ1bmN0aW9uIGdldENvbW1vblNraWxsU3VnZ2VzdGlvbnMocXVlcnk6IHN0cmluZyk6IFNraWxsU2VhcmNoUmVzdWx0W10ge1xuICBjb25zdCBjb21tb25Ta2lsbHMgPSBbXG4gICAgeyBuYW1lOiAnSmF2YVNjcmlwdCcsIGNhdGVnb3J5OiAnUHJvZ3JhbW1pbmcgTGFuZ3VhZ2VzJyB9LFxuICAgIHsgbmFtZTogJ1B5dGhvbicsIGNhdGVnb3J5OiAnUHJvZ3JhbW1pbmcgTGFuZ3VhZ2VzJyB9LFxuICAgIHsgbmFtZTogJ1JlYWN0JywgY2F0ZWdvcnk6ICdGcm9udGVuZCBGcmFtZXdvcmtzJyB9LFxuICAgIHsgbmFtZTogJ05vZGUuanMnLCBjYXRlZ29yeTogJ0JhY2tlbmQgVGVjaG5vbG9naWVzJyB9LFxuICAgIHsgbmFtZTogJ1R5cGVTY3JpcHQnLCBjYXRlZ29yeTogJ1Byb2dyYW1taW5nIExhbmd1YWdlcycgfSxcbiAgICB7IG5hbWU6ICdTUUwnLCBjYXRlZ29yeTogJ0RhdGFiYXNlIFRlY2hub2xvZ2llcycgfSxcbiAgICB7IG5hbWU6ICdHaXQnLCBjYXRlZ29yeTogJ0RldmVsb3BtZW50IFRvb2xzJyB9LFxuICAgIHsgbmFtZTogJ0RvY2tlcicsIGNhdGVnb3J5OiAnRGV2T3BzIFRvb2xzJyB9LFxuICAgIHsgbmFtZTogJ0FXUycsIGNhdGVnb3J5OiAnQ2xvdWQgUGxhdGZvcm1zJyB9LFxuICAgIHsgbmFtZTogJ1Byb2plY3QgTWFuYWdlbWVudCcsIGNhdGVnb3J5OiAnU29mdCBTa2lsbHMnIH0sXG4gICAgeyBuYW1lOiAnQ29tbXVuaWNhdGlvbicsIGNhdGVnb3J5OiAnU29mdCBTa2lsbHMnIH0sXG4gICAgeyBuYW1lOiAnTGVhZGVyc2hpcCcsIGNhdGVnb3J5OiAnU29mdCBTa2lsbHMnIH0sXG4gICAgeyBuYW1lOiAnUHJvYmxlbSBTb2x2aW5nJywgY2F0ZWdvcnk6ICdTb2Z0IFNraWxscycgfSxcbiAgICB7IG5hbWU6ICdEYXRhIEFuYWx5c2lzJywgY2F0ZWdvcnk6ICdBbmFseXRpY3MnIH0sXG4gICAgeyBuYW1lOiAnTWFjaGluZSBMZWFybmluZycsIGNhdGVnb3J5OiAnQUkvTUwnIH0sXG4gICAgeyBuYW1lOiAnVUkvVVggRGVzaWduJywgY2F0ZWdvcnk6ICdEZXNpZ24nIH0sXG4gICAgeyBuYW1lOiAnQWdpbGUgTWV0aG9kb2xvZ3knLCBjYXRlZ29yeTogJ1Byb2plY3QgTWFuYWdlbWVudCcgfSxcbiAgICB7IG5hbWU6ICdSRVNUIEFQSXMnLCBjYXRlZ29yeTogJ0JhY2tlbmQgVGVjaG5vbG9naWVzJyB9LFxuICAgIHsgbmFtZTogJ01vbmdvREInLCBjYXRlZ29yeTogJ0RhdGFiYXNlIFRlY2hub2xvZ2llcycgfSxcbiAgICB7IG5hbWU6ICdDU1MnLCBjYXRlZ29yeTogJ0Zyb250ZW5kIFRlY2hub2xvZ2llcycgfSxcbiAgXTtcblxuICBjb25zdCBxdWVyeUxvd2VyID0gcXVlcnkudG9Mb3dlckNhc2UoKTtcbiAgcmV0dXJuIGNvbW1vblNraWxsc1xuICAgIC5maWx0ZXIoc2tpbGwgPT4gXG4gICAgICBza2lsbC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocXVlcnlMb3dlcikgfHxcbiAgICAgIHNraWxsLmNhdGVnb3J5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocXVlcnlMb3dlcilcbiAgICApXG4gICAgLnNsaWNlKDAsIDEwKVxuICAgIC5tYXAoKHNraWxsLCBpbmRleCkgPT4gKHtcbiAgICAgIGlkOiBgY29tbW9uLSR7aW5kZXh9YCxcbiAgICAgIG5hbWU6IHNraWxsLm5hbWUsXG4gICAgICBjYXRlZ29yeTogc2tpbGwuY2F0ZWdvcnksXG4gICAgICBkZXNjcmlwdGlvbjogYCR7c2tpbGwubmFtZX0gLSAke3NraWxsLmNhdGVnb3J5fWAsXG4gICAgfSkpO1xufVxuXG5cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLElBQUFBLFFBQUE7QUFBQTtBQUFBLENBQUFDLGFBQUEsR0FBQUMsQ0FBQSxRQUFBQyxPQUFBO0FBQ0EsSUFBQUMsMkJBQUE7QUFBQTtBQUFBLENBQUFILGFBQUEsR0FBQUMsQ0FBQSxRQUFBQyxPQUFBO0FBRUEsSUFBQUUsUUFBQTtBQUFBO0FBQUEsQ0FBQUosYUFBQSxHQUFBQyxDQUFBLFFBQUFDLE9BQUE7QUFDQSxJQUFBRyx1QkFBQTtBQUFBO0FBQUEsQ0FBQUwsYUFBQSxHQUFBQyxDQUFBLFFBQUFDLE9BQUE7QUFDQSxJQUFBSSxNQUFBO0FBQUE7QUFBQSxDQUFBTixhQUFBLEdBQUFDLENBQUEsUUFBQUMsT0FBQTtBQUNBLElBQUFLLE1BQUE7QUFBQTtBQUFBLENBQUFQLGFBQUEsR0FBQUMsQ0FBQSxRQUFBQyxPQUFBO0FBQXlDO0FBQUFGLGFBQUEsR0FBQUMsQ0FBQTtBQXFCNUJPLE9BQUEsQ0FBQUMsR0FBRyxHQUFHLElBQUFOLDJCQUFBLENBQUFPLHdCQUF3QixFQUFDLFVBQU9DLE9BQW9CO0VBQUE7RUFBQVgsYUFBQSxHQUFBWSxDQUFBO0VBQUFaLGFBQUEsR0FBQUMsQ0FBQTtFQUFBLE9BQUFZLFNBQUEsaUJBQUdDLE9BQU87SUFBQTtJQUFBZCxhQUFBLEdBQUFZLENBQUE7Ozs7Ozs7Ozs7Ozs7O1VBRS9ELHFCQUFNLElBQUFOLE1BQUEsQ0FBQVMsZ0JBQWdCLEVBQUNSLE1BQUEsQ0FBQVMsV0FBVyxDQUFDOzs7OztVQUE3Q0MsT0FBTyxHQUFHQyxFQUFBLENBQUFDLElBQUEsRUFBbUM7VUFBQTtVQUFBbkIsYUFBQSxHQUFBQyxDQUFBO1VBQzdDbUIsTUFBTTtVQUFHO1VBQUEsQ0FBQXBCLGFBQUEsR0FBQXFCLENBQUEsWUFBQUMsRUFBQTtVQUFBO1VBQUEsQ0FBQXRCLGFBQUEsR0FBQXFCLENBQUEsV0FBQUosT0FBTztVQUFBO1VBQUEsQ0FBQWpCLGFBQUEsR0FBQXFCLENBQUEsV0FBUEosT0FBTztVQUFBO1VBQUEsQ0FBQWpCLGFBQUEsR0FBQXFCLENBQUE7VUFBQTtVQUFBLENBQUFyQixhQUFBLEdBQUFxQixDQUFBLFdBQVBKLE9BQU8sQ0FBRU0sSUFBSTtVQUFBO1VBQUEsQ0FBQXZCLGFBQUEsR0FBQXFCLENBQUEsV0FBQUMsRUFBQTtVQUFBO1VBQUEsQ0FBQXRCLGFBQUEsR0FBQXFCLENBQUE7VUFBQTtVQUFBLENBQUFyQixhQUFBLEdBQUFxQixDQUFBLFdBQUFDLEVBQUEsQ0FBRUUsRUFBRTtVQUFDO1VBQUF4QixhQUFBLEdBQUFDLENBQUE7VUFFekJ3QixZQUFZLEdBQUssSUFBSUMsR0FBRyxDQUFDZixPQUFPLENBQUNnQixHQUFHLENBQUMsQ0FBQUYsWUFBekI7VUFBMEI7VUFBQXpCLGFBQUEsR0FBQUMsQ0FBQTtVQUN4QzJCLEtBQUs7VUFBRztVQUFBLENBQUE1QixhQUFBLEdBQUFxQixDQUFBLFlBQUFRLEVBQUEsR0FBQUosWUFBWSxDQUFDSyxHQUFHLENBQUMsR0FBRyxDQUFDO1VBQUE7VUFBQSxDQUFBOUIsYUFBQSxHQUFBcUIsQ0FBQSxXQUFBUSxFQUFBO1VBQUE7VUFBQSxDQUFBN0IsYUFBQSxHQUFBcUIsQ0FBQTtVQUFBO1VBQUEsQ0FBQXJCLGFBQUEsR0FBQXFCLENBQUEsV0FBQVEsRUFBQSxDQUFFRSxJQUFJLEVBQUU7VUFBQztVQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1VBQ3RDK0IsS0FBSyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsQ0FBQ0MsUUFBUTtVQUFDO1VBQUEsQ0FBQW5DLGFBQUEsR0FBQXFCLENBQUEsV0FBQUksWUFBWSxDQUFDSyxHQUFHLENBQUMsT0FBTyxDQUFDO1VBQUE7VUFBQSxDQUFBOUIsYUFBQSxHQUFBcUIsQ0FBQSxXQUFJLElBQUksRUFBQyxFQUFFLEVBQUUsQ0FBQztVQUFDO1VBQUFyQixhQUFBLEdBQUFDLENBQUE7VUFDbEVtQyxRQUFRLEdBQUdYLFlBQVksQ0FBQ0ssR0FBRyxDQUFDLFVBQVUsQ0FBQztVQUFDO1VBQUE5QixhQUFBLEdBQUFDLENBQUE7VUFFOUM7VUFBSTtVQUFBLENBQUFELGFBQUEsR0FBQXFCLENBQUEsWUFBQ08sS0FBSztVQUFBO1VBQUEsQ0FBQTVCLGFBQUEsR0FBQXFCLENBQUEsV0FBSU8sS0FBSyxDQUFDUyxNQUFNLEdBQUcsQ0FBQyxHQUFFO1lBQUE7WUFBQXJDLGFBQUEsR0FBQXFCLENBQUE7WUFBQXJCLGFBQUEsR0FBQUMsQ0FBQTtZQUM5QixzQkFBT0YsUUFBQSxDQUFBdUMsWUFBWSxDQUFDQyxJQUFJLENBQUM7Y0FDdkJDLE9BQU8sRUFBRSxJQUFhO2NBQ3RCQyxJQUFJLEVBQUU7Z0JBQ0pELE9BQU8sRUFBRSxJQUFJO2dCQUNiRSxNQUFNLEVBQUUsRUFBRTtnQkFDVkMsS0FBSyxFQUFFLENBQUM7Z0JBQ1JmLEtBQUs7Z0JBQUU7Z0JBQUEsQ0FBQTVCLGFBQUEsR0FBQXFCLENBQUEsV0FBQU8sS0FBSztnQkFBQTtnQkFBQSxDQUFBNUIsYUFBQSxHQUFBcUIsQ0FBQSxXQUFJLEVBQUU7O2FBRXJCLENBQUM7VUFDSixDQUFDO1VBQUE7VUFBQTtZQUFBckIsYUFBQSxHQUFBcUIsQ0FBQTtVQUFBO1VBQUFyQixhQUFBLEdBQUFDLENBQUE7VUFHSzJDLGdCQUFnQixHQUFRO1lBQzVCQyxFQUFFLEVBQUUsQ0FDRjtjQUNFQyxJQUFJLEVBQUU7Z0JBQ0pDLFFBQVEsRUFBRW5CLEtBQUs7Z0JBQ2ZvQixJQUFJLEVBQUU7O2FBRVQsRUFDRDtjQUNFQyxXQUFXLEVBQUU7Z0JBQ1hGLFFBQVEsRUFBRW5CLEtBQUs7Z0JBQ2ZvQixJQUFJLEVBQUU7O2FBRVQsRUFDRDtjQUNFWixRQUFRLEVBQUU7Z0JBQ1JXLFFBQVEsRUFBRW5CLEtBQUs7Z0JBQ2ZvQixJQUFJLEVBQUU7O2FBRVQ7V0FFSjtVQUVEO1VBQUE7VUFBQWhELGFBQUEsR0FBQUMsQ0FBQTtVQUNBLElBQUltQyxRQUFRLEVBQUU7WUFBQTtZQUFBcEMsYUFBQSxHQUFBcUIsQ0FBQTtZQUFBckIsYUFBQSxHQUFBQyxDQUFBO1lBQ1oyQyxnQkFBZ0IsQ0FBQ00sR0FBRyxHQUFHLENBQ3JCO2NBQ0VkLFFBQVEsRUFBRTtnQkFDUmUsTUFBTSxFQUFFZixRQUFRO2dCQUNoQlksSUFBSSxFQUFFOzthQUVULENBQ0Y7VUFDSCxDQUFDO1VBQUE7VUFBQTtZQUFBaEQsYUFBQSxHQUFBcUIsQ0FBQTtVQUFBO1VBQUFyQixhQUFBLEdBQUFDLENBQUE7VUFHYyxxQkFBTUksdUJBQUEsQ0FBQStDLDBCQUEwQixDQUFDQyxrQkFBa0IsQ0FDaEV6QixLQUFLLEVBQ0w7WUFBQTtZQUFBNUIsYUFBQSxHQUFBWSxDQUFBO1lBQUFaLGFBQUEsR0FBQUMsQ0FBQTtZQUFBLE9BQUFZLFNBQUE7Y0FBQTtjQUFBYixhQUFBLEdBQUFZLENBQUE7Ozs7Ozs7Ozs7Ozs7b0JBRWlCLHFCQUFNUixRQUFBLENBQUFrRCxNQUFNLENBQUNDLEtBQUssQ0FBQ0MsUUFBUSxDQUFDO3NCQUN6Q0MsS0FBSyxFQUFFYixnQkFBZ0I7c0JBQ3ZCYyxPQUFPLEVBQUU7d0JBQ1BDLFVBQVUsRUFBRTswQkFDVkYsS0FBSyxFQUFFOzRCQUFFRyxRQUFRLEVBQUU7MEJBQUksQ0FBRTswQkFDekJDLE9BQU8sRUFBRTs0QkFBRUMsUUFBUSxFQUFFOzBCQUFNLENBQUU7MEJBQzdCQyxJQUFJLEVBQUU7O3VCQUVUO3NCQUNEQSxJQUFJLEVBQUUvQixLQUFLO3NCQUNYNkIsT0FBTyxFQUFFLENBQ1A7d0JBQ0VmLElBQUksRUFBRTt1QkFDUDtxQkFFSixDQUFDOzs7OztvQkFmSUosTUFBTSxHQUFHcEIsRUFBQSxDQUFBSCxJQUFBLEVBZWI7b0JBQUE7b0JBQUFuQixhQUFBLEdBQUFDLENBQUE7b0JBR0krRCxlQUFlLEdBQXdCdEIsTUFBTSxDQUFDdUIsR0FBRyxDQUFDLFVBQUFWLEtBQUs7c0JBQUE7c0JBQUF2RCxhQUFBLEdBQUFZLENBQUE7Ozs7c0JBQUksT0FBQzt3QkFDaEVZLEVBQUUsRUFBRStCLEtBQUssQ0FBQy9CLEVBQUU7d0JBQ1pzQixJQUFJLEVBQUVTLEtBQUssQ0FBQ1QsSUFBSTt3QkFDaEJWLFFBQVE7d0JBQUU7d0JBQUEsQ0FBQXBDLGFBQUEsR0FBQXFCLENBQUEsV0FBQWtDLEtBQUssQ0FBQ25CLFFBQVE7d0JBQUE7d0JBQUEsQ0FBQXBDLGFBQUEsR0FBQXFCLENBQUEsV0FBSSxTQUFTO3dCQUNyQzRCLFdBQVc7d0JBQUU7d0JBQUEsQ0FBQWpELGFBQUEsR0FBQXFCLENBQUEsV0FBQWtDLEtBQUssQ0FBQ04sV0FBVzt3QkFBQTt3QkFBQSxDQUFBakQsYUFBQSxHQUFBcUIsQ0FBQSxXQUFJNkMsU0FBUzt3QkFDM0NQLFVBQVUsRUFBRUosS0FBSyxDQUFDSSxVQUFVLENBQUMsQ0FBQyxDQUFDO3dCQUFBO3dCQUFBLENBQUEzRCxhQUFBLEdBQUFxQixDQUFBLFdBQUc7MEJBQ2hDOEMsYUFBYTswQkFBRTswQkFBQSxDQUFBbkUsYUFBQSxHQUFBcUIsQ0FBQSxXQUFBa0MsS0FBSyxDQUFDSSxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUNTLG1CQUFtQjswQkFBQTswQkFBQSxDQUFBcEUsYUFBQSxHQUFBcUIsQ0FBQSxXQUFJNkMsU0FBUzswQkFDbkVHLFdBQVc7MEJBQUU7MEJBQUEsQ0FBQXJFLGFBQUEsR0FBQXFCLENBQUE7MEJBQUE7MEJBQUEsQ0FBQXJCLGFBQUEsR0FBQXFCLENBQUEsWUFBQUMsRUFBQSxHQUFBaUMsS0FBSyxDQUFDSSxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUNVLFdBQVc7MEJBQUE7MEJBQUEsQ0FBQXJFLGFBQUEsR0FBQXFCLENBQUEsV0FBQUMsRUFBQTswQkFBQTswQkFBQSxDQUFBdEIsYUFBQSxHQUFBcUIsQ0FBQTswQkFBQTswQkFBQSxDQUFBckIsYUFBQSxHQUFBcUIsQ0FBQSxXQUFBQyxFQUFBLENBQUVnRCxRQUFRLEVBQUU7MEJBQUE7MEJBQUEsQ0FBQXRFLGFBQUEsR0FBQXFCLENBQUEsV0FBSTZDLFNBQVM7MEJBQ3JFSyxVQUFVLEVBQUVMLFNBQVMsQ0FBRTt5QkFDeEI7d0JBQUE7d0JBQUEsQ0FBQWxFLGFBQUEsR0FBQXFCLENBQUEsV0FBRzZDLFNBQVM7dUJBQ2Q7cUJBQUMsQ0FBQztvQkFFSDtvQkFBQTtvQkFBQWxFLGFBQUEsR0FBQUMsQ0FBQTtvQkFDQSxJQUFJK0QsZUFBZSxDQUFDM0IsTUFBTSxLQUFLLENBQUMsRUFBRTtzQkFBQTtzQkFBQXJDLGFBQUEsR0FBQXFCLENBQUE7c0JBQUFyQixhQUFBLEdBQUFDLENBQUE7c0JBQ2hDLHNCQUFPdUUseUJBQXlCLENBQUM1QyxLQUFLLENBQUM7b0JBQ3pDLENBQUM7b0JBQUE7b0JBQUE7c0JBQUE1QixhQUFBLEdBQUFxQixDQUFBO29CQUFBO29CQUFBckIsYUFBQSxHQUFBQyxDQUFBO29CQUVELHNCQUFPK0QsZUFBZTs7OztXQUN2QjtVQUNEO1VBQUEsQ0FBQWhFLGFBQUEsR0FBQXFCLENBQUEsV0FBQUQsTUFBTTtVQUFBO1VBQUEsQ0FBQXBCLGFBQUEsR0FBQXFCLENBQUEsV0FBSTZDLFNBQVMsRUFDcEI7Ozs7O1VBMUNLTyxNQUFNLEdBQUd2RCxFQUFBLENBQUFDLElBQUEsRUEwQ2Q7VUFBQTtVQUFBbkIsYUFBQSxHQUFBQyxDQUFBO1VBRUQsc0JBQU9GLFFBQUEsQ0FBQXVDLFlBQVksQ0FBQ0MsSUFBSSxDQUFDO1lBQ3ZCQyxPQUFPLEVBQUUsSUFBYTtZQUN0QkMsSUFBSSxFQUFFO2NBQ0pELE9BQU8sRUFBRSxJQUFJO2NBQ2JFLE1BQU0sRUFBRStCLE1BQU07Y0FDZDlCLEtBQUssRUFBRThCLE1BQU0sQ0FBQ3BDLE1BQU07Y0FDcEJULEtBQUssRUFBQUE7O1dBRVIsQ0FBQzs7OztDQUNILENBQUM7QUFFRjtBQUNBLFNBQVM0Qyx5QkFBeUJBLENBQUM1QyxLQUFhO0VBQUE7RUFBQTVCLGFBQUEsR0FBQVksQ0FBQTtFQUM5QyxJQUFNOEQsWUFBWTtFQUFBO0VBQUEsQ0FBQTFFLGFBQUEsR0FBQUMsQ0FBQSxTQUFHLENBQ25CO0lBQUU2QyxJQUFJLEVBQUUsWUFBWTtJQUFFVixRQUFRLEVBQUU7RUFBdUIsQ0FBRSxFQUN6RDtJQUFFVSxJQUFJLEVBQUUsUUFBUTtJQUFFVixRQUFRLEVBQUU7RUFBdUIsQ0FBRSxFQUNyRDtJQUFFVSxJQUFJLEVBQUUsT0FBTztJQUFFVixRQUFRLEVBQUU7RUFBcUIsQ0FBRSxFQUNsRDtJQUFFVSxJQUFJLEVBQUUsU0FBUztJQUFFVixRQUFRLEVBQUU7RUFBc0IsQ0FBRSxFQUNyRDtJQUFFVSxJQUFJLEVBQUUsWUFBWTtJQUFFVixRQUFRLEVBQUU7RUFBdUIsQ0FBRSxFQUN6RDtJQUFFVSxJQUFJLEVBQUUsS0FBSztJQUFFVixRQUFRLEVBQUU7RUFBdUIsQ0FBRSxFQUNsRDtJQUFFVSxJQUFJLEVBQUUsS0FBSztJQUFFVixRQUFRLEVBQUU7RUFBbUIsQ0FBRSxFQUM5QztJQUFFVSxJQUFJLEVBQUUsUUFBUTtJQUFFVixRQUFRLEVBQUU7RUFBYyxDQUFFLEVBQzVDO0lBQUVVLElBQUksRUFBRSxLQUFLO0lBQUVWLFFBQVEsRUFBRTtFQUFpQixDQUFFLEVBQzVDO0lBQUVVLElBQUksRUFBRSxvQkFBb0I7SUFBRVYsUUFBUSxFQUFFO0VBQWEsQ0FBRSxFQUN2RDtJQUFFVSxJQUFJLEVBQUUsZUFBZTtJQUFFVixRQUFRLEVBQUU7RUFBYSxDQUFFLEVBQ2xEO0lBQUVVLElBQUksRUFBRSxZQUFZO0lBQUVWLFFBQVEsRUFBRTtFQUFhLENBQUUsRUFDL0M7SUFBRVUsSUFBSSxFQUFFLGlCQUFpQjtJQUFFVixRQUFRLEVBQUU7RUFBYSxDQUFFLEVBQ3BEO0lBQUVVLElBQUksRUFBRSxlQUFlO0lBQUVWLFFBQVEsRUFBRTtFQUFXLENBQUUsRUFDaEQ7SUFBRVUsSUFBSSxFQUFFLGtCQUFrQjtJQUFFVixRQUFRLEVBQUU7RUFBTyxDQUFFLEVBQy9DO0lBQUVVLElBQUksRUFBRSxjQUFjO0lBQUVWLFFBQVEsRUFBRTtFQUFRLENBQUUsRUFDNUM7SUFBRVUsSUFBSSxFQUFFLG1CQUFtQjtJQUFFVixRQUFRLEVBQUU7RUFBb0IsQ0FBRSxFQUM3RDtJQUFFVSxJQUFJLEVBQUUsV0FBVztJQUFFVixRQUFRLEVBQUU7RUFBc0IsQ0FBRSxFQUN2RDtJQUFFVSxJQUFJLEVBQUUsU0FBUztJQUFFVixRQUFRLEVBQUU7RUFBdUIsQ0FBRSxFQUN0RDtJQUFFVSxJQUFJLEVBQUUsS0FBSztJQUFFVixRQUFRLEVBQUU7RUFBdUIsQ0FBRSxDQUNuRDtFQUVELElBQU11QyxVQUFVO0VBQUE7RUFBQSxDQUFBM0UsYUFBQSxHQUFBQyxDQUFBLFNBQUcyQixLQUFLLENBQUNnRCxXQUFXLEVBQUU7RUFBQztFQUFBNUUsYUFBQSxHQUFBQyxDQUFBO0VBQ3ZDLE9BQU95RSxZQUFZLENBQ2hCRyxNQUFNLENBQUMsVUFBQXRCLEtBQUs7SUFBQTtJQUFBdkQsYUFBQSxHQUFBWSxDQUFBO0lBQUFaLGFBQUEsR0FBQUMsQ0FBQTtJQUNYLGtDQUFBRCxhQUFBLEdBQUFxQixDQUFBLFdBQUFrQyxLQUFLLENBQUNULElBQUksQ0FBQzhCLFdBQVcsRUFBRSxDQUFDRSxRQUFRLENBQUNILFVBQVUsQ0FBQztJQUFBO0lBQUEsQ0FBQTNFLGFBQUEsR0FBQXFCLENBQUEsV0FDN0NrQyxLQUFLLENBQUNuQixRQUFRLENBQUN3QyxXQUFXLEVBQUUsQ0FBQ0UsUUFBUSxDQUFDSCxVQUFVLENBQUM7RUFEakQsQ0FDaUQsQ0FDbEQsQ0FDQUksS0FBSyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FDWmQsR0FBRyxDQUFDLFVBQUNWLEtBQUssRUFBRXlCLEtBQUs7SUFBQTtJQUFBaEYsYUFBQSxHQUFBWSxDQUFBO0lBQUFaLGFBQUEsR0FBQUMsQ0FBQTtJQUFLLE9BQUM7TUFDdEJ1QixFQUFFLEVBQUUsVUFBQXlELE1BQUEsQ0FBVUQsS0FBSyxDQUFFO01BQ3JCbEMsSUFBSSxFQUFFUyxLQUFLLENBQUNULElBQUk7TUFDaEJWLFFBQVEsRUFBRW1CLEtBQUssQ0FBQ25CLFFBQVE7TUFDeEJhLFdBQVcsRUFBRSxHQUFBZ0MsTUFBQSxDQUFHMUIsS0FBSyxDQUFDVCxJQUFJLFNBQUFtQyxNQUFBLENBQU0xQixLQUFLLENBQUNuQixRQUFRO0tBQy9DO0VBTHNCLENBS3JCLENBQUM7QUFDUCIsImlnbm9yZUxpc3QiOltdfQ==