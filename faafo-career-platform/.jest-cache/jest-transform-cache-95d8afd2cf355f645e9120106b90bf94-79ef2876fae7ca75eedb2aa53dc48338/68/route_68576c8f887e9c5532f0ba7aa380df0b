697ec9d3fc138200ed55bfbf4921a6c9
"use strict";

/* istanbul ignore next */
function cov_4uueiioih() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/enroll/route.ts";
  var hash = "733ef90492ce75e03ec3f946d1b6571bca9bf298";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/enroll/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 53
        }
      },
      "80": {
        start: {
          line: 51,
          column: 15
        },
        end: {
          line: 51,
          column: 37
        }
      },
      "81": {
        start: {
          line: 52,
          column: 18
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "82": {
        start: {
          line: 53,
          column: 13
        },
        end: {
          line: 53,
          column: 34
        }
      },
      "83": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 38
        }
      },
      "84": {
        start: {
          line: 55,
          column: 34
        },
        end: {
          line: 55,
          column: 76
        }
      },
      "85": {
        start: {
          line: 56,
          column: 18
        },
        end: {
          line: 56,
          column: 44
        }
      },
      "86": {
        start: {
          line: 57,
          column: 35
        },
        end: {
          line: 57,
          column: 87
        }
      },
      "87": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 188,
          column: 7
        }
      },
      "88": {
        start: {
          line: 59,
          column: 100
        },
        end: {
          line: 188,
          column: 3
        }
      },
      "89": {
        start: {
          line: 60,
          column: 17
        },
        end: {
          line: 60,
          column: 26
        }
      },
      "90": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 187,
          column: 7
        }
      },
      "91": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 186,
          column: 20
        }
      },
      "92": {
        start: {
          line: 63,
          column: 26
        },
        end: {
          line: 186,
          column: 15
        }
      },
      "93": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 185,
          column: 19
        }
      },
      "94": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 184,
          column: 21
        }
      },
      "95": {
        start: {
          line: 68,
          column: 32
        },
        end: {
          line: 68,
          column: 108
        }
      },
      "96": {
        start: {
          line: 70,
          column: 28
        },
        end: {
          line: 70,
          column: 48
        }
      },
      "97": {
        start: {
          line: 71,
          column: 28
        },
        end: {
          line: 73,
          column: 29
        }
      },
      "98": {
        start: {
          line: 72,
          column: 32
        },
        end: {
          line: 72,
          column: 153
        }
      },
      "99": {
        start: {
          line: 74,
          column: 28
        },
        end: {
          line: 74,
          column: 53
        }
      },
      "100": {
        start: {
          line: 75,
          column: 28
        },
        end: {
          line: 75,
          column: 57
        }
      },
      "101": {
        start: {
          line: 77,
          column: 28
        },
        end: {
          line: 77,
          column: 60
        }
      },
      "102": {
        start: {
          line: 78,
          column: 28
        },
        end: {
          line: 90,
          column: 36
        }
      },
      "103": {
        start: {
          line: 92,
          column: 28
        },
        end: {
          line: 92,
          column: 53
        }
      },
      "104": {
        start: {
          line: 93,
          column: 28
        },
        end: {
          line: 95,
          column: 29
        }
      },
      "105": {
        start: {
          line: 94,
          column: 32
        },
        end: {
          line: 94,
          column: 153
        }
      },
      "106": {
        start: {
          line: 96,
          column: 28
        },
        end: {
          line: 98,
          column: 29
        }
      },
      "107": {
        start: {
          line: 97,
          column: 32
        },
        end: {
          line: 97,
          column: 175
        }
      },
      "108": {
        start: {
          line: 99,
          column: 28
        },
        end: {
          line: 106,
          column: 36
        }
      },
      "109": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 108,
          column: 59
        }
      },
      "110": {
        start: {
          line: 109,
          column: 28
        },
        end: {
          line: 111,
          column: 29
        }
      },
      "111": {
        start: {
          line: 110,
          column: 32
        },
        end: {
          line: 110,
          column: 168
        }
      },
      "112": {
        start: {
          line: 112,
          column: 28
        },
        end: {
          line: 132,
          column: 36
        }
      },
      "113": {
        start: {
          line: 134,
          column: 28
        },
        end: {
          line: 134,
          column: 51
        }
      },
      "114": {
        start: {
          line: 135,
          column: 28
        },
        end: {
          line: 135,
          column: 90
        }
      },
      "115": {
        start: {
          line: 135,
          column: 66
        },
        end: {
          line: 135,
          column: 90
        }
      },
      "116": {
        start: {
          line: 136,
          column: 28
        },
        end: {
          line: 141,
          column: 35
        }
      },
      "117": {
        start: {
          line: 136,
          column: 88
        },
        end: {
          line: 141,
          column: 31
        }
      },
      "118": {
        start: {
          line: 142,
          column: 28
        },
        end: {
          line: 144,
          column: 36
        }
      },
      "119": {
        start: {
          line: 146,
          column: 28
        },
        end: {
          line: 146,
          column: 38
        }
      },
      "120": {
        start: {
          line: 147,
          column: 28
        },
        end: {
          line: 147,
          column: 41
        }
      },
      "121": {
        start: {
          line: 149,
          column: 28
        },
        end: {
          line: 149,
          column: 47
        }
      },
      "122": {
        start: {
          line: 150,
          column: 28
        },
        end: {
          line: 150,
          column: 55
        }
      },
      "123": {
        start: {
          line: 151,
          column: 28
        },
        end: {
          line: 168,
          column: 36
        }
      },
      "124": {
        start: {
          line: 170,
          column: 28
        },
        end: {
          line: 170,
          column: 38
        }
      },
      "125": {
        start: {
          line: 172,
          column: 28
        },
        end: {
          line: 172,
          column: 165
        }
      },
      "126": {
        start: {
          line: 175,
          column: 28
        },
        end: {
          line: 175,
          column: 38
        }
      },
      "127": {
        start: {
          line: 176,
          column: 28
        },
        end: {
          line: 176,
          column: 183
        }
      },
      "128": {
        start: {
          line: 178,
          column: 28
        },
        end: {
          line: 178,
          column: 38
        }
      },
      "129": {
        start: {
          line: 179,
          column: 28
        },
        end: {
          line: 183,
          column: 53
        }
      },
      "130": {
        start: {
          line: 190,
          column: 0
        },
        end: {
          line: 254,
          column: 7
        }
      },
      "131": {
        start: {
          line: 190,
          column: 102
        },
        end: {
          line: 254,
          column: 3
        }
      },
      "132": {
        start: {
          line: 191,
          column: 17
        },
        end: {
          line: 191,
          column: 26
        }
      },
      "133": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 253,
          column: 7
        }
      },
      "134": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 252,
          column: 20
        }
      },
      "135": {
        start: {
          line: 194,
          column: 26
        },
        end: {
          line: 252,
          column: 15
        }
      },
      "136": {
        start: {
          line: 197,
          column: 16
        },
        end: {
          line: 251,
          column: 19
        }
      },
      "137": {
        start: {
          line: 198,
          column: 20
        },
        end: {
          line: 250,
          column: 21
        }
      },
      "138": {
        start: {
          line: 199,
          column: 32
        },
        end: {
          line: 199,
          column: 108
        }
      },
      "139": {
        start: {
          line: 201,
          column: 28
        },
        end: {
          line: 201,
          column: 48
        }
      },
      "140": {
        start: {
          line: 202,
          column: 28
        },
        end: {
          line: 204,
          column: 29
        }
      },
      "141": {
        start: {
          line: 203,
          column: 32
        },
        end: {
          line: 203,
          column: 153
        }
      },
      "142": {
        start: {
          line: 205,
          column: 28
        },
        end: {
          line: 205,
          column: 53
        }
      },
      "143": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 206,
          column: 57
        }
      },
      "144": {
        start: {
          line: 208,
          column: 28
        },
        end: {
          line: 208,
          column: 60
        }
      },
      "145": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 216,
          column: 36
        }
      },
      "146": {
        start: {
          line: 218,
          column: 28
        },
        end: {
          line: 218,
          column: 51
        }
      },
      "147": {
        start: {
          line: 219,
          column: 28
        },
        end: {
          line: 221,
          column: 29
        }
      },
      "148": {
        start: {
          line: 220,
          column: 32
        },
        end: {
          line: 220,
          column: 164
        }
      },
      "149": {
        start: {
          line: 223,
          column: 28
        },
        end: {
          line: 225,
          column: 29
        }
      },
      "150": {
        start: {
          line: 224,
          column: 32
        },
        end: {
          line: 224,
          column: 174
        }
      },
      "151": {
        start: {
          line: 227,
          column: 28
        },
        end: {
          line: 234,
          column: 36
        }
      },
      "152": {
        start: {
          line: 237,
          column: 28
        },
        end: {
          line: 237,
          column: 38
        }
      },
      "153": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 239,
          column: 165
        }
      },
      "154": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 242,
          column: 38
        }
      },
      "155": {
        start: {
          line: 243,
          column: 28
        },
        end: {
          line: 243,
          column: 183
        }
      },
      "156": {
        start: {
          line: 245,
          column: 28
        },
        end: {
          line: 245,
          column: 38
        }
      },
      "157": {
        start: {
          line: 246,
          column: 28
        },
        end: {
          line: 249,
          column: 36
        }
      },
      "158": {
        start: {
          line: 256,
          column: 0
        },
        end: {
          line: 364,
          column: 7
        }
      },
      "159": {
        start: {
          line: 256,
          column: 99
        },
        end: {
          line: 364,
          column: 3
        }
      },
      "160": {
        start: {
          line: 257,
          column: 17
        },
        end: {
          line: 257,
          column: 26
        }
      },
      "161": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 363,
          column: 7
        }
      },
      "162": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 362,
          column: 20
        }
      },
      "163": {
        start: {
          line: 260,
          column: 26
        },
        end: {
          line: 362,
          column: 15
        }
      },
      "164": {
        start: {
          line: 263,
          column: 16
        },
        end: {
          line: 361,
          column: 19
        }
      },
      "165": {
        start: {
          line: 264,
          column: 20
        },
        end: {
          line: 360,
          column: 21
        }
      },
      "166": {
        start: {
          line: 265,
          column: 32
        },
        end: {
          line: 265,
          column: 108
        }
      },
      "167": {
        start: {
          line: 267,
          column: 28
        },
        end: {
          line: 267,
          column: 48
        }
      },
      "168": {
        start: {
          line: 268,
          column: 28
        },
        end: {
          line: 270,
          column: 29
        }
      },
      "169": {
        start: {
          line: 269,
          column: 32
        },
        end: {
          line: 269,
          column: 153
        }
      },
      "170": {
        start: {
          line: 271,
          column: 28
        },
        end: {
          line: 271,
          column: 53
        }
      },
      "171": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 272,
          column: 57
        }
      },
      "172": {
        start: {
          line: 274,
          column: 28
        },
        end: {
          line: 274,
          column: 60
        }
      },
      "173": {
        start: {
          line: 275,
          column: 28
        },
        end: {
          line: 275,
          column: 96
        }
      },
      "174": {
        start: {
          line: 276,
          column: 28
        },
        end: {
          line: 276,
          column: 111
        }
      },
      "175": {
        start: {
          line: 278,
          column: 28
        },
        end: {
          line: 278,
          column: 47
        }
      },
      "176": {
        start: {
          line: 279,
          column: 28
        },
        end: {
          line: 285,
          column: 29
        }
      },
      "177": {
        start: {
          line: 280,
          column: 32
        },
        end: {
          line: 284,
          column: 40
        }
      },
      "178": {
        start: {
          line: 286,
          column: 28
        },
        end: {
          line: 324,
          column: 36
        }
      },
      "179": {
        start: {
          line: 326,
          column: 28
        },
        end: {
          line: 326,
          column: 51
        }
      },
      "180": {
        start: {
          line: 327,
          column: 28
        },
        end: {
          line: 335,
          column: 29
        }
      },
      "181": {
        start: {
          line: 328,
          column: 32
        },
        end: {
          line: 334,
          column: 40
        }
      },
      "182": {
        start: {
          line: 336,
          column: 28
        },
        end: {
          line: 336,
          column: 134
        }
      },
      "183": {
        start: {
          line: 336,
          column: 91
        },
        end: {
          line: 336,
          column: 123
        }
      },
      "184": {
        start: {
          line: 337,
          column: 28
        },
        end: {
          line: 337,
          column: 137
        }
      },
      "185": {
        start: {
          line: 337,
          column: 92
        },
        end: {
          line: 337,
          column: 126
        }
      },
      "186": {
        start: {
          line: 338,
          column: 28
        },
        end: {
          line: 338,
          column: 128
        }
      },
      "187": {
        start: {
          line: 338,
          column: 96
        },
        end: {
          line: 338,
          column: 121
        }
      },
      "188": {
        start: {
          line: 339,
          column: 28
        },
        end: {
          line: 350,
          column: 30
        }
      },
      "189": {
        start: {
          line: 348,
          column: 100
        },
        end: {
          line: 348,
          column: 134
        }
      },
      "190": {
        start: {
          line: 349,
          column: 91
        },
        end: {
          line: 349,
          column: 136
        }
      },
      "191": {
        start: {
          line: 352,
          column: 28
        },
        end: {
          line: 352,
          column: 195
        }
      },
      "192": {
        start: {
          line: 355,
          column: 28
        },
        end: {
          line: 355,
          column: 38
        }
      },
      "193": {
        start: {
          line: 356,
          column: 28
        },
        end: {
          line: 359,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 59,
            column: 73
          },
          end: {
            line: 59,
            column: 74
          }
        },
        loc: {
          start: {
            line: 59,
            column: 98
          },
          end: {
            line: 188,
            column: 5
          }
        },
        line: 59
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 59,
            column: 150
          },
          end: {
            line: 59,
            column: 151
          }
        },
        loc: {
          start: {
            line: 59,
            column: 173
          },
          end: {
            line: 188,
            column: 1
          }
        },
        line: 59
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 61,
            column: 29
          },
          end: {
            line: 61,
            column: 30
          }
        },
        loc: {
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 187,
            column: 5
          }
        },
        line: 61
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 13
          }
        },
        loc: {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 186,
            column: 17
          }
        },
        line: 63
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 68
          }
        },
        loc: {
          start: {
            line: 63,
            column: 79
          },
          end: {
            line: 186,
            column: 13
          }
        },
        line: 63
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 66,
            column: 41
          },
          end: {
            line: 66,
            column: 42
          }
        },
        loc: {
          start: {
            line: 66,
            column: 55
          },
          end: {
            line: 185,
            column: 17
          }
        },
        line: 66
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 136,
            column: 70
          },
          end: {
            line: 136,
            column: 71
          }
        },
        loc: {
          start: {
            line: 136,
            column: 86
          },
          end: {
            line: 141,
            column: 33
          }
        },
        line: 136
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 190,
            column: 75
          },
          end: {
            line: 190,
            column: 76
          }
        },
        loc: {
          start: {
            line: 190,
            column: 100
          },
          end: {
            line: 254,
            column: 5
          }
        },
        line: 190
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 190,
            column: 152
          },
          end: {
            line: 190,
            column: 153
          }
        },
        loc: {
          start: {
            line: 190,
            column: 175
          },
          end: {
            line: 254,
            column: 1
          }
        },
        line: 190
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 192,
            column: 29
          },
          end: {
            line: 192,
            column: 30
          }
        },
        loc: {
          start: {
            line: 192,
            column: 43
          },
          end: {
            line: 253,
            column: 5
          }
        },
        line: 192
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 194,
            column: 12
          },
          end: {
            line: 194,
            column: 13
          }
        },
        loc: {
          start: {
            line: 194,
            column: 24
          },
          end: {
            line: 252,
            column: 17
          }
        },
        line: 194
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 194,
            column: 67
          },
          end: {
            line: 194,
            column: 68
          }
        },
        loc: {
          start: {
            line: 194,
            column: 79
          },
          end: {
            line: 252,
            column: 13
          }
        },
        line: 194
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 197,
            column: 41
          },
          end: {
            line: 197,
            column: 42
          }
        },
        loc: {
          start: {
            line: 197,
            column: 55
          },
          end: {
            line: 251,
            column: 17
          }
        },
        line: 197
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 256,
            column: 72
          },
          end: {
            line: 256,
            column: 73
          }
        },
        loc: {
          start: {
            line: 256,
            column: 97
          },
          end: {
            line: 364,
            column: 5
          }
        },
        line: 256
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 256,
            column: 149
          },
          end: {
            line: 256,
            column: 150
          }
        },
        loc: {
          start: {
            line: 256,
            column: 172
          },
          end: {
            line: 364,
            column: 1
          }
        },
        line: 256
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 258,
            column: 29
          },
          end: {
            line: 258,
            column: 30
          }
        },
        loc: {
          start: {
            line: 258,
            column: 43
          },
          end: {
            line: 363,
            column: 5
          }
        },
        line: 258
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 260,
            column: 13
          }
        },
        loc: {
          start: {
            line: 260,
            column: 24
          },
          end: {
            line: 362,
            column: 17
          }
        },
        line: 260
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 260,
            column: 67
          },
          end: {
            line: 260,
            column: 68
          }
        },
        loc: {
          start: {
            line: 260,
            column: 79
          },
          end: {
            line: 362,
            column: 13
          }
        },
        line: 260
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 263,
            column: 41
          },
          end: {
            line: 263,
            column: 42
          }
        },
        loc: {
          start: {
            line: 263,
            column: 55
          },
          end: {
            line: 361,
            column: 17
          }
        },
        line: 263
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 336,
            column: 76
          },
          end: {
            line: 336,
            column: 77
          }
        },
        loc: {
          start: {
            line: 336,
            column: 89
          },
          end: {
            line: 336,
            column: 125
          }
        },
        line: 336
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 337,
            column: 77
          },
          end: {
            line: 337,
            column: 78
          }
        },
        loc: {
          start: {
            line: 337,
            column: 90
          },
          end: {
            line: 337,
            column: 128
          }
        },
        line: 337
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 338,
            column: 76
          },
          end: {
            line: 338,
            column: 77
          }
        },
        loc: {
          start: {
            line: 338,
            column: 94
          },
          end: {
            line: 338,
            column: 123
          }
        },
        line: 338
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 348,
            column: 85
          },
          end: {
            line: 348,
            column: 86
          }
        },
        loc: {
          start: {
            line: 348,
            column: 98
          },
          end: {
            line: 348,
            column: 136
          }
        },
        line: 348
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 349,
            column: 76
          },
          end: {
            line: 349,
            column: 77
          }
        },
        loc: {
          start: {
            line: 349,
            column: 89
          },
          end: {
            line: 349,
            column: 138
          }
        },
        line: 349
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 184,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 68,
            column: 24
          },
          end: {
            line: 68,
            column: 108
          }
        }, {
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 75,
            column: 57
          }
        }, {
          start: {
            line: 76,
            column: 24
          },
          end: {
            line: 90,
            column: 36
          }
        }, {
          start: {
            line: 91,
            column: 24
          },
          end: {
            line: 106,
            column: 36
          }
        }, {
          start: {
            line: 107,
            column: 24
          },
          end: {
            line: 132,
            column: 36
          }
        }, {
          start: {
            line: 133,
            column: 24
          },
          end: {
            line: 144,
            column: 36
          }
        }, {
          start: {
            line: 145,
            column: 24
          },
          end: {
            line: 147,
            column: 41
          }
        }, {
          start: {
            line: 148,
            column: 24
          },
          end: {
            line: 168,
            column: 36
          }
        }, {
          start: {
            line: 169,
            column: 24
          },
          end: {
            line: 172,
            column: 165
          }
        }, {
          start: {
            line: 173,
            column: 24
          },
          end: {
            line: 176,
            column: 183
          }
        }, {
          start: {
            line: 177,
            column: 24
          },
          end: {
            line: 183,
            column: 53
          }
        }],
        line: 67
      },
      "36": {
        loc: {
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 73,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 73,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "37": {
        loc: {
          start: {
            line: 71,
            column: 34
          },
          end: {
            line: 71,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 132
          },
          end: {
            line: 71,
            column: 138
          }
        }, {
          start: {
            line: 71,
            column: 141
          },
          end: {
            line: 71,
            column: 146
          }
        }],
        line: 71
      },
      "38": {
        loc: {
          start: {
            line: 71,
            column: 34
          },
          end: {
            line: 71,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 34
          },
          end: {
            line: 71,
            column: 112
          }
        }, {
          start: {
            line: 71,
            column: 116
          },
          end: {
            line: 71,
            column: 129
          }
        }],
        line: 71
      },
      "39": {
        loc: {
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 71,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 81
          },
          end: {
            line: 71,
            column: 87
          }
        }, {
          start: {
            line: 71,
            column: 90
          },
          end: {
            line: 71,
            column: 102
          }
        }],
        line: 71
      },
      "40": {
        loc: {
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 71,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 71,
            column: 56
          }
        }, {
          start: {
            line: 71,
            column: 60
          },
          end: {
            line: 71,
            column: 78
          }
        }],
        line: 71
      },
      "41": {
        loc: {
          start: {
            line: 93,
            column: 28
          },
          end: {
            line: 95,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 28
          },
          end: {
            line: 95,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "42": {
        loc: {
          start: {
            line: 96,
            column: 28
          },
          end: {
            line: 98,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 28
          },
          end: {
            line: 98,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "43": {
        loc: {
          start: {
            line: 109,
            column: 28
          },
          end: {
            line: 111,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 28
          },
          end: {
            line: 111,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "44": {
        loc: {
          start: {
            line: 118,
            column: 55
          },
          end: {
            line: 118,
            column: 126
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 118,
            column: 112
          },
          end: {
            line: 118,
            column: 118
          }
        }, {
          start: {
            line: 118,
            column: 121
          },
          end: {
            line: 118,
            column: 126
          }
        }],
        line: 118
      },
      "45": {
        loc: {
          start: {
            line: 118,
            column: 55
          },
          end: {
            line: 118,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 118,
            column: 55
          },
          end: {
            line: 118,
            column: 92
          }
        }, {
          start: {
            line: 118,
            column: 96
          },
          end: {
            line: 118,
            column: 109
          }
        }],
        line: 118
      },
      "46": {
        loc: {
          start: {
            line: 135,
            column: 28
          },
          end: {
            line: 135,
            column: 90
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 28
          },
          end: {
            line: 135,
            column: 90
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "47": {
        loc: {
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 250,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 199,
            column: 24
          },
          end: {
            line: 199,
            column: 108
          }
        }, {
          start: {
            line: 200,
            column: 24
          },
          end: {
            line: 206,
            column: 57
          }
        }, {
          start: {
            line: 207,
            column: 24
          },
          end: {
            line: 216,
            column: 36
          }
        }, {
          start: {
            line: 217,
            column: 24
          },
          end: {
            line: 234,
            column: 36
          }
        }, {
          start: {
            line: 235,
            column: 24
          },
          end: {
            line: 239,
            column: 165
          }
        }, {
          start: {
            line: 240,
            column: 24
          },
          end: {
            line: 243,
            column: 183
          }
        }, {
          start: {
            line: 244,
            column: 24
          },
          end: {
            line: 249,
            column: 36
          }
        }],
        line: 198
      },
      "48": {
        loc: {
          start: {
            line: 202,
            column: 28
          },
          end: {
            line: 204,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 28
          },
          end: {
            line: 204,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "49": {
        loc: {
          start: {
            line: 202,
            column: 34
          },
          end: {
            line: 202,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 202,
            column: 132
          },
          end: {
            line: 202,
            column: 138
          }
        }, {
          start: {
            line: 202,
            column: 141
          },
          end: {
            line: 202,
            column: 146
          }
        }],
        line: 202
      },
      "50": {
        loc: {
          start: {
            line: 202,
            column: 34
          },
          end: {
            line: 202,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 202,
            column: 34
          },
          end: {
            line: 202,
            column: 112
          }
        }, {
          start: {
            line: 202,
            column: 116
          },
          end: {
            line: 202,
            column: 129
          }
        }],
        line: 202
      },
      "51": {
        loc: {
          start: {
            line: 202,
            column: 40
          },
          end: {
            line: 202,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 202,
            column: 81
          },
          end: {
            line: 202,
            column: 87
          }
        }, {
          start: {
            line: 202,
            column: 90
          },
          end: {
            line: 202,
            column: 102
          }
        }],
        line: 202
      },
      "52": {
        loc: {
          start: {
            line: 202,
            column: 40
          },
          end: {
            line: 202,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 202,
            column: 40
          },
          end: {
            line: 202,
            column: 56
          }
        }, {
          start: {
            line: 202,
            column: 60
          },
          end: {
            line: 202,
            column: 78
          }
        }],
        line: 202
      },
      "53": {
        loc: {
          start: {
            line: 219,
            column: 28
          },
          end: {
            line: 221,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 28
          },
          end: {
            line: 221,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "54": {
        loc: {
          start: {
            line: 223,
            column: 28
          },
          end: {
            line: 225,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 28
          },
          end: {
            line: 225,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "55": {
        loc: {
          start: {
            line: 264,
            column: 20
          },
          end: {
            line: 360,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 265,
            column: 24
          },
          end: {
            line: 265,
            column: 108
          }
        }, {
          start: {
            line: 266,
            column: 24
          },
          end: {
            line: 272,
            column: 57
          }
        }, {
          start: {
            line: 273,
            column: 24
          },
          end: {
            line: 276,
            column: 111
          }
        }, {
          start: {
            line: 277,
            column: 24
          },
          end: {
            line: 324,
            column: 36
          }
        }, {
          start: {
            line: 325,
            column: 24
          },
          end: {
            line: 352,
            column: 195
          }
        }, {
          start: {
            line: 353,
            column: 24
          },
          end: {
            line: 359,
            column: 36
          }
        }],
        line: 264
      },
      "56": {
        loc: {
          start: {
            line: 268,
            column: 28
          },
          end: {
            line: 270,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 268,
            column: 28
          },
          end: {
            line: 270,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 268
      },
      "57": {
        loc: {
          start: {
            line: 268,
            column: 34
          },
          end: {
            line: 268,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 268,
            column: 132
          },
          end: {
            line: 268,
            column: 138
          }
        }, {
          start: {
            line: 268,
            column: 141
          },
          end: {
            line: 268,
            column: 146
          }
        }],
        line: 268
      },
      "58": {
        loc: {
          start: {
            line: 268,
            column: 34
          },
          end: {
            line: 268,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 268,
            column: 34
          },
          end: {
            line: 268,
            column: 112
          }
        }, {
          start: {
            line: 268,
            column: 116
          },
          end: {
            line: 268,
            column: 129
          }
        }],
        line: 268
      },
      "59": {
        loc: {
          start: {
            line: 268,
            column: 40
          },
          end: {
            line: 268,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 268,
            column: 81
          },
          end: {
            line: 268,
            column: 87
          }
        }, {
          start: {
            line: 268,
            column: 90
          },
          end: {
            line: 268,
            column: 102
          }
        }],
        line: 268
      },
      "60": {
        loc: {
          start: {
            line: 268,
            column: 40
          },
          end: {
            line: 268,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 268,
            column: 40
          },
          end: {
            line: 268,
            column: 56
          }
        }, {
          start: {
            line: 268,
            column: 60
          },
          end: {
            line: 268,
            column: 78
          }
        }],
        line: 268
      },
      "61": {
        loc: {
          start: {
            line: 279,
            column: 28
          },
          end: {
            line: 285,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 28
          },
          end: {
            line: 285,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "62": {
        loc: {
          start: {
            line: 327,
            column: 28
          },
          end: {
            line: 335,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 28
          },
          end: {
            line: 335,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "63": {
        loc: {
          start: {
            line: 347,
            column: 57
          },
          end: {
            line: 347,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 347,
            column: 78
          },
          end: {
            line: 347,
            column: 121
          }
        }, {
          start: {
            line: 347,
            column: 124
          },
          end: {
            line: 347,
            column: 125
          }
        }],
        line: 347
      },
      "64": {
        loc: {
          start: {
            line: 348,
            column: 49
          },
          end: {
            line: 348,
            column: 192
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 348,
            column: 50
          },
          end: {
            line: 348,
            column: 183
          }
        }, {
          start: {
            line: 348,
            column: 188
          },
          end: {
            line: 348,
            column: 192
          }
        }],
        line: 348
      },
      "65": {
        loc: {
          start: {
            line: 348,
            column: 50
          },
          end: {
            line: 348,
            column: 183
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 348,
            column: 167
          },
          end: {
            line: 348,
            column: 173
          }
        }, {
          start: {
            line: 348,
            column: 176
          },
          end: {
            line: 348,
            column: 183
          }
        }],
        line: 348
      },
      "66": {
        loc: {
          start: {
            line: 348,
            column: 50
          },
          end: {
            line: 348,
            column: 164
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 348,
            column: 50
          },
          end: {
            line: 348,
            column: 147
          }
        }, {
          start: {
            line: 348,
            column: 151
          },
          end: {
            line: 348,
            column: 164
          }
        }],
        line: 348
      },
      "67": {
        loc: {
          start: {
            line: 348,
            column: 207
          },
          end: {
            line: 349,
            column: 201
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 349,
            column: 40
          },
          end: {
            line: 349,
            column: 194
          }
        }, {
          start: {
            line: 349,
            column: 197
          },
          end: {
            line: 349,
            column: 201
          }
        }],
        line: 348
      },
      "68": {
        loc: {
          start: {
            line: 349,
            column: 40
          },
          end: {
            line: 349,
            column: 194
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 349,
            column: 41
          },
          end: {
            line: 349,
            column: 185
          }
        }, {
          start: {
            line: 349,
            column: 190
          },
          end: {
            line: 349,
            column: 194
          }
        }],
        line: 349
      },
      "69": {
        loc: {
          start: {
            line: 349,
            column: 41
          },
          end: {
            line: 349,
            column: 185
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 349,
            column: 169
          },
          end: {
            line: 349,
            column: 175
          }
        }, {
          start: {
            line: 349,
            column: 178
          },
          end: {
            line: 349,
            column: 185
          }
        }],
        line: 349
      },
      "70": {
        loc: {
          start: {
            line: 349,
            column: 41
          },
          end: {
            line: 349,
            column: 166
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 349,
            column: 41
          },
          end: {
            line: 349,
            column: 149
          }
        }, {
          start: {
            line: 349,
            column: 153
          },
          end: {
            line: 349,
            column: 166
          }
        }],
        line: 349
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0, 0, 0, 0, 0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0, 0, 0, 0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/enroll/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,uCAAsC;AACtC,6EAA2E;AAC3E,6CAAgD;AAChD,wFAA8E;AAG9E,iCAAiC;AACpB,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,uFAC3C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,gCAAgC;YAC/E;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACA,qBAAM,MAAM,EAAA;;4BAA/B,cAAc,GAAK,CAAA,SAAY,CAAA,GAAjB;4BAGH,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oCACxD,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;oCAC7B,OAAO,EAAE;wCACP,KAAK,EAAE;4CACL,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;4CAC7B,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,SAAS,EAAE,IAAI;gDACf,UAAU,EAAE,IAAI;6CACjB;yCACF;qCACF;iCACF,CAAC,EAAA;;4BAZI,YAAY,GAAG,SAYnB;4BAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gCAClB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAED,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gCAC3B,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+CAA+C,EAAE,EAC1E,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAG0B,qBAAM,eAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;oCAClE,KAAK,EAAE;wCACL,qBAAqB,EAAE;4CACrB,MAAM,QAAA;4CACN,cAAc,gBAAA;yCACf;qCACF;iCACF,CAAC,EAAA;;4BAPI,kBAAkB,GAAG,SAOzB;4BAEF,IAAI,kBAAkB,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wCAAwC,EAAE,EACnE,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGkB,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;oCACtD,IAAI,EAAE;wCACJ,MAAM,QAAA;wCACN,cAAc,gBAAA;wCACd,MAAM,EAAE,aAAa;wCACrB,UAAU,EAAE,YAAY,CAAC,KAAK,CAAC,MAAM;wCACrC,aAAa,EAAE,MAAA,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,0CAAE,EAAE;qCACzC;oCACD,OAAO,EAAE;wCACP,YAAY,EAAE;4CACZ,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,KAAK,EAAE,IAAI;gDACX,WAAW,EAAE,IAAI;gDACjB,UAAU,EAAE,IAAI;gDAChB,cAAc,EAAE,IAAI;gDACpB,QAAQ,EAAE,IAAI;6CACf;yCACF;qCACF;iCACF,CAAC,EAAA;;4BApBI,UAAU,GAAG,SAoBjB;iCAGE,CAAA,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,EAA7B,wBAA6B;4BACzB,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,CAAC;gCACvD,MAAM,QAAA;gCACN,kBAAkB,EAAE,UAAU,CAAC,EAAE;gCACjC,MAAM,EAAE,IAAI,CAAC,EAAE;gCACf,MAAM,EAAE,aAAsB;6BAC/B,CAAC,EALsD,CAKtD,CAAC,CAAC;4BAEJ,qBAAM,eAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC;oCAC/C,IAAI,EAAE,gBAAgB;iCACvB,CAAC,EAAA;;4BAFF,SAEE,CAAC;;;4BAIC,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;4BACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;4BAE3B,qBAAM,eAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oCACpC,KAAK,EAAE;wCACL,WAAW,EAAE;4CACX,MAAM,QAAA;4CACN,IAAI,EAAE,KAAK;yCACZ;qCACF;oCACD,MAAM,EAAE;wCACN,eAAe,EAAE;4CACf,SAAS,EAAE,CAAC;yCACb;qCACF;oCACD,MAAM,EAAE;wCACN,MAAM,QAAA;wCACN,IAAI,EAAE,KAAK;wCACX,eAAe,EAAE,CAAC;qCACnB;iCACF,CAAC,EAAA;;4BAjBF,SAiBE,CAAC;4BAEH,wBAAwB;4BACxB,qBAAM,8CAAiB,CAAC,MAAM,CAAC,wBAAiB,cAAc,cAAI,MAAM,CAAE,CAAC,EAAA;;4BAD3E,wBAAwB;4BACxB,SAA2E,CAAC;4BAC5E,qBAAM,8CAAiB,CAAC,gBAAgB,CAAC,CAAC,gBAAgB,EAAE,wBAAiB,cAAc,CAAE,EAAE,MAAM,CAAC,CAAC,EAAA;;4BAAvG,SAAuG,CAAC;4BAE1G,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,UAAU;oCAChB,OAAO,EAAE,wCAAwC;iCAClD,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;;;iBACrB,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,uCAAuC;AAC1B,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,uFAC7C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,kCAAkC;YACjF;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACA,qBAAM,MAAM,EAAA;;4BAA/B,cAAc,GAAK,CAAA,SAAY,CAAA,GAAjB;4BAGL,qBAAM,eAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;oCAC1D,KAAK,EAAE;wCACL,qBAAqB,EAAE;4CACrB,MAAM,QAAA;4CACN,cAAc,gBAAA;yCACf;qCACF;iCACF,CAAC,EAAA;;4BAPI,UAAU,GAAG,SAOjB;4BAEF,IAAI,CAAC,UAAU,EAAE,CAAC;gCAChB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAC/D,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAED,6BAA6B;4BAC7B,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gCACtC,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,8CAA8C,EAAE,EACzE,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAED,6CAA6C;4BAC7C,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;oCACnC,KAAK,EAAE;wCACL,qBAAqB,EAAE;4CACrB,MAAM,QAAA;4CACN,cAAc,gBAAA;yCACf;qCACF;iCACF,CAAC,EAAA;;4BARF,6CAA6C;4BAC7C,SAOE,CAAC;4BAEH,wBAAwB;4BACxB,qBAAM,8CAAiB,CAAC,MAAM,CAAC,wBAAiB,cAAc,cAAI,MAAM,CAAE,CAAC,EAAA;;4BAD3E,wBAAwB;4BACxB,SAA2E,CAAC;4BAC5E,qBAAM,8CAAiB,CAAC,gBAAgB,CAAC,CAAC,gBAAgB,EAAE,wBAAiB,cAAc,CAAE,EAAE,MAAM,CAAC,CAAC,EAAA;;4BAAvG,SAAuG,CAAC;4BAExG,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,OAAO,EAAE,4CAA4C;iCACtD,CAAC,EAAC;;;iBACN,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,2CAA2C;AAC9B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE,8BAA8B;YAC9E;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACA,qBAAM,MAAM,EAAA;;4BAA/B,cAAc,GAAK,CAAA,SAAY,CAAA,GAAjB;4BAGlB,QAAQ,GAAG,qBAAc,cAAc,cAAI,MAAM,CAAE,CAAC;4BAG3C,qBAAM,8CAAiB,CAAC,GAAG,CAAM,QAAQ,CAAC,EAAA;;4BAAnD,MAAM,GAAG,SAA0C;4BACzD,IAAI,MAAM,EAAE,CAAC;gCACX,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,IAAI;wCACb,IAAI,EAAE,MAAM;wCACZ,MAAM,EAAE,IAAI;qCACb,CAAC,EAAC;4BACL,CAAC;4BAGkB,qBAAM,eAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;oCAC1D,KAAK,EAAE;wCACL,qBAAqB,EAAE;4CACrB,MAAM,QAAA;4CACN,cAAc,gBAAA;yCACf;qCACF;oCACD,OAAO,EAAE;wCACP,YAAY,EAAE;4CACZ,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,KAAK,EAAE,IAAI;gDACX,WAAW,EAAE,IAAI;gDACjB,UAAU,EAAE,IAAI;gDAChB,cAAc,EAAE,IAAI;gDACpB,QAAQ,EAAE,IAAI;6CACf;yCACF;wCACD,YAAY,EAAE;4CACZ,OAAO,EAAE;gDACP,IAAI,EAAE;oDACJ,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,KAAK,EAAE,IAAI;wDACX,SAAS,EAAE,IAAI;wDACf,QAAQ,EAAE,IAAI;wDACd,gBAAgB,EAAE,IAAI;wDACtB,UAAU,EAAE,IAAI;qDACjB;iDACF;6CACF;4CACD,OAAO,EAAE;gDACP,IAAI,EAAE;oDACJ,SAAS,EAAE,KAAK;iDACjB;6CACF;yCACF;qCACF;iCACF,CAAC,EAAA;;4BAtCI,UAAU,GAAG,SAsCjB;4BAEF,IAAI,CAAC,UAAU,EAAE,CAAC;gCAChB,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,IAAI;wCACb,IAAI,EAAE;4CACJ,QAAQ,EAAE,KAAK;4CACf,UAAU,EAAE,IAAI;yCACjB;qCACF,CAAC,EAAC;4BACL,CAAC;4BAGK,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,WAAW,EAAxB,CAAwB,CAAC,CAAC,MAAM,CAAC;4BACtF,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,aAAa,EAA1B,CAA0B,CAAC,CAAC,MAAM,CAAC;4BACzF,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,SAAS,EAAjB,CAAiB,EAAE,CAAC,CAAC,CAAC;4BAElF,YAAY,GAAG;gCACnB,QAAQ,EAAE,IAAI;gCACd,UAAU,wBACL,UAAU,KACb,eAAe,EAAE;wCACf,cAAc,gBAAA;wCACd,eAAe,iBAAA;wCACf,UAAU,EAAE,UAAU,CAAC,UAAU;wCACjC,eAAe,EAAE,UAAU,CAAC,eAAe;wCAC3C,cAAc,gBAAA;wCACd,eAAe,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;qCACtF,EACD,QAAQ,EAAE,CAAA,MAAA,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,aAAa,EAA1B,CAA0B,CAAC,0CAAE,IAAI,KAAI,IAAI,EACrF,WAAW,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;wCACrC,CAAA,MAAA,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,aAAa,EAArC,CAAqC,CAAC,0CAAE,IAAI,KAAI,IAAI,CAAC,CAAC,CAAC,IAAI,GAChG;6BACF,CAAC;4BAEF,sBAAsB;4BACtB,qBAAM,8CAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,MAAM,CAAC,EAAE,CAAC,EAAA;;4BADzH,sBAAsB;4BACtB,SAAyH,CAAC;4BAE1H,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,YAAY;iCACnB,CAAC,EAAC;;;iBACN,CACF,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/enroll/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { consolidatedCache } from '@/lib/services/consolidated-cache-service';\nimport { withCSRFProtection } from '@/lib/csrf';\n\n// POST - Enroll in learning path\nexport const POST = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 enrollments per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      const userId = session.user.id;\n      const { id: learningPathId } = await params;\n\n        // Check if learning path exists and is active\n        const learningPath = await prisma.learningPath.findUnique({\n          where: { id: learningPathId },\n          include: {\n            steps: {\n              orderBy: { stepOrder: 'asc' },\n              select: {\n                id: true,\n                stepOrder: true,\n                isRequired: true,\n              }\n            }\n          }\n        });\n\n        if (!learningPath) {\n          return NextResponse.json(\n            { success: false, error: 'Learning path not found' },\n            { status: 404 }\n          );\n        }\n\n        if (!learningPath.isActive) {\n          return NextResponse.json(\n            { success: false, error: 'Learning path is not available for enrollment' },\n            { status: 400 }\n          );\n        }\n\n        // Check if user is already enrolled\n        const existingEnrollment = await prisma.userLearningPath.findUnique({\n          where: {\n            userId_learningPathId: {\n              userId,\n              learningPathId\n            }\n          }\n        });\n\n        if (existingEnrollment) {\n          return NextResponse.json(\n            { success: false, error: 'Already enrolled in this learning path' },\n            { status: 409 }\n          );\n        }\n\n        // Create enrollment\n        const enrollment = await prisma.userLearningPath.create({\n          data: {\n            userId,\n            learningPathId,\n            status: 'NOT_STARTED',\n            totalSteps: learningPath.steps.length,\n            currentStepId: learningPath.steps[0]?.id,\n          },\n          include: {\n            learningPath: {\n              select: {\n                id: true,\n                title: true,\n                description: true,\n                difficulty: true,\n                estimatedHours: true,\n                category: true,\n              }\n            }\n          }\n        });\n\n        // Create progress records for all steps\n        if (learningPath.steps.length > 0) {\n          const stepProgressData = learningPath.steps.map(step => ({\n            userId,\n            userLearningPathId: enrollment.id,\n            stepId: step.id,\n            status: 'NOT_STARTED' as const,\n          }));\n\n          await prisma.userLearningPathProgress.createMany({\n            data: stepProgressData\n          });\n        }\n\n        // Create initial learning analytics record\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n\n        await prisma.learningAnalytics.upsert({\n          where: {\n            userId_date: {\n              userId,\n              date: today\n            }\n          },\n          update: {\n            pathsProgressed: {\n              increment: 1\n            }\n          },\n          create: {\n            userId,\n            date: today,\n            pathsProgressed: 1,\n          }\n        });\n\n        // Clear relevant caches\n        await consolidatedCache.delete(`learning_path:${learningPathId}:${userId}`);\n        await consolidatedCache.invalidateByTags(['learning_paths', `learning_path:${learningPathId}`, userId]);\n\n      return NextResponse.json({\n        success: true,\n        data: enrollment,\n        message: 'Successfully enrolled in learning path'\n      }, { status: 201 });\n    }\n  );\n});\n\n// DELETE - Unenroll from learning path\nexport const DELETE = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 unenrollments per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      const userId = session.user.id;\n      const { id: learningPathId } = await params;\n\n        // Check if user is enrolled\n        const enrollment = await prisma.userLearningPath.findUnique({\n          where: {\n            userId_learningPathId: {\n              userId,\n              learningPathId\n            }\n          }\n        });\n\n        if (!enrollment) {\n          return NextResponse.json(\n            { success: false, error: 'Not enrolled in this learning path' },\n            { status: 404 }\n          );\n        }\n\n        // Check if path is completed\n        if (enrollment.status === 'COMPLETED') {\n          return NextResponse.json(\n            { success: false, error: 'Cannot unenroll from completed learning path' },\n            { status: 400 }\n          );\n        }\n\n        // Delete enrollment and all related progress\n        await prisma.userLearningPath.delete({\n          where: {\n            userId_learningPathId: {\n              userId,\n              learningPathId\n            }\n          }\n        });\n\n        // Clear relevant caches\n        await consolidatedCache.delete(`learning_path:${learningPathId}:${userId}`);\n        await consolidatedCache.invalidateByTags(['learning_paths', `learning_path:${learningPathId}`, userId]);\n\n        return NextResponse.json({\n          success: true,\n          message: 'Successfully unenrolled from learning path'\n        });\n    }\n  );\n});\n\n// GET - Get enrollment status and progress\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      const userId = session.user.id;\n      const { id: learningPathId } = await params;\n\n        // Build cache key\n        const cacheKey = `enrollment:${learningPathId}:${userId}`;\n        \n        // Check cache first\n        const cached = await consolidatedCache.get<any>(cacheKey);\n        if (cached) {\n          return NextResponse.json({\n            success: true,\n            data: cached,\n            cached: true\n          });\n        }\n\n        // Get enrollment with detailed progress\n        const enrollment = await prisma.userLearningPath.findUnique({\n          where: {\n            userId_learningPathId: {\n              userId,\n              learningPathId\n            }\n          },\n          include: {\n            learningPath: {\n              select: {\n                id: true,\n                title: true,\n                description: true,\n                difficulty: true,\n                estimatedHours: true,\n                category: true,\n              }\n            },\n            stepProgress: {\n              include: {\n                step: {\n                  select: {\n                    id: true,\n                    title: true,\n                    stepOrder: true,\n                    stepType: true,\n                    estimatedMinutes: true,\n                    isRequired: true,\n                  }\n                }\n              },\n              orderBy: {\n                step: {\n                  stepOrder: 'asc'\n                }\n              }\n            }\n          }\n        });\n\n        if (!enrollment) {\n          return NextResponse.json({\n            success: true,\n            data: {\n              enrolled: false,\n              enrollment: null\n            }\n          });\n        }\n\n        // Calculate detailed progress metrics\n        const completedSteps = enrollment.stepProgress.filter(p => p.status === 'COMPLETED').length;\n        const inProgressSteps = enrollment.stepProgress.filter(p => p.status === 'IN_PROGRESS').length;\n        const totalTimeSpent = enrollment.stepProgress.reduce((sum, p) => sum + p.timeSpent, 0);\n        \n        const progressData = {\n          enrolled: true,\n          enrollment: {\n            ...enrollment,\n            progressMetrics: {\n              completedSteps,\n              inProgressSteps,\n              totalSteps: enrollment.totalSteps,\n              progressPercent: enrollment.progressPercent,\n              totalTimeSpent,\n              averageStepTime: completedSteps > 0 ? Math.round(totalTimeSpent / completedSteps) : 0,\n            },\n            nextStep: enrollment.stepProgress.find(p => p.status === 'NOT_STARTED')?.step || null,\n            currentStep: enrollment.currentStepId ? \n              enrollment.stepProgress.find(p => p.stepId === enrollment.currentStepId)?.step || null : null,\n          }\n        };\n\n        // Cache for 2 minutes\n        await consolidatedCache.set(cacheKey, progressData, { ttl: 2 * 60 * 1000, tags: ['enrollment', learningPathId, userId] });\n\n        return NextResponse.json({\n          success: true,\n          data: progressData\n        });\n    }\n  );\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "733ef90492ce75e03ec3f946d1b6571bca9bf298"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_4uueiioih = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_4uueiioih();
var __assign =
/* istanbul ignore next */
(cov_4uueiioih().s[0]++,
/* istanbul ignore next */
(cov_4uueiioih().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_4uueiioih().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_4uueiioih().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_4uueiioih().f[0]++;
  cov_4uueiioih().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_4uueiioih().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_4uueiioih().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_4uueiioih().f[1]++;
    cov_4uueiioih().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_4uueiioih().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_4uueiioih().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_4uueiioih().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_4uueiioih().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_4uueiioih().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_4uueiioih().b[2][0]++;
          cov_4uueiioih().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_4uueiioih().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_4uueiioih().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_4uueiioih().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_4uueiioih().s[11]++,
/* istanbul ignore next */
(cov_4uueiioih().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_4uueiioih().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_4uueiioih().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_4uueiioih().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_4uueiioih().f[3]++;
    cov_4uueiioih().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_4uueiioih().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_4uueiioih().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_4uueiioih().f[4]++;
      cov_4uueiioih().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_4uueiioih().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_4uueiioih().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_4uueiioih().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_4uueiioih().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_4uueiioih().f[6]++;
      cov_4uueiioih().s[15]++;
      try {
        /* istanbul ignore next */
        cov_4uueiioih().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_4uueiioih().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_4uueiioih().f[7]++;
      cov_4uueiioih().s[18]++;
      try {
        /* istanbul ignore next */
        cov_4uueiioih().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_4uueiioih().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_4uueiioih().f[8]++;
      cov_4uueiioih().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_4uueiioih().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_4uueiioih().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_4uueiioih().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_4uueiioih().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_4uueiioih().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_4uueiioih().s[23]++,
/* istanbul ignore next */
(cov_4uueiioih().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_4uueiioih().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_4uueiioih().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_4uueiioih().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_4uueiioih().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_4uueiioih().f[10]++;
        cov_4uueiioih().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_4uueiioih().b[9][0]++;
          cov_4uueiioih().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_4uueiioih().b[9][1]++;
        }
        cov_4uueiioih().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_4uueiioih().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_4uueiioih().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_4uueiioih().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_4uueiioih().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_4uueiioih().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_4uueiioih().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_4uueiioih().f[11]++;
    cov_4uueiioih().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_4uueiioih().f[12]++;
    cov_4uueiioih().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_4uueiioih().f[13]++;
      cov_4uueiioih().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_4uueiioih().f[14]++;
    cov_4uueiioih().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_4uueiioih().b[12][0]++;
      cov_4uueiioih().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_4uueiioih().b[12][1]++;
    }
    cov_4uueiioih().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_4uueiioih().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_4uueiioih().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_4uueiioih().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_4uueiioih().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_4uueiioih().s[36]++;
      try {
        /* istanbul ignore next */
        cov_4uueiioih().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_4uueiioih().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_4uueiioih().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_4uueiioih().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_4uueiioih().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_4uueiioih().b[18][0]++,
        /* istanbul ignore next */
        (cov_4uueiioih().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_4uueiioih().b[19][1]++,
        /* istanbul ignore next */
        (cov_4uueiioih().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_4uueiioih().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_4uueiioih().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_4uueiioih().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_4uueiioih().b[15][0]++;
          cov_4uueiioih().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_4uueiioih().b[15][1]++;
        }
        cov_4uueiioih().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_4uueiioih().b[21][0]++;
          cov_4uueiioih().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_4uueiioih().b[21][1]++;
        }
        cov_4uueiioih().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_4uueiioih().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_4uueiioih().b[22][1]++;
            cov_4uueiioih().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_4uueiioih().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_4uueiioih().b[22][2]++;
            cov_4uueiioih().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_4uueiioih().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_4uueiioih().b[22][3]++;
            cov_4uueiioih().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_4uueiioih().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_4uueiioih().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_4uueiioih().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_4uueiioih().b[22][4]++;
            cov_4uueiioih().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_4uueiioih().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_4uueiioih().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_4uueiioih().b[22][5]++;
            cov_4uueiioih().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_4uueiioih().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_4uueiioih().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_4uueiioih().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_4uueiioih().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_4uueiioih().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_4uueiioih().b[23][0]++;
              cov_4uueiioih().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_4uueiioih().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_4uueiioih().b[23][1]++;
            }
            cov_4uueiioih().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_4uueiioih().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_4uueiioih().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_4uueiioih().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_4uueiioih().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_4uueiioih().b[26][0]++;
              cov_4uueiioih().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_4uueiioih().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_4uueiioih().b[26][1]++;
            }
            cov_4uueiioih().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_4uueiioih().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_4uueiioih().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_4uueiioih().b[28][0]++;
              cov_4uueiioih().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_4uueiioih().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_4uueiioih().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_4uueiioih().b[28][1]++;
            }
            cov_4uueiioih().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_4uueiioih().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_4uueiioih().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_4uueiioih().b[30][0]++;
              cov_4uueiioih().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_4uueiioih().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_4uueiioih().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_4uueiioih().b[30][1]++;
            }
            cov_4uueiioih().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_4uueiioih().b[32][0]++;
              cov_4uueiioih().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_4uueiioih().b[32][1]++;
            }
            cov_4uueiioih().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_4uueiioih().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_4uueiioih().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_4uueiioih().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_4uueiioih().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_4uueiioih().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_4uueiioih().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_4uueiioih().b[33][0]++;
      cov_4uueiioih().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_4uueiioih().b[33][1]++;
    }
    cov_4uueiioih().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_4uueiioih().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_4uueiioih().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_4uueiioih().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_4uueiioih().s[79]++;
exports.GET = exports.DELETE = exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_4uueiioih().s[80]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_4uueiioih().s[81]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_4uueiioih().s[82]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_4uueiioih().s[83]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_4uueiioih().s[84]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_4uueiioih().s[85]++, require("@/lib/rateLimit"));
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_4uueiioih().s[86]++, require("@/lib/services/consolidated-cache-service"));
// POST - Enroll in learning path
/* istanbul ignore next */
cov_4uueiioih().s[87]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_4uueiioih().f[15]++;
  cov_4uueiioih().s[88]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_4uueiioih().f[16]++;
    var params =
    /* istanbul ignore next */
    (cov_4uueiioih().s[89]++, _b.params);
    /* istanbul ignore next */
    cov_4uueiioih().s[90]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_4uueiioih().f[17]++;
      cov_4uueiioih().s[91]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 50
      },
      // 50 enrollments per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_4uueiioih().f[18]++;
        cov_4uueiioih().s[92]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_4uueiioih().f[19]++;
          var session, userId, learningPathId, learningPath, existingEnrollment, enrollment, stepProgressData, today;
          var _a, _b;
          /* istanbul ignore next */
          cov_4uueiioih().s[93]++;
          return __generator(this, function (_c) {
            /* istanbul ignore next */
            cov_4uueiioih().f[20]++;
            cov_4uueiioih().s[94]++;
            switch (_c.label) {
              case 0:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][0]++;
                cov_4uueiioih().s[95]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][1]++;
                cov_4uueiioih().s[96]++;
                session = _c.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[97]++;
                if (!(
                /* istanbul ignore next */
                (cov_4uueiioih().b[38][0]++, (_a =
                /* istanbul ignore next */
                (cov_4uueiioih().b[40][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_4uueiioih().b[40][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_4uueiioih().b[39][0]++, void 0) :
                /* istanbul ignore next */
                (cov_4uueiioih().b[39][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_4uueiioih().b[38][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_4uueiioih().b[37][0]++, void 0) :
                /* istanbul ignore next */
                (cov_4uueiioih().b[37][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[36][0]++;
                  cov_4uueiioih().s[98]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[36][1]++;
                }
                cov_4uueiioih().s[99]++;
                userId = session.user.id;
                /* istanbul ignore next */
                cov_4uueiioih().s[100]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][2]++;
                cov_4uueiioih().s[101]++;
                learningPathId = _c.sent().id;
                /* istanbul ignore next */
                cov_4uueiioih().s[102]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.findUnique({
                  where: {
                    id: learningPathId
                  },
                  include: {
                    steps: {
                      orderBy: {
                        stepOrder: 'asc'
                      },
                      select: {
                        id: true,
                        stepOrder: true,
                        isRequired: true
                      }
                    }
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][3]++;
                cov_4uueiioih().s[103]++;
                learningPath = _c.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[104]++;
                if (!learningPath) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[41][0]++;
                  cov_4uueiioih().s[105]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Learning path not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[41][1]++;
                }
                cov_4uueiioih().s[106]++;
                if (!learningPath.isActive) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[42][0]++;
                  cov_4uueiioih().s[107]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Learning path is not available for enrollment'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[42][1]++;
                }
                cov_4uueiioih().s[108]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPath.findUnique({
                  where: {
                    userId_learningPathId: {
                      userId: userId,
                      learningPathId: learningPathId
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][4]++;
                cov_4uueiioih().s[109]++;
                existingEnrollment = _c.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[110]++;
                if (existingEnrollment) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[43][0]++;
                  cov_4uueiioih().s[111]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Already enrolled in this learning path'
                  }, {
                    status: 409
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[43][1]++;
                }
                cov_4uueiioih().s[112]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPath.create({
                  data: {
                    userId: userId,
                    learningPathId: learningPathId,
                    status: 'NOT_STARTED',
                    totalSteps: learningPath.steps.length,
                    currentStepId:
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[45][0]++, (_b = learningPath.steps[0]) === null) ||
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[45][1]++, _b === void 0) ?
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[44][0]++, void 0) :
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[44][1]++, _b.id)
                  },
                  include: {
                    learningPath: {
                      select: {
                        id: true,
                        title: true,
                        description: true,
                        difficulty: true,
                        estimatedHours: true,
                        category: true
                      }
                    }
                  }
                })];
              case 5:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][5]++;
                cov_4uueiioih().s[113]++;
                enrollment = _c.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[114]++;
                if (!(learningPath.steps.length > 0)) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[46][0]++;
                  cov_4uueiioih().s[115]++;
                  return [3 /*break*/, 7];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[46][1]++;
                }
                cov_4uueiioih().s[116]++;
                stepProgressData = learningPath.steps.map(function (step) {
                  /* istanbul ignore next */
                  cov_4uueiioih().f[21]++;
                  cov_4uueiioih().s[117]++;
                  return {
                    userId: userId,
                    userLearningPathId: enrollment.id,
                    stepId: step.id,
                    status: 'NOT_STARTED'
                  };
                });
                /* istanbul ignore next */
                cov_4uueiioih().s[118]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPathProgress.createMany({
                  data: stepProgressData
                })];
              case 6:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][6]++;
                cov_4uueiioih().s[119]++;
                _c.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[120]++;
                _c.label = 7;
              case 7:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][7]++;
                cov_4uueiioih().s[121]++;
                today = new Date();
                /* istanbul ignore next */
                cov_4uueiioih().s[122]++;
                today.setHours(0, 0, 0, 0);
                /* istanbul ignore next */
                cov_4uueiioih().s[123]++;
                return [4 /*yield*/, prisma_1.prisma.learningAnalytics.upsert({
                  where: {
                    userId_date: {
                      userId: userId,
                      date: today
                    }
                  },
                  update: {
                    pathsProgressed: {
                      increment: 1
                    }
                  },
                  create: {
                    userId: userId,
                    date: today,
                    pathsProgressed: 1
                  }
                })];
              case 8:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][8]++;
                cov_4uueiioih().s[124]++;
                _c.sent();
                // Clear relevant caches
                /* istanbul ignore next */
                cov_4uueiioih().s[125]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.delete("learning_path:".concat(learningPathId, ":").concat(userId))];
              case 9:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][9]++;
                cov_4uueiioih().s[126]++;
                // Clear relevant caches
                _c.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[127]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.invalidateByTags(['learning_paths', "learning_path:".concat(learningPathId), userId])];
              case 10:
                /* istanbul ignore next */
                cov_4uueiioih().b[35][10]++;
                cov_4uueiioih().s[128]++;
                _c.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[129]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: enrollment,
                  message: 'Successfully enrolled in learning path'
                }, {
                  status: 201
                })];
            }
          });
        });
      })];
    });
  });
});
// DELETE - Unenroll from learning path
/* istanbul ignore next */
cov_4uueiioih().s[130]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_4uueiioih().f[22]++;
  cov_4uueiioih().s[131]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_4uueiioih().f[23]++;
    var params =
    /* istanbul ignore next */
    (cov_4uueiioih().s[132]++, _b.params);
    /* istanbul ignore next */
    cov_4uueiioih().s[133]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_4uueiioih().f[24]++;
      cov_4uueiioih().s[134]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 20
      },
      // 20 unenrollments per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_4uueiioih().f[25]++;
        cov_4uueiioih().s[135]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_4uueiioih().f[26]++;
          var session, userId, learningPathId, enrollment;
          var _a;
          /* istanbul ignore next */
          cov_4uueiioih().s[136]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_4uueiioih().f[27]++;
            cov_4uueiioih().s[137]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_4uueiioih().b[47][0]++;
                cov_4uueiioih().s[138]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_4uueiioih().b[47][1]++;
                cov_4uueiioih().s[139]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[140]++;
                if (!(
                /* istanbul ignore next */
                (cov_4uueiioih().b[50][0]++, (_a =
                /* istanbul ignore next */
                (cov_4uueiioih().b[52][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_4uueiioih().b[52][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_4uueiioih().b[51][0]++, void 0) :
                /* istanbul ignore next */
                (cov_4uueiioih().b[51][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_4uueiioih().b[50][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_4uueiioih().b[49][0]++, void 0) :
                /* istanbul ignore next */
                (cov_4uueiioih().b[49][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[48][0]++;
                  cov_4uueiioih().s[141]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[48][1]++;
                }
                cov_4uueiioih().s[142]++;
                userId = session.user.id;
                /* istanbul ignore next */
                cov_4uueiioih().s[143]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_4uueiioih().b[47][2]++;
                cov_4uueiioih().s[144]++;
                learningPathId = _b.sent().id;
                /* istanbul ignore next */
                cov_4uueiioih().s[145]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPath.findUnique({
                  where: {
                    userId_learningPathId: {
                      userId: userId,
                      learningPathId: learningPathId
                    }
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_4uueiioih().b[47][3]++;
                cov_4uueiioih().s[146]++;
                enrollment = _b.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[147]++;
                if (!enrollment) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[53][0]++;
                  cov_4uueiioih().s[148]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Not enrolled in this learning path'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[53][1]++;
                }
                // Check if path is completed
                cov_4uueiioih().s[149]++;
                if (enrollment.status === 'COMPLETED') {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[54][0]++;
                  cov_4uueiioih().s[150]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Cannot unenroll from completed learning path'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[54][1]++;
                }
                // Delete enrollment and all related progress
                cov_4uueiioih().s[151]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPath.delete({
                  where: {
                    userId_learningPathId: {
                      userId: userId,
                      learningPathId: learningPathId
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_4uueiioih().b[47][4]++;
                cov_4uueiioih().s[152]++;
                // Delete enrollment and all related progress
                _b.sent();
                // Clear relevant caches
                /* istanbul ignore next */
                cov_4uueiioih().s[153]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.delete("learning_path:".concat(learningPathId, ":").concat(userId))];
              case 5:
                /* istanbul ignore next */
                cov_4uueiioih().b[47][5]++;
                cov_4uueiioih().s[154]++;
                // Clear relevant caches
                _b.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[155]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.invalidateByTags(['learning_paths', "learning_path:".concat(learningPathId), userId])];
              case 6:
                /* istanbul ignore next */
                cov_4uueiioih().b[47][6]++;
                cov_4uueiioih().s[156]++;
                _b.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[157]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  message: 'Successfully unenrolled from learning path'
                })];
            }
          });
        });
      })];
    });
  });
});
// GET - Get enrollment status and progress
/* istanbul ignore next */
cov_4uueiioih().s[158]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_4uueiioih().f[28]++;
  cov_4uueiioih().s[159]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_4uueiioih().f[29]++;
    var params =
    /* istanbul ignore next */
    (cov_4uueiioih().s[160]++, _b.params);
    /* istanbul ignore next */
    cov_4uueiioih().s[161]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_4uueiioih().f[30]++;
      cov_4uueiioih().s[162]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 100
      },
      // 100 requests per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_4uueiioih().f[31]++;
        cov_4uueiioih().s[163]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_4uueiioih().f[32]++;
          var session, userId, learningPathId, cacheKey, cached, enrollment, completedSteps, inProgressSteps, totalTimeSpent, progressData;
          var _a, _b, _c;
          /* istanbul ignore next */
          cov_4uueiioih().s[164]++;
          return __generator(this, function (_d) {
            /* istanbul ignore next */
            cov_4uueiioih().f[33]++;
            cov_4uueiioih().s[165]++;
            switch (_d.label) {
              case 0:
                /* istanbul ignore next */
                cov_4uueiioih().b[55][0]++;
                cov_4uueiioih().s[166]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_4uueiioih().b[55][1]++;
                cov_4uueiioih().s[167]++;
                session = _d.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[168]++;
                if (!(
                /* istanbul ignore next */
                (cov_4uueiioih().b[58][0]++, (_a =
                /* istanbul ignore next */
                (cov_4uueiioih().b[60][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_4uueiioih().b[60][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_4uueiioih().b[59][0]++, void 0) :
                /* istanbul ignore next */
                (cov_4uueiioih().b[59][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_4uueiioih().b[58][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_4uueiioih().b[57][0]++, void 0) :
                /* istanbul ignore next */
                (cov_4uueiioih().b[57][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[56][0]++;
                  cov_4uueiioih().s[169]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[56][1]++;
                }
                cov_4uueiioih().s[170]++;
                userId = session.user.id;
                /* istanbul ignore next */
                cov_4uueiioih().s[171]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_4uueiioih().b[55][2]++;
                cov_4uueiioih().s[172]++;
                learningPathId = _d.sent().id;
                /* istanbul ignore next */
                cov_4uueiioih().s[173]++;
                cacheKey = "enrollment:".concat(learningPathId, ":").concat(userId);
                /* istanbul ignore next */
                cov_4uueiioih().s[174]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.get(cacheKey)];
              case 3:
                /* istanbul ignore next */
                cov_4uueiioih().b[55][3]++;
                cov_4uueiioih().s[175]++;
                cached = _d.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[176]++;
                if (cached) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[61][0]++;
                  cov_4uueiioih().s[177]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: true,
                    data: cached,
                    cached: true
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[61][1]++;
                }
                cov_4uueiioih().s[178]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPath.findUnique({
                  where: {
                    userId_learningPathId: {
                      userId: userId,
                      learningPathId: learningPathId
                    }
                  },
                  include: {
                    learningPath: {
                      select: {
                        id: true,
                        title: true,
                        description: true,
                        difficulty: true,
                        estimatedHours: true,
                        category: true
                      }
                    },
                    stepProgress: {
                      include: {
                        step: {
                          select: {
                            id: true,
                            title: true,
                            stepOrder: true,
                            stepType: true,
                            estimatedMinutes: true,
                            isRequired: true
                          }
                        }
                      },
                      orderBy: {
                        step: {
                          stepOrder: 'asc'
                        }
                      }
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_4uueiioih().b[55][4]++;
                cov_4uueiioih().s[179]++;
                enrollment = _d.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[180]++;
                if (!enrollment) {
                  /* istanbul ignore next */
                  cov_4uueiioih().b[62][0]++;
                  cov_4uueiioih().s[181]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: true,
                    data: {
                      enrolled: false,
                      enrollment: null
                    }
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_4uueiioih().b[62][1]++;
                }
                cov_4uueiioih().s[182]++;
                completedSteps = enrollment.stepProgress.filter(function (p) {
                  /* istanbul ignore next */
                  cov_4uueiioih().f[34]++;
                  cov_4uueiioih().s[183]++;
                  return p.status === 'COMPLETED';
                }).length;
                /* istanbul ignore next */
                cov_4uueiioih().s[184]++;
                inProgressSteps = enrollment.stepProgress.filter(function (p) {
                  /* istanbul ignore next */
                  cov_4uueiioih().f[35]++;
                  cov_4uueiioih().s[185]++;
                  return p.status === 'IN_PROGRESS';
                }).length;
                /* istanbul ignore next */
                cov_4uueiioih().s[186]++;
                totalTimeSpent = enrollment.stepProgress.reduce(function (sum, p) {
                  /* istanbul ignore next */
                  cov_4uueiioih().f[36]++;
                  cov_4uueiioih().s[187]++;
                  return sum + p.timeSpent;
                }, 0);
                /* istanbul ignore next */
                cov_4uueiioih().s[188]++;
                progressData = {
                  enrolled: true,
                  enrollment: __assign(__assign({}, enrollment), {
                    progressMetrics: {
                      completedSteps: completedSteps,
                      inProgressSteps: inProgressSteps,
                      totalSteps: enrollment.totalSteps,
                      progressPercent: enrollment.progressPercent,
                      totalTimeSpent: totalTimeSpent,
                      averageStepTime: completedSteps > 0 ?
                      /* istanbul ignore next */
                      (cov_4uueiioih().b[63][0]++, Math.round(totalTimeSpent / completedSteps)) :
                      /* istanbul ignore next */
                      (cov_4uueiioih().b[63][1]++, 0)
                    },
                    nextStep:
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[64][0]++,
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[66][0]++, (_b = enrollment.stepProgress.find(function (p) {
                      /* istanbul ignore next */
                      cov_4uueiioih().f[37]++;
                      cov_4uueiioih().s[189]++;
                      return p.status === 'NOT_STARTED';
                    })) === null) ||
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[66][1]++, _b === void 0) ?
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[65][0]++, void 0) :
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[65][1]++, _b.step)) ||
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[64][1]++, null),
                    currentStep: enrollment.currentStepId ?
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[67][0]++,
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[68][0]++,
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[70][0]++, (_c = enrollment.stepProgress.find(function (p) {
                      /* istanbul ignore next */
                      cov_4uueiioih().f[38]++;
                      cov_4uueiioih().s[190]++;
                      return p.stepId === enrollment.currentStepId;
                    })) === null) ||
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[70][1]++, _c === void 0) ?
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[69][0]++, void 0) :
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[69][1]++, _c.step)) ||
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[68][1]++, null)) :
                    /* istanbul ignore next */
                    (cov_4uueiioih().b[67][1]++, null)
                  })
                };
                // Cache for 2 minutes
                /* istanbul ignore next */
                cov_4uueiioih().s[191]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.set(cacheKey, progressData, {
                  ttl: 2 * 60 * 1000,
                  tags: ['enrollment', learningPathId, userId]
                })];
              case 5:
                /* istanbul ignore next */
                cov_4uueiioih().b[55][5]++;
                cov_4uueiioih().s[192]++;
                // Cache for 2 minutes
                _d.sent();
                /* istanbul ignore next */
                cov_4uueiioih().s[193]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: progressData
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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