{"version": 3, "names": ["exports", "sanitizeText", "cov_2mn8ldl78g", "s", "validateSecurity", "validateInterviewResponse", "checkValidationRateLimit", "zod_1", "require", "isomorphic_dompurify_1", "__importDefault", "PATTERNS", "SAFE_TEXT", "PROFESSIONAL_TEXT", "EMAIL", "UUID", "URL", "MALICIOUS_PATTERNS", "input", "options", "f", "b", "sanitized", "stripWhitespace", "trim", "max<PERSON><PERSON><PERSON>", "length", "substring", "allowHtml", "default", "sanitize", "ALLOWED_TAGS", "ALLOWED_ATTR", "KEEP_CONTENT", "replace", "threats", "for<PERSON>ach", "pattern", "index", "test", "push", "<PERSON><PERSON><PERSON><PERSON>", "InterviewValidationSchemas", "responseText", "z", "string", "min", "max", "refine", "val", "security", "userNotes", "optional", "sessionConfig", "object", "sessionType", "enum", "careerPath", "experienceLevel", "companyType", "industryFocus", "specificRole", "interviewType", "preparationTime", "focusAreas", "array", "difficulty", "totalQuestions", "number", "timeValue", "uuid", "data", "errors", "sanitizedData", "__assign", "responseValidation", "apply", "notesValidation", "responseTime", "validationAttempts", "Map", "identifier", "maxAttempts", "windowMs", "now", "Date", "entry", "get", "count", "resetTime", "set"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/input-validation.ts"], "sourcesContent": ["import { z } from 'zod';\nimport DOMPurify from 'isomorphic-dompurify';\n\n/**\n * Comprehensive input validation and sanitization utilities\n * Addresses security issues identified by testerat\n */\n\n// Common validation patterns\nconst PATTERNS = {\n  // Basic text with no HTML\n  SAFE_TEXT: /^[a-zA-Z0-9\\s\\-_.,!?()'\"]+$/,\n  // Professional text (allows more punctuation)\n  PROFESSIONAL_TEXT: /^[a-zA-Z0-9\\s\\-_.,!?()'\"@#$%&*+=:;/\\\\[\\]{}|~`]+$/,\n  // Email validation\n  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\n  // UUID validation\n  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,\n  // URL validation\n  URL: /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/,\n};\n\n// Malicious patterns to detect and block\nconst MALICIOUS_PATTERNS = [\n  // XSS patterns\n  /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n  /<iframe\\b[^<]*(?:(?!<\\/iframe>)<[^<]*)*<\\/iframe>/gi,\n  /javascript:/gi,\n  /vbscript:/gi,\n  /onload\\s*=/gi,\n  /onerror\\s*=/gi,\n  /onclick\\s*=/gi,\n  /onmouseover\\s*=/gi,\n  \n  // SQL injection patterns\n  /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\\b)/gi,\n  /(--|#|\\/\\*|\\*\\/)/g,\n  /(\\bOR\\b|\\bAND\\b)\\s+\\d+\\s*=\\s*\\d+/gi,\n  \n  // Path traversal patterns\n  /\\.\\.\\//g,\n  /\\.\\.\\\\/g,\n  /%2e%2e%2f/gi,\n  /%2e%2e%5c/gi,\n  \n  // Template injection patterns\n  /\\{\\{.*\\}\\}/g,\n  /\\$\\{.*\\}/g,\n  /%\\{.*\\}/g,\n];\n\n/**\n * Sanitize text input by removing/escaping dangerous content\n */\nexport function sanitizeText(input: string, options: {\n  allowHtml?: boolean;\n  maxLength?: number;\n  stripWhitespace?: boolean;\n} = {}): string {\n  if (!input || typeof input !== 'string') {\n    return '';\n  }\n\n  let sanitized = input;\n\n  // Trim whitespace if requested\n  if (options.stripWhitespace) {\n    sanitized = sanitized.trim();\n  }\n\n  // Truncate if max length specified\n  if (options.maxLength && sanitized.length > options.maxLength) {\n    sanitized = sanitized.substring(0, options.maxLength);\n  }\n\n  // Handle HTML content\n  if (options.allowHtml) {\n    // Use DOMPurify to sanitize HTML\n    sanitized = DOMPurify.sanitize(sanitized, {\n      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],\n      ALLOWED_ATTR: [],\n      KEEP_CONTENT: true,\n    });\n  } else {\n    // Strip all HTML tags\n    sanitized = sanitized.replace(/<[^>]*>/g, '');\n  }\n\n  // Remove null bytes and other control characters\n  sanitized = sanitized.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]/g, '');\n\n  return sanitized;\n}\n\n/**\n * Validate input against malicious patterns\n */\nexport function validateSecurity(input: string): {\n  isValid: boolean;\n  threats: string[];\n  sanitized: string;\n} {\n  const threats: string[] = [];\n  let sanitized = input;\n\n  // Check for malicious patterns\n  MALICIOUS_PATTERNS.forEach((pattern, index) => {\n    if (pattern.test(input)) {\n      switch (index) {\n        case 0:\n        case 1:\n          threats.push('XSS script injection detected');\n          break;\n        case 2:\n        case 3:\n          threats.push('JavaScript protocol detected');\n          break;\n        case 4:\n        case 5:\n        case 6:\n        case 7:\n          threats.push('Event handler injection detected');\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n          threats.push('SQL injection pattern detected');\n          break;\n        case 12:\n        case 13:\n        case 14:\n        case 15:\n          threats.push('Path traversal attempt detected');\n          break;\n        case 16:\n        case 17:\n        case 18:\n          threats.push('Template injection detected');\n          break;\n      }\n      \n      // Remove the malicious content\n      sanitized = sanitized.replace(pattern, '');\n    }\n  });\n\n  return {\n    isValid: threats.length === 0,\n    threats,\n    sanitized: sanitizeText(sanitized, { stripWhitespace: true }),\n  };\n}\n\n/**\n * Interview practice specific validation schemas\n */\nexport const InterviewValidationSchemas = {\n  // Response text validation\n  responseText: z.string()\n    .min(10, 'Response must be at least 10 characters')\n    .max(5000, 'Response cannot exceed 5000 characters')\n    .refine((val) => {\n      const security = validateSecurity(val);\n      return security.isValid;\n    }, 'Response contains potentially harmful content'),\n\n  // User notes validation\n  userNotes: z.string()\n    .max(1000, 'Notes cannot exceed 1000 characters')\n    .optional()\n    .refine((val) => {\n      if (!val) return true;\n      const security = validateSecurity(val);\n      return security.isValid;\n    }, 'Notes contain potentially harmful content'),\n\n  // Session configuration validation\n  sessionConfig: z.object({\n    sessionType: z.enum(['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION']),\n    careerPath: z.string().max(100).optional(),\n    experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),\n    companyType: z.string().max(100).optional(),\n    industryFocus: z.string().max(100).optional(),\n    specificRole: z.string().max(100).optional(),\n    interviewType: z.enum(['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY']).optional(),\n    preparationTime: z.string().max(50).optional(),\n    focusAreas: z.array(z.string().max(100)).max(10).optional(),\n    difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).default('BEGINNER'),\n    totalQuestions: z.number().min(1).max(50).default(10),\n  }),\n\n  // Time validation\n  timeValue: z.number().min(0).max(7200), // Max 2 hours\n\n  // UUID validation\n  uuid: z.string().uuid('Invalid ID format'),\n};\n\n/**\n * Sanitize and validate interview response\n */\nexport function validateInterviewResponse(data: {\n  responseText: string;\n  userNotes?: string;\n  responseTime: number;\n  preparationTime: number;\n}): {\n  isValid: boolean;\n  errors: string[];\n  sanitizedData: typeof data;\n} {\n  const errors: string[] = [];\n  const sanitizedData = { ...data };\n\n  // Validate and sanitize response text\n  const responseValidation = validateSecurity(data.responseText);\n  if (!responseValidation.isValid) {\n    errors.push(...responseValidation.threats);\n  }\n  sanitizedData.responseText = sanitizeText(responseValidation.sanitized, {\n    maxLength: 5000,\n    stripWhitespace: true,\n  });\n\n  // Validate and sanitize user notes\n  if (data.userNotes) {\n    const notesValidation = validateSecurity(data.userNotes);\n    if (!notesValidation.isValid) {\n      errors.push(...notesValidation.threats);\n    }\n    sanitizedData.userNotes = sanitizeText(notesValidation.sanitized, {\n      maxLength: 1000,\n      stripWhitespace: true,\n    });\n  }\n\n  // Validate time values\n  if (data.responseTime < 0 || data.responseTime > 7200) {\n    errors.push('Invalid response time');\n  }\n  if (data.preparationTime < 0 || data.preparationTime > 1800) {\n    errors.push('Invalid preparation time');\n  }\n\n  // Check minimum response length\n  if (sanitizedData.responseText.length < 10) {\n    errors.push('Response is too short');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n    sanitizedData,\n  };\n}\n\n/**\n * Rate limiting for input validation\n */\nconst validationAttempts = new Map<string, { count: number; resetTime: number }>();\n\nexport function checkValidationRateLimit(identifier: string, maxAttempts = 10, windowMs = 60000): boolean {\n  const now = Date.now();\n  const entry = validationAttempts.get(identifier) || { count: 0, resetTime: now + windowMs };\n\n  if (now > entry.resetTime) {\n    entry.count = 0;\n    entry.resetTime = now + windowMs;\n  }\n\n  entry.count++;\n  validationAttempts.set(identifier, entry);\n\n  return entry.count <= maxAttempts;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDAA,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAsCC;AAAAC,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAI,gBAAA,GAAAA,gBAAA;AAuDC;AAAAF,cAAA,GAAAC,CAAA;AAkDDH,OAAA,CAAAK,yBAAA,GAAAA,yBAAA;AAqDC;AAAAH,cAAA,GAAAC,CAAA;AAODH,OAAA,CAAAM,wBAAA,GAAAA,wBAAA;AAtQA,IAAAC,KAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,QAAAK,OAAA;AACA,IAAAC,sBAAA;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,QAAAO,eAAA,CAAAF,OAAA;AAEA;;;;AAKA;AACA,IAAMG,QAAQ;AAAA;AAAA,CAAAT,cAAA,GAAAC,CAAA,QAAG;EACf;EACAS,SAAS,EAAE,6BAA6B;EACxC;EACAC,iBAAiB,EAAE,kDAAkD;EACrE;EACAC,KAAK,EAAE,kDAAkD;EACzD;EACAC,IAAI,EAAE,4EAA4E;EAClF;EACAC,GAAG,EAAE;CACN;AAED;AACA,IAAMC,kBAAkB;AAAA;AAAA,CAAAf,cAAA,GAAAC,CAAA,QAAG;AACzB;AACA,qDAAqD,EACrD,qDAAqD,EACrD,eAAe,EACf,aAAa,EACb,cAAc,EACd,eAAe,EACf,eAAe,EACf,mBAAmB;AAEnB;AACA,oEAAoE,EACpE,mBAAmB,EACnB,oCAAoC;AAEpC;AACA,SAAS,EACT,SAAS,EACT,aAAa,EACb,aAAa;AAEb;AACA,aAAa,EACb,WAAW,EACX,UAAU,CACX;AAED;;;AAGA,SAAgBF,YAAYA,CAACiB,KAAa,EAAEC,OAItC;EAAA;EAAAjB,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAC,CAAA;EAJsC,IAAAgB,OAAA;IAAA;IAAAjB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAAAgB,OAAA,KAItC;EAAA;EAAA;EAAA;IAAAjB,cAAA,GAAAmB,CAAA;EAAA;EAAAnB,cAAA,GAAAC,CAAA;EACJ;EAAI;EAAA,CAAAD,cAAA,GAAAmB,CAAA,WAACH,KAAK;EAAA;EAAA,CAAAhB,cAAA,GAAAmB,CAAA,UAAI,OAAOH,KAAK,KAAK,QAAQ,GAAE;IAAA;IAAAhB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IACvC,OAAO,EAAE;EACX,CAAC;EAAA;EAAA;IAAAD,cAAA,GAAAmB,CAAA;EAAA;EAED,IAAIC,SAAS;EAAA;EAAA,CAAApB,cAAA,GAAAC,CAAA,QAAGe,KAAK;EAErB;EAAA;EAAAhB,cAAA,GAAAC,CAAA;EACA,IAAIgB,OAAO,CAACI,eAAe,EAAE;IAAA;IAAArB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAC3BmB,SAAS,GAAGA,SAAS,CAACE,IAAI,EAAE;EAC9B,CAAC;EAAA;EAAA;IAAAtB,cAAA,GAAAmB,CAAA;EAAA;EAED;EAAAnB,cAAA,GAAAC,CAAA;EACA;EAAI;EAAA,CAAAD,cAAA,GAAAmB,CAAA,WAAAF,OAAO,CAACM,SAAS;EAAA;EAAA,CAAAvB,cAAA,GAAAmB,CAAA,WAAIC,SAAS,CAACI,MAAM,GAAGP,OAAO,CAACM,SAAS,GAAE;IAAA;IAAAvB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAC7DmB,SAAS,GAAGA,SAAS,CAACK,SAAS,CAAC,CAAC,EAAER,OAAO,CAACM,SAAS,CAAC;EACvD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAmB,CAAA;EAAA;EAED;EAAAnB,cAAA,GAAAC,CAAA;EACA,IAAIgB,OAAO,CAACS,SAAS,EAAE;IAAA;IAAA1B,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IACrB;IACAmB,SAAS,GAAGb,sBAAA,CAAAoB,OAAS,CAACC,QAAQ,CAACR,SAAS,EAAE;MACxCS,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACrEC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE;KACf,CAAC;EACJ,CAAC,MAAM;IAAA;IAAA/B,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IACL;IACAmB,SAAS,GAAGA,SAAS,CAACY,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAC/C;EAEA;EAAA;EAAAhC,cAAA,GAAAC,CAAA;EACAmB,SAAS,GAAGA,SAAS,CAACY,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC;EAAC;EAAAhC,cAAA,GAAAC,CAAA;EAEvE,OAAOmB,SAAS;AAClB;AAEA;;;AAGA,SAAgBlB,gBAAgBA,CAACc,KAAa;EAAA;EAAAhB,cAAA,GAAAkB,CAAA;EAK5C,IAAMe,OAAO;EAAA;EAAA,CAAAjC,cAAA,GAAAC,CAAA,QAAa,EAAE;EAC5B,IAAImB,SAAS;EAAA;EAAA,CAAApB,cAAA,GAAAC,CAAA,QAAGe,KAAK;EAErB;EAAA;EAAAhB,cAAA,GAAAC,CAAA;EACAc,kBAAkB,CAACmB,OAAO,CAAC,UAACC,OAAO,EAAEC,KAAK;IAAA;IAAApC,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAC,CAAA;IACxC,IAAIkC,OAAO,CAACE,IAAI,CAACrB,KAAK,CAAC,EAAE;MAAA;MAAAhB,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAC,CAAA;MACvB,QAAQmC,KAAK;QACX,KAAK,CAAC;UAAA;UAAApC,cAAA,GAAAmB,CAAA;QACN,KAAK,CAAC;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;UAAAnB,cAAA,GAAAC,CAAA;UACJgC,OAAO,CAACK,IAAI,CAAC,+BAA+B,CAAC;UAAC;UAAAtC,cAAA,GAAAC,CAAA;UAC9C;QACF,KAAK,CAAC;UAAA;UAAAD,cAAA,GAAAmB,CAAA;QACN,KAAK,CAAC;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;UAAAnB,cAAA,GAAAC,CAAA;UACJgC,OAAO,CAACK,IAAI,CAAC,8BAA8B,CAAC;UAAC;UAAAtC,cAAA,GAAAC,CAAA;UAC7C;QACF,KAAK,CAAC;UAAA;UAAAD,cAAA,GAAAmB,CAAA;QACN,KAAK,CAAC;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;QACN,KAAK,CAAC;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;QACN,KAAK,CAAC;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;UAAAnB,cAAA,GAAAC,CAAA;UACJgC,OAAO,CAACK,IAAI,CAAC,kCAAkC,CAAC;UAAC;UAAAtC,cAAA,GAAAC,CAAA;UACjD;QACF,KAAK,CAAC;UAAA;UAAAD,cAAA,GAAAmB,CAAA;QACN,KAAK,CAAC;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;QACN,KAAK,EAAE;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;QACP,KAAK,EAAE;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;UAAAnB,cAAA,GAAAC,CAAA;UACLgC,OAAO,CAACK,IAAI,CAAC,gCAAgC,CAAC;UAAC;UAAAtC,cAAA,GAAAC,CAAA;UAC/C;QACF,KAAK,EAAE;UAAA;UAAAD,cAAA,GAAAmB,CAAA;QACP,KAAK,EAAE;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;QACP,KAAK,EAAE;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;QACP,KAAK,EAAE;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;UAAAnB,cAAA,GAAAC,CAAA;UACLgC,OAAO,CAACK,IAAI,CAAC,iCAAiC,CAAC;UAAC;UAAAtC,cAAA,GAAAC,CAAA;UAChD;QACF,KAAK,EAAE;UAAA;UAAAD,cAAA,GAAAmB,CAAA;QACP,KAAK,EAAE;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;QACP,KAAK,EAAE;UAAA;UAAAnB,cAAA,GAAAmB,CAAA;UAAAnB,cAAA,GAAAC,CAAA;UACLgC,OAAO,CAACK,IAAI,CAAC,6BAA6B,CAAC;UAAC;UAAAtC,cAAA,GAAAC,CAAA;UAC5C;MACJ;MAEA;MAAA;MAAAD,cAAA,GAAAC,CAAA;MACAmB,SAAS,GAAGA,SAAS,CAACY,OAAO,CAACG,OAAO,EAAE,EAAE,CAAC;IAC5C,CAAC;IAAA;IAAA;MAAAnC,cAAA,GAAAmB,CAAA;IAAA;EACH,CAAC,CAAC;EAAC;EAAAnB,cAAA,GAAAC,CAAA;EAEH,OAAO;IACLsC,OAAO,EAAEN,OAAO,CAACT,MAAM,KAAK,CAAC;IAC7BS,OAAO,EAAAA,OAAA;IACPb,SAAS,EAAErB,YAAY,CAACqB,SAAS,EAAE;MAAEC,eAAe,EAAE;IAAI,CAAE;GAC7D;AACH;AAEA;;;AAAA;AAAArB,cAAA,GAAAC,CAAA;AAGaH,OAAA,CAAA0C,0BAA0B,GAAG;EACxC;EACAC,YAAY,EAAEpC,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CACrBC,GAAG,CAAC,EAAE,EAAE,yCAAyC,CAAC,CAClDC,GAAG,CAAC,IAAI,EAAE,wCAAwC,CAAC,CACnDC,MAAM,CAAC,UAACC,GAAG;IAAA;IAAA/C,cAAA,GAAAkB,CAAA;IACV,IAAM8B,QAAQ;IAAA;IAAA,CAAAhD,cAAA,GAAAC,CAAA,QAAGC,gBAAgB,CAAC6C,GAAG,CAAC;IAAC;IAAA/C,cAAA,GAAAC,CAAA;IACvC,OAAO+C,QAAQ,CAACT,OAAO;EACzB,CAAC,EAAE,+CAA+C,CAAC;EAErD;EACAU,SAAS,EAAE5C,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAClBE,GAAG,CAAC,IAAI,EAAE,qCAAqC,CAAC,CAChDK,QAAQ,EAAE,CACVJ,MAAM,CAAC,UAACC,GAAG;IAAA;IAAA/C,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAC,CAAA;IACV,IAAI,CAAC8C,GAAG,EAAE;MAAA;MAAA/C,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAC,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;IAAA;IAAA;MAAAD,cAAA,GAAAmB,CAAA;IAAA;IACtB,IAAM6B,QAAQ;IAAA;IAAA,CAAAhD,cAAA,GAAAC,CAAA,QAAGC,gBAAgB,CAAC6C,GAAG,CAAC;IAAC;IAAA/C,cAAA,GAAAC,CAAA;IACvC,OAAO+C,QAAQ,CAACT,OAAO;EACzB,CAAC,EAAE,2CAA2C,CAAC;EAEjD;EACAY,aAAa,EAAE9C,KAAA,CAAAqC,CAAC,CAACU,MAAM,CAAC;IACtBC,WAAW,EAAEhD,KAAA,CAAAqC,CAAC,CAACY,IAAI,CAAC,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;IAC3IC,UAAU,EAAElD,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAACE,GAAG,CAAC,GAAG,CAAC,CAACK,QAAQ,EAAE;IAC1CM,eAAe,EAAEnD,KAAA,CAAAqC,CAAC,CAACY,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAACJ,QAAQ,EAAE;IACtFO,WAAW,EAAEpD,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAACE,GAAG,CAAC,GAAG,CAAC,CAACK,QAAQ,EAAE;IAC3CQ,aAAa,EAAErD,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAACE,GAAG,CAAC,GAAG,CAAC,CAACK,QAAQ,EAAE;IAC7CS,YAAY,EAAEtD,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAACE,GAAG,CAAC,GAAG,CAAC,CAACK,QAAQ,EAAE;IAC5CU,aAAa,EAAEvD,KAAA,CAAAqC,CAAC,CAACY,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC,CAACJ,QAAQ,EAAE;IACnIW,eAAe,EAAExD,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAACE,GAAG,CAAC,EAAE,CAAC,CAACK,QAAQ,EAAE;IAC9CY,UAAU,EAAEzD,KAAA,CAAAqC,CAAC,CAACqB,KAAK,CAAC1D,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAACE,GAAG,CAAC,GAAG,CAAC,CAAC,CAACA,GAAG,CAAC,EAAE,CAAC,CAACK,QAAQ,EAAE;IAC3Dc,UAAU,EAAE3D,KAAA,CAAAqC,CAAC,CAACY,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC3B,OAAO,CAAC,UAAU,CAAC;IAC1FsC,cAAc,EAAE5D,KAAA,CAAAqC,CAAC,CAACwB,MAAM,EAAE,CAACtB,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,EAAE,CAAC,CAAClB,OAAO,CAAC,EAAE;GACrD,CAAC;EAEF;EACAwC,SAAS,EAAE9D,KAAA,CAAAqC,CAAC,CAACwB,MAAM,EAAE,CAACtB,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,IAAI,CAAC;EAAE;EAExC;EACAuB,IAAI,EAAE/D,KAAA,CAAAqC,CAAC,CAACC,MAAM,EAAE,CAACyB,IAAI,CAAC,mBAAmB;CAC1C;AAED;;;AAGA,SAAgBjE,yBAAyBA,CAACkE,IAKzC;EAAA;EAAArE,cAAA,GAAAkB,CAAA;EAKC,IAAMoD,MAAM;EAAA;EAAA,CAAAtE,cAAA,GAAAC,CAAA,QAAa,EAAE;EAC3B,IAAMsE,aAAa;EAAA;EAAA,CAAAvE,cAAA,GAAAC,CAAA,QAAAuE,QAAA,KAAQH,IAAI,CAAE;EAEjC;EACA,IAAMI,kBAAkB;EAAA;EAAA,CAAAzE,cAAA,GAAAC,CAAA,QAAGC,gBAAgB,CAACmE,IAAI,CAAC5B,YAAY,CAAC;EAAC;EAAAzC,cAAA,GAAAC,CAAA;EAC/D,IAAI,CAACwE,kBAAkB,CAAClC,OAAO,EAAE;IAAA;IAAAvC,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAC/BqE,MAAM,CAAChC,IAAI,CAAAoC,KAAA,CAAXJ,MAAM,EAASG,kBAAkB,CAACxC,OAAO;EAC3C,CAAC;EAAA;EAAA;IAAAjC,cAAA,GAAAmB,CAAA;EAAA;EAAAnB,cAAA,GAAAC,CAAA;EACDsE,aAAa,CAAC9B,YAAY,GAAG1C,YAAY,CAAC0E,kBAAkB,CAACrD,SAAS,EAAE;IACtEG,SAAS,EAAE,IAAI;IACfF,eAAe,EAAE;GAClB,CAAC;EAEF;EAAA;EAAArB,cAAA,GAAAC,CAAA;EACA,IAAIoE,IAAI,CAACpB,SAAS,EAAE;IAAA;IAAAjD,cAAA,GAAAmB,CAAA;IAClB,IAAMwD,eAAe;IAAA;IAAA,CAAA3E,cAAA,GAAAC,CAAA,QAAGC,gBAAgB,CAACmE,IAAI,CAACpB,SAAS,CAAC;IAAC;IAAAjD,cAAA,GAAAC,CAAA;IACzD,IAAI,CAAC0E,eAAe,CAACpC,OAAO,EAAE;MAAA;MAAAvC,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAC,CAAA;MAC5BqE,MAAM,CAAChC,IAAI,CAAAoC,KAAA,CAAXJ,MAAM,EAASK,eAAe,CAAC1C,OAAO;IACxC,CAAC;IAAA;IAAA;MAAAjC,cAAA,GAAAmB,CAAA;IAAA;IAAAnB,cAAA,GAAAC,CAAA;IACDsE,aAAa,CAACtB,SAAS,GAAGlD,YAAY,CAAC4E,eAAe,CAACvD,SAAS,EAAE;MAChEG,SAAS,EAAE,IAAI;MACfF,eAAe,EAAE;KAClB,CAAC;EACJ,CAAC;EAAA;EAAA;IAAArB,cAAA,GAAAmB,CAAA;EAAA;EAED;EAAAnB,cAAA,GAAAC,CAAA;EACA;EAAI;EAAA,CAAAD,cAAA,GAAAmB,CAAA,WAAAkD,IAAI,CAACO,YAAY,GAAG,CAAC;EAAA;EAAA,CAAA5E,cAAA,GAAAmB,CAAA,WAAIkD,IAAI,CAACO,YAAY,GAAG,IAAI,GAAE;IAAA;IAAA5E,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IACrDqE,MAAM,CAAChC,IAAI,CAAC,uBAAuB,CAAC;EACtC,CAAC;EAAA;EAAA;IAAAtC,cAAA,GAAAmB,CAAA;EAAA;EAAAnB,cAAA,GAAAC,CAAA;EACD;EAAI;EAAA,CAAAD,cAAA,GAAAmB,CAAA,WAAAkD,IAAI,CAACR,eAAe,GAAG,CAAC;EAAA;EAAA,CAAA7D,cAAA,GAAAmB,CAAA,WAAIkD,IAAI,CAACR,eAAe,GAAG,IAAI,GAAE;IAAA;IAAA7D,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAC3DqE,MAAM,CAAChC,IAAI,CAAC,0BAA0B,CAAC;EACzC,CAAC;EAAA;EAAA;IAAAtC,cAAA,GAAAmB,CAAA;EAAA;EAED;EAAAnB,cAAA,GAAAC,CAAA;EACA,IAAIsE,aAAa,CAAC9B,YAAY,CAACjB,MAAM,GAAG,EAAE,EAAE;IAAA;IAAAxB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAC1CqE,MAAM,CAAChC,IAAI,CAAC,uBAAuB,CAAC;EACtC,CAAC;EAAA;EAAA;IAAAtC,cAAA,GAAAmB,CAAA;EAAA;EAAAnB,cAAA,GAAAC,CAAA;EAED,OAAO;IACLsC,OAAO,EAAE+B,MAAM,CAAC9C,MAAM,KAAK,CAAC;IAC5B8C,MAAM,EAAAA,MAAA;IACNC,aAAa,EAAAA;GACd;AACH;AAEA;;;AAGA,IAAMM,kBAAkB;AAAA;AAAA,CAAA7E,cAAA,GAAAC,CAAA,QAAG,IAAI6E,GAAG,EAAgD;AAElF,SAAgB1E,wBAAwBA,CAAC2E,UAAkB,EAAEC,WAAgB,EAAEC,QAAgB;EAAA;EAAAjF,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAC,CAAA;EAAlC,IAAA+E,WAAA;IAAA;IAAAhF,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAAA+E,WAAA,KAAgB;EAAA;EAAA;EAAA;IAAAhF,cAAA,GAAAmB,CAAA;EAAA;EAAAnB,cAAA,GAAAC,CAAA;EAAE,IAAAgF,QAAA;IAAA;IAAAjF,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAAAgF,QAAA,QAAgB;EAAA;EAAA;EAAA;IAAAjF,cAAA,GAAAmB,CAAA;EAAA;EAC7F,IAAM+D,GAAG;EAAA;EAAA,CAAAlF,cAAA,GAAAC,CAAA,QAAGkF,IAAI,CAACD,GAAG,EAAE;EACtB,IAAME,KAAK;EAAA;EAAA,CAAApF,cAAA,GAAAC,CAAA;EAAG;EAAA,CAAAD,cAAA,GAAAmB,CAAA,WAAA0D,kBAAkB,CAACQ,GAAG,CAACN,UAAU,CAAC;EAAA;EAAA,CAAA/E,cAAA,GAAAmB,CAAA,WAAI;IAAEmE,KAAK,EAAE,CAAC;IAAEC,SAAS,EAAEL,GAAG,GAAGD;EAAQ,CAAE;EAAC;EAAAjF,cAAA,GAAAC,CAAA;EAE5F,IAAIiF,GAAG,GAAGE,KAAK,CAACG,SAAS,EAAE;IAAA;IAAAvF,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IACzBmF,KAAK,CAACE,KAAK,GAAG,CAAC;IAAC;IAAAtF,cAAA,GAAAC,CAAA;IAChBmF,KAAK,CAACG,SAAS,GAAGL,GAAG,GAAGD,QAAQ;EAClC,CAAC;EAAA;EAAA;IAAAjF,cAAA,GAAAmB,CAAA;EAAA;EAAAnB,cAAA,GAAAC,CAAA;EAEDmF,KAAK,CAACE,KAAK,EAAE;EAAC;EAAAtF,cAAA,GAAAC,CAAA;EACd4E,kBAAkB,CAACW,GAAG,CAACT,UAAU,EAAEK,KAAK,CAAC;EAAC;EAAApF,cAAA,GAAAC,CAAA;EAE1C,OAAOmF,KAAK,CAACE,KAAK,IAAIN,WAAW;AACnC", "ignoreList": []}