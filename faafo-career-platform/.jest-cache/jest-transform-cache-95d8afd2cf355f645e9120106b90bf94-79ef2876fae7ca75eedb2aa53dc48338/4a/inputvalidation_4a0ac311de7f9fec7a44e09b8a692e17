a2dc6669794f321745ef79cce2f6f1a0
"use strict";

/* istanbul ignore next */
function cov_2mn8ldl78g() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/input-validation.ts";
  var hash = "17c9f0ede884c480d470f898a9b6b3b4ed4ba361";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/input-validation.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 22
        },
        end: {
          line: 15,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 62
        }
      },
      "13": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 62
        }
      },
      "14": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 44
        }
      },
      "15": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 36
        }
      },
      "16": {
        start: {
          line: 19,
          column: 0
        },
        end: {
          line: 19,
          column: 44
        }
      },
      "17": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 62
        }
      },
      "18": {
        start: {
          line: 21,
          column: 0
        },
        end: {
          line: 21,
          column: 60
        }
      },
      "19": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 26
        }
      },
      "20": {
        start: {
          line: 23,
          column: 29
        },
        end: {
          line: 23,
          column: 77
        }
      },
      "21": {
        start: {
          line: 29,
          column: 15
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "22": {
        start: {
          line: 42,
          column: 25
        },
        end: {
          line: 65,
          column: 1
        }
      },
      "23": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 45
        }
      },
      "24": {
        start: {
          line: 70,
          column: 30
        },
        end: {
          line: 70,
          column: 43
        }
      },
      "25": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 73,
          column: 5
        }
      },
      "26": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 18
        }
      },
      "27": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 25
        }
      },
      "28": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 78,
          column: 5
        }
      },
      "29": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 77,
          column: 37
        }
      },
      "30": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 82,
          column: 5
        }
      },
      "31": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 62
        }
      },
      "32": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 95,
          column: 5
        }
      },
      "33": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 90,
          column: 11
        }
      },
      "34": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 94,
          column: 54
        }
      },
      "35": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 97,
          column: 75
        }
      },
      "36": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 98,
          column: 21
        }
      },
      "37": {
        start: {
          line: 104,
          column: 18
        },
        end: {
          line: 104,
          column: 20
        }
      },
      "38": {
        start: {
          line: 105,
          column: 20
        },
        end: {
          line: 105,
          column: 25
        }
      },
      "39": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 145,
          column: 7
        }
      },
      "40": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 144,
          column: 9
        }
      },
      "41": {
        start: {
          line: 109,
          column: 12
        },
        end: {
          line: 141,
          column: 13
        }
      },
      "42": {
        start: {
          line: 112,
          column: 20
        },
        end: {
          line: 112,
          column: 66
        }
      },
      "43": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 113,
          column: 26
        }
      },
      "44": {
        start: {
          line: 116,
          column: 20
        },
        end: {
          line: 116,
          column: 65
        }
      },
      "45": {
        start: {
          line: 117,
          column: 20
        },
        end: {
          line: 117,
          column: 26
        }
      },
      "46": {
        start: {
          line: 122,
          column: 20
        },
        end: {
          line: 122,
          column: 69
        }
      },
      "47": {
        start: {
          line: 123,
          column: 20
        },
        end: {
          line: 123,
          column: 26
        }
      },
      "48": {
        start: {
          line: 128,
          column: 20
        },
        end: {
          line: 128,
          column: 67
        }
      },
      "49": {
        start: {
          line: 129,
          column: 20
        },
        end: {
          line: 129,
          column: 26
        }
      },
      "50": {
        start: {
          line: 134,
          column: 20
        },
        end: {
          line: 134,
          column: 68
        }
      },
      "51": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 135,
          column: 26
        }
      },
      "52": {
        start: {
          line: 139,
          column: 20
        },
        end: {
          line: 139,
          column: 64
        }
      },
      "53": {
        start: {
          line: 140,
          column: 20
        },
        end: {
          line: 140,
          column: 26
        }
      },
      "54": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 143,
          column: 55
        }
      },
      "55": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 150,
          column: 6
        }
      },
      "56": {
        start: {
          line: 155,
          column: 0
        },
        end: {
          line: 192,
          column: 2
        }
      },
      "57": {
        start: {
          line: 161,
          column: 23
        },
        end: {
          line: 161,
          column: 44
        }
      },
      "58": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 162,
          column: 32
        }
      },
      "59": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 170,
          column: 24
        }
      },
      "60": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 170,
          column: 24
        }
      },
      "61": {
        start: {
          line: 171,
          column: 23
        },
        end: {
          line: 171,
          column: 44
        }
      },
      "62": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 172,
          column: 32
        }
      },
      "63": {
        start: {
          line: 197,
          column: 17
        },
        end: {
          line: 197,
          column: 19
        }
      },
      "64": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 42
        }
      },
      "65": {
        start: {
          line: 200,
          column: 29
        },
        end: {
          line: 200,
          column: 64
        }
      },
      "66": {
        start: {
          line: 201,
          column: 4
        },
        end: {
          line: 203,
          column: 5
        }
      },
      "67": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 202,
          column: 62
        }
      },
      "68": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 207,
          column: 7
        }
      },
      "69": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 218,
          column: 5
        }
      },
      "70": {
        start: {
          line: 210,
          column: 30
        },
        end: {
          line: 210,
          column: 62
        }
      },
      "71": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 213,
          column: 9
        }
      },
      "72": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 63
        }
      },
      "73": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 217,
          column: 11
        }
      },
      "74": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 222,
          column: 5
        }
      },
      "75": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 221,
          column: 45
        }
      },
      "76": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 225,
          column: 5
        }
      },
      "77": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 224,
          column: 48
        }
      },
      "78": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 229,
          column: 5
        }
      },
      "79": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 228,
          column: 45
        }
      },
      "80": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 234,
          column: 6
        }
      },
      "81": {
        start: {
          line: 239,
          column: 25
        },
        end: {
          line: 239,
          column: 34
        }
      },
      "82": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 241,
          column: 53
        }
      },
      "83": {
        start: {
          line: 241,
          column: 34
        },
        end: {
          line: 241,
          column: 51
        }
      },
      "84": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 242,
          column: 50
        }
      },
      "85": {
        start: {
          line: 242,
          column: 31
        },
        end: {
          line: 242,
          column: 48
        }
      },
      "86": {
        start: {
          line: 243,
          column: 14
        },
        end: {
          line: 243,
          column: 24
        }
      },
      "87": {
        start: {
          line: 244,
          column: 16
        },
        end: {
          line: 244,
          column: 93
        }
      },
      "88": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 248,
          column: 5
        }
      },
      "89": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 246,
          column: 24
        }
      },
      "90": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 41
        }
      },
      "91": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 249,
          column: 18
        }
      },
      "92": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 250,
          column: 46
        }
      },
      "93": {
        start: {
          line: 251,
          column: 4
        },
        end: {
          line: 251,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 56
          },
          end: {
            line: 13,
            column: 57
          }
        },
        loc: {
          start: {
            line: 13,
            column: 71
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "sanitizeText",
        decl: {
          start: {
            line: 69,
            column: 9
          },
          end: {
            line: 69,
            column: 21
          }
        },
        loc: {
          start: {
            line: 69,
            column: 38
          },
          end: {
            line: 99,
            column: 1
          }
        },
        line: 69
      },
      "4": {
        name: "validateSecurity",
        decl: {
          start: {
            line: 103,
            column: 9
          },
          end: {
            line: 103,
            column: 25
          }
        },
        loc: {
          start: {
            line: 103,
            column: 33
          },
          end: {
            line: 151,
            column: 1
          }
        },
        line: 103
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 107,
            column: 31
          },
          end: {
            line: 107,
            column: 32
          }
        },
        loc: {
          start: {
            line: 107,
            column: 57
          },
          end: {
            line: 145,
            column: 5
          }
        },
        line: 107
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 160,
            column: 17
          }
        },
        loc: {
          start: {
            line: 160,
            column: 31
          },
          end: {
            line: 163,
            column: 5
          }
        },
        line: 160
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 168,
            column: 16
          },
          end: {
            line: 168,
            column: 17
          }
        },
        loc: {
          start: {
            line: 168,
            column: 31
          },
          end: {
            line: 173,
            column: 5
          }
        },
        line: 168
      },
      "8": {
        name: "validateInterviewResponse",
        decl: {
          start: {
            line: 196,
            column: 9
          },
          end: {
            line: 196,
            column: 34
          }
        },
        loc: {
          start: {
            line: 196,
            column: 41
          },
          end: {
            line: 235,
            column: 1
          }
        },
        line: 196
      },
      "9": {
        name: "checkValidationRateLimit",
        decl: {
          start: {
            line: 240,
            column: 9
          },
          end: {
            line: 240,
            column: 33
          }
        },
        loc: {
          start: {
            line: 240,
            column: 69
          },
          end: {
            line: 252,
            column: 1
          }
        },
        line: 240
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 22
          },
          end: {
            line: 15,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 23
          },
          end: {
            line: 13,
            column: 27
          }
        }, {
          start: {
            line: 13,
            column: 31
          },
          end: {
            line: 13,
            column: 51
          }
        }, {
          start: {
            line: 13,
            column: 56
          },
          end: {
            line: 15,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 11
          },
          end: {
            line: 14,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 37
          },
          end: {
            line: 14,
            column: 40
          }
        }, {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 61
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 14,
            column: 12
          },
          end: {
            line: 14,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 12
          },
          end: {
            line: 14,
            column: 15
          }
        }, {
          start: {
            line: 14,
            column: 19
          },
          end: {
            line: 14,
            column: 33
          }
        }],
        line: 14
      },
      "6": {
        loc: {
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 70,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 70,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "7": {
        loc: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "8": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 14
          }
        }, {
          start: {
            line: 71,
            column: 18
          },
          end: {
            line: 71,
            column: 43
          }
        }],
        line: 71
      },
      "9": {
        loc: {
          start: {
            line: 76,
            column: 4
          },
          end: {
            line: 78,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 76,
            column: 4
          },
          end: {
            line: 78,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 76
      },
      "10": {
        loc: {
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "11": {
        loc: {
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 25
          }
        }, {
          start: {
            line: 80,
            column: 29
          },
          end: {
            line: 80,
            column: 65
          }
        }],
        line: 80
      },
      "12": {
        loc: {
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        }, {
          start: {
            line: 92,
            column: 9
          },
          end: {
            line: 95,
            column: 5
          }
        }],
        line: 84
      },
      "13": {
        loc: {
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 144,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 144,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "14": {
        loc: {
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 110,
            column: 16
          },
          end: {
            line: 110,
            column: 23
          }
        }, {
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 113,
            column: 26
          }
        }, {
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 114,
            column: 23
          }
        }, {
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 117,
            column: 26
          }
        }, {
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 118,
            column: 23
          }
        }, {
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 119,
            column: 23
          }
        }, {
          start: {
            line: 120,
            column: 16
          },
          end: {
            line: 120,
            column: 23
          }
        }, {
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 123,
            column: 26
          }
        }, {
          start: {
            line: 124,
            column: 16
          },
          end: {
            line: 124,
            column: 23
          }
        }, {
          start: {
            line: 125,
            column: 16
          },
          end: {
            line: 125,
            column: 23
          }
        }, {
          start: {
            line: 126,
            column: 16
          },
          end: {
            line: 126,
            column: 24
          }
        }, {
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 129,
            column: 26
          }
        }, {
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 130,
            column: 24
          }
        }, {
          start: {
            line: 131,
            column: 16
          },
          end: {
            line: 131,
            column: 24
          }
        }, {
          start: {
            line: 132,
            column: 16
          },
          end: {
            line: 132,
            column: 24
          }
        }, {
          start: {
            line: 133,
            column: 16
          },
          end: {
            line: 135,
            column: 26
          }
        }, {
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 136,
            column: 24
          }
        }, {
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 137,
            column: 24
          }
        }, {
          start: {
            line: 138,
            column: 16
          },
          end: {
            line: 140,
            column: 26
          }
        }],
        line: 109
      },
      "15": {
        loc: {
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 170,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 170,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 169
      },
      "16": {
        loc: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "17": {
        loc: {
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 218,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 218,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "18": {
        loc: {
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 213,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 213,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "19": {
        loc: {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 222,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 222,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "20": {
        loc: {
          start: {
            line: 220,
            column: 8
          },
          end: {
            line: 220,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 220,
            column: 8
          },
          end: {
            line: 220,
            column: 29
          }
        }, {
          start: {
            line: 220,
            column: 33
          },
          end: {
            line: 220,
            column: 57
          }
        }],
        line: 220
      },
      "21": {
        loc: {
          start: {
            line: 223,
            column: 4
          },
          end: {
            line: 225,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 4
          },
          end: {
            line: 225,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "22": {
        loc: {
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 223,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 223,
            column: 32
          }
        }, {
          start: {
            line: 223,
            column: 36
          },
          end: {
            line: 223,
            column: 63
          }
        }],
        line: 223
      },
      "23": {
        loc: {
          start: {
            line: 227,
            column: 4
          },
          end: {
            line: 229,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 4
          },
          end: {
            line: 229,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 227
      },
      "24": {
        loc: {
          start: {
            line: 241,
            column: 4
          },
          end: {
            line: 241,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 4
          },
          end: {
            line: 241,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "25": {
        loc: {
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "26": {
        loc: {
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 50
          }
        }, {
          start: {
            line: 244,
            column: 54
          },
          end: {
            line: 244,
            column: 93
          }
        }],
        line: 244
      },
      "27": {
        loc: {
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/input-validation.ts",
      mappings: ";;;;;;;;;;;;;;;;;AAsDA,oCAsCC;AAKD,4CAuDC;AAkDD,8DAqDC;AAOD,4DAaC;AAnRD,2BAAwB;AACxB,8EAA6C;AAE7C;;;GAGG;AAEH,6BAA6B;AAC7B,IAAM,QAAQ,GAAG;IACf,0BAA0B;IAC1B,SAAS,EAAE,6BAA6B;IACxC,8CAA8C;IAC9C,iBAAiB,EAAE,kDAAkD;IACrE,mBAAmB;IACnB,KAAK,EAAE,kDAAkD;IACzD,kBAAkB;IAClB,IAAI,EAAE,4EAA4E;IAClF,iBAAiB;IACjB,GAAG,EAAE,yGAAyG;CAC/G,CAAC;AAEF,yCAAyC;AACzC,IAAM,kBAAkB,GAAG;IACzB,eAAe;IACf,qDAAqD;IACrD,qDAAqD;IACrD,eAAe;IACf,aAAa;IACb,cAAc;IACd,eAAe;IACf,eAAe;IACf,mBAAmB;IAEnB,yBAAyB;IACzB,oEAAoE;IACpE,mBAAmB;IACnB,oCAAoC;IAEpC,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,aAAa;IACb,aAAa;IAEb,8BAA8B;IAC9B,aAAa;IACb,WAAW;IACX,UAAU;CACX,CAAC;AAEF;;GAEG;AACH,SAAgB,YAAY,CAAC,KAAa,EAAE,OAItC;IAJsC,wBAAA,EAAA,YAItC;IACJ,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACxC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,+BAA+B;IAC/B,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC5B,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED,mCAAmC;IACnC,IAAI,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QAC9D,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;IAED,sBAAsB;IACtB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;QACtB,iCAAiC;QACjC,SAAS,GAAG,8BAAS,CAAC,QAAQ,CAAC,SAAS,EAAE;YACxC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACrE,YAAY,EAAE,EAAE;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,sBAAsB;QACtB,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,iDAAiD;IACjD,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC;IAEvE,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAa;IAK5C,IAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,+BAA+B;IAC/B,kBAAkB,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,KAAK;QACxC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACxB,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,CAAC,CAAC;gBACP,KAAK,CAAC;oBACJ,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,CAAC,CAAC;gBACP,KAAK,CAAC;oBACJ,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;oBAC7C,MAAM;gBACR,KAAK,CAAC,CAAC;gBACP,KAAK,CAAC,CAAC;gBACP,KAAK,CAAC,CAAC;gBACP,KAAK,CAAC;oBACJ,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;oBACjD,MAAM;gBACR,KAAK,CAAC,CAAC;gBACP,KAAK,CAAC,CAAC;gBACP,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE;oBACL,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;oBAC/C,MAAM;gBACR,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE;oBACL,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;oBAChD,MAAM;gBACR,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE;oBACL,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;oBAC5C,MAAM;YACV,CAAC;YAED,+BAA+B;YAC/B,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,OAAO,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;QAC7B,OAAO,SAAA;QACP,SAAS,EAAE,YAAY,CAAC,SAAS,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;KAC9D,CAAC;AACJ,CAAC;AAED;;GAEG;AACU,QAAA,0BAA0B,GAAG;IACxC,2BAA2B;IAC3B,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE;SACrB,GAAG,CAAC,EAAE,EAAE,yCAAyC,CAAC;SAClD,GAAG,CAAC,IAAI,EAAE,wCAAwC,CAAC;SACnD,MAAM,CAAC,UAAC,GAAG;QACV,IAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACvC,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC,EAAE,+CAA+C,CAAC;IAErD,wBAAwB;IACxB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;SAClB,GAAG,CAAC,IAAI,EAAE,qCAAqC,CAAC;SAChD,QAAQ,EAAE;SACV,MAAM,CAAC,UAAC,GAAG;QACV,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC;QACtB,IAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACvC,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC,EAAE,2CAA2C,CAAC;IAEjD,mCAAmC;IACnC,aAAa,EAAE,OAAC,CAAC,MAAM,CAAC;QACtB,WAAW,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;QAC3I,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC1C,eAAe,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;QACtF,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC3C,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC5C,aAAa,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE;QACnI,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC9C,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC3D,UAAU,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;QAC1F,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;KACtD,CAAC;IAEF,kBAAkB;IAClB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,cAAc;IAEtD,kBAAkB;IAClB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC;CAC3C,CAAC;AAEF;;GAEG;AACH,SAAgB,yBAAyB,CAAC,IAKzC;IAKC,IAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAM,aAAa,gBAAQ,IAAI,CAAE,CAAC;IAElC,sCAAsC;IACtC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/D,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAChC,MAAM,CAAC,IAAI,OAAX,MAAM,EAAS,kBAAkB,CAAC,OAAO,EAAE;IAC7C,CAAC;IACD,aAAa,CAAC,YAAY,GAAG,YAAY,CAAC,kBAAkB,CAAC,SAAS,EAAE;QACtE,SAAS,EAAE,IAAI;QACf,eAAe,EAAE,IAAI;KACtB,CAAC,CAAC;IAEH,mCAAmC;IACnC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,IAAM,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC7B,MAAM,CAAC,IAAI,OAAX,MAAM,EAAS,eAAe,CAAC,OAAO,EAAE;QAC1C,CAAC;QACD,aAAa,CAAC,SAAS,GAAG,YAAY,CAAC,eAAe,CAAC,SAAS,EAAE;YAChE,SAAS,EAAE,IAAI;YACf,eAAe,EAAE,IAAI;SACtB,CAAC,CAAC;IACL,CAAC;IAED,uBAAuB;IACvB,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IACD,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC;QAC5D,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IAED,gCAAgC;IAChC,IAAI,aAAa,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IAED,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QAC5B,MAAM,QAAA;QACN,aAAa,eAAA;KACd,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAgD,CAAC;AAEnF,SAAgB,wBAAwB,CAAC,UAAkB,EAAE,WAAgB,EAAE,QAAgB;IAAlC,4BAAA,EAAA,gBAAgB;IAAE,yBAAA,EAAA,gBAAgB;IAC7F,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,IAAM,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC;IAE5F,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC1B,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAChB,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,QAAQ,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,KAAK,EAAE,CAAC;IACd,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAE1C,OAAO,KAAK,CAAC,KAAK,IAAI,WAAW,CAAC;AACpC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/input-validation.ts"],
      sourcesContent: ["import { z } from 'zod';\nimport DOMPurify from 'isomorphic-dompurify';\n\n/**\n * Comprehensive input validation and sanitization utilities\n * Addresses security issues identified by testerat\n */\n\n// Common validation patterns\nconst PATTERNS = {\n  // Basic text with no HTML\n  SAFE_TEXT: /^[a-zA-Z0-9\\s\\-_.,!?()'\"]+$/,\n  // Professional text (allows more punctuation)\n  PROFESSIONAL_TEXT: /^[a-zA-Z0-9\\s\\-_.,!?()'\"@#$%&*+=:;/\\\\[\\]{}|~`]+$/,\n  // Email validation\n  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\n  // UUID validation\n  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,\n  // URL validation\n  URL: /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/,\n};\n\n// Malicious patterns to detect and block\nconst MALICIOUS_PATTERNS = [\n  // XSS patterns\n  /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n  /<iframe\\b[^<]*(?:(?!<\\/iframe>)<[^<]*)*<\\/iframe>/gi,\n  /javascript:/gi,\n  /vbscript:/gi,\n  /onload\\s*=/gi,\n  /onerror\\s*=/gi,\n  /onclick\\s*=/gi,\n  /onmouseover\\s*=/gi,\n  \n  // SQL injection patterns\n  /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\\b)/gi,\n  /(--|#|\\/\\*|\\*\\/)/g,\n  /(\\bOR\\b|\\bAND\\b)\\s+\\d+\\s*=\\s*\\d+/gi,\n  \n  // Path traversal patterns\n  /\\.\\.\\//g,\n  /\\.\\.\\\\/g,\n  /%2e%2e%2f/gi,\n  /%2e%2e%5c/gi,\n  \n  // Template injection patterns\n  /\\{\\{.*\\}\\}/g,\n  /\\$\\{.*\\}/g,\n  /%\\{.*\\}/g,\n];\n\n/**\n * Sanitize text input by removing/escaping dangerous content\n */\nexport function sanitizeText(input: string, options: {\n  allowHtml?: boolean;\n  maxLength?: number;\n  stripWhitespace?: boolean;\n} = {}): string {\n  if (!input || typeof input !== 'string') {\n    return '';\n  }\n\n  let sanitized = input;\n\n  // Trim whitespace if requested\n  if (options.stripWhitespace) {\n    sanitized = sanitized.trim();\n  }\n\n  // Truncate if max length specified\n  if (options.maxLength && sanitized.length > options.maxLength) {\n    sanitized = sanitized.substring(0, options.maxLength);\n  }\n\n  // Handle HTML content\n  if (options.allowHtml) {\n    // Use DOMPurify to sanitize HTML\n    sanitized = DOMPurify.sanitize(sanitized, {\n      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],\n      ALLOWED_ATTR: [],\n      KEEP_CONTENT: true,\n    });\n  } else {\n    // Strip all HTML tags\n    sanitized = sanitized.replace(/<[^>]*>/g, '');\n  }\n\n  // Remove null bytes and other control characters\n  sanitized = sanitized.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]/g, '');\n\n  return sanitized;\n}\n\n/**\n * Validate input against malicious patterns\n */\nexport function validateSecurity(input: string): {\n  isValid: boolean;\n  threats: string[];\n  sanitized: string;\n} {\n  const threats: string[] = [];\n  let sanitized = input;\n\n  // Check for malicious patterns\n  MALICIOUS_PATTERNS.forEach((pattern, index) => {\n    if (pattern.test(input)) {\n      switch (index) {\n        case 0:\n        case 1:\n          threats.push('XSS script injection detected');\n          break;\n        case 2:\n        case 3:\n          threats.push('JavaScript protocol detected');\n          break;\n        case 4:\n        case 5:\n        case 6:\n        case 7:\n          threats.push('Event handler injection detected');\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n          threats.push('SQL injection pattern detected');\n          break;\n        case 12:\n        case 13:\n        case 14:\n        case 15:\n          threats.push('Path traversal attempt detected');\n          break;\n        case 16:\n        case 17:\n        case 18:\n          threats.push('Template injection detected');\n          break;\n      }\n      \n      // Remove the malicious content\n      sanitized = sanitized.replace(pattern, '');\n    }\n  });\n\n  return {\n    isValid: threats.length === 0,\n    threats,\n    sanitized: sanitizeText(sanitized, { stripWhitespace: true }),\n  };\n}\n\n/**\n * Interview practice specific validation schemas\n */\nexport const InterviewValidationSchemas = {\n  // Response text validation\n  responseText: z.string()\n    .min(10, 'Response must be at least 10 characters')\n    .max(5000, 'Response cannot exceed 5000 characters')\n    .refine((val) => {\n      const security = validateSecurity(val);\n      return security.isValid;\n    }, 'Response contains potentially harmful content'),\n\n  // User notes validation\n  userNotes: z.string()\n    .max(1000, 'Notes cannot exceed 1000 characters')\n    .optional()\n    .refine((val) => {\n      if (!val) return true;\n      const security = validateSecurity(val);\n      return security.isValid;\n    }, 'Notes contain potentially harmful content'),\n\n  // Session configuration validation\n  sessionConfig: z.object({\n    sessionType: z.enum(['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION']),\n    careerPath: z.string().max(100).optional(),\n    experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),\n    companyType: z.string().max(100).optional(),\n    industryFocus: z.string().max(100).optional(),\n    specificRole: z.string().max(100).optional(),\n    interviewType: z.enum(['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY']).optional(),\n    preparationTime: z.string().max(50).optional(),\n    focusAreas: z.array(z.string().max(100)).max(10).optional(),\n    difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).default('BEGINNER'),\n    totalQuestions: z.number().min(1).max(50).default(10),\n  }),\n\n  // Time validation\n  timeValue: z.number().min(0).max(7200), // Max 2 hours\n\n  // UUID validation\n  uuid: z.string().uuid('Invalid ID format'),\n};\n\n/**\n * Sanitize and validate interview response\n */\nexport function validateInterviewResponse(data: {\n  responseText: string;\n  userNotes?: string;\n  responseTime: number;\n  preparationTime: number;\n}): {\n  isValid: boolean;\n  errors: string[];\n  sanitizedData: typeof data;\n} {\n  const errors: string[] = [];\n  const sanitizedData = { ...data };\n\n  // Validate and sanitize response text\n  const responseValidation = validateSecurity(data.responseText);\n  if (!responseValidation.isValid) {\n    errors.push(...responseValidation.threats);\n  }\n  sanitizedData.responseText = sanitizeText(responseValidation.sanitized, {\n    maxLength: 5000,\n    stripWhitespace: true,\n  });\n\n  // Validate and sanitize user notes\n  if (data.userNotes) {\n    const notesValidation = validateSecurity(data.userNotes);\n    if (!notesValidation.isValid) {\n      errors.push(...notesValidation.threats);\n    }\n    sanitizedData.userNotes = sanitizeText(notesValidation.sanitized, {\n      maxLength: 1000,\n      stripWhitespace: true,\n    });\n  }\n\n  // Validate time values\n  if (data.responseTime < 0 || data.responseTime > 7200) {\n    errors.push('Invalid response time');\n  }\n  if (data.preparationTime < 0 || data.preparationTime > 1800) {\n    errors.push('Invalid preparation time');\n  }\n\n  // Check minimum response length\n  if (sanitizedData.responseText.length < 10) {\n    errors.push('Response is too short');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n    sanitizedData,\n  };\n}\n\n/**\n * Rate limiting for input validation\n */\nconst validationAttempts = new Map<string, { count: number; resetTime: number }>();\n\nexport function checkValidationRateLimit(identifier: string, maxAttempts = 10, windowMs = 60000): boolean {\n  const now = Date.now();\n  const entry = validationAttempts.get(identifier) || { count: 0, resetTime: now + windowMs };\n\n  if (now > entry.resetTime) {\n    entry.count = 0;\n    entry.resetTime = now + windowMs;\n  }\n\n  entry.count++;\n  validationAttempts.set(identifier, entry);\n\n  return entry.count <= maxAttempts;\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "17c9f0ede884c480d470f898a9b6b3b4ed4ba361"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2mn8ldl78g = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2mn8ldl78g();
var __assign =
/* istanbul ignore next */
(cov_2mn8ldl78g().s[0]++,
/* istanbul ignore next */
(cov_2mn8ldl78g().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2mn8ldl78g().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_2mn8ldl78g().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_2mn8ldl78g().f[0]++;
  cov_2mn8ldl78g().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().f[1]++;
    cov_2mn8ldl78g().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_2mn8ldl78g().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_2mn8ldl78g().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_2mn8ldl78g().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_2mn8ldl78g().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_2mn8ldl78g().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[2][0]++;
          cov_2mn8ldl78g().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_2mn8ldl78g().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_2mn8ldl78g().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[10]++;
  return __assign.apply(this, arguments);
}));
var __importDefault =
/* istanbul ignore next */
(cov_2mn8ldl78g().s[11]++,
/* istanbul ignore next */
(cov_2mn8ldl78g().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_2mn8ldl78g().b[3][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2mn8ldl78g().b[3][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2mn8ldl78g().f[2]++;
  cov_2mn8ldl78g().s[12]++;
  return /* istanbul ignore next */(cov_2mn8ldl78g().b[5][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[5][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[4][0]++, mod) :
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[4][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2mn8ldl78g().s[13]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2mn8ldl78g().s[14]++;
exports.InterviewValidationSchemas = void 0;
/* istanbul ignore next */
cov_2mn8ldl78g().s[15]++;
exports.sanitizeText = sanitizeText;
/* istanbul ignore next */
cov_2mn8ldl78g().s[16]++;
exports.validateSecurity = validateSecurity;
/* istanbul ignore next */
cov_2mn8ldl78g().s[17]++;
exports.validateInterviewResponse = validateInterviewResponse;
/* istanbul ignore next */
cov_2mn8ldl78g().s[18]++;
exports.checkValidationRateLimit = checkValidationRateLimit;
var zod_1 =
/* istanbul ignore next */
(cov_2mn8ldl78g().s[19]++, require("zod"));
var isomorphic_dompurify_1 =
/* istanbul ignore next */
(cov_2mn8ldl78g().s[20]++, __importDefault(require("isomorphic-dompurify")));
/**
 * Comprehensive input validation and sanitization utilities
 * Addresses security issues identified by testerat
 */
// Common validation patterns
var PATTERNS =
/* istanbul ignore next */
(cov_2mn8ldl78g().s[21]++, {
  // Basic text with no HTML
  SAFE_TEXT: /^[a-zA-Z0-9\s\-_.,!?()'"]+$/,
  // Professional text (allows more punctuation)
  PROFESSIONAL_TEXT: /^[a-zA-Z0-9\s\-_.,!?()'"@#$%&*+=:;/\\[\]{}|~`]+$/,
  // Email validation
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  // UUID validation
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  // URL validation
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
});
// Malicious patterns to detect and block
var MALICIOUS_PATTERNS =
/* istanbul ignore next */
(cov_2mn8ldl78g().s[22]++, [
// XSS patterns
/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, /javascript:/gi, /vbscript:/gi, /onload\s*=/gi, /onerror\s*=/gi, /onclick\s*=/gi, /onmouseover\s*=/gi,
// SQL injection patterns
/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi, /(--|#|\/\*|\*\/)/g, /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
// Path traversal patterns
/\.\.\//g, /\.\.\\/g, /%2e%2e%2f/gi, /%2e%2e%5c/gi,
// Template injection patterns
/\{\{.*\}\}/g, /\$\{.*\}/g, /%\{.*\}/g]);
/**
 * Sanitize text input by removing/escaping dangerous content
 */
function sanitizeText(input, options) {
  /* istanbul ignore next */
  cov_2mn8ldl78g().f[3]++;
  cov_2mn8ldl78g().s[23]++;
  if (options === void 0) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[6][0]++;
    cov_2mn8ldl78g().s[24]++;
    options = {};
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[6][1]++;
  }
  cov_2mn8ldl78g().s[25]++;
  if (
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[8][0]++, !input) ||
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[8][1]++, typeof input !== 'string')) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[7][0]++;
    cov_2mn8ldl78g().s[26]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[7][1]++;
  }
  var sanitized =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[27]++, input);
  // Trim whitespace if requested
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[28]++;
  if (options.stripWhitespace) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[9][0]++;
    cov_2mn8ldl78g().s[29]++;
    sanitized = sanitized.trim();
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[9][1]++;
  }
  // Truncate if max length specified
  cov_2mn8ldl78g().s[30]++;
  if (
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[11][0]++, options.maxLength) &&
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[11][1]++, sanitized.length > options.maxLength)) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[10][0]++;
    cov_2mn8ldl78g().s[31]++;
    sanitized = sanitized.substring(0, options.maxLength);
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[10][1]++;
  }
  // Handle HTML content
  cov_2mn8ldl78g().s[32]++;
  if (options.allowHtml) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[12][0]++;
    cov_2mn8ldl78g().s[33]++;
    // Use DOMPurify to sanitize HTML
    sanitized = isomorphic_dompurify_1.default.sanitize(sanitized, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true
    });
  } else {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[12][1]++;
    cov_2mn8ldl78g().s[34]++;
    // Strip all HTML tags
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  }
  // Remove null bytes and other control characters
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[35]++;
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[36]++;
  return sanitized;
}
/**
 * Validate input against malicious patterns
 */
function validateSecurity(input) {
  /* istanbul ignore next */
  cov_2mn8ldl78g().f[4]++;
  var threats =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[37]++, []);
  var sanitized =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[38]++, input);
  // Check for malicious patterns
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[39]++;
  MALICIOUS_PATTERNS.forEach(function (pattern, index) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().f[5]++;
    cov_2mn8ldl78g().s[40]++;
    if (pattern.test(input)) {
      /* istanbul ignore next */
      cov_2mn8ldl78g().b[13][0]++;
      cov_2mn8ldl78g().s[41]++;
      switch (index) {
        case 0:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][0]++;
        case 1:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][1]++;
          cov_2mn8ldl78g().s[42]++;
          threats.push('XSS script injection detected');
          /* istanbul ignore next */
          cov_2mn8ldl78g().s[43]++;
          break;
        case 2:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][2]++;
        case 3:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][3]++;
          cov_2mn8ldl78g().s[44]++;
          threats.push('JavaScript protocol detected');
          /* istanbul ignore next */
          cov_2mn8ldl78g().s[45]++;
          break;
        case 4:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][4]++;
        case 5:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][5]++;
        case 6:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][6]++;
        case 7:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][7]++;
          cov_2mn8ldl78g().s[46]++;
          threats.push('Event handler injection detected');
          /* istanbul ignore next */
          cov_2mn8ldl78g().s[47]++;
          break;
        case 8:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][8]++;
        case 9:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][9]++;
        case 10:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][10]++;
        case 11:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][11]++;
          cov_2mn8ldl78g().s[48]++;
          threats.push('SQL injection pattern detected');
          /* istanbul ignore next */
          cov_2mn8ldl78g().s[49]++;
          break;
        case 12:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][12]++;
        case 13:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][13]++;
        case 14:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][14]++;
        case 15:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][15]++;
          cov_2mn8ldl78g().s[50]++;
          threats.push('Path traversal attempt detected');
          /* istanbul ignore next */
          cov_2mn8ldl78g().s[51]++;
          break;
        case 16:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][16]++;
        case 17:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][17]++;
        case 18:
          /* istanbul ignore next */
          cov_2mn8ldl78g().b[14][18]++;
          cov_2mn8ldl78g().s[52]++;
          threats.push('Template injection detected');
          /* istanbul ignore next */
          cov_2mn8ldl78g().s[53]++;
          break;
      }
      // Remove the malicious content
      /* istanbul ignore next */
      cov_2mn8ldl78g().s[54]++;
      sanitized = sanitized.replace(pattern, '');
    } else
    /* istanbul ignore next */
    {
      cov_2mn8ldl78g().b[13][1]++;
    }
  });
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[55]++;
  return {
    isValid: threats.length === 0,
    threats: threats,
    sanitized: sanitizeText(sanitized, {
      stripWhitespace: true
    })
  };
}
/**
 * Interview practice specific validation schemas
 */
/* istanbul ignore next */
cov_2mn8ldl78g().s[56]++;
exports.InterviewValidationSchemas = {
  // Response text validation
  responseText: zod_1.z.string().min(10, 'Response must be at least 10 characters').max(5000, 'Response cannot exceed 5000 characters').refine(function (val) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().f[6]++;
    var security =
    /* istanbul ignore next */
    (cov_2mn8ldl78g().s[57]++, validateSecurity(val));
    /* istanbul ignore next */
    cov_2mn8ldl78g().s[58]++;
    return security.isValid;
  }, 'Response contains potentially harmful content'),
  // User notes validation
  userNotes: zod_1.z.string().max(1000, 'Notes cannot exceed 1000 characters').optional().refine(function (val) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().f[7]++;
    cov_2mn8ldl78g().s[59]++;
    if (!val) {
      /* istanbul ignore next */
      cov_2mn8ldl78g().b[15][0]++;
      cov_2mn8ldl78g().s[60]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_2mn8ldl78g().b[15][1]++;
    }
    var security =
    /* istanbul ignore next */
    (cov_2mn8ldl78g().s[61]++, validateSecurity(val));
    /* istanbul ignore next */
    cov_2mn8ldl78g().s[62]++;
    return security.isValid;
  }, 'Notes contain potentially harmful content'),
  // Session configuration validation
  sessionConfig: zod_1.z.object({
    sessionType: zod_1.z.enum(['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION']),
    careerPath: zod_1.z.string().max(100).optional(),
    experienceLevel: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
    companyType: zod_1.z.string().max(100).optional(),
    industryFocus: zod_1.z.string().max(100).optional(),
    specificRole: zod_1.z.string().max(100).optional(),
    interviewType: zod_1.z.enum(['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY']).optional(),
    preparationTime: zod_1.z.string().max(50).optional(),
    focusAreas: zod_1.z.array(zod_1.z.string().max(100)).max(10).optional(),
    difficulty: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).default('BEGINNER'),
    totalQuestions: zod_1.z.number().min(1).max(50).default(10)
  }),
  // Time validation
  timeValue: zod_1.z.number().min(0).max(7200),
  // Max 2 hours
  // UUID validation
  uuid: zod_1.z.string().uuid('Invalid ID format')
};
/**
 * Sanitize and validate interview response
 */
function validateInterviewResponse(data) {
  /* istanbul ignore next */
  cov_2mn8ldl78g().f[8]++;
  var errors =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[63]++, []);
  var sanitizedData =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[64]++, __assign({}, data));
  // Validate and sanitize response text
  var responseValidation =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[65]++, validateSecurity(data.responseText));
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[66]++;
  if (!responseValidation.isValid) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[16][0]++;
    cov_2mn8ldl78g().s[67]++;
    errors.push.apply(errors, responseValidation.threats);
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[16][1]++;
  }
  cov_2mn8ldl78g().s[68]++;
  sanitizedData.responseText = sanitizeText(responseValidation.sanitized, {
    maxLength: 5000,
    stripWhitespace: true
  });
  // Validate and sanitize user notes
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[69]++;
  if (data.userNotes) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[17][0]++;
    var notesValidation =
    /* istanbul ignore next */
    (cov_2mn8ldl78g().s[70]++, validateSecurity(data.userNotes));
    /* istanbul ignore next */
    cov_2mn8ldl78g().s[71]++;
    if (!notesValidation.isValid) {
      /* istanbul ignore next */
      cov_2mn8ldl78g().b[18][0]++;
      cov_2mn8ldl78g().s[72]++;
      errors.push.apply(errors, notesValidation.threats);
    } else
    /* istanbul ignore next */
    {
      cov_2mn8ldl78g().b[18][1]++;
    }
    cov_2mn8ldl78g().s[73]++;
    sanitizedData.userNotes = sanitizeText(notesValidation.sanitized, {
      maxLength: 1000,
      stripWhitespace: true
    });
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[17][1]++;
  }
  // Validate time values
  cov_2mn8ldl78g().s[74]++;
  if (
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[20][0]++, data.responseTime < 0) ||
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[20][1]++, data.responseTime > 7200)) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[19][0]++;
    cov_2mn8ldl78g().s[75]++;
    errors.push('Invalid response time');
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[19][1]++;
  }
  cov_2mn8ldl78g().s[76]++;
  if (
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[22][0]++, data.preparationTime < 0) ||
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[22][1]++, data.preparationTime > 1800)) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[21][0]++;
    cov_2mn8ldl78g().s[77]++;
    errors.push('Invalid preparation time');
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[21][1]++;
  }
  // Check minimum response length
  cov_2mn8ldl78g().s[78]++;
  if (sanitizedData.responseText.length < 10) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[23][0]++;
    cov_2mn8ldl78g().s[79]++;
    errors.push('Response is too short');
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[23][1]++;
  }
  cov_2mn8ldl78g().s[80]++;
  return {
    isValid: errors.length === 0,
    errors: errors,
    sanitizedData: sanitizedData
  };
}
/**
 * Rate limiting for input validation
 */
var validationAttempts =
/* istanbul ignore next */
(cov_2mn8ldl78g().s[81]++, new Map());
function checkValidationRateLimit(identifier, maxAttempts, windowMs) {
  /* istanbul ignore next */
  cov_2mn8ldl78g().f[9]++;
  cov_2mn8ldl78g().s[82]++;
  if (maxAttempts === void 0) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[24][0]++;
    cov_2mn8ldl78g().s[83]++;
    maxAttempts = 10;
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[24][1]++;
  }
  cov_2mn8ldl78g().s[84]++;
  if (windowMs === void 0) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[25][0]++;
    cov_2mn8ldl78g().s[85]++;
    windowMs = 60000;
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[25][1]++;
  }
  var now =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[86]++, Date.now());
  var entry =
  /* istanbul ignore next */
  (cov_2mn8ldl78g().s[87]++,
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[26][0]++, validationAttempts.get(identifier)) ||
  /* istanbul ignore next */
  (cov_2mn8ldl78g().b[26][1]++, {
    count: 0,
    resetTime: now + windowMs
  }));
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[88]++;
  if (now > entry.resetTime) {
    /* istanbul ignore next */
    cov_2mn8ldl78g().b[27][0]++;
    cov_2mn8ldl78g().s[89]++;
    entry.count = 0;
    /* istanbul ignore next */
    cov_2mn8ldl78g().s[90]++;
    entry.resetTime = now + windowMs;
  } else
  /* istanbul ignore next */
  {
    cov_2mn8ldl78g().b[27][1]++;
  }
  cov_2mn8ldl78g().s[91]++;
  entry.count++;
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[92]++;
  validationAttempts.set(identifier, entry);
  /* istanbul ignore next */
  cov_2mn8ldl78g().s[93]++;
  return entry.count <= maxAttempts;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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