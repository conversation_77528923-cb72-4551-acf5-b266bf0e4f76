47fbc0636d590b0cbf67669bf6631bda
"use strict";

/* istanbul ignore next */
function cov_2763i30p9o() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/steps/[stepId]/progress/route.ts";
  var hash = "74ef418bf282f4f7cd38f087774a3c21336be043";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/steps/[stepId]/progress/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "80": {
        start: {
          line: 51,
          column: 15
        },
        end: {
          line: 51,
          column: 37
        }
      },
      "81": {
        start: {
          line: 52,
          column: 18
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "82": {
        start: {
          line: 53,
          column: 13
        },
        end: {
          line: 53,
          column: 34
        }
      },
      "83": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 38
        }
      },
      "84": {
        start: {
          line: 55,
          column: 34
        },
        end: {
          line: 55,
          column: 76
        }
      },
      "85": {
        start: {
          line: 56,
          column: 18
        },
        end: {
          line: 56,
          column: 44
        }
      },
      "86": {
        start: {
          line: 57,
          column: 35
        },
        end: {
          line: 57,
          column: 87
        }
      },
      "87": {
        start: {
          line: 58,
          column: 35
        },
        end: {
          line: 58,
          column: 87
        }
      },
      "88": {
        start: {
          line: 59,
          column: 35
        },
        end: {
          line: 59,
          column: 87
        }
      },
      "89": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 60,
          column: 26
        }
      },
      "90": {
        start: {
          line: 61,
          column: 27
        },
        end: {
          line: 66,
          column: 2
        }
      },
      "91": {
        start: {
          line: 68,
          column: 0
        },
        end: {
          line: 335,
          column: 7
        }
      },
      "92": {
        start: {
          line: 68,
          column: 99
        },
        end: {
          line: 335,
          column: 3
        }
      },
      "93": {
        start: {
          line: 69,
          column: 17
        },
        end: {
          line: 69,
          column: 26
        }
      },
      "94": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 334,
          column: 7
        }
      },
      "95": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 333,
          column: 20
        }
      },
      "96": {
        start: {
          line: 72,
          column: 26
        },
        end: {
          line: 333,
          column: 15
        }
      },
      "97": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 332,
          column: 19
        }
      },
      "98": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 331,
          column: 21
        }
      },
      "99": {
        start: {
          line: 77,
          column: 32
        },
        end: {
          line: 77,
          column: 108
        }
      },
      "100": {
        start: {
          line: 79,
          column: 28
        },
        end: {
          line: 79,
          column: 48
        }
      },
      "101": {
        start: {
          line: 80,
          column: 28
        },
        end: {
          line: 82,
          column: 29
        }
      },
      "102": {
        start: {
          line: 81,
          column: 32
        },
        end: {
          line: 81,
          column: 153
        }
      },
      "103": {
        start: {
          line: 83,
          column: 28
        },
        end: {
          line: 83,
          column: 53
        }
      },
      "104": {
        start: {
          line: 84,
          column: 28
        },
        end: {
          line: 84,
          column: 57
        }
      },
      "105": {
        start: {
          line: 86,
          column: 28
        },
        end: {
          line: 86,
          column: 87
        }
      },
      "106": {
        start: {
          line: 87,
          column: 28
        },
        end: {
          line: 87,
          column: 65
        }
      },
      "107": {
        start: {
          line: 89,
          column: 28
        },
        end: {
          line: 89,
          column: 45
        }
      },
      "108": {
        start: {
          line: 90,
          column: 28
        },
        end: {
          line: 90,
          column: 78
        }
      },
      "109": {
        start: {
          line: 91,
          column: 28
        },
        end: {
          line: 97,
          column: 29
        }
      },
      "110": {
        start: {
          line: 92,
          column: 32
        },
        end: {
          line: 96,
          column: 57
        }
      },
      "111": {
        start: {
          line: 98,
          column: 28
        },
        end: {
          line: 98,
          column: 131
        }
      },
      "112": {
        start: {
          line: 99,
          column: 28
        },
        end: {
          line: 106,
          column: 36
        }
      },
      "113": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 108,
          column: 51
        }
      },
      "114": {
        start: {
          line: 109,
          column: 28
        },
        end: {
          line: 111,
          column: 29
        }
      },
      "115": {
        start: {
          line: 110,
          column: 32
        },
        end: {
          line: 110,
          column: 164
        }
      },
      "116": {
        start: {
          line: 112,
          column: 28
        },
        end: {
          line: 119,
          column: 36
        }
      },
      "117": {
        start: {
          line: 121,
          column: 28
        },
        end: {
          line: 121,
          column: 45
        }
      },
      "118": {
        start: {
          line: 122,
          column: 28
        },
        end: {
          line: 124,
          column: 29
        }
      },
      "119": {
        start: {
          line: 123,
          column: 32
        },
        end: {
          line: 123,
          column: 166
        }
      },
      "120": {
        start: {
          line: 125,
          column: 28
        },
        end: {
          line: 132,
          column: 36
        }
      },
      "121": {
        start: {
          line: 134,
          column: 28
        },
        end: {
          line: 134,
          column: 56
        }
      },
      "122": {
        start: {
          line: 135,
          column: 28
        },
        end: {
          line: 137,
          column: 29
        }
      },
      "123": {
        start: {
          line: 136,
          column: 32
        },
        end: {
          line: 136,
          column: 155
        }
      },
      "124": {
        start: {
          line: 138,
          column: 28
        },
        end: {
          line: 138,
          column: 259
        }
      },
      "125": {
        start: {
          line: 140,
          column: 28
        },
        end: {
          line: 146,
          column: 29
        }
      },
      "126": {
        start: {
          line: 141,
          column: 32
        },
        end: {
          line: 141,
          column: 66
        }
      },
      "127": {
        start: {
          line: 143,
          column: 33
        },
        end: {
          line: 146,
          column: 29
        }
      },
      "128": {
        start: {
          line: 144,
          column: 32
        },
        end: {
          line: 144,
          column: 68
        }
      },
      "129": {
        start: {
          line: 145,
          column: 32
        },
        end: {
          line: 145,
          column: 83
        }
      },
      "130": {
        start: {
          line: 147,
          column: 28
        },
        end: {
          line: 155,
          column: 36
        }
      },
      "131": {
        start: {
          line: 157,
          column: 28
        },
        end: {
          line: 157,
          column: 56
        }
      },
      "132": {
        start: {
          line: 158,
          column: 28
        },
        end: {
          line: 163,
          column: 36
        }
      },
      "133": {
        start: {
          line: 165,
          column: 28
        },
        end: {
          line: 165,
          column: 56
        }
      },
      "134": {
        start: {
          line: 166,
          column: 28
        },
        end: {
          line: 166,
          column: 126
        }
      },
      "135": {
        start: {
          line: 166,
          column: 83
        },
        end: {
          line: 166,
          column: 115
        }
      },
      "136": {
        start: {
          line: 167,
          column: 28
        },
        end: {
          line: 167,
          column: 64
        }
      },
      "137": {
        start: {
          line: 168,
          column: 28
        },
        end: {
          line: 168,
          column: 115
        }
      },
      "138": {
        start: {
          line: 169,
          column: 28
        },
        end: {
          line: 169,
          column: 120
        }
      },
      "139": {
        start: {
          line: 169,
          column: 88
        },
        end: {
          line: 169,
          column: 113
        }
      },
      "140": {
        start: {
          line: 170,
          column: 28
        },
        end: {
          line: 170,
          column: 59
        }
      },
      "141": {
        start: {
          line: 171,
          column: 28
        },
        end: {
          line: 176,
          column: 29
        }
      },
      "142": {
        start: {
          line: 172,
          column: 32
        },
        end: {
          line: 172,
          column: 57
        }
      },
      "143": {
        start: {
          line: 174,
          column: 33
        },
        end: {
          line: 176,
          column: 29
        }
      },
      "144": {
        start: {
          line: 174,
          column: 95
        },
        end: {
          line: 174,
          column: 129
        }
      },
      "145": {
        start: {
          line: 175,
          column: 32
        },
        end: {
          line: 175,
          column: 59
        }
      },
      "146": {
        start: {
          line: 177,
          column: 28
        },
        end: {
          line: 177,
          column: 69
        }
      },
      "147": {
        start: {
          line: 178,
          column: 28
        },
        end: {
          line: 178,
          column: 84
        }
      },
      "148": {
        start: {
          line: 178,
          column: 59
        },
        end: {
          line: 178,
          column: 84
        }
      },
      "149": {
        start: {
          line: 179,
          column: 28
        },
        end: {
          line: 185,
          column: 36
        }
      },
      "150": {
        start: {
          line: 187,
          column: 28
        },
        end: {
          line: 187,
          column: 49
        }
      },
      "151": {
        start: {
          line: 188,
          column: 28
        },
        end: {
          line: 188,
          column: 118
        }
      },
      "152": {
        start: {
          line: 189,
          column: 28
        },
        end: {
          line: 189,
          column: 42
        }
      },
      "153": {
        start: {
          line: 190,
          column: 33
        },
        end: {
          line: 202,
          column: 32
        }
      },
      "154": {
        start: {
          line: 204,
          column: 28
        },
        end: {
          line: 204,
          column: 58
        }
      },
      "155": {
        start: {
          line: 205,
          column: 28
        },
        end: {
          line: 205,
          column: 47
        }
      },
      "156": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 206,
          column: 55
        }
      },
      "157": {
        start: {
          line: 207,
          column: 28
        },
        end: {
          line: 228,
          column: 36
        }
      },
      "158": {
        start: {
          line: 230,
          column: 28
        },
        end: {
          line: 230,
          column: 38
        }
      },
      "159": {
        start: {
          line: 231,
          column: 28
        },
        end: {
          line: 231,
          column: 105
        }
      },
      "160": {
        start: {
          line: 231,
          column: 80
        },
        end: {
          line: 231,
          column: 105
        }
      },
      "161": {
        start: {
          line: 232,
          column: 28
        },
        end: {
          line: 237,
          column: 36
        }
      },
      "162": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 239,
          column: 53
        }
      },
      "163": {
        start: {
          line: 240,
          column: 28
        },
        end: {
          line: 240,
          column: 148
        }
      },
      "164": {
        start: {
          line: 240,
          column: 123
        },
        end: {
          line: 240,
          column: 148
        }
      },
      "165": {
        start: {
          line: 241,
          column: 28
        },
        end: {
          line: 241,
          column: 61
        }
      },
      "166": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 242,
          column: 42
        }
      },
      "167": {
        start: {
          line: 244,
          column: 28
        },
        end: {
          line: 244,
          column: 76
        }
      },
      "168": {
        start: {
          line: 244,
          column: 51
        },
        end: {
          line: 244,
          column: 76
        }
      },
      "169": {
        start: {
          line: 245,
          column: 28
        },
        end: {
          line: 245,
          column: 43
        }
      },
      "170": {
        start: {
          line: 246,
          column: 28
        },
        end: {
          line: 270,
          column: 36
        }
      },
      "171": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 272,
          column: 38
        }
      },
      "172": {
        start: {
          line: 273,
          column: 28
        },
        end: {
          line: 273,
          column: 42
        }
      },
      "173": {
        start: {
          line: 275,
          column: 28
        },
        end: {
          line: 275,
          column: 33
        }
      },
      "174": {
        start: {
          line: 276,
          column: 28
        },
        end: {
          line: 276,
          column: 53
        }
      },
      "175": {
        start: {
          line: 278,
          column: 28
        },
        end: {
          line: 278,
          column: 57
        }
      },
      "176": {
        start: {
          line: 279,
          column: 28
        },
        end: {
          line: 279,
          column: 103
        }
      },
      "177": {
        start: {
          line: 280,
          column: 28
        },
        end: {
          line: 280,
          column: 127
        }
      },
      "178": {
        start: {
          line: 282,
          column: 28
        },
        end: {
          line: 287,
          column: 36
        }
      },
      "179": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 38
        }
      },
      "180": {
        start: {
          line: 291,
          column: 28
        },
        end: {
          line: 291,
          column: 105
        }
      },
      "181": {
        start: {
          line: 291,
          column: 80
        },
        end: {
          line: 291,
          column: 105
        }
      },
      "182": {
        start: {
          line: 292,
          column: 28
        },
        end: {
          line: 295,
          column: 36
        }
      },
      "183": {
        start: {
          line: 297,
          column: 28
        },
        end: {
          line: 297,
          column: 53
        }
      },
      "184": {
        start: {
          line: 298,
          column: 28
        },
        end: {
          line: 298,
          column: 148
        }
      },
      "185": {
        start: {
          line: 298,
          column: 123
        },
        end: {
          line: 298,
          column: 148
        }
      },
      "186": {
        start: {
          line: 299,
          column: 28
        },
        end: {
          line: 299,
          column: 61
        }
      },
      "187": {
        start: {
          line: 300,
          column: 28
        },
        end: {
          line: 300,
          column: 42
        }
      },
      "188": {
        start: {
          line: 302,
          column: 28
        },
        end: {
          line: 302,
          column: 76
        }
      },
      "189": {
        start: {
          line: 302,
          column: 51
        },
        end: {
          line: 302,
          column: 76
        }
      },
      "190": {
        start: {
          line: 303,
          column: 28
        },
        end: {
          line: 303,
          column: 43
        }
      },
      "191": {
        start: {
          line: 304,
          column: 28
        },
        end: {
          line: 304,
          column: 115
        }
      },
      "192": {
        start: {
          line: 306,
          column: 28
        },
        end: {
          line: 306,
          column: 38
        }
      },
      "193": {
        start: {
          line: 307,
          column: 28
        },
        end: {
          line: 307,
          column: 42
        }
      },
      "194": {
        start: {
          line: 309,
          column: 28
        },
        end: {
          line: 309,
          column: 33
        }
      },
      "195": {
        start: {
          line: 310,
          column: 28
        },
        end: {
          line: 310,
          column: 53
        }
      },
      "196": {
        start: {
          line: 311,
          column: 33
        },
        end: {
          line: 311,
          column: 58
        }
      },
      "197": {
        start: {
          line: 313,
          column: 28
        },
        end: {
          line: 313,
          column: 53
        }
      },
      "198": {
        start: {
          line: 314,
          column: 28
        },
        end: {
          line: 314,
          column: 110
        }
      },
      "199": {
        start: {
          line: 315,
          column: 28
        },
        end: {
          line: 315,
          column: 53
        }
      },
      "200": {
        start: {
          line: 316,
          column: 33
        },
        end: {
          line: 330,
          column: 32
        }
      },
      "201": {
        start: {
          line: 337,
          column: 0
        },
        end: {
          line: 409,
          column: 7
        }
      },
      "202": {
        start: {
          line: 337,
          column: 99
        },
        end: {
          line: 409,
          column: 3
        }
      },
      "203": {
        start: {
          line: 338,
          column: 17
        },
        end: {
          line: 338,
          column: 26
        }
      },
      "204": {
        start: {
          line: 339,
          column: 4
        },
        end: {
          line: 408,
          column: 7
        }
      },
      "205": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 407,
          column: 20
        }
      },
      "206": {
        start: {
          line: 341,
          column: 26
        },
        end: {
          line: 407,
          column: 15
        }
      },
      "207": {
        start: {
          line: 344,
          column: 16
        },
        end: {
          line: 406,
          column: 19
        }
      },
      "208": {
        start: {
          line: 345,
          column: 20
        },
        end: {
          line: 405,
          column: 21
        }
      },
      "209": {
        start: {
          line: 346,
          column: 32
        },
        end: {
          line: 346,
          column: 108
        }
      },
      "210": {
        start: {
          line: 348,
          column: 28
        },
        end: {
          line: 348,
          column: 48
        }
      },
      "211": {
        start: {
          line: 349,
          column: 28
        },
        end: {
          line: 351,
          column: 29
        }
      },
      "212": {
        start: {
          line: 350,
          column: 32
        },
        end: {
          line: 350,
          column: 153
        }
      },
      "213": {
        start: {
          line: 352,
          column: 28
        },
        end: {
          line: 352,
          column: 53
        }
      },
      "214": {
        start: {
          line: 353,
          column: 28
        },
        end: {
          line: 353,
          column: 57
        }
      },
      "215": {
        start: {
          line: 355,
          column: 28
        },
        end: {
          line: 355,
          column: 87
        }
      },
      "216": {
        start: {
          line: 356,
          column: 28
        },
        end: {
          line: 356,
          column: 91
        }
      },
      "217": {
        start: {
          line: 357,
          column: 28
        },
        end: {
          line: 357,
          column: 111
        }
      },
      "218": {
        start: {
          line: 359,
          column: 28
        },
        end: {
          line: 359,
          column: 47
        }
      },
      "219": {
        start: {
          line: 360,
          column: 28
        },
        end: {
          line: 366,
          column: 29
        }
      },
      "220": {
        start: {
          line: 361,
          column: 32
        },
        end: {
          line: 365,
          column: 40
        }
      },
      "221": {
        start: {
          line: 367,
          column: 28
        },
        end: {
          line: 390,
          column: 36
        }
      },
      "222": {
        start: {
          line: 392,
          column: 28
        },
        end: {
          line: 392,
          column: 49
        }
      },
      "223": {
        start: {
          line: 393,
          column: 28
        },
        end: {
          line: 395,
          column: 29
        }
      },
      "224": {
        start: {
          line: 394,
          column: 32
        },
        end: {
          line: 394,
          column: 155
        }
      },
      "225": {
        start: {
          line: 397,
          column: 28
        },
        end: {
          line: 397,
          column: 178
        }
      },
      "226": {
        start: {
          line: 400,
          column: 28
        },
        end: {
          line: 400,
          column: 38
        }
      },
      "227": {
        start: {
          line: 401,
          column: 28
        },
        end: {
          line: 404,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 68,
            column: 72
          },
          end: {
            line: 68,
            column: 73
          }
        },
        loc: {
          start: {
            line: 68,
            column: 97
          },
          end: {
            line: 335,
            column: 5
          }
        },
        line: 68
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 68,
            column: 149
          },
          end: {
            line: 68,
            column: 150
          }
        },
        loc: {
          start: {
            line: 68,
            column: 172
          },
          end: {
            line: 335,
            column: 1
          }
        },
        line: 68
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 70,
            column: 29
          },
          end: {
            line: 70,
            column: 30
          }
        },
        loc: {
          start: {
            line: 70,
            column: 43
          },
          end: {
            line: 334,
            column: 5
          }
        },
        line: 70
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 72,
            column: 13
          }
        },
        loc: {
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 333,
            column: 17
          }
        },
        line: 72
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 72,
            column: 67
          },
          end: {
            line: 72,
            column: 68
          }
        },
        loc: {
          start: {
            line: 72,
            column: 79
          },
          end: {
            line: 333,
            column: 13
          }
        },
        line: 72
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 75,
            column: 41
          },
          end: {
            line: 75,
            column: 42
          }
        },
        loc: {
          start: {
            line: 75,
            column: 55
          },
          end: {
            line: 332,
            column: 17
          }
        },
        line: 75
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 166,
            column: 68
          },
          end: {
            line: 166,
            column: 69
          }
        },
        loc: {
          start: {
            line: 166,
            column: 81
          },
          end: {
            line: 166,
            column: 117
          }
        },
        line: 166
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 169,
            column: 68
          },
          end: {
            line: 169,
            column: 69
          }
        },
        loc: {
          start: {
            line: 169,
            column: 86
          },
          end: {
            line: 169,
            column: 115
          }
        },
        line: 169
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 174,
            column: 80
          },
          end: {
            line: 174,
            column: 81
          }
        },
        loc: {
          start: {
            line: 174,
            column: 93
          },
          end: {
            line: 174,
            column: 131
          }
        },
        line: 174
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 337,
            column: 72
          },
          end: {
            line: 337,
            column: 73
          }
        },
        loc: {
          start: {
            line: 337,
            column: 97
          },
          end: {
            line: 409,
            column: 5
          }
        },
        line: 337
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 337,
            column: 149
          },
          end: {
            line: 337,
            column: 150
          }
        },
        loc: {
          start: {
            line: 337,
            column: 172
          },
          end: {
            line: 409,
            column: 1
          }
        },
        line: 337
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 339,
            column: 29
          },
          end: {
            line: 339,
            column: 30
          }
        },
        loc: {
          start: {
            line: 339,
            column: 43
          },
          end: {
            line: 408,
            column: 5
          }
        },
        line: 339
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 341,
            column: 12
          },
          end: {
            line: 341,
            column: 13
          }
        },
        loc: {
          start: {
            line: 341,
            column: 24
          },
          end: {
            line: 407,
            column: 17
          }
        },
        line: 341
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 341,
            column: 67
          },
          end: {
            line: 341,
            column: 68
          }
        },
        loc: {
          start: {
            line: 341,
            column: 79
          },
          end: {
            line: 407,
            column: 13
          }
        },
        line: 341
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 344,
            column: 41
          },
          end: {
            line: 344,
            column: 42
          }
        },
        loc: {
          start: {
            line: 344,
            column: 55
          },
          end: {
            line: 406,
            column: 17
          }
        },
        line: 344
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 76,
            column: 20
          },
          end: {
            line: 331,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 77,
            column: 24
          },
          end: {
            line: 77,
            column: 108
          }
        }, {
          start: {
            line: 78,
            column: 24
          },
          end: {
            line: 84,
            column: 57
          }
        }, {
          start: {
            line: 85,
            column: 24
          },
          end: {
            line: 87,
            column: 65
          }
        }, {
          start: {
            line: 88,
            column: 24
          },
          end: {
            line: 106,
            column: 36
          }
        }, {
          start: {
            line: 107,
            column: 24
          },
          end: {
            line: 119,
            column: 36
          }
        }, {
          start: {
            line: 120,
            column: 24
          },
          end: {
            line: 132,
            column: 36
          }
        }, {
          start: {
            line: 133,
            column: 24
          },
          end: {
            line: 155,
            column: 36
          }
        }, {
          start: {
            line: 156,
            column: 24
          },
          end: {
            line: 163,
            column: 36
          }
        }, {
          start: {
            line: 164,
            column: 24
          },
          end: {
            line: 185,
            column: 36
          }
        }, {
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 189,
            column: 42
          }
        }, {
          start: {
            line: 190,
            column: 24
          },
          end: {
            line: 202,
            column: 32
          }
        }, {
          start: {
            line: 203,
            column: 24
          },
          end: {
            line: 228,
            column: 36
          }
        }, {
          start: {
            line: 229,
            column: 24
          },
          end: {
            line: 237,
            column: 36
          }
        }, {
          start: {
            line: 238,
            column: 24
          },
          end: {
            line: 242,
            column: 42
          }
        }, {
          start: {
            line: 243,
            column: 24
          },
          end: {
            line: 270,
            column: 36
          }
        }, {
          start: {
            line: 271,
            column: 24
          },
          end: {
            line: 273,
            column: 42
          }
        }, {
          start: {
            line: 274,
            column: 24
          },
          end: {
            line: 276,
            column: 53
          }
        }, {
          start: {
            line: 277,
            column: 24
          },
          end: {
            line: 287,
            column: 36
          }
        }, {
          start: {
            line: 288,
            column: 24
          },
          end: {
            line: 295,
            column: 36
          }
        }, {
          start: {
            line: 296,
            column: 24
          },
          end: {
            line: 300,
            column: 42
          }
        }, {
          start: {
            line: 301,
            column: 24
          },
          end: {
            line: 304,
            column: 115
          }
        }, {
          start: {
            line: 305,
            column: 24
          },
          end: {
            line: 307,
            column: 42
          }
        }, {
          start: {
            line: 308,
            column: 24
          },
          end: {
            line: 310,
            column: 53
          }
        }, {
          start: {
            line: 311,
            column: 24
          },
          end: {
            line: 311,
            column: 58
          }
        }, {
          start: {
            line: 312,
            column: 24
          },
          end: {
            line: 315,
            column: 53
          }
        }, {
          start: {
            line: 316,
            column: 24
          },
          end: {
            line: 330,
            column: 32
          }
        }],
        line: 76
      },
      "36": {
        loc: {
          start: {
            line: 80,
            column: 28
          },
          end: {
            line: 82,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 28
          },
          end: {
            line: 82,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "37": {
        loc: {
          start: {
            line: 80,
            column: 34
          },
          end: {
            line: 80,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 132
          },
          end: {
            line: 80,
            column: 138
          }
        }, {
          start: {
            line: 80,
            column: 141
          },
          end: {
            line: 80,
            column: 146
          }
        }],
        line: 80
      },
      "38": {
        loc: {
          start: {
            line: 80,
            column: 34
          },
          end: {
            line: 80,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 34
          },
          end: {
            line: 80,
            column: 112
          }
        }, {
          start: {
            line: 80,
            column: 116
          },
          end: {
            line: 80,
            column: 129
          }
        }],
        line: 80
      },
      "39": {
        loc: {
          start: {
            line: 80,
            column: 40
          },
          end: {
            line: 80,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 81
          },
          end: {
            line: 80,
            column: 87
          }
        }, {
          start: {
            line: 80,
            column: 90
          },
          end: {
            line: 80,
            column: 102
          }
        }],
        line: 80
      },
      "40": {
        loc: {
          start: {
            line: 80,
            column: 40
          },
          end: {
            line: 80,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 40
          },
          end: {
            line: 80,
            column: 56
          }
        }, {
          start: {
            line: 80,
            column: 60
          },
          end: {
            line: 80,
            column: 78
          }
        }],
        line: 80
      },
      "41": {
        loc: {
          start: {
            line: 91,
            column: 28
          },
          end: {
            line: 97,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 28
          },
          end: {
            line: 97,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "42": {
        loc: {
          start: {
            line: 109,
            column: 28
          },
          end: {
            line: 111,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 28
          },
          end: {
            line: 111,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "43": {
        loc: {
          start: {
            line: 122,
            column: 28
          },
          end: {
            line: 124,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 28
          },
          end: {
            line: 124,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "44": {
        loc: {
          start: {
            line: 122,
            column: 32
          },
          end: {
            line: 122,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 32
          },
          end: {
            line: 122,
            column: 37
          }
        }, {
          start: {
            line: 122,
            column: 41
          },
          end: {
            line: 122,
            column: 80
          }
        }],
        line: 122
      },
      "45": {
        loc: {
          start: {
            line: 135,
            column: 28
          },
          end: {
            line: 137,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 28
          },
          end: {
            line: 137,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "46": {
        loc: {
          start: {
            line: 138,
            column: 89
          },
          end: {
            line: 138,
            column: 168
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 89
          },
          end: {
            line: 138,
            column: 112
          }
        }, {
          start: {
            line: 138,
            column: 116
          },
          end: {
            line: 138,
            column: 168
          }
        }],
        line: 138
      },
      "47": {
        loc: {
          start: {
            line: 138,
            column: 173
          },
          end: {
            line: 138,
            column: 212
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 173
          },
          end: {
            line: 138,
            column: 192
          }
        }, {
          start: {
            line: 138,
            column: 196
          },
          end: {
            line: 138,
            column: 212
          }
        }],
        line: 138
      },
      "48": {
        loc: {
          start: {
            line: 138,
            column: 217
          },
          end: {
            line: 138,
            column: 256
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 217
          },
          end: {
            line: 138,
            column: 236
          }
        }, {
          start: {
            line: 138,
            column: 240
          },
          end: {
            line: 138,
            column: 256
          }
        }],
        line: 138
      },
      "49": {
        loc: {
          start: {
            line: 140,
            column: 28
          },
          end: {
            line: 146,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 28
          },
          end: {
            line: 146,
            column: 29
          }
        }, {
          start: {
            line: 143,
            column: 33
          },
          end: {
            line: 146,
            column: 29
          }
        }],
        line: 140
      },
      "50": {
        loc: {
          start: {
            line: 140,
            column: 32
          },
          end: {
            line: 140,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 32
          },
          end: {
            line: 140,
            column: 56
          }
        }, {
          start: {
            line: 140,
            column: 60
          },
          end: {
            line: 140,
            column: 86
          }
        }],
        line: 140
      },
      "51": {
        loc: {
          start: {
            line: 143,
            column: 33
          },
          end: {
            line: 146,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 33
          },
          end: {
            line: 146,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "52": {
        loc: {
          start: {
            line: 143,
            column: 37
          },
          end: {
            line: 143,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 37
          },
          end: {
            line: 143,
            column: 59
          }
        }, {
          start: {
            line: 143,
            column: 63
          },
          end: {
            line: 143,
            column: 101
          }
        }],
        line: 143
      },
      "53": {
        loc: {
          start: {
            line: 168,
            column: 46
          },
          end: {
            line: 168,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 168,
            column: 63
          },
          end: {
            line: 168,
            column: 110
          }
        }, {
          start: {
            line: 168,
            column: 113
          },
          end: {
            line: 168,
            column: 114
          }
        }],
        line: 168
      },
      "54": {
        loc: {
          start: {
            line: 171,
            column: 28
          },
          end: {
            line: 176,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 28
          },
          end: {
            line: 176,
            column: 29
          }
        }, {
          start: {
            line: 174,
            column: 33
          },
          end: {
            line: 176,
            column: 29
          }
        }],
        line: 171
      },
      "55": {
        loc: {
          start: {
            line: 171,
            column: 32
          },
          end: {
            line: 171,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 32
          },
          end: {
            line: 171,
            column: 61
          }
        }, {
          start: {
            line: 171,
            column: 65
          },
          end: {
            line: 171,
            column: 79
          }
        }],
        line: 171
      },
      "56": {
        loc: {
          start: {
            line: 174,
            column: 33
          },
          end: {
            line: 176,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 33
          },
          end: {
            line: 176,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "57": {
        loc: {
          start: {
            line: 174,
            column: 37
          },
          end: {
            line: 174,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 174,
            column: 37
          },
          end: {
            line: 174,
            column: 55
          }
        }, {
          start: {
            line: 174,
            column: 59
          },
          end: {
            line: 174,
            column: 132
          }
        }],
        line: 174
      },
      "58": {
        loc: {
          start: {
            line: 178,
            column: 28
          },
          end: {
            line: 178,
            column: 84
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 28
          },
          end: {
            line: 178,
            column: 84
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 178
      },
      "59": {
        loc: {
          start: {
            line: 188,
            column: 44
          },
          end: {
            line: 188,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 45
          },
          end: {
            line: 188,
            column: 108
          }
        }, {
          start: {
            line: 188,
            column: 113
          },
          end: {
            line: 188,
            column: 117
          }
        }],
        line: 188
      },
      "60": {
        loc: {
          start: {
            line: 188,
            column: 45
          },
          end: {
            line: 188,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 188,
            column: 88
          },
          end: {
            line: 188,
            column: 94
          }
        }, {
          start: {
            line: 188,
            column: 97
          },
          end: {
            line: 188,
            column: 108
          }
        }],
        line: 188
      },
      "61": {
        loc: {
          start: {
            line: 188,
            column: 45
          },
          end: {
            line: 188,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 45
          },
          end: {
            line: 188,
            column: 62
          }
        }, {
          start: {
            line: 188,
            column: 66
          },
          end: {
            line: 188,
            column: 85
          }
        }],
        line: 188
      },
      "62": {
        loc: {
          start: {
            line: 197,
            column: 237
          },
          end: {
            line: 199,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 197,
            column: 237
          },
          end: {
            line: 197,
            column: 263
          }
        }, {
          start: {
            line: 197,
            column: 267
          },
          end: {
            line: 197,
            column: 290
          }
        }, {
          start: {
            line: 197,
            column: 294
          },
          end: {
            line: 199,
            column: 33
          }
        }],
        line: 197
      },
      "63": {
        loc: {
          start: {
            line: 199,
            column: 38
          },
          end: {
            line: 201,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 199,
            column: 38
          },
          end: {
            line: 199,
            column: 66
          }
        }, {
          start: {
            line: 199,
            column: 70
          },
          end: {
            line: 199,
            column: 91
          }
        }, {
          start: {
            line: 199,
            column: 95
          },
          end: {
            line: 201,
            column: 33
          }
        }],
        line: 199
      },
      "64": {
        loc: {
          start: {
            line: 215,
            column: 55
          },
          end: {
            line: 215,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 55
          },
          end: {
            line: 215,
            column: 64
          }
        }, {
          start: {
            line: 215,
            column: 68
          },
          end: {
            line: 215,
            column: 69
          }
        }],
        line: 215
      },
      "65": {
        loc: {
          start: {
            line: 216,
            column: 46
          },
          end: {
            line: 220,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 46
          },
          end: {
            line: 216,
            column: 68
          }
        }, {
          start: {
            line: 216,
            column: 72
          },
          end: {
            line: 220,
            column: 37
          }
        }],
        line: 216
      },
      "66": {
        loc: {
          start: {
            line: 224,
            column: 51
          },
          end: {
            line: 224,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 224,
            column: 51
          },
          end: {
            line: 224,
            column: 60
          }
        }, {
          start: {
            line: 224,
            column: 64
          },
          end: {
            line: 224,
            column: 65
          }
        }],
        line: 224
      },
      "67": {
        loc: {
          start: {
            line: 225,
            column: 57
          },
          end: {
            line: 225,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 82
          },
          end: {
            line: 225,
            column: 83
          }
        }, {
          start: {
            line: 225,
            column: 86
          },
          end: {
            line: 225,
            column: 87
          }
        }],
        line: 225
      },
      "68": {
        loc: {
          start: {
            line: 231,
            column: 28
          },
          end: {
            line: 231,
            column: 105
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 28
          },
          end: {
            line: 231,
            column: 105
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 231
      },
      "69": {
        loc: {
          start: {
            line: 231,
            column: 34
          },
          end: {
            line: 231,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 34
          },
          end: {
            line: 231,
            column: 56
          }
        }, {
          start: {
            line: 231,
            column: 60
          },
          end: {
            line: 231,
            column: 77
          }
        }],
        line: 231
      },
      "70": {
        loc: {
          start: {
            line: 240,
            column: 28
          },
          end: {
            line: 240,
            column: 148
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 28
          },
          end: {
            line: 240,
            column: 148
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "71": {
        loc: {
          start: {
            line: 240,
            column: 34
          },
          end: {
            line: 240,
            column: 120
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 240,
            column: 85
          },
          end: {
            line: 240,
            column: 91
          }
        }, {
          start: {
            line: 240,
            column: 94
          },
          end: {
            line: 240,
            column: 120
          }
        }],
        line: 240
      },
      "72": {
        loc: {
          start: {
            line: 240,
            column: 34
          },
          end: {
            line: 240,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 240,
            column: 34
          },
          end: {
            line: 240,
            column: 55
          }
        }, {
          start: {
            line: 240,
            column: 59
          },
          end: {
            line: 240,
            column: 82
          }
        }],
        line: 240
      },
      "73": {
        loc: {
          start: {
            line: 244,
            column: 28
          },
          end: {
            line: 244,
            column: 76
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 244,
            column: 28
          },
          end: {
            line: 244,
            column: 76
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 244
      },
      "74": {
        loc: {
          start: {
            line: 259,
            column: 67
          },
          end: {
            line: 259,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 259,
            column: 67
          },
          end: {
            line: 259,
            column: 76
          }
        }, {
          start: {
            line: 259,
            column: 80
          },
          end: {
            line: 259,
            column: 81
          }
        }],
        line: 259
      },
      "75": {
        loc: {
          start: {
            line: 268,
            column: 67
          },
          end: {
            line: 268,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 268,
            column: 67
          },
          end: {
            line: 268,
            column: 76
          }
        }, {
          start: {
            line: 268,
            column: 80
          },
          end: {
            line: 268,
            column: 81
          }
        }],
        line: 268
      },
      "76": {
        loc: {
          start: {
            line: 291,
            column: 28
          },
          end: {
            line: 291,
            column: 105
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 28
          },
          end: {
            line: 291,
            column: 105
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      },
      "77": {
        loc: {
          start: {
            line: 291,
            column: 34
          },
          end: {
            line: 291,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 34
          },
          end: {
            line: 291,
            column: 56
          }
        }, {
          start: {
            line: 291,
            column: 60
          },
          end: {
            line: 291,
            column: 77
          }
        }],
        line: 291
      },
      "78": {
        loc: {
          start: {
            line: 298,
            column: 28
          },
          end: {
            line: 298,
            column: 148
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 298,
            column: 28
          },
          end: {
            line: 298,
            column: 148
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 298
      },
      "79": {
        loc: {
          start: {
            line: 298,
            column: 34
          },
          end: {
            line: 298,
            column: 120
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 298,
            column: 85
          },
          end: {
            line: 298,
            column: 91
          }
        }, {
          start: {
            line: 298,
            column: 94
          },
          end: {
            line: 298,
            column: 120
          }
        }],
        line: 298
      },
      "80": {
        loc: {
          start: {
            line: 298,
            column: 34
          },
          end: {
            line: 298,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 34
          },
          end: {
            line: 298,
            column: 55
          }
        }, {
          start: {
            line: 298,
            column: 59
          },
          end: {
            line: 298,
            column: 82
          }
        }],
        line: 298
      },
      "81": {
        loc: {
          start: {
            line: 302,
            column: 28
          },
          end: {
            line: 302,
            column: 76
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 28
          },
          end: {
            line: 302,
            column: 76
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "82": {
        loc: {
          start: {
            line: 345,
            column: 20
          },
          end: {
            line: 405,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 346,
            column: 24
          },
          end: {
            line: 346,
            column: 108
          }
        }, {
          start: {
            line: 347,
            column: 24
          },
          end: {
            line: 353,
            column: 57
          }
        }, {
          start: {
            line: 354,
            column: 24
          },
          end: {
            line: 357,
            column: 111
          }
        }, {
          start: {
            line: 358,
            column: 24
          },
          end: {
            line: 390,
            column: 36
          }
        }, {
          start: {
            line: 391,
            column: 24
          },
          end: {
            line: 397,
            column: 178
          }
        }, {
          start: {
            line: 398,
            column: 24
          },
          end: {
            line: 404,
            column: 36
          }
        }],
        line: 345
      },
      "83": {
        loc: {
          start: {
            line: 349,
            column: 28
          },
          end: {
            line: 351,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 349,
            column: 28
          },
          end: {
            line: 351,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 349
      },
      "84": {
        loc: {
          start: {
            line: 349,
            column: 34
          },
          end: {
            line: 349,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 349,
            column: 132
          },
          end: {
            line: 349,
            column: 138
          }
        }, {
          start: {
            line: 349,
            column: 141
          },
          end: {
            line: 349,
            column: 146
          }
        }],
        line: 349
      },
      "85": {
        loc: {
          start: {
            line: 349,
            column: 34
          },
          end: {
            line: 349,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 349,
            column: 34
          },
          end: {
            line: 349,
            column: 112
          }
        }, {
          start: {
            line: 349,
            column: 116
          },
          end: {
            line: 349,
            column: 129
          }
        }],
        line: 349
      },
      "86": {
        loc: {
          start: {
            line: 349,
            column: 40
          },
          end: {
            line: 349,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 349,
            column: 81
          },
          end: {
            line: 349,
            column: 87
          }
        }, {
          start: {
            line: 349,
            column: 90
          },
          end: {
            line: 349,
            column: 102
          }
        }],
        line: 349
      },
      "87": {
        loc: {
          start: {
            line: 349,
            column: 40
          },
          end: {
            line: 349,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 349,
            column: 40
          },
          end: {
            line: 349,
            column: 56
          }
        }, {
          start: {
            line: 349,
            column: 60
          },
          end: {
            line: 349,
            column: 78
          }
        }],
        line: 349
      },
      "88": {
        loc: {
          start: {
            line: 360,
            column: 28
          },
          end: {
            line: 366,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 28
          },
          end: {
            line: 366,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 360
      },
      "89": {
        loc: {
          start: {
            line: 393,
            column: 28
          },
          end: {
            line: 395,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 393,
            column: 28
          },
          end: {
            line: 395,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 393
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0, 0],
      "63": [0, 0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0, 0, 0, 0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/steps/[stepId]/progress/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,uCAAsC;AACtC,6EAA2E;AAC3E,6CAAgD;AAChD,wFAA8E;AAC9E,wFAAqF;AACrF,wFAAqF;AACrF,2BAAwB;AAGxB,IAAM,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;IAC3D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC5C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;CACvC,CAAC,CAAC;AAEH,6BAA6B;AAChB,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAA+D;QAA7D,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE,6BAA6B;YAC7E;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACQ,qBAAM,MAAM,EAAA;;4BAA7C,KAAiC,SAAY,EAAvC,cAAc,QAAA,EAAE,MAAM,YAAA;4BACnB,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;4BAA3B,IAAI,GAAG,SAAoB;4BAC3B,UAAU,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;4BAExD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gCACxB,sBAAO,qBAAY,CAAC,IAAI,CACtB;wCACE,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,sBAAsB;wCAC7B,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM;qCACjC,EACD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,KAAsC,UAAU,CAAC,IAAI,EAAnD,MAAM,YAAA,EAAE,SAAS,eAAA,EAAE,KAAK,WAAA,EAAE,KAAK,WAAA,CAAqB;4BAGzC,qBAAM,eAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;oCAC1D,KAAK,EAAE;wCACL,qBAAqB,EAAE;4CACrB,MAAM,QAAA;4CACN,cAAc,gBAAA;yCACf;qCACF;iCACF,CAAC,EAAA;;4BAPI,UAAU,GAAG,SAOjB;4BAEF,IAAI,CAAC,UAAU,EAAE,CAAC;gCAChB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAC/D,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGY,qBAAM,eAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;oCACpD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;oCACrB,OAAO,EAAE;wCACP,YAAY,EAAE;4CACZ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;yCACrB;qCACF;iCACF,CAAC,EAAA;;4BAPI,IAAI,GAAG,SAOX;4BAEF,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,cAAc,EAAE,CAAC;gCACrD,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,EAAE,EACjE,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEuB,qBAAM,eAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC;oCACvE,KAAK,EAAE;wCACL,aAAa,EAAE;4CACb,MAAM,QAAA;4CACN,MAAM,QAAA;yCACP;qCACF;iCACF,CAAC,EAAA;;4BAPI,eAAe,GAAG,SAOtB;4BAEF,IAAI,CAAC,eAAe,EAAE,CAAC;gCACrB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,EACtD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGK,UAAU,gCACd,MAAM,QAAA,IACH,CAAC,SAAS,KAAK,SAAS,IAAI,EAAE,SAAS,EAAE,eAAe,CAAC,SAAS,GAAG,SAAS,EAAE,CAAC,GACjF,CAAC,KAAK,KAAK,SAAS,IAAI,EAAE,KAAK,OAAA,EAAE,CAAC,GAClC,CAAC,KAAK,KAAK,SAAS,IAAI,EAAE,KAAK,OAAA,EAAE,CAAC,CACtC,CAAC;4BAEF,iCAAiC;4BACjC,IAAI,MAAM,KAAK,aAAa,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;gCAC3D,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;4BACpC,CAAC;iCAAM,IAAI,MAAM,KAAK,WAAW,IAAI,eAAe,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gCAC5E,UAAU,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;gCACpC,UAAU,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,GAAG,CAAC,CAAC;4BACrD,CAAC;4BAGuB,qBAAM,eAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC;oCACnE,KAAK,EAAE;wCACL,aAAa,EAAE;4CACb,MAAM,QAAA;4CACN,MAAM,QAAA;yCACP;qCACF;oCACD,IAAI,EAAE,UAAU;iCACjB,CAAC,EAAA;;4BARI,eAAe,GAAG,SAQtB;4BAGsB,qBAAM,eAAM,CAAC,wBAAwB,CAAC,QAAQ,CAAC;oCACrE,KAAK,EAAE;wCACL,MAAM,QAAA;wCACN,kBAAkB,EAAE,UAAU,CAAC,EAAE;qCAClC;iCACF,CAAC,EAAA;;4BALI,eAAe,GAAG,SAKtB;4BAEI,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,WAAW,EAAxB,CAAwB,CAAC,CAAC,MAAM,CAAC;4BAC9E,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC;4BACpC,eAAe,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACvF,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,SAAS,EAAjB,CAAiB,EAAE,CAAC,CAAC,CAAC;4BAG5E,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;4BACnC,IAAI,cAAc,KAAK,UAAU,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gCACpD,UAAU,GAAG,WAAW,CAAC;4BAC3B,CAAC;iCAAM,IAAI,cAAc,GAAG,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,aAAa,EAA1B,CAA0B,CAAC,EAAE,CAAC;gCACvF,UAAU,GAAG,aAAa,CAAC;4BAC7B,CAAC;4BAGG,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;iCACzC,CAAA,MAAM,KAAK,WAAW,CAAA,EAAtB,yBAAsB;4BACP,qBAAM,eAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;oCACvD,KAAK,EAAE;wCACL,cAAc,gBAAA;wCACd,SAAS,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;qCAClC;oCACD,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;iCAC9B,CAAC,EAAA;;4BANI,QAAQ,GAAG,SAMf;4BACF,aAAa,GAAG,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,EAAE,KAAI,IAAI,CAAC;;iCAIb,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;gCAC7D,KAAK,EAAE;oCACL,qBAAqB,EAAE;wCACrB,MAAM,QAAA;wCACN,cAAc,gBAAA;qCACf;iCACF;gCACD,IAAI,sBACF,MAAM,EAAE,UAAU,EAClB,cAAc,gBAAA,EACd,eAAe,iBAAA,EACf,cAAc,gBAAA,EACd,aAAa,eAAA,EACb,cAAc,EAAE,IAAI,IAAI,EAAE,IACvB,CAAC,UAAU,KAAK,WAAW,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI;oCAC3D,WAAW,EAAE,IAAI,IAAI,EAAE;iCACxB,CAAC,GACC,CAAC,UAAU,KAAK,aAAa,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI;oCAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;iCACtB,CAAC,CACH;6BACF,CAAC,EAAA;;4BArBI,iBAAiB,GAAG,SAqBxB;4BAGI,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;4BACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;4BAE3B,qBAAM,eAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oCACpC,KAAK,EAAE;wCACL,WAAW,EAAE;4CACX,MAAM,QAAA;4CACN,IAAI,EAAE,KAAK;yCACZ;qCACF;oCACD,MAAM,sBACJ,SAAS,EAAE;4CACT,SAAS,EAAE,SAAS,IAAI,CAAC;yCAC1B,IACE,CAAC,MAAM,KAAK,WAAW,IAAI;wCAC5B,eAAe,EAAE;4CACf,SAAS,EAAE,CAAC;yCACb;qCACF,CAAC,KACF,IAAI,EAAE,IAAI,IAAI,EAAE,GACjB;oCACD,MAAM,EAAE;wCACN,MAAM,QAAA;wCACN,IAAI,EAAE,KAAK;wCACX,SAAS,EAAE,SAAS,IAAI,CAAC;wCACzB,eAAe,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAC/C,eAAe,EAAE,CAAC,EAAE,6CAA6C;qCAClE;iCACF,CAAC,EAAA;;4BAzBF,SAyBE,CAAC;iCAGC,CAAA,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,YAAY,CAAA,EAA3C,yBAA2C;4BACxB,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oCACxD,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;oCAC7B,OAAO,EAAE;wCACP,MAAM,EAAE,IAAI;qCACb;iCACF,CAAC,EAAA;;4BALI,YAAY,GAAG,SAKnB;iCAEE,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,CAAC,MAAM,CAAA,EAA3B,yBAA2B;kCACU,EAAnB,KAAA,YAAY,CAAC,MAAM;;;iCAAnB,CAAA,cAAmB,CAAA;4BAA5B,KAAK;4BACd,qBAAM,eAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oCACpC,KAAK,EAAE;wCACL,cAAc,EAAE;4CACd,MAAM,QAAA;4CACN,OAAO,EAAE,KAAK,CAAC,EAAE;yCAClB;qCACF;oCACD,MAAM,EAAE;wCACN,cAAc,EAAE;4CACd,SAAS,EAAE,EAAE,CAAC,4BAA4B;yCAC3C;wCACD,aAAa,EAAE,IAAI,IAAI,EAAE;wCACzB,aAAa,EAAE;4CACb,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;yCAC7C;qCACF;oCACD,MAAM,EAAE;wCACN,MAAM,QAAA;wCACN,OAAO,EAAE,KAAK,CAAC,EAAE;wCACjB,YAAY,EAAE,UAAU;wCACxB,cAAc,EAAE,EAAE;wCAClB,aAAa,EAAE,IAAI,IAAI,EAAE;wCACzB,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;qCACjD;iCACF,CAAC,EAAA;;4BAxBF,SAwBE,CAAC;;;4BAzBe,IAAmB,CAAA;;;;4BAgCnC,YAAY,GAAG,IAAI,qDAAwB,EAAE,CAAC;4BAC9C,wBAAwB,GAAG,IAAI,qDAAwB,CAAC,YAAY,CAAC,CAAC;4BAE5E,wDAAwD;4BACxD,qBAAM,wBAAwB,CAAC,eAAe,CAAC;oCAC7C,KAAK,EAAE,0BAA0B;oCACjC,SAAS,EAAE,QAAQ;oCACnB,MAAM,QAAA;oCACN,IAAI,EAAE,EAAE,MAAM,QAAA,EAAE,cAAc,gBAAA,EAAE,MAAM,QAAA,EAAE;iCACzC,CAAC,EAAA;;4BANF,wDAAwD;4BACxD,SAKE,CAAC;iCAGC,CAAA,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,YAAY,CAAA,EAA3C,yBAA2C;4BACxB,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oCACxD,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;oCAC7B,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;iCAC1B,CAAC,EAAA;;4BAHI,YAAY,GAAG,SAGnB;iCAEE,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,CAAC,MAAM,CAAA,EAA3B,yBAA2B;kCACU,EAAnB,KAAA,YAAY,CAAC,MAAM;;;iCAAnB,CAAA,cAAmB,CAAA;4BAA5B,KAAK;4BACd,qBAAM,wBAAwB,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,EAAA;;4BAAtE,SAAsE,CAAC;;;4BADrD,IAAmB,CAAA;;;;;4BAM3C,OAAO,CAAC,IAAI,CAAC,qDAAqD,EAAE,YAAU,CAAC,CAAC;;iCAGlF,sBAAO,qBAAY,CAAC,IAAI,CAAC;gCACvB,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE;oCACJ,YAAY,EAAE,eAAe;oCAC7B,YAAY,EAAE;wCACZ,MAAM,EAAE,UAAU;wCAClB,cAAc,gBAAA;wCACd,UAAU,YAAA;wCACV,eAAe,iBAAA;wCACf,cAAc,gBAAA;wCACd,aAAa,eAAA;qCACd;iCACF;gCACD,OAAO,EAAE,+BAA+B;6BACzC,CAAC,EAAC;;;iBACN,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,0BAA0B;AACb,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAA+D;QAA7D,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE,8BAA8B;YAC9E;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACQ,qBAAM,MAAM,EAAA;;4BAA7C,KAAiC,SAAY,EAAvC,cAAc,QAAA,EAAE,MAAM,YAAA;4BAG1B,QAAQ,GAAG,wBAAiB,MAAM,cAAI,MAAM,CAAE,CAAC;4BAGtC,qBAAM,8CAAiB,CAAC,GAAG,CAAM,QAAQ,CAAC,EAAA;;4BAAnD,MAAM,GAAG,SAA0C;4BACzD,IAAI,MAAM,EAAE,CAAC;gCACX,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,IAAI;wCACb,IAAI,EAAE,MAAM;wCACZ,MAAM,EAAE,IAAI;qCACb,CAAC,EAAC;4BACL,CAAC;4BAGgB,qBAAM,eAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC;oCAChE,KAAK,EAAE;wCACL,aAAa,EAAE;4CACb,MAAM,QAAA;4CACN,MAAM,QAAA;yCACP;qCACF;oCACD,OAAO,EAAE;wCACP,IAAI,EAAE;4CACJ,OAAO,EAAE;gDACP,QAAQ,EAAE;oDACR,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,KAAK,EAAE,IAAI;wDACX,WAAW,EAAE,IAAI;wDACjB,IAAI,EAAE,IAAI;wDACV,GAAG,EAAE,IAAI;wDACT,QAAQ,EAAE,IAAI;qDACf;iDACF;6CACF;yCACF;qCACF;iCACF,CAAC,EAAA;;4BAvBI,QAAQ,GAAG,SAuBf;4BAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;gCACd,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,EACtD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAED,qBAAqB;4BACrB,qBAAM,8CAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,EAAA;;4BADxG,qBAAqB;4BACrB,SAAwG,CAAC;4BAE3G,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,QAAQ;iCACf,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/learning-paths/[id]/steps/[stepId]/progress/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { consolidatedCache } from '@/lib/services/consolidated-cache-service';\nimport { CacheInvalidationService } from '@/lib/services/cache-invalidation-service';\nimport { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';\nimport { z } from 'zod';\nimport { withCSRFProtection } from '@/lib/csrf';\n\nconst updateProgressSchema = z.object({\n  status: z.enum(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED']),\n  timeSpent: z.number().min(0).optional(),\n  score: z.number().min(0).max(100).optional(),\n  notes: z.string().max(1000).optional(),\n});\n\n// PUT - Update step progress\nexport const PUT = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string; stepId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 updates per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      const userId = session.user.id;\n      const { id: learningPathId, stepId } = await params;\n        const body = await request.json();\n        const validation = updateProgressSchema.safeParse(body);\n        \n        if (!validation.success) {\n          return NextResponse.json(\n            { \n              success: false, \n              error: 'Invalid request data',\n              details: validation.error.errors \n            },\n            { status: 400 }\n          );\n        }\n\n        const { status, timeSpent, score, notes } = validation.data;\n\n        // Verify user is enrolled in the learning path\n        const enrollment = await prisma.userLearningPath.findUnique({\n          where: {\n            userId_learningPathId: {\n              userId,\n              learningPathId\n            }\n          }\n        });\n\n        if (!enrollment) {\n          return NextResponse.json(\n            { success: false, error: 'Not enrolled in this learning path' },\n            { status: 404 }\n          );\n        }\n\n        // Get the step and current progress\n        const step = await prisma.learningPathStep.findUnique({\n          where: { id: stepId },\n          include: {\n            learningPath: {\n              select: { id: true }\n            }\n          }\n        });\n\n        if (!step || step.learningPath.id !== learningPathId) {\n          return NextResponse.json(\n            { success: false, error: 'Step not found in this learning path' },\n            { status: 404 }\n          );\n        }\n\n        const currentProgress = await prisma.userLearningPathProgress.findUnique({\n          where: {\n            userId_stepId: {\n              userId,\n              stepId\n            }\n          }\n        });\n\n        if (!currentProgress) {\n          return NextResponse.json(\n            { success: false, error: 'Progress record not found' },\n            { status: 404 }\n          );\n        }\n\n        // Prepare update data\n        const updateData: any = {\n          status,\n          ...(timeSpent !== undefined && { timeSpent: currentProgress.timeSpent + timeSpent }),\n          ...(score !== undefined && { score }),\n          ...(notes !== undefined && { notes }),\n        };\n\n        // Set timestamps based on status\n        if (status === 'IN_PROGRESS' && !currentProgress.startedAt) {\n          updateData.startedAt = new Date();\n        } else if (status === 'COMPLETED' && currentProgress.status !== 'COMPLETED') {\n          updateData.completedAt = new Date();\n          updateData.attempts = currentProgress.attempts + 1;\n        }\n\n        // Update step progress\n        const updatedProgress = await prisma.userLearningPathProgress.update({\n          where: {\n            userId_stepId: {\n              userId,\n              stepId\n            }\n          },\n          data: updateData\n        });\n\n        // Recalculate learning path progress\n        const allStepProgress = await prisma.userLearningPathProgress.findMany({\n          where: {\n            userId,\n            userLearningPathId: enrollment.id\n          }\n        });\n\n        const completedSteps = allStepProgress.filter(p => p.status === 'COMPLETED').length;\n        const totalSteps = allStepProgress.length;\n        const progressPercent = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;\n        const totalTimeSpent = allStepProgress.reduce((sum, p) => sum + p.timeSpent, 0);\n\n        // Determine overall learning path status\n        let pathStatus = enrollment.status;\n        if (completedSteps === totalSteps && totalSteps > 0) {\n          pathStatus = 'COMPLETED';\n        } else if (completedSteps > 0 || allStepProgress.some(p => p.status === 'IN_PROGRESS')) {\n          pathStatus = 'IN_PROGRESS';\n        }\n\n        // Find next step if current step is completed\n        let currentStepId = enrollment.currentStepId;\n        if (status === 'COMPLETED') {\n          const nextStep = await prisma.learningPathStep.findFirst({\n            where: {\n              learningPathId,\n              stepOrder: { gt: step.stepOrder }\n            },\n            orderBy: { stepOrder: 'asc' }\n          });\n          currentStepId = nextStep?.id || null;\n        }\n\n        // Update learning path progress\n        const updatedEnrollment = await prisma.userLearningPath.update({\n          where: {\n            userId_learningPathId: {\n              userId,\n              learningPathId\n            }\n          },\n          data: {\n            status: pathStatus,\n            completedSteps,\n            progressPercent,\n            totalTimeSpent,\n            currentStepId,\n            lastAccessedAt: new Date(),\n            ...(pathStatus === 'COMPLETED' && !enrollment.completedAt && {\n              completedAt: new Date()\n            }),\n            ...(pathStatus === 'IN_PROGRESS' && !enrollment.startedAt && {\n              startedAt: new Date()\n            }),\n          }\n        });\n\n        // Update daily learning analytics\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n\n        await prisma.learningAnalytics.upsert({\n          where: {\n            userId_date: {\n              userId,\n              date: today\n            }\n          },\n          update: {\n            timeSpent: {\n              increment: timeSpent || 0\n            },\n            ...(status === 'COMPLETED' && {\n              resourcesViewed: {\n                increment: 1\n              }\n            }),\n            date: new Date(),\n          },\n          create: {\n            userId,\n            date: today,\n            timeSpent: timeSpent || 0,\n            resourcesViewed: status === 'COMPLETED' ? 1 : 0,\n            pathsProgressed: 0, // This was already incremented on enrollment\n          }\n        });\n\n        // Update skill progress if step is completed\n        if (status === 'COMPLETED' && step.learningPath) {\n          const learningPath = await prisma.learningPath.findUnique({\n            where: { id: learningPathId },\n            include: {\n              skills: true\n            }\n          });\n\n          if (learningPath?.skills.length) {\n            for (const skill of learningPath.skills) {\n              await prisma.userSkillProgress.upsert({\n                where: {\n                  userId_skillId: {\n                    userId,\n                    skillId: skill.id\n                  }\n                },\n                update: {\n                  progressPoints: {\n                    increment: 10 // Points per completed step\n                  },\n                  lastPracticed: new Date(),\n                  practiceHours: {\n                    increment: Math.round((timeSpent || 0) / 60)\n                  }\n                },\n                create: {\n                  userId,\n                  skillId: skill.id,\n                  currentLevel: 'BEGINNER',\n                  progressPoints: 10,\n                  lastPracticed: new Date(),\n                  practiceHours: Math.round((timeSpent || 0) / 60),\n                }\n              });\n            }\n          }\n        }\n\n        // INTEGRATION FIX: Use new CacheInvalidationService for comprehensive cache invalidation\n        try {\n          const cacheService = new ConsolidatedCacheService();\n          const cacheInvalidationService = new CacheInvalidationService(cacheService);\n\n          // Invalidate learning progress and skill-related caches\n          await cacheInvalidationService.smartInvalidate({\n            table: 'UserLearningPathProgress',\n            operation: 'UPDATE',\n            userId,\n            data: { stepId, learningPathId, status }\n          });\n\n          // Also invalidate skill caches if skills were updated\n          if (status === 'COMPLETED' && step.learningPath) {\n            const learningPath = await prisma.learningPath.findUnique({\n              where: { id: learningPathId },\n              include: { skills: true }\n            });\n\n            if (learningPath?.skills.length) {\n              for (const skill of learningPath.skills) {\n                await cacheInvalidationService.invalidateSkillCaches(userId, skill.id);\n              }\n            }\n          }\n        } catch (cacheError) {\n          console.warn('Failed to invalidate learning path progress caches:', cacheError);\n        }\n\n        return NextResponse.json({\n          success: true,\n          data: {\n            stepProgress: updatedProgress,\n            pathProgress: {\n              status: pathStatus,\n              completedSteps,\n              totalSteps,\n              progressPercent,\n              totalTimeSpent,\n              currentStepId,\n            }\n          },\n          message: 'Progress updated successfully'\n        });\n    }\n  );\n});\n\n// GET - Get step progress\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string; stepId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 300 }, // 300 requests per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      const userId = session.user.id;\n      const { id: learningPathId, stepId } = await params;\n\n        // Build cache key\n        const cacheKey = `step_progress:${stepId}:${userId}`;\n        \n        // Check cache first\n        const cached = await consolidatedCache.get<any>(cacheKey);\n        if (cached) {\n          return NextResponse.json({\n            success: true,\n            data: cached,\n            cached: true\n          });\n        }\n\n        // Get step progress with step details\n        const progress = await prisma.userLearningPathProgress.findUnique({\n          where: {\n            userId_stepId: {\n              userId,\n              stepId\n            }\n          },\n          include: {\n            step: {\n              include: {\n                resource: {\n                  select: {\n                    id: true,\n                    title: true,\n                    description: true,\n                    type: true,\n                    url: true,\n                    duration: true,\n                  }\n                }\n              }\n            }\n          }\n        });\n\n        if (!progress) {\n          return NextResponse.json(\n            { success: false, error: 'Progress record not found' },\n            { status: 404 }\n          );\n        }\n\n        // Cache for 1 minute\n        await consolidatedCache.set(cacheKey, progress, { ttl: 60000, tags: ['step_progress', stepId, userId] });\n\n      return NextResponse.json({\n        success: true,\n        data: progress\n      });\n    }\n  );\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "74ef418bf282f4f7cd38f087774a3c21336be043"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2763i30p9o = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2763i30p9o();
var __assign =
/* istanbul ignore next */
(cov_2763i30p9o().s[0]++,
/* istanbul ignore next */
(cov_2763i30p9o().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2763i30p9o().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_2763i30p9o().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_2763i30p9o().f[0]++;
  cov_2763i30p9o().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_2763i30p9o().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_2763i30p9o().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_2763i30p9o().f[1]++;
    cov_2763i30p9o().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_2763i30p9o().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_2763i30p9o().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_2763i30p9o().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_2763i30p9o().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_2763i30p9o().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_2763i30p9o().b[2][0]++;
          cov_2763i30p9o().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_2763i30p9o().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_2763i30p9o().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_2763i30p9o().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_2763i30p9o().s[11]++,
/* istanbul ignore next */
(cov_2763i30p9o().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_2763i30p9o().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2763i30p9o().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2763i30p9o().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2763i30p9o().f[3]++;
    cov_2763i30p9o().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2763i30p9o().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_2763i30p9o().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2763i30p9o().f[4]++;
      cov_2763i30p9o().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2763i30p9o().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_2763i30p9o().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_2763i30p9o().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2763i30p9o().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2763i30p9o().f[6]++;
      cov_2763i30p9o().s[15]++;
      try {
        /* istanbul ignore next */
        cov_2763i30p9o().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2763i30p9o().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2763i30p9o().f[7]++;
      cov_2763i30p9o().s[18]++;
      try {
        /* istanbul ignore next */
        cov_2763i30p9o().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2763i30p9o().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2763i30p9o().f[8]++;
      cov_2763i30p9o().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2763i30p9o().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2763i30p9o().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2763i30p9o().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2763i30p9o().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2763i30p9o().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2763i30p9o().s[23]++,
/* istanbul ignore next */
(cov_2763i30p9o().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2763i30p9o().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2763i30p9o().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2763i30p9o().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_2763i30p9o().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2763i30p9o().f[10]++;
        cov_2763i30p9o().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2763i30p9o().b[9][0]++;
          cov_2763i30p9o().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2763i30p9o().b[9][1]++;
        }
        cov_2763i30p9o().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2763i30p9o().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2763i30p9o().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2763i30p9o().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2763i30p9o().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2763i30p9o().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2763i30p9o().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2763i30p9o().f[11]++;
    cov_2763i30p9o().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2763i30p9o().f[12]++;
    cov_2763i30p9o().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2763i30p9o().f[13]++;
      cov_2763i30p9o().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2763i30p9o().f[14]++;
    cov_2763i30p9o().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_2763i30p9o().b[12][0]++;
      cov_2763i30p9o().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2763i30p9o().b[12][1]++;
    }
    cov_2763i30p9o().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_2763i30p9o().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_2763i30p9o().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2763i30p9o().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2763i30p9o().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2763i30p9o().s[36]++;
      try {
        /* istanbul ignore next */
        cov_2763i30p9o().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2763i30p9o().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_2763i30p9o().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2763i30p9o().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2763i30p9o().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2763i30p9o().b[18][0]++,
        /* istanbul ignore next */
        (cov_2763i30p9o().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2763i30p9o().b[19][1]++,
        /* istanbul ignore next */
        (cov_2763i30p9o().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2763i30p9o().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2763i30p9o().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2763i30p9o().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2763i30p9o().b[15][0]++;
          cov_2763i30p9o().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2763i30p9o().b[15][1]++;
        }
        cov_2763i30p9o().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2763i30p9o().b[21][0]++;
          cov_2763i30p9o().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2763i30p9o().b[21][1]++;
        }
        cov_2763i30p9o().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2763i30p9o().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2763i30p9o().b[22][1]++;
            cov_2763i30p9o().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_2763i30p9o().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2763i30p9o().b[22][2]++;
            cov_2763i30p9o().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_2763i30p9o().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2763i30p9o().b[22][3]++;
            cov_2763i30p9o().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_2763i30p9o().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2763i30p9o().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_2763i30p9o().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2763i30p9o().b[22][4]++;
            cov_2763i30p9o().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2763i30p9o().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2763i30p9o().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2763i30p9o().b[22][5]++;
            cov_2763i30p9o().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_2763i30p9o().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2763i30p9o().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2763i30p9o().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2763i30p9o().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2763i30p9o().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2763i30p9o().b[23][0]++;
              cov_2763i30p9o().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2763i30p9o().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2763i30p9o().b[23][1]++;
            }
            cov_2763i30p9o().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_2763i30p9o().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2763i30p9o().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2763i30p9o().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2763i30p9o().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2763i30p9o().b[26][0]++;
              cov_2763i30p9o().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2763i30p9o().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2763i30p9o().b[26][1]++;
            }
            cov_2763i30p9o().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_2763i30p9o().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2763i30p9o().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2763i30p9o().b[28][0]++;
              cov_2763i30p9o().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2763i30p9o().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_2763i30p9o().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2763i30p9o().b[28][1]++;
            }
            cov_2763i30p9o().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_2763i30p9o().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_2763i30p9o().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2763i30p9o().b[30][0]++;
              cov_2763i30p9o().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2763i30p9o().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2763i30p9o().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2763i30p9o().b[30][1]++;
            }
            cov_2763i30p9o().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2763i30p9o().b[32][0]++;
              cov_2763i30p9o().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2763i30p9o().b[32][1]++;
            }
            cov_2763i30p9o().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2763i30p9o().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2763i30p9o().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2763i30p9o().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2763i30p9o().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2763i30p9o().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2763i30p9o().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2763i30p9o().b[33][0]++;
      cov_2763i30p9o().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2763i30p9o().b[33][1]++;
    }
    cov_2763i30p9o().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2763i30p9o().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2763i30p9o().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_2763i30p9o().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2763i30p9o().s[79]++;
exports.GET = exports.PUT = void 0;
var server_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[80]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[81]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[82]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[83]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[84]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[85]++, require("@/lib/rateLimit"));
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[86]++, require("@/lib/services/consolidated-cache-service"));
var cache_invalidation_service_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[87]++, require("@/lib/services/cache-invalidation-service"));
var consolidated_cache_service_2 =
/* istanbul ignore next */
(cov_2763i30p9o().s[88]++, require("@/lib/services/consolidated-cache-service"));
var zod_1 =
/* istanbul ignore next */
(cov_2763i30p9o().s[89]++, require("zod"));
var updateProgressSchema =
/* istanbul ignore next */
(cov_2763i30p9o().s[90]++, zod_1.z.object({
  status: zod_1.z.enum(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED']),
  timeSpent: zod_1.z.number().min(0).optional(),
  score: zod_1.z.number().min(0).max(100).optional(),
  notes: zod_1.z.string().max(1000).optional()
}));
// PUT - Update step progress
/* istanbul ignore next */
cov_2763i30p9o().s[91]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_2763i30p9o().f[15]++;
  cov_2763i30p9o().s[92]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_2763i30p9o().f[16]++;
    var params =
    /* istanbul ignore next */
    (cov_2763i30p9o().s[93]++, _b.params);
    /* istanbul ignore next */
    cov_2763i30p9o().s[94]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_2763i30p9o().f[17]++;
      cov_2763i30p9o().s[95]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 200
      },
      // 200 updates per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_2763i30p9o().f[18]++;
        cov_2763i30p9o().s[96]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2763i30p9o().f[19]++;
          var session, userId, _a, learningPathId, stepId, body, validation, _b, status, timeSpent, score, notes, enrollment, step, currentProgress, updateData, updatedProgress, allStepProgress, completedSteps, totalSteps, progressPercent, totalTimeSpent, pathStatus, currentStepId, nextStep, updatedEnrollment, today, learningPath, _i, _c, skill, cacheService, cacheInvalidationService, learningPath, _d, _e, skill, cacheError_1;
          var _f;
          /* istanbul ignore next */
          cov_2763i30p9o().s[97]++;
          return __generator(this, function (_g) {
            /* istanbul ignore next */
            cov_2763i30p9o().f[20]++;
            cov_2763i30p9o().s[98]++;
            switch (_g.label) {
              case 0:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][0]++;
                cov_2763i30p9o().s[99]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][1]++;
                cov_2763i30p9o().s[100]++;
                session = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[101]++;
                if (!(
                /* istanbul ignore next */
                (cov_2763i30p9o().b[38][0]++, (_f =
                /* istanbul ignore next */
                (cov_2763i30p9o().b[40][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[40][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[39][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[39][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[38][1]++, _f === void 0) ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[37][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[37][1]++, _f.id))) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[36][0]++;
                  cov_2763i30p9o().s[102]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[36][1]++;
                }
                cov_2763i30p9o().s[103]++;
                userId = session.user.id;
                /* istanbul ignore next */
                cov_2763i30p9o().s[104]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][2]++;
                cov_2763i30p9o().s[105]++;
                _a = _g.sent(), learningPathId = _a.id, stepId = _a.stepId;
                /* istanbul ignore next */
                cov_2763i30p9o().s[106]++;
                return [4 /*yield*/, request.json()];
              case 3:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][3]++;
                cov_2763i30p9o().s[107]++;
                body = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[108]++;
                validation = updateProgressSchema.safeParse(body);
                /* istanbul ignore next */
                cov_2763i30p9o().s[109]++;
                if (!validation.success) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[41][0]++;
                  cov_2763i30p9o().s[110]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Invalid request data',
                    details: validation.error.errors
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[41][1]++;
                }
                cov_2763i30p9o().s[111]++;
                _b = validation.data, status = _b.status, timeSpent = _b.timeSpent, score = _b.score, notes = _b.notes;
                /* istanbul ignore next */
                cov_2763i30p9o().s[112]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPath.findUnique({
                  where: {
                    userId_learningPathId: {
                      userId: userId,
                      learningPathId: learningPathId
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][4]++;
                cov_2763i30p9o().s[113]++;
                enrollment = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[114]++;
                if (!enrollment) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[42][0]++;
                  cov_2763i30p9o().s[115]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Not enrolled in this learning path'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[42][1]++;
                }
                cov_2763i30p9o().s[116]++;
                return [4 /*yield*/, prisma_1.prisma.learningPathStep.findUnique({
                  where: {
                    id: stepId
                  },
                  include: {
                    learningPath: {
                      select: {
                        id: true
                      }
                    }
                  }
                })];
              case 5:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][5]++;
                cov_2763i30p9o().s[117]++;
                step = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[118]++;
                if (
                /* istanbul ignore next */
                (cov_2763i30p9o().b[44][0]++, !step) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[44][1]++, step.learningPath.id !== learningPathId)) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[43][0]++;
                  cov_2763i30p9o().s[119]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Step not found in this learning path'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[43][1]++;
                }
                cov_2763i30p9o().s[120]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPathProgress.findUnique({
                  where: {
                    userId_stepId: {
                      userId: userId,
                      stepId: stepId
                    }
                  }
                })];
              case 6:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][6]++;
                cov_2763i30p9o().s[121]++;
                currentProgress = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[122]++;
                if (!currentProgress) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[45][0]++;
                  cov_2763i30p9o().s[123]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Progress record not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[45][1]++;
                }
                cov_2763i30p9o().s[124]++;
                updateData = __assign(__assign(__assign({
                  status: status
                },
                /* istanbul ignore next */
                (cov_2763i30p9o().b[46][0]++, timeSpent !== undefined) &&
                /* istanbul ignore next */
                (cov_2763i30p9o().b[46][1]++, {
                  timeSpent: currentProgress.timeSpent + timeSpent
                })),
                /* istanbul ignore next */
                (cov_2763i30p9o().b[47][0]++, score !== undefined) &&
                /* istanbul ignore next */
                (cov_2763i30p9o().b[47][1]++, {
                  score: score
                })),
                /* istanbul ignore next */
                (cov_2763i30p9o().b[48][0]++, notes !== undefined) &&
                /* istanbul ignore next */
                (cov_2763i30p9o().b[48][1]++, {
                  notes: notes
                }));
                // Set timestamps based on status
                /* istanbul ignore next */
                cov_2763i30p9o().s[125]++;
                if (
                /* istanbul ignore next */
                (cov_2763i30p9o().b[50][0]++, status === 'IN_PROGRESS') &&
                /* istanbul ignore next */
                (cov_2763i30p9o().b[50][1]++, !currentProgress.startedAt)) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[49][0]++;
                  cov_2763i30p9o().s[126]++;
                  updateData.startedAt = new Date();
                } else {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[49][1]++;
                  cov_2763i30p9o().s[127]++;
                  if (
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[52][0]++, status === 'COMPLETED') &&
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[52][1]++, currentProgress.status !== 'COMPLETED')) {
                    /* istanbul ignore next */
                    cov_2763i30p9o().b[51][0]++;
                    cov_2763i30p9o().s[128]++;
                    updateData.completedAt = new Date();
                    /* istanbul ignore next */
                    cov_2763i30p9o().s[129]++;
                    updateData.attempts = currentProgress.attempts + 1;
                  } else
                  /* istanbul ignore next */
                  {
                    cov_2763i30p9o().b[51][1]++;
                  }
                }
                /* istanbul ignore next */
                cov_2763i30p9o().s[130]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPathProgress.update({
                  where: {
                    userId_stepId: {
                      userId: userId,
                      stepId: stepId
                    }
                  },
                  data: updateData
                })];
              case 7:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][7]++;
                cov_2763i30p9o().s[131]++;
                updatedProgress = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[132]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPathProgress.findMany({
                  where: {
                    userId: userId,
                    userLearningPathId: enrollment.id
                  }
                })];
              case 8:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][8]++;
                cov_2763i30p9o().s[133]++;
                allStepProgress = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[134]++;
                completedSteps = allStepProgress.filter(function (p) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().f[21]++;
                  cov_2763i30p9o().s[135]++;
                  return p.status === 'COMPLETED';
                }).length;
                /* istanbul ignore next */
                cov_2763i30p9o().s[136]++;
                totalSteps = allStepProgress.length;
                /* istanbul ignore next */
                cov_2763i30p9o().s[137]++;
                progressPercent = totalSteps > 0 ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[53][0]++, Math.round(completedSteps / totalSteps * 100)) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[53][1]++, 0);
                /* istanbul ignore next */
                cov_2763i30p9o().s[138]++;
                totalTimeSpent = allStepProgress.reduce(function (sum, p) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().f[22]++;
                  cov_2763i30p9o().s[139]++;
                  return sum + p.timeSpent;
                }, 0);
                /* istanbul ignore next */
                cov_2763i30p9o().s[140]++;
                pathStatus = enrollment.status;
                /* istanbul ignore next */
                cov_2763i30p9o().s[141]++;
                if (
                /* istanbul ignore next */
                (cov_2763i30p9o().b[55][0]++, completedSteps === totalSteps) &&
                /* istanbul ignore next */
                (cov_2763i30p9o().b[55][1]++, totalSteps > 0)) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[54][0]++;
                  cov_2763i30p9o().s[142]++;
                  pathStatus = 'COMPLETED';
                } else {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[54][1]++;
                  cov_2763i30p9o().s[143]++;
                  if (
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[57][0]++, completedSteps > 0) ||
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[57][1]++, allStepProgress.some(function (p) {
                    /* istanbul ignore next */
                    cov_2763i30p9o().f[23]++;
                    cov_2763i30p9o().s[144]++;
                    return p.status === 'IN_PROGRESS';
                  }))) {
                    /* istanbul ignore next */
                    cov_2763i30p9o().b[56][0]++;
                    cov_2763i30p9o().s[145]++;
                    pathStatus = 'IN_PROGRESS';
                  } else
                  /* istanbul ignore next */
                  {
                    cov_2763i30p9o().b[56][1]++;
                  }
                }
                /* istanbul ignore next */
                cov_2763i30p9o().s[146]++;
                currentStepId = enrollment.currentStepId;
                /* istanbul ignore next */
                cov_2763i30p9o().s[147]++;
                if (!(status === 'COMPLETED')) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[58][0]++;
                  cov_2763i30p9o().s[148]++;
                  return [3 /*break*/, 10];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[58][1]++;
                }
                cov_2763i30p9o().s[149]++;
                return [4 /*yield*/, prisma_1.prisma.learningPathStep.findFirst({
                  where: {
                    learningPathId: learningPathId,
                    stepOrder: {
                      gt: step.stepOrder
                    }
                  },
                  orderBy: {
                    stepOrder: 'asc'
                  }
                })];
              case 9:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][9]++;
                cov_2763i30p9o().s[150]++;
                nextStep = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[151]++;
                currentStepId =
                /* istanbul ignore next */
                (cov_2763i30p9o().b[59][0]++,
                /* istanbul ignore next */
                (cov_2763i30p9o().b[61][0]++, nextStep === null) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[61][1]++, nextStep === void 0) ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[60][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[60][1]++, nextStep.id)) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[59][1]++, null);
                /* istanbul ignore next */
                cov_2763i30p9o().s[152]++;
                _g.label = 10;
              case 10:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][10]++;
                cov_2763i30p9o().s[153]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPath.update({
                  where: {
                    userId_learningPathId: {
                      userId: userId,
                      learningPathId: learningPathId
                    }
                  },
                  data: __assign(__assign({
                    status: pathStatus,
                    completedSteps: completedSteps,
                    progressPercent: progressPercent,
                    totalTimeSpent: totalTimeSpent,
                    currentStepId: currentStepId,
                    lastAccessedAt: new Date()
                  },
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[62][0]++, pathStatus === 'COMPLETED') &&
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[62][1]++, !enrollment.completedAt) &&
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[62][2]++, {
                    completedAt: new Date()
                  })),
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[63][0]++, pathStatus === 'IN_PROGRESS') &&
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[63][1]++, !enrollment.startedAt) &&
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[63][2]++, {
                    startedAt: new Date()
                  }))
                })];
              case 11:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][11]++;
                cov_2763i30p9o().s[154]++;
                updatedEnrollment = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[155]++;
                today = new Date();
                /* istanbul ignore next */
                cov_2763i30p9o().s[156]++;
                today.setHours(0, 0, 0, 0);
                /* istanbul ignore next */
                cov_2763i30p9o().s[157]++;
                return [4 /*yield*/, prisma_1.prisma.learningAnalytics.upsert({
                  where: {
                    userId_date: {
                      userId: userId,
                      date: today
                    }
                  },
                  update: __assign(__assign({
                    timeSpent: {
                      increment:
                      /* istanbul ignore next */
                      (cov_2763i30p9o().b[64][0]++, timeSpent) ||
                      /* istanbul ignore next */
                      (cov_2763i30p9o().b[64][1]++, 0)
                    }
                  },
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[65][0]++, status === 'COMPLETED') &&
                  /* istanbul ignore next */
                  (cov_2763i30p9o().b[65][1]++, {
                    resourcesViewed: {
                      increment: 1
                    }
                  })), {
                    date: new Date()
                  }),
                  create: {
                    userId: userId,
                    date: today,
                    timeSpent:
                    /* istanbul ignore next */
                    (cov_2763i30p9o().b[66][0]++, timeSpent) ||
                    /* istanbul ignore next */
                    (cov_2763i30p9o().b[66][1]++, 0),
                    resourcesViewed: status === 'COMPLETED' ?
                    /* istanbul ignore next */
                    (cov_2763i30p9o().b[67][0]++, 1) :
                    /* istanbul ignore next */
                    (cov_2763i30p9o().b[67][1]++, 0),
                    pathsProgressed: 0 // This was already incremented on enrollment
                  }
                })];
              case 12:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][12]++;
                cov_2763i30p9o().s[158]++;
                _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[159]++;
                if (!(
                /* istanbul ignore next */
                (cov_2763i30p9o().b[69][0]++, status === 'COMPLETED') &&
                /* istanbul ignore next */
                (cov_2763i30p9o().b[69][1]++, step.learningPath))) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[68][0]++;
                  cov_2763i30p9o().s[160]++;
                  return [3 /*break*/, 17];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[68][1]++;
                }
                cov_2763i30p9o().s[161]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.findUnique({
                  where: {
                    id: learningPathId
                  },
                  include: {
                    skills: true
                  }
                })];
              case 13:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][13]++;
                cov_2763i30p9o().s[162]++;
                learningPath = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[163]++;
                if (!(
                /* istanbul ignore next */
                (cov_2763i30p9o().b[72][0]++, learningPath === null) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[72][1]++, learningPath === void 0) ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[71][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[71][1]++, learningPath.skills.length))) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[70][0]++;
                  cov_2763i30p9o().s[164]++;
                  return [3 /*break*/, 17];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[70][1]++;
                }
                cov_2763i30p9o().s[165]++;
                _i = 0, _c = learningPath.skills;
                /* istanbul ignore next */
                cov_2763i30p9o().s[166]++;
                _g.label = 14;
              case 14:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][14]++;
                cov_2763i30p9o().s[167]++;
                if (!(_i < _c.length)) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[73][0]++;
                  cov_2763i30p9o().s[168]++;
                  return [3 /*break*/, 17];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[73][1]++;
                }
                cov_2763i30p9o().s[169]++;
                skill = _c[_i];
                /* istanbul ignore next */
                cov_2763i30p9o().s[170]++;
                return [4 /*yield*/, prisma_1.prisma.userSkillProgress.upsert({
                  where: {
                    userId_skillId: {
                      userId: userId,
                      skillId: skill.id
                    }
                  },
                  update: {
                    progressPoints: {
                      increment: 10 // Points per completed step
                    },
                    lastPracticed: new Date(),
                    practiceHours: {
                      increment: Math.round((
                      /* istanbul ignore next */
                      (cov_2763i30p9o().b[74][0]++, timeSpent) ||
                      /* istanbul ignore next */
                      (cov_2763i30p9o().b[74][1]++, 0)) / 60)
                    }
                  },
                  create: {
                    userId: userId,
                    skillId: skill.id,
                    currentLevel: 'BEGINNER',
                    progressPoints: 10,
                    lastPracticed: new Date(),
                    practiceHours: Math.round((
                    /* istanbul ignore next */
                    (cov_2763i30p9o().b[75][0]++, timeSpent) ||
                    /* istanbul ignore next */
                    (cov_2763i30p9o().b[75][1]++, 0)) / 60)
                  }
                })];
              case 15:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][15]++;
                cov_2763i30p9o().s[171]++;
                _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[172]++;
                _g.label = 16;
              case 16:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][16]++;
                cov_2763i30p9o().s[173]++;
                _i++;
                /* istanbul ignore next */
                cov_2763i30p9o().s[174]++;
                return [3 /*break*/, 14];
              case 17:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][17]++;
                cov_2763i30p9o().s[175]++;
                _g.trys.push([17, 24,, 25]);
                /* istanbul ignore next */
                cov_2763i30p9o().s[176]++;
                cacheService = new consolidated_cache_service_2.ConsolidatedCacheService();
                /* istanbul ignore next */
                cov_2763i30p9o().s[177]++;
                cacheInvalidationService = new cache_invalidation_service_1.CacheInvalidationService(cacheService);
                // Invalidate learning progress and skill-related caches
                /* istanbul ignore next */
                cov_2763i30p9o().s[178]++;
                return [4 /*yield*/, cacheInvalidationService.smartInvalidate({
                  table: 'UserLearningPathProgress',
                  operation: 'UPDATE',
                  userId: userId,
                  data: {
                    stepId: stepId,
                    learningPathId: learningPathId,
                    status: status
                  }
                })];
              case 18:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][18]++;
                cov_2763i30p9o().s[179]++;
                // Invalidate learning progress and skill-related caches
                _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[180]++;
                if (!(
                /* istanbul ignore next */
                (cov_2763i30p9o().b[77][0]++, status === 'COMPLETED') &&
                /* istanbul ignore next */
                (cov_2763i30p9o().b[77][1]++, step.learningPath))) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[76][0]++;
                  cov_2763i30p9o().s[181]++;
                  return [3 /*break*/, 23];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[76][1]++;
                }
                cov_2763i30p9o().s[182]++;
                return [4 /*yield*/, prisma_1.prisma.learningPath.findUnique({
                  where: {
                    id: learningPathId
                  },
                  include: {
                    skills: true
                  }
                })];
              case 19:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][19]++;
                cov_2763i30p9o().s[183]++;
                learningPath = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[184]++;
                if (!(
                /* istanbul ignore next */
                (cov_2763i30p9o().b[80][0]++, learningPath === null) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[80][1]++, learningPath === void 0) ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[79][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[79][1]++, learningPath.skills.length))) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[78][0]++;
                  cov_2763i30p9o().s[185]++;
                  return [3 /*break*/, 23];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[78][1]++;
                }
                cov_2763i30p9o().s[186]++;
                _d = 0, _e = learningPath.skills;
                /* istanbul ignore next */
                cov_2763i30p9o().s[187]++;
                _g.label = 20;
              case 20:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][20]++;
                cov_2763i30p9o().s[188]++;
                if (!(_d < _e.length)) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[81][0]++;
                  cov_2763i30p9o().s[189]++;
                  return [3 /*break*/, 23];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[81][1]++;
                }
                cov_2763i30p9o().s[190]++;
                skill = _e[_d];
                /* istanbul ignore next */
                cov_2763i30p9o().s[191]++;
                return [4 /*yield*/, cacheInvalidationService.invalidateSkillCaches(userId, skill.id)];
              case 21:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][21]++;
                cov_2763i30p9o().s[192]++;
                _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[193]++;
                _g.label = 22;
              case 22:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][22]++;
                cov_2763i30p9o().s[194]++;
                _d++;
                /* istanbul ignore next */
                cov_2763i30p9o().s[195]++;
                return [3 /*break*/, 20];
              case 23:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][23]++;
                cov_2763i30p9o().s[196]++;
                return [3 /*break*/, 25];
              case 24:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][24]++;
                cov_2763i30p9o().s[197]++;
                cacheError_1 = _g.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[198]++;
                console.warn('Failed to invalidate learning path progress caches:', cacheError_1);
                /* istanbul ignore next */
                cov_2763i30p9o().s[199]++;
                return [3 /*break*/, 25];
              case 25:
                /* istanbul ignore next */
                cov_2763i30p9o().b[35][25]++;
                cov_2763i30p9o().s[200]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: {
                    stepProgress: updatedProgress,
                    pathProgress: {
                      status: pathStatus,
                      completedSteps: completedSteps,
                      totalSteps: totalSteps,
                      progressPercent: progressPercent,
                      totalTimeSpent: totalTimeSpent,
                      currentStepId: currentStepId
                    }
                  },
                  message: 'Progress updated successfully'
                })];
            }
          });
        });
      })];
    });
  });
});
// GET - Get step progress
/* istanbul ignore next */
cov_2763i30p9o().s[201]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_2763i30p9o().f[24]++;
  cov_2763i30p9o().s[202]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_2763i30p9o().f[25]++;
    var params =
    /* istanbul ignore next */
    (cov_2763i30p9o().s[203]++, _b.params);
    /* istanbul ignore next */
    cov_2763i30p9o().s[204]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_2763i30p9o().f[26]++;
      cov_2763i30p9o().s[205]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 300
      },
      // 300 requests per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_2763i30p9o().f[27]++;
        cov_2763i30p9o().s[206]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2763i30p9o().f[28]++;
          var session, userId, _a, learningPathId, stepId, cacheKey, cached, progress;
          var _b;
          /* istanbul ignore next */
          cov_2763i30p9o().s[207]++;
          return __generator(this, function (_c) {
            /* istanbul ignore next */
            cov_2763i30p9o().f[29]++;
            cov_2763i30p9o().s[208]++;
            switch (_c.label) {
              case 0:
                /* istanbul ignore next */
                cov_2763i30p9o().b[82][0]++;
                cov_2763i30p9o().s[209]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_2763i30p9o().b[82][1]++;
                cov_2763i30p9o().s[210]++;
                session = _c.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[211]++;
                if (!(
                /* istanbul ignore next */
                (cov_2763i30p9o().b[85][0]++, (_b =
                /* istanbul ignore next */
                (cov_2763i30p9o().b[87][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[87][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[86][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[86][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_2763i30p9o().b[85][1]++, _b === void 0) ?
                /* istanbul ignore next */
                (cov_2763i30p9o().b[84][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2763i30p9o().b[84][1]++, _b.id))) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[83][0]++;
                  cov_2763i30p9o().s[212]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[83][1]++;
                }
                cov_2763i30p9o().s[213]++;
                userId = session.user.id;
                /* istanbul ignore next */
                cov_2763i30p9o().s[214]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_2763i30p9o().b[82][2]++;
                cov_2763i30p9o().s[215]++;
                _a = _c.sent(), learningPathId = _a.id, stepId = _a.stepId;
                /* istanbul ignore next */
                cov_2763i30p9o().s[216]++;
                cacheKey = "step_progress:".concat(stepId, ":").concat(userId);
                /* istanbul ignore next */
                cov_2763i30p9o().s[217]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.get(cacheKey)];
              case 3:
                /* istanbul ignore next */
                cov_2763i30p9o().b[82][3]++;
                cov_2763i30p9o().s[218]++;
                cached = _c.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[219]++;
                if (cached) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[88][0]++;
                  cov_2763i30p9o().s[220]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: true,
                    data: cached,
                    cached: true
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[88][1]++;
                }
                cov_2763i30p9o().s[221]++;
                return [4 /*yield*/, prisma_1.prisma.userLearningPathProgress.findUnique({
                  where: {
                    userId_stepId: {
                      userId: userId,
                      stepId: stepId
                    }
                  },
                  include: {
                    step: {
                      include: {
                        resource: {
                          select: {
                            id: true,
                            title: true,
                            description: true,
                            type: true,
                            url: true,
                            duration: true
                          }
                        }
                      }
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_2763i30p9o().b[82][4]++;
                cov_2763i30p9o().s[222]++;
                progress = _c.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[223]++;
                if (!progress) {
                  /* istanbul ignore next */
                  cov_2763i30p9o().b[89][0]++;
                  cov_2763i30p9o().s[224]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Progress record not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2763i30p9o().b[89][1]++;
                }
                // Cache for 1 minute
                cov_2763i30p9o().s[225]++;
                return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.set(cacheKey, progress, {
                  ttl: 60000,
                  tags: ['step_progress', stepId, userId]
                })];
              case 5:
                /* istanbul ignore next */
                cov_2763i30p9o().b[82][5]++;
                cov_2763i30p9o().s[226]++;
                // Cache for 1 minute
                _c.sent();
                /* istanbul ignore next */
                cov_2763i30p9o().s[227]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: progress
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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