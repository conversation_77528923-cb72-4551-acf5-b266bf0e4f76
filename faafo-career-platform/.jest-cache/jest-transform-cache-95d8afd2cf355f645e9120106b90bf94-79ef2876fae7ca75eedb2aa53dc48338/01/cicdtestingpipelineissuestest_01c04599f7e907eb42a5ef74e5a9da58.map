{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/cicd-testing-pipeline-issues.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;;;AAEH,yCAAuE;AACvE,0CAAoB;AACpB,8CAAwB;AAExB,IAAA,kBAAQ,EAAC,+BAA+B,EAAE;IACxC,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2CAA2C,EAAE;QACpD,IAAA,YAAE,EAAC,kEAAkE,EAAE;YACrE,IAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,CAAC;YAEjE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;gBACzE,IAAM,SAAO,GAAG,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC;gBAC1C,IAAM,cAAY,GAAG,EAAE,CAAC;gBAExB,8BAA8B;gBAC9B,IAAM,eAAe,GAAG;oBACtB,MAAM,EAAE,sBAAsB;oBAC9B,SAAS,EAAE,0CAA0C;oBACrD,eAAe,EAAE,oBAAoB;oBACrC,YAAY,EAAE,wBAAwB;oBACtC,WAAW,EAAE,iBAAiB;oBAC9B,kBAAkB,EAAE,wBAAwB;oBAC5C,UAAU,EAAE,kBAAkB;oBAC9B,kBAAkB,EAAE,mBAAmB;oBACvC,eAAe,EAAE,gBAAgB;iBAClC,CAAC;gBAEF,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,UAAC,EAAqB;wBAApB,MAAM,QAAA,EAAE,WAAW,QAAA;oBAC3D,IAAI,CAAC,SAAO,CAAC,MAAM,CAAC,EAAE,CAAC;wBACrB,cAAY,CAAC,IAAI,CAAC,kBAAW,MAAM,eAAK,WAAW,CAAE,CAAC,CAAC;oBACzD,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,0BAA0B;gBAC1B,IAAM,YAAY,GAAG,SAAO,CAAC,SAAS,CAAC,CAAC;gBACxC,IAAI,YAAY,EAAE,CAAC;oBACjB,oCAAoC;oBACpC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;wBACzC,cAAY,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;oBAC9D,CAAC;oBAED,sCAAsC;oBACtC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBACnF,cAAY,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;oBAChE,CAAC;oBAED,0CAA0C;oBAC1C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,yBAAyB,CAAC,EAAE,CAAC;wBACtD,cAAY,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;oBACtE,CAAC;gBACH,CAAC;gBAED,6CAA6C;gBAC7C,IAAM,UAAU,GAAG,SAAO,CAAC,MAAM,CAAC,CAAC;gBACnC,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;oBACvD,cAAY,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBAC7E,CAAC;gBAED,yFAAyF;gBACzF,IAAA,gBAAM,EAAC,cAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,8CAA8C;gBAC9C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,qEAAqE,EAAE;YACxE,IAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,CAAC;YAEjE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;gBACzE,IAAM,OAAO,GAAG,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC;gBAC1C,IAAM,qBAAmB,GAAG,EAAE,CAAC;gBAE/B,wCAAwC;gBACxC,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAC,EAAK;wBAAJ,GAAG,QAAA;oBAAM,OAAA,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC;gBAAtB,CAAsB,CAAC,CAAC;gBAEtF,WAAW,CAAC,OAAO,CAAC,UAAC,EAA2B;wBAA1B,UAAU,QAAA,EAAE,aAAa,QAAA;oBAC7C,wCAAwC;oBACxC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBACnE,qBAAmB,CAAC,IAAI,CAAC,UAAG,UAAU,oCAAiC,CAAC,CAAC;oBAC3E,CAAC;oBAED,qDAAqD;oBACrD,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;wBACtF,qBAAmB,CAAC,IAAI,CAAC,UAAG,UAAU,6CAA0C,CAAC,CAAC;oBACpF,CAAC;oBAED,0CAA0C;oBAC1C,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;wBACxE,qBAAmB,CAAC,IAAI,CAAC,UAAG,UAAU,yCAAsC,CAAC,CAAC;oBAChF,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,mEAAmE;gBACnE,IAAA,gBAAM,EAAC,qBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,kDAAkD,EAAE;QAC3D,IAAA,YAAE,EAAC,mEAAmE,EAAE;YACtE,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAElE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAC9D,IAAM,gBAAc,GAAG,EAAE,CAAC;gBAE1B,8BAA8B;gBAC9B,IAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;gBAE7F,IAAI,aAAa,EAAE,CAAC;oBAClB,IAAM,YAAU,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;oBAEpC,6CAA6C;oBAC7C,IAAM,kBAAkB,GAAG;wBACzB,QAAQ,EAAE,EAAE;wBACZ,SAAS,EAAE,EAAE;wBACb,KAAK,EAAE,EAAE;wBACT,UAAU,EAAE,EAAE;qBACf,CAAC;oBAEF,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,UAAC,EAAkB;4BAAjB,MAAM,QAAA,EAAE,QAAQ,QAAA;wBAC3D,IAAM,WAAW,GAAG,YAAU,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,UAAG,MAAM,gBAAa,CAAC,CAAC,CAAC;wBACzE,IAAI,WAAW,EAAE,CAAC;4BAChB,IAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC9C,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;gCAC5B,gBAAc,CAAC,IAAI,CAAC,UAAG,MAAM,wBAAc,YAAY,wCAAyB,QAAQ,OAAI,CAAC,CAAC;4BAChG,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,gBAAc,CAAC,IAAI,CAAC,kBAAW,MAAM,wBAAqB,CAAC,CAAC;wBAC9D,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,gBAAc,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;gBAClE,CAAC;gBAED,qCAAqC;gBACrC,IAAM,yBAAyB,GAAG,aAAa,CAAC,QAAQ,CAAC,mBAAmB,CAAC;oBAC7C,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEjG,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBAC/B,gBAAc,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBACnE,CAAC;gBAED,0EAA0E;gBAC1E,IAAA,gBAAM,EAAC,gBAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,6CAA6C;gBAC7C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,qEAAqE,EAAE;YACxE,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAElE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAC9D,IAAM,iBAAe,GAAG,EAAE,CAAC;gBAE3B,gCAAgC;gBAChC,IAAM,+BAA+B,GAAG,aAAa,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBAE3G,IAAI,+BAA+B,EAAE,CAAC;oBACpC,IAAM,YAAU,GAAG,+BAA+B,CAAC,CAAC,CAAC,CAAC;oBAEtD,sBAAsB;oBACtB,IAAM,kBAAkB,GAAG;wBACzB,cAAc;wBACd,WAAW;wBACX,UAAU;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;qBACR,CAAC;oBAEF,kBAAkB,CAAC,OAAO,CAAC,UAAA,SAAS;wBAClC,IAAI,CAAC,YAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;4BACpC,iBAAe,CAAC,IAAI,CAAC,sCAA+B,SAAS,CAAE,CAAC,CAAC;wBACnE,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,iBAAe,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;gBACnE,CAAC;gBAED,8CAA8C;gBAC9C,IAAM,wBAAwB,GAAG,aAAa,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;gBAE7F,IAAI,wBAAwB,EAAE,CAAC;oBAC7B,IAAM,WAAW,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC;oBAEhD,8BAA8B;oBAC9B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;wBAC5E,iBAAe,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;oBAC3E,CAAC;oBAED,4BAA4B;oBAC5B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBACvF,iBAAe,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;oBACvE,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,iBAAe,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;gBAC/D,CAAC;gBAED,yEAAyE;gBACzE,IAAA,gBAAM,EAAC,iBAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,4CAA4C,EAAE;QACrD,IAAA,YAAE,EAAC,gEAAgE,EAAE;YACnE,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;YACnE,IAAM,gBAAgB,GAAG,EAAE,CAAC;YAE5B,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,IAAM,eAAa,GAAG,YAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAA,IAAI;oBAC5D,OAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAA/C,CAA+C,CAChD,CAAC;gBAEF,IAAI,eAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/B,gBAAgB,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBAClE,CAAC;qBAAM,CAAC;oBACN,+BAA+B;oBAC/B,IAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;oBAClD,IAAM,mBAAmB,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAA,QAAQ;wBACzD,OAAA,eAAa,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAArC,CAAqC,CAAC;oBAAjE,CAAiE,CAClE,CAAC;oBAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBACzB,gBAAgB,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;oBACnE,CAAC;oBAED,2BAA2B;oBAC3B,eAAa,CAAC,OAAO,CAAC,UAAA,YAAY;wBAChC,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;wBAC3D,IAAM,eAAe,GAAG,YAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;wBAE9D,yCAAyC;wBACzC,IAAM,kBAAkB,GAAG;4BACzB,KAAK,EAAE,iBAAiB;4BACxB,OAAO,EAAE,kBAAkB;4BAC3B,UAAU,EAAE,uBAAuB;4BACnC,wBAAwB,EAAE,gBAAgB;4BAC1C,0BAA0B,EAAE,gBAAgB;4BAC5C,QAAQ,EAAE,0BAA0B;4BACpC,UAAU,CAAC,iBAAiB;yBAC7B,CAAC;wBAEF,kBAAkB,CAAC,OAAO,CAAC,UAAA,SAAS;4BAClC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gCACzC,gBAAgB,CAAC,IAAI,CAAC,UAAG,YAAY,uBAAa,SAAS,CAAE,CAAC,CAAC;4BACjE,CAAC;wBACH,CAAC,CAAC,CAAC;wBAEH,kCAAkC;wBAClC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;4BACvF,gBAAgB,CAAC,IAAI,CAAC,UAAG,YAAY,oCAAiC,CAAC,CAAC;wBAC1E,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,gBAAgB,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAChE,CAAC;YAED,4EAA4E;YAC5E,IAAA,gBAAM,EAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,8DAA8D,EAAE;YACjE,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,6BAA6B;YAC7B,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;YACpD,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAExD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,iBAAiB,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;YACjF,CAAC;iBAAM,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzC,iBAAiB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC1D,CAAC;iBAAM,CAAC;gBACN,IAAM,WAAW,GAAG,YAAE,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBAE3D,8BAA8B;gBAC9B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACnE,iBAAiB,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;YAED,sCAAsC;YACtC,IAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,CAAC;YACjE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;gBAEzE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;oBAChC,iBAAiB,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;YAED,qCAAqC;YACrC,IAAM,iBAAiB,GAAG;gBACxB,sBAAsB;gBACtB,kBAAkB;gBAClB,oBAAoB;aACrB,CAAC;YAEF,IAAM,mBAAmB,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAA,MAAM;gBACvD,OAAA,YAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;YAA/C,CAA+C,CAChD,CAAC;YAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,iBAAiB,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAC9D,CAAC;YAED,gEAAgE;YAChE,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,+DAA+D,EAAE;QACxE,IAAA,YAAE,EAAC,0DAA0D,EAAE;YAC7D,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;YACnE,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,IAAM,aAAa,GAAG,YAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAA,IAAI;oBAC5D,OAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAA/C,CAA+C,CAChD,CAAC;gBAEF,aAAa,CAAC,OAAO,CAAC,UAAA,YAAY;oBAChC,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;oBAC3D,IAAM,eAAe,GAAG,YAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBAE9D,oBAAoB;oBACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACtF,iBAAiB,CAAC,IAAI,CAAC,UAAG,YAAY,iCAA8B,CAAC,CAAC;oBACxE,CAAC;oBAED,mCAAmC;oBACnC,IAAI,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnF,iBAAiB,CAAC,IAAI,CAAC,UAAG,YAAY,0CAAuC,CAAC,CAAC;oBACjF,CAAC;oBAED,gCAAgC;oBAChC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;wBACjG,iBAAiB,CAAC,IAAI,CAAC,UAAG,YAAY,2CAAwC,CAAC,CAAC;oBAClF,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,+CAA+C;YAC/C,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAClE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAE9D,8CAA8C;gBAC9C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC1C,iBAAiB,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBAC/E,CAAC;gBAED,kCAAkC;gBAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBAClF,iBAAiB,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;YAED,oEAAoE;YACpE,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,yEAAyE,EAAE;YAC5E,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;YACnE,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,IAAM,aAAa,GAAG,YAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAA,IAAI;oBAC5D,OAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAA/C,CAA+C,CAChD,CAAC;gBAEF,aAAa,CAAC,OAAO,CAAC,UAAA,YAAY;oBAChC,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;oBAC3D,IAAM,eAAe,GAAG,YAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBAE9D,kCAAkC;oBAClC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBAClD,iBAAiB,CAAC,IAAI,CAAC,UAAG,YAAY,wCAAqC,CAAC,CAAC;oBAC/E,CAAC;oBAED,6BAA6B;oBAC7B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;wBACjG,iBAAiB,CAAC,IAAI,CAAC,UAAG,YAAY,wCAAqC,CAAC,CAAC;oBAC/E,CAAC;oBAED,oCAAoC;oBACpC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;wBACpF,iBAAiB,CAAC,IAAI,CAAC,UAAG,YAAY,mCAAgC,CAAC,CAAC;oBAC1E,CAAC;oBAED,wCAAwC;oBACxC,IAAI,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBACzF,iBAAiB,CAAC,IAAI,CAAC,UAAG,YAAY,2CAAwC,CAAC,CAAC;oBAClF,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,+DAA+D;YAC/D,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,sDAAsD,EAAE;QAC/D,IAAA,YAAE,EAAC,uEAAuE,EAAE;YAC1E,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,yCAAyC;YACzC,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAClE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAE9D,oCAAoC;gBACpC,IAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBACzE,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAM,WAAS,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;oBAEpC,wDAAwD;oBACxD,IAAM,iBAAiB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;oBACtD,iBAAiB,CAAC,OAAO,CAAC,UAAA,QAAQ;wBAChC,IAAI,CAAC,WAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;4BAClC,eAAe,CAAC,IAAI,CAAC,kBAAW,QAAQ,oCAAiC,CAAC,CAAC;wBAC7E,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBACpE,CAAC;gBAED,+BAA+B;gBAC/B,IAAM,sBAAsB,GAAG,aAAa,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBACzF,IAAI,sBAAsB,EAAE,CAAC;oBAC3B,IAAM,mBAAiB,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBAEpD,IAAM,yBAAyB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;oBACnE,yBAAyB,CAAC,OAAO,CAAC,UAAA,QAAQ;wBACxC,IAAI,CAAC,mBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;4BAC1C,eAAe,CAAC,IAAI,CAAC,kBAAW,QAAQ,uBAAoB,CAAC,CAAC;wBAChE,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAe,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAED,oCAAoC;YACpC,IAAM,kBAAkB,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;YACnE,kBAAkB,CAAC,OAAO,CAAC,UAAA,GAAG;gBAC5B,IAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;gBAC9C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC5B,eAAe,CAAC,IAAI,CAAC,kBAAW,GAAG,gCAA6B,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,2DAA2D;YAC3D,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,uEAAuE,EAAE;YAC1E,IAAM,gBAAgB,GAAG,EAAE,CAAC;YAE5B,qCAAqC;YACrC,IAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,CAAC;YACjE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;gBAEzE,oCAAoC;gBACpC,IAAM,cAAY,yBAAQ,WAAW,CAAC,YAAY,GAAK,WAAW,CAAC,eAAe,CAAE,CAAC;gBACrF,IAAM,eAAe,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;gBAEhE,IAAM,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,cAAY,CAAC,IAAI,CAAC,EAAlB,CAAkB,CAAC,CAAC;gBACvE,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,gBAAgB,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;YAED,kDAAkD;YAClD,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;YACnE,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,IAAM,aAAa,GAAG,YAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAA,IAAI;oBAC5D,OAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAA/C,CAA+C,CAChD,CAAC;gBAEF,IAAI,wBAAsB,GAAG,KAAK,CAAC;gBACnC,aAAa,CAAC,OAAO,CAAC,UAAA,YAAY;oBAChC,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;oBAC3D,IAAM,eAAe,GAAG,YAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBAE9D,IAAI,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;wBACnF,wBAAsB,GAAG,IAAI,CAAC;oBAChC,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,wBAAsB,EAAE,CAAC;oBAC5B,gBAAgB,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;YAED,0EAA0E;YAC1E,IAAA,gBAAM,EAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/cicd-testing-pipeline-issues.test.ts"], "sourcesContent": ["/**\n * CI/CD Testing Pipeline Issues Tests\n * \n * These tests prove CI/CD pipeline issues including incomplete test scripts,\n * inadequate coverage thresholds, and missing automation.\n * \n * EXPECTED TO FAIL - These tests demonstrate CI/CD pipeline issues that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\nimport fs from 'fs';\nimport path from 'path';\n\ndescribe('CI/CD Testing Pipeline Issues', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Incomplete Test Scripts', () => {\n    it('should fail - package.json test scripts are incomplete for CI/CD', () => {\n      const packageJsonPath = path.join(process.cwd(), 'package.json');\n      \n      if (fs.existsSync(packageJsonPath)) {\n        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));\n        const scripts = packageJson.scripts || {};\n        const scriptIssues = [];\n        \n        // Required CI/CD test scripts\n        const requiredScripts = {\n          'test': 'Basic test execution',\n          'test:ci': 'CI-specific test execution with coverage',\n          'test:coverage': 'Coverage reporting',\n          'test:watch': 'Development watch mode',\n          'test:unit': 'Unit tests only',\n          'test:integration': 'Integration tests only',\n          'test:e2e': 'End-to-end tests',\n          'test:performance': 'Performance tests',\n          'test:security': 'Security tests'\n        };\n        \n        Object.entries(requiredScripts).forEach(([script, description]) => {\n          if (!scripts[script]) {\n            scriptIssues.push(`Missing ${script}: ${description}`);\n          }\n        });\n        \n        // Check CI script quality\n        const testCiScript = scripts['test:ci'];\n        if (testCiScript) {\n          // CI script should include coverage\n          if (!testCiScript.includes('--coverage')) {\n            scriptIssues.push('test:ci script missing --coverage flag');\n          }\n          \n          // CI script should disable watch mode\n          if (testCiScript.includes('--watch') && !testCiScript.includes('--watchAll=false')) {\n            scriptIssues.push('test:ci script should disable watch mode');\n          }\n          \n          // CI script should have proper exit codes\n          if (!testCiScript.includes('--passWithNoTests=false')) {\n            scriptIssues.push('test:ci script should fail when no tests found');\n          }\n        }\n        \n        // Check for parallel execution configuration\n        const testScript = scripts['test'];\n        if (testScript && !testScript.includes('--maxWorkers')) {\n          scriptIssues.push('test script missing --maxWorkers configuration for CI');\n        }\n        \n        // EXPECTED TO FAIL: All required CI/CD scripts should be present and properly configured\n        expect(scriptIssues.length).toBe(0);\n      } else {\n        // EXPECTED TO FAIL: package.json should exist\n        expect(fs.existsSync(packageJsonPath)).toBe(true);\n      }\n    });\n\n    it('should fail - test scripts lack proper error handling and reporting', () => {\n      const packageJsonPath = path.join(process.cwd(), 'package.json');\n      \n      if (fs.existsSync(packageJsonPath)) {\n        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));\n        const scripts = packageJson.scripts || {};\n        const errorHandlingIssues = [];\n        \n        // Check test scripts for error handling\n        const testScripts = Object.entries(scripts).filter(([key]) => key.startsWith('test'));\n        \n        testScripts.forEach(([scriptName, scriptCommand]) => {\n          // Should have proper exit code handling\n          if (!scriptCommand.includes('--') && !scriptCommand.includes('&&')) {\n            errorHandlingIssues.push(`${scriptName}: No error handling or chaining`);\n          }\n          \n          // Coverage scripts should have threshold enforcement\n          if (scriptName.includes('coverage') && !scriptCommand.includes('--coverageThreshold')) {\n            errorHandlingIssues.push(`${scriptName}: Missing coverage threshold enforcement`);\n          }\n          \n          // CI scripts should have proper reporting\n          if (scriptName.includes('ci') && !scriptCommand.includes('--reporters')) {\n            errorHandlingIssues.push(`${scriptName}: Missing CI reporters configuration`);\n          }\n        });\n        \n        // EXPECTED TO FAIL: Test scripts should have proper error handling\n        expect(errorHandlingIssues.length).toBe(0);\n      }\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: Inadequate Coverage Thresholds', () => {\n    it('should fail - coverage thresholds are too low for production code', () => {\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      \n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        const coverageIssues = [];\n        \n        // Extract coverage thresholds\n        const coverageMatch = configContent.match(/coverageThreshold:\\s*{[^}]*global:\\s*{([^}]+)}/s);\n        \n        if (coverageMatch) {\n          const thresholds = coverageMatch[1];\n          \n          // Required minimum thresholds for production\n          const requiredThresholds = {\n            branches: 85,\n            functions: 90,\n            lines: 90,\n            statements: 90\n          };\n          \n          Object.entries(requiredThresholds).forEach(([metric, minValue]) => {\n            const metricMatch = thresholds.match(new RegExp(`${metric}:\\\\s*(\\\\d+)`));\n            if (metricMatch) {\n              const currentValue = parseInt(metricMatch[1]);\n              if (currentValue < minValue) {\n                coverageIssues.push(`${metric} threshold ${currentValue}% too low (should be ≥${minValue}%)`);\n              }\n            } else {\n              coverageIssues.push(`Missing ${metric} coverage threshold`);\n            }\n          });\n        } else {\n          coverageIssues.push('No global coverage thresholds configured');\n        }\n        \n        // Check for per-directory thresholds\n        const hasPerDirectoryThresholds = configContent.includes('coverageThreshold') && \n                                        (configContent.includes('src/') || configContent.includes('./'));\n        \n        if (!hasPerDirectoryThresholds) {\n          coverageIssues.push('Missing per-directory coverage thresholds');\n        }\n        \n        // EXPECTED TO FAIL: Coverage thresholds should be adequate for production\n        expect(coverageIssues.length).toBe(0);\n      } else {\n        // EXPECTED TO FAIL: Jest config should exist\n        expect(fs.existsSync(jestConfigPath)).toBe(true);\n      }\n    });\n\n    it('should fail - coverage configuration lacks critical file exclusions', () => {\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      \n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        const exclusionIssues = [];\n        \n        // Check for coverage exclusions\n        const coveragePathIgnorePatternsMatch = configContent.match(/coveragePathIgnorePatterns:\\s*\\[([^\\]]+)\\]/s);\n        \n        if (coveragePathIgnorePatternsMatch) {\n          const exclusions = coveragePathIgnorePatternsMatch[1];\n          \n          // Required exclusions\n          const requiredExclusions = [\n            'node_modules',\n            '__tests__',\n            'coverage',\n            '.next',\n            'dist',\n            'build'\n          ];\n          \n          requiredExclusions.forEach(exclusion => {\n            if (!exclusions.includes(exclusion)) {\n              exclusionIssues.push(`Missing coverage exclusion: ${exclusion}`);\n            }\n          });\n        } else {\n          exclusionIssues.push('No coveragePathIgnorePatterns configured');\n        }\n        \n        // Check for collectCoverageFrom configuration\n        const collectCoverageFromMatch = configContent.match(/collectCoverageFrom:\\s*\\[([^\\]]+)\\]/s);\n        \n        if (collectCoverageFromMatch) {\n          const collectFrom = collectCoverageFromMatch[1];\n          \n          // Should include source files\n          if (!collectFrom.includes('src/') && !collectFrom.includes('**/*.{ts,tsx}')) {\n            exclusionIssues.push('collectCoverageFrom missing source file patterns');\n          }\n          \n          // Should exclude test files\n          if (!collectFrom.includes('!**/*.test.*') && !collectFrom.includes('!**/__tests__/**')) {\n            exclusionIssues.push('collectCoverageFrom not excluding test files');\n          }\n        } else {\n          exclusionIssues.push('No collectCoverageFrom configuration');\n        }\n        \n        // EXPECTED TO FAIL: Coverage configuration should have proper exclusions\n        expect(exclusionIssues.length).toBe(0);\n      }\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: Missing CI/CD Automation', () => {\n    it('should fail - GitHub Actions workflow is missing or incomplete', () => {\n      const workflowsDir = path.join(process.cwd(), '.github/workflows');\n      const automationIssues = [];\n      \n      if (fs.existsSync(workflowsDir)) {\n        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => \n          file.endsWith('.yml') || file.endsWith('.yaml')\n        );\n        \n        if (workflowFiles.length === 0) {\n          automationIssues.push('No GitHub Actions workflow files found');\n        } else {\n          // Check for required workflows\n          const requiredWorkflows = ['ci', 'test', 'build'];\n          const hasRequiredWorkflow = requiredWorkflows.some(workflow => \n            workflowFiles.some(file => file.toLowerCase().includes(workflow))\n          );\n          \n          if (!hasRequiredWorkflow) {\n            automationIssues.push('Missing required CI/test/build workflow');\n          }\n          \n          // Analyze workflow content\n          workflowFiles.forEach(workflowFile => {\n            const workflowPath = path.join(workflowsDir, workflowFile);\n            const workflowContent = fs.readFileSync(workflowPath, 'utf8');\n            \n            // Check for required workflow components\n            const requiredComponents = [\n              'on:', // Trigger events\n              'jobs:', // Job definitions\n              'runs-on:', // Runner specification\n              'uses: actions/checkout', // Code checkout\n              'uses: actions/setup-node', // Node.js setup\n              'npm ci', // Dependency installation\n              'npm test' // Test execution\n            ];\n            \n            requiredComponents.forEach(component => {\n              if (!workflowContent.includes(component)) {\n                automationIssues.push(`${workflowFile}: Missing ${component}`);\n              }\n            });\n            \n            // Check for test result reporting\n            if (!workflowContent.includes('coverage') && !workflowContent.includes('test-results')) {\n              automationIssues.push(`${workflowFile}: Missing test result reporting`);\n            }\n          });\n        }\n      } else {\n        automationIssues.push('No .github/workflows directory found');\n      }\n      \n      // EXPECTED TO FAIL: GitHub Actions automation should be properly configured\n      expect(automationIssues.length).toBe(0);\n    });\n\n    it('should fail - pre-commit hooks and quality gates are missing', () => {\n      const qualityGateIssues = [];\n      \n      // Check for pre-commit hooks\n      const huskyDir = path.join(process.cwd(), '.husky');\n      const preCommitHook = path.join(huskyDir, 'pre-commit');\n      \n      if (!fs.existsSync(huskyDir)) {\n        qualityGateIssues.push('No .husky directory found (missing pre-commit hooks)');\n      } else if (!fs.existsSync(preCommitHook)) {\n        qualityGateIssues.push('No pre-commit hook configured');\n      } else {\n        const hookContent = fs.readFileSync(preCommitHook, 'utf8');\n        \n        // Pre-commit should run tests\n        if (!hookContent.includes('test') && !hookContent.includes('lint')) {\n          qualityGateIssues.push('Pre-commit hook missing test/lint execution');\n        }\n      }\n      \n      // Check for lint-staged configuration\n      const packageJsonPath = path.join(process.cwd(), 'package.json');\n      if (fs.existsSync(packageJsonPath)) {\n        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));\n        \n        if (!packageJson['lint-staged']) {\n          qualityGateIssues.push('No lint-staged configuration found');\n        }\n      }\n      \n      // Check for commitlint configuration\n      const commitlintConfigs = [\n        'commitlint.config.js',\n        '.commitlintrc.js',\n        '.commitlintrc.json'\n      ];\n      \n      const hasCommitlintConfig = commitlintConfigs.some(config => \n        fs.existsSync(path.join(process.cwd(), config))\n      );\n      \n      if (!hasCommitlintConfig) {\n        qualityGateIssues.push('No commitlint configuration found');\n      }\n      \n      // EXPECTED TO FAIL: Quality gates should be properly configured\n      expect(qualityGateIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Pipeline Performance and Reliability Issues', () => {\n    it('should fail - CI pipeline lacks caching and optimization', () => {\n      const workflowsDir = path.join(process.cwd(), '.github/workflows');\n      const performanceIssues = [];\n      \n      if (fs.existsSync(workflowsDir)) {\n        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => \n          file.endsWith('.yml') || file.endsWith('.yaml')\n        );\n        \n        workflowFiles.forEach(workflowFile => {\n          const workflowPath = path.join(workflowsDir, workflowFile);\n          const workflowContent = fs.readFileSync(workflowPath, 'utf8');\n          \n          // Check for caching\n          if (!workflowContent.includes('actions/cache') && !workflowContent.includes('cache:')) {\n            performanceIssues.push(`${workflowFile}: Missing dependency caching`);\n          }\n          \n          // Check for parallel job execution\n          if (workflowContent.includes('npm test') && !workflowContent.includes('strategy:')) {\n            performanceIssues.push(`${workflowFile}: Missing parallel execution strategy`);\n          }\n          \n          // Check for artifact management\n          if (!workflowContent.includes('actions/upload-artifact') && workflowContent.includes('coverage')) {\n            performanceIssues.push(`${workflowFile}: Missing artifact upload for coverage`);\n          }\n        });\n      }\n      \n      // Check Jest configuration for CI optimization\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        \n        // Should have maxWorkers configuration for CI\n        if (!configContent.includes('maxWorkers')) {\n          performanceIssues.push('Jest config missing maxWorkers optimization for CI');\n        }\n        \n        // Should have cache configuration\n        if (!configContent.includes('cache') && !configContent.includes('cacheDirectory')) {\n          performanceIssues.push('Jest config missing cache configuration');\n        }\n      }\n      \n      // EXPECTED TO FAIL: CI pipeline should be optimized for performance\n      expect(performanceIssues.length).toBe(0);\n    });\n\n    it('should fail - pipeline lacks proper error handling and retry mechanisms', () => {\n      const workflowsDir = path.join(process.cwd(), '.github/workflows');\n      const reliabilityIssues = [];\n      \n      if (fs.existsSync(workflowsDir)) {\n        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => \n          file.endsWith('.yml') || file.endsWith('.yaml')\n        );\n        \n        workflowFiles.forEach(workflowFile => {\n          const workflowPath = path.join(workflowsDir, workflowFile);\n          const workflowContent = fs.readFileSync(workflowPath, 'utf8');\n          \n          // Check for timeout configuration\n          if (!workflowContent.includes('timeout-minutes:')) {\n            reliabilityIssues.push(`${workflowFile}: Missing job timeout configuration`);\n          }\n          \n          // Check for retry mechanisms\n          if (!workflowContent.includes('continue-on-error') && !workflowContent.includes('if: failure()')) {\n            reliabilityIssues.push(`${workflowFile}: Missing error handling mechanisms`);\n          }\n          \n          // Check for notification on failure\n          if (!workflowContent.includes('slack') && !workflowContent.includes('notification')) {\n            reliabilityIssues.push(`${workflowFile}: Missing failure notification`);\n          }\n          \n          // Check for matrix strategy reliability\n          if (workflowContent.includes('matrix:') && !workflowContent.includes('fail-fast: false')) {\n            reliabilityIssues.push(`${workflowFile}: Matrix strategy should not fail fast`);\n          }\n        });\n      }\n      \n      // EXPECTED TO FAIL: Pipeline should have proper error handling\n      expect(reliabilityIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 5: Test Reporting and Monitoring Gaps', () => {\n    it('should fail - test results lack comprehensive reporting and analytics', () => {\n      const reportingIssues = [];\n      \n      // Check Jest configuration for reporting\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        \n        // Check for reporters configuration\n        const reportersMatch = configContent.match(/reporters:\\s*\\[([^\\]]+)\\]/s);\n        if (reportersMatch) {\n          const reporters = reportersMatch[1];\n          \n          // Should have multiple reporters for different purposes\n          const requiredReporters = ['default', 'json', 'html'];\n          requiredReporters.forEach(reporter => {\n            if (!reporters.includes(reporter)) {\n              reportingIssues.push(`Missing ${reporter} reporter in Jest configuration`);\n            }\n          });\n        } else {\n          reportingIssues.push('No reporters configuration in Jest config');\n        }\n        \n        // Check for coverage reporters\n        const coverageReportersMatch = configContent.match(/coverageReporters:\\s*\\[([^\\]]+)\\]/s);\n        if (coverageReportersMatch) {\n          const coverageReporters = coverageReportersMatch[1];\n          \n          const requiredCoverageReporters = ['text', 'lcov', 'html', 'json'];\n          requiredCoverageReporters.forEach(reporter => {\n            if (!coverageReporters.includes(reporter)) {\n              reportingIssues.push(`Missing ${reporter} coverage reporter`);\n            }\n          });\n        } else {\n          reportingIssues.push('No coverage reporters configuration');\n        }\n      }\n      \n      // Check for test result directories\n      const expectedReportDirs = ['coverage', 'test-results', 'reports'];\n      expectedReportDirs.forEach(dir => {\n        const dirPath = path.join(process.cwd(), dir);\n        if (!fs.existsSync(dirPath)) {\n          reportingIssues.push(`Missing ${dir} directory for test reports`);\n        }\n      });\n      \n      // EXPECTED TO FAIL: Test reporting should be comprehensive\n      expect(reportingIssues.length).toBe(0);\n    });\n\n    it('should fail - monitoring and alerting for test failures is inadequate', () => {\n      const monitoringIssues = [];\n      \n      // Check for monitoring configuration\n      const packageJsonPath = path.join(process.cwd(), 'package.json');\n      if (fs.existsSync(packageJsonPath)) {\n        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));\n        \n        // Check for monitoring dependencies\n        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };\n        const monitoringTools = ['@sentry/node', 'datadog', 'newrelic'];\n        \n        const hasMonitoring = monitoringTools.some(tool => dependencies[tool]);\n        if (!hasMonitoring) {\n          monitoringIssues.push('No monitoring tools configured for test failure tracking');\n        }\n      }\n      \n      // Check GitHub Actions for monitoring integration\n      const workflowsDir = path.join(process.cwd(), '.github/workflows');\n      if (fs.existsSync(workflowsDir)) {\n        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => \n          file.endsWith('.yml') || file.endsWith('.yaml')\n        );\n        \n        let hasFailureNotification = false;\n        workflowFiles.forEach(workflowFile => {\n          const workflowPath = path.join(workflowsDir, workflowFile);\n          const workflowContent = fs.readFileSync(workflowPath, 'utf8');\n          \n          if (workflowContent.includes('if: failure()') || workflowContent.includes('slack')) {\n            hasFailureNotification = true;\n          }\n        });\n        \n        if (!hasFailureNotification) {\n          monitoringIssues.push('No failure notification configured in CI workflows');\n        }\n      }\n      \n      // EXPECTED TO FAIL: Monitoring and alerting should be properly configured\n      expect(monitoringIssues.length).toBe(0);\n    });\n  });\n});\n"], "version": 3}