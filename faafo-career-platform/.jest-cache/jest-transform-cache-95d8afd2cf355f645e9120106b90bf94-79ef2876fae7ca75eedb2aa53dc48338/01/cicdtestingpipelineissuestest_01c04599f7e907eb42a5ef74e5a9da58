472b180706b2e84ac8312c0aa6531475
"use strict";
/**
 * CI/CD Testing Pipeline Issues Tests
 *
 * These tests prove CI/CD pipeline issues including incomplete test scripts,
 * inadequate coverage thresholds, and missing automation.
 *
 * EXPECTED TO FAIL - These tests demonstrate CI/CD pipeline issues that need fixing.
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
(0, globals_1.describe)('CI/CD Testing Pipeline Issues', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Incomplete Test Scripts', function () {
        (0, globals_1.it)('should fail - package.json test scripts are incomplete for CI/CD', function () {
            var packageJsonPath = path_1.default.join(process.cwd(), 'package.json');
            if (fs_1.default.existsSync(packageJsonPath)) {
                var packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf8'));
                var scripts_1 = packageJson.scripts || {};
                var scriptIssues_1 = [];
                // Required CI/CD test scripts
                var requiredScripts = {
                    'test': 'Basic test execution',
                    'test:ci': 'CI-specific test execution with coverage',
                    'test:coverage': 'Coverage reporting',
                    'test:watch': 'Development watch mode',
                    'test:unit': 'Unit tests only',
                    'test:integration': 'Integration tests only',
                    'test:e2e': 'End-to-end tests',
                    'test:performance': 'Performance tests',
                    'test:security': 'Security tests'
                };
                Object.entries(requiredScripts).forEach(function (_a) {
                    var script = _a[0], description = _a[1];
                    if (!scripts_1[script]) {
                        scriptIssues_1.push("Missing ".concat(script, ": ").concat(description));
                    }
                });
                // Check CI script quality
                var testCiScript = scripts_1['test:ci'];
                if (testCiScript) {
                    // CI script should include coverage
                    if (!testCiScript.includes('--coverage')) {
                        scriptIssues_1.push('test:ci script missing --coverage flag');
                    }
                    // CI script should disable watch mode
                    if (testCiScript.includes('--watch') && !testCiScript.includes('--watchAll=false')) {
                        scriptIssues_1.push('test:ci script should disable watch mode');
                    }
                    // CI script should have proper exit codes
                    if (!testCiScript.includes('--passWithNoTests=false')) {
                        scriptIssues_1.push('test:ci script should fail when no tests found');
                    }
                }
                // Check for parallel execution configuration
                var testScript = scripts_1['test'];
                if (testScript && !testScript.includes('--maxWorkers')) {
                    scriptIssues_1.push('test script missing --maxWorkers configuration for CI');
                }
                // EXPECTED TO FAIL: All required CI/CD scripts should be present and properly configured
                (0, globals_1.expect)(scriptIssues_1.length).toBe(0);
            }
            else {
                // EXPECTED TO FAIL: package.json should exist
                (0, globals_1.expect)(fs_1.default.existsSync(packageJsonPath)).toBe(true);
            }
        });
        (0, globals_1.it)('should fail - test scripts lack proper error handling and reporting', function () {
            var packageJsonPath = path_1.default.join(process.cwd(), 'package.json');
            if (fs_1.default.existsSync(packageJsonPath)) {
                var packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf8'));
                var scripts = packageJson.scripts || {};
                var errorHandlingIssues_1 = [];
                // Check test scripts for error handling
                var testScripts = Object.entries(scripts).filter(function (_a) {
                    var key = _a[0];
                    return key.startsWith('test');
                });
                testScripts.forEach(function (_a) {
                    var scriptName = _a[0], scriptCommand = _a[1];
                    // Should have proper exit code handling
                    if (!scriptCommand.includes('--') && !scriptCommand.includes('&&')) {
                        errorHandlingIssues_1.push("".concat(scriptName, ": No error handling or chaining"));
                    }
                    // Coverage scripts should have threshold enforcement
                    if (scriptName.includes('coverage') && !scriptCommand.includes('--coverageThreshold')) {
                        errorHandlingIssues_1.push("".concat(scriptName, ": Missing coverage threshold enforcement"));
                    }
                    // CI scripts should have proper reporting
                    if (scriptName.includes('ci') && !scriptCommand.includes('--reporters')) {
                        errorHandlingIssues_1.push("".concat(scriptName, ": Missing CI reporters configuration"));
                    }
                });
                // EXPECTED TO FAIL: Test scripts should have proper error handling
                (0, globals_1.expect)(errorHandlingIssues_1.length).toBe(0);
            }
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Inadequate Coverage Thresholds', function () {
        (0, globals_1.it)('should fail - coverage thresholds are too low for production code', function () {
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                var coverageIssues_1 = [];
                // Extract coverage thresholds
                var coverageMatch = configContent.match(/coverageThreshold:\s*{[^}]*global:\s*{([^}]+)}/s);
                if (coverageMatch) {
                    var thresholds_1 = coverageMatch[1];
                    // Required minimum thresholds for production
                    var requiredThresholds = {
                        branches: 85,
                        functions: 90,
                        lines: 90,
                        statements: 90
                    };
                    Object.entries(requiredThresholds).forEach(function (_a) {
                        var metric = _a[0], minValue = _a[1];
                        var metricMatch = thresholds_1.match(new RegExp("".concat(metric, ":\\s*(\\d+)")));
                        if (metricMatch) {
                            var currentValue = parseInt(metricMatch[1]);
                            if (currentValue < minValue) {
                                coverageIssues_1.push("".concat(metric, " threshold ").concat(currentValue, "% too low (should be \u2265").concat(minValue, "%)"));
                            }
                        }
                        else {
                            coverageIssues_1.push("Missing ".concat(metric, " coverage threshold"));
                        }
                    });
                }
                else {
                    coverageIssues_1.push('No global coverage thresholds configured');
                }
                // Check for per-directory thresholds
                var hasPerDirectoryThresholds = configContent.includes('coverageThreshold') &&
                    (configContent.includes('src/') || configContent.includes('./'));
                if (!hasPerDirectoryThresholds) {
                    coverageIssues_1.push('Missing per-directory coverage thresholds');
                }
                // EXPECTED TO FAIL: Coverage thresholds should be adequate for production
                (0, globals_1.expect)(coverageIssues_1.length).toBe(0);
            }
            else {
                // EXPECTED TO FAIL: Jest config should exist
                (0, globals_1.expect)(fs_1.default.existsSync(jestConfigPath)).toBe(true);
            }
        });
        (0, globals_1.it)('should fail - coverage configuration lacks critical file exclusions', function () {
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                var exclusionIssues_1 = [];
                // Check for coverage exclusions
                var coveragePathIgnorePatternsMatch = configContent.match(/coveragePathIgnorePatterns:\s*\[([^\]]+)\]/s);
                if (coveragePathIgnorePatternsMatch) {
                    var exclusions_1 = coveragePathIgnorePatternsMatch[1];
                    // Required exclusions
                    var requiredExclusions = [
                        'node_modules',
                        '__tests__',
                        'coverage',
                        '.next',
                        'dist',
                        'build'
                    ];
                    requiredExclusions.forEach(function (exclusion) {
                        if (!exclusions_1.includes(exclusion)) {
                            exclusionIssues_1.push("Missing coverage exclusion: ".concat(exclusion));
                        }
                    });
                }
                else {
                    exclusionIssues_1.push('No coveragePathIgnorePatterns configured');
                }
                // Check for collectCoverageFrom configuration
                var collectCoverageFromMatch = configContent.match(/collectCoverageFrom:\s*\[([^\]]+)\]/s);
                if (collectCoverageFromMatch) {
                    var collectFrom = collectCoverageFromMatch[1];
                    // Should include source files
                    if (!collectFrom.includes('src/') && !collectFrom.includes('**/*.{ts,tsx}')) {
                        exclusionIssues_1.push('collectCoverageFrom missing source file patterns');
                    }
                    // Should exclude test files
                    if (!collectFrom.includes('!**/*.test.*') && !collectFrom.includes('!**/__tests__/**')) {
                        exclusionIssues_1.push('collectCoverageFrom not excluding test files');
                    }
                }
                else {
                    exclusionIssues_1.push('No collectCoverageFrom configuration');
                }
                // EXPECTED TO FAIL: Coverage configuration should have proper exclusions
                (0, globals_1.expect)(exclusionIssues_1.length).toBe(0);
            }
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Missing CI/CD Automation', function () {
        (0, globals_1.it)('should fail - GitHub Actions workflow is missing or incomplete', function () {
            var workflowsDir = path_1.default.join(process.cwd(), '.github/workflows');
            var automationIssues = [];
            if (fs_1.default.existsSync(workflowsDir)) {
                var workflowFiles_1 = fs_1.default.readdirSync(workflowsDir).filter(function (file) {
                    return file.endsWith('.yml') || file.endsWith('.yaml');
                });
                if (workflowFiles_1.length === 0) {
                    automationIssues.push('No GitHub Actions workflow files found');
                }
                else {
                    // Check for required workflows
                    var requiredWorkflows = ['ci', 'test', 'build'];
                    var hasRequiredWorkflow = requiredWorkflows.some(function (workflow) {
                        return workflowFiles_1.some(function (file) { return file.toLowerCase().includes(workflow); });
                    });
                    if (!hasRequiredWorkflow) {
                        automationIssues.push('Missing required CI/test/build workflow');
                    }
                    // Analyze workflow content
                    workflowFiles_1.forEach(function (workflowFile) {
                        var workflowPath = path_1.default.join(workflowsDir, workflowFile);
                        var workflowContent = fs_1.default.readFileSync(workflowPath, 'utf8');
                        // Check for required workflow components
                        var requiredComponents = [
                            'on:', // Trigger events
                            'jobs:', // Job definitions
                            'runs-on:', // Runner specification
                            'uses: actions/checkout', // Code checkout
                            'uses: actions/setup-node', // Node.js setup
                            'npm ci', // Dependency installation
                            'npm test' // Test execution
                        ];
                        requiredComponents.forEach(function (component) {
                            if (!workflowContent.includes(component)) {
                                automationIssues.push("".concat(workflowFile, ": Missing ").concat(component));
                            }
                        });
                        // Check for test result reporting
                        if (!workflowContent.includes('coverage') && !workflowContent.includes('test-results')) {
                            automationIssues.push("".concat(workflowFile, ": Missing test result reporting"));
                        }
                    });
                }
            }
            else {
                automationIssues.push('No .github/workflows directory found');
            }
            // EXPECTED TO FAIL: GitHub Actions automation should be properly configured
            (0, globals_1.expect)(automationIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - pre-commit hooks and quality gates are missing', function () {
            var qualityGateIssues = [];
            // Check for pre-commit hooks
            var huskyDir = path_1.default.join(process.cwd(), '.husky');
            var preCommitHook = path_1.default.join(huskyDir, 'pre-commit');
            if (!fs_1.default.existsSync(huskyDir)) {
                qualityGateIssues.push('No .husky directory found (missing pre-commit hooks)');
            }
            else if (!fs_1.default.existsSync(preCommitHook)) {
                qualityGateIssues.push('No pre-commit hook configured');
            }
            else {
                var hookContent = fs_1.default.readFileSync(preCommitHook, 'utf8');
                // Pre-commit should run tests
                if (!hookContent.includes('test') && !hookContent.includes('lint')) {
                    qualityGateIssues.push('Pre-commit hook missing test/lint execution');
                }
            }
            // Check for lint-staged configuration
            var packageJsonPath = path_1.default.join(process.cwd(), 'package.json');
            if (fs_1.default.existsSync(packageJsonPath)) {
                var packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf8'));
                if (!packageJson['lint-staged']) {
                    qualityGateIssues.push('No lint-staged configuration found');
                }
            }
            // Check for commitlint configuration
            var commitlintConfigs = [
                'commitlint.config.js',
                '.commitlintrc.js',
                '.commitlintrc.json'
            ];
            var hasCommitlintConfig = commitlintConfigs.some(function (config) {
                return fs_1.default.existsSync(path_1.default.join(process.cwd(), config));
            });
            if (!hasCommitlintConfig) {
                qualityGateIssues.push('No commitlint configuration found');
            }
            // EXPECTED TO FAIL: Quality gates should be properly configured
            (0, globals_1.expect)(qualityGateIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Pipeline Performance and Reliability Issues', function () {
        (0, globals_1.it)('should fail - CI pipeline lacks caching and optimization', function () {
            var workflowsDir = path_1.default.join(process.cwd(), '.github/workflows');
            var performanceIssues = [];
            if (fs_1.default.existsSync(workflowsDir)) {
                var workflowFiles = fs_1.default.readdirSync(workflowsDir).filter(function (file) {
                    return file.endsWith('.yml') || file.endsWith('.yaml');
                });
                workflowFiles.forEach(function (workflowFile) {
                    var workflowPath = path_1.default.join(workflowsDir, workflowFile);
                    var workflowContent = fs_1.default.readFileSync(workflowPath, 'utf8');
                    // Check for caching
                    if (!workflowContent.includes('actions/cache') && !workflowContent.includes('cache:')) {
                        performanceIssues.push("".concat(workflowFile, ": Missing dependency caching"));
                    }
                    // Check for parallel job execution
                    if (workflowContent.includes('npm test') && !workflowContent.includes('strategy:')) {
                        performanceIssues.push("".concat(workflowFile, ": Missing parallel execution strategy"));
                    }
                    // Check for artifact management
                    if (!workflowContent.includes('actions/upload-artifact') && workflowContent.includes('coverage')) {
                        performanceIssues.push("".concat(workflowFile, ": Missing artifact upload for coverage"));
                    }
                });
            }
            // Check Jest configuration for CI optimization
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                // Should have maxWorkers configuration for CI
                if (!configContent.includes('maxWorkers')) {
                    performanceIssues.push('Jest config missing maxWorkers optimization for CI');
                }
                // Should have cache configuration
                if (!configContent.includes('cache') && !configContent.includes('cacheDirectory')) {
                    performanceIssues.push('Jest config missing cache configuration');
                }
            }
            // EXPECTED TO FAIL: CI pipeline should be optimized for performance
            (0, globals_1.expect)(performanceIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - pipeline lacks proper error handling and retry mechanisms', function () {
            var workflowsDir = path_1.default.join(process.cwd(), '.github/workflows');
            var reliabilityIssues = [];
            if (fs_1.default.existsSync(workflowsDir)) {
                var workflowFiles = fs_1.default.readdirSync(workflowsDir).filter(function (file) {
                    return file.endsWith('.yml') || file.endsWith('.yaml');
                });
                workflowFiles.forEach(function (workflowFile) {
                    var workflowPath = path_1.default.join(workflowsDir, workflowFile);
                    var workflowContent = fs_1.default.readFileSync(workflowPath, 'utf8');
                    // Check for timeout configuration
                    if (!workflowContent.includes('timeout-minutes:')) {
                        reliabilityIssues.push("".concat(workflowFile, ": Missing job timeout configuration"));
                    }
                    // Check for retry mechanisms
                    if (!workflowContent.includes('continue-on-error') && !workflowContent.includes('if: failure()')) {
                        reliabilityIssues.push("".concat(workflowFile, ": Missing error handling mechanisms"));
                    }
                    // Check for notification on failure
                    if (!workflowContent.includes('slack') && !workflowContent.includes('notification')) {
                        reliabilityIssues.push("".concat(workflowFile, ": Missing failure notification"));
                    }
                    // Check for matrix strategy reliability
                    if (workflowContent.includes('matrix:') && !workflowContent.includes('fail-fast: false')) {
                        reliabilityIssues.push("".concat(workflowFile, ": Matrix strategy should not fail fast"));
                    }
                });
            }
            // EXPECTED TO FAIL: Pipeline should have proper error handling
            (0, globals_1.expect)(reliabilityIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: Test Reporting and Monitoring Gaps', function () {
        (0, globals_1.it)('should fail - test results lack comprehensive reporting and analytics', function () {
            var reportingIssues = [];
            // Check Jest configuration for reporting
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                // Check for reporters configuration
                var reportersMatch = configContent.match(/reporters:\s*\[([^\]]+)\]/s);
                if (reportersMatch) {
                    var reporters_1 = reportersMatch[1];
                    // Should have multiple reporters for different purposes
                    var requiredReporters = ['default', 'json', 'html'];
                    requiredReporters.forEach(function (reporter) {
                        if (!reporters_1.includes(reporter)) {
                            reportingIssues.push("Missing ".concat(reporter, " reporter in Jest configuration"));
                        }
                    });
                }
                else {
                    reportingIssues.push('No reporters configuration in Jest config');
                }
                // Check for coverage reporters
                var coverageReportersMatch = configContent.match(/coverageReporters:\s*\[([^\]]+)\]/s);
                if (coverageReportersMatch) {
                    var coverageReporters_1 = coverageReportersMatch[1];
                    var requiredCoverageReporters = ['text', 'lcov', 'html', 'json'];
                    requiredCoverageReporters.forEach(function (reporter) {
                        if (!coverageReporters_1.includes(reporter)) {
                            reportingIssues.push("Missing ".concat(reporter, " coverage reporter"));
                        }
                    });
                }
                else {
                    reportingIssues.push('No coverage reporters configuration');
                }
            }
            // Check for test result directories
            var expectedReportDirs = ['coverage', 'test-results', 'reports'];
            expectedReportDirs.forEach(function (dir) {
                var dirPath = path_1.default.join(process.cwd(), dir);
                if (!fs_1.default.existsSync(dirPath)) {
                    reportingIssues.push("Missing ".concat(dir, " directory for test reports"));
                }
            });
            // EXPECTED TO FAIL: Test reporting should be comprehensive
            (0, globals_1.expect)(reportingIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - monitoring and alerting for test failures is inadequate', function () {
            var monitoringIssues = [];
            // Check for monitoring configuration
            var packageJsonPath = path_1.default.join(process.cwd(), 'package.json');
            if (fs_1.default.existsSync(packageJsonPath)) {
                var packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf8'));
                // Check for monitoring dependencies
                var dependencies_1 = __assign(__assign({}, packageJson.dependencies), packageJson.devDependencies);
                var monitoringTools = ['@sentry/node', 'datadog', 'newrelic'];
                var hasMonitoring = monitoringTools.some(function (tool) { return dependencies_1[tool]; });
                if (!hasMonitoring) {
                    monitoringIssues.push('No monitoring tools configured for test failure tracking');
                }
            }
            // Check GitHub Actions for monitoring integration
            var workflowsDir = path_1.default.join(process.cwd(), '.github/workflows');
            if (fs_1.default.existsSync(workflowsDir)) {
                var workflowFiles = fs_1.default.readdirSync(workflowsDir).filter(function (file) {
                    return file.endsWith('.yml') || file.endsWith('.yaml');
                });
                var hasFailureNotification_1 = false;
                workflowFiles.forEach(function (workflowFile) {
                    var workflowPath = path_1.default.join(workflowsDir, workflowFile);
                    var workflowContent = fs_1.default.readFileSync(workflowPath, 'utf8');
                    if (workflowContent.includes('if: failure()') || workflowContent.includes('slack')) {
                        hasFailureNotification_1 = true;
                    }
                });
                if (!hasFailureNotification_1) {
                    monitoringIssues.push('No failure notification configured in CI workflows');
                }
            }
            // EXPECTED TO FAIL: Monitoring and alerting should be properly configured
            (0, globals_1.expect)(monitoringIssues.length).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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