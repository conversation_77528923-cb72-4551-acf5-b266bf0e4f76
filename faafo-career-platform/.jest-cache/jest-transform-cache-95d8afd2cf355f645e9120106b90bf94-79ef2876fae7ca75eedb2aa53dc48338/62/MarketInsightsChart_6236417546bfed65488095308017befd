3c6d9512cb282bf316ed03dad8a68080
"use strict";
'use client';

/* istanbul ignore next */
function cov_2d3gbsqjv2() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/MarketInsightsChart.tsx";
  var hash = "b323cf903b2b25f55d1b2085b527a028e3b7cac0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/MarketInsightsChart.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "2": {
        start: {
          line: 4,
          column: 26
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 52
        }
      },
      "4": {
        start: {
          line: 6,
          column: 4
        },
        end: {
          line: 8,
          column: 5
        }
      },
      "5": {
        start: {
          line: 7,
          column: 6
        },
        end: {
          line: 7,
          column: 68
        }
      },
      "6": {
        start: {
          line: 7,
          column: 51
        },
        end: {
          line: 7,
          column: 63
        }
      },
      "7": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 39
        }
      },
      "8": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "9": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "10": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 17
        }
      },
      "11": {
        start: {
          line: 14,
          column: 25
        },
        end: {
          line: 18,
          column: 2
        }
      },
      "12": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 72
        }
      },
      "13": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 21
        }
      },
      "14": {
        start: {
          line: 19,
          column: 19
        },
        end: {
          line: 35,
          column: 4
        }
      },
      "15": {
        start: {
          line: 20,
          column: 18
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "16": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 25,
          column: 10
        }
      },
      "17": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 23
        }
      },
      "18": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "19": {
        start: {
          line: 23,
          column: 29
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "20": {
        start: {
          line: 23,
          column: 77
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "21": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 24,
          column: 22
        }
      },
      "22": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 26
        }
      },
      "23": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 34,
          column: 6
        }
      },
      "24": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "25": {
        start: {
          line: 29,
          column: 35
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "26": {
        start: {
          line: 30,
          column: 21
        },
        end: {
          line: 30,
          column: 23
        }
      },
      "27": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "28": {
        start: {
          line: 31,
          column: 25
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "29": {
        start: {
          line: 31,
          column: 38
        },
        end: {
          line: 31,
          column: 50
        }
      },
      "30": {
        start: {
          line: 31,
          column: 56
        },
        end: {
          line: 31,
          column: 57
        }
      },
      "31": {
        start: {
          line: 31,
          column: 78
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "32": {
        start: {
          line: 31,
          column: 102
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "33": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 40
        }
      },
      "34": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 22
        }
      },
      "35": {
        start: {
          line: 36,
          column: 20
        },
        end: {
          line: 44,
          column: 1
        }
      },
      "36": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 42,
          column: 5
        }
      },
      "37": {
        start: {
          line: 37,
          column: 40
        },
        end: {
          line: 42,
          column: 5
        }
      },
      "38": {
        start: {
          line: 37,
          column: 53
        },
        end: {
          line: 37,
          column: 54
        }
      },
      "39": {
        start: {
          line: 37,
          column: 60
        },
        end: {
          line: 37,
          column: 71
        }
      },
      "40": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 41,
          column: 9
        }
      },
      "41": {
        start: {
          line: 39,
          column: 12
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "42": {
        start: {
          line: 39,
          column: 21
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "43": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 28
        }
      },
      "44": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 43,
          column: 61
        }
      },
      "45": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 62
        }
      },
      "46": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 46,
          column: 38
        }
      },
      "47": {
        start: {
          line: 47,
          column: 20
        },
        end: {
          line: 47,
          column: 48
        }
      },
      "48": {
        start: {
          line: 48,
          column: 14
        },
        end: {
          line: 48,
          column: 44
        }
      },
      "49": {
        start: {
          line: 49,
          column: 17
        },
        end: {
          line: 49,
          column: 36
        }
      },
      "50": {
        start: {
          line: 50,
          column: 13
        },
        end: {
          line: 50,
          column: 44
        }
      },
      "51": {
        start: {
          line: 51,
          column: 15
        },
        end: {
          line: 51,
          column: 48
        }
      },
      "52": {
        start: {
          line: 52,
          column: 14
        },
        end: {
          line: 52,
          column: 46
        }
      },
      "53": {
        start: {
          line: 53,
          column: 21
        },
        end: {
          line: 53,
          column: 44
        }
      },
      "54": {
        start: {
          line: 55,
          column: 15
        },
        end: {
          line: 55,
          column: 22
        }
      },
      "55": {
        start: {
          line: 55,
          column: 29
        },
        end: {
          line: 55,
          column: 37
        }
      },
      "56": {
        start: {
          line: 55,
          column: 47
        },
        end: {
          line: 55,
          column: 85
        }
      },
      "57": {
        start: {
          line: 55,
          column: 92
        },
        end: {
          line: 55,
          column: 106
        }
      },
      "58": {
        start: {
          line: 55,
          column: 122
        },
        end: {
          line: 55,
          column: 184
        }
      },
      "59": {
        start: {
          line: 55,
          column: 191
        },
        end: {
          line: 55,
          column: 200
        }
      },
      "60": {
        start: {
          line: 55,
          column: 211
        },
        end: {
          line: 55,
          column: 235
        }
      },
      "61": {
        start: {
          line: 55,
          column: 242
        },
        end: {
          line: 55,
          column: 255
        }
      },
      "62": {
        start: {
          line: 55,
          column: 270
        },
        end: {
          line: 55,
          column: 295
        }
      },
      "63": {
        start: {
          line: 55,
          column: 302
        },
        end: {
          line: 55,
          column: 311
        }
      },
      "64": {
        start: {
          line: 55,
          column: 322
        },
        end: {
          line: 55,
          column: 358
        }
      },
      "65": {
        start: {
          line: 55,
          column: 369
        },
        end: {
          line: 55,
          column: 378
        }
      },
      "66": {
        start: {
          line: 55,
          column: 397
        },
        end: {
          line: 55,
          column: 414
        }
      },
      "67": {
        start: {
          line: 55,
          column: 421
        },
        end: {
          line: 55,
          column: 430
        }
      },
      "68": {
        start: {
          line: 55,
          column: 441
        },
        end: {
          line: 60,
          column: 10
        }
      },
      "69": {
        start: {
          line: 61,
          column: 13
        },
        end: {
          line: 61,
          column: 42
        }
      },
      "70": {
        start: {
          line: 61,
          column: 61
        },
        end: {
          line: 61,
          column: 66
        }
      },
      "71": {
        start: {
          line: 61,
          column: 88
        },
        end: {
          line: 61,
          column: 93
        }
      },
      "72": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 63,
          column: 53
        }
      },
      "73": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 66,
          column: 5
        }
      },
      "74": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 65,
          column: 107
        }
      },
      "75": {
        start: {
          line: 65,
          column: 63
        },
        end: {
          line: 65,
          column: 103
        }
      },
      "76": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 69,
          column: 5
        }
      },
      "77": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 78
        }
      },
      "78": {
        start: {
          line: 68,
          column: 45
        },
        end: {
          line: 68,
          column: 74
        }
      },
      "79": {
        start: {
          line: 71,
          column: 22
        },
        end: {
          line: 75,
          column: 5
        }
      },
      "80": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 73,
          column: 21
        }
      },
      "81": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 73,
          column: 21
        }
      },
      "82": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 74,
          column: 95
        }
      },
      "83": {
        start: {
          line: 74,
          column: 53
        },
        end: {
          line: 74,
          column: 72
        }
      },
      "84": {
        start: {
          line: 76,
          column: 18
        },
        end: {
          line: 80,
          column: 5
        }
      },
      "85": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 78,
          column: 21
        }
      },
      "86": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 21
        }
      },
      "87": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 44
        }
      },
      "88": {
        start: {
          line: 81,
          column: 18
        },
        end: {
          line: 85,
          column: 5
        }
      },
      "89": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 83,
          column: 21
        }
      },
      "90": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 21
        }
      },
      "91": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 44
        }
      },
      "92": {
        start: {
          line: 86,
          column: 20
        },
        end: {
          line: 86,
          column: 82
        }
      },
      "93": {
        start: {
          line: 86,
          column: 59
        },
        end: {
          line: 86,
          column: 78
        }
      },
      "94": {
        start: {
          line: 87,
          column: 20
        },
        end: {
          line: 87,
          column: 82
        }
      },
      "95": {
        start: {
          line: 87,
          column: 59
        },
        end: {
          line: 87,
          column: 78
        }
      },
      "96": {
        start: {
          line: 88,
          column: 20
        },
        end: {
          line: 88,
          column: 41
        }
      },
      "97": {
        start: {
          line: 89,
          column: 20
        },
        end: {
          line: 89,
          column: 89
        }
      },
      "98": {
        start: {
          line: 89,
          column: 59
        },
        end: {
          line: 89,
          column: 85
        }
      },
      "99": {
        start: {
          line: 90,
          column: 24
        },
        end: {
          line: 90,
          column: 89
        }
      },
      "100": {
        start: {
          line: 90,
          column: 59
        },
        end: {
          line: 90,
          column: 85
        }
      },
      "101": {
        start: {
          line: 91,
          column: 23
        },
        end: {
          line: 91,
          column: 88
        }
      },
      "102": {
        start: {
          line: 91,
          column: 58
        },
        end: {
          line: 91,
          column: 84
        }
      },
      "103": {
        start: {
          line: 92,
          column: 20
        },
        end: {
          line: 92,
          column: 82
        }
      },
      "104": {
        start: {
          line: 92,
          column: 59
        },
        end: {
          line: 92,
          column: 78
        }
      },
      "105": {
        start: {
          line: 93,
          column: 24
        },
        end: {
          line: 93,
          column: 82
        }
      },
      "106": {
        start: {
          line: 93,
          column: 59
        },
        end: {
          line: 93,
          column: 78
        }
      },
      "107": {
        start: {
          line: 94,
          column: 23
        },
        end: {
          line: 94,
          column: 81
        }
      },
      "108": {
        start: {
          line: 94,
          column: 58
        },
        end: {
          line: 94,
          column: 77
        }
      },
      "109": {
        start: {
          line: 96,
          column: 25
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "110": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 125,
          column: 9
        }
      },
      "111": {
        start: {
          line: 99,
          column: 16
        },
        end: {
          line: 103,
          column: 18
        }
      },
      "112": {
        start: {
          line: 102,
          column: 55
        },
        end: {
          line: 102,
          column: 105
        }
      },
      "113": {
        start: {
          line: 105,
          column: 16
        },
        end: {
          line: 109,
          column: 18
        }
      },
      "114": {
        start: {
          line: 108,
          column: 55
        },
        end: {
          line: 108,
          column: 84
        }
      },
      "115": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 115,
          column: 18
        }
      },
      "116": {
        start: {
          line: 114,
          column: 55
        },
        end: {
          line: 114,
          column: 79
        }
      },
      "117": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 124,
          column: 18
        }
      },
      "118": {
        start: {
          line: 123,
          column: 55
        },
        end: {
          line: 123,
          column: 79
        }
      },
      "119": {
        start: {
          line: 127,
          column: 22
        },
        end: {
          line: 127,
          column: 38
        }
      },
      "120": {
        start: {
          line: 129,
          column: 23
        },
        end: {
          line: 133,
          column: 5
        }
      },
      "121": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 130,
          column: 44
        }
      },
      "122": {
        start: {
          line: 130,
          column: 32
        },
        end: {
          line: 130,
          column: 42
        }
      },
      "123": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 132,
          column: 29
        }
      },
      "124": {
        start: {
          line: 131,
          column: 77
        },
        end: {
          line: 131,
          column: 108
        }
      },
      "125": {
        start: {
          line: 134,
          column: 23
        },
        end: {
          line: 141,
          column: 5
        }
      },
      "126": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 140,
          column: 26
        }
      },
      "127": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 154,
          column: 1798
        }
      },
      "128": {
        start: {
          line: 142,
          column: 621
        },
        end: {
          line: 142,
          column: 663
        }
      },
      "129": {
        start: {
          line: 142,
          column: 924
        },
        end: {
          line: 142,
          column: 959
        }
      },
      "130": {
        start: {
          line: 142,
          column: 1210
        },
        end: {
          line: 142,
          column: 1245
        }
      },
      "131": {
        start: {
          line: 142,
          column: 1500
        },
        end: {
          line: 142,
          column: 1539
        }
      },
      "132": {
        start: {
          line: 142,
          column: 6531
        },
        end: {
          line: 145,
          column: 50
        }
      },
      "133": {
        start: {
          line: 150,
          column: 153
        },
        end: {
          line: 150,
          column: 265
        }
      },
      "134": {
        start: {
          line: 150,
          column: 735
        },
        end: {
          line: 150,
          column: 1084
        }
      },
      "135": {
        start: {
          line: 151,
          column: 79
        },
        end: {
          line: 151,
          column: 114
        }
      },
      "136": {
        start: {
          line: 152,
          column: 76
        },
        end: {
          line: 152,
          column: 129
        }
      },
      "137": {
        start: {
          line: 154,
          column: 76
        },
        end: {
          line: 154,
          column: 454
        }
      },
      "138": {
        start: {
          line: 154,
          column: 761
        },
        end: {
          line: 154,
          column: 1116
        }
      },
      "139": {
        start: {
          line: 154,
          column: 1413
        },
        end: {
          line: 154,
          column: 1773
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 3,
            column: 75
          }
        },
        loc: {
          start: {
            line: 3,
            column: 96
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 38
          },
          end: {
            line: 7,
            column: 39
          }
        },
        loc: {
          start: {
            line: 7,
            column: 49
          },
          end: {
            line: 7,
            column: 65
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 10,
            column: 7
          }
        },
        loc: {
          start: {
            line: 10,
            column: 28
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 10
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 14,
            column: 81
          }
        },
        loc: {
          start: {
            line: 14,
            column: 95
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 16,
            column: 6
          }
        },
        loc: {
          start: {
            line: 16,
            column: 20
          },
          end: {
            line: 18,
            column: 1
          }
        },
        line: 16
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 52
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 19
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 19
          }
        },
        loc: {
          start: {
            line: 20,
            column: 30
          },
          end: {
            line: 27,
            column: 5
          }
        },
        line: 20
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 21,
            column: 49
          }
        },
        loc: {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 25,
            column: 9
          }
        },
        line: 21
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 28,
            column: 11
          },
          end: {
            line: 28,
            column: 12
          }
        },
        loc: {
          start: {
            line: 28,
            column: 26
          },
          end: {
            line: 34,
            column: 5
          }
        },
        line: 28
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 36,
            column: 52
          },
          end: {
            line: 36,
            column: 53
          }
        },
        loc: {
          start: {
            line: 36,
            column: 78
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 36
      },
      "10": {
        name: "MarketInsightsChart",
        decl: {
          start: {
            line: 54,
            column: 9
          },
          end: {
            line: 54,
            column: 28
          }
        },
        loc: {
          start: {
            line: 54,
            column: 33
          },
          end: {
            line: 155,
            column: 1
          }
        },
        line: 54
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 65,
            column: 45
          },
          end: {
            line: 65,
            column: 46
          }
        },
        loc: {
          start: {
            line: 65,
            column: 61
          },
          end: {
            line: 65,
            column: 105
          }
        },
        line: 65
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 68,
            column: 27
          },
          end: {
            line: 68,
            column: 28
          }
        },
        loc: {
          start: {
            line: 68,
            column: 43
          },
          end: {
            line: 68,
            column: 76
          }
        },
        line: 68
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 71,
            column: 22
          },
          end: {
            line: 71,
            column: 23
          }
        },
        loc: {
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 75,
            column: 5
          }
        },
        line: 71
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 74,
            column: 29
          },
          end: {
            line: 74,
            column: 30
          }
        },
        loc: {
          start: {
            line: 74,
            column: 51
          },
          end: {
            line: 74,
            column: 74
          }
        },
        line: 74
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 76,
            column: 18
          },
          end: {
            line: 76,
            column: 19
          }
        },
        loc: {
          start: {
            line: 76,
            column: 36
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 76
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 81,
            column: 18
          },
          end: {
            line: 81,
            column: 19
          }
        },
        loc: {
          start: {
            line: 81,
            column: 36
          },
          end: {
            line: 85,
            column: 5
          }
        },
        line: 81
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 86,
            column: 41
          },
          end: {
            line: 86,
            column: 42
          }
        },
        loc: {
          start: {
            line: 86,
            column: 57
          },
          end: {
            line: 86,
            column: 80
          }
        },
        line: 86
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 87,
            column: 41
          },
          end: {
            line: 87,
            column: 42
          }
        },
        loc: {
          start: {
            line: 87,
            column: 57
          },
          end: {
            line: 87,
            column: 80
          }
        },
        line: 87
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 89,
            column: 41
          },
          end: {
            line: 89,
            column: 42
          }
        },
        loc: {
          start: {
            line: 89,
            column: 57
          },
          end: {
            line: 89,
            column: 87
          }
        },
        line: 89
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 90,
            column: 41
          },
          end: {
            line: 90,
            column: 42
          }
        },
        loc: {
          start: {
            line: 90,
            column: 57
          },
          end: {
            line: 90,
            column: 87
          }
        },
        line: 90
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 91,
            column: 40
          },
          end: {
            line: 91,
            column: 41
          }
        },
        loc: {
          start: {
            line: 91,
            column: 56
          },
          end: {
            line: 91,
            column: 86
          }
        },
        line: 91
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 92,
            column: 41
          },
          end: {
            line: 92,
            column: 42
          }
        },
        loc: {
          start: {
            line: 92,
            column: 57
          },
          end: {
            line: 92,
            column: 80
          }
        },
        line: 92
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 93,
            column: 41
          },
          end: {
            line: 93,
            column: 42
          }
        },
        loc: {
          start: {
            line: 93,
            column: 57
          },
          end: {
            line: 93,
            column: 80
          }
        },
        line: 93
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 94,
            column: 40
          },
          end: {
            line: 94,
            column: 41
          }
        },
        loc: {
          start: {
            line: 94,
            column: 56
          },
          end: {
            line: 94,
            column: 79
          }
        },
        line: 94
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 96,
            column: 25
          },
          end: {
            line: 96,
            column: 26
          }
        },
        loc: {
          start: {
            line: 96,
            column: 37
          },
          end: {
            line: 126,
            column: 5
          }
        },
        line: 96
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 102,
            column: 36
          },
          end: {
            line: 102,
            column: 37
          }
        },
        loc: {
          start: {
            line: 102,
            column: 53
          },
          end: {
            line: 102,
            column: 107
          }
        },
        line: 102
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 108,
            column: 36
          },
          end: {
            line: 108,
            column: 37
          }
        },
        loc: {
          start: {
            line: 108,
            column: 53
          },
          end: {
            line: 108,
            column: 86
          }
        },
        line: 108
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 114,
            column: 36
          },
          end: {
            line: 114,
            column: 37
          }
        },
        loc: {
          start: {
            line: 114,
            column: 53
          },
          end: {
            line: 114,
            column: 81
          }
        },
        line: 114
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 123,
            column: 36
          },
          end: {
            line: 123,
            column: 37
          }
        },
        loc: {
          start: {
            line: 123,
            column: 53
          },
          end: {
            line: 123,
            column: 81
          }
        },
        line: 123
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 129,
            column: 23
          },
          end: {
            line: 129,
            column: 24
          }
        },
        loc: {
          start: {
            line: 129,
            column: 49
          },
          end: {
            line: 133,
            column: 5
          }
        },
        line: 129
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 131,
            column: 59
          },
          end: {
            line: 131,
            column: 60
          }
        },
        loc: {
          start: {
            line: 131,
            column: 75
          },
          end: {
            line: 131,
            column: 110
          }
        },
        line: 131
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 134,
            column: 23
          },
          end: {
            line: 134,
            column: 24
          }
        },
        loc: {
          start: {
            line: 134,
            column: 41
          },
          end: {
            line: 141,
            column: 5
          }
        },
        line: 134
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 142,
            column: 607
          },
          end: {
            line: 142,
            column: 608
          }
        },
        loc: {
          start: {
            line: 142,
            column: 619
          },
          end: {
            line: 142,
            column: 665
          }
        },
        line: 142
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 142,
            column: 910
          },
          end: {
            line: 142,
            column: 911
          }
        },
        loc: {
          start: {
            line: 142,
            column: 922
          },
          end: {
            line: 142,
            column: 961
          }
        },
        line: 142
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 142,
            column: 1196
          },
          end: {
            line: 142,
            column: 1197
          }
        },
        loc: {
          start: {
            line: 142,
            column: 1208
          },
          end: {
            line: 142,
            column: 1247
          }
        },
        line: 142
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 142,
            column: 1486
          },
          end: {
            line: 142,
            column: 1487
          }
        },
        loc: {
          start: {
            line: 142,
            column: 1498
          },
          end: {
            line: 142,
            column: 1541
          }
        },
        line: 142
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 142,
            column: 6506
          },
          end: {
            line: 142,
            column: 6507
          }
        },
        loc: {
          start: {
            line: 142,
            column: 6529
          },
          end: {
            line: 145,
            column: 52
          }
        },
        line: 142
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 150,
            column: 136
          },
          end: {
            line: 150,
            column: 137
          }
        },
        loc: {
          start: {
            line: 150,
            column: 151
          },
          end: {
            line: 150,
            column: 267
          }
        },
        line: 150
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 150,
            column: 716
          },
          end: {
            line: 150,
            column: 717
          }
        },
        loc: {
          start: {
            line: 150,
            column: 733
          },
          end: {
            line: 150,
            column: 1086
          }
        },
        line: 150
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 151,
            column: 60
          },
          end: {
            line: 151,
            column: 61
          }
        },
        loc: {
          start: {
            line: 151,
            column: 77
          },
          end: {
            line: 151,
            column: 116
          }
        },
        line: 151
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 152,
            column: 58
          },
          end: {
            line: 152,
            column: 59
          }
        },
        loc: {
          start: {
            line: 152,
            column: 74
          },
          end: {
            line: 152,
            column: 131
          }
        },
        line: 152
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 154,
            column: 57
          },
          end: {
            line: 154,
            column: 58
          }
        },
        loc: {
          start: {
            line: 154,
            column: 74
          },
          end: {
            line: 154,
            column: 456
          }
        },
        line: 154
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 154,
            column: 742
          },
          end: {
            line: 154,
            column: 743
          }
        },
        loc: {
          start: {
            line: 154,
            column: 759
          },
          end: {
            line: 154,
            column: 1118
          }
        },
        line: 154
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 154,
            column: 1394
          },
          end: {
            line: 154,
            column: 1395
          }
        },
        loc: {
          start: {
            line: 154,
            column: 1411
          },
          end: {
            line: 154,
            column: 1775
          }
        },
        line: 154
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 13,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 27
          }
        }, {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 3,
            column: 51
          }
        }, {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 10,
            column: 1
          }
        }, {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 13,
            column: 1
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "4": {
        loc: {
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 13
          }
        }, {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "5": {
        loc: {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 6,
            column: 34
          },
          end: {
            line: 6,
            column: 47
          }
        }, {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "6": {
        loc: {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 63
          }
        }, {
          start: {
            line: 6,
            column: 67
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "7": {
        loc: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "8": {
        loc: {
          start: {
            line: 14,
            column: 25
          },
          end: {
            line: 18,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 30
          }
        }, {
          start: {
            line: 14,
            column: 34
          },
          end: {
            line: 14,
            column: 57
          }
        }, {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "9": {
        loc: {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 16,
            column: 1
          }
        }, {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "10": {
        loc: {
          start: {
            line: 19,
            column: 19
          },
          end: {
            line: 35,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 20
          },
          end: {
            line: 19,
            column: 24
          }
        }, {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 19,
            column: 45
          }
        }, {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 35,
            column: 4
          }
        }],
        line: 19
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 25,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 21,
            column: 44
          }
        }, {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 25,
            column: 9
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "13": {
        loc: {
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "14": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 15
          }
        }, {
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 29,
            column: 33
          }
        }],
        line: 29
      },
      "15": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "16": {
        loc: {
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "17": {
        loc: {
          start: {
            line: 36,
            column: 20
          },
          end: {
            line: 44,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 21
          },
          end: {
            line: 36,
            column: 25
          }
        }, {
          start: {
            line: 36,
            column: 29
          },
          end: {
            line: 36,
            column: 47
          }
        }, {
          start: {
            line: 36,
            column: 52
          },
          end: {
            line: 44,
            column: 1
          }
        }],
        line: 36
      },
      "18": {
        loc: {
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 42,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 42,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "19": {
        loc: {
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 37,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 37,
            column: 12
          }
        }, {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 38
          }
        }],
        line: 37
      },
      "20": {
        loc: {
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 41,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 41,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "21": {
        loc: {
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 38,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 38,
            column: 14
          }
        }, {
          start: {
            line: 38,
            column: 18
          },
          end: {
            line: 38,
            column: 30
          }
        }],
        line: 38
      },
      "22": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "23": {
        loc: {
          start: {
            line: 43,
            column: 21
          },
          end: {
            line: 43,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 21
          },
          end: {
            line: 43,
            column: 23
          }
        }, {
          start: {
            line: 43,
            column: 27
          },
          end: {
            line: 43,
            column: 59
          }
        }],
        line: 43
      },
      "24": {
        loc: {
          start: {
            line: 55,
            column: 47
          },
          end: {
            line: 55,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 63
          },
          end: {
            line: 55,
            column: 80
          }
        }, {
          start: {
            line: 55,
            column: 83
          },
          end: {
            line: 55,
            column: 85
          }
        }],
        line: 55
      },
      "25": {
        loc: {
          start: {
            line: 55,
            column: 122
          },
          end: {
            line: 55,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 138
          },
          end: {
            line: 55,
            column: 179
          }
        }, {
          start: {
            line: 55,
            column: 182
          },
          end: {
            line: 55,
            column: 184
          }
        }],
        line: 55
      },
      "26": {
        loc: {
          start: {
            line: 55,
            column: 211
          },
          end: {
            line: 55,
            column: 235
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 227
          },
          end: {
            line: 55,
            column: 230
          }
        }, {
          start: {
            line: 55,
            column: 233
          },
          end: {
            line: 55,
            column: 235
          }
        }],
        line: 55
      },
      "27": {
        loc: {
          start: {
            line: 55,
            column: 270
          },
          end: {
            line: 55,
            column: 295
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 286
          },
          end: {
            line: 55,
            column: 290
          }
        }, {
          start: {
            line: 55,
            column: 293
          },
          end: {
            line: 55,
            column: 295
          }
        }],
        line: 55
      },
      "28": {
        loc: {
          start: {
            line: 55,
            column: 322
          },
          end: {
            line: 55,
            column: 358
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 338
          },
          end: {
            line: 55,
            column: 353
          }
        }, {
          start: {
            line: 55,
            column: 356
          },
          end: {
            line: 55,
            column: 358
          }
        }],
        line: 55
      },
      "29": {
        loc: {
          start: {
            line: 55,
            column: 441
          },
          end: {
            line: 60,
            column: 10
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 457
          },
          end: {
            line: 60,
            column: 5
          }
        }, {
          start: {
            line: 60,
            column: 8
          },
          end: {
            line: 60,
            column: 10
          }
        }],
        line: 55
      },
      "30": {
        loc: {
          start: {
            line: 64,
            column: 4
          },
          end: {
            line: 66,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 4
          },
          end: {
            line: 66,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "31": {
        loc: {
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 69,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 69,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "32": {
        loc: {
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 73,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 73,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "33": {
        loc: {
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 78,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 78,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "34": {
        loc: {
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 83,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 83,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "35": {
        loc: {
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 125,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 103,
            column: 18
          }
        }, {
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 109,
            column: 18
          }
        }, {
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 115,
            column: 18
          }
        }, {
          start: {
            line: 116,
            column: 12
          },
          end: {
            line: 124,
            column: 18
          }
        }],
        line: 97
      },
      "36": {
        loc: {
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 130,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 130,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "37": {
        loc: {
          start: {
            line: 142,
            column: 298
          },
          end: {
            line: 142,
            column: 388
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 298
          },
          end: {
            line: 142,
            column: 309
          }
        }, {
          start: {
            line: 142,
            column: 314
          },
          end: {
            line: 142,
            column: 387
          }
        }],
        line: 142
      },
      "38": {
        loc: {
          start: {
            line: 142,
            column: 526
          },
          end: {
            line: 142,
            column: 584
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 142,
            column: 563
          },
          end: {
            line: 142,
            column: 572
          }
        }, {
          start: {
            line: 142,
            column: 575
          },
          end: {
            line: 142,
            column: 584
          }
        }],
        line: 142
      },
      "39": {
        loc: {
          start: {
            line: 142,
            column: 836
          },
          end: {
            line: 142,
            column: 887
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 142,
            column: 866
          },
          end: {
            line: 142,
            column: 875
          }
        }, {
          start: {
            line: 142,
            column: 878
          },
          end: {
            line: 142,
            column: 887
          }
        }],
        line: 142
      },
      "40": {
        loc: {
          start: {
            line: 142,
            column: 1122
          },
          end: {
            line: 142,
            column: 1173
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 142,
            column: 1152
          },
          end: {
            line: 142,
            column: 1161
          }
        }, {
          start: {
            line: 142,
            column: 1164
          },
          end: {
            line: 142,
            column: 1173
          }
        }],
        line: 142
      },
      "41": {
        loc: {
          start: {
            line: 142,
            column: 1408
          },
          end: {
            line: 142,
            column: 1463
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 142,
            column: 1442
          },
          end: {
            line: 142,
            column: 1451
          }
        }, {
          start: {
            line: 142,
            column: 1454
          },
          end: {
            line: 142,
            column: 1463
          }
        }],
        line: 142
      },
      "42": {
        loc: {
          start: {
            line: 142,
            column: 1834
          },
          end: {
            line: 142,
            column: 3086
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 1834
          },
          end: {
            line: 142,
            column: 1868
          }
        }, {
          start: {
            line: 142,
            column: 1873
          },
          end: {
            line: 142,
            column: 3085
          }
        }],
        line: 142
      },
      "43": {
        loc: {
          start: {
            line: 142,
            column: 3088
          },
          end: {
            line: 142,
            column: 4330
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 3088
          },
          end: {
            line: 142,
            column: 3115
          }
        }, {
          start: {
            line: 142,
            column: 3120
          },
          end: {
            line: 142,
            column: 4329
          }
        }],
        line: 142
      },
      "44": {
        loc: {
          start: {
            line: 142,
            column: 4332
          },
          end: {
            line: 142,
            column: 5589
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 4332
          },
          end: {
            line: 142,
            column: 4359
          }
        }, {
          start: {
            line: 142,
            column: 4364
          },
          end: {
            line: 142,
            column: 5588
          }
        }],
        line: 142
      },
      "45": {
        loc: {
          start: {
            line: 150,
            column: 54
          },
          end: {
            line: 150,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 150,
            column: 54
          },
          end: {
            line: 150,
            column: 64
          }
        }, {
          start: {
            line: 150,
            column: 68
          },
          end: {
            line: 150,
            column: 113
          }
        }],
        line: 150
      },
      "46": {
        loc: {
          start: {
            line: 150,
            column: 380
          },
          end: {
            line: 154,
            column: 469
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 150,
            column: 380
          },
          end: {
            line: 150,
            column: 414
          }
        }, {
          start: {
            line: 150,
            column: 419
          },
          end: {
            line: 154,
            column: 468
          }
        }],
        line: 150
      },
      "47": {
        loc: {
          start: {
            line: 154,
            column: 471
          },
          end: {
            line: 154,
            column: 1127
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 471
          },
          end: {
            line: 154,
            column: 498
          }
        }, {
          start: {
            line: 154,
            column: 503
          },
          end: {
            line: 154,
            column: 1126
          }
        }],
        line: 154
      },
      "48": {
        loc: {
          start: {
            line: 154,
            column: 1129
          },
          end: {
            line: 154,
            column: 1784
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 1129
          },
          end: {
            line: 154,
            column: 1156
          }
        }, {
          start: {
            line: 154,
            column: 1161
          },
          end: {
            line: 154,
            column: 1783
          }
        }],
        line: 154
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/MarketInsightsChart.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8Cb,sCAoWC;;AAhZD,6CAAwC;AACxC,qCASkB;AAClB,6CAAiG;AACjG,iDAAgD;AAChD,+CAA8C;AAC9C,6CAAwE;AA8BxE,SAAwB,mBAAmB,CAAC,EAejB;QAdzB,IAAI,UAAA,EACJ,aAAyB,EAAzB,KAAK,mBAAG,iBAAiB,KAAA,EACzB,mBAAuD,EAAvD,WAAW,mBAAG,yCAAyC,KAAA,EACvD,cAAY,EAAZ,MAAM,mBAAG,GAAG,KAAA,EACZ,kBAAiB,EAAjB,UAAU,mBAAG,IAAI,KAAA,EACjB,cAAwB,EAAxB,MAAM,mBAAG,eAAe,KAAA,EACxB,MAAM,YAAA,EACN,cAAc,oBAAA,EACd,cAKC,EALD,MAAM,mBAAG;QACP,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;KAClB,KAAA;IAEK,IAAA,KAAsC,IAAA,gBAAQ,EAAC,MAAM,CAAC,EAArD,cAAc,QAAA,EAAE,iBAAiB,QAAoB,CAAC;IAE7D,uBAAuB;IACvB,IAAI,aAAa,qBAAO,IAAI,OAAC,CAAC;IAE9B,IAAI,cAAc,EAAE,CAAC;QACnB,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,QAAQ,KAAK,cAAc,EAAhC,CAAgC,CAAC,CAAC;IACjF,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACX,aAAa,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAArB,CAAqB,CAAC,CAAC;IACtD,CAAC;IAED,uBAAuB;IACvB,IAAM,WAAW,GAAG,UAAC,MAAgB;QACnC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,EAAX,CAAW,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IACvE,CAAC,CAAC;IAEF,IAAM,OAAO,GAAG,UAAC,MAAgB;QAC/B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,MAAM,EAAE;IAC7B,CAAC,CAAC;IAEF,IAAM,OAAO,GAAG,UAAC,MAAgB;QAC/B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,MAAM,EAAE;IAC7B,CAAC,CAAC;IAEF,IAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,EAAX,CAAW,CAAC,CAAC,CAAC;IAC7D,IAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,EAAX,CAAW,CAAC,CAAC,CAAC;IAC7D,IAAM,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;IAExC,IAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,aAAa,EAAlB,CAAkB,CAAC,CAAC,CAAC;IACpE,IAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,aAAa,EAAlB,CAAkB,CAAC,CAAC,CAAC;IACpE,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,aAAa,EAAlB,CAAkB,CAAC,CAAC,CAAC;IAEnE,IAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,EAAX,CAAW,CAAC,CAAC,CAAC;IAC7D,IAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,EAAX,CAAW,CAAC,CAAC,CAAC;IAC7D,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,EAAX,CAAW,CAAC,CAAC,CAAC;IAE5D,mDAAmD;IACnD,IAAM,cAAc,GAAG;QACrB,QAAQ,cAAc,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACX,OAAO;oBACL,KAAK,EAAE,gBAAgB;oBACvB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtE,cAAc,EAAE,UAAC,KAAa,IAAK,OAAA,WAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAG,EAAhC,CAAgC;iBACpE,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO;oBACL,KAAK,EAAE,aAAa;oBACpB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;oBACjE,cAAc,EAAE,UAAC,KAAa,IAAK,OAAA,UAAG,KAAK,MAAG,EAAX,CAAW;iBAC/C,CAAC;YACJ,KAAK,YAAY;gBACf,OAAO;oBACL,KAAK,EAAE,qBAAqB;oBAC5B,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;oBACnE,cAAc,EAAE,UAAC,KAAa,IAAK,OAAA,KAAK,CAAC,QAAQ,EAAE,EAAhB,CAAgB;iBACpD,CAAC;YACJ;gBACE,OAAO;oBACL,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE;wBACJ,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;wBACvD,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;qBACxD;oBACD,cAAc,EAAE,UAAC,KAAa,IAAK,OAAA,KAAK,CAAC,QAAQ,EAAE,EAAhB,CAAgB;iBACpD,CAAC;QACN,CAAC;IACH,CAAC,CAAC;IAEF,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IAErC,qCAAqC;IACrC,IAAM,YAAY,GAAG,UAAC,OAAwE,EAAE,KAAS;QAAT,sBAAA,EAAA,SAAS;QACvG,OAAO,kBAAI,aAAa,QACrB,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAC,CAAC,CAAC,OAAO,CAAY,GAAI,CAAC,CAAC,OAAO,CAAY,EAA/C,CAA+C,CAAC;aAC/D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,IAAM,YAAY,GAAG,UAAC,MAAc;QAClC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACpB,CAAC,CAAC;IAEF,OAAO,CACL,wBAAC,WAAI,eACH,wBAAC,iBAAU,eACT,wBAAC,gBAAS,IAAC,SAAS,EAAC,yBAAyB,aAC5C,uBAAC,wBAAS,IAAC,SAAS,EAAC,SAAS,GAAG,EAChC,KAAK,IACI,EACX,WAAW,IAAI,CACd,uBAAC,sBAAe,cAAE,WAAW,GAAmB,CACjD,EAGD,iCAAK,SAAS,EAAC,2BAA2B,aACxC,wBAAC,eAAM,IACL,OAAO,EAAE,cAAc,KAAK,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EACnE,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,cAAM,OAAA,iBAAiB,CAAC,eAAe,CAAC,EAAlC,CAAkC,aAEjD,uBAAC,yBAAU,IAAC,SAAS,EAAC,cAAc,GAAG,wBAEhC,EACT,wBAAC,eAAM,IACL,OAAO,EAAE,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAC5D,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,cAAM,OAAA,iBAAiB,CAAC,QAAQ,CAAC,EAA3B,CAA2B,aAE1C,uBAAC,yBAAU,IAAC,SAAS,EAAC,cAAc,GAAG,cAEhC,EACT,wBAAC,eAAM,IACL,OAAO,EAAE,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAC5D,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,cAAM,OAAA,iBAAiB,CAAC,QAAQ,CAAC,EAA3B,CAA2B,aAE1C,uBAAC,yBAAU,IAAC,SAAS,EAAC,cAAc,GAAG,cAEhC,EACT,wBAAC,eAAM,IACL,OAAO,EAAE,cAAc,KAAK,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAChE,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,cAAM,OAAA,iBAAiB,CAAC,YAAY,CAAC,EAA/B,CAA+B,aAE9C,uBAAC,oBAAK,IAAC,SAAS,EAAC,cAAc,GAAG,kBAE3B,IACL,IACK,EAEb,wBAAC,kBAAW,IAAC,SAAS,EAAC,WAAW,aAEhC,iCAAK,SAAS,EAAC,uCAAuC,aACnD,cAAc,KAAK,eAAe,IAAI,CACrC,6DACE,iCAAK,SAAS,EAAC,2DAA2D,aACxE,gCAAK,SAAS,EAAC,sDAAsD,2BAE/D,EACN,gCAAK,SAAS,EAAC,qDAAqD,YACjE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GACjB,IACF,EACN,iCAAK,SAAS,EAAC,6DAA6D,aAC1E,gCAAK,SAAS,EAAC,wDAAwD,2BAEjE,EACN,gCAAK,SAAS,EAAC,uDAAuD,YACnE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GACjB,IACF,EACN,iCAAK,SAAS,EAAC,6DAA6D,aAC1E,gCAAK,SAAS,EAAC,wDAAwD,2BAEjE,EACN,gCAAK,SAAS,EAAC,uDAAuD,YACnE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GACjB,IACF,IACL,CACJ,EAEA,cAAc,KAAK,QAAQ,IAAI,CAC9B,6DACE,iCAAK,SAAS,EAAC,2DAA2D,aACxE,gCAAK,SAAS,EAAC,sDAAsD,2BAE/D,EACN,gCAAK,SAAS,EAAC,qDAAqD,YACjE,YAAY,CAAC,SAAS,CAAC,GACpB,IACF,EACN,iCAAK,SAAS,EAAC,6DAA6D,aAC1E,gCAAK,SAAS,EAAC,wDAAwD,wBAEjE,EACN,gCAAK,SAAS,EAAC,uDAAuD,YACnE,YAAY,CAAC,aAAa,CAAC,GACxB,IACF,EACN,iCAAK,SAAS,EAAC,yDAAyD,aACtE,gCAAK,SAAS,EAAC,oDAAoD,uBAE7D,EACN,gCAAK,SAAS,EAAC,mDAAmD,YAC/D,YAAY,CAAC,YAAY,CAAC,GACvB,IACF,IACL,CACJ,EAEA,cAAc,KAAK,QAAQ,IAAI,CAC9B,6DACE,iCAAK,SAAS,EAAC,2DAA2D,aACxE,gCAAK,SAAS,EAAC,sDAAsD,2BAE/D,EACN,iCAAK,SAAS,EAAC,qDAAqD,aACjE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SACjB,IACF,EACN,iCAAK,SAAS,EAAC,6DAA6D,aAC1E,gCAAK,SAAS,EAAC,wDAAwD,wBAEjE,EACN,iCAAK,SAAS,EAAC,uDAAuD,aACnE,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,SACrB,IACF,EACN,iCAAK,SAAS,EAAC,yDAAyD,aACtE,gCAAK,SAAS,EAAC,oDAAoD,uBAE7D,EACN,iCAAK,SAAS,EAAC,mDAAmD,aAC/D,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,SACpB,IACF,IACL,CACJ,IACG,EAGN,4CACE,+BAAI,SAAS,EAAC,4BAA4B,YAAE,WAAW,CAAC,KAAK,GAAM,EACnE,gCAAK,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,QAAA,EAAE,YACnC,uBAAC,8BAAmB,cAClB,wBAAC,mBAAQ,IAAC,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,aAChF,uBAAC,wBAAa,IAAC,eAAe,EAAC,KAAK,EAAC,SAAS,EAAC,sCAAsC,GAAG,EACxF,uBAAC,gBAAK,IACJ,OAAO,EAAC,OAAO,EACf,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,EACtB,SAAS,EAAC,kCAAkC,GAC5C,EACF,uBAAC,gBAAK,IACJ,aAAa,EAAE,WAAW,CAAC,cAAc,EACzC,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,EACtB,SAAS,EAAC,kCAAkC,GAC5C,EACF,uBAAC,kBAAO,IACN,SAAS,EAAE,UAAC,KAAa,EAAE,IAAY,IAAK,OAAA;oDAC1C,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC;oDACjC,IAAI;iDACL,EAH2C,CAG3C,EACD,YAAY,EAAE;oDACZ,eAAe,EAAE,mBAAmB;oDACpC,MAAM,EAAE,yBAAyB;oDACjC,YAAY,EAAE,KAAK;oDACnB,KAAK,EAAE,mBAAmB;iDAC3B,GACD,EACD,UAAU,IAAI,uBAAC,iBAAM,KAAG,EAExB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,CAC7B,uBAAC,cAAG,IAEF,OAAO,EAAE,GAAG,CAAC,GAAG,EAChB,IAAI,EAAE,GAAG,CAAC,KAAK,EACf,IAAI,EAAE,GAAG,CAAC,IAAI,IAHT,GAAG,CAAC,GAAG,CAIZ,CACH,EAP8B,CAO9B,CAAC,IACO,GACS,GAClB,IACF,EAGN,iCAAK,SAAS,EAAC,uCAAuC,aACnD,cAAc,KAAK,eAAe,IAAI,CACrC,6DACE,4CACE,+BAAI,SAAS,EAAC,oBAAoB,mCAAwB,EAC1D,gCAAK,SAAS,EAAC,WAAW,YACvB,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CACrC,iCAAuB,SAAS,EAAC,8EAA8E,aAC7G,iCAAM,SAAS,EAAC,aAAa,YAAE,KAAK,CAAC,KAAK,GAAQ,EAClD,wBAAC,aAAK,IAAC,OAAO,EAAC,WAAW,aAAE,KAAK,CAAC,MAAM,gBAAiB,KAFjD,KAAK,CAAC,KAAK,CAGf,CACP,EALsC,CAKtC,CAAC,GACE,IACF,EAEN,4CACE,+BAAI,SAAS,EAAC,oBAAoB,qCAA0B,EAC5D,gCAAK,SAAS,EAAC,WAAW,YACvB,aAAa;qDACX,MAAM,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAA3B,CAA2B,CAAC;qDAC5C,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,EAA7C,CAA6C,CAAC;qDAC7D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;qDACX,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CACd,iCAAuB,SAAS,EAAC,8EAA8E,aAC7G,iCAAM,SAAS,EAAC,aAAa,YAAE,KAAK,CAAC,KAAK,GAAQ,EAClD,wBAAC,aAAK,IAAC,OAAO,EAAC,WAAW,kBAAG,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAa,KAF1E,KAAK,CAAC,KAAK,CAGf,CACP,EALe,CAKf,CAAC,GACA,IACF,IACL,CACJ,EAEA,cAAc,KAAK,QAAQ,IAAI,CAC9B,4CACE,+BAAI,SAAS,EAAC,oBAAoB,+BAAoB,EACtD,gCAAK,SAAS,EAAC,WAAW,YACvB,YAAY,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAC5C,iCAAuB,SAAS,EAAC,8EAA8E,aAC7G,iCAAM,SAAS,EAAC,aAAa,YAAE,KAAK,CAAC,KAAK,GAAQ,EAClD,uBAAC,aAAK,IAAC,OAAO,EAAC,WAAW,YAAE,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,GAAS,KAF9D,KAAK,CAAC,KAAK,CAGf,CACP,EAL6C,CAK7C,CAAC,GACE,IACF,CACP,EAEA,cAAc,KAAK,QAAQ,IAAI,CAC9B,4CACE,+BAAI,SAAS,EAAC,oBAAoB,gCAAqB,EACvD,gCAAK,SAAS,EAAC,WAAW,YACvB,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CACrC,iCAAuB,SAAS,EAAC,8EAA8E,aAC7G,iCAAM,SAAS,EAAC,aAAa,YAAE,KAAK,CAAC,KAAK,GAAQ,EAClD,wBAAC,aAAK,IAAC,OAAO,EAAC,WAAW,aAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAiB,KAF5D,KAAK,CAAC,KAAK,CAGf,CACP,EALsC,CAKtC,CAAC,GACE,IACF,CACP,IACG,IACM,IACT,CACR,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/MarketInsightsChart.tsx"],
      sourcesContent: ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  Legend,\n} from 'recharts';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { TrendingUp, DollarSign, BarChart3, Clock } from 'lucide-react';\n\ninterface MarketData {\n  skill: string;\n  demand: number;\n  supply: number;\n  averageSalary: number;\n  growth: number;\n  difficulty: number;\n  timeToLearn: number;\n  category: string;\n}\n\ninterface MarketInsightsChartProps {\n  data: MarketData[];\n  title?: string;\n  description?: string;\n  height?: number;\n  showLegend?: boolean;\n  metric?: 'demand-supply' | 'salary' | 'growth' | 'difficulty';\n  sortBy?: 'demand' | 'supply' | 'averageSalary' | 'growth' | 'difficulty';\n  filterCategory?: string;\n  colors?: {\n    demand?: string;\n    supply?: string;\n    salary?: string;\n    growth?: string;\n  };\n}\n\nexport default function MarketInsightsChart({\n  data,\n  title = \"Market Insights\",\n  description = \"Skill demand, supply, and market trends\",\n  height = 400,\n  showLegend = true,\n  metric = 'demand-supply',\n  sortBy,\n  filterCategory,\n  colors = {\n    demand: '#3b82f6',\n    supply: '#10b981',\n    salary: '#f59e0b',\n    growth: '#8b5cf6',\n  },\n}: MarketInsightsChartProps) {\n  const [selectedMetric, setSelectedMetric] = useState(metric);\n\n  // Filter and sort data\n  let processedData = [...data];\n  \n  if (filterCategory) {\n    processedData = processedData.filter(item => item.category === filterCategory);\n  }\n  \n  if (sortBy) {\n    processedData.sort((a, b) => b[sortBy] - a[sortBy]);\n  }\n\n  // Calculate statistics\n  const safeAverage = (values: number[]) => {\n    if (values.length === 0) return 0;\n    return values.reduce((sum, value) => sum + value, 0) / values.length;\n  };\n\n  const safeMax = (values: number[]) => {\n    if (values.length === 0) return 0;\n    return Math.max(...values);\n  };\n\n  const safeMin = (values: number[]) => {\n    if (values.length === 0) return 0;\n    return Math.min(...values);\n  };\n\n  const avgDemand = safeAverage(data.map(item => item.demand));\n  const avgSupply = safeAverage(data.map(item => item.supply));\n  const marketGap = avgDemand - avgSupply;\n  \n  const avgSalary = safeAverage(data.map(item => item.averageSalary));\n  const highestSalary = safeMax(data.map(item => item.averageSalary));\n  const lowestSalary = safeMin(data.map(item => item.averageSalary));\n  \n  const avgGrowth = safeAverage(data.map(item => item.growth));\n  const highestGrowth = safeMax(data.map(item => item.growth));\n  const lowestGrowth = safeMin(data.map(item => item.growth));\n\n  // Get chart configuration based on selected metric\n  const getChartConfig = () => {\n    switch (selectedMetric) {\n      case 'salary':\n        return {\n          title: 'Average Salary',\n          bars: [{ key: 'averageSalary', name: 'Salary', color: colors.salary }],\n          yAxisFormatter: (value: number) => `$${(value / 1000).toFixed(0)}k`,\n        };\n      case 'growth':\n        return {\n          title: 'Growth Rate',\n          bars: [{ key: 'growth', name: 'Growth %', color: colors.growth }],\n          yAxisFormatter: (value: number) => `${value}%`,\n        };\n      case 'difficulty':\n        return {\n          title: 'Learning Difficulty',\n          bars: [{ key: 'difficulty', name: 'Difficulty', color: '#ef4444' }],\n          yAxisFormatter: (value: number) => value.toString(),\n        };\n      default:\n        return {\n          title: 'Demand vs Supply',\n          bars: [\n            { key: 'demand', name: 'Demand', color: colors.demand },\n            { key: 'supply', name: 'Supply', color: colors.supply },\n          ],\n          yAxisFormatter: (value: number) => value.toString(),\n        };\n    }\n  };\n\n  const chartConfig = getChartConfig();\n\n  // Get top skills for recommendations\n  const getTopSkills = (sortKey: 'demand' | 'supply' | 'averageSalary' | 'growth' | 'difficulty', limit = 3) => {\n    return [...processedData]\n      .sort((a, b) => (b[sortKey] as number) - (a[sortKey] as number))\n      .slice(0, limit);\n  };\n\n  const formatSalary = (salary: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0,\n    }).format(salary);\n  };\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <BarChart3 className=\"h-5 w-5\" />\n          {title}\n        </CardTitle>\n        {description && (\n          <CardDescription>{description}</CardDescription>\n        )}\n        \n        {/* Metric Selection */}\n        <div className=\"flex flex-wrap gap-2 mt-4\">\n          <Button\n            variant={selectedMetric === 'demand-supply' ? 'default' : 'outline'}\n            size=\"sm\"\n            onClick={() => setSelectedMetric('demand-supply')}\n          >\n            <TrendingUp className=\"h-4 w-4 mr-1\" />\n            Demand vs Supply\n          </Button>\n          <Button\n            variant={selectedMetric === 'salary' ? 'default' : 'outline'}\n            size=\"sm\"\n            onClick={() => setSelectedMetric('salary')}\n          >\n            <DollarSign className=\"h-4 w-4 mr-1\" />\n            Salary\n          </Button>\n          <Button\n            variant={selectedMetric === 'growth' ? 'default' : 'outline'}\n            size=\"sm\"\n            onClick={() => setSelectedMetric('growth')}\n          >\n            <TrendingUp className=\"h-4 w-4 mr-1\" />\n            Growth\n          </Button>\n          <Button\n            variant={selectedMetric === 'difficulty' ? 'default' : 'outline'}\n            size=\"sm\"\n            onClick={() => setSelectedMetric('difficulty')}\n          >\n            <Clock className=\"h-4 w-4 mr-1\" />\n            Difficulty\n          </Button>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-6\">\n        {/* Summary Statistics */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {selectedMetric === 'demand-supply' && (\n            <>\n              <div className=\"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">\n                  Avg Demand\n                </div>\n                <div className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n                  {avgDemand.toFixed(1)}\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-green-700 dark:text-green-300\">\n                  Avg Supply\n                </div>\n                <div className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\n                  {avgSupply.toFixed(1)}\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-amber-700 dark:text-amber-300\">\n                  Market Gap\n                </div>\n                <div className=\"text-2xl font-bold text-amber-900 dark:text-amber-100\">\n                  {marketGap.toFixed(1)}\n                </div>\n              </div>\n            </>\n          )}\n          \n          {selectedMetric === 'salary' && (\n            <>\n              <div className=\"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">\n                  Avg Salary\n                </div>\n                <div className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n                  {formatSalary(avgSalary)}\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-green-700 dark:text-green-300\">\n                  Highest\n                </div>\n                <div className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\n                  {formatSalary(highestSalary)}\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n                  Lowest\n                </div>\n                <div className=\"text-2xl font-bold text-red-900 dark:text-red-100\">\n                  {formatSalary(lowestSalary)}\n                </div>\n              </div>\n            </>\n          )}\n          \n          {selectedMetric === 'growth' && (\n            <>\n              <div className=\"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">\n                  Avg Growth\n                </div>\n                <div className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n                  {avgGrowth.toFixed(1)}%\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-green-700 dark:text-green-300\">\n                  Highest\n                </div>\n                <div className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\n                  {highestGrowth.toFixed(1)}%\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\">\n                <div className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n                  Lowest\n                </div>\n                <div className=\"text-2xl font-bold text-red-900 dark:text-red-100\">\n                  {lowestGrowth.toFixed(1)}%\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Chart */}\n        <div>\n          <h3 className=\"text-lg font-semibold mb-4\">{chartConfig.title}</h3>\n          <div style={{ width: '100%', height }}>\n            <ResponsiveContainer>\n              <BarChart data={processedData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"stroke-gray-200 dark:stroke-gray-700\" />\n                <XAxis \n                  dataKey=\"skill\" \n                  tick={{ fontSize: 12 }}\n                  className=\"fill-gray-600 dark:fill-gray-400\"\n                />\n                <YAxis \n                  tickFormatter={chartConfig.yAxisFormatter}\n                  tick={{ fontSize: 12 }}\n                  className=\"fill-gray-600 dark:fill-gray-400\"\n                />\n                <Tooltip\n                  formatter={(value: number, name: string) => [\n                    chartConfig.yAxisFormatter(value),\n                    name\n                  ]}\n                  contentStyle={{\n                    backgroundColor: 'var(--background)',\n                    border: '1px solid var(--border)',\n                    borderRadius: '6px',\n                    color: 'var(--foreground)',\n                  }}\n                />\n                {showLegend && <Legend />}\n                \n                {chartConfig.bars.map((bar) => (\n                  <Bar\n                    key={bar.key}\n                    dataKey={bar.key}\n                    fill={bar.color}\n                    name={bar.name}\n                  />\n                ))}\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Skill Recommendations */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {selectedMetric === 'demand-supply' && (\n            <>\n              <div>\n                <h4 className=\"font-semibold mb-3\">High Demand Skills</h4>\n                <div className=\"space-y-2\">\n                  {getTopSkills('demand').map((skill) => (\n                    <div key={skill.skill} className=\"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded\">\n                      <span className=\"font-medium\">{skill.skill}</span>\n                      <Badge variant=\"secondary\">{skill.demand}% demand</Badge>\n                    </div>\n                  ))}\n                </div>\n              </div>\n              \n              <div>\n                <h4 className=\"font-semibold mb-3\">Market Opportunities</h4>\n                <div className=\"space-y-2\">\n                  {processedData\n                    .filter(skill => skill.demand > skill.supply)\n                    .sort((a, b) => (b.demand - b.supply) - (a.demand - a.supply))\n                    .slice(0, 3)\n                    .map((skill) => (\n                      <div key={skill.skill} className=\"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded\">\n                        <span className=\"font-medium\">{skill.skill}</span>\n                        <Badge variant=\"secondary\">+{(skill.demand - skill.supply).toFixed(1)} gap</Badge>\n                      </div>\n                    ))}\n                </div>\n              </div>\n            </>\n          )}\n          \n          {selectedMetric === 'salary' && (\n            <div>\n              <h4 className=\"font-semibold mb-3\">Highest Paying</h4>\n              <div className=\"space-y-2\">\n                {getTopSkills('averageSalary').map((skill) => (\n                  <div key={skill.skill} className=\"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded\">\n                    <span className=\"font-medium\">{skill.skill}</span>\n                    <Badge variant=\"secondary\">{formatSalary(skill.averageSalary)}</Badge>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n          \n          {selectedMetric === 'growth' && (\n            <div>\n              <h4 className=\"font-semibold mb-3\">Fastest Growing</h4>\n              <div className=\"space-y-2\">\n                {getTopSkills('growth').map((skill) => (\n                  <div key={skill.skill} className=\"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded\">\n                    <span className=\"font-medium\">{skill.skill}</span>\n                    <Badge variant=\"secondary\">{skill.growth.toFixed(1)}% growth</Badge>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b323cf903b2b25f55d1b2085b527a028e3b7cac0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2d3gbsqjv2 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2d3gbsqjv2();
var __createBinding =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[0]++,
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2d3gbsqjv2().f[0]++;
  cov_2d3gbsqjv2().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().b[2][0]++;
    cov_2d3gbsqjv2().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2d3gbsqjv2().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[5][1]++,
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().b[3][0]++;
    cov_2d3gbsqjv2().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_2d3gbsqjv2().f[1]++;
        cov_2d3gbsqjv2().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_2d3gbsqjv2().b[3][1]++;
  }
  cov_2d3gbsqjv2().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2d3gbsqjv2().f[2]++;
  cov_2d3gbsqjv2().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().b[7][0]++;
    cov_2d3gbsqjv2().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2d3gbsqjv2().b[7][1]++;
  }
  cov_2d3gbsqjv2().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[11]++,
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_2d3gbsqjv2().f[3]++;
  cov_2d3gbsqjv2().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_2d3gbsqjv2().f[4]++;
  cov_2d3gbsqjv2().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[14]++,
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_2d3gbsqjv2().f[5]++;
  cov_2d3gbsqjv2().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[6]++;
    cov_2d3gbsqjv2().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_2d3gbsqjv2().s[17]++, []);
      /* istanbul ignore next */
      cov_2d3gbsqjv2().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_2d3gbsqjv2().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_2d3gbsqjv2().b[12][0]++;
          cov_2d3gbsqjv2().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_2d3gbsqjv2().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_2d3gbsqjv2().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_2d3gbsqjv2().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[8]++;
    cov_2d3gbsqjv2().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().b[13][0]++;
      cov_2d3gbsqjv2().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_2d3gbsqjv2().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[26]++, {});
    /* istanbul ignore next */
    cov_2d3gbsqjv2().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().b[15][0]++;
      cov_2d3gbsqjv2().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_2d3gbsqjv2().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_2d3gbsqjv2().b[16][0]++;
          cov_2d3gbsqjv2().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_2d3gbsqjv2().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_2d3gbsqjv2().b[15][1]++;
    }
    cov_2d3gbsqjv2().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_2d3gbsqjv2().s[34]++;
    return result;
  };
}()));
var __spreadArray =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[35]++,
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[17][0]++, this) &&
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[17][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_2d3gbsqjv2().b[17][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_2d3gbsqjv2().f[9]++;
  cov_2d3gbsqjv2().s[36]++;
  if (
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[19][0]++, pack) ||
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[19][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().b[18][0]++;
    cov_2d3gbsqjv2().s[37]++;
    for (var i =
      /* istanbul ignore next */
      (cov_2d3gbsqjv2().s[38]++, 0), l =
      /* istanbul ignore next */
      (cov_2d3gbsqjv2().s[39]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().s[40]++;
      if (
      /* istanbul ignore next */
      (cov_2d3gbsqjv2().b[21][0]++, ar) ||
      /* istanbul ignore next */
      (cov_2d3gbsqjv2().b[21][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_2d3gbsqjv2().b[20][0]++;
        cov_2d3gbsqjv2().s[41]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_2d3gbsqjv2().b[22][0]++;
          cov_2d3gbsqjv2().s[42]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_2d3gbsqjv2().b[22][1]++;
        }
        cov_2d3gbsqjv2().s[43]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_2d3gbsqjv2().b[20][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_2d3gbsqjv2().b[18][1]++;
  }
  cov_2d3gbsqjv2().s[44]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[23][0]++, ar) ||
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().b[23][1]++, Array.prototype.slice.call(from)));
}));
/* istanbul ignore next */
cov_2d3gbsqjv2().s[45]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2d3gbsqjv2().s[46]++;
exports.default = MarketInsightsChart;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[47]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[48]++, __importStar(require("react")));
var recharts_1 =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[49]++, require("recharts"));
var card_1 =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[50]++, require("@/components/ui/card"));
var button_1 =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[51]++, require("@/components/ui/button"));
var badge_1 =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[52]++, require("@/components/ui/badge"));
var lucide_react_1 =
/* istanbul ignore next */
(cov_2d3gbsqjv2().s[53]++, require("lucide-react"));
function MarketInsightsChart(_a) {
  /* istanbul ignore next */
  cov_2d3gbsqjv2().f[10]++;
  var data =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[54]++, _a.data),
    _b =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[55]++, _a.title),
    title =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[56]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[24][0]++, "Market Insights") :
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[24][1]++, _b)),
    _c =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[57]++, _a.description),
    description =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[58]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[25][0]++, "Skill demand, supply, and market trends") :
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[25][1]++, _c)),
    _d =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[59]++, _a.height),
    height =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[60]++, _d === void 0 ?
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[26][0]++, 400) :
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[26][1]++, _d)),
    _e =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[61]++, _a.showLegend),
    showLegend =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[62]++, _e === void 0 ?
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[27][0]++, true) :
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[27][1]++, _e)),
    _f =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[63]++, _a.metric),
    metric =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[64]++, _f === void 0 ?
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[28][0]++, 'demand-supply') :
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[28][1]++, _f)),
    sortBy =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[65]++, _a.sortBy),
    filterCategory =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[66]++, _a.filterCategory),
    _g =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[67]++, _a.colors),
    colors =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[68]++, _g === void 0 ?
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[29][0]++, {
      demand: '#3b82f6',
      supply: '#10b981',
      salary: '#f59e0b',
      growth: '#8b5cf6'
    }) :
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().b[29][1]++, _g));
  var _h =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[69]++, (0, react_1.useState)(metric)),
    selectedMetric =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[70]++, _h[0]),
    setSelectedMetric =
    /* istanbul ignore next */
    (cov_2d3gbsqjv2().s[71]++, _h[1]);
  // Filter and sort data
  var processedData =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[72]++, __spreadArray([], data, true));
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[73]++;
  if (filterCategory) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().b[30][0]++;
    cov_2d3gbsqjv2().s[74]++;
    processedData = processedData.filter(function (item) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().f[11]++;
      cov_2d3gbsqjv2().s[75]++;
      return item.category === filterCategory;
    });
  } else
  /* istanbul ignore next */
  {
    cov_2d3gbsqjv2().b[30][1]++;
  }
  cov_2d3gbsqjv2().s[76]++;
  if (sortBy) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().b[31][0]++;
    cov_2d3gbsqjv2().s[77]++;
    processedData.sort(function (a, b) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().f[12]++;
      cov_2d3gbsqjv2().s[78]++;
      return b[sortBy] - a[sortBy];
    });
  } else
  /* istanbul ignore next */
  {
    cov_2d3gbsqjv2().b[31][1]++;
  }
  // Calculate statistics
  cov_2d3gbsqjv2().s[79]++;
  var safeAverage = function (values) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[13]++;
    cov_2d3gbsqjv2().s[80]++;
    if (values.length === 0) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().b[32][0]++;
      cov_2d3gbsqjv2().s[81]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_2d3gbsqjv2().b[32][1]++;
    }
    cov_2d3gbsqjv2().s[82]++;
    return values.reduce(function (sum, value) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().f[14]++;
      cov_2d3gbsqjv2().s[83]++;
      return sum + value;
    }, 0) / values.length;
  };
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[84]++;
  var safeMax = function (values) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[15]++;
    cov_2d3gbsqjv2().s[85]++;
    if (values.length === 0) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().b[33][0]++;
      cov_2d3gbsqjv2().s[86]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_2d3gbsqjv2().b[33][1]++;
    }
    cov_2d3gbsqjv2().s[87]++;
    return Math.max.apply(Math, values);
  };
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[88]++;
  var safeMin = function (values) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[16]++;
    cov_2d3gbsqjv2().s[89]++;
    if (values.length === 0) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().b[34][0]++;
      cov_2d3gbsqjv2().s[90]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_2d3gbsqjv2().b[34][1]++;
    }
    cov_2d3gbsqjv2().s[91]++;
    return Math.min.apply(Math, values);
  };
  var avgDemand =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[92]++, safeAverage(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[17]++;
    cov_2d3gbsqjv2().s[93]++;
    return item.demand;
  })));
  var avgSupply =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[94]++, safeAverage(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[18]++;
    cov_2d3gbsqjv2().s[95]++;
    return item.supply;
  })));
  var marketGap =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[96]++, avgDemand - avgSupply);
  var avgSalary =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[97]++, safeAverage(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[19]++;
    cov_2d3gbsqjv2().s[98]++;
    return item.averageSalary;
  })));
  var highestSalary =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[99]++, safeMax(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[20]++;
    cov_2d3gbsqjv2().s[100]++;
    return item.averageSalary;
  })));
  var lowestSalary =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[101]++, safeMin(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[21]++;
    cov_2d3gbsqjv2().s[102]++;
    return item.averageSalary;
  })));
  var avgGrowth =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[103]++, safeAverage(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[22]++;
    cov_2d3gbsqjv2().s[104]++;
    return item.growth;
  })));
  var highestGrowth =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[105]++, safeMax(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[23]++;
    cov_2d3gbsqjv2().s[106]++;
    return item.growth;
  })));
  var lowestGrowth =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[107]++, safeMin(data.map(function (item) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[24]++;
    cov_2d3gbsqjv2().s[108]++;
    return item.growth;
  })));
  // Get chart configuration based on selected metric
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[109]++;
  var getChartConfig = function () {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[25]++;
    cov_2d3gbsqjv2().s[110]++;
    switch (selectedMetric) {
      case 'salary':
        /* istanbul ignore next */
        cov_2d3gbsqjv2().b[35][0]++;
        cov_2d3gbsqjv2().s[111]++;
        return {
          title: 'Average Salary',
          bars: [{
            key: 'averageSalary',
            name: 'Salary',
            color: colors.salary
          }],
          yAxisFormatter: function (value) {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[26]++;
            cov_2d3gbsqjv2().s[112]++;
            return "$".concat((value / 1000).toFixed(0), "k");
          }
        };
      case 'growth':
        /* istanbul ignore next */
        cov_2d3gbsqjv2().b[35][1]++;
        cov_2d3gbsqjv2().s[113]++;
        return {
          title: 'Growth Rate',
          bars: [{
            key: 'growth',
            name: 'Growth %',
            color: colors.growth
          }],
          yAxisFormatter: function (value) {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[27]++;
            cov_2d3gbsqjv2().s[114]++;
            return "".concat(value, "%");
          }
        };
      case 'difficulty':
        /* istanbul ignore next */
        cov_2d3gbsqjv2().b[35][2]++;
        cov_2d3gbsqjv2().s[115]++;
        return {
          title: 'Learning Difficulty',
          bars: [{
            key: 'difficulty',
            name: 'Difficulty',
            color: '#ef4444'
          }],
          yAxisFormatter: function (value) {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[28]++;
            cov_2d3gbsqjv2().s[116]++;
            return value.toString();
          }
        };
      default:
        /* istanbul ignore next */
        cov_2d3gbsqjv2().b[35][3]++;
        cov_2d3gbsqjv2().s[117]++;
        return {
          title: 'Demand vs Supply',
          bars: [{
            key: 'demand',
            name: 'Demand',
            color: colors.demand
          }, {
            key: 'supply',
            name: 'Supply',
            color: colors.supply
          }],
          yAxisFormatter: function (value) {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[29]++;
            cov_2d3gbsqjv2().s[118]++;
            return value.toString();
          }
        };
    }
  };
  var chartConfig =
  /* istanbul ignore next */
  (cov_2d3gbsqjv2().s[119]++, getChartConfig());
  // Get top skills for recommendations
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[120]++;
  var getTopSkills = function (sortKey, limit) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[30]++;
    cov_2d3gbsqjv2().s[121]++;
    if (limit === void 0) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().b[36][0]++;
      cov_2d3gbsqjv2().s[122]++;
      limit = 3;
    } else
    /* istanbul ignore next */
    {
      cov_2d3gbsqjv2().b[36][1]++;
    }
    cov_2d3gbsqjv2().s[123]++;
    return __spreadArray([], processedData, true).sort(function (a, b) {
      /* istanbul ignore next */
      cov_2d3gbsqjv2().f[31]++;
      cov_2d3gbsqjv2().s[124]++;
      return b[sortKey] - a[sortKey];
    }).slice(0, limit);
  };
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[125]++;
  var formatSalary = function (salary) {
    /* istanbul ignore next */
    cov_2d3gbsqjv2().f[32]++;
    cov_2d3gbsqjv2().s[126]++;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(salary);
  };
  /* istanbul ignore next */
  cov_2d3gbsqjv2().s[127]++;
  return (0, jsx_runtime_1.jsxs)(card_1.Card, {
    children: [(0, jsx_runtime_1.jsxs)(card_1.CardHeader, {
      children: [(0, jsx_runtime_1.jsxs)(card_1.CardTitle, {
        className: "flex items-center gap-2",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.BarChart3, {
          className: "h-5 w-5"
        }), title]
      }),
      /* istanbul ignore next */
      (cov_2d3gbsqjv2().b[37][0]++, description) &&
      /* istanbul ignore next */
      (cov_2d3gbsqjv2().b[37][1]++, (0, jsx_runtime_1.jsx)(card_1.CardDescription, {
        children: description
      })), (0, jsx_runtime_1.jsxs)("div", {
        className: "flex flex-wrap gap-2 mt-4",
        children: [(0, jsx_runtime_1.jsxs)(button_1.Button, {
          variant: selectedMetric === 'demand-supply' ?
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[38][0]++, 'default') :
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[38][1]++, 'outline'),
          size: "sm",
          onClick: function () {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[33]++;
            cov_2d3gbsqjv2().s[128]++;
            return setSelectedMetric('demand-supply');
          },
          children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, {
            className: "h-4 w-4 mr-1"
          }), "Demand vs Supply"]
        }), (0, jsx_runtime_1.jsxs)(button_1.Button, {
          variant: selectedMetric === 'salary' ?
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[39][0]++, 'default') :
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[39][1]++, 'outline'),
          size: "sm",
          onClick: function () {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[34]++;
            cov_2d3gbsqjv2().s[129]++;
            return setSelectedMetric('salary');
          },
          children: [(0, jsx_runtime_1.jsx)(lucide_react_1.DollarSign, {
            className: "h-4 w-4 mr-1"
          }), "Salary"]
        }), (0, jsx_runtime_1.jsxs)(button_1.Button, {
          variant: selectedMetric === 'growth' ?
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[40][0]++, 'default') :
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[40][1]++, 'outline'),
          size: "sm",
          onClick: function () {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[35]++;
            cov_2d3gbsqjv2().s[130]++;
            return setSelectedMetric('growth');
          },
          children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, {
            className: "h-4 w-4 mr-1"
          }), "Growth"]
        }), (0, jsx_runtime_1.jsxs)(button_1.Button, {
          variant: selectedMetric === 'difficulty' ?
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[41][0]++, 'default') :
          /* istanbul ignore next */
          (cov_2d3gbsqjv2().b[41][1]++, 'outline'),
          size: "sm",
          onClick: function () {
            /* istanbul ignore next */
            cov_2d3gbsqjv2().f[36]++;
            cov_2d3gbsqjv2().s[131]++;
            return setSelectedMetric('difficulty');
          },
          children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Clock, {
            className: "h-4 w-4 mr-1"
          }), "Difficulty"]
        })]
      })]
    }), (0, jsx_runtime_1.jsxs)(card_1.CardContent, {
      className: "space-y-6",
      children: [(0, jsx_runtime_1.jsxs)("div", {
        className: "grid grid-cols-1 md:grid-cols-3 gap-4",
        children: [
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[42][0]++, selectedMetric === 'demand-supply') &&
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[42][1]++, (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {
          children: [(0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-blue-700 dark:text-blue-300",
              children: "Avg Demand"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "text-2xl font-bold text-blue-900 dark:text-blue-100",
              children: avgDemand.toFixed(1)
            })]
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-green-700 dark:text-green-300",
              children: "Avg Supply"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "text-2xl font-bold text-green-900 dark:text-green-100",
              children: avgSupply.toFixed(1)
            })]
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-amber-700 dark:text-amber-300",
              children: "Market Gap"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "text-2xl font-bold text-amber-900 dark:text-amber-100",
              children: marketGap.toFixed(1)
            })]
          })]
        })),
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[43][0]++, selectedMetric === 'salary') &&
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[43][1]++, (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {
          children: [(0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-blue-700 dark:text-blue-300",
              children: "Avg Salary"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "text-2xl font-bold text-blue-900 dark:text-blue-100",
              children: formatSalary(avgSalary)
            })]
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-green-700 dark:text-green-300",
              children: "Highest"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "text-2xl font-bold text-green-900 dark:text-green-100",
              children: formatSalary(highestSalary)
            })]
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-red-700 dark:text-red-300",
              children: "Lowest"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "text-2xl font-bold text-red-900 dark:text-red-100",
              children: formatSalary(lowestSalary)
            })]
          })]
        })),
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[44][0]++, selectedMetric === 'growth') &&
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[44][1]++, (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {
          children: [(0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-blue-700 dark:text-blue-300",
              children: "Avg Growth"
            }), (0, jsx_runtime_1.jsxs)("div", {
              className: "text-2xl font-bold text-blue-900 dark:text-blue-100",
              children: [avgGrowth.toFixed(1), "%"]
            })]
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-green-700 dark:text-green-300",
              children: "Highest"
            }), (0, jsx_runtime_1.jsxs)("div", {
              className: "text-2xl font-bold text-green-900 dark:text-green-100",
              children: [highestGrowth.toFixed(1), "%"]
            })]
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg",
            children: [(0, jsx_runtime_1.jsx)("div", {
              className: "text-sm font-medium text-red-700 dark:text-red-300",
              children: "Lowest"
            }), (0, jsx_runtime_1.jsxs)("div", {
              className: "text-2xl font-bold text-red-900 dark:text-red-100",
              children: [lowestGrowth.toFixed(1), "%"]
            })]
          })]
        }))]
      }), (0, jsx_runtime_1.jsxs)("div", {
        children: [(0, jsx_runtime_1.jsx)("h3", {
          className: "text-lg font-semibold mb-4",
          children: chartConfig.title
        }), (0, jsx_runtime_1.jsx)("div", {
          style: {
            width: '100%',
            height: height
          },
          children: (0, jsx_runtime_1.jsx)(recharts_1.ResponsiveContainer, {
            children: (0, jsx_runtime_1.jsxs)(recharts_1.BarChart, {
              data: processedData,
              margin: {
                top: 20,
                right: 30,
                left: 20,
                bottom: 5
              },
              children: [(0, jsx_runtime_1.jsx)(recharts_1.CartesianGrid, {
                strokeDasharray: "3 3",
                className: "stroke-gray-200 dark:stroke-gray-700"
              }), (0, jsx_runtime_1.jsx)(recharts_1.XAxis, {
                dataKey: "skill",
                tick: {
                  fontSize: 12
                },
                className: "fill-gray-600 dark:fill-gray-400"
              }), (0, jsx_runtime_1.jsx)(recharts_1.YAxis, {
                tickFormatter: chartConfig.yAxisFormatter,
                tick: {
                  fontSize: 12
                },
                className: "fill-gray-600 dark:fill-gray-400"
              }), (0, jsx_runtime_1.jsx)(recharts_1.Tooltip, {
                formatter: function (value, name) {
                  /* istanbul ignore next */
                  cov_2d3gbsqjv2().f[37]++;
                  cov_2d3gbsqjv2().s[132]++;
                  return [chartConfig.yAxisFormatter(value), name];
                },
                contentStyle: {
                  backgroundColor: 'var(--background)',
                  border: '1px solid var(--border)',
                  borderRadius: '6px',
                  color: 'var(--foreground)'
                }
              }),
              /* istanbul ignore next */
              (cov_2d3gbsqjv2().b[45][0]++, showLegend) &&
              /* istanbul ignore next */
              (cov_2d3gbsqjv2().b[45][1]++, (0, jsx_runtime_1.jsx)(recharts_1.Legend, {})), chartConfig.bars.map(function (bar) {
                /* istanbul ignore next */
                cov_2d3gbsqjv2().f[38]++;
                cov_2d3gbsqjv2().s[133]++;
                return (0, jsx_runtime_1.jsx)(recharts_1.Bar, {
                  dataKey: bar.key,
                  fill: bar.color,
                  name: bar.name
                }, bar.key);
              })]
            })
          })
        })]
      }), (0, jsx_runtime_1.jsxs)("div", {
        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
        children: [
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[46][0]++, selectedMetric === 'demand-supply') &&
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[46][1]++, (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {
          children: [(0, jsx_runtime_1.jsxs)("div", {
            children: [(0, jsx_runtime_1.jsx)("h4", {
              className: "font-semibold mb-3",
              children: "High Demand Skills"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "space-y-2",
              children: getTopSkills('demand').map(function (skill) {
                /* istanbul ignore next */
                cov_2d3gbsqjv2().f[39]++;
                cov_2d3gbsqjv2().s[134]++;
                return (0, jsx_runtime_1.jsxs)("div", {
                  className: "flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded",
                  children: [(0, jsx_runtime_1.jsx)("span", {
                    className: "font-medium",
                    children: skill.skill
                  }), (0, jsx_runtime_1.jsxs)(badge_1.Badge, {
                    variant: "secondary",
                    children: [skill.demand, "% demand"]
                  })]
                }, skill.skill);
              })
            })]
          }), (0, jsx_runtime_1.jsxs)("div", {
            children: [(0, jsx_runtime_1.jsx)("h4", {
              className: "font-semibold mb-3",
              children: "Market Opportunities"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "space-y-2",
              children: processedData.filter(function (skill) {
                /* istanbul ignore next */
                cov_2d3gbsqjv2().f[40]++;
                cov_2d3gbsqjv2().s[135]++;
                return skill.demand > skill.supply;
              }).sort(function (a, b) {
                /* istanbul ignore next */
                cov_2d3gbsqjv2().f[41]++;
                cov_2d3gbsqjv2().s[136]++;
                return b.demand - b.supply - (a.demand - a.supply);
              }).slice(0, 3).map(function (skill) {
                /* istanbul ignore next */
                cov_2d3gbsqjv2().f[42]++;
                cov_2d3gbsqjv2().s[137]++;
                return (0, jsx_runtime_1.jsxs)("div", {
                  className: "flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded",
                  children: [(0, jsx_runtime_1.jsx)("span", {
                    className: "font-medium",
                    children: skill.skill
                  }), (0, jsx_runtime_1.jsxs)(badge_1.Badge, {
                    variant: "secondary",
                    children: ["+", (skill.demand - skill.supply).toFixed(1), " gap"]
                  })]
                }, skill.skill);
              })
            })]
          })]
        })),
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[47][0]++, selectedMetric === 'salary') &&
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[47][1]++, (0, jsx_runtime_1.jsxs)("div", {
          children: [(0, jsx_runtime_1.jsx)("h4", {
            className: "font-semibold mb-3",
            children: "Highest Paying"
          }), (0, jsx_runtime_1.jsx)("div", {
            className: "space-y-2",
            children: getTopSkills('averageSalary').map(function (skill) {
              /* istanbul ignore next */
              cov_2d3gbsqjv2().f[43]++;
              cov_2d3gbsqjv2().s[138]++;
              return (0, jsx_runtime_1.jsxs)("div", {
                className: "flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded",
                children: [(0, jsx_runtime_1.jsx)("span", {
                  className: "font-medium",
                  children: skill.skill
                }), (0, jsx_runtime_1.jsx)(badge_1.Badge, {
                  variant: "secondary",
                  children: formatSalary(skill.averageSalary)
                })]
              }, skill.skill);
            })
          })]
        })),
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[48][0]++, selectedMetric === 'growth') &&
        /* istanbul ignore next */
        (cov_2d3gbsqjv2().b[48][1]++, (0, jsx_runtime_1.jsxs)("div", {
          children: [(0, jsx_runtime_1.jsx)("h4", {
            className: "font-semibold mb-3",
            children: "Fastest Growing"
          }), (0, jsx_runtime_1.jsx)("div", {
            className: "space-y-2",
            children: getTopSkills('growth').map(function (skill) {
              /* istanbul ignore next */
              cov_2d3gbsqjv2().f[44]++;
              cov_2d3gbsqjv2().s[139]++;
              return (0, jsx_runtime_1.jsxs)("div", {
                className: "flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded",
                children: [(0, jsx_runtime_1.jsx)("span", {
                  className: "font-medium",
                  children: skill.skill
                }), (0, jsx_runtime_1.jsxs)(badge_1.Badge, {
                  variant: "secondary",
                  children: [skill.growth.toFixed(1), "% growth"]
                })]
              }, skill.skill);
            })
          })]
        }))]
      })]
    })]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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