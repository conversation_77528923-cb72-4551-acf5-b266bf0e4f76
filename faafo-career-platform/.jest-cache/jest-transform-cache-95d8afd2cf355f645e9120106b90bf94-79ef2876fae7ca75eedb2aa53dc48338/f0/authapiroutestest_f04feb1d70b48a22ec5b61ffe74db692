4ee7892d733b5d607dad8d1ed8df8297
"use strict";
/**
 * Authentication API Logic Tests
 * Tests authentication patterns and security logic without importing route files
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock dependencies
jest.mock('next-auth', function () { return ({
    getServerSession: jest.fn(),
}); });
jest.mock('next-auth/jwt', function () { return ({
    getToken: jest.fn(),
}); });
jest.mock('@/lib/prisma', function () { return ({
    user: {
        findUnique: jest.fn(),
        update: jest.fn(),
    },
}); });
var server_1 = require("next/server");
var next_auth_1 = require("next-auth");
var jwt_1 = require("next-auth/jwt");
var mockGetServerSession = next_auth_1.getServerSession;
var mockGetToken = jwt_1.getToken;
var mockPrisma = require('@/lib/prisma');
describe('Authentication API Logic', function () {
    beforeEach(function () {
        jest.clearAllMocks();
    });
    describe('Session Validation Logic', function () {
        it('should validate authenticated user sessions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockUser, token, user;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockUser = {
                            id: 'user-123',
                            email: '<EMAIL>',
                            name: 'Test User',
                            role: 'user',
                        };
                        mockGetToken.mockResolvedValue({
                            sub: 'user-123',
                            email: '<EMAIL>',
                        });
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        expect(token === null || token === void 0 ? void 0 : token.sub).toBe('user-123');
                        return [4 /*yield*/, mockPrisma.user.findUnique({
                                where: { id: token === null || token === void 0 ? void 0 : token.sub },
                            })];
                    case 2:
                        user = _a.sent();
                        expect(user).toBeDefined();
                        expect(user.email).toBe('<EMAIL>');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should reject unauthenticated requests', function () { return __awaiter(void 0, void 0, void 0, function () {
            var token;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        expect(token).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle missing user data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var token, user;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetToken.mockResolvedValue({
                            sub: 'non-existent-user',
                            email: '<EMAIL>',
                        });
                        mockPrisma.user.findUnique.mockResolvedValue(null);
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        return [4 /*yield*/, mockPrisma.user.findUnique({
                                where: { id: token === null || token === void 0 ? void 0 : token.sub },
                            })];
                    case 2:
                        user = _a.sent();
                        expect(token === null || token === void 0 ? void 0 : token.sub).toBe('non-existent-user');
                        expect(user).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should identify admin users correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var adminUser, token, user;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        adminUser = {
                            id: 'admin-123',
                            email: '<EMAIL>',
                            role: 'admin',
                        };
                        mockGetToken.mockResolvedValue({
                            sub: 'admin-123',
                            email: '<EMAIL>',
                            role: 'admin',
                        });
                        mockPrisma.user.findUnique.mockResolvedValue(adminUser);
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        return [4 /*yield*/, mockPrisma.user.findUnique({
                                where: { id: token === null || token === void 0 ? void 0 : token.sub },
                            })];
                    case 2:
                        user = _a.sent();
                        expect(token === null || token === void 0 ? void 0 : token.role).toBe('admin');
                        expect(user.role).toBe('admin');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Verification Status Logic', function () {
        it('should return verification status for verified users', function () { return __awaiter(void 0, void 0, void 0, function () {
            var verifiedUser, session, user;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        verifiedUser = {
                            id: 'user-123',
                            email: '<EMAIL>',
                            emailVerified: new Date(),
                        };
                        mockGetServerSession.mockResolvedValue({
                            user: { email: '<EMAIL>' },
                            expires: new Date(Date.now() + 3600000).toISOString(),
                        });
                        mockPrisma.user.findUnique.mockResolvedValue(verifiedUser);
                        return [4 /*yield*/, mockGetServerSession()];
                    case 1:
                        session = _b.sent();
                        return [4 /*yield*/, mockPrisma.user.findUnique({
                                where: { email: (_a = session === null || session === void 0 ? void 0 : session.user) === null || _a === void 0 ? void 0 : _a.email },
                            })];
                    case 2:
                        user = _b.sent();
                        expect(user.emailVerified).toBeInstanceOf(Date);
                        expect(user.emailVerified).not.toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return verification status for unverified users', function () { return __awaiter(void 0, void 0, void 0, function () {
            var unverifiedUser, session, user;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        unverifiedUser = {
                            id: 'user-456',
                            email: '<EMAIL>',
                            emailVerified: null,
                        };
                        mockGetServerSession.mockResolvedValue({
                            user: { email: '<EMAIL>' },
                            expires: new Date(Date.now() + 3600000).toISOString(),
                        });
                        mockPrisma.user.findUnique.mockResolvedValue(unverifiedUser);
                        return [4 /*yield*/, mockGetServerSession()];
                    case 1:
                        session = _b.sent();
                        return [4 /*yield*/, mockPrisma.user.findUnique({
                                where: { email: (_a = session === null || session === void 0 ? void 0 : session.user) === null || _a === void 0 ? void 0 : _a.email },
                            })];
                    case 2:
                        user = _b.sent();
                        expect(user.emailVerified).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle unauthenticated verification requests', function () { return __awaiter(void 0, void 0, void 0, function () {
            var session;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetServerSession.mockResolvedValue(null);
                        return [4 /*yield*/, mockGetServerSession()];
                    case 1:
                        session = _a.sent();
                        expect(session).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Authentication Security Patterns', function () {
        it('should validate request methods', function () {
            var getRequest = new server_1.NextRequest('http://localhost:3000/api/auth/validate-session', {
                method: 'GET',
            });
            var postRequest = new server_1.NextRequest('http://localhost:3000/api/auth/validate-session', {
                method: 'POST',
            });
            expect(getRequest.method).toBe('GET');
            expect(postRequest.method).toBe('POST');
        });
        it('should handle CORS headers', function () {
            var request = new server_1.NextRequest('http://localhost:3000/api/auth/validate-session', {
                headers: {
                    'Origin': 'http://localhost:3000',
                },
            });
            expect(request.headers.get('Origin')).toBe('http://localhost:3000');
        });
        it('should validate content types', function () {
            var jsonRequest = new server_1.NextRequest('http://localhost:3000/api/auth/validate-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ test: 'data' }),
            });
            expect(jsonRequest.headers.get('Content-Type')).toBe('application/json');
        });
        it('should handle rate limiting patterns', function () {
            var requests = Array.from({ length: 10 }, function (_, i) {
                return new server_1.NextRequest("http://localhost:3000/api/auth/validate-session?attempt=".concat(i));
            });
            expect(requests).toHaveLength(10);
            expect(requests[0].url).toContain('attempt=0');
            expect(requests[9].url).toContain('attempt=9');
        });
    });
    describe('Error Handling Patterns', function () {
        it('should handle database connection errors', function () { return __awaiter(void 0, void 0, void 0, function () {
            var token, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetToken.mockResolvedValue({
                            sub: 'user-123',
                            email: '<EMAIL>',
                        });
                        mockPrisma.user.findUnique.mockRejectedValue(new Error('Database connection failed'));
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        expect(token === null || token === void 0 ? void 0 : token.sub).toBe('user-123');
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, mockPrisma.user.findUnique({ where: { id: token === null || token === void 0 ? void 0 : token.sub } })];
                    case 3:
                        _a.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _a.sent();
                        expect(error_1).toBeInstanceOf(Error);
                        expect(error_1.message).toBe('Database connection failed');
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        }); });
        it('should handle malformed tokens', function () { return __awaiter(void 0, void 0, void 0, function () {
            var token;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetToken.mockResolvedValue({
                            // Missing required fields
                            email: '<EMAIL>',
                            // sub is missing
                        });
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        expect(token === null || token === void 0 ? void 0 : token.sub).toBeUndefined();
                        expect(token === null || token === void 0 ? void 0 : token.email).toBe('<EMAIL>');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle expired sessions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expiredSession, session, isExpired;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expiredSession = {
                            user: { email: '<EMAIL>' },
                            expires: new Date(Date.now() - 3600000).toISOString(), // Expired 1 hour ago
                        };
                        mockGetServerSession.mockResolvedValue(expiredSession);
                        return [4 /*yield*/, mockGetServerSession()];
                    case 1:
                        session = _a.sent();
                        isExpired = new Date((session === null || session === void 0 ? void 0 : session.expires) || 0).getTime() < Date.now();
                        expect(isExpired).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Data Validation Patterns', function () {
        it('should validate email formats', function () {
            var validEmails = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
            var invalidEmails = [
                'invalid-email',
                '@domain.com',
                'user@',
                'user@domain', // no TLD
                'user@.com', // starts with dot
            ];
            // Simple but effective email regex for validation
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            validEmails.forEach(function (email) {
                expect(emailRegex.test(email)).toBe(true);
            });
            invalidEmails.forEach(function (email) {
                expect(emailRegex.test(email)).toBe(false);
            });
        });
        it('should sanitize user input', function () {
            var dangerousInputs = [
                '<script>alert("xss")</script>',
                'javascript:alert("xss")',
                '<img src="x" onerror="alert(1)">',
            ];
            var safeInputs = [
                'Normal text input',
                'User Name 123',
                '<EMAIL>',
            ];
            dangerousInputs.forEach(function (input) {
                var isDangerous = input.includes('<script>') ||
                    input.includes('javascript:') ||
                    input.includes('onerror=');
                expect(isDangerous).toBe(true);
            });
            safeInputs.forEach(function (input) {
                var isDangerous = input.includes('<script>') ||
                    input.includes('javascript:') ||
                    input.includes('onerror=');
                expect(isDangerous).toBe(false);
            });
        });
        it('should validate required fields', function () {
            var completeData = {
                email: '<EMAIL>',
                password: 'password123',
                name: 'Test User',
            };
            var incompleteData = {
                email: '<EMAIL>',
                // password missing
                name: 'Test User',
            };
            var requiredFields = ['email', 'password', 'name'];
            var isComplete = requiredFields.every(function (field) {
                return completeData[field];
            });
            var isIncomplete = requiredFields.every(function (field) {
                return incompleteData[field];
            });
            expect(isComplete).toBe(true);
            expect(isIncomplete).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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