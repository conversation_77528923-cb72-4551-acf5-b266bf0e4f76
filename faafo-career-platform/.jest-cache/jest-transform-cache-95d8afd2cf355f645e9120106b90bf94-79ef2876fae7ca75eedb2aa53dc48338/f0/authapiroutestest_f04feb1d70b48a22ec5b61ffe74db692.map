{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/api/auth-api-routes.test.ts", "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMH,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAM,OAAA,CAAC;IAC5B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;CAC5B,CAAC,EAF2B,CAE3B,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,cAAM,OAAA,CAAC;IAChC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;CACpB,CAAC,EAF+B,CAE/B,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,CAAC;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB;CACF,CAAC,EAL8B,CAK9B,CAAC,CAAC;AAlBJ,sCAA0C;AAC1C,uCAA6C;AAC7C,qCAAyC;AAkBzC,IAAM,oBAAoB,GAAG,4BAAgE,CAAC;AAC9F,IAAM,YAAY,GAAG,cAAgD,CAAC;AACtE,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE3C,QAAQ,CAAC,0BAA0B,EAAE;IACnC,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,6CAA6C,EAAE;;;;;wBAC1C,QAAQ,GAAG;4BACf,EAAE,EAAE,UAAU;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,WAAW;4BACjB,IAAI,EAAE,MAAM;yBACb,CAAC;wBAEF,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,UAAU;4BACf,KAAK,EAAE,kBAAkB;yBAC1B,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAGzC,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBAClC,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAEvB,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gCAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,EAAE;6BAC1B,CAAC,EAAA;;wBAFI,IAAI,GAAG,SAEX;wBAEF,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aAC7C,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;;wBAC3C,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEvB,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBAClC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aAC1B,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;;;;;wBACpC,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,mBAAmB;4BACxB,KAAK,EAAE,qBAAqB;yBAC7B,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAErC,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBACrB,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gCAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,EAAE;6BAC1B,CAAC,EAAA;;wBAFI,IAAI,GAAG,SAEX;wBAEF,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aACzB,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,SAAS,GAAG;4BAChB,EAAE,EAAE,WAAW;4BACf,KAAK,EAAE,mBAAmB;4BAC1B,IAAI,EAAE,OAAO;yBACd,CAAC;wBAEF,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,WAAW;4BAChB,KAAK,EAAE,mBAAmB;4BAC1B,IAAI,EAAE,OAAO;yBACd,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;wBAE1C,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBACrB,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gCAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,EAAE;6BAC1B,CAAC,EAAA;;wBAFI,IAAI,GAAG,SAEX;wBAEF,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;;aACjC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE;QACpC,EAAE,CAAC,sDAAsD,EAAE;;;;;;wBACnD,YAAY,GAAG;4BACnB,EAAE,EAAE,UAAU;4BACd,KAAK,EAAE,sBAAsB;4BAC7B,aAAa,EAAE,IAAI,IAAI,EAAE;yBAC1B,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE;4BACvC,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,WAAW,EAAE;yBACtD,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;wBAE3C,qBAAM,oBAAoB,EAAE,EAAA;;wBAAtC,OAAO,GAAG,SAA4B;wBAC/B,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gCAC5C,KAAK,EAAE,EAAE,KAAK,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,EAAE;6BACvC,CAAC,EAAA;;wBAFI,IAAI,GAAG,SAEX;wBAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBAChD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;;;;aAC3C,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE;;;;;;wBACrD,cAAc,GAAG;4BACrB,EAAE,EAAE,UAAU;4BACd,KAAK,EAAE,wBAAwB;4BAC/B,aAAa,EAAE,IAAI;yBACpB,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,KAAK,EAAE,wBAAwB,EAAE;4BACzC,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,WAAW,EAAE;yBACtD,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;wBAE7C,qBAAM,oBAAoB,EAAE,EAAA;;wBAAtC,OAAO,GAAG,SAA4B;wBAC/B,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gCAC5C,KAAK,EAAE,EAAE,KAAK,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,EAAE;6BACvC,CAAC,EAAA;;wBAFI,IAAI,GAAG,SAEX;wBAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;;;;;wBACxD,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAE7B,qBAAM,oBAAoB,EAAE,EAAA;;wBAAtC,OAAO,GAAG,SAA4B;wBAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aAC5B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE;QAC3C,EAAE,CAAC,iCAAiC,EAAE;YACpC,IAAM,UAAU,GAAG,IAAI,oBAAW,CAAC,iDAAiD,EAAE;gBACpF,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,IAAM,WAAW,GAAG,IAAI,oBAAW,CAAC,iDAAiD,EAAE;gBACrF,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE;YAC/B,IAAM,OAAO,GAAG,IAAI,oBAAW,CAAC,iDAAiD,EAAE;gBACjF,OAAO,EAAE;oBACP,QAAQ,EAAE,uBAAuB;iBAClC;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;YAClC,IAAM,WAAW,GAAG,IAAI,oBAAW,CAAC,iDAAiD,EAAE;gBACrF,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;aACvC,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC;gBAC/C,OAAA,IAAI,oBAAW,CAAC,kEAA2D,CAAC,CAAE,CAAC;YAA/E,CAA+E,CAChF,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,0CAA0C,EAAE;;;;;wBAC7C,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,UAAU;4BACf,KAAK,EAAE,kBAAkB;yBAC1B,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;wBAExE,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBAClC,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;;;;wBAGlC,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,EAAE,EAAE,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;;;;wBAEhE,MAAM,CAAC,OAAK,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;wBACpC,MAAM,CAAE,OAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;;;;;aAEvE,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE;;;;;wBACnC,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,0BAA0B;4BAC1B,KAAK,EAAE,kBAAkB;4BACzB,iBAAiB;yBAClB,CAAC,CAAC;wBAEW,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBAClC,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC;wBACnC,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aAC/C,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE;;;;;wBAC7B,cAAc,GAAG;4BACrB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BACnC,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,qBAAqB;yBAC7E,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;wBAEvC,qBAAM,oBAAoB,EAAE,EAAA;;wBAAtC,OAAO,GAAG,SAA4B;wBACtC,SAAS,GAAG,IAAI,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,KAAI,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAEzE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAC9B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,+BAA+B,EAAE;YAClC,IAAM,WAAW,GAAG;gBAClB,kBAAkB;gBAClB,wBAAwB;gBACxB,wBAAwB;aACzB,CAAC;YAEF,IAAM,aAAa,GAAG;gBACpB,eAAe;gBACf,aAAa;gBACb,OAAO;gBACP,aAAa,EAAE,SAAS;gBACxB,WAAW,EAAI,kBAAkB;aAClC,CAAC;YAEF,kDAAkD;YAClD,IAAM,UAAU,GAAG,4BAA4B,CAAC;YAEhD,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;gBACvB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YAEH,aAAa,CAAC,OAAO,CAAC,UAAA,KAAK;gBACzB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE;YAC/B,IAAM,eAAe,GAAG;gBACtB,+BAA+B;gBAC/B,yBAAyB;gBACzB,kCAAkC;aACnC,CAAC;YAEF,IAAM,UAAU,GAAG;gBACjB,mBAAmB;gBACnB,eAAe;gBACf,kBAAkB;aACnB,CAAC;YAEF,eAAe,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC3B,IAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;oBAC3B,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAC7B,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC9C,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,UAAU,CAAC,OAAO,CAAC,UAAA,KAAK;gBACtB,IAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;oBAC3B,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAC7B,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC9C,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;YACpC,IAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,aAAa;gBACvB,IAAI,EAAE,WAAW;aAClB,CAAC;YAEF,IAAM,cAAc,GAAG;gBACrB,KAAK,EAAE,kBAAkB;gBACzB,mBAAmB;gBACnB,IAAI,EAAE,WAAW;aAClB,CAAC;YAEF,IAAM,cAAc,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAErD,IAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,UAAA,KAAK;gBAC3C,OAAA,YAAY,CAAC,KAAkC,CAAC;YAAhD,CAAgD,CACjD,CAAC;YAEF,IAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,UAAA,KAAK;gBAC7C,OAAA,cAAc,CAAC,KAAoC,CAAC;YAApD,CAAoD,CACrD,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/api/auth-api-routes.test.ts"], "sourcesContent": ["/**\n * Authentication API Logic Tests\n * Tests authentication patterns and security logic without importing route files\n */\n\nimport { NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { getToken } from 'next-auth/jwt';\n\n// Mock dependencies\njest.mock('next-auth', () => ({\n  getServerSession: jest.fn(),\n}));\n\njest.mock('next-auth/jwt', () => ({\n  getToken: jest.fn(),\n}));\n\njest.mock('@/lib/prisma', () => ({\n  user: {\n    findUnique: jest.fn(),\n    update: jest.fn(),\n  },\n}));\n\nconst mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;\nconst mockGetToken = getToken as jest.MockedFunction<typeof getToken>;\nconst mockPrisma = require('@/lib/prisma');\n\ndescribe('Authentication API Logic', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('Session Validation Logic', () => {\n    it('should validate authenticated user sessions', async () => {\n      const mockUser = {\n        id: 'user-123',\n        email: '<EMAIL>',\n        name: 'Test User',\n        role: 'user',\n      };\n\n      mockGetToken.mockResolvedValue({\n        sub: 'user-123',\n        email: '<EMAIL>',\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n\n      // Test session validation logic\n      const token = await mockGetToken();\n      expect(token?.sub).toBe('user-123');\n      \n      const user = await mockPrisma.user.findUnique({\n        where: { id: token?.sub },\n      });\n      \n      expect(user).toBeDefined();\n      expect(user.email).toBe('<EMAIL>');\n    });\n\n    it('should reject unauthenticated requests', async () => {\n      mockGetToken.mockResolvedValue(null);\n\n      const token = await mockGetToken();\n      expect(token).toBeNull();\n    });\n\n    it('should handle missing user data', async () => {\n      mockGetToken.mockResolvedValue({\n        sub: 'non-existent-user',\n        email: '<EMAIL>',\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue(null);\n\n      const token = await mockGetToken();\n      const user = await mockPrisma.user.findUnique({\n        where: { id: token?.sub },\n      });\n\n      expect(token?.sub).toBe('non-existent-user');\n      expect(user).toBeNull();\n    });\n\n    it('should identify admin users correctly', async () => {\n      const adminUser = {\n        id: 'admin-123',\n        email: '<EMAIL>',\n        role: 'admin',\n      };\n\n      mockGetToken.mockResolvedValue({\n        sub: 'admin-123',\n        email: '<EMAIL>',\n        role: 'admin',\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue(adminUser);\n\n      const token = await mockGetToken();\n      const user = await mockPrisma.user.findUnique({\n        where: { id: token?.sub },\n      });\n\n      expect(token?.role).toBe('admin');\n      expect(user.role).toBe('admin');\n    });\n  });\n\n  describe('Verification Status Logic', () => {\n    it('should return verification status for verified users', async () => {\n      const verifiedUser = {\n        id: 'user-123',\n        email: '<EMAIL>',\n        emailVerified: new Date(),\n      };\n\n      mockGetServerSession.mockResolvedValue({\n        user: { email: '<EMAIL>' },\n        expires: new Date(Date.now() + 3600000).toISOString(),\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue(verifiedUser);\n\n      const session = await mockGetServerSession();\n      const user = await mockPrisma.user.findUnique({\n        where: { email: session?.user?.email },\n      });\n\n      expect(user.emailVerified).toBeInstanceOf(Date);\n      expect(user.emailVerified).not.toBeNull();\n    });\n\n    it('should return verification status for unverified users', async () => {\n      const unverifiedUser = {\n        id: 'user-456',\n        email: '<EMAIL>',\n        emailVerified: null,\n      };\n\n      mockGetServerSession.mockResolvedValue({\n        user: { email: '<EMAIL>' },\n        expires: new Date(Date.now() + 3600000).toISOString(),\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue(unverifiedUser);\n\n      const session = await mockGetServerSession();\n      const user = await mockPrisma.user.findUnique({\n        where: { email: session?.user?.email },\n      });\n\n      expect(user.emailVerified).toBeNull();\n    });\n\n    it('should handle unauthenticated verification requests', async () => {\n      mockGetServerSession.mockResolvedValue(null);\n\n      const session = await mockGetServerSession();\n      expect(session).toBeNull();\n    });\n  });\n\n  describe('Authentication Security Patterns', () => {\n    it('should validate request methods', () => {\n      const getRequest = new NextRequest('http://localhost:3000/api/auth/validate-session', {\n        method: 'GET',\n      });\n\n      const postRequest = new NextRequest('http://localhost:3000/api/auth/validate-session', {\n        method: 'POST',\n      });\n\n      expect(getRequest.method).toBe('GET');\n      expect(postRequest.method).toBe('POST');\n    });\n\n    it('should handle CORS headers', () => {\n      const request = new NextRequest('http://localhost:3000/api/auth/validate-session', {\n        headers: {\n          'Origin': 'http://localhost:3000',\n        },\n      });\n\n      expect(request.headers.get('Origin')).toBe('http://localhost:3000');\n    });\n\n    it('should validate content types', () => {\n      const jsonRequest = new NextRequest('http://localhost:3000/api/auth/validate-session', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ test: 'data' }),\n      });\n\n      expect(jsonRequest.headers.get('Content-Type')).toBe('application/json');\n    });\n\n    it('should handle rate limiting patterns', () => {\n      const requests = Array.from({ length: 10 }, (_, i) => \n        new NextRequest(`http://localhost:3000/api/auth/validate-session?attempt=${i}`)\n      );\n\n      expect(requests).toHaveLength(10);\n      expect(requests[0].url).toContain('attempt=0');\n      expect(requests[9].url).toContain('attempt=9');\n    });\n  });\n\n  describe('Error Handling Patterns', () => {\n    it('should handle database connection errors', async () => {\n      mockGetToken.mockResolvedValue({\n        sub: 'user-123',\n        email: '<EMAIL>',\n      });\n\n      mockPrisma.user.findUnique.mockRejectedValue(new Error('Database connection failed'));\n\n      const token = await mockGetToken();\n      expect(token?.sub).toBe('user-123');\n\n      try {\n        await mockPrisma.user.findUnique({ where: { id: token?.sub } });\n      } catch (error) {\n        expect(error).toBeInstanceOf(Error);\n        expect((error as Error).message).toBe('Database connection failed');\n      }\n    });\n\n    it('should handle malformed tokens', async () => {\n      mockGetToken.mockResolvedValue({\n        // Missing required fields\n        email: '<EMAIL>',\n        // sub is missing\n      });\n\n      const token = await mockGetToken();\n      expect(token?.sub).toBeUndefined();\n      expect(token?.email).toBe('<EMAIL>');\n    });\n\n    it('should handle expired sessions', async () => {\n      const expiredSession = {\n        user: { email: '<EMAIL>' },\n        expires: new Date(Date.now() - 3600000).toISOString(), // Expired 1 hour ago\n      };\n\n      mockGetServerSession.mockResolvedValue(expiredSession);\n\n      const session = await mockGetServerSession();\n      const isExpired = new Date(session?.expires || 0).getTime() < Date.now();\n      \n      expect(isExpired).toBe(true);\n    });\n  });\n\n  describe('Data Validation Patterns', () => {\n    it('should validate email formats', () => {\n      const validEmails = [\n        '<EMAIL>',\n        '<EMAIL>',\n        '<EMAIL>',\n      ];\n\n      const invalidEmails = [\n        'invalid-email',\n        '@domain.com',\n        'user@',\n        'user@domain', // no TLD\n        'user@.com',   // starts with dot\n      ];\n\n      // Simple but effective email regex for validation\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\n      validEmails.forEach(email => {\n        expect(emailRegex.test(email)).toBe(true);\n      });\n\n      invalidEmails.forEach(email => {\n        expect(emailRegex.test(email)).toBe(false);\n      });\n    });\n\n    it('should sanitize user input', () => {\n      const dangerousInputs = [\n        '<script>alert(\"xss\")</script>',\n        'javascript:alert(\"xss\")',\n        '<img src=\"x\" onerror=\"alert(1)\">',\n      ];\n\n      const safeInputs = [\n        'Normal text input',\n        'User Name 123',\n        '<EMAIL>',\n      ];\n\n      dangerousInputs.forEach(input => {\n        const isDangerous = input.includes('<script>') ||\n                           input.includes('javascript:') ||\n                           input.includes('onerror=');\n        expect(isDangerous).toBe(true);\n      });\n\n      safeInputs.forEach(input => {\n        const isDangerous = input.includes('<script>') ||\n                           input.includes('javascript:') ||\n                           input.includes('onerror=');\n        expect(isDangerous).toBe(false);\n      });\n    });\n\n    it('should validate required fields', () => {\n      const completeData = {\n        email: '<EMAIL>',\n        password: 'password123',\n        name: 'Test User',\n      };\n\n      const incompleteData = {\n        email: '<EMAIL>',\n        // password missing\n        name: 'Test User',\n      };\n\n      const requiredFields = ['email', 'password', 'name'];\n      \n      const isComplete = requiredFields.every(field => \n        completeData[field as keyof typeof completeData]\n      );\n      \n      const isIncomplete = requiredFields.every(field => \n        incompleteData[field as keyof typeof incompleteData]\n      );\n\n      expect(isComplete).toBe(true);\n      expect(isIncomplete).toBe(false);\n    });\n  });\n});\n"], "version": 3}