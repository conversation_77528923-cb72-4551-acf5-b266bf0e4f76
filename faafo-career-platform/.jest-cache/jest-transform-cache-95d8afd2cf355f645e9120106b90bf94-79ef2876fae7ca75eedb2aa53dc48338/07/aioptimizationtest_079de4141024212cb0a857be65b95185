1c48138dcae1599943c95cd3440aa1b7
"use strict";
/**
 * Phase 2 AI Service Call Optimization Tests
 *
 * Tests for advanced request deduplication, semantic matching,
 * cross-user optimization, and performance improvements.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock dependencies
jest.mock('@/lib/services/geminiService', function () { return ({
    geminiService: {
        analyzeSkillsGap: jest.fn(),
        generateCareerRecommendations: jest.fn(),
        generateInterviewQuestions: jest.fn(),
    },
    AIServiceLogger: {
        info: jest.fn(),
        debug: jest.fn(),
        error: jest.fn(),
    },
}); });
jest.mock('@/lib/services/cacheService', function () { return ({
    cacheService: {
        getJSON: jest.fn(),
        setJSON: jest.fn(),
        generateAIKey: jest.fn(),
    },
}); });
jest.mock('@/lib/services/enhanced-cache-service', function () { return ({
    enhancedCacheService: {
        get: jest.fn(),
        set: jest.fn(),
        getMetrics: jest.fn(function () { return ({
            l1Hits: 10,
            l2Hits: 5,
            sharedCacheHits: 3,
            misses: 2,
            averageResponseTime: 150,
        }); }),
    },
}); });
var optimized_ai_service_1 = require("@/lib/optimized-ai-service");
describe('Advanced Request Deduplication Service', function () {
    var deduplicationService;
    var mockAIFunction;
    beforeEach(function () {
        deduplicationService = new AdvancedRequestDeduplicationService({
            enableSemanticSimilarity: true,
            enableCrossUserDeduplication: true,
            enablePredictiveWarming: false, // Disable for testing
            similarityThreshold: 0.85,
            deduplicationWindow: 5000,
        });
        mockAIFunction = jest.fn().mockResolvedValue({
            success: true,
            data: { analysis: 'test result' },
        });
        jest.clearAllMocks();
    });
    describe('Exact Deduplication', function () {
        it('should deduplicate identical requests within the deduplication window', function () { return __awaiter(void 0, void 0, void 0, function () {
            var requestKey, result1, result2, metrics;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        requestKey = 'skills-analysis:software_engineer:mid_level:javascript,react';
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' })];
                    case 1:
                        result1 = _a.sent();
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' })];
                    case 2:
                        result2 = _a.sent();
                        // Should only call the AI function once
                        expect(mockAIFunction).toHaveBeenCalledTimes(1);
                        expect(result1).toEqual(result2);
                        metrics = deduplicationService.getMetrics();
                        expect(metrics.exactDuplicates).toBe(1);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should not deduplicate requests outside the deduplication window', function () { return __awaiter(void 0, void 0, void 0, function () {
            var requestKey, shortWindowService;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        requestKey = 'skills-analysis:software_engineer:mid_level:javascript,react';
                        // First request
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' })];
                    case 1:
                        // First request
                        _a.sent();
                        shortWindowService = new AdvancedRequestDeduplicationService({
                            deduplicationWindow: 1, // 1ms window
                        });
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 10); })];
                    case 2:
                        _a.sent();
                        // Second request after window expires
                        return [4 /*yield*/, shortWindowService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' })];
                    case 3:
                        // Second request after window expires
                        _a.sent();
                        expect(mockAIFunction).toHaveBeenCalledTimes(2);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Semantic Similarity Deduplication', function () {
        it('should deduplicate semantically similar requests', function () { return __awaiter(void 0, void 0, void 0, function () {
            var requestKey1, requestKey2, result1, result2, metrics;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        requestKey1 = 'skills-analysis:software engineer:mid level:javascript,react';
                        requestKey2 = 'skills-analysis:software_engineer:mid_level:javascript,react';
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey1, mockAIFunction, { userId: 'user1' })];
                    case 1:
                        result1 = _a.sent();
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey2, mockAIFunction, { userId: 'user2' })];
                    case 2:
                        result2 = _a.sent();
                        // Should deduplicate based on semantic similarity
                        expect(mockAIFunction).toHaveBeenCalledTimes(1);
                        expect(result1).toEqual(result2);
                        metrics = deduplicationService.getMetrics();
                        expect(metrics.semanticDuplicates).toBe(1);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should normalize common career path variations', function () { return __awaiter(void 0, void 0, void 0, function () {
            var service, normalized1, normalized2;
            return __generator(this, function (_a) {
                service = new AdvancedRequestDeduplicationService();
                normalized1 = service.normalizeParameters('software engineer:entry level');
                normalized2 = service.normalizeParameters('software_engineer:entry_level');
                expect(normalized1).toBe(normalized2);
                return [2 /*return*/];
            });
        }); });
    });
    describe('Cross-User Deduplication', function () {
        it('should deduplicate safe requests across users', function () { return __awaiter(void 0, void 0, void 0, function () {
            var requestKey, result1, result2, metrics;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        requestKey = 'career-recommendations:javascript,react';
                        mockAIFunction.mockResolvedValue({
                            success: true,
                            data: { recommendations: ['Frontend Developer', 'React Developer'] },
                        });
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, mockAIFunction, {
                                userId: 'user1',
                                enableCrossUserMatch: true
                            })];
                    case 1:
                        result1 = _a.sent();
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, mockAIFunction, {
                                userId: 'user2',
                                enableCrossUserMatch: true
                            })];
                    case 2:
                        result2 = _a.sent();
                        // Should reuse result for cross-user safe requests
                        expect(mockAIFunction).toHaveBeenCalledTimes(1);
                        expect(result1).toEqual(result2);
                        expect(result2.success).toBe(true);
                        metrics = deduplicationService.getMetrics();
                        // When semantic similarity is enabled, identical requests are caught by semantic deduplication
                        expect(metrics.semanticDuplicates).toBe(1);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should use cross-user deduplication when semantic similarity is disabled', function () { return __awaiter(void 0, void 0, void 0, function () {
            var crossUserService, requestKey, result1, result2, metrics;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        crossUserService = new AdvancedRequestDeduplicationService({
                            enableSemanticSimilarity: false,
                            enableCrossUserDeduplication: true,
                            enablePredictiveWarming: false,
                            deduplicationWindow: 5000,
                        });
                        requestKey = 'career-recommendations:javascript,react';
                        mockAIFunction.mockResolvedValue({
                            success: true,
                            data: { recommendations: ['Frontend Developer', 'React Developer'] },
                        });
                        return [4 /*yield*/, crossUserService.deduplicateRequest(requestKey, mockAIFunction, {
                                userId: 'user1',
                                enableCrossUserMatch: true
                            })];
                    case 1:
                        result1 = _a.sent();
                        return [4 /*yield*/, crossUserService.deduplicateRequest(requestKey, mockAIFunction, {
                                userId: 'user2',
                                enableCrossUserMatch: true
                            })];
                    case 2:
                        result2 = _a.sent();
                        // Should reuse result for cross-user safe requests
                        expect(mockAIFunction).toHaveBeenCalledTimes(1);
                        expect(result1).toEqual(result2);
                        expect(result2.success).toBe(true);
                        metrics = crossUserService.getMetrics();
                        expect(metrics.crossUserDuplicates).toBe(1);
                        expect(metrics.semanticDuplicates).toBe(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should not cross-user deduplicate sensitive request types', function () { return __awaiter(void 0, void 0, void 0, function () {
            var sensitiveService, requestKey, metrics;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        sensitiveService = new AdvancedRequestDeduplicationService({
                            enableSemanticSimilarity: false,
                            enableCrossUserDeduplication: true,
                            enablePredictiveWarming: false,
                            deduplicationWindow: 5000,
                        });
                        requestKey = 'skills-analysis:user_specific_data:sensitive';
                        mockAIFunction.mockResolvedValue({
                            success: true,
                            data: { analysis: 'user specific analysis' },
                        });
                        // First user makes request
                        return [4 /*yield*/, sensitiveService.deduplicateRequest(requestKey, mockAIFunction, {
                                userId: 'user1',
                                enableCrossUserMatch: true
                            })];
                    case 1:
                        // First user makes request
                        _a.sent();
                        // Second user makes same request
                        return [4 /*yield*/, sensitiveService.deduplicateRequest(requestKey, mockAIFunction, {
                                userId: 'user2',
                                enableCrossUserMatch: true
                            })];
                    case 2:
                        // Second user makes same request
                        _a.sent();
                        // Should call AI function twice for sensitive data (no cross-user sharing)
                        expect(mockAIFunction).toHaveBeenCalledTimes(2);
                        metrics = sensitiveService.getMetrics();
                        expect(metrics.crossUserDuplicates).toBe(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Performance Metrics', function () {
        it('should track comprehensive metrics', function () { return __awaiter(void 0, void 0, void 0, function () {
            var requestKey, metrics;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        requestKey = 'skills-analysis:test:metrics';
                        // Mock function with a small delay to ensure measurable response time
                        mockAIFunction.mockImplementation(function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0: return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 10); })];
                                    case 1:
                                        _a.sent();
                                        return [2 /*return*/, {
                                                success: true,
                                                data: { analysis: 'test result' },
                                            }];
                                }
                            });
                        }); });
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, mockAIFunction)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, mockAIFunction)];
                    case 2:
                        _a.sent();
                        metrics = deduplicationService.getMetrics();
                        expect(metrics.totalRequests).toBe(2);
                        expect(metrics.exactDuplicates).toBe(1);
                        expect(metrics.averageResponseTime).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should calculate deduplication savings', function () { return __awaiter(void 0, void 0, void 0, function () {
            var requestKey, slowMockFunction, metrics;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        requestKey = 'skills-analysis:performance:test';
                        slowMockFunction = jest.fn().mockImplementation(function () {
                            return new Promise(function (resolve) { return setTimeout(function () { return resolve({ success: true, data: {} }); }, 100); });
                        });
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, slowMockFunction)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, deduplicationService.deduplicateRequest(requestKey, slowMockFunction)];
                    case 2:
                        _a.sent();
                        metrics = deduplicationService.getMetrics();
                        expect(metrics.deduplicationSavings).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
describe('Optimized AI Service', function () {
    var optimizedService;
    beforeEach(function () {
        optimizedService = new optimized_ai_service_1.OptimizedAIService({
            enableAdvancedDeduplication: true,
            enableIntelligentCaching: true,
            enablePerformanceMonitoring: true,
        });
        jest.clearAllMocks();
    });
    describe('Skills Analysis Optimization', function () {
        it('should use advanced deduplication for skills analysis', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockGeminiService, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGeminiService = require('@/lib/services/geminiService').geminiService;
                        mockGeminiService.analyzeSkillsGap.mockResolvedValue({
                            success: true,
                            data: { skillsGap: ['React', 'TypeScript'] },
                        });
                        return [4 /*yield*/, optimizedService.analyzeSkillsGap(['JavaScript'], 'Frontend Developer', 'mid_level', {
                                userId: 'test-user',
                                priority: 'medium',
                                enableDeduplication: true,
                            })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.metadata.source).toBe('deduplication');
                        expect(result.metadata.requestId).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle cache hits properly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockEnhancedCache, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockEnhancedCache = require('@/lib/services/enhanced-cache-service').enhancedCacheService;
                        mockEnhancedCache.get.mockResolvedValue({
                            skillsGap: ['React', 'TypeScript'],
                        });
                        return [4 /*yield*/, optimizedService.analyzeSkillsGap(['JavaScript'], 'Frontend Developer', 'mid_level', {
                                userId: 'test-user',
                                enableCaching: true,
                            })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.metadata.source).toBe('cache');
                        expect(result.metadata.cacheHit).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Career Recommendations Optimization', function () {
        it('should optimize career recommendation requests', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockGeminiService, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGeminiService = require('@/lib/services/geminiService').geminiService;
                        mockGeminiService.generateCareerRecommendations.mockResolvedValue({
                            success: true,
                            data: { recommendations: ['Frontend Developer', 'Full Stack Developer'] },
                        });
                        return [4 /*yield*/, optimizedService.generateCareerRecommendations(['JavaScript', 'React'], { workStyle: 'remote', experience: 'mid_level' }, {
                                userId: 'test-user',
                                priority: 'high',
                                enableCrossUserMatch: true, // Safe for career recommendations
                            })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.metadata.requestId).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Interview Questions Optimization', function () {
        it('should enable cross-user deduplication for interview questions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockGeminiService, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGeminiService = require('@/lib/services/geminiService').geminiService;
                        mockGeminiService.generateInterviewQuestions.mockResolvedValue({
                            success: true,
                            data: { questions: ['Tell me about yourself', 'What are your strengths?'] },
                        });
                        return [4 /*yield*/, optimizedService.generateInterviewQuestions({
                                sessionType: 'BEHAVIORAL',
                                careerPath: 'Frontend Developer',
                                experienceLevel: 'mid_level',
                            }, {
                                userId: 'test-user',
                                enableCrossUserMatch: true, // Interview questions are safe to share
                            })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.metadata.source).toBe('deduplication');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Metrics and Monitoring', function () {
        it('should provide comprehensive optimization metrics', function () {
            var metrics = optimizedService.getOptimizationMetrics();
            expect(metrics).toHaveProperty('deduplication');
            expect(metrics).toHaveProperty('activeRequests');
            expect(metrics).toHaveProperty('totalRequests');
            expect(metrics).toHaveProperty('config');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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