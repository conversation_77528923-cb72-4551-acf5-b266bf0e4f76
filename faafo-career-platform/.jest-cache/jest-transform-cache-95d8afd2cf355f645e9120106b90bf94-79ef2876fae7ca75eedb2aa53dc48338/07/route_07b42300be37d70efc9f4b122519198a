bce6ca2934c41860dd4fbe5840717dda
"use strict";

/**
 * API endpoint for session validation
 * Provides server-side session validation for the authentication state manager
 */
/* istanbul ignore next */
function cov_j8718n3dm() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/validate-session/route.ts";
  var hash = "a614ec7e72ca60a15eb740cd8efab2e32f48f293";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/validate-session/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 22
        },
        end: {
          line: 16,
          column: 3
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 7,
          column: 33
        }
      },
      "2": {
        start: {
          line: 7,
          column: 26
        },
        end: {
          line: 7,
          column: 33
        }
      },
      "3": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 52
        }
      },
      "4": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 11,
          column: 5
        }
      },
      "5": {
        start: {
          line: 10,
          column: 6
        },
        end: {
          line: 10,
          column: 68
        }
      },
      "6": {
        start: {
          line: 10,
          column: 51
        },
        end: {
          line: 10,
          column: 63
        }
      },
      "7": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 39
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 33
        }
      },
      "9": {
        start: {
          line: 14,
          column: 26
        },
        end: {
          line: 14,
          column: 33
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 17
        }
      },
      "11": {
        start: {
          line: 17,
          column: 25
        },
        end: {
          line: 21,
          column: 2
        }
      },
      "12": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 72
        }
      },
      "13": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 21
        }
      },
      "14": {
        start: {
          line: 22,
          column: 19
        },
        end: {
          line: 38,
          column: 4
        }
      },
      "15": {
        start: {
          line: 23,
          column: 18
        },
        end: {
          line: 30,
          column: 5
        }
      },
      "16": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 28,
          column: 10
        }
      },
      "17": {
        start: {
          line: 25,
          column: 21
        },
        end: {
          line: 25,
          column: 23
        }
      },
      "18": {
        start: {
          line: 26,
          column: 12
        },
        end: {
          line: 26,
          column: 95
        }
      },
      "19": {
        start: {
          line: 26,
          column: 29
        },
        end: {
          line: 26,
          column: 95
        }
      },
      "20": {
        start: {
          line: 26,
          column: 77
        },
        end: {
          line: 26,
          column: 95
        }
      },
      "21": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 22
        }
      },
      "22": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 26
        }
      },
      "23": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 37,
          column: 6
        }
      },
      "24": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "25": {
        start: {
          line: 32,
          column: 35
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "26": {
        start: {
          line: 33,
          column: 21
        },
        end: {
          line: 33,
          column: 23
        }
      },
      "27": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 34,
          column: 137
        }
      },
      "28": {
        start: {
          line: 34,
          column: 25
        },
        end: {
          line: 34,
          column: 137
        }
      },
      "29": {
        start: {
          line: 34,
          column: 38
        },
        end: {
          line: 34,
          column: 50
        }
      },
      "30": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 57
        }
      },
      "31": {
        start: {
          line: 34,
          column: 78
        },
        end: {
          line: 34,
          column: 137
        }
      },
      "32": {
        start: {
          line: 34,
          column: 102
        },
        end: {
          line: 34,
          column: 137
        }
      },
      "33": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 40
        }
      },
      "34": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 36,
          column: 22
        }
      },
      "35": {
        start: {
          line: 39,
          column: 16
        },
        end: {
          line: 47,
          column: 1
        }
      },
      "36": {
        start: {
          line: 40,
          column: 28
        },
        end: {
          line: 40,
          column: 110
        }
      },
      "37": {
        start: {
          line: 40,
          column: 91
        },
        end: {
          line: 40,
          column: 106
        }
      },
      "38": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 46,
          column: 7
        }
      },
      "39": {
        start: {
          line: 42,
          column: 36
        },
        end: {
          line: 42,
          column: 97
        }
      },
      "40": {
        start: {
          line: 42,
          column: 42
        },
        end: {
          line: 42,
          column: 70
        }
      },
      "41": {
        start: {
          line: 42,
          column: 85
        },
        end: {
          line: 42,
          column: 95
        }
      },
      "42": {
        start: {
          line: 43,
          column: 35
        },
        end: {
          line: 43,
          column: 100
        }
      },
      "43": {
        start: {
          line: 43,
          column: 41
        },
        end: {
          line: 43,
          column: 73
        }
      },
      "44": {
        start: {
          line: 43,
          column: 88
        },
        end: {
          line: 43,
          column: 98
        }
      },
      "45": {
        start: {
          line: 44,
          column: 32
        },
        end: {
          line: 44,
          column: 116
        }
      },
      "46": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 78
        }
      },
      "47": {
        start: {
          line: 48,
          column: 18
        },
        end: {
          line: 74,
          column: 1
        }
      },
      "48": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 104
        }
      },
      "49": {
        start: {
          line: 49,
          column: 43
        },
        end: {
          line: 49,
          column: 68
        }
      },
      "50": {
        start: {
          line: 49,
          column: 57
        },
        end: {
          line: 49,
          column: 68
        }
      },
      "51": {
        start: {
          line: 49,
          column: 69
        },
        end: {
          line: 49,
          column: 81
        }
      },
      "52": {
        start: {
          line: 49,
          column: 119
        },
        end: {
          line: 49,
          column: 196
        }
      },
      "53": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 160
        }
      },
      "54": {
        start: {
          line: 50,
          column: 141
        },
        end: {
          line: 50,
          column: 153
        }
      },
      "55": {
        start: {
          line: 51,
          column: 23
        },
        end: {
          line: 51,
          column: 68
        }
      },
      "56": {
        start: {
          line: 51,
          column: 45
        },
        end: {
          line: 51,
          column: 65
        }
      },
      "57": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 70
        }
      },
      "58": {
        start: {
          line: 53,
          column: 15
        },
        end: {
          line: 53,
          column: 70
        }
      },
      "59": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 71,
          column: 66
        }
      },
      "60": {
        start: {
          line: 54,
          column: 50
        },
        end: {
          line: 71,
          column: 66
        }
      },
      "61": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 55,
          column: 169
        }
      },
      "62": {
        start: {
          line: 55,
          column: 160
        },
        end: {
          line: 55,
          column: 169
        }
      },
      "63": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 52
        }
      },
      "64": {
        start: {
          line: 56,
          column: 26
        },
        end: {
          line: 56,
          column: 52
        }
      },
      "65": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 69,
          column: 13
        }
      },
      "66": {
        start: {
          line: 58,
          column: 32
        },
        end: {
          line: 58,
          column: 39
        }
      },
      "67": {
        start: {
          line: 58,
          column: 40
        },
        end: {
          line: 58,
          column: 46
        }
      },
      "68": {
        start: {
          line: 59,
          column: 24
        },
        end: {
          line: 59,
          column: 34
        }
      },
      "69": {
        start: {
          line: 59,
          column: 35
        },
        end: {
          line: 59,
          column: 72
        }
      },
      "70": {
        start: {
          line: 60,
          column: 24
        },
        end: {
          line: 60,
          column: 34
        }
      },
      "71": {
        start: {
          line: 60,
          column: 35
        },
        end: {
          line: 60,
          column: 45
        }
      },
      "72": {
        start: {
          line: 60,
          column: 46
        },
        end: {
          line: 60,
          column: 55
        }
      },
      "73": {
        start: {
          line: 60,
          column: 56
        },
        end: {
          line: 60,
          column: 65
        }
      },
      "74": {
        start: {
          line: 61,
          column: 24
        },
        end: {
          line: 61,
          column: 41
        }
      },
      "75": {
        start: {
          line: 61,
          column: 42
        },
        end: {
          line: 61,
          column: 55
        }
      },
      "76": {
        start: {
          line: 61,
          column: 56
        },
        end: {
          line: 61,
          column: 65
        }
      },
      "77": {
        start: {
          line: 63,
          column: 20
        },
        end: {
          line: 63,
          column: 128
        }
      },
      "78": {
        start: {
          line: 63,
          column: 110
        },
        end: {
          line: 63,
          column: 116
        }
      },
      "79": {
        start: {
          line: 63,
          column: 117
        },
        end: {
          line: 63,
          column: 126
        }
      },
      "80": {
        start: {
          line: 64,
          column: 20
        },
        end: {
          line: 64,
          column: 106
        }
      },
      "81": {
        start: {
          line: 64,
          column: 81
        },
        end: {
          line: 64,
          column: 97
        }
      },
      "82": {
        start: {
          line: 64,
          column: 98
        },
        end: {
          line: 64,
          column: 104
        }
      },
      "83": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 65,
          column: 89
        }
      },
      "84": {
        start: {
          line: 65,
          column: 57
        },
        end: {
          line: 65,
          column: 72
        }
      },
      "85": {
        start: {
          line: 65,
          column: 73
        },
        end: {
          line: 65,
          column: 80
        }
      },
      "86": {
        start: {
          line: 65,
          column: 81
        },
        end: {
          line: 65,
          column: 87
        }
      },
      "87": {
        start: {
          line: 66,
          column: 20
        },
        end: {
          line: 66,
          column: 87
        }
      },
      "88": {
        start: {
          line: 66,
          column: 47
        },
        end: {
          line: 66,
          column: 62
        }
      },
      "89": {
        start: {
          line: 66,
          column: 63
        },
        end: {
          line: 66,
          column: 78
        }
      },
      "90": {
        start: {
          line: 66,
          column: 79
        },
        end: {
          line: 66,
          column: 85
        }
      },
      "91": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 67,
          column: 42
        }
      },
      "92": {
        start: {
          line: 67,
          column: 30
        },
        end: {
          line: 67,
          column: 42
        }
      },
      "93": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 68,
          column: 33
        }
      },
      "94": {
        start: {
          line: 68,
          column: 34
        },
        end: {
          line: 68,
          column: 43
        }
      },
      "95": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 70,
          column: 39
        }
      },
      "96": {
        start: {
          line: 71,
          column: 22
        },
        end: {
          line: 71,
          column: 34
        }
      },
      "97": {
        start: {
          line: 71,
          column: 35
        },
        end: {
          line: 71,
          column: 41
        }
      },
      "98": {
        start: {
          line: 71,
          column: 54
        },
        end: {
          line: 71,
          column: 64
        }
      },
      "99": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 35
        }
      },
      "100": {
        start: {
          line: 72,
          column: 23
        },
        end: {
          line: 72,
          column: 35
        }
      },
      "101": {
        start: {
          line: 72,
          column: 36
        },
        end: {
          line: 72,
          column: 89
        }
      },
      "102": {
        start: {
          line: 75,
          column: 0
        },
        end: {
          line: 75,
          column: 62
        }
      },
      "103": {
        start: {
          line: 76,
          column: 0
        },
        end: {
          line: 76,
          column: 67
        }
      },
      "104": {
        start: {
          line: 77,
          column: 15
        },
        end: {
          line: 77,
          column: 37
        }
      },
      "105": {
        start: {
          line: 78,
          column: 13
        },
        end: {
          line: 78,
          column: 38
        }
      },
      "106": {
        start: {
          line: 79,
          column: 13
        },
        end: {
          line: 79,
          column: 34
        }
      },
      "107": {
        start: {
          line: 80,
          column: 19
        },
        end: {
          line: 80,
          column: 46
        }
      },
      "108": {
        start: {
          line: 81,
          column: 19
        },
        end: {
          line: 81,
          column: 46
        }
      },
      "109": {
        start: {
          line: 82,
          column: 34
        },
        end: {
          line: 82,
          column: 76
        }
      },
      "110": {
        start: {
          line: 83,
          column: 0
        },
        end: {
          line: 184,
          column: 7
        }
      },
      "111": {
        start: {
          line: 83,
          column: 93
        },
        end: {
          line: 184,
          column: 3
        }
      },
      "112": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 183,
          column: 7
        }
      },
      "113": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 182,
          column: 9
        }
      },
      "114": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 79
        }
      },
      "115": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 100,
          column: 17
        }
      },
      "116": {
        start: {
          line: 91,
          column: 20
        },
        end: {
          line: 91,
          column: 78
        }
      },
      "117": {
        start: {
          line: 92,
          column: 20
        },
        end: {
          line: 92,
          column: 43
        }
      },
      "118": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 98,
          column: 22
        }
      },
      "119": {
        start: {
          line: 99,
          column: 20
        },
        end: {
          line: 99,
          column: 32
        }
      },
      "120": {
        start: {
          line: 101,
          column: 16
        },
        end: {
          line: 101,
          column: 87
        }
      },
      "121": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 103,
          column: 36
        }
      },
      "122": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 108,
          column: 17
        }
      },
      "123": {
        start: {
          line: 105,
          column: 20
        },
        end: {
          line: 105,
          column: 59
        }
      },
      "124": {
        start: {
          line: 106,
          column: 20
        },
        end: {
          line: 106,
          column: 43
        }
      },
      "125": {
        start: {
          line: 107,
          column: 20
        },
        end: {
          line: 107,
          column: 32
        }
      },
      "126": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 109,
          column: 52
        }
      },
      "127": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 110,
          column: 39
        }
      },
      "128": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 116,
          column: 17
        }
      },
      "129": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 113,
          column: 57
        }
      },
      "130": {
        start: {
          line: 114,
          column: 20
        },
        end: {
          line: 114,
          column: 43
        }
      },
      "131": {
        start: {
          line: 115,
          column: 20
        },
        end: {
          line: 115,
          column: 32
        }
      },
      "132": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 122,
          column: 17
        }
      },
      "133": {
        start: {
          line: 119,
          column: 20
        },
        end: {
          line: 119,
          column: 81
        }
      },
      "134": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 43
        }
      },
      "135": {
        start: {
          line: 121,
          column: 20
        },
        end: {
          line: 121,
          column: 32
        }
      },
      "136": {
        start: {
          line: 123,
          column: 16
        },
        end: {
          line: 123,
          column: 32
        }
      },
      "137": {
        start: {
          line: 124,
          column: 16
        },
        end: {
          line: 124,
          column: 29
        }
      },
      "138": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 126,
          column: 42
        }
      },
      "139": {
        start: {
          line: 127,
          column: 16
        },
        end: {
          line: 127,
          column: 85
        }
      },
      "140": {
        start: {
          line: 129,
          column: 16
        },
        end: {
          line: 129,
          column: 36
        }
      },
      "141": {
        start: {
          line: 130,
          column: 16
        },
        end: {
          line: 130,
          column: 40
        }
      },
      "142": {
        start: {
          line: 132,
          column: 16
        },
        end: {
          line: 132,
          column: 36
        }
      },
      "143": {
        start: {
          line: 133,
          column: 16
        },
        end: {
          line: 133,
          column: 97
        }
      },
      "144": {
        start: {
          line: 134,
          column: 16
        },
        end: {
          line: 134,
          column: 40
        }
      },
      "145": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 135,
          column: 130
        }
      },
      "146": {
        start: {
          line: 135,
          column: 78
        },
        end: {
          line: 135,
          column: 125
        }
      },
      "147": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 137,
          column: 56
        }
      },
      "148": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 138,
          column: 44
        }
      },
      "149": {
        start: {
          line: 139,
          column: 16
        },
        end: {
          line: 149,
          column: 24
        }
      },
      "150": {
        start: {
          line: 151,
          column: 16
        },
        end: {
          line: 151,
          column: 33
        }
      },
      "151": {
        start: {
          line: 152,
          column: 16
        },
        end: {
          line: 152,
          column: 59
        }
      },
      "152": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 26
        }
      },
      "153": {
        start: {
          line: 155,
          column: 16
        },
        end: {
          line: 159,
          column: 17
        }
      },
      "154": {
        start: {
          line: 156,
          column: 20
        },
        end: {
          line: 156,
          column: 56
        }
      },
      "155": {
        start: {
          line: 157,
          column: 20
        },
        end: {
          line: 157,
          column: 43
        }
      },
      "156": {
        start: {
          line: 158,
          column: 20
        },
        end: {
          line: 158,
          column: 32
        }
      },
      "157": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 181,
          column: 24
        }
      },
      "158": {
        start: {
          line: 186,
          column: 0
        },
        end: {
          line: 193,
          column: 7
        }
      },
      "159": {
        start: {
          line: 186,
          column: 87
        },
        end: {
          line: 193,
          column: 3
        }
      },
      "160": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 192,
          column: 7
        }
      },
      "161": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 48
        }
      },
      "162": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 31
        }
      },
      "163": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 191,
          column: 20
        }
      },
      "164": {
        start: {
          line: 194,
          column: 0
        },
        end: {
          line: 201,
          column: 7
        }
      },
      "165": {
        start: {
          line: 194,
          column: 86
        },
        end: {
          line: 201,
          column: 3
        }
      },
      "166": {
        start: {
          line: 196,
          column: 4
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "167": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 197,
          column: 48
        }
      },
      "168": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 198,
          column: 31
        }
      },
      "169": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 199,
          column: 20
        }
      },
      "170": {
        start: {
          line: 202,
          column: 0
        },
        end: {
          line: 209,
          column: 7
        }
      },
      "171": {
        start: {
          line: 202,
          column: 89
        },
        end: {
          line: 209,
          column: 3
        }
      },
      "172": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 208,
          column: 7
        }
      },
      "173": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 48
        }
      },
      "174": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 206,
          column: 31
        }
      },
      "175": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 207,
          column: 20
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 74
          },
          end: {
            line: 6,
            column: 75
          }
        },
        loc: {
          start: {
            line: 6,
            column: 96
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 38
          },
          end: {
            line: 10,
            column: 39
          }
        },
        loc: {
          start: {
            line: 10,
            column: 49
          },
          end: {
            line: 10,
            column: 65
          }
        },
        line: 10
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 6
          },
          end: {
            line: 13,
            column: 7
          }
        },
        loc: {
          start: {
            line: 13,
            column: 28
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 17,
            column: 80
          },
          end: {
            line: 17,
            column: 81
          }
        },
        loc: {
          start: {
            line: 17,
            column: 95
          },
          end: {
            line: 19,
            column: 1
          }
        },
        line: 17
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 19,
            column: 5
          },
          end: {
            line: 19,
            column: 6
          }
        },
        loc: {
          start: {
            line: 19,
            column: 20
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 19
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 22,
            column: 51
          },
          end: {
            line: 22,
            column: 52
          }
        },
        loc: {
          start: {
            line: 22,
            column: 63
          },
          end: {
            line: 38,
            column: 1
          }
        },
        line: 22
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 23,
            column: 18
          },
          end: {
            line: 23,
            column: 19
          }
        },
        loc: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 23
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 24,
            column: 48
          },
          end: {
            line: 24,
            column: 49
          }
        },
        loc: {
          start: {
            line: 24,
            column: 61
          },
          end: {
            line: 28,
            column: 9
          }
        },
        line: 24
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 31,
            column: 11
          },
          end: {
            line: 31,
            column: 12
          }
        },
        loc: {
          start: {
            line: 31,
            column: 26
          },
          end: {
            line: 37,
            column: 5
          }
        },
        line: 31
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 39,
            column: 44
          },
          end: {
            line: 39,
            column: 45
          }
        },
        loc: {
          start: {
            line: 39,
            column: 89
          },
          end: {
            line: 47,
            column: 1
          }
        },
        line: 39
      },
      "10": {
        name: "adopt",
        decl: {
          start: {
            line: 40,
            column: 13
          },
          end: {
            line: 40,
            column: 18
          }
        },
        loc: {
          start: {
            line: 40,
            column: 26
          },
          end: {
            line: 40,
            column: 112
          }
        },
        line: 40
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 40,
            column: 70
          },
          end: {
            line: 40,
            column: 71
          }
        },
        loc: {
          start: {
            line: 40,
            column: 89
          },
          end: {
            line: 40,
            column: 108
          }
        },
        line: 40
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 41,
            column: 36
          },
          end: {
            line: 41,
            column: 37
          }
        },
        loc: {
          start: {
            line: 41,
            column: 63
          },
          end: {
            line: 46,
            column: 5
          }
        },
        line: 41
      },
      "13": {
        name: "fulfilled",
        decl: {
          start: {
            line: 42,
            column: 17
          },
          end: {
            line: 42,
            column: 26
          }
        },
        loc: {
          start: {
            line: 42,
            column: 34
          },
          end: {
            line: 42,
            column: 99
          }
        },
        line: 42
      },
      "14": {
        name: "rejected",
        decl: {
          start: {
            line: 43,
            column: 17
          },
          end: {
            line: 43,
            column: 25
          }
        },
        loc: {
          start: {
            line: 43,
            column: 33
          },
          end: {
            line: 43,
            column: 102
          }
        },
        line: 43
      },
      "15": {
        name: "step",
        decl: {
          start: {
            line: 44,
            column: 17
          },
          end: {
            line: 44,
            column: 21
          }
        },
        loc: {
          start: {
            line: 44,
            column: 30
          },
          end: {
            line: 44,
            column: 118
          }
        },
        line: 44
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 48,
            column: 48
          },
          end: {
            line: 48,
            column: 49
          }
        },
        loc: {
          start: {
            line: 48,
            column: 73
          },
          end: {
            line: 74,
            column: 1
          }
        },
        line: 48
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 49,
            column: 30
          },
          end: {
            line: 49,
            column: 31
          }
        },
        loc: {
          start: {
            line: 49,
            column: 41
          },
          end: {
            line: 49,
            column: 83
          }
        },
        line: 49
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 50,
            column: 128
          },
          end: {
            line: 50,
            column: 129
          }
        },
        loc: {
          start: {
            line: 50,
            column: 139
          },
          end: {
            line: 50,
            column: 155
          }
        },
        line: 50
      },
      "19": {
        name: "verb",
        decl: {
          start: {
            line: 51,
            column: 13
          },
          end: {
            line: 51,
            column: 17
          }
        },
        loc: {
          start: {
            line: 51,
            column: 21
          },
          end: {
            line: 51,
            column: 70
          }
        },
        line: 51
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 51,
            column: 30
          },
          end: {
            line: 51,
            column: 31
          }
        },
        loc: {
          start: {
            line: 51,
            column: 43
          },
          end: {
            line: 51,
            column: 67
          }
        },
        line: 51
      },
      "21": {
        name: "step",
        decl: {
          start: {
            line: 52,
            column: 13
          },
          end: {
            line: 52,
            column: 17
          }
        },
        loc: {
          start: {
            line: 52,
            column: 22
          },
          end: {
            line: 73,
            column: 5
          }
        },
        line: 52
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 83,
            column: 72
          },
          end: {
            line: 83,
            column: 73
          }
        },
        loc: {
          start: {
            line: 83,
            column: 91
          },
          end: {
            line: 184,
            column: 5
          }
        },
        line: 83
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 83,
            column: 135
          },
          end: {
            line: 83,
            column: 136
          }
        },
        loc: {
          start: {
            line: 83,
            column: 147
          },
          end: {
            line: 184,
            column: 1
          }
        },
        line: 83
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 86,
            column: 29
          },
          end: {
            line: 86,
            column: 30
          }
        },
        loc: {
          start: {
            line: 86,
            column: 43
          },
          end: {
            line: 183,
            column: 5
          }
        },
        line: 86
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 135,
            column: 64
          },
          end: {
            line: 135,
            column: 65
          }
        },
        loc: {
          start: {
            line: 135,
            column: 76
          },
          end: {
            line: 135,
            column: 127
          }
        },
        line: 135
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 186,
            column: 73
          },
          end: {
            line: 186,
            column: 74
          }
        },
        loc: {
          start: {
            line: 186,
            column: 85
          },
          end: {
            line: 193,
            column: 5
          }
        },
        line: 186
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 186,
            column: 129
          },
          end: {
            line: 186,
            column: 130
          }
        },
        loc: {
          start: {
            line: 186,
            column: 141
          },
          end: {
            line: 193,
            column: 1
          }
        },
        line: 186
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 188,
            column: 29
          },
          end: {
            line: 188,
            column: 30
          }
        },
        loc: {
          start: {
            line: 188,
            column: 43
          },
          end: {
            line: 192,
            column: 5
          }
        },
        line: 188
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 194,
            column: 72
          },
          end: {
            line: 194,
            column: 73
          }
        },
        loc: {
          start: {
            line: 194,
            column: 84
          },
          end: {
            line: 201,
            column: 5
          }
        },
        line: 194
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 194,
            column: 128
          },
          end: {
            line: 194,
            column: 129
          }
        },
        loc: {
          start: {
            line: 194,
            column: 140
          },
          end: {
            line: 201,
            column: 1
          }
        },
        line: 194
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 196,
            column: 29
          },
          end: {
            line: 196,
            column: 30
          }
        },
        loc: {
          start: {
            line: 196,
            column: 43
          },
          end: {
            line: 200,
            column: 5
          }
        },
        line: 196
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 202,
            column: 75
          },
          end: {
            line: 202,
            column: 76
          }
        },
        loc: {
          start: {
            line: 202,
            column: 87
          },
          end: {
            line: 209,
            column: 5
          }
        },
        line: 202
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 202,
            column: 131
          },
          end: {
            line: 202,
            column: 132
          }
        },
        loc: {
          start: {
            line: 202,
            column: 143
          },
          end: {
            line: 209,
            column: 1
          }
        },
        line: 202
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 204,
            column: 29
          },
          end: {
            line: 204,
            column: 30
          }
        },
        loc: {
          start: {
            line: 204,
            column: 43
          },
          end: {
            line: 208,
            column: 5
          }
        },
        line: 204
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 16,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 23
          },
          end: {
            line: 6,
            column: 27
          }
        }, {
          start: {
            line: 6,
            column: 31
          },
          end: {
            line: 6,
            column: 51
          }
        }, {
          start: {
            line: 6,
            column: 57
          },
          end: {
            line: 16,
            column: 2
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 6,
            column: 57
          },
          end: {
            line: 16,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 6,
            column: 74
          },
          end: {
            line: 13,
            column: 1
          }
        }, {
          start: {
            line: 13,
            column: 6
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "2": {
        loc: {
          start: {
            line: 7,
            column: 4
          },
          end: {
            line: 7,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 7,
            column: 4
          },
          end: {
            line: 7,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 7
      },
      "3": {
        loc: {
          start: {
            line: 9,
            column: 4
          },
          end: {
            line: 11,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 9,
            column: 4
          },
          end: {
            line: 11,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 9
      },
      "4": {
        loc: {
          start: {
            line: 9,
            column: 8
          },
          end: {
            line: 9,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 9,
            column: 8
          },
          end: {
            line: 9,
            column: 13
          }
        }, {
          start: {
            line: 9,
            column: 18
          },
          end: {
            line: 9,
            column: 84
          }
        }],
        line: 9
      },
      "5": {
        loc: {
          start: {
            line: 9,
            column: 18
          },
          end: {
            line: 9,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 47
          }
        }, {
          start: {
            line: 9,
            column: 50
          },
          end: {
            line: 9,
            column: 84
          }
        }],
        line: 9
      },
      "6": {
        loc: {
          start: {
            line: 9,
            column: 50
          },
          end: {
            line: 9,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 9,
            column: 50
          },
          end: {
            line: 9,
            column: 63
          }
        }, {
          start: {
            line: 9,
            column: 67
          },
          end: {
            line: 9,
            column: 84
          }
        }],
        line: 9
      },
      "7": {
        loc: {
          start: {
            line: 14,
            column: 4
          },
          end: {
            line: 14,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 14,
            column: 4
          },
          end: {
            line: 14,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 14
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 25
          },
          end: {
            line: 21,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 26
          },
          end: {
            line: 17,
            column: 30
          }
        }, {
          start: {
            line: 17,
            column: 34
          },
          end: {
            line: 17,
            column: 57
          }
        }, {
          start: {
            line: 17,
            column: 63
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 17,
            column: 63
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 17,
            column: 80
          },
          end: {
            line: 19,
            column: 1
          }
        }, {
          start: {
            line: 19,
            column: 5
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 17
      },
      "10": {
        loc: {
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 38,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 20
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 22,
            column: 45
          }
        }, {
          start: {
            line: 22,
            column: 50
          },
          end: {
            line: 38,
            column: 4
          }
        }],
        line: 22
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 18
          },
          end: {
            line: 28,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 18
          },
          end: {
            line: 24,
            column: 44
          }
        }, {
          start: {
            line: 24,
            column: 48
          },
          end: {
            line: 28,
            column: 9
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 26,
            column: 29
          },
          end: {
            line: 26,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 29
          },
          end: {
            line: 26,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "13": {
        loc: {
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "14": {
        loc: {
          start: {
            line: 32,
            column: 12
          },
          end: {
            line: 32,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 12
          },
          end: {
            line: 32,
            column: 15
          }
        }, {
          start: {
            line: 32,
            column: 19
          },
          end: {
            line: 32,
            column: 33
          }
        }],
        line: 32
      },
      "15": {
        loc: {
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 34,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 34,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "16": {
        loc: {
          start: {
            line: 34,
            column: 78
          },
          end: {
            line: 34,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 78
          },
          end: {
            line: 34,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "17": {
        loc: {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 47,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 17
          },
          end: {
            line: 39,
            column: 21
          }
        }, {
          start: {
            line: 39,
            column: 25
          },
          end: {
            line: 39,
            column: 39
          }
        }, {
          start: {
            line: 39,
            column: 44
          },
          end: {
            line: 47,
            column: 1
          }
        }],
        line: 39
      },
      "18": {
        loc: {
          start: {
            line: 40,
            column: 35
          },
          end: {
            line: 40,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 40,
            column: 56
          },
          end: {
            line: 40,
            column: 61
          }
        }, {
          start: {
            line: 40,
            column: 64
          },
          end: {
            line: 40,
            column: 109
          }
        }],
        line: 40
      },
      "19": {
        loc: {
          start: {
            line: 41,
            column: 16
          },
          end: {
            line: 41,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 16
          },
          end: {
            line: 41,
            column: 17
          }
        }, {
          start: {
            line: 41,
            column: 22
          },
          end: {
            line: 41,
            column: 33
          }
        }],
        line: 41
      },
      "20": {
        loc: {
          start: {
            line: 44,
            column: 32
          },
          end: {
            line: 44,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 46
          },
          end: {
            line: 44,
            column: 67
          }
        }, {
          start: {
            line: 44,
            column: 70
          },
          end: {
            line: 44,
            column: 115
          }
        }],
        line: 44
      },
      "21": {
        loc: {
          start: {
            line: 45,
            column: 51
          },
          end: {
            line: 45,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 51
          },
          end: {
            line: 45,
            column: 61
          }
        }, {
          start: {
            line: 45,
            column: 65
          },
          end: {
            line: 45,
            column: 67
          }
        }],
        line: 45
      },
      "22": {
        loc: {
          start: {
            line: 48,
            column: 18
          },
          end: {
            line: 74,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 19
          },
          end: {
            line: 48,
            column: 23
          }
        }, {
          start: {
            line: 48,
            column: 27
          },
          end: {
            line: 48,
            column: 43
          }
        }, {
          start: {
            line: 48,
            column: 48
          },
          end: {
            line: 74,
            column: 1
          }
        }],
        line: 48
      },
      "23": {
        loc: {
          start: {
            line: 49,
            column: 43
          },
          end: {
            line: 49,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 43
          },
          end: {
            line: 49,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 49
      },
      "24": {
        loc: {
          start: {
            line: 49,
            column: 134
          },
          end: {
            line: 49,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 49,
            column: 167
          },
          end: {
            line: 49,
            column: 175
          }
        }, {
          start: {
            line: 49,
            column: 178
          },
          end: {
            line: 49,
            column: 184
          }
        }],
        line: 49
      },
      "25": {
        loc: {
          start: {
            line: 50,
            column: 74
          },
          end: {
            line: 50,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 74
          },
          end: {
            line: 50,
            column: 102
          }
        }, {
          start: {
            line: 50,
            column: 107
          },
          end: {
            line: 50,
            column: 155
          }
        }],
        line: 50
      },
      "26": {
        loc: {
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 53,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 53,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "27": {
        loc: {
          start: {
            line: 54,
            column: 15
          },
          end: {
            line: 54,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 15
          },
          end: {
            line: 54,
            column: 16
          }
        }, {
          start: {
            line: 54,
            column: 21
          },
          end: {
            line: 54,
            column: 44
          }
        }],
        line: 54
      },
      "28": {
        loc: {
          start: {
            line: 54,
            column: 28
          },
          end: {
            line: 54,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 28
          },
          end: {
            line: 54,
            column: 33
          }
        }, {
          start: {
            line: 54,
            column: 38
          },
          end: {
            line: 54,
            column: 43
          }
        }],
        line: 54
      },
      "29": {
        loc: {
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "30": {
        loc: {
          start: {
            line: 55,
            column: 23
          },
          end: {
            line: 55,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 23
          },
          end: {
            line: 55,
            column: 24
          }
        }, {
          start: {
            line: 55,
            column: 29
          },
          end: {
            line: 55,
            column: 125
          }
        }, {
          start: {
            line: 55,
            column: 130
          },
          end: {
            line: 55,
            column: 158
          }
        }],
        line: 55
      },
      "31": {
        loc: {
          start: {
            line: 55,
            column: 33
          },
          end: {
            line: 55,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 45
          },
          end: {
            line: 55,
            column: 56
          }
        }, {
          start: {
            line: 55,
            column: 59
          },
          end: {
            line: 55,
            column: 125
          }
        }],
        line: 55
      },
      "32": {
        loc: {
          start: {
            line: 55,
            column: 59
          },
          end: {
            line: 55,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 67
          },
          end: {
            line: 55,
            column: 116
          }
        }, {
          start: {
            line: 55,
            column: 119
          },
          end: {
            line: 55,
            column: 125
          }
        }],
        line: 55
      },
      "33": {
        loc: {
          start: {
            line: 55,
            column: 67
          },
          end: {
            line: 55,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 67
          },
          end: {
            line: 55,
            column: 77
          }
        }, {
          start: {
            line: 55,
            column: 82
          },
          end: {
            line: 55,
            column: 115
          }
        }],
        line: 55
      },
      "34": {
        loc: {
          start: {
            line: 55,
            column: 82
          },
          end: {
            line: 55,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 83
          },
          end: {
            line: 55,
            column: 98
          }
        }, {
          start: {
            line: 55,
            column: 103
          },
          end: {
            line: 55,
            column: 112
          }
        }],
        line: 55
      },
      "35": {
        loc: {
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "36": {
        loc: {
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 69,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 58,
            column: 16
          },
          end: {
            line: 58,
            column: 23
          }
        }, {
          start: {
            line: 58,
            column: 24
          },
          end: {
            line: 58,
            column: 46
          }
        }, {
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 59,
            column: 72
          }
        }, {
          start: {
            line: 60,
            column: 16
          },
          end: {
            line: 60,
            column: 65
          }
        }, {
          start: {
            line: 61,
            column: 16
          },
          end: {
            line: 61,
            column: 65
          }
        }, {
          start: {
            line: 62,
            column: 16
          },
          end: {
            line: 68,
            column: 43
          }
        }],
        line: 57
      },
      "37": {
        loc: {
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "38": {
        loc: {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 74
          }
        }, {
          start: {
            line: 63,
            column: 79
          },
          end: {
            line: 63,
            column: 90
          }
        }, {
          start: {
            line: 63,
            column: 94
          },
          end: {
            line: 63,
            column: 105
          }
        }],
        line: 63
      },
      "39": {
        loc: {
          start: {
            line: 63,
            column: 42
          },
          end: {
            line: 63,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 42
          },
          end: {
            line: 63,
            column: 54
          }
        }, {
          start: {
            line: 63,
            column: 58
          },
          end: {
            line: 63,
            column: 73
          }
        }],
        line: 63
      },
      "40": {
        loc: {
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 64,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 64,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "41": {
        loc: {
          start: {
            line: 64,
            column: 24
          },
          end: {
            line: 64,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 24
          },
          end: {
            line: 64,
            column: 35
          }
        }, {
          start: {
            line: 64,
            column: 40
          },
          end: {
            line: 64,
            column: 42
          }
        }, {
          start: {
            line: 64,
            column: 47
          },
          end: {
            line: 64,
            column: 59
          }
        }, {
          start: {
            line: 64,
            column: 63
          },
          end: {
            line: 64,
            column: 75
          }
        }],
        line: 64
      },
      "42": {
        loc: {
          start: {
            line: 65,
            column: 20
          },
          end: {
            line: 65,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 65,
            column: 20
          },
          end: {
            line: 65,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 65
      },
      "43": {
        loc: {
          start: {
            line: 65,
            column: 24
          },
          end: {
            line: 65,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 65,
            column: 24
          },
          end: {
            line: 65,
            column: 35
          }
        }, {
          start: {
            line: 65,
            column: 39
          },
          end: {
            line: 65,
            column: 53
          }
        }],
        line: 65
      },
      "44": {
        loc: {
          start: {
            line: 66,
            column: 20
          },
          end: {
            line: 66,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 66,
            column: 20
          },
          end: {
            line: 66,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 66
      },
      "45": {
        loc: {
          start: {
            line: 66,
            column: 24
          },
          end: {
            line: 66,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 66,
            column: 24
          },
          end: {
            line: 66,
            column: 25
          }
        }, {
          start: {
            line: 66,
            column: 29
          },
          end: {
            line: 66,
            column: 43
          }
        }],
        line: 66
      },
      "46": {
        loc: {
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 67,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 67,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "47": {
        loc: {
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 72,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 72,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "48": {
        loc: {
          start: {
            line: 72,
            column: 52
          },
          end: {
            line: 72,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 60
          },
          end: {
            line: 72,
            column: 65
          }
        }, {
          start: {
            line: 72,
            column: 68
          },
          end: {
            line: 72,
            column: 74
          }
        }],
        line: 72
      },
      "49": {
        loc: {
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 182,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 88,
            column: 12
          },
          end: {
            line: 101,
            column: 87
          }
        }, {
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 124,
            column: 29
          }
        }, {
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 127,
            column: 85
          }
        }, {
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 130,
            column: 40
          }
        }, {
          start: {
            line: 131,
            column: 12
          },
          end: {
            line: 134,
            column: 40
          }
        }, {
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 130
          }
        }, {
          start: {
            line: 136,
            column: 12
          },
          end: {
            line: 149,
            column: 24
          }
        }, {
          start: {
            line: 150,
            column: 12
          },
          end: {
            line: 152,
            column: 59
          }
        }, {
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 181,
            column: 24
          }
        }],
        line: 87
      },
      "50": {
        loc: {
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 100,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 100,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 90
      },
      "51": {
        loc: {
          start: {
            line: 104,
            column: 16
          },
          end: {
            line: 108,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 16
          },
          end: {
            line: 108,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "52": {
        loc: {
          start: {
            line: 104,
            column: 22
          },
          end: {
            line: 104,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 104,
            column: 120
          },
          end: {
            line: 104,
            column: 126
          }
        }, {
          start: {
            line: 104,
            column: 129
          },
          end: {
            line: 104,
            column: 134
          }
        }],
        line: 104
      },
      "53": {
        loc: {
          start: {
            line: 104,
            column: 22
          },
          end: {
            line: 104,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 104,
            column: 22
          },
          end: {
            line: 104,
            column: 100
          }
        }, {
          start: {
            line: 104,
            column: 104
          },
          end: {
            line: 104,
            column: 117
          }
        }],
        line: 104
      },
      "54": {
        loc: {
          start: {
            line: 104,
            column: 28
          },
          end: {
            line: 104,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 104,
            column: 69
          },
          end: {
            line: 104,
            column: 75
          }
        }, {
          start: {
            line: 104,
            column: 78
          },
          end: {
            line: 104,
            column: 90
          }
        }],
        line: 104
      },
      "55": {
        loc: {
          start: {
            line: 104,
            column: 28
          },
          end: {
            line: 104,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 104,
            column: 28
          },
          end: {
            line: 104,
            column: 44
          }
        }, {
          start: {
            line: 104,
            column: 48
          },
          end: {
            line: 104,
            column: 66
          }
        }],
        line: 104
      },
      "56": {
        loc: {
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 116,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 116,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "57": {
        loc: {
          start: {
            line: 112,
            column: 20
          },
          end: {
            line: 112,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 20
          },
          end: {
            line: 112,
            column: 36
          }
        }, {
          start: {
            line: 112,
            column: 40
          },
          end: {
            line: 112,
            column: 62
          }
        }],
        line: 112
      },
      "58": {
        loc: {
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "59": {
        loc: {
          start: {
            line: 118,
            column: 20
          },
          end: {
            line: 118,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 118,
            column: 20
          },
          end: {
            line: 118,
            column: 36
          }
        }, {
          start: {
            line: 118,
            column: 40
          },
          end: {
            line: 118,
            column: 86
          }
        }],
        line: 118
      },
      "60": {
        loc: {
          start: {
            line: 155,
            column: 16
          },
          end: {
            line: 159,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 16
          },
          end: {
            line: 159,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "61": {
        loc: {
          start: {
            line: 175,
            column: 43
          },
          end: {
            line: 175,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 175,
            column: 43
          },
          end: {
            line: 175,
            column: 65
          }
        }, {
          start: {
            line: 175,
            column: 69
          },
          end: {
            line: 175,
            column: 73
          }
        }],
        line: 175
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0, 0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/validate-session/route.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,+CAA+C;AAC/C,+CAAgD;AAChD,6EAAwF;AAqB3E,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;;;gBAEzE,eAAe,GAAG,yBAAY,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAExD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;oBACvB,KAAK,GAAG,IAAI,KAAK,CAAC,sCAAsC,CAAQ,CAAC;oBACvE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,KAAK,CAAC,OAAO,GAAG;wBACd,mBAAmB,EAAE,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE;wBACrD,uBAAuB,EAAE,eAAe,CAAC,SAAS,CAAC,QAAQ,EAAE;wBAC7D,mBAAmB,EAAE,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;wBACtE,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;qBACrF,CAAC;oBACF,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGe,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;oBACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGK,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBACpC,YAAY,GAAG,OAAc,CAAC;gBAEpC,+BAA+B;gBAC/B,IAAI,YAAY,CAAC,GAAG,IAAI,YAAY,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;oBACzC,KAAK,GAAG,IAAI,KAAK,CAAC,iBAAiB,CAAQ,CAAC;oBAClD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,4DAA4D;gBAC5D,IAAI,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;oBACjE,KAAK,GAAG,IAAI,KAAK,CAAC,yCAAyC,CAAQ,CAAC;oBAC1E,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGG,OAAO,GAAG,KAAK,CAAC;;;;gBAER,qBAAM,IAAA,wBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;gBAA5C,OAAO,GAAG,SAAkC,CAAC;;;;gBAE7C,OAAO,CAAC,KAAK,CAAC,wDAAwD,EAAE,OAAK,CAAC,CAAC;;oBAKxD,sFAAa,gBAAgB,QAAC;;gBAA/C,YAAY,GAAK,CAAA,SAA8B,CAAA,aAAnC;gBACd,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;gBAErB,qBAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE;wBAC9B,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,KAAK,EAAE,IAAI;4BACX,IAAI,EAAE,IAAI;4BACV,aAAa,EAAE,IAAI;4BACnB,SAAS,EAAE,IAAI;4BACf,SAAS,EAAE,IAAI;yBAChB;qBACF,CAAC,EAAA;;gBAVI,IAAI,GAAG,SAUX;gBAEF,qBAAM,MAAM,CAAC,WAAW,EAAE,EAAA;;gBAA1B,SAA0B,CAAC;gBAE3B,IAAI,CAAC,IAAI,EAAE,CAAC;oBACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;oBACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,+BAA+B;gBAC/B,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,KAAK,EAAE,IAAI;4BACX,IAAI,EAAE;gCACJ,EAAE,EAAE,IAAI,CAAC,EAAE;gCACX,KAAK,EAAE,IAAI,CAAC,KAAK;gCACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gCACf,aAAa,EAAE,IAAI,CAAC,aAAa;gCACjC,SAAS,EAAE,IAAI,CAAC,SAAS;gCACzB,SAAS,EAAE,IAAI,CAAC,SAAS;6BAC1B;4BACD,OAAO,SAAA;4BACP,WAAW,EAAE;gCACX,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI,IAAI;gCACzC,QAAQ,EAAE,YAAY,CAAC,GAAG;gCAC1B,SAAS,EAAE,YAAY,CAAC,GAAG;gCAC3B,YAAY,EAAE,GAAG;6BAClB;yBACF;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAEH,0BAA0B;AACb,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,+CAAU,OAAO;;;QACtD,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAQ,CAAC;QACrD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;QACvB,MAAM,KAAK,CAAC;;KACb,CAAC,CAAC;AAEU,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,+CAAU,OAAO;;;QACrD,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAQ,CAAC;QACrD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;QACvB,MAAM,KAAK,CAAC;;KACb,CAAC,CAAC;AAEU,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,+CAAU,OAAO;;;QACxD,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAQ,CAAC;QACrD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;QACvB,MAAM,KAAK,CAAC;;KACb,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/validate-session/route.ts"],
      sourcesContent: ["/**\n * API endpoint for session validation\n * Provides server-side session validation for the authentication state manager\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { isUserAdmin } from '@/lib/auth-utils';\nimport { rateLimiters } from '@/lib/rate-limit';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface SessionValidationResponse {\n  valid: boolean;\n  user: {\n    id: string;\n    email: string;\n    name: string | null;\n    emailVerified: Date | null;\n    createdAt: Date;\n    updatedAt: Date;\n  } | null;\n  isAdmin: boolean;\n  sessionInfo?: {\n    sessionId: string | null;\n    issuedAt: number;\n    expiresAt: number;\n    lastActivity: number;\n  };\n}\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<SessionValidationResponse>>> => {\n  // Apply rate limiting\n  const rateLimitResult = rateLimiters.api.check(request);\n\n  if (!rateLimitResult.allowed) {\n    const error = new Error('Too many session validation requests') as any;\n    error.statusCode = 429;\n    error.headers = {\n      'X-RateLimit-Limit': rateLimitResult.limit.toString(),\n      'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),\n      'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString(),\n      'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()\n    };\n    throw error;\n  }\n\n  // Get current session\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('No active session') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  // Validate session integrity\n  const now = Math.floor(Date.now() / 1000);\n  const sessionToken = session as any;\n\n  // Check if session has expired\n  if (sessionToken.exp && sessionToken.exp < now) {\n    const error = new Error('Session expired') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  // Check if session is too old (force refresh after 30 days)\n  if (sessionToken.iat && (now - sessionToken.iat) > (30 * 24 * 60 * 60)) {\n    const error = new Error('Session too old, please re-authenticate') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  // Check admin status\n  let isAdmin = false;\n  try {\n    isAdmin = await isUserAdmin(session.user.id);\n  } catch (error) {\n    console.error('Error checking admin status during session validation:', error);\n    // Don't fail validation just because admin check failed\n  }\n\n  // Validate user still exists and is active\n  const { PrismaClient } = await import('@prisma/client');\n  const prisma = new PrismaClient();\n\n  const user = await prisma.user.findUnique({\n    where: { id: session.user.id },\n    select: {\n      id: true,\n      email: true,\n      name: true,\n      emailVerified: true,\n      createdAt: true,\n      updatedAt: true\n    }\n  });\n\n  await prisma.$disconnect();\n\n  if (!user) {\n    const error = new Error('User not found') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  // Return successful validation\n  return NextResponse.json({\n    success: true,\n    data: {\n      valid: true,\n      user: {\n        id: user.id,\n        email: user.email,\n        name: user.name,\n        emailVerified: user.emailVerified,\n        createdAt: user.createdAt,\n        updatedAt: user.updatedAt\n      },\n      isAdmin,\n      sessionInfo: {\n        sessionId: sessionToken.sessionId || null,\n        issuedAt: sessionToken.iat,\n        expiresAt: sessionToken.exp,\n        lastActivity: now\n      }\n    }\n  });\n});\n\n// Only allow GET requests\nexport const POST = withUnifiedErrorHandling(async (): Promise<NextResponse<ApiResponse<never>>> => {\n  const error = new Error('Method not allowed') as any;\n  error.statusCode = 405;\n  throw error;\n});\n\nexport const PUT = withUnifiedErrorHandling(async (): Promise<NextResponse<ApiResponse<never>>> => {\n  const error = new Error('Method not allowed') as any;\n  error.statusCode = 405;\n  throw error;\n});\n\nexport const DELETE = withUnifiedErrorHandling(async (): Promise<NextResponse<ApiResponse<never>>> => {\n  const error = new Error('Method not allowed') as any;\n  error.statusCode = 405;\n  throw error;\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a614ec7e72ca60a15eb740cd8efab2e32f48f293"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_j8718n3dm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_j8718n3dm();
var __createBinding =
/* istanbul ignore next */
(cov_j8718n3dm().s[0]++,
/* istanbul ignore next */
(cov_j8718n3dm().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_j8718n3dm().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_j8718n3dm().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_j8718n3dm().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_j8718n3dm().f[0]++;
  cov_j8718n3dm().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_j8718n3dm().b[2][0]++;
    cov_j8718n3dm().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_j8718n3dm().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_j8718n3dm().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_j8718n3dm().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_j8718n3dm().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_j8718n3dm().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_j8718n3dm().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_j8718n3dm().b[5][1]++,
  /* istanbul ignore next */
  (cov_j8718n3dm().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_j8718n3dm().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_j8718n3dm().b[3][0]++;
    cov_j8718n3dm().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_j8718n3dm().f[1]++;
        cov_j8718n3dm().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_j8718n3dm().b[3][1]++;
  }
  cov_j8718n3dm().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_j8718n3dm().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_j8718n3dm().f[2]++;
  cov_j8718n3dm().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_j8718n3dm().b[7][0]++;
    cov_j8718n3dm().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_j8718n3dm().b[7][1]++;
  }
  cov_j8718n3dm().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_j8718n3dm().s[11]++,
/* istanbul ignore next */
(cov_j8718n3dm().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_j8718n3dm().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_j8718n3dm().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_j8718n3dm().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_j8718n3dm().f[3]++;
  cov_j8718n3dm().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_j8718n3dm().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_j8718n3dm().f[4]++;
  cov_j8718n3dm().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_j8718n3dm().s[14]++,
/* istanbul ignore next */
(cov_j8718n3dm().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_j8718n3dm().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_j8718n3dm().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_j8718n3dm().f[5]++;
  cov_j8718n3dm().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_j8718n3dm().f[6]++;
    cov_j8718n3dm().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_j8718n3dm().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_j8718n3dm().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_j8718n3dm().s[17]++, []);
      /* istanbul ignore next */
      cov_j8718n3dm().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_j8718n3dm().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_j8718n3dm().b[12][0]++;
          cov_j8718n3dm().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_j8718n3dm().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_j8718n3dm().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_j8718n3dm().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_j8718n3dm().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_j8718n3dm().f[8]++;
    cov_j8718n3dm().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_j8718n3dm().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_j8718n3dm().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_j8718n3dm().b[13][0]++;
      cov_j8718n3dm().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_j8718n3dm().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_j8718n3dm().s[26]++, {});
    /* istanbul ignore next */
    cov_j8718n3dm().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_j8718n3dm().b[15][0]++;
      cov_j8718n3dm().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_j8718n3dm().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_j8718n3dm().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_j8718n3dm().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_j8718n3dm().b[16][0]++;
          cov_j8718n3dm().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_j8718n3dm().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_j8718n3dm().b[15][1]++;
    }
    cov_j8718n3dm().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_j8718n3dm().s[34]++;
    return result;
  };
}()));
var __awaiter =
/* istanbul ignore next */
(cov_j8718n3dm().s[35]++,
/* istanbul ignore next */
(cov_j8718n3dm().b[17][0]++, this) &&
/* istanbul ignore next */
(cov_j8718n3dm().b[17][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_j8718n3dm().b[17][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_j8718n3dm().f[9]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_j8718n3dm().f[10]++;
    cov_j8718n3dm().s[36]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_j8718n3dm().b[18][0]++, value) :
    /* istanbul ignore next */
    (cov_j8718n3dm().b[18][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[11]++;
      cov_j8718n3dm().s[37]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_j8718n3dm().s[38]++;
  return new (
  /* istanbul ignore next */
  (cov_j8718n3dm().b[19][0]++, P) ||
  /* istanbul ignore next */
  (cov_j8718n3dm().b[19][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_j8718n3dm().f[12]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[13]++;
      cov_j8718n3dm().s[39]++;
      try {
        /* istanbul ignore next */
        cov_j8718n3dm().s[40]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_j8718n3dm().s[41]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[14]++;
      cov_j8718n3dm().s[42]++;
      try {
        /* istanbul ignore next */
        cov_j8718n3dm().s[43]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_j8718n3dm().s[44]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[15]++;
      cov_j8718n3dm().s[45]++;
      result.done ?
      /* istanbul ignore next */
      (cov_j8718n3dm().b[20][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_j8718n3dm().b[20][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_j8718n3dm().s[46]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_j8718n3dm().b[21][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_j8718n3dm().b[21][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_j8718n3dm().s[47]++,
/* istanbul ignore next */
(cov_j8718n3dm().b[22][0]++, this) &&
/* istanbul ignore next */
(cov_j8718n3dm().b[22][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_j8718n3dm().b[22][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_j8718n3dm().f[16]++;
  var _ =
    /* istanbul ignore next */
    (cov_j8718n3dm().s[48]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_j8718n3dm().f[17]++;
        cov_j8718n3dm().s[49]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_j8718n3dm().b[23][0]++;
          cov_j8718n3dm().s[50]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_j8718n3dm().b[23][1]++;
        }
        cov_j8718n3dm().s[51]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_j8718n3dm().s[52]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_j8718n3dm().b[24][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_j8718n3dm().b[24][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_j8718n3dm().s[53]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_j8718n3dm().b[25][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_j8718n3dm().b[25][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_j8718n3dm().f[18]++;
    cov_j8718n3dm().s[54]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_j8718n3dm().f[19]++;
    cov_j8718n3dm().s[55]++;
    return function (v) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[20]++;
      cov_j8718n3dm().s[56]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_j8718n3dm().f[21]++;
    cov_j8718n3dm().s[57]++;
    if (f) {
      /* istanbul ignore next */
      cov_j8718n3dm().b[26][0]++;
      cov_j8718n3dm().s[58]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_j8718n3dm().b[26][1]++;
    }
    cov_j8718n3dm().s[59]++;
    while (
    /* istanbul ignore next */
    (cov_j8718n3dm().b[27][0]++, g) &&
    /* istanbul ignore next */
    (cov_j8718n3dm().b[27][1]++, g = 0,
    /* istanbul ignore next */
    (cov_j8718n3dm().b[28][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_j8718n3dm().b[28][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_j8718n3dm().s[60]++;
      try {
        /* istanbul ignore next */
        cov_j8718n3dm().s[61]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_j8718n3dm().b[30][0]++, y) &&
        /* istanbul ignore next */
        (cov_j8718n3dm().b[30][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_j8718n3dm().b[31][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_j8718n3dm().b[31][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_j8718n3dm().b[32][0]++,
        /* istanbul ignore next */
        (cov_j8718n3dm().b[33][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_j8718n3dm().b[33][1]++,
        /* istanbul ignore next */
        (cov_j8718n3dm().b[34][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_j8718n3dm().b[34][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_j8718n3dm().b[32][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_j8718n3dm().b[30][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_j8718n3dm().b[29][0]++;
          cov_j8718n3dm().s[62]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_j8718n3dm().b[29][1]++;
        }
        cov_j8718n3dm().s[63]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_j8718n3dm().b[35][0]++;
          cov_j8718n3dm().s[64]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_j8718n3dm().b[35][1]++;
        }
        cov_j8718n3dm().s[65]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_j8718n3dm().b[36][0]++;
          case 1:
            /* istanbul ignore next */
            cov_j8718n3dm().b[36][1]++;
            cov_j8718n3dm().s[66]++;
            t = op;
            /* istanbul ignore next */
            cov_j8718n3dm().s[67]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_j8718n3dm().b[36][2]++;
            cov_j8718n3dm().s[68]++;
            _.label++;
            /* istanbul ignore next */
            cov_j8718n3dm().s[69]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_j8718n3dm().b[36][3]++;
            cov_j8718n3dm().s[70]++;
            _.label++;
            /* istanbul ignore next */
            cov_j8718n3dm().s[71]++;
            y = op[1];
            /* istanbul ignore next */
            cov_j8718n3dm().s[72]++;
            op = [0];
            /* istanbul ignore next */
            cov_j8718n3dm().s[73]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_j8718n3dm().b[36][4]++;
            cov_j8718n3dm().s[74]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_j8718n3dm().s[75]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_j8718n3dm().s[76]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_j8718n3dm().b[36][5]++;
            cov_j8718n3dm().s[77]++;
            if (
            /* istanbul ignore next */
            (cov_j8718n3dm().b[38][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_j8718n3dm().b[39][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_j8718n3dm().b[39][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_j8718n3dm().b[38][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_j8718n3dm().b[38][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_j8718n3dm().b[37][0]++;
              cov_j8718n3dm().s[78]++;
              _ = 0;
              /* istanbul ignore next */
              cov_j8718n3dm().s[79]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_j8718n3dm().b[37][1]++;
            }
            cov_j8718n3dm().s[80]++;
            if (
            /* istanbul ignore next */
            (cov_j8718n3dm().b[41][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_j8718n3dm().b[41][1]++, !t) ||
            /* istanbul ignore next */
            (cov_j8718n3dm().b[41][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_j8718n3dm().b[41][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_j8718n3dm().b[40][0]++;
              cov_j8718n3dm().s[81]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_j8718n3dm().s[82]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_j8718n3dm().b[40][1]++;
            }
            cov_j8718n3dm().s[83]++;
            if (
            /* istanbul ignore next */
            (cov_j8718n3dm().b[43][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_j8718n3dm().b[43][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_j8718n3dm().b[42][0]++;
              cov_j8718n3dm().s[84]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_j8718n3dm().s[85]++;
              t = op;
              /* istanbul ignore next */
              cov_j8718n3dm().s[86]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_j8718n3dm().b[42][1]++;
            }
            cov_j8718n3dm().s[87]++;
            if (
            /* istanbul ignore next */
            (cov_j8718n3dm().b[45][0]++, t) &&
            /* istanbul ignore next */
            (cov_j8718n3dm().b[45][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_j8718n3dm().b[44][0]++;
              cov_j8718n3dm().s[88]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_j8718n3dm().s[89]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_j8718n3dm().s[90]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_j8718n3dm().b[44][1]++;
            }
            cov_j8718n3dm().s[91]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_j8718n3dm().b[46][0]++;
              cov_j8718n3dm().s[92]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_j8718n3dm().b[46][1]++;
            }
            cov_j8718n3dm().s[93]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_j8718n3dm().s[94]++;
            continue;
        }
        /* istanbul ignore next */
        cov_j8718n3dm().s[95]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_j8718n3dm().s[96]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_j8718n3dm().s[97]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_j8718n3dm().s[98]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_j8718n3dm().s[99]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_j8718n3dm().b[47][0]++;
      cov_j8718n3dm().s[100]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_j8718n3dm().b[47][1]++;
    }
    cov_j8718n3dm().s[101]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_j8718n3dm().b[48][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_j8718n3dm().b[48][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_j8718n3dm().s[102]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_j8718n3dm().s[103]++;
exports.DELETE = exports.PUT = exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_j8718n3dm().s[104]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_j8718n3dm().s[105]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_j8718n3dm().s[106]++, require("@/lib/auth"));
var auth_utils_1 =
/* istanbul ignore next */
(cov_j8718n3dm().s[107]++, require("@/lib/auth-utils"));
var rate_limit_1 =
/* istanbul ignore next */
(cov_j8718n3dm().s[108]++, require("@/lib/rate-limit"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_j8718n3dm().s[109]++, require("@/lib/unified-api-error-handler"));
/* istanbul ignore next */
cov_j8718n3dm().s[110]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_j8718n3dm().f[22]++;
  cov_j8718n3dm().s[111]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_j8718n3dm().f[23]++;
    var rateLimitResult, error, session, error, now, sessionToken, error, error, isAdmin, error_1, PrismaClient, prisma, user, error;
    var _a;
    /* istanbul ignore next */
    cov_j8718n3dm().s[112]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[24]++;
      cov_j8718n3dm().s[113]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][0]++;
          cov_j8718n3dm().s[114]++;
          rateLimitResult = rate_limit_1.rateLimiters.api.check(request);
          /* istanbul ignore next */
          cov_j8718n3dm().s[115]++;
          if (!rateLimitResult.allowed) {
            /* istanbul ignore next */
            cov_j8718n3dm().b[50][0]++;
            cov_j8718n3dm().s[116]++;
            error = new Error('Too many session validation requests');
            /* istanbul ignore next */
            cov_j8718n3dm().s[117]++;
            error.statusCode = 429;
            /* istanbul ignore next */
            cov_j8718n3dm().s[118]++;
            error.headers = {
              'X-RateLimit-Limit': rateLimitResult.limit.toString(),
              'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
              'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString(),
              'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
            };
            /* istanbul ignore next */
            cov_j8718n3dm().s[119]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_j8718n3dm().b[50][1]++;
          }
          cov_j8718n3dm().s[120]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][1]++;
          cov_j8718n3dm().s[121]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_j8718n3dm().s[122]++;
          if (!(
          /* istanbul ignore next */
          (cov_j8718n3dm().b[53][0]++, (_a =
          /* istanbul ignore next */
          (cov_j8718n3dm().b[55][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_j8718n3dm().b[55][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_j8718n3dm().b[54][0]++, void 0) :
          /* istanbul ignore next */
          (cov_j8718n3dm().b[54][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_j8718n3dm().b[53][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_j8718n3dm().b[52][0]++, void 0) :
          /* istanbul ignore next */
          (cov_j8718n3dm().b[52][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_j8718n3dm().b[51][0]++;
            cov_j8718n3dm().s[123]++;
            error = new Error('No active session');
            /* istanbul ignore next */
            cov_j8718n3dm().s[124]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_j8718n3dm().s[125]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_j8718n3dm().b[51][1]++;
          }
          cov_j8718n3dm().s[126]++;
          now = Math.floor(Date.now() / 1000);
          /* istanbul ignore next */
          cov_j8718n3dm().s[127]++;
          sessionToken = session;
          // Check if session has expired
          /* istanbul ignore next */
          cov_j8718n3dm().s[128]++;
          if (
          /* istanbul ignore next */
          (cov_j8718n3dm().b[57][0]++, sessionToken.exp) &&
          /* istanbul ignore next */
          (cov_j8718n3dm().b[57][1]++, sessionToken.exp < now)) {
            /* istanbul ignore next */
            cov_j8718n3dm().b[56][0]++;
            cov_j8718n3dm().s[129]++;
            error = new Error('Session expired');
            /* istanbul ignore next */
            cov_j8718n3dm().s[130]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_j8718n3dm().s[131]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_j8718n3dm().b[56][1]++;
          }
          // Check if session is too old (force refresh after 30 days)
          cov_j8718n3dm().s[132]++;
          if (
          /* istanbul ignore next */
          (cov_j8718n3dm().b[59][0]++, sessionToken.iat) &&
          /* istanbul ignore next */
          (cov_j8718n3dm().b[59][1]++, now - sessionToken.iat > 30 * 24 * 60 * 60)) {
            /* istanbul ignore next */
            cov_j8718n3dm().b[58][0]++;
            cov_j8718n3dm().s[133]++;
            error = new Error('Session too old, please re-authenticate');
            /* istanbul ignore next */
            cov_j8718n3dm().s[134]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_j8718n3dm().s[135]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_j8718n3dm().b[58][1]++;
          }
          cov_j8718n3dm().s[136]++;
          isAdmin = false;
          /* istanbul ignore next */
          cov_j8718n3dm().s[137]++;
          _b.label = 2;
        case 2:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][2]++;
          cov_j8718n3dm().s[138]++;
          _b.trys.push([2, 4,, 5]);
          /* istanbul ignore next */
          cov_j8718n3dm().s[139]++;
          return [4 /*yield*/, (0, auth_utils_1.isUserAdmin)(session.user.id)];
        case 3:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][3]++;
          cov_j8718n3dm().s[140]++;
          isAdmin = _b.sent();
          /* istanbul ignore next */
          cov_j8718n3dm().s[141]++;
          return [3 /*break*/, 5];
        case 4:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][4]++;
          cov_j8718n3dm().s[142]++;
          error_1 = _b.sent();
          /* istanbul ignore next */
          cov_j8718n3dm().s[143]++;
          console.error('Error checking admin status during session validation:', error_1);
          /* istanbul ignore next */
          cov_j8718n3dm().s[144]++;
          return [3 /*break*/, 5];
        case 5:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][5]++;
          cov_j8718n3dm().s[145]++;
          return [4 /*yield*/, Promise.resolve().then(function () {
            /* istanbul ignore next */
            cov_j8718n3dm().f[25]++;
            cov_j8718n3dm().s[146]++;
            return __importStar(require('@prisma/client'));
          })];
        case 6:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][6]++;
          cov_j8718n3dm().s[147]++;
          PrismaClient = _b.sent().PrismaClient;
          /* istanbul ignore next */
          cov_j8718n3dm().s[148]++;
          prisma = new PrismaClient();
          /* istanbul ignore next */
          cov_j8718n3dm().s[149]++;
          return [4 /*yield*/, prisma.user.findUnique({
            where: {
              id: session.user.id
            },
            select: {
              id: true,
              email: true,
              name: true,
              emailVerified: true,
              createdAt: true,
              updatedAt: true
            }
          })];
        case 7:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][7]++;
          cov_j8718n3dm().s[150]++;
          user = _b.sent();
          /* istanbul ignore next */
          cov_j8718n3dm().s[151]++;
          return [4 /*yield*/, prisma.$disconnect()];
        case 8:
          /* istanbul ignore next */
          cov_j8718n3dm().b[49][8]++;
          cov_j8718n3dm().s[152]++;
          _b.sent();
          /* istanbul ignore next */
          cov_j8718n3dm().s[153]++;
          if (!user) {
            /* istanbul ignore next */
            cov_j8718n3dm().b[60][0]++;
            cov_j8718n3dm().s[154]++;
            error = new Error('User not found');
            /* istanbul ignore next */
            cov_j8718n3dm().s[155]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_j8718n3dm().s[156]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_j8718n3dm().b[60][1]++;
          }
          // Return successful validation
          cov_j8718n3dm().s[157]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              valid: true,
              user: {
                id: user.id,
                email: user.email,
                name: user.name,
                emailVerified: user.emailVerified,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
              },
              isAdmin: isAdmin,
              sessionInfo: {
                sessionId:
                /* istanbul ignore next */
                (cov_j8718n3dm().b[61][0]++, sessionToken.sessionId) ||
                /* istanbul ignore next */
                (cov_j8718n3dm().b[61][1]++, null),
                issuedAt: sessionToken.iat,
                expiresAt: sessionToken.exp,
                lastActivity: now
              }
            }
          })];
      }
    });
  });
});
// Only allow GET requests
/* istanbul ignore next */
cov_j8718n3dm().s[158]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function () {
  /* istanbul ignore next */
  cov_j8718n3dm().f[26]++;
  cov_j8718n3dm().s[159]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_j8718n3dm().f[27]++;
    var error;
    /* istanbul ignore next */
    cov_j8718n3dm().s[160]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[28]++;
      cov_j8718n3dm().s[161]++;
      error = new Error('Method not allowed');
      /* istanbul ignore next */
      cov_j8718n3dm().s[162]++;
      error.statusCode = 405;
      /* istanbul ignore next */
      cov_j8718n3dm().s[163]++;
      throw error;
    });
  });
});
/* istanbul ignore next */
cov_j8718n3dm().s[164]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function () {
  /* istanbul ignore next */
  cov_j8718n3dm().f[29]++;
  cov_j8718n3dm().s[165]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_j8718n3dm().f[30]++;
    var error;
    /* istanbul ignore next */
    cov_j8718n3dm().s[166]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[31]++;
      cov_j8718n3dm().s[167]++;
      error = new Error('Method not allowed');
      /* istanbul ignore next */
      cov_j8718n3dm().s[168]++;
      error.statusCode = 405;
      /* istanbul ignore next */
      cov_j8718n3dm().s[169]++;
      throw error;
    });
  });
});
/* istanbul ignore next */
cov_j8718n3dm().s[170]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function () {
  /* istanbul ignore next */
  cov_j8718n3dm().f[32]++;
  cov_j8718n3dm().s[171]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_j8718n3dm().f[33]++;
    var error;
    /* istanbul ignore next */
    cov_j8718n3dm().s[172]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_j8718n3dm().f[34]++;
      cov_j8718n3dm().s[173]++;
      error = new Error('Method not allowed');
      /* istanbul ignore next */
      cov_j8718n3dm().s[174]++;
      error.statusCode = 405;
      /* istanbul ignore next */
      cov_j8718n3dm().s[175]++;
      throw error;
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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