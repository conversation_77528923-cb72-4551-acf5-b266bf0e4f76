{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/lib/ai-optimization.test.ts", "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE,cAAM,OAAA,CAAC;IAC/C,aAAa,EAAE;QACb,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,6BAA6B,EAAE,IAAI,CAAC,EAAE,EAAE;QACxC,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;KACtC;IACD,eAAe,EAAE;QACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB;CACF,CAAC,EAX8C,CAW9C,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,cAAM,OAAA,CAAC;IAC9C,YAAY,EAAE;QACZ,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB;CACF,CAAC,EAN6C,CAM7C,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,uCAAuC,EAAE,cAAM,OAAA,CAAC;IACxD,oBAAoB,EAAE;QACpB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;YACzB,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,CAAC;YACT,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,CAAC;YACT,mBAAmB,EAAE,GAAG;SACzB,CAAC,EANwB,CAMxB,CAAC;KACJ;CACF,CAAC,EAZuD,CAYvD,CAAC,CAAC;AApCJ,mEAAgE;AAsChE,QAAQ,CAAC,wCAAwC,EAAE;IACjD,IAAI,oBAAyD,CAAC;IAC9D,IAAI,cAAyB,CAAC;IAE9B,UAAU,CAAC;QACT,oBAAoB,GAAG,IAAI,mCAAmC,CAAC;YAC7D,wBAAwB,EAAE,IAAI;YAC9B,4BAA4B,EAAE,IAAI;YAClC,uBAAuB,EAAE,KAAK,EAAE,sBAAsB;YACtD,mBAAmB,EAAE,IAAI;YACzB,mBAAmB,EAAE,IAAI;SAC1B,CAAC,CAAC;QAEH,cAAc,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;YAC3C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,uEAAuE,EAAE;;;;;wBACpE,UAAU,GAAG,8DAA8D,CAAC;wBAGlE,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAA;;wBAAxG,OAAO,GAAG,SAA8F;wBAG9F,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAA;;wBAAxG,OAAO,GAAG,SAA8F;wBAE9G,wCAAwC;wBACxC,MAAM,CAAC,cAAc,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBAE3B,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,CAAC;wBAClD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aACzC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE;;;;;wBAC/D,UAAU,GAAG,8DAA8D,CAAC;wBAElF,gBAAgB;wBAChB,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAA;;wBAD9F,gBAAgB;wBAChB,SAA8F,CAAC;wBAGzF,kBAAkB,GAAG,IAAI,mCAAmC,CAAC;4BACjE,mBAAmB,EAAE,CAAC,EAAE,aAAa;yBACtC,CAAC,CAAC;wBAEH,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,EAAvB,CAAuB,CAAC,EAAA;;wBAArD,SAAqD,CAAC;wBAEtD,sCAAsC;wBACtC,qBAAM,kBAAkB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAA;;wBAD5F,sCAAsC;wBACtC,SAA4F,CAAC;wBAE7F,MAAM,CAAC,cAAc,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;;;;aACjD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE;QAC5C,EAAE,CAAC,kDAAkD,EAAE;;;;;wBAC/C,WAAW,GAAG,8DAA8D,CAAC;wBAC7E,WAAW,GAAG,8DAA8D,CAAC;wBAGnE,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,WAAW,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAA;;wBAAzG,OAAO,GAAG,SAA+F;wBAG/F,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,WAAW,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAA;;wBAAzG,OAAO,GAAG,SAA+F;wBAE/G,kDAAkD;wBAClD,MAAM,CAAC,cAAc,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBAE3B,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,CAAC;wBAClD,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC5C,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;;;gBAC7C,OAAO,GAAG,IAAI,mCAAmC,EAAE,CAAC;gBAGpD,WAAW,GAAI,OAAe,CAAC,mBAAmB,CAAC,+BAA+B,CAAC,CAAC;gBACpF,WAAW,GAAI,OAAe,CAAC,mBAAmB,CAAC,+BAA+B,CAAC,CAAC;gBAE1F,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;;aACvC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,+CAA+C,EAAE;;;;;wBAE5C,UAAU,GAAG,yCAAyC,CAAC;wBAE7D,cAAc,CAAC,iBAAiB,CAAC;4BAC/B,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,eAAe,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,EAAE;yBACrE,CAAC,CAAC;wBAGa,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE;gCACxF,MAAM,EAAE,OAAO;gCACf,oBAAoB,EAAE,IAAI;6BAC3B,CAAC,EAAA;;wBAHI,OAAO,GAAG,SAGd;wBAGc,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE;gCACxF,MAAM,EAAE,OAAO;gCACf,oBAAoB,EAAE,IAAI;6BAC3B,CAAC,EAAA;;wBAHI,OAAO,GAAG,SAGd;wBAEF,mDAAmD;wBACnD,MAAM,CAAC,cAAc,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBACjC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAE7B,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,CAAC;wBAClD,+FAA+F;wBAC/F,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC5C,CAAC,CAAC;QAEH,EAAE,CAAC,0EAA0E,EAAE;;;;;wBAEvE,gBAAgB,GAAG,IAAI,mCAAmC,CAAC;4BAC/D,wBAAwB,EAAE,KAAK;4BAC/B,4BAA4B,EAAE,IAAI;4BAClC,uBAAuB,EAAE,KAAK;4BAC9B,mBAAmB,EAAE,IAAI;yBAC1B,CAAC,CAAC;wBAEG,UAAU,GAAG,yCAAyC,CAAC;wBAE7D,cAAc,CAAC,iBAAiB,CAAC;4BAC/B,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,eAAe,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,EAAE;yBACrE,CAAC,CAAC;wBAGa,qBAAM,gBAAgB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE;gCACpF,MAAM,EAAE,OAAO;gCACf,oBAAoB,EAAE,IAAI;6BAC3B,CAAC,EAAA;;wBAHI,OAAO,GAAG,SAGd;wBAGc,qBAAM,gBAAgB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE;gCACpF,MAAM,EAAE,OAAO;gCACf,oBAAoB,EAAE,IAAI;6BAC3B,CAAC,EAAA;;wBAHI,OAAO,GAAG,SAGd;wBAEF,mDAAmD;wBACnD,MAAM,CAAC,cAAc,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBACjC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAE7B,OAAO,GAAG,gBAAgB,CAAC,UAAU,EAAE,CAAC;wBAC9C,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAC5C,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC5C,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE;;;;;wBAExD,gBAAgB,GAAG,IAAI,mCAAmC,CAAC;4BAC/D,wBAAwB,EAAE,KAAK;4BAC/B,4BAA4B,EAAE,IAAI;4BAClC,uBAAuB,EAAE,KAAK;4BAC9B,mBAAmB,EAAE,IAAI;yBAC1B,CAAC,CAAC;wBAGG,UAAU,GAAG,8CAA8C,CAAC;wBAElE,cAAc,CAAC,iBAAiB,CAAC;4BAC/B,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,QAAQ,EAAE,wBAAwB,EAAE;yBAC7C,CAAC,CAAC;wBAEH,2BAA2B;wBAC3B,qBAAM,gBAAgB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE;gCACpE,MAAM,EAAE,OAAO;gCACf,oBAAoB,EAAE,IAAI;6BAC3B,CAAC,EAAA;;wBAJF,2BAA2B;wBAC3B,SAGE,CAAC;wBAEH,iCAAiC;wBACjC,qBAAM,gBAAgB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,EAAE;gCACpE,MAAM,EAAE,OAAO;gCACf,oBAAoB,EAAE,IAAI;6BAC3B,CAAC,EAAA;;wBAJF,iCAAiC;wBACjC,SAGE,CAAC;wBAEH,2EAA2E;wBAC3E,MAAM,CAAC,cAAc,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAE1C,OAAO,GAAG,gBAAgB,CAAC,UAAU,EAAE,CAAC;wBAC9C,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC7C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,oCAAoC,EAAE;;;;;wBACjC,UAAU,GAAG,8BAA8B,CAAC;wBAElD,sEAAsE;wBACtE,cAAc,CAAC,kBAAkB,CAAC;;;4CAChC,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,EAAvB,CAAuB,CAAC,EAAA;;wCAArD,SAAqD,CAAC;wCACtD,sBAAO;gDACL,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE;6CAClC,EAAC;;;6BACH,CAAC,CAAC;wBAEH,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,CAAC,EAAA;;wBAAzE,SAAyE,CAAC;wBAC1E,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,cAAc,CAAC,EAAA;;wBAAzE,SAAyE,CAAC;wBAEpE,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,CAAC;wBAElD,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACtC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACxC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aACxD,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;;wBACrC,UAAU,GAAG,kCAAkC,CAAC;wBAGhD,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC;4BACpD,OAAA,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAApC,CAAoC,EAAE,GAAG,CAAC,EAA3D,CAA2D,CAAC;wBAAnF,CAAmF,CACpF,CAAC;wBAEF,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAA;;wBAA3E,SAA2E,CAAC;wBAC5E,qBAAM,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAA;;wBAA3E,SAA2E,CAAC;wBAEtE,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,CAAC;wBAClD,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aACzD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,sBAAsB,EAAE;IAC/B,IAAI,gBAAoC,CAAC;IAEzC,UAAU,CAAC;QACT,gBAAgB,GAAG,IAAI,yCAAkB,CAAC;YACxC,2BAA2B,EAAE,IAAI;YACjC,wBAAwB,EAAE,IAAI;YAC9B,2BAA2B,EAAE,IAAI;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE;QACvC,EAAE,CAAC,uDAAuD,EAAE;;;;;wBACpD,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC,aAAa,CAAC;wBAChF,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;4BACnD,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE;yBAC7C,CAAC,CAAC;wBAEY,qBAAM,gBAAgB,CAAC,gBAAgB,CACpD,CAAC,YAAY,CAAC,EACd,oBAAoB,EACpB,WAAW,EACX;gCACE,MAAM,EAAE,WAAW;gCACnB,QAAQ,EAAE,QAAQ;gCAClB,mBAAmB,EAAE,IAAI;6BAC1B,CACF,EAAA;;wBATK,MAAM,GAAG,SASd;wBAED,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBACrD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACjD,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,iBAAiB,GAAG,OAAO,CAAC,uCAAuC,CAAC,CAAC,oBAAoB,CAAC;wBAChG,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC;4BACtC,SAAS,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;yBACnC,CAAC,CAAC;wBAEY,qBAAM,gBAAgB,CAAC,gBAAgB,CACpD,CAAC,YAAY,CAAC,EACd,oBAAoB,EACpB,WAAW,EACX;gCACE,MAAM,EAAE,WAAW;gCACnB,aAAa,EAAE,IAAI;6BACpB,CACF,EAAA;;wBARK,MAAM,GAAG,SAQd;wBAED,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBAC7C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAC7C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE;QAC9C,EAAE,CAAC,gDAAgD,EAAE;;;;;wBAC7C,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC,aAAa,CAAC;wBAChF,iBAAiB,CAAC,6BAA6B,CAAC,iBAAiB,CAAC;4BAChE,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,eAAe,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,EAAE;yBAC1E,CAAC,CAAC;wBAEY,qBAAM,gBAAgB,CAAC,6BAA6B,CACjE,CAAC,YAAY,EAAE,OAAO,CAAC,EACvB,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,EAChD;gCACE,MAAM,EAAE,WAAW;gCACnB,QAAQ,EAAE,MAAM;gCAChB,oBAAoB,EAAE,IAAI,EAAE,kCAAkC;6BAC/D,CACF,EAAA;;wBARK,MAAM,GAAG,SAQd;wBAED,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACjD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE;QAC3C,EAAE,CAAC,gEAAgE,EAAE;;;;;wBAC7D,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC,aAAa,CAAC;wBAChF,iBAAiB,CAAC,0BAA0B,CAAC,iBAAiB,CAAC;4BAC7D,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,wBAAwB,EAAE,0BAA0B,CAAC,EAAE;yBAC5E,CAAC,CAAC;wBAEY,qBAAM,gBAAgB,CAAC,0BAA0B,CAC9D;gCACE,WAAW,EAAE,YAAY;gCACzB,UAAU,EAAE,oBAAoB;gCAChC,eAAe,EAAE,WAAW;6BAC7B,EACD;gCACE,MAAM,EAAE,WAAW;gCACnB,oBAAoB,EAAE,IAAI,EAAE,wCAAwC;6BACrE,CACF,EAAA;;wBAVK,MAAM,GAAG,SAUd;wBAED,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;;;;aACtD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,mDAAmD,EAAE;YACtD,IAAM,OAAO,GAAG,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;YAE1D,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/lib/ai-optimization.test.ts"], "sourcesContent": ["/**\n * Phase 2 AI Service Call Optimization Tests\n * \n * Tests for advanced request deduplication, semantic matching,\n * cross-user optimization, and performance improvements.\n */\n\nimport { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';\nimport { OptimizedAIService } from '@/lib/optimized-ai-service';\n\n// Mock dependencies\njest.mock('@/lib/services/geminiService', () => ({\n  geminiService: {\n    analyzeSkillsGap: jest.fn(),\n    generateCareerRecommendations: jest.fn(),\n    generateInterviewQuestions: jest.fn(),\n  },\n  AIServiceLogger: {\n    info: jest.fn(),\n    debug: jest.fn(),\n    error: jest.fn(),\n  },\n}));\n\njest.mock('@/lib/services/cacheService', () => ({\n  cacheService: {\n    getJSON: jest.fn(),\n    setJSON: jest.fn(),\n    generateAIKey: jest.fn(),\n  },\n}));\n\njest.mock('@/lib/services/enhanced-cache-service', () => ({\n  enhancedCacheService: {\n    get: jest.fn(),\n    set: jest.fn(),\n    getMetrics: jest.fn(() => ({\n      l1Hits: 10,\n      l2Hits: 5,\n      sharedCacheHits: 3,\n      misses: 2,\n      averageResponseTime: 150,\n    })),\n  },\n}));\n\ndescribe('Advanced Request Deduplication Service', () => {\n  let deduplicationService: AdvancedRequestDeduplicationService;\n  let mockAIFunction: jest.Mock;\n\n  beforeEach(() => {\n    deduplicationService = new AdvancedRequestDeduplicationService({\n      enableSemanticSimilarity: true,\n      enableCrossUserDeduplication: true,\n      enablePredictiveWarming: false, // Disable for testing\n      similarityThreshold: 0.85,\n      deduplicationWindow: 5000,\n    });\n\n    mockAIFunction = jest.fn().mockResolvedValue({\n      success: true,\n      data: { analysis: 'test result' },\n    });\n\n    jest.clearAllMocks();\n  });\n\n  describe('Exact Deduplication', () => {\n    it('should deduplicate identical requests within the deduplication window', async () => {\n      const requestKey = 'skills-analysis:software_engineer:mid_level:javascript,react';\n\n      // First request\n      const result1 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });\n\n      // Second identical request should be deduplicated\n      const result2 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });\n\n      // Should only call the AI function once\n      expect(mockAIFunction).toHaveBeenCalledTimes(1);\n      expect(result1).toEqual(result2);\n\n      const metrics = deduplicationService.getMetrics();\n      expect(metrics.exactDuplicates).toBe(1);\n    });\n\n    it('should not deduplicate requests outside the deduplication window', async () => {\n      const requestKey = 'skills-analysis:software_engineer:mid_level:javascript,react';\n      \n      // First request\n      await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });\n      \n      // Wait for deduplication window to expire (simulate with shorter window)\n      const shortWindowService = new AdvancedRequestDeduplicationService({\n        deduplicationWindow: 1, // 1ms window\n      });\n      \n      await new Promise(resolve => setTimeout(resolve, 10));\n      \n      // Second request after window expires\n      await shortWindowService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });\n      \n      expect(mockAIFunction).toHaveBeenCalledTimes(2);\n    });\n  });\n\n  describe('Semantic Similarity Deduplication', () => {\n    it('should deduplicate semantically similar requests', async () => {\n      const requestKey1 = 'skills-analysis:software engineer:mid level:javascript,react';\n      const requestKey2 = 'skills-analysis:software_engineer:mid_level:javascript,react';\n\n      // First request\n      const result1 = await deduplicationService.deduplicateRequest(requestKey1, mockAIFunction, { userId: 'user1' });\n\n      // Second semantically similar request should be deduplicated\n      const result2 = await deduplicationService.deduplicateRequest(requestKey2, mockAIFunction, { userId: 'user2' });\n\n      // Should deduplicate based on semantic similarity\n      expect(mockAIFunction).toHaveBeenCalledTimes(1);\n      expect(result1).toEqual(result2);\n\n      const metrics = deduplicationService.getMetrics();\n      expect(metrics.semanticDuplicates).toBe(1);\n    });\n\n    it('should normalize common career path variations', async () => {\n      const service = new AdvancedRequestDeduplicationService();\n      \n      // Test parameter normalization\n      const normalized1 = (service as any).normalizeParameters('software engineer:entry level');\n      const normalized2 = (service as any).normalizeParameters('software_engineer:entry_level');\n      \n      expect(normalized1).toBe(normalized2);\n    });\n  });\n\n  describe('Cross-User Deduplication', () => {\n    it('should deduplicate safe requests across users', async () => {\n      // Use identical request keys but different users to test cross-user deduplication\n      const requestKey = 'career-recommendations:javascript,react';\n\n      mockAIFunction.mockResolvedValue({\n        success: true,\n        data: { recommendations: ['Frontend Developer', 'React Developer'] },\n      });\n\n      // First user makes request\n      const result1 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, {\n        userId: 'user1',\n        enableCrossUserMatch: true\n      });\n\n      // Second user makes identical request\n      const result2 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, {\n        userId: 'user2',\n        enableCrossUserMatch: true\n      });\n\n      // Should reuse result for cross-user safe requests\n      expect(mockAIFunction).toHaveBeenCalledTimes(1);\n      expect(result1).toEqual(result2);\n      expect(result2.success).toBe(true);\n\n      const metrics = deduplicationService.getMetrics();\n      // When semantic similarity is enabled, identical requests are caught by semantic deduplication\n      expect(metrics.semanticDuplicates).toBe(1);\n    });\n\n    it('should use cross-user deduplication when semantic similarity is disabled', async () => {\n      // Create service with semantic similarity disabled but cross-user enabled\n      const crossUserService = new AdvancedRequestDeduplicationService({\n        enableSemanticSimilarity: false,\n        enableCrossUserDeduplication: true,\n        enablePredictiveWarming: false,\n        deduplicationWindow: 5000,\n      });\n\n      const requestKey = 'career-recommendations:javascript,react';\n\n      mockAIFunction.mockResolvedValue({\n        success: true,\n        data: { recommendations: ['Frontend Developer', 'React Developer'] },\n      });\n\n      // First user makes request\n      const result1 = await crossUserService.deduplicateRequest(requestKey, mockAIFunction, {\n        userId: 'user1',\n        enableCrossUserMatch: true\n      });\n\n      // Second user makes identical request\n      const result2 = await crossUserService.deduplicateRequest(requestKey, mockAIFunction, {\n        userId: 'user2',\n        enableCrossUserMatch: true\n      });\n\n      // Should reuse result for cross-user safe requests\n      expect(mockAIFunction).toHaveBeenCalledTimes(1);\n      expect(result1).toEqual(result2);\n      expect(result2.success).toBe(true);\n\n      const metrics = crossUserService.getMetrics();\n      expect(metrics.crossUserDuplicates).toBe(1);\n      expect(metrics.semanticDuplicates).toBe(0);\n    });\n\n    it('should not cross-user deduplicate sensitive request types', async () => {\n      // Create service with semantic similarity disabled to test cross-user logic specifically\n      const sensitiveService = new AdvancedRequestDeduplicationService({\n        enableSemanticSimilarity: false,\n        enableCrossUserDeduplication: true,\n        enablePredictiveWarming: false,\n        deduplicationWindow: 5000,\n      });\n\n      // Use a request type that is not in the safe list\n      const requestKey = 'skills-analysis:user_specific_data:sensitive';\n\n      mockAIFunction.mockResolvedValue({\n        success: true,\n        data: { analysis: 'user specific analysis' },\n      });\n\n      // First user makes request\n      await sensitiveService.deduplicateRequest(requestKey, mockAIFunction, {\n        userId: 'user1',\n        enableCrossUserMatch: true\n      });\n\n      // Second user makes same request\n      await sensitiveService.deduplicateRequest(requestKey, mockAIFunction, {\n        userId: 'user2',\n        enableCrossUserMatch: true\n      });\n\n      // Should call AI function twice for sensitive data (no cross-user sharing)\n      expect(mockAIFunction).toHaveBeenCalledTimes(2);\n\n      const metrics = sensitiveService.getMetrics();\n      expect(metrics.crossUserDuplicates).toBe(0);\n    });\n  });\n\n  describe('Performance Metrics', () => {\n    it('should track comprehensive metrics', async () => {\n      const requestKey = 'skills-analysis:test:metrics';\n\n      // Mock function with a small delay to ensure measurable response time\n      mockAIFunction.mockImplementation(async () => {\n        await new Promise(resolve => setTimeout(resolve, 10));\n        return {\n          success: true,\n          data: { analysis: 'test result' },\n        };\n      });\n\n      await deduplicationService.deduplicateRequest(requestKey, mockAIFunction);\n      await deduplicationService.deduplicateRequest(requestKey, mockAIFunction);\n\n      const metrics = deduplicationService.getMetrics();\n\n      expect(metrics.totalRequests).toBe(2);\n      expect(metrics.exactDuplicates).toBe(1);\n      expect(metrics.averageResponseTime).toBeGreaterThan(0);\n    });\n\n    it('should calculate deduplication savings', async () => {\n      const requestKey = 'skills-analysis:performance:test';\n      \n      // Simulate slow AI function\n      const slowMockFunction = jest.fn().mockImplementation(() => \n        new Promise(resolve => setTimeout(() => resolve({ success: true, data: {} }), 100))\n      );\n\n      await deduplicationService.deduplicateRequest(requestKey, slowMockFunction);\n      await deduplicationService.deduplicateRequest(requestKey, slowMockFunction);\n      \n      const metrics = deduplicationService.getMetrics();\n      expect(metrics.deduplicationSavings).toBeGreaterThan(0);\n    });\n  });\n});\n\ndescribe('Optimized AI Service', () => {\n  let optimizedService: OptimizedAIService;\n\n  beforeEach(() => {\n    optimizedService = new OptimizedAIService({\n      enableAdvancedDeduplication: true,\n      enableIntelligentCaching: true,\n      enablePerformanceMonitoring: true,\n    });\n\n    jest.clearAllMocks();\n  });\n\n  describe('Skills Analysis Optimization', () => {\n    it('should use advanced deduplication for skills analysis', async () => {\n      const mockGeminiService = require('@/lib/services/geminiService').geminiService;\n      mockGeminiService.analyzeSkillsGap.mockResolvedValue({\n        success: true,\n        data: { skillsGap: ['React', 'TypeScript'] },\n      });\n\n      const result = await optimizedService.analyzeSkillsGap(\n        ['JavaScript'],\n        'Frontend Developer',\n        'mid_level',\n        {\n          userId: 'test-user',\n          priority: 'medium',\n          enableDeduplication: true,\n        }\n      );\n\n      expect(result.success).toBe(true);\n      expect(result.metadata.source).toBe('deduplication');\n      expect(result.metadata.requestId).toBeDefined();\n    });\n\n    it('should handle cache hits properly', async () => {\n      const mockEnhancedCache = require('@/lib/services/enhanced-cache-service').enhancedCacheService;\n      mockEnhancedCache.get.mockResolvedValue({\n        skillsGap: ['React', 'TypeScript'],\n      });\n\n      const result = await optimizedService.analyzeSkillsGap(\n        ['JavaScript'],\n        'Frontend Developer',\n        'mid_level',\n        {\n          userId: 'test-user',\n          enableCaching: true,\n        }\n      );\n\n      expect(result.success).toBe(true);\n      expect(result.metadata.source).toBe('cache');\n      expect(result.metadata.cacheHit).toBe(true);\n    });\n  });\n\n  describe('Career Recommendations Optimization', () => {\n    it('should optimize career recommendation requests', async () => {\n      const mockGeminiService = require('@/lib/services/geminiService').geminiService;\n      mockGeminiService.generateCareerRecommendations.mockResolvedValue({\n        success: true,\n        data: { recommendations: ['Frontend Developer', 'Full Stack Developer'] },\n      });\n\n      const result = await optimizedService.generateCareerRecommendations(\n        ['JavaScript', 'React'],\n        { workStyle: 'remote', experience: 'mid_level' },\n        {\n          userId: 'test-user',\n          priority: 'high',\n          enableCrossUserMatch: true, // Safe for career recommendations\n        }\n      );\n\n      expect(result.success).toBe(true);\n      expect(result.metadata.requestId).toBeDefined();\n    });\n  });\n\n  describe('Interview Questions Optimization', () => {\n    it('should enable cross-user deduplication for interview questions', async () => {\n      const mockGeminiService = require('@/lib/services/geminiService').geminiService;\n      mockGeminiService.generateInterviewQuestions.mockResolvedValue({\n        success: true,\n        data: { questions: ['Tell me about yourself', 'What are your strengths?'] },\n      });\n\n      const result = await optimizedService.generateInterviewQuestions(\n        {\n          sessionType: 'BEHAVIORAL',\n          careerPath: 'Frontend Developer',\n          experienceLevel: 'mid_level',\n        },\n        {\n          userId: 'test-user',\n          enableCrossUserMatch: true, // Interview questions are safe to share\n        }\n      );\n\n      expect(result.success).toBe(true);\n      expect(result.metadata.source).toBe('deduplication');\n    });\n  });\n\n  describe('Metrics and Monitoring', () => {\n    it('should provide comprehensive optimization metrics', () => {\n      const metrics = optimizedService.getOptimizationMetrics();\n      \n      expect(metrics).toHaveProperty('deduplication');\n      expect(metrics).toHaveProperty('activeRequests');\n      expect(metrics).toHaveProperty('totalRequests');\n      expect(metrics).toHaveProperty('config');\n    });\n  });\n});\n"], "version": 3}