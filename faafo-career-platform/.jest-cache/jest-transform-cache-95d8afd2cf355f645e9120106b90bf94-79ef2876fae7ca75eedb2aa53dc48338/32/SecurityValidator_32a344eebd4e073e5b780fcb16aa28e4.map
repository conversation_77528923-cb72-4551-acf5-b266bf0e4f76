{"version": 3, "names": ["SecurityValidator", "cov_13eriynybl", "s", "f", "rateLimitMap", "Map", "securityIncidents", "SQL_INJECTION_PATTERNS", "XSS_PATTERNS", "NOSQL_INJECTION_PATTERNS", "PROTOTYPE_POLLUTION_PATTERNS", "config", "maxInputLength", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enableSqlInjectionDetection", "enableXssDetection", "enableRateLimiting", "rateLimitWindow", "rateLimitMaxRequests", "getInstance", "instance", "b", "prototype", "validateInput", "input", "context", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "sanitizedInput", "validateString", "Array", "isArray", "validate<PERSON><PERSON>y", "validateObject", "validatePrimitive", "error", "logSecurityIncident", "type", "timestamp", "Date", "request", "JSON", "stringify", "severity", "errorType", "security<PERSON><PERSON><PERSON>", "length", "concat", "threatType", "_i", "_a", "pattern", "test", "_b", "_c", "_d", "_e", "sanitized", "sanitizeString", "sanitizedArray", "i", "itemValidation", "push", "inputString", "sanitizedObject", "Object", "entries", "key", "value", "keyValidation", "valueValidation", "stringValue", "String", "trim", "replace", "containsXSS", "xssPatterns", "some", "validateJsonField", "jsonData", "fieldName", "jsonString", "templateInjectionPatterns", "templateInjectionPatterns_1", "sanitizeJsonObject", "obj", "_this", "map", "item", "sanitized<PERSON>ey", "validateRateLimit", "identifier", "now", "rateLimitData", "get", "set", "count", "windowStart", "incident", "console", "warn", "slice", "getSecurityStatistics", "last24Hours", "filter", "getTime", "incidentsByType", "reduce", "acc", "incidentsBySeverity", "totalIncidents", "rateLimitStatus", "activeIdentifiers", "size", "clearRateLimitData", "clear", "updateConfig", "newConfig", "__assign", "exports", "securityValidator"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security/SecurityValidator.ts"], "sourcesContent": ["import { performance } from 'perf_hooks';\n\nexport interface SecurityValidationResult {\n  isValid: boolean;\n  errorType?: 'SECURITY_ERROR' | 'VALIDATION_ERROR' | 'RESOURCE_ERROR';\n  error?: string;\n  securityAlert?: boolean;\n  sanitizedInput?: any;\n  threatType?: string;\n}\n\nexport interface SecurityConfig {\n  maxInputLength: number;\n  maxArrayLength: number;\n  enableSqlInjectionDetection: boolean;\n  enableXssDetection: boolean;\n  enableRateLimiting: boolean;\n  rateLimitWindow: number; // milliseconds\n  rateLimitMaxRequests: number;\n}\n\nexport interface SecurityIncident {\n  type: string;\n  userId?: string;\n  timestamp: Date;\n  request: string;\n  ipAddress?: string;\n  userAgent?: string;\n  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n}\n\n/**\n * Comprehensive security validator for skill gap analyzer\n */\nexport class SecurityValidator {\n  private static instance: SecurityValidator;\n  private config: SecurityConfig;\n  private rateLimitMap: Map<string, { count: number; windowStart: number }> = new Map();\n  private securityIncidents: SecurityIncident[] = [];\n\n  // Security patterns - using simple string patterns for better compatibility\n  private readonly SQL_INJECTION_PATTERNS = [\n    /DROP\\s+TABLE/i,\n    /DELETE\\s+FROM/i,\n    /INSERT\\s+INTO/i,\n    /UPDATE\\s+SET/i,\n    /UNION\\s+SELECT/i,\n    /OR\\s+1\\s*=\\s*1/i,\n    /AND\\s+1\\s*=\\s*1/i,\n    /'\\s*OR\\s*'/i,\n    /--/,\n    /\\/\\*/,\n    /\\*\\//\n  ];\n\n  private readonly XSS_PATTERNS = [\n    /<script[^>]*>.*?<\\/script>/gi,\n    /<iframe[^>]*>.*?<\\/iframe>/gi,\n    /<object[^>]*>.*?<\\/object>/gi,\n    /<embed[^>]*>/gi,\n    /<link[^>]*>/gi,\n    /<meta[^>]*>/gi,\n    /javascript:/gi,\n    /vbscript:/gi,\n    /data:text\\/html/gi,\n    /on\\w+\\s*=/gi, // Event handlers like onclick, onload, etc.\n    /<img[^>]*src[^>]*>/gi,\n    /<svg[^>]*>.*?<\\/svg>/gi\n  ];\n\n  private readonly NOSQL_INJECTION_PATTERNS = [\n    /\\$ne\\s*:/gi,\n    /\\$gt\\s*:/gi,\n    /\\$lt\\s*:/gi,\n    /\\$regex\\s*:/gi,\n    /\\$where\\s*:/gi,\n    /\\$or\\s*:/gi,\n    /\\$and\\s*:/gi,\n    /\\$in\\s*:/gi,\n    /\\$nin\\s*:/gi\n  ];\n\n  private readonly PROTOTYPE_POLLUTION_PATTERNS = [\n    /__proto__/gi,\n    /constructor/gi,\n    /prototype/gi\n  ];\n\n  private constructor() {\n    this.config = {\n      maxInputLength: 1000,\n      maxArrayLength: 100,\n      enableSqlInjectionDetection: true,\n      enableXssDetection: true,\n      enableRateLimiting: true,\n      rateLimitWindow: 60000, // 1 minute\n      rateLimitMaxRequests: 100\n    };\n  }\n\n  public static getInstance(): SecurityValidator {\n    if (!SecurityValidator.instance) {\n      SecurityValidator.instance = new SecurityValidator();\n    }\n    return SecurityValidator.instance;\n  }\n\n  /**\n   * Comprehensive input validation\n   */\n  validateInput(input: any, context: string = 'general'): SecurityValidationResult {\n    try {\n      // Check for null/undefined\n      if (input === null || input === undefined) {\n        return { isValid: true, sanitizedInput: input };\n      }\n\n      // Handle different input types\n      if (typeof input === 'string') {\n        return this.validateString(input, context);\n      } else if (Array.isArray(input)) {\n        return this.validateArray(input, context);\n      } else if (typeof input === 'object') {\n        return this.validateObject(input, context);\n      } else {\n        return this.validatePrimitive(input, context);\n      }\n    } catch (error) {\n      this.logSecurityIncident({\n        type: 'VALIDATION_ERROR',\n        timestamp: new Date(),\n        request: JSON.stringify(input),\n        severity: 'MEDIUM'\n      });\n\n      return {\n        isValid: false,\n        errorType: 'SECURITY_ERROR',\n        error: 'Input validation failed',\n        securityAlert: true\n      };\n    }\n  }\n\n  /**\n   * Validate string inputs\n   */\n  private validateString(input: string, context: string): SecurityValidationResult {\n    // Length validation\n    if (input.length > this.config.maxInputLength) {\n      this.logSecurityIncident({\n        type: 'DOS_ATTEMPT',\n        timestamp: new Date(),\n        request: `Input too long: ${input.length} characters`,\n        severity: 'HIGH'\n      });\n\n      return {\n        isValid: false,\n        errorType: 'VALIDATION_ERROR',\n        error: 'Input too long',\n        securityAlert: true,\n        threatType: 'DOS_ATTEMPT'\n      };\n    }\n\n    // SQL Injection detection\n    if (this.config.enableSqlInjectionDetection) {\n      for (const pattern of this.SQL_INJECTION_PATTERNS) {\n        if (pattern.test(input)) {\n          this.logSecurityIncident({\n            type: 'SQL_INJECTION_ATTEMPT',\n            timestamp: new Date(),\n            request: input,\n            severity: 'CRITICAL'\n          });\n\n          return {\n            isValid: false,\n            errorType: 'SECURITY_ERROR',\n            error: 'Malicious input detected',\n            securityAlert: true,\n            threatType: 'SQL_INJECTION'\n          };\n        }\n      }\n    }\n\n    // XSS detection\n    if (this.config.enableXssDetection) {\n      for (const pattern of this.XSS_PATTERNS) {\n        if (pattern.test(input)) {\n          this.logSecurityIncident({\n            type: 'XSS_ATTEMPT',\n            timestamp: new Date(),\n            request: input,\n            severity: 'HIGH'\n          });\n\n          return {\n            isValid: false,\n            errorType: 'SECURITY_ERROR',\n            error: 'Malicious input detected',\n            securityAlert: true,\n            threatType: 'XSS'\n          };\n        }\n      }\n    }\n\n    // NoSQL Injection detection\n    for (const pattern of this.NOSQL_INJECTION_PATTERNS) {\n      if (pattern.test(input)) {\n        this.logSecurityIncident({\n          type: 'NOSQL_INJECTION_ATTEMPT',\n          timestamp: new Date(),\n          request: input,\n          severity: 'HIGH'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious input detected',\n          securityAlert: true,\n          threatType: 'NOSQL_INJECTION'\n        };\n      }\n    }\n\n    // Sanitize the input\n    const sanitized = this.sanitizeString(input);\n\n    return {\n      isValid: true,\n      sanitizedInput: sanitized\n    };\n  }\n\n  /**\n   * Validate array inputs\n   */\n  private validateArray(input: any[], context: string): SecurityValidationResult {\n    // Length validation\n    if (input.length > this.config.maxArrayLength) {\n      this.logSecurityIncident({\n        type: 'DOS_ATTEMPT',\n        timestamp: new Date(),\n        request: `Array too large: ${input.length} items`,\n        severity: 'HIGH'\n      });\n\n      return {\n        isValid: false,\n        errorType: 'VALIDATION_ERROR',\n        error: 'Too many skills',\n        securityAlert: true,\n        threatType: 'DOS_ATTEMPT'\n      };\n    }\n\n    // Validate each item in the array\n    const sanitizedArray = [];\n    for (let i = 0; i < input.length; i++) {\n      const itemValidation = this.validateInput(input[i], `${context}[${i}]`);\n      \n      if (!itemValidation.isValid) {\n        // If any item is malicious, reject the entire array\n        this.logSecurityIncident({\n          type: 'ARRAY_INJECTION_ATTEMPT',\n          timestamp: new Date(),\n          request: JSON.stringify(input),\n          severity: 'HIGH'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious input detected in array',\n          securityAlert: true,\n          threatType: 'ARRAY_INJECTION'\n        };\n      }\n\n      sanitizedArray.push(itemValidation.sanitizedInput);\n    }\n\n    return {\n      isValid: true,\n      sanitizedInput: sanitizedArray\n    };\n  }\n\n  /**\n   * Validate object inputs\n   */\n  private validateObject(input: any, context: string): SecurityValidationResult {\n    // Check for prototype pollution\n    for (const pattern of this.PROTOTYPE_POLLUTION_PATTERNS) {\n      const inputString = JSON.stringify(input);\n      if (pattern.test(inputString)) {\n        this.logSecurityIncident({\n          type: 'PROTOTYPE_POLLUTION_ATTEMPT',\n          timestamp: new Date(),\n          request: inputString,\n          severity: 'CRITICAL'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious object structure detected',\n          securityAlert: true,\n          threatType: 'PROTOTYPE_POLLUTION'\n        };\n      }\n    }\n\n    // Validate each property\n    const sanitizedObject: any = {};\n    for (const [key, value] of Object.entries(input)) {\n      const keyValidation = this.validateInput(key, `${context}.key`);\n      const valueValidation = this.validateInput(value, `${context}.${key}`);\n\n      if (!keyValidation.isValid || !valueValidation.isValid) {\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious input detected in object',\n          securityAlert: true\n        };\n      }\n\n      sanitizedObject[keyValidation.sanitizedInput || key] = valueValidation.sanitizedInput;\n    }\n\n    return {\n      isValid: true,\n      sanitizedInput: sanitizedObject\n    };\n  }\n\n  /**\n   * Validate primitive inputs\n   */\n  private validatePrimitive(input: any, context: string): SecurityValidationResult {\n    // Convert to string and validate\n    const stringValue = String(input);\n    return this.validateString(stringValue, context);\n  }\n\n  /**\n   * Sanitize string input\n   */\n  private sanitizeString(input: string): string {\n    return input\n      .trim()\n      .replace(/[<>]/g, '') // Remove angle brackets\n      .replace(/javascript:/gi, '') // Remove javascript: protocol\n      .replace(/vbscript:/gi, '') // Remove vbscript: protocol\n      .replace(/data:text\\/html/gi, '') // Remove data URLs\n      .replace(/on\\w+\\s*=/gi, ''); // Remove event handlers\n  }\n\n  /**\n   * Check if input contains XSS patterns\n   */\n  static containsXSS(input: string): boolean {\n    if (!input || typeof input !== 'string') {\n      return false;\n    }\n\n    const xssPatterns = [\n      /<script[^>]*>.*?<\\/script>/gi,\n      /<iframe[^>]*>.*?<\\/iframe>/gi,\n      /<object[^>]*>.*?<\\/object>/gi,\n      /<embed[^>]*>/gi,\n      /<link[^>]*>/gi,\n      /<meta[^>]*>/gi,\n      /javascript:/gi,\n      /vbscript:/gi,\n      /data:text\\/html/gi,\n      /on\\w+\\s*=/gi, // Event handlers like onclick, onload, etc.\n      /<img[^>]*src[^>]*>/gi,\n      /<svg[^>]*>.*?<\\/svg>/gi\n    ];\n\n    return xssPatterns.some(pattern => pattern.test(input));\n  }\n\n  /**\n   * Validate and sanitize JSON fields\n   */\n  static validateJsonField(jsonData: any, fieldName: string): SecurityValidationResult {\n    try {\n      if (!jsonData) {\n        return { isValid: true, sanitizedInput: jsonData };\n      }\n\n      const jsonString = JSON.stringify(jsonData);\n\n      // Check for XSS in JSON\n      if (this.containsXSS(jsonString)) {\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: `XSS detected in ${fieldName}`,\n          securityAlert: true,\n          threatType: 'XSS'\n        };\n      }\n\n      // Check for template injection\n      const templateInjectionPatterns = [\n        /\\$\\{[^}]*\\}/g, // ${...} template literals\n        /\\{\\{[^}]*\\}\\}/g, // {{...}} handlebars/mustache\n        /%\\{[^}]*\\}/g, // %{...} ruby-style\n      ];\n\n      for (const pattern of templateInjectionPatterns) {\n        if (pattern.test(jsonString)) {\n          return {\n            isValid: false,\n            errorType: 'SECURITY_ERROR',\n            error: `Template injection detected in ${fieldName}`,\n            securityAlert: true,\n            threatType: 'TEMPLATE_INJECTION'\n          };\n        }\n      }\n\n      // Recursively sanitize object properties\n      const sanitized = this.sanitizeJsonObject(jsonData);\n\n      return {\n        isValid: true,\n        sanitizedInput: sanitized\n      };\n    } catch (error) {\n      return {\n        isValid: false,\n        errorType: 'VALIDATION_ERROR',\n        error: `Invalid JSON in ${fieldName}`,\n        securityAlert: false\n      };\n    }\n  }\n\n  /**\n   * Recursively sanitize JSON object\n   */\n  private static sanitizeJsonObject(obj: any): any {\n    if (obj === null || obj === undefined) {\n      return obj;\n    }\n\n    if (typeof obj === 'string') {\n      // Sanitize string values\n      return obj\n        .replace(/<script[^>]*>.*?<\\/script>/gi, '')\n        .replace(/javascript:/gi, '')\n        .replace(/vbscript:/gi, '')\n        .replace(/on\\w+\\s*=/gi, '')\n        .replace(/\\$\\{[^}]*\\}/g, '') // Remove template literals\n        .trim();\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(item => this.sanitizeJsonObject(item));\n    }\n\n    if (typeof obj === 'object') {\n      const sanitized: any = {};\n      for (const [key, value] of Object.entries(obj)) {\n        // Sanitize both key and value\n        const sanitizedKey = typeof key === 'string' ?\n          key.replace(/[<>]/g, '').replace(/javascript:/gi, '') : key;\n        sanitized[sanitizedKey] = this.sanitizeJsonObject(value);\n      }\n      return sanitized;\n    }\n\n    return obj;\n  }\n\n  /**\n   * Rate limiting validation\n   */\n  validateRateLimit(identifier: string): SecurityValidationResult {\n    if (!this.config.enableRateLimiting) {\n      return { isValid: true };\n    }\n\n    const now = Date.now();\n    const rateLimitData = this.rateLimitMap.get(identifier);\n\n    if (!rateLimitData) {\n      // First request from this identifier\n      this.rateLimitMap.set(identifier, { count: 1, windowStart: now });\n      return { isValid: true };\n    }\n\n    // Check if we're still in the same window\n    if (now - rateLimitData.windowStart < this.config.rateLimitWindow) {\n      if (rateLimitData.count >= this.config.rateLimitMaxRequests) {\n        this.logSecurityIncident({\n          type: 'RATE_LIMIT_EXCEEDED',\n          timestamp: new Date(),\n          request: `Identifier: ${identifier}, Count: ${rateLimitData.count}`,\n          severity: 'MEDIUM'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'RESOURCE_ERROR',\n          error: 'Rate limit exceeded',\n          securityAlert: true,\n          threatType: 'RATE_LIMIT_EXCEEDED'\n        };\n      }\n\n      // Increment count\n      rateLimitData.count++;\n    } else {\n      // New window\n      this.rateLimitMap.set(identifier, { count: 1, windowStart: now });\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Log security incident\n   */\n  private logSecurityIncident(incident: Omit<SecurityIncident, 'timestamp'> & { timestamp: Date }): void {\n    this.securityIncidents.push(incident);\n\n    // Log to console for monitoring\n    console.warn('SECURITY_ALERT', incident);\n\n    // Keep only last 1000 incidents to prevent memory issues\n    if (this.securityIncidents.length > 1000) {\n      this.securityIncidents = this.securityIncidents.slice(-1000);\n    }\n  }\n\n  /**\n   * Get security statistics\n   */\n  getSecurityStatistics(): any {\n    const now = Date.now();\n    const last24Hours = this.securityIncidents.filter(\n      incident => now - incident.timestamp.getTime() < 24 * 60 * 60 * 1000\n    );\n\n    const incidentsByType = last24Hours.reduce((acc, incident) => {\n      acc[incident.type] = (acc[incident.type] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n\n    const incidentsBySeverity = last24Hours.reduce((acc, incident) => {\n      acc[incident.severity] = (acc[incident.severity] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n\n    return {\n      totalIncidents: this.securityIncidents.length,\n      last24Hours: last24Hours.length,\n      incidentsByType,\n      incidentsBySeverity,\n      rateLimitStatus: {\n        activeIdentifiers: this.rateLimitMap.size,\n        config: this.config\n      }\n    };\n  }\n\n  /**\n   * Clear rate limit data (for testing)\n   */\n  clearRateLimitData(): void {\n    this.rateLimitMap.clear();\n  }\n\n  /**\n   * Update security configuration\n   */\n  updateConfig(newConfig: Partial<SecurityConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n  }\n}\n\n// Export singleton instance\nexport const securityValidator = SecurityValidator.getInstance();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;;;AAGA,IAAAA,iBAAA;AAAA;AAAA,cAAAC,cAAA,GAAAC,CAAA;EAAA;EAAAD,cAAA,GAAAE,CAAA;EAsDE,SAAAH,kBAAA;IAAA;IAAAC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAnDQ,KAAAE,YAAY,GAAwD,IAAIC,GAAG,EAAE;IAAC;IAAAJ,cAAA,GAAAC,CAAA;IAC9E,KAAAI,iBAAiB,GAAuB,EAAE;IAElD;IAAA;IAAAL,cAAA,GAAAC,CAAA;IACiB,KAAAK,sBAAsB,GAAG,CACxC,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,iBAAiB,EACjB,kBAAkB,EAClB,aAAa,EACb,IAAI,EACJ,MAAM,EACN,MAAM,CACP;IAAC;IAAAN,cAAA,GAAAC,CAAA;IAEe,KAAAM,YAAY,GAAG,CAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,eAAe,EACf,aAAa,EACb,mBAAmB,EACnB,aAAa;IAAE;IACf,sBAAsB,EACtB,wBAAwB,CACzB;IAAC;IAAAP,cAAA,GAAAC,CAAA;IAEe,KAAAO,wBAAwB,GAAG,CAC1C,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,eAAe,EACf,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,aAAa,CACd;IAAC;IAAAR,cAAA,GAAAC,CAAA;IAEe,KAAAQ,4BAA4B,GAAG,CAC9C,aAAa,EACb,eAAe,EACf,aAAa,CACd;IAAC;IAAAT,cAAA,GAAAC,CAAA;IAGA,IAAI,CAACS,MAAM,GAAG;MACZC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,GAAG;MACnBC,2BAA2B,EAAE,IAAI;MACjCC,kBAAkB,EAAE,IAAI;MACxBC,kBAAkB,EAAE,IAAI;MACxBC,eAAe,EAAE,KAAK;MAAE;MACxBC,oBAAoB,EAAE;KACvB;EACH;EAAC;EAAAjB,cAAA,GAAAC,CAAA;EAEaF,iBAAA,CAAAmB,WAAW,GAAzB;IAAA;IAAAlB,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IACE,IAAI,CAACF,iBAAiB,CAACoB,QAAQ,EAAE;MAAA;MAAAnB,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAC/BF,iBAAiB,CAACoB,QAAQ,GAAG,IAAIpB,iBAAiB,EAAE;IACtD,CAAC;IAAA;IAAA;MAAAC,cAAA,GAAAoB,CAAA;IAAA;IAAApB,cAAA,GAAAC,CAAA;IACD,OAAOF,iBAAiB,CAACoB,QAAQ;EACnC,CAAC;EAED;;;EAAA;EAAAnB,cAAA,GAAAC,CAAA;EAGAF,iBAAA,CAAAsB,SAAA,CAAAC,aAAa,GAAb,UAAcC,KAAU,EAAEC,OAA2B;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAA3B,IAAAuB,OAAA;MAAA;MAAAxB,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAAAuB,OAAA,YAA2B;IAAA;IAAA;IAAA;MAAAxB,cAAA,GAAAoB,CAAA;IAAA;IAAApB,cAAA,GAAAC,CAAA;IACnD,IAAI;MAAA;MAAAD,cAAA,GAAAC,CAAA;MACF;MACA;MAAI;MAAA,CAAAD,cAAA,GAAAoB,CAAA,UAAAG,KAAK,KAAK,IAAI;MAAA;MAAA,CAAAvB,cAAA,GAAAoB,CAAA,UAAIG,KAAK,KAAKE,SAAS,GAAE;QAAA;QAAAzB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACzC,OAAO;UAAEyB,OAAO,EAAE,IAAI;UAAEC,cAAc,EAAEJ;QAAK,CAAE;MACjD,CAAC;MAAA;MAAA;QAAAvB,cAAA,GAAAoB,CAAA;MAAA;MAED;MAAApB,cAAA,GAAAC,CAAA;MACA,IAAI,OAAOsB,KAAK,KAAK,QAAQ,EAAE;QAAA;QAAAvB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC7B,OAAO,IAAI,CAAC2B,cAAc,CAACL,KAAK,EAAEC,OAAO,CAAC;MAC5C,CAAC,MAAM;QAAA;QAAAxB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,IAAI4B,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE;UAAA;UAAAvB,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAC/B,OAAO,IAAI,CAAC8B,aAAa,CAACR,KAAK,EAAEC,OAAO,CAAC;QAC3C,CAAC,MAAM;UAAA;UAAAxB,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAAA,IAAI,OAAOsB,KAAK,KAAK,QAAQ,EAAE;YAAA;YAAAvB,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACpC,OAAO,IAAI,CAAC+B,cAAc,CAACT,KAAK,EAAEC,OAAO,CAAC;UAC5C,CAAC,MAAM;YAAA;YAAAxB,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACL,OAAO,IAAI,CAACgC,iBAAiB,CAACV,KAAK,EAAEC,OAAO,CAAC;UAC/C;QAAA;MAAA;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA;MAAAlC,cAAA,GAAAC,CAAA;MACd,IAAI,CAACkC,mBAAmB,CAAC;QACvBC,IAAI,EAAE,kBAAkB;QACxBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,OAAO,EAAEC,IAAI,CAACC,SAAS,CAAClB,KAAK,CAAC;QAC9BmB,QAAQ,EAAE;OACX,CAAC;MAAC;MAAA1C,cAAA,GAAAC,CAAA;MAEH,OAAO;QACLyB,OAAO,EAAE,KAAK;QACdiB,SAAS,EAAE,gBAAgB;QAC3BT,KAAK,EAAE,yBAAyB;QAChCU,aAAa,EAAE;OAChB;IACH;EACF,CAAC;EAED;;;EAAA;EAAA5C,cAAA,GAAAC,CAAA;EAGQF,iBAAA,CAAAsB,SAAA,CAAAO,cAAc,GAAtB,UAAuBL,KAAa,EAAEC,OAAe;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IACnD;IACA,IAAIsB,KAAK,CAACsB,MAAM,GAAG,IAAI,CAACnC,MAAM,CAACC,cAAc,EAAE;MAAA;MAAAX,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAC7C,IAAI,CAACkC,mBAAmB,CAAC;QACvBC,IAAI,EAAE,aAAa;QACnBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,OAAO,EAAE,mBAAAO,MAAA,CAAmBvB,KAAK,CAACsB,MAAM,gBAAa;QACrDH,QAAQ,EAAE;OACX,CAAC;MAAC;MAAA1C,cAAA,GAAAC,CAAA;MAEH,OAAO;QACLyB,OAAO,EAAE,KAAK;QACdiB,SAAS,EAAE,kBAAkB;QAC7BT,KAAK,EAAE,gBAAgB;QACvBU,aAAa,EAAE,IAAI;QACnBG,UAAU,EAAE;OACb;IACH,CAAC;IAAA;IAAA;MAAA/C,cAAA,GAAAoB,CAAA;IAAA;IAED;IAAApB,cAAA,GAAAC,CAAA;IACA,IAAI,IAAI,CAACS,MAAM,CAACG,2BAA2B,EAAE;MAAA;MAAAb,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAC3C,KAAsB,IAAA+C,EAAA;QAAA;QAAA,CAAAhD,cAAA,GAAAC,CAAA,SAA2B,GAA3BgD,EAAA;QAAA;QAAA,CAAAjD,cAAA,GAAAC,CAAA,YAAI,CAACK,sBAAsB,GAA3B0C,EAAA,GAAAC,EAAA,CAAAJ,MAA2B,EAA3BG,EAAA,EAA2B,EAAE;QAA9C,IAAME,OAAO;QAAA;QAAA,CAAAlD,cAAA,GAAAC,CAAA,QAAAgD,EAAA,CAAAD,EAAA;QAAA;QAAAhD,cAAA,GAAAC,CAAA;QAChB,IAAIiD,OAAO,CAACC,IAAI,CAAC5B,KAAK,CAAC,EAAE;UAAA;UAAAvB,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UACvB,IAAI,CAACkC,mBAAmB,CAAC;YACvBC,IAAI,EAAE,uBAAuB;YAC7BC,SAAS,EAAE,IAAIC,IAAI,EAAE;YACrBC,OAAO,EAAEhB,KAAK;YACdmB,QAAQ,EAAE;WACX,CAAC;UAAC;UAAA1C,cAAA,GAAAC,CAAA;UAEH,OAAO;YACLyB,OAAO,EAAE,KAAK;YACdiB,SAAS,EAAE,gBAAgB;YAC3BT,KAAK,EAAE,0BAA0B;YACjCU,aAAa,EAAE,IAAI;YACnBG,UAAU,EAAE;WACb;QACH,CAAC;QAAA;QAAA;UAAA/C,cAAA,GAAAoB,CAAA;QAAA;MACH;IACF,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAoB,CAAA;IAAA;IAED;IAAApB,cAAA,GAAAC,CAAA;IACA,IAAI,IAAI,CAACS,MAAM,CAACI,kBAAkB,EAAE;MAAA;MAAAd,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAClC,KAAsB,IAAAmD,EAAA;QAAA;QAAA,CAAApD,cAAA,GAAAC,CAAA,SAAiB,GAAjBoD,EAAA;QAAA;QAAA,CAAArD,cAAA,GAAAC,CAAA,YAAI,CAACM,YAAY,GAAjB6C,EAAA,GAAAC,EAAA,CAAAR,MAAiB,EAAjBO,EAAA,EAAiB,EAAE;QAApC,IAAMF,OAAO;QAAA;QAAA,CAAAlD,cAAA,GAAAC,CAAA,QAAAoD,EAAA,CAAAD,EAAA;QAAA;QAAApD,cAAA,GAAAC,CAAA;QAChB,IAAIiD,OAAO,CAACC,IAAI,CAAC5B,KAAK,CAAC,EAAE;UAAA;UAAAvB,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UACvB,IAAI,CAACkC,mBAAmB,CAAC;YACvBC,IAAI,EAAE,aAAa;YACnBC,SAAS,EAAE,IAAIC,IAAI,EAAE;YACrBC,OAAO,EAAEhB,KAAK;YACdmB,QAAQ,EAAE;WACX,CAAC;UAAC;UAAA1C,cAAA,GAAAC,CAAA;UAEH,OAAO;YACLyB,OAAO,EAAE,KAAK;YACdiB,SAAS,EAAE,gBAAgB;YAC3BT,KAAK,EAAE,0BAA0B;YACjCU,aAAa,EAAE,IAAI;YACnBG,UAAU,EAAE;WACb;QACH,CAAC;QAAA;QAAA;UAAA/C,cAAA,GAAAoB,CAAA;QAAA;MACH;IACF,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAoB,CAAA;IAAA;IAED;IAAApB,cAAA,GAAAC,CAAA;IACA,KAAsB,IAAAqD,EAAA;MAAA;MAAA,CAAAtD,cAAA,GAAAC,CAAA,SAA6B,GAA7BsD,EAAA;MAAA;MAAA,CAAAvD,cAAA,GAAAC,CAAA,YAAI,CAACO,wBAAwB,GAA7B8C,EAAA,GAAAC,EAAA,CAAAV,MAA6B,EAA7BS,EAAA,EAA6B,EAAE;MAAhD,IAAMJ,OAAO;MAAA;MAAA,CAAAlD,cAAA,GAAAC,CAAA,QAAAsD,EAAA,CAAAD,EAAA;MAAA;MAAAtD,cAAA,GAAAC,CAAA;MAChB,IAAIiD,OAAO,CAACC,IAAI,CAAC5B,KAAK,CAAC,EAAE;QAAA;QAAAvB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACvB,IAAI,CAACkC,mBAAmB,CAAC;UACvBC,IAAI,EAAE,yBAAyB;UAC/BC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAEhB,KAAK;UACdmB,QAAQ,EAAE;SACX,CAAC;QAAC;QAAA1C,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyB,OAAO,EAAE,KAAK;UACdiB,SAAS,EAAE,gBAAgB;UAC3BT,KAAK,EAAE,0BAA0B;UACjCU,aAAa,EAAE,IAAI;UACnBG,UAAU,EAAE;SACb;MACH,CAAC;MAAA;MAAA;QAAA/C,cAAA,GAAAoB,CAAA;MAAA;IACH;IAEA;IACA,IAAMoC,SAAS;IAAA;IAAA,CAAAxD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACwD,cAAc,CAAClC,KAAK,CAAC;IAAC;IAAAvB,cAAA,GAAAC,CAAA;IAE7C,OAAO;MACLyB,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE6B;KACjB;EACH,CAAC;EAED;;;EAAA;EAAAxD,cAAA,GAAAC,CAAA;EAGQF,iBAAA,CAAAsB,SAAA,CAAAU,aAAa,GAArB,UAAsBR,KAAY,EAAEC,OAAe;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IACjD;IACA,IAAIsB,KAAK,CAACsB,MAAM,GAAG,IAAI,CAACnC,MAAM,CAACE,cAAc,EAAE;MAAA;MAAAZ,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAC7C,IAAI,CAACkC,mBAAmB,CAAC;QACvBC,IAAI,EAAE,aAAa;QACnBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,OAAO,EAAE,oBAAAO,MAAA,CAAoBvB,KAAK,CAACsB,MAAM,WAAQ;QACjDH,QAAQ,EAAE;OACX,CAAC;MAAC;MAAA1C,cAAA,GAAAC,CAAA;MAEH,OAAO;QACLyB,OAAO,EAAE,KAAK;QACdiB,SAAS,EAAE,kBAAkB;QAC7BT,KAAK,EAAE,iBAAiB;QACxBU,aAAa,EAAE,IAAI;QACnBG,UAAU,EAAE;OACb;IACH,CAAC;IAAA;IAAA;MAAA/C,cAAA,GAAAoB,CAAA;IAAA;IAED;IACA,IAAMsC,cAAc;IAAA;IAAA,CAAA1D,cAAA,GAAAC,CAAA,QAAG,EAAE;IAAC;IAAAD,cAAA,GAAAC,CAAA;IAC1B,KAAK,IAAI0D,CAAC;IAAA;IAAA,CAAA3D,cAAA,GAAAC,CAAA,QAAG,CAAC,GAAE0D,CAAC,GAAGpC,KAAK,CAACsB,MAAM,EAAEc,CAAC,EAAE,EAAE;MACrC,IAAMC,cAAc;MAAA;MAAA,CAAA5D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACqB,aAAa,CAACC,KAAK,CAACoC,CAAC,CAAC,EAAE,GAAAb,MAAA,CAAGtB,OAAO,OAAAsB,MAAA,CAAIa,CAAC,MAAG,CAAC;MAAC;MAAA3D,cAAA,GAAAC,CAAA;MAExE,IAAI,CAAC2D,cAAc,CAAClC,OAAO,EAAE;QAAA;QAAA1B,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC3B;QACA,IAAI,CAACkC,mBAAmB,CAAC;UACvBC,IAAI,EAAE,yBAAyB;UAC/BC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAEC,IAAI,CAACC,SAAS,CAAClB,KAAK,CAAC;UAC9BmB,QAAQ,EAAE;SACX,CAAC;QAAC;QAAA1C,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyB,OAAO,EAAE,KAAK;UACdiB,SAAS,EAAE,gBAAgB;UAC3BT,KAAK,EAAE,mCAAmC;UAC1CU,aAAa,EAAE,IAAI;UACnBG,UAAU,EAAE;SACb;MACH,CAAC;MAAA;MAAA;QAAA/C,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAEDyD,cAAc,CAACG,IAAI,CAACD,cAAc,CAACjC,cAAc,CAAC;IACpD;IAAC;IAAA3B,cAAA,GAAAC,CAAA;IAED,OAAO;MACLyB,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE+B;KACjB;EACH,CAAC;EAED;;;EAAA;EAAA1D,cAAA,GAAAC,CAAA;EAGQF,iBAAA,CAAAsB,SAAA,CAAAW,cAAc,GAAtB,UAAuBT,KAAU,EAAEC,OAAe;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAChD;IACA,KAAsB,IAAA+C,EAAA;MAAA;MAAA,CAAAhD,cAAA,GAAAC,CAAA,SAAiC,GAAjCgD,EAAA;MAAA;MAAA,CAAAjD,cAAA,GAAAC,CAAA,YAAI,CAACQ,4BAA4B,GAAjCuC,EAAA,GAAAC,EAAA,CAAAJ,MAAiC,EAAjCG,EAAA,EAAiC,EAAE;MAApD,IAAME,OAAO;MAAA;MAAA,CAAAlD,cAAA,GAAAC,CAAA,QAAAgD,EAAA,CAAAD,EAAA;MAChB,IAAMc,WAAW;MAAA;MAAA,CAAA9D,cAAA,GAAAC,CAAA,QAAGuC,IAAI,CAACC,SAAS,CAAClB,KAAK,CAAC;MAAC;MAAAvB,cAAA,GAAAC,CAAA;MAC1C,IAAIiD,OAAO,CAACC,IAAI,CAACW,WAAW,CAAC,EAAE;QAAA;QAAA9D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC7B,IAAI,CAACkC,mBAAmB,CAAC;UACvBC,IAAI,EAAE,6BAA6B;UACnCC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAEuB,WAAW;UACpBpB,QAAQ,EAAE;SACX,CAAC;QAAC;QAAA1C,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyB,OAAO,EAAE,KAAK;UACdiB,SAAS,EAAE,gBAAgB;UAC3BT,KAAK,EAAE,qCAAqC;UAC5CU,aAAa,EAAE,IAAI;UACnBG,UAAU,EAAE;SACb;MACH,CAAC;MAAA;MAAA;QAAA/C,cAAA,GAAAoB,CAAA;MAAA;IACH;IAEA;IACA,IAAM2C,eAAe;IAAA;IAAA,CAAA/D,cAAA,GAAAC,CAAA,QAAQ,EAAE;IAAC;IAAAD,cAAA,GAAAC,CAAA;IAChC,KAA2B,IAAAmD,EAAA;MAAA;MAAA,CAAApD,cAAA,GAAAC,CAAA,SAAqB,GAArBoD,EAAA;MAAA;MAAA,CAAArD,cAAA,GAAAC,CAAA,QAAA+D,MAAM,CAACC,OAAO,CAAC1C,KAAK,CAAC,GAArB6B,EAAA,GAAAC,EAAA,CAAAR,MAAqB,EAArBO,EAAA,EAAqB,EAAE;MAAvC,IAAAE,EAAA;QAAA;QAAA,CAAAtD,cAAA,GAAAC,CAAA,QAAAoD,EAAA,CAAAD,EAAA,CAAY;QAAXc,GAAG;QAAA;QAAA,CAAAlE,cAAA,GAAAC,CAAA,QAAAqD,EAAA;QAAEa,KAAK;QAAA;QAAA,CAAAnE,cAAA,GAAAC,CAAA,QAAAqD,EAAA;MACpB,IAAMc,aAAa;MAAA;MAAA,CAAApE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACqB,aAAa,CAAC4C,GAAG,EAAE,GAAApB,MAAA,CAAGtB,OAAO,SAAM,CAAC;MAC/D,IAAM6C,eAAe;MAAA;MAAA,CAAArE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACqB,aAAa,CAAC6C,KAAK,EAAE,GAAArB,MAAA,CAAGtB,OAAO,OAAAsB,MAAA,CAAIoB,GAAG,CAAE,CAAC;MAAC;MAAAlE,cAAA,GAAAC,CAAA;MAEvE;MAAI;MAAA,CAAAD,cAAA,GAAAoB,CAAA,YAACgD,aAAa,CAAC1C,OAAO;MAAA;MAAA,CAAA1B,cAAA,GAAAoB,CAAA,WAAI,CAACiD,eAAe,CAAC3C,OAAO,GAAE;QAAA;QAAA1B,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACtD,OAAO;UACLyB,OAAO,EAAE,KAAK;UACdiB,SAAS,EAAE,gBAAgB;UAC3BT,KAAK,EAAE,oCAAoC;UAC3CU,aAAa,EAAE;SAChB;MACH,CAAC;MAAA;MAAA;QAAA5C,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED8D,eAAe;MAAC;MAAA,CAAA/D,cAAA,GAAAoB,CAAA,WAAAgD,aAAa,CAACzC,cAAc;MAAA;MAAA,CAAA3B,cAAA,GAAAoB,CAAA,WAAI8C,GAAG,EAAC,GAAGG,eAAe,CAAC1C,cAAc;IACvF;IAAC;IAAA3B,cAAA,GAAAC,CAAA;IAED,OAAO;MACLyB,OAAO,EAAE,IAAI;MACbC,cAAc,EAAEoC;KACjB;EACH,CAAC;EAED;;;EAAA;EAAA/D,cAAA,GAAAC,CAAA;EAGQF,iBAAA,CAAAsB,SAAA,CAAAY,iBAAiB,GAAzB,UAA0BV,KAAU,EAAEC,OAAe;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IACnD;IACA,IAAMoE,WAAW;IAAA;IAAA,CAAAtE,cAAA,GAAAC,CAAA,SAAGsE,MAAM,CAAChD,KAAK,CAAC;IAAC;IAAAvB,cAAA,GAAAC,CAAA;IAClC,OAAO,IAAI,CAAC2B,cAAc,CAAC0C,WAAW,EAAE9C,OAAO,CAAC;EAClD,CAAC;EAED;;;EAAA;EAAAxB,cAAA,GAAAC,CAAA;EAGQF,iBAAA,CAAAsB,SAAA,CAAAoC,cAAc,GAAtB,UAAuBlC,KAAa;IAAA;IAAAvB,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAClC,OAAOsB,KAAK,CACTiD,IAAI,EAAE,CACNC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;IAAA,CAC7BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IAAA,CAC3BA,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;IAAA,CACjCA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;EACjC,CAAC;EAED;;;EAAA;EAAAzE,cAAA,GAAAC,CAAA;EAGOF,iBAAA,CAAA2E,WAAW,GAAlB,UAAmBnD,KAAa;IAAA;IAAAvB,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAC9B;IAAI;IAAA,CAAAD,cAAA,GAAAoB,CAAA,YAACG,KAAK;IAAA;IAAA,CAAAvB,cAAA,GAAAoB,CAAA,WAAI,OAAOG,KAAK,KAAK,QAAQ,GAAE;MAAA;MAAAvB,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACvC,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAD,cAAA,GAAAoB,CAAA;IAAA;IAED,IAAMuD,WAAW;IAAA;IAAA,CAAA3E,cAAA,GAAAC,CAAA,SAAG,CAClB,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC9B,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,eAAe,EACf,aAAa,EACb,mBAAmB,EACnB,aAAa;IAAE;IACf,sBAAsB,EACtB,wBAAwB,CACzB;IAAC;IAAAD,cAAA,GAAAC,CAAA;IAEF,OAAO0E,WAAW,CAACC,IAAI,CAAC,UAAA1B,OAAO;MAAA;MAAAlD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAI,OAAAiD,OAAO,CAACC,IAAI,CAAC5B,KAAK,CAAC;IAAnB,CAAmB,CAAC;EACzD,CAAC;EAED;;;EAAA;EAAAvB,cAAA,GAAAC,CAAA;EAGOF,iBAAA,CAAA8E,iBAAiB,GAAxB,UAAyBC,QAAa,EAAEC,SAAiB;IAAA;IAAA/E,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IACvD,IAAI;MAAA;MAAAD,cAAA,GAAAC,CAAA;MACF,IAAI,CAAC6E,QAAQ,EAAE;QAAA;QAAA9E,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACb,OAAO;UAAEyB,OAAO,EAAE,IAAI;UAAEC,cAAc,EAAEmD;QAAQ,CAAE;MACpD,CAAC;MAAA;MAAA;QAAA9E,cAAA,GAAAoB,CAAA;MAAA;MAED,IAAM4D,UAAU;MAAA;MAAA,CAAAhF,cAAA,GAAAC,CAAA,SAAGuC,IAAI,CAACC,SAAS,CAACqC,QAAQ,CAAC;MAE3C;MAAA;MAAA9E,cAAA,GAAAC,CAAA;MACA,IAAI,IAAI,CAACyE,WAAW,CAACM,UAAU,CAAC,EAAE;QAAA;QAAAhF,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAChC,OAAO;UACLyB,OAAO,EAAE,KAAK;UACdiB,SAAS,EAAE,gBAAgB;UAC3BT,KAAK,EAAE,mBAAAY,MAAA,CAAmBiC,SAAS,CAAE;UACrCnC,aAAa,EAAE,IAAI;UACnBG,UAAU,EAAE;SACb;MACH,CAAC;MAAA;MAAA;QAAA/C,cAAA,GAAAoB,CAAA;MAAA;MAED;MACA,IAAM6D,yBAAyB;MAAA;MAAA,CAAAjF,cAAA,GAAAC,CAAA,SAAG,CAChC,cAAc;MAAE;MAChB,gBAAgB;MAAE;MAClB,aAAa,CAAE;MAAA,CAChB;MAAC;MAAAD,cAAA,GAAAC,CAAA;MAEF,KAAsB,IAAA+C,EAAA;QAAA;QAAA,CAAAhD,cAAA,GAAAC,CAAA,UAAyB,GAAzBiF,2BAAA;QAAA;QAAA,CAAAlF,cAAA,GAAAC,CAAA,SAAAgF,yBAAyB,GAAzBjC,EAAA,GAAAkC,2BAAA,CAAArC,MAAyB,EAAzBG,EAAA,EAAyB,EAAE;QAA5C,IAAME,OAAO;QAAA;QAAA,CAAAlD,cAAA,GAAAC,CAAA,SAAAiF,2BAAA,CAAAlC,EAAA;QAAA;QAAAhD,cAAA,GAAAC,CAAA;QAChB,IAAIiD,OAAO,CAACC,IAAI,CAAC6B,UAAU,CAAC,EAAE;UAAA;UAAAhF,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAC5B,OAAO;YACLyB,OAAO,EAAE,KAAK;YACdiB,SAAS,EAAE,gBAAgB;YAC3BT,KAAK,EAAE,kCAAAY,MAAA,CAAkCiC,SAAS,CAAE;YACpDnC,aAAa,EAAE,IAAI;YACnBG,UAAU,EAAE;WACb;QACH,CAAC;QAAA;QAAA;UAAA/C,cAAA,GAAAoB,CAAA;QAAA;MACH;MAEA;MACA,IAAMoC,SAAS;MAAA;MAAA,CAAAxD,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACkF,kBAAkB,CAACL,QAAQ,CAAC;MAAC;MAAA9E,cAAA,GAAAC,CAAA;MAEpD,OAAO;QACLyB,OAAO,EAAE,IAAI;QACbC,cAAc,EAAE6B;OACjB;IACH,CAAC,CAAC,OAAOtB,KAAK,EAAE;MAAA;MAAAlC,cAAA,GAAAC,CAAA;MACd,OAAO;QACLyB,OAAO,EAAE,KAAK;QACdiB,SAAS,EAAE,kBAAkB;QAC7BT,KAAK,EAAE,mBAAAY,MAAA,CAAmBiC,SAAS,CAAE;QACrCnC,aAAa,EAAE;OAChB;IACH;EACF,CAAC;EAED;;;EAAA;EAAA5C,cAAA,GAAAC,CAAA;EAGeF,iBAAA,CAAAoF,kBAAkB,GAAjC,UAAkCC,GAAQ;IAAA;IAAApF,cAAA,GAAAE,CAAA;IAA1C,IAAAmF,KAAA;IAAA;IAAA,CAAArF,cAAA,GAAAC,CAAA;IAgCC;IAAAD,cAAA,GAAAC,CAAA;IA/BC;IAAI;IAAA,CAAAD,cAAA,GAAAoB,CAAA,WAAAgE,GAAG,KAAK,IAAI;IAAA;IAAA,CAAApF,cAAA,GAAAoB,CAAA,WAAIgE,GAAG,KAAK3D,SAAS,GAAE;MAAA;MAAAzB,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACrC,OAAOmF,GAAG;IACZ,CAAC;IAAA;IAAA;MAAApF,cAAA,GAAAoB,CAAA;IAAA;IAAApB,cAAA,GAAAC,CAAA;IAED,IAAI,OAAOmF,GAAG,KAAK,QAAQ,EAAE;MAAA;MAAApF,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAC3B;MACA,OAAOmF,GAAG,CACPX,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAC3CA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAC1BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAC1BA,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;MAAA,CAC5BD,IAAI,EAAE;IACX,CAAC;IAAA;IAAA;MAAAxE,cAAA,GAAAoB,CAAA;IAAA;IAAApB,cAAA,GAAAC,CAAA;IAED,IAAI4B,KAAK,CAACC,OAAO,CAACsD,GAAG,CAAC,EAAE;MAAA;MAAApF,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACtB,OAAOmF,GAAG,CAACE,GAAG,CAAC,UAAAC,IAAI;QAAA;QAAAvF,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAC,CAAA;QAAI,OAAAoF,KAAI,CAACF,kBAAkB,CAACI,IAAI,CAAC;MAA7B,CAA6B,CAAC;IACvD,CAAC;IAAA;IAAA;MAAAvF,cAAA,GAAAoB,CAAA;IAAA;IAAApB,cAAA,GAAAC,CAAA;IAED,IAAI,OAAOmF,GAAG,KAAK,QAAQ,EAAE;MAAA;MAAApF,cAAA,GAAAoB,CAAA;MAC3B,IAAMoC,SAAS;MAAA;MAAA,CAAAxD,cAAA,GAAAC,CAAA,SAAQ,EAAE;MAAC;MAAAD,cAAA,GAAAC,CAAA;MAC1B,KAA2B,IAAA+C,EAAA;QAAA;QAAA,CAAAhD,cAAA,GAAAC,CAAA,UAAmB,GAAnBgD,EAAA;QAAA;QAAA,CAAAjD,cAAA,GAAAC,CAAA,SAAA+D,MAAM,CAACC,OAAO,CAACmB,GAAG,CAAC,GAAnBpC,EAAA,GAAAC,EAAA,CAAAJ,MAAmB,EAAnBG,EAAA,EAAmB,EAAE;QAArC,IAAAI,EAAA;UAAA;UAAA,CAAApD,cAAA,GAAAC,CAAA,SAAAgD,EAAA,CAAAD,EAAA,CAAY;UAAXkB,GAAG;UAAA;UAAA,CAAAlE,cAAA,GAAAC,CAAA,SAAAmD,EAAA;UAAEe,KAAK;UAAA;UAAA,CAAAnE,cAAA,GAAAC,CAAA,SAAAmD,EAAA;QACpB;QACA,IAAMoC,YAAY;QAAA;QAAA,CAAAxF,cAAA,GAAAC,CAAA,SAAG,OAAOiE,GAAG,KAAK,QAAQ;QAAA;QAAA,CAAAlE,cAAA,GAAAoB,CAAA,WAC1C8C,GAAG,CAACO,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;QAAA;QAAA,CAAAzE,cAAA,GAAAoB,CAAA,WAAG8C,GAAG;QAAC;QAAAlE,cAAA,GAAAC,CAAA;QAC9DuD,SAAS,CAACgC,YAAY,CAAC,GAAG,IAAI,CAACL,kBAAkB,CAAChB,KAAK,CAAC;MAC1D;MAAC;MAAAnE,cAAA,GAAAC,CAAA;MACD,OAAOuD,SAAS;IAClB,CAAC;IAAA;IAAA;MAAAxD,cAAA,GAAAoB,CAAA;IAAA;IAAApB,cAAA,GAAAC,CAAA;IAED,OAAOmF,GAAG;EACZ,CAAC;EAED;;;EAAA;EAAApF,cAAA,GAAAC,CAAA;EAGAF,iBAAA,CAAAsB,SAAA,CAAAoE,iBAAiB,GAAjB,UAAkBC,UAAkB;IAAA;IAAA1F,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAClC,IAAI,CAAC,IAAI,CAACS,MAAM,CAACK,kBAAkB,EAAE;MAAA;MAAAf,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACnC,OAAO;QAAEyB,OAAO,EAAE;MAAI,CAAE;IAC1B,CAAC;IAAA;IAAA;MAAA1B,cAAA,GAAAoB,CAAA;IAAA;IAED,IAAMuE,GAAG;IAAA;IAAA,CAAA3F,cAAA,GAAAC,CAAA,SAAGqC,IAAI,CAACqD,GAAG,EAAE;IACtB,IAAMC,aAAa;IAAA;IAAA,CAAA5F,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACE,YAAY,CAAC0F,GAAG,CAACH,UAAU,CAAC;IAAC;IAAA1F,cAAA,GAAAC,CAAA;IAExD,IAAI,CAAC2F,aAAa,EAAE;MAAA;MAAA5F,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAClB;MACA,IAAI,CAACE,YAAY,CAAC2F,GAAG,CAACJ,UAAU,EAAE;QAAEK,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAEL;MAAG,CAAE,CAAC;MAAC;MAAA3F,cAAA,GAAAC,CAAA;MAClE,OAAO;QAAEyB,OAAO,EAAE;MAAI,CAAE;IAC1B,CAAC;IAAA;IAAA;MAAA1B,cAAA,GAAAoB,CAAA;IAAA;IAED;IAAApB,cAAA,GAAAC,CAAA;IACA,IAAI0F,GAAG,GAAGC,aAAa,CAACI,WAAW,GAAG,IAAI,CAACtF,MAAM,CAACM,eAAe,EAAE;MAAA;MAAAhB,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACjE,IAAI2F,aAAa,CAACG,KAAK,IAAI,IAAI,CAACrF,MAAM,CAACO,oBAAoB,EAAE;QAAA;QAAAjB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC3D,IAAI,CAACkC,mBAAmB,CAAC;UACvBC,IAAI,EAAE,qBAAqB;UAC3BC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,OAAO,EAAE,eAAAO,MAAA,CAAe4C,UAAU,eAAA5C,MAAA,CAAY8C,aAAa,CAACG,KAAK,CAAE;UACnErD,QAAQ,EAAE;SACX,CAAC;QAAC;QAAA1C,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyB,OAAO,EAAE,KAAK;UACdiB,SAAS,EAAE,gBAAgB;UAC3BT,KAAK,EAAE,qBAAqB;UAC5BU,aAAa,EAAE,IAAI;UACnBG,UAAU,EAAE;SACb;MACH,CAAC;MAAA;MAAA;QAAA/C,cAAA,GAAAoB,CAAA;MAAA;MAED;MAAApB,cAAA,GAAAC,CAAA;MACA2F,aAAa,CAACG,KAAK,EAAE;IACvB,CAAC,MAAM;MAAA;MAAA/F,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACL;MACA,IAAI,CAACE,YAAY,CAAC2F,GAAG,CAACJ,UAAU,EAAE;QAAEK,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAEL;MAAG,CAAE,CAAC;IACnE;IAAC;IAAA3F,cAAA,GAAAC,CAAA;IAED,OAAO;MAAEyB,OAAO,EAAE;IAAI,CAAE;EAC1B,CAAC;EAED;;;EAAA;EAAA1B,cAAA,GAAAC,CAAA;EAGQF,iBAAA,CAAAsB,SAAA,CAAAc,mBAAmB,GAA3B,UAA4B8D,QAAmE;IAAA;IAAAjG,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAC7F,IAAI,CAACI,iBAAiB,CAACwD,IAAI,CAACoC,QAAQ,CAAC;IAErC;IAAA;IAAAjG,cAAA,GAAAC,CAAA;IACAiG,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAEF,QAAQ,CAAC;IAExC;IAAA;IAAAjG,cAAA,GAAAC,CAAA;IACA,IAAI,IAAI,CAACI,iBAAiB,CAACwC,MAAM,GAAG,IAAI,EAAE;MAAA;MAAA7C,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACxC,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC+F,KAAK,CAAC,CAAC,IAAI,CAAC;IAC9D,CAAC;IAAA;IAAA;MAAApG,cAAA,GAAAoB,CAAA;IAAA;EACH,CAAC;EAED;;;EAAA;EAAApB,cAAA,GAAAC,CAAA;EAGAF,iBAAA,CAAAsB,SAAA,CAAAgF,qBAAqB,GAArB;IAAA;IAAArG,cAAA,GAAAE,CAAA;IACE,IAAMyF,GAAG;IAAA;IAAA,CAAA3F,cAAA,GAAAC,CAAA,SAAGqC,IAAI,CAACqD,GAAG,EAAE;IACtB,IAAMW,WAAW;IAAA;IAAA,CAAAtG,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACI,iBAAiB,CAACkG,MAAM,CAC/C,UAAAN,QAAQ;MAAA;MAAAjG,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAI,OAAA0F,GAAG,GAAGM,QAAQ,CAAC5D,SAAS,CAACmE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IAAxD,CAAwD,CACrE;IAED,IAAMC,eAAe;IAAA;IAAA,CAAAzG,cAAA,GAAAC,CAAA,SAAGqG,WAAW,CAACI,MAAM,CAAC,UAACC,GAAG,EAAEV,QAAQ;MAAA;MAAAjG,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MACvD0G,GAAG,CAACV,QAAQ,CAAC7D,IAAI,CAAC,GAAG;MAAC;MAAA,CAAApC,cAAA,GAAAoB,CAAA,WAAAuF,GAAG,CAACV,QAAQ,CAAC7D,IAAI,CAAC;MAAA;MAAA,CAAApC,cAAA,GAAAoB,CAAA,WAAI,CAAC,KAAI,CAAC;MAAC;MAAApB,cAAA,GAAAC,CAAA;MACnD,OAAO0G,GAAG;IACZ,CAAC,EAAE,EAA4B,CAAC;IAEhC,IAAMC,mBAAmB;IAAA;IAAA,CAAA5G,cAAA,GAAAC,CAAA,SAAGqG,WAAW,CAACI,MAAM,CAAC,UAACC,GAAG,EAAEV,QAAQ;MAAA;MAAAjG,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAC3D0G,GAAG,CAACV,QAAQ,CAACvD,QAAQ,CAAC,GAAG;MAAC;MAAA,CAAA1C,cAAA,GAAAoB,CAAA,WAAAuF,GAAG,CAACV,QAAQ,CAACvD,QAAQ,CAAC;MAAA;MAAA,CAAA1C,cAAA,GAAAoB,CAAA,WAAI,CAAC,KAAI,CAAC;MAAC;MAAApB,cAAA,GAAAC,CAAA;MAC3D,OAAO0G,GAAG;IACZ,CAAC,EAAE,EAA4B,CAAC;IAAC;IAAA3G,cAAA,GAAAC,CAAA;IAEjC,OAAO;MACL4G,cAAc,EAAE,IAAI,CAACxG,iBAAiB,CAACwC,MAAM;MAC7CyD,WAAW,EAAEA,WAAW,CAACzD,MAAM;MAC/B4D,eAAe,EAAAA,eAAA;MACfG,mBAAmB,EAAAA,mBAAA;MACnBE,eAAe,EAAE;QACfC,iBAAiB,EAAE,IAAI,CAAC5G,YAAY,CAAC6G,IAAI;QACzCtG,MAAM,EAAE,IAAI,CAACA;;KAEhB;EACH,CAAC;EAED;;;EAAA;EAAAV,cAAA,GAAAC,CAAA;EAGAF,iBAAA,CAAAsB,SAAA,CAAA4F,kBAAkB,GAAlB;IAAA;IAAAjH,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IACE,IAAI,CAACE,YAAY,CAAC+G,KAAK,EAAE;EAC3B,CAAC;EAED;;;EAAA;EAAAlH,cAAA,GAAAC,CAAA;EAGAF,iBAAA,CAAAsB,SAAA,CAAA8F,YAAY,GAAZ,UAAaC,SAAkC;IAAA;IAAApH,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAC,CAAA;IAC7C,IAAI,CAACS,MAAM,GAAA2G,QAAA,CAAAA,QAAA,KAAQ,IAAI,CAAC3G,MAAM,GAAK0G,SAAS,CAAE;EAChD,CAAC;EAAA;EAAApH,cAAA,GAAAC,CAAA;EACH,OAAAF,iBAAC;AAAD,CAAC,CA5iBD;AA4iBC;AAAAC,cAAA,GAAAC,CAAA;AA5iBYqH,OAAA,CAAAvH,iBAAA,GAAAA,iBAAA;AA8iBb;AAAA;AAAAC,cAAA,GAAAC,CAAA;AACaqH,OAAA,CAAAC,iBAAiB,GAAGxH,iBAAiB,CAACmB,WAAW,EAAE", "ignoreList": []}