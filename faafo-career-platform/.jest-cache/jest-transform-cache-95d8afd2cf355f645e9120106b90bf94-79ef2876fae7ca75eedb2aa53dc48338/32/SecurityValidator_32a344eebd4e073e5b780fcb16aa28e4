9cb45627cfaa236c6fe52266dd2eacbd
"use strict";

/* istanbul ignore next */
function cov_13eriynybl() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security/SecurityValidator.ts";
  var hash = "c360f7a43e827c120ef2dccc2e14e07d8dd13785";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security/SecurityValidator.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "12": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 63
        }
      },
      "13": {
        start: {
          line: 18,
          column: 39
        },
        end: {
          line: 521,
          column: 3
        }
      },
      "14": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 38
        }
      },
      "15": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 36
        }
      },
      "16": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 35,
          column: 10
        }
      },
      "17": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 49,
          column: 10
        }
      },
      "18": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 60,
          column: 10
        }
      },
      "19": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 65,
          column: 10
        }
      },
      "20": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 74,
          column: 10
        }
      },
      "21": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 81,
          column: 6
        }
      },
      "22": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 79,
          column: 9
        }
      },
      "23": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 65
        }
      },
      "24": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 42
        }
      },
      "25": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 120,
          column: 6
        }
      },
      "26": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 86,
          column: 56
        }
      },
      "27": {
        start: {
          line: 86,
          column: 34
        },
        end: {
          line: 86,
          column: 54
        }
      },
      "28": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 119,
          column: 9
        }
      },
      "29": {
        start: {
          line: 89,
          column: 12
        },
        end: {
          line: 91,
          column: 13
        }
      },
      "30": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 90,
          column: 64
        }
      },
      "31": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 104,
          column: 13
        }
      },
      "32": {
        start: {
          line: 94,
          column: 16
        },
        end: {
          line: 94,
          column: 59
        }
      },
      "33": {
        start: {
          line: 96,
          column: 17
        },
        end: {
          line: 104,
          column: 13
        }
      },
      "34": {
        start: {
          line: 97,
          column: 16
        },
        end: {
          line: 97,
          column: 58
        }
      },
      "35": {
        start: {
          line: 99,
          column: 17
        },
        end: {
          line: 104,
          column: 13
        }
      },
      "36": {
        start: {
          line: 100,
          column: 16
        },
        end: {
          line: 100,
          column: 59
        }
      },
      "37": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 103,
          column: 62
        }
      },
      "38": {
        start: {
          line: 107,
          column: 12
        },
        end: {
          line: 112,
          column: 15
        }
      },
      "39": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 118,
          column: 14
        }
      },
      "40": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 208,
          column: 6
        }
      },
      "41": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "42": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 132,
          column: 15
        }
      },
      "43": {
        start: {
          line: 133,
          column: 12
        },
        end: {
          line: 139,
          column: 14
        }
      },
      "44": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 161,
          column: 9
        }
      },
      "45": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 160,
          column: 13
        }
      },
      "46": {
        start: {
          line: 143,
          column: 26
        },
        end: {
          line: 143,
          column: 27
        }
      },
      "47": {
        start: {
          line: 143,
          column: 34
        },
        end: {
          line: 143,
          column: 61
        }
      },
      "48": {
        start: {
          line: 144,
          column: 30
        },
        end: {
          line: 144,
          column: 36
        }
      },
      "49": {
        start: {
          line: 145,
          column: 16
        },
        end: {
          line: 159,
          column: 17
        }
      },
      "50": {
        start: {
          line: 146,
          column: 20
        },
        end: {
          line: 151,
          column: 23
        }
      },
      "51": {
        start: {
          line: 152,
          column: 20
        },
        end: {
          line: 158,
          column: 22
        }
      },
      "52": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 182,
          column: 9
        }
      },
      "53": {
        start: {
          line: 164,
          column: 12
        },
        end: {
          line: 181,
          column: 13
        }
      },
      "54": {
        start: {
          line: 164,
          column: 26
        },
        end: {
          line: 164,
          column: 27
        }
      },
      "55": {
        start: {
          line: 164,
          column: 34
        },
        end: {
          line: 164,
          column: 51
        }
      },
      "56": {
        start: {
          line: 165,
          column: 30
        },
        end: {
          line: 165,
          column: 36
        }
      },
      "57": {
        start: {
          line: 166,
          column: 16
        },
        end: {
          line: 180,
          column: 17
        }
      },
      "58": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 172,
          column: 23
        }
      },
      "59": {
        start: {
          line: 173,
          column: 20
        },
        end: {
          line: 179,
          column: 22
        }
      },
      "60": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 201,
          column: 9
        }
      },
      "61": {
        start: {
          line: 184,
          column: 22
        },
        end: {
          line: 184,
          column: 23
        }
      },
      "62": {
        start: {
          line: 184,
          column: 30
        },
        end: {
          line: 184,
          column: 59
        }
      },
      "63": {
        start: {
          line: 185,
          column: 26
        },
        end: {
          line: 185,
          column: 32
        }
      },
      "64": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 200,
          column: 13
        }
      },
      "65": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 192,
          column: 19
        }
      },
      "66": {
        start: {
          line: 193,
          column: 16
        },
        end: {
          line: 199,
          column: 18
        }
      },
      "67": {
        start: {
          line: 203,
          column: 24
        },
        end: {
          line: 203,
          column: 50
        }
      },
      "68": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 207,
          column: 10
        }
      },
      "69": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 255,
          column: 6
        }
      },
      "70": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 228,
          column: 9
        }
      },
      "71": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 220,
          column: 15
        }
      },
      "72": {
        start: {
          line: 221,
          column: 12
        },
        end: {
          line: 227,
          column: 14
        }
      },
      "73": {
        start: {
          line: 230,
          column: 29
        },
        end: {
          line: 230,
          column: 31
        }
      },
      "74": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "75": {
        start: {
          line: 231,
          column: 21
        },
        end: {
          line: 231,
          column: 22
        }
      },
      "76": {
        start: {
          line: 232,
          column: 33
        },
        end: {
          line: 232,
          column: 101
        }
      },
      "77": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 248,
          column: 13
        }
      },
      "78": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 240,
          column: 19
        }
      },
      "79": {
        start: {
          line: 241,
          column: 16
        },
        end: {
          line: 247,
          column: 18
        }
      },
      "80": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 249,
          column: 63
        }
      },
      "81": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 254,
          column: 10
        }
      },
      "82": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 300,
          column: 6
        }
      },
      "83": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 279,
          column: 9
        }
      },
      "84": {
        start: {
          line: 261,
          column: 22
        },
        end: {
          line: 261,
          column: 23
        }
      },
      "85": {
        start: {
          line: 261,
          column: 30
        },
        end: {
          line: 261,
          column: 63
        }
      },
      "86": {
        start: {
          line: 262,
          column: 26
        },
        end: {
          line: 262,
          column: 32
        }
      },
      "87": {
        start: {
          line: 263,
          column: 30
        },
        end: {
          line: 263,
          column: 51
        }
      },
      "88": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 278,
          column: 13
        }
      },
      "89": {
        start: {
          line: 265,
          column: 16
        },
        end: {
          line: 270,
          column: 19
        }
      },
      "90": {
        start: {
          line: 271,
          column: 16
        },
        end: {
          line: 277,
          column: 18
        }
      },
      "91": {
        start: {
          line: 281,
          column: 30
        },
        end: {
          line: 281,
          column: 32
        }
      },
      "92": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "93": {
        start: {
          line: 282,
          column: 22
        },
        end: {
          line: 282,
          column: 23
        }
      },
      "94": {
        start: {
          line: 282,
          column: 30
        },
        end: {
          line: 282,
          column: 51
        }
      },
      "95": {
        start: {
          line: 283,
          column: 21
        },
        end: {
          line: 283,
          column: 27
        }
      },
      "96": {
        start: {
          line: 283,
          column: 35
        },
        end: {
          line: 283,
          column: 40
        }
      },
      "97": {
        start: {
          line: 283,
          column: 50
        },
        end: {
          line: 283,
          column: 55
        }
      },
      "98": {
        start: {
          line: 284,
          column: 32
        },
        end: {
          line: 284,
          column: 83
        }
      },
      "99": {
        start: {
          line: 285,
          column: 34
        },
        end: {
          line: 285,
          column: 96
        }
      },
      "100": {
        start: {
          line: 286,
          column: 12
        },
        end: {
          line: 293,
          column: 13
        }
      },
      "101": {
        start: {
          line: 287,
          column: 16
        },
        end: {
          line: 292,
          column: 18
        }
      },
      "102": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 294,
          column: 98
        }
      },
      "103": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 299,
          column: 10
        }
      },
      "104": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 308,
          column: 6
        }
      },
      "105": {
        start: {
          line: 306,
          column: 26
        },
        end: {
          line: 306,
          column: 39
        }
      },
      "106": {
        start: {
          line: 307,
          column: 8
        },
        end: {
          line: 307,
          column: 57
        }
      },
      "107": {
        start: {
          line: 312,
          column: 4
        },
        end: {
          line: 320,
          column: 6
        }
      },
      "108": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 319,
          column: 40
        }
      },
      "109": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 343,
          column: 6
        }
      },
      "110": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 327,
          column: 9
        }
      },
      "111": {
        start: {
          line: 326,
          column: 12
        },
        end: {
          line: 326,
          column: 25
        }
      },
      "112": {
        start: {
          line: 328,
          column: 26
        },
        end: {
          line: 341,
          column: 9
        }
      },
      "113": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 342,
          column: 84
        }
      },
      "114": {
        start: {
          line: 342,
          column: 53
        },
        end: {
          line: 342,
          column: 80
        }
      },
      "115": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 396,
          column: 6
        }
      },
      "116": {
        start: {
          line: 348,
          column: 8
        },
        end: {
          line: 395,
          column: 9
        }
      },
      "117": {
        start: {
          line: 349,
          column: 12
        },
        end: {
          line: 351,
          column: 13
        }
      },
      "118": {
        start: {
          line: 350,
          column: 16
        },
        end: {
          line: 350,
          column: 67
        }
      },
      "119": {
        start: {
          line: 352,
          column: 29
        },
        end: {
          line: 352,
          column: 53
        }
      },
      "120": {
        start: {
          line: 354,
          column: 12
        },
        end: {
          line: 362,
          column: 13
        }
      },
      "121": {
        start: {
          line: 355,
          column: 16
        },
        end: {
          line: 361,
          column: 18
        }
      },
      "122": {
        start: {
          line: 364,
          column: 44
        },
        end: {
          line: 368,
          column: 13
        }
      },
      "123": {
        start: {
          line: 369,
          column: 12
        },
        end: {
          line: 380,
          column: 13
        }
      },
      "124": {
        start: {
          line: 369,
          column: 26
        },
        end: {
          line: 369,
          column: 27
        }
      },
      "125": {
        start: {
          line: 369,
          column: 59
        },
        end: {
          line: 369,
          column: 84
        }
      },
      "126": {
        start: {
          line: 370,
          column: 30
        },
        end: {
          line: 370,
          column: 61
        }
      },
      "127": {
        start: {
          line: 371,
          column: 16
        },
        end: {
          line: 379,
          column: 17
        }
      },
      "128": {
        start: {
          line: 372,
          column: 20
        },
        end: {
          line: 378,
          column: 22
        }
      },
      "129": {
        start: {
          line: 382,
          column: 28
        },
        end: {
          line: 382,
          column: 61
        }
      },
      "130": {
        start: {
          line: 383,
          column: 12
        },
        end: {
          line: 386,
          column: 14
        }
      },
      "131": {
        start: {
          line: 389,
          column: 12
        },
        end: {
          line: 394,
          column: 14
        }
      },
      "132": {
        start: {
          line: 400,
          column: 4
        },
        end: {
          line: 430,
          column: 6
        }
      },
      "133": {
        start: {
          line: 401,
          column: 20
        },
        end: {
          line: 401,
          column: 24
        }
      },
      "134": {
        start: {
          line: 402,
          column: 8
        },
        end: {
          line: 404,
          column: 9
        }
      },
      "135": {
        start: {
          line: 403,
          column: 12
        },
        end: {
          line: 403,
          column: 23
        }
      },
      "136": {
        start: {
          line: 405,
          column: 8
        },
        end: {
          line: 414,
          column: 9
        }
      },
      "137": {
        start: {
          line: 407,
          column: 12
        },
        end: {
          line: 413,
          column: 24
        }
      },
      "138": {
        start: {
          line: 415,
          column: 8
        },
        end: {
          line: 417,
          column: 9
        }
      },
      "139": {
        start: {
          line: 416,
          column: 12
        },
        end: {
          line: 416,
          column: 87
        }
      },
      "140": {
        start: {
          line: 416,
          column: 45
        },
        end: {
          line: 416,
          column: 83
        }
      },
      "141": {
        start: {
          line: 418,
          column: 8
        },
        end: {
          line: 428,
          column: 9
        }
      },
      "142": {
        start: {
          line: 419,
          column: 28
        },
        end: {
          line: 419,
          column: 30
        }
      },
      "143": {
        start: {
          line: 420,
          column: 12
        },
        end: {
          line: 426,
          column: 13
        }
      },
      "144": {
        start: {
          line: 420,
          column: 26
        },
        end: {
          line: 420,
          column: 27
        }
      },
      "145": {
        start: {
          line: 420,
          column: 34
        },
        end: {
          line: 420,
          column: 53
        }
      },
      "146": {
        start: {
          line: 421,
          column: 25
        },
        end: {
          line: 421,
          column: 31
        }
      },
      "147": {
        start: {
          line: 421,
          column: 39
        },
        end: {
          line: 421,
          column: 44
        }
      },
      "148": {
        start: {
          line: 421,
          column: 54
        },
        end: {
          line: 421,
          column: 59
        }
      },
      "149": {
        start: {
          line: 423,
          column: 35
        },
        end: {
          line: 424,
          column: 79
        }
      },
      "150": {
        start: {
          line: 425,
          column: 16
        },
        end: {
          line: 425,
          column: 73
        }
      },
      "151": {
        start: {
          line: 427,
          column: 12
        },
        end: {
          line: 427,
          column: 29
        }
      },
      "152": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 429,
          column: 19
        }
      },
      "153": {
        start: {
          line: 434,
          column: 4
        },
        end: {
          line: 470,
          column: 6
        }
      },
      "154": {
        start: {
          line: 435,
          column: 8
        },
        end: {
          line: 437,
          column: 9
        }
      },
      "155": {
        start: {
          line: 436,
          column: 12
        },
        end: {
          line: 436,
          column: 37
        }
      },
      "156": {
        start: {
          line: 438,
          column: 18
        },
        end: {
          line: 438,
          column: 28
        }
      },
      "157": {
        start: {
          line: 439,
          column: 28
        },
        end: {
          line: 439,
          column: 61
        }
      },
      "158": {
        start: {
          line: 440,
          column: 8
        },
        end: {
          line: 444,
          column: 9
        }
      },
      "159": {
        start: {
          line: 442,
          column: 12
        },
        end: {
          line: 442,
          column: 78
        }
      },
      "160": {
        start: {
          line: 443,
          column: 12
        },
        end: {
          line: 443,
          column: 37
        }
      },
      "161": {
        start: {
          line: 446,
          column: 8
        },
        end: {
          line: 468,
          column: 9
        }
      },
      "162": {
        start: {
          line: 447,
          column: 12
        },
        end: {
          line: 461,
          column: 13
        }
      },
      "163": {
        start: {
          line: 448,
          column: 16
        },
        end: {
          line: 453,
          column: 19
        }
      },
      "164": {
        start: {
          line: 454,
          column: 16
        },
        end: {
          line: 460,
          column: 18
        }
      },
      "165": {
        start: {
          line: 463,
          column: 12
        },
        end: {
          line: 463,
          column: 34
        }
      },
      "166": {
        start: {
          line: 467,
          column: 12
        },
        end: {
          line: 467,
          column: 78
        }
      },
      "167": {
        start: {
          line: 469,
          column: 8
        },
        end: {
          line: 469,
          column: 33
        }
      },
      "168": {
        start: {
          line: 474,
          column: 4
        },
        end: {
          line: 482,
          column: 6
        }
      },
      "169": {
        start: {
          line: 475,
          column: 8
        },
        end: {
          line: 475,
          column: 46
        }
      },
      "170": {
        start: {
          line: 477,
          column: 8
        },
        end: {
          line: 477,
          column: 49
        }
      },
      "171": {
        start: {
          line: 479,
          column: 8
        },
        end: {
          line: 481,
          column: 9
        }
      },
      "172": {
        start: {
          line: 480,
          column: 12
        },
        end: {
          line: 480,
          column: 73
        }
      },
      "173": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 507,
          column: 6
        }
      },
      "174": {
        start: {
          line: 487,
          column: 18
        },
        end: {
          line: 487,
          column: 28
        }
      },
      "175": {
        start: {
          line: 488,
          column: 26
        },
        end: {
          line: 488,
          column: 145
        }
      },
      "176": {
        start: {
          line: 488,
          column: 78
        },
        end: {
          line: 488,
          column: 142
        }
      },
      "177": {
        start: {
          line: 489,
          column: 30
        },
        end: {
          line: 492,
          column: 14
        }
      },
      "178": {
        start: {
          line: 490,
          column: 12
        },
        end: {
          line: 490,
          column: 63
        }
      },
      "179": {
        start: {
          line: 491,
          column: 12
        },
        end: {
          line: 491,
          column: 23
        }
      },
      "180": {
        start: {
          line: 493,
          column: 34
        },
        end: {
          line: 496,
          column: 14
        }
      },
      "181": {
        start: {
          line: 494,
          column: 12
        },
        end: {
          line: 494,
          column: 71
        }
      },
      "182": {
        start: {
          line: 495,
          column: 12
        },
        end: {
          line: 495,
          column: 23
        }
      },
      "183": {
        start: {
          line: 497,
          column: 8
        },
        end: {
          line: 506,
          column: 10
        }
      },
      "184": {
        start: {
          line: 511,
          column: 4
        },
        end: {
          line: 513,
          column: 6
        }
      },
      "185": {
        start: {
          line: 512,
          column: 8
        },
        end: {
          line: 512,
          column: 34
        }
      },
      "186": {
        start: {
          line: 517,
          column: 4
        },
        end: {
          line: 519,
          column: 6
        }
      },
      "187": {
        start: {
          line: 518,
          column: 8
        },
        end: {
          line: 518,
          column: 69
        }
      },
      "188": {
        start: {
          line: 520,
          column: 4
        },
        end: {
          line: 520,
          column: 29
        }
      },
      "189": {
        start: {
          line: 522,
          column: 0
        },
        end: {
          line: 522,
          column: 46
        }
      },
      "190": {
        start: {
          line: 524,
          column: 0
        },
        end: {
          line: 524,
          column: 60
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 18,
            column: 39
          },
          end: {
            line: 18,
            column: 40
          }
        },
        loc: {
          start: {
            line: 18,
            column: 51
          },
          end: {
            line: 521,
            column: 1
          }
        },
        line: 18
      },
      "3": {
        name: "SecurityValidator",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 30
          }
        },
        loc: {
          start: {
            line: 19,
            column: 33
          },
          end: {
            line: 75,
            column: 5
          }
        },
        line: 19
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 76,
            column: 36
          },
          end: {
            line: 76,
            column: 37
          }
        },
        loc: {
          start: {
            line: 76,
            column: 48
          },
          end: {
            line: 81,
            column: 5
          }
        },
        line: 76
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 85,
            column: 48
          },
          end: {
            line: 85,
            column: 49
          }
        },
        loc: {
          start: {
            line: 85,
            column: 74
          },
          end: {
            line: 120,
            column: 5
          }
        },
        line: 85
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 124,
            column: 49
          },
          end: {
            line: 124,
            column: 50
          }
        },
        loc: {
          start: {
            line: 124,
            column: 75
          },
          end: {
            line: 208,
            column: 5
          }
        },
        line: 124
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 212,
            column: 48
          },
          end: {
            line: 212,
            column: 49
          }
        },
        loc: {
          start: {
            line: 212,
            column: 74
          },
          end: {
            line: 255,
            column: 5
          }
        },
        line: 212
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 259,
            column: 49
          },
          end: {
            line: 259,
            column: 50
          }
        },
        loc: {
          start: {
            line: 259,
            column: 75
          },
          end: {
            line: 300,
            column: 5
          }
        },
        line: 259
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 304,
            column: 52
          },
          end: {
            line: 304,
            column: 53
          }
        },
        loc: {
          start: {
            line: 304,
            column: 78
          },
          end: {
            line: 308,
            column: 5
          }
        },
        line: 304
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 312,
            column: 49
          },
          end: {
            line: 312,
            column: 50
          }
        },
        loc: {
          start: {
            line: 312,
            column: 66
          },
          end: {
            line: 320,
            column: 5
          }
        },
        line: 312
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 324,
            column: 36
          },
          end: {
            line: 324,
            column: 37
          }
        },
        loc: {
          start: {
            line: 324,
            column: 53
          },
          end: {
            line: 343,
            column: 5
          }
        },
        line: 324
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 342,
            column: 32
          },
          end: {
            line: 342,
            column: 33
          }
        },
        loc: {
          start: {
            line: 342,
            column: 51
          },
          end: {
            line: 342,
            column: 82
          }
        },
        line: 342
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 347,
            column: 42
          },
          end: {
            line: 347,
            column: 43
          }
        },
        loc: {
          start: {
            line: 347,
            column: 73
          },
          end: {
            line: 396,
            column: 5
          }
        },
        line: 347
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 400,
            column: 43
          },
          end: {
            line: 400,
            column: 44
          }
        },
        loc: {
          start: {
            line: 400,
            column: 58
          },
          end: {
            line: 430,
            column: 5
          }
        },
        line: 400
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 416,
            column: 27
          },
          end: {
            line: 416,
            column: 28
          }
        },
        loc: {
          start: {
            line: 416,
            column: 43
          },
          end: {
            line: 416,
            column: 85
          }
        },
        line: 416
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 434,
            column: 52
          },
          end: {
            line: 434,
            column: 53
          }
        },
        loc: {
          start: {
            line: 434,
            column: 74
          },
          end: {
            line: 470,
            column: 5
          }
        },
        line: 434
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 474,
            column: 54
          },
          end: {
            line: 474,
            column: 55
          }
        },
        loc: {
          start: {
            line: 474,
            column: 74
          },
          end: {
            line: 482,
            column: 5
          }
        },
        line: 474
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 486,
            column: 56
          },
          end: {
            line: 486,
            column: 57
          }
        },
        loc: {
          start: {
            line: 486,
            column: 68
          },
          end: {
            line: 507,
            column: 5
          }
        },
        line: 486
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 488,
            column: 56
          },
          end: {
            line: 488,
            column: 57
          }
        },
        loc: {
          start: {
            line: 488,
            column: 76
          },
          end: {
            line: 488,
            column: 144
          }
        },
        line: 488
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 489,
            column: 49
          },
          end: {
            line: 489,
            column: 50
          }
        },
        loc: {
          start: {
            line: 489,
            column: 74
          },
          end: {
            line: 492,
            column: 9
          }
        },
        line: 489
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 493,
            column: 53
          },
          end: {
            line: 493,
            column: 54
          }
        },
        loc: {
          start: {
            line: 493,
            column: 78
          },
          end: {
            line: 496,
            column: 9
          }
        },
        line: 493
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 511,
            column: 53
          },
          end: {
            line: 511,
            column: 54
          }
        },
        loc: {
          start: {
            line: 511,
            column: 65
          },
          end: {
            line: 513,
            column: 5
          }
        },
        line: 511
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 517,
            column: 47
          },
          end: {
            line: 517,
            column: 48
          }
        },
        loc: {
          start: {
            line: 517,
            column: 68
          },
          end: {
            line: 519,
            column: 5
          }
        },
        line: 517
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 79,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 79,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "4": {
        loc: {
          start: {
            line: 86,
            column: 8
          },
          end: {
            line: 86,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 8
          },
          end: {
            line: 86,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "5": {
        loc: {
          start: {
            line: 89,
            column: 12
          },
          end: {
            line: 91,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 12
          },
          end: {
            line: 91,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "6": {
        loc: {
          start: {
            line: 89,
            column: 16
          },
          end: {
            line: 89,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 89,
            column: 16
          },
          end: {
            line: 89,
            column: 30
          }
        }, {
          start: {
            line: 89,
            column: 34
          },
          end: {
            line: 89,
            column: 53
          }
        }],
        line: 89
      },
      "7": {
        loc: {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 104,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 104,
            column: 13
          }
        }, {
          start: {
            line: 96,
            column: 17
          },
          end: {
            line: 104,
            column: 13
          }
        }],
        line: 93
      },
      "8": {
        loc: {
          start: {
            line: 96,
            column: 17
          },
          end: {
            line: 104,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 17
          },
          end: {
            line: 104,
            column: 13
          }
        }, {
          start: {
            line: 99,
            column: 17
          },
          end: {
            line: 104,
            column: 13
          }
        }],
        line: 96
      },
      "9": {
        loc: {
          start: {
            line: 99,
            column: 17
          },
          end: {
            line: 104,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 99,
            column: 17
          },
          end: {
            line: 104,
            column: 13
          }
        }, {
          start: {
            line: 102,
            column: 17
          },
          end: {
            line: 104,
            column: 13
          }
        }],
        line: 99
      },
      "10": {
        loc: {
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "11": {
        loc: {
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 161,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 161,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "12": {
        loc: {
          start: {
            line: 145,
            column: 16
          },
          end: {
            line: 159,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 16
          },
          end: {
            line: 159,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 145
      },
      "13": {
        loc: {
          start: {
            line: 163,
            column: 8
          },
          end: {
            line: 182,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 8
          },
          end: {
            line: 182,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "14": {
        loc: {
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 180,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 180,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "15": {
        loc: {
          start: {
            line: 186,
            column: 12
          },
          end: {
            line: 200,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 12
          },
          end: {
            line: 200,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "16": {
        loc: {
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 228,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 228,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "17": {
        loc: {
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 248,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 248,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "18": {
        loc: {
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 278,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 278,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 264
      },
      "19": {
        loc: {
          start: {
            line: 286,
            column: 12
          },
          end: {
            line: 293,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 286,
            column: 12
          },
          end: {
            line: 293,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 286
      },
      "20": {
        loc: {
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 286,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 286,
            column: 38
          }
        }, {
          start: {
            line: 286,
            column: 42
          },
          end: {
            line: 286,
            column: 66
          }
        }],
        line: 286
      },
      "21": {
        loc: {
          start: {
            line: 294,
            column: 28
          },
          end: {
            line: 294,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 294,
            column: 28
          },
          end: {
            line: 294,
            column: 56
          }
        }, {
          start: {
            line: 294,
            column: 60
          },
          end: {
            line: 294,
            column: 63
          }
        }],
        line: 294
      },
      "22": {
        loc: {
          start: {
            line: 325,
            column: 8
          },
          end: {
            line: 327,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 325,
            column: 8
          },
          end: {
            line: 327,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 325
      },
      "23": {
        loc: {
          start: {
            line: 325,
            column: 12
          },
          end: {
            line: 325,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 325,
            column: 12
          },
          end: {
            line: 325,
            column: 18
          }
        }, {
          start: {
            line: 325,
            column: 22
          },
          end: {
            line: 325,
            column: 47
          }
        }],
        line: 325
      },
      "24": {
        loc: {
          start: {
            line: 349,
            column: 12
          },
          end: {
            line: 351,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 349,
            column: 12
          },
          end: {
            line: 351,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 349
      },
      "25": {
        loc: {
          start: {
            line: 354,
            column: 12
          },
          end: {
            line: 362,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 354,
            column: 12
          },
          end: {
            line: 362,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 354
      },
      "26": {
        loc: {
          start: {
            line: 371,
            column: 16
          },
          end: {
            line: 379,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 371,
            column: 16
          },
          end: {
            line: 379,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 371
      },
      "27": {
        loc: {
          start: {
            line: 402,
            column: 8
          },
          end: {
            line: 404,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 402,
            column: 8
          },
          end: {
            line: 404,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 402
      },
      "28": {
        loc: {
          start: {
            line: 402,
            column: 12
          },
          end: {
            line: 402,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 402,
            column: 12
          },
          end: {
            line: 402,
            column: 24
          }
        }, {
          start: {
            line: 402,
            column: 28
          },
          end: {
            line: 402,
            column: 45
          }
        }],
        line: 402
      },
      "29": {
        loc: {
          start: {
            line: 405,
            column: 8
          },
          end: {
            line: 414,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 405,
            column: 8
          },
          end: {
            line: 414,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 405
      },
      "30": {
        loc: {
          start: {
            line: 415,
            column: 8
          },
          end: {
            line: 417,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 8
          },
          end: {
            line: 417,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "31": {
        loc: {
          start: {
            line: 418,
            column: 8
          },
          end: {
            line: 428,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 8
          },
          end: {
            line: 428,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 418
      },
      "32": {
        loc: {
          start: {
            line: 423,
            column: 35
          },
          end: {
            line: 424,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 424,
            column: 20
          },
          end: {
            line: 424,
            column: 73
          }
        }, {
          start: {
            line: 424,
            column: 76
          },
          end: {
            line: 424,
            column: 79
          }
        }],
        line: 423
      },
      "33": {
        loc: {
          start: {
            line: 435,
            column: 8
          },
          end: {
            line: 437,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 435,
            column: 8
          },
          end: {
            line: 437,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 435
      },
      "34": {
        loc: {
          start: {
            line: 440,
            column: 8
          },
          end: {
            line: 444,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 440,
            column: 8
          },
          end: {
            line: 444,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 440
      },
      "35": {
        loc: {
          start: {
            line: 446,
            column: 8
          },
          end: {
            line: 468,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 446,
            column: 8
          },
          end: {
            line: 468,
            column: 9
          }
        }, {
          start: {
            line: 465,
            column: 13
          },
          end: {
            line: 468,
            column: 9
          }
        }],
        line: 446
      },
      "36": {
        loc: {
          start: {
            line: 447,
            column: 12
          },
          end: {
            line: 461,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 447,
            column: 12
          },
          end: {
            line: 461,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 447
      },
      "37": {
        loc: {
          start: {
            line: 479,
            column: 8
          },
          end: {
            line: 481,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 479,
            column: 8
          },
          end: {
            line: 481,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 479
      },
      "38": {
        loc: {
          start: {
            line: 490,
            column: 34
          },
          end: {
            line: 490,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 490,
            column: 34
          },
          end: {
            line: 490,
            column: 52
          }
        }, {
          start: {
            line: 490,
            column: 56
          },
          end: {
            line: 490,
            column: 57
          }
        }],
        line: 490
      },
      "39": {
        loc: {
          start: {
            line: 494,
            column: 38
          },
          end: {
            line: 494,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 494,
            column: 38
          },
          end: {
            line: 494,
            column: 60
          }
        }, {
          start: {
            line: 494,
            column: 64
          },
          end: {
            line: 494,
            column: 65
          }
        }],
        line: 494
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security/SecurityValidator.ts",
      mappings: ";;;;;;;;;;;;;;AA+BA;;GAEG;AACH;IAsDE;QAnDQ,iBAAY,GAAwD,IAAI,GAAG,EAAE,CAAC;QAC9E,sBAAiB,GAAuB,EAAE,CAAC;QAEnD,4EAA4E;QAC3D,2BAAsB,GAAG;YACxC,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB;YAClB,aAAa;YACb,IAAI;YACJ,MAAM;YACN,MAAM;SACP,CAAC;QAEe,iBAAY,GAAG;YAC9B,8BAA8B;YAC9B,8BAA8B;YAC9B,8BAA8B;YAC9B,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,eAAe;YACf,aAAa;YACb,mBAAmB;YACnB,aAAa,EAAE,4CAA4C;YAC3D,sBAAsB;YACtB,wBAAwB;SACzB,CAAC;QAEe,6BAAwB,GAAG;YAC1C,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,eAAe;YACf,eAAe;YACf,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,aAAa;SACd,CAAC;QAEe,iCAA4B,GAAG;YAC9C,aAAa;YACb,eAAe;YACf,aAAa;SACd,CAAC;QAGA,IAAI,CAAC,MAAM,GAAG;YACZ,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,GAAG;YACnB,2BAA2B,EAAE,IAAI;YACjC,kBAAkB,EAAE,IAAI;YACxB,kBAAkB,EAAE,IAAI;YACxB,eAAe,EAAE,KAAK,EAAE,WAAW;YACnC,oBAAoB,EAAE,GAAG;SAC1B,CAAC;IACJ,CAAC;IAEa,6BAAW,GAAzB;QACE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAChC,iBAAiB,CAAC,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACvD,CAAC;QACD,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,yCAAa,GAAb,UAAc,KAAU,EAAE,OAA2B;QAA3B,wBAAA,EAAA,mBAA2B;QACnD,IAAI,CAAC;YACH,2BAA2B;YAC3B,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC1C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;YAClD,CAAC;YAED,+BAA+B;YAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,mBAAmB,CAAC;gBACvB,IAAI,EAAE,kBAAkB;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBAC9B,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,gBAAgB;gBAC3B,KAAK,EAAE,yBAAyB;gBAChC,aAAa,EAAE,IAAI;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0CAAc,GAAtB,UAAuB,KAAa,EAAE,OAAe;QACnD,oBAAoB;QACpB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC9C,IAAI,CAAC,mBAAmB,CAAC;gBACvB,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,0BAAmB,KAAK,CAAC,MAAM,gBAAa;gBACrD,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,kBAAkB;gBAC7B,KAAK,EAAE,gBAAgB;gBACvB,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,aAAa;aAC1B,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE,CAAC;YAC5C,KAAsB,UAA2B,EAA3B,KAAA,IAAI,CAAC,sBAAsB,EAA3B,cAA2B,EAA3B,IAA2B,EAAE,CAAC;gBAA/C,IAAM,OAAO,SAAA;gBAChB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACxB,IAAI,CAAC,mBAAmB,CAAC;wBACvB,IAAI,EAAE,uBAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,UAAU;qBACrB,CAAC,CAAC;oBAEH,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,SAAS,EAAE,gBAAgB;wBAC3B,KAAK,EAAE,0BAA0B;wBACjC,aAAa,EAAE,IAAI;wBACnB,UAAU,EAAE,eAAe;qBAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACnC,KAAsB,UAAiB,EAAjB,KAAA,IAAI,CAAC,YAAY,EAAjB,cAAiB,EAAjB,IAAiB,EAAE,CAAC;gBAArC,IAAM,OAAO,SAAA;gBAChB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACxB,IAAI,CAAC,mBAAmB,CAAC;wBACvB,IAAI,EAAE,aAAa;wBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,MAAM;qBACjB,CAAC,CAAC;oBAEH,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,SAAS,EAAE,gBAAgB;wBAC3B,KAAK,EAAE,0BAA0B;wBACjC,aAAa,EAAE,IAAI;wBACnB,UAAU,EAAE,KAAK;qBAClB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,KAAsB,UAA6B,EAA7B,KAAA,IAAI,CAAC,wBAAwB,EAA7B,cAA6B,EAA7B,IAA6B,EAAE,CAAC;YAAjD,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,mBAAmB,CAAC;oBACvB,IAAI,EAAE,yBAAyB;oBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,MAAM;iBACjB,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,0BAA0B;oBACjC,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,iBAAiB;iBAC9B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,qBAAqB;QACrB,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE7C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yCAAa,GAArB,UAAsB,KAAY,EAAE,OAAe;QACjD,oBAAoB;QACpB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC9C,IAAI,CAAC,mBAAmB,CAAC;gBACvB,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,2BAAoB,KAAK,CAAC,MAAM,WAAQ;gBACjD,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,kBAAkB;gBAC7B,KAAK,EAAE,iBAAiB;gBACxB,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,aAAa;aAC1B,CAAC;QACJ,CAAC;QAED,kCAAkC;QAClC,IAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,IAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAG,OAAO,cAAI,CAAC,MAAG,CAAC,CAAC;YAExE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC5B,oDAAoD;gBACpD,IAAI,CAAC,mBAAmB,CAAC;oBACvB,IAAI,EAAE,yBAAyB;oBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC9B,QAAQ,EAAE,MAAM;iBACjB,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,mCAAmC;oBAC1C,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,iBAAiB;iBAC9B,CAAC;YACJ,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACrD,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,cAAc;SAC/B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,0CAAc,GAAtB,UAAuB,KAAU,EAAE,OAAe;QAChD,gCAAgC;QAChC,KAAsB,UAAiC,EAAjC,KAAA,IAAI,CAAC,4BAA4B,EAAjC,cAAiC,EAAjC,IAAiC,EAAE,CAAC;YAArD,IAAM,OAAO,SAAA;YAChB,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,mBAAmB,CAAC;oBACvB,IAAI,EAAE,6BAA6B;oBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,WAAW;oBACpB,QAAQ,EAAE,UAAU;iBACrB,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,qCAAqC;oBAC5C,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,qBAAqB;iBAClC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAM,eAAe,GAAQ,EAAE,CAAC;QAChC,KAA2B,UAAqB,EAArB,KAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAArB,cAAqB,EAArB,IAAqB,EAAE,CAAC;YAAxC,IAAA,WAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;YACpB,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,UAAG,OAAO,SAAM,CAAC,CAAC;YAChE,IAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,UAAG,OAAO,cAAI,GAAG,CAAE,CAAC,CAAC;YAEvE,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBACvD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,oCAAoC;oBAC3C,aAAa,EAAE,IAAI;iBACpB,CAAC;YACJ,CAAC;YAED,eAAe,CAAC,aAAa,CAAC,cAAc,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,cAAc,CAAC;QACxF,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,eAAe;SAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,6CAAiB,GAAzB,UAA0B,KAAU,EAAE,OAAe;QACnD,iCAAiC;QACjC,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,0CAAc,GAAtB,UAAuB,KAAa;QAClC,OAAO,KAAK;aACT,IAAI,EAAE;aACN,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,wBAAwB;aAC7C,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,8BAA8B;aAC3D,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,4BAA4B;aACvD,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,mBAAmB;aACpD,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,wBAAwB;IACzD,CAAC;IAED;;OAEG;IACI,6BAAW,GAAlB,UAAmB,KAAa;QAC9B,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAM,WAAW,GAAG;YAClB,8BAA8B;YAC9B,8BAA8B;YAC9B,8BAA8B;YAC9B,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,eAAe;YACf,aAAa;YACb,mBAAmB;YACnB,aAAa,EAAE,4CAA4C;YAC3D,sBAAsB;YACtB,wBAAwB;SACzB,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,mCAAiB,GAAxB,UAAyB,QAAa,EAAE,SAAiB;QACvD,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC;YACrD,CAAC;YAED,IAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAE5C,wBAAwB;YACxB,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,0BAAmB,SAAS,CAAE;oBACrC,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,KAAK;iBAClB,CAAC;YACJ,CAAC;YAED,+BAA+B;YAC/B,IAAM,yBAAyB,GAAG;gBAChC,cAAc,EAAE,2BAA2B;gBAC3C,gBAAgB,EAAE,8BAA8B;gBAChD,aAAa,EAAE,oBAAoB;aACpC,CAAC;YAEF,KAAsB,UAAyB,EAAzB,uDAAyB,EAAzB,uCAAyB,EAAzB,IAAyB,EAAE,CAAC;gBAA7C,IAAM,OAAO,kCAAA;gBAChB,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC7B,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,SAAS,EAAE,gBAAgB;wBAC3B,KAAK,EAAE,yCAAkC,SAAS,CAAE;wBACpD,aAAa,EAAE,IAAI;wBACnB,UAAU,EAAE,oBAAoB;qBACjC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,yCAAyC;YACzC,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAEpD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,cAAc,EAAE,SAAS;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,kBAAkB;gBAC7B,KAAK,EAAE,0BAAmB,SAAS,CAAE;gBACrC,aAAa,EAAE,KAAK;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACY,oCAAkB,GAAjC,UAAkC,GAAQ;QAA1C,iBAgCC;QA/BC,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,GAAG,CAAC;QACb,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,yBAAyB;YACzB,OAAO,GAAG;iBACP,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC;iBAC3C,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;iBAC5B,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;iBAC1B,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;iBAC1B,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,2BAA2B;iBACvD,IAAI,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAA7B,CAA6B,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,IAAM,SAAS,GAAQ,EAAE,CAAC;YAC1B,KAA2B,UAAmB,EAAnB,KAAA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAnB,cAAmB,EAAnB,IAAmB,EAAE,CAAC;gBAAtC,IAAA,WAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;gBACpB,8BAA8B;gBAC9B,IAAM,YAAY,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC;oBAC5C,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC9D,SAAS,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3D,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACH,6CAAiB,GAAjB,UAAkB,UAAkB;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACpC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAExD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,qCAAqC;YACrC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;YAClE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAED,0CAA0C;QAC1C,IAAI,GAAG,GAAG,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YAClE,IAAI,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBAC5D,IAAI,CAAC,mBAAmB,CAAC;oBACvB,IAAI,EAAE,qBAAqB;oBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,sBAAe,UAAU,sBAAY,aAAa,CAAC,KAAK,CAAE;oBACnE,QAAQ,EAAE,QAAQ;iBACnB,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,qBAAqB;oBAC5B,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,qBAAqB;iBAClC,CAAC;YACJ,CAAC;YAED,kBAAkB;YAClB,aAAa,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,aAAa;YACb,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,+CAAmB,GAA3B,UAA4B,QAAmE;QAC7F,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtC,gCAAgC;QAChC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;QAEzC,yDAAyD;QACzD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iDAAqB,GAArB;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAC/C,UAAA,QAAQ,IAAI,OAAA,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAxD,CAAwD,CACrE,CAAC;QAEF,IAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,QAAQ;YACvD,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,IAAM,mBAAmB,GAAG,WAAW,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,QAAQ;YAC3D,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3D,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM;YAC7C,WAAW,EAAE,WAAW,CAAC,MAAM;YAC/B,eAAe,iBAAA;YACf,mBAAmB,qBAAA;YACnB,eAAe,EAAE;gBACf,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;gBACzC,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,8CAAkB,GAAlB;QACE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,wCAAY,GAAZ,UAAa,SAAkC;QAC7C,IAAI,CAAC,MAAM,yBAAQ,IAAI,CAAC,MAAM,GAAK,SAAS,CAAE,CAAC;IACjD,CAAC;IACH,wBAAC;AAAD,CAAC,AA5iBD,IA4iBC;AA5iBY,8CAAiB;AA8iB9B,4BAA4B;AACf,QAAA,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security/SecurityValidator.ts"],
      sourcesContent: ["import { performance } from 'perf_hooks';\n\nexport interface SecurityValidationResult {\n  isValid: boolean;\n  errorType?: 'SECURITY_ERROR' | 'VALIDATION_ERROR' | 'RESOURCE_ERROR';\n  error?: string;\n  securityAlert?: boolean;\n  sanitizedInput?: any;\n  threatType?: string;\n}\n\nexport interface SecurityConfig {\n  maxInputLength: number;\n  maxArrayLength: number;\n  enableSqlInjectionDetection: boolean;\n  enableXssDetection: boolean;\n  enableRateLimiting: boolean;\n  rateLimitWindow: number; // milliseconds\n  rateLimitMaxRequests: number;\n}\n\nexport interface SecurityIncident {\n  type: string;\n  userId?: string;\n  timestamp: Date;\n  request: string;\n  ipAddress?: string;\n  userAgent?: string;\n  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n}\n\n/**\n * Comprehensive security validator for skill gap analyzer\n */\nexport class SecurityValidator {\n  private static instance: SecurityValidator;\n  private config: SecurityConfig;\n  private rateLimitMap: Map<string, { count: number; windowStart: number }> = new Map();\n  private securityIncidents: SecurityIncident[] = [];\n\n  // Security patterns - using simple string patterns for better compatibility\n  private readonly SQL_INJECTION_PATTERNS = [\n    /DROP\\s+TABLE/i,\n    /DELETE\\s+FROM/i,\n    /INSERT\\s+INTO/i,\n    /UPDATE\\s+SET/i,\n    /UNION\\s+SELECT/i,\n    /OR\\s+1\\s*=\\s*1/i,\n    /AND\\s+1\\s*=\\s*1/i,\n    /'\\s*OR\\s*'/i,\n    /--/,\n    /\\/\\*/,\n    /\\*\\//\n  ];\n\n  private readonly XSS_PATTERNS = [\n    /<script[^>]*>.*?<\\/script>/gi,\n    /<iframe[^>]*>.*?<\\/iframe>/gi,\n    /<object[^>]*>.*?<\\/object>/gi,\n    /<embed[^>]*>/gi,\n    /<link[^>]*>/gi,\n    /<meta[^>]*>/gi,\n    /javascript:/gi,\n    /vbscript:/gi,\n    /data:text\\/html/gi,\n    /on\\w+\\s*=/gi, // Event handlers like onclick, onload, etc.\n    /<img[^>]*src[^>]*>/gi,\n    /<svg[^>]*>.*?<\\/svg>/gi\n  ];\n\n  private readonly NOSQL_INJECTION_PATTERNS = [\n    /\\$ne\\s*:/gi,\n    /\\$gt\\s*:/gi,\n    /\\$lt\\s*:/gi,\n    /\\$regex\\s*:/gi,\n    /\\$where\\s*:/gi,\n    /\\$or\\s*:/gi,\n    /\\$and\\s*:/gi,\n    /\\$in\\s*:/gi,\n    /\\$nin\\s*:/gi\n  ];\n\n  private readonly PROTOTYPE_POLLUTION_PATTERNS = [\n    /__proto__/gi,\n    /constructor/gi,\n    /prototype/gi\n  ];\n\n  private constructor() {\n    this.config = {\n      maxInputLength: 1000,\n      maxArrayLength: 100,\n      enableSqlInjectionDetection: true,\n      enableXssDetection: true,\n      enableRateLimiting: true,\n      rateLimitWindow: 60000, // 1 minute\n      rateLimitMaxRequests: 100\n    };\n  }\n\n  public static getInstance(): SecurityValidator {\n    if (!SecurityValidator.instance) {\n      SecurityValidator.instance = new SecurityValidator();\n    }\n    return SecurityValidator.instance;\n  }\n\n  /**\n   * Comprehensive input validation\n   */\n  validateInput(input: any, context: string = 'general'): SecurityValidationResult {\n    try {\n      // Check for null/undefined\n      if (input === null || input === undefined) {\n        return { isValid: true, sanitizedInput: input };\n      }\n\n      // Handle different input types\n      if (typeof input === 'string') {\n        return this.validateString(input, context);\n      } else if (Array.isArray(input)) {\n        return this.validateArray(input, context);\n      } else if (typeof input === 'object') {\n        return this.validateObject(input, context);\n      } else {\n        return this.validatePrimitive(input, context);\n      }\n    } catch (error) {\n      this.logSecurityIncident({\n        type: 'VALIDATION_ERROR',\n        timestamp: new Date(),\n        request: JSON.stringify(input),\n        severity: 'MEDIUM'\n      });\n\n      return {\n        isValid: false,\n        errorType: 'SECURITY_ERROR',\n        error: 'Input validation failed',\n        securityAlert: true\n      };\n    }\n  }\n\n  /**\n   * Validate string inputs\n   */\n  private validateString(input: string, context: string): SecurityValidationResult {\n    // Length validation\n    if (input.length > this.config.maxInputLength) {\n      this.logSecurityIncident({\n        type: 'DOS_ATTEMPT',\n        timestamp: new Date(),\n        request: `Input too long: ${input.length} characters`,\n        severity: 'HIGH'\n      });\n\n      return {\n        isValid: false,\n        errorType: 'VALIDATION_ERROR',\n        error: 'Input too long',\n        securityAlert: true,\n        threatType: 'DOS_ATTEMPT'\n      };\n    }\n\n    // SQL Injection detection\n    if (this.config.enableSqlInjectionDetection) {\n      for (const pattern of this.SQL_INJECTION_PATTERNS) {\n        if (pattern.test(input)) {\n          this.logSecurityIncident({\n            type: 'SQL_INJECTION_ATTEMPT',\n            timestamp: new Date(),\n            request: input,\n            severity: 'CRITICAL'\n          });\n\n          return {\n            isValid: false,\n            errorType: 'SECURITY_ERROR',\n            error: 'Malicious input detected',\n            securityAlert: true,\n            threatType: 'SQL_INJECTION'\n          };\n        }\n      }\n    }\n\n    // XSS detection\n    if (this.config.enableXssDetection) {\n      for (const pattern of this.XSS_PATTERNS) {\n        if (pattern.test(input)) {\n          this.logSecurityIncident({\n            type: 'XSS_ATTEMPT',\n            timestamp: new Date(),\n            request: input,\n            severity: 'HIGH'\n          });\n\n          return {\n            isValid: false,\n            errorType: 'SECURITY_ERROR',\n            error: 'Malicious input detected',\n            securityAlert: true,\n            threatType: 'XSS'\n          };\n        }\n      }\n    }\n\n    // NoSQL Injection detection\n    for (const pattern of this.NOSQL_INJECTION_PATTERNS) {\n      if (pattern.test(input)) {\n        this.logSecurityIncident({\n          type: 'NOSQL_INJECTION_ATTEMPT',\n          timestamp: new Date(),\n          request: input,\n          severity: 'HIGH'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious input detected',\n          securityAlert: true,\n          threatType: 'NOSQL_INJECTION'\n        };\n      }\n    }\n\n    // Sanitize the input\n    const sanitized = this.sanitizeString(input);\n\n    return {\n      isValid: true,\n      sanitizedInput: sanitized\n    };\n  }\n\n  /**\n   * Validate array inputs\n   */\n  private validateArray(input: any[], context: string): SecurityValidationResult {\n    // Length validation\n    if (input.length > this.config.maxArrayLength) {\n      this.logSecurityIncident({\n        type: 'DOS_ATTEMPT',\n        timestamp: new Date(),\n        request: `Array too large: ${input.length} items`,\n        severity: 'HIGH'\n      });\n\n      return {\n        isValid: false,\n        errorType: 'VALIDATION_ERROR',\n        error: 'Too many skills',\n        securityAlert: true,\n        threatType: 'DOS_ATTEMPT'\n      };\n    }\n\n    // Validate each item in the array\n    const sanitizedArray = [];\n    for (let i = 0; i < input.length; i++) {\n      const itemValidation = this.validateInput(input[i], `${context}[${i}]`);\n      \n      if (!itemValidation.isValid) {\n        // If any item is malicious, reject the entire array\n        this.logSecurityIncident({\n          type: 'ARRAY_INJECTION_ATTEMPT',\n          timestamp: new Date(),\n          request: JSON.stringify(input),\n          severity: 'HIGH'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious input detected in array',\n          securityAlert: true,\n          threatType: 'ARRAY_INJECTION'\n        };\n      }\n\n      sanitizedArray.push(itemValidation.sanitizedInput);\n    }\n\n    return {\n      isValid: true,\n      sanitizedInput: sanitizedArray\n    };\n  }\n\n  /**\n   * Validate object inputs\n   */\n  private validateObject(input: any, context: string): SecurityValidationResult {\n    // Check for prototype pollution\n    for (const pattern of this.PROTOTYPE_POLLUTION_PATTERNS) {\n      const inputString = JSON.stringify(input);\n      if (pattern.test(inputString)) {\n        this.logSecurityIncident({\n          type: 'PROTOTYPE_POLLUTION_ATTEMPT',\n          timestamp: new Date(),\n          request: inputString,\n          severity: 'CRITICAL'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious object structure detected',\n          securityAlert: true,\n          threatType: 'PROTOTYPE_POLLUTION'\n        };\n      }\n    }\n\n    // Validate each property\n    const sanitizedObject: any = {};\n    for (const [key, value] of Object.entries(input)) {\n      const keyValidation = this.validateInput(key, `${context}.key`);\n      const valueValidation = this.validateInput(value, `${context}.${key}`);\n\n      if (!keyValidation.isValid || !valueValidation.isValid) {\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: 'Malicious input detected in object',\n          securityAlert: true\n        };\n      }\n\n      sanitizedObject[keyValidation.sanitizedInput || key] = valueValidation.sanitizedInput;\n    }\n\n    return {\n      isValid: true,\n      sanitizedInput: sanitizedObject\n    };\n  }\n\n  /**\n   * Validate primitive inputs\n   */\n  private validatePrimitive(input: any, context: string): SecurityValidationResult {\n    // Convert to string and validate\n    const stringValue = String(input);\n    return this.validateString(stringValue, context);\n  }\n\n  /**\n   * Sanitize string input\n   */\n  private sanitizeString(input: string): string {\n    return input\n      .trim()\n      .replace(/[<>]/g, '') // Remove angle brackets\n      .replace(/javascript:/gi, '') // Remove javascript: protocol\n      .replace(/vbscript:/gi, '') // Remove vbscript: protocol\n      .replace(/data:text\\/html/gi, '') // Remove data URLs\n      .replace(/on\\w+\\s*=/gi, ''); // Remove event handlers\n  }\n\n  /**\n   * Check if input contains XSS patterns\n   */\n  static containsXSS(input: string): boolean {\n    if (!input || typeof input !== 'string') {\n      return false;\n    }\n\n    const xssPatterns = [\n      /<script[^>]*>.*?<\\/script>/gi,\n      /<iframe[^>]*>.*?<\\/iframe>/gi,\n      /<object[^>]*>.*?<\\/object>/gi,\n      /<embed[^>]*>/gi,\n      /<link[^>]*>/gi,\n      /<meta[^>]*>/gi,\n      /javascript:/gi,\n      /vbscript:/gi,\n      /data:text\\/html/gi,\n      /on\\w+\\s*=/gi, // Event handlers like onclick, onload, etc.\n      /<img[^>]*src[^>]*>/gi,\n      /<svg[^>]*>.*?<\\/svg>/gi\n    ];\n\n    return xssPatterns.some(pattern => pattern.test(input));\n  }\n\n  /**\n   * Validate and sanitize JSON fields\n   */\n  static validateJsonField(jsonData: any, fieldName: string): SecurityValidationResult {\n    try {\n      if (!jsonData) {\n        return { isValid: true, sanitizedInput: jsonData };\n      }\n\n      const jsonString = JSON.stringify(jsonData);\n\n      // Check for XSS in JSON\n      if (this.containsXSS(jsonString)) {\n        return {\n          isValid: false,\n          errorType: 'SECURITY_ERROR',\n          error: `XSS detected in ${fieldName}`,\n          securityAlert: true,\n          threatType: 'XSS'\n        };\n      }\n\n      // Check for template injection\n      const templateInjectionPatterns = [\n        /\\$\\{[^}]*\\}/g, // ${...} template literals\n        /\\{\\{[^}]*\\}\\}/g, // {{...}} handlebars/mustache\n        /%\\{[^}]*\\}/g, // %{...} ruby-style\n      ];\n\n      for (const pattern of templateInjectionPatterns) {\n        if (pattern.test(jsonString)) {\n          return {\n            isValid: false,\n            errorType: 'SECURITY_ERROR',\n            error: `Template injection detected in ${fieldName}`,\n            securityAlert: true,\n            threatType: 'TEMPLATE_INJECTION'\n          };\n        }\n      }\n\n      // Recursively sanitize object properties\n      const sanitized = this.sanitizeJsonObject(jsonData);\n\n      return {\n        isValid: true,\n        sanitizedInput: sanitized\n      };\n    } catch (error) {\n      return {\n        isValid: false,\n        errorType: 'VALIDATION_ERROR',\n        error: `Invalid JSON in ${fieldName}`,\n        securityAlert: false\n      };\n    }\n  }\n\n  /**\n   * Recursively sanitize JSON object\n   */\n  private static sanitizeJsonObject(obj: any): any {\n    if (obj === null || obj === undefined) {\n      return obj;\n    }\n\n    if (typeof obj === 'string') {\n      // Sanitize string values\n      return obj\n        .replace(/<script[^>]*>.*?<\\/script>/gi, '')\n        .replace(/javascript:/gi, '')\n        .replace(/vbscript:/gi, '')\n        .replace(/on\\w+\\s*=/gi, '')\n        .replace(/\\$\\{[^}]*\\}/g, '') // Remove template literals\n        .trim();\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(item => this.sanitizeJsonObject(item));\n    }\n\n    if (typeof obj === 'object') {\n      const sanitized: any = {};\n      for (const [key, value] of Object.entries(obj)) {\n        // Sanitize both key and value\n        const sanitizedKey = typeof key === 'string' ?\n          key.replace(/[<>]/g, '').replace(/javascript:/gi, '') : key;\n        sanitized[sanitizedKey] = this.sanitizeJsonObject(value);\n      }\n      return sanitized;\n    }\n\n    return obj;\n  }\n\n  /**\n   * Rate limiting validation\n   */\n  validateRateLimit(identifier: string): SecurityValidationResult {\n    if (!this.config.enableRateLimiting) {\n      return { isValid: true };\n    }\n\n    const now = Date.now();\n    const rateLimitData = this.rateLimitMap.get(identifier);\n\n    if (!rateLimitData) {\n      // First request from this identifier\n      this.rateLimitMap.set(identifier, { count: 1, windowStart: now });\n      return { isValid: true };\n    }\n\n    // Check if we're still in the same window\n    if (now - rateLimitData.windowStart < this.config.rateLimitWindow) {\n      if (rateLimitData.count >= this.config.rateLimitMaxRequests) {\n        this.logSecurityIncident({\n          type: 'RATE_LIMIT_EXCEEDED',\n          timestamp: new Date(),\n          request: `Identifier: ${identifier}, Count: ${rateLimitData.count}`,\n          severity: 'MEDIUM'\n        });\n\n        return {\n          isValid: false,\n          errorType: 'RESOURCE_ERROR',\n          error: 'Rate limit exceeded',\n          securityAlert: true,\n          threatType: 'RATE_LIMIT_EXCEEDED'\n        };\n      }\n\n      // Increment count\n      rateLimitData.count++;\n    } else {\n      // New window\n      this.rateLimitMap.set(identifier, { count: 1, windowStart: now });\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Log security incident\n   */\n  private logSecurityIncident(incident: Omit<SecurityIncident, 'timestamp'> & { timestamp: Date }): void {\n    this.securityIncidents.push(incident);\n\n    // Log to console for monitoring\n    console.warn('SECURITY_ALERT', incident);\n\n    // Keep only last 1000 incidents to prevent memory issues\n    if (this.securityIncidents.length > 1000) {\n      this.securityIncidents = this.securityIncidents.slice(-1000);\n    }\n  }\n\n  /**\n   * Get security statistics\n   */\n  getSecurityStatistics(): any {\n    const now = Date.now();\n    const last24Hours = this.securityIncidents.filter(\n      incident => now - incident.timestamp.getTime() < 24 * 60 * 60 * 1000\n    );\n\n    const incidentsByType = last24Hours.reduce((acc, incident) => {\n      acc[incident.type] = (acc[incident.type] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n\n    const incidentsBySeverity = last24Hours.reduce((acc, incident) => {\n      acc[incident.severity] = (acc[incident.severity] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n\n    return {\n      totalIncidents: this.securityIncidents.length,\n      last24Hours: last24Hours.length,\n      incidentsByType,\n      incidentsBySeverity,\n      rateLimitStatus: {\n        activeIdentifiers: this.rateLimitMap.size,\n        config: this.config\n      }\n    };\n  }\n\n  /**\n   * Clear rate limit data (for testing)\n   */\n  clearRateLimitData(): void {\n    this.rateLimitMap.clear();\n  }\n\n  /**\n   * Update security configuration\n   */\n  updateConfig(newConfig: Partial<SecurityConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n  }\n}\n\n// Export singleton instance\nexport const securityValidator = SecurityValidator.getInstance();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c360f7a43e827c120ef2dccc2e14e07d8dd13785"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_13eriynybl = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_13eriynybl();
var __assign =
/* istanbul ignore next */
(cov_13eriynybl().s[0]++,
/* istanbul ignore next */
(cov_13eriynybl().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_13eriynybl().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_13eriynybl().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_13eriynybl().f[0]++;
  cov_13eriynybl().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_13eriynybl().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_13eriynybl().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_13eriynybl().f[1]++;
    cov_13eriynybl().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_13eriynybl().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_13eriynybl().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_13eriynybl().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_13eriynybl().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_13eriynybl().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_13eriynybl().b[2][0]++;
          cov_13eriynybl().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_13eriynybl().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_13eriynybl().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_13eriynybl().s[10]++;
  return __assign.apply(this, arguments);
}));
/* istanbul ignore next */
cov_13eriynybl().s[11]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_13eriynybl().s[12]++;
exports.securityValidator = exports.SecurityValidator = void 0;
/**
 * Comprehensive security validator for skill gap analyzer
 */
var SecurityValidator =
/* istanbul ignore next */
(/** @class */cov_13eriynybl().s[13]++, function () {
  /* istanbul ignore next */
  cov_13eriynybl().f[2]++;
  function SecurityValidator() {
    /* istanbul ignore next */
    cov_13eriynybl().f[3]++;
    cov_13eriynybl().s[14]++;
    this.rateLimitMap = new Map();
    /* istanbul ignore next */
    cov_13eriynybl().s[15]++;
    this.securityIncidents = [];
    // Security patterns - using simple string patterns for better compatibility
    /* istanbul ignore next */
    cov_13eriynybl().s[16]++;
    this.SQL_INJECTION_PATTERNS = [/DROP\s+TABLE/i, /DELETE\s+FROM/i, /INSERT\s+INTO/i, /UPDATE\s+SET/i, /UNION\s+SELECT/i, /OR\s+1\s*=\s*1/i, /AND\s+1\s*=\s*1/i, /'\s*OR\s*'/i, /--/, /\/\*/, /\*\//];
    /* istanbul ignore next */
    cov_13eriynybl().s[17]++;
    this.XSS_PATTERNS = [/<script[^>]*>.*?<\/script>/gi, /<iframe[^>]*>.*?<\/iframe>/gi, /<object[^>]*>.*?<\/object>/gi, /<embed[^>]*>/gi, /<link[^>]*>/gi, /<meta[^>]*>/gi, /javascript:/gi, /vbscript:/gi, /data:text\/html/gi, /on\w+\s*=/gi,
    // Event handlers like onclick, onload, etc.
    /<img[^>]*src[^>]*>/gi, /<svg[^>]*>.*?<\/svg>/gi];
    /* istanbul ignore next */
    cov_13eriynybl().s[18]++;
    this.NOSQL_INJECTION_PATTERNS = [/\$ne\s*:/gi, /\$gt\s*:/gi, /\$lt\s*:/gi, /\$regex\s*:/gi, /\$where\s*:/gi, /\$or\s*:/gi, /\$and\s*:/gi, /\$in\s*:/gi, /\$nin\s*:/gi];
    /* istanbul ignore next */
    cov_13eriynybl().s[19]++;
    this.PROTOTYPE_POLLUTION_PATTERNS = [/__proto__/gi, /constructor/gi, /prototype/gi];
    /* istanbul ignore next */
    cov_13eriynybl().s[20]++;
    this.config = {
      maxInputLength: 1000,
      maxArrayLength: 100,
      enableSqlInjectionDetection: true,
      enableXssDetection: true,
      enableRateLimiting: true,
      rateLimitWindow: 60000,
      // 1 minute
      rateLimitMaxRequests: 100
    };
  }
  /* istanbul ignore next */
  cov_13eriynybl().s[21]++;
  SecurityValidator.getInstance = function () {
    /* istanbul ignore next */
    cov_13eriynybl().f[4]++;
    cov_13eriynybl().s[22]++;
    if (!SecurityValidator.instance) {
      /* istanbul ignore next */
      cov_13eriynybl().b[3][0]++;
      cov_13eriynybl().s[23]++;
      SecurityValidator.instance = new SecurityValidator();
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[3][1]++;
    }
    cov_13eriynybl().s[24]++;
    return SecurityValidator.instance;
  };
  /**
   * Comprehensive input validation
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[25]++;
  SecurityValidator.prototype.validateInput = function (input, context) {
    /* istanbul ignore next */
    cov_13eriynybl().f[5]++;
    cov_13eriynybl().s[26]++;
    if (context === void 0) {
      /* istanbul ignore next */
      cov_13eriynybl().b[4][0]++;
      cov_13eriynybl().s[27]++;
      context = 'general';
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[4][1]++;
    }
    cov_13eriynybl().s[28]++;
    try {
      /* istanbul ignore next */
      cov_13eriynybl().s[29]++;
      // Check for null/undefined
      if (
      /* istanbul ignore next */
      (cov_13eriynybl().b[6][0]++, input === null) ||
      /* istanbul ignore next */
      (cov_13eriynybl().b[6][1]++, input === undefined)) {
        /* istanbul ignore next */
        cov_13eriynybl().b[5][0]++;
        cov_13eriynybl().s[30]++;
        return {
          isValid: true,
          sanitizedInput: input
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[5][1]++;
      }
      // Handle different input types
      cov_13eriynybl().s[31]++;
      if (typeof input === 'string') {
        /* istanbul ignore next */
        cov_13eriynybl().b[7][0]++;
        cov_13eriynybl().s[32]++;
        return this.validateString(input, context);
      } else {
        /* istanbul ignore next */
        cov_13eriynybl().b[7][1]++;
        cov_13eriynybl().s[33]++;
        if (Array.isArray(input)) {
          /* istanbul ignore next */
          cov_13eriynybl().b[8][0]++;
          cov_13eriynybl().s[34]++;
          return this.validateArray(input, context);
        } else {
          /* istanbul ignore next */
          cov_13eriynybl().b[8][1]++;
          cov_13eriynybl().s[35]++;
          if (typeof input === 'object') {
            /* istanbul ignore next */
            cov_13eriynybl().b[9][0]++;
            cov_13eriynybl().s[36]++;
            return this.validateObject(input, context);
          } else {
            /* istanbul ignore next */
            cov_13eriynybl().b[9][1]++;
            cov_13eriynybl().s[37]++;
            return this.validatePrimitive(input, context);
          }
        }
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_13eriynybl().s[38]++;
      this.logSecurityIncident({
        type: 'VALIDATION_ERROR',
        timestamp: new Date(),
        request: JSON.stringify(input),
        severity: 'MEDIUM'
      });
      /* istanbul ignore next */
      cov_13eriynybl().s[39]++;
      return {
        isValid: false,
        errorType: 'SECURITY_ERROR',
        error: 'Input validation failed',
        securityAlert: true
      };
    }
  };
  /**
   * Validate string inputs
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[40]++;
  SecurityValidator.prototype.validateString = function (input, context) {
    /* istanbul ignore next */
    cov_13eriynybl().f[6]++;
    cov_13eriynybl().s[41]++;
    // Length validation
    if (input.length > this.config.maxInputLength) {
      /* istanbul ignore next */
      cov_13eriynybl().b[10][0]++;
      cov_13eriynybl().s[42]++;
      this.logSecurityIncident({
        type: 'DOS_ATTEMPT',
        timestamp: new Date(),
        request: "Input too long: ".concat(input.length, " characters"),
        severity: 'HIGH'
      });
      /* istanbul ignore next */
      cov_13eriynybl().s[43]++;
      return {
        isValid: false,
        errorType: 'VALIDATION_ERROR',
        error: 'Input too long',
        securityAlert: true,
        threatType: 'DOS_ATTEMPT'
      };
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[10][1]++;
    }
    // SQL Injection detection
    cov_13eriynybl().s[44]++;
    if (this.config.enableSqlInjectionDetection) {
      /* istanbul ignore next */
      cov_13eriynybl().b[11][0]++;
      cov_13eriynybl().s[45]++;
      for (var _i =
        /* istanbul ignore next */
        (cov_13eriynybl().s[46]++, 0), _a =
        /* istanbul ignore next */
        (cov_13eriynybl().s[47]++, this.SQL_INJECTION_PATTERNS); _i < _a.length; _i++) {
        var pattern =
        /* istanbul ignore next */
        (cov_13eriynybl().s[48]++, _a[_i]);
        /* istanbul ignore next */
        cov_13eriynybl().s[49]++;
        if (pattern.test(input)) {
          /* istanbul ignore next */
          cov_13eriynybl().b[12][0]++;
          cov_13eriynybl().s[50]++;
          this.logSecurityIncident({
            type: 'SQL_INJECTION_ATTEMPT',
            timestamp: new Date(),
            request: input,
            severity: 'CRITICAL'
          });
          /* istanbul ignore next */
          cov_13eriynybl().s[51]++;
          return {
            isValid: false,
            errorType: 'SECURITY_ERROR',
            error: 'Malicious input detected',
            securityAlert: true,
            threatType: 'SQL_INJECTION'
          };
        } else
        /* istanbul ignore next */
        {
          cov_13eriynybl().b[12][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[11][1]++;
    }
    // XSS detection
    cov_13eriynybl().s[52]++;
    if (this.config.enableXssDetection) {
      /* istanbul ignore next */
      cov_13eriynybl().b[13][0]++;
      cov_13eriynybl().s[53]++;
      for (var _b =
        /* istanbul ignore next */
        (cov_13eriynybl().s[54]++, 0), _c =
        /* istanbul ignore next */
        (cov_13eriynybl().s[55]++, this.XSS_PATTERNS); _b < _c.length; _b++) {
        var pattern =
        /* istanbul ignore next */
        (cov_13eriynybl().s[56]++, _c[_b]);
        /* istanbul ignore next */
        cov_13eriynybl().s[57]++;
        if (pattern.test(input)) {
          /* istanbul ignore next */
          cov_13eriynybl().b[14][0]++;
          cov_13eriynybl().s[58]++;
          this.logSecurityIncident({
            type: 'XSS_ATTEMPT',
            timestamp: new Date(),
            request: input,
            severity: 'HIGH'
          });
          /* istanbul ignore next */
          cov_13eriynybl().s[59]++;
          return {
            isValid: false,
            errorType: 'SECURITY_ERROR',
            error: 'Malicious input detected',
            securityAlert: true,
            threatType: 'XSS'
          };
        } else
        /* istanbul ignore next */
        {
          cov_13eriynybl().b[14][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[13][1]++;
    }
    // NoSQL Injection detection
    cov_13eriynybl().s[60]++;
    for (var _d =
      /* istanbul ignore next */
      (cov_13eriynybl().s[61]++, 0), _e =
      /* istanbul ignore next */
      (cov_13eriynybl().s[62]++, this.NOSQL_INJECTION_PATTERNS); _d < _e.length; _d++) {
      var pattern =
      /* istanbul ignore next */
      (cov_13eriynybl().s[63]++, _e[_d]);
      /* istanbul ignore next */
      cov_13eriynybl().s[64]++;
      if (pattern.test(input)) {
        /* istanbul ignore next */
        cov_13eriynybl().b[15][0]++;
        cov_13eriynybl().s[65]++;
        this.logSecurityIncident({
          type: 'NOSQL_INJECTION_ATTEMPT',
          timestamp: new Date(),
          request: input,
          severity: 'HIGH'
        });
        /* istanbul ignore next */
        cov_13eriynybl().s[66]++;
        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious input detected',
          securityAlert: true,
          threatType: 'NOSQL_INJECTION'
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[15][1]++;
      }
    }
    // Sanitize the input
    var sanitized =
    /* istanbul ignore next */
    (cov_13eriynybl().s[67]++, this.sanitizeString(input));
    /* istanbul ignore next */
    cov_13eriynybl().s[68]++;
    return {
      isValid: true,
      sanitizedInput: sanitized
    };
  };
  /**
   * Validate array inputs
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[69]++;
  SecurityValidator.prototype.validateArray = function (input, context) {
    /* istanbul ignore next */
    cov_13eriynybl().f[7]++;
    cov_13eriynybl().s[70]++;
    // Length validation
    if (input.length > this.config.maxArrayLength) {
      /* istanbul ignore next */
      cov_13eriynybl().b[16][0]++;
      cov_13eriynybl().s[71]++;
      this.logSecurityIncident({
        type: 'DOS_ATTEMPT',
        timestamp: new Date(),
        request: "Array too large: ".concat(input.length, " items"),
        severity: 'HIGH'
      });
      /* istanbul ignore next */
      cov_13eriynybl().s[72]++;
      return {
        isValid: false,
        errorType: 'VALIDATION_ERROR',
        error: 'Too many skills',
        securityAlert: true,
        threatType: 'DOS_ATTEMPT'
      };
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[16][1]++;
    }
    // Validate each item in the array
    var sanitizedArray =
    /* istanbul ignore next */
    (cov_13eriynybl().s[73]++, []);
    /* istanbul ignore next */
    cov_13eriynybl().s[74]++;
    for (var i =
    /* istanbul ignore next */
    (cov_13eriynybl().s[75]++, 0); i < input.length; i++) {
      var itemValidation =
      /* istanbul ignore next */
      (cov_13eriynybl().s[76]++, this.validateInput(input[i], "".concat(context, "[").concat(i, "]")));
      /* istanbul ignore next */
      cov_13eriynybl().s[77]++;
      if (!itemValidation.isValid) {
        /* istanbul ignore next */
        cov_13eriynybl().b[17][0]++;
        cov_13eriynybl().s[78]++;
        // If any item is malicious, reject the entire array
        this.logSecurityIncident({
          type: 'ARRAY_INJECTION_ATTEMPT',
          timestamp: new Date(),
          request: JSON.stringify(input),
          severity: 'HIGH'
        });
        /* istanbul ignore next */
        cov_13eriynybl().s[79]++;
        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious input detected in array',
          securityAlert: true,
          threatType: 'ARRAY_INJECTION'
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[17][1]++;
      }
      cov_13eriynybl().s[80]++;
      sanitizedArray.push(itemValidation.sanitizedInput);
    }
    /* istanbul ignore next */
    cov_13eriynybl().s[81]++;
    return {
      isValid: true,
      sanitizedInput: sanitizedArray
    };
  };
  /**
   * Validate object inputs
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[82]++;
  SecurityValidator.prototype.validateObject = function (input, context) {
    /* istanbul ignore next */
    cov_13eriynybl().f[8]++;
    cov_13eriynybl().s[83]++;
    // Check for prototype pollution
    for (var _i =
      /* istanbul ignore next */
      (cov_13eriynybl().s[84]++, 0), _a =
      /* istanbul ignore next */
      (cov_13eriynybl().s[85]++, this.PROTOTYPE_POLLUTION_PATTERNS); _i < _a.length; _i++) {
      var pattern =
      /* istanbul ignore next */
      (cov_13eriynybl().s[86]++, _a[_i]);
      var inputString =
      /* istanbul ignore next */
      (cov_13eriynybl().s[87]++, JSON.stringify(input));
      /* istanbul ignore next */
      cov_13eriynybl().s[88]++;
      if (pattern.test(inputString)) {
        /* istanbul ignore next */
        cov_13eriynybl().b[18][0]++;
        cov_13eriynybl().s[89]++;
        this.logSecurityIncident({
          type: 'PROTOTYPE_POLLUTION_ATTEMPT',
          timestamp: new Date(),
          request: inputString,
          severity: 'CRITICAL'
        });
        /* istanbul ignore next */
        cov_13eriynybl().s[90]++;
        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious object structure detected',
          securityAlert: true,
          threatType: 'PROTOTYPE_POLLUTION'
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[18][1]++;
      }
    }
    // Validate each property
    var sanitizedObject =
    /* istanbul ignore next */
    (cov_13eriynybl().s[91]++, {});
    /* istanbul ignore next */
    cov_13eriynybl().s[92]++;
    for (var _b =
      /* istanbul ignore next */
      (cov_13eriynybl().s[93]++, 0), _c =
      /* istanbul ignore next */
      (cov_13eriynybl().s[94]++, Object.entries(input)); _b < _c.length; _b++) {
      var _d =
        /* istanbul ignore next */
        (cov_13eriynybl().s[95]++, _c[_b]),
        key =
        /* istanbul ignore next */
        (cov_13eriynybl().s[96]++, _d[0]),
        value =
        /* istanbul ignore next */
        (cov_13eriynybl().s[97]++, _d[1]);
      var keyValidation =
      /* istanbul ignore next */
      (cov_13eriynybl().s[98]++, this.validateInput(key, "".concat(context, ".key")));
      var valueValidation =
      /* istanbul ignore next */
      (cov_13eriynybl().s[99]++, this.validateInput(value, "".concat(context, ".").concat(key)));
      /* istanbul ignore next */
      cov_13eriynybl().s[100]++;
      if (
      /* istanbul ignore next */
      (cov_13eriynybl().b[20][0]++, !keyValidation.isValid) ||
      /* istanbul ignore next */
      (cov_13eriynybl().b[20][1]++, !valueValidation.isValid)) {
        /* istanbul ignore next */
        cov_13eriynybl().b[19][0]++;
        cov_13eriynybl().s[101]++;
        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious input detected in object',
          securityAlert: true
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[19][1]++;
      }
      cov_13eriynybl().s[102]++;
      sanitizedObject[
      /* istanbul ignore next */
      (cov_13eriynybl().b[21][0]++, keyValidation.sanitizedInput) ||
      /* istanbul ignore next */
      (cov_13eriynybl().b[21][1]++, key)] = valueValidation.sanitizedInput;
    }
    /* istanbul ignore next */
    cov_13eriynybl().s[103]++;
    return {
      isValid: true,
      sanitizedInput: sanitizedObject
    };
  };
  /**
   * Validate primitive inputs
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[104]++;
  SecurityValidator.prototype.validatePrimitive = function (input, context) {
    /* istanbul ignore next */
    cov_13eriynybl().f[9]++;
    // Convert to string and validate
    var stringValue =
    /* istanbul ignore next */
    (cov_13eriynybl().s[105]++, String(input));
    /* istanbul ignore next */
    cov_13eriynybl().s[106]++;
    return this.validateString(stringValue, context);
  };
  /**
   * Sanitize string input
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[107]++;
  SecurityValidator.prototype.sanitizeString = function (input) {
    /* istanbul ignore next */
    cov_13eriynybl().f[10]++;
    cov_13eriynybl().s[108]++;
    return input.trim().replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/data:text\/html/gi, '') // Remove data URLs
    .replace(/on\w+\s*=/gi, ''); // Remove event handlers
  };
  /**
   * Check if input contains XSS patterns
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[109]++;
  SecurityValidator.containsXSS = function (input) {
    /* istanbul ignore next */
    cov_13eriynybl().f[11]++;
    cov_13eriynybl().s[110]++;
    if (
    /* istanbul ignore next */
    (cov_13eriynybl().b[23][0]++, !input) ||
    /* istanbul ignore next */
    (cov_13eriynybl().b[23][1]++, typeof input !== 'string')) {
      /* istanbul ignore next */
      cov_13eriynybl().b[22][0]++;
      cov_13eriynybl().s[111]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[22][1]++;
    }
    var xssPatterns =
    /* istanbul ignore next */
    (cov_13eriynybl().s[112]++, [/<script[^>]*>.*?<\/script>/gi, /<iframe[^>]*>.*?<\/iframe>/gi, /<object[^>]*>.*?<\/object>/gi, /<embed[^>]*>/gi, /<link[^>]*>/gi, /<meta[^>]*>/gi, /javascript:/gi, /vbscript:/gi, /data:text\/html/gi, /on\w+\s*=/gi,
    // Event handlers like onclick, onload, etc.
    /<img[^>]*src[^>]*>/gi, /<svg[^>]*>.*?<\/svg>/gi]);
    /* istanbul ignore next */
    cov_13eriynybl().s[113]++;
    return xssPatterns.some(function (pattern) {
      /* istanbul ignore next */
      cov_13eriynybl().f[12]++;
      cov_13eriynybl().s[114]++;
      return pattern.test(input);
    });
  };
  /**
   * Validate and sanitize JSON fields
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[115]++;
  SecurityValidator.validateJsonField = function (jsonData, fieldName) {
    /* istanbul ignore next */
    cov_13eriynybl().f[13]++;
    cov_13eriynybl().s[116]++;
    try {
      /* istanbul ignore next */
      cov_13eriynybl().s[117]++;
      if (!jsonData) {
        /* istanbul ignore next */
        cov_13eriynybl().b[24][0]++;
        cov_13eriynybl().s[118]++;
        return {
          isValid: true,
          sanitizedInput: jsonData
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[24][1]++;
      }
      var jsonString =
      /* istanbul ignore next */
      (cov_13eriynybl().s[119]++, JSON.stringify(jsonData));
      // Check for XSS in JSON
      /* istanbul ignore next */
      cov_13eriynybl().s[120]++;
      if (this.containsXSS(jsonString)) {
        /* istanbul ignore next */
        cov_13eriynybl().b[25][0]++;
        cov_13eriynybl().s[121]++;
        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: "XSS detected in ".concat(fieldName),
          securityAlert: true,
          threatType: 'XSS'
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[25][1]++;
      }
      // Check for template injection
      var templateInjectionPatterns =
      /* istanbul ignore next */
      (cov_13eriynybl().s[122]++, [/\$\{[^}]*\}/g,
      // ${...} template literals
      /\{\{[^}]*\}\}/g,
      // {{...}} handlebars/mustache
      /%\{[^}]*\}/g // %{...} ruby-style
      ]);
      /* istanbul ignore next */
      cov_13eriynybl().s[123]++;
      for (var _i =
        /* istanbul ignore next */
        (cov_13eriynybl().s[124]++, 0), templateInjectionPatterns_1 =
        /* istanbul ignore next */
        (cov_13eriynybl().s[125]++, templateInjectionPatterns); _i < templateInjectionPatterns_1.length; _i++) {
        var pattern =
        /* istanbul ignore next */
        (cov_13eriynybl().s[126]++, templateInjectionPatterns_1[_i]);
        /* istanbul ignore next */
        cov_13eriynybl().s[127]++;
        if (pattern.test(jsonString)) {
          /* istanbul ignore next */
          cov_13eriynybl().b[26][0]++;
          cov_13eriynybl().s[128]++;
          return {
            isValid: false,
            errorType: 'SECURITY_ERROR',
            error: "Template injection detected in ".concat(fieldName),
            securityAlert: true,
            threatType: 'TEMPLATE_INJECTION'
          };
        } else
        /* istanbul ignore next */
        {
          cov_13eriynybl().b[26][1]++;
        }
      }
      // Recursively sanitize object properties
      var sanitized =
      /* istanbul ignore next */
      (cov_13eriynybl().s[129]++, this.sanitizeJsonObject(jsonData));
      /* istanbul ignore next */
      cov_13eriynybl().s[130]++;
      return {
        isValid: true,
        sanitizedInput: sanitized
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_13eriynybl().s[131]++;
      return {
        isValid: false,
        errorType: 'VALIDATION_ERROR',
        error: "Invalid JSON in ".concat(fieldName),
        securityAlert: false
      };
    }
  };
  /**
   * Recursively sanitize JSON object
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[132]++;
  SecurityValidator.sanitizeJsonObject = function (obj) {
    /* istanbul ignore next */
    cov_13eriynybl().f[14]++;
    var _this =
    /* istanbul ignore next */
    (cov_13eriynybl().s[133]++, this);
    /* istanbul ignore next */
    cov_13eriynybl().s[134]++;
    if (
    /* istanbul ignore next */
    (cov_13eriynybl().b[28][0]++, obj === null) ||
    /* istanbul ignore next */
    (cov_13eriynybl().b[28][1]++, obj === undefined)) {
      /* istanbul ignore next */
      cov_13eriynybl().b[27][0]++;
      cov_13eriynybl().s[135]++;
      return obj;
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[27][1]++;
    }
    cov_13eriynybl().s[136]++;
    if (typeof obj === 'string') {
      /* istanbul ignore next */
      cov_13eriynybl().b[29][0]++;
      cov_13eriynybl().s[137]++;
      // Sanitize string values
      return obj.replace(/<script[^>]*>.*?<\/script>/gi, '').replace(/javascript:/gi, '').replace(/vbscript:/gi, '').replace(/on\w+\s*=/gi, '').replace(/\$\{[^}]*\}/g, '') // Remove template literals
      .trim();
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[29][1]++;
    }
    cov_13eriynybl().s[138]++;
    if (Array.isArray(obj)) {
      /* istanbul ignore next */
      cov_13eriynybl().b[30][0]++;
      cov_13eriynybl().s[139]++;
      return obj.map(function (item) {
        /* istanbul ignore next */
        cov_13eriynybl().f[15]++;
        cov_13eriynybl().s[140]++;
        return _this.sanitizeJsonObject(item);
      });
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[30][1]++;
    }
    cov_13eriynybl().s[141]++;
    if (typeof obj === 'object') {
      /* istanbul ignore next */
      cov_13eriynybl().b[31][0]++;
      var sanitized =
      /* istanbul ignore next */
      (cov_13eriynybl().s[142]++, {});
      /* istanbul ignore next */
      cov_13eriynybl().s[143]++;
      for (var _i =
        /* istanbul ignore next */
        (cov_13eriynybl().s[144]++, 0), _a =
        /* istanbul ignore next */
        (cov_13eriynybl().s[145]++, Object.entries(obj)); _i < _a.length; _i++) {
        var _b =
          /* istanbul ignore next */
          (cov_13eriynybl().s[146]++, _a[_i]),
          key =
          /* istanbul ignore next */
          (cov_13eriynybl().s[147]++, _b[0]),
          value =
          /* istanbul ignore next */
          (cov_13eriynybl().s[148]++, _b[1]);
        // Sanitize both key and value
        var sanitizedKey =
        /* istanbul ignore next */
        (cov_13eriynybl().s[149]++, typeof key === 'string' ?
        /* istanbul ignore next */
        (cov_13eriynybl().b[32][0]++, key.replace(/[<>]/g, '').replace(/javascript:/gi, '')) :
        /* istanbul ignore next */
        (cov_13eriynybl().b[32][1]++, key));
        /* istanbul ignore next */
        cov_13eriynybl().s[150]++;
        sanitized[sanitizedKey] = this.sanitizeJsonObject(value);
      }
      /* istanbul ignore next */
      cov_13eriynybl().s[151]++;
      return sanitized;
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[31][1]++;
    }
    cov_13eriynybl().s[152]++;
    return obj;
  };
  /**
   * Rate limiting validation
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[153]++;
  SecurityValidator.prototype.validateRateLimit = function (identifier) {
    /* istanbul ignore next */
    cov_13eriynybl().f[16]++;
    cov_13eriynybl().s[154]++;
    if (!this.config.enableRateLimiting) {
      /* istanbul ignore next */
      cov_13eriynybl().b[33][0]++;
      cov_13eriynybl().s[155]++;
      return {
        isValid: true
      };
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[33][1]++;
    }
    var now =
    /* istanbul ignore next */
    (cov_13eriynybl().s[156]++, Date.now());
    var rateLimitData =
    /* istanbul ignore next */
    (cov_13eriynybl().s[157]++, this.rateLimitMap.get(identifier));
    /* istanbul ignore next */
    cov_13eriynybl().s[158]++;
    if (!rateLimitData) {
      /* istanbul ignore next */
      cov_13eriynybl().b[34][0]++;
      cov_13eriynybl().s[159]++;
      // First request from this identifier
      this.rateLimitMap.set(identifier, {
        count: 1,
        windowStart: now
      });
      /* istanbul ignore next */
      cov_13eriynybl().s[160]++;
      return {
        isValid: true
      };
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[34][1]++;
    }
    // Check if we're still in the same window
    cov_13eriynybl().s[161]++;
    if (now - rateLimitData.windowStart < this.config.rateLimitWindow) {
      /* istanbul ignore next */
      cov_13eriynybl().b[35][0]++;
      cov_13eriynybl().s[162]++;
      if (rateLimitData.count >= this.config.rateLimitMaxRequests) {
        /* istanbul ignore next */
        cov_13eriynybl().b[36][0]++;
        cov_13eriynybl().s[163]++;
        this.logSecurityIncident({
          type: 'RATE_LIMIT_EXCEEDED',
          timestamp: new Date(),
          request: "Identifier: ".concat(identifier, ", Count: ").concat(rateLimitData.count),
          severity: 'MEDIUM'
        });
        /* istanbul ignore next */
        cov_13eriynybl().s[164]++;
        return {
          isValid: false,
          errorType: 'RESOURCE_ERROR',
          error: 'Rate limit exceeded',
          securityAlert: true,
          threatType: 'RATE_LIMIT_EXCEEDED'
        };
      } else
      /* istanbul ignore next */
      {
        cov_13eriynybl().b[36][1]++;
      }
      // Increment count
      cov_13eriynybl().s[165]++;
      rateLimitData.count++;
    } else {
      /* istanbul ignore next */
      cov_13eriynybl().b[35][1]++;
      cov_13eriynybl().s[166]++;
      // New window
      this.rateLimitMap.set(identifier, {
        count: 1,
        windowStart: now
      });
    }
    /* istanbul ignore next */
    cov_13eriynybl().s[167]++;
    return {
      isValid: true
    };
  };
  /**
   * Log security incident
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[168]++;
  SecurityValidator.prototype.logSecurityIncident = function (incident) {
    /* istanbul ignore next */
    cov_13eriynybl().f[17]++;
    cov_13eriynybl().s[169]++;
    this.securityIncidents.push(incident);
    // Log to console for monitoring
    /* istanbul ignore next */
    cov_13eriynybl().s[170]++;
    console.warn('SECURITY_ALERT', incident);
    // Keep only last 1000 incidents to prevent memory issues
    /* istanbul ignore next */
    cov_13eriynybl().s[171]++;
    if (this.securityIncidents.length > 1000) {
      /* istanbul ignore next */
      cov_13eriynybl().b[37][0]++;
      cov_13eriynybl().s[172]++;
      this.securityIncidents = this.securityIncidents.slice(-1000);
    } else
    /* istanbul ignore next */
    {
      cov_13eriynybl().b[37][1]++;
    }
  };
  /**
   * Get security statistics
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[173]++;
  SecurityValidator.prototype.getSecurityStatistics = function () {
    /* istanbul ignore next */
    cov_13eriynybl().f[18]++;
    var now =
    /* istanbul ignore next */
    (cov_13eriynybl().s[174]++, Date.now());
    var last24Hours =
    /* istanbul ignore next */
    (cov_13eriynybl().s[175]++, this.securityIncidents.filter(function (incident) {
      /* istanbul ignore next */
      cov_13eriynybl().f[19]++;
      cov_13eriynybl().s[176]++;
      return now - incident.timestamp.getTime() < 24 * 60 * 60 * 1000;
    }));
    var incidentsByType =
    /* istanbul ignore next */
    (cov_13eriynybl().s[177]++, last24Hours.reduce(function (acc, incident) {
      /* istanbul ignore next */
      cov_13eriynybl().f[20]++;
      cov_13eriynybl().s[178]++;
      acc[incident.type] = (
      /* istanbul ignore next */
      (cov_13eriynybl().b[38][0]++, acc[incident.type]) ||
      /* istanbul ignore next */
      (cov_13eriynybl().b[38][1]++, 0)) + 1;
      /* istanbul ignore next */
      cov_13eriynybl().s[179]++;
      return acc;
    }, {}));
    var incidentsBySeverity =
    /* istanbul ignore next */
    (cov_13eriynybl().s[180]++, last24Hours.reduce(function (acc, incident) {
      /* istanbul ignore next */
      cov_13eriynybl().f[21]++;
      cov_13eriynybl().s[181]++;
      acc[incident.severity] = (
      /* istanbul ignore next */
      (cov_13eriynybl().b[39][0]++, acc[incident.severity]) ||
      /* istanbul ignore next */
      (cov_13eriynybl().b[39][1]++, 0)) + 1;
      /* istanbul ignore next */
      cov_13eriynybl().s[182]++;
      return acc;
    }, {}));
    /* istanbul ignore next */
    cov_13eriynybl().s[183]++;
    return {
      totalIncidents: this.securityIncidents.length,
      last24Hours: last24Hours.length,
      incidentsByType: incidentsByType,
      incidentsBySeverity: incidentsBySeverity,
      rateLimitStatus: {
        activeIdentifiers: this.rateLimitMap.size,
        config: this.config
      }
    };
  };
  /**
   * Clear rate limit data (for testing)
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[184]++;
  SecurityValidator.prototype.clearRateLimitData = function () {
    /* istanbul ignore next */
    cov_13eriynybl().f[22]++;
    cov_13eriynybl().s[185]++;
    this.rateLimitMap.clear();
  };
  /**
   * Update security configuration
   */
  /* istanbul ignore next */
  cov_13eriynybl().s[186]++;
  SecurityValidator.prototype.updateConfig = function (newConfig) {
    /* istanbul ignore next */
    cov_13eriynybl().f[23]++;
    cov_13eriynybl().s[187]++;
    this.config = __assign(__assign({}, this.config), newConfig);
  };
  /* istanbul ignore next */
  cov_13eriynybl().s[188]++;
  return SecurityValidator;
}());
/* istanbul ignore next */
cov_13eriynybl().s[189]++;
exports.SecurityValidator = SecurityValidator;
// Export singleton instance
/* istanbul ignore next */
cov_13eriynybl().s[190]++;
exports.securityValidator = SecurityValidator.getInstance();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTZWN1cml0eVZhbGlkYXRvciIsImNvdl8xM2VyaXlueWJsIiwicyIsImYiLCJyYXRlTGltaXRNYXAiLCJNYXAiLCJzZWN1cml0eUluY2lkZW50cyIsIlNRTF9JTkpFQ1RJT05fUEFUVEVSTlMiLCJYU1NfUEFUVEVSTlMiLCJOT1NRTF9JTkpFQ1RJT05fUEFUVEVSTlMiLCJQUk9UT1RZUEVfUE9MTFVUSU9OX1BBVFRFUk5TIiwiY29uZmlnIiwibWF4SW5wdXRMZW5ndGgiLCJtYXhBcnJheUxlbmd0aCIsImVuYWJsZVNxbEluamVjdGlvbkRldGVjdGlvbiIsImVuYWJsZVhzc0RldGVjdGlvbiIsImVuYWJsZVJhdGVMaW1pdGluZyIsInJhdGVMaW1pdFdpbmRvdyIsInJhdGVMaW1pdE1heFJlcXVlc3RzIiwiZ2V0SW5zdGFuY2UiLCJpbnN0YW5jZSIsImIiLCJwcm90b3R5cGUiLCJ2YWxpZGF0ZUlucHV0IiwiaW5wdXQiLCJjb250ZXh0IiwidW5kZWZpbmVkIiwiaXNWYWxpZCIsInNhbml0aXplZElucHV0IiwidmFsaWRhdGVTdHJpbmciLCJBcnJheSIsImlzQXJyYXkiLCJ2YWxpZGF0ZUFycmF5IiwidmFsaWRhdGVPYmplY3QiLCJ2YWxpZGF0ZVByaW1pdGl2ZSIsImVycm9yIiwibG9nU2VjdXJpdHlJbmNpZGVudCIsInR5cGUiLCJ0aW1lc3RhbXAiLCJEYXRlIiwicmVxdWVzdCIsIkpTT04iLCJzdHJpbmdpZnkiLCJzZXZlcml0eSIsImVycm9yVHlwZSIsInNlY3VyaXR5QWxlcnQiLCJsZW5ndGgiLCJjb25jYXQiLCJ0aHJlYXRUeXBlIiwiX2kiLCJfYSIsInBhdHRlcm4iLCJ0ZXN0IiwiX2IiLCJfYyIsIl9kIiwiX2UiLCJzYW5pdGl6ZWQiLCJzYW5pdGl6ZVN0cmluZyIsInNhbml0aXplZEFycmF5IiwiaSIsIml0ZW1WYWxpZGF0aW9uIiwicHVzaCIsImlucHV0U3RyaW5nIiwic2FuaXRpemVkT2JqZWN0IiwiT2JqZWN0IiwiZW50cmllcyIsImtleSIsInZhbHVlIiwia2V5VmFsaWRhdGlvbiIsInZhbHVlVmFsaWRhdGlvbiIsInN0cmluZ1ZhbHVlIiwiU3RyaW5nIiwidHJpbSIsInJlcGxhY2UiLCJjb250YWluc1hTUyIsInhzc1BhdHRlcm5zIiwic29tZSIsInZhbGlkYXRlSnNvbkZpZWxkIiwianNvbkRhdGEiLCJmaWVsZE5hbWUiLCJqc29uU3RyaW5nIiwidGVtcGxhdGVJbmplY3Rpb25QYXR0ZXJucyIsInRlbXBsYXRlSW5qZWN0aW9uUGF0dGVybnNfMSIsInNhbml0aXplSnNvbk9iamVjdCIsIm9iaiIsIl90aGlzIiwibWFwIiwiaXRlbSIsInNhbml0aXplZEtleSIsInZhbGlkYXRlUmF0ZUxpbWl0IiwiaWRlbnRpZmllciIsIm5vdyIsInJhdGVMaW1pdERhdGEiLCJnZXQiLCJzZXQiLCJjb3VudCIsIndpbmRvd1N0YXJ0IiwiaW5jaWRlbnQiLCJjb25zb2xlIiwid2FybiIsInNsaWNlIiwiZ2V0U2VjdXJpdHlTdGF0aXN0aWNzIiwibGFzdDI0SG91cnMiLCJmaWx0ZXIiLCJnZXRUaW1lIiwiaW5jaWRlbnRzQnlUeXBlIiwicmVkdWNlIiwiYWNjIiwiaW5jaWRlbnRzQnlTZXZlcml0eSIsInRvdGFsSW5jaWRlbnRzIiwicmF0ZUxpbWl0U3RhdHVzIiwiYWN0aXZlSWRlbnRpZmllcnMiLCJzaXplIiwiY2xlYXJSYXRlTGltaXREYXRhIiwiY2xlYXIiLCJ1cGRhdGVDb25maWciLCJuZXdDb25maWciLCJfX2Fzc2lnbiIsImV4cG9ydHMiLCJzZWN1cml0eVZhbGlkYXRvciJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9kZDYwL2ZhYWZvL2ZhYWZvL2ZhYWZvLWNhcmVlci1wbGF0Zm9ybS9zcmMvbGliL3NlY3VyaXR5L1NlY3VyaXR5VmFsaWRhdG9yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBlcmZvcm1hbmNlIH0gZnJvbSAncGVyZl9ob29rcyc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2VjdXJpdHlWYWxpZGF0aW9uUmVzdWx0IHtcbiAgaXNWYWxpZDogYm9vbGVhbjtcbiAgZXJyb3JUeXBlPzogJ1NFQ1VSSVRZX0VSUk9SJyB8ICdWQUxJREFUSU9OX0VSUk9SJyB8ICdSRVNPVVJDRV9FUlJPUic7XG4gIGVycm9yPzogc3RyaW5nO1xuICBzZWN1cml0eUFsZXJ0PzogYm9vbGVhbjtcbiAgc2FuaXRpemVkSW5wdXQ/OiBhbnk7XG4gIHRocmVhdFR5cGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2VjdXJpdHlDb25maWcge1xuICBtYXhJbnB1dExlbmd0aDogbnVtYmVyO1xuICBtYXhBcnJheUxlbmd0aDogbnVtYmVyO1xuICBlbmFibGVTcWxJbmplY3Rpb25EZXRlY3Rpb246IGJvb2xlYW47XG4gIGVuYWJsZVhzc0RldGVjdGlvbjogYm9vbGVhbjtcbiAgZW5hYmxlUmF0ZUxpbWl0aW5nOiBib29sZWFuO1xuICByYXRlTGltaXRXaW5kb3c6IG51bWJlcjsgLy8gbWlsbGlzZWNvbmRzXG4gIHJhdGVMaW1pdE1heFJlcXVlc3RzOiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2VjdXJpdHlJbmNpZGVudCB7XG4gIHR5cGU6IHN0cmluZztcbiAgdXNlcklkPzogc3RyaW5nO1xuICB0aW1lc3RhbXA6IERhdGU7XG4gIHJlcXVlc3Q6IHN0cmluZztcbiAgaXBBZGRyZXNzPzogc3RyaW5nO1xuICB1c2VyQWdlbnQ/OiBzdHJpbmc7XG4gIHNldmVyaXR5OiAnTE9XJyB8ICdNRURJVU0nIHwgJ0hJR0gnIHwgJ0NSSVRJQ0FMJztcbn1cblxuLyoqXG4gKiBDb21wcmVoZW5zaXZlIHNlY3VyaXR5IHZhbGlkYXRvciBmb3Igc2tpbGwgZ2FwIGFuYWx5emVyXG4gKi9cbmV4cG9ydCBjbGFzcyBTZWN1cml0eVZhbGlkYXRvciB7XG4gIHByaXZhdGUgc3RhdGljIGluc3RhbmNlOiBTZWN1cml0eVZhbGlkYXRvcjtcbiAgcHJpdmF0ZSBjb25maWc6IFNlY3VyaXR5Q29uZmlnO1xuICBwcml2YXRlIHJhdGVMaW1pdE1hcDogTWFwPHN0cmluZywgeyBjb3VudDogbnVtYmVyOyB3aW5kb3dTdGFydDogbnVtYmVyIH0+ID0gbmV3IE1hcCgpO1xuICBwcml2YXRlIHNlY3VyaXR5SW5jaWRlbnRzOiBTZWN1cml0eUluY2lkZW50W10gPSBbXTtcblxuICAvLyBTZWN1cml0eSBwYXR0ZXJucyAtIHVzaW5nIHNpbXBsZSBzdHJpbmcgcGF0dGVybnMgZm9yIGJldHRlciBjb21wYXRpYmlsaXR5XG4gIHByaXZhdGUgcmVhZG9ubHkgU1FMX0lOSkVDVElPTl9QQVRURVJOUyA9IFtcbiAgICAvRFJPUFxccytUQUJMRS9pLFxuICAgIC9ERUxFVEVcXHMrRlJPTS9pLFxuICAgIC9JTlNFUlRcXHMrSU5UTy9pLFxuICAgIC9VUERBVEVcXHMrU0VUL2ksXG4gICAgL1VOSU9OXFxzK1NFTEVDVC9pLFxuICAgIC9PUlxccysxXFxzKj1cXHMqMS9pLFxuICAgIC9BTkRcXHMrMVxccyo9XFxzKjEvaSxcbiAgICAvJ1xccypPUlxccyonL2ksXG4gICAgLy0tLyxcbiAgICAvXFwvXFwqLyxcbiAgICAvXFwqXFwvL1xuICBdO1xuXG4gIHByaXZhdGUgcmVhZG9ubHkgWFNTX1BBVFRFUk5TID0gW1xuICAgIC88c2NyaXB0W14+XSo+Lio/PFxcL3NjcmlwdD4vZ2ksXG4gICAgLzxpZnJhbWVbXj5dKj4uKj88XFwvaWZyYW1lPi9naSxcbiAgICAvPG9iamVjdFtePl0qPi4qPzxcXC9vYmplY3Q+L2dpLFxuICAgIC88ZW1iZWRbXj5dKj4vZ2ksXG4gICAgLzxsaW5rW14+XSo+L2dpLFxuICAgIC88bWV0YVtePl0qPi9naSxcbiAgICAvamF2YXNjcmlwdDovZ2ksXG4gICAgL3Zic2NyaXB0Oi9naSxcbiAgICAvZGF0YTp0ZXh0XFwvaHRtbC9naSxcbiAgICAvb25cXHcrXFxzKj0vZ2ksIC8vIEV2ZW50IGhhbmRsZXJzIGxpa2Ugb25jbGljaywgb25sb2FkLCBldGMuXG4gICAgLzxpbWdbXj5dKnNyY1tePl0qPi9naSxcbiAgICAvPHN2Z1tePl0qPi4qPzxcXC9zdmc+L2dpXG4gIF07XG5cbiAgcHJpdmF0ZSByZWFkb25seSBOT1NRTF9JTkpFQ1RJT05fUEFUVEVSTlMgPSBbXG4gICAgL1xcJG5lXFxzKjovZ2ksXG4gICAgL1xcJGd0XFxzKjovZ2ksXG4gICAgL1xcJGx0XFxzKjovZ2ksXG4gICAgL1xcJHJlZ2V4XFxzKjovZ2ksXG4gICAgL1xcJHdoZXJlXFxzKjovZ2ksXG4gICAgL1xcJG9yXFxzKjovZ2ksXG4gICAgL1xcJGFuZFxccyo6L2dpLFxuICAgIC9cXCRpblxccyo6L2dpLFxuICAgIC9cXCRuaW5cXHMqOi9naVxuICBdO1xuXG4gIHByaXZhdGUgcmVhZG9ubHkgUFJPVE9UWVBFX1BPTExVVElPTl9QQVRURVJOUyA9IFtcbiAgICAvX19wcm90b19fL2dpLFxuICAgIC9jb25zdHJ1Y3Rvci9naSxcbiAgICAvcHJvdG90eXBlL2dpXG4gIF07XG5cbiAgcHJpdmF0ZSBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmNvbmZpZyA9IHtcbiAgICAgIG1heElucHV0TGVuZ3RoOiAxMDAwLFxuICAgICAgbWF4QXJyYXlMZW5ndGg6IDEwMCxcbiAgICAgIGVuYWJsZVNxbEluamVjdGlvbkRldGVjdGlvbjogdHJ1ZSxcbiAgICAgIGVuYWJsZVhzc0RldGVjdGlvbjogdHJ1ZSxcbiAgICAgIGVuYWJsZVJhdGVMaW1pdGluZzogdHJ1ZSxcbiAgICAgIHJhdGVMaW1pdFdpbmRvdzogNjAwMDAsIC8vIDEgbWludXRlXG4gICAgICByYXRlTGltaXRNYXhSZXF1ZXN0czogMTAwXG4gICAgfTtcbiAgfVxuXG4gIHB1YmxpYyBzdGF0aWMgZ2V0SW5zdGFuY2UoKTogU2VjdXJpdHlWYWxpZGF0b3Ige1xuICAgIGlmICghU2VjdXJpdHlWYWxpZGF0b3IuaW5zdGFuY2UpIHtcbiAgICAgIFNlY3VyaXR5VmFsaWRhdG9yLmluc3RhbmNlID0gbmV3IFNlY3VyaXR5VmFsaWRhdG9yKCk7XG4gICAgfVxuICAgIHJldHVybiBTZWN1cml0eVZhbGlkYXRvci5pbnN0YW5jZTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb21wcmVoZW5zaXZlIGlucHV0IHZhbGlkYXRpb25cbiAgICovXG4gIHZhbGlkYXRlSW5wdXQoaW5wdXQ6IGFueSwgY29udGV4dDogc3RyaW5nID0gJ2dlbmVyYWwnKTogU2VjdXJpdHlWYWxpZGF0aW9uUmVzdWx0IHtcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgZm9yIG51bGwvdW5kZWZpbmVkXG4gICAgICBpZiAoaW5wdXQgPT09IG51bGwgfHwgaW5wdXQgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4geyBpc1ZhbGlkOiB0cnVlLCBzYW5pdGl6ZWRJbnB1dDogaW5wdXQgfTtcbiAgICAgIH1cblxuICAgICAgLy8gSGFuZGxlIGRpZmZlcmVudCBpbnB1dCB0eXBlc1xuICAgICAgaWYgKHR5cGVvZiBpbnB1dCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsaWRhdGVTdHJpbmcoaW5wdXQsIGNvbnRleHQpO1xuICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KGlucHV0KSkge1xuICAgICAgICByZXR1cm4gdGhpcy52YWxpZGF0ZUFycmF5KGlucHV0LCBjb250ZXh0KTtcbiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIGlucHV0ID09PSAnb2JqZWN0Jykge1xuICAgICAgICByZXR1cm4gdGhpcy52YWxpZGF0ZU9iamVjdChpbnB1dCwgY29udGV4dCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gdGhpcy52YWxpZGF0ZVByaW1pdGl2ZShpbnB1dCwgY29udGV4dCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRoaXMubG9nU2VjdXJpdHlJbmNpZGVudCh7XG4gICAgICAgIHR5cGU6ICdWQUxJREFUSU9OX0VSUk9SJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgICByZXF1ZXN0OiBKU09OLnN0cmluZ2lmeShpbnB1dCksXG4gICAgICAgIHNldmVyaXR5OiAnTUVESVVNJ1xuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICBlcnJvclR5cGU6ICdTRUNVUklUWV9FUlJPUicsXG4gICAgICAgIGVycm9yOiAnSW5wdXQgdmFsaWRhdGlvbiBmYWlsZWQnLFxuICAgICAgICBzZWN1cml0eUFsZXJ0OiB0cnVlXG4gICAgICB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBWYWxpZGF0ZSBzdHJpbmcgaW5wdXRzXG4gICAqL1xuICBwcml2YXRlIHZhbGlkYXRlU3RyaW5nKGlucHV0OiBzdHJpbmcsIGNvbnRleHQ6IHN0cmluZyk6IFNlY3VyaXR5VmFsaWRhdGlvblJlc3VsdCB7XG4gICAgLy8gTGVuZ3RoIHZhbGlkYXRpb25cbiAgICBpZiAoaW5wdXQubGVuZ3RoID4gdGhpcy5jb25maWcubWF4SW5wdXRMZW5ndGgpIHtcbiAgICAgIHRoaXMubG9nU2VjdXJpdHlJbmNpZGVudCh7XG4gICAgICAgIHR5cGU6ICdET1NfQVRURU1QVCcsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgICAgcmVxdWVzdDogYElucHV0IHRvbyBsb25nOiAke2lucHV0Lmxlbmd0aH0gY2hhcmFjdGVyc2AsXG4gICAgICAgIHNldmVyaXR5OiAnSElHSCdcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgZXJyb3JUeXBlOiAnVkFMSURBVElPTl9FUlJPUicsXG4gICAgICAgIGVycm9yOiAnSW5wdXQgdG9vIGxvbmcnLFxuICAgICAgICBzZWN1cml0eUFsZXJ0OiB0cnVlLFxuICAgICAgICB0aHJlYXRUeXBlOiAnRE9TX0FUVEVNUFQnXG4gICAgICB9O1xuICAgIH1cblxuICAgIC8vIFNRTCBJbmplY3Rpb24gZGV0ZWN0aW9uXG4gICAgaWYgKHRoaXMuY29uZmlnLmVuYWJsZVNxbEluamVjdGlvbkRldGVjdGlvbikge1xuICAgICAgZm9yIChjb25zdCBwYXR0ZXJuIG9mIHRoaXMuU1FMX0lOSkVDVElPTl9QQVRURVJOUykge1xuICAgICAgICBpZiAocGF0dGVybi50ZXN0KGlucHV0KSkge1xuICAgICAgICAgIHRoaXMubG9nU2VjdXJpdHlJbmNpZGVudCh7XG4gICAgICAgICAgICB0eXBlOiAnU1FMX0lOSkVDVElPTl9BVFRFTVBUJyxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgIHJlcXVlc3Q6IGlucHV0LFxuICAgICAgICAgICAgc2V2ZXJpdHk6ICdDUklUSUNBTCdcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgICAgIGVycm9yVHlwZTogJ1NFQ1VSSVRZX0VSUk9SJyxcbiAgICAgICAgICAgIGVycm9yOiAnTWFsaWNpb3VzIGlucHV0IGRldGVjdGVkJyxcbiAgICAgICAgICAgIHNlY3VyaXR5QWxlcnQ6IHRydWUsXG4gICAgICAgICAgICB0aHJlYXRUeXBlOiAnU1FMX0lOSkVDVElPTidcbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gWFNTIGRldGVjdGlvblxuICAgIGlmICh0aGlzLmNvbmZpZy5lbmFibGVYc3NEZXRlY3Rpb24pIHtcbiAgICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiB0aGlzLlhTU19QQVRURVJOUykge1xuICAgICAgICBpZiAocGF0dGVybi50ZXN0KGlucHV0KSkge1xuICAgICAgICAgIHRoaXMubG9nU2VjdXJpdHlJbmNpZGVudCh7XG4gICAgICAgICAgICB0eXBlOiAnWFNTX0FUVEVNUFQnLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgcmVxdWVzdDogaW5wdXQsXG4gICAgICAgICAgICBzZXZlcml0eTogJ0hJR0gnXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgICAgICBlcnJvclR5cGU6ICdTRUNVUklUWV9FUlJPUicsXG4gICAgICAgICAgICBlcnJvcjogJ01hbGljaW91cyBpbnB1dCBkZXRlY3RlZCcsXG4gICAgICAgICAgICBzZWN1cml0eUFsZXJ0OiB0cnVlLFxuICAgICAgICAgICAgdGhyZWF0VHlwZTogJ1hTUydcbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gTm9TUUwgSW5qZWN0aW9uIGRldGVjdGlvblxuICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiB0aGlzLk5PU1FMX0lOSkVDVElPTl9QQVRURVJOUykge1xuICAgICAgaWYgKHBhdHRlcm4udGVzdChpbnB1dCkpIHtcbiAgICAgICAgdGhpcy5sb2dTZWN1cml0eUluY2lkZW50KHtcbiAgICAgICAgICB0eXBlOiAnTk9TUUxfSU5KRUNUSU9OX0FUVEVNUFQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgICAgICByZXF1ZXN0OiBpbnB1dCxcbiAgICAgICAgICBzZXZlcml0eTogJ0hJR0gnXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgICAgZXJyb3JUeXBlOiAnU0VDVVJJVFlfRVJST1InLFxuICAgICAgICAgIGVycm9yOiAnTWFsaWNpb3VzIGlucHV0IGRldGVjdGVkJyxcbiAgICAgICAgICBzZWN1cml0eUFsZXJ0OiB0cnVlLFxuICAgICAgICAgIHRocmVhdFR5cGU6ICdOT1NRTF9JTkpFQ1RJT04nXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gU2FuaXRpemUgdGhlIGlucHV0XG4gICAgY29uc3Qgc2FuaXRpemVkID0gdGhpcy5zYW5pdGl6ZVN0cmluZyhpbnB1dCk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgaXNWYWxpZDogdHJ1ZSxcbiAgICAgIHNhbml0aXplZElucHV0OiBzYW5pdGl6ZWRcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRlIGFycmF5IGlucHV0c1xuICAgKi9cbiAgcHJpdmF0ZSB2YWxpZGF0ZUFycmF5KGlucHV0OiBhbnlbXSwgY29udGV4dDogc3RyaW5nKTogU2VjdXJpdHlWYWxpZGF0aW9uUmVzdWx0IHtcbiAgICAvLyBMZW5ndGggdmFsaWRhdGlvblxuICAgIGlmIChpbnB1dC5sZW5ndGggPiB0aGlzLmNvbmZpZy5tYXhBcnJheUxlbmd0aCkge1xuICAgICAgdGhpcy5sb2dTZWN1cml0eUluY2lkZW50KHtcbiAgICAgICAgdHlwZTogJ0RPU19BVFRFTVBUJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgICByZXF1ZXN0OiBgQXJyYXkgdG9vIGxhcmdlOiAke2lucHV0Lmxlbmd0aH0gaXRlbXNgLFxuICAgICAgICBzZXZlcml0eTogJ0hJR0gnXG4gICAgICB9KTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIGVycm9yVHlwZTogJ1ZBTElEQVRJT05fRVJST1InLFxuICAgICAgICBlcnJvcjogJ1RvbyBtYW55IHNraWxscycsXG4gICAgICAgIHNlY3VyaXR5QWxlcnQ6IHRydWUsXG4gICAgICAgIHRocmVhdFR5cGU6ICdET1NfQVRURU1QVCdcbiAgICAgIH07XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZWFjaCBpdGVtIGluIHRoZSBhcnJheVxuICAgIGNvbnN0IHNhbml0aXplZEFycmF5ID0gW107XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBpbnB1dC5sZW5ndGg7IGkrKykge1xuICAgICAgY29uc3QgaXRlbVZhbGlkYXRpb24gPSB0aGlzLnZhbGlkYXRlSW5wdXQoaW5wdXRbaV0sIGAke2NvbnRleHR9WyR7aX1dYCk7XG4gICAgICBcbiAgICAgIGlmICghaXRlbVZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgICAvLyBJZiBhbnkgaXRlbSBpcyBtYWxpY2lvdXMsIHJlamVjdCB0aGUgZW50aXJlIGFycmF5XG4gICAgICAgIHRoaXMubG9nU2VjdXJpdHlJbmNpZGVudCh7XG4gICAgICAgICAgdHlwZTogJ0FSUkFZX0lOSkVDVElPTl9BVFRFTVBUJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgcmVxdWVzdDogSlNPTi5zdHJpbmdpZnkoaW5wdXQpLFxuICAgICAgICAgIHNldmVyaXR5OiAnSElHSCdcbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgICBlcnJvclR5cGU6ICdTRUNVUklUWV9FUlJPUicsXG4gICAgICAgICAgZXJyb3I6ICdNYWxpY2lvdXMgaW5wdXQgZGV0ZWN0ZWQgaW4gYXJyYXknLFxuICAgICAgICAgIHNlY3VyaXR5QWxlcnQ6IHRydWUsXG4gICAgICAgICAgdGhyZWF0VHlwZTogJ0FSUkFZX0lOSkVDVElPTidcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgc2FuaXRpemVkQXJyYXkucHVzaChpdGVtVmFsaWRhdGlvbi5zYW5pdGl6ZWRJbnB1dCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGlzVmFsaWQ6IHRydWUsXG4gICAgICBzYW5pdGl6ZWRJbnB1dDogc2FuaXRpemVkQXJyYXlcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRlIG9iamVjdCBpbnB1dHNcbiAgICovXG4gIHByaXZhdGUgdmFsaWRhdGVPYmplY3QoaW5wdXQ6IGFueSwgY29udGV4dDogc3RyaW5nKTogU2VjdXJpdHlWYWxpZGF0aW9uUmVzdWx0IHtcbiAgICAvLyBDaGVjayBmb3IgcHJvdG90eXBlIHBvbGx1dGlvblxuICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiB0aGlzLlBST1RPVFlQRV9QT0xMVVRJT05fUEFUVEVSTlMpIHtcbiAgICAgIGNvbnN0IGlucHV0U3RyaW5nID0gSlNPTi5zdHJpbmdpZnkoaW5wdXQpO1xuICAgICAgaWYgKHBhdHRlcm4udGVzdChpbnB1dFN0cmluZykpIHtcbiAgICAgICAgdGhpcy5sb2dTZWN1cml0eUluY2lkZW50KHtcbiAgICAgICAgICB0eXBlOiAnUFJPVE9UWVBFX1BPTExVVElPTl9BVFRFTVBUJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgcmVxdWVzdDogaW5wdXRTdHJpbmcsXG4gICAgICAgICAgc2V2ZXJpdHk6ICdDUklUSUNBTCdcbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgICBlcnJvclR5cGU6ICdTRUNVUklUWV9FUlJPUicsXG4gICAgICAgICAgZXJyb3I6ICdNYWxpY2lvdXMgb2JqZWN0IHN0cnVjdHVyZSBkZXRlY3RlZCcsXG4gICAgICAgICAgc2VjdXJpdHlBbGVydDogdHJ1ZSxcbiAgICAgICAgICB0aHJlYXRUeXBlOiAnUFJPVE9UWVBFX1BPTExVVElPTidcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBlYWNoIHByb3BlcnR5XG4gICAgY29uc3Qgc2FuaXRpemVkT2JqZWN0OiBhbnkgPSB7fTtcbiAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhpbnB1dCkpIHtcbiAgICAgIGNvbnN0IGtleVZhbGlkYXRpb24gPSB0aGlzLnZhbGlkYXRlSW5wdXQoa2V5LCBgJHtjb250ZXh0fS5rZXlgKTtcbiAgICAgIGNvbnN0IHZhbHVlVmFsaWRhdGlvbiA9IHRoaXMudmFsaWRhdGVJbnB1dCh2YWx1ZSwgYCR7Y29udGV4dH0uJHtrZXl9YCk7XG5cbiAgICAgIGlmICgha2V5VmFsaWRhdGlvbi5pc1ZhbGlkIHx8ICF2YWx1ZVZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICAgIGVycm9yVHlwZTogJ1NFQ1VSSVRZX0VSUk9SJyxcbiAgICAgICAgICBlcnJvcjogJ01hbGljaW91cyBpbnB1dCBkZXRlY3RlZCBpbiBvYmplY3QnLFxuICAgICAgICAgIHNlY3VyaXR5QWxlcnQ6IHRydWVcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgc2FuaXRpemVkT2JqZWN0W2tleVZhbGlkYXRpb24uc2FuaXRpemVkSW5wdXQgfHwga2V5XSA9IHZhbHVlVmFsaWRhdGlvbi5zYW5pdGl6ZWRJbnB1dDtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgaXNWYWxpZDogdHJ1ZSxcbiAgICAgIHNhbml0aXplZElucHV0OiBzYW5pdGl6ZWRPYmplY3RcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRlIHByaW1pdGl2ZSBpbnB1dHNcbiAgICovXG4gIHByaXZhdGUgdmFsaWRhdGVQcmltaXRpdmUoaW5wdXQ6IGFueSwgY29udGV4dDogc3RyaW5nKTogU2VjdXJpdHlWYWxpZGF0aW9uUmVzdWx0IHtcbiAgICAvLyBDb252ZXJ0IHRvIHN0cmluZyBhbmQgdmFsaWRhdGVcbiAgICBjb25zdCBzdHJpbmdWYWx1ZSA9IFN0cmluZyhpbnB1dCk7XG4gICAgcmV0dXJuIHRoaXMudmFsaWRhdGVTdHJpbmcoc3RyaW5nVmFsdWUsIGNvbnRleHQpO1xuICB9XG5cbiAgLyoqXG4gICAqIFNhbml0aXplIHN0cmluZyBpbnB1dFxuICAgKi9cbiAgcHJpdmF0ZSBzYW5pdGl6ZVN0cmluZyhpbnB1dDogc3RyaW5nKTogc3RyaW5nIHtcbiAgICByZXR1cm4gaW5wdXRcbiAgICAgIC50cmltKClcbiAgICAgIC5yZXBsYWNlKC9bPD5dL2csICcnKSAvLyBSZW1vdmUgYW5nbGUgYnJhY2tldHNcbiAgICAgIC5yZXBsYWNlKC9qYXZhc2NyaXB0Oi9naSwgJycpIC8vIFJlbW92ZSBqYXZhc2NyaXB0OiBwcm90b2NvbFxuICAgICAgLnJlcGxhY2UoL3Zic2NyaXB0Oi9naSwgJycpIC8vIFJlbW92ZSB2YnNjcmlwdDogcHJvdG9jb2xcbiAgICAgIC5yZXBsYWNlKC9kYXRhOnRleHRcXC9odG1sL2dpLCAnJykgLy8gUmVtb3ZlIGRhdGEgVVJMc1xuICAgICAgLnJlcGxhY2UoL29uXFx3K1xccyo9L2dpLCAnJyk7IC8vIFJlbW92ZSBldmVudCBoYW5kbGVyc1xuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIGlucHV0IGNvbnRhaW5zIFhTUyBwYXR0ZXJuc1xuICAgKi9cbiAgc3RhdGljIGNvbnRhaW5zWFNTKGlucHV0OiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBpZiAoIWlucHV0IHx8IHR5cGVvZiBpbnB1dCAhPT0gJ3N0cmluZycpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBjb25zdCB4c3NQYXR0ZXJucyA9IFtcbiAgICAgIC88c2NyaXB0W14+XSo+Lio/PFxcL3NjcmlwdD4vZ2ksXG4gICAgICAvPGlmcmFtZVtePl0qPi4qPzxcXC9pZnJhbWU+L2dpLFxuICAgICAgLzxvYmplY3RbXj5dKj4uKj88XFwvb2JqZWN0Pi9naSxcbiAgICAgIC88ZW1iZWRbXj5dKj4vZ2ksXG4gICAgICAvPGxpbmtbXj5dKj4vZ2ksXG4gICAgICAvPG1ldGFbXj5dKj4vZ2ksXG4gICAgICAvamF2YXNjcmlwdDovZ2ksXG4gICAgICAvdmJzY3JpcHQ6L2dpLFxuICAgICAgL2RhdGE6dGV4dFxcL2h0bWwvZ2ksXG4gICAgICAvb25cXHcrXFxzKj0vZ2ksIC8vIEV2ZW50IGhhbmRsZXJzIGxpa2Ugb25jbGljaywgb25sb2FkLCBldGMuXG4gICAgICAvPGltZ1tePl0qc3JjW14+XSo+L2dpLFxuICAgICAgLzxzdmdbXj5dKj4uKj88XFwvc3ZnPi9naVxuICAgIF07XG5cbiAgICByZXR1cm4geHNzUGF0dGVybnMuc29tZShwYXR0ZXJuID0+IHBhdHRlcm4udGVzdChpbnB1dCkpO1xuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRlIGFuZCBzYW5pdGl6ZSBKU09OIGZpZWxkc1xuICAgKi9cbiAgc3RhdGljIHZhbGlkYXRlSnNvbkZpZWxkKGpzb25EYXRhOiBhbnksIGZpZWxkTmFtZTogc3RyaW5nKTogU2VjdXJpdHlWYWxpZGF0aW9uUmVzdWx0IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCFqc29uRGF0YSkge1xuICAgICAgICByZXR1cm4geyBpc1ZhbGlkOiB0cnVlLCBzYW5pdGl6ZWRJbnB1dDoganNvbkRhdGEgfTtcbiAgICAgIH1cblxuICAgICAgY29uc3QganNvblN0cmluZyA9IEpTT04uc3RyaW5naWZ5KGpzb25EYXRhKTtcblxuICAgICAgLy8gQ2hlY2sgZm9yIFhTUyBpbiBKU09OXG4gICAgICBpZiAodGhpcy5jb250YWluc1hTUyhqc29uU3RyaW5nKSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICAgIGVycm9yVHlwZTogJ1NFQ1VSSVRZX0VSUk9SJyxcbiAgICAgICAgICBlcnJvcjogYFhTUyBkZXRlY3RlZCBpbiAke2ZpZWxkTmFtZX1gLFxuICAgICAgICAgIHNlY3VyaXR5QWxlcnQ6IHRydWUsXG4gICAgICAgICAgdGhyZWF0VHlwZTogJ1hTUydcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgZm9yIHRlbXBsYXRlIGluamVjdGlvblxuICAgICAgY29uc3QgdGVtcGxhdGVJbmplY3Rpb25QYXR0ZXJucyA9IFtcbiAgICAgICAgL1xcJFxce1tefV0qXFx9L2csIC8vICR7Li4ufSB0ZW1wbGF0ZSBsaXRlcmFsc1xuICAgICAgICAvXFx7XFx7W159XSpcXH1cXH0vZywgLy8ge3suLi59fSBoYW5kbGViYXJzL211c3RhY2hlXG4gICAgICAgIC8lXFx7W159XSpcXH0vZywgLy8gJXsuLi59IHJ1Ynktc3R5bGVcbiAgICAgIF07XG5cbiAgICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiB0ZW1wbGF0ZUluamVjdGlvblBhdHRlcm5zKSB7XG4gICAgICAgIGlmIChwYXR0ZXJuLnRlc3QoanNvblN0cmluZykpIHtcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgICAgICBlcnJvclR5cGU6ICdTRUNVUklUWV9FUlJPUicsXG4gICAgICAgICAgICBlcnJvcjogYFRlbXBsYXRlIGluamVjdGlvbiBkZXRlY3RlZCBpbiAke2ZpZWxkTmFtZX1gLFxuICAgICAgICAgICAgc2VjdXJpdHlBbGVydDogdHJ1ZSxcbiAgICAgICAgICAgIHRocmVhdFR5cGU6ICdURU1QTEFURV9JTkpFQ1RJT04nXG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBSZWN1cnNpdmVseSBzYW5pdGl6ZSBvYmplY3QgcHJvcGVydGllc1xuICAgICAgY29uc3Qgc2FuaXRpemVkID0gdGhpcy5zYW5pdGl6ZUpzb25PYmplY3QoanNvbkRhdGEpO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBpc1ZhbGlkOiB0cnVlLFxuICAgICAgICBzYW5pdGl6ZWRJbnB1dDogc2FuaXRpemVkXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgZXJyb3JUeXBlOiAnVkFMSURBVElPTl9FUlJPUicsXG4gICAgICAgIGVycm9yOiBgSW52YWxpZCBKU09OIGluICR7ZmllbGROYW1lfWAsXG4gICAgICAgIHNlY3VyaXR5QWxlcnQ6IGZhbHNlXG4gICAgICB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBSZWN1cnNpdmVseSBzYW5pdGl6ZSBKU09OIG9iamVjdFxuICAgKi9cbiAgcHJpdmF0ZSBzdGF0aWMgc2FuaXRpemVKc29uT2JqZWN0KG9iajogYW55KTogYW55IHtcbiAgICBpZiAob2JqID09PSBudWxsIHx8IG9iaiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXR1cm4gb2JqO1xuICAgIH1cblxuICAgIGlmICh0eXBlb2Ygb2JqID09PSAnc3RyaW5nJykge1xuICAgICAgLy8gU2FuaXRpemUgc3RyaW5nIHZhbHVlc1xuICAgICAgcmV0dXJuIG9ialxuICAgICAgICAucmVwbGFjZSgvPHNjcmlwdFtePl0qPi4qPzxcXC9zY3JpcHQ+L2dpLCAnJylcbiAgICAgICAgLnJlcGxhY2UoL2phdmFzY3JpcHQ6L2dpLCAnJylcbiAgICAgICAgLnJlcGxhY2UoL3Zic2NyaXB0Oi9naSwgJycpXG4gICAgICAgIC5yZXBsYWNlKC9vblxcdytcXHMqPS9naSwgJycpXG4gICAgICAgIC5yZXBsYWNlKC9cXCRcXHtbXn1dKlxcfS9nLCAnJykgLy8gUmVtb3ZlIHRlbXBsYXRlIGxpdGVyYWxzXG4gICAgICAgIC50cmltKCk7XG4gICAgfVxuXG4gICAgaWYgKEFycmF5LmlzQXJyYXkob2JqKSkge1xuICAgICAgcmV0dXJuIG9iai5tYXAoaXRlbSA9PiB0aGlzLnNhbml0aXplSnNvbk9iamVjdChpdGVtKSk7XG4gICAgfVxuXG4gICAgaWYgKHR5cGVvZiBvYmogPT09ICdvYmplY3QnKSB7XG4gICAgICBjb25zdCBzYW5pdGl6ZWQ6IGFueSA9IHt9O1xuICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMob2JqKSkge1xuICAgICAgICAvLyBTYW5pdGl6ZSBib3RoIGtleSBhbmQgdmFsdWVcbiAgICAgICAgY29uc3Qgc2FuaXRpemVkS2V5ID0gdHlwZW9mIGtleSA9PT0gJ3N0cmluZycgP1xuICAgICAgICAgIGtleS5yZXBsYWNlKC9bPD5dL2csICcnKS5yZXBsYWNlKC9qYXZhc2NyaXB0Oi9naSwgJycpIDoga2V5O1xuICAgICAgICBzYW5pdGl6ZWRbc2FuaXRpemVkS2V5XSA9IHRoaXMuc2FuaXRpemVKc29uT2JqZWN0KHZhbHVlKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBzYW5pdGl6ZWQ7XG4gICAgfVxuXG4gICAgcmV0dXJuIG9iajtcbiAgfVxuXG4gIC8qKlxuICAgKiBSYXRlIGxpbWl0aW5nIHZhbGlkYXRpb25cbiAgICovXG4gIHZhbGlkYXRlUmF0ZUxpbWl0KGlkZW50aWZpZXI6IHN0cmluZyk6IFNlY3VyaXR5VmFsaWRhdGlvblJlc3VsdCB7XG4gICAgaWYgKCF0aGlzLmNvbmZpZy5lbmFibGVSYXRlTGltaXRpbmcpIHtcbiAgICAgIHJldHVybiB7IGlzVmFsaWQ6IHRydWUgfTtcbiAgICB9XG5cbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuICAgIGNvbnN0IHJhdGVMaW1pdERhdGEgPSB0aGlzLnJhdGVMaW1pdE1hcC5nZXQoaWRlbnRpZmllcik7XG5cbiAgICBpZiAoIXJhdGVMaW1pdERhdGEpIHtcbiAgICAgIC8vIEZpcnN0IHJlcXVlc3QgZnJvbSB0aGlzIGlkZW50aWZpZXJcbiAgICAgIHRoaXMucmF0ZUxpbWl0TWFwLnNldChpZGVudGlmaWVyLCB7IGNvdW50OiAxLCB3aW5kb3dTdGFydDogbm93IH0pO1xuICAgICAgcmV0dXJuIHsgaXNWYWxpZDogdHJ1ZSB9O1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHdlJ3JlIHN0aWxsIGluIHRoZSBzYW1lIHdpbmRvd1xuICAgIGlmIChub3cgLSByYXRlTGltaXREYXRhLndpbmRvd1N0YXJ0IDwgdGhpcy5jb25maWcucmF0ZUxpbWl0V2luZG93KSB7XG4gICAgICBpZiAocmF0ZUxpbWl0RGF0YS5jb3VudCA+PSB0aGlzLmNvbmZpZy5yYXRlTGltaXRNYXhSZXF1ZXN0cykge1xuICAgICAgICB0aGlzLmxvZ1NlY3VyaXR5SW5jaWRlbnQoe1xuICAgICAgICAgIHR5cGU6ICdSQVRFX0xJTUlUX0VYQ0VFREVEJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgcmVxdWVzdDogYElkZW50aWZpZXI6ICR7aWRlbnRpZmllcn0sIENvdW50OiAke3JhdGVMaW1pdERhdGEuY291bnR9YCxcbiAgICAgICAgICBzZXZlcml0eTogJ01FRElVTSdcbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgICBlcnJvclR5cGU6ICdSRVNPVVJDRV9FUlJPUicsXG4gICAgICAgICAgZXJyb3I6ICdSYXRlIGxpbWl0IGV4Y2VlZGVkJyxcbiAgICAgICAgICBzZWN1cml0eUFsZXJ0OiB0cnVlLFxuICAgICAgICAgIHRocmVhdFR5cGU6ICdSQVRFX0xJTUlUX0VYQ0VFREVEJ1xuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgICAvLyBJbmNyZW1lbnQgY291bnRcbiAgICAgIHJhdGVMaW1pdERhdGEuY291bnQrKztcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gTmV3IHdpbmRvd1xuICAgICAgdGhpcy5yYXRlTGltaXRNYXAuc2V0KGlkZW50aWZpZXIsIHsgY291bnQ6IDEsIHdpbmRvd1N0YXJ0OiBub3cgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgaXNWYWxpZDogdHJ1ZSB9O1xuICB9XG5cbiAgLyoqXG4gICAqIExvZyBzZWN1cml0eSBpbmNpZGVudFxuICAgKi9cbiAgcHJpdmF0ZSBsb2dTZWN1cml0eUluY2lkZW50KGluY2lkZW50OiBPbWl0PFNlY3VyaXR5SW5jaWRlbnQsICd0aW1lc3RhbXAnPiAmIHsgdGltZXN0YW1wOiBEYXRlIH0pOiB2b2lkIHtcbiAgICB0aGlzLnNlY3VyaXR5SW5jaWRlbnRzLnB1c2goaW5jaWRlbnQpO1xuXG4gICAgLy8gTG9nIHRvIGNvbnNvbGUgZm9yIG1vbml0b3JpbmdcbiAgICBjb25zb2xlLndhcm4oJ1NFQ1VSSVRZX0FMRVJUJywgaW5jaWRlbnQpO1xuXG4gICAgLy8gS2VlcCBvbmx5IGxhc3QgMTAwMCBpbmNpZGVudHMgdG8gcHJldmVudCBtZW1vcnkgaXNzdWVzXG4gICAgaWYgKHRoaXMuc2VjdXJpdHlJbmNpZGVudHMubGVuZ3RoID4gMTAwMCkge1xuICAgICAgdGhpcy5zZWN1cml0eUluY2lkZW50cyA9IHRoaXMuc2VjdXJpdHlJbmNpZGVudHMuc2xpY2UoLTEwMDApO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgc2VjdXJpdHkgc3RhdGlzdGljc1xuICAgKi9cbiAgZ2V0U2VjdXJpdHlTdGF0aXN0aWNzKCk6IGFueSB7XG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICBjb25zdCBsYXN0MjRIb3VycyA9IHRoaXMuc2VjdXJpdHlJbmNpZGVudHMuZmlsdGVyKFxuICAgICAgaW5jaWRlbnQgPT4gbm93IC0gaW5jaWRlbnQudGltZXN0YW1wLmdldFRpbWUoKSA8IDI0ICogNjAgKiA2MCAqIDEwMDBcbiAgICApO1xuXG4gICAgY29uc3QgaW5jaWRlbnRzQnlUeXBlID0gbGFzdDI0SG91cnMucmVkdWNlKChhY2MsIGluY2lkZW50KSA9PiB7XG4gICAgICBhY2NbaW5jaWRlbnQudHlwZV0gPSAoYWNjW2luY2lkZW50LnR5cGVdIHx8IDApICsgMTtcbiAgICAgIHJldHVybiBhY2M7XG4gICAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgbnVtYmVyPik7XG5cbiAgICBjb25zdCBpbmNpZGVudHNCeVNldmVyaXR5ID0gbGFzdDI0SG91cnMucmVkdWNlKChhY2MsIGluY2lkZW50KSA9PiB7XG4gICAgICBhY2NbaW5jaWRlbnQuc2V2ZXJpdHldID0gKGFjY1tpbmNpZGVudC5zZXZlcml0eV0gfHwgMCkgKyAxO1xuICAgICAgcmV0dXJuIGFjYztcbiAgICB9LCB7fSBhcyBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+KTtcblxuICAgIHJldHVybiB7XG4gICAgICB0b3RhbEluY2lkZW50czogdGhpcy5zZWN1cml0eUluY2lkZW50cy5sZW5ndGgsXG4gICAgICBsYXN0MjRIb3VyczogbGFzdDI0SG91cnMubGVuZ3RoLFxuICAgICAgaW5jaWRlbnRzQnlUeXBlLFxuICAgICAgaW5jaWRlbnRzQnlTZXZlcml0eSxcbiAgICAgIHJhdGVMaW1pdFN0YXR1czoge1xuICAgICAgICBhY3RpdmVJZGVudGlmaWVyczogdGhpcy5yYXRlTGltaXRNYXAuc2l6ZSxcbiAgICAgICAgY29uZmlnOiB0aGlzLmNvbmZpZ1xuICAgICAgfVxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogQ2xlYXIgcmF0ZSBsaW1pdCBkYXRhIChmb3IgdGVzdGluZylcbiAgICovXG4gIGNsZWFyUmF0ZUxpbWl0RGF0YSgpOiB2b2lkIHtcbiAgICB0aGlzLnJhdGVMaW1pdE1hcC5jbGVhcigpO1xuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBzZWN1cml0eSBjb25maWd1cmF0aW9uXG4gICAqL1xuICB1cGRhdGVDb25maWcobmV3Q29uZmlnOiBQYXJ0aWFsPFNlY3VyaXR5Q29uZmlnPik6IHZvaWQge1xuICAgIHRoaXMuY29uZmlnID0geyAuLi50aGlzLmNvbmZpZywgLi4ubmV3Q29uZmlnIH07XG4gIH1cbn1cblxuLy8gRXhwb3J0IHNpbmdsZXRvbiBpbnN0YW5jZVxuZXhwb3J0IGNvbnN0IHNlY3VyaXR5VmFsaWRhdG9yID0gU2VjdXJpdHlWYWxpZGF0b3IuZ2V0SW5zdGFuY2UoKTtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUErQkE7OztBQUdBLElBQUFBLGlCQUFBO0FBQUE7QUFBQSxjQUFBQyxjQUFBLEdBQUFDLENBQUE7RUFBQTtFQUFBRCxjQUFBLEdBQUFFLENBQUE7RUFzREUsU0FBQUgsa0JBQUE7SUFBQTtJQUFBQyxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBQyxDQUFBO0lBbkRRLEtBQUFFLFlBQVksR0FBd0QsSUFBSUMsR0FBRyxFQUFFO0lBQUM7SUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQzlFLEtBQUFJLGlCQUFpQixHQUF1QixFQUFFO0lBRWxEO0lBQUE7SUFBQUwsY0FBQSxHQUFBQyxDQUFBO0lBQ2lCLEtBQUFLLHNCQUFzQixHQUFHLENBQ3hDLGVBQWUsRUFDZixnQkFBZ0IsRUFDaEIsZ0JBQWdCLEVBQ2hCLGVBQWUsRUFDZixpQkFBaUIsRUFDakIsaUJBQWlCLEVBQ2pCLGtCQUFrQixFQUNsQixhQUFhLEVBQ2IsSUFBSSxFQUNKLE1BQU0sRUFDTixNQUFNLENBQ1A7SUFBQztJQUFBTixjQUFBLEdBQUFDLENBQUE7SUFFZSxLQUFBTSxZQUFZLEdBQUcsQ0FDOUIsOEJBQThCLEVBQzlCLDhCQUE4QixFQUM5Qiw4QkFBOEIsRUFDOUIsZ0JBQWdCLEVBQ2hCLGVBQWUsRUFDZixlQUFlLEVBQ2YsZUFBZSxFQUNmLGFBQWEsRUFDYixtQkFBbUIsRUFDbkIsYUFBYTtJQUFFO0lBQ2Ysc0JBQXNCLEVBQ3RCLHdCQUF3QixDQUN6QjtJQUFDO0lBQUFQLGNBQUEsR0FBQUMsQ0FBQTtJQUVlLEtBQUFPLHdCQUF3QixHQUFHLENBQzFDLFlBQVksRUFDWixZQUFZLEVBQ1osWUFBWSxFQUNaLGVBQWUsRUFDZixlQUFlLEVBQ2YsWUFBWSxFQUNaLGFBQWEsRUFDYixZQUFZLEVBQ1osYUFBYSxDQUNkO0lBQUM7SUFBQVIsY0FBQSxHQUFBQyxDQUFBO0lBRWUsS0FBQVEsNEJBQTRCLEdBQUcsQ0FDOUMsYUFBYSxFQUNiLGVBQWUsRUFDZixhQUFhLENBQ2Q7SUFBQztJQUFBVCxjQUFBLEdBQUFDLENBQUE7SUFHQSxJQUFJLENBQUNTLE1BQU0sR0FBRztNQUNaQyxjQUFjLEVBQUUsSUFBSTtNQUNwQkMsY0FBYyxFQUFFLEdBQUc7TUFDbkJDLDJCQUEyQixFQUFFLElBQUk7TUFDakNDLGtCQUFrQixFQUFFLElBQUk7TUFDeEJDLGtCQUFrQixFQUFFLElBQUk7TUFDeEJDLGVBQWUsRUFBRSxLQUFLO01BQUU7TUFDeEJDLG9CQUFvQixFQUFFO0tBQ3ZCO0VBQ0g7RUFBQztFQUFBakIsY0FBQSxHQUFBQyxDQUFBO0VBRWFGLGlCQUFBLENBQUFtQixXQUFXLEdBQXpCO0lBQUE7SUFBQWxCLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFDLENBQUE7SUFDRSxJQUFJLENBQUNGLGlCQUFpQixDQUFDb0IsUUFBUSxFQUFFO01BQUE7TUFBQW5CLGNBQUEsR0FBQW9CLENBQUE7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUMvQkYsaUJBQWlCLENBQUNvQixRQUFRLEdBQUcsSUFBSXBCLGlCQUFpQixFQUFFO0lBQ3RELENBQUM7SUFBQTtJQUFBO01BQUFDLGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUFBcEIsY0FBQSxHQUFBQyxDQUFBO0lBQ0QsT0FBT0YsaUJBQWlCLENBQUNvQixRQUFRO0VBQ25DLENBQUM7RUFFRDs7O0VBQUE7RUFBQW5CLGNBQUEsR0FBQUMsQ0FBQTtFQUdBRixpQkFBQSxDQUFBc0IsU0FBQSxDQUFBQyxhQUFhLEdBQWIsVUFBY0MsS0FBVSxFQUFFQyxPQUEyQjtJQUFBO0lBQUF4QixjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBQyxDQUFBO0lBQTNCLElBQUF1QixPQUFBO01BQUE7TUFBQXhCLGNBQUEsR0FBQW9CLENBQUE7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUFBdUIsT0FBQSxZQUEyQjtJQUFBO0lBQUE7SUFBQTtNQUFBeEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUFBO0lBQUFwQixjQUFBLEdBQUFDLENBQUE7SUFDbkQsSUFBSTtNQUFBO01BQUFELGNBQUEsR0FBQUMsQ0FBQTtNQUNGO01BQ0E7TUFBSTtNQUFBLENBQUFELGNBQUEsR0FBQW9CLENBQUEsVUFBQUcsS0FBSyxLQUFLLElBQUk7TUFBQTtNQUFBLENBQUF2QixjQUFBLEdBQUFvQixDQUFBLFVBQUlHLEtBQUssS0FBS0UsU0FBUyxHQUFFO1FBQUE7UUFBQXpCLGNBQUEsR0FBQW9CLENBQUE7UUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtRQUN6QyxPQUFPO1VBQUV5QixPQUFPLEVBQUUsSUFBSTtVQUFFQyxjQUFjLEVBQUVKO1FBQUssQ0FBRTtNQUNqRCxDQUFDO01BQUE7TUFBQTtRQUFBdkIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO01BRUQ7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUNBLElBQUksT0FBT3NCLEtBQUssS0FBSyxRQUFRLEVBQUU7UUFBQTtRQUFBdkIsY0FBQSxHQUFBb0IsQ0FBQTtRQUFBcEIsY0FBQSxHQUFBQyxDQUFBO1FBQzdCLE9BQU8sSUFBSSxDQUFDMkIsY0FBYyxDQUFDTCxLQUFLLEVBQUVDLE9BQU8sQ0FBQztNQUM1QyxDQUFDLE1BQU07UUFBQTtRQUFBeEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUFBcEIsY0FBQSxHQUFBQyxDQUFBO1FBQUEsSUFBSTRCLEtBQUssQ0FBQ0MsT0FBTyxDQUFDUCxLQUFLLENBQUMsRUFBRTtVQUFBO1VBQUF2QixjQUFBLEdBQUFvQixDQUFBO1VBQUFwQixjQUFBLEdBQUFDLENBQUE7VUFDL0IsT0FBTyxJQUFJLENBQUM4QixhQUFhLENBQUNSLEtBQUssRUFBRUMsT0FBTyxDQUFDO1FBQzNDLENBQUMsTUFBTTtVQUFBO1VBQUF4QixjQUFBLEdBQUFvQixDQUFBO1VBQUFwQixjQUFBLEdBQUFDLENBQUE7VUFBQSxJQUFJLE9BQU9zQixLQUFLLEtBQUssUUFBUSxFQUFFO1lBQUE7WUFBQXZCLGNBQUEsR0FBQW9CLENBQUE7WUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtZQUNwQyxPQUFPLElBQUksQ0FBQytCLGNBQWMsQ0FBQ1QsS0FBSyxFQUFFQyxPQUFPLENBQUM7VUFDNUMsQ0FBQyxNQUFNO1lBQUE7WUFBQXhCLGNBQUEsR0FBQW9CLENBQUE7WUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtZQUNMLE9BQU8sSUFBSSxDQUFDZ0MsaUJBQWlCLENBQUNWLEtBQUssRUFBRUMsT0FBTyxDQUFDO1VBQy9DO1FBQUE7TUFBQTtJQUNGLENBQUMsQ0FBQyxPQUFPVSxLQUFLLEVBQUU7TUFBQTtNQUFBbEMsY0FBQSxHQUFBQyxDQUFBO01BQ2QsSUFBSSxDQUFDa0MsbUJBQW1CLENBQUM7UUFDdkJDLElBQUksRUFBRSxrQkFBa0I7UUFDeEJDLFNBQVMsRUFBRSxJQUFJQyxJQUFJLEVBQUU7UUFDckJDLE9BQU8sRUFBRUMsSUFBSSxDQUFDQyxTQUFTLENBQUNsQixLQUFLLENBQUM7UUFDOUJtQixRQUFRLEVBQUU7T0FDWCxDQUFDO01BQUM7TUFBQTFDLGNBQUEsR0FBQUMsQ0FBQTtNQUVILE9BQU87UUFDTHlCLE9BQU8sRUFBRSxLQUFLO1FBQ2RpQixTQUFTLEVBQUUsZ0JBQWdCO1FBQzNCVCxLQUFLLEVBQUUseUJBQXlCO1FBQ2hDVSxhQUFhLEVBQUU7T0FDaEI7SUFDSDtFQUNGLENBQUM7RUFFRDs7O0VBQUE7RUFBQTVDLGNBQUEsR0FBQUMsQ0FBQTtFQUdRRixpQkFBQSxDQUFBc0IsU0FBQSxDQUFBTyxjQUFjLEdBQXRCLFVBQXVCTCxLQUFhLEVBQUVDLE9BQWU7SUFBQTtJQUFBeEIsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUMsQ0FBQTtJQUNuRDtJQUNBLElBQUlzQixLQUFLLENBQUNzQixNQUFNLEdBQUcsSUFBSSxDQUFDbkMsTUFBTSxDQUFDQyxjQUFjLEVBQUU7TUFBQTtNQUFBWCxjQUFBLEdBQUFvQixDQUFBO01BQUFwQixjQUFBLEdBQUFDLENBQUE7TUFDN0MsSUFBSSxDQUFDa0MsbUJBQW1CLENBQUM7UUFDdkJDLElBQUksRUFBRSxhQUFhO1FBQ25CQyxTQUFTLEVBQUUsSUFBSUMsSUFBSSxFQUFFO1FBQ3JCQyxPQUFPLEVBQUUsbUJBQUFPLE1BQUEsQ0FBbUJ2QixLQUFLLENBQUNzQixNQUFNLGdCQUFhO1FBQ3JESCxRQUFRLEVBQUU7T0FDWCxDQUFDO01BQUM7TUFBQTFDLGNBQUEsR0FBQUMsQ0FBQTtNQUVILE9BQU87UUFDTHlCLE9BQU8sRUFBRSxLQUFLO1FBQ2RpQixTQUFTLEVBQUUsa0JBQWtCO1FBQzdCVCxLQUFLLEVBQUUsZ0JBQWdCO1FBQ3ZCVSxhQUFhLEVBQUUsSUFBSTtRQUNuQkcsVUFBVSxFQUFFO09BQ2I7SUFDSCxDQUFDO0lBQUE7SUFBQTtNQUFBL0MsY0FBQSxHQUFBb0IsQ0FBQTtJQUFBO0lBRUQ7SUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtJQUNBLElBQUksSUFBSSxDQUFDUyxNQUFNLENBQUNHLDJCQUEyQixFQUFFO01BQUE7TUFBQWIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBcEIsY0FBQSxHQUFBQyxDQUFBO01BQzNDLEtBQXNCLElBQUErQyxFQUFBO1FBQUE7UUFBQSxDQUFBaEQsY0FBQSxHQUFBQyxDQUFBLFNBQTJCLEdBQTNCZ0QsRUFBQTtRQUFBO1FBQUEsQ0FBQWpELGNBQUEsR0FBQUMsQ0FBQSxZQUFJLENBQUNLLHNCQUFzQixHQUEzQjBDLEVBQUEsR0FBQUMsRUFBQSxDQUFBSixNQUEyQixFQUEzQkcsRUFBQSxFQUEyQixFQUFFO1FBQTlDLElBQU1FLE9BQU87UUFBQTtRQUFBLENBQUFsRCxjQUFBLEdBQUFDLENBQUEsUUFBQWdELEVBQUEsQ0FBQUQsRUFBQTtRQUFBO1FBQUFoRCxjQUFBLEdBQUFDLENBQUE7UUFDaEIsSUFBSWlELE9BQU8sQ0FBQ0MsSUFBSSxDQUFDNUIsS0FBSyxDQUFDLEVBQUU7VUFBQTtVQUFBdkIsY0FBQSxHQUFBb0IsQ0FBQTtVQUFBcEIsY0FBQSxHQUFBQyxDQUFBO1VBQ3ZCLElBQUksQ0FBQ2tDLG1CQUFtQixDQUFDO1lBQ3ZCQyxJQUFJLEVBQUUsdUJBQXVCO1lBQzdCQyxTQUFTLEVBQUUsSUFBSUMsSUFBSSxFQUFFO1lBQ3JCQyxPQUFPLEVBQUVoQixLQUFLO1lBQ2RtQixRQUFRLEVBQUU7V0FDWCxDQUFDO1VBQUM7VUFBQTFDLGNBQUEsR0FBQUMsQ0FBQTtVQUVILE9BQU87WUFDTHlCLE9BQU8sRUFBRSxLQUFLO1lBQ2RpQixTQUFTLEVBQUUsZ0JBQWdCO1lBQzNCVCxLQUFLLEVBQUUsMEJBQTBCO1lBQ2pDVSxhQUFhLEVBQUUsSUFBSTtZQUNuQkcsVUFBVSxFQUFFO1dBQ2I7UUFDSCxDQUFDO1FBQUE7UUFBQTtVQUFBL0MsY0FBQSxHQUFBb0IsQ0FBQTtRQUFBO01BQ0g7SUFDRixDQUFDO0lBQUE7SUFBQTtNQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUFBO0lBRUQ7SUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtJQUNBLElBQUksSUFBSSxDQUFDUyxNQUFNLENBQUNJLGtCQUFrQixFQUFFO01BQUE7TUFBQWQsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBcEIsY0FBQSxHQUFBQyxDQUFBO01BQ2xDLEtBQXNCLElBQUFtRCxFQUFBO1FBQUE7UUFBQSxDQUFBcEQsY0FBQSxHQUFBQyxDQUFBLFNBQWlCLEdBQWpCb0QsRUFBQTtRQUFBO1FBQUEsQ0FBQXJELGNBQUEsR0FBQUMsQ0FBQSxZQUFJLENBQUNNLFlBQVksR0FBakI2QyxFQUFBLEdBQUFDLEVBQUEsQ0FBQVIsTUFBaUIsRUFBakJPLEVBQUEsRUFBaUIsRUFBRTtRQUFwQyxJQUFNRixPQUFPO1FBQUE7UUFBQSxDQUFBbEQsY0FBQSxHQUFBQyxDQUFBLFFBQUFvRCxFQUFBLENBQUFELEVBQUE7UUFBQTtRQUFBcEQsY0FBQSxHQUFBQyxDQUFBO1FBQ2hCLElBQUlpRCxPQUFPLENBQUNDLElBQUksQ0FBQzVCLEtBQUssQ0FBQyxFQUFFO1VBQUE7VUFBQXZCLGNBQUEsR0FBQW9CLENBQUE7VUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtVQUN2QixJQUFJLENBQUNrQyxtQkFBbUIsQ0FBQztZQUN2QkMsSUFBSSxFQUFFLGFBQWE7WUFDbkJDLFNBQVMsRUFBRSxJQUFJQyxJQUFJLEVBQUU7WUFDckJDLE9BQU8sRUFBRWhCLEtBQUs7WUFDZG1CLFFBQVEsRUFBRTtXQUNYLENBQUM7VUFBQztVQUFBMUMsY0FBQSxHQUFBQyxDQUFBO1VBRUgsT0FBTztZQUNMeUIsT0FBTyxFQUFFLEtBQUs7WUFDZGlCLFNBQVMsRUFBRSxnQkFBZ0I7WUFDM0JULEtBQUssRUFBRSwwQkFBMEI7WUFDakNVLGFBQWEsRUFBRSxJQUFJO1lBQ25CRyxVQUFVLEVBQUU7V0FDYjtRQUNILENBQUM7UUFBQTtRQUFBO1VBQUEvQyxjQUFBLEdBQUFvQixDQUFBO1FBQUE7TUFDSDtJQUNGLENBQUM7SUFBQTtJQUFBO01BQUFwQixjQUFBLEdBQUFvQixDQUFBO0lBQUE7SUFFRDtJQUFBcEIsY0FBQSxHQUFBQyxDQUFBO0lBQ0EsS0FBc0IsSUFBQXFELEVBQUE7TUFBQTtNQUFBLENBQUF0RCxjQUFBLEdBQUFDLENBQUEsU0FBNkIsR0FBN0JzRCxFQUFBO01BQUE7TUFBQSxDQUFBdkQsY0FBQSxHQUFBQyxDQUFBLFlBQUksQ0FBQ08sd0JBQXdCLEdBQTdCOEMsRUFBQSxHQUFBQyxFQUFBLENBQUFWLE1BQTZCLEVBQTdCUyxFQUFBLEVBQTZCLEVBQUU7TUFBaEQsSUFBTUosT0FBTztNQUFBO01BQUEsQ0FBQWxELGNBQUEsR0FBQUMsQ0FBQSxRQUFBc0QsRUFBQSxDQUFBRCxFQUFBO01BQUE7TUFBQXRELGNBQUEsR0FBQUMsQ0FBQTtNQUNoQixJQUFJaUQsT0FBTyxDQUFDQyxJQUFJLENBQUM1QixLQUFLLENBQUMsRUFBRTtRQUFBO1FBQUF2QixjQUFBLEdBQUFvQixDQUFBO1FBQUFwQixjQUFBLEdBQUFDLENBQUE7UUFDdkIsSUFBSSxDQUFDa0MsbUJBQW1CLENBQUM7VUFDdkJDLElBQUksRUFBRSx5QkFBeUI7VUFDL0JDLFNBQVMsRUFBRSxJQUFJQyxJQUFJLEVBQUU7VUFDckJDLE9BQU8sRUFBRWhCLEtBQUs7VUFDZG1CLFFBQVEsRUFBRTtTQUNYLENBQUM7UUFBQztRQUFBMUMsY0FBQSxHQUFBQyxDQUFBO1FBRUgsT0FBTztVQUNMeUIsT0FBTyxFQUFFLEtBQUs7VUFDZGlCLFNBQVMsRUFBRSxnQkFBZ0I7VUFDM0JULEtBQUssRUFBRSwwQkFBMEI7VUFDakNVLGFBQWEsRUFBRSxJQUFJO1VBQ25CRyxVQUFVLEVBQUU7U0FDYjtNQUNILENBQUM7TUFBQTtNQUFBO1FBQUEvQyxjQUFBLEdBQUFvQixDQUFBO01BQUE7SUFDSDtJQUVBO0lBQ0EsSUFBTW9DLFNBQVM7SUFBQTtJQUFBLENBQUF4RCxjQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUN3RCxjQUFjLENBQUNsQyxLQUFLLENBQUM7SUFBQztJQUFBdkIsY0FBQSxHQUFBQyxDQUFBO0lBRTdDLE9BQU87TUFDTHlCLE9BQU8sRUFBRSxJQUFJO01BQ2JDLGNBQWMsRUFBRTZCO0tBQ2pCO0VBQ0gsQ0FBQztFQUVEOzs7RUFBQTtFQUFBeEQsY0FBQSxHQUFBQyxDQUFBO0VBR1FGLGlCQUFBLENBQUFzQixTQUFBLENBQUFVLGFBQWEsR0FBckIsVUFBc0JSLEtBQVksRUFBRUMsT0FBZTtJQUFBO0lBQUF4QixjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBQyxDQUFBO0lBQ2pEO0lBQ0EsSUFBSXNCLEtBQUssQ0FBQ3NCLE1BQU0sR0FBRyxJQUFJLENBQUNuQyxNQUFNLENBQUNFLGNBQWMsRUFBRTtNQUFBO01BQUFaLGNBQUEsR0FBQW9CLENBQUE7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUM3QyxJQUFJLENBQUNrQyxtQkFBbUIsQ0FBQztRQUN2QkMsSUFBSSxFQUFFLGFBQWE7UUFDbkJDLFNBQVMsRUFBRSxJQUFJQyxJQUFJLEVBQUU7UUFDckJDLE9BQU8sRUFBRSxvQkFBQU8sTUFBQSxDQUFvQnZCLEtBQUssQ0FBQ3NCLE1BQU0sV0FBUTtRQUNqREgsUUFBUSxFQUFFO09BQ1gsQ0FBQztNQUFDO01BQUExQyxjQUFBLEdBQUFDLENBQUE7TUFFSCxPQUFPO1FBQ0x5QixPQUFPLEVBQUUsS0FBSztRQUNkaUIsU0FBUyxFQUFFLGtCQUFrQjtRQUM3QlQsS0FBSyxFQUFFLGlCQUFpQjtRQUN4QlUsYUFBYSxFQUFFLElBQUk7UUFDbkJHLFVBQVUsRUFBRTtPQUNiO0lBQ0gsQ0FBQztJQUFBO0lBQUE7TUFBQS9DLGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUVEO0lBQ0EsSUFBTXNDLGNBQWM7SUFBQTtJQUFBLENBQUExRCxjQUFBLEdBQUFDLENBQUEsUUFBRyxFQUFFO0lBQUM7SUFBQUQsY0FBQSxHQUFBQyxDQUFBO0lBQzFCLEtBQUssSUFBSTBELENBQUM7SUFBQTtJQUFBLENBQUEzRCxjQUFBLEdBQUFDLENBQUEsUUFBRyxDQUFDLEdBQUUwRCxDQUFDLEdBQUdwQyxLQUFLLENBQUNzQixNQUFNLEVBQUVjLENBQUMsRUFBRSxFQUFFO01BQ3JDLElBQU1DLGNBQWM7TUFBQTtNQUFBLENBQUE1RCxjQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNxQixhQUFhLENBQUNDLEtBQUssQ0FBQ29DLENBQUMsQ0FBQyxFQUFFLEdBQUFiLE1BQUEsQ0FBR3RCLE9BQU8sT0FBQXNCLE1BQUEsQ0FBSWEsQ0FBQyxNQUFHLENBQUM7TUFBQztNQUFBM0QsY0FBQSxHQUFBQyxDQUFBO01BRXhFLElBQUksQ0FBQzJELGNBQWMsQ0FBQ2xDLE9BQU8sRUFBRTtRQUFBO1FBQUExQixjQUFBLEdBQUFvQixDQUFBO1FBQUFwQixjQUFBLEdBQUFDLENBQUE7UUFDM0I7UUFDQSxJQUFJLENBQUNrQyxtQkFBbUIsQ0FBQztVQUN2QkMsSUFBSSxFQUFFLHlCQUF5QjtVQUMvQkMsU0FBUyxFQUFFLElBQUlDLElBQUksRUFBRTtVQUNyQkMsT0FBTyxFQUFFQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ2xCLEtBQUssQ0FBQztVQUM5Qm1CLFFBQVEsRUFBRTtTQUNYLENBQUM7UUFBQztRQUFBMUMsY0FBQSxHQUFBQyxDQUFBO1FBRUgsT0FBTztVQUNMeUIsT0FBTyxFQUFFLEtBQUs7VUFDZGlCLFNBQVMsRUFBRSxnQkFBZ0I7VUFDM0JULEtBQUssRUFBRSxtQ0FBbUM7VUFDMUNVLGFBQWEsRUFBRSxJQUFJO1VBQ25CRyxVQUFVLEVBQUU7U0FDYjtNQUNILENBQUM7TUFBQTtNQUFBO1FBQUEvQyxjQUFBLEdBQUFvQixDQUFBO01BQUE7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUVEeUQsY0FBYyxDQUFDRyxJQUFJLENBQUNELGNBQWMsQ0FBQ2pDLGNBQWMsQ0FBQztJQUNwRDtJQUFDO0lBQUEzQixjQUFBLEdBQUFDLENBQUE7SUFFRCxPQUFPO01BQ0x5QixPQUFPLEVBQUUsSUFBSTtNQUNiQyxjQUFjLEVBQUUrQjtLQUNqQjtFQUNILENBQUM7RUFFRDs7O0VBQUE7RUFBQTFELGNBQUEsR0FBQUMsQ0FBQTtFQUdRRixpQkFBQSxDQUFBc0IsU0FBQSxDQUFBVyxjQUFjLEdBQXRCLFVBQXVCVCxLQUFVLEVBQUVDLE9BQWU7SUFBQTtJQUFBeEIsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUMsQ0FBQTtJQUNoRDtJQUNBLEtBQXNCLElBQUErQyxFQUFBO01BQUE7TUFBQSxDQUFBaEQsY0FBQSxHQUFBQyxDQUFBLFNBQWlDLEdBQWpDZ0QsRUFBQTtNQUFBO01BQUEsQ0FBQWpELGNBQUEsR0FBQUMsQ0FBQSxZQUFJLENBQUNRLDRCQUE0QixHQUFqQ3VDLEVBQUEsR0FBQUMsRUFBQSxDQUFBSixNQUFpQyxFQUFqQ0csRUFBQSxFQUFpQyxFQUFFO01BQXBELElBQU1FLE9BQU87TUFBQTtNQUFBLENBQUFsRCxjQUFBLEdBQUFDLENBQUEsUUFBQWdELEVBQUEsQ0FBQUQsRUFBQTtNQUNoQixJQUFNYyxXQUFXO01BQUE7TUFBQSxDQUFBOUQsY0FBQSxHQUFBQyxDQUFBLFFBQUd1QyxJQUFJLENBQUNDLFNBQVMsQ0FBQ2xCLEtBQUssQ0FBQztNQUFDO01BQUF2QixjQUFBLEdBQUFDLENBQUE7TUFDMUMsSUFBSWlELE9BQU8sQ0FBQ0MsSUFBSSxDQUFDVyxXQUFXLENBQUMsRUFBRTtRQUFBO1FBQUE5RCxjQUFBLEdBQUFvQixDQUFBO1FBQUFwQixjQUFBLEdBQUFDLENBQUE7UUFDN0IsSUFBSSxDQUFDa0MsbUJBQW1CLENBQUM7VUFDdkJDLElBQUksRUFBRSw2QkFBNkI7VUFDbkNDLFNBQVMsRUFBRSxJQUFJQyxJQUFJLEVBQUU7VUFDckJDLE9BQU8sRUFBRXVCLFdBQVc7VUFDcEJwQixRQUFRLEVBQUU7U0FDWCxDQUFDO1FBQUM7UUFBQTFDLGNBQUEsR0FBQUMsQ0FBQTtRQUVILE9BQU87VUFDTHlCLE9BQU8sRUFBRSxLQUFLO1VBQ2RpQixTQUFTLEVBQUUsZ0JBQWdCO1VBQzNCVCxLQUFLLEVBQUUscUNBQXFDO1VBQzVDVSxhQUFhLEVBQUUsSUFBSTtVQUNuQkcsVUFBVSxFQUFFO1NBQ2I7TUFDSCxDQUFDO01BQUE7TUFBQTtRQUFBL0MsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQ0g7SUFFQTtJQUNBLElBQU0yQyxlQUFlO0lBQUE7SUFBQSxDQUFBL0QsY0FBQSxHQUFBQyxDQUFBLFFBQVEsRUFBRTtJQUFDO0lBQUFELGNBQUEsR0FBQUMsQ0FBQTtJQUNoQyxLQUEyQixJQUFBbUQsRUFBQTtNQUFBO01BQUEsQ0FBQXBELGNBQUEsR0FBQUMsQ0FBQSxTQUFxQixHQUFyQm9ELEVBQUE7TUFBQTtNQUFBLENBQUFyRCxjQUFBLEdBQUFDLENBQUEsUUFBQStELE1BQU0sQ0FBQ0MsT0FBTyxDQUFDMUMsS0FBSyxDQUFDLEdBQXJCNkIsRUFBQSxHQUFBQyxFQUFBLENBQUFSLE1BQXFCLEVBQXJCTyxFQUFBLEVBQXFCLEVBQUU7TUFBdkMsSUFBQUUsRUFBQTtRQUFBO1FBQUEsQ0FBQXRELGNBQUEsR0FBQUMsQ0FBQSxRQUFBb0QsRUFBQSxDQUFBRCxFQUFBLENBQVk7UUFBWGMsR0FBRztRQUFBO1FBQUEsQ0FBQWxFLGNBQUEsR0FBQUMsQ0FBQSxRQUFBcUQsRUFBQTtRQUFFYSxLQUFLO1FBQUE7UUFBQSxDQUFBbkUsY0FBQSxHQUFBQyxDQUFBLFFBQUFxRCxFQUFBO01BQ3BCLElBQU1jLGFBQWE7TUFBQTtNQUFBLENBQUFwRSxjQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNxQixhQUFhLENBQUM0QyxHQUFHLEVBQUUsR0FBQXBCLE1BQUEsQ0FBR3RCLE9BQU8sU0FBTSxDQUFDO01BQy9ELElBQU02QyxlQUFlO01BQUE7TUFBQSxDQUFBckUsY0FBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSSxDQUFDcUIsYUFBYSxDQUFDNkMsS0FBSyxFQUFFLEdBQUFyQixNQUFBLENBQUd0QixPQUFPLE9BQUFzQixNQUFBLENBQUlvQixHQUFHLENBQUUsQ0FBQztNQUFDO01BQUFsRSxjQUFBLEdBQUFDLENBQUE7TUFFdkU7TUFBSTtNQUFBLENBQUFELGNBQUEsR0FBQW9CLENBQUEsWUFBQ2dELGFBQWEsQ0FBQzFDLE9BQU87TUFBQTtNQUFBLENBQUExQixjQUFBLEdBQUFvQixDQUFBLFdBQUksQ0FBQ2lELGVBQWUsQ0FBQzNDLE9BQU8sR0FBRTtRQUFBO1FBQUExQixjQUFBLEdBQUFvQixDQUFBO1FBQUFwQixjQUFBLEdBQUFDLENBQUE7UUFDdEQsT0FBTztVQUNMeUIsT0FBTyxFQUFFLEtBQUs7VUFDZGlCLFNBQVMsRUFBRSxnQkFBZ0I7VUFDM0JULEtBQUssRUFBRSxvQ0FBb0M7VUFDM0NVLGFBQWEsRUFBRTtTQUNoQjtNQUNILENBQUM7TUFBQTtNQUFBO1FBQUE1QyxjQUFBLEdBQUFvQixDQUFBO01BQUE7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUVEOEQsZUFBZTtNQUFDO01BQUEsQ0FBQS9ELGNBQUEsR0FBQW9CLENBQUEsV0FBQWdELGFBQWEsQ0FBQ3pDLGNBQWM7TUFBQTtNQUFBLENBQUEzQixjQUFBLEdBQUFvQixDQUFBLFdBQUk4QyxHQUFHLEVBQUMsR0FBR0csZUFBZSxDQUFDMUMsY0FBYztJQUN2RjtJQUFDO0lBQUEzQixjQUFBLEdBQUFDLENBQUE7SUFFRCxPQUFPO01BQ0x5QixPQUFPLEVBQUUsSUFBSTtNQUNiQyxjQUFjLEVBQUVvQztLQUNqQjtFQUNILENBQUM7RUFFRDs7O0VBQUE7RUFBQS9ELGNBQUEsR0FBQUMsQ0FBQTtFQUdRRixpQkFBQSxDQUFBc0IsU0FBQSxDQUFBWSxpQkFBaUIsR0FBekIsVUFBMEJWLEtBQVUsRUFBRUMsT0FBZTtJQUFBO0lBQUF4QixjQUFBLEdBQUFFLENBQUE7SUFDbkQ7SUFDQSxJQUFNb0UsV0FBVztJQUFBO0lBQUEsQ0FBQXRFLGNBQUEsR0FBQUMsQ0FBQSxTQUFHc0UsTUFBTSxDQUFDaEQsS0FBSyxDQUFDO0lBQUM7SUFBQXZCLGNBQUEsR0FBQUMsQ0FBQTtJQUNsQyxPQUFPLElBQUksQ0FBQzJCLGNBQWMsQ0FBQzBDLFdBQVcsRUFBRTlDLE9BQU8sQ0FBQztFQUNsRCxDQUFDO0VBRUQ7OztFQUFBO0VBQUF4QixjQUFBLEdBQUFDLENBQUE7RUFHUUYsaUJBQUEsQ0FBQXNCLFNBQUEsQ0FBQW9DLGNBQWMsR0FBdEIsVUFBdUJsQyxLQUFhO0lBQUE7SUFBQXZCLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFDLENBQUE7SUFDbEMsT0FBT3NCLEtBQUssQ0FDVGlELElBQUksRUFBRSxDQUNOQyxPQUFPLENBQUMsT0FBTyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBQUEsQ0FDckJBLE9BQU8sQ0FBQyxlQUFlLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFBQSxDQUM3QkEsT0FBTyxDQUFDLGFBQWEsRUFBRSxFQUFFLENBQUMsQ0FBQztJQUFBLENBQzNCQSxPQUFPLENBQUMsbUJBQW1CLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFBQSxDQUNqQ0EsT0FBTyxDQUFDLGFBQWEsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDO0VBQ2pDLENBQUM7RUFFRDs7O0VBQUE7RUFBQXpFLGNBQUEsR0FBQUMsQ0FBQTtFQUdPRixpQkFBQSxDQUFBMkUsV0FBVyxHQUFsQixVQUFtQm5ELEtBQWE7SUFBQTtJQUFBdkIsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUMsQ0FBQTtJQUM5QjtJQUFJO0lBQUEsQ0FBQUQsY0FBQSxHQUFBb0IsQ0FBQSxZQUFDRyxLQUFLO0lBQUE7SUFBQSxDQUFBdkIsY0FBQSxHQUFBb0IsQ0FBQSxXQUFJLE9BQU9HLEtBQUssS0FBSyxRQUFRLEdBQUU7TUFBQTtNQUFBdkIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBcEIsY0FBQSxHQUFBQyxDQUFBO01BQ3ZDLE9BQU8sS0FBSztJQUNkLENBQUM7SUFBQTtJQUFBO01BQUFELGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUVELElBQU11RCxXQUFXO0lBQUE7SUFBQSxDQUFBM0UsY0FBQSxHQUFBQyxDQUFBLFNBQUcsQ0FDbEIsOEJBQThCLEVBQzlCLDhCQUE4QixFQUM5Qiw4QkFBOEIsRUFDOUIsZ0JBQWdCLEVBQ2hCLGVBQWUsRUFDZixlQUFlLEVBQ2YsZUFBZSxFQUNmLGFBQWEsRUFDYixtQkFBbUIsRUFDbkIsYUFBYTtJQUFFO0lBQ2Ysc0JBQXNCLEVBQ3RCLHdCQUF3QixDQUN6QjtJQUFDO0lBQUFELGNBQUEsR0FBQUMsQ0FBQTtJQUVGLE9BQU8wRSxXQUFXLENBQUNDLElBQUksQ0FBQyxVQUFBMUIsT0FBTztNQUFBO01BQUFsRCxjQUFBLEdBQUFFLENBQUE7TUFBQUYsY0FBQSxHQUFBQyxDQUFBO01BQUksT0FBQWlELE9BQU8sQ0FBQ0MsSUFBSSxDQUFDNUIsS0FBSyxDQUFDO0lBQW5CLENBQW1CLENBQUM7RUFDekQsQ0FBQztFQUVEOzs7RUFBQTtFQUFBdkIsY0FBQSxHQUFBQyxDQUFBO0VBR09GLGlCQUFBLENBQUE4RSxpQkFBaUIsR0FBeEIsVUFBeUJDLFFBQWEsRUFBRUMsU0FBaUI7SUFBQTtJQUFBL0UsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUMsQ0FBQTtJQUN2RCxJQUFJO01BQUE7TUFBQUQsY0FBQSxHQUFBQyxDQUFBO01BQ0YsSUFBSSxDQUFDNkUsUUFBUSxFQUFFO1FBQUE7UUFBQTlFLGNBQUEsR0FBQW9CLENBQUE7UUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtRQUNiLE9BQU87VUFBRXlCLE9BQU8sRUFBRSxJQUFJO1VBQUVDLGNBQWMsRUFBRW1EO1FBQVEsQ0FBRTtNQUNwRCxDQUFDO01BQUE7TUFBQTtRQUFBOUUsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO01BRUQsSUFBTTRELFVBQVU7TUFBQTtNQUFBLENBQUFoRixjQUFBLEdBQUFDLENBQUEsU0FBR3VDLElBQUksQ0FBQ0MsU0FBUyxDQUFDcUMsUUFBUSxDQUFDO01BRTNDO01BQUE7TUFBQTlFLGNBQUEsR0FBQUMsQ0FBQTtNQUNBLElBQUksSUFBSSxDQUFDeUUsV0FBVyxDQUFDTSxVQUFVLENBQUMsRUFBRTtRQUFBO1FBQUFoRixjQUFBLEdBQUFvQixDQUFBO1FBQUFwQixjQUFBLEdBQUFDLENBQUE7UUFDaEMsT0FBTztVQUNMeUIsT0FBTyxFQUFFLEtBQUs7VUFDZGlCLFNBQVMsRUFBRSxnQkFBZ0I7VUFDM0JULEtBQUssRUFBRSxtQkFBQVksTUFBQSxDQUFtQmlDLFNBQVMsQ0FBRTtVQUNyQ25DLGFBQWEsRUFBRSxJQUFJO1VBQ25CRyxVQUFVLEVBQUU7U0FDYjtNQUNILENBQUM7TUFBQTtNQUFBO1FBQUEvQyxjQUFBLEdBQUFvQixDQUFBO01BQUE7TUFFRDtNQUNBLElBQU02RCx5QkFBeUI7TUFBQTtNQUFBLENBQUFqRixjQUFBLEdBQUFDLENBQUEsU0FBRyxDQUNoQyxjQUFjO01BQUU7TUFDaEIsZ0JBQWdCO01BQUU7TUFDbEIsYUFBYSxDQUFFO01BQUEsQ0FDaEI7TUFBQztNQUFBRCxjQUFBLEdBQUFDLENBQUE7TUFFRixLQUFzQixJQUFBK0MsRUFBQTtRQUFBO1FBQUEsQ0FBQWhELGNBQUEsR0FBQUMsQ0FBQSxVQUF5QixHQUF6QmlGLDJCQUFBO1FBQUE7UUFBQSxDQUFBbEYsY0FBQSxHQUFBQyxDQUFBLFNBQUFnRix5QkFBeUIsR0FBekJqQyxFQUFBLEdBQUFrQywyQkFBQSxDQUFBckMsTUFBeUIsRUFBekJHLEVBQUEsRUFBeUIsRUFBRTtRQUE1QyxJQUFNRSxPQUFPO1FBQUE7UUFBQSxDQUFBbEQsY0FBQSxHQUFBQyxDQUFBLFNBQUFpRiwyQkFBQSxDQUFBbEMsRUFBQTtRQUFBO1FBQUFoRCxjQUFBLEdBQUFDLENBQUE7UUFDaEIsSUFBSWlELE9BQU8sQ0FBQ0MsSUFBSSxDQUFDNkIsVUFBVSxDQUFDLEVBQUU7VUFBQTtVQUFBaEYsY0FBQSxHQUFBb0IsQ0FBQTtVQUFBcEIsY0FBQSxHQUFBQyxDQUFBO1VBQzVCLE9BQU87WUFDTHlCLE9BQU8sRUFBRSxLQUFLO1lBQ2RpQixTQUFTLEVBQUUsZ0JBQWdCO1lBQzNCVCxLQUFLLEVBQUUsa0NBQUFZLE1BQUEsQ0FBa0NpQyxTQUFTLENBQUU7WUFDcERuQyxhQUFhLEVBQUUsSUFBSTtZQUNuQkcsVUFBVSxFQUFFO1dBQ2I7UUFDSCxDQUFDO1FBQUE7UUFBQTtVQUFBL0MsY0FBQSxHQUFBb0IsQ0FBQTtRQUFBO01BQ0g7TUFFQTtNQUNBLElBQU1vQyxTQUFTO01BQUE7TUFBQSxDQUFBeEQsY0FBQSxHQUFBQyxDQUFBLFNBQUcsSUFBSSxDQUFDa0Ysa0JBQWtCLENBQUNMLFFBQVEsQ0FBQztNQUFDO01BQUE5RSxjQUFBLEdBQUFDLENBQUE7TUFFcEQsT0FBTztRQUNMeUIsT0FBTyxFQUFFLElBQUk7UUFDYkMsY0FBYyxFQUFFNkI7T0FDakI7SUFDSCxDQUFDLENBQUMsT0FBT3RCLEtBQUssRUFBRTtNQUFBO01BQUFsQyxjQUFBLEdBQUFDLENBQUE7TUFDZCxPQUFPO1FBQ0x5QixPQUFPLEVBQUUsS0FBSztRQUNkaUIsU0FBUyxFQUFFLGtCQUFrQjtRQUM3QlQsS0FBSyxFQUFFLG1CQUFBWSxNQUFBLENBQW1CaUMsU0FBUyxDQUFFO1FBQ3JDbkMsYUFBYSxFQUFFO09BQ2hCO0lBQ0g7RUFDRixDQUFDO0VBRUQ7OztFQUFBO0VBQUE1QyxjQUFBLEdBQUFDLENBQUE7RUFHZUYsaUJBQUEsQ0FBQW9GLGtCQUFrQixHQUFqQyxVQUFrQ0MsR0FBUTtJQUFBO0lBQUFwRixjQUFBLEdBQUFFLENBQUE7SUFBMUMsSUFBQW1GLEtBQUE7SUFBQTtJQUFBLENBQUFyRixjQUFBLEdBQUFDLENBQUE7SUFnQ0M7SUFBQUQsY0FBQSxHQUFBQyxDQUFBO0lBL0JDO0lBQUk7SUFBQSxDQUFBRCxjQUFBLEdBQUFvQixDQUFBLFdBQUFnRSxHQUFHLEtBQUssSUFBSTtJQUFBO0lBQUEsQ0FBQXBGLGNBQUEsR0FBQW9CLENBQUEsV0FBSWdFLEdBQUcsS0FBSzNELFNBQVMsR0FBRTtNQUFBO01BQUF6QixjQUFBLEdBQUFvQixDQUFBO01BQUFwQixjQUFBLEdBQUFDLENBQUE7TUFDckMsT0FBT21GLEdBQUc7SUFDWixDQUFDO0lBQUE7SUFBQTtNQUFBcEYsY0FBQSxHQUFBb0IsQ0FBQTtJQUFBO0lBQUFwQixjQUFBLEdBQUFDLENBQUE7SUFFRCxJQUFJLE9BQU9tRixHQUFHLEtBQUssUUFBUSxFQUFFO01BQUE7TUFBQXBGLGNBQUEsR0FBQW9CLENBQUE7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUMzQjtNQUNBLE9BQU9tRixHQUFHLENBQ1BYLE9BQU8sQ0FBQyw4QkFBOEIsRUFBRSxFQUFFLENBQUMsQ0FDM0NBLE9BQU8sQ0FBQyxlQUFlLEVBQUUsRUFBRSxDQUFDLENBQzVCQSxPQUFPLENBQUMsYUFBYSxFQUFFLEVBQUUsQ0FBQyxDQUMxQkEsT0FBTyxDQUFDLGFBQWEsRUFBRSxFQUFFLENBQUMsQ0FDMUJBLE9BQU8sQ0FBQyxjQUFjLEVBQUUsRUFBRSxDQUFDLENBQUM7TUFBQSxDQUM1QkQsSUFBSSxFQUFFO0lBQ1gsQ0FBQztJQUFBO0lBQUE7TUFBQXhFLGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUFBcEIsY0FBQSxHQUFBQyxDQUFBO0lBRUQsSUFBSTRCLEtBQUssQ0FBQ0MsT0FBTyxDQUFDc0QsR0FBRyxDQUFDLEVBQUU7TUFBQTtNQUFBcEYsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBcEIsY0FBQSxHQUFBQyxDQUFBO01BQ3RCLE9BQU9tRixHQUFHLENBQUNFLEdBQUcsQ0FBQyxVQUFBQyxJQUFJO1FBQUE7UUFBQXZGLGNBQUEsR0FBQUUsQ0FBQTtRQUFBRixjQUFBLEdBQUFDLENBQUE7UUFBSSxPQUFBb0YsS0FBSSxDQUFDRixrQkFBa0IsQ0FBQ0ksSUFBSSxDQUFDO01BQTdCLENBQTZCLENBQUM7SUFDdkQsQ0FBQztJQUFBO0lBQUE7TUFBQXZGLGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUFBcEIsY0FBQSxHQUFBQyxDQUFBO0lBRUQsSUFBSSxPQUFPbUYsR0FBRyxLQUFLLFFBQVEsRUFBRTtNQUFBO01BQUFwRixjQUFBLEdBQUFvQixDQUFBO01BQzNCLElBQU1vQyxTQUFTO01BQUE7TUFBQSxDQUFBeEQsY0FBQSxHQUFBQyxDQUFBLFNBQVEsRUFBRTtNQUFDO01BQUFELGNBQUEsR0FBQUMsQ0FBQTtNQUMxQixLQUEyQixJQUFBK0MsRUFBQTtRQUFBO1FBQUEsQ0FBQWhELGNBQUEsR0FBQUMsQ0FBQSxVQUFtQixHQUFuQmdELEVBQUE7UUFBQTtRQUFBLENBQUFqRCxjQUFBLEdBQUFDLENBQUEsU0FBQStELE1BQU0sQ0FBQ0MsT0FBTyxDQUFDbUIsR0FBRyxDQUFDLEdBQW5CcEMsRUFBQSxHQUFBQyxFQUFBLENBQUFKLE1BQW1CLEVBQW5CRyxFQUFBLEVBQW1CLEVBQUU7UUFBckMsSUFBQUksRUFBQTtVQUFBO1VBQUEsQ0FBQXBELGNBQUEsR0FBQUMsQ0FBQSxTQUFBZ0QsRUFBQSxDQUFBRCxFQUFBLENBQVk7VUFBWGtCLEdBQUc7VUFBQTtVQUFBLENBQUFsRSxjQUFBLEdBQUFDLENBQUEsU0FBQW1ELEVBQUE7VUFBRWUsS0FBSztVQUFBO1VBQUEsQ0FBQW5FLGNBQUEsR0FBQUMsQ0FBQSxTQUFBbUQsRUFBQTtRQUNwQjtRQUNBLElBQU1vQyxZQUFZO1FBQUE7UUFBQSxDQUFBeEYsY0FBQSxHQUFBQyxDQUFBLFNBQUcsT0FBT2lFLEdBQUcsS0FBSyxRQUFRO1FBQUE7UUFBQSxDQUFBbEUsY0FBQSxHQUFBb0IsQ0FBQSxXQUMxQzhDLEdBQUcsQ0FBQ08sT0FBTyxDQUFDLE9BQU8sRUFBRSxFQUFFLENBQUMsQ0FBQ0EsT0FBTyxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUM7UUFBQTtRQUFBLENBQUF6RSxjQUFBLEdBQUFvQixDQUFBLFdBQUc4QyxHQUFHO1FBQUM7UUFBQWxFLGNBQUEsR0FBQUMsQ0FBQTtRQUM5RHVELFNBQVMsQ0FBQ2dDLFlBQVksQ0FBQyxHQUFHLElBQUksQ0FBQ0wsa0JBQWtCLENBQUNoQixLQUFLLENBQUM7TUFDMUQ7TUFBQztNQUFBbkUsY0FBQSxHQUFBQyxDQUFBO01BQ0QsT0FBT3VELFNBQVM7SUFDbEIsQ0FBQztJQUFBO0lBQUE7TUFBQXhELGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUFBcEIsY0FBQSxHQUFBQyxDQUFBO0lBRUQsT0FBT21GLEdBQUc7RUFDWixDQUFDO0VBRUQ7OztFQUFBO0VBQUFwRixjQUFBLEdBQUFDLENBQUE7RUFHQUYsaUJBQUEsQ0FBQXNCLFNBQUEsQ0FBQW9FLGlCQUFpQixHQUFqQixVQUFrQkMsVUFBa0I7SUFBQTtJQUFBMUYsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUMsQ0FBQTtJQUNsQyxJQUFJLENBQUMsSUFBSSxDQUFDUyxNQUFNLENBQUNLLGtCQUFrQixFQUFFO01BQUE7TUFBQWYsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBcEIsY0FBQSxHQUFBQyxDQUFBO01BQ25DLE9BQU87UUFBRXlCLE9BQU8sRUFBRTtNQUFJLENBQUU7SUFDMUIsQ0FBQztJQUFBO0lBQUE7TUFBQTFCLGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUVELElBQU11RSxHQUFHO0lBQUE7SUFBQSxDQUFBM0YsY0FBQSxHQUFBQyxDQUFBLFNBQUdxQyxJQUFJLENBQUNxRCxHQUFHLEVBQUU7SUFDdEIsSUFBTUMsYUFBYTtJQUFBO0lBQUEsQ0FBQTVGLGNBQUEsR0FBQUMsQ0FBQSxTQUFHLElBQUksQ0FBQ0UsWUFBWSxDQUFDMEYsR0FBRyxDQUFDSCxVQUFVLENBQUM7SUFBQztJQUFBMUYsY0FBQSxHQUFBQyxDQUFBO0lBRXhELElBQUksQ0FBQzJGLGFBQWEsRUFBRTtNQUFBO01BQUE1RixjQUFBLEdBQUFvQixDQUFBO01BQUFwQixjQUFBLEdBQUFDLENBQUE7TUFDbEI7TUFDQSxJQUFJLENBQUNFLFlBQVksQ0FBQzJGLEdBQUcsQ0FBQ0osVUFBVSxFQUFFO1FBQUVLLEtBQUssRUFBRSxDQUFDO1FBQUVDLFdBQVcsRUFBRUw7TUFBRyxDQUFFLENBQUM7TUFBQztNQUFBM0YsY0FBQSxHQUFBQyxDQUFBO01BQ2xFLE9BQU87UUFBRXlCLE9BQU8sRUFBRTtNQUFJLENBQUU7SUFDMUIsQ0FBQztJQUFBO0lBQUE7TUFBQTFCLGNBQUEsR0FBQW9CLENBQUE7SUFBQTtJQUVEO0lBQUFwQixjQUFBLEdBQUFDLENBQUE7SUFDQSxJQUFJMEYsR0FBRyxHQUFHQyxhQUFhLENBQUNJLFdBQVcsR0FBRyxJQUFJLENBQUN0RixNQUFNLENBQUNNLGVBQWUsRUFBRTtNQUFBO01BQUFoQixjQUFBLEdBQUFvQixDQUFBO01BQUFwQixjQUFBLEdBQUFDLENBQUE7TUFDakUsSUFBSTJGLGFBQWEsQ0FBQ0csS0FBSyxJQUFJLElBQUksQ0FBQ3JGLE1BQU0sQ0FBQ08sb0JBQW9CLEVBQUU7UUFBQTtRQUFBakIsY0FBQSxHQUFBb0IsQ0FBQTtRQUFBcEIsY0FBQSxHQUFBQyxDQUFBO1FBQzNELElBQUksQ0FBQ2tDLG1CQUFtQixDQUFDO1VBQ3ZCQyxJQUFJLEVBQUUscUJBQXFCO1VBQzNCQyxTQUFTLEVBQUUsSUFBSUMsSUFBSSxFQUFFO1VBQ3JCQyxPQUFPLEVBQUUsZUFBQU8sTUFBQSxDQUFlNEMsVUFBVSxlQUFBNUMsTUFBQSxDQUFZOEMsYUFBYSxDQUFDRyxLQUFLLENBQUU7VUFDbkVyRCxRQUFRLEVBQUU7U0FDWCxDQUFDO1FBQUM7UUFBQTFDLGNBQUEsR0FBQUMsQ0FBQTtRQUVILE9BQU87VUFDTHlCLE9BQU8sRUFBRSxLQUFLO1VBQ2RpQixTQUFTLEVBQUUsZ0JBQWdCO1VBQzNCVCxLQUFLLEVBQUUscUJBQXFCO1VBQzVCVSxhQUFhLEVBQUUsSUFBSTtVQUNuQkcsVUFBVSxFQUFFO1NBQ2I7TUFDSCxDQUFDO01BQUE7TUFBQTtRQUFBL0MsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO01BRUQ7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUNBMkYsYUFBYSxDQUFDRyxLQUFLLEVBQUU7SUFDdkIsQ0FBQyxNQUFNO01BQUE7TUFBQS9GLGNBQUEsR0FBQW9CLENBQUE7TUFBQXBCLGNBQUEsR0FBQUMsQ0FBQTtNQUNMO01BQ0EsSUFBSSxDQUFDRSxZQUFZLENBQUMyRixHQUFHLENBQUNKLFVBQVUsRUFBRTtRQUFFSyxLQUFLLEVBQUUsQ0FBQztRQUFFQyxXQUFXLEVBQUVMO01BQUcsQ0FBRSxDQUFDO0lBQ25FO0lBQUM7SUFBQTNGLGNBQUEsR0FBQUMsQ0FBQTtJQUVELE9BQU87TUFBRXlCLE9BQU8sRUFBRTtJQUFJLENBQUU7RUFDMUIsQ0FBQztFQUVEOzs7RUFBQTtFQUFBMUIsY0FBQSxHQUFBQyxDQUFBO0VBR1FGLGlCQUFBLENBQUFzQixTQUFBLENBQUFjLG1CQUFtQixHQUEzQixVQUE0QjhELFFBQW1FO0lBQUE7SUFBQWpHLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFDLENBQUE7SUFDN0YsSUFBSSxDQUFDSSxpQkFBaUIsQ0FBQ3dELElBQUksQ0FBQ29DLFFBQVEsQ0FBQztJQUVyQztJQUFBO0lBQUFqRyxjQUFBLEdBQUFDLENBQUE7SUFDQWlHLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLGdCQUFnQixFQUFFRixRQUFRLENBQUM7SUFFeEM7SUFBQTtJQUFBakcsY0FBQSxHQUFBQyxDQUFBO0lBQ0EsSUFBSSxJQUFJLENBQUNJLGlCQUFpQixDQUFDd0MsTUFBTSxHQUFHLElBQUksRUFBRTtNQUFBO01BQUE3QyxjQUFBLEdBQUFvQixDQUFBO01BQUFwQixjQUFBLEdBQUFDLENBQUE7TUFDeEMsSUFBSSxDQUFDSSxpQkFBaUIsR0FBRyxJQUFJLENBQUNBLGlCQUFpQixDQUFDK0YsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDO0lBQzlELENBQUM7SUFBQTtJQUFBO01BQUFwRyxjQUFBLEdBQUFvQixDQUFBO0lBQUE7RUFDSCxDQUFDO0VBRUQ7OztFQUFBO0VBQUFwQixjQUFBLEdBQUFDLENBQUE7RUFHQUYsaUJBQUEsQ0FBQXNCLFNBQUEsQ0FBQWdGLHFCQUFxQixHQUFyQjtJQUFBO0lBQUFyRyxjQUFBLEdBQUFFLENBQUE7SUFDRSxJQUFNeUYsR0FBRztJQUFBO0lBQUEsQ0FBQTNGLGNBQUEsR0FBQUMsQ0FBQSxTQUFHcUMsSUFBSSxDQUFDcUQsR0FBRyxFQUFFO0lBQ3RCLElBQU1XLFdBQVc7SUFBQTtJQUFBLENBQUF0RyxjQUFBLEdBQUFDLENBQUEsU0FBRyxJQUFJLENBQUNJLGlCQUFpQixDQUFDa0csTUFBTSxDQUMvQyxVQUFBTixRQUFRO01BQUE7TUFBQWpHLGNBQUEsR0FBQUUsQ0FBQTtNQUFBRixjQUFBLEdBQUFDLENBQUE7TUFBSSxPQUFBMEYsR0FBRyxHQUFHTSxRQUFRLENBQUM1RCxTQUFTLENBQUNtRSxPQUFPLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJO0lBQXhELENBQXdELENBQ3JFO0lBRUQsSUFBTUMsZUFBZTtJQUFBO0lBQUEsQ0FBQXpHLGNBQUEsR0FBQUMsQ0FBQSxTQUFHcUcsV0FBVyxDQUFDSSxNQUFNLENBQUMsVUFBQ0MsR0FBRyxFQUFFVixRQUFRO01BQUE7TUFBQWpHLGNBQUEsR0FBQUUsQ0FBQTtNQUFBRixjQUFBLEdBQUFDLENBQUE7TUFDdkQwRyxHQUFHLENBQUNWLFFBQVEsQ0FBQzdELElBQUksQ0FBQyxHQUFHO01BQUM7TUFBQSxDQUFBcEMsY0FBQSxHQUFBb0IsQ0FBQSxXQUFBdUYsR0FBRyxDQUFDVixRQUFRLENBQUM3RCxJQUFJLENBQUM7TUFBQTtNQUFBLENBQUFwQyxjQUFBLEdBQUFvQixDQUFBLFdBQUksQ0FBQyxLQUFJLENBQUM7TUFBQztNQUFBcEIsY0FBQSxHQUFBQyxDQUFBO01BQ25ELE9BQU8wRyxHQUFHO0lBQ1osQ0FBQyxFQUFFLEVBQTRCLENBQUM7SUFFaEMsSUFBTUMsbUJBQW1CO0lBQUE7SUFBQSxDQUFBNUcsY0FBQSxHQUFBQyxDQUFBLFNBQUdxRyxXQUFXLENBQUNJLE1BQU0sQ0FBQyxVQUFDQyxHQUFHLEVBQUVWLFFBQVE7TUFBQTtNQUFBakcsY0FBQSxHQUFBRSxDQUFBO01BQUFGLGNBQUEsR0FBQUMsQ0FBQTtNQUMzRDBHLEdBQUcsQ0FBQ1YsUUFBUSxDQUFDdkQsUUFBUSxDQUFDLEdBQUc7TUFBQztNQUFBLENBQUExQyxjQUFBLEdBQUFvQixDQUFBLFdBQUF1RixHQUFHLENBQUNWLFFBQVEsQ0FBQ3ZELFFBQVEsQ0FBQztNQUFBO01BQUEsQ0FBQTFDLGNBQUEsR0FBQW9CLENBQUEsV0FBSSxDQUFDLEtBQUksQ0FBQztNQUFDO01BQUFwQixjQUFBLEdBQUFDLENBQUE7TUFDM0QsT0FBTzBHLEdBQUc7SUFDWixDQUFDLEVBQUUsRUFBNEIsQ0FBQztJQUFDO0lBQUEzRyxjQUFBLEdBQUFDLENBQUE7SUFFakMsT0FBTztNQUNMNEcsY0FBYyxFQUFFLElBQUksQ0FBQ3hHLGlCQUFpQixDQUFDd0MsTUFBTTtNQUM3Q3lELFdBQVcsRUFBRUEsV0FBVyxDQUFDekQsTUFBTTtNQUMvQjRELGVBQWUsRUFBQUEsZUFBQTtNQUNmRyxtQkFBbUIsRUFBQUEsbUJBQUE7TUFDbkJFLGVBQWUsRUFBRTtRQUNmQyxpQkFBaUIsRUFBRSxJQUFJLENBQUM1RyxZQUFZLENBQUM2RyxJQUFJO1FBQ3pDdEcsTUFBTSxFQUFFLElBQUksQ0FBQ0E7O0tBRWhCO0VBQ0gsQ0FBQztFQUVEOzs7RUFBQTtFQUFBVixjQUFBLEdBQUFDLENBQUE7RUFHQUYsaUJBQUEsQ0FBQXNCLFNBQUEsQ0FBQTRGLGtCQUFrQixHQUFsQjtJQUFBO0lBQUFqSCxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBQyxDQUFBO0lBQ0UsSUFBSSxDQUFDRSxZQUFZLENBQUMrRyxLQUFLLEVBQUU7RUFDM0IsQ0FBQztFQUVEOzs7RUFBQTtFQUFBbEgsY0FBQSxHQUFBQyxDQUFBO0VBR0FGLGlCQUFBLENBQUFzQixTQUFBLENBQUE4RixZQUFZLEdBQVosVUFBYUMsU0FBa0M7SUFBQTtJQUFBcEgsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUMsQ0FBQTtJQUM3QyxJQUFJLENBQUNTLE1BQU0sR0FBQTJHLFFBQUEsQ0FBQUEsUUFBQSxLQUFRLElBQUksQ0FBQzNHLE1BQU0sR0FBSzBHLFNBQVMsQ0FBRTtFQUNoRCxDQUFDO0VBQUE7RUFBQXBILGNBQUEsR0FBQUMsQ0FBQTtFQUNILE9BQUFGLGlCQUFDO0FBQUQsQ0FBQyxDQTVpQkQ7QUE0aUJDO0FBQUFDLGNBQUEsR0FBQUMsQ0FBQTtBQTVpQllxSCxPQUFBLENBQUF2SCxpQkFBQSxHQUFBQSxpQkFBQTtBQThpQmI7QUFBQTtBQUFBQyxjQUFBLEdBQUFDLENBQUE7QUFDYXFILE9BQUEsQ0FBQUMsaUJBQWlCLEdBQUd4SCxpQkFBaUIsQ0FBQ21CLFdBQVcsRUFBRSIsImlnbm9yZUxpc3QiOltdfQ==