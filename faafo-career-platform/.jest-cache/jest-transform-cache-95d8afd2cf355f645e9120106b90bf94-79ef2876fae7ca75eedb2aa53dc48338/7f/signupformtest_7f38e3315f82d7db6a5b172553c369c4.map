{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/signup-form.test.tsx", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,aAAa;AACb,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAM,OAAA,CAAC;IAClC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;QACtB,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,EAAlC,CAAkC,CAAC;QAC7D,SAAS,EAAE,KAAK;KACjB,CAAC,EAHqB,CAGrB,CAAC;CACJ,CAAC,EALiC,CAKjC,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,cAAM,OAAA,CAAC;IAC5C,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,UAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,IAAK,OAAA,CAAC;QAC3D,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,UAAC,CAAC;YACtB,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC;QACF,YAAY,EAAE,KAAK;QACnB,UAAU,EAAE;YACV,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,IAAI;SACd;QACD,iBAAiB,EAAE,EAAE;KACtB,CAAC,EAb0D,CAa1D,CAAC;CACJ,CAAC,EAf2C,CAe3C,CAAC,CAAC;AAhCJ;;;GAGG;AAEH,gDAA0B;AAC1B,gDAA4E;AAC5E,uEAAiD;AA2BjD,aAAa;AACb,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;AAEzB,QAAQ,CAAC,YAAY,EAAE;IACrB,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;QACpB,KAA2C,CAAC,SAAS,EAAE,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE;QACxC,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;QAEvB,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACpE,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC/D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC7E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4DAA4D,EAAE;;;;;oBACzD,YAAY,GAAG;wBACnB,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,OAAO,EAAE,4EAA4E;wCACrF,oBAAoB,EAAE,IAAI;qCAC3B,CAAC,EAAA;;6BAAA;wBACF,OAAO,EAAE;4BACP,GAAG,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;yBAC9B;qBACF,CAAC;oBAED,KAA2C,CAAC,iBAAiB,CAAC,YAAwB,CAAC,CAAC;oBAEzF,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;oBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;oBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;oBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC5E,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;oBAEH,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;oBACpF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;oBACjE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;oBAC/F,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,6BAA6B,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;oBAEhG,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,aAAa,EAAE;wBAChD,MAAM,EAAE,MAAM;wBACd,OAAO,EAAE;4BACP,cAAc,EAAE,kBAAkB;yBACnC;wBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,KAAK,EAAE,kBAAkB;4BACzB,QAAQ,EAAE,aAAa;yBACxB,CAAC;qBACH,CAAC,CAAC;;;;SACJ,CAAC,CAAC;IAEH,EAAE,CAAC,4BAA4B,EAAE;;;;;oBACzB,YAAY,GAAG;wBACnB,EAAE,EAAE,KAAK;wBACT,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,OAAO,EAAE,qBAAqB;qCAC/B,CAAC,EAAA;;6BAAA;wBACF,OAAO,EAAE;4BACP,GAAG,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;yBAC9B;qBACF,CAAC;oBAED,KAA2C,CAAC,iBAAiB,CAAC,YAAwB,CAAC,CAAC;oBAEzF,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;oBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,CAAC,CAAC;oBAC5E,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;oBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC9E,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;oBAEH,kEAAkE;oBAClE,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;oBACpE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;;;;SACjF,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;oBAEtC,cAAc,GAAG;wBACrB,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,OAAO,EAAE,4EAA4E;wCACrF,oBAAoB,EAAE,IAAI;qCAC3B,CAAC,EAAA;;6BAAA;wBACF,OAAO,EAAE;4BACP,GAAG,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;yBAC9B;qBACF,CAAC;oBAED,KAA2C,CAAC,qBAAqB,CAAC,cAA0B,CAAC,CAAC;oBAE/F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;oBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;oBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;oBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC5E,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;oBAGG,cAAc,GAAG;wBACrB,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,OAAO,EAAE,uCAAuC;qCACjD,CAAC,EAAA;;6BAAA;qBACH,CAAC;oBAED,KAA2C,CAAC,qBAAqB,CAAC,cAA0B,CAAC,CAAC;oBAEzF,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE,CAAC,CAAC;oBACxF,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBACxF,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;oBAEH,MAAM,CAAC,KAAK,CAAC,CAAC,wBAAwB,CAAC,+BAA+B,EAAE;wBACtE,MAAM,EAAE,MAAM;wBACd,OAAO,EAAE;4BACP,cAAc,EAAE,kBAAkB;yBACnC;wBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,KAAK,EAAE,kBAAkB;yBAC1B,CAAC;qBACH,CAAC,CAAC;;;;SACJ,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;oBAEtC,cAAc,GAAG;wBACrB,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,OAAO,EAAE,4EAA4E;wCACrF,oBAAoB,EAAE,IAAI;qCAC3B,CAAC,EAAA;;6BAAA;wBACF,OAAO,EAAE;4BACP,GAAG,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;yBAC9B;qBACF,CAAC;oBAED,KAA2C,CAAC,qBAAqB,CAAC,cAA0B,CAAC,CAAC;oBAE/F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;oBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;oBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;oBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC5E,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;oBAGG,cAAc,GAAG;wBACrB,EAAE,EAAE,KAAK;wBACT,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,KAAK,EAAE,0FAA0F;qCAClG,CAAC,EAAA;;6BAAA;qBACH,CAAC;oBAED,KAA2C,CAAC,qBAAqB,CAAC,cAA0B,CAAC,CAAC;oBAEzF,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE,CAAC,CAAC;oBACxF,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gDAAgD,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBACjG,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;;;;SACJ,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE;;;;;oBAEvC,cAAc,GAAG;wBACrB,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,OAAO,EAAE,4EAA4E;wCACrF,oBAAoB,EAAE,IAAI;qCAC3B,CAAC,EAAA;;6BAAA;wBACF,OAAO,EAAE;4BACP,GAAG,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;yBAC9B;qBACF,CAAC;oBAED,KAA2C,CAAC,qBAAqB,CAAC,cAA0B,CAAC,CAAC;oBAE/F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;oBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;oBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;oBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC5E,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;oBAGG,oBAAoB,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,6BAA6B,EAAE,CAAC,CAAC;oBACjG,iBAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;oBAEtC,4BAA4B;oBAC5B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACpE,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BAC/D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;wBAClF,CAAC,CAAC,EAAA;;oBALF,4BAA4B;oBAC5B,SAIE,CAAC;oBAEH,yBAAyB;oBACzB,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBAChE,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;;;;SAC5D,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;oBAEtC,OAAO,GAAG,IAAI,OAAO,CAAC,UAAC,OAAO;wBAClC,cAAc,GAAG,OAAO,CAAC;oBAC3B,CAAC,CAAC,CAAC;oBAEF,KAA2C,CAAC,eAAe,CAAC,OAA4B,CAAC,CAAC;oBAE3F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;oBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;oBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;oBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,4BAA4B;oBAC5B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;wBACxE,CAAC,CAAC,EAAA;;oBAHF,4BAA4B;oBAC5B,SAEE,CAAC;oBAEH,sBAAsB;oBACtB,cAAe,CAAC;wBACd,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE;;gCAAY,sBAAA,CAAC;wCACjB,OAAO,EAAE,4EAA4E;wCACrF,oBAAoB,EAAE,IAAI;qCAC3B,CAAC,EAAA;;6BAAA;wBACF,OAAO,EAAE;4BACP,GAAG,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;yBAC9B;qBACF,CAAC,CAAC;oBAEH,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAC5E,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;;;;SACJ,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;oBAC3C,KAA2C,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;oBAE3F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;oBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;oBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;oBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE9B,qBAAM,IAAA,eAAO,EAAC;4BACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBACvF,CAAC,CAAC,EAAA;;oBAFF,SAEE,CAAC;;;;SACJ,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE;QAC7C,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;QAEvB,IAAM,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAM,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEzD,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,EAAE,CAAC;QAClC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACnC,UAAU,GAAG,+BAA+B,CAAC;wBAC7C,aAAa,GAAG;4BACpB,EAAE,EAAE,KAAK;4BACT,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,UAAU;yCACpB,CAAC,EAAA;;iCAAA;yBACH,CAAC;wBAED,KAA2C,CAAC,iBAAiB,CAAC,aAAyB,CAAC,CAAC;wBAE1F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;wBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;wBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;wBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;wBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBAE9B,qBAAM,IAAA,eAAO,EAAC;gCACZ,0DAA0D;gCAC1D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;gCAC5G,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;4BACtD,CAAC,CAAC,EAAA;;wBAJF,SAIE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;;;gBAC3C,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;gBAEnD,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;gBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;gBAC3D,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;gBAE/D,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;;;aAC3C,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;;;;;wBAC9C,YAAY,GAAG,0BAA0B,CAAC;wBAC1C,eAAe,GAAG,mBAAmB,CAAC;wBAEtC,eAAe,GAAG;4BACtB,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,oEAAoE;4CAC7E,oBAAoB,EAAE,IAAI;yCAC3B,CAAC,EAAA;;iCAAA;yBACH,CAAC;wBAED,KAA2C,CAAC,iBAAiB,CAAC,eAA2B,CAAC,CAAC;wBAE5F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;wBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;wBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;wBAClE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC;wBACxE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBAE9B,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,MAAM,CAAC,gBAAgB,CAAC;oCAC7E,MAAM,EAAE,MAAM;oCACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wCACnB,KAAK,EAAE,YAAY;wCACnB,QAAQ,EAAE,eAAe;qCAC1B,CAAC;iCACH,CAAC,CAAC,CAAC;4BACN,CAAC,CAAC,EAAA;;wBARF,SAQE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACnC,eAAe,GAAG;4BACtB,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,0BAA0B;4CACnC,oBAAoB,EAAE,IAAI;yCAC3B,CAAC,EAAA;;iCAAA;yBACH,CAAC;wBAED,KAA2C,CAAC,kBAAkB,CAAC;4BAC9D,OAAA,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,eAA2B,CAAC,EAApC,CAAoC,EAAE,GAAG,CAAC,EAA3D,CAA2D,CAAC;wBAAnF,CAAmF,CACpF,CAAC;wBAEF,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;wBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;wBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;wBACxE,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;wBAEtE,eAAe;wBACf,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBAC9B,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBAC9B,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBAE9B,gEAAgE;wBAChE,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,KAAK,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;4BACzC,CAAC,CAAC,EAAA;;wBAHF,gEAAgE;wBAChE,SAEE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;YACtC,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;YAEvB,IAAM,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEzD,qBAAqB;YACrB,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAC9D,iBAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE9B,iDAAiD;YACjD,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE;YACjC,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;YAEvB,IAAM,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAE3D,qBAAqB;YACrB,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC;YACrE,iBAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3B,iDAAiD;YACjD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;YAEvB,IAAM,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC3D,IAAM,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACzD,IAAM,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;YAEtE,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;;;;;wBACvC,aAAa,GAAG;4BACpB,EAAE,EAAE,KAAK;4BACT,IAAI,EAAE;;oCAAY,sBAAA,CAAC;4CACjB,OAAO,EAAE,sBAAsB;yCAChC,CAAC,EAAA;;iCAAA;yBACH,CAAC;wBAED,KAA2C,CAAC,iBAAiB,CAAC,aAAyB,CAAC,CAAC;wBAE1F,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;wBAEjB,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBACrD,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBACnD,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;wBAEtE,iBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,CAAC,CAAC;wBAC5E,iBAAS,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;wBACtE,iBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBAE9B,qBAAM,IAAA,eAAO,EAAC;gCACZ,IAAM,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;gCAC/D,MAAM,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;4BACxD,CAAC,CAAC,EAAA;;wBAHF,SAGE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;YACvC,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;YAEvB,IAAM,UAAU,GAAG,cAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC3D,IAAM,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACzD,IAAM,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;YAEtE,kDAAkD;YAClD,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEhD,iBAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEnD,iBAAS,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAA,cAAM,EAAC,uBAAC,oBAAU,KAAG,CAAC,CAAC;YAEvB,sDAAsD;YACtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAClE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAEhE,kCAAkC;YAClC,IAAM,IAAI,GAAG,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/signup-form.test.tsx"], "sourcesContent": ["/**\n * Comprehensive SignupForm Component Tests\n * Tests registration UI, form validation, error handling, and user interactions\n */\n\nimport React from 'react';\nimport { render, screen, fireEvent, waitFor } from '@testing-library/react';\nimport SignupForm from '@/components/SignupForm';\n\n// Mock hooks\njest.mock('@/hooks/useCSRF', () => ({\n  useCSRF: jest.fn(() => ({\n    getHeaders: jest.fn(() => ({ 'X-CSRF-Token': 'test-token' })),\n    isLoading: false,\n  })),\n}));\n\njest.mock('@/hooks/useFormValidation', () => ({\n  useValidatedForm: jest.fn((initialData, rules, onSubmit) => ({\n    data: initialData,\n    updateField: jest.fn(),\n    handleSubmit: jest.fn((e) => {\n      e.preventDefault();\n      onSubmit(initialData, initialData);\n    }),\n    isSubmitting: false,\n    validation: {\n      errors: {},\n      isValid: true,\n    },\n    validationActions: {},\n  })),\n}));\n\n// Mock fetch\nglobal.fetch = jest.fn();\n\ndescribe('SignupForm', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n    (fetch as jest.MockedFunction<typeof fetch>).mockClear();\n  });\n\n  it('should render signup form correctly', () => {\n    render(<SignupForm />);\n    \n    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();\n    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();\n    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();\n    expect(screen.getByText(/terms of service/i)).toBeInTheDocument();\n    expect(screen.getByText(/privacy policy/i)).toBeInTheDocument();\n  });\n\n  it('should handle successful signup with verification required', async () => {\n    const mockResponse = {\n      ok: true,\n      json: async () => ({\n        message: 'User created successfully. Please check your email to verify your account.',\n        requiresVerification: true,\n      }),\n      headers: {\n        get: () => 'application/json',\n      },\n    };\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as Response);\n\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n    const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n    fireEvent.change(passwordInput, { target: { value: 'password123' } });\n    fireEvent.click(submitButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();\n    });\n\n    expect(screen.getByText(/we've sent a verification email to/i)).toBeInTheDocument();\n    expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();\n    expect(screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();\n    expect(screen.getByRole('button', { name: /register a different email/i })).toBeInTheDocument();\n\n    expect(fetch).toHaveBeenCalledWith('/api/signup', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        email: '<EMAIL>',\n        password: 'password123',\n      }),\n    });\n  });\n\n  it('should handle signup error', async () => {\n    const mockResponse = {\n      ok: false,\n      json: async () => ({\n        message: 'User already exists',\n      }),\n      headers: {\n        get: () => 'application/json',\n      },\n    };\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as Response);\n\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n    const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n    fireEvent.change(passwordInput, { target: { value: 'password123' } });\n    fireEvent.click(submitButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/error: user already exists/i)).toBeInTheDocument();\n    });\n\n    // Should still show the form, not the verification success screen\n    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();\n    expect(screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();\n  });\n\n  it('should handle resend verification email', async () => {\n    // First, simulate successful signup\n    const signupResponse = {\n      ok: true,\n      json: async () => ({\n        message: 'User created successfully. Please check your email to verify your account.',\n        requiresVerification: true,\n      }),\n      headers: {\n        get: () => 'application/json',\n      },\n    };\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as Response);\n\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n    const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n    fireEvent.change(passwordInput, { target: { value: 'password123' } });\n    fireEvent.click(submitButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();\n    });\n\n    // Now test resend verification\n    const resendResponse = {\n      ok: true,\n      json: async () => ({\n        message: 'Verification email sent successfully.',\n      }),\n    };\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(resendResponse as Response);\n\n    const resendButton = screen.getByRole('button', { name: /resend verification email/i });\n    fireEvent.click(resendButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/verification email sent successfully/i)).toBeInTheDocument();\n    });\n\n    expect(fetch).toHaveBeenLastCalledWith('/api/auth/resend-verification', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        email: '<EMAIL>',\n      }),\n    });\n  });\n\n  it('should handle resend verification error', async () => {\n    // First, simulate successful signup\n    const signupResponse = {\n      ok: true,\n      json: async () => ({\n        message: 'User created successfully. Please check your email to verify your account.',\n        requiresVerification: true,\n      }),\n      headers: {\n        get: () => 'application/json',\n      },\n    };\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as Response);\n\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n    const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n    fireEvent.change(passwordInput, { target: { value: 'password123' } });\n    fireEvent.click(submitButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();\n    });\n\n    // Mock resend error\n    const resendResponse = {\n      ok: false,\n      json: async () => ({\n        error: 'A verification email was recently sent. Please wait 5 minutes before requesting another.',\n      }),\n    };\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(resendResponse as Response);\n\n    const resendButton = screen.getByRole('button', { name: /resend verification email/i });\n    fireEvent.click(resendButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/error: a verification email was recently sent/i)).toBeInTheDocument();\n    });\n  });\n\n  it('should allow registering different email', async () => {\n    // First, simulate successful signup\n    const signupResponse = {\n      ok: true,\n      json: async () => ({\n        message: 'User created successfully. Please check your email to verify your account.',\n        requiresVerification: true,\n      }),\n      headers: {\n        get: () => 'application/json',\n      },\n    };\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as Response);\n\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n    const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n    fireEvent.change(passwordInput, { target: { value: 'password123' } });\n    fireEvent.click(submitButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();\n    });\n\n    // Click \"Register a different email\"\n    const differentEmailButton = screen.getByRole('button', { name: /register a different email/i });\n    fireEvent.click(differentEmailButton);\n\n    // Should return to the form\n    await waitFor(() => {\n      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();\n      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();\n      expect(screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();\n    });\n\n    // Form should be cleared\n    expect(screen.getByLabelText(/email address/i)).toHaveValue('');\n    expect(screen.getByLabelText(/password/i)).toHaveValue('');\n  });\n\n  it('should show loading state during signup', async () => {\n    let resolvePromise: (value: any) => void;\n    const promise = new Promise((resolve) => {\n      resolvePromise = resolve;\n    });\n\n    (fetch as jest.MockedFunction<typeof fetch>).mockReturnValue(promise as Promise<Response>);\n\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n    const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n    fireEvent.change(passwordInput, { target: { value: 'password123' } });\n    fireEvent.click(submitButton);\n\n    // Should show loading state\n    await waitFor(() => {\n      expect(screen.getByRole('status')).toHaveTextContent('Signing up...');\n    });\n\n    // Resolve the promise\n    resolvePromise!({\n      ok: true,\n      json: async () => ({\n        message: 'User created successfully. Please check your email to verify your account.',\n        requiresVerification: true,\n      }),\n      headers: {\n        get: () => 'application/json',\n      },\n    });\n\n    await waitFor(() => {\n      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();\n    });\n  });\n\n  it('should handle network errors gracefully', async () => {\n    (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValue(new Error('Network error'));\n\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n    const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n    fireEvent.change(passwordInput, { target: { value: 'password123' } });\n    fireEvent.click(submitButton);\n\n    await waitFor(() => {\n      expect(screen.getByText(/error: an unexpected error occurred/i)).toBeInTheDocument();\n    });\n  });\n\n  it('should require email and password fields', () => {\n    render(<SignupForm />);\n\n    const emailInput = screen.getByLabelText(/email address/i);\n    const passwordInput = screen.getByLabelText(/password/i);\n\n    expect(emailInput).toBeRequired();\n    expect(passwordInput).toBeRequired();\n  });\n\n  describe('Security and Edge Cases', () => {\n    it('should prevent XSS in error messages', async () => {\n      const xssPayload = '<script>alert(\"xss\")</script>';\n      const errorResponse = {\n        ok: false,\n        json: async () => ({\n          message: xssPayload,\n        }),\n      };\n\n      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(errorResponse as Response);\n\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n      const passwordInput = screen.getByLabelText(/password/i);\n      const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n      fireEvent.change(passwordInput, { target: { value: 'password123' } });\n      fireEvent.click(submitButton);\n\n      await waitFor(() => {\n        // Error should be displayed as text, not executed as HTML\n        expect(screen.getByText(new RegExp(xssPayload.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')))).toBeInTheDocument();\n        expect(document.querySelector('script')).toBeNull();\n      });\n    });\n\n    it('should handle extremely long email addresses', async () => {\n      const longEmail = 'a'.repeat(250) + '@example.com';\n\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n      fireEvent.change(emailInput, { target: { value: longEmail } });\n\n      expect(emailInput).toHaveValue(longEmail);\n    });\n\n    it('should handle special characters in credentials', async () => {\n      const specialEmail = '<EMAIL>';\n      const specialPassword = 'P@ssw0rd!#$%^&*()';\n\n      const successResponse = {\n        ok: true,\n        json: async () => ({\n          message: 'Registration successful! Please check your email for verification.',\n          requiresVerification: true,\n        }),\n      };\n\n      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(successResponse as Response);\n\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n      const passwordInput = screen.getByLabelText(/password/i);\n      const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n      fireEvent.change(emailInput, { target: { value: specialEmail } });\n      fireEvent.change(passwordInput, { target: { value: specialPassword } });\n      fireEvent.click(submitButton);\n\n      await waitFor(() => {\n        expect(fetch).toHaveBeenCalledWith('/api/auth/signup', expect.objectContaining({\n          method: 'POST',\n          body: JSON.stringify({\n            email: specialEmail,\n            password: specialPassword,\n          }),\n        }));\n      });\n    });\n\n    it('should handle rapid form submissions', async () => {\n      const successResponse = {\n        ok: true,\n        json: async () => ({\n          message: 'Registration successful!',\n          requiresVerification: true,\n        }),\n      };\n\n      (fetch as jest.MockedFunction<typeof fetch>).mockImplementation(() =>\n        new Promise(resolve => setTimeout(() => resolve(successResponse as Response), 100))\n      );\n\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n      const passwordInput = screen.getByLabelText(/password/i);\n      const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n      fireEvent.change(passwordInput, { target: { value: 'password123' } });\n\n      // Rapid clicks\n      fireEvent.click(submitButton);\n      fireEvent.click(submitButton);\n      fireEvent.click(submitButton);\n\n      // Should only call fetch once due to form submission protection\n      await waitFor(() => {\n        expect(fetch).toHaveBeenCalledTimes(1);\n      });\n    });\n\n    it('should validate password strength', () => {\n      render(<SignupForm />);\n\n      const passwordInput = screen.getByLabelText(/password/i);\n\n      // Test weak password\n      fireEvent.change(passwordInput, { target: { value: '123' } });\n      fireEvent.blur(passwordInput);\n\n      // Should show validation error for weak password\n      expect(passwordInput).toHaveValue('123');\n    });\n\n    it('should validate email format', () => {\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n\n      // Test invalid email\n      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });\n      fireEvent.blur(emailInput);\n\n      // Should show validation error for invalid email\n      expect(emailInput).toHaveValue('invalid-email');\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('should have proper ARIA labels and roles', () => {\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n      const passwordInput = screen.getByLabelText(/password/i);\n      const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n      expect(emailInput).toHaveAttribute('aria-required', 'true');\n      expect(passwordInput).toHaveAttribute('aria-required', 'true');\n      expect(submitButton).toHaveAttribute('type', 'submit');\n    });\n\n    it('should announce errors to screen readers', async () => {\n      const errorResponse = {\n        ok: false,\n        json: async () => ({\n          message: 'Email already exists',\n        }),\n      };\n\n      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(errorResponse as Response);\n\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n      const passwordInput = screen.getByLabelText(/password/i);\n      const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });\n      fireEvent.change(passwordInput, { target: { value: 'password123' } });\n      fireEvent.click(submitButton);\n\n      await waitFor(() => {\n        const errorElement = screen.getByText(/email already exists/i);\n        expect(errorElement).toHaveAttribute('role', 'alert');\n      });\n    });\n\n    it('should support keyboard navigation', () => {\n      render(<SignupForm />);\n\n      const emailInput = screen.getByLabelText(/email address/i);\n      const passwordInput = screen.getByLabelText(/password/i);\n      const submitButton = screen.getByRole('button', { name: /sign up/i });\n\n      // Tab order should be email -> password -> submit\n      emailInput.focus();\n      expect(document.activeElement).toBe(emailInput);\n\n      fireEvent.keyDown(emailInput, { key: 'Tab' });\n      expect(document.activeElement).toBe(passwordInput);\n\n      fireEvent.keyDown(passwordInput, { key: 'Tab' });\n      expect(document.activeElement).toBe(submitButton);\n    });\n\n    it('should have proper form labels and descriptions', () => {\n      render(<SignupForm />);\n\n      // Check for terms of service and privacy policy links\n      expect(screen.getByText(/terms of service/i)).toBeInTheDocument();\n      expect(screen.getByText(/privacy policy/i)).toBeInTheDocument();\n\n      // Check for proper form structure\n      const form = screen.getByRole('form');\n      expect(form).toBeInTheDocument();\n    });\n  });\n});\n"], "version": 3}