9b3d55a752f9ecb36ea712b437140d6c
"use strict";

/* istanbul ignore next */
function cov_1c1s12ussx() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/badge.tsx";
  var hash = "005019fd05b71db68a4dbe9b748f15141076815f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/badge.tsx",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 22
        },
        end: {
          line: 23,
          column: 3
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 33
        }
      },
      "13": {
        start: {
          line: 14,
          column: 26
        },
        end: {
          line: 14,
          column: 33
        }
      },
      "14": {
        start: {
          line: 15,
          column: 15
        },
        end: {
          line: 15,
          column: 52
        }
      },
      "15": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 18,
          column: 5
        }
      },
      "16": {
        start: {
          line: 17,
          column: 6
        },
        end: {
          line: 17,
          column: 68
        }
      },
      "17": {
        start: {
          line: 17,
          column: 51
        },
        end: {
          line: 17,
          column: 63
        }
      },
      "18": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 19,
          column: 39
        }
      },
      "19": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 33
        }
      },
      "20": {
        start: {
          line: 21,
          column: 26
        },
        end: {
          line: 21,
          column: 33
        }
      },
      "21": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 17
        }
      },
      "22": {
        start: {
          line: 24,
          column: 25
        },
        end: {
          line: 28,
          column: 2
        }
      },
      "23": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 72
        }
      },
      "24": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 21
        }
      },
      "25": {
        start: {
          line: 29,
          column: 19
        },
        end: {
          line: 45,
          column: 4
        }
      },
      "26": {
        start: {
          line: 30,
          column: 18
        },
        end: {
          line: 37,
          column: 5
        }
      },
      "27": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 35,
          column: 10
        }
      },
      "28": {
        start: {
          line: 32,
          column: 21
        },
        end: {
          line: 32,
          column: 23
        }
      },
      "29": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 95
        }
      },
      "30": {
        start: {
          line: 33,
          column: 29
        },
        end: {
          line: 33,
          column: 95
        }
      },
      "31": {
        start: {
          line: 33,
          column: 77
        },
        end: {
          line: 33,
          column: 95
        }
      },
      "32": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 22
        }
      },
      "33": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 36,
          column: 26
        }
      },
      "34": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 44,
          column: 6
        }
      },
      "35": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 46
        }
      },
      "36": {
        start: {
          line: 39,
          column: 35
        },
        end: {
          line: 39,
          column: 46
        }
      },
      "37": {
        start: {
          line: 40,
          column: 21
        },
        end: {
          line: 40,
          column: 23
        }
      },
      "38": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 137
        }
      },
      "39": {
        start: {
          line: 41,
          column: 25
        },
        end: {
          line: 41,
          column: 137
        }
      },
      "40": {
        start: {
          line: 41,
          column: 38
        },
        end: {
          line: 41,
          column: 50
        }
      },
      "41": {
        start: {
          line: 41,
          column: 56
        },
        end: {
          line: 41,
          column: 57
        }
      },
      "42": {
        start: {
          line: 41,
          column: 78
        },
        end: {
          line: 41,
          column: 137
        }
      },
      "43": {
        start: {
          line: 41,
          column: 102
        },
        end: {
          line: 41,
          column: 137
        }
      },
      "44": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 40
        }
      },
      "45": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 22
        }
      },
      "46": {
        start: {
          line: 46,
          column: 13
        },
        end: {
          line: 56,
          column: 1
        }
      },
      "47": {
        start: {
          line: 47,
          column: 12
        },
        end: {
          line: 47,
          column: 14
        }
      },
      "48": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 49,
          column: 20
        }
      },
      "49": {
        start: {
          line: 48,
          column: 21
        },
        end: {
          line: 49,
          column: 20
        }
      },
      "50": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 20
        }
      },
      "51": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 54,
          column: 9
        }
      },
      "52": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 54,
          column: 9
        }
      },
      "53": {
        start: {
          line: 51,
          column: 21
        },
        end: {
          line: 51,
          column: 22
        }
      },
      "54": {
        start: {
          line: 51,
          column: 28
        },
        end: {
          line: 51,
          column: 59
        }
      },
      "55": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 53,
          column: 34
        }
      },
      "56": {
        start: {
          line: 53,
          column: 16
        },
        end: {
          line: 53,
          column: 34
        }
      },
      "57": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 13
        }
      },
      "58": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 62
        }
      },
      "59": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 31
        }
      },
      "60": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 59,
          column: 22
        }
      },
      "61": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 48
        }
      },
      "62": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 42
        }
      },
      "63": {
        start: {
          line: 62,
          column: 19
        },
        end: {
          line: 62,
          column: 50
        }
      },
      "64": {
        start: {
          line: 63,
          column: 33
        },
        end: {
          line: 63,
          column: 68
        }
      },
      "65": {
        start: {
          line: 64,
          column: 14
        },
        end: {
          line: 64,
          column: 36
        }
      },
      "66": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 77,
          column: 2
        }
      },
      "67": {
        start: {
          line: 78,
          column: 0
        },
        end: {
          line: 78,
          column: 38
        }
      },
      "68": {
        start: {
          line: 80,
          column: 20
        },
        end: {
          line: 80,
          column: 32
        }
      },
      "69": {
        start: {
          line: 80,
          column: 44
        },
        end: {
          line: 80,
          column: 54
        }
      },
      "70": {
        start: {
          line: 80,
          column: 61
        },
        end: {
          line: 80,
          column: 71
        }
      },
      "71": {
        start: {
          line: 80,
          column: 83
        },
        end: {
          line: 80,
          column: 109
        }
      },
      "72": {
        start: {
          line: 80,
          column: 119
        },
        end: {
          line: 80,
          column: 166
        }
      },
      "73": {
        start: {
          line: 81,
          column: 15
        },
        end: {
          line: 81,
          column: 51
        }
      },
      "74": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 161
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 75
          }
        },
        loc: {
          start: {
            line: 13,
            column: 96
          },
          end: {
            line: 20,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 39
          }
        },
        loc: {
          start: {
            line: 17,
            column: 49
          },
          end: {
            line: 17,
            column: 65
          }
        },
        line: 17
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 20,
            column: 6
          },
          end: {
            line: 20,
            column: 7
          }
        },
        loc: {
          start: {
            line: 20,
            column: 28
          },
          end: {
            line: 23,
            column: 1
          }
        },
        line: 20
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 24,
            column: 80
          },
          end: {
            line: 24,
            column: 81
          }
        },
        loc: {
          start: {
            line: 24,
            column: 95
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 24
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 26,
            column: 5
          },
          end: {
            line: 26,
            column: 6
          }
        },
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 28,
            column: 1
          }
        },
        line: 26
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 29,
            column: 51
          },
          end: {
            line: 29,
            column: 52
          }
        },
        loc: {
          start: {
            line: 29,
            column: 63
          },
          end: {
            line: 45,
            column: 1
          }
        },
        line: 29
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 30,
            column: 18
          },
          end: {
            line: 30,
            column: 19
          }
        },
        loc: {
          start: {
            line: 30,
            column: 30
          },
          end: {
            line: 37,
            column: 5
          }
        },
        line: 30
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 31,
            column: 48
          },
          end: {
            line: 31,
            column: 49
          }
        },
        loc: {
          start: {
            line: 31,
            column: 61
          },
          end: {
            line: 35,
            column: 9
          }
        },
        line: 31
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 38,
            column: 11
          },
          end: {
            line: 38,
            column: 12
          }
        },
        loc: {
          start: {
            line: 38,
            column: 26
          },
          end: {
            line: 44,
            column: 5
          }
        },
        line: 38
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 46,
            column: 38
          },
          end: {
            line: 46,
            column: 39
          }
        },
        loc: {
          start: {
            line: 46,
            column: 54
          },
          end: {
            line: 56,
            column: 1
          }
        },
        line: 46
      },
      "12": {
        name: "Badge",
        decl: {
          start: {
            line: 79,
            column: 9
          },
          end: {
            line: 79,
            column: 14
          }
        },
        loc: {
          start: {
            line: 79,
            column: 19
          },
          end: {
            line: 83,
            column: 1
          }
        },
        line: 79
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 22
          },
          end: {
            line: 23,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 23
          },
          end: {
            line: 13,
            column: 27
          }
        }, {
          start: {
            line: 13,
            column: 31
          },
          end: {
            line: 13,
            column: 51
          }
        }, {
          start: {
            line: 13,
            column: 57
          },
          end: {
            line: 23,
            column: 2
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 13,
            column: 57
          },
          end: {
            line: 23,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 20,
            column: 1
          }
        }, {
          start: {
            line: 20,
            column: 6
          },
          end: {
            line: 23,
            column: 1
          }
        }],
        line: 13
      },
      "5": {
        loc: {
          start: {
            line: 14,
            column: 4
          },
          end: {
            line: 14,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 14,
            column: 4
          },
          end: {
            line: 14,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 14
      },
      "6": {
        loc: {
          start: {
            line: 16,
            column: 4
          },
          end: {
            line: 18,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 4
          },
          end: {
            line: 18,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "7": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 13
          }
        }, {
          start: {
            line: 16,
            column: 18
          },
          end: {
            line: 16,
            column: 84
          }
        }],
        line: 16
      },
      "8": {
        loc: {
          start: {
            line: 16,
            column: 18
          },
          end: {
            line: 16,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 47
          }
        }, {
          start: {
            line: 16,
            column: 50
          },
          end: {
            line: 16,
            column: 84
          }
        }],
        line: 16
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 50
          },
          end: {
            line: 16,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 16,
            column: 50
          },
          end: {
            line: 16,
            column: 63
          }
        }, {
          start: {
            line: 16,
            column: 67
          },
          end: {
            line: 16,
            column: 84
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 21,
            column: 4
          },
          end: {
            line: 21,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 21,
            column: 4
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 21
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 25
          },
          end: {
            line: 28,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 26
          },
          end: {
            line: 24,
            column: 30
          }
        }, {
          start: {
            line: 24,
            column: 34
          },
          end: {
            line: 24,
            column: 57
          }
        }, {
          start: {
            line: 24,
            column: 63
          },
          end: {
            line: 28,
            column: 1
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 24,
            column: 63
          },
          end: {
            line: 28,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 24,
            column: 80
          },
          end: {
            line: 26,
            column: 1
          }
        }, {
          start: {
            line: 26,
            column: 5
          },
          end: {
            line: 28,
            column: 1
          }
        }],
        line: 24
      },
      "13": {
        loc: {
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 45,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 28
          },
          end: {
            line: 29,
            column: 45
          }
        }, {
          start: {
            line: 29,
            column: 50
          },
          end: {
            line: 45,
            column: 4
          }
        }],
        line: 29
      },
      "14": {
        loc: {
          start: {
            line: 31,
            column: 18
          },
          end: {
            line: 35,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 18
          },
          end: {
            line: 31,
            column: 44
          }
        }, {
          start: {
            line: 31,
            column: 48
          },
          end: {
            line: 35,
            column: 9
          }
        }],
        line: 31
      },
      "15": {
        loc: {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "16": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "17": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "18": {
        loc: {
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 41,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 41,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "19": {
        loc: {
          start: {
            line: 41,
            column: 78
          },
          end: {
            line: 41,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 78
          },
          end: {
            line: 41,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "20": {
        loc: {
          start: {
            line: 46,
            column: 13
          },
          end: {
            line: 56,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 14
          },
          end: {
            line: 46,
            column: 18
          }
        }, {
          start: {
            line: 46,
            column: 22
          },
          end: {
            line: 46,
            column: 33
          }
        }, {
          start: {
            line: 46,
            column: 38
          },
          end: {
            line: 56,
            column: 1
          }
        }],
        line: 46
      },
      "21": {
        loc: {
          start: {
            line: 48,
            column: 21
          },
          end: {
            line: 49,
            column: 20
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 21
          },
          end: {
            line: 49,
            column: 20
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "22": {
        loc: {
          start: {
            line: 48,
            column: 25
          },
          end: {
            line: 48,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 25
          },
          end: {
            line: 48,
            column: 67
          }
        }, {
          start: {
            line: 48,
            column: 71
          },
          end: {
            line: 48,
            column: 87
          }
        }],
        line: 48
      },
      "23": {
        loc: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 54,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 54,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "24": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 17
          }
        }, {
          start: {
            line: 50,
            column: 21
          },
          end: {
            line: 50,
            column: 71
          }
        }],
        line: 50
      },
      "25": {
        loc: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 53,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 53,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "26": {
        loc: {
          start: {
            line: 52,
            column: 16
          },
          end: {
            line: 52,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 16
          },
          end: {
            line: 52,
            column: 35
          }
        }, {
          start: {
            line: 52,
            column: 39
          },
          end: {
            line: 52,
            column: 90
          }
        }],
        line: 52
      },
      "27": {
        loc: {
          start: {
            line: 80,
            column: 83
          },
          end: {
            line: 80,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 99
          },
          end: {
            line: 80,
            column: 104
          }
        }, {
          start: {
            line: 80,
            column: 107
          },
          end: {
            line: 80,
            column: 109
          }
        }],
        line: 80
      },
      "28": {
        loc: {
          start: {
            line: 81,
            column: 15
          },
          end: {
            line: 81,
            column: 51
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 81,
            column: 25
          },
          end: {
            line: 81,
            column: 42
          }
        }, {
          start: {
            line: 81,
            column: 45
          },
          end: {
            line: 81,
            column: 51
          }
        }],
        line: 81
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/badge.tsx",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CS,sBAAK;;AA/Cd,2CAA8B;AAC9B,mDAA2C;AAC3C,qEAAiE;AAEjE,qCAAgC;AAIhC,IAAM,aAAa,GAAG,IAAA,8BAAG,EACvB,gZAAgZ,EAChZ;IACE,QAAQ,EAAE;QACR,OAAO,EAAE;YACP,OAAO,EACL,gFAAgF;YAClF,SAAS,EACP,sFAAsF;YACxF,WAAW,EACT,2KAA2K;YAC7K,OAAO,EACL,wEAAwE;SAC3E;KACF;IACD,eAAe,EAAE;QACf,OAAO,EAAE,SAAS;KACnB;CACF,CACF,CAAA;AAoBe,sCAAa;AAlB7B,SAAS,KAAK,CAAC,EAM6C;IAL1D,IAAA,SAAS,eAAA,EACT,OAAO,aAAA,EACP,eAAe,EAAf,OAAO,mBAAG,KAAK,KAAA,EACZ,KAAK,cAJK,mCAKd,CADS;IAGR,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,iBAAI,CAAC,CAAC,CAAC,MAAM,CAAA;IAEpC,OAAO,CACL,uBAAC,IAAI,0BACO,OAAO,EACjB,SAAS,EAAE,IAAA,UAAE,EAAC,aAAa,CAAC,EAAE,OAAO,SAAA,EAAE,CAAC,EAAE,SAAS,CAAC,IAChD,KAAK,EACT,CACH,CAAA;AACH,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/badge.tsx"],
      sourcesContent: ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\n\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "005019fd05b71db68a4dbe9b748f15141076815f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1c1s12ussx = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1c1s12ussx();
var __assign =
/* istanbul ignore next */
(cov_1c1s12ussx().s[0]++,
/* istanbul ignore next */
(cov_1c1s12ussx().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1c1s12ussx().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1c1s12ussx().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[0]++;
  cov_1c1s12ussx().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1c1s12ussx().f[1]++;
    cov_1c1s12ussx().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1c1s12ussx().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1c1s12ussx().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1c1s12ussx().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1c1s12ussx().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1c1s12ussx().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1c1s12ussx().b[2][0]++;
          cov_1c1s12ussx().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1c1s12ussx().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1c1s12ussx().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1c1s12ussx().s[10]++;
  return __assign.apply(this, arguments);
}));
var __createBinding =
/* istanbul ignore next */
(cov_1c1s12ussx().s[11]++,
/* istanbul ignore next */
(cov_1c1s12ussx().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1c1s12ussx().b[3][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_1c1s12ussx().b[3][2]++, Object.create ?
/* istanbul ignore next */
(cov_1c1s12ussx().b[4][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[2]++;
  cov_1c1s12ussx().s[12]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_1c1s12ussx().b[5][0]++;
    cov_1c1s12ussx().s[13]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_1c1s12ussx().b[5][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_1c1s12ussx().s[14]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_1c1s12ussx().s[15]++;
  if (
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[7][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[7][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[8][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[8][1]++,
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[9][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[9][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_1c1s12ussx().b[6][0]++;
    cov_1c1s12ussx().s[16]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_1c1s12ussx().f[3]++;
        cov_1c1s12ussx().s[17]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_1c1s12ussx().b[6][1]++;
  }
  cov_1c1s12ussx().s[18]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_1c1s12ussx().b[4][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[4]++;
  cov_1c1s12ussx().s[19]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_1c1s12ussx().b[10][0]++;
    cov_1c1s12ussx().s[20]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_1c1s12ussx().b[10][1]++;
  }
  cov_1c1s12ussx().s[21]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_1c1s12ussx().s[22]++,
/* istanbul ignore next */
(cov_1c1s12ussx().b[11][0]++, this) &&
/* istanbul ignore next */
(cov_1c1s12ussx().b[11][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_1c1s12ussx().b[11][2]++, Object.create ?
/* istanbul ignore next */
(cov_1c1s12ussx().b[12][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[5]++;
  cov_1c1s12ussx().s[23]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_1c1s12ussx().b[12][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[6]++;
  cov_1c1s12ussx().s[24]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_1c1s12ussx().s[25]++,
/* istanbul ignore next */
(cov_1c1s12ussx().b[13][0]++, this) &&
/* istanbul ignore next */
(cov_1c1s12ussx().b[13][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_1c1s12ussx().b[13][2]++, function () {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[7]++;
  cov_1c1s12ussx().s[26]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_1c1s12ussx().f[8]++;
    cov_1c1s12ussx().s[27]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[14][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[14][1]++, function (o) {
      /* istanbul ignore next */
      cov_1c1s12ussx().f[9]++;
      var ar =
      /* istanbul ignore next */
      (cov_1c1s12ussx().s[28]++, []);
      /* istanbul ignore next */
      cov_1c1s12ussx().s[29]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_1c1s12ussx().s[30]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_1c1s12ussx().b[15][0]++;
          cov_1c1s12ussx().s[31]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_1c1s12ussx().b[15][1]++;
        }
      }
      /* istanbul ignore next */
      cov_1c1s12ussx().s[32]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_1c1s12ussx().s[33]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_1c1s12ussx().s[34]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_1c1s12ussx().f[10]++;
    cov_1c1s12ussx().s[35]++;
    if (
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[17][0]++, mod) &&
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[17][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_1c1s12ussx().b[16][0]++;
      cov_1c1s12ussx().s[36]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_1c1s12ussx().b[16][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_1c1s12ussx().s[37]++, {});
    /* istanbul ignore next */
    cov_1c1s12ussx().s[38]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_1c1s12ussx().b[18][0]++;
      cov_1c1s12ussx().s[39]++;
      for (var k =
        /* istanbul ignore next */
        (cov_1c1s12ussx().s[40]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_1c1s12ussx().s[41]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_1c1s12ussx().s[42]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_1c1s12ussx().b[19][0]++;
          cov_1c1s12ussx().s[43]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_1c1s12ussx().b[19][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_1c1s12ussx().b[18][1]++;
    }
    cov_1c1s12ussx().s[44]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_1c1s12ussx().s[45]++;
    return result;
  };
}()));
var __rest =
/* istanbul ignore next */
(cov_1c1s12ussx().s[46]++,
/* istanbul ignore next */
(cov_1c1s12ussx().b[20][0]++, this) &&
/* istanbul ignore next */
(cov_1c1s12ussx().b[20][1]++, this.__rest) ||
/* istanbul ignore next */
(cov_1c1s12ussx().b[20][2]++, function (s, e) {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[11]++;
  var t =
  /* istanbul ignore next */
  (cov_1c1s12ussx().s[47]++, {});
  /* istanbul ignore next */
  cov_1c1s12ussx().s[48]++;
  for (var p in s) {
    /* istanbul ignore next */
    cov_1c1s12ussx().s[49]++;
    if (
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[22][0]++, Object.prototype.hasOwnProperty.call(s, p)) &&
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[22][1]++, e.indexOf(p) < 0)) {
      /* istanbul ignore next */
      cov_1c1s12ussx().b[21][0]++;
      cov_1c1s12ussx().s[50]++;
      t[p] = s[p];
    } else
    /* istanbul ignore next */
    {
      cov_1c1s12ussx().b[21][1]++;
    }
  }
  /* istanbul ignore next */
  cov_1c1s12ussx().s[51]++;
  if (
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[24][0]++, s != null) &&
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[24][1]++, typeof Object.getOwnPropertySymbols === "function")) {
    /* istanbul ignore next */
    cov_1c1s12ussx().b[23][0]++;
    cov_1c1s12ussx().s[52]++;
    for (var i =
      /* istanbul ignore next */
      (cov_1c1s12ussx().s[53]++, 0), p =
      /* istanbul ignore next */
      (cov_1c1s12ussx().s[54]++, Object.getOwnPropertySymbols(s)); i < p.length; i++) {
      /* istanbul ignore next */
      cov_1c1s12ussx().s[55]++;
      if (
      /* istanbul ignore next */
      (cov_1c1s12ussx().b[26][0]++, e.indexOf(p[i]) < 0) &&
      /* istanbul ignore next */
      (cov_1c1s12ussx().b[26][1]++, Object.prototype.propertyIsEnumerable.call(s, p[i]))) {
        /* istanbul ignore next */
        cov_1c1s12ussx().b[25][0]++;
        cov_1c1s12ussx().s[56]++;
        t[p[i]] = s[p[i]];
      } else
      /* istanbul ignore next */
      {
        cov_1c1s12ussx().b[25][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_1c1s12ussx().b[23][1]++;
  }
  cov_1c1s12ussx().s[57]++;
  return t;
}));
/* istanbul ignore next */
cov_1c1s12ussx().s[58]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1c1s12ussx().s[59]++;
exports.badgeVariants = void 0;
/* istanbul ignore next */
cov_1c1s12ussx().s[60]++;
exports.Badge = Badge;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_1c1s12ussx().s[61]++, require("react/jsx-runtime"));
var React =
/* istanbul ignore next */
(cov_1c1s12ussx().s[62]++, __importStar(require("react")));
var react_slot_1 =
/* istanbul ignore next */
(cov_1c1s12ussx().s[63]++, require("@radix-ui/react-slot"));
var class_variance_authority_1 =
/* istanbul ignore next */
(cov_1c1s12ussx().s[64]++, require("class-variance-authority"));
var utils_1 =
/* istanbul ignore next */
(cov_1c1s12ussx().s[65]++, require("@/lib/utils"));
var badgeVariants =
/* istanbul ignore next */
(cov_1c1s12ussx().s[66]++, (0, class_variance_authority_1.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden", {
  variants: {
    variant: {
      default: "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",
      secondary: "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
      destructive: "border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
      outline: "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"
    }
  },
  defaultVariants: {
    variant: "default"
  }
}));
/* istanbul ignore next */
cov_1c1s12ussx().s[67]++;
exports.badgeVariants = badgeVariants;
function Badge(_a) {
  /* istanbul ignore next */
  cov_1c1s12ussx().f[12]++;
  var className =
    /* istanbul ignore next */
    (cov_1c1s12ussx().s[68]++, _a.className),
    variant =
    /* istanbul ignore next */
    (cov_1c1s12ussx().s[69]++, _a.variant),
    _b =
    /* istanbul ignore next */
    (cov_1c1s12ussx().s[70]++, _a.asChild),
    asChild =
    /* istanbul ignore next */
    (cov_1c1s12ussx().s[71]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[27][0]++, false) :
    /* istanbul ignore next */
    (cov_1c1s12ussx().b[27][1]++, _b)),
    props =
    /* istanbul ignore next */
    (cov_1c1s12ussx().s[72]++, __rest(_a, ["className", "variant", "asChild"]));
  var Comp =
  /* istanbul ignore next */
  (cov_1c1s12ussx().s[73]++, asChild ?
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[28][0]++, react_slot_1.Slot) :
  /* istanbul ignore next */
  (cov_1c1s12ussx().b[28][1]++, "span"));
  /* istanbul ignore next */
  cov_1c1s12ussx().s[74]++;
  return (0, jsx_runtime_1.jsx)(Comp, __assign({
    "data-slot": "badge",
    className: (0, utils_1.cn)(badgeVariants({
      variant: variant
    }), className)
  }, props));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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