{"version": 3, "names": ["exports", "Badge", "React", "cov_1c1s12ussx", "s", "__importStar", "require", "react_slot_1", "class_variance_authority_1", "utils_1", "badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "_a", "f", "className", "_b", "<PERSON><PERSON><PERSON><PERSON>", "b", "props", "__rest", "Comp", "Slot", "jsx_runtime_1", "jsx", "__assign", "cn"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\n\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CSA,OAAA,CAAAC,KAAA,GAAAA,KAAA;;;;AA/CT,IAAAC,KAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,YAAA,CAAAC,OAAA;AACA,IAAAC,YAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAE,OAAA;AACA,IAAAE,0BAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,QAAAE,OAAA;AAEA,IAAAG,OAAA;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,QAAAE,OAAA;AAIA,IAAMI,aAAa;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,QAAG,IAAAI,0BAAA,CAAAG,GAAG,EACvB,gZAAgZ,EAChZ;EACEC,QAAQ,EAAE;IACRC,OAAO,EAAE;MACPC,OAAO,EACL,gFAAgF;MAClFC,SAAS,EACP,sFAAsF;MACxFC,WAAW,EACT,2KAA2K;MAC7KC,OAAO,EACL;;GAEL;EACDC,eAAe,EAAE;IACfL,OAAO,EAAE;;CAEZ,CACF;AAAA;AAAAV,cAAA,GAAAC,CAAA;AAoBeJ,OAAA,CAAAU,aAAA,GAAAA,aAAA;AAlBhB,SAAST,KAAKA,CAACkB,EAM6C;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAL1D,IAAAC,SAAS;IAAA;IAAA,CAAAlB,cAAA,GAAAC,CAAA,QAAAe,EAAA,CAAAE,SAAA;IACTR,OAAO;IAAA;IAAA,CAAAV,cAAA,GAAAC,CAAA,QAAAe,EAAA,CAAAN,OAAA;IACPS,EAAA;IAAA;IAAA,CAAAnB,cAAA,GAAAC,CAAA,QAAAe,EAAA,CAAAI,OAAe;IAAfA,OAAO;IAAA;IAAA,CAAApB,cAAA,GAAAC,CAAA,QAAAkB,EAAA;IAAA;IAAA,CAAAnB,cAAA,GAAAqB,CAAA,WAAG,KAAK;IAAA;IAAA,CAAArB,cAAA,GAAAqB,CAAA,WAAAF,EAAA;IACZG,KAAK;IAAA;IAAA,CAAAtB,cAAA,GAAAC,CAAA,QAAAsB,MAAA,CAAAP,EAAA,EAJK,mCAKd,CADS;EAGR,IAAMQ,IAAI;EAAA;EAAA,CAAAxB,cAAA,GAAAC,CAAA,QAAGmB,OAAO;EAAA;EAAA,CAAApB,cAAA,GAAAqB,CAAA,WAAGjB,YAAA,CAAAqB,IAAI;EAAA;EAAA,CAAAzB,cAAA,GAAAqB,CAAA,WAAG,MAAM;EAAA;EAAArB,cAAA,GAAAC,CAAA;EAEpC,OACE,IAAAyB,aAAA,CAAAC,GAAA,EAACH,IAAI,EAAAI,QAAA;IAAA,aACO,OAAO;IACjBV,SAAS,EAAE,IAAAZ,OAAA,CAAAuB,EAAE,EAACtB,aAAa,CAAC;MAAEG,OAAO,EAAAA;IAAA,CAAE,CAAC,EAAEQ,SAAS;EAAC,GAChDI,KAAK,EACT;AAEN", "ignoreList": []}