fddaefcc944eb22c2685d6f5b84428e1
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock hooks
jest.mock('@/hooks/useCSRF', function () { return ({
    useCSRF: jest.fn(function () { return ({
        getHeaders: jest.fn(function () { return ({ 'X-CSRF-Token': 'test-token' }); }),
        isLoading: false,
    }); }),
}); });
jest.mock('@/hooks/useFormValidation', function () { return ({
    useValidatedForm: jest.fn(function (initialData, rules, onSubmit) { return ({
        data: initialData,
        updateField: jest.fn(),
        handleSubmit: jest.fn(function (e) {
            e.preventDefault();
            onSubmit(initialData, initialData);
        }),
        isSubmitting: false,
        validation: {
            errors: {},
            isValid: true,
        },
        validationActions: {},
    }); }),
}); });
/**
 * Comprehensive SignupForm Component Tests
 * Tests registration UI, form validation, error handling, and user interactions
 */
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
var SignupForm_1 = __importDefault(require("@/components/SignupForm"));
// Mock fetch
global.fetch = jest.fn();
describe('SignupForm', function () {
    beforeEach(function () {
        jest.clearAllMocks();
        fetch.mockClear();
    });
    it('should render signup form correctly', function () {
        (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
        expect(react_2.screen.getByLabelText(/email address/i)).toBeInTheDocument();
        expect(react_2.screen.getByLabelText(/password/i)).toBeInTheDocument();
        expect(react_2.screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
        expect(react_2.screen.getByText(/terms of service/i)).toBeInTheDocument();
        expect(react_2.screen.getByText(/privacy policy/i)).toBeInTheDocument();
    });
    it('should handle successful signup with verification required', function () { return __awaiter(void 0, void 0, void 0, function () {
        var mockResponse, emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mockResponse = {
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'User created successfully. Please check your email to verify your account.',
                                        requiresVerification: true,
                                    })];
                            });
                        }); },
                        headers: {
                            get: function () { return 'application/json'; },
                        },
                    };
                    fetch.mockResolvedValue(mockResponse);
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/registration successful!/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    expect(react_2.screen.getByText(/we've sent a verification email to/i)).toBeInTheDocument();
                    expect(react_2.screen.getByText(/<EMAIL>/)).toBeInTheDocument();
                    expect(react_2.screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
                    expect(react_2.screen.getByRole('button', { name: /register a different email/i })).toBeInTheDocument();
                    expect(fetch).toHaveBeenCalledWith('/api/signup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'password123',
                        }),
                    });
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle signup error', function () { return __awaiter(void 0, void 0, void 0, function () {
        var mockResponse, emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mockResponse = {
                        ok: false,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'User already exists',
                                    })];
                            });
                        }); },
                        headers: {
                            get: function () { return 'application/json'; },
                        },
                    };
                    fetch.mockResolvedValue(mockResponse);
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/error: user already exists/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    // Should still show the form, not the verification success screen
                    expect(react_2.screen.getByLabelText(/email address/i)).toBeInTheDocument();
                    expect(react_2.screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle resend verification email', function () { return __awaiter(void 0, void 0, void 0, function () {
        var signupResponse, emailInput, passwordInput, submitButton, resendResponse, resendButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    signupResponse = {
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'User created successfully. Please check your email to verify your account.',
                                        requiresVerification: true,
                                    })];
                            });
                        }); },
                        headers: {
                            get: function () { return 'application/json'; },
                        },
                    };
                    fetch.mockResolvedValueOnce(signupResponse);
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/registration successful!/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    resendResponse = {
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'Verification email sent successfully.',
                                    })];
                            });
                        }); },
                    };
                    fetch.mockResolvedValueOnce(resendResponse);
                    resendButton = react_2.screen.getByRole('button', { name: /resend verification email/i });
                    react_2.fireEvent.click(resendButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/verification email sent successfully/i)).toBeInTheDocument();
                        })];
                case 2:
                    _a.sent();
                    expect(fetch).toHaveBeenLastCalledWith('/api/auth/resend-verification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                        }),
                    });
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle resend verification error', function () { return __awaiter(void 0, void 0, void 0, function () {
        var signupResponse, emailInput, passwordInput, submitButton, resendResponse, resendButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    signupResponse = {
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'User created successfully. Please check your email to verify your account.',
                                        requiresVerification: true,
                                    })];
                            });
                        }); },
                        headers: {
                            get: function () { return 'application/json'; },
                        },
                    };
                    fetch.mockResolvedValueOnce(signupResponse);
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/registration successful!/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    resendResponse = {
                        ok: false,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        error: 'A verification email was recently sent. Please wait 5 minutes before requesting another.',
                                    })];
                            });
                        }); },
                    };
                    fetch.mockResolvedValueOnce(resendResponse);
                    resendButton = react_2.screen.getByRole('button', { name: /resend verification email/i });
                    react_2.fireEvent.click(resendButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/error: a verification email was recently sent/i)).toBeInTheDocument();
                        })];
                case 2:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should allow registering different email', function () { return __awaiter(void 0, void 0, void 0, function () {
        var signupResponse, emailInput, passwordInput, submitButton, differentEmailButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    signupResponse = {
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'User created successfully. Please check your email to verify your account.',
                                        requiresVerification: true,
                                    })];
                            });
                        }); },
                        headers: {
                            get: function () { return 'application/json'; },
                        },
                    };
                    fetch.mockResolvedValueOnce(signupResponse);
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/registration successful!/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    differentEmailButton = react_2.screen.getByRole('button', { name: /register a different email/i });
                    react_2.fireEvent.click(differentEmailButton);
                    // Should return to the form
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByLabelText(/email address/i)).toBeInTheDocument();
                            expect(react_2.screen.getByLabelText(/password/i)).toBeInTheDocument();
                            expect(react_2.screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();
                        })];
                case 2:
                    // Should return to the form
                    _a.sent();
                    // Form should be cleared
                    expect(react_2.screen.getByLabelText(/email address/i)).toHaveValue('');
                    expect(react_2.screen.getByLabelText(/password/i)).toHaveValue('');
                    return [2 /*return*/];
            }
        });
    }); });
    it('should show loading state during signup', function () { return __awaiter(void 0, void 0, void 0, function () {
        var resolvePromise, promise, emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    promise = new Promise(function (resolve) {
                        resolvePromise = resolve;
                    });
                    fetch.mockReturnValue(promise);
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    // Should show loading state
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByRole('status')).toHaveTextContent('Signing up...');
                        })];
                case 1:
                    // Should show loading state
                    _a.sent();
                    // Resolve the promise
                    resolvePromise({
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'User created successfully. Please check your email to verify your account.',
                                        requiresVerification: true,
                                    })];
                            });
                        }); },
                        headers: {
                            get: function () { return 'application/json'; },
                        },
                    });
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/registration successful!/i)).toBeInTheDocument();
                        })];
                case 2:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle network errors gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    fetch.mockRejectedValue(new Error('Network error'));
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/error: an unexpected error occurred/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should require email and password fields', function () {
        (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
        var emailInput = react_2.screen.getByLabelText(/email address/i);
        var passwordInput = react_2.screen.getByLabelText(/password/i);
        expect(emailInput).toBeRequired();
        expect(passwordInput).toBeRequired();
    });
    describe('Security and Edge Cases', function () {
        it('should prevent XSS in error messages', function () { return __awaiter(void 0, void 0, void 0, function () {
            var xssPayload, errorResponse, emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        xssPayload = '<script>alert("xss")</script>';
                        errorResponse = {
                            ok: false,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            message: xssPayload,
                                        })];
                                });
                            }); },
                        };
                        fetch.mockResolvedValue(errorResponse);
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email address/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                        react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                        react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                        react_2.fireEvent.click(submitButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                // Error should be displayed as text, not executed as HTML
                                expect(react_2.screen.getByText(new RegExp(xssPayload.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')))).toBeInTheDocument();
                                expect(document.querySelector('script')).toBeNull();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle extremely long email addresses', function () { return __awaiter(void 0, void 0, void 0, function () {
            var longEmail, emailInput;
            return __generator(this, function (_a) {
                longEmail = 'a'.repeat(250) + '@example.com';
                (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                emailInput = react_2.screen.getByLabelText(/email address/i);
                react_2.fireEvent.change(emailInput, { target: { value: longEmail } });
                expect(emailInput).toHaveValue(longEmail);
                return [2 /*return*/];
            });
        }); });
        it('should handle special characters in credentials', function () { return __awaiter(void 0, void 0, void 0, function () {
            var specialEmail, specialPassword, successResponse, emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        specialEmail = '<EMAIL>';
                        specialPassword = 'P@ssw0rd!#$%^&*()';
                        successResponse = {
                            ok: true,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            message: 'Registration successful! Please check your email for verification.',
                                            requiresVerification: true,
                                        })];
                                });
                            }); },
                        };
                        fetch.mockResolvedValue(successResponse);
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email address/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                        react_2.fireEvent.change(emailInput, { target: { value: specialEmail } });
                        react_2.fireEvent.change(passwordInput, { target: { value: specialPassword } });
                        react_2.fireEvent.click(submitButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(fetch).toHaveBeenCalledWith('/api/auth/signup', expect.objectContaining({
                                    method: 'POST',
                                    body: JSON.stringify({
                                        email: specialEmail,
                                        password: specialPassword,
                                    }),
                                }));
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle rapid form submissions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var successResponse, emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        successResponse = {
                            ok: true,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            message: 'Registration successful!',
                                            requiresVerification: true,
                                        })];
                                });
                            }); },
                        };
                        fetch.mockImplementation(function () {
                            return new Promise(function (resolve) { return setTimeout(function () { return resolve(successResponse); }, 100); });
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email address/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                        react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                        react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                        // Rapid clicks
                        react_2.fireEvent.click(submitButton);
                        react_2.fireEvent.click(submitButton);
                        react_2.fireEvent.click(submitButton);
                        // Should only call fetch once due to form submission protection
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(fetch).toHaveBeenCalledTimes(1);
                            })];
                    case 1:
                        // Should only call fetch once due to form submission protection
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate password strength', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
            var passwordInput = react_2.screen.getByLabelText(/password/i);
            // Test weak password
            react_2.fireEvent.change(passwordInput, { target: { value: '123' } });
            react_2.fireEvent.blur(passwordInput);
            // Should show validation error for weak password
            expect(passwordInput).toHaveValue('123');
        });
        it('should validate email format', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
            var emailInput = react_2.screen.getByLabelText(/email address/i);
            // Test invalid email
            react_2.fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
            react_2.fireEvent.blur(emailInput);
            // Should show validation error for invalid email
            expect(emailInput).toHaveValue('invalid-email');
        });
    });
    describe('Accessibility', function () {
        it('should have proper ARIA labels and roles', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
            var emailInput = react_2.screen.getByLabelText(/email address/i);
            var passwordInput = react_2.screen.getByLabelText(/password/i);
            var submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
            expect(emailInput).toHaveAttribute('aria-required', 'true');
            expect(passwordInput).toHaveAttribute('aria-required', 'true');
            expect(submitButton).toHaveAttribute('type', 'submit');
        });
        it('should announce errors to screen readers', function () { return __awaiter(void 0, void 0, void 0, function () {
            var errorResponse, emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        errorResponse = {
                            ok: false,
                            json: function () { return __awaiter(void 0, void 0, void 0, function () {
                                return __generator(this, function (_a) {
                                    return [2 /*return*/, ({
                                            message: 'Email already exists',
                                        })];
                                });
                            }); },
                        };
                        fetch.mockResolvedValue(errorResponse);
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email address/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
                        react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                        react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                        react_2.fireEvent.click(submitButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                var errorElement = react_2.screen.getByText(/email already exists/i);
                                expect(errorElement).toHaveAttribute('role', 'alert');
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should support keyboard navigation', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
            var emailInput = react_2.screen.getByLabelText(/email address/i);
            var passwordInput = react_2.screen.getByLabelText(/password/i);
            var submitButton = react_2.screen.getByRole('button', { name: /sign up/i });
            // Tab order should be email -> password -> submit
            emailInput.focus();
            expect(document.activeElement).toBe(emailInput);
            react_2.fireEvent.keyDown(emailInput, { key: 'Tab' });
            expect(document.activeElement).toBe(passwordInput);
            react_2.fireEvent.keyDown(passwordInput, { key: 'Tab' });
            expect(document.activeElement).toBe(submitButton);
        });
        it('should have proper form labels and descriptions', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SignupForm_1.default, {}));
            // Check for terms of service and privacy policy links
            expect(react_2.screen.getByText(/terms of service/i)).toBeInTheDocument();
            expect(react_2.screen.getByText(/privacy policy/i)).toBeInTheDocument();
            // Check for proper form structure
            var form = react_2.screen.getByRole('form');
            expect(form).toBeInTheDocument();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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