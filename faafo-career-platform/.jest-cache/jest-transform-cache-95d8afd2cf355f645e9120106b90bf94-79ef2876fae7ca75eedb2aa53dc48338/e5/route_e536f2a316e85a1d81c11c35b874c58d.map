{"version": 3, "names": ["server_1", "cov_qwef75gnq", "s", "require", "next_1", "auth_1", "prisma_1", "__importDefault", "unified_api_error_handler_1", "exports", "dynamic", "GET", "withUnifiedErrorHandling", "request", "f", "__awaiter", "Promise", "getServerSession", "authOptions", "session", "_d", "sent", "b", "_b", "user", "id", "error", "Error", "statusCode", "searchParams", "URL", "url", "query", "get", "category", "author", "date<PERSON><PERSON><PERSON>", "sortBy", "tags", "_c", "split", "filter", "Boolean", "page", "parseInt", "limit", "skip", "<PERSON><PERSON><PERSON><PERSON>", "isHidden", "OR", "title", "contains", "mode", "content", "replies", "some", "categoryId", "name", "email", "profile", "firstName", "lastName", "now", "Date", "startDate", "getFullYear", "getMonth", "getDate", "getTime", "createdAt", "gte", "length", "path", "array_contains", "orderBy", "replyCount", "likeCount", "isPinned", "all", "default", "forumPost", "find<PERSON>any", "where", "include", "select", "profilePictureUrl", "forumReputation", "forumPostCount", "forumReplyCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progressLevel", "slug", "color", "_count", "reactions", "bookmarks", "userId", "type", "take", "count", "_a", "posts", "totalPosts", "formattedPosts", "map", "post", "__assign", "userReaction", "isBookmarked", "undefined", "NextResponse", "json", "success", "data", "pagination", "total", "pages", "Math", "ceil", "searchQuery"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/search/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\n// Force dynamic rendering for this route\nexport const dynamic = 'force-dynamic';\n\ninterface ForumSearchResponse {\n  posts: any[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n  searchQuery: {\n    query: string;\n    category: string;\n    author: string;\n    dateRange: string;\n    sortBy: string;\n    tags: string[];\n  };\n}\n\n// GET /api/forum/search - Advanced forum search\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ForumSearchResponse>>> => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('q') || '';\n    const category = searchParams.get('category') || '';\n    const author = searchParams.get('author') || '';\n    const dateRange = searchParams.get('dateRange') || '';\n    const sortBy = searchParams.get('sortBy') || 'newest';\n    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const skip = (page - 1) * limit;\n\n    // Build where clause for posts\n    const whereClause: any = {\n      isHidden: false,\n    };\n\n    // Text search in title and content\n    if (query) {\n      whereClause.OR = [\n        {\n          title: {\n            contains: query,\n            mode: 'insensitive',\n          },\n        },\n        {\n          content: {\n            contains: query,\n            mode: 'insensitive',\n          },\n        },\n        {\n          replies: {\n            some: {\n              content: {\n                contains: query,\n                mode: 'insensitive',\n              },\n              isHidden: false,\n            },\n          },\n        },\n      ];\n    }\n\n    // Category filter\n    if (category) {\n      whereClause.categoryId = category;\n    }\n\n    // Author filter\n    if (author) {\n      whereClause.author = {\n        OR: [\n          {\n            name: {\n              contains: author,\n              mode: 'insensitive',\n            },\n          },\n          {\n            email: {\n              contains: author,\n              mode: 'insensitive',\n            },\n          },\n          {\n            profile: {\n              OR: [\n                {\n                  firstName: {\n                    contains: author,\n                    mode: 'insensitive',\n                  },\n                },\n                {\n                  lastName: {\n                    contains: author,\n                    mode: 'insensitive',\n                  },\n                },\n              ],\n            },\n          },\n        ],\n      };\n    }\n\n    // Date range filter\n    if (dateRange) {\n      const now = new Date();\n      let startDate: Date;\n\n      switch (dateRange) {\n        case 'today':\n          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n          break;\n        case 'week':\n          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n          break;\n        case 'month':\n          startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n          break;\n        case 'year':\n          startDate = new Date(now.getFullYear(), 0, 1);\n          break;\n        default:\n          startDate = new Date(0);\n      }\n\n      whereClause.createdAt = {\n        gte: startDate,\n      };\n    }\n\n    // Tags filter\n    if (tags.length > 0) {\n      whereClause.tags = {\n        path: '$',\n        array_contains: tags,\n      };\n    }\n\n    // Build order by clause\n    let orderBy: any = { createdAt: 'desc' }; // default newest first\n\n    switch (sortBy) {\n      case 'oldest':\n        orderBy = { createdAt: 'asc' };\n        break;\n      case 'most-replies':\n        orderBy = { replyCount: 'desc' };\n        break;\n      case 'most-reactions':\n        orderBy = { likeCount: 'desc' };\n        break;\n      case 'relevance':\n        // For relevance, we'll use a combination of factors\n        // This is a simplified version - in production you might want to use full-text search\n        orderBy = [\n          { isPinned: 'desc' },\n          { likeCount: 'desc' },\n          { replyCount: 'desc' },\n          { createdAt: 'desc' },\n        ];\n        break;\n      default:\n        orderBy = { createdAt: 'desc' };\n    }\n\n    // Execute search\n    const [posts, totalPosts] = await Promise.all([\n      prisma.forumPost.findMany({\n        where: whereClause,\n        include: {\n          author: {\n            select: {\n              id: true,\n              email: true,\n              name: true,\n              profile: {\n                select: {\n                  profilePictureUrl: true,\n                  forumReputation: true,\n                  forumPostCount: true,\n                  forumReplyCount: true,\n                  currentCareerPath: true,\n                  progressLevel: true,\n                },\n              },\n            },\n          },\n          category: {\n            select: {\n              id: true,\n              name: true,\n              slug: true,\n              color: true,\n            },\n          },\n          _count: {\n            select: {\n              replies: true,\n              reactions: true,\n              bookmarks: true,\n            },\n          },\n          reactions: {\n            where: {\n              userId: session.user.id,\n            },\n            select: {\n              type: true,\n            },\n          },\n          bookmarks: {\n            where: {\n              userId: session.user.id,\n            },\n            select: {\n              id: true,\n            },\n          },\n        },\n        orderBy,\n        skip,\n        take: limit,\n      }),\n      prisma.forumPost.count({\n        where: whereClause,\n      }),\n    ]);\n\n    // Format posts with user interaction data\n    const formattedPosts = posts.map(post => ({\n      ...post,\n      userReaction: post.reactions[0]?.type || null,\n      isBookmarked: post.bookmarks.length > 0,\n      reactions: undefined, // Remove from response\n      bookmarks: undefined, // Remove from response\n    }));\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      posts: formattedPosts,\n      pagination: {\n        page,\n        limit,\n        total: totalPosts,\n        pages: Math.ceil(totalPosts / limit),\n      },\n      searchQuery: {\n        query,\n        category,\n        author,\n        dateRange,\n        sortBy,\n        tags,\n      },\n    }\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAG,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AACA,IAAAK,2BAAA;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA;AAAA;AAAAF,aAAA,GAAAC,CAAA;AACaO,OAAA,CAAAC,OAAO,GAAG,eAAe;AAoBtC;AAAA;AAAAT,aAAA,GAAAC,CAAA;AACaO,OAAA,CAAAE,GAAG,GAAG,IAAAH,2BAAA,CAAAI,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAZ,aAAA,GAAAa,CAAA;EAAAb,aAAA,GAAAC,CAAA;EAAA,OAAAa,SAAA,iBAAGC,OAAO;IAAA;IAAAf,aAAA,GAAAa,CAAA;;;;;;;;;;;;;;UAC/D,qBAAM,IAAAV,MAAA,CAAAa,gBAAgB,EAACZ,MAAA,CAAAa,WAAW,CAAC;;;;;UAA7CC,OAAO,GAAGC,EAAA,CAAAC,IAAA,EAAmC;UAAA;UAAApB,aAAA,GAAAC,CAAA;UAEnD,IAAI;UAAC;UAAA,CAAAD,aAAA,GAAAqB,CAAA,YAAAC,EAAA;UAAA;UAAA,CAAAtB,aAAA,GAAAqB,CAAA,WAAAH,OAAO;UAAA;UAAA,CAAAlB,aAAA,GAAAqB,CAAA,WAAPH,OAAO;UAAA;UAAA,CAAAlB,aAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,aAAA,GAAAqB,CAAA,WAAPH,OAAO,CAAEK,IAAI;UAAA;UAAA,CAAAvB,aAAA,GAAAqB,CAAA,WAAAC,EAAA;UAAA;UAAA,CAAAtB,aAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,aAAA,GAAAqB,CAAA,WAAAC,EAAA,CAAEE,EAAE,IAAE;YAAA;YAAAxB,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YAChBwB,KAAK,GAAG,IAAIC,KAAK,CAAC,yBAAyB,CAAQ;YAAC;YAAA1B,aAAA,GAAAC,CAAA;YAC1DwB,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA3B,aAAA,GAAAC,CAAA;YACvB,MAAMwB,KAAK;UACb,CAAC;UAAA;UAAA;YAAAzB,aAAA,GAAAqB,CAAA;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAES2B,YAAY,GAAK,IAAIC,GAAG,CAACjB,OAAO,CAACkB,GAAG,CAAC,CAAAF,YAAzB;UAA0B;UAAA5B,aAAA,GAAAC,CAAA;UACxC8B,KAAK;UAAG;UAAA,CAAA/B,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,GAAG,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,EAAE;UAAC;UAAArB,aAAA,GAAAC,CAAA;UACpCgC,QAAQ;UAAG;UAAA,CAAAjC,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,UAAU,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,EAAE;UAAC;UAAArB,aAAA,GAAAC,CAAA;UAC9CiC,MAAM;UAAG;UAAA,CAAAlC,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,QAAQ,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,EAAE;UAAC;UAAArB,aAAA,GAAAC,CAAA;UAC1CkC,SAAS;UAAG;UAAA,CAAAnC,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,EAAE;UAAC;UAAArB,aAAA,GAAAC,CAAA;UAChDmC,MAAM;UAAG;UAAA,CAAApC,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,QAAQ,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,QAAQ;UAAC;UAAArB,aAAA,GAAAC,CAAA;UAChDoC,IAAI;UAAG;UAAA,CAAArC,aAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,aAAA,GAAAqB,CAAA,YAAAiB,EAAA,GAAAV,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAAiB,EAAA;UAAA;UAAA,CAAAtC,aAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,aAAA,GAAAqB,CAAA,WAAAiB,EAAA,CAAEC,KAAK,CAAC,GAAG,EAAEC,MAAM,CAACC,OAAO,CAAC;UAAA;UAAA,CAAAzC,aAAA,GAAAqB,CAAA,WAAI,EAAE;UAAC;UAAArB,aAAA,GAAAC,CAAA;UAClEyC,IAAI,GAAGC,QAAQ;UAAC;UAAA,CAAA3C,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,GAAG,EAAC;UAAC;UAAArB,aAAA,GAAAC,CAAA;UACjD2C,KAAK,GAAGD,QAAQ;UAAC;UAAA,CAAA3C,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,IAAI,EAAC;UAAC;UAAArB,aAAA,GAAAC,CAAA;UACpD4C,IAAI,GAAG,CAACH,IAAI,GAAG,CAAC,IAAIE,KAAK;UAAC;UAAA5C,aAAA,GAAAC,CAAA;UAG1B6C,WAAW,GAAQ;YACvBC,QAAQ,EAAE;WACX;UAED;UAAA;UAAA/C,aAAA,GAAAC,CAAA;UACA,IAAI8B,KAAK,EAAE;YAAA;YAAA/B,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YACT6C,WAAW,CAACE,EAAE,GAAG,CACf;cACEC,KAAK,EAAE;gBACLC,QAAQ,EAAEnB,KAAK;gBACfoB,IAAI,EAAE;;aAET,EACD;cACEC,OAAO,EAAE;gBACPF,QAAQ,EAAEnB,KAAK;gBACfoB,IAAI,EAAE;;aAET,EACD;cACEE,OAAO,EAAE;gBACPC,IAAI,EAAE;kBACJF,OAAO,EAAE;oBACPF,QAAQ,EAAEnB,KAAK;oBACfoB,IAAI,EAAE;mBACP;kBACDJ,QAAQ,EAAE;;;aAGf,CACF;UACH,CAAC;UAAA;UAAA;YAAA/C,aAAA,GAAAqB,CAAA;UAAA;UAED;UAAArB,aAAA,GAAAC,CAAA;UACA,IAAIgC,QAAQ,EAAE;YAAA;YAAAjC,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YACZ6C,WAAW,CAACS,UAAU,GAAGtB,QAAQ;UACnC,CAAC;UAAA;UAAA;YAAAjC,aAAA,GAAAqB,CAAA;UAAA;UAED;UAAArB,aAAA,GAAAC,CAAA;UACA,IAAIiC,MAAM,EAAE;YAAA;YAAAlC,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YACV6C,WAAW,CAACZ,MAAM,GAAG;cACnBc,EAAE,EAAE,CACF;gBACEQ,IAAI,EAAE;kBACJN,QAAQ,EAAEhB,MAAM;kBAChBiB,IAAI,EAAE;;eAET,EACD;gBACEM,KAAK,EAAE;kBACLP,QAAQ,EAAEhB,MAAM;kBAChBiB,IAAI,EAAE;;eAET,EACD;gBACEO,OAAO,EAAE;kBACPV,EAAE,EAAE,CACF;oBACEW,SAAS,EAAE;sBACTT,QAAQ,EAAEhB,MAAM;sBAChBiB,IAAI,EAAE;;mBAET,EACD;oBACES,QAAQ,EAAE;sBACRV,QAAQ,EAAEhB,MAAM;sBAChBiB,IAAI,EAAE;;mBAET;;eAGN;aAEJ;UACH,CAAC;UAAA;UAAA;YAAAnD,aAAA,GAAAqB,CAAA;UAAA;UAED;UAAArB,aAAA,GAAAC,CAAA;UACA,IAAIkC,SAAS,EAAE;YAAA;YAAAnC,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YACP4D,GAAG,GAAG,IAAIC,IAAI,EAAE;YAAC;YAAA9D,aAAA,GAAAC,CAAA;YACnB8D,SAAS,SAAM;YAAC;YAAA/D,aAAA,GAAAC,CAAA;YAEpB,QAAQkC,SAAS;cACf,KAAK,OAAO;gBAAA;gBAAAnC,aAAA,GAAAqB,CAAA;gBAAArB,aAAA,GAAAC,CAAA;gBACV8D,SAAS,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,CAAC;gBAAC;gBAAAlE,aAAA,GAAAC,CAAA;gBACvE;cACF,KAAK,MAAM;gBAAA;gBAAAD,aAAA,GAAAqB,CAAA;gBAAArB,aAAA,GAAAC,CAAA;gBACT8D,SAAS,GAAG,IAAID,IAAI,CAACD,GAAG,CAACM,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;gBAAC;gBAAAnE,aAAA,GAAAC,CAAA;gBAC9D;cACF,KAAK,OAAO;gBAAA;gBAAAD,aAAA,GAAAqB,CAAA;gBAAArB,aAAA,GAAAC,CAAA;gBACV8D,SAAS,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAE,CAAC,CAAC;gBAAC;gBAAAjE,aAAA,GAAAC,CAAA;gBAC3D;cACF,KAAK,MAAM;gBAAA;gBAAAD,aAAA,GAAAqB,CAAA;gBAAArB,aAAA,GAAAC,CAAA;gBACT8D,SAAS,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAC;gBAAAhE,aAAA,GAAAC,CAAA;gBAC9C;cACF;gBAAA;gBAAAD,aAAA,GAAAqB,CAAA;gBAAArB,aAAA,GAAAC,CAAA;gBACE8D,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC,CAAC;YAC3B;YAAC;YAAA9D,aAAA,GAAAC,CAAA;YAED6C,WAAW,CAACsB,SAAS,GAAG;cACtBC,GAAG,EAAEN;aACN;UACH,CAAC;UAAA;UAAA;YAAA/D,aAAA,GAAAqB,CAAA;UAAA;UAED;UAAArB,aAAA,GAAAC,CAAA;UACA,IAAIoC,IAAI,CAACiC,MAAM,GAAG,CAAC,EAAE;YAAA;YAAAtE,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YACnB6C,WAAW,CAACT,IAAI,GAAG;cACjBkC,IAAI,EAAE,GAAG;cACTC,cAAc,EAAEnC;aACjB;UACH,CAAC;UAAA;UAAA;YAAArC,aAAA,GAAAqB,CAAA;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAGGwE,OAAO,GAAQ;YAAEL,SAAS,EAAE;UAAM,CAAE;UAAC;UAAApE,aAAA,GAAAC,CAAA;UAEzC,QAAQmC,MAAM;YACZ,KAAK,QAAQ;cAAA;cAAApC,aAAA,GAAAqB,CAAA;cAAArB,aAAA,GAAAC,CAAA;cACXwE,OAAO,GAAG;gBAAEL,SAAS,EAAE;cAAK,CAAE;cAAC;cAAApE,aAAA,GAAAC,CAAA;cAC/B;YACF,KAAK,cAAc;cAAA;cAAAD,aAAA,GAAAqB,CAAA;cAAArB,aAAA,GAAAC,CAAA;cACjBwE,OAAO,GAAG;gBAAEC,UAAU,EAAE;cAAM,CAAE;cAAC;cAAA1E,aAAA,GAAAC,CAAA;cACjC;YACF,KAAK,gBAAgB;cAAA;cAAAD,aAAA,GAAAqB,CAAA;cAAArB,aAAA,GAAAC,CAAA;cACnBwE,OAAO,GAAG;gBAAEE,SAAS,EAAE;cAAM,CAAE;cAAC;cAAA3E,aAAA,GAAAC,CAAA;cAChC;YACF,KAAK,WAAW;cAAA;cAAAD,aAAA,GAAAqB,CAAA;cAAArB,aAAA,GAAAC,CAAA;cACd;cACA;cACAwE,OAAO,GAAG,CACR;gBAAEG,QAAQ,EAAE;cAAM,CAAE,EACpB;gBAAED,SAAS,EAAE;cAAM,CAAE,EACrB;gBAAED,UAAU,EAAE;cAAM,CAAE,EACtB;gBAAEN,SAAS,EAAE;cAAM,CAAE,CACtB;cAAC;cAAApE,aAAA,GAAAC,CAAA;cACF;YACF;cAAA;cAAAD,aAAA,GAAAqB,CAAA;cAAArB,aAAA,GAAAC,CAAA;cACEwE,OAAO,GAAG;gBAAEL,SAAS,EAAE;cAAM,CAAE;UACnC;UAAC;UAAApE,aAAA,GAAAC,CAAA;UAG2B,qBAAMc,OAAO,CAAC8D,GAAG,CAAC,CAC5CxE,QAAA,CAAAyE,OAAM,CAACC,SAAS,CAACC,QAAQ,CAAC;YACxBC,KAAK,EAAEnC,WAAW;YAClBoC,OAAO,EAAE;cACPhD,MAAM,EAAE;gBACNiD,MAAM,EAAE;kBACN3D,EAAE,EAAE,IAAI;kBACRiC,KAAK,EAAE,IAAI;kBACXD,IAAI,EAAE,IAAI;kBACVE,OAAO,EAAE;oBACPyB,MAAM,EAAE;sBACNC,iBAAiB,EAAE,IAAI;sBACvBC,eAAe,EAAE,IAAI;sBACrBC,cAAc,EAAE,IAAI;sBACpBC,eAAe,EAAE,IAAI;sBACrBC,iBAAiB,EAAE,IAAI;sBACvBC,aAAa,EAAE;;;;eAItB;cACDxD,QAAQ,EAAE;gBACRkD,MAAM,EAAE;kBACN3D,EAAE,EAAE,IAAI;kBACRgC,IAAI,EAAE,IAAI;kBACVkC,IAAI,EAAE,IAAI;kBACVC,KAAK,EAAE;;eAEV;cACDC,MAAM,EAAE;gBACNT,MAAM,EAAE;kBACN9B,OAAO,EAAE,IAAI;kBACbwC,SAAS,EAAE,IAAI;kBACfC,SAAS,EAAE;;eAEd;cACDD,SAAS,EAAE;gBACTZ,KAAK,EAAE;kBACLc,MAAM,EAAE7E,OAAO,CAACK,IAAI,CAACC;iBACtB;gBACD2D,MAAM,EAAE;kBACNa,IAAI,EAAE;;eAET;cACDF,SAAS,EAAE;gBACTb,KAAK,EAAE;kBACLc,MAAM,EAAE7E,OAAO,CAACK,IAAI,CAACC;iBACtB;gBACD2D,MAAM,EAAE;kBACN3D,EAAE,EAAE;;;aAGT;YACDiD,OAAO,EAAAA,OAAA;YACP5B,IAAI,EAAAA,IAAA;YACJoD,IAAI,EAAErD;WACP,CAAC,EACFvC,QAAA,CAAAyE,OAAM,CAACC,SAAS,CAACmB,KAAK,CAAC;YACrBjB,KAAK,EAAEnC;WACR,CAAC,CACH,CAAC;;;;;UA5DIqD,EAAA,GAAsBhF,EAAA,CAAAC,IAAA,EA4D1B,EA5DKgF,KAAK,GAAAD,EAAA,KAAEE,UAAU,GAAAF,EAAA;UAAA;UAAAnG,aAAA,GAAAC,CAAA;UA+DlBqG,cAAc,GAAGF,KAAK,CAACG,GAAG,CAAC,UAAAC,IAAI;YAAA;YAAAxG,aAAA,GAAAa,CAAA;;;;YAAI,OAAA4F,QAAA,CAAAA,QAAA,KACpCD,IAAI;cACPE,YAAY;cAAE;cAAA,CAAA1G,aAAA,GAAAqB,CAAA;cAAA;cAAA,CAAArB,aAAA,GAAAqB,CAAA,YAAA8E,EAAA,GAAAK,IAAI,CAACX,SAAS,CAAC,CAAC,CAAC;cAAA;cAAA,CAAA7F,aAAA,GAAAqB,CAAA,WAAA8E,EAAA;cAAA;cAAA,CAAAnG,aAAA,GAAAqB,CAAA;cAAA;cAAA,CAAArB,aAAA,GAAAqB,CAAA,WAAA8E,EAAA,CAAEH,IAAI;cAAA;cAAA,CAAAhG,aAAA,GAAAqB,CAAA,WAAI,IAAI;cAC7CsF,YAAY,EAAEH,IAAI,CAACV,SAAS,CAACxB,MAAM,GAAG,CAAC;cACvCuB,SAAS,EAAEe,SAAS;cACpBd,SAAS,EAAEc;YAAS;WACpB,CAAC;UAAC;UAAA5G,aAAA,GAAAC,CAAA;UAEN,sBAAOF,QAAA,CAAA8G,YAAY,CAACC,IAAI,CAAC;YACvBC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACJZ,KAAK,EAAEE,cAAc;cACrBW,UAAU,EAAE;gBACVvE,IAAI,EAAAA,IAAA;gBACJE,KAAK,EAAAA,KAAA;gBACLsE,KAAK,EAAEb,UAAU;gBACjBc,KAAK,EAAEC,IAAI,CAACC,IAAI,CAAChB,UAAU,GAAGzD,KAAK;eACpC;cACD0E,WAAW,EAAE;gBACXvF,KAAK,EAAAA,KAAA;gBACLE,QAAQ,EAAAA,QAAA;gBACRC,MAAM,EAAAA,MAAA;gBACNC,SAAS,EAAAA,SAAA;gBACTC,MAAM,EAAAA,MAAA;gBACNC,IAAI,EAAAA;;;WAGT,CAAC;;;;CACH,CAAC", "ignoreList": []}