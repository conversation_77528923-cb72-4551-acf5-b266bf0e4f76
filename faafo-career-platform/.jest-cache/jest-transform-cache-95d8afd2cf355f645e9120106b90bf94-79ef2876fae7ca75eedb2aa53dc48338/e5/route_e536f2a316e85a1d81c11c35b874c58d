14d3a572082eaa15687b2d7d30d19ec5
"use strict";

/* istanbul ignore next */
function cov_qwef75gnq() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/search/route.ts";
  var hash = "251a1242a94cf787bdd13cc1b98f0135ff6f1f26";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/search/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 39
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 13
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "85": {
        start: {
          line: 57,
          column: 15
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "86": {
        start: {
          line: 58,
          column: 34
        },
        end: {
          line: 58,
          column: 76
        }
      },
      "87": {
        start: {
          line: 60,
          column: 0
        },
        end: {
          line: 60,
          column: 34
        }
      },
      "88": {
        start: {
          line: 62,
          column: 0
        },
        end: {
          line: 301,
          column: 7
        }
      },
      "89": {
        start: {
          line: 62,
          column: 93
        },
        end: {
          line: 301,
          column: 3
        }
      },
      "90": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 300,
          column: 7
        }
      },
      "91": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 299,
          column: 9
        }
      },
      "92": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 67,
          column: 91
        }
      },
      "93": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 36
        }
      },
      "94": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 74,
          column: 17
        }
      },
      "95": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 71,
          column: 65
        }
      },
      "96": {
        start: {
          line: 72,
          column: 20
        },
        end: {
          line: 72,
          column: 43
        }
      },
      "97": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 32
        }
      },
      "98": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 75,
          column: 65
        }
      },
      "99": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 76,
          column: 52
        }
      },
      "100": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 62
        }
      },
      "101": {
        start: {
          line: 78,
          column: 16
        },
        end: {
          line: 78,
          column: 58
        }
      },
      "102": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 79,
          column: 64
        }
      },
      "103": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 80,
          column: 64
        }
      },
      "104": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 81,
          column: 130
        }
      },
      "105": {
        start: {
          line: 82,
          column: 16
        },
        end: {
          line: 82,
          column: 65
        }
      },
      "106": {
        start: {
          line: 83,
          column: 16
        },
        end: {
          line: 83,
          column: 68
        }
      },
      "107": {
        start: {
          line: 84,
          column: 16
        },
        end: {
          line: 84,
          column: 42
        }
      },
      "108": {
        start: {
          line: 85,
          column: 16
        },
        end: {
          line: 87,
          column: 18
        }
      },
      "109": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 115,
          column: 17
        }
      },
      "110": {
        start: {
          line: 90,
          column: 20
        },
        end: {
          line: 114,
          column: 22
        }
      },
      "111": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 119,
          column: 17
        }
      },
      "112": {
        start: {
          line: 118,
          column: 20
        },
        end: {
          line: 118,
          column: 54
        }
      },
      "113": {
        start: {
          line: 121,
          column: 16
        },
        end: {
          line: 156,
          column: 17
        }
      },
      "114": {
        start: {
          line: 122,
          column: 20
        },
        end: {
          line: 155,
          column: 22
        }
      },
      "115": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 180,
          column: 17
        }
      },
      "116": {
        start: {
          line: 159,
          column: 20
        },
        end: {
          line: 159,
          column: 37
        }
      },
      "117": {
        start: {
          line: 160,
          column: 20
        },
        end: {
          line: 160,
          column: 39
        }
      },
      "118": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 176,
          column: 21
        }
      },
      "119": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 163,
          column: 99
        }
      },
      "120": {
        start: {
          line: 164,
          column: 28
        },
        end: {
          line: 164,
          column: 34
        }
      },
      "121": {
        start: {
          line: 166,
          column: 28
        },
        end: {
          line: 166,
          column: 90
        }
      },
      "122": {
        start: {
          line: 167,
          column: 28
        },
        end: {
          line: 167,
          column: 34
        }
      },
      "123": {
        start: {
          line: 169,
          column: 28
        },
        end: {
          line: 169,
          column: 87
        }
      },
      "124": {
        start: {
          line: 170,
          column: 28
        },
        end: {
          line: 170,
          column: 34
        }
      },
      "125": {
        start: {
          line: 172,
          column: 28
        },
        end: {
          line: 172,
          column: 74
        }
      },
      "126": {
        start: {
          line: 173,
          column: 28
        },
        end: {
          line: 173,
          column: 34
        }
      },
      "127": {
        start: {
          line: 175,
          column: 28
        },
        end: {
          line: 175,
          column: 52
        }
      },
      "128": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 179,
          column: 22
        }
      },
      "129": {
        start: {
          line: 182,
          column: 16
        },
        end: {
          line: 187,
          column: 17
        }
      },
      "130": {
        start: {
          line: 183,
          column: 20
        },
        end: {
          line: 186,
          column: 22
        }
      },
      "131": {
        start: {
          line: 188,
          column: 16
        },
        end: {
          line: 188,
          column: 48
        }
      },
      "132": {
        start: {
          line: 189,
          column: 16
        },
        end: {
          line: 211,
          column: 17
        }
      },
      "133": {
        start: {
          line: 191,
          column: 24
        },
        end: {
          line: 191,
          column: 55
        }
      },
      "134": {
        start: {
          line: 192,
          column: 24
        },
        end: {
          line: 192,
          column: 30
        }
      },
      "135": {
        start: {
          line: 194,
          column: 24
        },
        end: {
          line: 194,
          column: 57
        }
      },
      "136": {
        start: {
          line: 195,
          column: 24
        },
        end: {
          line: 195,
          column: 30
        }
      },
      "137": {
        start: {
          line: 197,
          column: 24
        },
        end: {
          line: 197,
          column: 56
        }
      },
      "138": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 30
        }
      },
      "139": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 207,
          column: 26
        }
      },
      "140": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 30
        }
      },
      "141": {
        start: {
          line: 210,
          column: 24
        },
        end: {
          line: 210,
          column: 56
        }
      },
      "142": {
        start: {
          line: 212,
          column: 16
        },
        end: {
          line: 272,
          column: 24
        }
      },
      "143": {
        start: {
          line: 274,
          column: 16
        },
        end: {
          line: 274,
          column: 66
        }
      },
      "144": {
        start: {
          line: 275,
          column: 16
        },
        end: {
          line: 278,
          column: 19
        }
      },
      "145": {
        start: {
          line: 277,
          column: 20
        },
        end: {
          line: 277,
          column: 242
        }
      },
      "146": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 298,
          column: 24
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 62,
            column: 72
          },
          end: {
            line: 62,
            column: 73
          }
        },
        loc: {
          start: {
            line: 62,
            column: 91
          },
          end: {
            line: 301,
            column: 5
          }
        },
        line: 62
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 62,
            column: 135
          },
          end: {
            line: 62,
            column: 136
          }
        },
        loc: {
          start: {
            line: 62,
            column: 147
          },
          end: {
            line: 301,
            column: 1
          }
        },
        line: 62
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 65,
            column: 29
          },
          end: {
            line: 65,
            column: 30
          }
        },
        loc: {
          start: {
            line: 65,
            column: 43
          },
          end: {
            line: 300,
            column: 5
          }
        },
        line: 65
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 275,
            column: 43
          },
          end: {
            line: 275,
            column: 44
          }
        },
        loc: {
          start: {
            line: 275,
            column: 59
          },
          end: {
            line: 278,
            column: 17
          }
        },
        line: 275
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 66,
            column: 8
          },
          end: {
            line: 299,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 67,
            column: 91
          }
        }, {
          start: {
            line: 68,
            column: 12
          },
          end: {
            line: 272,
            column: 24
          }
        }, {
          start: {
            line: 273,
            column: 12
          },
          end: {
            line: 298,
            column: 24
          }
        }],
        line: 66
      },
      "39": {
        loc: {
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 74,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 74,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "40": {
        loc: {
          start: {
            line: 70,
            column: 22
          },
          end: {
            line: 70,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 120
          },
          end: {
            line: 70,
            column: 126
          }
        }, {
          start: {
            line: 70,
            column: 129
          },
          end: {
            line: 70,
            column: 134
          }
        }],
        line: 70
      },
      "41": {
        loc: {
          start: {
            line: 70,
            column: 22
          },
          end: {
            line: 70,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 22
          },
          end: {
            line: 70,
            column: 100
          }
        }, {
          start: {
            line: 70,
            column: 104
          },
          end: {
            line: 70,
            column: 117
          }
        }],
        line: 70
      },
      "42": {
        loc: {
          start: {
            line: 70,
            column: 28
          },
          end: {
            line: 70,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 69
          },
          end: {
            line: 70,
            column: 75
          }
        }, {
          start: {
            line: 70,
            column: 78
          },
          end: {
            line: 70,
            column: 90
          }
        }],
        line: 70
      },
      "43": {
        loc: {
          start: {
            line: 70,
            column: 28
          },
          end: {
            line: 70,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 28
          },
          end: {
            line: 70,
            column: 44
          }
        }, {
          start: {
            line: 70,
            column: 48
          },
          end: {
            line: 70,
            column: 66
          }
        }],
        line: 70
      },
      "44": {
        loc: {
          start: {
            line: 76,
            column: 24
          },
          end: {
            line: 76,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 76,
            column: 24
          },
          end: {
            line: 76,
            column: 45
          }
        }, {
          start: {
            line: 76,
            column: 49
          },
          end: {
            line: 76,
            column: 51
          }
        }],
        line: 76
      },
      "45": {
        loc: {
          start: {
            line: 77,
            column: 27
          },
          end: {
            line: 77,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 77,
            column: 27
          },
          end: {
            line: 77,
            column: 55
          }
        }, {
          start: {
            line: 77,
            column: 59
          },
          end: {
            line: 77,
            column: 61
          }
        }],
        line: 77
      },
      "46": {
        loc: {
          start: {
            line: 78,
            column: 25
          },
          end: {
            line: 78,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 78,
            column: 25
          },
          end: {
            line: 78,
            column: 51
          }
        }, {
          start: {
            line: 78,
            column: 55
          },
          end: {
            line: 78,
            column: 57
          }
        }],
        line: 78
      },
      "47": {
        loc: {
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 79,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 79,
            column: 57
          }
        }, {
          start: {
            line: 79,
            column: 61
          },
          end: {
            line: 79,
            column: 63
          }
        }],
        line: 79
      },
      "48": {
        loc: {
          start: {
            line: 80,
            column: 25
          },
          end: {
            line: 80,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 25
          },
          end: {
            line: 80,
            column: 51
          }
        }, {
          start: {
            line: 80,
            column: 55
          },
          end: {
            line: 80,
            column: 63
          }
        }],
        line: 80
      },
      "49": {
        loc: {
          start: {
            line: 81,
            column: 23
          },
          end: {
            line: 81,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 24
          },
          end: {
            line: 81,
            column: 122
          }
        }, {
          start: {
            line: 81,
            column: 127
          },
          end: {
            line: 81,
            column: 129
          }
        }],
        line: 81
      },
      "50": {
        loc: {
          start: {
            line: 81,
            column: 24
          },
          end: {
            line: 81,
            column: 122
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 81,
            column: 84
          },
          end: {
            line: 81,
            column: 90
          }
        }, {
          start: {
            line: 81,
            column: 93
          },
          end: {
            line: 81,
            column: 122
          }
        }],
        line: 81
      },
      "51": {
        loc: {
          start: {
            line: 81,
            column: 24
          },
          end: {
            line: 81,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 24
          },
          end: {
            line: 81,
            column: 64
          }
        }, {
          start: {
            line: 81,
            column: 68
          },
          end: {
            line: 81,
            column: 81
          }
        }],
        line: 81
      },
      "52": {
        loc: {
          start: {
            line: 82,
            column: 32
          },
          end: {
            line: 82,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 32
          },
          end: {
            line: 82,
            column: 56
          }
        }, {
          start: {
            line: 82,
            column: 60
          },
          end: {
            line: 82,
            column: 63
          }
        }],
        line: 82
      },
      "53": {
        loc: {
          start: {
            line: 83,
            column: 33
          },
          end: {
            line: 83,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 33
          },
          end: {
            line: 83,
            column: 58
          }
        }, {
          start: {
            line: 83,
            column: 62
          },
          end: {
            line: 83,
            column: 66
          }
        }],
        line: 83
      },
      "54": {
        loc: {
          start: {
            line: 89,
            column: 16
          },
          end: {
            line: 115,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 16
          },
          end: {
            line: 115,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "55": {
        loc: {
          start: {
            line: 117,
            column: 16
          },
          end: {
            line: 119,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 16
          },
          end: {
            line: 119,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "56": {
        loc: {
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 156,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 156,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "57": {
        loc: {
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 180,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 180,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "58": {
        loc: {
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 176,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 162,
            column: 24
          },
          end: {
            line: 164,
            column: 34
          }
        }, {
          start: {
            line: 165,
            column: 24
          },
          end: {
            line: 167,
            column: 34
          }
        }, {
          start: {
            line: 168,
            column: 24
          },
          end: {
            line: 170,
            column: 34
          }
        }, {
          start: {
            line: 171,
            column: 24
          },
          end: {
            line: 173,
            column: 34
          }
        }, {
          start: {
            line: 174,
            column: 24
          },
          end: {
            line: 175,
            column: 52
          }
        }],
        line: 161
      },
      "59": {
        loc: {
          start: {
            line: 182,
            column: 16
          },
          end: {
            line: 187,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 16
          },
          end: {
            line: 187,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "60": {
        loc: {
          start: {
            line: 189,
            column: 16
          },
          end: {
            line: 211,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 190,
            column: 20
          },
          end: {
            line: 192,
            column: 30
          }
        }, {
          start: {
            line: 193,
            column: 20
          },
          end: {
            line: 195,
            column: 30
          }
        }, {
          start: {
            line: 196,
            column: 20
          },
          end: {
            line: 198,
            column: 30
          }
        }, {
          start: {
            line: 199,
            column: 20
          },
          end: {
            line: 208,
            column: 30
          }
        }, {
          start: {
            line: 209,
            column: 20
          },
          end: {
            line: 210,
            column: 56
          }
        }],
        line: 189
      },
      "61": {
        loc: {
          start: {
            line: 277,
            column: 73
          },
          end: {
            line: 277,
            column: 152
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 277,
            column: 74
          },
          end: {
            line: 277,
            column: 143
          }
        }, {
          start: {
            line: 277,
            column: 148
          },
          end: {
            line: 277,
            column: 152
          }
        }],
        line: 277
      },
      "62": {
        loc: {
          start: {
            line: 277,
            column: 74
          },
          end: {
            line: 277,
            column: 143
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 277,
            column: 127
          },
          end: {
            line: 277,
            column: 133
          }
        }, {
          start: {
            line: 277,
            column: 136
          },
          end: {
            line: 277,
            column: 143
          }
        }],
        line: 277
      },
      "63": {
        loc: {
          start: {
            line: 277,
            column: 74
          },
          end: {
            line: 277,
            column: 124
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 277,
            column: 74
          },
          end: {
            line: 277,
            column: 107
          }
        }, {
          start: {
            line: 277,
            column: 111
          },
          end: {
            line: 277,
            column: 124
          }
        }],
        line: 277
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0, 0, 0, 0],
      "59": [0, 0],
      "60": [0, 0, 0, 0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/search/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAClC,6EAAwF;AAExF,yCAAyC;AAC5B,QAAA,OAAO,GAAG,eAAe,CAAC;AAoBvC,gDAAgD;AACnC,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;;oBAC/D,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAES,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACpC,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;gBAC9C,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC1C,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBAChD,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC;gBAChD,IAAI,GAAG,CAAA,MAAA,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,KAAI,EAAE,CAAC;gBAClE,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;gBACjD,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;gBACpD,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;gBAG1B,WAAW,GAAQ;oBACvB,QAAQ,EAAE,KAAK;iBAChB,CAAC;gBAEF,mCAAmC;gBACnC,IAAI,KAAK,EAAE,CAAC;oBACV,WAAW,CAAC,EAAE,GAAG;wBACf;4BACE,KAAK,EAAE;gCACL,QAAQ,EAAE,KAAK;gCACf,IAAI,EAAE,aAAa;6BACpB;yBACF;wBACD;4BACE,OAAO,EAAE;gCACP,QAAQ,EAAE,KAAK;gCACf,IAAI,EAAE,aAAa;6BACpB;yBACF;wBACD;4BACE,OAAO,EAAE;gCACP,IAAI,EAAE;oCACJ,OAAO,EAAE;wCACP,QAAQ,EAAE,KAAK;wCACf,IAAI,EAAE,aAAa;qCACpB;oCACD,QAAQ,EAAE,KAAK;iCAChB;6BACF;yBACF;qBACF,CAAC;gBACJ,CAAC;gBAED,kBAAkB;gBAClB,IAAI,QAAQ,EAAE,CAAC;oBACb,WAAW,CAAC,UAAU,GAAG,QAAQ,CAAC;gBACpC,CAAC;gBAED,gBAAgB;gBAChB,IAAI,MAAM,EAAE,CAAC;oBACX,WAAW,CAAC,MAAM,GAAG;wBACnB,EAAE,EAAE;4BACF;gCACE,IAAI,EAAE;oCACJ,QAAQ,EAAE,MAAM;oCAChB,IAAI,EAAE,aAAa;iCACpB;6BACF;4BACD;gCACE,KAAK,EAAE;oCACL,QAAQ,EAAE,MAAM;oCAChB,IAAI,EAAE,aAAa;iCACpB;6BACF;4BACD;gCACE,OAAO,EAAE;oCACP,EAAE,EAAE;wCACF;4CACE,SAAS,EAAE;gDACT,QAAQ,EAAE,MAAM;gDAChB,IAAI,EAAE,aAAa;6CACpB;yCACF;wCACD;4CACE,QAAQ,EAAE;gDACR,QAAQ,EAAE,MAAM;gDAChB,IAAI,EAAE,aAAa;6CACpB;yCACF;qCACF;iCACF;6BACF;yBACF;qBACF,CAAC;gBACJ,CAAC;gBAED,oBAAoB;gBACpB,IAAI,SAAS,EAAE,CAAC;oBACR,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;oBACnB,SAAS,SAAM,CAAC;oBAEpB,QAAQ,SAAS,EAAE,CAAC;wBAClB,KAAK,OAAO;4BACV,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;4BACvE,MAAM;wBACR,KAAK,MAAM;4BACT,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;4BAC9D,MAAM;wBACR,KAAK,OAAO;4BACV,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;4BAC3D,MAAM;wBACR,KAAK,MAAM;4BACT,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;4BAC9C,MAAM;wBACR;4BACE,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC;oBAED,WAAW,CAAC,SAAS,GAAG;wBACtB,GAAG,EAAE,SAAS;qBACf,CAAC;gBACJ,CAAC;gBAED,cAAc;gBACd,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpB,WAAW,CAAC,IAAI,GAAG;wBACjB,IAAI,EAAE,GAAG;wBACT,cAAc,EAAE,IAAI;qBACrB,CAAC;gBACJ,CAAC;gBAGG,OAAO,GAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;gBAEzC,QAAQ,MAAM,EAAE,CAAC;oBACf,KAAK,QAAQ;wBACX,OAAO,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;wBAC/B,MAAM;oBACR,KAAK,cAAc;wBACjB,OAAO,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;wBACjC,MAAM;oBACR,KAAK,gBAAgB;wBACnB,OAAO,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;wBAChC,MAAM;oBACR,KAAK,WAAW;wBACd,oDAAoD;wBACpD,sFAAsF;wBACtF,OAAO,GAAG;4BACR,EAAE,QAAQ,EAAE,MAAM,EAAE;4BACpB,EAAE,SAAS,EAAE,MAAM,EAAE;4BACrB,EAAE,UAAU,EAAE,MAAM,EAAE;4BACtB,EAAE,SAAS,EAAE,MAAM,EAAE;yBACtB,CAAC;wBACF,MAAM;oBACR;wBACE,OAAO,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;gBACpC,CAAC;gBAG2B,qBAAM,OAAO,CAAC,GAAG,CAAC;wBAC5C,gBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;4BACxB,KAAK,EAAE,WAAW;4BAClB,OAAO,EAAE;gCACP,MAAM,EAAE;oCACN,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,KAAK,EAAE,IAAI;wCACX,IAAI,EAAE,IAAI;wCACV,OAAO,EAAE;4CACP,MAAM,EAAE;gDACN,iBAAiB,EAAE,IAAI;gDACvB,eAAe,EAAE,IAAI;gDACrB,cAAc,EAAE,IAAI;gDACpB,eAAe,EAAE,IAAI;gDACrB,iBAAiB,EAAE,IAAI;gDACvB,aAAa,EAAE,IAAI;6CACpB;yCACF;qCACF;iCACF;gCACD,QAAQ,EAAE;oCACR,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,IAAI,EAAE,IAAI;wCACV,IAAI,EAAE,IAAI;wCACV,KAAK,EAAE,IAAI;qCACZ;iCACF;gCACD,MAAM,EAAE;oCACN,MAAM,EAAE;wCACN,OAAO,EAAE,IAAI;wCACb,SAAS,EAAE,IAAI;wCACf,SAAS,EAAE,IAAI;qCAChB;iCACF;gCACD,SAAS,EAAE;oCACT,KAAK,EAAE;wCACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;qCACxB;oCACD,MAAM,EAAE;wCACN,IAAI,EAAE,IAAI;qCACX;iCACF;gCACD,SAAS,EAAE;oCACT,KAAK,EAAE;wCACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;qCACxB;oCACD,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;qCACT;iCACF;6BACF;4BACD,OAAO,SAAA;4BACP,IAAI,MAAA;4BACJ,IAAI,EAAE,KAAK;yBACZ,CAAC;wBACF,gBAAM,CAAC,SAAS,CAAC,KAAK,CAAC;4BACrB,KAAK,EAAE,WAAW;yBACnB,CAAC;qBACH,CAAC,EAAA;;gBA5DI,KAAsB,SA4D1B,EA5DK,KAAK,QAAA,EAAE,UAAU,QAAA;gBA+DlB,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI;;oBAAI,OAAA,uBACpC,IAAI,KACP,YAAY,EAAE,CAAA,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,0CAAE,IAAI,KAAI,IAAI,EAC7C,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EACvC,SAAS,EAAE,SAAS,EACpB,SAAS,EAAE,SAAS,IACpB,CAAA;iBAAA,CAAC,CAAC;gBAEN,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,KAAK,EAAE,cAAc;4BACrB,UAAU,EAAE;gCACV,IAAI,MAAA;gCACJ,KAAK,OAAA;gCACL,KAAK,EAAE,UAAU;gCACjB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;6BACrC;4BACD,WAAW,EAAE;gCACX,KAAK,OAAA;gCACL,QAAQ,UAAA;gCACR,MAAM,QAAA;gCACN,SAAS,WAAA;gCACT,MAAM,QAAA;gCACN,IAAI,MAAA;6BACL;yBACF;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/search/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\n// Force dynamic rendering for this route\nexport const dynamic = 'force-dynamic';\n\ninterface ForumSearchResponse {\n  posts: any[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n  searchQuery: {\n    query: string;\n    category: string;\n    author: string;\n    dateRange: string;\n    sortBy: string;\n    tags: string[];\n  };\n}\n\n// GET /api/forum/search - Advanced forum search\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ForumSearchResponse>>> => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('q') || '';\n    const category = searchParams.get('category') || '';\n    const author = searchParams.get('author') || '';\n    const dateRange = searchParams.get('dateRange') || '';\n    const sortBy = searchParams.get('sortBy') || 'newest';\n    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const skip = (page - 1) * limit;\n\n    // Build where clause for posts\n    const whereClause: any = {\n      isHidden: false,\n    };\n\n    // Text search in title and content\n    if (query) {\n      whereClause.OR = [\n        {\n          title: {\n            contains: query,\n            mode: 'insensitive',\n          },\n        },\n        {\n          content: {\n            contains: query,\n            mode: 'insensitive',\n          },\n        },\n        {\n          replies: {\n            some: {\n              content: {\n                contains: query,\n                mode: 'insensitive',\n              },\n              isHidden: false,\n            },\n          },\n        },\n      ];\n    }\n\n    // Category filter\n    if (category) {\n      whereClause.categoryId = category;\n    }\n\n    // Author filter\n    if (author) {\n      whereClause.author = {\n        OR: [\n          {\n            name: {\n              contains: author,\n              mode: 'insensitive',\n            },\n          },\n          {\n            email: {\n              contains: author,\n              mode: 'insensitive',\n            },\n          },\n          {\n            profile: {\n              OR: [\n                {\n                  firstName: {\n                    contains: author,\n                    mode: 'insensitive',\n                  },\n                },\n                {\n                  lastName: {\n                    contains: author,\n                    mode: 'insensitive',\n                  },\n                },\n              ],\n            },\n          },\n        ],\n      };\n    }\n\n    // Date range filter\n    if (dateRange) {\n      const now = new Date();\n      let startDate: Date;\n\n      switch (dateRange) {\n        case 'today':\n          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n          break;\n        case 'week':\n          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n          break;\n        case 'month':\n          startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n          break;\n        case 'year':\n          startDate = new Date(now.getFullYear(), 0, 1);\n          break;\n        default:\n          startDate = new Date(0);\n      }\n\n      whereClause.createdAt = {\n        gte: startDate,\n      };\n    }\n\n    // Tags filter\n    if (tags.length > 0) {\n      whereClause.tags = {\n        path: '$',\n        array_contains: tags,\n      };\n    }\n\n    // Build order by clause\n    let orderBy: any = { createdAt: 'desc' }; // default newest first\n\n    switch (sortBy) {\n      case 'oldest':\n        orderBy = { createdAt: 'asc' };\n        break;\n      case 'most-replies':\n        orderBy = { replyCount: 'desc' };\n        break;\n      case 'most-reactions':\n        orderBy = { likeCount: 'desc' };\n        break;\n      case 'relevance':\n        // For relevance, we'll use a combination of factors\n        // This is a simplified version - in production you might want to use full-text search\n        orderBy = [\n          { isPinned: 'desc' },\n          { likeCount: 'desc' },\n          { replyCount: 'desc' },\n          { createdAt: 'desc' },\n        ];\n        break;\n      default:\n        orderBy = { createdAt: 'desc' };\n    }\n\n    // Execute search\n    const [posts, totalPosts] = await Promise.all([\n      prisma.forumPost.findMany({\n        where: whereClause,\n        include: {\n          author: {\n            select: {\n              id: true,\n              email: true,\n              name: true,\n              profile: {\n                select: {\n                  profilePictureUrl: true,\n                  forumReputation: true,\n                  forumPostCount: true,\n                  forumReplyCount: true,\n                  currentCareerPath: true,\n                  progressLevel: true,\n                },\n              },\n            },\n          },\n          category: {\n            select: {\n              id: true,\n              name: true,\n              slug: true,\n              color: true,\n            },\n          },\n          _count: {\n            select: {\n              replies: true,\n              reactions: true,\n              bookmarks: true,\n            },\n          },\n          reactions: {\n            where: {\n              userId: session.user.id,\n            },\n            select: {\n              type: true,\n            },\n          },\n          bookmarks: {\n            where: {\n              userId: session.user.id,\n            },\n            select: {\n              id: true,\n            },\n          },\n        },\n        orderBy,\n        skip,\n        take: limit,\n      }),\n      prisma.forumPost.count({\n        where: whereClause,\n      }),\n    ]);\n\n    // Format posts with user interaction data\n    const formattedPosts = posts.map(post => ({\n      ...post,\n      userReaction: post.reactions[0]?.type || null,\n      isBookmarked: post.bookmarks.length > 0,\n      reactions: undefined, // Remove from response\n      bookmarks: undefined, // Remove from response\n    }));\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      posts: formattedPosts,\n      pagination: {\n        page,\n        limit,\n        total: totalPosts,\n        pages: Math.ceil(totalPosts / limit),\n      },\n      searchQuery: {\n        query,\n        category,\n        author,\n        dateRange,\n        sortBy,\n        tags,\n      },\n    }\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "251a1242a94cf787bdd13cc1b98f0135ff6f1f26"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_qwef75gnq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_qwef75gnq();
var __assign =
/* istanbul ignore next */
(cov_qwef75gnq().s[0]++,
/* istanbul ignore next */
(cov_qwef75gnq().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_qwef75gnq().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_qwef75gnq().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_qwef75gnq().f[0]++;
  cov_qwef75gnq().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_qwef75gnq().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_qwef75gnq().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_qwef75gnq().f[1]++;
    cov_qwef75gnq().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_qwef75gnq().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_qwef75gnq().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_qwef75gnq().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_qwef75gnq().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_qwef75gnq().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_qwef75gnq().b[2][0]++;
          cov_qwef75gnq().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_qwef75gnq().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_qwef75gnq().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_qwef75gnq().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_qwef75gnq().s[11]++,
/* istanbul ignore next */
(cov_qwef75gnq().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_qwef75gnq().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_qwef75gnq().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_qwef75gnq().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_qwef75gnq().f[3]++;
    cov_qwef75gnq().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_qwef75gnq().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_qwef75gnq().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_qwef75gnq().f[4]++;
      cov_qwef75gnq().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_qwef75gnq().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_qwef75gnq().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_qwef75gnq().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_qwef75gnq().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_qwef75gnq().f[6]++;
      cov_qwef75gnq().s[15]++;
      try {
        /* istanbul ignore next */
        cov_qwef75gnq().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_qwef75gnq().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_qwef75gnq().f[7]++;
      cov_qwef75gnq().s[18]++;
      try {
        /* istanbul ignore next */
        cov_qwef75gnq().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_qwef75gnq().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_qwef75gnq().f[8]++;
      cov_qwef75gnq().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_qwef75gnq().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_qwef75gnq().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_qwef75gnq().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_qwef75gnq().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_qwef75gnq().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_qwef75gnq().s[23]++,
/* istanbul ignore next */
(cov_qwef75gnq().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_qwef75gnq().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_qwef75gnq().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_qwef75gnq().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_qwef75gnq().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_qwef75gnq().f[10]++;
        cov_qwef75gnq().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_qwef75gnq().b[9][0]++;
          cov_qwef75gnq().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_qwef75gnq().b[9][1]++;
        }
        cov_qwef75gnq().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_qwef75gnq().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_qwef75gnq().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_qwef75gnq().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_qwef75gnq().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_qwef75gnq().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_qwef75gnq().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_qwef75gnq().f[11]++;
    cov_qwef75gnq().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_qwef75gnq().f[12]++;
    cov_qwef75gnq().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_qwef75gnq().f[13]++;
      cov_qwef75gnq().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_qwef75gnq().f[14]++;
    cov_qwef75gnq().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_qwef75gnq().b[12][0]++;
      cov_qwef75gnq().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_qwef75gnq().b[12][1]++;
    }
    cov_qwef75gnq().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_qwef75gnq().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_qwef75gnq().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_qwef75gnq().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_qwef75gnq().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_qwef75gnq().s[36]++;
      try {
        /* istanbul ignore next */
        cov_qwef75gnq().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_qwef75gnq().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_qwef75gnq().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_qwef75gnq().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_qwef75gnq().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_qwef75gnq().b[18][0]++,
        /* istanbul ignore next */
        (cov_qwef75gnq().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_qwef75gnq().b[19][1]++,
        /* istanbul ignore next */
        (cov_qwef75gnq().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_qwef75gnq().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_qwef75gnq().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_qwef75gnq().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_qwef75gnq().b[15][0]++;
          cov_qwef75gnq().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_qwef75gnq().b[15][1]++;
        }
        cov_qwef75gnq().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_qwef75gnq().b[21][0]++;
          cov_qwef75gnq().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_qwef75gnq().b[21][1]++;
        }
        cov_qwef75gnq().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_qwef75gnq().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_qwef75gnq().b[22][1]++;
            cov_qwef75gnq().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_qwef75gnq().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_qwef75gnq().b[22][2]++;
            cov_qwef75gnq().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_qwef75gnq().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_qwef75gnq().b[22][3]++;
            cov_qwef75gnq().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_qwef75gnq().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_qwef75gnq().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_qwef75gnq().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_qwef75gnq().b[22][4]++;
            cov_qwef75gnq().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_qwef75gnq().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_qwef75gnq().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_qwef75gnq().b[22][5]++;
            cov_qwef75gnq().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_qwef75gnq().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_qwef75gnq().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_qwef75gnq().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_qwef75gnq().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_qwef75gnq().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_qwef75gnq().b[23][0]++;
              cov_qwef75gnq().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_qwef75gnq().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_qwef75gnq().b[23][1]++;
            }
            cov_qwef75gnq().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_qwef75gnq().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_qwef75gnq().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_qwef75gnq().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_qwef75gnq().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_qwef75gnq().b[26][0]++;
              cov_qwef75gnq().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_qwef75gnq().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_qwef75gnq().b[26][1]++;
            }
            cov_qwef75gnq().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_qwef75gnq().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_qwef75gnq().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_qwef75gnq().b[28][0]++;
              cov_qwef75gnq().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_qwef75gnq().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_qwef75gnq().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_qwef75gnq().b[28][1]++;
            }
            cov_qwef75gnq().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_qwef75gnq().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_qwef75gnq().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_qwef75gnq().b[30][0]++;
              cov_qwef75gnq().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_qwef75gnq().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_qwef75gnq().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_qwef75gnq().b[30][1]++;
            }
            cov_qwef75gnq().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_qwef75gnq().b[32][0]++;
              cov_qwef75gnq().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_qwef75gnq().b[32][1]++;
            }
            cov_qwef75gnq().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_qwef75gnq().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_qwef75gnq().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_qwef75gnq().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_qwef75gnq().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_qwef75gnq().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_qwef75gnq().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_qwef75gnq().b[33][0]++;
      cov_qwef75gnq().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_qwef75gnq().b[33][1]++;
    }
    cov_qwef75gnq().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_qwef75gnq().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_qwef75gnq().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_qwef75gnq().s[78]++,
/* istanbul ignore next */
(cov_qwef75gnq().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_qwef75gnq().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_qwef75gnq().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_qwef75gnq().f[15]++;
  cov_qwef75gnq().s[79]++;
  return /* istanbul ignore next */(cov_qwef75gnq().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_qwef75gnq().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_qwef75gnq().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_qwef75gnq().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_qwef75gnq().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_qwef75gnq().s[81]++;
exports.GET = exports.dynamic = void 0;
var server_1 =
/* istanbul ignore next */
(cov_qwef75gnq().s[82]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_qwef75gnq().s[83]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_qwef75gnq().s[84]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_qwef75gnq().s[85]++, __importDefault(require("@/lib/prisma")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_qwef75gnq().s[86]++, require("@/lib/unified-api-error-handler"));
// Force dynamic rendering for this route
/* istanbul ignore next */
cov_qwef75gnq().s[87]++;
exports.dynamic = 'force-dynamic';
// GET /api/forum/search - Advanced forum search
/* istanbul ignore next */
cov_qwef75gnq().s[88]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_qwef75gnq().f[16]++;
  cov_qwef75gnq().s[89]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_qwef75gnq().f[17]++;
    var session, error, searchParams, query, category, author, dateRange, sortBy, tags, page, limit, skip, whereClause, now, startDate, orderBy, _a, posts, totalPosts, formattedPosts;
    var _b, _c;
    /* istanbul ignore next */
    cov_qwef75gnq().s[90]++;
    return __generator(this, function (_d) {
      /* istanbul ignore next */
      cov_qwef75gnq().f[18]++;
      cov_qwef75gnq().s[91]++;
      switch (_d.label) {
        case 0:
          /* istanbul ignore next */
          cov_qwef75gnq().b[38][0]++;
          cov_qwef75gnq().s[92]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_qwef75gnq().b[38][1]++;
          cov_qwef75gnq().s[93]++;
          session = _d.sent();
          /* istanbul ignore next */
          cov_qwef75gnq().s[94]++;
          if (!(
          /* istanbul ignore next */
          (cov_qwef75gnq().b[41][0]++, (_b =
          /* istanbul ignore next */
          (cov_qwef75gnq().b[43][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[43][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_qwef75gnq().b[42][0]++, void 0) :
          /* istanbul ignore next */
          (cov_qwef75gnq().b[42][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[41][1]++, _b === void 0) ?
          /* istanbul ignore next */
          (cov_qwef75gnq().b[40][0]++, void 0) :
          /* istanbul ignore next */
          (cov_qwef75gnq().b[40][1]++, _b.id))) {
            /* istanbul ignore next */
            cov_qwef75gnq().b[39][0]++;
            cov_qwef75gnq().s[95]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_qwef75gnq().s[96]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_qwef75gnq().s[97]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_qwef75gnq().b[39][1]++;
          }
          cov_qwef75gnq().s[98]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_qwef75gnq().s[99]++;
          query =
          /* istanbul ignore next */
          (cov_qwef75gnq().b[44][0]++, searchParams.get('q')) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[44][1]++, '');
          /* istanbul ignore next */
          cov_qwef75gnq().s[100]++;
          category =
          /* istanbul ignore next */
          (cov_qwef75gnq().b[45][0]++, searchParams.get('category')) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[45][1]++, '');
          /* istanbul ignore next */
          cov_qwef75gnq().s[101]++;
          author =
          /* istanbul ignore next */
          (cov_qwef75gnq().b[46][0]++, searchParams.get('author')) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[46][1]++, '');
          /* istanbul ignore next */
          cov_qwef75gnq().s[102]++;
          dateRange =
          /* istanbul ignore next */
          (cov_qwef75gnq().b[47][0]++, searchParams.get('dateRange')) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[47][1]++, '');
          /* istanbul ignore next */
          cov_qwef75gnq().s[103]++;
          sortBy =
          /* istanbul ignore next */
          (cov_qwef75gnq().b[48][0]++, searchParams.get('sortBy')) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[48][1]++, 'newest');
          /* istanbul ignore next */
          cov_qwef75gnq().s[104]++;
          tags =
          /* istanbul ignore next */
          (cov_qwef75gnq().b[49][0]++,
          /* istanbul ignore next */
          (cov_qwef75gnq().b[51][0]++, (_c = searchParams.get('tags')) === null) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[51][1]++, _c === void 0) ?
          /* istanbul ignore next */
          (cov_qwef75gnq().b[50][0]++, void 0) :
          /* istanbul ignore next */
          (cov_qwef75gnq().b[50][1]++, _c.split(',').filter(Boolean))) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[49][1]++, []);
          /* istanbul ignore next */
          cov_qwef75gnq().s[105]++;
          page = parseInt(
          /* istanbul ignore next */
          (cov_qwef75gnq().b[52][0]++, searchParams.get('page')) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[52][1]++, '1'));
          /* istanbul ignore next */
          cov_qwef75gnq().s[106]++;
          limit = parseInt(
          /* istanbul ignore next */
          (cov_qwef75gnq().b[53][0]++, searchParams.get('limit')) ||
          /* istanbul ignore next */
          (cov_qwef75gnq().b[53][1]++, '20'));
          /* istanbul ignore next */
          cov_qwef75gnq().s[107]++;
          skip = (page - 1) * limit;
          /* istanbul ignore next */
          cov_qwef75gnq().s[108]++;
          whereClause = {
            isHidden: false
          };
          // Text search in title and content
          /* istanbul ignore next */
          cov_qwef75gnq().s[109]++;
          if (query) {
            /* istanbul ignore next */
            cov_qwef75gnq().b[54][0]++;
            cov_qwef75gnq().s[110]++;
            whereClause.OR = [{
              title: {
                contains: query,
                mode: 'insensitive'
              }
            }, {
              content: {
                contains: query,
                mode: 'insensitive'
              }
            }, {
              replies: {
                some: {
                  content: {
                    contains: query,
                    mode: 'insensitive'
                  },
                  isHidden: false
                }
              }
            }];
          } else
          /* istanbul ignore next */
          {
            cov_qwef75gnq().b[54][1]++;
          }
          // Category filter
          cov_qwef75gnq().s[111]++;
          if (category) {
            /* istanbul ignore next */
            cov_qwef75gnq().b[55][0]++;
            cov_qwef75gnq().s[112]++;
            whereClause.categoryId = category;
          } else
          /* istanbul ignore next */
          {
            cov_qwef75gnq().b[55][1]++;
          }
          // Author filter
          cov_qwef75gnq().s[113]++;
          if (author) {
            /* istanbul ignore next */
            cov_qwef75gnq().b[56][0]++;
            cov_qwef75gnq().s[114]++;
            whereClause.author = {
              OR: [{
                name: {
                  contains: author,
                  mode: 'insensitive'
                }
              }, {
                email: {
                  contains: author,
                  mode: 'insensitive'
                }
              }, {
                profile: {
                  OR: [{
                    firstName: {
                      contains: author,
                      mode: 'insensitive'
                    }
                  }, {
                    lastName: {
                      contains: author,
                      mode: 'insensitive'
                    }
                  }]
                }
              }]
            };
          } else
          /* istanbul ignore next */
          {
            cov_qwef75gnq().b[56][1]++;
          }
          // Date range filter
          cov_qwef75gnq().s[115]++;
          if (dateRange) {
            /* istanbul ignore next */
            cov_qwef75gnq().b[57][0]++;
            cov_qwef75gnq().s[116]++;
            now = new Date();
            /* istanbul ignore next */
            cov_qwef75gnq().s[117]++;
            startDate = void 0;
            /* istanbul ignore next */
            cov_qwef75gnq().s[118]++;
            switch (dateRange) {
              case 'today':
                /* istanbul ignore next */
                cov_qwef75gnq().b[58][0]++;
                cov_qwef75gnq().s[119]++;
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                /* istanbul ignore next */
                cov_qwef75gnq().s[120]++;
                break;
              case 'week':
                /* istanbul ignore next */
                cov_qwef75gnq().b[58][1]++;
                cov_qwef75gnq().s[121]++;
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                /* istanbul ignore next */
                cov_qwef75gnq().s[122]++;
                break;
              case 'month':
                /* istanbul ignore next */
                cov_qwef75gnq().b[58][2]++;
                cov_qwef75gnq().s[123]++;
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                /* istanbul ignore next */
                cov_qwef75gnq().s[124]++;
                break;
              case 'year':
                /* istanbul ignore next */
                cov_qwef75gnq().b[58][3]++;
                cov_qwef75gnq().s[125]++;
                startDate = new Date(now.getFullYear(), 0, 1);
                /* istanbul ignore next */
                cov_qwef75gnq().s[126]++;
                break;
              default:
                /* istanbul ignore next */
                cov_qwef75gnq().b[58][4]++;
                cov_qwef75gnq().s[127]++;
                startDate = new Date(0);
            }
            /* istanbul ignore next */
            cov_qwef75gnq().s[128]++;
            whereClause.createdAt = {
              gte: startDate
            };
          } else
          /* istanbul ignore next */
          {
            cov_qwef75gnq().b[57][1]++;
          }
          // Tags filter
          cov_qwef75gnq().s[129]++;
          if (tags.length > 0) {
            /* istanbul ignore next */
            cov_qwef75gnq().b[59][0]++;
            cov_qwef75gnq().s[130]++;
            whereClause.tags = {
              path: '$',
              array_contains: tags
            };
          } else
          /* istanbul ignore next */
          {
            cov_qwef75gnq().b[59][1]++;
          }
          cov_qwef75gnq().s[131]++;
          orderBy = {
            createdAt: 'desc'
          };
          /* istanbul ignore next */
          cov_qwef75gnq().s[132]++;
          switch (sortBy) {
            case 'oldest':
              /* istanbul ignore next */
              cov_qwef75gnq().b[60][0]++;
              cov_qwef75gnq().s[133]++;
              orderBy = {
                createdAt: 'asc'
              };
              /* istanbul ignore next */
              cov_qwef75gnq().s[134]++;
              break;
            case 'most-replies':
              /* istanbul ignore next */
              cov_qwef75gnq().b[60][1]++;
              cov_qwef75gnq().s[135]++;
              orderBy = {
                replyCount: 'desc'
              };
              /* istanbul ignore next */
              cov_qwef75gnq().s[136]++;
              break;
            case 'most-reactions':
              /* istanbul ignore next */
              cov_qwef75gnq().b[60][2]++;
              cov_qwef75gnq().s[137]++;
              orderBy = {
                likeCount: 'desc'
              };
              /* istanbul ignore next */
              cov_qwef75gnq().s[138]++;
              break;
            case 'relevance':
              /* istanbul ignore next */
              cov_qwef75gnq().b[60][3]++;
              cov_qwef75gnq().s[139]++;
              // For relevance, we'll use a combination of factors
              // This is a simplified version - in production you might want to use full-text search
              orderBy = [{
                isPinned: 'desc'
              }, {
                likeCount: 'desc'
              }, {
                replyCount: 'desc'
              }, {
                createdAt: 'desc'
              }];
              /* istanbul ignore next */
              cov_qwef75gnq().s[140]++;
              break;
            default:
              /* istanbul ignore next */
              cov_qwef75gnq().b[60][4]++;
              cov_qwef75gnq().s[141]++;
              orderBy = {
                createdAt: 'desc'
              };
          }
          /* istanbul ignore next */
          cov_qwef75gnq().s[142]++;
          return [4 /*yield*/, Promise.all([prisma_1.default.forumPost.findMany({
            where: whereClause,
            include: {
              author: {
                select: {
                  id: true,
                  email: true,
                  name: true,
                  profile: {
                    select: {
                      profilePictureUrl: true,
                      forumReputation: true,
                      forumPostCount: true,
                      forumReplyCount: true,
                      currentCareerPath: true,
                      progressLevel: true
                    }
                  }
                }
              },
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  color: true
                }
              },
              _count: {
                select: {
                  replies: true,
                  reactions: true,
                  bookmarks: true
                }
              },
              reactions: {
                where: {
                  userId: session.user.id
                },
                select: {
                  type: true
                }
              },
              bookmarks: {
                where: {
                  userId: session.user.id
                },
                select: {
                  id: true
                }
              }
            },
            orderBy: orderBy,
            skip: skip,
            take: limit
          }), prisma_1.default.forumPost.count({
            where: whereClause
          })])];
        case 2:
          /* istanbul ignore next */
          cov_qwef75gnq().b[38][2]++;
          cov_qwef75gnq().s[143]++;
          _a = _d.sent(), posts = _a[0], totalPosts = _a[1];
          /* istanbul ignore next */
          cov_qwef75gnq().s[144]++;
          formattedPosts = posts.map(function (post) {
            /* istanbul ignore next */
            cov_qwef75gnq().f[19]++;
            var _a;
            /* istanbul ignore next */
            cov_qwef75gnq().s[145]++;
            return __assign(__assign({}, post), {
              userReaction:
              /* istanbul ignore next */
              (cov_qwef75gnq().b[61][0]++,
              /* istanbul ignore next */
              (cov_qwef75gnq().b[63][0]++, (_a = post.reactions[0]) === null) ||
              /* istanbul ignore next */
              (cov_qwef75gnq().b[63][1]++, _a === void 0) ?
              /* istanbul ignore next */
              (cov_qwef75gnq().b[62][0]++, void 0) :
              /* istanbul ignore next */
              (cov_qwef75gnq().b[62][1]++, _a.type)) ||
              /* istanbul ignore next */
              (cov_qwef75gnq().b[61][1]++, null),
              isBookmarked: post.bookmarks.length > 0,
              reactions: undefined,
              bookmarks: undefined
            });
          });
          /* istanbul ignore next */
          cov_qwef75gnq().s[146]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              posts: formattedPosts,
              pagination: {
                page: page,
                limit: limit,
                total: totalPosts,
                pages: Math.ceil(totalPosts / limit)
              },
              searchQuery: {
                query: query,
                category: category,
                author: author,
                dateRange: dateRange,
                sortBy: sortBy,
                tags: tags
              }
            }
          })];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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