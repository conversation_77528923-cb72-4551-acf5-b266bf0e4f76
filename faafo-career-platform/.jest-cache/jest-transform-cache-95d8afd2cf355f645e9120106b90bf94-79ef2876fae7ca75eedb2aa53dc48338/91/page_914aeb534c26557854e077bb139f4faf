162e2e40f8a8f96e9b96b479fdae57f3
"use strict";
'use client';

/* istanbul ignore next */
function cov_25c7qq7k14() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/assessment/results/[id]/page.tsx";
  var hash = "7904f2cbd9ab051bd0b023e9e762c3711a4da317";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/assessment/results/[id]/page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 20
        },
        end: {
          line: 11,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 5
        }
      },
      "2": {
        start: {
          line: 4,
          column: 40
        },
        end: {
          line: 9,
          column: 5
        }
      },
      "3": {
        start: {
          line: 4,
          column: 53
        },
        end: {
          line: 4,
          column: 54
        }
      },
      "4": {
        start: {
          line: 4,
          column: 60
        },
        end: {
          line: 4,
          column: 71
        }
      },
      "5": {
        start: {
          line: 5,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 6,
          column: 65
        }
      },
      "7": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 65
        }
      },
      "8": {
        start: {
          line: 7,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 61
        }
      },
      "10": {
        start: {
          line: 12,
          column: 22
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "11": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "12": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "13": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 40
        }
      },
      "14": {
        start: {
          line: 17,
          column: 20
        },
        end: {
          line: 17,
          column: 48
        }
      },
      "15": {
        start: {
          line: 18,
          column: 14
        },
        end: {
          line: 18,
          column: 40
        }
      },
      "16": {
        start: {
          line: 19,
          column: 19
        },
        end: {
          line: 19,
          column: 45
        }
      },
      "17": {
        start: {
          line: 20,
          column: 14
        },
        end: {
          line: 20,
          column: 30
        }
      },
      "18": {
        start: {
          line: 21,
          column: 26
        },
        end: {
          line: 21,
          column: 95
        }
      },
      "19": {
        start: {
          line: 22,
          column: 34
        },
        end: {
          line: 22,
          column: 111
        }
      },
      "20": {
        start: {
          line: 23,
          column: 17
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "21": {
        start: {
          line: 24,
          column: 13
        },
        end: {
          line: 24,
          column: 44
        }
      },
      "22": {
        start: {
          line: 25,
          column: 15
        },
        end: {
          line: 25,
          column: 48
        }
      },
      "23": {
        start: {
          line: 26,
          column: 13
        },
        end: {
          line: 26,
          column: 44
        }
      },
      "24": {
        start: {
          line: 27,
          column: 21
        },
        end: {
          line: 27,
          column: 44
        }
      },
      "25": {
        start: {
          line: 28,
          column: 13
        },
        end: {
          line: 28,
          column: 50
        }
      },
      "26": {
        start: {
          line: 30,
          column: 17
        },
        end: {
          line: 30,
          column: 26
        }
      },
      "27": {
        start: {
          line: 31,
          column: 13
        },
        end: {
          line: 31,
          column: 38
        }
      },
      "28": {
        start: {
          line: 31,
          column: 50
        },
        end: {
          line: 31,
          column: 57
        }
      },
      "29": {
        start: {
          line: 31,
          column: 68
        },
        end: {
          line: 31,
          column: 77
        }
      },
      "30": {
        start: {
          line: 32,
          column: 17
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "31": {
        start: {
          line: 33,
          column: 25
        },
        end: {
          line: 33,
          column: 49
        }
      },
      "32": {
        start: {
          line: 34,
          column: 13
        },
        end: {
          line: 34,
          column: 46
        }
      },
      "33": {
        start: {
          line: 34,
          column: 59
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "34": {
        start: {
          line: 34,
          column: 80
        },
        end: {
          line: 34,
          column: 85
        }
      },
      "35": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 39,
          column: 44
        }
      },
      "36": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 38,
          column: 9
        }
      },
      "37": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 118
        }
      },
      "38": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 42,
          column: 5
        }
      },
      "39": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 841
        }
      },
      "40": {
        start: {
          line: 41,
          column: 516
        },
        end: {
          line: 41,
          column: 829
        }
      },
      "41": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 45,
          column: 5
        }
      },
      "42": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 785
        }
      },
      "43": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "44": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 47,
          column: 694
        }
      },
      "45": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 1064
        }
      },
      "46": {
        start: {
          line: 49,
          column: 263
        },
        end: {
          line: 49,
          column: 289
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 52
          },
          end: {
            line: 3,
            column: 53
          }
        },
        loc: {
          start: {
            line: 3,
            column: 78
          },
          end: {
            line: 11,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 12,
            column: 56
          },
          end: {
            line: 12,
            column: 57
          }
        },
        loc: {
          start: {
            line: 12,
            column: 71
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 12
      },
      "2": {
        name: "AssessmentResultsPage",
        decl: {
          start: {
            line: 29,
            column: 9
          },
          end: {
            line: 29,
            column: 30
          }
        },
        loc: {
          start: {
            line: 29,
            column: 35
          },
          end: {
            line: 50,
            column: 1
          }
        },
        line: 29
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 35,
            column: 27
          },
          end: {
            line: 35,
            column: 28
          }
        },
        loc: {
          start: {
            line: 35,
            column: 39
          },
          end: {
            line: 39,
            column: 5
          }
        },
        line: 35
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 41,
            column: 498
          },
          end: {
            line: 41,
            column: 499
          }
        },
        loc: {
          start: {
            line: 41,
            column: 514
          },
          end: {
            line: 41,
            column: 831
          }
        },
        line: 41
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 49,
            column: 244
          },
          end: {
            line: 49,
            column: 245
          }
        },
        loc: {
          start: {
            line: 49,
            column: 261
          },
          end: {
            line: 49,
            column: 291
          }
        },
        line: 49
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 20
          },
          end: {
            line: 11,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 21
          },
          end: {
            line: 3,
            column: 25
          }
        }, {
          start: {
            line: 3,
            column: 29
          },
          end: {
            line: 3,
            column: 47
          }
        }, {
          start: {
            line: 3,
            column: 52
          },
          end: {
            line: 11,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 9,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 9,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 8
          },
          end: {
            line: 4,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 8
          },
          end: {
            line: 4,
            column: 12
          }
        }, {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 38
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 8,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 8,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      },
      "4": {
        loc: {
          start: {
            line: 5,
            column: 12
          },
          end: {
            line: 5,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 12
          },
          end: {
            line: 5,
            column: 14
          }
        }, {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 30
          }
        }],
        line: 5
      },
      "5": {
        loc: {
          start: {
            line: 6,
            column: 12
          },
          end: {
            line: 6,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 12
          },
          end: {
            line: 6,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "6": {
        loc: {
          start: {
            line: 10,
            column: 21
          },
          end: {
            line: 10,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 10,
            column: 21
          },
          end: {
            line: 10,
            column: 23
          }
        }, {
          start: {
            line: 10,
            column: 27
          },
          end: {
            line: 10,
            column: 59
          }
        }],
        line: 10
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 22
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 23
          },
          end: {
            line: 12,
            column: 27
          }
        }, {
          start: {
            line: 12,
            column: 31
          },
          end: {
            line: 12,
            column: 51
          }
        }, {
          start: {
            line: 12,
            column: 56
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 37
          },
          end: {
            line: 13,
            column: 40
          }
        }, {
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 13,
            column: 61
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 13,
            column: 12
          },
          end: {
            line: 13,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 12
          },
          end: {
            line: 13,
            column: 15
          }
        }, {
          start: {
            line: 13,
            column: 19
          },
          end: {
            line: 13,
            column: 33
          }
        }],
        line: 13
      },
      "10": {
        loc: {
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 38,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 38,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "11": {
        loc: {
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 42,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 42,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "12": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 45,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 45,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "13": {
        loc: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "14": {
        loc: {
          start: {
            line: 49,
            column: 841
          },
          end: {
            line: 49,
            column: 1058
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 49,
            column: 868
          },
          end: {
            line: 49,
            column: 964
          }
        }, {
          start: {
            line: 49,
            column: 969
          },
          end: {
            line: 49,
            column: 1057
          }
        }],
        line: 49
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/assessment/results/[id]/page.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;AAoBb,wCA0FC;;AA5GD,yCAA6C;AAC7C,8CAA4C;AAC5C,+BAAiD;AACjD,gGAA0E;AAC1E,gHAA0F;AAC1F,qDAAoD;AACpD,6CAA4C;AAC5C,iDAAgD;AAChD,6CAAgF;AAChF,6CAAgE;AAChE,mDAA6B;AAQ7B,SAAwB,qBAAqB,CAAC,EAAsC;QAApC,MAAM,YAAA;IAC9C,IAAA,KAA4B,IAAA,kBAAU,GAAE,EAAhC,OAAO,UAAA,EAAE,MAAM,YAAiB,CAAC;IAC/C,IAAM,MAAM,GAAG,IAAA,sBAAS,GAAE,CAAC;IAC3B,IAAM,cAAc,GAAG,IAAA,WAAG,EAAC,MAAM,CAAC,CAAC;IAC7B,IAAA,KAA0B,IAAA,gBAAQ,EAA0B,UAAU,CAAC,EAAtE,QAAQ,QAAA,EAAE,WAAW,QAAiD,CAAC;IAE9E,IAAA,iBAAS,EAAC;QACR,IAAI,MAAM,KAAK,iBAAiB,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,qBAAqB,GAAG,kBAAkB,CAAC,8BAAuB,cAAc,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC;QACtG,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IAExC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,OAAO,CACL,iCAAK,SAAS,EAAC,kCAAkC,aAC/C,iCAAK,SAAS,EAAC,kBAAkB,aAC/B,uBAAC,mBAAQ,IAAC,SAAS,EAAC,uBAAuB,GAAG,EAC9C,uBAAC,mBAAQ,IAAC,SAAS,EAAC,kBAAkB,GAAG,IACrC,EACN,gCAAK,SAAS,EAAC,sDAAsD,YAClE,kBAAI,KAAK,CAAC,CAAC,CAAC,QAAE,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAC3B,wBAAC,WAAI,IAAS,SAAS,EAAC,KAAK,aAC3B,uBAAC,mBAAQ,IAAC,SAAS,EAAC,gBAAgB,GAAG,EACvC,uBAAC,mBAAQ,IAAC,SAAS,EAAC,iBAAiB,GAAG,EACxC,uBAAC,mBAAQ,IAAC,SAAS,EAAC,WAAW,GAAG,KAHzB,CAAC,CAIL,CACR,EAN4B,CAM5B,CAAC,GACE,IACF,CACP,CAAC;IACJ,CAAC;IAED,IAAI,MAAM,KAAK,iBAAiB,EAAE,CAAC;QACjC,OAAO,CACL,gCAAK,SAAS,EAAC,wBAAwB,YACrC,wBAAC,WAAI,IAAC,SAAS,EAAC,iBAAiB,aAC/B,uBAAC,0BAAW,IAAC,SAAS,EAAC,wCAAwC,GAAG,EAClE,+BAAI,SAAS,EAAC,0CAA0C,wCAA6B,EACrF,8BAAG,SAAS,EAAC,oBAAoB,gEAAoD,EACrF,uBAAC,eAAM,IAAC,OAAO,kBACb,uBAAC,cAAI,IAAC,IAAI,EAAE,6BAAsB,kBAAkB,CAAC,8BAAuB,cAAc,CAAC,EAAE,CAAE,CAAC,CAAE,wBAE3F,GACA,IACJ,GACH,CACP,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;QACvB,OAAO,CACL,gCAAK,SAAS,EAAC,wBAAwB,YACrC,wBAAC,WAAI,IAAC,SAAS,EAAC,iBAAiB,aAC/B,uBAAC,0BAAW,IAAC,SAAS,EAAC,qCAAqC,GAAG,EAC/D,+BAAI,SAAS,EAAC,yCAAyC,sCAA2B,EAClF,8BAAG,SAAS,EAAC,mBAAmB,yDAA6C,EAC7E,uBAAC,eAAM,IAAC,OAAO,kBACb,uBAAC,cAAI,IAAC,IAAI,EAAC,aAAa,gCAAuB,GACxC,IACJ,GACH,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,iCAAK,SAAS,EAAC,wBAAwB,aAErC,gCAAK,SAAS,EAAC,0BAA0B,YACvC,uBAAC,WAAI,IAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAC,KAAK,IAAK,OAAA,WAAW,CAAC,KAAgC,CAAC,EAA7C,CAA6C,YAC5F,wBAAC,eAAQ,IAAC,SAAS,EAAC,kCAAkC,aACpD,wBAAC,kBAAW,IAAC,KAAK,EAAC,UAAU,EAAC,SAAS,EAAC,yBAAyB,aAC/D,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,wBAEpB,EACd,wBAAC,kBAAW,IAAC,KAAK,EAAC,UAAU,EAAC,SAAS,EAAC,yBAAyB,aAC/D,uBAAC,wBAAS,IAAC,SAAS,EAAC,SAAS,GAAG,wBAErB,IACL,GACN,GACH,EAGL,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,CACzB,uBAAC,mCAAyB,IAAC,YAAY,EAAE,cAAc,CAAC,EAAE,GAAI,CAC/D,CAAC,CAAC,CAAC,CACF,uBAAC,2BAAiB,IAAC,YAAY,EAAE,cAAc,CAAC,EAAE,GAAI,CACvD,IACG,CACP,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/assessment/results/[id]/page.tsx"],
      sourcesContent: ["'use client';\n\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, use, useState } from 'react';\nimport AssessmentResults from '@/components/assessment/AssessmentResults';\nimport EnhancedAssessmentResults from '@/components/assessment/EnhancedAssessmentResults';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Card } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { AlertCircle, Sparkles, BarChart3 } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface AssessmentResultsPageProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\nexport default function AssessmentResultsPage({ params }: AssessmentResultsPageProps) {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const resolvedParams = use(params);\n  const [viewMode, setViewMode] = useState<'standard' | 'enhanced'>('enhanced');\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/login?callbackUrl=' + encodeURIComponent(`/assessment/results/${resolvedParams.id}`));\n    }\n  }, [status, router, resolvedParams.id]);\n\n  if (status === 'loading') {\n    return (\n      <div className=\"container mx-auto py-8 space-y-6\">\n        <div className=\"text-center mb-8\">\n          <Skeleton className=\"h-8 w-64 mx-auto mb-4\" />\n          <Skeleton className=\"h-4 w-96 mx-auto\" />\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {[...Array(6)].map((_, i) => (\n            <Card key={i} className=\"p-6\">\n              <Skeleton className=\"h-6 w-3/4 mb-4\" />\n              <Skeleton className=\"h-4 w-full mb-2\" />\n              <Skeleton className=\"h-4 w-5/6\" />\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (status === 'unauthenticated') {\n    return (\n      <div className=\"container mx-auto py-8\">\n        <Card className=\"p-8 text-center\">\n          <AlertCircle className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-semibold text-gray-700 mb-2\">Authentication Required</h2>\n          <p className=\"text-gray-600 mb-4\">Please sign in to view your assessment results.</p>\n          <Button asChild>\n            <Link href={`/login?callbackUrl=${encodeURIComponent(`/assessment/results/${resolvedParams.id}`)}`}>\n              Sign In\n            </Link>\n          </Button>\n        </Card>\n      </div>\n    );\n  }\n\n  if (!resolvedParams.id) {\n    return (\n      <div className=\"container mx-auto py-8\">\n        <Card className=\"p-8 text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-semibold text-red-700 mb-2\">Invalid Assessment ID</h2>\n          <p className=\"text-red-600 mb-4\">The assessment ID provided is not valid.</p>\n          <Button asChild>\n            <Link href=\"/assessment\">Take Assessment</Link>\n          </Button>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto py-8\">\n      {/* View Mode Toggle */}\n      <div className=\"mb-6 flex justify-center\">\n        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'standard' | 'enhanced')}>\n          <TabsList className=\"grid w-full max-w-md grid-cols-2\">\n            <TabsTrigger value=\"enhanced\" className=\"flex items-center gap-2\">\n              <Sparkles className=\"h-4 w-4\" />\n              Enhanced Results\n            </TabsTrigger>\n            <TabsTrigger value=\"standard\" className=\"flex items-center gap-2\">\n              <BarChart3 className=\"h-4 w-4\" />\n              Standard Results\n            </TabsTrigger>\n          </TabsList>\n        </Tabs>\n      </div>\n\n      {/* Results Content */}\n      {viewMode === 'enhanced' ? (\n        <EnhancedAssessmentResults assessmentId={resolvedParams.id} />\n      ) : (\n        <AssessmentResults assessmentId={resolvedParams.id} />\n      )}\n    </div>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7904f2cbd9ab051bd0b023e9e762c3711a4da317"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25c7qq7k14 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25c7qq7k14();
var __spreadArray =
/* istanbul ignore next */
(cov_25c7qq7k14().s[0]++,
/* istanbul ignore next */
(cov_25c7qq7k14().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_25c7qq7k14().b[0][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_25c7qq7k14().b[0][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_25c7qq7k14().f[0]++;
  cov_25c7qq7k14().s[1]++;
  if (
  /* istanbul ignore next */
  (cov_25c7qq7k14().b[2][0]++, pack) ||
  /* istanbul ignore next */
  (cov_25c7qq7k14().b[2][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_25c7qq7k14().b[1][0]++;
    cov_25c7qq7k14().s[2]++;
    for (var i =
      /* istanbul ignore next */
      (cov_25c7qq7k14().s[3]++, 0), l =
      /* istanbul ignore next */
      (cov_25c7qq7k14().s[4]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_25c7qq7k14().s[5]++;
      if (
      /* istanbul ignore next */
      (cov_25c7qq7k14().b[4][0]++, ar) ||
      /* istanbul ignore next */
      (cov_25c7qq7k14().b[4][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_25c7qq7k14().b[3][0]++;
        cov_25c7qq7k14().s[6]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_25c7qq7k14().b[5][0]++;
          cov_25c7qq7k14().s[7]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_25c7qq7k14().b[5][1]++;
        }
        cov_25c7qq7k14().s[8]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_25c7qq7k14().b[3][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_25c7qq7k14().b[1][1]++;
  }
  cov_25c7qq7k14().s[9]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_25c7qq7k14().b[6][0]++, ar) ||
  /* istanbul ignore next */
  (cov_25c7qq7k14().b[6][1]++, Array.prototype.slice.call(from)));
}));
var __importDefault =
/* istanbul ignore next */
(cov_25c7qq7k14().s[10]++,
/* istanbul ignore next */
(cov_25c7qq7k14().b[7][0]++, this) &&
/* istanbul ignore next */
(cov_25c7qq7k14().b[7][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_25c7qq7k14().b[7][2]++, function (mod) {
  /* istanbul ignore next */
  cov_25c7qq7k14().f[1]++;
  cov_25c7qq7k14().s[11]++;
  return /* istanbul ignore next */(cov_25c7qq7k14().b[9][0]++, mod) &&
  /* istanbul ignore next */
  (cov_25c7qq7k14().b[9][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_25c7qq7k14().b[8][0]++, mod) :
  /* istanbul ignore next */
  (cov_25c7qq7k14().b[8][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_25c7qq7k14().s[12]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_25c7qq7k14().s[13]++;
exports.default = AssessmentResultsPage;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[14]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[15]++, require("next-auth/react"));
var navigation_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[16]++, require("next/navigation"));
var react_2 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[17]++, require("react"));
var AssessmentResults_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[18]++, __importDefault(require("@/components/assessment/AssessmentResults")));
var EnhancedAssessmentResults_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[19]++, __importDefault(require("@/components/assessment/EnhancedAssessmentResults")));
var skeleton_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[20]++, require("@/components/ui/skeleton"));
var card_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[21]++, require("@/components/ui/card"));
var button_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[22]++, require("@/components/ui/button"));
var tabs_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[23]++, require("@/components/ui/tabs"));
var lucide_react_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[24]++, require("lucide-react"));
var link_1 =
/* istanbul ignore next */
(cov_25c7qq7k14().s[25]++, __importDefault(require("next/link")));
function AssessmentResultsPage(_a) {
  /* istanbul ignore next */
  cov_25c7qq7k14().f[2]++;
  var params =
  /* istanbul ignore next */
  (cov_25c7qq7k14().s[26]++, _a.params);
  var _b =
    /* istanbul ignore next */
    (cov_25c7qq7k14().s[27]++, (0, react_1.useSession)()),
    session =
    /* istanbul ignore next */
    (cov_25c7qq7k14().s[28]++, _b.data),
    status =
    /* istanbul ignore next */
    (cov_25c7qq7k14().s[29]++, _b.status);
  var router =
  /* istanbul ignore next */
  (cov_25c7qq7k14().s[30]++, (0, navigation_1.useRouter)());
  var resolvedParams =
  /* istanbul ignore next */
  (cov_25c7qq7k14().s[31]++, (0, react_2.use)(params));
  var _c =
    /* istanbul ignore next */
    (cov_25c7qq7k14().s[32]++, (0, react_2.useState)('enhanced')),
    viewMode =
    /* istanbul ignore next */
    (cov_25c7qq7k14().s[33]++, _c[0]),
    setViewMode =
    /* istanbul ignore next */
    (cov_25c7qq7k14().s[34]++, _c[1]);
  /* istanbul ignore next */
  cov_25c7qq7k14().s[35]++;
  (0, react_2.useEffect)(function () {
    /* istanbul ignore next */
    cov_25c7qq7k14().f[3]++;
    cov_25c7qq7k14().s[36]++;
    if (status === 'unauthenticated') {
      /* istanbul ignore next */
      cov_25c7qq7k14().b[10][0]++;
      cov_25c7qq7k14().s[37]++;
      router.push('/login?callbackUrl=' + encodeURIComponent("/assessment/results/".concat(resolvedParams.id)));
    } else
    /* istanbul ignore next */
    {
      cov_25c7qq7k14().b[10][1]++;
    }
  }, [status, router, resolvedParams.id]);
  /* istanbul ignore next */
  cov_25c7qq7k14().s[38]++;
  if (status === 'loading') {
    /* istanbul ignore next */
    cov_25c7qq7k14().b[11][0]++;
    cov_25c7qq7k14().s[39]++;
    return (0, jsx_runtime_1.jsxs)("div", {
      className: "container mx-auto py-8 space-y-6",
      children: [(0, jsx_runtime_1.jsxs)("div", {
        className: "text-center mb-8",
        children: [(0, jsx_runtime_1.jsx)(skeleton_1.Skeleton, {
          className: "h-8 w-64 mx-auto mb-4"
        }), (0, jsx_runtime_1.jsx)(skeleton_1.Skeleton, {
          className: "h-4 w-96 mx-auto"
        })]
      }), (0, jsx_runtime_1.jsx)("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
        children: __spreadArray([], Array(6), true).map(function (_, i) {
          /* istanbul ignore next */
          cov_25c7qq7k14().f[4]++;
          cov_25c7qq7k14().s[40]++;
          return (0, jsx_runtime_1.jsxs)(card_1.Card, {
            className: "p-6",
            children: [(0, jsx_runtime_1.jsx)(skeleton_1.Skeleton, {
              className: "h-6 w-3/4 mb-4"
            }), (0, jsx_runtime_1.jsx)(skeleton_1.Skeleton, {
              className: "h-4 w-full mb-2"
            }), (0, jsx_runtime_1.jsx)(skeleton_1.Skeleton, {
              className: "h-4 w-5/6"
            })]
          }, i);
        })
      })]
    });
  } else
  /* istanbul ignore next */
  {
    cov_25c7qq7k14().b[11][1]++;
  }
  cov_25c7qq7k14().s[41]++;
  if (status === 'unauthenticated') {
    /* istanbul ignore next */
    cov_25c7qq7k14().b[12][0]++;
    cov_25c7qq7k14().s[42]++;
    return (0, jsx_runtime_1.jsx)("div", {
      className: "container mx-auto py-8",
      children: (0, jsx_runtime_1.jsxs)(card_1.Card, {
        className: "p-8 text-center",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertCircle, {
          className: "h-12 w-12 text-yellow-500 mx-auto mb-4"
        }), (0, jsx_runtime_1.jsx)("h2", {
          className: "text-xl font-semibold text-gray-700 mb-2",
          children: "Authentication Required"
        }), (0, jsx_runtime_1.jsx)("p", {
          className: "text-gray-600 mb-4",
          children: "Please sign in to view your assessment results."
        }), (0, jsx_runtime_1.jsx)(button_1.Button, {
          asChild: true,
          children: (0, jsx_runtime_1.jsx)(link_1.default, {
            href: "/login?callbackUrl=".concat(encodeURIComponent("/assessment/results/".concat(resolvedParams.id))),
            children: "Sign In"
          })
        })]
      })
    });
  } else
  /* istanbul ignore next */
  {
    cov_25c7qq7k14().b[12][1]++;
  }
  cov_25c7qq7k14().s[43]++;
  if (!resolvedParams.id) {
    /* istanbul ignore next */
    cov_25c7qq7k14().b[13][0]++;
    cov_25c7qq7k14().s[44]++;
    return (0, jsx_runtime_1.jsx)("div", {
      className: "container mx-auto py-8",
      children: (0, jsx_runtime_1.jsxs)(card_1.Card, {
        className: "p-8 text-center",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertCircle, {
          className: "h-12 w-12 text-red-500 mx-auto mb-4"
        }), (0, jsx_runtime_1.jsx)("h2", {
          className: "text-xl font-semibold text-red-700 mb-2",
          children: "Invalid Assessment ID"
        }), (0, jsx_runtime_1.jsx)("p", {
          className: "text-red-600 mb-4",
          children: "The assessment ID provided is not valid."
        }), (0, jsx_runtime_1.jsx)(button_1.Button, {
          asChild: true,
          children: (0, jsx_runtime_1.jsx)(link_1.default, {
            href: "/assessment",
            children: "Take Assessment"
          })
        })]
      })
    });
  } else
  /* istanbul ignore next */
  {
    cov_25c7qq7k14().b[13][1]++;
  }
  cov_25c7qq7k14().s[45]++;
  return (0, jsx_runtime_1.jsxs)("div", {
    className: "container mx-auto py-8",
    children: [(0, jsx_runtime_1.jsx)("div", {
      className: "mb-6 flex justify-center",
      children: (0, jsx_runtime_1.jsx)(tabs_1.Tabs, {
        value: viewMode,
        onValueChange: function (value) {
          /* istanbul ignore next */
          cov_25c7qq7k14().f[5]++;
          cov_25c7qq7k14().s[46]++;
          return setViewMode(value);
        },
        children: (0, jsx_runtime_1.jsxs)(tabs_1.TabsList, {
          className: "grid w-full max-w-md grid-cols-2",
          children: [(0, jsx_runtime_1.jsxs)(tabs_1.TabsTrigger, {
            value: "enhanced",
            className: "flex items-center gap-2",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Sparkles, {
              className: "h-4 w-4"
            }), "Enhanced Results"]
          }), (0, jsx_runtime_1.jsxs)(tabs_1.TabsTrigger, {
            value: "standard",
            className: "flex items-center gap-2",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.BarChart3, {
              className: "h-4 w-4"
            }), "Standard Results"]
          })]
        })
      })
    }), viewMode === 'enhanced' ?
    /* istanbul ignore next */
    (cov_25c7qq7k14().b[14][0]++, (0, jsx_runtime_1.jsx)(EnhancedAssessmentResults_1.default, {
      assessmentId: resolvedParams.id
    })) :
    /* istanbul ignore next */
    (cov_25c7qq7k14().b[14][1]++, (0, jsx_runtime_1.jsx)(AssessmentResults_1.default, {
      assessmentId: resolvedParams.id
    }))]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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