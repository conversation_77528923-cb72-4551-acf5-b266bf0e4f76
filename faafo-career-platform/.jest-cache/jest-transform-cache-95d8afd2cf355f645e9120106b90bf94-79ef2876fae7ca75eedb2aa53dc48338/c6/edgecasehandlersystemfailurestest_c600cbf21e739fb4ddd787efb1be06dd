c271d783e7063132868c12b93c5c5963
"use strict";
/**
 * EdgeCaseHandler System Failures Tests
 * Focused on system failure scenarios and error handling
 * Optimized for fast execution
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EdgeCaseHandler_1 = require("@/lib/skills/EdgeCaseHandler");
// Shared mock factories for performance
var createMockServices = function () { return ({
    mockAssessmentEngine: {
        createAssessment: jest.fn(),
        generateQuestions: jest.fn(),
        submitResponse: jest.fn(),
        calculateResults: jest.fn(),
        getAssessment: jest.fn(),
        getAssessmentsByUser: jest.fn(),
    },
    mockMarketDataService: {
        getSkillMarketData: jest.fn(),
        getMultipleSkillsMarketData: jest.fn(),
        analyzeMarketTrends: jest.fn(),
        getSalaryInsights: jest.fn(),
        getLocationBasedMarketData: jest.fn(),
        getMarketBasedRecommendations: jest.fn(),
    },
    mockLearningPathService: {
        generateLearningPath: jest.fn(),
        updateProgress: jest.fn(),
        completeMilestone: jest.fn(),
    },
}); };
describe('EdgeCaseHandler - System Failures', function () {
    var edgeCaseHandler;
    var mocks;
    beforeEach(function () {
        mocks = createMockServices();
        edgeCaseHandler = new EdgeCaseHandler_1.EdgeCaseHandler(mocks.mockAssessmentEngine, mocks.mockMarketDataService, mocks.mockLearningPathService);
    });
    describe('Database Failures', function () {
        it('should handle database connection failures', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Database connection failed');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Database connection failed');
                        expect(result.errorType).toBe('SYSTEM_ERROR');
                        expect(result.retryable).toBe(true);
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle database timeout errors', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Query timeout');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Query timeout');
                        expect(result.errorType).toBe('TIMEOUT_ERROR');
                        expect(result.retryable).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
    });
    describe('AI Service Failures', function () {
        it('should handle AI service timeouts', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            throw new Error('AI service timeout');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('AI service timeout');
                        expect(result.errorType).toBe('TIMEOUT_ERROR');
                        expect(result.retryable).toBe(true);
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle AI service rate limiting', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            throw new Error('Rate limit exceeded');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Rate limit exceeded');
                        expect(result.errorType).toBe('AI_SERVICE_ERROR');
                        expect(result.retryAfter).toBeDefined();
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
    });
    describe('Memory and Performance Limits', function () {
        it('should handle memory exhaustion gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: Array(1000).fill('skill'), // Reduced from 100000 for performance
                            careerPathId: 'path-456',
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Values exceed maximum thresholds');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        expect(result.suggestedOptimizations).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle processing timeouts', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: Array(100).fill({ skill: 'javascript', level: 5, confidence: 6 }), // Reduced from 1000
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        // Mock a timeout operation with shorter delay for testing
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            return new Promise(function (resolve) {
                                setTimeout(function () { return resolve({}); }, 6000); // 6 second delay
                            });
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request, { timeout: 2000 })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Processing timeout');
                        expect(result.errorType).toBe('TIMEOUT_ERROR');
                        expect(result.partialResults).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
    });
    describe('Monitoring and Alerting', function () {
        it('should log security incidents', function () { return __awaiter(void 0, void 0, void 0, function () {
            var maliciousRequest, logSpy;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        maliciousRequest = {
                            userId: "'; DROP TABLE users; --",
                            skillIds: ["'; DELETE FROM skills; --"],
                            careerPathId: "1' OR '1'='1",
                        };
                        logSpy = jest.spyOn(console, 'error').mockImplementation();
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(maliciousRequest)];
                    case 1:
                        _a.sent();
                        expect(logSpy).toHaveBeenCalledWith(expect.stringContaining('SECURITY_ALERT'), expect.objectContaining({
                            type: 'SQL_INJECTION_ATTEMPT',
                            userId: maliciousRequest.userId,
                            timestamp: expect.any(Date),
                        }));
                        logSpy.mockRestore();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should track error patterns', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, i, errorStats;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Database connection failed');
                        });
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < 3)) return [3 /*break*/, 4];
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, edgeCaseHandler.getErrorStatistics()];
                    case 5:
                        errorStats = _a.sent();
                        expect(errorStats.totalErrors).toBeGreaterThanOrEqual(3);
                        expect(errorStats.errorsByType.SYSTEM_ERROR).toBeGreaterThanOrEqual(3);
                        expect(errorStats.mostCommonError).toBe('Database connection failed');
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
    });
    describe('Performance Optimization Tests', function () {
        it('should complete validation within performance threshold', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startTime, request, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startTime = Date.now();
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript', 'react', 'node'],
                            careerPathId: 'path-456',
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        _a.sent();
                        duration = Date.now() - startTime;
                        expect(duration).toBeLessThan(3000); // Should complete within 3 seconds
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle multiple concurrent requests efficiently', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startTime, request, promises, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startTime = Date.now();
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        promises = Array(3).fill(null).map(function (_, index) {
                            return edgeCaseHandler.handleSkillAssessment(__assign(__assign({}, request), { userId: "user-".concat(index) }));
                        });
                        return [4 /*yield*/, Promise.all(promises)];
                    case 1:
                        _a.sent();
                        duration = Date.now() - startTime;
                        expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
        it('should maintain memory efficiency during processing', function () { return __awaiter(void 0, void 0, void 0, function () {
            var initialMemory, request, i, finalMemory, memoryIncrease;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        initialMemory = process.memoryUsage().heapUsed;
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < 5)) return [3 /*break*/, 4];
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(__assign(__assign({}, request), { userId: "user-".concat(i) }))];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4:
                        finalMemory = process.memoryUsage().heapUsed;
                        memoryIncrease = finalMemory - initialMemory;
                        // Memory increase should be reasonable (less than 50MB)
                        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
                        return [2 /*return*/];
                }
            });
        }); }, 10000);
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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