{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/edge-case-handler.system-failures.test.ts", "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,gEAA+D;AAK/D,wCAAwC;AACxC,IAAM,kBAAkB,GAAG,cAAM,OAAA,CAAC;IAChC,oBAAoB,EAAE;QACpB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB;IACR,qBAAqB,EAAE;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;QACtC,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;QACrC,6BAA6B,EAAE,IAAI,CAAC,EAAE,EAAE;KAClC;IACR,uBAAuB,EAAE;QACvB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC/B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;KACtB;CACT,CAAC,EAtB+B,CAsB/B,CAAC;AAEH,QAAQ,CAAC,mCAAmC,EAAE;IAC5C,IAAI,eAAgC,CAAC;IACrC,IAAI,KAA4C,CAAC;IAEjD,UAAU,CAAC;QACT,KAAK,GAAG,kBAAkB,EAAE,CAAC;QAC7B,eAAe,GAAG,IAAI,iCAAe,CACnC,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,qBAAqB,EAC3B,KAAK,CAAC,uBAAuB,CAC9B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,4CAA4C,EAAE;;;;;wBACzC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BAC7D,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;wBAChD,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;wBAC7D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBAC9C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACpC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BAC7D,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;wBACnC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBAC/C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACrC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEF,KAAK,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;4BACpE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;wBACxC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;wBACrD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBAC/C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACpC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,wCAAwC,EAAE;;;;;wBACrC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEF,KAAK,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;4BACpE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;wBACzC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACxC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE;QACxC,EAAE,CAAC,4CAA4C,EAAE;;;;;wBACzC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,sCAAsC;4BAC3E,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEa,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;wBACnE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACrD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,oBAAoB;4BACtG,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEF,0DAA0D;wBAC1D,KAAK,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;4BACpE,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;gCACzB,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,EAAS,CAAC,EAAlB,CAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,iBAAiB;4BAC/D,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAA;;wBAAvF,MAAM,GAAG,SAA8E;wBAE7F,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;wBACrD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBAC/C,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC7C,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,+BAA+B,EAAE;;;;;wBAC5B,gBAAgB,GAAG;4BACvB,MAAM,EAAE,yBAAyB;4BACjC,QAAQ,EAAE,CAAC,2BAA2B,CAAC;4BACvC,YAAY,EAAE,cAAc;yBAC7B,CAAC;wBAEI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,kBAAkB,EAAE,CAAC;wBAEjE,qBAAM,eAAe,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAA;;wBAA7D,SAA6D,CAAC;wBAE9D,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACjC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EACzC,MAAM,CAAC,gBAAgB,CAAC;4BACtB,IAAI,EAAE,uBAAuB;4BAC7B,MAAM,EAAE,gBAAgB,CAAC,MAAM;4BAC/B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;yBAC5B,CAAC,CACH,CAAC;wBAEF,MAAM,CAAC,WAAW,EAAE,CAAC;;;;aACtB,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,6BAA6B,EAAE;;;;;wBAC1B,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BAC7D,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;wBAChD,CAAC,CAAC,CAAC;wBAGM,CAAC,GAAG,CAAC;;;6BAAE,CAAA,CAAC,GAAG,CAAC,CAAA;wBACnB,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAApD,SAAoD,CAAC;;;wBADhC,CAAC,EAAE,CAAA;;4BAIP,qBAAM,eAAe,CAAC,kBAAkB,EAAE,EAAA;;wBAAvD,UAAU,GAAG,SAA0C;wBAE7D,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACzD,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACvE,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;;;;aACvE,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE;QACzC,EAAE,CAAC,yDAAyD,EAAE;;;;;wBACtD,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAEvB,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC;4BACzC,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAApD,SAAoD,CAAC;wBAE/C,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBACxC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;;;;aACzE,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,wDAAwD,EAAE;;;;;wBACrD,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAEvB,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAGI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,KAAK;4BAChD,OAAA,eAAe,CAAC,qBAAqB,uBAChC,OAAO,KACV,MAAM,EAAE,eAAQ,KAAK,CAAE,IACvB;wBAHF,CAGE,CACH,CAAC;wBAEF,qBAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAA;;wBAA3B,SAA2B,CAAC;wBAEtB,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBACxC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;;;;aACzE,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,qDAAqD,EAAE;;;;;wBAClD,aAAa,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;wBAE/C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAGO,CAAC,GAAG,CAAC;;;6BAAE,CAAA,CAAC,GAAG,CAAC,CAAA;wBACnB,qBAAM,eAAe,CAAC,qBAAqB,uBACtC,OAAO,KACV,MAAM,EAAE,eAAQ,CAAC,CAAE,IACnB,EAAA;;wBAHF,SAGE,CAAC;;;wBAJkB,CAAC,EAAE,CAAA;;;wBAOpB,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;wBAC7C,cAAc,GAAG,WAAW,GAAG,aAAa,CAAC;wBAEnD,wDAAwD;wBACxD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;;;aACvD,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/edge-case-handler.system-failures.test.ts"], "sourcesContent": ["/**\n * EdgeCaseHandler System Failures Tests\n * Focused on system failure scenarios and error handling\n * Optimized for fast execution\n */\n\nimport { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';\nimport { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';\nimport { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';\nimport { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';\n\n// Shared mock factories for performance\nconst createMockServices = () => ({\n  mockAssessmentEngine: {\n    createAssessment: jest.fn(),\n    generateQuestions: jest.fn(),\n    submitResponse: jest.fn(),\n    calculateResults: jest.fn(),\n    getAssessment: jest.fn(),\n    getAssessmentsByUser: jest.fn(),\n  } as any,\n  mockMarketDataService: {\n    getSkillMarketData: jest.fn(),\n    getMultipleSkillsMarketData: jest.fn(),\n    analyzeMarketTrends: jest.fn(),\n    getSalaryInsights: jest.fn(),\n    getLocationBasedMarketData: jest.fn(),\n    getMarketBasedRecommendations: jest.fn(),\n  } as any,\n  mockLearningPathService: {\n    generateLearningPath: jest.fn(),\n    updateProgress: jest.fn(),\n    completeMilestone: jest.fn(),\n  } as any,\n});\n\ndescribe('EdgeCaseHandler - System Failures', () => {\n  let edgeCaseHandler: EdgeCaseHandler;\n  let mocks: ReturnType<typeof createMockServices>;\n\n  beforeEach(() => {\n    mocks = createMockServices();\n    edgeCaseHandler = new EdgeCaseHandler(\n      mocks.mockAssessmentEngine,\n      mocks.mockMarketDataService,\n      mocks.mockLearningPathService\n    );\n  });\n\n  describe('Database Failures', () => {\n    it('should handle database connection failures', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        throw new Error('Database connection failed');\n      });\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Database connection failed');\n      expect(result.errorType).toBe('SYSTEM_ERROR');\n      expect(result.retryable).toBe(true);\n      expect(result.fallbackData).toBeDefined();\n    }, 5000);\n\n    it('should handle database timeout errors', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        throw new Error('Query timeout');\n      });\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Query timeout');\n      expect(result.errorType).toBe('TIMEOUT_ERROR');\n      expect(result.retryable).toBe(true);\n    }, 5000);\n  });\n\n  describe('AI Service Failures', () => {\n    it('should handle AI service timeouts', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 500,\n      };\n\n      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {\n        throw new Error('AI service timeout');\n      });\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('AI service timeout');\n      expect(result.errorType).toBe('TIMEOUT_ERROR');\n      expect(result.retryable).toBe(true);\n      expect(result.fallbackData).toBeDefined();\n    }, 5000);\n\n    it('should handle AI service rate limiting', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 500,\n      };\n\n      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {\n        throw new Error('Rate limit exceeded');\n      });\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Rate limit exceeded');\n      expect(result.errorType).toBe('AI_SERVICE_ERROR');\n      expect(result.retryAfter).toBeDefined();\n      expect(result.fallbackData).toBeDefined();\n    }, 5000);\n  });\n\n  describe('Memory and Performance Limits', () => {\n    it('should handle memory exhaustion gracefully', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: Array(1000).fill('skill'), // Reduced from 100000 for performance\n        careerPathId: 'path-456',\n      };\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Values exceed maximum thresholds');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n      expect(result.suggestedOptimizations).toBeDefined();\n    }, 5000);\n\n    it('should handle processing timeouts', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: Array(100).fill({ skill: 'javascript', level: 5, confidence: 6 }), // Reduced from 1000\n        targetRole: 'Full Stack Developer',\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 500,\n      };\n\n      // Mock a timeout operation with shorter delay for testing\n      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {\n        return new Promise((resolve) => {\n          setTimeout(() => resolve({} as any), 6000); // 6 second delay\n        });\n      });\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request, { timeout: 2000 });\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Processing timeout');\n      expect(result.errorType).toBe('TIMEOUT_ERROR');\n      expect(result.partialResults).toBeDefined();\n    }, 8000);\n  });\n\n  describe('Monitoring and Alerting', () => {\n    it('should log security incidents', async () => {\n      const maliciousRequest = {\n        userId: \"'; DROP TABLE users; --\",\n        skillIds: [\"'; DELETE FROM skills; --\"],\n        careerPathId: \"1' OR '1'='1\",\n      };\n\n      const logSpy = jest.spyOn(console, 'error').mockImplementation();\n\n      await edgeCaseHandler.handleSkillAssessment(maliciousRequest);\n\n      expect(logSpy).toHaveBeenCalledWith(\n        expect.stringContaining('SECURITY_ALERT'),\n        expect.objectContaining({\n          type: 'SQL_INJECTION_ATTEMPT',\n          userId: maliciousRequest.userId,\n          timestamp: expect.any(Date),\n        })\n      );\n\n      logSpy.mockRestore();\n    }, 3000);\n\n    it('should track error patterns', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        throw new Error('Database connection failed');\n      });\n\n      // Generate multiple errors with reduced iterations for performance\n      for (let i = 0; i < 3; i++) {\n        await edgeCaseHandler.handleSkillAssessment(request);\n      }\n\n      const errorStats = await edgeCaseHandler.getErrorStatistics();\n\n      expect(errorStats.totalErrors).toBeGreaterThanOrEqual(3);\n      expect(errorStats.errorsByType.SYSTEM_ERROR).toBeGreaterThanOrEqual(3);\n      expect(errorStats.mostCommonError).toBe('Database connection failed');\n    }, 8000);\n  });\n\n  describe('Performance Optimization Tests', () => {\n    it('should complete validation within performance threshold', async () => {\n      const startTime = Date.now();\n      \n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript', 'react', 'node'],\n        careerPathId: 'path-456',\n      };\n\n      await edgeCaseHandler.handleSkillAssessment(request);\n      \n      const duration = Date.now() - startTime;\n      expect(duration).toBeLessThan(3000); // Should complete within 3 seconds\n    }, 5000);\n\n    it('should handle multiple concurrent requests efficiently', async () => {\n      const startTime = Date.now();\n      \n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      // Reduced concurrent requests for performance\n      const promises = Array(3).fill(null).map((_, index) => \n        edgeCaseHandler.handleSkillAssessment({\n          ...request,\n          userId: `user-${index}`\n        })\n      );\n\n      await Promise.all(promises);\n      \n      const duration = Date.now() - startTime;\n      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds\n    }, 8000);\n\n    it('should maintain memory efficiency during processing', async () => {\n      const initialMemory = process.memoryUsage().heapUsed;\n      \n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      // Process multiple requests\n      for (let i = 0; i < 5; i++) {\n        await edgeCaseHandler.handleSkillAssessment({\n          ...request,\n          userId: `user-${i}`\n        });\n      }\n\n      const finalMemory = process.memoryUsage().heapUsed;\n      const memoryIncrease = finalMemory - initialMemory;\n      \n      // Memory increase should be reasonable (less than 50MB)\n      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);\n    }, 10000);\n  });\n});\n"], "version": 3}