1aee989d660921fd36c3c530435aad54
"use strict";

/* istanbul ignore next */
function cov_1pmv7qz2yk() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/ai-insights/route.ts";
  var hash = "d601435aa4c43f5f9b2e33da7b986f76601ee0c9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/ai-insights/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 53
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 18
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "73": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "74": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 76
        }
      },
      "75": {
        start: {
          line: 47,
          column: 15
        },
        end: {
          line: 47,
          column: 55
        }
      },
      "76": {
        start: {
          line: 48,
          column: 36
        },
        end: {
          line: 48,
          column: 80
        }
      },
      "77": {
        start: {
          line: 49,
          column: 34
        },
        end: {
          line: 49,
          column: 76
        }
      },
      "78": {
        start: {
          line: 50,
          column: 14
        },
        end: {
          line: 50,
          column: 36
        }
      },
      "79": {
        start: {
          line: 51,
          column: 18
        },
        end: {
          line: 51,
          column: 44
        }
      },
      "80": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 26
        }
      },
      "81": {
        start: {
          line: 54,
          column: 30
        },
        end: {
          line: 60,
          column: 2
        }
      },
      "82": {
        start: {
          line: 62,
          column: 26
        },
        end: {
          line: 66,
          column: 1
        }
      },
      "83": {
        start: {
          line: 67,
          column: 0
        },
        end: {
          line: 235,
          column: 7
        }
      },
      "84": {
        start: {
          line: 67,
          column: 99
        },
        end: {
          line: 235,
          column: 3
        }
      },
      "85": {
        start: {
          line: 68,
          column: 17
        },
        end: {
          line: 68,
          column: 26
        }
      },
      "86": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 234,
          column: 7
        }
      },
      "87": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 233,
          column: 20
        }
      },
      "88": {
        start: {
          line: 70,
          column: 105
        },
        end: {
          line: 233,
          column: 15
        }
      },
      "89": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 232,
          column: 19
        }
      },
      "90": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 231,
          column: 21
        }
      },
      "91": {
        start: {
          line: 75,
          column: 32
        },
        end: {
          line: 75,
          column: 108
        }
      },
      "92": {
        start: {
          line: 77,
          column: 28
        },
        end: {
          line: 77,
          column: 48
        }
      },
      "93": {
        start: {
          line: 78,
          column: 28
        },
        end: {
          line: 78,
          column: 57
        }
      },
      "94": {
        start: {
          line: 80,
          column: 28
        },
        end: {
          line: 80,
          column: 58
        }
      },
      "95": {
        start: {
          line: 82,
          column: 28
        },
        end: {
          line: 86,
          column: 29
        }
      },
      "96": {
        start: {
          line: 83,
          column: 32
        },
        end: {
          line: 83,
          column: 77
        }
      },
      "97": {
        start: {
          line: 84,
          column: 32
        },
        end: {
          line: 84,
          column: 55
        }
      },
      "98": {
        start: {
          line: 85,
          column: 32
        },
        end: {
          line: 85,
          column: 44
        }
      },
      "99": {
        start: {
          line: 88,
          column: 28
        },
        end: {
          line: 96,
          column: 29
        }
      },
      "100": {
        start: {
          line: 89,
          column: 32
        },
        end: {
          line: 89,
          column: 94
        }
      },
      "101": {
        start: {
          line: 92,
          column: 32
        },
        end: {
          line: 92,
          column: 80
        }
      },
      "102": {
        start: {
          line: 93,
          column: 32
        },
        end: {
          line: 93,
          column: 55
        }
      },
      "103": {
        start: {
          line: 94,
          column: 32
        },
        end: {
          line: 94,
          column: 129
        }
      },
      "104": {
        start: {
          line: 95,
          column: 32
        },
        end: {
          line: 95,
          column: 44
        }
      },
      "105": {
        start: {
          line: 98,
          column: 28
        },
        end: {
          line: 104,
          column: 29
        }
      },
      "106": {
        start: {
          line: 99,
          column: 32
        },
        end: {
          line: 99,
          column: 93
        }
      },
      "107": {
        start: {
          line: 100,
          column: 32
        },
        end: {
          line: 100,
          column: 55
        }
      },
      "108": {
        start: {
          line: 101,
          column: 32
        },
        end: {
          line: 101,
          column: 54
        }
      },
      "109": {
        start: {
          line: 102,
          column: 32
        },
        end: {
          line: 102,
          column: 55
        }
      },
      "110": {
        start: {
          line: 103,
          column: 32
        },
        end: {
          line: 103,
          column: 44
        }
      },
      "111": {
        start: {
          line: 105,
          column: 28
        },
        end: {
          line: 105,
          column: 115
        }
      },
      "112": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 106,
          column: 41
        }
      },
      "113": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 108,
          column: 54
        }
      },
      "114": {
        start: {
          line: 109,
          column: 28
        },
        end: {
          line: 109,
          column: 78
        }
      },
      "115": {
        start: {
          line: 111,
          column: 28
        },
        end: {
          line: 111,
          column: 55
        }
      },
      "116": {
        start: {
          line: 112,
          column: 28
        },
        end: {
          line: 124,
          column: 29
        }
      },
      "117": {
        start: {
          line: 113,
          column: 32
        },
        end: {
          line: 113,
          column: 68
        }
      },
      "118": {
        start: {
          line: 115,
          column: 32
        },
        end: {
          line: 123,
          column: 33
        }
      },
      "119": {
        start: {
          line: 116,
          column: 36
        },
        end: {
          line: 122,
          column: 44
        }
      },
      "120": {
        start: {
          line: 125,
          column: 28
        },
        end: {
          line: 125,
          column: 52
        }
      },
      "121": {
        start: {
          line: 127,
          column: 28
        },
        end: {
          line: 127,
          column: 53
        }
      },
      "122": {
        start: {
          line: 128,
          column: 28
        },
        end: {
          line: 128,
          column: 82
        }
      },
      "123": {
        start: {
          line: 129,
          column: 28
        },
        end: {
          line: 129,
          column: 52
        }
      },
      "124": {
        start: {
          line: 130,
          column: 32
        },
        end: {
          line: 145,
          column: 32
        }
      },
      "125": {
        start: {
          line: 147,
          column: 28
        },
        end: {
          line: 147,
          column: 51
        }
      },
      "126": {
        start: {
          line: 148,
          column: 28
        },
        end: {
          line: 153,
          column: 29
        }
      },
      "127": {
        start: {
          line: 149,
          column: 32
        },
        end: {
          line: 149,
          column: 91
        }
      },
      "128": {
        start: {
          line: 150,
          column: 32
        },
        end: {
          line: 150,
          column: 55
        }
      },
      "129": {
        start: {
          line: 151,
          column: 32
        },
        end: {
          line: 151,
          column: 68
        }
      },
      "130": {
        start: {
          line: 152,
          column: 32
        },
        end: {
          line: 152,
          column: 44
        }
      },
      "131": {
        start: {
          line: 155,
          column: 28
        },
        end: {
          line: 162,
          column: 29
        }
      },
      "132": {
        start: {
          line: 156,
          column: 32
        },
        end: {
          line: 156,
          column: 98
        }
      },
      "133": {
        start: {
          line: 157,
          column: 32
        },
        end: {
          line: 157,
          column: 55
        }
      },
      "134": {
        start: {
          line: 158,
          column: 32
        },
        end: {
          line: 158,
          column: 65
        }
      },
      "135": {
        start: {
          line: 159,
          column: 32
        },
        end: {
          line: 159,
          column: 60
        }
      },
      "136": {
        start: {
          line: 160,
          column: 32
        },
        end: {
          line: 160,
          column: 139
        }
      },
      "137": {
        start: {
          line: 161,
          column: 32
        },
        end: {
          line: 161,
          column: 44
        }
      },
      "138": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 167,
          column: 29
        }
      },
      "139": {
        start: {
          line: 164,
          column: 32
        },
        end: {
          line: 164,
          column: 81
        }
      },
      "140": {
        start: {
          line: 165,
          column: 32
        },
        end: {
          line: 165,
          column: 55
        }
      },
      "141": {
        start: {
          line: 166,
          column: 32
        },
        end: {
          line: 166,
          column: 44
        }
      },
      "142": {
        start: {
          line: 168,
          column: 28
        },
        end: {
          line: 168,
          column: 46
        }
      },
      "143": {
        start: {
          line: 169,
          column: 28
        },
        end: {
          line: 179,
          column: 31
        }
      },
      "144": {
        start: {
          line: 170,
          column: 32
        },
        end: {
          line: 178,
          column: 33
        }
      },
      "145": {
        start: {
          line: 171,
          column: 48
        },
        end: {
          line: 173,
          column: 62
        }
      },
      "146": {
        start: {
          line: 174,
          column: 36
        },
        end: {
          line: 174,
          column: 79
        }
      },
      "147": {
        start: {
          line: 177,
          column: 36
        },
        end: {
          line: 177,
          column: 94
        }
      },
      "148": {
        start: {
          line: 180,
          column: 28
        },
        end: {
          line: 180,
          column: 156
        }
      },
      "149": {
        start: {
          line: 182,
          column: 28
        },
        end: {
          line: 182,
          column: 56
        }
      },
      "150": {
        start: {
          line: 184,
          column: 28
        },
        end: {
          line: 184,
          column: 138
        }
      },
      "151": {
        start: {
          line: 185,
          column: 28
        },
        end: {
          line: 185,
          column: 51
        }
      },
      "152": {
        start: {
          line: 186,
          column: 28
        },
        end: {
          line: 186,
          column: 41
        }
      },
      "153": {
        start: {
          line: 188,
          column: 28
        },
        end: {
          line: 188,
          column: 56
        }
      },
      "154": {
        start: {
          line: 189,
          column: 28
        },
        end: {
          line: 191,
          column: 31
        }
      },
      "155": {
        start: {
          line: 190,
          column: 32
        },
        end: {
          line: 190,
          column: 126
        }
      },
      "156": {
        start: {
          line: 190,
          column: 57
        },
        end: {
          line: 190,
          column: 107
        }
      },
      "157": {
        start: {
          line: 192,
          column: 28
        },
        end: {
          line: 192,
          column: 241
        }
      },
      "158": {
        start: {
          line: 193,
          column: 28
        },
        end: {
          line: 193,
          column: 102
        }
      },
      "159": {
        start: {
          line: 195,
          column: 28
        },
        end: {
          line: 195,
          column: 51
        }
      },
      "160": {
        start: {
          line: 196,
          column: 28
        },
        end: {
          line: 196,
          column: 53
        }
      },
      "161": {
        start: {
          line: 198,
          column: 28
        },
        end: {
          line: 198,
          column: 50
        }
      },
      "162": {
        start: {
          line: 199,
          column: 28
        },
        end: {
          line: 199,
          column: 86
        }
      },
      "163": {
        start: {
          line: 201,
          column: 28
        },
        end: {
          line: 208,
          column: 53
        }
      },
      "164": {
        start: {
          line: 210,
          column: 28
        },
        end: {
          line: 210,
          column: 68
        }
      },
      "165": {
        start: {
          line: 211,
          column: 28
        },
        end: {
          line: 211,
          column: 170
        }
      },
      "166": {
        start: {
          line: 212,
          column: 28
        },
        end: {
          line: 212,
          column: 42
        }
      },
      "167": {
        start: {
          line: 214,
          column: 28
        },
        end: {
          line: 214,
          column: 57
        }
      },
      "168": {
        start: {
          line: 215,
          column: 28
        },
        end: {
          line: 215,
          column: 113
        }
      },
      "169": {
        start: {
          line: 217,
          column: 28
        },
        end: {
          line: 217,
          column: 38
        }
      },
      "170": {
        start: {
          line: 218,
          column: 28
        },
        end: {
          line: 218,
          column: 53
        }
      },
      "171": {
        start: {
          line: 220,
          column: 28
        },
        end: {
          line: 220,
          column: 53
        }
      },
      "172": {
        start: {
          line: 221,
          column: 28
        },
        end: {
          line: 221,
          column: 88
        }
      },
      "173": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 53
        }
      },
      "174": {
        start: {
          line: 223,
          column: 33
        },
        end: {
          line: 230,
          column: 32
        }
      },
      "175": {
        start: {
          line: 237,
          column: 0
        },
        end: {
          line: 320,
          column: 7
        }
      },
      "176": {
        start: {
          line: 237,
          column: 100
        },
        end: {
          line: 320,
          column: 3
        }
      },
      "177": {
        start: {
          line: 240,
          column: 17
        },
        end: {
          line: 240,
          column: 26
        }
      },
      "178": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 319,
          column: 7
        }
      },
      "179": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 318,
          column: 9
        }
      },
      "180": {
        start: {
          line: 243,
          column: 20
        },
        end: {
          line: 243,
          column: 96
        }
      },
      "181": {
        start: {
          line: 245,
          column: 16
        },
        end: {
          line: 245,
          column: 36
        }
      },
      "182": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 246,
          column: 45
        }
      },
      "183": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 248,
          column: 46
        }
      },
      "184": {
        start: {
          line: 249,
          column: 16
        },
        end: {
          line: 249,
          column: 53
        }
      },
      "185": {
        start: {
          line: 251,
          column: 16
        },
        end: {
          line: 251,
          column: 33
        }
      },
      "186": {
        start: {
          line: 252,
          column: 16
        },
        end: {
          line: 256,
          column: 17
        }
      },
      "187": {
        start: {
          line: 253,
          column: 20
        },
        end: {
          line: 253,
          column: 65
        }
      },
      "188": {
        start: {
          line: 254,
          column: 20
        },
        end: {
          line: 254,
          column: 43
        }
      },
      "189": {
        start: {
          line: 255,
          column: 20
        },
        end: {
          line: 255,
          column: 32
        }
      },
      "190": {
        start: {
          line: 257,
          column: 16
        },
        end: {
          line: 261,
          column: 17
        }
      },
      "191": {
        start: {
          line: 258,
          column: 20
        },
        end: {
          line: 258,
          column: 72
        }
      },
      "192": {
        start: {
          line: 259,
          column: 20
        },
        end: {
          line: 259,
          column: 43
        }
      },
      "193": {
        start: {
          line: 260,
          column: 20
        },
        end: {
          line: 260,
          column: 32
        }
      },
      "194": {
        start: {
          line: 262,
          column: 16
        },
        end: {
          line: 262,
          column: 299
        }
      },
      "195": {
        start: {
          line: 263,
          column: 16
        },
        end: {
          line: 271,
          column: 24
        }
      },
      "196": {
        start: {
          line: 273,
          column: 16
        },
        end: {
          line: 273,
          column: 39
        }
      },
      "197": {
        start: {
          line: 274,
          column: 16
        },
        end: {
          line: 278,
          column: 17
        }
      },
      "198": {
        start: {
          line: 275,
          column: 20
        },
        end: {
          line: 275,
          column: 79
        }
      },
      "199": {
        start: {
          line: 276,
          column: 20
        },
        end: {
          line: 276,
          column: 43
        }
      },
      "200": {
        start: {
          line: 277,
          column: 20
        },
        end: {
          line: 277,
          column: 32
        }
      },
      "201": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 34
        }
      },
      "202": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 290,
          column: 19
        }
      },
      "203": {
        start: {
          line: 281,
          column: 20
        },
        end: {
          line: 289,
          column: 21
        }
      },
      "204": {
        start: {
          line: 282,
          column: 36
        },
        end: {
          line: 284,
          column: 50
        }
      },
      "205": {
        start: {
          line: 285,
          column: 24
        },
        end: {
          line: 285,
          column: 67
        }
      },
      "206": {
        start: {
          line: 288,
          column: 24
        },
        end: {
          line: 288,
          column: 82
        }
      },
      "207": {
        start: {
          line: 292,
          column: 16
        },
        end: {
          line: 297,
          column: 18
        }
      },
      "208": {
        start: {
          line: 298,
          column: 16
        },
        end: {
          line: 298,
          column: 144
        }
      },
      "209": {
        start: {
          line: 300,
          column: 16
        },
        end: {
          line: 300,
          column: 44
        }
      },
      "210": {
        start: {
          line: 301,
          column: 16
        },
        end: {
          line: 301,
          column: 229
        }
      },
      "211": {
        start: {
          line: 303,
          column: 16
        },
        end: {
          line: 303,
          column: 39
        }
      },
      "212": {
        start: {
          line: 304,
          column: 16
        },
        end: {
          line: 304,
          column: 129
        }
      },
      "213": {
        start: {
          line: 305,
          column: 16
        },
        end: {
          line: 305,
          column: 106
        }
      },
      "214": {
        start: {
          line: 307,
          column: 16
        },
        end: {
          line: 307,
          column: 26
        }
      },
      "215": {
        start: {
          line: 308,
          column: 16
        },
        end: {
          line: 308,
          column: 132
        }
      },
      "216": {
        start: {
          line: 309,
          column: 16
        },
        end: {
          line: 317,
          column: 24
        }
      },
      "217": {
        start: {
          line: 322,
          column: 0
        },
        end: {
          line: 352,
          column: 7
        }
      },
      "218": {
        start: {
          line: 322,
          column: 102
        },
        end: {
          line: 352,
          column: 3
        }
      },
      "219": {
        start: {
          line: 325,
          column: 17
        },
        end: {
          line: 325,
          column: 26
        }
      },
      "220": {
        start: {
          line: 326,
          column: 4
        },
        end: {
          line: 351,
          column: 7
        }
      },
      "221": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 350,
          column: 9
        }
      },
      "222": {
        start: {
          line: 328,
          column: 20
        },
        end: {
          line: 328,
          column: 96
        }
      },
      "223": {
        start: {
          line: 330,
          column: 16
        },
        end: {
          line: 330,
          column: 36
        }
      },
      "224": {
        start: {
          line: 331,
          column: 16
        },
        end: {
          line: 331,
          column: 45
        }
      },
      "225": {
        start: {
          line: 333,
          column: 16
        },
        end: {
          line: 333,
          column: 46
        }
      },
      "226": {
        start: {
          line: 334,
          column: 16
        },
        end: {
          line: 338,
          column: 17
        }
      },
      "227": {
        start: {
          line: 335,
          column: 20
        },
        end: {
          line: 335,
          column: 65
        }
      },
      "228": {
        start: {
          line: 336,
          column: 20
        },
        end: {
          line: 336,
          column: 43
        }
      },
      "229": {
        start: {
          line: 337,
          column: 20
        },
        end: {
          line: 337,
          column: 32
        }
      },
      "230": {
        start: {
          line: 339,
          column: 16
        },
        end: {
          line: 339,
          column: 92
        }
      },
      "231": {
        start: {
          line: 340,
          column: 16
        },
        end: {
          line: 340,
          column: 47
        }
      },
      "232": {
        start: {
          line: 341,
          column: 16
        },
        end: {
          line: 341,
          column: 129
        }
      },
      "233": {
        start: {
          line: 342,
          column: 16
        },
        end: {
          line: 349,
          column: 24
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 67,
            column: 72
          },
          end: {
            line: 67,
            column: 73
          }
        },
        loc: {
          start: {
            line: 67,
            column: 97
          },
          end: {
            line: 235,
            column: 5
          }
        },
        line: 67
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 67,
            column: 150
          },
          end: {
            line: 67,
            column: 151
          }
        },
        loc: {
          start: {
            line: 67,
            column: 173
          },
          end: {
            line: 235,
            column: 1
          }
        },
        line: 67
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 69,
            column: 29
          },
          end: {
            line: 69,
            column: 30
          }
        },
        loc: {
          start: {
            line: 69,
            column: 43
          },
          end: {
            line: 234,
            column: 5
          }
        },
        line: 69
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 70,
            column: 91
          },
          end: {
            line: 70,
            column: 92
          }
        },
        loc: {
          start: {
            line: 70,
            column: 103
          },
          end: {
            line: 233,
            column: 17
          }
        },
        line: 70
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 70,
            column: 146
          },
          end: {
            line: 70,
            column: 147
          }
        },
        loc: {
          start: {
            line: 70,
            column: 158
          },
          end: {
            line: 233,
            column: 13
          }
        },
        line: 70
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 73,
            column: 41
          },
          end: {
            line: 73,
            column: 42
          }
        },
        loc: {
          start: {
            line: 73,
            column: 55
          },
          end: {
            line: 232,
            column: 17
          }
        },
        line: 73
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 169,
            column: 57
          },
          end: {
            line: 169,
            column: 58
          }
        },
        loc: {
          start: {
            line: 169,
            column: 77
          },
          end: {
            line: 179,
            column: 29
          }
        },
        line: 169
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 189,
            column: 57
          },
          end: {
            line: 189,
            column: 58
          }
        },
        loc: {
          start: {
            line: 189,
            column: 78
          },
          end: {
            line: 191,
            column: 29
          }
        },
        line: 189
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 190,
            column: 43
          },
          end: {
            line: 190,
            column: 44
          }
        },
        loc: {
          start: {
            line: 190,
            column: 55
          },
          end: {
            line: 190,
            column: 109
          }
        },
        line: 190
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 237,
            column: 73
          },
          end: {
            line: 237,
            column: 74
          }
        },
        loc: {
          start: {
            line: 237,
            column: 98
          },
          end: {
            line: 320,
            column: 5
          }
        },
        line: 237
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 237,
            column: 151
          },
          end: {
            line: 237,
            column: 152
          }
        },
        loc: {
          start: {
            line: 237,
            column: 174
          },
          end: {
            line: 320,
            column: 1
          }
        },
        line: 237
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 241,
            column: 29
          },
          end: {
            line: 241,
            column: 30
          }
        },
        loc: {
          start: {
            line: 241,
            column: 43
          },
          end: {
            line: 319,
            column: 5
          }
        },
        line: 241
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 280,
            column: 45
          },
          end: {
            line: 280,
            column: 46
          }
        },
        loc: {
          start: {
            line: 280,
            column: 65
          },
          end: {
            line: 290,
            column: 17
          }
        },
        line: 280
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 322,
            column: 75
          },
          end: {
            line: 322,
            column: 76
          }
        },
        loc: {
          start: {
            line: 322,
            column: 100
          },
          end: {
            line: 352,
            column: 5
          }
        },
        line: 322
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 322,
            column: 153
          },
          end: {
            line: 322,
            column: 154
          }
        },
        loc: {
          start: {
            line: 322,
            column: 176
          },
          end: {
            line: 352,
            column: 1
          }
        },
        line: 322
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 326,
            column: 29
          },
          end: {
            line: 326,
            column: 30
          }
        },
        loc: {
          start: {
            line: 326,
            column: 43
          },
          end: {
            line: 351,
            column: 5
          }
        },
        line: 326
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 231,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 75,
            column: 24
          },
          end: {
            line: 75,
            column: 108
          }
        }, {
          start: {
            line: 76,
            column: 24
          },
          end: {
            line: 78,
            column: 57
          }
        }, {
          start: {
            line: 79,
            column: 24
          },
          end: {
            line: 106,
            column: 41
          }
        }, {
          start: {
            line: 107,
            column: 24
          },
          end: {
            line: 109,
            column: 78
          }
        }, {
          start: {
            line: 110,
            column: 24
          },
          end: {
            line: 125,
            column: 52
          }
        }, {
          start: {
            line: 126,
            column: 24
          },
          end: {
            line: 129,
            column: 52
          }
        }, {
          start: {
            line: 130,
            column: 24
          },
          end: {
            line: 145,
            column: 32
          }
        }, {
          start: {
            line: 146,
            column: 24
          },
          end: {
            line: 180,
            column: 156
          }
        }, {
          start: {
            line: 181,
            column: 24
          },
          end: {
            line: 186,
            column: 41
          }
        }, {
          start: {
            line: 187,
            column: 24
          },
          end: {
            line: 193,
            column: 102
          }
        }, {
          start: {
            line: 194,
            column: 24
          },
          end: {
            line: 196,
            column: 53
          }
        }, {
          start: {
            line: 197,
            column: 24
          },
          end: {
            line: 208,
            column: 53
          }
        }, {
          start: {
            line: 209,
            column: 24
          },
          end: {
            line: 212,
            column: 42
          }
        }, {
          start: {
            line: 213,
            column: 24
          },
          end: {
            line: 215,
            column: 113
          }
        }, {
          start: {
            line: 216,
            column: 24
          },
          end: {
            line: 218,
            column: 53
          }
        }, {
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 222,
            column: 53
          }
        }, {
          start: {
            line: 223,
            column: 24
          },
          end: {
            line: 230,
            column: 32
          }
        }],
        line: 74
      },
      "36": {
        loc: {
          start: {
            line: 82,
            column: 28
          },
          end: {
            line: 86,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 28
          },
          end: {
            line: 86,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "37": {
        loc: {
          start: {
            line: 82,
            column: 34
          },
          end: {
            line: 82,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 82,
            column: 132
          },
          end: {
            line: 82,
            column: 138
          }
        }, {
          start: {
            line: 82,
            column: 141
          },
          end: {
            line: 82,
            column: 146
          }
        }],
        line: 82
      },
      "38": {
        loc: {
          start: {
            line: 82,
            column: 34
          },
          end: {
            line: 82,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 34
          },
          end: {
            line: 82,
            column: 112
          }
        }, {
          start: {
            line: 82,
            column: 116
          },
          end: {
            line: 82,
            column: 129
          }
        }],
        line: 82
      },
      "39": {
        loc: {
          start: {
            line: 82,
            column: 40
          },
          end: {
            line: 82,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 82,
            column: 81
          },
          end: {
            line: 82,
            column: 87
          }
        }, {
          start: {
            line: 82,
            column: 90
          },
          end: {
            line: 82,
            column: 102
          }
        }],
        line: 82
      },
      "40": {
        loc: {
          start: {
            line: 82,
            column: 40
          },
          end: {
            line: 82,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 40
          },
          end: {
            line: 82,
            column: 56
          }
        }, {
          start: {
            line: 82,
            column: 60
          },
          end: {
            line: 82,
            column: 78
          }
        }],
        line: 82
      },
      "41": {
        loc: {
          start: {
            line: 94,
            column: 48
          },
          end: {
            line: 94,
            column: 128
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 94,
            column: 94
          },
          end: {
            line: 94,
            column: 116
          }
        }, {
          start: {
            line: 94,
            column: 119
          },
          end: {
            line: 94,
            column: 128
          }
        }],
        line: 94
      },
      "42": {
        loc: {
          start: {
            line: 98,
            column: 28
          },
          end: {
            line: 104,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 28
          },
          end: {
            line: 104,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "43": {
        loc: {
          start: {
            line: 112,
            column: 28
          },
          end: {
            line: 124,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 28
          },
          end: {
            line: 124,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "44": {
        loc: {
          start: {
            line: 112,
            column: 32
          },
          end: {
            line: 112,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 32
          },
          end: {
            line: 112,
            column: 46
          }
        }, {
          start: {
            line: 112,
            column: 50
          },
          end: {
            line: 112,
            column: 84
          }
        }],
        line: 112
      },
      "45": {
        loc: {
          start: {
            line: 115,
            column: 32
          },
          end: {
            line: 123,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 32
          },
          end: {
            line: 123,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "46": {
        loc: {
          start: {
            line: 115,
            column: 36
          },
          end: {
            line: 115,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 36
          },
          end: {
            line: 115,
            column: 42
          }
        }, {
          start: {
            line: 115,
            column: 46
          },
          end: {
            line: 115,
            column: 72
          }
        }, {
          start: {
            line: 115,
            column: 76
          },
          end: {
            line: 115,
            column: 102
          }
        }],
        line: 115
      },
      "47": {
        loc: {
          start: {
            line: 121,
            column: 57
          },
          end: {
            line: 121,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 121,
            column: 57
          },
          end: {
            line: 121,
            column: 75
          }
        }, {
          start: {
            line: 121,
            column: 79
          },
          end: {
            line: 121,
            column: 103
          }
        }],
        line: 121
      },
      "48": {
        loc: {
          start: {
            line: 148,
            column: 28
          },
          end: {
            line: 153,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 28
          },
          end: {
            line: 153,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 148
      },
      "49": {
        loc: {
          start: {
            line: 155,
            column: 28
          },
          end: {
            line: 162,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 28
          },
          end: {
            line: 162,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "50": {
        loc: {
          start: {
            line: 155,
            column: 32
          },
          end: {
            line: 155,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 155,
            column: 32
          },
          end: {
            line: 155,
            column: 53
          }
        }, {
          start: {
            line: 155,
            column: 57
          },
          end: {
            line: 155,
            column: 88
          }
        }],
        line: 155
      },
      "51": {
        loc: {
          start: {
            line: 160,
            column: 57
          },
          end: {
            line: 160,
            column: 138
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 58
          },
          end: {
            line: 160,
            column: 132
          }
        }, {
          start: {
            line: 160,
            column: 137
          },
          end: {
            line: 160,
            column: 138
          }
        }],
        line: 160
      },
      "52": {
        loc: {
          start: {
            line: 160,
            column: 58
          },
          end: {
            line: 160,
            column: 132
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 160,
            column: 114
          },
          end: {
            line: 160,
            column: 120
          }
        }, {
          start: {
            line: 160,
            column: 123
          },
          end: {
            line: 160,
            column: 132
          }
        }],
        line: 160
      },
      "53": {
        loc: {
          start: {
            line: 160,
            column: 58
          },
          end: {
            line: 160,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 58
          },
          end: {
            line: 160,
            column: 94
          }
        }, {
          start: {
            line: 160,
            column: 98
          },
          end: {
            line: 160,
            column: 111
          }
        }],
        line: 160
      },
      "54": {
        loc: {
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 167,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 167,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "55": {
        loc: {
          start: {
            line: 171,
            column: 48
          },
          end: {
            line: 173,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 172,
            column: 42
          },
          end: {
            line: 172,
            column: 74
          }
        }, {
          start: {
            line: 173,
            column: 42
          },
          end: {
            line: 173,
            column: 62
          }
        }],
        line: 171
      },
      "56": {
        loc: {
          start: {
            line: 207,
            column: 45
          },
          end: {
            line: 207,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 207,
            column: 74
          },
          end: {
            line: 207,
            column: 91
          }
        }, {
          start: {
            line: 207,
            column: 94
          },
          end: {
            line: 207,
            column: 109
          }
        }],
        line: 207
      },
      "57": {
        loc: {
          start: {
            line: 242,
            column: 8
          },
          end: {
            line: 318,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 243,
            column: 96
          }
        }, {
          start: {
            line: 244,
            column: 12
          },
          end: {
            line: 246,
            column: 45
          }
        }, {
          start: {
            line: 247,
            column: 12
          },
          end: {
            line: 249,
            column: 53
          }
        }, {
          start: {
            line: 250,
            column: 12
          },
          end: {
            line: 271,
            column: 24
          }
        }, {
          start: {
            line: 272,
            column: 12
          },
          end: {
            line: 298,
            column: 144
          }
        }, {
          start: {
            line: 299,
            column: 12
          },
          end: {
            line: 301,
            column: 229
          }
        }, {
          start: {
            line: 302,
            column: 12
          },
          end: {
            line: 305,
            column: 106
          }
        }, {
          start: {
            line: 306,
            column: 12
          },
          end: {
            line: 317,
            column: 24
          }
        }],
        line: 242
      },
      "58": {
        loc: {
          start: {
            line: 252,
            column: 16
          },
          end: {
            line: 256,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 16
          },
          end: {
            line: 256,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "59": {
        loc: {
          start: {
            line: 252,
            column: 22
          },
          end: {
            line: 252,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 252,
            column: 120
          },
          end: {
            line: 252,
            column: 126
          }
        }, {
          start: {
            line: 252,
            column: 129
          },
          end: {
            line: 252,
            column: 134
          }
        }],
        line: 252
      },
      "60": {
        loc: {
          start: {
            line: 252,
            column: 22
          },
          end: {
            line: 252,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 22
          },
          end: {
            line: 252,
            column: 100
          }
        }, {
          start: {
            line: 252,
            column: 104
          },
          end: {
            line: 252,
            column: 117
          }
        }],
        line: 252
      },
      "61": {
        loc: {
          start: {
            line: 252,
            column: 28
          },
          end: {
            line: 252,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 252,
            column: 69
          },
          end: {
            line: 252,
            column: 75
          }
        }, {
          start: {
            line: 252,
            column: 78
          },
          end: {
            line: 252,
            column: 90
          }
        }],
        line: 252
      },
      "62": {
        loc: {
          start: {
            line: 252,
            column: 28
          },
          end: {
            line: 252,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 28
          },
          end: {
            line: 252,
            column: 44
          }
        }, {
          start: {
            line: 252,
            column: 48
          },
          end: {
            line: 252,
            column: 66
          }
        }],
        line: 252
      },
      "63": {
        loc: {
          start: {
            line: 257,
            column: 16
          },
          end: {
            line: 261,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 16
          },
          end: {
            line: 261,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "64": {
        loc: {
          start: {
            line: 262,
            column: 51
          },
          end: {
            line: 262,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 262,
            column: 67
          },
          end: {
            line: 262,
            column: 69
          }
        }, {
          start: {
            line: 262,
            column: 72
          },
          end: {
            line: 262,
            column: 74
          }
        }],
        line: 262
      },
      "65": {
        loc: {
          start: {
            line: 262,
            column: 117
          },
          end: {
            line: 262,
            column: 148
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 262,
            column: 133
          },
          end: {
            line: 262,
            column: 143
          }
        }, {
          start: {
            line: 262,
            column: 146
          },
          end: {
            line: 262,
            column: 148
          }
        }],
        line: 262
      },
      "66": {
        loc: {
          start: {
            line: 262,
            column: 199
          },
          end: {
            line: 262,
            column: 224
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 262,
            column: 215
          },
          end: {
            line: 262,
            column: 219
          }
        }, {
          start: {
            line: 262,
            column: 222
          },
          end: {
            line: 262,
            column: 224
          }
        }],
        line: 262
      },
      "67": {
        loc: {
          start: {
            line: 262,
            column: 273
          },
          end: {
            line: 262,
            column: 298
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 262,
            column: 289
          },
          end: {
            line: 262,
            column: 293
          }
        }, {
          start: {
            line: 262,
            column: 296
          },
          end: {
            line: 262,
            column: 298
          }
        }],
        line: 262
      },
      "68": {
        loc: {
          start: {
            line: 274,
            column: 16
          },
          end: {
            line: 278,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 16
          },
          end: {
            line: 278,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "69": {
        loc: {
          start: {
            line: 282,
            column: 36
          },
          end: {
            line: 284,
            column: 50
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 283,
            column: 30
          },
          end: {
            line: 283,
            column: 62
          }
        }, {
          start: {
            line: 284,
            column: 30
          },
          end: {
            line: 284,
            column: 50
          }
        }],
        line: 282
      },
      "70": {
        loc: {
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 350,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 328,
            column: 12
          },
          end: {
            line: 328,
            column: 96
          }
        }, {
          start: {
            line: 329,
            column: 12
          },
          end: {
            line: 331,
            column: 45
          }
        }, {
          start: {
            line: 332,
            column: 12
          },
          end: {
            line: 349,
            column: 24
          }
        }],
        line: 327
      },
      "71": {
        loc: {
          start: {
            line: 334,
            column: 16
          },
          end: {
            line: 338,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 334,
            column: 16
          },
          end: {
            line: 338,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 334
      },
      "72": {
        loc: {
          start: {
            line: 334,
            column: 22
          },
          end: {
            line: 334,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 334,
            column: 120
          },
          end: {
            line: 334,
            column: 126
          }
        }, {
          start: {
            line: 334,
            column: 129
          },
          end: {
            line: 334,
            column: 134
          }
        }],
        line: 334
      },
      "73": {
        loc: {
          start: {
            line: 334,
            column: 22
          },
          end: {
            line: 334,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 334,
            column: 22
          },
          end: {
            line: 334,
            column: 100
          }
        }, {
          start: {
            line: 334,
            column: 104
          },
          end: {
            line: 334,
            column: 117
          }
        }],
        line: 334
      },
      "74": {
        loc: {
          start: {
            line: 334,
            column: 28
          },
          end: {
            line: 334,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 334,
            column: 69
          },
          end: {
            line: 334,
            column: 75
          }
        }, {
          start: {
            line: 334,
            column: 78
          },
          end: {
            line: 334,
            column: 90
          }
        }],
        line: 334
      },
      "75": {
        loc: {
          start: {
            line: 334,
            column: 28
          },
          end: {
            line: 334,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 334,
            column: 28
          },
          end: {
            line: 334,
            column: 44
          }
        }, {
          start: {
            line: 334,
            column: 48
          },
          end: {
            line: 334,
            column: 66
          }
        }],
        line: 334
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0, 0, 0, 0, 0, 0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/ai-insights/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,6EAAwF;AACxF,wDAAkC;AAClC,iFAAgF;AAChF,6EAA4E;AAE5E,qCAAoC;AACpC,6CAAiF;AACjF,2BAAwB;AAGxB,0BAA0B;AAC1B,IAAM,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,8BAA8B,CAAC;IAC7D,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1C,aAAa,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE;IACxE,iBAAiB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACzC,gBAAgB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACzC,CAAC,CAAC;AAEH,2CAA2C;AAC3C,IAAM,mBAAmB,GAAG;IAC1B,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IACvC,WAAW,EAAE,CAAC,EAAE,2CAA2C;IAC3D,OAAO,EAAE,6EAA6E;CACvF,CAAC;AAQW,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,qEAGzC,OAAO,YAFR,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAAC,OAAO,EAAE,mBAAmB,EAAE;;;;;gCACjC,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACtB,qBAAM,MAAM,EAAA;;4BAA7B,YAAY,GAAK,CAAA,SAAY,CAAA,GAAjB;4BAExB,uBAAuB;4BACvB,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;gCAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,mBAAmB;4BACnB,IAAI,CAAC;gCACH,uBAAuB,CAAC,KAAK,CAAC,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;4BAClD,CAAC;4BAAC,OAAO,eAAe,EAAE,CAAC;gCACnB,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAQ,CAAC;gCAC7D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,KAAK,CAAC,OAAO,GAAG,eAAe,YAAY,OAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;gCAC3F,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,qCAAqC;4BACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;gCACjC,KAAK,GAAG,IAAI,KAAK,CAAC,yCAAyC,CAAQ,CAAC;gCAC1E,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;gCACtB,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,YAAY;gCACpC,MAAM,KAAK,CAAC;4BACd,CAAC;4BAGO,QAAQ,GAAG,sBAAe,YAAY,cAAI,OAAO,CAAC,IAAI,CAAC,EAAE,YAAS,CAAC;;;;4BAGhD,qBAAM,aAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAA;;4BAA1C,cAAc,GAAG,SAAyB;4BAEhD,IAAI,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;gCACnD,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gCAE1C,iCAAiC;gCACjC,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;oCACvE,sBAAO,qBAAY,CAAC,IAAI,CAAC;4CACvB,OAAO,EAAE,IAAI;4CACb,IAAI,EAAE,MAAM;4CACZ,MAAM,EAAE,IAAI;4CACZ,OAAO,EAAE,kCAAkC;4CAC3C,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yCAC5D,CAAC,EAAC;gCACL,CAAC;4BACH,CAAC;;;;4BAED,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,YAAU,CAAC,CAAC;;gCAKnC,qBAAM,gBAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gCACnD,KAAK,EAAE;oCACL,EAAE,EAAE,YAAY;oCAChB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;iCACxB;gCACD,OAAO,EAAE;oCACP,SAAS,EAAE,IAAI;oCACf,IAAI,EAAE;wCACJ,MAAM,EAAE;4CACN,EAAE,EAAE,IAAI;4CACR,KAAK,EAAE,IAAI;4CACX,IAAI,EAAE,IAAI;yCACX;qCACF;iCACF;6BACF,CAAC,EAAA;;4BAfI,UAAU,GAAG,SAejB;4BAEJ,IAAI,CAAC,UAAU,EAAE,CAAC;gCACV,KAAK,GAAG,IAAI,KAAK,CAAC,uCAAuC,CAAQ,CAAC;gCACxE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;gCACpC,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,0CAA0C;4BAC1C,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCACvD,KAAK,GAAG,IAAI,KAAK,CAAC,8CAA8C,CAAQ,CAAC;gCAC/E,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,KAAK,CAAC,IAAI,GAAG,mBAAmB,CAAC;gCACjC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC;gCAC5B,KAAK,CAAC,gBAAgB,GAAG,CAAA,MAAA,UAAU,CAAC,SAAS,0CAAE,MAAM,KAAI,CAAC,CAAC;gCAC3D,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gCAChC,KAAK,GAAG,IAAI,KAAK,CAAC,6BAA6B,CAAQ,CAAC;gCAC9D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAGK,YAAY,GAAuB,EAAE,CAAC;4BAC5C,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;gCACnC,IAAI,CAAC;oCACH,IAAM,KAAK,GAAG,OAAO,QAAQ,CAAC,WAAW,KAAK,QAAQ;wCACpD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;wCAClC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;oCACzB,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,KAA0C,CAAC;gCAClF,CAAC;gCAAC,WAAM,CAAC;oCACP,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,WAAgD,CAAC;gCACjG,CAAC;4BACH,CAAC,CAAC,CAAC;4BAGqB,qBAAM,qDAAyB,CAAC,uBAAuB,CAC7E,YAAY,EACZ,YAAY,CACb,EAAA;;4BAHK,eAAe,GAAG,SAGvB;4BAEC,oDAAoD;4BACpD,OAAO,CAAC,GAAG,CAAC,0CAAmC,OAAO,CAAC,IAAI,CAAC,EAAE,0BAAgB,YAAY,CAAE,CAAC,CAAC;4BAExF,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;4BAKrB,cAAc,GAAG,IAAI,OAAO,CAAC,UAAC,CAAC,EAAE,MAAM;gCAC3C,UAAU,CAAC,cAAM,OAAA,MAAM,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,EAA1C,CAA0C,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;4BAC9E,CAAC,CAAC,CAAC;4BAEG,mBAAmB,GAAG,yDAA2B,CAAC,kBAAkB,CACxE,YAAY,EACZ,YAAY,EACZ,eAAe,CAAC,QAAQ,EACxB,eAAe,CAAC,yBAAyB,EACzC,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;4BAEW,qBAAM,OAAO,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC,EAAA;;4BAAtE,UAAU,GAAG,SAAyD,CAAC;;;;4BAGvE,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,SAAO,CAAC,CAAC;4BAExD,kDAAkD;4BAClD,sBAAO,qBAAY,CAAC,IAAI,CACtB;oCACE,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,+BAA+B;oCACtC,IAAI,EAAE,sBAAsB;oCAC5B,QAAQ,EAAE,IAAI;oCACd,UAAU,EAAE,EAAE,EAAE,WAAW;oCAC3B,OAAO,EAAE,SAAO,YAAY,KAAK,CAAC,CAAC,CAAC,SAAO,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iCACtE,EACD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;;4BAGE,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;4BAC9C,OAAO,CAAC,GAAG,CAAC,mCAA4B,cAAc,yBAAe,OAAO,CAAC,IAAI,CAAC,EAAE,0BAAgB,YAAY,CAAE,CAAC,CAAC;;;;4BAIlH,qBAAM,aAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,EAAA;;4BAA5D,SAA4D,CAAC;;;;4BAE7D,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,YAAU,CAAC,CAAC;;iCAI9D,sBAAO,qBAAY,CAAC,IAAI,CAAC;gCACvB,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE;oCACJ,QAAQ,EAAE,UAAU;oCACpB,MAAM,EAAE,KAAK;oCACb,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iCACtC;6BACF,CAAC,EAAC;;;iBACJ,CAA2D,EAAC;;KAC9D,CAAC,CAAC;AASH,oEAAoE;AACvD,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,qEAG1C,OAAO,YAFR,OAAoB,EACpB,EAA+C;;;QAA7C,MAAM,YAAA;;;oBAEQ,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACtB,qBAAM,MAAM,EAAA;;gBAA7B,YAAY,GAAK,CAAA,SAAY,CAAA,GAAjB;gBACX,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAA3B,IAAI,GAAG,SAAoB;gBAEjC,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACZ,CAAC;gBAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;oBACjC,KAAK,GAAG,IAAI,KAAK,CAAC,gCAAgC,CAAQ,CAAC;oBACjE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGC,KAIE,IAAI,WAJS,EAAf,UAAU,mBAAG,EAAE,KAAA,EACf,KAGE,IAAI,cAHoB,EAA1B,aAAa,mBAAG,UAAU,KAAA,EAC1B,KAEE,IAAI,kBAFkB,EAAxB,iBAAiB,mBAAG,IAAI,KAAA,EACxB,KACE,IAAI,iBADiB,EAAvB,gBAAgB,mBAAG,IAAI,KAAA,CAChB;gBAGU,qBAAM,gBAAM,CAAC,UAAU,CAAC,SAAS,CAAC;wBACnD,KAAK,EAAE;4BACL,EAAE,EAAE,YAAY;4BAChB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;yBACxB;wBACD,OAAO,EAAE;4BACP,SAAS,EAAE,IAAI;yBAChB;qBACF,CAAC,EAAA;;gBARI,UAAU,GAAG,SAQjB;gBAEF,IAAI,CAAC,UAAU,EAAE,CAAC;oBACV,KAAK,GAAG,IAAI,KAAK,CAAC,uCAAuC,CAAQ,CAAC;oBACxE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGK,YAAY,GAAuB,EAAE,CAAC;gBAC5C,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;oBACnC,IAAI,CAAC;wBACH,IAAM,KAAK,GAAG,OAAO,QAAQ,CAAC,WAAW,KAAK,QAAQ;4BACpD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;4BAClC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;wBACzB,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,KAA0C,CAAC;oBAClF,CAAC;oBAAC,WAAM,CAAC;wBACP,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,WAAgD,CAAC;oBACjG,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,wDAAwD;gBACvD,YAAoB,CAAC,cAAc,GAAG;oBACrC,UAAU,YAAA;oBACV,aAAa,eAAA;oBACb,iBAAiB,mBAAA;oBACjB,gBAAgB,kBAAA;iBACjB,CAAC;gBAGsB,qBAAM,qDAAyB,CAAC,uBAAuB,CAC7E,YAAY,EACZ,YAAY,CACb,EAAA;;gBAHK,eAAe,GAAG,SAGvB;gBAGkB,qBAAM,yDAA2B,CAAC,kBAAkB,CACrE,YAAY,EACZ,YAAY,EACZ,eAAe,CAAC,QAAQ,EACxB,eAAe,CAAC,yBAAyB,EACzC,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,EAAA;;gBANK,UAAU,GAAG,SAMlB;gBAGK,cAAc,GAAG,sBAAe,YAAY,cAAI,OAAO,CAAC,IAAI,CAAC,EAAE,qBAAW,IAAI,CAAC,GAAG,EAAE,CAAE,CAAC;gBAC7F,qBAAM,aAAK,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,EAAA;;gBAAjE,SAAiE,CAAC,CAAC,eAAe;gBAElF,OAAO,CAAC,GAAG,CAAC,gDAAyC,OAAO,CAAC,IAAI,CAAC,EAAE,0BAAgB,YAAY,CAAE,CAAC,CAAC;gBAEtG,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,QAAQ,EAAE,UAAU;4BACpB,WAAW,EAAE,IAAI;4BACjB,UAAU,YAAA;4BACV,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACtC;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAQH,6CAA6C;AAChC,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,qEAG5C,OAAO,YAFR,OAAoB,EACpB,EAA+C;;;QAA7C,MAAM,YAAA;;;oBAEQ,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACtB,qBAAM,MAAM,EAAA;;gBAA7B,YAAY,GAAK,CAAA,SAAY,CAAA,GAAjB;gBAExB,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGO,QAAQ,GAAG,sBAAe,YAAY,cAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAE,CAAC;gBAClE,aAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAEvB,OAAO,CAAC,GAAG,CAAC,6CAAsC,OAAO,CAAC,IAAI,CAAC,EAAE,0BAAgB,YAAY,CAAE,CAAC,CAAC;gBAEnG,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,OAAO,EAAE,IAAI;4BACb,YAAY,cAAA;4BACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/assessment/[id]/ai-insights/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport prisma from '@/lib/prisma';\nimport { aiEnhancedAssessmentService } from '@/lib/aiEnhancedAssessmentService';\nimport { EnhancedAssessmentService } from '@/lib/enhancedAssessmentService';\nimport { AssessmentResponse } from '@/lib/assessmentScoring';\nimport { cache } from '@/lib/cache';\nimport { withRateLimit, rateLimitConfigs, sanitizeInput } from '@/lib/rateLimit';\nimport { z } from 'zod';\nimport { withCSRFProtection } from '@/lib/csrf';\n\n// Input validation schema\nconst aiInsightsRequestSchema = z.object({\n  assessmentId: z.string().uuid('Invalid assessment ID format'),\n  focusAreas: z.array(z.string()).optional(),\n  analysisDepth: z.enum(['basic', 'standard', 'comprehensive']).optional(),\n  includeMarketData: z.boolean().optional(),\n  personalityFocus: z.boolean().optional(),\n});\n\n// Rate limit configuration for AI insights\nconst aiInsightsRateLimit = {\n  windowMs: 15 * 60 * 1000, // 15 minutes\n  maxRequests: 5, // 5 AI insights generations per 15 minutes\n  message: 'Too many AI insights requests. Please wait before generating more insights.',\n};\n\ninterface AIInsightsResponse {\n  insights: any;\n  cached: boolean;\n  generatedAt: string;\n}\n\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<AIInsightsResponse>>> => {\n  return withRateLimit(request, aiInsightsRateLimit, async () => {\n    const session = await getServerSession(authOptions);\n    const { id: assessmentId } = await params;\n\n    // Authentication check\n    if (!session?.user?.id) {\n      const error = new Error('Authentication required') as any;\n      error.statusCode = 401;\n      throw error;\n    }\n\n    // Input validation\n    try {\n      aiInsightsRequestSchema.parse({ assessmentId });\n    } catch (validationError) {\n      const error = new Error('Invalid request parameters') as any;\n      error.statusCode = 400;\n      error.details = validationError instanceof z.ZodError ? validationError.errors : undefined;\n      throw error;\n    }\n\n    // Check if AI services are available\n    if (!process.env.GOOGLE_GEMINI_API_KEY) {\n      const error = new Error('AI services are temporarily unavailable') as any;\n      error.statusCode = 503;\n      error.fallback = true;\n      error.retryAfter = 300; // 5 minutes\n      throw error;\n    }\n\n      // Check cache first with improved error handling\n      const cacheKey = `ai_insights:${assessmentId}:${session.user.id}:v1.0.0`;\n\n      try {\n        const cachedInsights = await cache.get(cacheKey);\n\n        if (cachedInsights && typeof cachedInsights === 'string') {\n          const parsed = JSON.parse(cachedInsights);\n\n          // Validate cached data structure\n          if (parsed && typeof parsed === 'object' && parsed.personalityAnalysis) {\n            return NextResponse.json({\n              success: true,\n              data: parsed,\n              cached: true,\n              message: 'AI insights retrieved from cache',\n              generatedAt: parsed.generatedAt || new Date().toISOString()\n            });\n          }\n        }\n      } catch (cacheError) {\n        console.error('Cache retrieval error:', cacheError);\n        // Continue to generate new insights if cache fails\n      }\n\n      // Verify assessment belongs to user with improved error handling\n      const assessment = await prisma.assessment.findFirst({\n        where: {\n          id: assessmentId,\n          userId: session.user.id\n        },\n        include: {\n          responses: true,\n          user: {\n            select: {\n              id: true,\n              email: true,\n              name: true\n            }\n          }\n        }\n      });\n\n    if (!assessment) {\n      const error = new Error('Assessment not found or access denied') as any;\n      error.statusCode = 404;\n      error.code = 'ASSESSMENT_NOT_FOUND';\n      throw error;\n    }\n\n    // Validate assessment has sufficient data\n    if (!assessment.responses || assessment.responses.length < 3) {\n      const error = new Error('Insufficient assessment data for AI analysis') as any;\n      error.statusCode = 400;\n      error.code = 'INSUFFICIENT_DATA';\n      error.requiredResponses = 3;\n      error.currentResponses = assessment.responses?.length || 0;\n      throw error;\n    }\n\n    if (assessment.status !== 'COMPLETED') {\n      const error = new Error('Assessment is not completed') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    // Convert assessment responses to the expected format\n    const responseData: AssessmentResponse = {};\n    assessment.responses.forEach(response => {\n      try {\n        const value = typeof response.answerValue === 'string' \n          ? JSON.parse(response.answerValue) \n          : response.answerValue;\n        responseData[response.questionKey] = value as string | string[] | number | null;\n      } catch {\n        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;\n      }\n    });\n\n    // Generate enhanced results first\n    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(\n      assessmentId,\n      responseData\n    );\n\n      // Generate AI insights with timeout and retry logic\n      console.log(`Generating AI insights for user ${session.user.id}, assessment ${assessmentId}`);\n\n      const startTime = Date.now();\n      let aiInsights;\n\n      try {\n        // Set timeout for AI generation (5 minutes)\n        const timeoutPromise = new Promise((_, reject) => {\n          setTimeout(() => reject(new Error('AI generation timeout')), 5 * 60 * 1000);\n        });\n\n        const aiGenerationPromise = aiEnhancedAssessmentService.generateAIInsights(\n          assessmentId,\n          responseData,\n          enhancedResults.insights,\n          enhancedResults.careerPathRecommendations,\n          session.user.id\n        );\n\n        aiInsights = await Promise.race([aiGenerationPromise, timeoutPromise]);\n\n      } catch (aiError) {\n        console.error('AI insights generation error:', aiError);\n\n        // Return fallback insights with error information\n        return NextResponse.json(\n          {\n            success: false,\n            error: 'AI insights generation failed',\n            code: 'AI_GENERATION_FAILED',\n            fallback: true,\n            retryAfter: 60, // 1 minute\n            details: aiError instanceof Error ? aiError.message : 'Unknown error'\n          },\n          { status: 500 }\n        );\n      }\n\n      const generationTime = Date.now() - startTime;\n      console.log(`AI insights generated in ${generationTime}ms for user ${session.user.id}, assessment ${assessmentId}`);\n\n      // Cache the results for 24 hours with error handling\n      try {\n        await cache.set(cacheKey, JSON.stringify(aiInsights), 86400);\n      } catch (cacheError) {\n        console.error('Failed to cache AI insights:', cacheError);\n        // Continue without caching\n      }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        insights: aiInsights,\n        cached: false,\n        generatedAt: new Date().toISOString()\n      }\n    });\n  }) as Promise<NextResponse<ApiResponse<AIInsightsResponse>>>;\n});\n\ninterface AIInsightsRegenerateResponse {\n  insights: any;\n  regenerated: boolean;\n  focusAreas: string[];\n  generatedAt: string;\n}\n\n// POST endpoint to regenerate AI insights with specific focus areas\nexport const POST = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<AIInsightsRegenerateResponse>>> => {\n  const session = await getServerSession(authOptions);\n  const { id: assessmentId } = await params;\n  const body = await request.json();\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n    }\n\n    if (!process.env.GOOGLE_GEMINI_API_KEY) {\n      const error = new Error('AI services are not configured') as any;\n      error.statusCode = 503;\n      throw error;\n    }\n\n    const {\n      focusAreas = [],\n      analysisDepth = 'standard',\n      includeMarketData = true,\n      personalityFocus = true\n    } = body;\n\n    // Verify assessment belongs to user\n    const assessment = await prisma.assessment.findFirst({\n      where: {\n        id: assessmentId,\n        userId: session.user.id\n      },\n      include: {\n        responses: true\n      }\n    });\n\n    if (!assessment) {\n      const error = new Error('Assessment not found or access denied') as any;\n      error.statusCode = 404;\n      throw error;\n    }\n\n    // Convert assessment responses\n    const responseData: AssessmentResponse = {};\n    assessment.responses.forEach(response => {\n      try {\n        const value = typeof response.answerValue === 'string' \n          ? JSON.parse(response.answerValue) \n          : response.answerValue;\n        responseData[response.questionKey] = value as string | string[] | number | null;\n      } catch {\n        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;\n      }\n    });\n\n    // Add user preferences to response data for AI analysis\n    (responseData as any).ai_preferences = {\n      focusAreas,\n      analysisDepth,\n      includeMarketData,\n      personalityFocus\n    };\n\n    // Generate enhanced results with preferences\n    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(\n      assessmentId,\n      responseData\n    );\n\n    // Generate AI insights with custom focus\n    const aiInsights = await aiEnhancedAssessmentService.generateAIInsights(\n      assessmentId,\n      responseData,\n      enhancedResults.insights,\n      enhancedResults.careerPathRecommendations,\n      session.user.id\n    );\n\n    // Cache with custom key for preferences\n    const customCacheKey = `ai_insights:${assessmentId}:${session.user.id}:custom:${Date.now()}`;\n    await cache.set(customCacheKey, JSON.stringify(aiInsights), 3600); // 1 hour cache\n\n    console.log(`Custom AI insights generated for user ${session.user.id}, assessment ${assessmentId}`);\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      insights: aiInsights,\n      regenerated: true,\n      focusAreas,\n      generatedAt: new Date().toISOString()\n    }\n  });\n});\n\ninterface CacheClearResponse {\n  cleared: boolean;\n  assessmentId: string;\n  clearedAt: string;\n}\n\n// DELETE endpoint to clear AI insights cache\nexport const DELETE = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n): Promise<NextResponse<ApiResponse<CacheClearResponse>>> => {\n  const session = await getServerSession(authOptions);\n  const { id: assessmentId } = await params;\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n    // Clear cache for this assessment\n    const cacheKey = `ai_insights:${assessmentId}:${session.user.id}`;\n    cache.delete(cacheKey);\n\n    console.log(`AI insights cache cleared for user ${session.user.id}, assessment ${assessmentId}`);\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      cleared: true,\n      assessmentId,\n      clearedAt: new Date().toISOString()\n    }\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d601435aa4c43f5f9b2e33da7b986f76601ee0c9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1pmv7qz2yk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1pmv7qz2yk();
var __awaiter =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[0]++,
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1pmv7qz2yk().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[1]++;
    cov_1pmv7qz2yk().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[2]++;
      cov_1pmv7qz2yk().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1pmv7qz2yk().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1pmv7qz2yk().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1pmv7qz2yk().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[4]++;
      cov_1pmv7qz2yk().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[5]++;
      cov_1pmv7qz2yk().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[6]++;
      cov_1pmv7qz2yk().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1pmv7qz2yk().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1pmv7qz2yk().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1pmv7qz2yk().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[12]++,
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1pmv7qz2yk().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().f[8]++;
        cov_1pmv7qz2yk().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[6][0]++;
          cov_1pmv7qz2yk().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1pmv7qz2yk().b[6][1]++;
        }
        cov_1pmv7qz2yk().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1pmv7qz2yk().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1pmv7qz2yk().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1pmv7qz2yk().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[9]++;
    cov_1pmv7qz2yk().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[10]++;
    cov_1pmv7qz2yk().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[11]++;
      cov_1pmv7qz2yk().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[12]++;
    cov_1pmv7qz2yk().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().b[9][0]++;
      cov_1pmv7qz2yk().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1pmv7qz2yk().b[9][1]++;
    }
    cov_1pmv7qz2yk().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[15][0]++,
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[16][1]++,
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1pmv7qz2yk().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[12][0]++;
          cov_1pmv7qz2yk().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1pmv7qz2yk().b[12][1]++;
        }
        cov_1pmv7qz2yk().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[18][0]++;
          cov_1pmv7qz2yk().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1pmv7qz2yk().b[18][1]++;
        }
        cov_1pmv7qz2yk().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[19][1]++;
            cov_1pmv7qz2yk().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[19][2]++;
            cov_1pmv7qz2yk().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[19][3]++;
            cov_1pmv7qz2yk().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[19][4]++;
            cov_1pmv7qz2yk().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[19][5]++;
            cov_1pmv7qz2yk().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1pmv7qz2yk().b[20][0]++;
              cov_1pmv7qz2yk().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1pmv7qz2yk().b[20][1]++;
            }
            cov_1pmv7qz2yk().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1pmv7qz2yk().b[23][0]++;
              cov_1pmv7qz2yk().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pmv7qz2yk().b[23][1]++;
            }
            cov_1pmv7qz2yk().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1pmv7qz2yk().b[25][0]++;
              cov_1pmv7qz2yk().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pmv7qz2yk().b[25][1]++;
            }
            cov_1pmv7qz2yk().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1pmv7qz2yk().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1pmv7qz2yk().b[27][0]++;
              cov_1pmv7qz2yk().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pmv7qz2yk().b[27][1]++;
            }
            cov_1pmv7qz2yk().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1pmv7qz2yk().b[29][0]++;
              cov_1pmv7qz2yk().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1pmv7qz2yk().b[29][1]++;
            }
            cov_1pmv7qz2yk().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1pmv7qz2yk().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().b[30][0]++;
      cov_1pmv7qz2yk().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1pmv7qz2yk().b[30][1]++;
    }
    cov_1pmv7qz2yk().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1pmv7qz2yk().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1pmv7qz2yk().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[67]++,
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1pmv7qz2yk().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1pmv7qz2yk().f[13]++;
  cov_1pmv7qz2yk().s[68]++;
  return /* istanbul ignore next */(cov_1pmv7qz2yk().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1pmv7qz2yk().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1pmv7qz2yk().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_1pmv7qz2yk().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1pmv7qz2yk().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1pmv7qz2yk().s[70]++;
exports.DELETE = exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[71]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[72]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[73]++, require("@/lib/auth"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[74]++, require("@/lib/unified-api-error-handler"));
var prisma_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[75]++, __importDefault(require("@/lib/prisma")));
var aiEnhancedAssessmentService_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[76]++, require("@/lib/aiEnhancedAssessmentService"));
var enhancedAssessmentService_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[77]++, require("@/lib/enhancedAssessmentService"));
var cache_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[78]++, require("@/lib/cache"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[79]++, require("@/lib/rateLimit"));
var zod_1 =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[80]++, require("zod"));
// Input validation schema
var aiInsightsRequestSchema =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[81]++, zod_1.z.object({
  assessmentId: zod_1.z.string().uuid('Invalid assessment ID format'),
  focusAreas: zod_1.z.array(zod_1.z.string()).optional(),
  analysisDepth: zod_1.z.enum(['basic', 'standard', 'comprehensive']).optional(),
  includeMarketData: zod_1.z.boolean().optional(),
  personalityFocus: zod_1.z.boolean().optional()
}));
// Rate limit configuration for AI insights
var aiInsightsRateLimit =
/* istanbul ignore next */
(cov_1pmv7qz2yk().s[82]++, {
  windowMs: 15 * 60 * 1000,
  // 15 minutes
  maxRequests: 5,
  // 5 AI insights generations per 15 minutes
  message: 'Too many AI insights requests. Please wait before generating more insights.'
});
/* istanbul ignore next */
cov_1pmv7qz2yk().s[83]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1pmv7qz2yk().f[14]++;
  cov_1pmv7qz2yk().s[84]++;
  return __awaiter(void 0, [request_1, _a], Promise, function (request, _b) {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[15]++;
    var params =
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().s[85]++, _b.params);
    /* istanbul ignore next */
    cov_1pmv7qz2yk().s[86]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[16]++;
      cov_1pmv7qz2yk().s[87]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, aiInsightsRateLimit, function () {
        /* istanbul ignore next */
        cov_1pmv7qz2yk().f[17]++;
        cov_1pmv7qz2yk().s[88]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1pmv7qz2yk().f[18]++;
          var session, assessmentId, error, error, error, cacheKey, cachedInsights, parsed, cacheError_1, assessment, error, error, error, responseData, enhancedResults, startTime, aiInsights, timeoutPromise, aiGenerationPromise, aiError_1, generationTime, cacheError_2;
          var _a, _b;
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[89]++;
          return __generator(this, function (_c) {
            /* istanbul ignore next */
            cov_1pmv7qz2yk().f[19]++;
            cov_1pmv7qz2yk().s[90]++;
            switch (_c.label) {
              case 0:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][0]++;
                cov_1pmv7qz2yk().s[91]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][1]++;
                cov_1pmv7qz2yk().s[92]++;
                session = _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[93]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][2]++;
                cov_1pmv7qz2yk().s[94]++;
                assessmentId = _c.sent().id;
                // Authentication check
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[95]++;
                if (!(
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[38][0]++, (_a =
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[40][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[40][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[39][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[39][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[38][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[37][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[37][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().b[36][0]++;
                  cov_1pmv7qz2yk().s[96]++;
                  error = new Error('Authentication required');
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[97]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[98]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_1pmv7qz2yk().b[36][1]++;
                }
                // Input validation
                cov_1pmv7qz2yk().s[99]++;
                try {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[100]++;
                  aiInsightsRequestSchema.parse({
                    assessmentId: assessmentId
                  });
                } catch (validationError) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[101]++;
                  error = new Error('Invalid request parameters');
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[102]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[103]++;
                  error.details = validationError instanceof zod_1.z.ZodError ?
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[41][0]++, validationError.errors) :
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[41][1]++, undefined);
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[104]++;
                  throw error;
                }
                // Check if AI services are available
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[105]++;
                if (!process.env.GOOGLE_GEMINI_API_KEY) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().b[42][0]++;
                  cov_1pmv7qz2yk().s[106]++;
                  error = new Error('AI services are temporarily unavailable');
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[107]++;
                  error.statusCode = 503;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[108]++;
                  error.fallback = true;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[109]++;
                  error.retryAfter = 300; // 5 minutes
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[110]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_1pmv7qz2yk().b[42][1]++;
                }
                cov_1pmv7qz2yk().s[111]++;
                cacheKey = "ai_insights:".concat(assessmentId, ":").concat(session.user.id, ":v1.0.0");
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[112]++;
                _c.label = 3;
              case 3:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][3]++;
                cov_1pmv7qz2yk().s[113]++;
                _c.trys.push([3, 5,, 6]);
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[114]++;
                return [4 /*yield*/, cache_1.cache.get(cacheKey)];
              case 4:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][4]++;
                cov_1pmv7qz2yk().s[115]++;
                cachedInsights = _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[116]++;
                if (
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[44][0]++, cachedInsights) &&
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[44][1]++, typeof cachedInsights === 'string')) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().b[43][0]++;
                  cov_1pmv7qz2yk().s[117]++;
                  parsed = JSON.parse(cachedInsights);
                  // Validate cached data structure
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[118]++;
                  if (
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[46][0]++, parsed) &&
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[46][1]++, typeof parsed === 'object') &&
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[46][2]++, parsed.personalityAnalysis)) {
                    /* istanbul ignore next */
                    cov_1pmv7qz2yk().b[45][0]++;
                    cov_1pmv7qz2yk().s[119]++;
                    return [2 /*return*/, server_1.NextResponse.json({
                      success: true,
                      data: parsed,
                      cached: true,
                      message: 'AI insights retrieved from cache',
                      generatedAt:
                      /* istanbul ignore next */
                      (cov_1pmv7qz2yk().b[47][0]++, parsed.generatedAt) ||
                      /* istanbul ignore next */
                      (cov_1pmv7qz2yk().b[47][1]++, new Date().toISOString())
                    })];
                  } else
                  /* istanbul ignore next */
                  {
                    cov_1pmv7qz2yk().b[45][1]++;
                  }
                } else
                /* istanbul ignore next */
                {
                  cov_1pmv7qz2yk().b[43][1]++;
                }
                cov_1pmv7qz2yk().s[120]++;
                return [3 /*break*/, 6];
              case 5:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][5]++;
                cov_1pmv7qz2yk().s[121]++;
                cacheError_1 = _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[122]++;
                console.error('Cache retrieval error:', cacheError_1);
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[123]++;
                return [3 /*break*/, 6];
              case 6:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][6]++;
                cov_1pmv7qz2yk().s[124]++;
                return [4 /*yield*/, prisma_1.default.assessment.findFirst({
                  where: {
                    id: assessmentId,
                    userId: session.user.id
                  },
                  include: {
                    responses: true,
                    user: {
                      select: {
                        id: true,
                        email: true,
                        name: true
                      }
                    }
                  }
                })];
              case 7:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][7]++;
                cov_1pmv7qz2yk().s[125]++;
                assessment = _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[126]++;
                if (!assessment) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().b[48][0]++;
                  cov_1pmv7qz2yk().s[127]++;
                  error = new Error('Assessment not found or access denied');
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[128]++;
                  error.statusCode = 404;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[129]++;
                  error.code = 'ASSESSMENT_NOT_FOUND';
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[130]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_1pmv7qz2yk().b[48][1]++;
                }
                // Validate assessment has sufficient data
                cov_1pmv7qz2yk().s[131]++;
                if (
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[50][0]++, !assessment.responses) ||
                /* istanbul ignore next */
                (cov_1pmv7qz2yk().b[50][1]++, assessment.responses.length < 3)) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().b[49][0]++;
                  cov_1pmv7qz2yk().s[132]++;
                  error = new Error('Insufficient assessment data for AI analysis');
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[133]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[134]++;
                  error.code = 'INSUFFICIENT_DATA';
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[135]++;
                  error.requiredResponses = 3;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[136]++;
                  error.currentResponses =
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[51][0]++,
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[53][0]++, (_b = assessment.responses) === null) ||
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[53][1]++, _b === void 0) ?
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[52][0]++, void 0) :
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[52][1]++, _b.length)) ||
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[51][1]++, 0);
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[137]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_1pmv7qz2yk().b[49][1]++;
                }
                cov_1pmv7qz2yk().s[138]++;
                if (assessment.status !== 'COMPLETED') {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().b[54][0]++;
                  cov_1pmv7qz2yk().s[139]++;
                  error = new Error('Assessment is not completed');
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[140]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().s[141]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_1pmv7qz2yk().b[54][1]++;
                }
                cov_1pmv7qz2yk().s[142]++;
                responseData = {};
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[143]++;
                assessment.responses.forEach(function (response) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().f[20]++;
                  cov_1pmv7qz2yk().s[144]++;
                  try {
                    var value =
                    /* istanbul ignore next */
                    (cov_1pmv7qz2yk().s[145]++, typeof response.answerValue === 'string' ?
                    /* istanbul ignore next */
                    (cov_1pmv7qz2yk().b[55][0]++, JSON.parse(response.answerValue)) :
                    /* istanbul ignore next */
                    (cov_1pmv7qz2yk().b[55][1]++, response.answerValue));
                    /* istanbul ignore next */
                    cov_1pmv7qz2yk().s[146]++;
                    responseData[response.questionKey] = value;
                  } catch (_a) {
                    /* istanbul ignore next */
                    cov_1pmv7qz2yk().s[147]++;
                    responseData[response.questionKey] = response.answerValue;
                  }
                });
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[148]++;
                return [4 /*yield*/, enhancedAssessmentService_1.EnhancedAssessmentService.generateEnhancedResults(assessmentId, responseData)];
              case 8:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][8]++;
                cov_1pmv7qz2yk().s[149]++;
                enhancedResults = _c.sent();
                // Generate AI insights with timeout and retry logic
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[150]++;
                console.log("Generating AI insights for user ".concat(session.user.id, ", assessment ").concat(assessmentId));
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[151]++;
                startTime = Date.now();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[152]++;
                _c.label = 9;
              case 9:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][9]++;
                cov_1pmv7qz2yk().s[153]++;
                _c.trys.push([9, 11,, 12]);
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[154]++;
                timeoutPromise = new Promise(function (_, reject) {
                  /* istanbul ignore next */
                  cov_1pmv7qz2yk().f[21]++;
                  cov_1pmv7qz2yk().s[155]++;
                  setTimeout(function () {
                    /* istanbul ignore next */
                    cov_1pmv7qz2yk().f[22]++;
                    cov_1pmv7qz2yk().s[156]++;
                    return reject(new Error('AI generation timeout'));
                  }, 5 * 60 * 1000);
                });
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[157]++;
                aiGenerationPromise = aiEnhancedAssessmentService_1.aiEnhancedAssessmentService.generateAIInsights(assessmentId, responseData, enhancedResults.insights, enhancedResults.careerPathRecommendations, session.user.id);
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[158]++;
                return [4 /*yield*/, Promise.race([aiGenerationPromise, timeoutPromise])];
              case 10:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][10]++;
                cov_1pmv7qz2yk().s[159]++;
                aiInsights = _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[160]++;
                return [3 /*break*/, 12];
              case 11:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][11]++;
                cov_1pmv7qz2yk().s[161]++;
                aiError_1 = _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[162]++;
                console.error('AI insights generation error:', aiError_1);
                // Return fallback insights with error information
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[163]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: false,
                  error: 'AI insights generation failed',
                  code: 'AI_GENERATION_FAILED',
                  fallback: true,
                  retryAfter: 60,
                  // 1 minute
                  details: aiError_1 instanceof Error ?
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[56][0]++, aiError_1.message) :
                  /* istanbul ignore next */
                  (cov_1pmv7qz2yk().b[56][1]++, 'Unknown error')
                }, {
                  status: 500
                })];
              case 12:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][12]++;
                cov_1pmv7qz2yk().s[164]++;
                generationTime = Date.now() - startTime;
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[165]++;
                console.log("AI insights generated in ".concat(generationTime, "ms for user ").concat(session.user.id, ", assessment ").concat(assessmentId));
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[166]++;
                _c.label = 13;
              case 13:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][13]++;
                cov_1pmv7qz2yk().s[167]++;
                _c.trys.push([13, 15,, 16]);
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[168]++;
                return [4 /*yield*/, cache_1.cache.set(cacheKey, JSON.stringify(aiInsights), 86400)];
              case 14:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][14]++;
                cov_1pmv7qz2yk().s[169]++;
                _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[170]++;
                return [3 /*break*/, 16];
              case 15:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][15]++;
                cov_1pmv7qz2yk().s[171]++;
                cacheError_2 = _c.sent();
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[172]++;
                console.error('Failed to cache AI insights:', cacheError_2);
                /* istanbul ignore next */
                cov_1pmv7qz2yk().s[173]++;
                return [3 /*break*/, 16];
              case 16:
                /* istanbul ignore next */
                cov_1pmv7qz2yk().b[35][16]++;
                cov_1pmv7qz2yk().s[174]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: {
                    insights: aiInsights,
                    cached: false,
                    generatedAt: new Date().toISOString()
                  }
                })];
            }
          });
        });
      })];
    });
  });
});
// POST endpoint to regenerate AI insights with specific focus areas
/* istanbul ignore next */
cov_1pmv7qz2yk().s[175]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1pmv7qz2yk().f[23]++;
  cov_1pmv7qz2yk().s[176]++;
  return __awaiter(void 0, [request_1, _a], Promise, function (request, _b) {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[24]++;
    var session, assessmentId, body, error, error, _c, focusAreas, _d, analysisDepth, _e, includeMarketData, _f, personalityFocus, assessment, error, responseData, enhancedResults, aiInsights, customCacheKey;
    var _g;
    var params =
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().s[177]++, _b.params);
    /* istanbul ignore next */
    cov_1pmv7qz2yk().s[178]++;
    return __generator(this, function (_h) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[25]++;
      cov_1pmv7qz2yk().s[179]++;
      switch (_h.label) {
        case 0:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][0]++;
          cov_1pmv7qz2yk().s[180]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][1]++;
          cov_1pmv7qz2yk().s[181]++;
          session = _h.sent();
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[182]++;
          return [4 /*yield*/, params];
        case 2:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][2]++;
          cov_1pmv7qz2yk().s[183]++;
          assessmentId = _h.sent().id;
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[184]++;
          return [4 /*yield*/, request.json()];
        case 3:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][3]++;
          cov_1pmv7qz2yk().s[185]++;
          body = _h.sent();
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[186]++;
          if (!(
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[60][0]++, (_g =
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[62][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[62][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[61][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[61][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[60][1]++, _g === void 0) ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[59][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[59][1]++, _g.id))) {
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[58][0]++;
            cov_1pmv7qz2yk().s[187]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[188]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[189]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1pmv7qz2yk().b[58][1]++;
          }
          cov_1pmv7qz2yk().s[190]++;
          if (!process.env.GOOGLE_GEMINI_API_KEY) {
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[63][0]++;
            cov_1pmv7qz2yk().s[191]++;
            error = new Error('AI services are not configured');
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[192]++;
            error.statusCode = 503;
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[193]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1pmv7qz2yk().b[63][1]++;
          }
          cov_1pmv7qz2yk().s[194]++;
          _c = body.focusAreas, focusAreas = _c === void 0 ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[64][0]++, []) :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[64][1]++, _c), _d = body.analysisDepth, analysisDepth = _d === void 0 ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[65][0]++, 'standard') :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[65][1]++, _d), _e = body.includeMarketData, includeMarketData = _e === void 0 ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[66][0]++, true) :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[66][1]++, _e), _f = body.personalityFocus, personalityFocus = _f === void 0 ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[67][0]++, true) :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[67][1]++, _f);
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[195]++;
          return [4 /*yield*/, prisma_1.default.assessment.findFirst({
            where: {
              id: assessmentId,
              userId: session.user.id
            },
            include: {
              responses: true
            }
          })];
        case 4:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][4]++;
          cov_1pmv7qz2yk().s[196]++;
          assessment = _h.sent();
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[197]++;
          if (!assessment) {
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[68][0]++;
            cov_1pmv7qz2yk().s[198]++;
            error = new Error('Assessment not found or access denied');
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[199]++;
            error.statusCode = 404;
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[200]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1pmv7qz2yk().b[68][1]++;
          }
          cov_1pmv7qz2yk().s[201]++;
          responseData = {};
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[202]++;
          assessment.responses.forEach(function (response) {
            /* istanbul ignore next */
            cov_1pmv7qz2yk().f[26]++;
            cov_1pmv7qz2yk().s[203]++;
            try {
              var value =
              /* istanbul ignore next */
              (cov_1pmv7qz2yk().s[204]++, typeof response.answerValue === 'string' ?
              /* istanbul ignore next */
              (cov_1pmv7qz2yk().b[69][0]++, JSON.parse(response.answerValue)) :
              /* istanbul ignore next */
              (cov_1pmv7qz2yk().b[69][1]++, response.answerValue));
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[205]++;
              responseData[response.questionKey] = value;
            } catch (_a) {
              /* istanbul ignore next */
              cov_1pmv7qz2yk().s[206]++;
              responseData[response.questionKey] = response.answerValue;
            }
          });
          // Add user preferences to response data for AI analysis
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[207]++;
          responseData.ai_preferences = {
            focusAreas: focusAreas,
            analysisDepth: analysisDepth,
            includeMarketData: includeMarketData,
            personalityFocus: personalityFocus
          };
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[208]++;
          return [4 /*yield*/, enhancedAssessmentService_1.EnhancedAssessmentService.generateEnhancedResults(assessmentId, responseData)];
        case 5:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][5]++;
          cov_1pmv7qz2yk().s[209]++;
          enhancedResults = _h.sent();
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[210]++;
          return [4 /*yield*/, aiEnhancedAssessmentService_1.aiEnhancedAssessmentService.generateAIInsights(assessmentId, responseData, enhancedResults.insights, enhancedResults.careerPathRecommendations, session.user.id)];
        case 6:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][6]++;
          cov_1pmv7qz2yk().s[211]++;
          aiInsights = _h.sent();
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[212]++;
          customCacheKey = "ai_insights:".concat(assessmentId, ":").concat(session.user.id, ":custom:").concat(Date.now());
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[213]++;
          return [4 /*yield*/, cache_1.cache.set(customCacheKey, JSON.stringify(aiInsights), 3600)];
        case 7:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[57][7]++;
          cov_1pmv7qz2yk().s[214]++;
          _h.sent(); // 1 hour cache
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[215]++;
          console.log("Custom AI insights generated for user ".concat(session.user.id, ", assessment ").concat(assessmentId));
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[216]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              insights: aiInsights,
              regenerated: true,
              focusAreas: focusAreas,
              generatedAt: new Date().toISOString()
            }
          })];
      }
    });
  });
});
// DELETE endpoint to clear AI insights cache
/* istanbul ignore next */
cov_1pmv7qz2yk().s[217]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_1pmv7qz2yk().f[27]++;
  cov_1pmv7qz2yk().s[218]++;
  return __awaiter(void 0, [request_1, _a], Promise, function (request, _b) {
    /* istanbul ignore next */
    cov_1pmv7qz2yk().f[28]++;
    var session, assessmentId, error, cacheKey;
    var _c;
    var params =
    /* istanbul ignore next */
    (cov_1pmv7qz2yk().s[219]++, _b.params);
    /* istanbul ignore next */
    cov_1pmv7qz2yk().s[220]++;
    return __generator(this, function (_d) {
      /* istanbul ignore next */
      cov_1pmv7qz2yk().f[29]++;
      cov_1pmv7qz2yk().s[221]++;
      switch (_d.label) {
        case 0:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[70][0]++;
          cov_1pmv7qz2yk().s[222]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[70][1]++;
          cov_1pmv7qz2yk().s[223]++;
          session = _d.sent();
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[224]++;
          return [4 /*yield*/, params];
        case 2:
          /* istanbul ignore next */
          cov_1pmv7qz2yk().b[70][2]++;
          cov_1pmv7qz2yk().s[225]++;
          assessmentId = _d.sent().id;
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[226]++;
          if (!(
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[73][0]++, (_c =
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[75][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[75][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[74][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[74][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[73][1]++, _c === void 0) ?
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[72][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1pmv7qz2yk().b[72][1]++, _c.id))) {
            /* istanbul ignore next */
            cov_1pmv7qz2yk().b[71][0]++;
            cov_1pmv7qz2yk().s[227]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[228]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_1pmv7qz2yk().s[229]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1pmv7qz2yk().b[71][1]++;
          }
          cov_1pmv7qz2yk().s[230]++;
          cacheKey = "ai_insights:".concat(assessmentId, ":").concat(session.user.id);
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[231]++;
          cache_1.cache.delete(cacheKey);
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[232]++;
          console.log("AI insights cache cleared for user ".concat(session.user.id, ", assessment ").concat(assessmentId));
          /* istanbul ignore next */
          cov_1pmv7qz2yk().s[233]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              cleared: true,
              assessmentId: assessmentId,
              clearedAt: new Date().toISOString()
            }
          })];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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