{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/__mocks__/useFormValidation.ts", "mappings": ";;;AAAA,IAAM,YAAY,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAEjD,IAAM,eAAe,GAAG,IAAI,CAAC,EAAE,CAAC,UAAC,KAAa,EAAE,KAAa;IAC1D,YAAoB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACvC,CAAC,CAAC,CAAC;AAEH,IAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,UAAC,CAAO;IACvC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC,CAAC,cAAc,EAAE,CAAC;IACrB,CAAC;IACD,iCAAiC;IACjC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEU,QAAA,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;IAC7C,IAAI,EAAE,YAAY;IAClB,WAAW,EAAE,eAAe;IAC5B,YAAY,EAAE,gBAAgB;IAC9B,YAAY,EAAE,KAAK;IACnB,UAAU,EAAE;QACV,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,IAAI;KACd;IACD,iBAAiB,EAAE;QACjB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;KACvB;CACF,CAAC,EAd4C,CAc5C,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/__mocks__/useFormValidation.ts"], "sourcesContent": ["const mockFormData = { email: '', password: '' };\n\nconst mockUpdateField = jest.fn((field: string, value: string) => {\n  (mockFormData as any)[field] = value;\n});\n\nconst mockHandleSubmit = jest.fn((e?: any) => {\n  if (e && e.preventDefault) {\n    e.preventDefault();\n  }\n  // Simulate form submission logic\n  return Promise.resolve();\n});\n\nexport const useValidatedForm = jest.fn(() => ({\n  data: mockFormData,\n  updateField: mockUpdateField,\n  handleSubmit: mockHandleSubmit,\n  isSubmitting: false,\n  validation: {\n    errors: {},\n    isValid: true,\n  },\n  validationActions: {\n    validateField: jest.fn(),\n    validateForm: jest.fn(),\n    clearErrors: jest.fn(),\n  },\n}));\n"], "version": 3}