c300ea206f39a33a931cf91a555ea18b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useValidatedForm = void 0;
var mockFormData = { email: '', password: '' };
var mockUpdateField = jest.fn(function (field, value) {
    mockFormData[field] = value;
});
var mockHandleSubmit = jest.fn(function (e) {
    if (e && e.preventDefault) {
        e.preventDefault();
    }
    // Simulate form submission logic
    return Promise.resolve();
});
exports.useValidatedForm = jest.fn(function () { return ({
    data: mockFormData,
    updateField: mockUpdateField,
    handleSubmit: mockHandleSubmit,
    isSubmitting: false,
    validation: {
        errors: {},
        isValid: true,
    },
    validationActions: {
        validateField: jest.fn(),
        validateForm: jest.fn(),
        clearErrors: jest.fn(),
    },
}); });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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