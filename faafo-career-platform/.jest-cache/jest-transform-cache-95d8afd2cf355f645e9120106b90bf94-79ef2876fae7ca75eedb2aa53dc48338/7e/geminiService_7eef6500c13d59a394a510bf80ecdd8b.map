{"version": 3, "names": ["generative_ai_1", "cov_rbz368dur", "s", "require", "consolidated_cache_service_1", "ai_input_validator_1", "__importDefault", "CircuitBreaker", "f", "state", "isOpen", "failureCount", "lastFailureTime", "successCount", "failureT<PERSON><PERSON>old", "recoveryTimeout", "prototype", "execute", "operation", "Promise", "b", "Date", "now", "Error", "result", "_a", "sent", "onSuccess", "onFailure", "error_1", "AIServiceLogger", "warn", "getState", "__assign", "geminiCircuitBreaker", "info", "message", "context", "console", "log", "concat", "toISOString", "error", "stack", "name", "debug", "logLevel", "process", "env", "NODE_ENV", "exports", "<PERSON><PERSON><PERSON><PERSON>", "GOOGLE_GEMINI_API_KEY", "length", "genAI", "GoogleGenerativeAI", "configs", "resume_analysis", "temperature", "topK", "topP", "maxOutputTokens", "career_recommendations", "skills_analysis", "interview_prep", "interview_questions", "interview_analysis", "RESPONSE_SCHEMAS", "type", "required", "properties", "strengths", "items", "weaknesses", "suggestions", "skillsIdentified", "experienceLevel", "enum", "industryFit", "overallScore", "min", "max", "recommendations", "careerPath", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "matchScore", "reasoning", "requiredSkills", "timeToTransition", "salaryRange", "growthPotential", "questions", "questionText", "questionType", "category", "difficulty", "expectedDuration", "hints", "followUpQuestions", "industrySpecific", "tags", "isRequired", "metadata", "analysis", "feedback", "improvements", "starMethodScore", "confidenceLevel", "communicationScore", "technicalScore", "GeminiService", "_this", "RATE_LIMIT_REQUESTS_PER_MINUTE", "RATE_LIMIT_WINDOW_MS", "model", "getGenerativeModel", "cacheEnabled", "rateLimiter", "Map", "cacheTTLStr", "AI_CACHE_TTL", "parsedTTL", "parseInt", "isNaN", "cacheTTL", "setInterval", "cleanupRateLimiter", "rateLimitPerMinute", "performanceOptimizationsEnabled", "initializePerformanceOptimizations", "checkRateLimit", "userId", "userLimit", "get", "set", "count", "resetTime", "limit", "_i", "Array", "from", "entries", "_b", "delete", "generateFallbackData", "config<PERSON><PERSON>", "structure", "keyPoints", "sessionType", "totalQuestions", "difficultyDistribution", "INTERMEDIATE", "categoryDistribution", "GENERAL", "contentQuality", "score", "relevance", "positive", "constructive", "area", "suggestion", "priority", "content", "generateContent", "prompt_1", "configKey_1", "cacheKey_1", "prompt", "cache<PERSON>ey", "timeoutMs", "startTime", "cacheHit", "success", "consolidatedCache", "cached", "responseTime_1", "substring", "responseTime", "data", "config", "timeoutPromise", "_", "reject", "setTimeout", "race", "contents", "role", "parts", "text", "generationConfig", "response", "containsHarmfulContent", "JSON", "parse", "jsonError", "jsonMatch", "match", "extractError", "responseLength", "parseMethod", "ensureInterviewAnalysisFields", "ttl", "responseSize", "stringify", "errorContext", "undefined", "error_2", "errorMessage", "errorType", "includes", "analyzeResume", "resumeText", "trim", "harmfulPatterns", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "pattern", "toLowerCase", "sanitizedResumeText", "<PERSON><PERSON><PERSON>", "toString", "slice", "generateCareerRecommendations", "assessmentData", "currentSkills", "preferences", "isArray", "sanitizedSkills", "filter", "skill", "map", "join", "analyzeSkillsGap", "targetCareerPath", "replace", "analyzeComprehensiveSkillGap", "careerPathData", "skillName", "selfRating", "yearsOfExperience", "lastUsed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetLevel", "timeframe", "hoursPerWeek", "learningStyle", "budget", "focusAreas", "learningResources", "resource", "title", "skillLevel", "skills", "cost", "generatePersonalizedLearningPlan", "skillGaps", "userPreferences", "marketData", "gap", "currentLevel", "gapSeverity", "estimatedLearningTime", "demandLevel", "growthTrend", "salaryImpact", "analyzeSkillMarketTrends", "skills_1", "targetIndustry_1", "targetIndustry", "region", "validateSkillAssessment", "userContext", "industry", "relatedSkills", "previousAssessments", "assessment", "rating", "date", "generateInterviewPrep", "companyType", "generateInterviewQuestions", "params", "industryFocus", "specificRole", "interviewType", "questionTypes", "categories", "analyzeInterviewResponse", "responseValidation", "default", "validateInterviewResponse", "responseText", "<PERSON><PERSON><PERSON><PERSON>", "errors", "questionValidation", "validateTextInput", "allowHtml", "requireAlphanumeric", "sanitizedResponseText", "sanitizedInput", "sanitizedQuestionText", "questionCategory", "generatePersonalizedContent", "contentType_1", "userContext_1", "contentType", "additionalParams", "generateRequestHash", "hash", "getRequestPriority", "priorityMap", "healthCheck", "aiHealthy", "error_3", "cacheHealth", "redis", "memory", "advancedCacheHealth", "status", "requestOptimizerHealth", "performanceMonitorHealth", "ai", "cache", "getServiceStats", "cacheStats", "connected", "size", "rateLimiterStats", "activeUsers", "securityScan", "riskLevel", "threats", "validateResponseStructure", "validateResumeAnalysis", "validateCareerRecommendations", "validateInterviewAnalysis", "validateInterviewQuestions", "every", "rec", "aiScore", "Math", "q", "sanitizeText", "validation", "allowSpecialChars", "geminiService"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/geminiService.ts"], "sourcesContent": ["import { GoogleGenerativeAI, GenerativeModel, GenerationConfig } from '@google/generative-ai';\nimport { consolidatedCache } from '@/lib/services/consolidated-cache-service';\n// Temporarily disable advanced modules to fix import issues\n// import redisCache from '@/lib/redis-cache';\nimport AIInputValidator from '@/lib/ai-input-validator';\n// import AIResponseParser from '@/lib/ai-response-parser';\n// import { aiServiceMonitor } from '@/lib/ai-service-monitor';\n// import { advancedCacheManager } from '@/lib/advanced-cache-manager';\n// import { requestOptimizer } from '@/lib/request-optimizer';\n// import { performanceMonitor } from '@/lib/performance-monitor';\n\n// Circuit Breaker for external AI service calls\ninterface CircuitBreakerState {\n  isOpen: boolean;\n  failureCount: number;\n  lastFailureTime: number;\n  successCount: number;\n}\n\nclass CircuitBreaker {\n  private state: CircuitBreakerState = {\n    isOpen: false,\n    failureCount: 0,\n    lastFailureTime: 0,\n    successCount: 0\n  };\n\n  private readonly failureThreshold = 5;\n  private readonly recoveryTimeout = 60000; // 1 minute\n\n  async execute<T>(operation: () => Promise<T>): Promise<T> {\n    if (this.state.isOpen) {\n      if (Date.now() - this.state.lastFailureTime > this.recoveryTimeout) {\n        this.state.isOpen = false;\n        this.state.failureCount = 0;\n      } else {\n        throw new Error('Circuit breaker is open - service temporarily unavailable');\n      }\n    }\n\n    try {\n      const result = await operation();\n      this.onSuccess();\n      return result;\n    } catch (error) {\n      this.onFailure();\n      throw error;\n    }\n  }\n\n  private onSuccess(): void {\n    this.state.successCount++;\n    this.state.failureCount = 0;\n  }\n\n  private onFailure(): void {\n    this.state.failureCount++;\n    this.state.lastFailureTime = Date.now();\n\n    if (this.state.failureCount >= this.failureThreshold) {\n      this.state.isOpen = true;\n      AIServiceLogger.warn('Circuit breaker opened due to repeated failures');\n    }\n  }\n\n  getState(): CircuitBreakerState {\n    return { ...this.state };\n  }\n}\n\nconst geminiCircuitBreaker = new CircuitBreaker();\n\n// Enhanced logging utility for AI service\nexport class AIServiceLogger {\n  private static logLevel = process.env.NODE_ENV === 'development' ? 'debug' : 'info';\n\n  static info(message: string, context?: any) {\n    console.log(`[AI-Service] ${new Date().toISOString()} INFO: ${message}`, context || '');\n  }\n\n  static warn(message: string, context?: any) {\n    console.warn(`[AI-Service] ${new Date().toISOString()} WARN: ${message}`, context || '');\n  }\n\n  static error(message: string, error?: any, context?: any) {\n    console.error(`[AI-Service] ${new Date().toISOString()} ERROR: ${message}`, {\n      error: error instanceof Error ? {\n        message: error.message,\n        stack: error.stack,\n        name: error.name\n      } : error,\n      context\n    });\n  }\n\n  static debug(message: string, context?: any) {\n    if (this.logLevel === 'debug') {\n      console.log(`[AI-Service] ${new Date().toISOString()} DEBUG: ${message}`, context || '');\n    }\n  }\n}\n\n// Validate API key before initialization\nconst apiKey = process.env.GOOGLE_GEMINI_API_KEY;\nif (!apiKey || apiKey.length < 10) {\n  AIServiceLogger.error('CRITICAL: Invalid or missing GOOGLE_GEMINI_API_KEY environment variable');\n  throw new Error('Gemini AI service cannot be initialized: Invalid API key configuration');\n}\n\n// Initialize Google Gemini AI\nconst genAI = new GoogleGenerativeAI(apiKey);\n\n// Configuration for different AI tasks\nconst configs: Record<string, GenerationConfig> = {\n  resume_analysis: {\n    temperature: 0.3,\n    topK: 40,\n    topP: 0.95,\n    maxOutputTokens: 2048,\n  },\n  career_recommendations: {\n    temperature: 0.7,\n    topK: 40,\n    topP: 0.95,\n    maxOutputTokens: 1024,\n  },\n  skills_analysis: {\n    temperature: 0.4,\n    topK: 40,\n    topP: 0.95,\n    maxOutputTokens: 1536,\n  },\n  interview_prep: {\n    temperature: 0.6,\n    topK: 40,\n    topP: 0.95,\n    maxOutputTokens: 2048,\n  },\n  interview_questions: {\n    temperature: 0.7,\n    topK: 40,\n    topP: 0.95,\n    maxOutputTokens: 3072,\n  },\n  interview_analysis: {\n    temperature: 0.4,\n    topK: 40,\n    topP: 0.95,\n    maxOutputTokens: 2048,\n  },\n};\n\nexport interface AIResponse {\n  success: boolean;\n  data?: any;\n  error?: string;\n  cached?: boolean;\n}\n\nexport interface ResumeAnalysisResult {\n  strengths: string[];\n  weaknesses: string[];\n  suggestions: string[];\n  skillsIdentified: string[];\n  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';\n  industryFit: string[];\n  overallScore: number;\n}\n\nexport interface CareerRecommendation {\n  careerPath: string;\n  matchScore: number;\n  reasoning: string;\n  requiredSkills: string[];\n  timeToTransition: string;\n  salaryRange: string;\n  growthPotential: string;\n}\n\nexport interface SkillsAnalysisResult {\n  currentSkills: string[];\n  skillGaps: string[];\n  learningRecommendations: {\n    skill: string;\n    priority: 'high' | 'medium' | 'low';\n    estimatedTime: string;\n    resources: string[];\n  }[];\n  careerReadiness: number;\n}\n\nexport interface InterviewPrepResult {\n  commonQuestions: {\n    question: string;\n    category: 'behavioral' | 'technical' | 'situational';\n    sampleAnswer: string;\n    tips: string[];\n  }[];\n  industrySpecificQuestions: {\n    question: string;\n    difficulty: 'easy' | 'medium' | 'hard';\n    keyPoints: string[];\n  }[];\n  preparationTips: string[];\n}\n\nexport interface InterviewQuestionsResult {\n  questions: {\n    questionText: string;\n    questionType: string;\n    category: string;\n    difficulty: string;\n    expectedDuration: number;\n    context: string;\n    hints: any;\n    followUpQuestions: any;\n    industrySpecific: boolean;\n    tags: any;\n    isRequired: boolean;\n  }[];\n  metadata: {\n    sessionType: string;\n    totalQuestions: number;\n    difficultyDistribution: any;\n    categoryDistribution: any;\n  };\n}\n\nexport interface InterviewResponseAnalysis {\n  overallScore: number;\n  analysis: any;\n  feedback: any;\n  strengths: any;\n  improvements: any;\n  starMethodScore?: number;\n  confidenceLevel?: number;\n  communicationScore?: number;\n  technicalScore?: number;\n}\n\n// Response validation schemas\nconst RESPONSE_SCHEMAS = {\n  resume_analysis: {\n    type: 'object' as const,\n    required: ['strengths', 'weaknesses', 'suggestions', 'overallScore'],\n    properties: {\n      strengths: { type: 'array' as const, items: { type: 'string' as const } },\n      weaknesses: { type: 'array' as const, items: { type: 'string' as const } },\n      suggestions: { type: 'array' as const, items: { type: 'string' as const } },\n      skillsIdentified: { type: 'array' as const, items: { type: 'string' as const } },\n      experienceLevel: { type: 'string' as const, enum: ['entry', 'mid', 'senior', 'executive'] },\n      industryFit: { type: 'array' as const, items: { type: 'string' as const } },\n      overallScore: { type: 'number' as const, min: 1, max: 100 }\n    }\n  },\n  career_recommendations: {\n    type: 'object' as const,\n    required: ['recommendations'],\n    properties: {\n      recommendations: {\n        type: 'array' as const,\n        items: {\n          type: 'object' as const,\n          required: ['careerPath', 'matchScore', 'reasoning'],\n          properties: {\n            careerPath: { type: 'string' as const, minLength: 1, maxLength: 200 },\n            matchScore: { type: 'number' as const, min: 1, max: 100 },\n            reasoning: { type: 'string' as const, minLength: 10, maxLength: 1000 },\n            requiredSkills: { type: 'array' as const, items: { type: 'string' as const } },\n            timeToTransition: { type: 'string' as const },\n            salaryRange: { type: 'string' as const },\n            growthPotential: { type: 'string' as const }\n          }\n        }\n      }\n    }\n  },\n  interview_questions: {\n    type: 'object' as const,\n    required: ['questions'],\n    properties: {\n      questions: {\n        type: 'array' as const,\n        items: {\n          type: 'object' as const,\n          required: ['questionText', 'questionType', 'category', 'difficulty'],\n          properties: {\n            questionText: { type: 'string' as const, minLength: 10, maxLength: 1000 },\n            questionType: { type: 'string' as const },\n            category: { type: 'string' as const },\n            difficulty: { type: 'string' as const, enum: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'] },\n            expectedDuration: { type: 'number' as const, min: 30, max: 600 },\n            context: { type: 'string' as const },\n            hints: { type: 'object' as const },\n            followUpQuestions: { type: 'array' as const },\n            industrySpecific: { type: 'boolean' as const },\n            tags: { type: 'array' as const },\n            isRequired: { type: 'boolean' as const }\n          }\n        }\n      },\n      metadata: { type: 'object' as const }\n    }\n  },\n  interview_analysis: {\n    type: 'object' as const,\n    required: ['overallScore', 'analysis', 'feedback'],\n    properties: {\n      overallScore: { type: 'number' as const, min: 1, max: 10 },\n      analysis: { type: 'object' as const },\n      feedback: { type: 'object' as const },\n      strengths: { type: 'array' as const },\n      improvements: { type: 'array' as const },\n      starMethodScore: { type: 'number' as const, min: 1, max: 10 },\n      confidenceLevel: { type: 'number' as const, min: 0, max: 1 },\n      communicationScore: { type: 'number' as const, min: 1, max: 10 },\n      technicalScore: { type: 'number' as const, min: 1, max: 10 }\n    }\n  }\n};\n\ninterface RateLimitEntry {\n  count: number;\n  resetTime: number;\n}\n\nclass GeminiService {\n  private model: GenerativeModel;\n  private cacheEnabled: boolean;\n  private cacheTTL: number;\n  private rateLimiter: Map<string, RateLimitEntry>;\n  private readonly RATE_LIMIT_REQUESTS_PER_MINUTE = 60; // Adjust based on your API limits\n  private readonly RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute\n\n  constructor() {\n    try {\n      this.model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });\n      this.cacheEnabled = process.env.NODE_ENV === 'production';\n      this.rateLimiter = new Map();\n\n      // Validate and set cache TTL\n      const cacheTTLStr = process.env.AI_CACHE_TTL || '3600';\n      const parsedTTL = parseInt(cacheTTLStr);\n      if (isNaN(parsedTTL) || parsedTTL < 0) {\n        AIServiceLogger.warn('Invalid AI_CACHE_TTL value, using default 3600 seconds');\n        this.cacheTTL = 3600;\n      } else {\n        this.cacheTTL = parsedTTL;\n      }\n\n      // Clean up rate limiter every 5 minutes\n      setInterval(() => this.cleanupRateLimiter(), 5 * 60 * 1000);\n\n      // Initialize performance optimizations (temporarily disabled)\n      // this.initializePerformanceOptimizations();\n\n      AIServiceLogger.info('Gemini AI Service initialized successfully', {\n        model: 'gemini-1.5-flash',\n        cacheEnabled: this.cacheEnabled,\n        cacheTTL: this.cacheTTL,\n        rateLimitPerMinute: this.RATE_LIMIT_REQUESTS_PER_MINUTE,\n        performanceOptimizationsEnabled: false\n      });\n    } catch (error) {\n      AIServiceLogger.error('Failed to initialize Gemini AI Service', error);\n      throw new Error('Gemini AI service initialization failed');\n    }\n  }\n\n  private async initializePerformanceOptimizations(): Promise<void> {\n    try {\n      // Initialize advanced cache manager (temporarily disabled)\n      // await advancedCacheManager.initialize();\n\n      // Start performance monitoring (temporarily disabled)\n      // performanceMonitor.recordMetric('service_initialization', 1, {\n      //   timestamp: Date.now(),\n      //   component: 'gemini-service'\n      // });\n\n      AIServiceLogger.info('Performance optimizations initialized successfully (basic mode)');\n    } catch (error) {\n      AIServiceLogger.error('Failed to initialize performance optimizations', error);\n    }\n  }\n\n  private checkRateLimit(userId: string = 'anonymous'): boolean {\n    const now = Date.now();\n    const userLimit = this.rateLimiter.get(userId);\n\n    if (!userLimit) {\n      // First request for this user\n      this.rateLimiter.set(userId, {\n        count: 1,\n        resetTime: now + this.RATE_LIMIT_WINDOW_MS\n      });\n      AIServiceLogger.debug('Rate limit initialized for user', { userId, count: 1 });\n      return true;\n    }\n\n    // Check if window has expired\n    if (now > userLimit.resetTime) {\n      // Reset the window\n      this.rateLimiter.set(userId, {\n        count: 1,\n        resetTime: now + this.RATE_LIMIT_WINDOW_MS\n      });\n      AIServiceLogger.debug('Rate limit window reset for user', { userId, count: 1 });\n      return true;\n    }\n\n    // Check if under limit\n    if (userLimit.count < this.RATE_LIMIT_REQUESTS_PER_MINUTE) {\n      userLimit.count++;\n      AIServiceLogger.debug('Rate limit check passed', { userId, count: userLimit.count, limit: this.RATE_LIMIT_REQUESTS_PER_MINUTE });\n      return true;\n    }\n\n    AIServiceLogger.warn('Rate limit exceeded', { userId, count: userLimit.count, limit: this.RATE_LIMIT_REQUESTS_PER_MINUTE });\n    return false; // Rate limit exceeded\n  }\n\n  private cleanupRateLimiter(): void {\n    const now = Date.now();\n    for (const [userId, limit] of Array.from(this.rateLimiter.entries())) {\n      if (now > limit.resetTime) {\n        this.rateLimiter.delete(userId);\n      }\n    }\n  }\n\n  private generateFallbackData(configKey: string): any {\n    switch (configKey) {\n      case 'resume_analysis':\n        return {\n          strengths: ['Professional presentation', 'Clear work history'],\n          weaknesses: ['Could include more specific achievements', 'Skills section could be expanded'],\n          suggestions: ['Add quantifiable results to work experience', 'Include relevant certifications'],\n          skillsIdentified: ['Communication', 'Problem solving', 'Teamwork'],\n          experienceLevel: 'mid',\n          industryFit: ['Technology', 'Business'],\n          overallScore: 75\n        };\n\n      case 'career_recommendations':\n        return {\n          recommendations: [\n            {\n              careerPath: 'Software Developer',\n              matchScore: 80,\n              reasoning: 'Your technical skills and problem-solving abilities align well with software development roles.',\n              requiredSkills: ['Programming', 'Software Design', 'Testing'],\n              timeToTransition: '6-12 months',\n              salaryRange: '$60,000 - $100,000',\n              growthPotential: 'High'\n            }\n          ]\n        };\n\n      case 'interview_questions':\n        return {\n          questions: [\n            {\n              questionText: 'Tell me about yourself and your professional background.',\n              questionType: 'BEHAVIORAL',\n              category: 'GENERAL',\n              difficulty: 'INTERMEDIATE',\n              expectedDuration: 180,\n              context: 'This is a common opening question to assess communication skills.',\n              hints: {\n                structure: 'Use a brief professional summary highlighting relevant experience',\n                keyPoints: ['Current role', 'Key achievements', 'Career goals']\n              },\n              followUpQuestions: ['What interests you most about this role?'],\n              industrySpecific: false,\n              tags: ['opening', 'general'],\n              isRequired: true\n            }\n          ],\n          metadata: {\n            sessionType: 'GENERAL',\n            totalQuestions: 1,\n            difficultyDistribution: { INTERMEDIATE: 1 },\n            categoryDistribution: { GENERAL: 1 }\n          }\n        };\n\n      case 'interview_analysis':\n        return {\n          overallScore: 6,\n          analysis: {\n            contentQuality: { score: 6, feedback: 'Response provides adequate detail' },\n            structure: { score: 5, feedback: 'Could benefit from better organization' },\n            relevance: { score: 7, feedback: 'Addresses the question appropriately' }\n          },\n          feedback: {\n            positive: ['Clear communication', 'Relevant examples'],\n            constructive: ['Could provide more specific details', 'Consider using STAR method']\n          },\n          strengths: ['Good communication skills', 'Relevant experience mentioned'],\n          improvements: [\n            { area: 'Structure', suggestion: 'Use STAR method for behavioral questions', priority: 'high' }\n          ],\n          starMethodScore: 5,\n          confidenceLevel: 0.7,\n          communicationScore: 7,\n          technicalScore: 6\n        };\n\n      default:\n        return { content: 'Fallback response generated due to parsing failure' };\n    }\n  }\n\n  private async generateContent(\n    prompt: string,\n    configKey: string,\n    cacheKey?: string,\n    timeoutMs: number = 30000,\n    userId?: string\n  ): Promise<AIResponse> {\n    const startTime = Date.now();\n    let cacheHit = false;\n\n    // Record performance metrics (temporarily disabled)\n    // performanceMonitor.recordMetric(`${configKey}_request_start`, 1, {\n    //   userId,\n    //   promptLength: prompt.length\n    // });\n\n    try {\n      // Check rate limit first\n      if (!this.checkRateLimit(userId || 'anonymous')) {\n        // aiServiceMonitor.recordRateLimitHit(userId || 'anonymous');\n        return {\n          success: false,\n          error: 'Rate limit exceeded. Please wait before making another request.',\n        };\n      }\n\n      // Check cache first if enabled (using consolidated cache)\n      if (this.cacheEnabled && cacheKey) {\n        const cached = await consolidatedCache.get<any>(cacheKey);\n        if (cached) {\n          cacheHit = true;\n          const responseTime = Date.now() - startTime;\n\n          // Record performance metrics (temporarily disabled)\n          // performanceMonitor.recordResponseTime(configKey, responseTime, userId);\n          // performanceMonitor.recordCacheHit(configKey, true);\n\n          AIServiceLogger.debug('Basic cache hit', {\n            cacheKey: cacheKey.substring(0, 50) + '...',\n            configKey,\n            responseTime\n          });\n\n          // Record cache hit in monitoring (temporarily disabled)\n          // aiServiceMonitor.recordOperation(configKey, responseTime, true, true, userId);\n\n          return {\n            success: true,\n            data: cached,\n            cached: true,\n          };\n        } else {\n          // performanceMonitor.recordCacheHit(configKey, false);\n          AIServiceLogger.debug('Basic cache miss', {\n            cacheKey: cacheKey.substring(0, 50) + '...',\n            configKey\n          });\n        }\n      }\n\n      // Configure the model for this specific task\n      const config = configs[configKey] || configs.career_recommendations;\n\n      // Create timeout promise\n      const timeoutPromise = new Promise<never>((_, reject) => {\n        setTimeout(() => reject(new Error('AI request timeout')), timeoutMs);\n      });\n\n      // Execute with timeout (simplified without request optimizer)\n      const result = await Promise.race([\n        this.model.generateContent({\n          contents: [{ role: 'user', parts: [{ text: prompt }] }],\n          generationConfig: config,\n        }),\n        timeoutPromise\n      ]);\n\n      const response = await result.response;\n      const text = response.text();\n\n      // Security validation\n      if (!text) {\n        return {\n          success: false,\n          error: 'Empty response from AI service',\n        };\n      }\n\n      if (text.length > 50000) {\n        return {\n          success: false,\n          error: 'AI response too large',\n        };\n      }\n\n      if (this.containsHarmfulContent(text)) {\n        return {\n          success: false,\n          error: 'AI response contains inappropriate content',\n        };\n      }\n\n      // Parse response using basic JSON parsing (simplified)\n      let data;\n      try {\n        // Try to parse as JSON first\n        data = JSON.parse(text);\n      } catch (jsonError) {\n        // If JSON parsing fails, try to extract JSON from the text\n        const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n          try {\n            data = JSON.parse(jsonMatch[0]);\n          } catch (extractError) {\n            AIServiceLogger.error('Failed to parse AI response as JSON', extractError, {\n              configKey,\n              responseLength: text.length\n            });\n            return {\n              success: false,\n              error: 'Failed to parse AI response as valid JSON',\n            };\n          }\n        } else {\n          // Return raw text if no JSON found\n          data = { response: text };\n        }\n      }\n\n      // Log parsing success\n      AIServiceLogger.debug('AI response parsed successfully', {\n        configKey,\n        parseMethod: 'basic_json',\n        responseLength: text.length\n      });\n\n      // Post-process data to ensure required fields are present\n      if (configKey === 'interview_analysis') {\n        data = this.ensureInterviewAnalysisFields(data);\n      }\n\n      // Cache the result if enabled (using consolidated cache)\n      if (this.cacheEnabled && cacheKey) {\n        await consolidatedCache.set(cacheKey, data, {\n          ttl: this.cacheTTL * 1000, // Convert seconds to milliseconds\n          tags: ['ai_service', 'gemini', configKey]\n        });\n        AIServiceLogger.debug('Response cached in consolidated cache', {\n          cacheKey: cacheKey.substring(0, 50) + '...',\n          configKey,\n          ttl: this.cacheTTL\n        });\n      }\n\n      const responseTime = Date.now() - startTime;\n\n      AIServiceLogger.info('AI request completed successfully', {\n        configKey,\n        responseSize: JSON.stringify(data).length,\n        userId: userId || 'anonymous',\n        cached: false,\n        responseTime\n      });\n\n      // Record performance metrics (temporarily disabled)\n      // performanceMonitor.recordResponseTime(configKey, responseTime, userId);\n      // performanceMonitor.recordMetric(`${configKey}_success`, 1, { userId });\n\n      // Record successful operation in monitoring (temporarily disabled)\n      // aiServiceMonitor.recordOperation(configKey, responseTime, true, cacheHit, userId);\n\n      return {\n        success: true,\n        data,\n        cached: false,\n      };\n    } catch (error) {\n      const errorContext = {\n        operation: 'generateContent',\n        configKey,\n        cacheKey: cacheKey ? cacheKey.substring(0, 50) + '...' : undefined,\n        timeoutMs,\n        userId: userId || 'anonymous'\n      };\n\n      AIServiceLogger.error('Gemini AI request failed', error, errorContext);\n\n      // Categorize error types for better handling\n      let errorMessage = 'AI service unavailable';\n      let errorType = 'unknown';\n\n      if (error instanceof Error) {\n        if (error.message.includes('timeout')) {\n          errorMessage = 'AI request timed out - please try again';\n          errorType = 'timeout';\n        } else if (error.message.includes('quota') || error.message.includes('limit')) {\n          errorMessage = 'AI service quota exceeded - please try again later';\n          errorType = 'quota_exceeded';\n        } else if (error.message.includes('authentication') || error.message.includes('unauthorized')) {\n          errorMessage = 'AI service authentication failed';\n          errorType = 'auth_failed';\n        } else if (error.message.includes('network') || error.message.includes('connection')) {\n          errorMessage = 'Network connection failed - please check your internet connection';\n          errorType = 'network_error';\n        } else {\n          errorMessage = error.message;\n          errorType = 'api_error';\n        }\n      }\n\n      // Log error metrics for monitoring\n      AIServiceLogger.debug('Error categorized', { errorType, configKey, userId });\n\n      // Record performance metrics for errors (temporarily disabled)\n      const responseTime = Date.now() - startTime;\n      // performanceMonitor.recordResponseTime(configKey, responseTime, userId);\n      // performanceMonitor.recordError(configKey, errorType, userId);\n\n      // Record failed operation in monitoring (temporarily disabled)\n      // aiServiceMonitor.recordOperation(configKey, responseTime, false, cacheHit, userId, errorMessage);\n\n      return {\n        success: false,\n        error: errorMessage,\n      };\n    }\n  }\n\n  async analyzeResume(resumeText: string, userId?: string): Promise<AIResponse> {\n    // Basic validation (simplified without AIInputValidator)\n    if (!resumeText || resumeText.trim().length === 0) {\n      AIServiceLogger.warn('Resume analysis input validation failed - empty text', {\n        userId: userId || 'anonymous'\n      });\n      return {\n        success: false,\n        error: 'Resume text cannot be empty'\n      };\n    }\n\n    if (resumeText.length > 50000) {\n      AIServiceLogger.warn('Resume analysis input validation failed - too long', {\n        userId: userId || 'anonymous'\n      });\n      return {\n        success: false,\n        error: 'Resume text is too long (max 50,000 characters)'\n      };\n    }\n\n    // Basic security check for obvious harmful content\n    const harmfulPatterns = ['<script', 'javascript:', 'data:text/html'];\n    const hasHarmfulContent = harmfulPatterns.some(pattern =>\n      resumeText.toLowerCase().includes(pattern.toLowerCase())\n    );\n\n    if (hasHarmfulContent) {\n      AIServiceLogger.error('Potentially harmful content detected in resume analysis', {\n        userId: userId || 'anonymous'\n      });\n      return {\n        success: false,\n        error: 'Resume content contains potentially harmful elements'\n      };\n    }\n\n    const sanitizedResumeText = resumeText.trim();\n\n    const prompt = `\nAnalyze the following resume and provide a comprehensive assessment in JSON format:\n\nResume Text:\n${sanitizedResumeText}\n\nPlease provide your analysis in the following JSON structure:\n{\n  \"strengths\": [\"list of key strengths\"],\n  \"weaknesses\": [\"areas for improvement\"],\n  \"suggestions\": [\"specific actionable suggestions\"],\n  \"skillsIdentified\": [\"list of skills found in resume\"],\n  \"experienceLevel\": \"entry|mid|senior|executive\",\n  \"industryFit\": [\"industries this person would fit well in\"],\n  \"overallScore\": number between 1-100\n}\n\nFocus on:\n1. Professional presentation and formatting\n2. Skills and experience relevance\n3. Career progression and achievements\n4. Areas for improvement\n5. Industry alignment\n`;\n\n    const cacheKey = userId ? `resume_analysis:${userId}:${Buffer.from(resumeText).toString('base64').slice(0, 32)}` : undefined;\n\n    return this.generateContent(prompt, 'resume_analysis', cacheKey, 30000, userId);\n  }\n\n  async generateCareerRecommendations(\n    assessmentData: any,\n    currentSkills: string[],\n    preferences: any,\n    userId?: string\n  ): Promise<AIResponse> {\n    // Basic validation for skills array\n    if (!Array.isArray(currentSkills)) {\n      AIServiceLogger.warn('Career recommendations skills validation failed - not array', {\n        userId: userId || 'anonymous'\n      });\n      return {\n        success: false,\n        error: 'Skills must be provided as an array'\n      };\n    }\n\n    if (currentSkills.length === 0) {\n      AIServiceLogger.warn('Career recommendations skills validation failed - empty array', {\n        userId: userId || 'anonymous'\n      });\n      return {\n        success: false,\n        error: 'At least one skill must be provided'\n      };\n    }\n\n    const sanitizedSkills = currentSkills.filter(skill =>\n      typeof skill === 'string' && skill.trim().length > 0\n    ).map(skill => skill.trim());\n    const prompt = `\nBased on the following career assessment data, current skills, and preferences, provide personalized career path recommendations in JSON format:\n\nAssessment Data:\n${JSON.stringify(assessmentData, null, 2)}\n\nCurrent Skills:\n${sanitizedSkills.join(', ')}\n\nPreferences:\n${JSON.stringify(preferences, null, 2)}\n\nPlease provide 3-5 career recommendations in the following JSON structure:\n{\n  \"recommendations\": [\n    {\n      \"careerPath\": \"specific career title\",\n      \"matchScore\": number between 1-100,\n      \"reasoning\": \"detailed explanation of why this fits\",\n      \"requiredSkills\": [\"skills needed for this career\"],\n      \"timeToTransition\": \"estimated time to transition\",\n      \"salaryRange\": \"typical salary range\",\n      \"growthPotential\": \"career growth prospects\"\n    }\n  ]\n}\n\nConsider:\n1. Skills alignment and transferability\n2. Personal values and work preferences\n3. Market demand and growth potential\n4. Realistic transition timeline\n5. Salary expectations and growth\n`;\n\n    const cacheKey = userId ? `career_recommendations:${userId}:${Date.now().toString().slice(-8)}` : undefined;\n\n    return this.generateContent(prompt, 'career_recommendations', cacheKey, 30000, userId);\n  }\n\n  async analyzeSkillsGap(\n    currentSkills: string[],\n    targetCareerPath: string,\n    experienceLevel: string,\n    userId?: string\n  ): Promise<AIResponse> {\n    const prompt = `\nAnalyze the skills gap for transitioning to a specific career path and provide learning recommendations in JSON format:\n\nCurrent Skills:\n${currentSkills.join(', ')}\n\nTarget Career Path:\n${targetCareerPath}\n\nExperience Level:\n${experienceLevel}\n\nPlease provide your analysis in the following JSON structure:\n{\n  \"currentSkills\": [\"validated current skills relevant to target career\"],\n  \"skillGaps\": [\"skills needed but not currently possessed\"],\n  \"learningRecommendations\": [\n    {\n      \"skill\": \"specific skill name\",\n      \"priority\": \"high|medium|low\",\n      \"estimatedTime\": \"time to learn this skill\",\n      \"resources\": [\"recommended learning resources or types\"]\n    }\n  ],\n  \"careerReadiness\": number between 1-100\n}\n\nFocus on:\n1. Most critical skills for the target career\n2. Realistic learning timelines\n3. Prioritization based on impact\n4. Practical learning approaches\n5. Current market requirements\n`;\n\n    const cacheKey = userId ? `skills_analysis:${userId}:${targetCareerPath.replace(/\\s+/g, '_')}` : undefined;\n\n    return this.generateContent(prompt, 'skills_analysis', cacheKey, 30000, userId);\n  }\n\n  async analyzeComprehensiveSkillGap(\n    currentSkills: Array<{\n      skillName: string;\n      selfRating: number;\n      confidenceLevel: number;\n      lastUsed?: string;\n      yearsOfExperience?: number;\n    }>,\n    targetCareerPath: {\n      careerPathId?: string;\n      careerPathName: string;\n      targetLevel: string;\n    },\n    preferences: {\n      timeframe: string;\n      hoursPerWeek: number;\n      learningStyle: string[];\n      budget: string;\n      focusAreas: string[];\n    },\n    careerPathData: any,\n    userId?: string\n  ): Promise<AIResponse> {\n    const prompt = `\nPerform a comprehensive skill gap analysis and generate a detailed learning plan in JSON format:\n\nCURRENT SKILLS ASSESSMENT:\n${currentSkills.map(skill => `\n- ${skill.skillName}: Self-Rating ${skill.selfRating}/10, Confidence ${skill.confidenceLevel}/10\n  ${skill.yearsOfExperience ? `Experience: ${skill.yearsOfExperience} years` : ''}\n  ${skill.lastUsed ? `Last Used: ${skill.lastUsed}` : ''}\n`).join('')}\n\nTARGET CAREER PATH:\n- Career: ${targetCareerPath.careerPathName}\n- Target Level: ${targetCareerPath.targetLevel}\n- Required Skills: ${careerPathData?.requiredSkills?.map((s: any) => s.name).join(', ') || 'Not specified'}\n\nLEARNING PREFERENCES:\n- Timeframe: ${preferences.timeframe}\n- Available Hours/Week: ${preferences.hoursPerWeek}\n- Learning Style: ${preferences.learningStyle.join(', ')}\n- Budget: ${preferences.budget}\n- Focus Areas: ${preferences.focusAreas.join(', ')}\n\nAVAILABLE RESOURCES:\n${careerPathData?.learningResources?.slice(0, 10).map((resource: any) => `\n- ${resource.title} (${resource.type}) - ${resource.skillLevel} level\n  Skills: ${resource.skills?.join(', ') || 'General'}\n  Cost: ${resource.cost || 'Free'}\n`).join('') || 'No specific resources available'}\n\nPlease provide your comprehensive analysis in the following JSON structure:\n{\n  \"skillGaps\": [\n    {\n      \"skillId\": \"generated-id\",\n      \"skillName\": \"specific skill name\",\n      \"currentLevel\": number 1-10,\n      \"targetLevel\": number 1-10,\n      \"gapSeverity\": \"CRITICAL|HIGH|MEDIUM|LOW\",\n      \"priority\": number 1-100,\n      \"estimatedLearningTime\": number in hours,\n      \"marketDemand\": \"VERY_HIGH|HIGH|MODERATE|LOW|VERY_LOW\",\n      \"salaryImpact\": number percentage increase\n    }\n  ],\n  \"learningPlan\": {\n    \"totalEstimatedHours\": number,\n    \"milestones\": [\n      {\n        \"month\": number,\n        \"skills\": [\"skill names to focus on\"],\n        \"estimatedHours\": number,\n        \"learningPaths\": [\"recommended learning path names\"],\n        \"objectives\": [\"specific learning objectives\"],\n        \"assessmentCriteria\": [\"how to measure progress\"]\n      }\n    ],\n    \"recommendedResources\": [\n      {\n        \"resourceId\": \"resource-id\",\n        \"resourceType\": \"COURSE|BOOK|PROJECT|CERTIFICATION|TUTORIAL\",\n        \"priority\": \"CRITICAL|HIGH|MEDIUM|LOW|OPTIONAL\",\n        \"skillsAddressed\": [\"skill names\"],\n        \"estimatedHours\": number,\n        \"cost\": \"FREE|FREEMIUM|PAID\",\n        \"difficulty\": \"BEGINNER|INTERMEDIATE|ADVANCED|EXPERT\",\n        \"prerequisites\": [\"prerequisite skills\"]\n      }\n    ]\n  },\n  \"careerReadiness\": {\n    \"currentScore\": number 0-100,\n    \"targetScore\": number 0-100,\n    \"improvementPotential\": number 0-100,\n    \"timeToTarget\": number in months,\n    \"confidenceLevel\": number 0-100,\n    \"marketCompetitiveness\": number 0-100\n  },\n  \"marketInsights\": {\n    \"industryTrends\": [\n      {\n        \"skill\": \"skill name\",\n        \"trend\": \"DECLINING|STABLE|GROWING|RAPIDLY_GROWING|EMERGING\",\n        \"demandLevel\": \"VERY_HIGH|HIGH|MODERATE|LOW|VERY_LOW\",\n        \"futureOutlook\": \"description of future prospects\"\n      }\n    ],\n    \"salaryProjections\": {\n      \"currentEstimate\": number,\n      \"targetEstimate\": number,\n      \"improvementPotential\": number percentage,\n      \"timeToRealization\": number in months\n    },\n    \"competitiveAdvantage\": [\n      {\n        \"skill\": \"skill name\",\n        \"advantage\": \"description of competitive advantage\",\n        \"rarity\": \"COMMON|UNCOMMON|RARE|VERY_RARE\"\n      }\n    ]\n  }\n}\n\nANALYSIS GUIDELINES:\n1. Be realistic about learning timelines based on available hours\n2. Prioritize skills with highest impact on career goals\n3. Consider market demand and salary impact\n4. Account for learning style preferences\n5. Suggest progressive skill building (prerequisites first)\n6. Include both technical and soft skills\n7. Provide actionable, specific recommendations\n8. Consider budget constraints when suggesting resources\n9. Factor in skill obsolescence and future trends\n10. Personalize recommendations based on current skill levels\n\nFocus on creating a comprehensive, actionable plan that maximizes career advancement potential within the specified timeframe and constraints.\n`;\n\n    const cacheKey = userId ? `comprehensive_skills_analysis:${userId}:${targetCareerPath.careerPathName.replace(/\\s+/g, '_')}_${targetCareerPath.targetLevel}_${preferences.timeframe}` : undefined;\n\n    return this.generateContent(prompt, 'comprehensive_skills_analysis', cacheKey, 45000, userId);\n  }\n\n  async generatePersonalizedLearningPlan(\n    skillGaps: Array<{\n      skillName: string;\n      currentLevel: number;\n      targetLevel: number;\n      gapSeverity: string;\n      priority: number;\n      estimatedLearningTime: number;\n    }>,\n    userPreferences: {\n      timeframe: string;\n      hoursPerWeek: number;\n      learningStyle: string[];\n      budget: string;\n      focusAreas: string[];\n    },\n    marketData: Array<{\n      skillName: string;\n      demandLevel: string;\n      growthTrend: string;\n      salaryImpact?: number;\n    }>,\n    userId?: string\n  ): Promise<AIResponse> {\n    const prompt = `\nGenerate a personalized learning plan optimized for the user's specific needs and constraints in JSON format:\n\nSKILL GAPS TO ADDRESS:\n${skillGaps.map(gap => `\n- ${gap.skillName}: Current ${gap.currentLevel}/10 → Target ${gap.targetLevel}/10\n  Gap Severity: ${gap.gapSeverity}, Priority: ${gap.priority}/100\n  Estimated Learning Time: ${gap.estimatedLearningTime} hours\n`).join('')}\n\nUSER PREFERENCES:\n- Timeframe: ${userPreferences.timeframe}\n- Available Hours/Week: ${userPreferences.hoursPerWeek}\n- Learning Style: ${userPreferences.learningStyle.join(', ')}\n- Budget: ${userPreferences.budget}\n- Focus Areas: ${userPreferences.focusAreas.join(', ')}\n\nMARKET DATA:\n${marketData.map(data => `\n- ${data.skillName}: Demand ${data.demandLevel}, Trend ${data.growthTrend}\n  ${data.salaryImpact ? `Salary Impact: +${data.salaryImpact}%` : ''}\n`).join('')}\n\nPlease provide a personalized learning plan in the following JSON structure:\n{\n  \"learningPlan\": {\n    \"totalDuration\": \"timeframe description\",\n    \"totalHours\": number,\n    \"weeklyCommitment\": number,\n    \"phases\": [\n      {\n        \"phaseNumber\": number,\n        \"phaseName\": \"phase name\",\n        \"duration\": \"duration description\",\n        \"objectives\": [\"specific learning objectives\"],\n        \"skills\": [\n          {\n            \"skillName\": \"skill name\",\n            \"currentLevel\": number,\n            \"targetLevel\": number,\n            \"hoursAllocated\": number,\n            \"learningApproach\": \"description of approach\",\n            \"resources\": [\n              {\n                \"type\": \"COURSE|BOOK|PROJECT|TUTORIAL|PRACTICE\",\n                \"title\": \"resource title\",\n                \"description\": \"resource description\",\n                \"estimatedHours\": number,\n                \"cost\": \"FREE|FREEMIUM|PAID\",\n                \"difficulty\": \"BEGINNER|INTERMEDIATE|ADVANCED\",\n                \"learningStyle\": [\"VISUAL|AUDITORY|KINESTHETIC|READING\"],\n                \"priority\": \"HIGH|MEDIUM|LOW\"\n              }\n            ],\n            \"milestones\": [\n              {\n                \"week\": number,\n                \"milestone\": \"milestone description\",\n                \"assessmentMethod\": \"how to assess progress\"\n              }\n            ]\n          }\n        ],\n        \"practiceProjects\": [\n          {\n            \"projectName\": \"project name\",\n            \"description\": \"project description\",\n            \"skillsApplied\": [\"skill names\"],\n            \"estimatedHours\": number,\n            \"difficulty\": \"BEGINNER|INTERMEDIATE|ADVANCED\",\n            \"deliverables\": [\"expected deliverables\"]\n          }\n        ]\n      }\n    ]\n  }\n}\n\nOPTIMIZATION GUIDELINES:\n1. Prioritize high-impact, high-demand skills first\n2. Balance quick wins with long-term skill development\n3. Optimize for the user's available time and learning style\n4. Consider budget constraints and maximize free resources\n5. Include practical application through projects\n6. Build in flexibility for plan adjustments\n7. Provide clear progress measurement methods\n8. Include motivation and accountability strategies\n9. Consider skill dependencies and logical progression\n10. Integrate networking and community building\n\nCreate a plan that is realistic, actionable, and optimized for the user's success within their constraints.\n`;\n\n    const cacheKey = userId ? `personalized_learning_plan:${userId}:${userPreferences.timeframe}_${userPreferences.hoursPerWeek}h` : undefined;\n\n    return this.generateContent(prompt, 'personalized_learning_plan', cacheKey, 45000, userId);\n  }\n\n  async analyzeSkillMarketTrends(\n    skills: string[],\n    targetIndustry: string,\n    region: string = 'GLOBAL'\n  ): Promise<AIResponse> {\n    const prompt = `\nAnalyze current market trends and demand for the specified skills in JSON format:\n\nSKILLS TO ANALYZE:\n${skills.map(skill => `- ${skill}`).join('\\n')}\n\nTARGET INDUSTRY: ${targetIndustry}\nREGION: ${region}\n\nPlease provide comprehensive market analysis in the following JSON structure:\n{\n  \"marketAnalysis\": {\n    \"analysisDate\": \"${new Date().toISOString()}\",\n    \"region\": \"${region}\",\n    \"industry\": \"${targetIndustry}\",\n    \"overallMarketHealth\": \"EXCELLENT|GOOD|MODERATE|POOR|DECLINING\"\n  },\n  \"skillTrends\": [\n    {\n      \"skillName\": \"skill name\",\n      \"demandLevel\": \"VERY_HIGH|HIGH|MODERATE|LOW|VERY_LOW\",\n      \"growthTrend\": \"RAPIDLY_GROWING|GROWING|STABLE|DECLINING|RAPIDLY_DECLINING\",\n      \"marketSaturation\": \"LOW|MODERATE|HIGH|OVERSATURATED\",\n      \"averageSalaryImpact\": number percentage,\n      \"jobPostingsGrowth\": number percentage,\n      \"futureOutlook\": {\n        \"nextYear\": \"trend prediction for next year\",\n        \"nextFiveYears\": \"trend prediction for next 5 years\",\n        \"emergingOpportunities\": [\"new opportunities emerging\"],\n        \"potentialThreats\": [\"potential threats to skill relevance\"]\n      }\n    }\n  ]\n}\n\nANALYSIS GUIDELINES:\n1. Base analysis on current market trends and data\n2. Consider industry-specific factors and requirements\n3. Account for regional differences in demand\n4. Factor in technological advancement impact\n5. Consider economic factors affecting skill demand\n6. Analyze both current state and future projections\n7. Provide actionable insights for career planning\n8. Consider skill interdependencies and combinations\n9. Account for automation and AI impact on skills\n10. Provide realistic timelines and expectations\n\nFocus on providing accurate, actionable market intelligence that helps with strategic career planning and skill development decisions.\n`;\n\n    const cacheKey = `skill_market_trends:${targetIndustry}:${region}:${skills.join('_').replace(/\\s+/g, '_')}`;\n\n    return this.generateContent(prompt, 'skill_market_analysis', cacheKey, 30000);\n  }\n\n  async validateSkillAssessment(\n    skillName: string,\n    selfRating: number,\n    userContext: {\n      experienceLevel?: string;\n      industry?: string;\n      yearsOfExperience?: number;\n      relatedSkills?: string[];\n      previousAssessments?: Array<{\n        skillName: string;\n        rating: number;\n        date: string;\n      }>;\n    }\n  ): Promise<AIResponse> {\n    const prompt = `\nValidate and provide feedback on a skill self-assessment in JSON format:\n\nSKILL ASSESSMENT:\n- Skill: ${skillName}\n- Self-Rating: ${selfRating}/10\n- User Experience Level: ${userContext.experienceLevel || 'Not specified'}\n- Industry: ${userContext.industry || 'Not specified'}\n- Years of Experience: ${userContext.yearsOfExperience || 'Not specified'}\n- Related Skills: ${userContext.relatedSkills?.join(', ') || 'None specified'}\n\nPREVIOUS ASSESSMENTS:\n${userContext.previousAssessments?.map(assessment =>\n  `- ${assessment.skillName}: ${assessment.rating}/10 (${assessment.date})`\n).join('\\n') || 'No previous assessments'}\n\nPlease provide assessment validation in the following JSON structure:\n{\n  \"validationResult\": {\n    \"assessmentAccuracy\": \"ACCURATE|SLIGHTLY_HIGH|SLIGHTLY_LOW|SIGNIFICANTLY_HIGH|SIGNIFICANTLY_LOW\",\n    \"confidenceLevel\": number 0-100,\n    \"reasoning\": \"explanation of the validation assessment\",\n    \"suggestedRating\": number 1-10,\n    \"ratingJustification\": \"justification for suggested rating\"\n  },\n  \"skillAnalysis\": {\n    \"skillComplexity\": \"BASIC|INTERMEDIATE|ADVANCED|EXPERT\",\n    \"learningCurve\": \"GENTLE|MODERATE|STEEP|VERY_STEEP\",\n    \"marketValue\": \"LOW|MODERATE|HIGH|VERY_HIGH\",\n    \"industryRelevance\": \"LOW|MODERATE|HIGH|CRITICAL\",\n    \"skillCategory\": \"TECHNICAL|SOFT_SKILL|DOMAIN_SPECIFIC|LEADERSHIP|CREATIVE\"\n  },\n  \"assessmentGuidance\": {\n    \"ratingCriteria\": {\n      \"level1to2\": \"criteria for beginner level (1-2)\",\n      \"level3to4\": \"criteria for basic competency (3-4)\",\n      \"level5to6\": \"criteria for intermediate level (5-6)\",\n      \"level7to8\": \"criteria for advanced level (7-8)\",\n      \"level9to10\": \"criteria for expert level (9-10)\"\n    },\n    \"selfAssessmentTips\": [\n      \"tip for more accurate self-assessment\",\n      \"another tip for better evaluation\"\n    ],\n    \"validationMethods\": [\n      {\n        \"method\": \"validation method name\",\n        \"description\": \"how to validate this skill level\",\n        \"timeRequired\": \"time needed for validation\",\n        \"cost\": \"FREE|LOW|MODERATE|HIGH\"\n      }\n    ]\n  }\n}\n\nVALIDATION GUIDELINES:\n1. Consider industry standards and typical skill progression\n2. Account for experience level and career stage\n3. Evaluate consistency with related skills\n4. Provide constructive, actionable feedback\n5. Consider market context and skill value\n6. Suggest realistic improvement paths\n7. Identify potential over/under-estimation\n8. Provide objective validation methods\n9. Consider skill complexity and learning curve\n10. Offer benchmarking against industry standards\n\nFocus on helping the user develop more accurate self-assessment skills while providing actionable guidance for skill development.\n`;\n\n    const cacheKey = `skill_assessment_validation:${skillName.replace(/\\s+/g, '_')}:${selfRating}:${userContext.experienceLevel || 'unknown'}`;\n\n    return this.generateContent(prompt, 'skill_assessment_validation', cacheKey, 30000);\n  }\n\n  async generateInterviewPrep(\n    careerPath: string,\n    experienceLevel: string,\n    companyType: string,\n    userId?: string\n  ): Promise<AIResponse> {\n    const prompt = `\nGenerate comprehensive interview preparation materials for the following scenario in JSON format:\n\nCareer Path: ${careerPath}\nExperience Level: ${experienceLevel}\nCompany Type: ${companyType}\n\nPlease provide interview preparation in the following JSON structure:\n{\n  \"commonQuestions\": [\n    {\n      \"question\": \"interview question\",\n      \"category\": \"behavioral|technical|situational\",\n      \"sampleAnswer\": \"example answer framework\",\n      \"tips\": [\"specific tips for answering this question\"]\n    }\n  ],\n  \"industrySpecificQuestions\": [\n    {\n      \"question\": \"industry-specific question\",\n      \"difficulty\": \"easy|medium|hard\",\n      \"keyPoints\": [\"key points to address in answer\"]\n    }\n  ],\n  \"preparationTips\": [\"general interview preparation advice\"]\n}\n\nInclude:\n1. 8-10 common interview questions with sample answers\n2. 5-7 industry-specific technical questions\n3. Behavioral questions using STAR method\n4. Company research tips\n5. Questions to ask the interviewer\n`;\n\n    const cacheKey = userId ? `interview_prep:${userId}:${careerPath.replace(/\\s+/g, '_')}_${companyType}` : undefined;\n\n    return this.generateContent(prompt, 'interview_prep', cacheKey, 30000, userId);\n  }\n\n  async generateInterviewQuestions(params: {\n    sessionType: string;\n    careerPath?: string;\n    experienceLevel?: string;\n    companyType?: string;\n    industryFocus?: string;\n    specificRole?: string;\n    interviewType?: string;\n    focusAreas?: any;\n    difficulty?: string;\n    questionTypes?: string[];\n    categories?: string[];\n    count: number;\n  }): Promise<AIResponse> {\n    // Basic validation for interview parameters\n    if (!params.sessionType || typeof params.sessionType !== 'string') {\n      AIServiceLogger.warn('Interview questions parameter validation failed - invalid sessionType', {\n        params: params\n      });\n      return {\n        success: false,\n        error: 'Session type is required and must be a string'\n      };\n    }\n\n    if (!params.count || params.count < 1 || params.count > 50) {\n      AIServiceLogger.warn('Interview questions parameter validation failed - invalid count', {\n        params: params\n      });\n      return {\n        success: false,\n        error: 'Question count must be between 1 and 50'\n      };\n    }\n    const {\n      sessionType,\n      careerPath,\n      experienceLevel,\n      companyType,\n      industryFocus,\n      specificRole,\n      interviewType,\n      focusAreas,\n      difficulty,\n      questionTypes,\n      categories,\n      count\n    } = params;\n\n    const prompt = `\nGenerate ${count} interview questions for a practice session with the following parameters in JSON format:\n\nSession Type: ${sessionType}\nCareer Path: ${careerPath || 'General'}\nExperience Level: ${experienceLevel || 'INTERMEDIATE'}\nCompany Type: ${companyType || 'Corporate'}\nIndustry Focus: ${industryFocus || 'General'}\nSpecific Role: ${specificRole || 'Not specified'}\nInterview Type: ${interviewType || 'VIDEO'}\nFocus Areas: ${JSON.stringify(focusAreas) || 'General interview skills'}\nDifficulty: ${difficulty || 'INTERMEDIATE'}\nQuestion Types: ${JSON.stringify(questionTypes) || 'Mixed'}\nCategories: ${JSON.stringify(categories) || 'Mixed'}\n\nPlease provide the questions in the following JSON structure:\n{\n  \"questions\": [\n    {\n      \"questionText\": \"The actual interview question\",\n      \"questionType\": \"BEHAVIORAL|TECHNICAL|SITUATIONAL|COMPANY_CULTURE|LEADERSHIP|PROBLEM_SOLVING|COMMUNICATION|STRESS_TEST|CASE_STUDY|ROLE_SPECIFIC\",\n      \"category\": \"GENERAL|TECHNICAL_SKILLS|SOFT_SKILLS|LEADERSHIP|PROBLEM_SOLVING|COMMUNICATION|TEAMWORK|ADAPTABILITY|CREATIVITY|ANALYTICAL_THINKING|CUSTOMER_SERVICE|SALES|MANAGEMENT|STRATEGY|ETHICS|INDUSTRY_KNOWLEDGE\",\n      \"difficulty\": \"BEGINNER|INTERMEDIATE|ADVANCED|EXPERT\",\n      \"expectedDuration\": 180,\n      \"context\": \"Why this question is asked and what interviewers look for\",\n      \"hints\": {\n        \"structure\": \"Suggested answer structure (e.g., STAR method)\",\n        \"keyPoints\": [\"Key points to address\"],\n        \"commonMistakes\": [\"What to avoid\"]\n      },\n      \"followUpQuestions\": [\"Potential follow-up questions\"],\n      \"industrySpecific\": false,\n      \"tags\": [\"relevant\", \"tags\"],\n      \"isRequired\": true\n    }\n  ],\n  \"metadata\": {\n    \"sessionType\": \"${sessionType}\",\n    \"totalQuestions\": ${count},\n    \"difficultyDistribution\": {\"BEGINNER\": 0, \"INTERMEDIATE\": 0, \"ADVANCED\": 0, \"EXPERT\": 0},\n    \"categoryDistribution\": {\"TECHNICAL_SKILLS\": 0, \"SOFT_SKILLS\": 0, \"LEADERSHIP\": 0}\n  }\n}\n\nGuidelines:\n1. Ensure questions are appropriate for the experience level and role\n2. Mix different question types and categories for comprehensive practice\n3. Include both general and industry-specific questions when relevant\n4. Provide helpful context and hints for each question\n5. Make questions progressively challenging if multiple difficulty levels\n6. Include behavioral questions that can use STAR method\n7. Add technical questions relevant to the career path\n8. Consider the company type when crafting questions\n9. Ensure questions are realistic and commonly asked\n10. Provide meaningful follow-up questions that interviewers might ask\n\nFocus on creating high-quality, realistic interview questions that will genuinely help the user prepare for their target role and company type.\n`;\n\n    const cacheKey = `interview_questions:${sessionType}:${careerPath}:${difficulty}:${count}`;\n\n    return this.generateContent(prompt, 'interview_questions', cacheKey, 30000);\n  }\n\n  async analyzeInterviewResponse(params: {\n    questionText: string;\n    questionType: string;\n    questionCategory: string;\n    responseText: string;\n    responseTime: number;\n    expectedDuration: number;\n    careerPath?: string;\n    experienceLevel?: string;\n    context?: string;\n  }): Promise<AIResponse> {\n    // Validate response text\n    const responseValidation = AIInputValidator.validateInterviewResponse(params.responseText);\n    if (!responseValidation.isValid) {\n      AIServiceLogger.warn('Interview response validation failed', {\n        errors: responseValidation.errors\n      });\n      return {\n        success: false,\n        error: `Invalid response content: ${responseValidation.errors.join(', ')}`\n      };\n    }\n\n    // Validate question text\n    const questionValidation = AIInputValidator.validateTextInput(params.questionText, {\n      maxLength: 1000,\n      allowHtml: false,\n      requireAlphanumeric: true\n    });\n    if (!questionValidation.isValid) {\n      AIServiceLogger.warn('Interview question validation failed', {\n        errors: questionValidation.errors\n      });\n      return {\n        success: false,\n        error: `Invalid question content: ${questionValidation.errors.join(', ')}`\n      };\n    }\n\n    const sanitizedResponseText = responseValidation.sanitizedInput || params.responseText;\n    const sanitizedQuestionText = questionValidation.sanitizedInput || params.questionText;\n    const {\n      questionText,\n      questionType,\n      questionCategory,\n      responseText,\n      responseTime,\n      expectedDuration,\n      careerPath,\n      experienceLevel,\n      context\n    } = params;\n\n    const prompt = `\nAnalyze the following interview response and provide comprehensive feedback in JSON format:\n\nQuestion: ${sanitizedQuestionText}\nQuestion Type: ${questionType}\nQuestion Category: ${questionCategory}\nContext: ${context || 'Standard interview question'}\nExpected Duration: ${expectedDuration} seconds\nActual Response Time: ${responseTime} seconds\nCareer Path: ${careerPath || 'General'}\nExperience Level: ${experienceLevel || 'INTERMEDIATE'}\n\nResponse:\n${sanitizedResponseText}\n\nPlease provide your analysis in the following JSON structure:\n{\n  \"overallScore\": number between 1-10,\n  \"analysis\": {\n    \"contentQuality\": {\n      \"score\": number between 1-10,\n      \"feedback\": \"Detailed feedback on content quality\"\n    },\n    \"structure\": {\n      \"score\": number between 1-10,\n      \"feedback\": \"Feedback on answer structure and organization\"\n    },\n    \"relevance\": {\n      \"score\": number between 1-10,\n      \"feedback\": \"How well the answer addresses the question\"\n    },\n    \"specificity\": {\n      \"score\": number between 1-10,\n      \"feedback\": \"Use of specific examples and details\"\n    },\n    \"timing\": {\n      \"score\": number between 1-10,\n      \"feedback\": \"Appropriateness of response length and timing\"\n    }\n  },\n  \"feedback\": {\n    \"positive\": [\"What the candidate did well\"],\n    \"constructive\": [\"Areas for improvement with specific suggestions\"],\n    \"missing\": [\"Key elements that should have been included\"]\n  },\n  \"strengths\": [\"Specific strengths demonstrated in the response\"],\n  \"improvements\": [\n    {\n      \"area\": \"Area needing improvement\",\n      \"suggestion\": \"Specific actionable suggestion\",\n      \"priority\": \"high|medium|low\"\n    }\n  ],\n  \"starMethodScore\": number between 1-10 (for behavioral questions),\n  \"confidenceLevel\": number between 1-10,\n  \"communicationScore\": number between 1-10,\n  \"technicalScore\": number between 1-10 (for technical questions)\n}\n\nAnalysis Guidelines:\n1. Be constructive and encouraging while providing honest feedback\n2. Focus on specific, actionable improvements\n3. Consider the experience level when evaluating the response\n4. For behavioral questions, evaluate STAR method usage\n5. For technical questions, assess accuracy and depth\n6. Consider timing - too short may lack detail, too long may lose focus\n7. Evaluate communication clarity and professionalism\n8. Provide industry-specific feedback when relevant\n9. Highlight both strengths and areas for growth\n10. Offer specific examples of how to improve\n\nBe thorough but supportive in your analysis to help the user improve their interview skills.\n`;\n\n    return this.generateContent(prompt, 'interview_analysis', undefined, 30000);\n  }\n\n  async generatePersonalizedContent(\n    contentType: string,\n    userContext: any,\n    additionalParams: any = {},\n    userId?: string\n  ): Promise<AIResponse> {\n    const prompt = `\nGenerate personalized ${contentType} content based on the following user context:\n\nUser Context:\n${JSON.stringify(userContext, null, 2)}\n\nAdditional Parameters:\n${JSON.stringify(additionalParams, null, 2)}\n\nPlease provide relevant, actionable, and personalized content that helps the user with their career transition goals.\nFormat the response as JSON with appropriate structure for the content type.\n`;\n\n    const cacheKey = userId ? `personalized_content:${userId}:${contentType}:${Date.now().toString().slice(-8)}` : undefined;\n\n    return this.generateContent(prompt, 'career_recommendations', cacheKey, 30000, userId);\n  }\n\n  private generateRequestHash(prompt: string): string {\n    // Generate a hash of the prompt for request deduplication\n    const hash = Buffer.from(prompt).toString('base64').slice(0, 16);\n    return hash;\n  }\n\n  private getRequestPriority(configKey: string): number {\n    // Assign priority based on operation type\n    const priorityMap: { [key: string]: number } = {\n      'resume_analysis': 3,\n      'career_recommendations': 2,\n      'interview_questions': 2,\n      'skills_analysis': 1,\n      'interview_prep': 1\n    };\n\n    return priorityMap[configKey] || 1;\n  }\n\n  // Health check method\n  async healthCheck(): Promise<{ ai: boolean; cache: { redis: boolean; memory: boolean } }> {\n    let aiHealthy = false;\n\n    try {\n      const timeoutPromise = new Promise<never>((_, reject) => {\n        setTimeout(() => reject(new Error('Health check timeout')), 10000);\n      });\n\n      const result = await Promise.race([\n        this.model.generateContent({\n          contents: [{ role: 'user', parts: [{ text: 'Hello, please respond with \"OK\"' }] }],\n          generationConfig: { maxOutputTokens: 10 },\n        }),\n        timeoutPromise\n      ]);\n\n      const response = await result.response;\n      aiHealthy = response.text().toLowerCase().includes('ok');\n    } catch (error) {\n      AIServiceLogger.warn('AI health check failed', error);\n    }\n\n    // Temporarily disabled advanced health checks\n    // const cacheHealth = await redisCache.healthCheck();\n    // const advancedCacheHealth = await advancedCacheManager.healthCheck();\n    // const requestOptimizerHealth = await requestOptimizer.healthCheck();\n    // const performanceMonitorHealth = await performanceMonitor.healthCheck();\n\n    const cacheHealth = { redis: false, memory: true };\n    const advancedCacheHealth = { status: 'disabled' };\n    const requestOptimizerHealth = { status: 'disabled' };\n    const performanceMonitorHealth = { status: 'disabled' };\n\n    return {\n      ai: aiHealthy,\n      cache: cacheHealth\n    };\n  }\n\n  // Get service statistics\n  async getServiceStats() {\n    // Temporarily disabled\n    // const cacheStats = redisCache.getStats();\n    const cacheStats = { redis: { connected: false }, memory: { size: 0 } };\n    const rateLimiterStats = {\n      activeUsers: this.rateLimiter.size,\n      rateLimitPerMinute: this.RATE_LIMIT_REQUESTS_PER_MINUTE\n    };\n\n    return {\n      cache: cacheStats,\n      rateLimiter: rateLimiterStats,\n      config: {\n        cacheEnabled: this.cacheEnabled,\n        cacheTTL: this.cacheTTL,\n        model: 'gemini-1.5-flash'\n      }\n    };\n  }\n\n  // Enhanced security validation methods\n  private containsHarmfulContent(text: string): boolean {\n    const securityScan = AIInputValidator.securityScan(text);\n\n    if (securityScan.riskLevel === 'high') {\n      AIServiceLogger.error('High-risk content detected', { threats: securityScan.threats });\n      return true;\n    }\n\n    if (securityScan.riskLevel === 'medium') {\n      AIServiceLogger.warn('Medium-risk content detected', { threats: securityScan.threats });\n      // Allow medium-risk content but log it\n    }\n\n    return false;\n  }\n\n  private validateResponseStructure(data: any, configKey: string): boolean {\n    if (!data || typeof data !== 'object') {\n      return false;\n    }\n\n    // Basic validation based on config type\n    switch (configKey) {\n      case 'resume_analysis':\n        return this.validateResumeAnalysis(data);\n      case 'career_recommendations':\n        return this.validateCareerRecommendations(data);\n      case 'interview_analysis':\n        return this.validateInterviewAnalysis(data);\n      case 'interview_questions':\n        return this.validateInterviewQuestions(data);\n      default:\n        return true; // Allow unknown types for now\n    }\n  }\n\n  private validateResumeAnalysis(data: any): boolean {\n    return (\n      Array.isArray(data.strengths) &&\n      Array.isArray(data.weaknesses) &&\n      Array.isArray(data.suggestions) &&\n      typeof data.overallScore === 'number' &&\n      data.overallScore >= 0 &&\n      data.overallScore <= 100\n    );\n  }\n\n  private validateCareerRecommendations(data: any): boolean {\n    return (\n      Array.isArray(data.recommendations) &&\n      data.recommendations.every((rec: any) =>\n        typeof rec.careerPath === 'string' &&\n        typeof rec.matchScore === 'number' &&\n        rec.matchScore >= 0 &&\n        rec.matchScore <= 100\n      )\n    );\n  }\n\n  private validateInterviewAnalysis(data: any): boolean {\n    return (\n      typeof data.overallScore === 'number' &&\n      data.overallScore >= 0 &&\n      data.overallScore <= 10 &&\n      typeof data.analysis === 'object' &&\n      typeof data.feedback === 'object'\n    );\n  }\n\n  private ensureInterviewAnalysisFields(data: any): any {\n    // Ensure aiScore is present and valid\n    if (typeof data.aiScore !== 'number' || isNaN(data.aiScore)) {\n      // Try to extract from overallScore\n      if (typeof data.overallScore === 'number' && !isNaN(data.overallScore)) {\n        data.aiScore = data.overallScore;\n      } else {\n        // Fallback to a default score\n        data.aiScore = 5;\n        console.warn('AI response missing valid score, using fallback score of 5');\n      }\n    }\n\n    // Ensure score is within valid range\n    data.aiScore = Math.max(1, Math.min(10, data.aiScore));\n\n    // Ensure other required fields exist\n    if (!data.feedback) {\n      data.feedback = { strengths: [], improvements: [] };\n    }\n    if (!data.strengths) {\n      data.strengths = [];\n    }\n    if (!data.improvements) {\n      data.improvements = [];\n    }\n    if (typeof data.starMethodScore !== 'number') {\n      data.starMethodScore = data.aiScore;\n    }\n    if (typeof data.confidenceLevel !== 'number') {\n      data.confidenceLevel = 0.8;\n    }\n    if (typeof data.communicationScore !== 'number') {\n      data.communicationScore = data.aiScore;\n    }\n\n    return data;\n  }\n\n  private validateInterviewQuestions(data: any): boolean {\n    return (\n      Array.isArray(data.questions) &&\n      data.questions.every((q: any) =>\n        typeof q.questionText === 'string' &&\n        typeof q.questionType === 'string' &&\n        typeof q.category === 'string'\n      )\n    );\n  }\n\n  private sanitizeText(text: string): string {\n    const validation = AIInputValidator.validateTextInput(text, {\n      maxLength: 50000,\n      allowHtml: false,\n      allowSpecialChars: true\n    });\n\n    if (!validation.isValid) {\n      AIServiceLogger.warn('Text sanitization failed', { errors: validation.errors });\n      return '';\n    }\n\n    return validation.sanitizedInput || text;\n  }\n}\n\nexport const geminiService = new GeminiService();\nexport default geminiService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,eAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,4BAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA;AACA;AACA,IAAAE,oBAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAI,eAAA,CAAAH,OAAA;AAeA,IAAAI,cAAA;AAAA;AAAA,cAAAN,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAO,CAAA;EAAA,SAAAD,eAAA;IAAA;IAAAN,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACU,KAAAO,KAAK,GAAwB;MACnCC,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBC,YAAY,EAAE;KACf;IAAC;IAAAZ,aAAA,GAAAC,CAAA;IAEe,KAAAY,gBAAgB,GAAG,CAAC;IAAC;IAAAb,aAAA,GAAAC,CAAA;IACrB,KAAAa,eAAe,GAAG,KAAK,CAAC,CAAC;EAwC5C;EAAC;EAAAd,aAAA,GAAAC,CAAA;EAtCOK,cAAA,CAAAS,SAAA,CAAAC,OAAO,GAAb,UAAiBC,SAA2B;IAAA;IAAAjB,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAGiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;;;;;;YACpD,IAAI,IAAI,CAACC,KAAK,CAACC,MAAM,EAAE;cAAA;cAAAT,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACrB,IAAImB,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACb,KAAK,CAACG,eAAe,GAAG,IAAI,CAACG,eAAe,EAAE;gBAAA;gBAAAd,aAAA,GAAAmB,CAAA;gBAAAnB,aAAA,GAAAC,CAAA;gBAClE,IAAI,CAACO,KAAK,CAACC,MAAM,GAAG,KAAK;gBAAC;gBAAAT,aAAA,GAAAC,CAAA;gBAC1B,IAAI,CAACO,KAAK,CAACE,YAAY,GAAG,CAAC;cAC7B,CAAC,MAAM;gBAAA;gBAAAV,aAAA,GAAAmB,CAAA;gBAAAnB,aAAA,GAAAC,CAAA;gBACL,MAAM,IAAIqB,KAAK,CAAC,2DAA2D,CAAC;cAC9E;YACF,CAAC;YAAA;YAAA;cAAAtB,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;;;;;;;;;YAGgB,qBAAMgB,SAAS,EAAE;;;;;YAA1BM,MAAM,GAAGC,EAAA,CAAAC,IAAA,EAAiB;YAAA;YAAAzB,aAAA,GAAAC,CAAA;YAChC,IAAI,CAACyB,SAAS,EAAE;YAAC;YAAA1B,aAAA,GAAAC,CAAA;YACjB,sBAAOsB,MAAM;;;;;;;;YAEb,IAAI,CAACI,SAAS,EAAE;YAAC;YAAA3B,aAAA,GAAAC,CAAA;YACjB,MAAM2B,OAAK;;;;;;;;;GAEd;EAAA;EAAA5B,aAAA,GAAAC,CAAA;EAEOK,cAAA,CAAAS,SAAA,CAAAW,SAAS,GAAjB;IAAA;IAAA1B,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACE,IAAI,CAACO,KAAK,CAACI,YAAY,EAAE;IAAC;IAAAZ,aAAA,GAAAC,CAAA;IAC1B,IAAI,CAACO,KAAK,CAACE,YAAY,GAAG,CAAC;EAC7B,CAAC;EAAA;EAAAV,aAAA,GAAAC,CAAA;EAEOK,cAAA,CAAAS,SAAA,CAAAY,SAAS,GAAjB;IAAA;IAAA3B,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACE,IAAI,CAACO,KAAK,CAACE,YAAY,EAAE;IAAC;IAAAV,aAAA,GAAAC,CAAA;IAC1B,IAAI,CAACO,KAAK,CAACG,eAAe,GAAGS,IAAI,CAACC,GAAG,EAAE;IAAC;IAAArB,aAAA,GAAAC,CAAA;IAExC,IAAI,IAAI,CAACO,KAAK,CAACE,YAAY,IAAI,IAAI,CAACG,gBAAgB,EAAE;MAAA;MAAAb,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACpD,IAAI,CAACO,KAAK,CAACC,MAAM,GAAG,IAAI;MAAC;MAAAT,aAAA,GAAAC,CAAA;MACzB4B,eAAe,CAACC,IAAI,CAAC,iDAAiD,CAAC;IACzE,CAAC;IAAA;IAAA;MAAA9B,aAAA,GAAAmB,CAAA;IAAA;EACH,CAAC;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EAEDK,cAAA,CAAAS,SAAA,CAAAgB,QAAQ,GAAR;IAAA;IAAA/B,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACE,OAAA+B,QAAA,KAAY,IAAI,CAACxB,KAAK;EACxB,CAAC;EAAA;EAAAR,aAAA,GAAAC,CAAA;EACH,OAAAK,cAAC;AAAD,CAAC,CAjDD;AAmDA,IAAM2B,oBAAoB;AAAA;AAAA,CAAAjC,aAAA,GAAAC,CAAA,SAAG,IAAIK,cAAc,EAAE;AAEjD;AACA,IAAAuB,eAAA;AAAA;AAAA,cAAA7B,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAO,CAAA;EAAA,SAAAsB,gBAAA;IAAA;IAAA7B,aAAA,GAAAO,CAAA;EA2BA;EAAC;EAAAP,aAAA,GAAAC,CAAA;EAxBQ4B,eAAA,CAAAK,IAAI,GAAX,UAAYC,OAAe,EAAEC,OAAa;IAAA;IAAApC,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACxCoC,OAAO,CAACC,GAAG,CAAC,gBAAAC,MAAA,CAAgB,IAAInB,IAAI,EAAE,CAACoB,WAAW,EAAE,aAAAD,MAAA,CAAUJ,OAAO,CAAE;IAAE;IAAA,CAAAnC,aAAA,GAAAmB,CAAA,WAAAiB,OAAO;IAAA;IAAA,CAAApC,aAAA,GAAAmB,CAAA,WAAI,EAAE,EAAC;EACzF,CAAC;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EAEM4B,eAAA,CAAAC,IAAI,GAAX,UAAYK,OAAe,EAAEC,OAAa;IAAA;IAAApC,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACxCoC,OAAO,CAACP,IAAI,CAAC,gBAAAS,MAAA,CAAgB,IAAInB,IAAI,EAAE,CAACoB,WAAW,EAAE,aAAAD,MAAA,CAAUJ,OAAO,CAAE;IAAE;IAAA,CAAAnC,aAAA,GAAAmB,CAAA,WAAAiB,OAAO;IAAA;IAAA,CAAApC,aAAA,GAAAmB,CAAA,WAAI,EAAE,EAAC;EAC1F,CAAC;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EAEM4B,eAAA,CAAAY,KAAK,GAAZ,UAAaN,OAAe,EAAEM,KAAW,EAAEL,OAAa;IAAA;IAAApC,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACtDoC,OAAO,CAACI,KAAK,CAAC,gBAAAF,MAAA,CAAgB,IAAInB,IAAI,EAAE,CAACoB,WAAW,EAAE,cAAAD,MAAA,CAAWJ,OAAO,CAAE,EAAE;MAC1EM,KAAK,EAAEA,KAAK,YAAYnB,KAAK;MAAA;MAAA,CAAAtB,aAAA,GAAAmB,CAAA,WAAG;QAC9BgB,OAAO,EAAEM,KAAK,CAACN,OAAO;QACtBO,KAAK,EAAED,KAAK,CAACC,KAAK;QAClBC,IAAI,EAAEF,KAAK,CAACE;OACb;MAAA;MAAA,CAAA3C,aAAA,GAAAmB,CAAA,WAAGsB,KAAK;MACTL,OAAO,EAAAA;KACR,CAAC;EACJ,CAAC;EAAA;EAAApC,aAAA,GAAAC,CAAA;EAEM4B,eAAA,CAAAe,KAAK,GAAZ,UAAaT,OAAe,EAAEC,OAAa;IAAA;IAAApC,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACzC,IAAI,IAAI,CAAC4C,QAAQ,KAAK,OAAO,EAAE;MAAA;MAAA7C,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAC7BoC,OAAO,CAACC,GAAG,CAAC,gBAAAC,MAAA,CAAgB,IAAInB,IAAI,EAAE,CAACoB,WAAW,EAAE,cAAAD,MAAA,CAAWJ,OAAO,CAAE;MAAE;MAAA,CAAAnC,aAAA,GAAAmB,CAAA,WAAAiB,OAAO;MAAA;MAAA,CAAApC,aAAA,GAAAmB,CAAA,WAAI,EAAE,EAAC;IAC1F,CAAC;IAAA;IAAA;MAAAnB,aAAA,GAAAmB,CAAA;IAAA;EACH,CAAC;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EAzBc4B,eAAA,CAAAgB,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;EAAA;EAAA,CAAAhD,aAAA,GAAAmB,CAAA,WAAG,OAAO;EAAA;EAAA,CAAAnB,aAAA,GAAAmB,CAAA,WAAG,MAAM;EAAC;EAAAnB,aAAA,GAAAC,CAAA;EA0BtF,OAAA4B,eAAC;CAAA,CA3BD;AA2BC;AAAA7B,aAAA,GAAAC,CAAA;AA3BYgD,OAAA,CAAApB,eAAA,GAAAA,eAAA;AA6Bb;AACA,IAAMqB,MAAM;AAAA;AAAA,CAAAlD,aAAA,GAAAC,CAAA,SAAG6C,OAAO,CAACC,GAAG,CAACI,qBAAqB;AAAC;AAAAnD,aAAA,GAAAC,CAAA;AACjD;AAAI;AAAA,CAAAD,aAAA,GAAAmB,CAAA,YAAC+B,MAAM;AAAA;AAAA,CAAAlD,aAAA,GAAAmB,CAAA,WAAI+B,MAAM,CAACE,MAAM,GAAG,EAAE,GAAE;EAAA;EAAApD,aAAA,GAAAmB,CAAA;EAAAnB,aAAA,GAAAC,CAAA;EACjC4B,eAAe,CAACY,KAAK,CAAC,yEAAyE,CAAC;EAAC;EAAAzC,aAAA,GAAAC,CAAA;EACjG,MAAM,IAAIqB,KAAK,CAAC,wEAAwE,CAAC;AAC3F,CAAC;AAAA;AAAA;EAAAtB,aAAA,GAAAmB,CAAA;AAAA;AAED;AACA,IAAMkC,KAAK;AAAA;AAAA,CAAArD,aAAA,GAAAC,CAAA,SAAG,IAAIF,eAAA,CAAAuD,kBAAkB,CAACJ,MAAM,CAAC;AAE5C;AACA,IAAMK,OAAO;AAAA;AAAA,CAAAvD,aAAA,GAAAC,CAAA,SAAqC;EAChDuD,eAAe,EAAE;IACfC,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAI;IACVC,eAAe,EAAE;GAClB;EACDC,sBAAsB,EAAE;IACtBJ,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAI;IACVC,eAAe,EAAE;GAClB;EACDE,eAAe,EAAE;IACfL,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAI;IACVC,eAAe,EAAE;GAClB;EACDG,cAAc,EAAE;IACdN,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAI;IACVC,eAAe,EAAE;GAClB;EACDI,mBAAmB,EAAE;IACnBP,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAI;IACVC,eAAe,EAAE;GAClB;EACDK,kBAAkB,EAAE;IAClBR,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAI;IACVC,eAAe,EAAE;;CAEpB;AA0FD;AACA,IAAMM,gBAAgB;AAAA;AAAA,CAAAlE,aAAA,GAAAC,CAAA,SAAG;EACvBuD,eAAe,EAAE;IACfW,IAAI,EAAE,QAAiB;IACvBC,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,CAAC;IACpEC,UAAU,EAAE;MACVC,SAAS,EAAE;QAAEH,IAAI,EAAE,OAAgB;QAAEI,KAAK,EAAE;UAAEJ,IAAI,EAAE;QAAiB;MAAE,CAAE;MACzEK,UAAU,EAAE;QAAEL,IAAI,EAAE,OAAgB;QAAEI,KAAK,EAAE;UAAEJ,IAAI,EAAE;QAAiB;MAAE,CAAE;MAC1EM,WAAW,EAAE;QAAEN,IAAI,EAAE,OAAgB;QAAEI,KAAK,EAAE;UAAEJ,IAAI,EAAE;QAAiB;MAAE,CAAE;MAC3EO,gBAAgB,EAAE;QAAEP,IAAI,EAAE,OAAgB;QAAEI,KAAK,EAAE;UAAEJ,IAAI,EAAE;QAAiB;MAAE,CAAE;MAChFQ,eAAe,EAAE;QAAER,IAAI,EAAE,QAAiB;QAAES,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW;MAAC,CAAE;MAC3FC,WAAW,EAAE;QAAEV,IAAI,EAAE,OAAgB;QAAEI,KAAK,EAAE;UAAEJ,IAAI,EAAE;QAAiB;MAAE,CAAE;MAC3EW,YAAY,EAAE;QAAEX,IAAI,EAAE,QAAiB;QAAEY,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAG;;GAE5D;EACDnB,sBAAsB,EAAE;IACtBM,IAAI,EAAE,QAAiB;IACvBC,QAAQ,EAAE,CAAC,iBAAiB,CAAC;IAC7BC,UAAU,EAAE;MACVY,eAAe,EAAE;QACfd,IAAI,EAAE,OAAgB;QACtBI,KAAK,EAAE;UACLJ,IAAI,EAAE,QAAiB;UACvBC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC;UACnDC,UAAU,EAAE;YACVa,UAAU,EAAE;cAAEf,IAAI,EAAE,QAAiB;cAAEgB,SAAS,EAAE,CAAC;cAAEC,SAAS,EAAE;YAAG,CAAE;YACrEC,UAAU,EAAE;cAAElB,IAAI,EAAE,QAAiB;cAAEY,GAAG,EAAE,CAAC;cAAEC,GAAG,EAAE;YAAG,CAAE;YACzDM,SAAS,EAAE;cAAEnB,IAAI,EAAE,QAAiB;cAAEgB,SAAS,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAI,CAAE;YACtEG,cAAc,EAAE;cAAEpB,IAAI,EAAE,OAAgB;cAAEI,KAAK,EAAE;gBAAEJ,IAAI,EAAE;cAAiB;YAAE,CAAE;YAC9EqB,gBAAgB,EAAE;cAAErB,IAAI,EAAE;YAAiB,CAAE;YAC7CsB,WAAW,EAAE;cAAEtB,IAAI,EAAE;YAAiB,CAAE;YACxCuB,eAAe,EAAE;cAAEvB,IAAI,EAAE;YAAiB;;;;;GAKnD;EACDH,mBAAmB,EAAE;IACnBG,IAAI,EAAE,QAAiB;IACvBC,QAAQ,EAAE,CAAC,WAAW,CAAC;IACvBC,UAAU,EAAE;MACVsB,SAAS,EAAE;QACTxB,IAAI,EAAE,OAAgB;QACtBI,KAAK,EAAE;UACLJ,IAAI,EAAE,QAAiB;UACvBC,QAAQ,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC;UACpEC,UAAU,EAAE;YACVuB,YAAY,EAAE;cAAEzB,IAAI,EAAE,QAAiB;cAAEgB,SAAS,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAI,CAAE;YACzES,YAAY,EAAE;cAAE1B,IAAI,EAAE;YAAiB,CAAE;YACzC2B,QAAQ,EAAE;cAAE3B,IAAI,EAAE;YAAiB,CAAE;YACrC4B,UAAU,EAAE;cAAE5B,IAAI,EAAE,QAAiB;cAAES,IAAI,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ;YAAC,CAAE;YACjGoB,gBAAgB,EAAE;cAAE7B,IAAI,EAAE,QAAiB;cAAEY,GAAG,EAAE,EAAE;cAAEC,GAAG,EAAE;YAAG,CAAE;YAChE5C,OAAO,EAAE;cAAE+B,IAAI,EAAE;YAAiB,CAAE;YACpC8B,KAAK,EAAE;cAAE9B,IAAI,EAAE;YAAiB,CAAE;YAClC+B,iBAAiB,EAAE;cAAE/B,IAAI,EAAE;YAAgB,CAAE;YAC7CgC,gBAAgB,EAAE;cAAEhC,IAAI,EAAE;YAAkB,CAAE;YAC9CiC,IAAI,EAAE;cAAEjC,IAAI,EAAE;YAAgB,CAAE;YAChCkC,UAAU,EAAE;cAAElC,IAAI,EAAE;YAAkB;;;OAG3C;MACDmC,QAAQ,EAAE;QAAEnC,IAAI,EAAE;MAAiB;;GAEtC;EACDF,kBAAkB,EAAE;IAClBE,IAAI,EAAE,QAAiB;IACvBC,QAAQ,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;IAClDC,UAAU,EAAE;MACVS,YAAY,EAAE;QAAEX,IAAI,EAAE,QAAiB;QAAEY,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAE;MAC1DuB,QAAQ,EAAE;QAAEpC,IAAI,EAAE;MAAiB,CAAE;MACrCqC,QAAQ,EAAE;QAAErC,IAAI,EAAE;MAAiB,CAAE;MACrCG,SAAS,EAAE;QAAEH,IAAI,EAAE;MAAgB,CAAE;MACrCsC,YAAY,EAAE;QAAEtC,IAAI,EAAE;MAAgB,CAAE;MACxCuC,eAAe,EAAE;QAAEvC,IAAI,EAAE,QAAiB;QAAEY,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAE;MAC7D2B,eAAe,EAAE;QAAExC,IAAI,EAAE,QAAiB;QAAEY,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAC,CAAE;MAC5D4B,kBAAkB,EAAE;QAAEzC,IAAI,EAAE,QAAiB;QAAEY,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAE;MAChE6B,cAAc,EAAE;QAAE1C,IAAI,EAAE,QAAiB;QAAEY,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE;;;CAG/D;AAOD,IAAA8B,aAAA;AAAA;AAAA,cAAA9G,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAO,CAAA;EAQE,SAAAuG,cAAA;IAAA;IAAA9G,aAAA,GAAAO,CAAA;IAAA,IAAAwG,KAAA;IAAA;IAAA,CAAA/G,aAAA,GAAAC,CAAA;IAiCC;IAAAD,aAAA,GAAAC,CAAA;IApCgB,KAAA+G,8BAA8B,GAAG,EAAE,CAAC,CAAC;IAAA;IAAAhH,aAAA,GAAAC,CAAA;IACrC,KAAAgH,oBAAoB,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAAA;IAAAjH,aAAA,GAAAC,CAAA;IAGjD,IAAI;MAAA;MAAAD,aAAA,GAAAC,CAAA;MACF,IAAI,CAACiH,KAAK,GAAG7D,KAAK,CAAC8D,kBAAkB,CAAC;QAAED,KAAK,EAAE;MAAkB,CAAE,CAAC;MAAC;MAAAlH,aAAA,GAAAC,CAAA;MACrE,IAAI,CAACmH,YAAY,GAAGtE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;MAAC;MAAAhD,aAAA,GAAAC,CAAA;MAC1D,IAAI,CAACoH,WAAW,GAAG,IAAIC,GAAG,EAAE;MAE5B;MACA,IAAMC,WAAW;MAAA;MAAA,CAAAvH,aAAA,GAAAC,CAAA;MAAG;MAAA,CAAAD,aAAA,GAAAmB,CAAA,WAAA2B,OAAO,CAACC,GAAG,CAACyE,YAAY;MAAA;MAAA,CAAAxH,aAAA,GAAAmB,CAAA,WAAI,MAAM;MACtD,IAAMsG,SAAS;MAAA;MAAA,CAAAzH,aAAA,GAAAC,CAAA,SAAGyH,QAAQ,CAACH,WAAW,CAAC;MAAC;MAAAvH,aAAA,GAAAC,CAAA;MACxC;MAAI;MAAA,CAAAD,aAAA,GAAAmB,CAAA,WAAAwG,KAAK,CAACF,SAAS,CAAC;MAAA;MAAA,CAAAzH,aAAA,GAAAmB,CAAA,WAAIsG,SAAS,GAAG,CAAC,GAAE;QAAA;QAAAzH,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACrC4B,eAAe,CAACC,IAAI,CAAC,wDAAwD,CAAC;QAAC;QAAA9B,aAAA,GAAAC,CAAA;QAC/E,IAAI,CAAC2H,QAAQ,GAAG,IAAI;MACtB,CAAC,MAAM;QAAA;QAAA5H,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACL,IAAI,CAAC2H,QAAQ,GAAGH,SAAS;MAC3B;MAEA;MAAA;MAAAzH,aAAA,GAAAC,CAAA;MACA4H,WAAW,CAAC;QAAA;QAAA7H,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAAM,OAAA8G,KAAI,CAACe,kBAAkB,EAAE;MAAzB,CAAyB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAE3D;MACA;MAAA;MAAA9H,aAAA,GAAAC,CAAA;MAEA4B,eAAe,CAACK,IAAI,CAAC,4CAA4C,EAAE;QACjEgF,KAAK,EAAE,kBAAkB;QACzBE,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BQ,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBG,kBAAkB,EAAE,IAAI,CAACf,8BAA8B;QACvDgB,+BAA+B,EAAE;OAClC,CAAC;IACJ,CAAC,CAAC,OAAOvF,KAAK,EAAE;MAAA;MAAAzC,aAAA,GAAAC,CAAA;MACd4B,eAAe,CAACY,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAAC;MAAAzC,aAAA,GAAAC,CAAA;MACvE,MAAM,IAAIqB,KAAK,CAAC,yCAAyC,CAAC;IAC5D;EACF;EAAC;EAAAtB,aAAA,GAAAC,CAAA;EAEa6G,aAAA,CAAA/F,SAAA,CAAAkH,kCAAkC,GAAhD;IAAA;IAAAjI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAoDiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;;;;;QACzD,IAAI;UAAA;UAAAD,aAAA,GAAAC,CAAA;UACF;UACA;UAEA;UACA;UACA;UACA;UACA;UAEA4B,eAAe,CAACK,IAAI,CAAC,iEAAiE,CAAC;QACzF,CAAC,CAAC,OAAOO,KAAK,EAAE;UAAA;UAAAzC,aAAA,GAAAC,CAAA;UACd4B,eAAe,CAACY,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QAChF;QAAC;QAAAzC,aAAA,GAAAC,CAAA;;;;GACF;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAmH,cAAc,GAAtB,UAAuBC,MAA4B;IAAA;IAAAnI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAA5B,IAAAkI,MAAA;MAAA;MAAAnI,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAAAkI,MAAA,cAA4B;IAAA;IAAA;IAAA;MAAAnI,aAAA,GAAAmB,CAAA;IAAA;IACjD,IAAME,GAAG;IAAA;IAAA,CAAArB,aAAA,GAAAC,CAAA,SAAGmB,IAAI,CAACC,GAAG,EAAE;IACtB,IAAM+G,SAAS;IAAA;IAAA,CAAApI,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACoH,WAAW,CAACgB,GAAG,CAACF,MAAM,CAAC;IAAC;IAAAnI,aAAA,GAAAC,CAAA;IAE/C,IAAI,CAACmI,SAAS,EAAE;MAAA;MAAApI,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACd;MACA,IAAI,CAACoH,WAAW,CAACiB,GAAG,CAACH,MAAM,EAAE;QAC3BI,KAAK,EAAE,CAAC;QACRC,SAAS,EAAEnH,GAAG,GAAG,IAAI,CAAC4F;OACvB,CAAC;MAAC;MAAAjH,aAAA,GAAAC,CAAA;MACH4B,eAAe,CAACe,KAAK,CAAC,iCAAiC,EAAE;QAAEuF,MAAM,EAAAA,MAAA;QAAEI,KAAK,EAAE;MAAC,CAAE,CAAC;MAAC;MAAAvI,aAAA,GAAAC,CAAA;MAC/E,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAmB,CAAA;IAAA;IAED;IAAAnB,aAAA,GAAAC,CAAA;IACA,IAAIoB,GAAG,GAAG+G,SAAS,CAACI,SAAS,EAAE;MAAA;MAAAxI,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAC7B;MACA,IAAI,CAACoH,WAAW,CAACiB,GAAG,CAACH,MAAM,EAAE;QAC3BI,KAAK,EAAE,CAAC;QACRC,SAAS,EAAEnH,GAAG,GAAG,IAAI,CAAC4F;OACvB,CAAC;MAAC;MAAAjH,aAAA,GAAAC,CAAA;MACH4B,eAAe,CAACe,KAAK,CAAC,kCAAkC,EAAE;QAAEuF,MAAM,EAAAA,MAAA;QAAEI,KAAK,EAAE;MAAC,CAAE,CAAC;MAAC;MAAAvI,aAAA,GAAAC,CAAA;MAChF,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAmB,CAAA;IAAA;IAED;IAAAnB,aAAA,GAAAC,CAAA;IACA,IAAImI,SAAS,CAACG,KAAK,GAAG,IAAI,CAACvB,8BAA8B,EAAE;MAAA;MAAAhH,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACzDmI,SAAS,CAACG,KAAK,EAAE;MAAC;MAAAvI,aAAA,GAAAC,CAAA;MAClB4B,eAAe,CAACe,KAAK,CAAC,yBAAyB,EAAE;QAAEuF,MAAM,EAAAA,MAAA;QAAEI,KAAK,EAAEH,SAAS,CAACG,KAAK;QAAEE,KAAK,EAAE,IAAI,CAACzB;MAA8B,CAAE,CAAC;MAAC;MAAAhH,aAAA,GAAAC,CAAA;MACjI,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IAED4B,eAAe,CAACC,IAAI,CAAC,qBAAqB,EAAE;MAAEqG,MAAM,EAAAA,MAAA;MAAEI,KAAK,EAAEH,SAAS,CAACG,KAAK;MAAEE,KAAK,EAAE,IAAI,CAACzB;IAA8B,CAAE,CAAC;IAAC;IAAAhH,aAAA,GAAAC,CAAA;IAC5H,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAA+G,kBAAkB,GAA1B;IAAA;IAAA9H,aAAA,GAAAO,CAAA;IACE,IAAMc,GAAG;IAAA;IAAA,CAAArB,aAAA,GAAAC,CAAA,SAAGmB,IAAI,CAACC,GAAG,EAAE;IAAC;IAAArB,aAAA,GAAAC,CAAA;IACvB,KAA8B,IAAAyI,EAAA;MAAA;MAAA,CAAA1I,aAAA,GAAAC,CAAA,UAAsC,GAAtCuB,EAAA;MAAA;MAAA,CAAAxB,aAAA,GAAAC,CAAA,SAAA0I,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvB,WAAW,CAACwB,OAAO,EAAE,CAAC,GAAtCH,EAAA,GAAAlH,EAAA,CAAA4B,MAAsC,EAAtCsF,EAAA,EAAsC,EAAE;MAA3D,IAAAI,EAAA;QAAA;QAAA,CAAA9I,aAAA,GAAAC,CAAA,SAAAuB,EAAA,CAAAkH,EAAA,CAAe;QAAdP,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAC,CAAA,SAAA6I,EAAA;QAAEL,KAAK;QAAA;QAAA,CAAAzI,aAAA,GAAAC,CAAA,SAAA6I,EAAA;MAAA;MAAA9I,aAAA,GAAAC,CAAA;MACvB,IAAIoB,GAAG,GAAGoH,KAAK,CAACD,SAAS,EAAE;QAAA;QAAAxI,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACzB,IAAI,CAACoH,WAAW,CAAC0B,MAAM,CAACZ,MAAM,CAAC;MACjC,CAAC;MAAA;MAAA;QAAAnI,aAAA,GAAAmB,CAAA;MAAA;IACH;EACF,CAAC;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAiI,oBAAoB,GAA5B,UAA6BC,SAAiB;IAAA;IAAAjJ,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAC5C,QAAQgJ,SAAS;MACf,KAAK,iBAAiB;QAAA;QAAAjJ,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACpB,OAAO;UACLqE,SAAS,EAAE,CAAC,2BAA2B,EAAE,oBAAoB,CAAC;UAC9DE,UAAU,EAAE,CAAC,0CAA0C,EAAE,kCAAkC,CAAC;UAC5FC,WAAW,EAAE,CAAC,6CAA6C,EAAE,iCAAiC,CAAC;UAC/FC,gBAAgB,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,UAAU,CAAC;UAClEC,eAAe,EAAE,KAAK;UACtBE,WAAW,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;UACvCC,YAAY,EAAE;SACf;MAEH,KAAK,wBAAwB;QAAA;QAAA9E,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QAC3B,OAAO;UACLgF,eAAe,EAAE,CACf;YACEC,UAAU,EAAE,oBAAoB;YAChCG,UAAU,EAAE,EAAE;YACdC,SAAS,EAAE,iGAAiG;YAC5GC,cAAc,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,SAAS,CAAC;YAC7DC,gBAAgB,EAAE,aAAa;YAC/BC,WAAW,EAAE,oBAAoB;YACjCC,eAAe,EAAE;WAClB;SAEJ;MAEH,KAAK,qBAAqB;QAAA;QAAA1F,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACxB,OAAO;UACL0F,SAAS,EAAE,CACT;YACEC,YAAY,EAAE,0DAA0D;YACxEC,YAAY,EAAE,YAAY;YAC1BC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,cAAc;YAC1BC,gBAAgB,EAAE,GAAG;YACrB5D,OAAO,EAAE,mEAAmE;YAC5E6D,KAAK,EAAE;cACLiD,SAAS,EAAE,mEAAmE;cAC9EC,SAAS,EAAE,CAAC,cAAc,EAAE,kBAAkB,EAAE,cAAc;aAC/D;YACDjD,iBAAiB,EAAE,CAAC,0CAA0C,CAAC;YAC/DC,gBAAgB,EAAE,KAAK;YACvBC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;YAC5BC,UAAU,EAAE;WACb,CACF;UACDC,QAAQ,EAAE;YACR8C,WAAW,EAAE,SAAS;YACtBC,cAAc,EAAE,CAAC;YACjBC,sBAAsB,EAAE;cAAEC,YAAY,EAAE;YAAC,CAAE;YAC3CC,oBAAoB,EAAE;cAAEC,OAAO,EAAE;YAAC;;SAErC;MAEH,KAAK,oBAAoB;QAAA;QAAAzJ,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACvB,OAAO;UACL6E,YAAY,EAAE,CAAC;UACfyB,QAAQ,EAAE;YACRmD,cAAc,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAEnD,QAAQ,EAAE;YAAmC,CAAE;YAC3E0C,SAAS,EAAE;cAAES,KAAK,EAAE,CAAC;cAAEnD,QAAQ,EAAE;YAAwC,CAAE;YAC3EoD,SAAS,EAAE;cAAED,KAAK,EAAE,CAAC;cAAEnD,QAAQ,EAAE;YAAsC;WACxE;UACDA,QAAQ,EAAE;YACRqD,QAAQ,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,CAAC;YACtDC,YAAY,EAAE,CAAC,qCAAqC,EAAE,4BAA4B;WACnF;UACDxF,SAAS,EAAE,CAAC,2BAA2B,EAAE,+BAA+B,CAAC;UACzEmC,YAAY,EAAE,CACZ;YAAEsD,IAAI,EAAE,WAAW;YAAEC,UAAU,EAAE,0CAA0C;YAAEC,QAAQ,EAAE;UAAM,CAAE,CAChG;UACDvD,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,GAAG;UACpBC,kBAAkB,EAAE,CAAC;UACrBC,cAAc,EAAE;SACjB;MAEH;QAAA;QAAA7G,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACE,OAAO;UAAEiK,OAAO,EAAE;QAAoD,CAAE;IAC5E;EACF,CAAC;EAAA;EAAAlK,aAAA,GAAAC,CAAA;EAEa6G,aAAA,CAAA/F,SAAA,CAAAoJ,eAAe,GAA7B,UAAAC,QAAA,EAAAC,WAAA,EAAAC,UAAA;IAAA;IAAAtK,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;sCAMGiB,OAAO,YALRqJ,MAAc,EACdtB,SAAiB,EACjBuB,QAAiB,EACjBC,SAAyB,EACzBtC,MAAe;MAAA;MAAAnI,aAAA,GAAAO,CAAA;;;;MADf,IAAAkK,SAAA;QAAA;QAAAzK,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QAAAwK,SAAA,QAAyB;MAAA;MAAA;MAAA;QAAAzK,aAAA,GAAAmB,CAAA;MAAA;MAAAnB,aAAA,GAAAC,CAAA;;;;;;;;;;YAGnByK,SAAS,GAAGtJ,IAAI,CAACC,GAAG,EAAE;YAAC;YAAArB,aAAA,GAAAC,CAAA;YACzB0K,QAAQ,GAAG,KAAK;YAAC;YAAA3K,aAAA,GAAAC,CAAA;;;;;;;YASnB;YAAA;YAAAD,aAAA,GAAAC,CAAA;YACA,IAAI,CAAC,IAAI,CAACiI,cAAc;YAAC;YAAA,CAAAlI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;YAAA;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW,EAAC,EAAE;cAAA;cAAAnB,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAC/C;cACA,sBAAO;gBACL2K,OAAO,EAAE,KAAK;gBACdnI,KAAK,EAAE;eACR;YACH,CAAC;YAAA;YAAA;cAAAzC,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;;YAGG;YAAA,CAAAD,aAAA,GAAAmB,CAAA,eAAI,CAACiG,YAAY;YAAA;YAAA,CAAApH,aAAA,GAAAmB,CAAA,WAAIqJ,QAAQ,IAA7B;cAAA;cAAAxK,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAAA;YAAA,CAA6B;YAAA;YAAA;cAAAD,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAChB,qBAAME,4BAAA,CAAA0K,iBAAiB,CAACxC,GAAG,CAAMmC,QAAQ,CAAC;;;;;YAAnDM,MAAM,GAAGtJ,EAAA,CAAAC,IAAA,EAA0C;YAAA;YAAAzB,aAAA,GAAAC,CAAA;YACzD,IAAI6K,MAAM,EAAE;cAAA;cAAA9K,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACV0K,QAAQ,GAAG,IAAI;cAAC;cAAA3K,aAAA,GAAAC,CAAA;cACV8K,cAAA,GAAe3J,IAAI,CAACC,GAAG,EAAE,GAAGqJ,SAAS;cAE3C;cACA;cACA;cAAA;cAAA1K,aAAA,GAAAC,CAAA;cAEA4B,eAAe,CAACe,KAAK,CAAC,iBAAiB,EAAE;gBACvC4H,QAAQ,EAAEA,QAAQ,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;gBAC3C/B,SAAS,EAAAA,SAAA;gBACTgC,YAAY,EAAAF;eACb,CAAC;cAEF;cACA;cAAA;cAAA/K,aAAA,GAAAC,CAAA;cAEA,sBAAO;gBACL2K,OAAO,EAAE,IAAI;gBACbM,IAAI,EAAEJ,MAAM;gBACZA,MAAM,EAAE;eACT;YACH,CAAC,MAAM;cAAA;cAAA9K,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACL;cACA4B,eAAe,CAACe,KAAK,CAAC,kBAAkB,EAAE;gBACxC4H,QAAQ,EAAEA,QAAQ,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;gBAC3C/B,SAAS,EAAAA;eACV,CAAC;YACJ;YAAC;YAAAjJ,aAAA,GAAAC,CAAA;;;;;;YAIGkL,MAAM;YAAG;YAAA,CAAAnL,aAAA,GAAAmB,CAAA,WAAAoC,OAAO,CAAC0F,SAAS,CAAC;YAAA;YAAA,CAAAjJ,aAAA,GAAAmB,CAAA,WAAIoC,OAAO,CAACM,sBAAsB;YAAC;YAAA7D,aAAA,GAAAC,CAAA;YAG9DmL,cAAc,GAAG,IAAIlK,OAAO,CAAQ,UAACmK,CAAC,EAAEC,MAAM;cAAA;cAAAtL,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAClDsL,UAAU,CAAC;gBAAA;gBAAAvL,aAAA,GAAAO,CAAA;gBAAAP,aAAA,GAAAC,CAAA;gBAAM,OAAAqL,MAAM,CAAC,IAAIhK,KAAK,CAAC,oBAAoB,CAAC,CAAC;cAAvC,CAAuC,EAAEmJ,SAAS,CAAC;YACtE,CAAC,CAAC;YAAC;YAAAzK,aAAA,GAAAC,CAAA;YAGY,qBAAMiB,OAAO,CAACsK,IAAI,CAAC,CAChC,IAAI,CAACtE,KAAK,CAACiD,eAAe,CAAC;cACzBsB,QAAQ,EAAE,CAAC;gBAAEC,IAAI,EAAE,MAAM;gBAAEC,KAAK,EAAE,CAAC;kBAAEC,IAAI,EAAErB;gBAAM,CAAE;cAAC,CAAE,CAAC;cACvDsB,gBAAgB,EAAEV;aACnB,CAAC,EACFC,cAAc,CACf,CAAC;;;;;YANI7J,MAAM,GAAGC,EAAA,CAAAC,IAAA,EAMb;YAAA;YAAAzB,aAAA,GAAAC,CAAA;YAEe,qBAAMsB,MAAM,CAACuK,QAAQ;;;;;YAAhCA,QAAQ,GAAGtK,EAAA,CAAAC,IAAA,EAAqB;YAAA;YAAAzB,aAAA,GAAAC,CAAA;YAChC2L,IAAI,GAAGE,QAAQ,CAACF,IAAI,EAAE;YAE5B;YAAA;YAAA5L,aAAA,GAAAC,CAAA;YACA,IAAI,CAAC2L,IAAI,EAAE;cAAA;cAAA5L,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACT,sBAAO;gBACL2K,OAAO,EAAE,KAAK;gBACdnI,KAAK,EAAE;eACR;YACH,CAAC;YAAA;YAAA;cAAAzC,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAED,IAAI2L,IAAI,CAACxI,MAAM,GAAG,KAAK,EAAE;cAAA;cAAApD,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACvB,sBAAO;gBACL2K,OAAO,EAAE,KAAK;gBACdnI,KAAK,EAAE;eACR;YACH,CAAC;YAAA;YAAA;cAAAzC,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAED,IAAI,IAAI,CAAC8L,sBAAsB,CAACH,IAAI,CAAC,EAAE;cAAA;cAAA5L,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACrC,sBAAO;gBACL2K,OAAO,EAAE,KAAK;gBACdnI,KAAK,EAAE;eACR;YACH,CAAC;YAAA;YAAA;cAAAzC,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAGGiL,IAAI;YAAC;YAAAlL,aAAA,GAAAC,CAAA;YACT,IAAI;cAAA;cAAAD,aAAA,GAAAC,CAAA;cACF;cACAiL,IAAI,GAAGc,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC;YACzB,CAAC,CAAC,OAAOM,SAAS,EAAE;cAAA;cAAAlM,aAAA,GAAAC,CAAA;cAEZkM,SAAS,GAAGP,IAAI,CAACQ,KAAK,CAAC,aAAa,CAAC;cAAC;cAAApM,aAAA,GAAAC,CAAA;cAC5C,IAAIkM,SAAS,EAAE;gBAAA;gBAAAnM,aAAA,GAAAmB,CAAA;gBAAAnB,aAAA,GAAAC,CAAA;gBACb,IAAI;kBAAA;kBAAAD,aAAA,GAAAC,CAAA;kBACFiL,IAAI,GAAGc,IAAI,CAACC,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC,OAAOE,YAAY,EAAE;kBAAA;kBAAArM,aAAA,GAAAC,CAAA;kBACrB4B,eAAe,CAACY,KAAK,CAAC,qCAAqC,EAAE4J,YAAY,EAAE;oBACzEpD,SAAS,EAAAA,SAAA;oBACTqD,cAAc,EAAEV,IAAI,CAACxI;mBACtB,CAAC;kBAAC;kBAAApD,aAAA,GAAAC,CAAA;kBACH,sBAAO;oBACL2K,OAAO,EAAE,KAAK;oBACdnI,KAAK,EAAE;mBACR;gBACH;cACF,CAAC,MAAM;gBAAA;gBAAAzC,aAAA,GAAAmB,CAAA;gBAAAnB,aAAA,GAAAC,CAAA;gBACL;gBACAiL,IAAI,GAAG;kBAAEY,QAAQ,EAAEF;gBAAI,CAAE;cAC3B;YACF;YAEA;YAAA;YAAA5L,aAAA,GAAAC,CAAA;YACA4B,eAAe,CAACe,KAAK,CAAC,iCAAiC,EAAE;cACvDqG,SAAS,EAAAA,SAAA;cACTsD,WAAW,EAAE,YAAY;cACzBD,cAAc,EAAEV,IAAI,CAACxI;aACtB,CAAC;YAEF;YAAA;YAAApD,aAAA,GAAAC,CAAA;YACA,IAAIgJ,SAAS,KAAK,oBAAoB,EAAE;cAAA;cAAAjJ,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACtCiL,IAAI,GAAG,IAAI,CAACsB,6BAA6B,CAACtB,IAAI,CAAC;YACjD,CAAC;YAAA;YAAA;cAAAlL,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;;YAGG;YAAA,CAAAD,aAAA,GAAAmB,CAAA,eAAI,CAACiG,YAAY;YAAA;YAAA,CAAApH,aAAA,GAAAmB,CAAA,WAAIqJ,QAAQ,IAA7B;cAAA;cAAAxK,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAAA;YAAA,CAA6B;YAAA;YAAA;cAAAD,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAC/B,qBAAME,4BAAA,CAAA0K,iBAAiB,CAACvC,GAAG,CAACkC,QAAQ,EAAEU,IAAI,EAAE;cAC1CuB,GAAG,EAAE,IAAI,CAAC7E,QAAQ,GAAG,IAAI;cAAE;cAC3BxB,IAAI,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE6C,SAAS;aACzC,CAAC;;;;;YAHFzH,EAAA,CAAAC,IAAA,EAGE;YAAC;YAAAzB,aAAA,GAAAC,CAAA;YACH4B,eAAe,CAACe,KAAK,CAAC,uCAAuC,EAAE;cAC7D4H,QAAQ,EAAEA,QAAQ,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;cAC3C/B,SAAS,EAAAA,SAAA;cACTwD,GAAG,EAAE,IAAI,CAAC7E;aACX,CAAC;YAAC;YAAA5H,aAAA,GAAAC,CAAA;;;;;;YAGCgL,YAAY,GAAG7J,IAAI,CAACC,GAAG,EAAE,GAAGqJ,SAAS;YAAC;YAAA1K,aAAA,GAAAC,CAAA;YAE5C4B,eAAe,CAACK,IAAI,CAAC,mCAAmC,EAAE;cACxD+G,SAAS,EAAAA,SAAA;cACTyD,YAAY,EAAEV,IAAI,CAACW,SAAS,CAACzB,IAAI,CAAC,CAAC9H,MAAM;cACzC+E,MAAM;cAAE;cAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;cAAA;cAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW;cAC7B2J,MAAM,EAAE,KAAK;cACbG,YAAY,EAAAA;aACb,CAAC;YAEF;YACA;YACA;YAEA;YACA;YAAA;YAAAjL,aAAA,GAAAC,CAAA;YAEA,sBAAO;cACL2K,OAAO,EAAE,IAAI;cACbM,IAAI,EAAAA,IAAA;cACJJ,MAAM,EAAE;aACT;;;;;;;;YAEK8B,YAAY,GAAG;cACnB3L,SAAS,EAAE,iBAAiB;cAC5BgI,SAAS,EAAAA,SAAA;cACTuB,QAAQ,EAAEA,QAAQ;cAAA;cAAA,CAAAxK,aAAA,GAAAmB,CAAA,WAAGqJ,QAAQ,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;cAAA;cAAA,CAAAhL,aAAA,GAAAmB,CAAA,WAAG0L,SAAS;cAClEpC,SAAS,EAAAA,SAAA;cACTtC,MAAM;cAAE;cAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;cAAA;cAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW;aAC9B;YAAC;YAAAnB,aAAA,GAAAC,CAAA;YAEF4B,eAAe,CAACY,KAAK,CAAC,0BAA0B,EAAEqK,OAAK,EAAEF,YAAY,CAAC;YAAC;YAAA5M,aAAA,GAAAC,CAAA;YAGnE8M,YAAY,GAAG,wBAAwB;YAAC;YAAA/M,aAAA,GAAAC,CAAA;YACxC+M,SAAS,GAAG,SAAS;YAAC;YAAAhN,aAAA,GAAAC,CAAA;YAE1B,IAAI6M,OAAK,YAAYxL,KAAK,EAAE;cAAA;cAAAtB,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAC1B,IAAI6M,OAAK,CAAC3K,OAAO,CAAC8K,QAAQ,CAAC,SAAS,CAAC,EAAE;gBAAA;gBAAAjN,aAAA,GAAAmB,CAAA;gBAAAnB,aAAA,GAAAC,CAAA;gBACrC8M,YAAY,GAAG,yCAAyC;gBAAC;gBAAA/M,aAAA,GAAAC,CAAA;gBACzD+M,SAAS,GAAG,SAAS;cACvB,CAAC,MAAM;gBAAA;gBAAAhN,aAAA,GAAAmB,CAAA;gBAAAnB,aAAA,GAAAC,CAAA;gBAAA;gBAAI;gBAAA,CAAAD,aAAA,GAAAmB,CAAA,WAAA2L,OAAK,CAAC3K,OAAO,CAAC8K,QAAQ,CAAC,OAAO,CAAC;gBAAA;gBAAA,CAAAjN,aAAA,GAAAmB,CAAA,WAAI2L,OAAK,CAAC3K,OAAO,CAAC8K,QAAQ,CAAC,OAAO,CAAC,GAAE;kBAAA;kBAAAjN,aAAA,GAAAmB,CAAA;kBAAAnB,aAAA,GAAAC,CAAA;kBAC7E8M,YAAY,GAAG,oDAAoD;kBAAC;kBAAA/M,aAAA,GAAAC,CAAA;kBACpE+M,SAAS,GAAG,gBAAgB;gBAC9B,CAAC,MAAM;kBAAA;kBAAAhN,aAAA,GAAAmB,CAAA;kBAAAnB,aAAA,GAAAC,CAAA;kBAAA;kBAAI;kBAAA,CAAAD,aAAA,GAAAmB,CAAA,WAAA2L,OAAK,CAAC3K,OAAO,CAAC8K,QAAQ,CAAC,gBAAgB,CAAC;kBAAA;kBAAA,CAAAjN,aAAA,GAAAmB,CAAA,WAAI2L,OAAK,CAAC3K,OAAO,CAAC8K,QAAQ,CAAC,cAAc,CAAC,GAAE;oBAAA;oBAAAjN,aAAA,GAAAmB,CAAA;oBAAAnB,aAAA,GAAAC,CAAA;oBAC7F8M,YAAY,GAAG,kCAAkC;oBAAC;oBAAA/M,aAAA,GAAAC,CAAA;oBAClD+M,SAAS,GAAG,aAAa;kBAC3B,CAAC,MAAM;oBAAA;oBAAAhN,aAAA,GAAAmB,CAAA;oBAAAnB,aAAA,GAAAC,CAAA;oBAAA;oBAAI;oBAAA,CAAAD,aAAA,GAAAmB,CAAA,WAAA2L,OAAK,CAAC3K,OAAO,CAAC8K,QAAQ,CAAC,SAAS,CAAC;oBAAA;oBAAA,CAAAjN,aAAA,GAAAmB,CAAA,WAAI2L,OAAK,CAAC3K,OAAO,CAAC8K,QAAQ,CAAC,YAAY,CAAC,GAAE;sBAAA;sBAAAjN,aAAA,GAAAmB,CAAA;sBAAAnB,aAAA,GAAAC,CAAA;sBACpF8M,YAAY,GAAG,mEAAmE;sBAAC;sBAAA/M,aAAA,GAAAC,CAAA;sBACnF+M,SAAS,GAAG,eAAe;oBAC7B,CAAC,MAAM;sBAAA;sBAAAhN,aAAA,GAAAmB,CAAA;sBAAAnB,aAAA,GAAAC,CAAA;sBACL8M,YAAY,GAAGD,OAAK,CAAC3K,OAAO;sBAAC;sBAAAnC,aAAA,GAAAC,CAAA;sBAC7B+M,SAAS,GAAG,WAAW;oBACzB;kBAAA;gBAAA;cAAA;YACF,CAAC;YAAA;YAAA;cAAAhN,aAAA,GAAAmB,CAAA;YAAA;YAED;YAAAnB,aAAA,GAAAC,CAAA;YACA4B,eAAe,CAACe,KAAK,CAAC,mBAAmB,EAAE;cAAEoK,SAAS,EAAAA,SAAA;cAAE/D,SAAS,EAAAA,SAAA;cAAEd,MAAM,EAAAA;YAAA,CAAE,CAAC;YAAC;YAAAnI,aAAA,GAAAC,CAAA;YAGvEgL,YAAY,GAAG7J,IAAI,CAACC,GAAG,EAAE,GAAGqJ,SAAS;YAC3C;YACA;YAEA;YACA;YAAA;YAAA1K,aAAA,GAAAC,CAAA;YAEA,sBAAO;cACL2K,OAAO,EAAE,KAAK;cACdnI,KAAK,EAAEsK;aACR;;;;;;;;;GAEJ;EAAA;EAAA/M,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAAmM,aAAa,GAAnB,UAAoBC,UAAkB,EAAEhF,MAAe;IAAA;IAAAnI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAGiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;QAC/D;QACA;QAAI;QAAA,CAAAP,aAAA,GAAAmB,CAAA,YAACgM,UAAU;QAAA;QAAA,CAAAnN,aAAA,GAAAmB,CAAA,WAAIgM,UAAU,CAACC,IAAI,EAAE,CAAChK,MAAM,KAAK,CAAC,GAAE;UAAA;UAAApD,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UACjD4B,eAAe,CAACC,IAAI,CAAC,sDAAsD,EAAE;YAC3EqG,MAAM;YAAE;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;YAAA;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW;WAC9B,CAAC;UAAC;UAAAnB,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE;WACR;QACH,CAAC;QAAA;QAAA;UAAAzC,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAED,IAAIkN,UAAU,CAAC/J,MAAM,GAAG,KAAK,EAAE;UAAA;UAAApD,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UAC7B4B,eAAe,CAACC,IAAI,CAAC,oDAAoD,EAAE;YACzEqG,MAAM;YAAE;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;YAAA;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW;WAC9B,CAAC;UAAC;UAAAnB,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE;WACR;QACH,CAAC;QAAA;QAAA;UAAAzC,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAGKoN,eAAe,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,gBAAgB,CAAC;QAAC;QAAArN,aAAA,GAAAC,CAAA;QAC/DqN,iBAAiB,GAAGD,eAAe,CAACE,IAAI,CAAC,UAAAC,OAAO;UAAA;UAAAxN,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UACpD,OAAAkN,UAAU,CAACM,WAAW,EAAE,CAACR,QAAQ,CAACO,OAAO,CAACC,WAAW,EAAE,CAAC;QAAxD,CAAwD,CACzD;QAAC;QAAAzN,aAAA,GAAAC,CAAA;QAEF,IAAIqN,iBAAiB,EAAE;UAAA;UAAAtN,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UACrB4B,eAAe,CAACY,KAAK,CAAC,yDAAyD,EAAE;YAC/E0F,MAAM;YAAE;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;YAAA;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW;WAC9B,CAAC;UAAC;UAAAnB,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE;WACR;QACH,CAAC;QAAA;QAAA;UAAAzC,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAEKyN,mBAAmB,GAAGP,UAAU,CAACC,IAAI,EAAE;QAAC;QAAApN,aAAA,GAAAC,CAAA;QAExCsK,MAAM,GAAG,0GAAAhI,MAAA,CAIjBmL,mBAAmB,ooBAmBpB;QAAC;QAAA1N,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAGrC,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAG,mBAAAoB,MAAA,CAAmB4F,MAAM,OAAA5F,MAAA,CAAIoL,MAAM,CAAC/E,IAAI,CAACuE,UAAU,CAAC,CAACS,QAAQ,CAAC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE;QAAA;QAAA,CAAA7N,aAAA,GAAAmB,CAAA,WAAG0L,SAAS;QAAC;QAAA7M,aAAA,GAAAC,CAAA;QAE7H,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,iBAAiB,EAAEC,QAAQ,EAAE,KAAK,EAAErC,MAAM,CAAC;;;GAChF;EAAA;EAAAnI,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAA+M,6BAA6B,GAAnC,UACEC,cAAmB,EACnBC,aAAuB,EACvBC,WAAgB,EAChB9F,MAAe;IAAA;IAAAnI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCACdiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;QACR;QACA,IAAI,CAACoI,KAAK,CAACuF,OAAO,CAACF,aAAa,CAAC,EAAE;UAAA;UAAAhO,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UACjC4B,eAAe,CAACC,IAAI,CAAC,6DAA6D,EAAE;YAClFqG,MAAM;YAAE;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;YAAA;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW;WAC9B,CAAC;UAAC;UAAAnB,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE;WACR;QACH,CAAC;QAAA;QAAA;UAAAzC,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAED,IAAI+N,aAAa,CAAC5K,MAAM,KAAK,CAAC,EAAE;UAAA;UAAApD,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UAC9B4B,eAAe,CAACC,IAAI,CAAC,+DAA+D,EAAE;YACpFqG,MAAM;YAAE;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAAgH,MAAM;YAAA;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,WAAW;WAC9B,CAAC;UAAC;UAAAnB,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE;WACR;QACH,CAAC;QAAA;QAAA;UAAAzC,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAEKkO,eAAe,GAAGH,aAAa,CAACI,MAAM,CAAC,UAAAC,KAAK;UAAA;UAAArO,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAChD,kCAAAD,aAAA,GAAAmB,CAAA,kBAAOkN,KAAK,KAAK,QAAQ;UAAA;UAAA,CAAArO,aAAA,GAAAmB,CAAA,WAAIkN,KAAK,CAACjB,IAAI,EAAE,CAAChK,MAAM,GAAG,CAAC;QAApD,CAAoD,CACrD,CAACkL,GAAG,CAAC,UAAAD,KAAK;UAAA;UAAArO,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAAI,OAAAoO,KAAK,CAACjB,IAAI,EAAE;QAAZ,CAAY,CAAC;QAAC;QAAApN,aAAA,GAAAC,CAAA;QACvBsK,MAAM,GAAG,2KAAAhI,MAAA,CAIjByJ,IAAI,CAACW,SAAS,CAACoB,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,2BAAAxL,MAAA,CAGvC4L,eAAe,CAACI,IAAI,CAAC,IAAI,CAAC,wBAAAhM,MAAA,CAG1ByJ,IAAI,CAACW,SAAS,CAACsB,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,4tBAuBrC;QAAC;QAAAjO,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAGrC,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAG,0BAAAoB,MAAA,CAA0B4F,MAAM,OAAA5F,MAAA,CAAInB,IAAI,CAACC,GAAG,EAAE,CAACuM,QAAQ,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE;QAAA;QAAA,CAAA7N,aAAA,GAAAmB,CAAA,WAAG0L,SAAS;QAAC;QAAA7M,aAAA,GAAAC,CAAA;QAE5G,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,wBAAwB,EAAEC,QAAQ,EAAE,KAAK,EAAErC,MAAM,CAAC;;;GACvF;EAAA;EAAAnI,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAAyN,gBAAgB,GAAtB,UACER,aAAuB,EACvBS,gBAAwB,EACxB9J,eAAuB,EACvBwD,MAAe;IAAA;IAAAnI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCACdiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;QACFgK,MAAM,GAAG,iJAAAhI,MAAA,CAIjByL,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,+BAAAhM,MAAA,CAGxBkM,gBAAgB,6BAAAlM,MAAA,CAGhBoC,eAAe,utBAuBhB;QAAC;QAAA3E,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAGrC,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAG,mBAAAoB,MAAA,CAAmB4F,MAAM,OAAA5F,MAAA,CAAIkM,gBAAgB,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAE;QAAA;QAAA,CAAA1O,aAAA,GAAAmB,CAAA,WAAG0L,SAAS;QAAC;QAAA7M,aAAA,GAAAC,CAAA;QAE3G,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,iBAAiB,EAAEC,QAAQ,EAAE,KAAK,EAAErC,MAAM,CAAC;;;GAChF;EAAA;EAAAnI,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAA4N,4BAA4B,GAAlC,UACEX,aAME,EACFS,gBAIC,EACDR,WAMC,EACDW,cAAmB,EACnBzG,MAAe;IAAA;IAAAnI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCACdiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;;QACFgK,MAAM,GAAG,qIAAAhI,MAAA,CAIjByL,aAAa,CAACM,GAAG,CAAC,UAAAD,KAAK;UAAA;UAAArO,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAAI,cAAAsC,MAAA,CACzB8L,KAAK,CAACQ,SAAS,oBAAAtM,MAAA,CAAiB8L,KAAK,CAACS,UAAU,sBAAAvM,MAAA,CAAmB8L,KAAK,CAAC1H,eAAe,aAAApE,MAAA,CACxF8L,KAAK,CAACU,iBAAiB;UAAA;UAAA,CAAA/O,aAAA,GAAAmB,CAAA,YAAG,eAAAoB,MAAA,CAAe8L,KAAK,CAACU,iBAAiB,WAAQ;UAAA;UAAA,CAAA/O,aAAA,GAAAmB,CAAA,YAAG,EAAE,WAAAoB,MAAA,CAC7E8L,KAAK,CAACW,QAAQ;UAAA;UAAA,CAAAhP,aAAA,GAAAmB,CAAA,YAAG,cAAAoB,MAAA,CAAc8L,KAAK,CAACW,QAAQ,CAAE;UAAA;UAAA,CAAAhP,aAAA,GAAAmB,CAAA,YAAG,EAAE,QACvD;QAJ4B,CAI5B,CAAC,CAACoN,IAAI,CAAC,EAAE,CAAC,yCAAAhM,MAAA,CAGCkM,gBAAgB,CAACQ,cAAc,wBAAA1M,MAAA,CACzBkM,gBAAgB,CAACS,WAAW,2BAAA3M,MAAA;QACzB;QAAA,CAAAvC,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,aAAAK,EAAA;QAAA;QAAA,CAAAxB,aAAA,GAAAmB,CAAA,YAAAyN,cAAc;QAAA;QAAA,CAAA5O,aAAA,GAAAmB,CAAA,YAAdyN,cAAc;QAAA;QAAA,CAAA5O,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,YAAdyN,cAAc,CAAErJ,cAAc;QAAA;QAAA,CAAAvF,aAAA,GAAAmB,CAAA,YAAAK,EAAA;QAAA;QAAA,CAAAxB,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,YAAAK,EAAA,CAAE8M,GAAG,CAAC,UAACrO,CAAM;UAAA;UAAAD,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAAK,OAAAA,CAAC,CAAC0C,IAAI;QAAN,CAAM,EAAE4L,IAAI,CAAC,IAAI,CAAC;QAAA;QAAA,CAAAvO,aAAA,GAAAmB,CAAA,YAAI,eAAe,+CAAAoB,MAAA,CAG3F0L,WAAW,CAACkB,SAAS,gCAAA5M,MAAA,CACV0L,WAAW,CAACmB,YAAY,0BAAA7M,MAAA,CAC9B0L,WAAW,CAACoB,aAAa,CAACd,IAAI,CAAC,IAAI,CAAC,kBAAAhM,MAAA,CAC5C0L,WAAW,CAACqB,MAAM,uBAAA/M,MAAA,CACb0L,WAAW,CAACsB,UAAU,CAAChB,IAAI,CAAC,IAAI,CAAC,gCAAAhM,MAAA;QAGhD;QAAA,CAAAvC,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,aAAA2H,EAAA;QAAA;QAAA,CAAA9I,aAAA,GAAAmB,CAAA,YAAAyN,cAAc;QAAA;QAAA,CAAA5O,aAAA,GAAAmB,CAAA,YAAdyN,cAAc;QAAA;QAAA,CAAA5O,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,YAAdyN,cAAc,CAAEY,iBAAiB;QAAA;QAAA,CAAAxP,aAAA,GAAAmB,CAAA,YAAA2H,EAAA;QAAA;QAAA,CAAA9I,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,YAAA2H,EAAA,CAAE+E,KAAK,CAAC,CAAC,EAAE,EAAE,EAAES,GAAG,CAAC,UAACmB,QAAa;UAAA;UAAAzP,aAAA,GAAAO,CAAA;;;;UAAK,cAAAgC,MAAA,CACrEkN,QAAQ,CAACC,KAAK,QAAAnN,MAAA,CAAKkN,QAAQ,CAACtL,IAAI,UAAA5B,MAAA,CAAOkN,QAAQ,CAACE,UAAU,wBAAApN,MAAA;UAClD;UAAA,CAAAvC,aAAA,GAAAmB,CAAA;UAAA;UAAA,CAAAnB,aAAA,GAAAmB,CAAA,aAAAK,EAAA,GAAAiO,QAAQ,CAACG,MAAM;UAAA;UAAA,CAAA5P,aAAA,GAAAmB,CAAA,YAAAK,EAAA;UAAA;UAAA,CAAAxB,aAAA,GAAAmB,CAAA;UAAA;UAAA,CAAAnB,aAAA,GAAAmB,CAAA,YAAAK,EAAA,CAAE+M,IAAI,CAAC,IAAI,CAAC;UAAA;UAAA,CAAAvO,aAAA,GAAAmB,CAAA,YAAI,SAAS,iBAAAoB,MAAA;UAC1C;UAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAsO,QAAQ,CAACI,IAAI;UAAA;UAAA,CAAA7P,aAAA,GAAAmB,CAAA,YAAI,MAAM,QAChC;SAAA,EAAEoN,IAAI,CAAC,EAAE,CAAC;QAAA;QAAA,CAAAvO,aAAA,GAAAmB,CAAA,YAAI,iCAAiC,8nGAwF/C;QAAC;QAAAnB,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAGrC,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAmB,CAAA,YAAG,iCAAAoB,MAAA,CAAiC4F,MAAM,OAAA5F,MAAA,CAAIkM,gBAAgB,CAACQ,cAAc,CAACP,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,OAAAnM,MAAA,CAAIkM,gBAAgB,CAACS,WAAW,OAAA3M,MAAA,CAAI0L,WAAW,CAACkB,SAAS,CAAE;QAAA;QAAA,CAAAnP,aAAA,GAAAmB,CAAA,YAAG0L,SAAS;QAAC;QAAA7M,aAAA,GAAAC,CAAA;QAEjM,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,+BAA+B,EAAEC,QAAQ,EAAE,KAAK,EAAErC,MAAM,CAAC;;;GAC9F;EAAA;EAAAnI,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAA+O,gCAAgC,GAAtC,UACEC,SAOE,EACFC,eAMC,EACDC,UAKE,EACF9H,MAAe;IAAA;IAAAnI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCACdiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;QACFgK,MAAM,GAAG,8IAAAhI,MAAA,CAIjBwN,SAAS,CAACzB,GAAG,CAAC,UAAA4B,GAAG;UAAA;UAAAlQ,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAAI,cAAAsC,MAAA,CACnB2N,GAAG,CAACrB,SAAS,gBAAAtM,MAAA,CAAa2N,GAAG,CAACC,YAAY,wBAAA5N,MAAA,CAAgB2N,GAAG,CAAChB,WAAW,2BAAA3M,MAAA,CAC3D2N,GAAG,CAACE,WAAW,kBAAA7N,MAAA,CAAe2N,GAAG,CAACjG,QAAQ,uCAAA1H,MAAA,CAC/B2N,GAAG,CAACG,qBAAqB,aACrD;QAJsB,CAItB,CAAC,CAAC9B,IAAI,CAAC,EAAE,CAAC,0CAAAhM,MAAA,CAGIyN,eAAe,CAACb,SAAS,gCAAA5M,MAAA,CACdyN,eAAe,CAACZ,YAAY,0BAAA7M,MAAA,CAClCyN,eAAe,CAACX,aAAa,CAACd,IAAI,CAAC,IAAI,CAAC,kBAAAhM,MAAA,CAChDyN,eAAe,CAACV,MAAM,uBAAA/M,MAAA,CACjByN,eAAe,CAACT,UAAU,CAAChB,IAAI,CAAC,IAAI,CAAC,wBAAAhM,MAAA,CAGpD0N,UAAU,CAAC3B,GAAG,CAAC,UAAApD,IAAI;UAAA;UAAAlL,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAAI,cAAAsC,MAAA,CACrB2I,IAAI,CAAC2D,SAAS,eAAAtM,MAAA,CAAY2I,IAAI,CAACoF,WAAW,cAAA/N,MAAA,CAAW2I,IAAI,CAACqF,WAAW,UAAAhO,MAAA,CACrE2I,IAAI,CAACsF,YAAY;UAAA;UAAA,CAAAxQ,aAAA,GAAAmB,CAAA,YAAG,mBAAAoB,MAAA,CAAmB2I,IAAI,CAACsF,YAAY,MAAG;UAAA;UAAA,CAAAxQ,aAAA,GAAAmB,CAAA,YAAG,EAAE,QACnE;QAHwB,CAGxB,CAAC,CAACoN,IAAI,CAAC,EAAE,CAAC,6iFAsEV;QAAC;QAAAvO,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAGrC,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAmB,CAAA,YAAG,8BAAAoB,MAAA,CAA8B4F,MAAM,OAAA5F,MAAA,CAAIyN,eAAe,CAACb,SAAS,OAAA5M,MAAA,CAAIyN,eAAe,CAACZ,YAAY,MAAG;QAAA;QAAA,CAAApP,aAAA,GAAAmB,CAAA,YAAG0L,SAAS;QAAC;QAAA7M,aAAA,GAAAC,CAAA;QAE3I,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,4BAA4B,EAAEC,QAAQ,EAAE,KAAK,EAAErC,MAAM,CAAC;;;GAC3F;EAAA;EAAAnI,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAA0P,wBAAwB,GAA9B,UAAAC,QAAA,EAAAC,gBAAA;IAAA;IAAA3Q,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;sCAIGiB,OAAO,YAHR0O,MAAgB,EAChBgB,cAAsB,EACtBC,MAAyB;MAAA;MAAA7Q,aAAA,GAAAO,CAAA;;;;MAAzB,IAAAsQ,MAAA;QAAA;QAAA7Q,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QAAA4Q,MAAA,WAAyB;MAAA;MAAA;MAAA;QAAA7Q,aAAA,GAAAmB,CAAA;MAAA;MAAAnB,aAAA,GAAAC,CAAA;;;;;QAEnBsK,MAAM,GAAG,8GAAAhI,MAAA,CAIjBqN,MAAM,CAACtB,GAAG,CAAC,UAAAD,KAAK;UAAA;UAAArO,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAAI,YAAAsC,MAAA,CAAK8L,KAAK,CAAE;QAAZ,CAAY,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,2BAAAhM,MAAA,CAE3BqO,cAAc,gBAAArO,MAAA,CACvBsO,MAAM,6IAAAtO,MAAA,CAKO,IAAInB,IAAI,EAAE,CAACoB,WAAW,EAAE,6BAAAD,MAAA,CAC9BsO,MAAM,+BAAAtO,MAAA,CACJqO,cAAc,o8CAkChC;QAAC;QAAA5Q,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAG,uBAAAjI,MAAA,CAAuBqO,cAAc,OAAArO,MAAA,CAAIsO,MAAM,OAAAtO,MAAA,CAAIqN,MAAM,CAACrB,IAAI,CAAC,GAAG,CAAC,CAACG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAE;QAAC;QAAA1O,aAAA,GAAAC,CAAA;QAE5G,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,uBAAuB,EAAEC,QAAQ,EAAE,KAAK,CAAC;;;GAC9E;EAAA;EAAAxK,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAA+P,uBAAuB,GAA7B,UACEjC,SAAiB,EACjBC,UAAkB,EAClBiC,WAUC;IAAA;IAAA/Q,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCACAiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;;QACFgK,MAAM,GAAG,6GAAAhI,MAAA,CAIRsM,SAAS,uBAAAtM,MAAA,CACHuM,UAAU,oCAAAvM,MAAA;QACA;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA4P,WAAW,CAACpM,eAAe;QAAA;QAAA,CAAA3E,aAAA,GAAAmB,CAAA,YAAI,eAAe,qBAAAoB,MAAA;QAC3D;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA4P,WAAW,CAACC,QAAQ;QAAA;QAAA,CAAAhR,aAAA,GAAAmB,CAAA,YAAI,eAAe,gCAAAoB,MAAA;QAC5B;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA4P,WAAW,CAAChC,iBAAiB;QAAA;QAAA,CAAA/O,aAAA,GAAAmB,CAAA,YAAI,eAAe,2BAAAoB,MAAA;QACrD;QAAA,CAAAvC,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,aAAAK,EAAA,GAAAuP,WAAW,CAACE,aAAa;QAAA;QAAA,CAAAjR,aAAA,GAAAmB,CAAA,YAAAK,EAAA;QAAA;QAAA,CAAAxB,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,YAAAK,EAAA,CAAE+M,IAAI,CAAC,IAAI,CAAC;QAAA;QAAA,CAAAvO,aAAA,GAAAmB,CAAA,YAAI,gBAAgB,kCAAAoB,MAAA;QAG3E;QAAA,CAAAvC,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,aAAA2H,EAAA,GAAAiI,WAAW,CAACG,mBAAmB;QAAA;QAAA,CAAAlR,aAAA,GAAAmB,CAAA,YAAA2H,EAAA;QAAA;QAAA,CAAA9I,aAAA,GAAAmB,CAAA;QAAA;QAAA,CAAAnB,aAAA,GAAAmB,CAAA,YAAA2H,EAAA,CAAEwF,GAAG,CAAC,UAAA6C,UAAU;UAAA;UAAAnR,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAC/C,YAAAsC,MAAA,CAAK4O,UAAU,CAACtC,SAAS,QAAAtM,MAAA,CAAK4O,UAAU,CAACC,MAAM,WAAA7O,MAAA,CAAQ4O,UAAU,CAACE,IAAI,MAAG;QAAzE,CAAyE,EACzE9C,IAAI,CAAC,IAAI,CAAC;QAAA;QAAA,CAAAvO,aAAA,GAAAmB,CAAA,YAAI,yBAAyB,yqEAsDxC;QAAC;QAAAnB,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAG,+BAAAjI,MAAA,CAA+BsM,SAAS,CAACH,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,OAAAnM,MAAA,CAAIuM,UAAU,OAAAvM,MAAA;QAAI;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA4P,WAAW,CAACpM,eAAe;QAAA;QAAA,CAAA3E,aAAA,GAAAmB,CAAA,YAAI,SAAS,EAAE;QAAC;QAAAnB,aAAA,GAAAC,CAAA;QAE3I,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,6BAA6B,EAAEC,QAAQ,EAAE,KAAK,CAAC;;;GACpF;EAAA;EAAAxK,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAAuQ,qBAAqB,GAA3B,UACEpM,UAAkB,EAClBP,eAAuB,EACvB4M,WAAmB,EACnBpJ,MAAe;IAAA;IAAAnI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCACdiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;QACFgK,MAAM,GAAG,uHAAAhI,MAAA,CAGJ2C,UAAU,0BAAA3C,MAAA,CACLoC,eAAe,sBAAApC,MAAA,CACnBgP,WAAW,y1BA4B1B;QAAC;QAAAvR,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAGrC,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAmB,CAAA,YAAG,kBAAAoB,MAAA,CAAkB4F,MAAM,OAAA5F,MAAA,CAAI2C,UAAU,CAACwJ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,OAAAnM,MAAA,CAAIgP,WAAW,CAAE;QAAA;QAAA,CAAAvR,aAAA,GAAAmB,CAAA,YAAG0L,SAAS;QAAC;QAAA7M,aAAA,GAAAC,CAAA;QAEnH,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,gBAAgB,EAAEC,QAAQ,EAAE,KAAK,EAAErC,MAAM,CAAC;;;GAC/E;EAAA;EAAAnI,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAAyQ,0BAA0B,GAAhC,UAAiCC,MAahC;IAAA;IAAAzR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAGiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;QACT;QACA;QAAI;QAAA,CAAAP,aAAA,GAAAmB,CAAA,aAACsQ,MAAM,CAACrI,WAAW;QAAA;QAAA,CAAApJ,aAAA,GAAAmB,CAAA,YAAI,OAAOsQ,MAAM,CAACrI,WAAW,KAAK,QAAQ,GAAE;UAAA;UAAApJ,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UACjE4B,eAAe,CAACC,IAAI,CAAC,uEAAuE,EAAE;YAC5F2P,MAAM,EAAEA;WACT,CAAC;UAAC;UAAAzR,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE;WACR;QACH,CAAC;QAAA;QAAA;UAAAzC,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAED;QAAI;QAAA,CAAAD,aAAA,GAAAmB,CAAA,aAACsQ,MAAM,CAAClJ,KAAK;QAAA;QAAA,CAAAvI,aAAA,GAAAmB,CAAA,YAAIsQ,MAAM,CAAClJ,KAAK,GAAG,CAAC;QAAA;QAAA,CAAAvI,aAAA,GAAAmB,CAAA,YAAIsQ,MAAM,CAAClJ,KAAK,GAAG,EAAE,GAAE;UAAA;UAAAvI,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UAC1D4B,eAAe,CAACC,IAAI,CAAC,iEAAiE,EAAE;YACtF2P,MAAM,EAAEA;WACT,CAAC;UAAC;UAAAzR,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE;WACR;QACH,CAAC;QAAA;QAAA;UAAAzC,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAECmJ,WAAW,GAYTqI,MAAM,CAAArI,WAZG,EACXlE,UAAU,GAWRuM,MAAM,CAAAvM,UAXE,EACVP,eAAe,GAUb8M,MAAM,CAAA9M,eAVO,EACf4M,WAAW,GASTE,MAAM,CAAAF,WATG,EACXG,aAAa,GAQXD,MAAM,CAAAC,aARK,EACbC,YAAY,GAOVF,MAAM,CAAAE,YAPI,EACZC,aAAa,GAMXH,MAAM,CAAAG,aANK,EACbrC,UAAU,GAKRkC,MAAM,CAAAlC,UALE,EACVxJ,UAAU,GAIR0L,MAAM,CAAA1L,UAJE,EACV8L,aAAa,GAGXJ,MAAM,CAAAI,aAHK,EACbC,UAAU,GAERL,MAAM,CAAAK,UAFE,EACVvJ,KAAK,GACHkJ,MAAM,CAAAlJ,KADH;QACI;QAAAvI,aAAA,GAAAC,CAAA;QAELsK,MAAM,GAAG,cAAAhI,MAAA,CACRgG,KAAK,iHAAAhG,MAAA,CAEA6G,WAAW,qBAAA7G,MAAA;QACZ;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA+D,UAAU;QAAA;QAAA,CAAAlF,aAAA,GAAAmB,CAAA,YAAI,SAAS,2BAAAoB,MAAA;QAClB;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAwD,eAAe;QAAA;QAAA,CAAA3E,aAAA,GAAAmB,CAAA,YAAI,cAAc,uBAAAoB,MAAA;QACrC;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAoQ,WAAW;QAAA;QAAA,CAAAvR,aAAA,GAAAmB,CAAA,YAAI,WAAW,yBAAAoB,MAAA;QACxB;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAuQ,aAAa;QAAA;QAAA,CAAA1R,aAAA,GAAAmB,CAAA,YAAI,SAAS,wBAAAoB,MAAA;QAC3B;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAwQ,YAAY;QAAA;QAAA,CAAA3R,aAAA,GAAAmB,CAAA,YAAI,eAAe,yBAAAoB,MAAA;QAC9B;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAyQ,aAAa;QAAA;QAAA,CAAA5R,aAAA,GAAAmB,CAAA,YAAI,OAAO,sBAAAoB,MAAA;QAC3B;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA6K,IAAI,CAACW,SAAS,CAAC4C,UAAU,CAAC;QAAA;QAAA,CAAAvP,aAAA,GAAAmB,CAAA,YAAI,0BAA0B,qBAAAoB,MAAA;QACzD;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA4E,UAAU;QAAA;QAAA,CAAA/F,aAAA,GAAAmB,CAAA,YAAI,cAAc,yBAAAoB,MAAA;QACxB;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA6K,IAAI,CAACW,SAAS,CAACkF,aAAa,CAAC;QAAA;QAAA,CAAA7R,aAAA,GAAAmB,CAAA,YAAI,OAAO,qBAAAoB,MAAA;QAC5C;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA6K,IAAI,CAACW,SAAS,CAACmF,UAAU,CAAC;QAAA;QAAA,CAAA9R,aAAA,GAAAmB,CAAA,YAAI,OAAO,+oCAAAoB,MAAA,CAwB7B6G,WAAW,mCAAA7G,MAAA,CACTgG,KAAK,6/BAmB5B;QAAC;QAAAvI,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAG,uBAAAjI,MAAA,CAAuB6G,WAAW,OAAA7G,MAAA,CAAI2C,UAAU,OAAA3C,MAAA,CAAIwD,UAAU,OAAAxD,MAAA,CAAIgG,KAAK,CAAE;QAAC;QAAAvI,aAAA,GAAAC,CAAA;QAE3F,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,qBAAqB,EAAEC,QAAQ,EAAE,KAAK,CAAC;;;GAC5E;EAAA;EAAAxK,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAAgR,wBAAwB,GAA9B,UAA+BN,MAU9B;IAAA;IAAAzR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAGiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;QAEHyR,kBAAkB,GAAG5R,oBAAA,CAAA6R,OAAgB,CAACC,yBAAyB,CAACT,MAAM,CAACU,YAAY,CAAC;QAAC;QAAAnS,aAAA,GAAAC,CAAA;QAC3F,IAAI,CAAC+R,kBAAkB,CAACI,OAAO,EAAE;UAAA;UAAApS,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UAC/B4B,eAAe,CAACC,IAAI,CAAC,sCAAsC,EAAE;YAC3DuQ,MAAM,EAAEL,kBAAkB,CAACK;WAC5B,CAAC;UAAC;UAAArS,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE,6BAAAF,MAAA,CAA6ByP,kBAAkB,CAACK,MAAM,CAAC9D,IAAI,CAAC,IAAI,CAAC;WACzE;QACH,CAAC;QAAA;QAAA;UAAAvO,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAGKqS,kBAAkB,GAAGlS,oBAAA,CAAA6R,OAAgB,CAACM,iBAAiB,CAACd,MAAM,CAAC7L,YAAY,EAAE;UACjFR,SAAS,EAAE,IAAI;UACfoN,SAAS,EAAE,KAAK;UAChBC,mBAAmB,EAAE;SACtB,CAAC;QAAC;QAAAzS,aAAA,GAAAC,CAAA;QACH,IAAI,CAACqS,kBAAkB,CAACF,OAAO,EAAE;UAAA;UAAApS,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UAC/B4B,eAAe,CAACC,IAAI,CAAC,sCAAsC,EAAE;YAC3DuQ,MAAM,EAAEC,kBAAkB,CAACD;WAC5B,CAAC;UAAC;UAAArS,aAAA,GAAAC,CAAA;UACH,sBAAO;YACL2K,OAAO,EAAE,KAAK;YACdnI,KAAK,EAAE,6BAAAF,MAAA,CAA6B+P,kBAAkB,CAACD,MAAM,CAAC9D,IAAI,CAAC,IAAI,CAAC;WACzE;QACH,CAAC;QAAA;QAAA;UAAAvO,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;QAEKyS,qBAAqB;QAAG;QAAA,CAAA1S,aAAA,GAAAmB,CAAA,YAAA6Q,kBAAkB,CAACW,cAAc;QAAA;QAAA,CAAA3S,aAAA,GAAAmB,CAAA,YAAIsQ,MAAM,CAACU,YAAY;QAAC;QAAAnS,aAAA,GAAAC,CAAA;QACjF2S,qBAAqB;QAAG;QAAA,CAAA5S,aAAA,GAAAmB,CAAA,YAAAmR,kBAAkB,CAACK,cAAc;QAAA;QAAA,CAAA3S,aAAA,GAAAmB,CAAA,YAAIsQ,MAAM,CAAC7L,YAAY;QAAC;QAAA5F,aAAA,GAAAC,CAAA;QAErF2F,YAAY,GASV6L,MAAM,CAAA7L,YATI,EACZC,YAAY,GAQV4L,MAAM,CAAA5L,YARI,EACZgN,gBAAgB,GAOdpB,MAAM,CAAAoB,gBAPQ,EAChBV,YAAY,GAMVV,MAAM,CAAAU,YANI,EACZlH,YAAY,GAKVwG,MAAM,CAAAxG,YALI,EACZjF,gBAAgB,GAIdyL,MAAM,CAAAzL,gBAJQ,EAChBd,UAAU,GAGRuM,MAAM,CAAAvM,UAHE,EACVP,eAAe,GAEb8M,MAAM,CAAA9M,eAFO,EACfvC,OAAO,GACLqP,MAAM,CAAArP,OADD;QACE;QAAApC,aAAA,GAAAC,CAAA;QAELsK,MAAM,GAAG,8GAAAhI,MAAA,CAGPqQ,qBAAqB,uBAAArQ,MAAA,CAChBsD,YAAY,2BAAAtD,MAAA,CACRsQ,gBAAgB,iBAAAtQ,MAAA;QAC1B;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAiB,OAAO;QAAA;QAAA,CAAApC,aAAA,GAAAmB,CAAA,YAAI,6BAA6B,4BAAAoB,MAAA,CAC9ByD,gBAAgB,sCAAAzD,MAAA,CACb0I,YAAY,6BAAA1I,MAAA;QACrB;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAA+D,UAAU;QAAA;QAAA,CAAAlF,aAAA,GAAAmB,CAAA,YAAI,SAAS,2BAAAoB,MAAA;QAClB;QAAA,CAAAvC,aAAA,GAAAmB,CAAA,YAAAwD,eAAe;QAAA;QAAA,CAAA3E,aAAA,GAAAmB,CAAA,YAAI,cAAc,sBAAAoB,MAAA,CAGnDmQ,qBAAqB,6rEA2DtB;QAAC;QAAA1S,aAAA,GAAAC,CAAA;QAEE,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,oBAAoB,EAAEsC,SAAS,EAAE,KAAK,CAAC;;;GAC5E;EAAA;EAAA7M,aAAA,GAAAC,CAAA;EAEK6G,aAAA,CAAA/F,SAAA,CAAA+R,2BAA2B,GAAjC,UAAAC,aAAA,EAAAC,aAAA;IAAA;IAAAhT,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;sCAKGiB,OAAO,YAJR+R,WAAmB,EACnBlC,WAAgB,EAChBmC,gBAA0B,EAC1B/K,MAAe;MAAA;MAAAnI,aAAA,GAAAO,CAAA;;;;MADf,IAAA2S,gBAAA;QAAA;QAAAlT,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QAAAiT,gBAAA,KAA0B;MAAA;MAAA;MAAA;QAAAlT,aAAA,GAAAmB,CAAA;MAAA;MAAAnB,aAAA,GAAAC,CAAA;;;;;QAGpBsK,MAAM,GAAG,2BAAAhI,MAAA,CACK0Q,WAAW,sEAAA1Q,MAAA,CAGjCyJ,IAAI,CAACW,SAAS,CAACoE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,kCAAAxO,MAAA,CAGpCyJ,IAAI,CAACW,SAAS,CAACuG,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,8MAI1C;QAAC;QAAAlT,aAAA,GAAAC,CAAA;QAEQuK,QAAQ,GAAGrC,MAAM;QAAA;QAAA,CAAAnI,aAAA,GAAAmB,CAAA,YAAG,wBAAAoB,MAAA,CAAwB4F,MAAM,OAAA5F,MAAA,CAAI0Q,WAAW,OAAA1Q,MAAA,CAAInB,IAAI,CAACC,GAAG,EAAE,CAACuM,QAAQ,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE;QAAA;QAAA,CAAA7N,aAAA,GAAAmB,CAAA,YAAG0L,SAAS;QAAC;QAAA7M,aAAA,GAAAC,CAAA;QAEzH,sBAAO,IAAI,CAACkK,eAAe,CAACI,MAAM,EAAE,wBAAwB,EAAEC,QAAQ,EAAE,KAAK,EAAErC,MAAM,CAAC;;;GACvF;EAAA;EAAAnI,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAoS,mBAAmB,GAA3B,UAA4B5I,MAAc;IAAA;IAAAvK,aAAA,GAAAO,CAAA;IACxC;IACA,IAAM6S,IAAI;IAAA;IAAA,CAAApT,aAAA,GAAAC,CAAA,SAAG0N,MAAM,CAAC/E,IAAI,CAAC2B,MAAM,CAAC,CAACqD,QAAQ,CAAC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAAC;IAAA7N,aAAA,GAAAC,CAAA;IACjE,OAAOmT,IAAI;EACb,CAAC;EAAA;EAAApT,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAsS,kBAAkB,GAA1B,UAA2BpK,SAAiB;IAAA;IAAAjJ,aAAA,GAAAO,CAAA;IAC1C;IACA,IAAM+S,WAAW;IAAA;IAAA,CAAAtT,aAAA,GAAAC,CAAA,SAA8B;MAC7C,iBAAiB,EAAE,CAAC;MACpB,wBAAwB,EAAE,CAAC;MAC3B,qBAAqB,EAAE,CAAC;MACxB,iBAAiB,EAAE,CAAC;MACpB,gBAAgB,EAAE;KACnB;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAEF,OAAO,2BAAAD,aAAA,GAAAmB,CAAA,YAAAmS,WAAW,CAACrK,SAAS,CAAC;IAAA;IAAA,CAAAjJ,aAAA,GAAAmB,CAAA,YAAI,CAAC;EACpC,CAAC;EAED;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EACM6G,aAAA,CAAA/F,SAAA,CAAAwS,WAAW,GAAjB;IAAA;IAAAvT,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAqBiB,OAAO;MAAA;MAAAlB,aAAA,GAAAO,CAAA;;;;;;;;;;;;;YACtBiT,SAAS,GAAG,KAAK;YAAC;YAAAxT,aAAA,GAAAC,CAAA;;;;;;;;;YAGdmL,cAAc,GAAG,IAAIlK,OAAO,CAAQ,UAACmK,CAAC,EAAEC,MAAM;cAAA;cAAAtL,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAClDsL,UAAU,CAAC;gBAAA;gBAAAvL,aAAA,GAAAO,CAAA;gBAAAP,aAAA,GAAAC,CAAA;gBAAM,OAAAqL,MAAM,CAAC,IAAIhK,KAAK,CAAC,sBAAsB,CAAC,CAAC;cAAzC,CAAyC,EAAE,KAAK,CAAC;YACpE,CAAC,CAAC;YAAC;YAAAtB,aAAA,GAAAC,CAAA;YAEY,qBAAMiB,OAAO,CAACsK,IAAI,CAAC,CAChC,IAAI,CAACtE,KAAK,CAACiD,eAAe,CAAC;cACzBsB,QAAQ,EAAE,CAAC;gBAAEC,IAAI,EAAE,MAAM;gBAAEC,KAAK,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAiC,CAAE;cAAC,CAAE,CAAC;cAClFC,gBAAgB,EAAE;gBAAEjI,eAAe,EAAE;cAAE;aACxC,CAAC,EACFwH,cAAc,CACf,CAAC;;;;;YANI7J,MAAM,GAAGC,EAAA,CAAAC,IAAA,EAMb;YAAA;YAAAzB,aAAA,GAAAC,CAAA;YAEe,qBAAMsB,MAAM,CAACuK,QAAQ;;;;;YAAhCA,QAAQ,GAAGtK,EAAA,CAAAC,IAAA,EAAqB;YAAA;YAAAzB,aAAA,GAAAC,CAAA;YACtCuT,SAAS,GAAG1H,QAAQ,CAACF,IAAI,EAAE,CAAC6B,WAAW,EAAE,CAACR,QAAQ,CAAC,IAAI,CAAC;YAAC;YAAAjN,aAAA,GAAAC,CAAA;;;;;;;;;YAEzD4B,eAAe,CAACC,IAAI,CAAC,wBAAwB,EAAE2R,OAAK,CAAC;YAAC;YAAAzT,aAAA,GAAAC,CAAA;;;;;;YASlDyT,WAAW,GAAG;cAAEC,KAAK,EAAE,KAAK;cAAEC,MAAM,EAAE;YAAI,CAAE;YAAC;YAAA5T,aAAA,GAAAC,CAAA;YAC7C4T,mBAAmB,GAAG;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAC;YAAA9T,aAAA,GAAAC,CAAA;YAC7C8T,sBAAsB,GAAG;cAAED,MAAM,EAAE;YAAU,CAAE;YAAC;YAAA9T,aAAA,GAAAC,CAAA;YAChD+T,wBAAwB,GAAG;cAAEF,MAAM,EAAE;YAAU,CAAE;YAAC;YAAA9T,aAAA,GAAAC,CAAA;YAExD,sBAAO;cACLgU,EAAE,EAAET,SAAS;cACbU,KAAK,EAAER;aACR;;;;GACF;EAED;EAAA;EAAA1T,aAAA,GAAAC,CAAA;EACM6G,aAAA,CAAA/F,SAAA,CAAAoT,eAAe,GAArB;IAAA;IAAAnU,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;;;;;;;;;;;QAGQmU,UAAU,GAAG;UAAET,KAAK,EAAE;YAAEU,SAAS,EAAE;UAAK,CAAE;UAAET,MAAM,EAAE;YAAEU,IAAI,EAAE;UAAC;QAAE,CAAE;QAAC;QAAAtU,aAAA,GAAAC,CAAA;QAClEsU,gBAAgB,GAAG;UACvBC,WAAW,EAAE,IAAI,CAACnN,WAAW,CAACiN,IAAI;UAClCvM,kBAAkB,EAAE,IAAI,CAACf;SAC1B;QAAC;QAAAhH,aAAA,GAAAC,CAAA;QAEF,sBAAO;UACLiU,KAAK,EAAEE,UAAU;UACjB/M,WAAW,EAAEkN,gBAAgB;UAC7BpJ,MAAM,EAAE;YACN/D,YAAY,EAAE,IAAI,CAACA,YAAY;YAC/BQ,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBV,KAAK,EAAE;;SAEV;;;GACF;EAED;EAAA;EAAAlH,aAAA,GAAAC,CAAA;EACQ6G,aAAA,CAAA/F,SAAA,CAAAgL,sBAAsB,GAA9B,UAA+BH,IAAY;IAAA;IAAA5L,aAAA,GAAAO,CAAA;IACzC,IAAMkU,YAAY;IAAA;IAAA,CAAAzU,aAAA,GAAAC,CAAA,SAAGG,oBAAA,CAAA6R,OAAgB,CAACwC,YAAY,CAAC7I,IAAI,CAAC;IAAC;IAAA5L,aAAA,GAAAC,CAAA;IAEzD,IAAIwU,YAAY,CAACC,SAAS,KAAK,MAAM,EAAE;MAAA;MAAA1U,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACrC4B,eAAe,CAACY,KAAK,CAAC,4BAA4B,EAAE;QAAEkS,OAAO,EAAEF,YAAY,CAACE;MAAO,CAAE,CAAC;MAAC;MAAA3U,aAAA,GAAAC,CAAA;MACvF,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IAED,IAAIwU,YAAY,CAACC,SAAS,KAAK,QAAQ,EAAE;MAAA;MAAA1U,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACvC4B,eAAe,CAACC,IAAI,CAAC,8BAA8B,EAAE;QAAE6S,OAAO,EAAEF,YAAY,CAACE;MAAO,CAAE,CAAC;MACvF;IACF,CAAC;IAAA;IAAA;MAAA3U,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IAED,OAAO,KAAK;EACd,CAAC;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAA6T,yBAAyB,GAAjC,UAAkC1J,IAAS,EAAEjC,SAAiB;IAAA;IAAAjJ,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAC5D;IAAI;IAAA,CAAAD,aAAA,GAAAmB,CAAA,aAAC+J,IAAI;IAAA;IAAA,CAAAlL,aAAA,GAAAmB,CAAA,YAAI,OAAO+J,IAAI,KAAK,QAAQ,GAAE;MAAA;MAAAlL,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACrC,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAmB,CAAA;IAAA;IAED;IAAAnB,aAAA,GAAAC,CAAA;IACA,QAAQgJ,SAAS;MACf,KAAK,iBAAiB;QAAA;QAAAjJ,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACpB,OAAO,IAAI,CAAC4U,sBAAsB,CAAC3J,IAAI,CAAC;MAC1C,KAAK,wBAAwB;QAAA;QAAAlL,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QAC3B,OAAO,IAAI,CAAC6U,6BAA6B,CAAC5J,IAAI,CAAC;MACjD,KAAK,oBAAoB;QAAA;QAAAlL,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACvB,OAAO,IAAI,CAAC8U,yBAAyB,CAAC7J,IAAI,CAAC;MAC7C,KAAK,qBAAqB;QAAA;QAAAlL,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACxB,OAAO,IAAI,CAAC+U,0BAA0B,CAAC9J,IAAI,CAAC;MAC9C;QAAA;QAAAlL,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACE,OAAO,IAAI;MAAE;IACjB;EACF,CAAC;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAA8T,sBAAsB,GAA9B,UAA+B3J,IAAS;IAAA;IAAAlL,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACtC,OACE,2BAAAD,aAAA,GAAAmB,CAAA,YAAAwH,KAAK,CAACuF,OAAO,CAAChD,IAAI,CAAC5G,SAAS,CAAC;IAAA;IAAA,CAAAtE,aAAA,GAAAmB,CAAA,YAC7BwH,KAAK,CAACuF,OAAO,CAAChD,IAAI,CAAC1G,UAAU,CAAC;IAAA;IAAA,CAAAxE,aAAA,GAAAmB,CAAA,YAC9BwH,KAAK,CAACuF,OAAO,CAAChD,IAAI,CAACzG,WAAW,CAAC;IAAA;IAAA,CAAAzE,aAAA,GAAAmB,CAAA,YAC/B,OAAO+J,IAAI,CAACpG,YAAY,KAAK,QAAQ;IAAA;IAAA,CAAA9E,aAAA,GAAAmB,CAAA,YACrC+J,IAAI,CAACpG,YAAY,IAAI,CAAC;IAAA;IAAA,CAAA9E,aAAA,GAAAmB,CAAA,YACtB+J,IAAI,CAACpG,YAAY,IAAI,GAAG;EAE5B,CAAC;EAAA;EAAA9E,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAA+T,6BAA6B,GAArC,UAAsC5J,IAAS;IAAA;IAAAlL,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAC7C,OACE,2BAAAD,aAAA,GAAAmB,CAAA,YAAAwH,KAAK,CAACuF,OAAO,CAAChD,IAAI,CAACjG,eAAe,CAAC;IAAA;IAAA,CAAAjF,aAAA,GAAAmB,CAAA,YACnC+J,IAAI,CAACjG,eAAe,CAACgQ,KAAK,CAAC,UAACC,GAAQ;MAAA;MAAAlV,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAClC,kCAAAD,aAAA,GAAAmB,CAAA,mBAAO+T,GAAG,CAAChQ,UAAU,KAAK,QAAQ;MAAA;MAAA,CAAAlF,aAAA,GAAAmB,CAAA,YAClC,OAAO+T,GAAG,CAAC7P,UAAU,KAAK,QAAQ;MAAA;MAAA,CAAArF,aAAA,GAAAmB,CAAA,YAClC+T,GAAG,CAAC7P,UAAU,IAAI,CAAC;MAAA;MAAA,CAAArF,aAAA,GAAAmB,CAAA,YACnB+T,GAAG,CAAC7P,UAAU,IAAI,GAAG;IAHrB,CAGqB,CACtB;EAEL,CAAC;EAAA;EAAArF,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAgU,yBAAyB,GAAjC,UAAkC7J,IAAS;IAAA;IAAAlL,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACzC,OACE,2BAAAD,aAAA,GAAAmB,CAAA,mBAAO+J,IAAI,CAACpG,YAAY,KAAK,QAAQ;IAAA;IAAA,CAAA9E,aAAA,GAAAmB,CAAA,YACrC+J,IAAI,CAACpG,YAAY,IAAI,CAAC;IAAA;IAAA,CAAA9E,aAAA,GAAAmB,CAAA,YACtB+J,IAAI,CAACpG,YAAY,IAAI,EAAE;IAAA;IAAA,CAAA9E,aAAA,GAAAmB,CAAA,YACvB,OAAO+J,IAAI,CAAC3E,QAAQ,KAAK,QAAQ;IAAA;IAAA,CAAAvG,aAAA,GAAAmB,CAAA,YACjC,OAAO+J,IAAI,CAAC1E,QAAQ,KAAK,QAAQ;EAErC,CAAC;EAAA;EAAAxG,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAyL,6BAA6B,GAArC,UAAsCtB,IAAS;IAAA;IAAAlL,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAC7C;IACA;IAAI;IAAA,CAAAD,aAAA,GAAAmB,CAAA,mBAAO+J,IAAI,CAACiK,OAAO,KAAK,QAAQ;IAAA;IAAA,CAAAnV,aAAA,GAAAmB,CAAA,YAAIwG,KAAK,CAACuD,IAAI,CAACiK,OAAO,CAAC,GAAE;MAAA;MAAAnV,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAC3D;MACA;MAAI;MAAA,CAAAD,aAAA,GAAAmB,CAAA,mBAAO+J,IAAI,CAACpG,YAAY,KAAK,QAAQ;MAAA;MAAA,CAAA9E,aAAA,GAAAmB,CAAA,YAAI,CAACwG,KAAK,CAACuD,IAAI,CAACpG,YAAY,CAAC,GAAE;QAAA;QAAA9E,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACtEiL,IAAI,CAACiK,OAAO,GAAGjK,IAAI,CAACpG,YAAY;MAClC,CAAC,MAAM;QAAA;QAAA9E,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QACL;QACAiL,IAAI,CAACiK,OAAO,GAAG,CAAC;QAAC;QAAAnV,aAAA,GAAAC,CAAA;QACjBoC,OAAO,CAACP,IAAI,CAAC,4DAA4D,CAAC;MAC5E;IACF,CAAC;IAAA;IAAA;MAAA9B,aAAA,GAAAmB,CAAA;IAAA;IAED;IAAAnB,aAAA,GAAAC,CAAA;IACAiL,IAAI,CAACiK,OAAO,GAAGC,IAAI,CAACpQ,GAAG,CAAC,CAAC,EAAEoQ,IAAI,CAACrQ,GAAG,CAAC,EAAE,EAAEmG,IAAI,CAACiK,OAAO,CAAC,CAAC;IAEtD;IAAA;IAAAnV,aAAA,GAAAC,CAAA;IACA,IAAI,CAACiL,IAAI,CAAC1E,QAAQ,EAAE;MAAA;MAAAxG,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAClBiL,IAAI,CAAC1E,QAAQ,GAAG;QAAElC,SAAS,EAAE,EAAE;QAAEmC,YAAY,EAAE;MAAE,CAAE;IACrD,CAAC;IAAA;IAAA;MAAAzG,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IACD,IAAI,CAACiL,IAAI,CAAC5G,SAAS,EAAE;MAAA;MAAAtE,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACnBiL,IAAI,CAAC5G,SAAS,GAAG,EAAE;IACrB,CAAC;IAAA;IAAA;MAAAtE,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IACD,IAAI,CAACiL,IAAI,CAACzE,YAAY,EAAE;MAAA;MAAAzG,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACtBiL,IAAI,CAACzE,YAAY,GAAG,EAAE;IACxB,CAAC;IAAA;IAAA;MAAAzG,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IACD,IAAI,OAAOiL,IAAI,CAACxE,eAAe,KAAK,QAAQ,EAAE;MAAA;MAAA1G,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAC5CiL,IAAI,CAACxE,eAAe,GAAGwE,IAAI,CAACiK,OAAO;IACrC,CAAC;IAAA;IAAA;MAAAnV,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IACD,IAAI,OAAOiL,IAAI,CAACvE,eAAe,KAAK,QAAQ,EAAE;MAAA;MAAA3G,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAC5CiL,IAAI,CAACvE,eAAe,GAAG,GAAG;IAC5B,CAAC;IAAA;IAAA;MAAA3G,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IACD,IAAI,OAAOiL,IAAI,CAACtE,kBAAkB,KAAK,QAAQ,EAAE;MAAA;MAAA5G,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAC/CiL,IAAI,CAACtE,kBAAkB,GAAGsE,IAAI,CAACiK,OAAO;IACxC,CAAC;IAAA;IAAA;MAAAnV,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IAED,OAAOiL,IAAI;EACb,CAAC;EAAA;EAAAlL,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAiU,0BAA0B,GAAlC,UAAmC9J,IAAS;IAAA;IAAAlL,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAC1C,OACE,2BAAAD,aAAA,GAAAmB,CAAA,YAAAwH,KAAK,CAACuF,OAAO,CAAChD,IAAI,CAACvF,SAAS,CAAC;IAAA;IAAA,CAAA3F,aAAA,GAAAmB,CAAA,YAC7B+J,IAAI,CAACvF,SAAS,CAACsP,KAAK,CAAC,UAACI,CAAM;MAAA;MAAArV,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAC1B,kCAAAD,aAAA,GAAAmB,CAAA,mBAAOkU,CAAC,CAACzP,YAAY,KAAK,QAAQ;MAAA;MAAA,CAAA5F,aAAA,GAAAmB,CAAA,YAClC,OAAOkU,CAAC,CAACxP,YAAY,KAAK,QAAQ;MAAA;MAAA,CAAA7F,aAAA,GAAAmB,CAAA,YAClC,OAAOkU,CAAC,CAACvP,QAAQ,KAAK,QAAQ;IAF9B,CAE8B,CAC/B;EAEL,CAAC;EAAA;EAAA9F,aAAA,GAAAC,CAAA;EAEO6G,aAAA,CAAA/F,SAAA,CAAAuU,YAAY,GAApB,UAAqB1J,IAAY;IAAA;IAAA5L,aAAA,GAAAO,CAAA;IAC/B,IAAMgV,UAAU;IAAA;IAAA,CAAAvV,aAAA,GAAAC,CAAA,SAAGG,oBAAA,CAAA6R,OAAgB,CAACM,iBAAiB,CAAC3G,IAAI,EAAE;MAC1DxG,SAAS,EAAE,KAAK;MAChBoN,SAAS,EAAE,KAAK;MAChBgD,iBAAiB,EAAE;KACpB,CAAC;IAAC;IAAAxV,aAAA,GAAAC,CAAA;IAEH,IAAI,CAACsV,UAAU,CAACnD,OAAO,EAAE;MAAA;MAAApS,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MACvB4B,eAAe,CAACC,IAAI,CAAC,0BAA0B,EAAE;QAAEuQ,MAAM,EAAEkD,UAAU,CAAClD;MAAM,CAAE,CAAC;MAAC;MAAArS,aAAA,GAAAC,CAAA;MAChF,OAAO,EAAE;IACX,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IAED,OAAO,2BAAAD,aAAA,GAAAmB,CAAA,YAAAoU,UAAU,CAAC5C,cAAc;IAAA;IAAA,CAAA3S,aAAA,GAAAmB,CAAA,YAAIyK,IAAI;EAC1C,CAAC;EAAA;EAAA5L,aAAA,GAAAC,CAAA;EACH,OAAA6G,aAAC;AAAD,CAAC,CA1gDD;AA0gDC;AAAA9G,aAAA,GAAAC,CAAA;AAEYgD,OAAA,CAAAwS,aAAa,GAAG,IAAI3O,aAAa,EAAE;AAAC;AAAA9G,aAAA,GAAAC,CAAA;AACjDgD,OAAA,CAAAgP,OAAA,GAAehP,OAAA,CAAAwS,aAAa", "ignoreList": []}