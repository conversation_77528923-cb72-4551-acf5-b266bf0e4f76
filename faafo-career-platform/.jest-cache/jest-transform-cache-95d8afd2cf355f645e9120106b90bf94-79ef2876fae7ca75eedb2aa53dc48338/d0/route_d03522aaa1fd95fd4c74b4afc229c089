cfce51d8c200d7a1ad7c738edcd110a5
"use strict";

/**
 * Test endpoint for AI service functionality
 * ONLY FOR TESTING - BYPASSES AUTHENTICATION
 */
/* istanbul ignore next */
function cov_1yaz24beg9() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/test/ai-service/route.ts";
  var hash = "a0b37a6ea35fa36a6453a315c692b19cea54063a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/test/ai-service/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "2": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "4": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "5": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "6": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "8": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "9": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "11": {
        start: {
          line: 17,
          column: 16
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "12": {
        start: {
          line: 18,
          column: 28
        },
        end: {
          line: 18,
          column: 110
        }
      },
      "13": {
        start: {
          line: 18,
          column: 91
        },
        end: {
          line: 18,
          column: 106
        }
      },
      "14": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 24,
          column: 7
        }
      },
      "15": {
        start: {
          line: 20,
          column: 36
        },
        end: {
          line: 20,
          column: 97
        }
      },
      "16": {
        start: {
          line: 20,
          column: 42
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "17": {
        start: {
          line: 20,
          column: 85
        },
        end: {
          line: 20,
          column: 95
        }
      },
      "18": {
        start: {
          line: 21,
          column: 35
        },
        end: {
          line: 21,
          column: 100
        }
      },
      "19": {
        start: {
          line: 21,
          column: 41
        },
        end: {
          line: 21,
          column: 73
        }
      },
      "20": {
        start: {
          line: 21,
          column: 88
        },
        end: {
          line: 21,
          column: 98
        }
      },
      "21": {
        start: {
          line: 22,
          column: 32
        },
        end: {
          line: 22,
          column: 116
        }
      },
      "22": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 78
        }
      },
      "23": {
        start: {
          line: 26,
          column: 18
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "24": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "25": {
        start: {
          line: 27,
          column: 43
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "26": {
        start: {
          line: 27,
          column: 57
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "27": {
        start: {
          line: 27,
          column: 69
        },
        end: {
          line: 27,
          column: 81
        }
      },
      "28": {
        start: {
          line: 27,
          column: 119
        },
        end: {
          line: 27,
          column: 196
        }
      },
      "29": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 160
        }
      },
      "30": {
        start: {
          line: 28,
          column: 141
        },
        end: {
          line: 28,
          column: 153
        }
      },
      "31": {
        start: {
          line: 29,
          column: 23
        },
        end: {
          line: 29,
          column: 68
        }
      },
      "32": {
        start: {
          line: 29,
          column: 45
        },
        end: {
          line: 29,
          column: 65
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "34": {
        start: {
          line: 31,
          column: 15
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "35": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "36": {
        start: {
          line: 32,
          column: 50
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "37": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "38": {
        start: {
          line: 33,
          column: 160
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "39": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "40": {
        start: {
          line: 34,
          column: 26
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "41": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "42": {
        start: {
          line: 36,
          column: 32
        },
        end: {
          line: 36,
          column: 39
        }
      },
      "43": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 46
        }
      },
      "44": {
        start: {
          line: 37,
          column: 24
        },
        end: {
          line: 37,
          column: 34
        }
      },
      "45": {
        start: {
          line: 37,
          column: 35
        },
        end: {
          line: 37,
          column: 72
        }
      },
      "46": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "47": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 45
        }
      },
      "48": {
        start: {
          line: 38,
          column: 46
        },
        end: {
          line: 38,
          column: 55
        }
      },
      "49": {
        start: {
          line: 38,
          column: 56
        },
        end: {
          line: 38,
          column: 65
        }
      },
      "50": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 41
        }
      },
      "51": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "52": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "53": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 128
        }
      },
      "54": {
        start: {
          line: 41,
          column: 110
        },
        end: {
          line: 41,
          column: 116
        }
      },
      "55": {
        start: {
          line: 41,
          column: 117
        },
        end: {
          line: 41,
          column: 126
        }
      },
      "56": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 106
        }
      },
      "57": {
        start: {
          line: 42,
          column: 81
        },
        end: {
          line: 42,
          column: 97
        }
      },
      "58": {
        start: {
          line: 42,
          column: 98
        },
        end: {
          line: 42,
          column: 104
        }
      },
      "59": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 89
        }
      },
      "60": {
        start: {
          line: 43,
          column: 57
        },
        end: {
          line: 43,
          column: 72
        }
      },
      "61": {
        start: {
          line: 43,
          column: 73
        },
        end: {
          line: 43,
          column: 80
        }
      },
      "62": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 87
        }
      },
      "63": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "64": {
        start: {
          line: 44,
          column: 47
        },
        end: {
          line: 44,
          column: 62
        }
      },
      "65": {
        start: {
          line: 44,
          column: 63
        },
        end: {
          line: 44,
          column: 78
        }
      },
      "66": {
        start: {
          line: 44,
          column: 79
        },
        end: {
          line: 44,
          column: 85
        }
      },
      "67": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "68": {
        start: {
          line: 45,
          column: 30
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "69": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 33
        }
      },
      "70": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 43
        }
      },
      "71": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 48,
          column: 39
        }
      },
      "72": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "73": {
        start: {
          line: 49,
          column: 35
        },
        end: {
          line: 49,
          column: 41
        }
      },
      "74": {
        start: {
          line: 49,
          column: 54
        },
        end: {
          line: 49,
          column: 64
        }
      },
      "75": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "76": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "77": {
        start: {
          line: 50,
          column: 36
        },
        end: {
          line: 50,
          column: 89
        }
      },
      "78": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 62
        }
      },
      "79": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 36
        }
      },
      "80": {
        start: {
          line: 55,
          column: 15
        },
        end: {
          line: 55,
          column: 37
        }
      },
      "81": {
        start: {
          line: 56,
          column: 22
        },
        end: {
          line: 56,
          column: 61
        }
      },
      "82": {
        start: {
          line: 57,
          column: 27
        },
        end: {
          line: 57,
          column: 62
        }
      },
      "83": {
        start: {
          line: 58,
          column: 34
        },
        end: {
          line: 58,
          column: 76
        }
      },
      "84": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 91,
          column: 7
        }
      },
      "85": {
        start: {
          line: 59,
          column: 94
        },
        end: {
          line: 91,
          column: 3
        }
      },
      "86": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 90,
          column: 7
        }
      },
      "87": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 89,
          column: 9
        }
      },
      "88": {
        start: {
          line: 63,
          column: 20
        },
        end: {
          line: 63,
          column: 57
        }
      },
      "89": {
        start: {
          line: 65,
          column: 16
        },
        end: {
          line: 65,
          column: 71
        }
      },
      "90": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 30
        }
      },
      "91": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 73,
          column: 17
        }
      },
      "92": {
        start: {
          line: 68,
          column: 44
        },
        end: {
          line: 68,
          column: 68
        }
      },
      "93": {
        start: {
          line: 69,
          column: 51
        },
        end: {
          line: 69,
          column: 75
        }
      },
      "94": {
        start: {
          line: 70,
          column: 48
        },
        end: {
          line: 70,
          column: 72
        }
      },
      "95": {
        start: {
          line: 71,
          column: 41
        },
        end: {
          line: 71,
          column: 65
        }
      },
      "96": {
        start: {
          line: 72,
          column: 39
        },
        end: {
          line: 72,
          column: 64
        }
      },
      "97": {
        start: {
          line: 74,
          column: 16
        },
        end: {
          line: 74,
          column: 41
        }
      },
      "98": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 67
        }
      },
      "99": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 53
        }
      },
      "100": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 77,
          column: 74
        }
      },
      "101": {
        start: {
          line: 78,
          column: 20
        },
        end: {
          line: 78,
          column: 53
        }
      },
      "102": {
        start: {
          line: 79,
          column: 20
        },
        end: {
          line: 79,
          column: 71
        }
      },
      "103": {
        start: {
          line: 80,
          column: 20
        },
        end: {
          line: 80,
          column: 53
        }
      },
      "104": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 81,
          column: 60
        }
      },
      "105": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 82,
          column: 53
        }
      },
      "106": {
        start: {
          line: 83,
          column: 21
        },
        end: {
          line: 83,
          column: 60
        }
      },
      "107": {
        start: {
          line: 84,
          column: 21
        },
        end: {
          line: 84,
          column: 54
        }
      },
      "108": {
        start: {
          line: 86,
          column: 16
        },
        end: {
          line: 86,
          column: 55
        }
      },
      "109": {
        start: {
          line: 87,
          column: 16
        },
        end: {
          line: 87,
          column: 39
        }
      },
      "110": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 28
        }
      },
      "111": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 111,
          column: 7
        }
      },
      "112": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 110,
          column: 11
        }
      },
      "113": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 109,
          column: 13
        }
      },
      "114": {
        start: {
          line: 98,
          column: 20
        },
        end: {
          line: 98,
          column: 342
        }
      },
      "115": {
        start: {
          line: 99,
          column: 20
        },
        end: {
          line: 99,
          column: 115
        }
      },
      "116": {
        start: {
          line: 101,
          column: 20
        },
        end: {
          line: 101,
          column: 39
        }
      },
      "117": {
        start: {
          line: 102,
          column: 20
        },
        end: {
          line: 108,
          column: 28
        }
      },
      "118": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 134,
          column: 7
        }
      },
      "119": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 133,
          column: 11
        }
      },
      "120": {
        start: {
          line: 117,
          column: 12
        },
        end: {
          line: 132,
          column: 13
        }
      },
      "121": {
        start: {
          line: 119,
          column: 20
        },
        end: {
          line: 119,
          column: 112
        }
      },
      "122": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 93
        }
      },
      "123": {
        start: {
          line: 121,
          column: 20
        },
        end: {
          line: 121,
          column: 85
        }
      },
      "124": {
        start: {
          line: 122,
          column: 20
        },
        end: {
          line: 122,
          column: 163
        }
      },
      "125": {
        start: {
          line: 124,
          column: 20
        },
        end: {
          line: 124,
          column: 39
        }
      },
      "126": {
        start: {
          line: 125,
          column: 20
        },
        end: {
          line: 131,
          column: 28
        }
      },
      "127": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "128": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 154,
          column: 11
        }
      },
      "129": {
        start: {
          line: 140,
          column: 12
        },
        end: {
          line: 153,
          column: 13
        }
      },
      "130": {
        start: {
          line: 142,
          column: 20
        },
        end: {
          line: 142,
          column: 181
        }
      },
      "131": {
        start: {
          line: 143,
          column: 20
        },
        end: {
          line: 143,
          column: 107
        }
      },
      "132": {
        start: {
          line: 145,
          column: 20
        },
        end: {
          line: 145,
          column: 39
        }
      },
      "133": {
        start: {
          line: 146,
          column: 20
        },
        end: {
          line: 152,
          column: 28
        }
      },
      "134": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 174,
          column: 7
        }
      },
      "135": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 173,
          column: 11
        }
      },
      "136": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 172,
          column: 13
        }
      },
      "137": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 90
        }
      },
      "138": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 45
        }
      },
      "139": {
        start: {
          line: 165,
          column: 20
        },
        end: {
          line: 171,
          column: 28
        }
      },
      "140": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 195,
          column: 7
        }
      },
      "141": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 194,
          column: 11
        }
      },
      "142": {
        start: {
          line: 180,
          column: 12
        },
        end: {
          line: 180,
          column: 73
        }
      },
      "143": {
        start: {
          line: 181,
          column: 12
        },
        end: {
          line: 181,
          column: 82
        }
      },
      "144": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 182,
          column: 86
        }
      },
      "145": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 193,
          column: 20
        }
      },
      "146": {
        start: {
          line: 197,
          column: 0
        },
        end: {
          line: 242,
          column: 7
        }
      },
      "147": {
        start: {
          line: 197,
          column: 93
        },
        end: {
          line: 242,
          column: 3
        }
      },
      "148": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 241,
          column: 7
        }
      },
      "149": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 240,
          column: 9
        }
      },
      "150": {
        start: {
          line: 202,
          column: 16
        },
        end: {
          line: 202,
          column: 65
        }
      },
      "151": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 203,
          column: 52
        }
      },
      "152": {
        start: {
          line: 204,
          column: 16
        },
        end: {
          line: 222,
          column: 17
        }
      },
      "153": {
        start: {
          line: 205,
          column: 20
        },
        end: {
          line: 221,
          column: 28
        }
      },
      "154": {
        start: {
          line: 223,
          column: 16
        },
        end: {
          line: 223,
          column: 30
        }
      },
      "155": {
        start: {
          line: 224,
          column: 16
        },
        end: {
          line: 228,
          column: 17
        }
      },
      "156": {
        start: {
          line: 225,
          column: 41
        },
        end: {
          line: 225,
          column: 65
        }
      },
      "157": {
        start: {
          line: 226,
          column: 39
        },
        end: {
          line: 226,
          column: 63
        }
      },
      "158": {
        start: {
          line: 227,
          column: 44
        },
        end: {
          line: 227,
          column: 68
        }
      },
      "159": {
        start: {
          line: 229,
          column: 16
        },
        end: {
          line: 229,
          column: 40
        }
      },
      "160": {
        start: {
          line: 230,
          column: 20
        },
        end: {
          line: 230,
          column: 60
        }
      },
      "161": {
        start: {
          line: 231,
          column: 20
        },
        end: {
          line: 231,
          column: 53
        }
      },
      "162": {
        start: {
          line: 232,
          column: 20
        },
        end: {
          line: 232,
          column: 59
        }
      },
      "163": {
        start: {
          line: 233,
          column: 20
        },
        end: {
          line: 233,
          column: 53
        }
      },
      "164": {
        start: {
          line: 234,
          column: 20
        },
        end: {
          line: 234,
          column: 65
        }
      },
      "165": {
        start: {
          line: 235,
          column: 20
        },
        end: {
          line: 235,
          column: 53
        }
      },
      "166": {
        start: {
          line: 237,
          column: 16
        },
        end: {
          line: 237,
          column: 71
        }
      },
      "167": {
        start: {
          line: 238,
          column: 16
        },
        end: {
          line: 238,
          column: 39
        }
      },
      "168": {
        start: {
          line: 239,
          column: 16
        },
        end: {
          line: 239,
          column: 28
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 17,
            column: 45
          }
        },
        loc: {
          start: {
            line: 17,
            column: 89
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 18
          }
        },
        loc: {
          start: {
            line: 18,
            column: 26
          },
          end: {
            line: 18,
            column: 112
          }
        },
        line: 18
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 71
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 18,
            column: 108
          }
        },
        line: 18
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 36
          },
          end: {
            line: 19,
            column: 37
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 24,
            column: 5
          }
        },
        line: 19
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 20,
            column: 17
          },
          end: {
            line: 20,
            column: 26
          }
        },
        loc: {
          start: {
            line: 20,
            column: 34
          },
          end: {
            line: 20,
            column: 99
          }
        },
        line: 20
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 25
          }
        },
        loc: {
          start: {
            line: 21,
            column: 33
          },
          end: {
            line: 21,
            column: 102
          }
        },
        line: 21
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 21
          }
        },
        loc: {
          start: {
            line: 22,
            column: 30
          },
          end: {
            line: 22,
            column: 118
          }
        },
        line: 22
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 26,
            column: 49
          }
        },
        loc: {
          start: {
            line: 26,
            column: 73
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 26
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 27,
            column: 30
          },
          end: {
            line: 27,
            column: 31
          }
        },
        loc: {
          start: {
            line: 27,
            column: 41
          },
          end: {
            line: 27,
            column: 83
          }
        },
        line: 27
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 28,
            column: 128
          },
          end: {
            line: 28,
            column: 129
          }
        },
        loc: {
          start: {
            line: 28,
            column: 139
          },
          end: {
            line: 28,
            column: 155
          }
        },
        line: 28
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 29,
            column: 13
          },
          end: {
            line: 29,
            column: 17
          }
        },
        loc: {
          start: {
            line: 29,
            column: 21
          },
          end: {
            line: 29,
            column: 70
          }
        },
        line: 29
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 29,
            column: 31
          }
        },
        loc: {
          start: {
            line: 29,
            column: 43
          },
          end: {
            line: 29,
            column: 67
          }
        },
        line: 29
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 22
          },
          end: {
            line: 51,
            column: 5
          }
        },
        line: 30
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 59,
            column: 73
          },
          end: {
            line: 59,
            column: 74
          }
        },
        loc: {
          start: {
            line: 59,
            column: 92
          },
          end: {
            line: 91,
            column: 5
          }
        },
        line: 59
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 59,
            column: 136
          },
          end: {
            line: 59,
            column: 137
          }
        },
        loc: {
          start: {
            line: 59,
            column: 148
          },
          end: {
            line: 91,
            column: 1
          }
        },
        line: 59
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 61,
            column: 29
          },
          end: {
            line: 61,
            column: 30
          }
        },
        loc: {
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 90,
            column: 5
          }
        },
        line: 61
      },
      "18": {
        name: "testResumeAnalysis",
        decl: {
          start: {
            line: 92,
            column: 9
          },
          end: {
            line: 92,
            column: 27
          }
        },
        loc: {
          start: {
            line: 92,
            column: 34
          },
          end: {
            line: 112,
            column: 1
          }
        },
        line: 92
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 93,
            column: 43
          },
          end: {
            line: 93,
            column: 44
          }
        },
        loc: {
          start: {
            line: 93,
            column: 55
          },
          end: {
            line: 111,
            column: 5
          }
        },
        line: 93
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 95,
            column: 33
          },
          end: {
            line: 95,
            column: 34
          }
        },
        loc: {
          start: {
            line: 95,
            column: 47
          },
          end: {
            line: 110,
            column: 9
          }
        },
        line: 95
      },
      "21": {
        name: "testCareerRecommendations",
        decl: {
          start: {
            line: 113,
            column: 9
          },
          end: {
            line: 113,
            column: 34
          }
        },
        loc: {
          start: {
            line: 113,
            column: 41
          },
          end: {
            line: 135,
            column: 1
          }
        },
        line: 113
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 114,
            column: 43
          },
          end: {
            line: 114,
            column: 44
          }
        },
        loc: {
          start: {
            line: 114,
            column: 55
          },
          end: {
            line: 134,
            column: 5
          }
        },
        line: 114
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 116,
            column: 33
          },
          end: {
            line: 116,
            column: 34
          }
        },
        loc: {
          start: {
            line: 116,
            column: 47
          },
          end: {
            line: 133,
            column: 9
          }
        },
        line: 116
      },
      "24": {
        name: "testInterviewQuestions",
        decl: {
          start: {
            line: 136,
            column: 9
          },
          end: {
            line: 136,
            column: 31
          }
        },
        loc: {
          start: {
            line: 136,
            column: 38
          },
          end: {
            line: 156,
            column: 1
          }
        },
        line: 136
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 137,
            column: 43
          },
          end: {
            line: 137,
            column: 44
          }
        },
        loc: {
          start: {
            line: 137,
            column: 55
          },
          end: {
            line: 155,
            column: 5
          }
        },
        line: 137
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 139,
            column: 33
          },
          end: {
            line: 139,
            column: 34
          }
        },
        loc: {
          start: {
            line: 139,
            column: 47
          },
          end: {
            line: 154,
            column: 9
          }
        },
        line: 139
      },
      "27": {
        name: "testHealthCheck",
        decl: {
          start: {
            line: 157,
            column: 9
          },
          end: {
            line: 157,
            column: 24
          }
        },
        loc: {
          start: {
            line: 157,
            column: 27
          },
          end: {
            line: 175,
            column: 1
          }
        },
        line: 157
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 158,
            column: 43
          },
          end: {
            line: 158,
            column: 44
          }
        },
        loc: {
          start: {
            line: 158,
            column: 55
          },
          end: {
            line: 174,
            column: 5
          }
        },
        line: 158
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 160,
            column: 33
          },
          end: {
            line: 160,
            column: 34
          }
        },
        loc: {
          start: {
            line: 160,
            column: 47
          },
          end: {
            line: 173,
            column: 9
          }
        },
        line: 160
      },
      "30": {
        name: "testMonitoring",
        decl: {
          start: {
            line: 176,
            column: 9
          },
          end: {
            line: 176,
            column: 23
          }
        },
        loc: {
          start: {
            line: 176,
            column: 26
          },
          end: {
            line: 196,
            column: 1
          }
        },
        line: 176
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 177,
            column: 43
          },
          end: {
            line: 177,
            column: 44
          }
        },
        loc: {
          start: {
            line: 177,
            column: 55
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 177
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 179,
            column: 33
          },
          end: {
            line: 179,
            column: 34
          }
        },
        loc: {
          start: {
            line: 179,
            column: 47
          },
          end: {
            line: 194,
            column: 9
          }
        },
        line: 179
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 197,
            column: 72
          },
          end: {
            line: 197,
            column: 73
          }
        },
        loc: {
          start: {
            line: 197,
            column: 91
          },
          end: {
            line: 242,
            column: 5
          }
        },
        line: 197
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 197,
            column: 135
          },
          end: {
            line: 197,
            column: 136
          }
        },
        loc: {
          start: {
            line: 197,
            column: 147
          },
          end: {
            line: 242,
            column: 1
          }
        },
        line: 197
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 199,
            column: 29
          },
          end: {
            line: 199,
            column: 30
          }
        },
        loc: {
          start: {
            line: 199,
            column: 43
          },
          end: {
            line: 241,
            column: 5
          }
        },
        line: 199
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 16
          },
          end: {
            line: 25,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 21
          }
        }, {
          start: {
            line: 17,
            column: 25
          },
          end: {
            line: 17,
            column: 39
          }
        }, {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 25,
            column: 1
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 18,
            column: 35
          },
          end: {
            line: 18,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 56
          },
          end: {
            line: 18,
            column: 61
          }
        }, {
          start: {
            line: 18,
            column: 64
          },
          end: {
            line: 18,
            column: 109
          }
        }],
        line: 18
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 17
          }
        }, {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 19,
            column: 33
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 22,
            column: 32
          },
          end: {
            line: 22,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 46
          },
          end: {
            line: 22,
            column: 67
          }
        }, {
          start: {
            line: 22,
            column: 70
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "7": {
        loc: {
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 61
          }
        }, {
          start: {
            line: 23,
            column: 65
          },
          end: {
            line: 23,
            column: 67
          }
        }],
        line: 23
      },
      "8": {
        loc: {
          start: {
            line: 26,
            column: 18
          },
          end: {
            line: 52,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 19
          },
          end: {
            line: 26,
            column: 23
          }
        }, {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 26,
            column: 43
          }
        }, {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 52,
            column: 1
          }
        }],
        line: 26
      },
      "9": {
        loc: {
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "10": {
        loc: {
          start: {
            line: 27,
            column: 134
          },
          end: {
            line: 27,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 27,
            column: 167
          },
          end: {
            line: 27,
            column: 175
          }
        }, {
          start: {
            line: 27,
            column: 178
          },
          end: {
            line: 27,
            column: 184
          }
        }],
        line: 27
      },
      "11": {
        loc: {
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 102
          }
        }, {
          start: {
            line: 28,
            column: 107
          },
          end: {
            line: 28,
            column: 155
          }
        }],
        line: 28
      },
      "12": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "13": {
        loc: {
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 16
          }
        }, {
          start: {
            line: 32,
            column: 21
          },
          end: {
            line: 32,
            column: 44
          }
        }],
        line: 32
      },
      "14": {
        loc: {
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 33
          }
        }, {
          start: {
            line: 32,
            column: 38
          },
          end: {
            line: 32,
            column: 43
          }
        }],
        line: 32
      },
      "15": {
        loc: {
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "16": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 24
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 125
          }
        }, {
          start: {
            line: 33,
            column: 130
          },
          end: {
            line: 33,
            column: 158
          }
        }],
        line: 33
      },
      "17": {
        loc: {
          start: {
            line: 33,
            column: 33
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 45
          },
          end: {
            line: 33,
            column: 56
          }
        }, {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "18": {
        loc: {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        }, {
          start: {
            line: 33,
            column: 119
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "19": {
        loc: {
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 77
          }
        }, {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 115
          }
        }],
        line: 33
      },
      "20": {
        loc: {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 83
          },
          end: {
            line: 33,
            column: 98
          }
        }, {
          start: {
            line: 33,
            column: 103
          },
          end: {
            line: 33,
            column: 112
          }
        }],
        line: 33
      },
      "21": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "22": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 47,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 36,
            column: 23
          }
        }, {
          start: {
            line: 36,
            column: 24
          },
          end: {
            line: 36,
            column: 46
          }
        }, {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 72
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 46,
            column: 43
          }
        }],
        line: 35
      },
      "23": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "24": {
        loc: {
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 74
          }
        }, {
          start: {
            line: 41,
            column: 79
          },
          end: {
            line: 41,
            column: 90
          }
        }, {
          start: {
            line: 41,
            column: 94
          },
          end: {
            line: 41,
            column: 105
          }
        }],
        line: 41
      },
      "25": {
        loc: {
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 54
          }
        }, {
          start: {
            line: 41,
            column: 58
          },
          end: {
            line: 41,
            column: 73
          }
        }],
        line: 41
      },
      "26": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "27": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 35
          }
        }, {
          start: {
            line: 42,
            column: 40
          },
          end: {
            line: 42,
            column: 42
          }
        }, {
          start: {
            line: 42,
            column: 47
          },
          end: {
            line: 42,
            column: 59
          }
        }, {
          start: {
            line: 42,
            column: 63
          },
          end: {
            line: 42,
            column: 75
          }
        }],
        line: 42
      },
      "28": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "29": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 39
          },
          end: {
            line: 43,
            column: 53
          }
        }],
        line: 43
      },
      "30": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "31": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 25
          }
        }, {
          start: {
            line: 44,
            column: 29
          },
          end: {
            line: 44,
            column: 43
          }
        }],
        line: 44
      },
      "32": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "33": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "34": {
        loc: {
          start: {
            line: 50,
            column: 52
          },
          end: {
            line: 50,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 60
          },
          end: {
            line: 50,
            column: 65
          }
        }, {
          start: {
            line: 50,
            column: 68
          },
          end: {
            line: 50,
            column: 74
          }
        }],
        line: 50
      },
      "35": {
        loc: {
          start: {
            line: 62,
            column: 8
          },
          end: {
            line: 89,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 57
          }
        }, {
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 74,
            column: 41
          }
        }, {
          start: {
            line: 75,
            column: 12
          },
          end: {
            line: 75,
            column: 67
          }
        }, {
          start: {
            line: 76,
            column: 12
          },
          end: {
            line: 76,
            column: 53
          }
        }, {
          start: {
            line: 77,
            column: 12
          },
          end: {
            line: 77,
            column: 74
          }
        }, {
          start: {
            line: 78,
            column: 12
          },
          end: {
            line: 78,
            column: 53
          }
        }, {
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 79,
            column: 71
          }
        }, {
          start: {
            line: 80,
            column: 12
          },
          end: {
            line: 80,
            column: 53
          }
        }, {
          start: {
            line: 81,
            column: 12
          },
          end: {
            line: 81,
            column: 60
          }
        }, {
          start: {
            line: 82,
            column: 12
          },
          end: {
            line: 82,
            column: 53
          }
        }, {
          start: {
            line: 83,
            column: 12
          },
          end: {
            line: 83,
            column: 60
          }
        }, {
          start: {
            line: 84,
            column: 12
          },
          end: {
            line: 84,
            column: 54
          }
        }, {
          start: {
            line: 85,
            column: 12
          },
          end: {
            line: 88,
            column: 28
          }
        }],
        line: 62
      },
      "36": {
        loc: {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 73,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 68,
            column: 20
          },
          end: {
            line: 68,
            column: 68
          }
        }, {
          start: {
            line: 69,
            column: 20
          },
          end: {
            line: 69,
            column: 75
          }
        }, {
          start: {
            line: 70,
            column: 20
          },
          end: {
            line: 70,
            column: 72
          }
        }, {
          start: {
            line: 71,
            column: 20
          },
          end: {
            line: 71,
            column: 65
          }
        }, {
          start: {
            line: 72,
            column: 20
          },
          end: {
            line: 72,
            column: 64
          }
        }],
        line: 67
      },
      "37": {
        loc: {
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 109,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 97,
            column: 16
          },
          end: {
            line: 99,
            column: 115
          }
        }, {
          start: {
            line: 100,
            column: 16
          },
          end: {
            line: 108,
            column: 28
          }
        }],
        line: 96
      },
      "38": {
        loc: {
          start: {
            line: 98,
            column: 33
          },
          end: {
            line: 98,
            column: 341
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 33
          },
          end: {
            line: 98,
            column: 48
          }
        }, {
          start: {
            line: 98,
            column: 52
          },
          end: {
            line: 98,
            column: 341
          }
        }],
        line: 98
      },
      "39": {
        loc: {
          start: {
            line: 117,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 122,
            column: 163
          }
        }, {
          start: {
            line: 123,
            column: 16
          },
          end: {
            line: 131,
            column: 28
          }
        }],
        line: 117
      },
      "40": {
        loc: {
          start: {
            line: 119,
            column: 37
          },
          end: {
            line: 119,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 37
          },
          end: {
            line: 119,
            column: 56
          }
        }, {
          start: {
            line: 119,
            column: 60
          },
          end: {
            line: 119,
            column: 111
          }
        }],
        line: 119
      },
      "41": {
        loc: {
          start: {
            line: 120,
            column: 36
          },
          end: {
            line: 120,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 120,
            column: 36
          },
          end: {
            line: 120,
            column: 54
          }
        }, {
          start: {
            line: 120,
            column: 58
          },
          end: {
            line: 120,
            column: 92
          }
        }],
        line: 120
      },
      "42": {
        loc: {
          start: {
            line: 121,
            column: 34
          },
          end: {
            line: 121,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 121,
            column: 34
          },
          end: {
            line: 121,
            column: 50
          }
        }, {
          start: {
            line: 121,
            column: 54
          },
          end: {
            line: 121,
            column: 84
          }
        }],
        line: 121
      },
      "43": {
        loc: {
          start: {
            line: 140,
            column: 12
          },
          end: {
            line: 153,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 141,
            column: 16
          },
          end: {
            line: 143,
            column: 107
          }
        }, {
          start: {
            line: 144,
            column: 16
          },
          end: {
            line: 152,
            column: 28
          }
        }],
        line: 140
      },
      "44": {
        loc: {
          start: {
            line: 161,
            column: 12
          },
          end: {
            line: 172,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 162,
            column: 16
          },
          end: {
            line: 162,
            column: 90
          }
        }, {
          start: {
            line: 163,
            column: 16
          },
          end: {
            line: 171,
            column: 28
          }
        }],
        line: 161
      },
      "45": {
        loc: {
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 240,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 201,
            column: 12
          },
          end: {
            line: 229,
            column: 40
          }
        }, {
          start: {
            line: 230,
            column: 12
          },
          end: {
            line: 230,
            column: 60
          }
        }, {
          start: {
            line: 231,
            column: 12
          },
          end: {
            line: 231,
            column: 53
          }
        }, {
          start: {
            line: 232,
            column: 12
          },
          end: {
            line: 232,
            column: 59
          }
        }, {
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 233,
            column: 53
          }
        }, {
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 234,
            column: 65
          }
        }, {
          start: {
            line: 235,
            column: 12
          },
          end: {
            line: 235,
            column: 53
          }
        }, {
          start: {
            line: 236,
            column: 12
          },
          end: {
            line: 239,
            column: 28
          }
        }],
        line: 200
      },
      "46": {
        loc: {
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 222,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 222,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "47": {
        loc: {
          start: {
            line: 224,
            column: 16
          },
          end: {
            line: 228,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 225,
            column: 20
          },
          end: {
            line: 225,
            column: 65
          }
        }, {
          start: {
            line: 226,
            column: 20
          },
          end: {
            line: 226,
            column: 63
          }
        }, {
          start: {
            line: 227,
            column: 20
          },
          end: {
            line: 227,
            column: 68
          }
        }],
        line: 224
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0, 0, 0, 0, 0, 0, 0],
      "46": [0, 0],
      "47": [0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/test/ai-service/route.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,sCAAwD;AACxD,8DAA6D;AAC7D,+DAA4D;AAC5D,6EAAwF;AAE3E,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;oBACrD,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAAzC,KAAqB,SAAoB,EAAvC,QAAQ,cAAA,EAAE,IAAI,UAAA;gBAEd,KAAA,QAAQ,CAAA;;yBACT,iBAAiB,CAAC,CAAlB,wBAAiB;yBAGjB,wBAAwB,CAAC,CAAzB,wBAAwB;yBAGxB,qBAAqB,CAAC,CAAtB,wBAAqB;yBAGrB,cAAc,CAAC,CAAf,wBAAc;yBAGd,YAAY,CAAC,CAAb,yBAAY;;;oBAXR,qBAAM,kBAAkB,CAAC,IAAI,CAAC,EAAA;oBAArC,sBAAO,SAA8B,EAAC;oBAG/B,qBAAM,yBAAyB,CAAC,IAAI,CAAC,EAAA;oBAA5C,sBAAO,SAAqC,EAAC;oBAGtC,qBAAM,sBAAsB,CAAC,IAAI,CAAC,EAAA;oBAAzC,sBAAO,SAAkC,EAAC;oBAGnC,qBAAM,eAAe,EAAE,EAAA;oBAA9B,sBAAO,SAAuB,EAAC;qBAGxB,qBAAM,cAAc,EAAE,EAAA;qBAA7B,sBAAO,SAAsB,EAAC;;gBAGxB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;gBACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBACvB,MAAM,KAAK,CAAC;;;KAEjB,CAAC,CAAC;AAEH,SAAe,kBAAkB,CAAC,IAAS;;;;;;oBACnC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,iSAavC,CAAC;oBAEe,qBAAM,6BAAa,CAAC,aAAa,CAAC,UAAU,EAAE,eAAe,CAAC,EAAA;;oBAAvE,MAAM,GAAG,SAA8D;oBAE7E,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAa;4BACtB,IAAI,EAAE;gCACJ,QAAQ,EAAE,iBAAiB;gCAC3B,MAAM,QAAA;6BACP;yBACF,CAAC,EAAC;;;;CACJ;AAED,SAAe,yBAAyB,CAAC,IAAS;;;;;;oBAC1C,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC5F,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBACzE,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC;oBAExD,qBAAM,6BAAa,CAAC,6BAA6B,CAC9D,cAAc,EACd,aAAa,EACb,WAAW,EACX,eAAe,CAChB,EAAA;;oBALK,MAAM,GAAG,SAKd;oBAED,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAa;4BACtB,IAAI,EAAE;gCACJ,QAAQ,EAAE,wBAAwB;gCAClC,MAAM,QAAA;6BACP;yBACF,CAAC,EAAC;;;;CACJ;AAED,SAAe,sBAAsB,CAAC,IAAS;;;;;;oBACvC,MAAM,cACV,WAAW,EAAE,oBAAoB,EACjC,UAAU,EAAE,mBAAmB,EAC/B,eAAe,EAAE,QAAQ,EACzB,UAAU,EAAE,cAAc,EAC1B,KAAK,EAAE,CAAC,IACL,IAAI,CACR,CAAC;oBAEa,qBAAM,6BAAa,CAAC,0BAA0B,CAAC,MAAM,CAAC,EAAA;;oBAA/D,MAAM,GAAG,SAAsD;oBAErE,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAa;4BACtB,IAAI,EAAE;gCACJ,QAAQ,EAAE,qBAAqB;gCAC/B,MAAM,QAAA;6BACP;yBACF,CAAC,EAAC;;;;CACJ;AAED,SAAe,eAAe;;;;;wBACP,qBAAM,6BAAa,CAAC,WAAW,EAAE,EAAA;;oBAAhD,YAAY,GAAG,SAAiC;oBAEtD,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAa;4BACtB,IAAI,EAAE;gCACJ,QAAQ,EAAE,cAAc;gCACxB,MAAM,EAAE,YAAY;6BACrB;yBACF,CAAC,EAAC;;;;CACJ;AAED,SAAe,cAAc;;;;YACrB,OAAO,GAAG,qCAAgB,CAAC,UAAU,EAAE,CAAC;YACxC,SAAS,GAAG,qCAAgB,CAAC,iBAAiB,EAAE,CAAC;YACjD,QAAQ,GAAG,qCAAgB,CAAC,sBAAsB,EAAE,CAAC;YAE3D,sBAAO,qBAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,EAAE,IAAa;oBACtB,IAAI,EAAE;wBACJ,QAAQ,EAAE,YAAY;wBACtB,MAAM,EAAE;4BACN,OAAO,SAAA;4BACP,SAAS,WAAA;4BACT,QAAQ,UAAA;yBACT;qBACF;iBACF,CAAC,EAAC;;;CACJ;AAEY,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;;gBACvE,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE1C,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,OAAO,EAAE,0BAA0B;gCACnC,cAAc,EAAE;oCACd,iBAAiB;oCACjB,wBAAwB;oCACxB,qBAAqB;oCACrB,cAAc;oCACd,YAAY;iCACb;gCACD,KAAK,EAAE;oCACL,IAAI,EAAE,iDAAiD;oCACvD,GAAG,EAAE,sCAAsC;iCAC5C;6BACF;yBACF,CAAC,EAAC;gBACL,CAAC;gBAGO,KAAA,QAAQ,CAAA;;yBACT,cAAc,CAAC,CAAf,wBAAc;yBAGd,YAAY,CAAC,CAAb,wBAAY;yBAGZ,iBAAiB,CAAC,CAAlB,wBAAiB;;;oBALb,qBAAM,eAAe,EAAE,EAAA;oBAA9B,sBAAO,SAAuB,EAAC;oBAGxB,qBAAM,cAAc,EAAE,EAAA;oBAA7B,sBAAO,SAAsB,EAAC;oBAGvB,qBAAM,kBAAkB,CAAC,EAAE,CAAC,EAAA;oBAAnC,sBAAO,SAA4B,EAAC;;gBAG9B,KAAK,GAAG,IAAI,KAAK,CAAC,mCAAmC,CAAQ,CAAC;gBACpE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBACvB,MAAM,KAAK,CAAC;;;KAEjB,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/test/ai-service/route.ts"],
      sourcesContent: ["/**\n * Test endpoint for AI service functionality\n * ONLY FOR TESTING - BYPASSES AUTHENTICATION\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { geminiService } from '@/lib/services/geminiService';\nimport { aiServiceMonitor } from '@/lib/ai-service-monitor';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {\n  const { testType, data } = await request.json();\n\n  switch (testType) {\n    case 'resume-analysis':\n      return await testResumeAnalysis(data);\n\n    case 'career-recommendations':\n      return await testCareerRecommendations(data);\n\n    case 'interview-questions':\n      return await testInterviewQuestions(data);\n\n    case 'health-check':\n      return await testHealthCheck();\n\n    case 'monitoring':\n      return await testMonitoring();\n\n    default:\n      const error = new Error('Invalid test type') as any;\n      error.statusCode = 400;\n      throw error;\n  }\n});\n\nasync function testResumeAnalysis(data: any) {\n  const resumeText = data.resumeText || `\nJohn Doe\nSoftware Engineer\nEmail: <EMAIL>\n\nEXPERIENCE:\nSenior Software Engineer at TechCorp (2020-2024)\n- Led development of microservices architecture\n- Implemented CI/CD pipelines\n- Mentored junior developers\n\nSKILLS:\nJavaScript, React, Node.js, Python, AWS\n`;\n\n  const result = await geminiService.analyzeResume(resumeText, 'test-user-123');\n  \n  return NextResponse.json({\n    success: true as const,\n    data: {\n      testType: 'resume-analysis',\n      result\n    }\n  });\n}\n\nasync function testCareerRecommendations(data: any) {\n  const assessmentData = data.assessmentData || { experience: 'senior', interests: ['technology'] };\n  const currentSkills = data.currentSkills || ['JavaScript', 'React', 'Node.js'];\n  const preferences = data.preferences || { workStyle: 'collaborative' };\n\n  const result = await geminiService.generateCareerRecommendations(\n    assessmentData,\n    currentSkills,\n    preferences,\n    'test-user-123'\n  );\n  \n  return NextResponse.json({\n    success: true as const,\n    data: {\n      testType: 'career-recommendations',\n      result\n    }\n  });\n}\n\nasync function testInterviewQuestions(data: any) {\n  const params = {\n    sessionType: 'TECHNICAL_PRACTICE',\n    careerPath: 'Software Engineer',\n    experienceLevel: 'SENIOR',\n    difficulty: 'INTERMEDIATE',\n    count: 3,\n    ...data\n  };\n\n  const result = await geminiService.generateInterviewQuestions(params);\n  \n  return NextResponse.json({\n    success: true as const,\n    data: {\n      testType: 'interview-questions',\n      result\n    }\n  });\n}\n\nasync function testHealthCheck() {\n  const healthStatus = await geminiService.healthCheck();\n  \n  return NextResponse.json({\n    success: true as const,\n    data: {\n      testType: 'health-check',\n      result: healthStatus\n    }\n  });\n}\n\nasync function testMonitoring() {\n  const metrics = aiServiceMonitor.getMetrics();\n  const analytics = aiServiceMonitor.getUsageAnalytics();\n  const insights = aiServiceMonitor.getPerformanceInsights();\n  \n  return NextResponse.json({\n    success: true as const,\n    data: {\n      testType: 'monitoring',\n      result: {\n        metrics,\n        analytics,\n        insights\n      }\n    }\n  });\n}\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {\n  const { searchParams } = new URL(request.url);\n  const testType = searchParams.get('test');\n\n  if (!testType) {\n    return NextResponse.json({\n      success: true,\n      data: {\n        message: 'AI Service Test Endpoint',\n        availableTests: [\n          'resume-analysis',\n          'career-recommendations',\n          'interview-questions',\n          'health-check',\n          'monitoring'\n        ],\n        usage: {\n          POST: 'Send { \"testType\": \"test-name\", \"data\": {...} }',\n          GET: 'Use ?test=test-name for simple tests'\n        }\n      }\n    });\n  }\n\n  // Simple GET tests\n  switch (testType) {\n    case 'health-check':\n      return await testHealthCheck();\n\n    case 'monitoring':\n      return await testMonitoring();\n\n    case 'resume-analysis':\n      return await testResumeAnalysis({});\n\n    default:\n      const error = new Error('Invalid test type for GET request') as any;\n      error.statusCode = 400;\n      throw error;\n  }\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a0b37a6ea35fa36a6453a315c692b19cea54063a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1yaz24beg9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1yaz24beg9();
var __assign =
/* istanbul ignore next */
(cov_1yaz24beg9().s[0]++,
/* istanbul ignore next */
(cov_1yaz24beg9().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1yaz24beg9().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1yaz24beg9().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[0]++;
  cov_1yaz24beg9().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1yaz24beg9().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1yaz24beg9().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[1]++;
    cov_1yaz24beg9().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1yaz24beg9().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1yaz24beg9().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1yaz24beg9().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1yaz24beg9().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1yaz24beg9().b[2][0]++;
          cov_1yaz24beg9().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1yaz24beg9().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1yaz24beg9().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1yaz24beg9().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1yaz24beg9().s[11]++,
/* istanbul ignore next */
(cov_1yaz24beg9().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1yaz24beg9().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1yaz24beg9().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[3]++;
    cov_1yaz24beg9().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[4]++;
      cov_1yaz24beg9().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1yaz24beg9().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1yaz24beg9().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1yaz24beg9().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[6]++;
      cov_1yaz24beg9().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[7]++;
      cov_1yaz24beg9().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[8]++;
      cov_1yaz24beg9().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1yaz24beg9().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1yaz24beg9().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1yaz24beg9().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1yaz24beg9().s[23]++,
/* istanbul ignore next */
(cov_1yaz24beg9().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1yaz24beg9().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1yaz24beg9().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1yaz24beg9().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1yaz24beg9().f[10]++;
        cov_1yaz24beg9().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1yaz24beg9().b[9][0]++;
          cov_1yaz24beg9().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1yaz24beg9().b[9][1]++;
        }
        cov_1yaz24beg9().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1yaz24beg9().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1yaz24beg9().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1yaz24beg9().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1yaz24beg9().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[11]++;
    cov_1yaz24beg9().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[12]++;
    cov_1yaz24beg9().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[13]++;
      cov_1yaz24beg9().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[14]++;
    cov_1yaz24beg9().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1yaz24beg9().b[12][0]++;
      cov_1yaz24beg9().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1yaz24beg9().b[12][1]++;
    }
    cov_1yaz24beg9().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1yaz24beg9().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1yaz24beg9().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[18][0]++,
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[19][1]++,
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1yaz24beg9().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1yaz24beg9().b[15][0]++;
          cov_1yaz24beg9().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1yaz24beg9().b[15][1]++;
        }
        cov_1yaz24beg9().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1yaz24beg9().b[21][0]++;
          cov_1yaz24beg9().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1yaz24beg9().b[21][1]++;
        }
        cov_1yaz24beg9().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1yaz24beg9().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1yaz24beg9().b[22][1]++;
            cov_1yaz24beg9().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1yaz24beg9().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1yaz24beg9().b[22][2]++;
            cov_1yaz24beg9().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1yaz24beg9().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1yaz24beg9().b[22][3]++;
            cov_1yaz24beg9().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1yaz24beg9().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1yaz24beg9().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1yaz24beg9().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1yaz24beg9().b[22][4]++;
            cov_1yaz24beg9().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1yaz24beg9().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1yaz24beg9().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1yaz24beg9().b[22][5]++;
            cov_1yaz24beg9().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1yaz24beg9().b[23][0]++;
              cov_1yaz24beg9().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1yaz24beg9().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1yaz24beg9().b[23][1]++;
            }
            cov_1yaz24beg9().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1yaz24beg9().b[26][0]++;
              cov_1yaz24beg9().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1yaz24beg9().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1yaz24beg9().b[26][1]++;
            }
            cov_1yaz24beg9().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1yaz24beg9().b[28][0]++;
              cov_1yaz24beg9().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1yaz24beg9().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1yaz24beg9().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1yaz24beg9().b[28][1]++;
            }
            cov_1yaz24beg9().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1yaz24beg9().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1yaz24beg9().b[30][0]++;
              cov_1yaz24beg9().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1yaz24beg9().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1yaz24beg9().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1yaz24beg9().b[30][1]++;
            }
            cov_1yaz24beg9().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1yaz24beg9().b[32][0]++;
              cov_1yaz24beg9().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1yaz24beg9().b[32][1]++;
            }
            cov_1yaz24beg9().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1yaz24beg9().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1yaz24beg9().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1yaz24beg9().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1yaz24beg9().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1yaz24beg9().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1yaz24beg9().b[33][0]++;
      cov_1yaz24beg9().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1yaz24beg9().b[33][1]++;
    }
    cov_1yaz24beg9().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1yaz24beg9().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1yaz24beg9().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1yaz24beg9().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1yaz24beg9().s[79]++;
exports.GET = exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1yaz24beg9().s[80]++, require("next/server"));
var geminiService_1 =
/* istanbul ignore next */
(cov_1yaz24beg9().s[81]++, require("@/lib/services/geminiService"));
var ai_service_monitor_1 =
/* istanbul ignore next */
(cov_1yaz24beg9().s[82]++, require("@/lib/ai-service-monitor"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1yaz24beg9().s[83]++, require("@/lib/unified-api-error-handler"));
/* istanbul ignore next */
cov_1yaz24beg9().s[84]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[15]++;
  cov_1yaz24beg9().s[85]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[16]++;
    var _a, testType, data, _b, error;
    /* istanbul ignore next */
    cov_1yaz24beg9().s[86]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[17]++;
      cov_1yaz24beg9().s[87]++;
      switch (_c.label) {
        case 0:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][0]++;
          cov_1yaz24beg9().s[88]++;
          return [4 /*yield*/, request.json()];
        case 1:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][1]++;
          cov_1yaz24beg9().s[89]++;
          _a = _c.sent(), testType = _a.testType, data = _a.data;
          /* istanbul ignore next */
          cov_1yaz24beg9().s[90]++;
          _b = testType;
          /* istanbul ignore next */
          cov_1yaz24beg9().s[91]++;
          switch (_b) {
            case 'resume-analysis':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[36][0]++;
              cov_1yaz24beg9().s[92]++;
              return [3 /*break*/, 2];
            case 'career-recommendations':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[36][1]++;
              cov_1yaz24beg9().s[93]++;
              return [3 /*break*/, 4];
            case 'interview-questions':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[36][2]++;
              cov_1yaz24beg9().s[94]++;
              return [3 /*break*/, 6];
            case 'health-check':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[36][3]++;
              cov_1yaz24beg9().s[95]++;
              return [3 /*break*/, 8];
            case 'monitoring':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[36][4]++;
              cov_1yaz24beg9().s[96]++;
              return [3 /*break*/, 10];
          }
          /* istanbul ignore next */
          cov_1yaz24beg9().s[97]++;
          return [3 /*break*/, 12];
        case 2:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][2]++;
          cov_1yaz24beg9().s[98]++;
          return [4 /*yield*/, testResumeAnalysis(data)];
        case 3:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][3]++;
          cov_1yaz24beg9().s[99]++;
          return [2 /*return*/, _c.sent()];
        case 4:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][4]++;
          cov_1yaz24beg9().s[100]++;
          return [4 /*yield*/, testCareerRecommendations(data)];
        case 5:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][5]++;
          cov_1yaz24beg9().s[101]++;
          return [2 /*return*/, _c.sent()];
        case 6:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][6]++;
          cov_1yaz24beg9().s[102]++;
          return [4 /*yield*/, testInterviewQuestions(data)];
        case 7:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][7]++;
          cov_1yaz24beg9().s[103]++;
          return [2 /*return*/, _c.sent()];
        case 8:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][8]++;
          cov_1yaz24beg9().s[104]++;
          return [4 /*yield*/, testHealthCheck()];
        case 9:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][9]++;
          cov_1yaz24beg9().s[105]++;
          return [2 /*return*/, _c.sent()];
        case 10:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][10]++;
          cov_1yaz24beg9().s[106]++;
          return [4 /*yield*/, testMonitoring()];
        case 11:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][11]++;
          cov_1yaz24beg9().s[107]++;
          return [2 /*return*/, _c.sent()];
        case 12:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[35][12]++;
          cov_1yaz24beg9().s[108]++;
          error = new Error('Invalid test type');
          /* istanbul ignore next */
          cov_1yaz24beg9().s[109]++;
          error.statusCode = 400;
          /* istanbul ignore next */
          cov_1yaz24beg9().s[110]++;
          throw error;
      }
    });
  });
});
function testResumeAnalysis(data) {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[18]++;
  cov_1yaz24beg9().s[111]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[19]++;
    var resumeText, result;
    /* istanbul ignore next */
    cov_1yaz24beg9().s[112]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[20]++;
      cov_1yaz24beg9().s[113]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[37][0]++;
          cov_1yaz24beg9().s[114]++;
          resumeText =
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[38][0]++, data.resumeText) ||
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[38][1]++, "\nJohn Doe\nSoftware Engineer\nEmail: <EMAIL>\n\nEXPERIENCE:\nSenior Software Engineer at TechCorp (2020-2024)\n- Led development of microservices architecture\n- Implemented CI/CD pipelines\n- Mentored junior developers\n\nSKILLS:\nJavaScript, React, Node.js, Python, AWS\n");
          /* istanbul ignore next */
          cov_1yaz24beg9().s[115]++;
          return [4 /*yield*/, geminiService_1.geminiService.analyzeResume(resumeText, 'test-user-123')];
        case 1:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[37][1]++;
          cov_1yaz24beg9().s[116]++;
          result = _a.sent();
          /* istanbul ignore next */
          cov_1yaz24beg9().s[117]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              testType: 'resume-analysis',
              result: result
            }
          })];
      }
    });
  });
}
function testCareerRecommendations(data) {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[21]++;
  cov_1yaz24beg9().s[118]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[22]++;
    var assessmentData, currentSkills, preferences, result;
    /* istanbul ignore next */
    cov_1yaz24beg9().s[119]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[23]++;
      cov_1yaz24beg9().s[120]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[39][0]++;
          cov_1yaz24beg9().s[121]++;
          assessmentData =
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[40][0]++, data.assessmentData) ||
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[40][1]++, {
            experience: 'senior',
            interests: ['technology']
          });
          /* istanbul ignore next */
          cov_1yaz24beg9().s[122]++;
          currentSkills =
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[41][0]++, data.currentSkills) ||
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[41][1]++, ['JavaScript', 'React', 'Node.js']);
          /* istanbul ignore next */
          cov_1yaz24beg9().s[123]++;
          preferences =
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[42][0]++, data.preferences) ||
          /* istanbul ignore next */
          (cov_1yaz24beg9().b[42][1]++, {
            workStyle: 'collaborative'
          });
          /* istanbul ignore next */
          cov_1yaz24beg9().s[124]++;
          return [4 /*yield*/, geminiService_1.geminiService.generateCareerRecommendations(assessmentData, currentSkills, preferences, 'test-user-123')];
        case 1:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[39][1]++;
          cov_1yaz24beg9().s[125]++;
          result = _a.sent();
          /* istanbul ignore next */
          cov_1yaz24beg9().s[126]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              testType: 'career-recommendations',
              result: result
            }
          })];
      }
    });
  });
}
function testInterviewQuestions(data) {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[24]++;
  cov_1yaz24beg9().s[127]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[25]++;
    var params, result;
    /* istanbul ignore next */
    cov_1yaz24beg9().s[128]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[26]++;
      cov_1yaz24beg9().s[129]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[43][0]++;
          cov_1yaz24beg9().s[130]++;
          params = __assign({
            sessionType: 'TECHNICAL_PRACTICE',
            careerPath: 'Software Engineer',
            experienceLevel: 'SENIOR',
            difficulty: 'INTERMEDIATE',
            count: 3
          }, data);
          /* istanbul ignore next */
          cov_1yaz24beg9().s[131]++;
          return [4 /*yield*/, geminiService_1.geminiService.generateInterviewQuestions(params)];
        case 1:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[43][1]++;
          cov_1yaz24beg9().s[132]++;
          result = _a.sent();
          /* istanbul ignore next */
          cov_1yaz24beg9().s[133]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              testType: 'interview-questions',
              result: result
            }
          })];
      }
    });
  });
}
function testHealthCheck() {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[27]++;
  cov_1yaz24beg9().s[134]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[28]++;
    var healthStatus;
    /* istanbul ignore next */
    cov_1yaz24beg9().s[135]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[29]++;
      cov_1yaz24beg9().s[136]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[44][0]++;
          cov_1yaz24beg9().s[137]++;
          return [4 /*yield*/, geminiService_1.geminiService.healthCheck()];
        case 1:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[44][1]++;
          cov_1yaz24beg9().s[138]++;
          healthStatus = _a.sent();
          /* istanbul ignore next */
          cov_1yaz24beg9().s[139]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              testType: 'health-check',
              result: healthStatus
            }
          })];
      }
    });
  });
}
function testMonitoring() {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[30]++;
  cov_1yaz24beg9().s[140]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[31]++;
    var metrics, analytics, insights;
    /* istanbul ignore next */
    cov_1yaz24beg9().s[141]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[32]++;
      cov_1yaz24beg9().s[142]++;
      metrics = ai_service_monitor_1.aiServiceMonitor.getMetrics();
      /* istanbul ignore next */
      cov_1yaz24beg9().s[143]++;
      analytics = ai_service_monitor_1.aiServiceMonitor.getUsageAnalytics();
      /* istanbul ignore next */
      cov_1yaz24beg9().s[144]++;
      insights = ai_service_monitor_1.aiServiceMonitor.getPerformanceInsights();
      /* istanbul ignore next */
      cov_1yaz24beg9().s[145]++;
      return [2 /*return*/, server_1.NextResponse.json({
        success: true,
        data: {
          testType: 'monitoring',
          result: {
            metrics: metrics,
            analytics: analytics,
            insights: insights
          }
        }
      })];
    });
  });
}
/* istanbul ignore next */
cov_1yaz24beg9().s[146]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1yaz24beg9().f[33]++;
  cov_1yaz24beg9().s[147]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1yaz24beg9().f[34]++;
    var searchParams, testType, _a, error;
    /* istanbul ignore next */
    cov_1yaz24beg9().s[148]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_1yaz24beg9().f[35]++;
      cov_1yaz24beg9().s[149]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][0]++;
          cov_1yaz24beg9().s[150]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_1yaz24beg9().s[151]++;
          testType = searchParams.get('test');
          /* istanbul ignore next */
          cov_1yaz24beg9().s[152]++;
          if (!testType) {
            /* istanbul ignore next */
            cov_1yaz24beg9().b[46][0]++;
            cov_1yaz24beg9().s[153]++;
            return [2 /*return*/, server_1.NextResponse.json({
              success: true,
              data: {
                message: 'AI Service Test Endpoint',
                availableTests: ['resume-analysis', 'career-recommendations', 'interview-questions', 'health-check', 'monitoring'],
                usage: {
                  POST: 'Send { "testType": "test-name", "data": {...} }',
                  GET: 'Use ?test=test-name for simple tests'
                }
              }
            })];
          } else
          /* istanbul ignore next */
          {
            cov_1yaz24beg9().b[46][1]++;
          }
          cov_1yaz24beg9().s[154]++;
          _a = testType;
          /* istanbul ignore next */
          cov_1yaz24beg9().s[155]++;
          switch (_a) {
            case 'health-check':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[47][0]++;
              cov_1yaz24beg9().s[156]++;
              return [3 /*break*/, 1];
            case 'monitoring':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[47][1]++;
              cov_1yaz24beg9().s[157]++;
              return [3 /*break*/, 3];
            case 'resume-analysis':
              /* istanbul ignore next */
              cov_1yaz24beg9().b[47][2]++;
              cov_1yaz24beg9().s[158]++;
              return [3 /*break*/, 5];
          }
          /* istanbul ignore next */
          cov_1yaz24beg9().s[159]++;
          return [3 /*break*/, 7];
        case 1:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][1]++;
          cov_1yaz24beg9().s[160]++;
          return [4 /*yield*/, testHealthCheck()];
        case 2:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][2]++;
          cov_1yaz24beg9().s[161]++;
          return [2 /*return*/, _b.sent()];
        case 3:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][3]++;
          cov_1yaz24beg9().s[162]++;
          return [4 /*yield*/, testMonitoring()];
        case 4:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][4]++;
          cov_1yaz24beg9().s[163]++;
          return [2 /*return*/, _b.sent()];
        case 5:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][5]++;
          cov_1yaz24beg9().s[164]++;
          return [4 /*yield*/, testResumeAnalysis({})];
        case 6:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][6]++;
          cov_1yaz24beg9().s[165]++;
          return [2 /*return*/, _b.sent()];
        case 7:
          /* istanbul ignore next */
          cov_1yaz24beg9().b[45][7]++;
          cov_1yaz24beg9().s[166]++;
          error = new Error('Invalid test type for GET request');
          /* istanbul ignore next */
          cov_1yaz24beg9().s[167]++;
          error.statusCode = 400;
          /* istanbul ignore next */
          cov_1yaz24beg9().s[168]++;
          throw error;
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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