9d61239a0b7247371477bf479d396989
"use strict";

/**
 * Advanced Query Performance Monitoring API
 * Provides access to query performance metrics, alerts, and optimization suggestions
 * Part of Phase 2B: Advanced Query Optimization
 */
/* istanbul ignore next */
function cov_2lm7n2lqx1() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/performance/route.ts";
  var hash = "0c07d948bb86c4a09c78d92fb8b7c0e3cba7f952";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/performance/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 15,
          column: 1
        }
      },
      "1": {
        start: {
          line: 8,
          column: 28
        },
        end: {
          line: 8,
          column: 110
        }
      },
      "2": {
        start: {
          line: 8,
          column: 91
        },
        end: {
          line: 8,
          column: 106
        }
      },
      "3": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 14,
          column: 7
        }
      },
      "4": {
        start: {
          line: 10,
          column: 36
        },
        end: {
          line: 10,
          column: 97
        }
      },
      "5": {
        start: {
          line: 10,
          column: 42
        },
        end: {
          line: 10,
          column: 70
        }
      },
      "6": {
        start: {
          line: 10,
          column: 85
        },
        end: {
          line: 10,
          column: 95
        }
      },
      "7": {
        start: {
          line: 11,
          column: 35
        },
        end: {
          line: 11,
          column: 100
        }
      },
      "8": {
        start: {
          line: 11,
          column: 41
        },
        end: {
          line: 11,
          column: 73
        }
      },
      "9": {
        start: {
          line: 11,
          column: 88
        },
        end: {
          line: 11,
          column: 98
        }
      },
      "10": {
        start: {
          line: 12,
          column: 32
        },
        end: {
          line: 12,
          column: 116
        }
      },
      "11": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 78
        }
      },
      "12": {
        start: {
          line: 16,
          column: 18
        },
        end: {
          line: 42,
          column: 1
        }
      },
      "13": {
        start: {
          line: 17,
          column: 12
        },
        end: {
          line: 17,
          column: 104
        }
      },
      "14": {
        start: {
          line: 17,
          column: 43
        },
        end: {
          line: 17,
          column: 68
        }
      },
      "15": {
        start: {
          line: 17,
          column: 57
        },
        end: {
          line: 17,
          column: 68
        }
      },
      "16": {
        start: {
          line: 17,
          column: 69
        },
        end: {
          line: 17,
          column: 81
        }
      },
      "17": {
        start: {
          line: 17,
          column: 119
        },
        end: {
          line: 17,
          column: 196
        }
      },
      "18": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 160
        }
      },
      "19": {
        start: {
          line: 18,
          column: 141
        },
        end: {
          line: 18,
          column: 153
        }
      },
      "20": {
        start: {
          line: 19,
          column: 23
        },
        end: {
          line: 19,
          column: 68
        }
      },
      "21": {
        start: {
          line: 19,
          column: 45
        },
        end: {
          line: 19,
          column: 65
        }
      },
      "22": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 70
        }
      },
      "23": {
        start: {
          line: 21,
          column: 15
        },
        end: {
          line: 21,
          column: 70
        }
      },
      "24": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 39,
          column: 66
        }
      },
      "25": {
        start: {
          line: 22,
          column: 50
        },
        end: {
          line: 39,
          column: 66
        }
      },
      "26": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 169
        }
      },
      "27": {
        start: {
          line: 23,
          column: 160
        },
        end: {
          line: 23,
          column: 169
        }
      },
      "28": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 24,
          column: 52
        }
      },
      "29": {
        start: {
          line: 24,
          column: 26
        },
        end: {
          line: 24,
          column: 52
        }
      },
      "30": {
        start: {
          line: 25,
          column: 12
        },
        end: {
          line: 37,
          column: 13
        }
      },
      "31": {
        start: {
          line: 26,
          column: 32
        },
        end: {
          line: 26,
          column: 39
        }
      },
      "32": {
        start: {
          line: 26,
          column: 40
        },
        end: {
          line: 26,
          column: 46
        }
      },
      "33": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "34": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 72
        }
      },
      "35": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 34
        }
      },
      "36": {
        start: {
          line: 28,
          column: 35
        },
        end: {
          line: 28,
          column: 45
        }
      },
      "37": {
        start: {
          line: 28,
          column: 46
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "38": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "39": {
        start: {
          line: 29,
          column: 24
        },
        end: {
          line: 29,
          column: 41
        }
      },
      "40": {
        start: {
          line: 29,
          column: 42
        },
        end: {
          line: 29,
          column: 55
        }
      },
      "41": {
        start: {
          line: 29,
          column: 56
        },
        end: {
          line: 29,
          column: 65
        }
      },
      "42": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 128
        }
      },
      "43": {
        start: {
          line: 31,
          column: 110
        },
        end: {
          line: 31,
          column: 116
        }
      },
      "44": {
        start: {
          line: 31,
          column: 117
        },
        end: {
          line: 31,
          column: 126
        }
      },
      "45": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 106
        }
      },
      "46": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 97
        }
      },
      "47": {
        start: {
          line: 32,
          column: 98
        },
        end: {
          line: 32,
          column: 104
        }
      },
      "48": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 89
        }
      },
      "49": {
        start: {
          line: 33,
          column: 57
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "50": {
        start: {
          line: 33,
          column: 73
        },
        end: {
          line: 33,
          column: 80
        }
      },
      "51": {
        start: {
          line: 33,
          column: 81
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "52": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 87
        }
      },
      "53": {
        start: {
          line: 34,
          column: 47
        },
        end: {
          line: 34,
          column: 62
        }
      },
      "54": {
        start: {
          line: 34,
          column: 63
        },
        end: {
          line: 34,
          column: 78
        }
      },
      "55": {
        start: {
          line: 34,
          column: 79
        },
        end: {
          line: 34,
          column: 85
        }
      },
      "56": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 42
        }
      },
      "57": {
        start: {
          line: 35,
          column: 30
        },
        end: {
          line: 35,
          column: 42
        }
      },
      "58": {
        start: {
          line: 36,
          column: 20
        },
        end: {
          line: 36,
          column: 33
        }
      },
      "59": {
        start: {
          line: 36,
          column: 34
        },
        end: {
          line: 36,
          column: 43
        }
      },
      "60": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 39
        }
      },
      "61": {
        start: {
          line: 39,
          column: 22
        },
        end: {
          line: 39,
          column: 34
        }
      },
      "62": {
        start: {
          line: 39,
          column: 35
        },
        end: {
          line: 39,
          column: 41
        }
      },
      "63": {
        start: {
          line: 39,
          column: 54
        },
        end: {
          line: 39,
          column: 64
        }
      },
      "64": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 40,
          column: 35
        }
      },
      "65": {
        start: {
          line: 40,
          column: 23
        },
        end: {
          line: 40,
          column: 35
        }
      },
      "66": {
        start: {
          line: 40,
          column: 36
        },
        end: {
          line: 40,
          column: 89
        }
      },
      "67": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 62
        }
      },
      "68": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 36
        }
      },
      "69": {
        start: {
          line: 45,
          column: 15
        },
        end: {
          line: 45,
          column: 37
        }
      },
      "70": {
        start: {
          line: 46,
          column: 18
        },
        end: {
          line: 46,
          column: 38
        }
      },
      "71": {
        start: {
          line: 47,
          column: 13
        },
        end: {
          line: 47,
          column: 34
        }
      },
      "72": {
        start: {
          line: 48,
          column: 34
        },
        end: {
          line: 48,
          column: 76
        }
      },
      "73": {
        start: {
          line: 49,
          column: 43
        },
        end: {
          line: 49,
          column: 103
        }
      },
      "74": {
        start: {
          line: 50,
          column: 12
        },
        end: {
          line: 50,
          column: 26
        }
      },
      "75": {
        start: {
          line: 52,
          column: 29
        },
        end: {
          line: 59,
          column: 2
        }
      },
      "76": {
        start: {
          line: 55,
          column: 70
        },
        end: {
          line: 55,
          column: 109
        }
      },
      "77": {
        start: {
          line: 56,
          column: 68
        },
        end: {
          line: 56,
          column: 107
        }
      },
      "78": {
        start: {
          line: 60,
          column: 24
        },
        end: {
          line: 63,
          column: 2
        }
      },
      "79": {
        start: {
          line: 65,
          column: 0
        },
        end: {
          line: 95,
          column: 7
        }
      },
      "80": {
        start: {
          line: 65,
          column: 93
        },
        end: {
          line: 95,
          column: 3
        }
      },
      "81": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 94,
          column: 7
        }
      },
      "82": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 93,
          column: 9
        }
      },
      "83": {
        start: {
          line: 70,
          column: 20
        },
        end: {
          line: 70,
          column: 96
        }
      },
      "84": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 72,
          column: 36
        }
      },
      "85": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 75,
          column: 17
        }
      },
      "86": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 76
        }
      },
      "87": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 76,
          column: 65
        }
      },
      "88": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 98
        }
      },
      "89": {
        start: {
          line: 78,
          column: 16
        },
        end: {
          line: 91,
          column: 17
        }
      },
      "90": {
        start: {
          line: 80,
          column: 24
        },
        end: {
          line: 80,
          column: 76
        }
      },
      "91": {
        start: {
          line: 82,
          column: 24
        },
        end: {
          line: 82,
          column: 69
        }
      },
      "92": {
        start: {
          line: 84,
          column: 24
        },
        end: {
          line: 84,
          column: 75
        }
      },
      "93": {
        start: {
          line: 86,
          column: 24
        },
        end: {
          line: 86,
          column: 80
        }
      },
      "94": {
        start: {
          line: 88,
          column: 24
        },
        end: {
          line: 88,
          column: 80
        }
      },
      "95": {
        start: {
          line: 90,
          column: 24
        },
        end: {
          line: 90,
          column: 58
        }
      },
      "96": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 38
        }
      },
      "97": {
        start: {
          line: 97,
          column: 0
        },
        end: {
          line: 125,
          column: 7
        }
      },
      "98": {
        start: {
          line: 97,
          column: 94
        },
        end: {
          line: 125,
          column: 3
        }
      },
      "99": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 124,
          column: 7
        }
      },
      "100": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 123,
          column: 9
        }
      },
      "101": {
        start: {
          line: 102,
          column: 20
        },
        end: {
          line: 102,
          column: 96
        }
      },
      "102": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 104,
          column: 36
        }
      },
      "103": {
        start: {
          line: 105,
          column: 16
        },
        end: {
          line: 107,
          column: 17
        }
      },
      "104": {
        start: {
          line: 106,
          column: 20
        },
        end: {
          line: 106,
          column: 76
        }
      },
      "105": {
        start: {
          line: 108,
          column: 16
        },
        end: {
          line: 108,
          column: 53
        }
      },
      "106": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 110,
          column: 33
        }
      },
      "107": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 111,
          column: 55
        }
      },
      "108": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 121,
          column: 17
        }
      },
      "109": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 136
        }
      },
      "110": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 118,
          column: 32
        }
      },
      "111": {
        start: {
          line: 120,
          column: 24
        },
        end: {
          line: 120,
          column: 58
        }
      },
      "112": {
        start: {
          line: 122,
          column: 16
        },
        end: {
          line: 122,
          column: 38
        }
      },
      "113": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 163,
          column: 7
        }
      },
      "114": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 162,
          column: 11
        }
      },
      "115": {
        start: {
          line: 133,
          column: 12
        },
        end: {
          line: 160,
          column: 13
        }
      },
      "116": {
        start: {
          line: 134,
          column: 16
        },
        end: {
          line: 134,
          column: 177
        }
      },
      "117": {
        start: {
          line: 135,
          column: 16
        },
        end: {
          line: 150,
          column: 24
        }
      },
      "118": {
        start: {
          line: 143,
          column: 73
        },
        end: {
          line: 143,
          column: 102
        }
      },
      "119": {
        start: {
          line: 146,
          column: 68
        },
        end: {
          line: 146,
          column: 86
        }
      },
      "120": {
        start: {
          line: 153,
          column: 16
        },
        end: {
          line: 153,
          column: 55
        }
      },
      "121": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 100
        }
      },
      "122": {
        start: {
          line: 155,
          column: 16
        },
        end: {
          line: 155,
          column: 140
        }
      },
      "123": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 159,
          column: 24
        }
      },
      "124": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 161,
          column: 34
        }
      },
      "125": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 182,
          column: 7
        }
      },
      "126": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 181,
          column: 11
        }
      },
      "127": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 108
        }
      },
      "128": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 180,
          column: 20
        }
      },
      "129": {
        start: {
          line: 176,
          column: 73
        },
        end: {
          line: 176,
          column: 106
        }
      },
      "130": {
        start: {
          line: 177,
          column: 72
        },
        end: {
          line: 177,
          column: 104
        }
      },
      "131": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 196,
          column: 7
        }
      },
      "132": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 195,
          column: 11
        }
      },
      "133": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 188,
          column: 51
        }
      },
      "134": {
        start: {
          line: 189,
          column: 12
        },
        end: {
          line: 189,
          column: 100
        }
      },
      "135": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 187
        }
      },
      "136": {
        start: {
          line: 191,
          column: 12
        },
        end: {
          line: 194,
          column: 20
        }
      },
      "137": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 214,
          column: 7
        }
      },
      "138": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 213,
          column: 11
        }
      },
      "139": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 204,
          column: 13
        }
      },
      "140": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 203,
          column: 161
        }
      },
      "141": {
        start: {
          line: 205,
          column: 12
        },
        end: {
          line: 205,
          column: 140
        }
      },
      "142": {
        start: {
          line: 206,
          column: 12
        },
        end: {
          line: 212,
          column: 20
        }
      },
      "143": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 233,
          column: 7
        }
      },
      "144": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 232,
          column: 11
        }
      },
      "145": {
        start: {
          line: 220,
          column: 12
        },
        end: {
          line: 222,
          column: 13
        }
      },
      "146": {
        start: {
          line: 221,
          column: 16
        },
        end: {
          line: 221,
          column: 170
        }
      },
      "147": {
        start: {
          line: 223,
          column: 12
        },
        end: {
          line: 223,
          column: 151
        }
      },
      "148": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 231,
          column: 20
        }
      },
      "149": {
        start: {
          line: 240,
          column: 22
        },
        end: {
          line: 243,
          column: 5
        }
      },
      "150": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 244,
          column: 53
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 7,
            column: 45
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 7
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 8,
            column: 13
          },
          end: {
            line: 8,
            column: 18
          }
        },
        loc: {
          start: {
            line: 8,
            column: 26
          },
          end: {
            line: 8,
            column: 112
          }
        },
        line: 8
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 8,
            column: 70
          },
          end: {
            line: 8,
            column: 71
          }
        },
        loc: {
          start: {
            line: 8,
            column: 89
          },
          end: {
            line: 8,
            column: 108
          }
        },
        line: 8
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 9,
            column: 36
          },
          end: {
            line: 9,
            column: 37
          }
        },
        loc: {
          start: {
            line: 9,
            column: 63
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 9
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 26
          }
        },
        loc: {
          start: {
            line: 10,
            column: 34
          },
          end: {
            line: 10,
            column: 99
          }
        },
        line: 10
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 25
          }
        },
        loc: {
          start: {
            line: 11,
            column: 33
          },
          end: {
            line: 11,
            column: 102
          }
        },
        line: 11
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 12,
            column: 17
          },
          end: {
            line: 12,
            column: 21
          }
        },
        loc: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 118
          }
        },
        line: 12
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 16,
            column: 48
          },
          end: {
            line: 16,
            column: 49
          }
        },
        loc: {
          start: {
            line: 16,
            column: 73
          },
          end: {
            line: 42,
            column: 1
          }
        },
        line: 16
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 17,
            column: 30
          },
          end: {
            line: 17,
            column: 31
          }
        },
        loc: {
          start: {
            line: 17,
            column: 41
          },
          end: {
            line: 17,
            column: 83
          }
        },
        line: 17
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 18,
            column: 128
          },
          end: {
            line: 18,
            column: 129
          }
        },
        loc: {
          start: {
            line: 18,
            column: 139
          },
          end: {
            line: 18,
            column: 155
          }
        },
        line: 18
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 21
          },
          end: {
            line: 19,
            column: 70
          }
        },
        line: 19
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 19,
            column: 31
          }
        },
        loc: {
          start: {
            line: 19,
            column: 43
          },
          end: {
            line: 19,
            column: 67
          }
        },
        line: 19
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 20,
            column: 13
          },
          end: {
            line: 20,
            column: 17
          }
        },
        loc: {
          start: {
            line: 20,
            column: 22
          },
          end: {
            line: 41,
            column: 5
          }
        },
        line: 20
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 55,
            column: 53
          },
          end: {
            line: 55,
            column: 54
          }
        },
        loc: {
          start: {
            line: 55,
            column: 68
          },
          end: {
            line: 55,
            column: 111
          }
        },
        line: 55
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 56,
            column: 51
          },
          end: {
            line: 56,
            column: 52
          }
        },
        loc: {
          start: {
            line: 56,
            column: 66
          },
          end: {
            line: 56,
            column: 109
          }
        },
        line: 56
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 65,
            column: 72
          },
          end: {
            line: 65,
            column: 73
          }
        },
        loc: {
          start: {
            line: 65,
            column: 91
          },
          end: {
            line: 95,
            column: 5
          }
        },
        line: 65
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 65,
            column: 134
          },
          end: {
            line: 65,
            column: 135
          }
        },
        loc: {
          start: {
            line: 65,
            column: 146
          },
          end: {
            line: 95,
            column: 1
          }
        },
        line: 65
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 68,
            column: 29
          },
          end: {
            line: 68,
            column: 30
          }
        },
        loc: {
          start: {
            line: 68,
            column: 43
          },
          end: {
            line: 94,
            column: 5
          }
        },
        line: 68
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 97,
            column: 73
          },
          end: {
            line: 97,
            column: 74
          }
        },
        loc: {
          start: {
            line: 97,
            column: 92
          },
          end: {
            line: 125,
            column: 5
          }
        },
        line: 97
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 97,
            column: 135
          },
          end: {
            line: 97,
            column: 136
          }
        },
        loc: {
          start: {
            line: 97,
            column: 147
          },
          end: {
            line: 125,
            column: 1
          }
        },
        line: 97
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 100,
            column: 29
          },
          end: {
            line: 100,
            column: 30
          }
        },
        loc: {
          start: {
            line: 100,
            column: 43
          },
          end: {
            line: 124,
            column: 5
          }
        },
        line: 100
      },
      "21": {
        name: "handleMetricsRequest",
        decl: {
          start: {
            line: 129,
            column: 9
          },
          end: {
            line: 129,
            column: 29
          }
        },
        loc: {
          start: {
            line: 129,
            column: 38
          },
          end: {
            line: 164,
            column: 1
          }
        },
        line: 129
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 130,
            column: 44
          },
          end: {
            line: 130,
            column: 45
          }
        },
        loc: {
          start: {
            line: 130,
            column: 56
          },
          end: {
            line: 163,
            column: 5
          }
        },
        line: 130
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 132,
            column: 33
          },
          end: {
            line: 132,
            column: 34
          }
        },
        loc: {
          start: {
            line: 132,
            column: 47
          },
          end: {
            line: 162,
            column: 9
          }
        },
        line: 132
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 143,
            column: 53
          },
          end: {
            line: 143,
            column: 54
          }
        },
        loc: {
          start: {
            line: 143,
            column: 71
          },
          end: {
            line: 143,
            column: 104
          }
        },
        line: 143
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 146,
            column: 53
          },
          end: {
            line: 146,
            column: 54
          }
        },
        loc: {
          start: {
            line: 146,
            column: 66
          },
          end: {
            line: 146,
            column: 88
          }
        },
        line: 146
      },
      "26": {
        name: "handleAlertsRequest",
        decl: {
          start: {
            line: 165,
            column: 9
          },
          end: {
            line: 165,
            column: 28
          }
        },
        loc: {
          start: {
            line: 165,
            column: 31
          },
          end: {
            line: 183,
            column: 1
          }
        },
        line: 165
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 166,
            column: 44
          },
          end: {
            line: 166,
            column: 45
          }
        },
        loc: {
          start: {
            line: 166,
            column: 56
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 166
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 168,
            column: 33
          },
          end: {
            line: 168,
            column: 34
          }
        },
        loc: {
          start: {
            line: 168,
            column: 47
          },
          end: {
            line: 181,
            column: 9
          }
        },
        line: 168
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 176,
            column: 58
          },
          end: {
            line: 176,
            column: 59
          }
        },
        loc: {
          start: {
            line: 176,
            column: 71
          },
          end: {
            line: 176,
            column: 108
          }
        },
        line: 176
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 177,
            column: 57
          },
          end: {
            line: 177,
            column: 58
          }
        },
        loc: {
          start: {
            line: 177,
            column: 70
          },
          end: {
            line: 177,
            column: 106
          }
        },
        line: 177
      },
      "31": {
        name: "handleReportRequest",
        decl: {
          start: {
            line: 184,
            column: 9
          },
          end: {
            line: 184,
            column: 28
          }
        },
        loc: {
          start: {
            line: 184,
            column: 37
          },
          end: {
            line: 197,
            column: 1
          }
        },
        line: 184
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 185,
            column: 44
          },
          end: {
            line: 185,
            column: 45
          }
        },
        loc: {
          start: {
            line: 185,
            column: 56
          },
          end: {
            line: 196,
            column: 5
          }
        },
        line: 185
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 187,
            column: 33
          },
          end: {
            line: 187,
            column: 34
          }
        },
        loc: {
          start: {
            line: 187,
            column: 47
          },
          end: {
            line: 195,
            column: 9
          }
        },
        line: 187
      },
      "34": {
        name: "handleSuggestionsRequest",
        decl: {
          start: {
            line: 198,
            column: 9
          },
          end: {
            line: 198,
            column: 33
          }
        },
        loc: {
          start: {
            line: 198,
            column: 42
          },
          end: {
            line: 215,
            column: 1
          }
        },
        line: 198
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 199,
            column: 44
          },
          end: {
            line: 199,
            column: 45
          }
        },
        loc: {
          start: {
            line: 199,
            column: 56
          },
          end: {
            line: 214,
            column: 5
          }
        },
        line: 199
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 201,
            column: 33
          },
          end: {
            line: 201,
            column: 34
          }
        },
        loc: {
          start: {
            line: 201,
            column: 47
          },
          end: {
            line: 213,
            column: 9
          }
        },
        line: 201
      },
      "37": {
        name: "handleRegressionsRequest",
        decl: {
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 33
          }
        },
        loc: {
          start: {
            line: 216,
            column: 42
          },
          end: {
            line: 234,
            column: 1
          }
        },
        line: 216
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 217,
            column: 44
          },
          end: {
            line: 217,
            column: 45
          }
        },
        loc: {
          start: {
            line: 217,
            column: 56
          },
          end: {
            line: 233,
            column: 5
          }
        },
        line: 217
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 219,
            column: 33
          },
          end: {
            line: 219,
            column: 34
          }
        },
        loc: {
          start: {
            line: 219,
            column: 47
          },
          end: {
            line: 232,
            column: 9
          }
        },
        line: 219
      },
      "40": {
        name: "isAdminUser",
        decl: {
          start: {
            line: 238,
            column: 9
          },
          end: {
            line: 238,
            column: 20
          }
        },
        loc: {
          start: {
            line: 238,
            column: 28
          },
          end: {
            line: 245,
            column: 1
          }
        },
        line: 238
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 7,
            column: 16
          },
          end: {
            line: 15,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        }, {
          start: {
            line: 7,
            column: 25
          },
          end: {
            line: 7,
            column: 39
          }
        }, {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 15,
            column: 1
          }
        }],
        line: 7
      },
      "1": {
        loc: {
          start: {
            line: 8,
            column: 35
          },
          end: {
            line: 8,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 8,
            column: 56
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 64
          },
          end: {
            line: 8,
            column: 109
          }
        }],
        line: 8
      },
      "2": {
        loc: {
          start: {
            line: 9,
            column: 16
          },
          end: {
            line: 9,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 9,
            column: 16
          },
          end: {
            line: 9,
            column: 17
          }
        }, {
          start: {
            line: 9,
            column: 22
          },
          end: {
            line: 9,
            column: 33
          }
        }],
        line: 9
      },
      "3": {
        loc: {
          start: {
            line: 12,
            column: 32
          },
          end: {
            line: 12,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 46
          },
          end: {
            line: 12,
            column: 67
          }
        }, {
          start: {
            line: 12,
            column: 70
          },
          end: {
            line: 12,
            column: 115
          }
        }],
        line: 12
      },
      "4": {
        loc: {
          start: {
            line: 13,
            column: 51
          },
          end: {
            line: 13,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 51
          },
          end: {
            line: 13,
            column: 61
          }
        }, {
          start: {
            line: 13,
            column: 65
          },
          end: {
            line: 13,
            column: 67
          }
        }],
        line: 13
      },
      "5": {
        loc: {
          start: {
            line: 16,
            column: 18
          },
          end: {
            line: 42,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 16,
            column: 19
          },
          end: {
            line: 16,
            column: 23
          }
        }, {
          start: {
            line: 16,
            column: 27
          },
          end: {
            line: 16,
            column: 43
          }
        }, {
          start: {
            line: 16,
            column: 48
          },
          end: {
            line: 42,
            column: 1
          }
        }],
        line: 16
      },
      "6": {
        loc: {
          start: {
            line: 17,
            column: 43
          },
          end: {
            line: 17,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 43
          },
          end: {
            line: 17,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "7": {
        loc: {
          start: {
            line: 17,
            column: 134
          },
          end: {
            line: 17,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 17,
            column: 167
          },
          end: {
            line: 17,
            column: 175
          }
        }, {
          start: {
            line: 17,
            column: 178
          },
          end: {
            line: 17,
            column: 184
          }
        }],
        line: 17
      },
      "8": {
        loc: {
          start: {
            line: 18,
            column: 74
          },
          end: {
            line: 18,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 74
          },
          end: {
            line: 18,
            column: 102
          }
        }, {
          start: {
            line: 18,
            column: 107
          },
          end: {
            line: 18,
            column: 155
          }
        }],
        line: 18
      },
      "9": {
        loc: {
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 21
      },
      "10": {
        loc: {
          start: {
            line: 22,
            column: 15
          },
          end: {
            line: 22,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 15
          },
          end: {
            line: 22,
            column: 16
          }
        }, {
          start: {
            line: 22,
            column: 21
          },
          end: {
            line: 22,
            column: 44
          }
        }],
        line: 22
      },
      "11": {
        loc: {
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 22,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 22,
            column: 33
          }
        }, {
          start: {
            line: 22,
            column: 38
          },
          end: {
            line: 22,
            column: 43
          }
        }],
        line: 22
      },
      "12": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "13": {
        loc: {
          start: {
            line: 23,
            column: 23
          },
          end: {
            line: 23,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 23
          },
          end: {
            line: 23,
            column: 24
          }
        }, {
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 125
          }
        }, {
          start: {
            line: 23,
            column: 130
          },
          end: {
            line: 23,
            column: 158
          }
        }],
        line: 23
      },
      "14": {
        loc: {
          start: {
            line: 23,
            column: 33
          },
          end: {
            line: 23,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 45
          },
          end: {
            line: 23,
            column: 56
          }
        }, {
          start: {
            line: 23,
            column: 59
          },
          end: {
            line: 23,
            column: 125
          }
        }],
        line: 23
      },
      "15": {
        loc: {
          start: {
            line: 23,
            column: 59
          },
          end: {
            line: 23,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 67
          },
          end: {
            line: 23,
            column: 116
          }
        }, {
          start: {
            line: 23,
            column: 119
          },
          end: {
            line: 23,
            column: 125
          }
        }],
        line: 23
      },
      "16": {
        loc: {
          start: {
            line: 23,
            column: 67
          },
          end: {
            line: 23,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 67
          },
          end: {
            line: 23,
            column: 77
          }
        }, {
          start: {
            line: 23,
            column: 82
          },
          end: {
            line: 23,
            column: 115
          }
        }],
        line: 23
      },
      "17": {
        loc: {
          start: {
            line: 23,
            column: 82
          },
          end: {
            line: 23,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 83
          },
          end: {
            line: 23,
            column: 98
          }
        }, {
          start: {
            line: 23,
            column: 103
          },
          end: {
            line: 23,
            column: 112
          }
        }],
        line: 23
      },
      "18": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 24,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 24,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "19": {
        loc: {
          start: {
            line: 25,
            column: 12
          },
          end: {
            line: 37,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 23
          }
        }, {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 46
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 72
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 29,
            column: 65
          }
        }, {
          start: {
            line: 30,
            column: 16
          },
          end: {
            line: 36,
            column: 43
          }
        }],
        line: 25
      },
      "20": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "21": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 74
          }
        }, {
          start: {
            line: 31,
            column: 79
          },
          end: {
            line: 31,
            column: 90
          }
        }, {
          start: {
            line: 31,
            column: 94
          },
          end: {
            line: 31,
            column: 105
          }
        }],
        line: 31
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 42
          },
          end: {
            line: 31,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 42
          },
          end: {
            line: 31,
            column: 54
          }
        }, {
          start: {
            line: 31,
            column: 58
          },
          end: {
            line: 31,
            column: 73
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "24": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 40
          },
          end: {
            line: 32,
            column: 42
          }
        }, {
          start: {
            line: 32,
            column: 47
          },
          end: {
            line: 32,
            column: 59
          }
        }, {
          start: {
            line: 32,
            column: 63
          },
          end: {
            line: 32,
            column: 75
          }
        }],
        line: 32
      },
      "25": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "26": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 35
          }
        }, {
          start: {
            line: 33,
            column: 39
          },
          end: {
            line: 33,
            column: 53
          }
        }],
        line: 33
      },
      "27": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "28": {
        loc: {
          start: {
            line: 34,
            column: 24
          },
          end: {
            line: 34,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 24
          },
          end: {
            line: 34,
            column: 25
          }
        }, {
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 43
          }
        }],
        line: 34
      },
      "29": {
        loc: {
          start: {
            line: 35,
            column: 20
          },
          end: {
            line: 35,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 20
          },
          end: {
            line: 35,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 52
          },
          end: {
            line: 40,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 40,
            column: 60
          },
          end: {
            line: 40,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 68
          },
          end: {
            line: 40,
            column: 74
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 55,
            column: 77
          },
          end: {
            line: 55,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 83
          },
          end: {
            line: 55,
            column: 96
          }
        }, {
          start: {
            line: 55,
            column: 99
          },
          end: {
            line: 55,
            column: 108
          }
        }],
        line: 55
      },
      "33": {
        loc: {
          start: {
            line: 56,
            column: 75
          },
          end: {
            line: 56,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 81
          },
          end: {
            line: 56,
            column: 94
          }
        }, {
          start: {
            line: 56,
            column: 97
          },
          end: {
            line: 56,
            column: 106
          }
        }],
        line: 56
      },
      "34": {
        loc: {
          start: {
            line: 69,
            column: 8
          },
          end: {
            line: 93,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 70,
            column: 96
          }
        }, {
          start: {
            line: 71,
            column: 12
          },
          end: {
            line: 92,
            column: 38
          }
        }],
        line: 69
      },
      "35": {
        loc: {
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 75,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 75,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 73
      },
      "36": {
        loc: {
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 73,
            column: 174
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 73,
            column: 138
          }
        }, {
          start: {
            line: 73,
            column: 142
          },
          end: {
            line: 73,
            column: 174
          }
        }],
        line: 73
      },
      "37": {
        loc: {
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 137
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 120
          },
          end: {
            line: 73,
            column: 126
          }
        }, {
          start: {
            line: 73,
            column: 129
          },
          end: {
            line: 73,
            column: 137
          }
        }],
        line: 73
      },
      "38": {
        loc: {
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 100
          }
        }, {
          start: {
            line: 73,
            column: 104
          },
          end: {
            line: 73,
            column: 117
          }
        }],
        line: 73
      },
      "39": {
        loc: {
          start: {
            line: 73,
            column: 28
          },
          end: {
            line: 73,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 69
          },
          end: {
            line: 73,
            column: 75
          }
        }, {
          start: {
            line: 73,
            column: 78
          },
          end: {
            line: 73,
            column: 90
          }
        }],
        line: 73
      },
      "40": {
        loc: {
          start: {
            line: 73,
            column: 28
          },
          end: {
            line: 73,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 28
          },
          end: {
            line: 73,
            column: 44
          }
        }, {
          start: {
            line: 73,
            column: 48
          },
          end: {
            line: 73,
            column: 66
          }
        }],
        line: 73
      },
      "41": {
        loc: {
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 91,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 79,
            column: 20
          },
          end: {
            line: 80,
            column: 76
          }
        }, {
          start: {
            line: 81,
            column: 20
          },
          end: {
            line: 82,
            column: 69
          }
        }, {
          start: {
            line: 83,
            column: 20
          },
          end: {
            line: 84,
            column: 75
          }
        }, {
          start: {
            line: 85,
            column: 20
          },
          end: {
            line: 86,
            column: 80
          }
        }, {
          start: {
            line: 87,
            column: 20
          },
          end: {
            line: 88,
            column: 80
          }
        }, {
          start: {
            line: 89,
            column: 20
          },
          end: {
            line: 90,
            column: 58
          }
        }],
        line: 78
      },
      "42": {
        loc: {
          start: {
            line: 101,
            column: 8
          },
          end: {
            line: 123,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 102,
            column: 96
          }
        }, {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 108,
            column: 53
          }
        }, {
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 122,
            column: 38
          }
        }],
        line: 101
      },
      "43": {
        loc: {
          start: {
            line: 105,
            column: 16
          },
          end: {
            line: 107,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 16
          },
          end: {
            line: 107,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "44": {
        loc: {
          start: {
            line: 105,
            column: 20
          },
          end: {
            line: 105,
            column: 174
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 20
          },
          end: {
            line: 105,
            column: 138
          }
        }, {
          start: {
            line: 105,
            column: 142
          },
          end: {
            line: 105,
            column: 174
          }
        }],
        line: 105
      },
      "45": {
        loc: {
          start: {
            line: 105,
            column: 22
          },
          end: {
            line: 105,
            column: 137
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 105,
            column: 120
          },
          end: {
            line: 105,
            column: 126
          }
        }, {
          start: {
            line: 105,
            column: 129
          },
          end: {
            line: 105,
            column: 137
          }
        }],
        line: 105
      },
      "46": {
        loc: {
          start: {
            line: 105,
            column: 22
          },
          end: {
            line: 105,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 22
          },
          end: {
            line: 105,
            column: 100
          }
        }, {
          start: {
            line: 105,
            column: 104
          },
          end: {
            line: 105,
            column: 117
          }
        }],
        line: 105
      },
      "47": {
        loc: {
          start: {
            line: 105,
            column: 28
          },
          end: {
            line: 105,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 105,
            column: 69
          },
          end: {
            line: 105,
            column: 75
          }
        }, {
          start: {
            line: 105,
            column: 78
          },
          end: {
            line: 105,
            column: 90
          }
        }],
        line: 105
      },
      "48": {
        loc: {
          start: {
            line: 105,
            column: 28
          },
          end: {
            line: 105,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 28
          },
          end: {
            line: 105,
            column: 44
          }
        }, {
          start: {
            line: 105,
            column: 48
          },
          end: {
            line: 105,
            column: 66
          }
        }],
        line: 105
      },
      "49": {
        loc: {
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 121,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 118,
            column: 32
          }
        }, {
          start: {
            line: 119,
            column: 20
          },
          end: {
            line: 120,
            column: 58
          }
        }],
        line: 112
      },
      "50": {
        loc: {
          start: {
            line: 117,
            column: 41
          },
          end: {
            line: 117,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 117,
            column: 51
          },
          end: {
            line: 117,
            column: 71
          }
        }, {
          start: {
            line: 117,
            column: 74
          },
          end: {
            line: 117,
            column: 91
          }
        }],
        line: 117
      },
      "51": {
        loc: {
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 160,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 160,
            column: 13
          }
        }, {
          start: {
            line: 152,
            column: 17
          },
          end: {
            line: 160,
            column: 13
          }
        }],
        line: 133
      },
      "52": {
        loc: {
          start: {
            line: 142,
            column: 54
          },
          end: {
            line: 144,
            column: 39
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 143,
            column: 38
          },
          end: {
            line: 143,
            column: 125
          }
        }, {
          start: {
            line: 144,
            column: 38
          },
          end: {
            line: 144,
            column: 39
          }
        }],
        line: 142
      },
      "53": {
        loc: {
          start: {
            line: 145,
            column: 46
          },
          end: {
            line: 147,
            column: 39
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 146,
            column: 38
          },
          end: {
            line: 146,
            column: 113
          }
        }, {
          start: {
            line: 147,
            column: 38
          },
          end: {
            line: 147,
            column: 39
          }
        }],
        line: 145
      },
      "54": {
        loc: {
          start: {
            line: 153,
            column: 26
          },
          end: {
            line: 153,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 26
          },
          end: {
            line: 153,
            column: 40
          }
        }, {
          start: {
            line: 153,
            column: 44
          },
          end: {
            line: 153,
            column: 54
          }
        }],
        line: 153
      },
      "55": {
        loc: {
          start: {
            line: 154,
            column: 28
          },
          end: {
            line: 154,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 28
          },
          end: {
            line: 154,
            column: 44
          }
        }, {
          start: {
            line: 154,
            column: 48
          },
          end: {
            line: 154,
            column: 99
          }
        }],
        line: 154
      },
      "56": {
        loc: {
          start: {
            line: 188,
            column: 22
          },
          end: {
            line: 188,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 22
          },
          end: {
            line: 188,
            column: 36
          }
        }, {
          start: {
            line: 188,
            column: 40
          },
          end: {
            line: 188,
            column: 50
          }
        }],
        line: 188
      },
      "57": {
        loc: {
          start: {
            line: 189,
            column: 24
          },
          end: {
            line: 189,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 189,
            column: 24
          },
          end: {
            line: 189,
            column: 40
          }
        }, {
          start: {
            line: 189,
            column: 44
          },
          end: {
            line: 189,
            column: 99
          }
        }],
        line: 189
      },
      "58": {
        loc: {
          start: {
            line: 190,
            column: 136
          },
          end: {
            line: 190,
            column: 185
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 190,
            column: 155
          },
          end: {
            line: 190,
            column: 173
          }
        }, {
          start: {
            line: 190,
            column: 176
          },
          end: {
            line: 190,
            column: 185
          }
        }],
        line: 190
      },
      "59": {
        loc: {
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 204,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 204,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "60": {
        loc: {
          start: {
            line: 220,
            column: 12
          },
          end: {
            line: 222,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 12
          },
          end: {
            line: 222,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0, 0, 0, 0, 0],
      "42": [0, 0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/performance/route.ts",
      mappings: ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,6EAA2E;AAC3E,wGAAoG;AACpG,2BAAwB;AAExB,qBAAqB;AACrB,IAAM,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;IAChG,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,EAA/B,CAA+B,CAAC;IAClF,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,EAA/B,CAA+B,CAAC;IAChF,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;IACtD,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;CAC1D,CAAC,CAAC;AAEH,IAAM,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACjC,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;IAC/B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;CACpB,CAAC,CAAC;AAEH,kCAAkC;AACrB,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;;;;oBAErD,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;gBAC1D,CAAC;gBAEO,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,MAAM,GAAG,sBAAsB,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAExF,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtB,KAAK,SAAS;wBACZ,sBAAO,oBAAoB,CAAC,MAAM,CAAC,EAAC;oBAEtC,KAAK,QAAQ;wBACX,sBAAO,mBAAmB,EAAE,EAAC;oBAE/B,KAAK,QAAQ;wBACX,sBAAO,mBAAmB,CAAC,MAAM,CAAC,EAAC;oBAErC,KAAK,aAAa;wBAChB,sBAAO,wBAAwB,CAAC,MAAM,CAAC,EAAC;oBAE1C,KAAK,aAAa;wBAChB,sBAAO,wBAAwB,CAAC,MAAM,CAAC,EAAC;oBAE1C;wBACE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBACtC,CAAC;;;;KACF,CAAC,CAAC;AAEH,oDAAoD;AACvC,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;;;;oBAEtD,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;gBAC1D,CAAC;gBAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAA3B,IAAI,GAAG,SAAoB;gBAC3B,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAE7C,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtB,KAAK,aAAa;wBACV,OAAO,GAAG,oEAA+B,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;wBACjF,sBAAO,qBAAY,CAAC,IAAI,CAAC;gCACvB,OAAO,SAAA;gCACP,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,iBAAiB;6BAC5D,CAAC,EAAC;oBAEL;wBACE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBACtC,CAAC;;;;KACF,CAAC,CAAC;AAEH;;GAEG;AACH,SAAe,oBAAoB,CAAC,MAAW;mCAAG,OAAO;;;YACvD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,GAAG,oEAA+B,CAAC,eAAe,CAC7D,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,OAAO,CACf,CAAC;gBAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,SAAS,EAAE,MAAM,CAAC,SAAS;4BAC3B,OAAO,SAAA;4BACP,OAAO,EAAE;gCACP,YAAY,EAAE,OAAO,CAAC,MAAM;gCAC5B,oBAAoB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;oCACtC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,aAAa,EAArB,CAAqB,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM;oCACvE,CAAC,CAAC,CAAC;gCACL,YAAY,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;oCAC9B,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAV,CAAU,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;oCACzD,CAAC,CAAC,CAAC;6BACN;yBACF;qBACF,CAAC,EAAC;YACL,CAAC;iBAAM,CAAC;gBAEA,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;gBACvC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;gBAEpF,MAAM,GAAG,oEAA+B,CAAC,yBAAyB,CACtE,SAAS,EACT,OAAO,CACR,CAAC;gBAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM;qBACb,CAAC,EAAC;YACL,CAAC;;;;CACF;AAED,SAAe,mBAAmB;mCAAI,OAAO;;;YACrC,MAAM,GAAG,oEAA+B,CAAC,eAAe,EAAE,CAAC;YAEjE,sBAAO,qBAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,MAAM,QAAA;wBACN,OAAO,EAAE;4BACP,WAAW,EAAE,MAAM,CAAC,MAAM;4BAC1B,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,UAAU,EAAzB,CAAyB,CAAC,CAAC,MAAM;4BACpE,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,SAAS,EAAxB,CAAwB,CAAC,CAAC,MAAM;yBACnE;qBACF;iBACF,CAAC,EAAC;;;CACJ;AAED,SAAe,mBAAmB,CAAC,MAAW;mCAAG,OAAO;;;YAChD,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;YACvC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YAExF,MAAM,GAAG,oEAA+B,CAAC,yBAAyB,CACtE,SAAS,EACT,OAAO,EACP,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAClD,CAAC;YAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,MAAM;iBACb,CAAC,EAAC;;;CACJ;AAED,SAAe,wBAAwB,CAAC,MAAW;mCAAG,OAAO;;;YAC3D,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACtB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,iDAAiD,EAAE,EAC5E,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;YACJ,CAAC;YAEK,WAAW,GAAG,oEAA+B,CAAC,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEjG,sBAAO,qBAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,SAAS,EAAE,MAAM,CAAC,SAAS;wBAC3B,WAAW,aAAA;qBACZ;iBACF,CAAC,EAAC;;;CACJ;AAED,SAAe,wBAAwB,CAAC,MAAW;mCAAG,OAAO;;;YAC3D,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACtB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0DAA0D,EAAE,EACrF,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;YACJ,CAAC;YAEK,UAAU,GAAG,oEAA+B,CAAC,iBAAiB,CAClE,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,YAAY,CACpB,CAAC;YAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,SAAS,EAAE,MAAM,CAAC,SAAS;wBAC3B,YAAY,EAAE,MAAM,CAAC,YAAY;wBACjC,UAAU,YAAA;qBACX;iBACF,CAAC,EAAC;;;CACJ;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,KAAa;IAChC,wCAAwC;IACxC,IAAM,WAAW,GAAG;QAClB,mBAAmB;QACnB,qBAAqB,CAAC,uBAAuB;KAC9C,CAAC;IAEF,OAAO,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;AACnD,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/performance/route.ts"],
      sourcesContent: ["/**\n * Advanced Query Performance Monitoring API\n * Provides access to query performance metrics, alerts, and optimization suggestions\n * Part of Phase 2B: Advanced Query Optimization\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { advancedQueryPerformanceMonitor } from '@/lib/services/advanced-query-performance-monitor';\nimport { z } from 'zod';\n\n// Validation schemas\nconst performanceQuerySchema = z.object({\n  action: z.enum(['metrics', 'alerts', 'report', 'suggestions', 'regressions']).default('metrics'),\n  queryName: z.string().optional(),\n  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),\n  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),\n  limit: z.coerce.number().min(1).max(1000).default(100),\n  lookbackDays: z.coerce.number().min(1).max(30).default(7)\n});\n\nconst alertActionSchema = z.object({\n  action: z.enum(['acknowledge']),\n  alertId: z.string()\n});\n\n// GET - Retrieve performance data\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  // Check admin authentication\n  const session = await getServerSession(authOptions);\n  if (!session?.user?.email || !isAdminUser(session.user.email)) {\n    throw new Error('Unauthorized. Admin access required.');\n  }\n\n  const { searchParams } = new URL(request.url);\n  const params = performanceQuerySchema.parse(Object.fromEntries(searchParams.entries()));\n\n  switch (params.action) {\n    case 'metrics':\n      return handleMetricsRequest(params);\n\n    case 'alerts':\n      return handleAlertsRequest();\n\n    case 'report':\n      return handleReportRequest(params);\n\n    case 'suggestions':\n      return handleSuggestionsRequest(params);\n\n    case 'regressions':\n      return handleRegressionsRequest(params);\n\n    default:\n      throw new Error('Invalid action');\n  }\n});\n\n// POST - Perform actions (acknowledge alerts, etc.)\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest) => {\n  // Check admin authentication\n  const session = await getServerSession(authOptions);\n  if (!session?.user?.email || !isAdminUser(session.user.email)) {\n    throw new Error('Unauthorized. Admin access required.');\n  }\n\n  const body = await request.json();\n  const params = alertActionSchema.parse(body);\n\n  switch (params.action) {\n    case 'acknowledge':\n      const success = advancedQueryPerformanceMonitor.acknowledgeAlert(params.alertId);\n      return NextResponse.json({\n        success,\n        message: success ? 'Alert acknowledged' : 'Alert not found'\n      });\n\n    default:\n      throw new Error('Invalid action');\n  }\n});\n\n/**\n * Handler functions\n */\nasync function handleMetricsRequest(params: any): Promise<NextResponse> {\n  if (params.queryName) {\n    const metrics = advancedQueryPerformanceMonitor.getQueryMetrics(\n      params.queryName,\n      params.limit,\n      params.startDate,\n      params.endDate\n    );\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        queryName: params.queryName,\n        metrics,\n        summary: {\n          totalQueries: metrics.length,\n          averageExecutionTime: metrics.length > 0 \n            ? metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length \n            : 0,\n          cacheHitRate: metrics.length > 0 \n            ? metrics.filter(m => m.cacheHit).length / metrics.length \n            : 0\n        }\n      }\n    });\n  } else {\n    // Return recent metrics for all queries\n    const endDate = params.endDate || new Date();\n    const startDate = params.startDate || new Date(endDate.getTime() - (24 * 60 * 60 * 1000)); // Last 24 hours\n    \n    const report = advancedQueryPerformanceMonitor.generatePerformanceReport(\n      startDate,\n      endDate\n    );\n    \n    return NextResponse.json({\n      success: true,\n      data: report\n    });\n  }\n}\n\nasync function handleAlertsRequest(): Promise<NextResponse> {\n  const alerts = advancedQueryPerformanceMonitor.getActiveAlerts();\n  \n  return NextResponse.json({\n    success: true,\n    data: {\n      alerts,\n      summary: {\n        totalAlerts: alerts.length,\n        criticalAlerts: alerts.filter(a => a.severity === 'critical').length,\n        warningAlerts: alerts.filter(a => a.severity === 'warning').length\n      }\n    }\n  });\n}\n\nasync function handleReportRequest(params: any): Promise<NextResponse> {\n  const endDate = params.endDate || new Date();\n  const startDate = params.startDate || new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000)); // Last 7 days\n  \n  const report = advancedQueryPerformanceMonitor.generatePerformanceReport(\n    startDate,\n    endDate,\n    params.queryName ? [params.queryName] : undefined\n  );\n  \n  return NextResponse.json({\n    success: true,\n    data: report\n  });\n}\n\nasync function handleSuggestionsRequest(params: any): Promise<NextResponse> {\n  if (!params.queryName) {\n    return NextResponse.json(\n      { success: false, error: 'queryName parameter is required for suggestions' },\n      { status: 400 }\n    );\n  }\n  \n  const suggestions = advancedQueryPerformanceMonitor.getOptimizationSuggestions(params.queryName);\n  \n  return NextResponse.json({\n    success: true,\n    data: {\n      queryName: params.queryName,\n      suggestions\n    }\n  });\n}\n\nasync function handleRegressionsRequest(params: any): Promise<NextResponse> {\n  if (!params.queryName) {\n    return NextResponse.json(\n      { success: false, error: 'queryName parameter is required for regression detection' },\n      { status: 400 }\n    );\n  }\n  \n  const regression = advancedQueryPerformanceMonitor.detectRegressions(\n    params.queryName,\n    params.lookbackDays\n  );\n  \n  return NextResponse.json({\n    success: true,\n    data: {\n      queryName: params.queryName,\n      lookbackDays: params.lookbackDays,\n      regression\n    }\n  });\n}\n\n/**\n * Helper function to check admin access\n */\nfunction isAdminUser(email: string): boolean {\n  // Add your admin email check logic here\n  const adminEmails = [\n    '<EMAIL>',\n    '<EMAIL>' // Add your admin email\n  ];\n  \n  return adminEmails.includes(email.toLowerCase());\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0c07d948bb86c4a09c78d92fb8b7c0e3cba7f952"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2lm7n2lqx1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2lm7n2lqx1();
var __awaiter =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[0]++,
/* istanbul ignore next */
(cov_2lm7n2lqx1().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2lm7n2lqx1().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2lm7n2lqx1().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[1]++;
    cov_2lm7n2lqx1().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[2]++;
      cov_2lm7n2lqx1().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2lm7n2lqx1().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_2lm7n2lqx1().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_2lm7n2lqx1().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[4]++;
      cov_2lm7n2lqx1().s[4]++;
      try {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[5]++;
      cov_2lm7n2lqx1().s[7]++;
      try {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[6]++;
      cov_2lm7n2lqx1().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[12]++,
/* istanbul ignore next */
(cov_2lm7n2lqx1().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_2lm7n2lqx1().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2lm7n2lqx1().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().f[8]++;
        cov_2lm7n2lqx1().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[6][0]++;
          cov_2lm7n2lqx1().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2lm7n2lqx1().b[6][1]++;
        }
        cov_2lm7n2lqx1().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2lm7n2lqx1().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2lm7n2lqx1().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2lm7n2lqx1().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[9]++;
    cov_2lm7n2lqx1().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[10]++;
    cov_2lm7n2lqx1().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[11]++;
      cov_2lm7n2lqx1().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[12]++;
    cov_2lm7n2lqx1().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().b[9][0]++;
      cov_2lm7n2lqx1().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2lm7n2lqx1().b[9][1]++;
    }
    cov_2lm7n2lqx1().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[25]++;
      try {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[15][0]++,
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[16][1]++,
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[12][0]++;
          cov_2lm7n2lqx1().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2lm7n2lqx1().b[12][1]++;
        }
        cov_2lm7n2lqx1().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[18][0]++;
          cov_2lm7n2lqx1().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2lm7n2lqx1().b[18][1]++;
        }
        cov_2lm7n2lqx1().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[19][1]++;
            cov_2lm7n2lqx1().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[19][2]++;
            cov_2lm7n2lqx1().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[19][3]++;
            cov_2lm7n2lqx1().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[19][4]++;
            cov_2lm7n2lqx1().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[19][5]++;
            cov_2lm7n2lqx1().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[20][0]++;
              cov_2lm7n2lqx1().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2lm7n2lqx1().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2lm7n2lqx1().b[20][1]++;
            }
            cov_2lm7n2lqx1().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[23][0]++;
              cov_2lm7n2lqx1().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2lm7n2lqx1().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2lm7n2lqx1().b[23][1]++;
            }
            cov_2lm7n2lqx1().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[25][0]++;
              cov_2lm7n2lqx1().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2lm7n2lqx1().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_2lm7n2lqx1().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2lm7n2lqx1().b[25][1]++;
            }
            cov_2lm7n2lqx1().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_2lm7n2lqx1().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[27][0]++;
              cov_2lm7n2lqx1().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2lm7n2lqx1().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2lm7n2lqx1().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2lm7n2lqx1().b[27][1]++;
            }
            cov_2lm7n2lqx1().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[29][0]++;
              cov_2lm7n2lqx1().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2lm7n2lqx1().b[29][1]++;
            }
            cov_2lm7n2lqx1().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2lm7n2lqx1().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().b[30][0]++;
      cov_2lm7n2lqx1().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2lm7n2lqx1().b[30][1]++;
    }
    cov_2lm7n2lqx1().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_2lm7n2lqx1().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2lm7n2lqx1().s[68]++;
exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[69]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[70]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[71]++, require("@/lib/auth"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[72]++, require("@/lib/unified-api-error-handler"));
var advanced_query_performance_monitor_1 =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[73]++, require("@/lib/services/advanced-query-performance-monitor"));
var zod_1 =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[74]++, require("zod"));
// Validation schemas
var performanceQuerySchema =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[75]++, zod_1.z.object({
  action: zod_1.z.enum(['metrics', 'alerts', 'report', 'suggestions', 'regressions']).default('metrics'),
  queryName: zod_1.z.string().optional(),
  startDate: zod_1.z.string().optional().transform(function (val) {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[13]++;
    cov_2lm7n2lqx1().s[76]++;
    return val ?
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[32][0]++, new Date(val)) :
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[32][1]++, undefined);
  }),
  endDate: zod_1.z.string().optional().transform(function (val) {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[14]++;
    cov_2lm7n2lqx1().s[77]++;
    return val ?
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[33][0]++, new Date(val)) :
    /* istanbul ignore next */
    (cov_2lm7n2lqx1().b[33][1]++, undefined);
  }),
  limit: zod_1.z.coerce.number().min(1).max(1000).default(100),
  lookbackDays: zod_1.z.coerce.number().min(1).max(30).default(7)
}));
var alertActionSchema =
/* istanbul ignore next */
(cov_2lm7n2lqx1().s[78]++, zod_1.z.object({
  action: zod_1.z.enum(['acknowledge']),
  alertId: zod_1.z.string()
}));
// GET - Retrieve performance data
/* istanbul ignore next */
cov_2lm7n2lqx1().s[79]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[15]++;
  cov_2lm7n2lqx1().s[80]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[16]++;
    var session, searchParams, params;
    var _a;
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[81]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[17]++;
      cov_2lm7n2lqx1().s[82]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[34][0]++;
          cov_2lm7n2lqx1().s[83]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[34][1]++;
          cov_2lm7n2lqx1().s[84]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[85]++;
          if (
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[36][0]++, !(
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[38][0]++, (_a =
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[40][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[40][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[39][0]++, void 0) :
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[39][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[38][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[37][0]++, void 0) :
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[37][1]++, _a.email))) ||
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[36][1]++, !isAdminUser(session.user.email))) {
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[35][0]++;
            cov_2lm7n2lqx1().s[86]++;
            throw new Error('Unauthorized. Admin access required.');
          } else
          /* istanbul ignore next */
          {
            cov_2lm7n2lqx1().b[35][1]++;
          }
          cov_2lm7n2lqx1().s[87]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[88]++;
          params = performanceQuerySchema.parse(Object.fromEntries(searchParams.entries()));
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[89]++;
          switch (params.action) {
            case 'metrics':
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[41][0]++;
              cov_2lm7n2lqx1().s[90]++;
              return [2 /*return*/, handleMetricsRequest(params)];
            case 'alerts':
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[41][1]++;
              cov_2lm7n2lqx1().s[91]++;
              return [2 /*return*/, handleAlertsRequest()];
            case 'report':
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[41][2]++;
              cov_2lm7n2lqx1().s[92]++;
              return [2 /*return*/, handleReportRequest(params)];
            case 'suggestions':
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[41][3]++;
              cov_2lm7n2lqx1().s[93]++;
              return [2 /*return*/, handleSuggestionsRequest(params)];
            case 'regressions':
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[41][4]++;
              cov_2lm7n2lqx1().s[94]++;
              return [2 /*return*/, handleRegressionsRequest(params)];
            default:
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[41][5]++;
              cov_2lm7n2lqx1().s[95]++;
              throw new Error('Invalid action');
          }
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[96]++;
          return [2 /*return*/];
      }
    });
  });
});
// POST - Perform actions (acknowledge alerts, etc.)
/* istanbul ignore next */
cov_2lm7n2lqx1().s[97]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[18]++;
  cov_2lm7n2lqx1().s[98]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[19]++;
    var session, body, params, success;
    var _a;
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[99]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[20]++;
      cov_2lm7n2lqx1().s[100]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[42][0]++;
          cov_2lm7n2lqx1().s[101]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[42][1]++;
          cov_2lm7n2lqx1().s[102]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[103]++;
          if (
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[44][0]++, !(
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[46][0]++, (_a =
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[48][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[48][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[47][0]++, void 0) :
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[47][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[46][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[45][0]++, void 0) :
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[45][1]++, _a.email))) ||
          /* istanbul ignore next */
          (cov_2lm7n2lqx1().b[44][1]++, !isAdminUser(session.user.email))) {
            /* istanbul ignore next */
            cov_2lm7n2lqx1().b[43][0]++;
            cov_2lm7n2lqx1().s[104]++;
            throw new Error('Unauthorized. Admin access required.');
          } else
          /* istanbul ignore next */
          {
            cov_2lm7n2lqx1().b[43][1]++;
          }
          cov_2lm7n2lqx1().s[105]++;
          return [4 /*yield*/, request.json()];
        case 2:
          /* istanbul ignore next */
          cov_2lm7n2lqx1().b[42][2]++;
          cov_2lm7n2lqx1().s[106]++;
          body = _b.sent();
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[107]++;
          params = alertActionSchema.parse(body);
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[108]++;
          switch (params.action) {
            case 'acknowledge':
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[49][0]++;
              cov_2lm7n2lqx1().s[109]++;
              success = advanced_query_performance_monitor_1.advancedQueryPerformanceMonitor.acknowledgeAlert(params.alertId);
              /* istanbul ignore next */
              cov_2lm7n2lqx1().s[110]++;
              return [2 /*return*/, server_1.NextResponse.json({
                success: success,
                message: success ?
                /* istanbul ignore next */
                (cov_2lm7n2lqx1().b[50][0]++, 'Alert acknowledged') :
                /* istanbul ignore next */
                (cov_2lm7n2lqx1().b[50][1]++, 'Alert not found')
              })];
            default:
              /* istanbul ignore next */
              cov_2lm7n2lqx1().b[49][1]++;
              cov_2lm7n2lqx1().s[111]++;
              throw new Error('Invalid action');
          }
          /* istanbul ignore next */
          cov_2lm7n2lqx1().s[112]++;
          return [2 /*return*/];
      }
    });
  });
});
/**
 * Handler functions
 */
function handleMetricsRequest(params) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[21]++;
  cov_2lm7n2lqx1().s[113]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[22]++;
    var metrics, endDate, startDate, report;
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[114]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[23]++;
      cov_2lm7n2lqx1().s[115]++;
      if (params.queryName) {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().b[51][0]++;
        cov_2lm7n2lqx1().s[116]++;
        metrics = advanced_query_performance_monitor_1.advancedQueryPerformanceMonitor.getQueryMetrics(params.queryName, params.limit, params.startDate, params.endDate);
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[117]++;
        return [2 /*return*/, server_1.NextResponse.json({
          success: true,
          data: {
            queryName: params.queryName,
            metrics: metrics,
            summary: {
              totalQueries: metrics.length,
              averageExecutionTime: metrics.length > 0 ?
              /* istanbul ignore next */
              (cov_2lm7n2lqx1().b[52][0]++, metrics.reduce(function (sum, m) {
                /* istanbul ignore next */
                cov_2lm7n2lqx1().f[24]++;
                cov_2lm7n2lqx1().s[118]++;
                return sum + m.executionTime;
              }, 0) / metrics.length) :
              /* istanbul ignore next */
              (cov_2lm7n2lqx1().b[52][1]++, 0),
              cacheHitRate: metrics.length > 0 ?
              /* istanbul ignore next */
              (cov_2lm7n2lqx1().b[53][0]++, metrics.filter(function (m) {
                /* istanbul ignore next */
                cov_2lm7n2lqx1().f[25]++;
                cov_2lm7n2lqx1().s[119]++;
                return m.cacheHit;
              }).length / metrics.length) :
              /* istanbul ignore next */
              (cov_2lm7n2lqx1().b[53][1]++, 0)
            }
          }
        })];
      } else {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().b[51][1]++;
        cov_2lm7n2lqx1().s[120]++;
        endDate =
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[54][0]++, params.endDate) ||
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[54][1]++, new Date());
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[121]++;
        startDate =
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[55][0]++, params.startDate) ||
        /* istanbul ignore next */
        (cov_2lm7n2lqx1().b[55][1]++, new Date(endDate.getTime() - 24 * 60 * 60 * 1000));
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[122]++;
        report = advanced_query_performance_monitor_1.advancedQueryPerformanceMonitor.generatePerformanceReport(startDate, endDate);
        /* istanbul ignore next */
        cov_2lm7n2lqx1().s[123]++;
        return [2 /*return*/, server_1.NextResponse.json({
          success: true,
          data: report
        })];
      }
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[124]++;
      return [2 /*return*/];
    });
  });
}
function handleAlertsRequest() {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[26]++;
  cov_2lm7n2lqx1().s[125]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[27]++;
    var alerts;
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[126]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[28]++;
      cov_2lm7n2lqx1().s[127]++;
      alerts = advanced_query_performance_monitor_1.advancedQueryPerformanceMonitor.getActiveAlerts();
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[128]++;
      return [2 /*return*/, server_1.NextResponse.json({
        success: true,
        data: {
          alerts: alerts,
          summary: {
            totalAlerts: alerts.length,
            criticalAlerts: alerts.filter(function (a) {
              /* istanbul ignore next */
              cov_2lm7n2lqx1().f[29]++;
              cov_2lm7n2lqx1().s[129]++;
              return a.severity === 'critical';
            }).length,
            warningAlerts: alerts.filter(function (a) {
              /* istanbul ignore next */
              cov_2lm7n2lqx1().f[30]++;
              cov_2lm7n2lqx1().s[130]++;
              return a.severity === 'warning';
            }).length
          }
        }
      })];
    });
  });
}
function handleReportRequest(params) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[31]++;
  cov_2lm7n2lqx1().s[131]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[32]++;
    var endDate, startDate, report;
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[132]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[33]++;
      cov_2lm7n2lqx1().s[133]++;
      endDate =
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[56][0]++, params.endDate) ||
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[56][1]++, new Date());
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[134]++;
      startDate =
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[57][0]++, params.startDate) ||
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[57][1]++, new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000));
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[135]++;
      report = advanced_query_performance_monitor_1.advancedQueryPerformanceMonitor.generatePerformanceReport(startDate, endDate, params.queryName ?
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[58][0]++, [params.queryName]) :
      /* istanbul ignore next */
      (cov_2lm7n2lqx1().b[58][1]++, undefined));
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[136]++;
      return [2 /*return*/, server_1.NextResponse.json({
        success: true,
        data: report
      })];
    });
  });
}
function handleSuggestionsRequest(params) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[34]++;
  cov_2lm7n2lqx1().s[137]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[35]++;
    var suggestions;
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[138]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[36]++;
      cov_2lm7n2lqx1().s[139]++;
      if (!params.queryName) {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().b[59][0]++;
        cov_2lm7n2lqx1().s[140]++;
        return [2 /*return*/, server_1.NextResponse.json({
          success: false,
          error: 'queryName parameter is required for suggestions'
        }, {
          status: 400
        })];
      } else
      /* istanbul ignore next */
      {
        cov_2lm7n2lqx1().b[59][1]++;
      }
      cov_2lm7n2lqx1().s[141]++;
      suggestions = advanced_query_performance_monitor_1.advancedQueryPerformanceMonitor.getOptimizationSuggestions(params.queryName);
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[142]++;
      return [2 /*return*/, server_1.NextResponse.json({
        success: true,
        data: {
          queryName: params.queryName,
          suggestions: suggestions
        }
      })];
    });
  });
}
function handleRegressionsRequest(params) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[37]++;
  cov_2lm7n2lqx1().s[143]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2lm7n2lqx1().f[38]++;
    var regression;
    /* istanbul ignore next */
    cov_2lm7n2lqx1().s[144]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2lm7n2lqx1().f[39]++;
      cov_2lm7n2lqx1().s[145]++;
      if (!params.queryName) {
        /* istanbul ignore next */
        cov_2lm7n2lqx1().b[60][0]++;
        cov_2lm7n2lqx1().s[146]++;
        return [2 /*return*/, server_1.NextResponse.json({
          success: false,
          error: 'queryName parameter is required for regression detection'
        }, {
          status: 400
        })];
      } else
      /* istanbul ignore next */
      {
        cov_2lm7n2lqx1().b[60][1]++;
      }
      cov_2lm7n2lqx1().s[147]++;
      regression = advanced_query_performance_monitor_1.advancedQueryPerformanceMonitor.detectRegressions(params.queryName, params.lookbackDays);
      /* istanbul ignore next */
      cov_2lm7n2lqx1().s[148]++;
      return [2 /*return*/, server_1.NextResponse.json({
        success: true,
        data: {
          queryName: params.queryName,
          lookbackDays: params.lookbackDays,
          regression: regression
        }
      })];
    });
  });
}
/**
 * Helper function to check admin access
 */
function isAdminUser(email) {
  /* istanbul ignore next */
  cov_2lm7n2lqx1().f[40]++;
  // Add your admin email check logic here
  var adminEmails =
  /* istanbul ignore next */
  (cov_2lm7n2lqx1().s[149]++, ['<EMAIL>', '<EMAIL>' // Add your admin email
  ]);
  /* istanbul ignore next */
  cov_2lm7n2lqx1().s[150]++;
  return adminEmails.includes(email.toLowerCase());
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************