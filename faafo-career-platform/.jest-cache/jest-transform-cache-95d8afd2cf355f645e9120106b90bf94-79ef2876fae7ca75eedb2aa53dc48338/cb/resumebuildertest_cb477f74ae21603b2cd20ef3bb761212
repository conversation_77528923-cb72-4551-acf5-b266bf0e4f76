0125ce8fae4e2b663285436e2c0a0b46
"use strict";
/**
 * Resume Builder API Tests
 *
 * Tests for the resume builder API endpoints including CRUD operations,
 * authentication, validation, and error handling.
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/lib/prisma', function () { return ({
    user: {
        findUnique: jest.fn(),
    },
    resume: {
        findMany: jest.fn(),
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
    },
}); });
jest.mock('@/lib/errorReporting');
jest.mock('@/lib/logger');
jest.mock('@/lib/errorTracking');
jest.mock('@/lib/csrf');
jest.mock('@/lib/rateLimit');
var server_1 = require("next/server");
var next_1 = require("next-auth/next");
var route_1 = require("@/app/api/resume-builder/route");
var route_2 = require("@/app/api/resume-builder/[id]/route");
var prisma_1 = __importDefault(require("@/lib/prisma"));
var mockGetServerSession = next_1.getServerSession;
var mockPrisma = prisma_1.default;
describe('Resume Builder API', function () {
    beforeEach(function () {
        jest.clearAllMocks();
    });
    describe('GET /api/resume-builder', function () {
        it('should return 401 when user is not authenticated', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetServerSession.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder');
                        return [4 /*yield*/, (0, route_1.GET)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(401);
                        expect(data.error).toBe('Not authenticated');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return user resumes when authenticated', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockResumes, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockResumes = [
                            {
                                id: 'resume-1',
                                title: 'Software Engineer Resume',
                                template: 'modern',
                                isPublic: false,
                                lastExported: null,
                                exportCount: 0,
                                createdAt: new Date(),
                                updatedAt: new Date(),
                            }
                        ];
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findMany.mockResolvedValue(mockResumes);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder');
                        return [4 /*yield*/, (0, route_1.GET)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockResumes);
                        expect(mockPrisma.resume.findMany).toHaveBeenCalledWith({
                            where: {
                                userId: 'user-1',
                                isActive: true
                            },
                            select: {
                                id: true,
                                title: true,
                                template: true,
                                isPublic: true,
                                lastExported: true,
                                exportCount: true,
                                createdAt: true,
                                updatedAt: true
                            },
                            orderBy: { updatedAt: 'desc' }
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return 404 when user not found', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder');
                        return [4 /*yield*/, (0, route_1.GET)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(404);
                        expect(data.error).toBe('User not found');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('POST /api/resume-builder', function () {
        it('should create a new resume when valid data is provided', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockResumeData, mockCreatedResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockResumeData = {
                            title: 'My Resume',
                            personalInfo: {
                                firstName: 'John',
                                lastName: 'Doe',
                                email: '<EMAIL>'
                            },
                            summary: 'Experienced developer',
                            experience: [],
                            education: [],
                            skills: [],
                            template: 'modern',
                            isPublic: false
                        };
                        mockCreatedResume = __assign(__assign({ id: 'resume-1', userId: 'user-1' }, mockResumeData), { createdAt: new Date(), updatedAt: new Date() });
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.create.mockResolvedValue(mockCreatedResume);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder', {
                            method: 'POST',
                            body: JSON.stringify(mockResumeData),
                            headers: { 'Content-Type': 'application/json' }
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(201);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockCreatedResume);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return 400 when validation fails', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, invalidResumeData, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        invalidResumeData = {
                            // Missing required title
                            personalInfo: {
                                firstName: 'John',
                                lastName: 'Doe',
                                email: 'invalid-email' // Invalid email format
                            }
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder', {
                            method: 'POST',
                            body: JSON.stringify(invalidResumeData),
                            headers: { 'Content-Type': 'application/json' }
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(400);
                        expect(data.success).toBe(false);
                        expect(data.error).toBe('Validation failed');
                        expect(data.details).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('GET /api/resume-builder/[id]', function () {
        it('should return specific resume when user owns it', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockResume = {
                            id: 'resume-1',
                            userId: 'user-1',
                            title: 'My Resume',
                            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
                            template: 'modern',
                            isActive: true
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(mockResume);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1');
                        return [4 /*yield*/, (0, route_2.GET)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockResume);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return 404 when resume not found or not owned by user', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1');
                        return [4 /*yield*/, (0, route_2.GET)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(404);
                        expect(data.success).toBe(false);
                        expect(data.error).toBe('Resume not found');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('PUT /api/resume-builder/[id]', function () {
        it('should update resume when user owns it', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockExistingResume, updateData, mockUpdatedResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockExistingResume = {
                            id: 'resume-1',
                            userId: 'user-1',
                            title: 'Old Title',
                            isActive: true
                        };
                        updateData = {
                            title: 'Updated Title',
                            summary: 'Updated summary'
                        };
                        mockUpdatedResume = __assign(__assign(__assign({}, mockExistingResume), updateData), { updatedAt: new Date() });
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);
                        mockPrisma.resume.update.mockResolvedValue(mockUpdatedResume);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
                            method: 'PUT',
                            body: JSON.stringify(updateData),
                            headers: { 'Content-Type': 'application/json' }
                        });
                        return [4 /*yield*/, (0, route_2.PUT)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockUpdatedResume);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('DELETE /api/resume-builder/[id]', function () {
        it('should soft delete resume when user owns it', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockExistingResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockExistingResume = {
                            id: 'resume-1',
                            userId: 'user-1',
                            title: 'My Resume',
                            isActive: true
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);
                        mockPrisma.resume.update.mockResolvedValue(__assign(__assign({}, mockExistingResume), { isActive: false }));
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
                            method: 'DELETE'
                        });
                        return [4 /*yield*/, (0, route_2.DELETE)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.message).toBe('Resume deleted successfully');
                        expect(mockPrisma.resume.update).toHaveBeenCalledWith({
                            where: { id: 'resume-1' },
                            data: {
                                isActive: false,
                                updatedAt: expect.any(Date)
                            }
                        });
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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