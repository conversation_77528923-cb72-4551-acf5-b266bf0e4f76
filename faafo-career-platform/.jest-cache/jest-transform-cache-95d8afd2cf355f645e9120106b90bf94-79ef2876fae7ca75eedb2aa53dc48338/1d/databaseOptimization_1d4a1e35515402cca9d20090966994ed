6f9a8e56c07450d5ee5cb5f6854fa6cf
"use strict";

/* istanbul ignore next */
function cov_p8oku5j5a() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/databaseOptimization.ts";
  var hash = "a155c81f0bfb759d0e0a28a359024cfdfe030991";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/databaseOptimization.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 27
        },
        end: {
          line: 5,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 115
        }
      },
      "2": {
        start: {
          line: 3,
          column: 33
        },
        end: {
          line: 3,
          column: 86
        }
      },
      "3": {
        start: {
          line: 3,
          column: 96
        },
        end: {
          line: 3,
          column: 113
        }
      },
      "4": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 18
        }
      },
      "5": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "6": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "7": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "8": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "9": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "10": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "11": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "12": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "13": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "14": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "15": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "16": {
        start: {
          line: 17,
          column: 16
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "17": {
        start: {
          line: 18,
          column: 28
        },
        end: {
          line: 18,
          column: 110
        }
      },
      "18": {
        start: {
          line: 18,
          column: 91
        },
        end: {
          line: 18,
          column: 106
        }
      },
      "19": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 24,
          column: 7
        }
      },
      "20": {
        start: {
          line: 20,
          column: 36
        },
        end: {
          line: 20,
          column: 97
        }
      },
      "21": {
        start: {
          line: 20,
          column: 42
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "22": {
        start: {
          line: 20,
          column: 85
        },
        end: {
          line: 20,
          column: 95
        }
      },
      "23": {
        start: {
          line: 21,
          column: 35
        },
        end: {
          line: 21,
          column: 100
        }
      },
      "24": {
        start: {
          line: 21,
          column: 41
        },
        end: {
          line: 21,
          column: 73
        }
      },
      "25": {
        start: {
          line: 21,
          column: 88
        },
        end: {
          line: 21,
          column: 98
        }
      },
      "26": {
        start: {
          line: 22,
          column: 32
        },
        end: {
          line: 22,
          column: 116
        }
      },
      "27": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 78
        }
      },
      "28": {
        start: {
          line: 26,
          column: 18
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "29": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "30": {
        start: {
          line: 27,
          column: 43
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "31": {
        start: {
          line: 27,
          column: 57
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "32": {
        start: {
          line: 27,
          column: 69
        },
        end: {
          line: 27,
          column: 81
        }
      },
      "33": {
        start: {
          line: 27,
          column: 119
        },
        end: {
          line: 27,
          column: 196
        }
      },
      "34": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 160
        }
      },
      "35": {
        start: {
          line: 28,
          column: 141
        },
        end: {
          line: 28,
          column: 153
        }
      },
      "36": {
        start: {
          line: 29,
          column: 23
        },
        end: {
          line: 29,
          column: 68
        }
      },
      "37": {
        start: {
          line: 29,
          column: 45
        },
        end: {
          line: 29,
          column: 65
        }
      },
      "38": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "39": {
        start: {
          line: 31,
          column: 15
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "40": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "41": {
        start: {
          line: 32,
          column: 50
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "42": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "43": {
        start: {
          line: 33,
          column: 160
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "44": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "45": {
        start: {
          line: 34,
          column: 26
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "46": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "47": {
        start: {
          line: 36,
          column: 32
        },
        end: {
          line: 36,
          column: 39
        }
      },
      "48": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 46
        }
      },
      "49": {
        start: {
          line: 37,
          column: 24
        },
        end: {
          line: 37,
          column: 34
        }
      },
      "50": {
        start: {
          line: 37,
          column: 35
        },
        end: {
          line: 37,
          column: 72
        }
      },
      "51": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "52": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 45
        }
      },
      "53": {
        start: {
          line: 38,
          column: 46
        },
        end: {
          line: 38,
          column: 55
        }
      },
      "54": {
        start: {
          line: 38,
          column: 56
        },
        end: {
          line: 38,
          column: 65
        }
      },
      "55": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 41
        }
      },
      "56": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "57": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "58": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 128
        }
      },
      "59": {
        start: {
          line: 41,
          column: 110
        },
        end: {
          line: 41,
          column: 116
        }
      },
      "60": {
        start: {
          line: 41,
          column: 117
        },
        end: {
          line: 41,
          column: 126
        }
      },
      "61": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 106
        }
      },
      "62": {
        start: {
          line: 42,
          column: 81
        },
        end: {
          line: 42,
          column: 97
        }
      },
      "63": {
        start: {
          line: 42,
          column: 98
        },
        end: {
          line: 42,
          column: 104
        }
      },
      "64": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 89
        }
      },
      "65": {
        start: {
          line: 43,
          column: 57
        },
        end: {
          line: 43,
          column: 72
        }
      },
      "66": {
        start: {
          line: 43,
          column: 73
        },
        end: {
          line: 43,
          column: 80
        }
      },
      "67": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 87
        }
      },
      "68": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "69": {
        start: {
          line: 44,
          column: 47
        },
        end: {
          line: 44,
          column: 62
        }
      },
      "70": {
        start: {
          line: 44,
          column: 63
        },
        end: {
          line: 44,
          column: 78
        }
      },
      "71": {
        start: {
          line: 44,
          column: 79
        },
        end: {
          line: 44,
          column: 85
        }
      },
      "72": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "73": {
        start: {
          line: 45,
          column: 30
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "74": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 33
        }
      },
      "75": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 43
        }
      },
      "76": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 48,
          column: 39
        }
      },
      "77": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "78": {
        start: {
          line: 49,
          column: 35
        },
        end: {
          line: 49,
          column: 41
        }
      },
      "79": {
        start: {
          line: 49,
          column: 54
        },
        end: {
          line: 49,
          column: 64
        }
      },
      "80": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "81": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "82": {
        start: {
          line: 50,
          column: 36
        },
        end: {
          line: 50,
          column: 89
        }
      },
      "83": {
        start: {
          line: 53,
          column: 22
        },
        end: {
          line: 55,
          column: 1
        }
      },
      "84": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 54,
          column: 62
        }
      },
      "85": {
        start: {
          line: 56,
          column: 0
        },
        end: {
          line: 56,
          column: 62
        }
      },
      "86": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 32
        }
      },
      "87": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 48
        }
      },
      "88": {
        start: {
          line: 59,
          column: 15
        },
        end: {
          line: 59,
          column: 55
        }
      },
      "89": {
        start: {
          line: 60,
          column: 11
        },
        end: {
          line: 60,
          column: 41
        }
      },
      "90": {
        start: {
          line: 61,
          column: 13
        },
        end: {
          line: 61,
          column: 45
        }
      },
      "91": {
        start: {
          line: 62,
          column: 49
        },
        end: {
          line: 430,
          column: 3
        }
      },
      "92": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 64,
          column: 31
        }
      },
      "93": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 65,
          column: 38
        }
      },
      "94": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 121,
          column: 6
        }
      },
      "95": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 120,
          column: 11
        }
      },
      "96": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 119,
          column: 15
        }
      },
      "97": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 118,
          column: 17
        }
      },
      "98": {
        start: {
          line: 74,
          column: 24
        },
        end: {
          line: 74,
          column: 50
        }
      },
      "99": {
        start: {
          line: 75,
          column: 24
        },
        end: {
          line: 75,
          column: 128
        }
      },
      "100": {
        start: {
          line: 76,
          column: 24
        },
        end: {
          line: 81,
          column: 25
        }
      },
      "101": {
        start: {
          line: 77,
          column: 28
        },
        end: {
          line: 80,
          column: 35
        }
      },
      "102": {
        start: {
          line: 82,
          column: 24
        },
        end: {
          line: 82,
          column: 85
        }
      },
      "103": {
        start: {
          line: 83,
          column: 24
        },
        end: {
          line: 86,
          column: 107
        }
      },
      "104": {
        start: {
          line: 85,
          column: 51
        },
        end: {
          line: 85,
          column: 70
        }
      },
      "105": {
        start: {
          line: 86,
          column: 54
        },
        end: {
          line: 86,
          column: 103
        }
      },
      "106": {
        start: {
          line: 87,
          column: 24
        },
        end: {
          line: 87,
          column: 36
        }
      },
      "107": {
        start: {
          line: 88,
          column: 24
        },
        end: {
          line: 88,
          column: 41
        }
      },
      "108": {
        start: {
          line: 89,
          column: 24
        },
        end: {
          line: 89,
          column: 58
        }
      },
      "109": {
        start: {
          line: 90,
          column: 24
        },
        end: {
          line: 90,
          column: 37
        }
      },
      "110": {
        start: {
          line: 92,
          column: 24
        },
        end: {
          line: 92,
          column: 81
        }
      },
      "111": {
        start: {
          line: 92,
          column: 57
        },
        end: {
          line: 92,
          column: 81
        }
      },
      "112": {
        start: {
          line: 93,
          column: 24
        },
        end: {
          line: 93,
          column: 53
        }
      },
      "113": {
        start: {
          line: 94,
          column: 24
        },
        end: {
          line: 94,
          column: 37
        }
      },
      "114": {
        start: {
          line: 96,
          column: 24
        },
        end: {
          line: 96,
          column: 50
        }
      },
      "115": {
        start: {
          line: 97,
          column: 24
        },
        end: {
          line: 97,
          column: 92
        }
      },
      "116": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 99,
          column: 34
        }
      },
      "117": {
        start: {
          line: 100,
          column: 24
        },
        end: {
          line: 100,
          column: 39
        }
      },
      "118": {
        start: {
          line: 101,
          column: 24
        },
        end: {
          line: 101,
          column: 48
        }
      },
      "119": {
        start: {
          line: 103,
          column: 24
        },
        end: {
          line: 103,
          column: 44
        }
      },
      "120": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 104,
          column: 96
        }
      },
      "121": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 105,
          column: 122
        }
      },
      "122": {
        start: {
          line: 106,
          column: 24
        },
        end: {
          line: 106,
          column: 48
        }
      },
      "123": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 29
        }
      },
      "124": {
        start: {
          line: 109,
          column: 24
        },
        end: {
          line: 109,
          column: 48
        }
      },
      "125": {
        start: {
          line: 110,
          column: 28
        },
        end: {
          line: 110,
          column: 275
        }
      },
      "126": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 112,
          column: 44
        }
      },
      "127": {
        start: {
          line: 113,
          column: 24
        },
        end: {
          line: 116,
          column: 31
        }
      },
      "128": {
        start: {
          line: 117,
          column: 28
        },
        end: {
          line: 117,
          column: 50
        }
      },
      "129": {
        start: {
          line: 123,
          column: 4
        },
        end: {
          line: 146,
          column: 6
        }
      },
      "130": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 145,
          column: 11
        }
      },
      "131": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 62
        }
      },
      "132": {
        start: {
          line: 126,
          column: 43
        },
        end: {
          line: 126,
          column: 60
        }
      },
      "133": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 144,
          column: 15
        }
      },
      "134": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 133,
          column: 18
        }
      },
      "135": {
        start: {
          line: 134,
          column: 16
        },
        end: {
          line: 134,
          column: 47
        }
      },
      "136": {
        start: {
          line: 136,
          column: 16
        },
        end: {
          line: 138,
          column: 17
        }
      },
      "137": {
        start: {
          line: 137,
          column: 20
        },
        end: {
          line: 137,
          column: 89
        }
      },
      "138": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 142,
          column: 17
        }
      },
      "139": {
        start: {
          line: 141,
          column: 20
        },
        end: {
          line: 141,
          column: 115
        }
      },
      "140": {
        start: {
          line: 143,
          column: 16
        },
        end: {
          line: 143,
          column: 38
        }
      },
      "141": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 200,
          column: 6
        }
      },
      "142": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 199,
          column: 11
        }
      },
      "143": {
        start: {
          line: 151,
          column: 12
        },
        end: {
          line: 198,
          column: 15
        }
      },
      "144": {
        start: {
          line: 152,
          column: 16
        },
        end: {
          line: 197,
          column: 17
        }
      },
      "145": {
        start: {
          line: 154,
          column: 24
        },
        end: {
          line: 154,
          column: 47
        }
      },
      "146": {
        start: {
          line: 155,
          column: 24
        },
        end: {
          line: 155,
          column: 37
        }
      },
      "147": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 157,
          column: 50
        }
      },
      "148": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 166,
          column: 32
        }
      },
      "149": {
        start: {
          line: 168,
          column: 24
        },
        end: {
          line: 168,
          column: 229
        }
      },
      "150": {
        start: {
          line: 169,
          column: 24
        },
        end: {
          line: 169,
          column: 59
        }
      },
      "151": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 170,
          column: 93
        }
      },
      "152": {
        start: {
          line: 172,
          column: 24
        },
        end: {
          line: 172,
          column: 34
        }
      },
      "153": {
        start: {
          line: 173,
          column: 24
        },
        end: {
          line: 173,
          column: 70
        }
      },
      "154": {
        start: {
          line: 174,
          column: 24
        },
        end: {
          line: 176,
          column: 32
        }
      },
      "155": {
        start: {
          line: 175,
          column: 71
        },
        end: {
          line: 175,
          column: 100
        }
      },
      "156": {
        start: {
          line: 177,
          column: 24
        },
        end: {
          line: 180,
          column: 42
        }
      },
      "157": {
        start: {
          line: 178,
          column: 51
        },
        end: {
          line: 178,
          column: 80
        }
      },
      "158": {
        start: {
          line: 179,
          column: 52
        },
        end: {
          line: 179,
          column: 93
        }
      },
      "159": {
        start: {
          line: 181,
          column: 24
        },
        end: {
          line: 191,
          column: 31
        }
      },
      "160": {
        start: {
          line: 193,
          column: 24
        },
        end: {
          line: 193,
          column: 44
        }
      },
      "161": {
        start: {
          line: 194,
          column: 24
        },
        end: {
          line: 194,
          column: 80
        }
      },
      "162": {
        start: {
          line: 195,
          column: 24
        },
        end: {
          line: 195,
          column: 38
        }
      },
      "163": {
        start: {
          line: 196,
          column: 28
        },
        end: {
          line: 196,
          column: 50
        }
      },
      "164": {
        start: {
          line: 202,
          column: 4
        },
        end: {
          line: 252,
          column: 6
        }
      },
      "165": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 251,
          column: 11
        }
      },
      "166": {
        start: {
          line: 205,
          column: 12
        },
        end: {
          line: 250,
          column: 15
        }
      },
      "167": {
        start: {
          line: 206,
          column: 16
        },
        end: {
          line: 249,
          column: 17
        }
      },
      "168": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 50
        }
      },
      "169": {
        start: {
          line: 209,
          column: 24
        },
        end: {
          line: 209,
          column: 69
        }
      },
      "170": {
        start: {
          line: 210,
          column: 24
        },
        end: {
          line: 210,
          column: 89
        }
      },
      "171": {
        start: {
          line: 210,
          column: 65
        },
        end: {
          line: 210,
          column: 89
        }
      },
      "172": {
        start: {
          line: 212,
          column: 24
        },
        end: {
          line: 212,
          column: 164
        }
      },
      "173": {
        start: {
          line: 215,
          column: 24
        },
        end: {
          line: 215,
          column: 34
        }
      },
      "174": {
        start: {
          line: 216,
          column: 24
        },
        end: {
          line: 216,
          column: 178
        }
      },
      "175": {
        start: {
          line: 218,
          column: 24
        },
        end: {
          line: 218,
          column: 34
        }
      },
      "176": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 222,
          column: 31
        }
      },
      "177": {
        start: {
          line: 224,
          column: 24
        },
        end: {
          line: 224,
          column: 85
        }
      },
      "178": {
        start: {
          line: 224,
          column: 61
        },
        end: {
          line: 224,
          column: 85
        }
      },
      "179": {
        start: {
          line: 226,
          column: 24
        },
        end: {
          line: 226,
          column: 164
        }
      },
      "180": {
        start: {
          line: 229,
          column: 24
        },
        end: {
          line: 229,
          column: 34
        }
      },
      "181": {
        start: {
          line: 230,
          column: 24
        },
        end: {
          line: 230,
          column: 162
        }
      },
      "182": {
        start: {
          line: 232,
          column: 24
        },
        end: {
          line: 232,
          column: 34
        }
      },
      "183": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 236,
          column: 31
        }
      },
      "184": {
        start: {
          line: 237,
          column: 28
        },
        end: {
          line: 240,
          column: 27
        }
      },
      "185": {
        start: {
          line: 241,
          column: 28
        },
        end: {
          line: 241,
          column: 52
        }
      },
      "186": {
        start: {
          line: 243,
          column: 24
        },
        end: {
          line: 243,
          column: 44
        }
      },
      "187": {
        start: {
          line: 244,
          column: 24
        },
        end: {
          line: 247,
          column: 31
        }
      },
      "188": {
        start: {
          line: 248,
          column: 28
        },
        end: {
          line: 248,
          column: 50
        }
      },
      "189": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 294,
          column: 6
        }
      },
      "190": {
        start: {
          line: 255,
          column: 8
        },
        end: {
          line: 293,
          column: 11
        }
      },
      "191": {
        start: {
          line: 257,
          column: 12
        },
        end: {
          line: 292,
          column: 15
        }
      },
      "192": {
        start: {
          line: 258,
          column: 16
        },
        end: {
          line: 291,
          column: 17
        }
      },
      "193": {
        start: {
          line: 260,
          column: 24
        },
        end: {
          line: 260,
          column: 50
        }
      },
      "194": {
        start: {
          line: 261,
          column: 24
        },
        end: {
          line: 261,
          column: 69
        }
      },
      "195": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 89
        }
      },
      "196": {
        start: {
          line: 262,
          column: 65
        },
        end: {
          line: 262,
          column: 89
        }
      },
      "197": {
        start: {
          line: 263,
          column: 24
        },
        end: {
          line: 263,
          column: 690
        }
      },
      "198": {
        start: {
          line: 265,
          column: 24
        },
        end: {
          line: 265,
          column: 47
        }
      },
      "199": {
        start: {
          line: 266,
          column: 24
        },
        end: {
          line: 269,
          column: 31
        }
      },
      "200": {
        start: {
          line: 271,
          column: 24
        },
        end: {
          line: 271,
          column: 85
        }
      },
      "201": {
        start: {
          line: 271,
          column: 61
        },
        end: {
          line: 271,
          column: 85
        }
      },
      "202": {
        start: {
          line: 272,
          column: 24
        },
        end: {
          line: 272,
          column: 484
        }
      },
      "203": {
        start: {
          line: 274,
          column: 24
        },
        end: {
          line: 274,
          column: 44
        }
      },
      "204": {
        start: {
          line: 275,
          column: 24
        },
        end: {
          line: 278,
          column: 31
        }
      },
      "205": {
        start: {
          line: 279,
          column: 28
        },
        end: {
          line: 282,
          column: 27
        }
      },
      "206": {
        start: {
          line: 283,
          column: 28
        },
        end: {
          line: 283,
          column: 52
        }
      },
      "207": {
        start: {
          line: 285,
          column: 24
        },
        end: {
          line: 285,
          column: 44
        }
      },
      "208": {
        start: {
          line: 286,
          column: 24
        },
        end: {
          line: 286,
          column: 79
        }
      },
      "209": {
        start: {
          line: 287,
          column: 24
        },
        end: {
          line: 289,
          column: 31
        }
      },
      "210": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 50
        }
      },
      "211": {
        start: {
          line: 296,
          column: 4
        },
        end: {
          line: 352,
          column: 6
        }
      },
      "212": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 351,
          column: 11
        }
      },
      "213": {
        start: {
          line: 299,
          column: 24
        },
        end: {
          line: 299,
          column: 28
        }
      },
      "214": {
        start: {
          line: 300,
          column: 12
        },
        end: {
          line: 350,
          column: 15
        }
      },
      "215": {
        start: {
          line: 301,
          column: 16
        },
        end: {
          line: 349,
          column: 17
        }
      },
      "216": {
        start: {
          line: 303,
          column: 24
        },
        end: {
          line: 303,
          column: 50
        }
      },
      "217": {
        start: {
          line: 304,
          column: 24
        },
        end: {
          line: 319,
          column: 26
        }
      },
      "218": {
        start: {
          line: 320,
          column: 24
        },
        end: {
          line: 340,
          column: 37
        }
      },
      "219": {
        start: {
          line: 320,
          column: 87
        },
        end: {
          line: 340,
          column: 31
        }
      },
      "220": {
        start: {
          line: 322,
          column: 32
        },
        end: {
          line: 339,
          column: 35
        }
      },
      "221": {
        start: {
          line: 323,
          column: 36
        },
        end: {
          line: 338,
          column: 37
        }
      },
      "222": {
        start: {
          line: 325,
          column: 44
        },
        end: {
          line: 325,
          column: 70
        }
      },
      "223": {
        start: {
          line: 326,
          column: 44
        },
        end: {
          line: 326,
          column: 103
        }
      },
      "224": {
        start: {
          line: 327,
          column: 44
        },
        end: {
          line: 329,
          column: 45
        }
      },
      "225": {
        start: {
          line: 328,
          column: 48
        },
        end: {
          line: 328,
          column: 141
        }
      },
      "226": {
        start: {
          line: 330,
          column: 44
        },
        end: {
          line: 330,
          column: 102
        }
      },
      "227": {
        start: {
          line: 332,
          column: 44
        },
        end: {
          line: 332,
          column: 62
        }
      },
      "228": {
        start: {
          line: 333,
          column: 44
        },
        end: {
          line: 333,
          column: 98
        }
      },
      "229": {
        start: {
          line: 335,
          column: 44
        },
        end: {
          line: 335,
          column: 64
        }
      },
      "230": {
        start: {
          line: 336,
          column: 44
        },
        end: {
          line: 336,
          column: 134
        }
      },
      "231": {
        start: {
          line: 337,
          column: 48
        },
        end: {
          line: 337,
          column: 70
        }
      },
      "232": {
        start: {
          line: 342,
          column: 24
        },
        end: {
          line: 342,
          column: 47
        }
      },
      "233": {
        start: {
          line: 343,
          column: 24
        },
        end: {
          line: 343,
          column: 110
        }
      },
      "234": {
        start: {
          line: 343,
          column: 80
        },
        end: {
          line: 343,
          column: 105
        }
      },
      "235": {
        start: {
          line: 345,
          column: 24
        },
        end: {
          line: 345,
          column: 44
        }
      },
      "236": {
        start: {
          line: 346,
          column: 24
        },
        end: {
          line: 346,
          column: 77
        }
      },
      "237": {
        start: {
          line: 347,
          column: 24
        },
        end: {
          line: 347,
          column: 50
        }
      },
      "238": {
        start: {
          line: 348,
          column: 28
        },
        end: {
          line: 348,
          column: 50
        }
      },
      "239": {
        start: {
          line: 354,
          column: 4
        },
        end: {
          line: 375,
          column: 6
        }
      },
      "240": {
        start: {
          line: 355,
          column: 8
        },
        end: {
          line: 374,
          column: 11
        }
      },
      "241": {
        start: {
          line: 356,
          column: 12
        },
        end: {
          line: 373,
          column: 15
        }
      },
      "242": {
        start: {
          line: 357,
          column: 16
        },
        end: {
          line: 371,
          column: 17
        }
      },
      "243": {
        start: {
          line: 360,
          column: 20
        },
        end: {
          line: 365,
          column: 27
        }
      },
      "244": {
        start: {
          line: 368,
          column: 20
        },
        end: {
          line: 370,
          column: 27
        }
      },
      "245": {
        start: {
          line: 372,
          column: 16
        },
        end: {
          line: 372,
          column: 38
        }
      },
      "246": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 419,
          column: 6
        }
      },
      "247": {
        start: {
          line: 378,
          column: 8
        },
        end: {
          line: 418,
          column: 11
        }
      },
      "248": {
        start: {
          line: 380,
          column: 12
        },
        end: {
          line: 417,
          column: 15
        }
      },
      "249": {
        start: {
          line: 381,
          column: 16
        },
        end: {
          line: 416,
          column: 17
        }
      },
      "250": {
        start: {
          line: 383,
          column: 24
        },
        end: {
          line: 383,
          column: 45
        }
      },
      "251": {
        start: {
          line: 384,
          column: 24
        },
        end: {
          line: 384,
          column: 37
        }
      },
      "252": {
        start: {
          line: 386,
          column: 24
        },
        end: {
          line: 386,
          column: 50
        }
      },
      "253": {
        start: {
          line: 387,
          column: 24
        },
        end: {
          line: 387,
          column: 70
        }
      },
      "254": {
        start: {
          line: 389,
          column: 24
        },
        end: {
          line: 389,
          column: 42
        }
      },
      "255": {
        start: {
          line: 391,
          column: 24
        },
        end: {
          line: 393,
          column: 25
        }
      },
      "256": {
        start: {
          line: 392,
          column: 28
        },
        end: {
          line: 392,
          column: 132
        }
      },
      "257": {
        start: {
          line: 394,
          column: 24
        },
        end: {
          line: 396,
          column: 25
        }
      },
      "258": {
        start: {
          line: 395,
          column: 28
        },
        end: {
          line: 395,
          column: 123
        }
      },
      "259": {
        start: {
          line: 398,
          column: 24
        },
        end: {
          line: 400,
          column: 25
        }
      },
      "260": {
        start: {
          line: 399,
          column: 28
        },
        end: {
          line: 399,
          column: 138
        }
      },
      "261": {
        start: {
          line: 401,
          column: 24
        },
        end: {
          line: 403,
          column: 25
        }
      },
      "262": {
        start: {
          line: 402,
          column: 28
        },
        end: {
          line: 402,
          column: 135
        }
      },
      "263": {
        start: {
          line: 404,
          column: 24
        },
        end: {
          line: 406,
          column: 25
        }
      },
      "264": {
        start: {
          line: 405,
          column: 28
        },
        end: {
          line: 405,
          column: 139
        }
      },
      "265": {
        start: {
          line: 408,
          column: 24
        },
        end: {
          line: 408,
          column: 125
        }
      },
      "266": {
        start: {
          line: 409,
          column: 24
        },
        end: {
          line: 409,
          column: 129
        }
      },
      "267": {
        start: {
          line: 410,
          column: 24
        },
        end: {
          line: 410,
          column: 118
        }
      },
      "268": {
        start: {
          line: 411,
          column: 24
        },
        end: {
          line: 411,
          column: 63
        }
      },
      "269": {
        start: {
          line: 413,
          column: 24
        },
        end: {
          line: 413,
          column: 44
        }
      },
      "270": {
        start: {
          line: 414,
          column: 24
        },
        end: {
          line: 414,
          column: 114
        }
      },
      "271": {
        start: {
          line: 415,
          column: 28
        },
        end: {
          line: 415,
          column: 50
        }
      },
      "272": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 423,
          column: 6
        }
      },
      "273": {
        start: {
          line: 422,
          column: 8
        },
        end: {
          line: 422,
          column: 31
        }
      },
      "274": {
        start: {
          line: 425,
          column: 4
        },
        end: {
          line: 428,
          column: 6
        }
      },
      "275": {
        start: {
          line: 426,
          column: 8
        },
        end: {
          line: 426,
          column: 45
        }
      },
      "276": {
        start: {
          line: 426,
          column: 32
        },
        end: {
          line: 426,
          column: 43
        }
      },
      "277": {
        start: {
          line: 427,
          column: 8
        },
        end: {
          line: 427,
          column: 47
        }
      },
      "278": {
        start: {
          line: 429,
          column: 4
        },
        end: {
          line: 429,
          column: 39
        }
      },
      "279": {
        start: {
          line: 432,
          column: 0
        },
        end: {
          line: 432,
          column: 59
        }
      },
      "280": {
        start: {
          line: 435,
          column: 16
        },
        end: {
          line: 435,
          column: 20
        }
      },
      "281": {
        start: {
          line: 436,
          column: 4
        },
        end: {
          line: 453,
          column: 11
        }
      },
      "282": {
        start: {
          line: 436,
          column: 52
        },
        end: {
          line: 453,
          column: 7
        }
      },
      "283": {
        start: {
          line: 438,
          column: 8
        },
        end: {
          line: 452,
          column: 11
        }
      },
      "284": {
        start: {
          line: 439,
          column: 12
        },
        end: {
          line: 451,
          column: 13
        }
      },
      "285": {
        start: {
          line: 441,
          column: 20
        },
        end: {
          line: 441,
          column: 39
        }
      },
      "286": {
        start: {
          line: 442,
          column: 20
        },
        end: {
          line: 442,
          column: 55
        }
      },
      "287": {
        start: {
          line: 444,
          column: 20
        },
        end: {
          line: 444,
          column: 39
        }
      },
      "288": {
        start: {
          line: 445,
          column: 20
        },
        end: {
          line: 445,
          column: 50
        }
      },
      "289": {
        start: {
          line: 446,
          column: 20
        },
        end: {
          line: 446,
          column: 83
        }
      },
      "290": {
        start: {
          line: 447,
          column: 20
        },
        end: {
          line: 447,
          column: 97
        }
      },
      "291": {
        start: {
          line: 449,
          column: 20
        },
        end: {
          line: 449,
          column: 30
        }
      },
      "292": {
        start: {
          line: 450,
          column: 20
        },
        end: {
          line: 450,
          column: 50
        }
      },
      "293": {
        start: {
          line: 455,
          column: 0
        },
        end: {
          line: 455,
          column: 41
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 66
          },
          end: {
            line: 2,
            column: 67
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 5,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 17,
            column: 45
          }
        },
        loc: {
          start: {
            line: 17,
            column: 89
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 17
      },
      "4": {
        name: "adopt",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 18
          }
        },
        loc: {
          start: {
            line: 18,
            column: 26
          },
          end: {
            line: 18,
            column: 112
          }
        },
        line: 18
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 71
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 18,
            column: 108
          }
        },
        line: 18
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 19,
            column: 36
          },
          end: {
            line: 19,
            column: 37
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 24,
            column: 5
          }
        },
        line: 19
      },
      "7": {
        name: "fulfilled",
        decl: {
          start: {
            line: 20,
            column: 17
          },
          end: {
            line: 20,
            column: 26
          }
        },
        loc: {
          start: {
            line: 20,
            column: 34
          },
          end: {
            line: 20,
            column: 99
          }
        },
        line: 20
      },
      "8": {
        name: "rejected",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 25
          }
        },
        loc: {
          start: {
            line: 21,
            column: 33
          },
          end: {
            line: 21,
            column: 102
          }
        },
        line: 21
      },
      "9": {
        name: "step",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 21
          }
        },
        loc: {
          start: {
            line: 22,
            column: 30
          },
          end: {
            line: 22,
            column: 118
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 26,
            column: 49
          }
        },
        loc: {
          start: {
            line: 26,
            column: 73
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 26
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 27,
            column: 30
          },
          end: {
            line: 27,
            column: 31
          }
        },
        loc: {
          start: {
            line: 27,
            column: 41
          },
          end: {
            line: 27,
            column: 83
          }
        },
        line: 27
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 28,
            column: 128
          },
          end: {
            line: 28,
            column: 129
          }
        },
        loc: {
          start: {
            line: 28,
            column: 139
          },
          end: {
            line: 28,
            column: 155
          }
        },
        line: 28
      },
      "13": {
        name: "verb",
        decl: {
          start: {
            line: 29,
            column: 13
          },
          end: {
            line: 29,
            column: 17
          }
        },
        loc: {
          start: {
            line: 29,
            column: 21
          },
          end: {
            line: 29,
            column: 70
          }
        },
        line: 29
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 29,
            column: 31
          }
        },
        loc: {
          start: {
            line: 29,
            column: 43
          },
          end: {
            line: 29,
            column: 67
          }
        },
        line: 29
      },
      "15": {
        name: "step",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 22
          },
          end: {
            line: 51,
            column: 5
          }
        },
        line: 30
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 53,
            column: 56
          },
          end: {
            line: 53,
            column: 57
          }
        },
        loc: {
          start: {
            line: 53,
            column: 71
          },
          end: {
            line: 55,
            column: 1
          }
        },
        line: 53
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 62,
            column: 49
          },
          end: {
            line: 62,
            column: 50
          }
        },
        loc: {
          start: {
            line: 62,
            column: 61
          },
          end: {
            line: 430,
            column: 1
          }
        },
        line: 62
      },
      "18": {
        name: "DatabaseOptimizationService",
        decl: {
          start: {
            line: 63,
            column: 13
          },
          end: {
            line: 63,
            column: 40
          }
        },
        loc: {
          start: {
            line: 63,
            column: 43
          },
          end: {
            line: 66,
            column: 5
          }
        },
        line: 63
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 68,
            column: 68
          },
          end: {
            line: 68,
            column: 69
          }
        },
        loc: {
          start: {
            line: 68,
            column: 80
          },
          end: {
            line: 121,
            column: 5
          }
        },
        line: 68
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 69,
            column: 48
          },
          end: {
            line: 69,
            column: 49
          }
        },
        loc: {
          start: {
            line: 69,
            column: 60
          },
          end: {
            line: 120,
            column: 9
          }
        },
        line: 69
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 71,
            column: 37
          },
          end: {
            line: 71,
            column: 38
          }
        },
        loc: {
          start: {
            line: 71,
            column: 51
          },
          end: {
            line: 119,
            column: 13
          }
        },
        line: 71
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 85,
            column: 33
          },
          end: {
            line: 85,
            column: 34
          }
        },
        loc: {
          start: {
            line: 85,
            column: 49
          },
          end: {
            line: 85,
            column: 72
          }
        },
        line: 85
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 86,
            column: 36
          },
          end: {
            line: 86,
            column: 37
          }
        },
        loc: {
          start: {
            line: 86,
            column: 52
          },
          end: {
            line: 86,
            column: 105
          }
        },
        line: 86
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 123,
            column: 55
          },
          end: {
            line: 123,
            column: 56
          }
        },
        loc: {
          start: {
            line: 123,
            column: 93
          },
          end: {
            line: 146,
            column: 5
          }
        },
        line: 123
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 124,
            column: 51
          },
          end: {
            line: 124,
            column: 52
          }
        },
        loc: {
          start: {
            line: 124,
            column: 99
          },
          end: {
            line: 145,
            column: 9
          }
        },
        line: 124
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 127,
            column: 37
          },
          end: {
            line: 127,
            column: 38
          }
        },
        loc: {
          start: {
            line: 127,
            column: 51
          },
          end: {
            line: 144,
            column: 13
          }
        },
        line: 127
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 148,
            column: 61
          },
          end: {
            line: 148,
            column: 62
          }
        },
        loc: {
          start: {
            line: 148,
            column: 73
          },
          end: {
            line: 200,
            column: 5
          }
        },
        line: 148
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 149,
            column: 48
          },
          end: {
            line: 149,
            column: 49
          }
        },
        loc: {
          start: {
            line: 149,
            column: 60
          },
          end: {
            line: 199,
            column: 9
          }
        },
        line: 149
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 151,
            column: 37
          },
          end: {
            line: 151,
            column: 38
          }
        },
        loc: {
          start: {
            line: 151,
            column: 51
          },
          end: {
            line: 198,
            column: 13
          }
        },
        line: 151
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 175,
            column: 51
          },
          end: {
            line: 175,
            column: 52
          }
        },
        loc: {
          start: {
            line: 175,
            column: 69
          },
          end: {
            line: 175,
            column: 102
          }
        },
        line: 175
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 178,
            column: 36
          },
          end: {
            line: 178,
            column: 37
          }
        },
        loc: {
          start: {
            line: 178,
            column: 49
          },
          end: {
            line: 178,
            column: 82
          }
        },
        line: 178
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 179,
            column: 34
          },
          end: {
            line: 179,
            column: 35
          }
        },
        loc: {
          start: {
            line: 179,
            column: 50
          },
          end: {
            line: 179,
            column: 95
          }
        },
        line: 179
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 202,
            column: 61
          },
          end: {
            line: 202,
            column: 62
          }
        },
        loc: {
          start: {
            line: 202,
            column: 73
          },
          end: {
            line: 252,
            column: 5
          }
        },
        line: 202
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 203,
            column: 48
          },
          end: {
            line: 203,
            column: 49
          }
        },
        loc: {
          start: {
            line: 203,
            column: 60
          },
          end: {
            line: 251,
            column: 9
          }
        },
        line: 203
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 205,
            column: 37
          },
          end: {
            line: 205,
            column: 38
          }
        },
        loc: {
          start: {
            line: 205,
            column: 51
          },
          end: {
            line: 250,
            column: 13
          }
        },
        line: 205
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 254,
            column: 62
          },
          end: {
            line: 254,
            column: 63
          }
        },
        loc: {
          start: {
            line: 254,
            column: 74
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 254
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 255,
            column: 48
          },
          end: {
            line: 255,
            column: 49
          }
        },
        loc: {
          start: {
            line: 255,
            column: 60
          },
          end: {
            line: 293,
            column: 9
          }
        },
        line: 255
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 257,
            column: 37
          },
          end: {
            line: 257,
            column: 38
          }
        },
        loc: {
          start: {
            line: 257,
            column: 51
          },
          end: {
            line: 292,
            column: 13
          }
        },
        line: 257
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 296,
            column: 58
          },
          end: {
            line: 296,
            column: 59
          }
        },
        loc: {
          start: {
            line: 296,
            column: 70
          },
          end: {
            line: 352,
            column: 5
          }
        },
        line: 296
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 297,
            column: 48
          },
          end: {
            line: 297,
            column: 49
          }
        },
        loc: {
          start: {
            line: 297,
            column: 60
          },
          end: {
            line: 351,
            column: 9
          }
        },
        line: 297
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 300,
            column: 37
          },
          end: {
            line: 300,
            column: 38
          }
        },
        loc: {
          start: {
            line: 300,
            column: 51
          },
          end: {
            line: 350,
            column: 13
          }
        },
        line: 300
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 320,
            column: 68
          },
          end: {
            line: 320,
            column: 69
          }
        },
        loc: {
          start: {
            line: 320,
            column: 85
          },
          end: {
            line: 340,
            column: 33
          }
        },
        line: 320
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 320,
            column: 127
          },
          end: {
            line: 320,
            column: 128
          }
        },
        loc: {
          start: {
            line: 320,
            column: 139
          },
          end: {
            line: 340,
            column: 29
          }
        },
        line: 320
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 322,
            column: 57
          },
          end: {
            line: 322,
            column: 58
          }
        },
        loc: {
          start: {
            line: 322,
            column: 71
          },
          end: {
            line: 339,
            column: 33
          }
        },
        line: 322
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 343,
            column: 62
          },
          end: {
            line: 343,
            column: 63
          }
        },
        loc: {
          start: {
            line: 343,
            column: 78
          },
          end: {
            line: 343,
            column: 107
          }
        },
        line: 343
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 354,
            column: 67
          },
          end: {
            line: 354,
            column: 68
          }
        },
        loc: {
          start: {
            line: 354,
            column: 79
          },
          end: {
            line: 375,
            column: 5
          }
        },
        line: 354
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 355,
            column: 48
          },
          end: {
            line: 355,
            column: 49
          }
        },
        loc: {
          start: {
            line: 355,
            column: 60
          },
          end: {
            line: 374,
            column: 9
          }
        },
        line: 355
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 356,
            column: 37
          },
          end: {
            line: 356,
            column: 38
          }
        },
        loc: {
          start: {
            line: 356,
            column: 51
          },
          end: {
            line: 373,
            column: 13
          }
        },
        line: 356
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 377,
            column: 74
          },
          end: {
            line: 377,
            column: 75
          }
        },
        loc: {
          start: {
            line: 377,
            column: 86
          },
          end: {
            line: 419,
            column: 5
          }
        },
        line: 377
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 378,
            column: 48
          },
          end: {
            line: 378,
            column: 49
          }
        },
        loc: {
          start: {
            line: 378,
            column: 60
          },
          end: {
            line: 418,
            column: 9
          }
        },
        line: 378
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 380,
            column: 37
          },
          end: {
            line: 380,
            column: 38
          }
        },
        loc: {
          start: {
            line: 380,
            column: 51
          },
          end: {
            line: 417,
            column: 13
          }
        },
        line: 380
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 421,
            column: 57
          },
          end: {
            line: 421,
            column: 58
          }
        },
        loc: {
          start: {
            line: 421,
            column: 69
          },
          end: {
            line: 423,
            column: 5
          }
        },
        line: 421
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 425,
            column: 61
          },
          end: {
            line: 425,
            column: 62
          }
        },
        loc: {
          start: {
            line: 425,
            column: 78
          },
          end: {
            line: 428,
            column: 5
          }
        },
        line: 425
      },
      "54": {
        name: "setupQueryTracking",
        decl: {
          start: {
            line: 434,
            column: 9
          },
          end: {
            line: 434,
            column: 27
          }
        },
        loc: {
          start: {
            line: 434,
            column: 30
          },
          end: {
            line: 454,
            column: 1
          }
        },
        line: 434
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 436,
            column: 26
          },
          end: {
            line: 436,
            column: 27
          }
        },
        loc: {
          start: {
            line: 436,
            column: 50
          },
          end: {
            line: 453,
            column: 9
          }
        },
        line: 436
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 436,
            column: 92
          },
          end: {
            line: 436,
            column: 93
          }
        },
        loc: {
          start: {
            line: 436,
            column: 104
          },
          end: {
            line: 453,
            column: 5
          }
        },
        line: 436
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 438,
            column: 33
          },
          end: {
            line: 438,
            column: 34
          }
        },
        loc: {
          start: {
            line: 438,
            column: 47
          },
          end: {
            line: 452,
            column: 9
          }
        },
        line: 438
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 27
          },
          end: {
            line: 5,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 28
          },
          end: {
            line: 2,
            column: 32
          }
        }, {
          start: {
            line: 2,
            column: 36
          },
          end: {
            line: 2,
            column: 61
          }
        }, {
          start: {
            line: 2,
            column: 66
          },
          end: {
            line: 5,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 115
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 115
          }
        }, {
          start: {
            line: 3,
            column: 94
          },
          end: {
            line: 3,
            column: 115
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "5": {
        loc: {
          start: {
            line: 17,
            column: 16
          },
          end: {
            line: 25,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 21
          }
        }, {
          start: {
            line: 17,
            column: 25
          },
          end: {
            line: 17,
            column: 39
          }
        }, {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 25,
            column: 1
          }
        }],
        line: 17
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 35
          },
          end: {
            line: 18,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 56
          },
          end: {
            line: 18,
            column: 61
          }
        }, {
          start: {
            line: 18,
            column: 64
          },
          end: {
            line: 18,
            column: 109
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 17
          }
        }, {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 19,
            column: 33
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 32
          },
          end: {
            line: 22,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 46
          },
          end: {
            line: 22,
            column: 67
          }
        }, {
          start: {
            line: 22,
            column: 70
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 61
          }
        }, {
          start: {
            line: 23,
            column: 65
          },
          end: {
            line: 23,
            column: 67
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 26,
            column: 18
          },
          end: {
            line: 52,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 19
          },
          end: {
            line: 26,
            column: 23
          }
        }, {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 26,
            column: 43
          }
        }, {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 52,
            column: 1
          }
        }],
        line: 26
      },
      "11": {
        loc: {
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 134
          },
          end: {
            line: 27,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 27,
            column: 167
          },
          end: {
            line: 27,
            column: 175
          }
        }, {
          start: {
            line: 27,
            column: 178
          },
          end: {
            line: 27,
            column: 184
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 102
          }
        }, {
          start: {
            line: 28,
            column: 107
          },
          end: {
            line: 28,
            column: 155
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "15": {
        loc: {
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 16
          }
        }, {
          start: {
            line: 32,
            column: 21
          },
          end: {
            line: 32,
            column: 44
          }
        }],
        line: 32
      },
      "16": {
        loc: {
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 33
          }
        }, {
          start: {
            line: 32,
            column: 38
          },
          end: {
            line: 32,
            column: 43
          }
        }],
        line: 32
      },
      "17": {
        loc: {
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "18": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 24
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 125
          }
        }, {
          start: {
            line: 33,
            column: 130
          },
          end: {
            line: 33,
            column: 158
          }
        }],
        line: 33
      },
      "19": {
        loc: {
          start: {
            line: 33,
            column: 33
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 45
          },
          end: {
            line: 33,
            column: 56
          }
        }, {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "20": {
        loc: {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        }, {
          start: {
            line: 33,
            column: 119
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "21": {
        loc: {
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 77
          }
        }, {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 115
          }
        }],
        line: 33
      },
      "22": {
        loc: {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 83
          },
          end: {
            line: 33,
            column: 98
          }
        }, {
          start: {
            line: 33,
            column: 103
          },
          end: {
            line: 33,
            column: 112
          }
        }],
        line: 33
      },
      "23": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "24": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 47,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 36,
            column: 23
          }
        }, {
          start: {
            line: 36,
            column: 24
          },
          end: {
            line: 36,
            column: 46
          }
        }, {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 72
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 46,
            column: 43
          }
        }],
        line: 35
      },
      "25": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "26": {
        loc: {
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 74
          }
        }, {
          start: {
            line: 41,
            column: 79
          },
          end: {
            line: 41,
            column: 90
          }
        }, {
          start: {
            line: 41,
            column: 94
          },
          end: {
            line: 41,
            column: 105
          }
        }],
        line: 41
      },
      "27": {
        loc: {
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 54
          }
        }, {
          start: {
            line: 41,
            column: 58
          },
          end: {
            line: 41,
            column: 73
          }
        }],
        line: 41
      },
      "28": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "29": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 35
          }
        }, {
          start: {
            line: 42,
            column: 40
          },
          end: {
            line: 42,
            column: 42
          }
        }, {
          start: {
            line: 42,
            column: 47
          },
          end: {
            line: 42,
            column: 59
          }
        }, {
          start: {
            line: 42,
            column: 63
          },
          end: {
            line: 42,
            column: 75
          }
        }],
        line: 42
      },
      "30": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "31": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 39
          },
          end: {
            line: 43,
            column: 53
          }
        }],
        line: 43
      },
      "32": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "33": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 25
          }
        }, {
          start: {
            line: 44,
            column: 29
          },
          end: {
            line: 44,
            column: 43
          }
        }],
        line: 44
      },
      "34": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "35": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 52
          },
          end: {
            line: 50,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 60
          },
          end: {
            line: 50,
            column: 65
          }
        }, {
          start: {
            line: 50,
            column: 68
          },
          end: {
            line: 50,
            column: 74
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 53,
            column: 22
          },
          end: {
            line: 55,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 23
          },
          end: {
            line: 53,
            column: 27
          }
        }, {
          start: {
            line: 53,
            column: 31
          },
          end: {
            line: 53,
            column: 51
          }
        }, {
          start: {
            line: 53,
            column: 56
          },
          end: {
            line: 55,
            column: 1
          }
        }],
        line: 53
      },
      "38": {
        loc: {
          start: {
            line: 54,
            column: 11
          },
          end: {
            line: 54,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 54,
            column: 37
          },
          end: {
            line: 54,
            column: 40
          }
        }, {
          start: {
            line: 54,
            column: 43
          },
          end: {
            line: 54,
            column: 61
          }
        }],
        line: 54
      },
      "39": {
        loc: {
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 15
          }
        }, {
          start: {
            line: 54,
            column: 19
          },
          end: {
            line: 54,
            column: 33
          }
        }],
        line: 54
      },
      "40": {
        loc: {
          start: {
            line: 72,
            column: 16
          },
          end: {
            line: 118,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 90,
            column: 37
          }
        }, {
          start: {
            line: 91,
            column: 20
          },
          end: {
            line: 94,
            column: 37
          }
        }, {
          start: {
            line: 95,
            column: 20
          },
          end: {
            line: 97,
            column: 92
          }
        }, {
          start: {
            line: 98,
            column: 20
          },
          end: {
            line: 101,
            column: 48
          }
        }, {
          start: {
            line: 102,
            column: 20
          },
          end: {
            line: 106,
            column: 48
          }
        }, {
          start: {
            line: 107,
            column: 20
          },
          end: {
            line: 109,
            column: 48
          }
        }, {
          start: {
            line: 110,
            column: 20
          },
          end: {
            line: 110,
            column: 275
          }
        }, {
          start: {
            line: 111,
            column: 20
          },
          end: {
            line: 116,
            column: 31
          }
        }, {
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 117,
            column: 50
          }
        }],
        line: 72
      },
      "41": {
        loc: {
          start: {
            line: 76,
            column: 24
          },
          end: {
            line: 81,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 76,
            column: 24
          },
          end: {
            line: 81,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 76
      },
      "42": {
        loc: {
          start: {
            line: 86,
            column: 61
          },
          end: {
            line: 86,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 61
          },
          end: {
            line: 86,
            column: 76
          }
        }, {
          start: {
            line: 86,
            column: 80
          },
          end: {
            line: 86,
            column: 102
          }
        }],
        line: 86
      },
      "43": {
        loc: {
          start: {
            line: 92,
            column: 24
          },
          end: {
            line: 92,
            column: 81
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 24
          },
          end: {
            line: 92,
            column: 81
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "44": {
        loc: {
          start: {
            line: 104,
            column: 35
          },
          end: {
            line: 104,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 104,
            column: 62
          },
          end: {
            line: 104,
            column: 77
          }
        }, {
          start: {
            line: 104,
            column: 80
          },
          end: {
            line: 104,
            column: 95
          }
        }],
        line: 104
      },
      "45": {
        loc: {
          start: {
            line: 110,
            column: 164
          },
          end: {
            line: 110,
            column: 226
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 110,
            column: 184
          },
          end: {
            line: 110,
            column: 221
          }
        }, {
          start: {
            line: 110,
            column: 224
          },
          end: {
            line: 110,
            column: 226
          }
        }],
        line: 110
      },
      "46": {
        loc: {
          start: {
            line: 110,
            column: 232
          },
          end: {
            line: 110,
            column: 271
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 110,
            column: 232
          },
          end: {
            line: 110,
            column: 249
          }
        }, {
          start: {
            line: 110,
            column: 253
          },
          end: {
            line: 110,
            column: 271
          }
        }],
        line: 110
      },
      "47": {
        loc: {
          start: {
            line: 115,
            column: 76
          },
          end: {
            line: 115,
            column: 136
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 115,
            column: 103
          },
          end: {
            line: 115,
            column: 118
          }
        }, {
          start: {
            line: 115,
            column: 121
          },
          end: {
            line: 115,
            column: 136
          }
        }],
        line: 115
      },
      "48": {
        loc: {
          start: {
            line: 126,
            column: 12
          },
          end: {
            line: 126,
            column: 62
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 12
          },
          end: {
            line: 126,
            column: 62
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "49": {
        loc: {
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 138,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 138,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "50": {
        loc: {
          start: {
            line: 140,
            column: 16
          },
          end: {
            line: 142,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 16
          },
          end: {
            line: 142,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "51": {
        loc: {
          start: {
            line: 152,
            column: 16
          },
          end: {
            line: 197,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 153,
            column: 20
          },
          end: {
            line: 155,
            column: 37
          }
        }, {
          start: {
            line: 156,
            column: 20
          },
          end: {
            line: 166,
            column: 32
          }
        }, {
          start: {
            line: 167,
            column: 20
          },
          end: {
            line: 170,
            column: 93
          }
        }, {
          start: {
            line: 171,
            column: 20
          },
          end: {
            line: 191,
            column: 31
          }
        }, {
          start: {
            line: 192,
            column: 20
          },
          end: {
            line: 195,
            column: 38
          }
        }, {
          start: {
            line: 196,
            column: 20
          },
          end: {
            line: 196,
            column: 50
          }
        }],
        line: 152
      },
      "52": {
        loc: {
          start: {
            line: 174,
            column: 39
          },
          end: {
            line: 176,
            column: 31
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 175,
            column: 30
          },
          end: {
            line: 175,
            column: 129
          }
        }, {
          start: {
            line: 176,
            column: 30
          },
          end: {
            line: 176,
            column: 31
          }
        }],
        line: 174
      },
      "53": {
        loc: {
          start: {
            line: 206,
            column: 16
          },
          end: {
            line: 249,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 207,
            column: 20
          },
          end: {
            line: 212,
            column: 164
          }
        }, {
          start: {
            line: 213,
            column: 20
          },
          end: {
            line: 216,
            column: 178
          }
        }, {
          start: {
            line: 217,
            column: 20
          },
          end: {
            line: 222,
            column: 31
          }
        }, {
          start: {
            line: 223,
            column: 20
          },
          end: {
            line: 226,
            column: 164
          }
        }, {
          start: {
            line: 227,
            column: 20
          },
          end: {
            line: 230,
            column: 162
          }
        }, {
          start: {
            line: 231,
            column: 20
          },
          end: {
            line: 236,
            column: 31
          }
        }, {
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 240,
            column: 27
          }
        }, {
          start: {
            line: 241,
            column: 20
          },
          end: {
            line: 241,
            column: 52
          }
        }, {
          start: {
            line: 242,
            column: 20
          },
          end: {
            line: 247,
            column: 31
          }
        }, {
          start: {
            line: 248,
            column: 20
          },
          end: {
            line: 248,
            column: 50
          }
        }],
        line: 206
      },
      "54": {
        loc: {
          start: {
            line: 209,
            column: 38
          },
          end: {
            line: 209,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 38
          },
          end: {
            line: 209,
            column: 62
          }
        }, {
          start: {
            line: 209,
            column: 66
          },
          end: {
            line: 209,
            column: 68
          }
        }],
        line: 209
      },
      "55": {
        loc: {
          start: {
            line: 210,
            column: 24
          },
          end: {
            line: 210,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 24
          },
          end: {
            line: 210,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "56": {
        loc: {
          start: {
            line: 212,
            column: 74
          },
          end: {
            line: 212,
            column: 161
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 74
          },
          end: {
            line: 212,
            column: 90
          }
        }, {
          start: {
            line: 212,
            column: 95
          },
          end: {
            line: 212,
            column: 160
          }
        }],
        line: 212
      },
      "57": {
        loc: {
          start: {
            line: 216,
            column: 74
          },
          end: {
            line: 216,
            column: 175
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 74
          },
          end: {
            line: 216,
            column: 90
          }
        }, {
          start: {
            line: 216,
            column: 95
          },
          end: {
            line: 216,
            column: 174
          }
        }],
        line: 216
      },
      "58": {
        loc: {
          start: {
            line: 224,
            column: 24
          },
          end: {
            line: 224,
            column: 85
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 24
          },
          end: {
            line: 224,
            column: 85
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "59": {
        loc: {
          start: {
            line: 226,
            column: 74
          },
          end: {
            line: 226,
            column: 161
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 74
          },
          end: {
            line: 226,
            column: 90
          }
        }, {
          start: {
            line: 226,
            column: 95
          },
          end: {
            line: 226,
            column: 160
          }
        }],
        line: 226
      },
      "60": {
        loc: {
          start: {
            line: 230,
            column: 74
          },
          end: {
            line: 230,
            column: 159
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 230,
            column: 74
          },
          end: {
            line: 230,
            column: 90
          }
        }, {
          start: {
            line: 230,
            column: 95
          },
          end: {
            line: 230,
            column: 158
          }
        }],
        line: 230
      },
      "61": {
        loc: {
          start: {
            line: 246,
            column: 81
          },
          end: {
            line: 246,
            column: 141
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 246,
            column: 108
          },
          end: {
            line: 246,
            column: 123
          }
        }, {
          start: {
            line: 246,
            column: 126
          },
          end: {
            line: 246,
            column: 141
          }
        }],
        line: 246
      },
      "62": {
        loc: {
          start: {
            line: 258,
            column: 16
          },
          end: {
            line: 291,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 259,
            column: 20
          },
          end: {
            line: 263,
            column: 690
          }
        }, {
          start: {
            line: 264,
            column: 20
          },
          end: {
            line: 269,
            column: 31
          }
        }, {
          start: {
            line: 270,
            column: 20
          },
          end: {
            line: 272,
            column: 484
          }
        }, {
          start: {
            line: 273,
            column: 20
          },
          end: {
            line: 278,
            column: 31
          }
        }, {
          start: {
            line: 279,
            column: 20
          },
          end: {
            line: 282,
            column: 27
          }
        }, {
          start: {
            line: 283,
            column: 20
          },
          end: {
            line: 283,
            column: 52
          }
        }, {
          start: {
            line: 284,
            column: 20
          },
          end: {
            line: 289,
            column: 31
          }
        }, {
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 290,
            column: 50
          }
        }],
        line: 258
      },
      "63": {
        loc: {
          start: {
            line: 261,
            column: 38
          },
          end: {
            line: 261,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 38
          },
          end: {
            line: 261,
            column: 62
          }
        }, {
          start: {
            line: 261,
            column: 66
          },
          end: {
            line: 261,
            column: 68
          }
        }],
        line: 261
      },
      "64": {
        loc: {
          start: {
            line: 262,
            column: 24
          },
          end: {
            line: 262,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 262,
            column: 24
          },
          end: {
            line: 262,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 262
      },
      "65": {
        loc: {
          start: {
            line: 263,
            column: 72
          },
          end: {
            line: 263,
            column: 687
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 72
          },
          end: {
            line: 263,
            column: 88
          }
        }, {
          start: {
            line: 263,
            column: 93
          },
          end: {
            line: 263,
            column: 686
          }
        }],
        line: 263
      },
      "66": {
        loc: {
          start: {
            line: 271,
            column: 24
          },
          end: {
            line: 271,
            column: 85
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 24
          },
          end: {
            line: 271,
            column: 85
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "67": {
        loc: {
          start: {
            line: 272,
            column: 72
          },
          end: {
            line: 272,
            column: 481
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 272,
            column: 72
          },
          end: {
            line: 272,
            column: 88
          }
        }, {
          start: {
            line: 272,
            column: 93
          },
          end: {
            line: 272,
            column: 480
          }
        }],
        line: 272
      },
      "68": {
        loc: {
          start: {
            line: 288,
            column: 39
          },
          end: {
            line: 288,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 288,
            column: 66
          },
          end: {
            line: 288,
            column: 81
          }
        }, {
          start: {
            line: 288,
            column: 84
          },
          end: {
            line: 288,
            column: 99
          }
        }],
        line: 288
      },
      "69": {
        loc: {
          start: {
            line: 301,
            column: 16
          },
          end: {
            line: 349,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 302,
            column: 20
          },
          end: {
            line: 340,
            column: 37
          }
        }, {
          start: {
            line: 341,
            column: 20
          },
          end: {
            line: 343,
            column: 110
          }
        }, {
          start: {
            line: 344,
            column: 20
          },
          end: {
            line: 347,
            column: 50
          }
        }, {
          start: {
            line: 348,
            column: 20
          },
          end: {
            line: 348,
            column: 50
          }
        }],
        line: 301
      },
      "70": {
        loc: {
          start: {
            line: 323,
            column: 36
          },
          end: {
            line: 338,
            column: 37
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 324,
            column: 40
          },
          end: {
            line: 330,
            column: 102
          }
        }, {
          start: {
            line: 331,
            column: 40
          },
          end: {
            line: 333,
            column: 98
          }
        }, {
          start: {
            line: 334,
            column: 40
          },
          end: {
            line: 336,
            column: 134
          }
        }, {
          start: {
            line: 337,
            column: 40
          },
          end: {
            line: 337,
            column: 70
          }
        }],
        line: 323
      },
      "71": {
        loc: {
          start: {
            line: 327,
            column: 44
          },
          end: {
            line: 329,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 44
          },
          end: {
            line: 329,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "72": {
        loc: {
          start: {
            line: 362,
            column: 44
          },
          end: {
            line: 362,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 362,
            column: 44
          },
          end: {
            line: 362,
            column: 85
          }
        }, {
          start: {
            line: 362,
            column: 89
          },
          end: {
            line: 362,
            column: 98
          }
        }],
        line: 362
      },
      "73": {
        loc: {
          start: {
            line: 363,
            column: 47
          },
          end: {
            line: 363,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 363,
            column: 47
          },
          end: {
            line: 363,
            column: 81
          }
        }, {
          start: {
            line: 363,
            column: 85
          },
          end: {
            line: 363,
            column: 94
          }
        }],
        line: 363
      },
      "74": {
        loc: {
          start: {
            line: 369,
            column: 35
          },
          end: {
            line: 369,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 369,
            column: 60
          },
          end: {
            line: 369,
            column: 73
          }
        }, {
          start: {
            line: 369,
            column: 76
          },
          end: {
            line: 369,
            column: 91
          }
        }],
        line: 369
      },
      "75": {
        loc: {
          start: {
            line: 381,
            column: 16
          },
          end: {
            line: 416,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 382,
            column: 20
          },
          end: {
            line: 384,
            column: 37
          }
        }, {
          start: {
            line: 385,
            column: 20
          },
          end: {
            line: 387,
            column: 70
          }
        }, {
          start: {
            line: 388,
            column: 20
          },
          end: {
            line: 411,
            column: 63
          }
        }, {
          start: {
            line: 412,
            column: 20
          },
          end: {
            line: 414,
            column: 114
          }
        }, {
          start: {
            line: 415,
            column: 20
          },
          end: {
            line: 415,
            column: 50
          }
        }],
        line: 381
      },
      "76": {
        loc: {
          start: {
            line: 391,
            column: 24
          },
          end: {
            line: 393,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 391,
            column: 24
          },
          end: {
            line: 393,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 391
      },
      "77": {
        loc: {
          start: {
            line: 394,
            column: 24
          },
          end: {
            line: 396,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 394,
            column: 24
          },
          end: {
            line: 396,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 394
      },
      "78": {
        loc: {
          start: {
            line: 398,
            column: 24
          },
          end: {
            line: 400,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 398,
            column: 24
          },
          end: {
            line: 400,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 398
      },
      "79": {
        loc: {
          start: {
            line: 401,
            column: 24
          },
          end: {
            line: 403,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 401,
            column: 24
          },
          end: {
            line: 403,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 401
      },
      "80": {
        loc: {
          start: {
            line: 404,
            column: 24
          },
          end: {
            line: 406,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 24
          },
          end: {
            line: 406,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "81": {
        loc: {
          start: {
            line: 426,
            column: 8
          },
          end: {
            line: 426,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 426,
            column: 8
          },
          end: {
            line: 426,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 426
      },
      "82": {
        loc: {
          start: {
            line: 439,
            column: 12
          },
          end: {
            line: 451,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 440,
            column: 16
          },
          end: {
            line: 442,
            column: 55
          }
        }, {
          start: {
            line: 443,
            column: 16
          },
          end: {
            line: 447,
            column: 97
          }
        }, {
          start: {
            line: 448,
            column: 16
          },
          end: {
            line: 450,
            column: 50
          }
        }],
        line: 439
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0, 0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0, 0, 0, 0, 0],
      "52": [0, 0],
      "53": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0, 0, 0, 0, 0, 0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0, 0, 0],
      "70": [0, 0, 0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0, 0, 0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/databaseOptimization.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2WA,gDAYC;AAvXD,wDAAkC;AAClC,0CAAoB;AACpB,8CAAwB;AAqBxB;IAAA;QACU,iBAAY,GAA8B,EAAE,CAAC;QAC7C,sBAAiB,GAAG,IAAI,CAAC;IA4UnC,CAAC;IA1UC,4BAA4B;IACtB,6DAAuB,GAA7B;uCAAiC,OAAO;;;;;;wBAE9B,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,6BAA6B,CAAC,CAAC;wBAEpG,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;4BAChC,sBAAO;oCACL,OAAO,EAAE,KAAK;oCACd,OAAO,EAAE,wCAAwC;iCAClD,EAAC;wBACJ,CAAC;wBAEK,UAAU,GAAG,YAAE,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;wBACnD,UAAU,GAAG,UAAU;6BAC1B,KAAK,CAAC,GAAG,CAAC;6BACV,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,EAAE,EAAX,CAAW,CAAC;6BACxB,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAzC,CAAyC,CAAC,CAAC;wBAEvD,MAAM,GAAa,EAAE,CAAC;wBACxB,YAAY,GAAG,CAAC,CAAC;8BAEa,EAAV,yBAAU;;;6BAAV,CAAA,wBAAU,CAAA;wBAAvB,SAAS;;;;wBAEhB,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAA;;wBAAzC,SAAyC,CAAC;wBAC1C,YAAY,EAAE,CAAC;;;;wBAET,QAAQ,GAAG,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;wBAC1E,MAAM,CAAC,IAAI,CAAC,6BAAsB,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,mBAAS,QAAQ,CAAE,CAAC,CAAC;;;wBAN9D,IAAU,CAAA;;4BAUlC,iCACE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAC5B,OAAO,EAAE,kBAAW,YAAY,kCAAwB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAK,MAAM,CAAC,MAAM,YAAS,CAAC,CAAC,CAAC,EAAE,CAAE,IAC3G,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,QAAA,EAAE,CAAC,GACpC;;;wBAGF,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,OAAO,EAAE,mCAA4B,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAE;6BAChG,EAAC;;;;;KAEL;IAED,4BAA4B;IACtB,gDAAU,GAAhB;0CAAoF,OAAO,YAA1E,KAAa,EAAE,eAAuB,EAAE,YAAwB;;YAAxB,6BAAA,EAAA,gBAAwB;;gBACzE,MAAM,GAA4B;oBACtC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,wBAAwB;oBACxD,aAAa,EAAE,eAAe;oBAC9B,YAAY,cAAA;oBACZ,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAE/B,2BAA2B;gBAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACvE,CAAC;gBAED,mBAAmB;gBACnB,IAAI,eAAe,GAAG,IAAI,EAAE,CAAC,CAAC,oCAAoC;oBAChE,OAAO,CAAC,IAAI,CAAC,+BAAwB,eAAe,SAAM,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvF,CAAC;;;;KACF;IAED,0BAA0B;IACpB,sDAAgB,GAAtB;uCAA0B,OAAO;;;;;wBACzB,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAWvB,qBAAM,OAAO,CAAC,GAAG,CAAC;gCACpB,gBAAM,CAAC,IAAI,CAAC,KAAK,EAAE;gCACnB,gBAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC;gCAC5D,gBAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC;gCACxD,gBAAM,CAAC,UAAU,CAAC,KAAK,EAAE;gCACzB,gBAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC;gCACtD,gBAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC;gCAC5F,gBAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC;6BAClE,CAAC,EAAA;;wBAhBI,KAQF,SAQF,EAfA,UAAU,QAAA,EACV,sBAAsB,QAAA,EACtB,kBAAkB,QAAA,EAClB,gBAAgB,QAAA,EAChB,eAAe,QAAA,EACf,iBAAiB,QAAA,EACjB,sBAAsB,QAAA;wBAWlB,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBACzC,qBAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,SAAS,CAAC,EAAA;;wBAApD,SAAoD,CAAC;wBAG/C,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC9C,YAAY,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC;4BAC3C,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,aAAa,EAArB,CAAqB,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM;4BACnF,CAAC,CAAC,CAAC,CAAC;wBAGA,WAAW,GAAG,aAAa;6BAC9B,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,GAAG,GAAG,EAArB,CAAqB,CAAC;6BAClC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,EAAjC,CAAiC,CAAC;6BACjD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;wBAEhB,sBAAO;gCACL,UAAU,YAAA;gCACV,sBAAsB,wBAAA;gCACtB,kBAAkB,oBAAA;gCAClB,gBAAgB,kBAAA;gCAChB,eAAe,iBAAA;gCACf,iBAAiB,mBAAA;gCACjB,sBAAsB,wBAAA;gCACtB,YAAY,cAAA;gCACZ,WAAW,aAAA;6BACZ,EAAC;;;wBAGF,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,OAAK,CAAC,CAAC;wBACtD,MAAM,OAAK,CAAC;;;;;KAEf;IAED,wEAAwE;IAClE,sDAAgB,GAAtB;uCAA0B,OAAO;;;;;;wBAOvB,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;6BAE/C,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAlC,wBAAkC;wBACpC,0BAA0B;wBAC1B,qBAAM,gBAAM,CAAC,WAAW,4EAAA,SAAS,MAAA;;wBADjC,0BAA0B;wBAC1B,SAAiC,CAAC;wBAClC,qBAAM,gBAAM,CAAC,WAAW,mFAAA,gBAAgB,MAAA;;wBAAxC,SAAwC,CAAC;wBACzC,sBAAO;gCACL,OAAO,EAAE,IAAI;gCACb,OAAO,EAAE,4CAA4C;6BACtD,EAAC;;6BACO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAA9B,wBAA8B;wBACvC,sBAAsB;wBACtB,qBAAM,gBAAM,CAAC,WAAW,4EAAA,SAAS,MAAA;;wBADjC,sBAAsB;wBACtB,SAAiC,CAAC;wBAClC,qBAAM,gBAAM,CAAC,WAAW,2EAAA,QAAQ,MAAA;;wBAAhC,SAAgC,CAAC;wBACjC,sBAAO;gCACL,OAAO,EAAE,IAAI;gCACb,OAAO,EAAE,wCAAwC;6BAClD,EAAC;4BAEF,sBAAO;4BACL,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE,4DAA4D;yBACtE,EAAC;;;;wBAIJ,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,OAAO,EAAE,wCAAiC,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAE;6BACrG,EAAC;;;;;KAEL;IAED,sCAAsC;IAChC,uDAAiB,GAAvB;uCAA2B,OAAO;;;;;;wBAExB,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;6BAE/C,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAlC,wBAAkC;wBAEjB,qBAAM,gBAAM,CAAC,SAAS,oVAAA,iRAWxC,MAAA;;wBAXK,UAAU,GAAG,SAWlB;wBAED,sBAAO;gCACL,IAAI,EAAE,YAAY;gCAClB,UAAU,YAAA;6BACX,EAAC;;6BACO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAA9B,wBAA8B;wBAGvB,qBAAM,gBAAM,CAAC,SAAS,6OAAA,0KAMrC,MAAA;;wBANK,OAAO,GAAG,SAMf;wBAED,sBAAO;gCACL,IAAI,EAAE,QAAQ;gCACd,OAAO,SAAA;6BACR,EAAC;4BAEF,sBAAO;4BACL,IAAI,EAAE,SAAS;4BACf,OAAO,EAAE,qDAAqD;yBAC/D,EAAC;;;;wBAIJ,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,OAAK,CAAC,CAAC;wBACrD,sBAAO;gCACL,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;6BAChE,EAAC;;;;;KAEL;IAED,iCAAiC;IAC3B,mDAAa,GAAnB;uCAAuB,OAAO;;;;;;;wBAEpB,MAAM,GAAG;4BACb,MAAM;4BACN,YAAY;4BACZ,oBAAoB;4BACpB,kBAAkB;4BAClB,sBAAsB;4BACtB,cAAc;4BACd,kBAAkB;4BAClB,0BAA0B;4BAC1B,WAAW;4BACX,cAAc;4BACd,YAAY;4BACZ,OAAO;4BACP,mBAAmB;4BACnB,mBAAmB;yBACpB,CAAC;wBAEiB,qBAAM,OAAO,CAAC,GAAG,CAClC,MAAM,CAAC,GAAG,CAAC,UAAO,KAAK;;;;;;4CAGb,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;4CACjE,IAAI,CAAE,gBAAc,CAAC,SAAS,CAAC,EAAE,CAAC;gDAChC,sBAAO,EAAE,KAAK,OAAA,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAC;4CACxE,CAAC;4CACa,qBAAO,gBAAc,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAA;;4CAAhD,KAAK,GAAG,SAAwC;4CACtD,sBAAO,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,EAAC;;;4CAExB,sBAAO,EAAE,KAAK,OAAA,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAC;;;;iCAEtE,CAAC,CACH,EAAA;;wBAdK,UAAU,GAAG,SAclB;wBAED,sBAAO,UAAU,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAjB,CAAiB,CAAC,EAAC;;;wBAGpD,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,OAAK,CAAC,CAAC;wBACnD,sBAAO,EAAE,EAAC;;;;;KAEb;IAED,6BAA6B;IACvB,4DAAsB,GAA5B;uCAAgC,OAAO;;gBACrC,IAAI,CAAC;oBACH,gFAAgF;oBAChF,8CAA8C;oBAC9C,sBAAO;4BACL,iBAAiB,EAAE,yCAAyC;4BAC5D,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,SAAS;4BACtE,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,SAAS;4BAClE,OAAO,EAAE,sEAAsE;yBAChF,EAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,sBAAO;4BACL,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;yBAChE,EAAC;gBACJ,CAAC;;;;KACF;IAED,8BAA8B;IACxB,mEAA6B,GAAnC;uCAAuC,OAAO;;;;;wBACtC,eAAe,GAAa,EAAE,CAAC;;;;wBAGrB,qBAAM,IAAI,CAAC,gBAAgB,EAAE,EAAA;;wBAArC,KAAK,GAAG,SAA6B;wBAE3C,yBAAyB;wBACzB,IAAI,KAAK,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;4BAC7B,eAAe,CAAC,IAAI,CAAC,iFAAiF,CAAC,CAAC;wBAC1G,CAAC;wBAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACjC,eAAe,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;wBACjG,CAAC;wBAED,oBAAoB;wBACpB,IAAI,KAAK,CAAC,UAAU,GAAG,KAAK,EAAE,CAAC;4BAC7B,eAAe,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;wBAChH,CAAC;wBAED,IAAI,KAAK,CAAC,sBAAsB,GAAG,IAAI,EAAE,CAAC;4BACxC,eAAe,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;wBAC7G,CAAC;wBAED,IAAI,KAAK,CAAC,iBAAiB,GAAG,IAAI,EAAE,CAAC;4BACnC,eAAe,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;wBACjH,CAAC;wBAED,0BAA0B;wBAC1B,eAAe,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;wBACrG,eAAe,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;wBACzG,eAAe,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;wBAE9F,sBAAO,eAAe,EAAC;;;wBAGvB,sBAAO,CAAC,gEAAgE,CAAC,EAAC;;;;;KAE7E;IAED,sBAAsB;IACtB,kDAAY,GAAZ;QACE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IACzB,CAAC;IAED,2BAA2B;IAC3B,sDAAgB,GAAhB,UAAiB,KAAkB;QAAlB,sBAAA,EAAA,UAAkB;QACjC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IACH,kCAAC;AAAD,CAAC,AA9UD,IA8UC;AAED,4BAA4B;AACf,QAAA,cAAc,GAAG,IAAI,2BAA2B,EAAE,CAAC;AAEhE,+CAA+C;AAC/C,SAAgB,kBAAkB;IAAlC,iBAYC;IAXC,gBAAM,CAAC,IAAI,CAAC,UAAO,MAAM,EAAE,IAAI;;;;;oBACvB,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACV,qBAAM,IAAI,CAAC,MAAM,CAAC,EAAA;;oBAA3B,MAAM,GAAG,SAAkB;oBAC3B,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;oBAG9B,SAAS,GAAG,UAAG,MAAM,CAAC,KAAK,cAAI,MAAM,CAAC,MAAM,CAAE,CAAC;oBACrD,qBAAM,sBAAc,CAAC,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAA;;oBAApD,SAAoD,CAAC;oBAErD,sBAAO,MAAM,EAAC;;;SACf,CAAC,CAAC;AACL,CAAC;AAED,kBAAe,sBAAc,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/databaseOptimization.ts"],
      sourcesContent: ["import prisma from '@/lib/prisma';\nimport fs from 'fs';\nimport path from 'path';\n\ninterface QueryPerformanceMetrics {\n  query: string;\n  executionTime: number;\n  rowsAffected: number;\n  timestamp: Date;\n}\n\ninterface DatabaseStats {\n  totalUsers: number;\n  totalLearningResources: number;\n  totalLearningPaths: number;\n  totalAssessments: number;\n  totalForumPosts: number;\n  activeEnrollments: number;\n  completedLearningPaths: number;\n  avgQueryTime: number;\n  slowQueries: QueryPerformanceMetrics[];\n}\n\nclass DatabaseOptimizationService {\n  private queryMetrics: QueryPerformanceMetrics[] = [];\n  private maxMetricsHistory = 1000;\n\n  // Apply performance indexes\n  async applyPerformanceIndexes(): Promise<{ success: boolean; message: string; errors?: string[] }> {\n    try {\n      const indexesPath = path.join(process.cwd(), 'prisma', 'migrations', 'add_performance_indexes.sql');\n      \n      if (!fs.existsSync(indexesPath)) {\n        return {\n          success: false,\n          message: 'Performance indexes SQL file not found'\n        };\n      }\n\n      const indexesSQL = fs.readFileSync(indexesPath, 'utf-8');\n      const statements = indexesSQL\n        .split(';')\n        .map(stmt => stmt.trim())\n        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));\n\n      const errors: string[] = [];\n      let successCount = 0;\n\n      for (const statement of statements) {\n        try {\n          await prisma.$executeRawUnsafe(statement);\n          successCount++;\n        } catch (error) {\n          const errorMsg = error instanceof Error ? error.message : 'Unknown error';\n          errors.push(`Failed to execute: ${statement.substring(0, 100)}... - ${errorMsg}`);\n        }\n      }\n\n      return {\n        success: errors.length === 0,\n        message: `Applied ${successCount} indexes successfully${errors.length > 0 ? `, ${errors.length} failed` : ''}`,\n        ...(errors.length > 0 && { errors })\n      };\n\n    } catch (error) {\n      return {\n        success: false,\n        message: `Failed to apply indexes: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  // Monitor query performance\n  async trackQuery(query: string, executionTimeMs: number, rowsAffected: number = 0): Promise<void> {\n    const metric: QueryPerformanceMetrics = {\n      query: query.substring(0, 500), // Truncate long queries\n      executionTime: executionTimeMs,\n      rowsAffected,\n      timestamp: new Date()\n    };\n\n    this.queryMetrics.push(metric);\n\n    // Keep only recent metrics\n    if (this.queryMetrics.length > this.maxMetricsHistory) {\n      this.queryMetrics = this.queryMetrics.slice(-this.maxMetricsHistory);\n    }\n\n    // Log slow queries\n    if (executionTimeMs > 1000) { // Queries taking more than 1 second\n      console.warn(`Slow query detected (${executionTimeMs}ms):`, query.substring(0, 200));\n    }\n  }\n\n  // Get database statistics\n  async getDatabaseStats(): Promise<DatabaseStats> {\n    const startTime = Date.now();\n\n    try {\n      const [\n        totalUsers,\n        totalLearningResources,\n        totalLearningPaths,\n        totalAssessments,\n        totalForumPosts,\n        activeEnrollments,\n        completedLearningPaths\n      ] = await Promise.all([\n        prisma.user.count(),\n        prisma.learningResource.count({ where: { isActive: true } }),\n        prisma.learningPath.count({ where: { isActive: true } }),\n        prisma.assessment.count(),\n        prisma.forumPost.count({ where: { isHidden: false } }),\n        prisma.userLearningPath.count({ where: { status: { in: ['IN_PROGRESS', 'NOT_STARTED'] } } }),\n        prisma.userLearningPath.count({ where: { status: 'COMPLETED' } })\n      ]);\n\n      const queryTime = Date.now() - startTime;\n      await this.trackQuery('getDatabaseStats', queryTime);\n\n      // Calculate average query time from recent metrics\n      const recentMetrics = this.queryMetrics.slice(-100);\n      const avgQueryTime = recentMetrics.length > 0 \n        ? recentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / recentMetrics.length \n        : 0;\n\n      // Get slow queries from recent metrics\n      const slowQueries = recentMetrics\n        .filter(m => m.executionTime > 500)\n        .sort((a, b) => b.executionTime - a.executionTime)\n        .slice(0, 10);\n\n      return {\n        totalUsers,\n        totalLearningResources,\n        totalLearningPaths,\n        totalAssessments,\n        totalForumPosts,\n        activeEnrollments,\n        completedLearningPaths,\n        avgQueryTime,\n        slowQueries\n      };\n\n    } catch (error) {\n      console.error('Error getting database stats:', error);\n      throw error;\n    }\n  }\n\n  // Optimize database by running ANALYZE and VACUUM (PostgreSQL specific)\n  async optimizeDatabase(): Promise<{ success: boolean; message: string }> {\n    try {\n      // Note: These commands are database-specific\n      // For SQLite, we can use ANALYZE and VACUUM\n      // For PostgreSQL, we can use ANALYZE and VACUUM\n      // For MySQL, we can use ANALYZE TABLE and OPTIMIZE TABLE\n\n      const databaseUrl = process.env.DATABASE_URL || '';\n      \n      if (databaseUrl.includes('postgresql')) {\n        // PostgreSQL optimization\n        await prisma.$executeRaw`ANALYZE`;\n        await prisma.$executeRaw`VACUUM ANALYZE`;\n        return {\n          success: true,\n          message: 'PostgreSQL database optimized successfully'\n        };\n      } else if (databaseUrl.includes('sqlite')) {\n        // SQLite optimization\n        await prisma.$executeRaw`ANALYZE`;\n        await prisma.$executeRaw`VACUUM`;\n        return {\n          success: true,\n          message: 'SQLite database optimized successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Database optimization not supported for this database type'\n        };\n      }\n\n    } catch (error) {\n      return {\n        success: false,\n        message: `Database optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  // Check index usage and effectiveness\n  async analyzeIndexUsage(): Promise<any> {\n    try {\n      const databaseUrl = process.env.DATABASE_URL || '';\n      \n      if (databaseUrl.includes('postgresql')) {\n        // PostgreSQL index usage analysis\n        const indexUsage = await prisma.$queryRaw`\n          SELECT \n            schemaname,\n            tablename,\n            indexname,\n            idx_tup_read,\n            idx_tup_fetch,\n            idx_scan\n          FROM pg_stat_user_indexes \n          ORDER BY idx_scan DESC\n          LIMIT 20\n        `;\n        \n        return {\n          type: 'postgresql',\n          indexUsage\n        };\n      } else if (databaseUrl.includes('sqlite')) {\n        // SQLite doesn't have built-in index usage stats\n        // We can check if indexes exist\n        const indexes = await prisma.$queryRaw`\n          SELECT name, sql \n          FROM sqlite_master \n          WHERE type = 'index' \n          AND name NOT LIKE 'sqlite_%'\n          ORDER BY name\n        `;\n        \n        return {\n          type: 'sqlite',\n          indexes\n        };\n      } else {\n        return {\n          type: 'unknown',\n          message: 'Index analysis not supported for this database type'\n        };\n      }\n\n    } catch (error) {\n      console.error('Error analyzing index usage:', error);\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  // Get table sizes and row counts\n  async getTableSizes(): Promise<any> {\n    try {\n      const tables = [\n        'User',\n        'Assessment',\n        'AssessmentResponse',\n        'LearningResource',\n        'UserLearningProgress',\n        'LearningPath',\n        'UserLearningPath',\n        'UserLearningPathProgress',\n        'ForumPost',\n        'ForumComment',\n        'CareerPath',\n        'Skill',\n        'UserSkillProgress',\n        'LearningAnalytics'\n      ];\n\n      const tableSizes = await Promise.all(\n        tables.map(async (table) => {\n          try {\n            // Check if the model exists in Prisma client\n            const modelName = table.charAt(0).toLowerCase() + table.slice(1);\n            if (!(prisma as any)[modelName]) {\n              return { table, count: 0, error: 'Model not found in Prisma client' };\n            }\n            const count = await (prisma as any)[modelName].count();\n            return { table, count };\n          } catch (error) {\n            return { table, count: 0, error: 'Table not found or accessible' };\n          }\n        })\n      );\n\n      return tableSizes.sort((a, b) => b.count - a.count);\n\n    } catch (error) {\n      console.error('Error getting table sizes:', error);\n      return [];\n    }\n  }\n\n  // Connection pool monitoring\n  async getConnectionPoolStats(): Promise<any> {\n    try {\n      // This would depend on the specific database and connection pool implementation\n      // For now, we'll return basic connection info\n      return {\n        activeConnections: 'N/A - depends on database configuration',\n        maxConnections: process.env.DATABASE_CONNECTION_POOL_SIZE || 'default',\n        connectionTimeout: process.env.DATABASE_QUERY_TIMEOUT || 'default',\n        message: 'Connection pool monitoring requires database-specific implementation'\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  // Performance recommendations\n  async getPerformanceRecommendations(): Promise<string[]> {\n    const recommendations: string[] = [];\n    \n    try {\n      const stats = await this.getDatabaseStats();\n      \n      // Check for slow queries\n      if (stats.avgQueryTime > 500) {\n        recommendations.push('Average query time is high. Consider optimizing slow queries or adding indexes.');\n      }\n      \n      if (stats.slowQueries.length > 5) {\n        recommendations.push('Multiple slow queries detected. Review query patterns and index usage.');\n      }\n      \n      // Check data volume\n      if (stats.totalUsers > 10000) {\n        recommendations.push('Large user base detected. Consider implementing read replicas for better performance.');\n      }\n      \n      if (stats.totalLearningResources > 5000) {\n        recommendations.push('Large learning resource catalog. Consider implementing search indexes and caching.');\n      }\n      \n      if (stats.activeEnrollments > 1000) {\n        recommendations.push('High enrollment activity. Monitor learning path performance and consider optimization.');\n      }\n      \n      // General recommendations\n      recommendations.push('Regularly run database optimization (ANALYZE/VACUUM) for better performance.');\n      recommendations.push('Monitor query performance and add indexes for frequently accessed data patterns.');\n      recommendations.push('Consider implementing caching for frequently accessed read-only data.');\n      \n      return recommendations;\n      \n    } catch (error) {\n      return ['Error generating recommendations. Check database connectivity.'];\n    }\n  }\n\n  // Clear query metrics\n  clearMetrics(): void {\n    this.queryMetrics = [];\n  }\n\n  // Get recent query metrics\n  getRecentMetrics(limit: number = 50): QueryPerformanceMetrics[] {\n    return this.queryMetrics.slice(-limit);\n  }\n}\n\n// Export singleton instance\nexport const dbOptimization = new DatabaseOptimizationService();\n\n// Prisma middleware to track query performance\nexport function setupQueryTracking() {\n  prisma.$use(async (params, next) => {\n    const start = Date.now();\n    const result = await next(params);\n    const duration = Date.now() - start;\n    \n    // Track the query\n    const queryInfo = `${params.model}.${params.action}`;\n    await dbOptimization.trackQuery(queryInfo, duration);\n    \n    return result;\n  });\n}\n\nexport default dbOptimization;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a155c81f0bfb759d0e0a28a359024cfdfe030991"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_p8oku5j5a = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_p8oku5j5a();
var __makeTemplateObject =
/* istanbul ignore next */
(cov_p8oku5j5a().s[0]++,
/* istanbul ignore next */
(cov_p8oku5j5a().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_p8oku5j5a().b[0][1]++, this.__makeTemplateObject) ||
/* istanbul ignore next */
(cov_p8oku5j5a().b[0][2]++, function (cooked, raw) {
  /* istanbul ignore next */
  cov_p8oku5j5a().f[0]++;
  cov_p8oku5j5a().s[1]++;
  if (Object.defineProperty) {
    /* istanbul ignore next */
    cov_p8oku5j5a().b[1][0]++;
    cov_p8oku5j5a().s[2]++;
    Object.defineProperty(cooked, "raw", {
      value: raw
    });
  } else {
    /* istanbul ignore next */
    cov_p8oku5j5a().b[1][1]++;
    cov_p8oku5j5a().s[3]++;
    cooked.raw = raw;
  }
  /* istanbul ignore next */
  cov_p8oku5j5a().s[4]++;
  return cooked;
}));
var __assign =
/* istanbul ignore next */
(cov_p8oku5j5a().s[5]++,
/* istanbul ignore next */
(cov_p8oku5j5a().b[2][0]++, this) &&
/* istanbul ignore next */
(cov_p8oku5j5a().b[2][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_p8oku5j5a().b[2][2]++, function () {
  /* istanbul ignore next */
  cov_p8oku5j5a().f[1]++;
  cov_p8oku5j5a().s[6]++;
  __assign =
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[3][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[3][1]++, function (t) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[2]++;
    cov_p8oku5j5a().s[7]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_p8oku5j5a().s[8]++, 1), n =
      /* istanbul ignore next */
      (cov_p8oku5j5a().s[9]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_p8oku5j5a().s[10]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_p8oku5j5a().s[11]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[12]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_p8oku5j5a().b[4][0]++;
          cov_p8oku5j5a().s[13]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_p8oku5j5a().b[4][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_p8oku5j5a().s[14]++;
    return t;
  });
  /* istanbul ignore next */
  cov_p8oku5j5a().s[15]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_p8oku5j5a().s[16]++,
/* istanbul ignore next */
(cov_p8oku5j5a().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_p8oku5j5a().b[5][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_p8oku5j5a().b[5][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_p8oku5j5a().f[3]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[4]++;
    cov_p8oku5j5a().s[17]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[6][0]++, value) :
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[6][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[5]++;
      cov_p8oku5j5a().s[18]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_p8oku5j5a().s[19]++;
  return new (
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[7][0]++, P) ||
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[7][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[6]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[7]++;
      cov_p8oku5j5a().s[20]++;
      try {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[21]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[22]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[8]++;
      cov_p8oku5j5a().s[23]++;
      try {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[24]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[25]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[9]++;
      cov_p8oku5j5a().s[26]++;
      result.done ?
      /* istanbul ignore next */
      (cov_p8oku5j5a().b[8][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_p8oku5j5a().b[8][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_p8oku5j5a().s[27]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[9][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[9][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_p8oku5j5a().s[28]++,
/* istanbul ignore next */
(cov_p8oku5j5a().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_p8oku5j5a().b[10][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_p8oku5j5a().b[10][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_p8oku5j5a().f[10]++;
  var _ =
    /* istanbul ignore next */
    (cov_p8oku5j5a().s[29]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[11]++;
        cov_p8oku5j5a().s[30]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_p8oku5j5a().b[11][0]++;
          cov_p8oku5j5a().s[31]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_p8oku5j5a().b[11][1]++;
        }
        cov_p8oku5j5a().s[32]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_p8oku5j5a().s[33]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[12][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[12][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_p8oku5j5a().s[34]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[13][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[13][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[12]++;
    cov_p8oku5j5a().s[35]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[13]++;
    cov_p8oku5j5a().s[36]++;
    return function (v) {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[14]++;
      cov_p8oku5j5a().s[37]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[15]++;
    cov_p8oku5j5a().s[38]++;
    if (f) {
      /* istanbul ignore next */
      cov_p8oku5j5a().b[14][0]++;
      cov_p8oku5j5a().s[39]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_p8oku5j5a().b[14][1]++;
    }
    cov_p8oku5j5a().s[40]++;
    while (
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[15][0]++, g) &&
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[15][1]++, g = 0,
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[16][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_p8oku5j5a().b[16][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_p8oku5j5a().s[41]++;
      try {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[42]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[18][0]++, y) &&
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[18][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[19][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[19][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[20][0]++,
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[21][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[21][1]++,
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[22][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[22][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[20][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_p8oku5j5a().b[18][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_p8oku5j5a().b[17][0]++;
          cov_p8oku5j5a().s[43]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_p8oku5j5a().b[17][1]++;
        }
        cov_p8oku5j5a().s[44]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_p8oku5j5a().b[23][0]++;
          cov_p8oku5j5a().s[45]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_p8oku5j5a().b[23][1]++;
        }
        cov_p8oku5j5a().s[46]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[24][0]++;
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[24][1]++;
            cov_p8oku5j5a().s[47]++;
            t = op;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[48]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[24][2]++;
            cov_p8oku5j5a().s[49]++;
            _.label++;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[50]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[24][3]++;
            cov_p8oku5j5a().s[51]++;
            _.label++;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[52]++;
            y = op[1];
            /* istanbul ignore next */
            cov_p8oku5j5a().s[53]++;
            op = [0];
            /* istanbul ignore next */
            cov_p8oku5j5a().s[54]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[24][4]++;
            cov_p8oku5j5a().s[55]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[56]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[57]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[24][5]++;
            cov_p8oku5j5a().s[58]++;
            if (
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[26][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[27][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[27][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[26][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[26][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[25][0]++;
              cov_p8oku5j5a().s[59]++;
              _ = 0;
              /* istanbul ignore next */
              cov_p8oku5j5a().s[60]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[25][1]++;
            }
            cov_p8oku5j5a().s[61]++;
            if (
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[29][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[29][1]++, !t) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[29][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[29][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[28][0]++;
              cov_p8oku5j5a().s[62]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_p8oku5j5a().s[63]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[28][1]++;
            }
            cov_p8oku5j5a().s[64]++;
            if (
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[31][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[31][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[30][0]++;
              cov_p8oku5j5a().s[65]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_p8oku5j5a().s[66]++;
              t = op;
              /* istanbul ignore next */
              cov_p8oku5j5a().s[67]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[30][1]++;
            }
            cov_p8oku5j5a().s[68]++;
            if (
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[33][0]++, t) &&
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[33][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[32][0]++;
              cov_p8oku5j5a().s[69]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_p8oku5j5a().s[70]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_p8oku5j5a().s[71]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[32][1]++;
            }
            cov_p8oku5j5a().s[72]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[34][0]++;
              cov_p8oku5j5a().s[73]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[34][1]++;
            }
            cov_p8oku5j5a().s[74]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[75]++;
            continue;
        }
        /* istanbul ignore next */
        cov_p8oku5j5a().s[76]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[77]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_p8oku5j5a().s[78]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_p8oku5j5a().s[79]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_p8oku5j5a().s[80]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_p8oku5j5a().b[35][0]++;
      cov_p8oku5j5a().s[81]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_p8oku5j5a().b[35][1]++;
    }
    cov_p8oku5j5a().s[82]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_p8oku5j5a().b[36][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_p8oku5j5a().b[36][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_p8oku5j5a().s[83]++,
/* istanbul ignore next */
(cov_p8oku5j5a().b[37][0]++, this) &&
/* istanbul ignore next */
(cov_p8oku5j5a().b[37][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_p8oku5j5a().b[37][2]++, function (mod) {
  /* istanbul ignore next */
  cov_p8oku5j5a().f[16]++;
  cov_p8oku5j5a().s[84]++;
  return /* istanbul ignore next */(cov_p8oku5j5a().b[39][0]++, mod) &&
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[39][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[38][0]++, mod) :
  /* istanbul ignore next */
  (cov_p8oku5j5a().b[38][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_p8oku5j5a().s[85]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_p8oku5j5a().s[86]++;
exports.dbOptimization = void 0;
/* istanbul ignore next */
cov_p8oku5j5a().s[87]++;
exports.setupQueryTracking = setupQueryTracking;
var prisma_1 =
/* istanbul ignore next */
(cov_p8oku5j5a().s[88]++, __importDefault(require("@/lib/prisma")));
var fs_1 =
/* istanbul ignore next */
(cov_p8oku5j5a().s[89]++, __importDefault(require("fs")));
var path_1 =
/* istanbul ignore next */
(cov_p8oku5j5a().s[90]++, __importDefault(require("path")));
var DatabaseOptimizationService =
/* istanbul ignore next */
(/** @class */cov_p8oku5j5a().s[91]++, function () {
  /* istanbul ignore next */
  cov_p8oku5j5a().f[17]++;
  function DatabaseOptimizationService() {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[18]++;
    cov_p8oku5j5a().s[92]++;
    this.queryMetrics = [];
    /* istanbul ignore next */
    cov_p8oku5j5a().s[93]++;
    this.maxMetricsHistory = 1000;
  }
  // Apply performance indexes
  /* istanbul ignore next */
  cov_p8oku5j5a().s[94]++;
  DatabaseOptimizationService.prototype.applyPerformanceIndexes = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[19]++;
    cov_p8oku5j5a().s[95]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[20]++;
      var indexesPath, indexesSQL, statements, errors, successCount, _i, statements_1, statement, error_1, errorMsg, error_2;
      /* istanbul ignore next */
      cov_p8oku5j5a().s[96]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[21]++;
        cov_p8oku5j5a().s[97]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][0]++;
            cov_p8oku5j5a().s[98]++;
            _a.trys.push([0, 7,, 8]);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[99]++;
            indexesPath = path_1.default.join(process.cwd(), 'prisma', 'migrations', 'add_performance_indexes.sql');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[100]++;
            if (!fs_1.default.existsSync(indexesPath)) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[41][0]++;
              cov_p8oku5j5a().s[101]++;
              return [2 /*return*/, {
                success: false,
                message: 'Performance indexes SQL file not found'
              }];
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[41][1]++;
            }
            cov_p8oku5j5a().s[102]++;
            indexesSQL = fs_1.default.readFileSync(indexesPath, 'utf-8');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[103]++;
            statements = indexesSQL.split(';').map(function (stmt) {
              /* istanbul ignore next */
              cov_p8oku5j5a().f[22]++;
              cov_p8oku5j5a().s[104]++;
              return stmt.trim();
            }).filter(function (stmt) {
              /* istanbul ignore next */
              cov_p8oku5j5a().f[23]++;
              cov_p8oku5j5a().s[105]++;
              return /* istanbul ignore next */(cov_p8oku5j5a().b[42][0]++, stmt.length > 0) &&
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[42][1]++, !stmt.startsWith('--'));
            });
            /* istanbul ignore next */
            cov_p8oku5j5a().s[106]++;
            errors = [];
            /* istanbul ignore next */
            cov_p8oku5j5a().s[107]++;
            successCount = 0;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[108]++;
            _i = 0, statements_1 = statements;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[109]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][1]++;
            cov_p8oku5j5a().s[110]++;
            if (!(_i < statements_1.length)) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[43][0]++;
              cov_p8oku5j5a().s[111]++;
              return [3 /*break*/, 6];
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[43][1]++;
            }
            cov_p8oku5j5a().s[112]++;
            statement = statements_1[_i];
            /* istanbul ignore next */
            cov_p8oku5j5a().s[113]++;
            _a.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][2]++;
            cov_p8oku5j5a().s[114]++;
            _a.trys.push([2, 4,, 5]);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[115]++;
            return [4 /*yield*/, prisma_1.default.$executeRawUnsafe(statement)];
          case 3:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][3]++;
            cov_p8oku5j5a().s[116]++;
            _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[117]++;
            successCount++;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[118]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][4]++;
            cov_p8oku5j5a().s[119]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[120]++;
            errorMsg = error_1 instanceof Error ?
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[44][0]++, error_1.message) :
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[44][1]++, 'Unknown error');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[121]++;
            errors.push("Failed to execute: ".concat(statement.substring(0, 100), "... - ").concat(errorMsg));
            /* istanbul ignore next */
            cov_p8oku5j5a().s[122]++;
            return [3 /*break*/, 5];
          case 5:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][5]++;
            cov_p8oku5j5a().s[123]++;
            _i++;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[124]++;
            return [3 /*break*/, 1];
          case 6:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][6]++;
            cov_p8oku5j5a().s[125]++;
            return [2 /*return*/, __assign({
              success: errors.length === 0,
              message: "Applied ".concat(successCount, " indexes successfully").concat(errors.length > 0 ?
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[45][0]++, ", ".concat(errors.length, " failed")) :
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[45][1]++, ''))
            },
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[46][0]++, errors.length > 0) &&
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[46][1]++, {
              errors: errors
            }))];
          case 7:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][7]++;
            cov_p8oku5j5a().s[126]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[127]++;
            return [2 /*return*/, {
              success: false,
              message: "Failed to apply indexes: ".concat(error_2 instanceof Error ?
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[47][0]++, error_2.message) :
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[47][1]++, 'Unknown error'))
            }];
          case 8:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[40][8]++;
            cov_p8oku5j5a().s[128]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Monitor query performance
  /* istanbul ignore next */
  cov_p8oku5j5a().s[129]++;
  DatabaseOptimizationService.prototype.trackQuery = function (query_1, executionTimeMs_1) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[24]++;
    cov_p8oku5j5a().s[130]++;
    return __awaiter(this, arguments, Promise, function (query, executionTimeMs, rowsAffected) {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[25]++;
      var metric;
      /* istanbul ignore next */
      cov_p8oku5j5a().s[131]++;
      if (rowsAffected === void 0) {
        /* istanbul ignore next */
        cov_p8oku5j5a().b[48][0]++;
        cov_p8oku5j5a().s[132]++;
        rowsAffected = 0;
      } else
      /* istanbul ignore next */
      {
        cov_p8oku5j5a().b[48][1]++;
      }
      cov_p8oku5j5a().s[133]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[26]++;
        cov_p8oku5j5a().s[134]++;
        metric = {
          query: query.substring(0, 500),
          // Truncate long queries
          executionTime: executionTimeMs,
          rowsAffected: rowsAffected,
          timestamp: new Date()
        };
        /* istanbul ignore next */
        cov_p8oku5j5a().s[135]++;
        this.queryMetrics.push(metric);
        // Keep only recent metrics
        /* istanbul ignore next */
        cov_p8oku5j5a().s[136]++;
        if (this.queryMetrics.length > this.maxMetricsHistory) {
          /* istanbul ignore next */
          cov_p8oku5j5a().b[49][0]++;
          cov_p8oku5j5a().s[137]++;
          this.queryMetrics = this.queryMetrics.slice(-this.maxMetricsHistory);
        } else
        /* istanbul ignore next */
        {
          cov_p8oku5j5a().b[49][1]++;
        }
        // Log slow queries
        cov_p8oku5j5a().s[138]++;
        if (executionTimeMs > 1000) {
          /* istanbul ignore next */
          cov_p8oku5j5a().b[50][0]++;
          cov_p8oku5j5a().s[139]++;
          // Queries taking more than 1 second
          console.warn("Slow query detected (".concat(executionTimeMs, "ms):"), query.substring(0, 200));
        } else
        /* istanbul ignore next */
        {
          cov_p8oku5j5a().b[50][1]++;
        }
        cov_p8oku5j5a().s[140]++;
        return [2 /*return*/];
      });
    });
  };
  // Get database statistics
  /* istanbul ignore next */
  cov_p8oku5j5a().s[141]++;
  DatabaseOptimizationService.prototype.getDatabaseStats = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[27]++;
    cov_p8oku5j5a().s[142]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[28]++;
      var startTime, _a, totalUsers, totalLearningResources, totalLearningPaths, totalAssessments, totalForumPosts, activeEnrollments, completedLearningPaths, queryTime, recentMetrics, avgQueryTime, slowQueries, error_3;
      /* istanbul ignore next */
      cov_p8oku5j5a().s[143]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[29]++;
        cov_p8oku5j5a().s[144]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[51][0]++;
            cov_p8oku5j5a().s[145]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[146]++;
            _b.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[51][1]++;
            cov_p8oku5j5a().s[147]++;
            _b.trys.push([1, 4,, 5]);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[148]++;
            return [4 /*yield*/, Promise.all([prisma_1.default.user.count(), prisma_1.default.learningResource.count({
              where: {
                isActive: true
              }
            }), prisma_1.default.learningPath.count({
              where: {
                isActive: true
              }
            }), prisma_1.default.assessment.count(), prisma_1.default.forumPost.count({
              where: {
                isHidden: false
              }
            }), prisma_1.default.userLearningPath.count({
              where: {
                status: {
                  in: ['IN_PROGRESS', 'NOT_STARTED']
                }
              }
            }), prisma_1.default.userLearningPath.count({
              where: {
                status: 'COMPLETED'
              }
            })])];
          case 2:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[51][2]++;
            cov_p8oku5j5a().s[149]++;
            _a = _b.sent(), totalUsers = _a[0], totalLearningResources = _a[1], totalLearningPaths = _a[2], totalAssessments = _a[3], totalForumPosts = _a[4], activeEnrollments = _a[5], completedLearningPaths = _a[6];
            /* istanbul ignore next */
            cov_p8oku5j5a().s[150]++;
            queryTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[151]++;
            return [4 /*yield*/, this.trackQuery('getDatabaseStats', queryTime)];
          case 3:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[51][3]++;
            cov_p8oku5j5a().s[152]++;
            _b.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[153]++;
            recentMetrics = this.queryMetrics.slice(-100);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[154]++;
            avgQueryTime = recentMetrics.length > 0 ?
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[52][0]++, recentMetrics.reduce(function (sum, m) {
              /* istanbul ignore next */
              cov_p8oku5j5a().f[30]++;
              cov_p8oku5j5a().s[155]++;
              return sum + m.executionTime;
            }, 0) / recentMetrics.length) :
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[52][1]++, 0);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[156]++;
            slowQueries = recentMetrics.filter(function (m) {
              /* istanbul ignore next */
              cov_p8oku5j5a().f[31]++;
              cov_p8oku5j5a().s[157]++;
              return m.executionTime > 500;
            }).sort(function (a, b) {
              /* istanbul ignore next */
              cov_p8oku5j5a().f[32]++;
              cov_p8oku5j5a().s[158]++;
              return b.executionTime - a.executionTime;
            }).slice(0, 10);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[159]++;
            return [2 /*return*/, {
              totalUsers: totalUsers,
              totalLearningResources: totalLearningResources,
              totalLearningPaths: totalLearningPaths,
              totalAssessments: totalAssessments,
              totalForumPosts: totalForumPosts,
              activeEnrollments: activeEnrollments,
              completedLearningPaths: completedLearningPaths,
              avgQueryTime: avgQueryTime,
              slowQueries: slowQueries
            }];
          case 4:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[51][4]++;
            cov_p8oku5j5a().s[160]++;
            error_3 = _b.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[161]++;
            console.error('Error getting database stats:', error_3);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[162]++;
            throw error_3;
          case 5:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[51][5]++;
            cov_p8oku5j5a().s[163]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Optimize database by running ANALYZE and VACUUM (PostgreSQL specific)
  /* istanbul ignore next */
  cov_p8oku5j5a().s[164]++;
  DatabaseOptimizationService.prototype.optimizeDatabase = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[33]++;
    cov_p8oku5j5a().s[165]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[34]++;
      var databaseUrl, error_4;
      /* istanbul ignore next */
      cov_p8oku5j5a().s[166]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[35]++;
        cov_p8oku5j5a().s[167]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][0]++;
            cov_p8oku5j5a().s[168]++;
            _a.trys.push([0, 8,, 9]);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[169]++;
            databaseUrl =
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[54][0]++, process.env.DATABASE_URL) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[54][1]++, '');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[170]++;
            if (!databaseUrl.includes('postgresql')) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[55][0]++;
              cov_p8oku5j5a().s[171]++;
              return [3 /*break*/, 3];
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[55][1]++;
            }
            // PostgreSQL optimization
            cov_p8oku5j5a().s[172]++;
            return [4 /*yield*/, prisma_1.default.$executeRaw(
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[56][0]++, templateObject_1) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[56][1]++, templateObject_1 = __makeTemplateObject(["ANALYZE"], ["ANALYZE"])))];
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][1]++;
            cov_p8oku5j5a().s[173]++;
            // PostgreSQL optimization
            _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[174]++;
            return [4 /*yield*/, prisma_1.default.$executeRaw(
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[57][0]++, templateObject_2) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[57][1]++, templateObject_2 = __makeTemplateObject(["VACUUM ANALYZE"], ["VACUUM ANALYZE"])))];
          case 2:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][2]++;
            cov_p8oku5j5a().s[175]++;
            _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[176]++;
            return [2 /*return*/, {
              success: true,
              message: 'PostgreSQL database optimized successfully'
            }];
          case 3:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][3]++;
            cov_p8oku5j5a().s[177]++;
            if (!databaseUrl.includes('sqlite')) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[58][0]++;
              cov_p8oku5j5a().s[178]++;
              return [3 /*break*/, 6];
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[58][1]++;
            }
            // SQLite optimization
            cov_p8oku5j5a().s[179]++;
            return [4 /*yield*/, prisma_1.default.$executeRaw(
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[59][0]++, templateObject_3) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[59][1]++, templateObject_3 = __makeTemplateObject(["ANALYZE"], ["ANALYZE"])))];
          case 4:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][4]++;
            cov_p8oku5j5a().s[180]++;
            // SQLite optimization
            _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[181]++;
            return [4 /*yield*/, prisma_1.default.$executeRaw(
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[60][0]++, templateObject_4) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[60][1]++, templateObject_4 = __makeTemplateObject(["VACUUM"], ["VACUUM"])))];
          case 5:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][5]++;
            cov_p8oku5j5a().s[182]++;
            _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[183]++;
            return [2 /*return*/, {
              success: true,
              message: 'SQLite database optimized successfully'
            }];
          case 6:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][6]++;
            cov_p8oku5j5a().s[184]++;
            return [2 /*return*/, {
              success: false,
              message: 'Database optimization not supported for this database type'
            }];
          case 7:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][7]++;
            cov_p8oku5j5a().s[185]++;
            return [3 /*break*/, 9];
          case 8:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][8]++;
            cov_p8oku5j5a().s[186]++;
            error_4 = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[187]++;
            return [2 /*return*/, {
              success: false,
              message: "Database optimization failed: ".concat(error_4 instanceof Error ?
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[61][0]++, error_4.message) :
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[61][1]++, 'Unknown error'))
            }];
          case 9:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[53][9]++;
            cov_p8oku5j5a().s[188]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Check index usage and effectiveness
  /* istanbul ignore next */
  cov_p8oku5j5a().s[189]++;
  DatabaseOptimizationService.prototype.analyzeIndexUsage = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[36]++;
    cov_p8oku5j5a().s[190]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[37]++;
      var databaseUrl, indexUsage, indexes, error_5;
      /* istanbul ignore next */
      cov_p8oku5j5a().s[191]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[38]++;
        cov_p8oku5j5a().s[192]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][0]++;
            cov_p8oku5j5a().s[193]++;
            _a.trys.push([0, 6,, 7]);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[194]++;
            databaseUrl =
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[63][0]++, process.env.DATABASE_URL) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[63][1]++, '');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[195]++;
            if (!databaseUrl.includes('postgresql')) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[64][0]++;
              cov_p8oku5j5a().s[196]++;
              return [3 /*break*/, 2];
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[64][1]++;
            }
            cov_p8oku5j5a().s[197]++;
            return [4 /*yield*/, prisma_1.default.$queryRaw(
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[65][0]++, templateObject_5) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[65][1]++, templateObject_5 = __makeTemplateObject(["\n          SELECT \n            schemaname,\n            tablename,\n            indexname,\n            idx_tup_read,\n            idx_tup_fetch,\n            idx_scan\n          FROM pg_stat_user_indexes \n          ORDER BY idx_scan DESC\n          LIMIT 20\n        "], ["\n          SELECT \n            schemaname,\n            tablename,\n            indexname,\n            idx_tup_read,\n            idx_tup_fetch,\n            idx_scan\n          FROM pg_stat_user_indexes \n          ORDER BY idx_scan DESC\n          LIMIT 20\n        "])))];
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][1]++;
            cov_p8oku5j5a().s[198]++;
            indexUsage = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[199]++;
            return [2 /*return*/, {
              type: 'postgresql',
              indexUsage: indexUsage
            }];
          case 2:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][2]++;
            cov_p8oku5j5a().s[200]++;
            if (!databaseUrl.includes('sqlite')) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[66][0]++;
              cov_p8oku5j5a().s[201]++;
              return [3 /*break*/, 4];
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[66][1]++;
            }
            cov_p8oku5j5a().s[202]++;
            return [4 /*yield*/, prisma_1.default.$queryRaw(
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[67][0]++, templateObject_6) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[67][1]++, templateObject_6 = __makeTemplateObject(["\n          SELECT name, sql \n          FROM sqlite_master \n          WHERE type = 'index' \n          AND name NOT LIKE 'sqlite_%'\n          ORDER BY name\n        "], ["\n          SELECT name, sql \n          FROM sqlite_master \n          WHERE type = 'index' \n          AND name NOT LIKE 'sqlite_%'\n          ORDER BY name\n        "])))];
          case 3:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][3]++;
            cov_p8oku5j5a().s[203]++;
            indexes = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[204]++;
            return [2 /*return*/, {
              type: 'sqlite',
              indexes: indexes
            }];
          case 4:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][4]++;
            cov_p8oku5j5a().s[205]++;
            return [2 /*return*/, {
              type: 'unknown',
              message: 'Index analysis not supported for this database type'
            }];
          case 5:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][5]++;
            cov_p8oku5j5a().s[206]++;
            return [3 /*break*/, 7];
          case 6:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][6]++;
            cov_p8oku5j5a().s[207]++;
            error_5 = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[208]++;
            console.error('Error analyzing index usage:', error_5);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[209]++;
            return [2 /*return*/, {
              error: error_5 instanceof Error ?
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[68][0]++, error_5.message) :
              /* istanbul ignore next */
              (cov_p8oku5j5a().b[68][1]++, 'Unknown error')
            }];
          case 7:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[62][7]++;
            cov_p8oku5j5a().s[210]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Get table sizes and row counts
  /* istanbul ignore next */
  cov_p8oku5j5a().s[211]++;
  DatabaseOptimizationService.prototype.getTableSizes = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[39]++;
    cov_p8oku5j5a().s[212]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[40]++;
      var tables, tableSizes, error_6;
      var _this =
      /* istanbul ignore next */
      (cov_p8oku5j5a().s[213]++, this);
      /* istanbul ignore next */
      cov_p8oku5j5a().s[214]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[41]++;
        cov_p8oku5j5a().s[215]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[69][0]++;
            cov_p8oku5j5a().s[216]++;
            _a.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[217]++;
            tables = ['User', 'Assessment', 'AssessmentResponse', 'LearningResource', 'UserLearningProgress', 'LearningPath', 'UserLearningPath', 'UserLearningPathProgress', 'ForumPost', 'ForumComment', 'CareerPath', 'Skill', 'UserSkillProgress', 'LearningAnalytics'];
            /* istanbul ignore next */
            cov_p8oku5j5a().s[218]++;
            return [4 /*yield*/, Promise.all(tables.map(function (table) {
              /* istanbul ignore next */
              cov_p8oku5j5a().f[42]++;
              cov_p8oku5j5a().s[219]++;
              return __awaiter(_this, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_p8oku5j5a().f[43]++;
                var modelName, count, error_7;
                /* istanbul ignore next */
                cov_p8oku5j5a().s[220]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_p8oku5j5a().f[44]++;
                  cov_p8oku5j5a().s[221]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_p8oku5j5a().b[70][0]++;
                      cov_p8oku5j5a().s[222]++;
                      _a.trys.push([0, 2,, 3]);
                      /* istanbul ignore next */
                      cov_p8oku5j5a().s[223]++;
                      modelName = table.charAt(0).toLowerCase() + table.slice(1);
                      /* istanbul ignore next */
                      cov_p8oku5j5a().s[224]++;
                      if (!prisma_1.default[modelName]) {
                        /* istanbul ignore next */
                        cov_p8oku5j5a().b[71][0]++;
                        cov_p8oku5j5a().s[225]++;
                        return [2 /*return*/, {
                          table: table,
                          count: 0,
                          error: 'Model not found in Prisma client'
                        }];
                      } else
                      /* istanbul ignore next */
                      {
                        cov_p8oku5j5a().b[71][1]++;
                      }
                      cov_p8oku5j5a().s[226]++;
                      return [4 /*yield*/, prisma_1.default[modelName].count()];
                    case 1:
                      /* istanbul ignore next */
                      cov_p8oku5j5a().b[70][1]++;
                      cov_p8oku5j5a().s[227]++;
                      count = _a.sent();
                      /* istanbul ignore next */
                      cov_p8oku5j5a().s[228]++;
                      return [2 /*return*/, {
                        table: table,
                        count: count
                      }];
                    case 2:
                      /* istanbul ignore next */
                      cov_p8oku5j5a().b[70][2]++;
                      cov_p8oku5j5a().s[229]++;
                      error_7 = _a.sent();
                      /* istanbul ignore next */
                      cov_p8oku5j5a().s[230]++;
                      return [2 /*return*/, {
                        table: table,
                        count: 0,
                        error: 'Table not found or accessible'
                      }];
                    case 3:
                      /* istanbul ignore next */
                      cov_p8oku5j5a().b[70][3]++;
                      cov_p8oku5j5a().s[231]++;
                      return [2 /*return*/];
                  }
                });
              });
            }))];
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[69][1]++;
            cov_p8oku5j5a().s[232]++;
            tableSizes = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[233]++;
            return [2 /*return*/, tableSizes.sort(function (a, b) {
              /* istanbul ignore next */
              cov_p8oku5j5a().f[45]++;
              cov_p8oku5j5a().s[234]++;
              return b.count - a.count;
            })];
          case 2:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[69][2]++;
            cov_p8oku5j5a().s[235]++;
            error_6 = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[236]++;
            console.error('Error getting table sizes:', error_6);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[237]++;
            return [2 /*return*/, []];
          case 3:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[69][3]++;
            cov_p8oku5j5a().s[238]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Connection pool monitoring
  /* istanbul ignore next */
  cov_p8oku5j5a().s[239]++;
  DatabaseOptimizationService.prototype.getConnectionPoolStats = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[46]++;
    cov_p8oku5j5a().s[240]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[47]++;
      cov_p8oku5j5a().s[241]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[48]++;
        cov_p8oku5j5a().s[242]++;
        try {
          /* istanbul ignore next */
          cov_p8oku5j5a().s[243]++;
          // This would depend on the specific database and connection pool implementation
          // For now, we'll return basic connection info
          return [2 /*return*/, {
            activeConnections: 'N/A - depends on database configuration',
            maxConnections:
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[72][0]++, process.env.DATABASE_CONNECTION_POOL_SIZE) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[72][1]++, 'default'),
            connectionTimeout:
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[73][0]++, process.env.DATABASE_QUERY_TIMEOUT) ||
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[73][1]++, 'default'),
            message: 'Connection pool monitoring requires database-specific implementation'
          }];
        } catch (error) {
          /* istanbul ignore next */
          cov_p8oku5j5a().s[244]++;
          return [2 /*return*/, {
            error: error instanceof Error ?
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[74][0]++, error.message) :
            /* istanbul ignore next */
            (cov_p8oku5j5a().b[74][1]++, 'Unknown error')
          }];
        }
        /* istanbul ignore next */
        cov_p8oku5j5a().s[245]++;
        return [2 /*return*/];
      });
    });
  };
  // Performance recommendations
  /* istanbul ignore next */
  cov_p8oku5j5a().s[246]++;
  DatabaseOptimizationService.prototype.getPerformanceRecommendations = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[49]++;
    cov_p8oku5j5a().s[247]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[50]++;
      var recommendations, stats, error_8;
      /* istanbul ignore next */
      cov_p8oku5j5a().s[248]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[51]++;
        cov_p8oku5j5a().s[249]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[75][0]++;
            cov_p8oku5j5a().s[250]++;
            recommendations = [];
            /* istanbul ignore next */
            cov_p8oku5j5a().s[251]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[75][1]++;
            cov_p8oku5j5a().s[252]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[253]++;
            return [4 /*yield*/, this.getDatabaseStats()];
          case 2:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[75][2]++;
            cov_p8oku5j5a().s[254]++;
            stats = _a.sent();
            // Check for slow queries
            /* istanbul ignore next */
            cov_p8oku5j5a().s[255]++;
            if (stats.avgQueryTime > 500) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[76][0]++;
              cov_p8oku5j5a().s[256]++;
              recommendations.push('Average query time is high. Consider optimizing slow queries or adding indexes.');
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[76][1]++;
            }
            cov_p8oku5j5a().s[257]++;
            if (stats.slowQueries.length > 5) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[77][0]++;
              cov_p8oku5j5a().s[258]++;
              recommendations.push('Multiple slow queries detected. Review query patterns and index usage.');
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[77][1]++;
            }
            // Check data volume
            cov_p8oku5j5a().s[259]++;
            if (stats.totalUsers > 10000) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[78][0]++;
              cov_p8oku5j5a().s[260]++;
              recommendations.push('Large user base detected. Consider implementing read replicas for better performance.');
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[78][1]++;
            }
            cov_p8oku5j5a().s[261]++;
            if (stats.totalLearningResources > 5000) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[79][0]++;
              cov_p8oku5j5a().s[262]++;
              recommendations.push('Large learning resource catalog. Consider implementing search indexes and caching.');
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[79][1]++;
            }
            cov_p8oku5j5a().s[263]++;
            if (stats.activeEnrollments > 1000) {
              /* istanbul ignore next */
              cov_p8oku5j5a().b[80][0]++;
              cov_p8oku5j5a().s[264]++;
              recommendations.push('High enrollment activity. Monitor learning path performance and consider optimization.');
            } else
            /* istanbul ignore next */
            {
              cov_p8oku5j5a().b[80][1]++;
            }
            // General recommendations
            cov_p8oku5j5a().s[265]++;
            recommendations.push('Regularly run database optimization (ANALYZE/VACUUM) for better performance.');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[266]++;
            recommendations.push('Monitor query performance and add indexes for frequently accessed data patterns.');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[267]++;
            recommendations.push('Consider implementing caching for frequently accessed read-only data.');
            /* istanbul ignore next */
            cov_p8oku5j5a().s[268]++;
            return [2 /*return*/, recommendations];
          case 3:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[75][3]++;
            cov_p8oku5j5a().s[269]++;
            error_8 = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[270]++;
            return [2 /*return*/, ['Error generating recommendations. Check database connectivity.']];
          case 4:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[75][4]++;
            cov_p8oku5j5a().s[271]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Clear query metrics
  /* istanbul ignore next */
  cov_p8oku5j5a().s[272]++;
  DatabaseOptimizationService.prototype.clearMetrics = function () {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[52]++;
    cov_p8oku5j5a().s[273]++;
    this.queryMetrics = [];
  };
  // Get recent query metrics
  /* istanbul ignore next */
  cov_p8oku5j5a().s[274]++;
  DatabaseOptimizationService.prototype.getRecentMetrics = function (limit) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[53]++;
    cov_p8oku5j5a().s[275]++;
    if (limit === void 0) {
      /* istanbul ignore next */
      cov_p8oku5j5a().b[81][0]++;
      cov_p8oku5j5a().s[276]++;
      limit = 50;
    } else
    /* istanbul ignore next */
    {
      cov_p8oku5j5a().b[81][1]++;
    }
    cov_p8oku5j5a().s[277]++;
    return this.queryMetrics.slice(-limit);
  };
  /* istanbul ignore next */
  cov_p8oku5j5a().s[278]++;
  return DatabaseOptimizationService;
}());
// Export singleton instance
/* istanbul ignore next */
cov_p8oku5j5a().s[279]++;
exports.dbOptimization = new DatabaseOptimizationService();
// Prisma middleware to track query performance
function setupQueryTracking() {
  /* istanbul ignore next */
  cov_p8oku5j5a().f[54]++;
  var _this =
  /* istanbul ignore next */
  (cov_p8oku5j5a().s[280]++, this);
  /* istanbul ignore next */
  cov_p8oku5j5a().s[281]++;
  prisma_1.default.$use(function (params, next) {
    /* istanbul ignore next */
    cov_p8oku5j5a().f[55]++;
    cov_p8oku5j5a().s[282]++;
    return __awaiter(_this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_p8oku5j5a().f[56]++;
      var start, result, duration, queryInfo;
      /* istanbul ignore next */
      cov_p8oku5j5a().s[283]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_p8oku5j5a().f[57]++;
        cov_p8oku5j5a().s[284]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[82][0]++;
            cov_p8oku5j5a().s[285]++;
            start = Date.now();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[286]++;
            return [4 /*yield*/, next(params)];
          case 1:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[82][1]++;
            cov_p8oku5j5a().s[287]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[288]++;
            duration = Date.now() - start;
            /* istanbul ignore next */
            cov_p8oku5j5a().s[289]++;
            queryInfo = "".concat(params.model, ".").concat(params.action);
            /* istanbul ignore next */
            cov_p8oku5j5a().s[290]++;
            return [4 /*yield*/, exports.dbOptimization.trackQuery(queryInfo, duration)];
          case 2:
            /* istanbul ignore next */
            cov_p8oku5j5a().b[82][2]++;
            cov_p8oku5j5a().s[291]++;
            _a.sent();
            /* istanbul ignore next */
            cov_p8oku5j5a().s[292]++;
            return [2 /*return*/, result];
        }
      });
    });
  });
}
/* istanbul ignore next */
cov_p8oku5j5a().s[293]++;
exports.default = exports.dbOptimization;
var templateObject_1, templateObject_2, templateObject_3, templateObject_4, templateObject_5, templateObject_6;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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