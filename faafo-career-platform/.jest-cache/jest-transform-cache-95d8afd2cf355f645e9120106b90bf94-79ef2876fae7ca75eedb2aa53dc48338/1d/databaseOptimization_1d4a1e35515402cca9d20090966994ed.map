{"version": 3, "names": ["exports", "setupQueryTracking", "prisma_1", "cov_p8oku5j5a", "s", "__importDefault", "require", "fs_1", "path_1", "DatabaseOptimizationService", "f", "queryMetrics", "maxMetricsHistory", "prototype", "applyPerformanceIndexes", "Promise", "indexesPath", "default", "join", "process", "cwd", "existsSync", "b", "success", "message", "indexesSQL", "readFileSync", "statements", "split", "map", "stmt", "trim", "filter", "length", "startsWith", "errors", "successCount", "statements_1", "_i", "statement", "$executeRawUnsafe", "_a", "sent", "errorMsg", "error_1", "Error", "push", "concat", "substring", "__assign", "error_2", "trackQuery", "query_1", "executionTimeMs_1", "query", "executionTimeMs", "rowsAffected", "metric", "executionTime", "timestamp", "Date", "slice", "console", "warn", "getDatabaseStats", "startTime", "now", "all", "user", "count", "learningResource", "where", "isActive", "learningPath", "assessment", "forumPost", "isHidden", "userLearningPath", "status", "in", "_b", "totalUsers", "totalLearningResources", "totalLearningPaths", "totalAssessments", "totalForumPosts", "activeEnrollments", "completedLearningPaths", "queryTime", "recentMetrics", "avgQueryTime", "reduce", "sum", "m", "slowQueries", "sort", "a", "error", "error_3", "optimizeDatabase", "databaseUrl", "env", "DATABASE_URL", "includes", "$executeRaw", "templateObject_1", "__makeTemplateObject", "templateObject_2", "templateObject_3", "templateObject_4", "error_4", "analyzeIndexUsage", "$queryRaw", "templateObject_5", "indexUsage", "type", "templateObject_6", "indexes", "error_5", "getTableSizes", "tables", "table", "__awaiter", "_this", "modelName", "char<PERSON>t", "toLowerCase", "tableSizes", "error_6", "getConnectionPoolStats", "activeConnections", "maxConnections", "DATABASE_CONNECTION_POOL_SIZE", "connectionTimeout", "DATABASE_QUERY_TIMEOUT", "getPerformanceRecommendations", "recommendations", "stats", "clearMetrics", "getRecentMetrics", "limit", "dbOptimization", "$use", "params", "next", "start", "result", "duration", "queryInfo", "model", "action"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/databaseOptimization.ts"], "sourcesContent": ["import prisma from '@/lib/prisma';\nimport fs from 'fs';\nimport path from 'path';\n\ninterface QueryPerformanceMetrics {\n  query: string;\n  executionTime: number;\n  rowsAffected: number;\n  timestamp: Date;\n}\n\ninterface DatabaseStats {\n  totalUsers: number;\n  totalLearningResources: number;\n  totalLearningPaths: number;\n  totalAssessments: number;\n  totalForumPosts: number;\n  activeEnrollments: number;\n  completedLearningPaths: number;\n  avgQueryTime: number;\n  slowQueries: QueryPerformanceMetrics[];\n}\n\nclass DatabaseOptimizationService {\n  private queryMetrics: QueryPerformanceMetrics[] = [];\n  private maxMetricsHistory = 1000;\n\n  // Apply performance indexes\n  async applyPerformanceIndexes(): Promise<{ success: boolean; message: string; errors?: string[] }> {\n    try {\n      const indexesPath = path.join(process.cwd(), 'prisma', 'migrations', 'add_performance_indexes.sql');\n      \n      if (!fs.existsSync(indexesPath)) {\n        return {\n          success: false,\n          message: 'Performance indexes SQL file not found'\n        };\n      }\n\n      const indexesSQL = fs.readFileSync(indexesPath, 'utf-8');\n      const statements = indexesSQL\n        .split(';')\n        .map(stmt => stmt.trim())\n        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));\n\n      const errors: string[] = [];\n      let successCount = 0;\n\n      for (const statement of statements) {\n        try {\n          await prisma.$executeRawUnsafe(statement);\n          successCount++;\n        } catch (error) {\n          const errorMsg = error instanceof Error ? error.message : 'Unknown error';\n          errors.push(`Failed to execute: ${statement.substring(0, 100)}... - ${errorMsg}`);\n        }\n      }\n\n      return {\n        success: errors.length === 0,\n        message: `Applied ${successCount} indexes successfully${errors.length > 0 ? `, ${errors.length} failed` : ''}`,\n        ...(errors.length > 0 && { errors })\n      };\n\n    } catch (error) {\n      return {\n        success: false,\n        message: `Failed to apply indexes: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  // Monitor query performance\n  async trackQuery(query: string, executionTimeMs: number, rowsAffected: number = 0): Promise<void> {\n    const metric: QueryPerformanceMetrics = {\n      query: query.substring(0, 500), // Truncate long queries\n      executionTime: executionTimeMs,\n      rowsAffected,\n      timestamp: new Date()\n    };\n\n    this.queryMetrics.push(metric);\n\n    // Keep only recent metrics\n    if (this.queryMetrics.length > this.maxMetricsHistory) {\n      this.queryMetrics = this.queryMetrics.slice(-this.maxMetricsHistory);\n    }\n\n    // Log slow queries\n    if (executionTimeMs > 1000) { // Queries taking more than 1 second\n      console.warn(`Slow query detected (${executionTimeMs}ms):`, query.substring(0, 200));\n    }\n  }\n\n  // Get database statistics\n  async getDatabaseStats(): Promise<DatabaseStats> {\n    const startTime = Date.now();\n\n    try {\n      const [\n        totalUsers,\n        totalLearningResources,\n        totalLearningPaths,\n        totalAssessments,\n        totalForumPosts,\n        activeEnrollments,\n        completedLearningPaths\n      ] = await Promise.all([\n        prisma.user.count(),\n        prisma.learningResource.count({ where: { isActive: true } }),\n        prisma.learningPath.count({ where: { isActive: true } }),\n        prisma.assessment.count(),\n        prisma.forumPost.count({ where: { isHidden: false } }),\n        prisma.userLearningPath.count({ where: { status: { in: ['IN_PROGRESS', 'NOT_STARTED'] } } }),\n        prisma.userLearningPath.count({ where: { status: 'COMPLETED' } })\n      ]);\n\n      const queryTime = Date.now() - startTime;\n      await this.trackQuery('getDatabaseStats', queryTime);\n\n      // Calculate average query time from recent metrics\n      const recentMetrics = this.queryMetrics.slice(-100);\n      const avgQueryTime = recentMetrics.length > 0 \n        ? recentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / recentMetrics.length \n        : 0;\n\n      // Get slow queries from recent metrics\n      const slowQueries = recentMetrics\n        .filter(m => m.executionTime > 500)\n        .sort((a, b) => b.executionTime - a.executionTime)\n        .slice(0, 10);\n\n      return {\n        totalUsers,\n        totalLearningResources,\n        totalLearningPaths,\n        totalAssessments,\n        totalForumPosts,\n        activeEnrollments,\n        completedLearningPaths,\n        avgQueryTime,\n        slowQueries\n      };\n\n    } catch (error) {\n      console.error('Error getting database stats:', error);\n      throw error;\n    }\n  }\n\n  // Optimize database by running ANALYZE and VACUUM (PostgreSQL specific)\n  async optimizeDatabase(): Promise<{ success: boolean; message: string }> {\n    try {\n      // Note: These commands are database-specific\n      // For SQLite, we can use ANALYZE and VACUUM\n      // For PostgreSQL, we can use ANALYZE and VACUUM\n      // For MySQL, we can use ANALYZE TABLE and OPTIMIZE TABLE\n\n      const databaseUrl = process.env.DATABASE_URL || '';\n      \n      if (databaseUrl.includes('postgresql')) {\n        // PostgreSQL optimization\n        await prisma.$executeRaw`ANALYZE`;\n        await prisma.$executeRaw`VACUUM ANALYZE`;\n        return {\n          success: true,\n          message: 'PostgreSQL database optimized successfully'\n        };\n      } else if (databaseUrl.includes('sqlite')) {\n        // SQLite optimization\n        await prisma.$executeRaw`ANALYZE`;\n        await prisma.$executeRaw`VACUUM`;\n        return {\n          success: true,\n          message: 'SQLite database optimized successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Database optimization not supported for this database type'\n        };\n      }\n\n    } catch (error) {\n      return {\n        success: false,\n        message: `Database optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  // Check index usage and effectiveness\n  async analyzeIndexUsage(): Promise<any> {\n    try {\n      const databaseUrl = process.env.DATABASE_URL || '';\n      \n      if (databaseUrl.includes('postgresql')) {\n        // PostgreSQL index usage analysis\n        const indexUsage = await prisma.$queryRaw`\n          SELECT \n            schemaname,\n            tablename,\n            indexname,\n            idx_tup_read,\n            idx_tup_fetch,\n            idx_scan\n          FROM pg_stat_user_indexes \n          ORDER BY idx_scan DESC\n          LIMIT 20\n        `;\n        \n        return {\n          type: 'postgresql',\n          indexUsage\n        };\n      } else if (databaseUrl.includes('sqlite')) {\n        // SQLite doesn't have built-in index usage stats\n        // We can check if indexes exist\n        const indexes = await prisma.$queryRaw`\n          SELECT name, sql \n          FROM sqlite_master \n          WHERE type = 'index' \n          AND name NOT LIKE 'sqlite_%'\n          ORDER BY name\n        `;\n        \n        return {\n          type: 'sqlite',\n          indexes\n        };\n      } else {\n        return {\n          type: 'unknown',\n          message: 'Index analysis not supported for this database type'\n        };\n      }\n\n    } catch (error) {\n      console.error('Error analyzing index usage:', error);\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  // Get table sizes and row counts\n  async getTableSizes(): Promise<any> {\n    try {\n      const tables = [\n        'User',\n        'Assessment',\n        'AssessmentResponse',\n        'LearningResource',\n        'UserLearningProgress',\n        'LearningPath',\n        'UserLearningPath',\n        'UserLearningPathProgress',\n        'ForumPost',\n        'ForumComment',\n        'CareerPath',\n        'Skill',\n        'UserSkillProgress',\n        'LearningAnalytics'\n      ];\n\n      const tableSizes = await Promise.all(\n        tables.map(async (table) => {\n          try {\n            // Check if the model exists in Prisma client\n            const modelName = table.charAt(0).toLowerCase() + table.slice(1);\n            if (!(prisma as any)[modelName]) {\n              return { table, count: 0, error: 'Model not found in Prisma client' };\n            }\n            const count = await (prisma as any)[modelName].count();\n            return { table, count };\n          } catch (error) {\n            return { table, count: 0, error: 'Table not found or accessible' };\n          }\n        })\n      );\n\n      return tableSizes.sort((a, b) => b.count - a.count);\n\n    } catch (error) {\n      console.error('Error getting table sizes:', error);\n      return [];\n    }\n  }\n\n  // Connection pool monitoring\n  async getConnectionPoolStats(): Promise<any> {\n    try {\n      // This would depend on the specific database and connection pool implementation\n      // For now, we'll return basic connection info\n      return {\n        activeConnections: 'N/A - depends on database configuration',\n        maxConnections: process.env.DATABASE_CONNECTION_POOL_SIZE || 'default',\n        connectionTimeout: process.env.DATABASE_QUERY_TIMEOUT || 'default',\n        message: 'Connection pool monitoring requires database-specific implementation'\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  // Performance recommendations\n  async getPerformanceRecommendations(): Promise<string[]> {\n    const recommendations: string[] = [];\n    \n    try {\n      const stats = await this.getDatabaseStats();\n      \n      // Check for slow queries\n      if (stats.avgQueryTime > 500) {\n        recommendations.push('Average query time is high. Consider optimizing slow queries or adding indexes.');\n      }\n      \n      if (stats.slowQueries.length > 5) {\n        recommendations.push('Multiple slow queries detected. Review query patterns and index usage.');\n      }\n      \n      // Check data volume\n      if (stats.totalUsers > 10000) {\n        recommendations.push('Large user base detected. Consider implementing read replicas for better performance.');\n      }\n      \n      if (stats.totalLearningResources > 5000) {\n        recommendations.push('Large learning resource catalog. Consider implementing search indexes and caching.');\n      }\n      \n      if (stats.activeEnrollments > 1000) {\n        recommendations.push('High enrollment activity. Monitor learning path performance and consider optimization.');\n      }\n      \n      // General recommendations\n      recommendations.push('Regularly run database optimization (ANALYZE/VACUUM) for better performance.');\n      recommendations.push('Monitor query performance and add indexes for frequently accessed data patterns.');\n      recommendations.push('Consider implementing caching for frequently accessed read-only data.');\n      \n      return recommendations;\n      \n    } catch (error) {\n      return ['Error generating recommendations. Check database connectivity.'];\n    }\n  }\n\n  // Clear query metrics\n  clearMetrics(): void {\n    this.queryMetrics = [];\n  }\n\n  // Get recent query metrics\n  getRecentMetrics(limit: number = 50): QueryPerformanceMetrics[] {\n    return this.queryMetrics.slice(-limit);\n  }\n}\n\n// Export singleton instance\nexport const dbOptimization = new DatabaseOptimizationService();\n\n// Prisma middleware to track query performance\nexport function setupQueryTracking() {\n  prisma.$use(async (params, next) => {\n    const start = Date.now();\n    const result = await next(params);\n    const duration = Date.now() - start;\n    \n    // Track the query\n    const queryInfo = `${params.model}.${params.action}`;\n    await dbOptimization.trackQuery(queryInfo, duration);\n    \n    return result;\n  });\n}\n\nexport default dbOptimization;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2WAA,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AA3WA,IAAAC,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,IAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AAqBA,IAAAG,2BAAA;AAAA;AAAA,cAAAN,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAO,CAAA;EAAA,SAAAD,4BAAA;IAAA;IAAAN,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACU,KAAAO,YAAY,GAA8B,EAAE;IAAC;IAAAR,aAAA,GAAAC,CAAA;IAC7C,KAAAQ,iBAAiB,GAAG,IAAI;EA4UlC;EA1UE;EAAA;EAAAT,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAAC,uBAAuB,GAA7B;IAAA;IAAAX,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAiCW,OAAO;MAAA;MAAAZ,aAAA,GAAAO,CAAA;;;;;;;;;;;;;;;;YAE9BM,WAAW,GAAGR,MAAA,CAAAS,OAAI,CAACC,IAAI,CAACC,OAAO,CAACC,GAAG,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,6BAA6B,CAAC;YAAC;YAAAjB,aAAA,GAAAC,CAAA;YAEpG,IAAI,CAACG,IAAA,CAAAU,OAAE,CAACI,UAAU,CAACL,WAAW,CAAC,EAAE;cAAA;cAAAb,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAC/B,sBAAO;gBACLmB,OAAO,EAAE,KAAK;gBACdC,OAAO,EAAE;eACV;YACH,CAAC;YAAA;YAAA;cAAArB,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAEKqB,UAAU,GAAGlB,IAAA,CAAAU,OAAE,CAACS,YAAY,CAACV,WAAW,EAAE,OAAO,CAAC;YAAC;YAAAb,aAAA,GAAAC,CAAA;YACnDuB,UAAU,GAAGF,UAAU,CAC1BG,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAC,UAAAC,IAAI;cAAA;cAAA3B,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAAI,OAAA0B,IAAI,CAACC,IAAI,EAAE;YAAX,CAAW,CAAC,CACxBC,MAAM,CAAC,UAAAF,IAAI;cAAA;cAAA3B,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAAI,kCAAAD,aAAA,GAAAmB,CAAA,WAAAQ,IAAI,CAACG,MAAM,GAAG,CAAC;cAAA;cAAA,CAAA9B,aAAA,GAAAmB,CAAA,WAAI,CAACQ,IAAI,CAACI,UAAU,CAAC,IAAI,CAAC;YAAzC,CAAyC,CAAC;YAAC;YAAA/B,aAAA,GAAAC,CAAA;YAEvD+B,MAAM,GAAa,EAAE;YAAC;YAAAhC,aAAA,GAAAC,CAAA;YACxBgC,YAAY,GAAG,CAAC;YAAC;YAAAjC,aAAA,GAAAC,CAAA;kBAEa,EAAViC,YAAA,GAAAV,UAAU;YAAA;YAAAxB,aAAA,GAAAC,CAAA;;;;;;kBAAVkC,EAAA,GAAAD,YAAA,CAAAJ,MAAU;cAAA;cAAA9B,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAAA;YAAA;YAAA;YAAA;cAAAD,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAAvBmC,SAAS,GAAAF,YAAA,CAAAC,EAAA;YAAA;YAAAnC,aAAA,GAAAC,CAAA;;;;;;;;;YAEhB,qBAAMF,QAAA,CAAAe,OAAM,CAACuB,iBAAiB,CAACD,SAAS,CAAC;;;;;YAAzCE,EAAA,CAAAC,IAAA,EAAyC;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YAC1CgC,YAAY,EAAE;YAAC;YAAAjC,aAAA,GAAAC,CAAA;;;;;;;;;YAETuC,QAAQ,GAAGC,OAAK,YAAYC,KAAK;YAAA;YAAA,CAAA1C,aAAA,GAAAmB,CAAA,WAAGsB,OAAK,CAACpB,OAAO;YAAA;YAAA,CAAArB,aAAA,GAAAmB,CAAA,WAAG,eAAe;YAAC;YAAAnB,aAAA,GAAAC,CAAA;YAC1E+B,MAAM,CAACW,IAAI,CAAC,sBAAAC,MAAA,CAAsBR,SAAS,CAACS,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,YAAAD,MAAA,CAASJ,QAAQ,CAAE,CAAC;YAAC;YAAAxC,aAAA,GAAAC,CAAA;;;;;;YAN9DkC,EAAA,EAAU;YAAA;YAAAnC,aAAA,GAAAC,CAAA;;;;;;YAUlC,sBAAA6C,QAAA;cACE1B,OAAO,EAAEY,MAAM,CAACF,MAAM,KAAK,CAAC;cAC5BT,OAAO,EAAE,WAAAuB,MAAA,CAAWX,YAAY,2BAAAW,MAAA,CAAwBZ,MAAM,CAACF,MAAM,GAAG,CAAC;cAAA;cAAA,CAAA9B,aAAA,GAAAmB,CAAA,WAAG,KAAAyB,MAAA,CAAKZ,MAAM,CAACF,MAAM,YAAS;cAAA;cAAA,CAAA9B,aAAA,GAAAmB,CAAA,WAAG,EAAE;YAAE;YAC1G;YAAA,CAAAnB,aAAA,GAAAmB,CAAA,WAAAa,MAAM,CAACF,MAAM,GAAG,CAAC;YAAA;YAAA,CAAA9B,aAAA,GAAAmB,CAAA,WAAI;cAAEa,MAAM,EAAAA;YAAA,CAAE,CAAC;;;;;;;;YAItC,sBAAO;cACLZ,OAAO,EAAE,KAAK;cACdC,OAAO,EAAE,4BAAAuB,MAAA,CAA4BG,OAAK,YAAYL,KAAK;cAAA;cAAA,CAAA1C,aAAA,GAAAmB,CAAA,WAAG4B,OAAK,CAAC1B,OAAO;cAAA;cAAA,CAAArB,aAAA,GAAAmB,CAAA,WAAG,eAAe;aAC9F;;;;;;;;;GAEJ;EAED;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAAsC,UAAU,GAAhB,UAAAC,OAAA,EAAAC,iBAAA;IAAA;IAAAlD,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;sCAAoFW,OAAO,YAA1EuC,KAAa,EAAEC,eAAuB,EAAEC,YAAwB;MAAA;MAAArD,aAAA,GAAAO,CAAA;;;;MAAxB,IAAA8C,YAAA;QAAA;QAAArD,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAC,CAAA;QAAAoD,YAAA,IAAwB;MAAA;MAAA;MAAA;QAAArD,aAAA,GAAAmB,CAAA;MAAA;MAAAnB,aAAA,GAAAC,CAAA;;;;;QACzEqD,MAAM,GAA4B;UACtCH,KAAK,EAAEA,KAAK,CAACN,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;UAAE;UAChCU,aAAa,EAAEH,eAAe;UAC9BC,YAAY,EAAAA,YAAA;UACZG,SAAS,EAAE,IAAIC,IAAI;SACpB;QAAC;QAAAzD,aAAA,GAAAC,CAAA;QAEF,IAAI,CAACO,YAAY,CAACmC,IAAI,CAACW,MAAM,CAAC;QAE9B;QAAA;QAAAtD,aAAA,GAAAC,CAAA;QACA,IAAI,IAAI,CAACO,YAAY,CAACsB,MAAM,GAAG,IAAI,CAACrB,iBAAiB,EAAE;UAAA;UAAAT,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UACrD,IAAI,CAACO,YAAY,GAAG,IAAI,CAACA,YAAY,CAACkD,KAAK,CAAC,CAAC,IAAI,CAACjD,iBAAiB,CAAC;QACtE,CAAC;QAAA;QAAA;UAAAT,aAAA,GAAAmB,CAAA;QAAA;QAED;QAAAnB,aAAA,GAAAC,CAAA;QACA,IAAImD,eAAe,GAAG,IAAI,EAAE;UAAA;UAAApD,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAC,CAAA;UAAE;UAC5B0D,OAAO,CAACC,IAAI,CAAC,wBAAAhB,MAAA,CAAwBQ,eAAe,SAAM,EAAED,KAAK,CAACN,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACtF,CAAC;QAAA;QAAA;UAAA7C,aAAA,GAAAmB,CAAA;QAAA;QAAAnB,aAAA,GAAAC,CAAA;;;;GACF;EAED;EAAA;EAAAD,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAAmD,gBAAgB,GAAtB;IAAA;IAAA7D,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAA0BW,OAAO;MAAA;MAAAZ,aAAA,GAAAO,CAAA;;;;;;;;;;;;;YACzBuD,SAAS,GAAGL,IAAI,CAACM,GAAG,EAAE;YAAC;YAAA/D,aAAA,GAAAC,CAAA;;;;;;;;;YAWvB,qBAAMW,OAAO,CAACoD,GAAG,CAAC,CACpBjE,QAAA,CAAAe,OAAM,CAACmD,IAAI,CAACC,KAAK,EAAE,EACnBnE,QAAA,CAAAe,OAAM,CAACqD,gBAAgB,CAACD,KAAK,CAAC;cAAEE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAI;YAAE,CAAE,CAAC,EAC5DtE,QAAA,CAAAe,OAAM,CAACwD,YAAY,CAACJ,KAAK,CAAC;cAAEE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAI;YAAE,CAAE,CAAC,EACxDtE,QAAA,CAAAe,OAAM,CAACyD,UAAU,CAACL,KAAK,EAAE,EACzBnE,QAAA,CAAAe,OAAM,CAAC0D,SAAS,CAACN,KAAK,CAAC;cAAEE,KAAK,EAAE;gBAAEK,QAAQ,EAAE;cAAK;YAAE,CAAE,CAAC,EACtD1E,QAAA,CAAAe,OAAM,CAAC4D,gBAAgB,CAACR,KAAK,CAAC;cAAEE,KAAK,EAAE;gBAAEO,MAAM,EAAE;kBAAEC,EAAE,EAAE,CAAC,aAAa,EAAE,aAAa;gBAAC;cAAE;YAAE,CAAE,CAAC,EAC5F7E,QAAA,CAAAe,OAAM,CAAC4D,gBAAgB,CAACR,KAAK,CAAC;cAAEE,KAAK,EAAE;gBAAEO,MAAM,EAAE;cAAW;YAAE,CAAE,CAAC,CAClE,CAAC;;;;;YAhBIrC,EAAA,GAQFuC,EAAA,CAAAtC,IAAA,EAQF,EAfAuC,UAAU,GAAAxC,EAAA,KACVyC,sBAAsB,GAAAzC,EAAA,KACtB0C,kBAAkB,GAAA1C,EAAA,KAClB2C,gBAAgB,GAAA3C,EAAA,KAChB4C,eAAe,GAAA5C,EAAA,KACf6C,iBAAiB,GAAA7C,EAAA,KACjB8C,sBAAsB,GAAA9C,EAAA;YAAA;YAAAtC,aAAA,GAAAC,CAAA;YAWlBoF,SAAS,GAAG5B,IAAI,CAACM,GAAG,EAAE,GAAGD,SAAS;YAAC;YAAA9D,aAAA,GAAAC,CAAA;YACzC,qBAAM,IAAI,CAAC+C,UAAU,CAAC,kBAAkB,EAAEqC,SAAS,CAAC;;;;;YAApDR,EAAA,CAAAtC,IAAA,EAAoD;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YAG/CqF,aAAa,GAAG,IAAI,CAAC9E,YAAY,CAACkD,KAAK,CAAC,CAAC,GAAG,CAAC;YAAC;YAAA1D,aAAA,GAAAC,CAAA;YAC9CsF,YAAY,GAAGD,aAAa,CAACxD,MAAM,GAAG,CAAC;YAAA;YAAA,CAAA9B,aAAA,GAAAmB,CAAA,WACzCmE,aAAa,CAACE,MAAM,CAAC,UAACC,GAAG,EAAEC,CAAC;cAAA;cAAA1F,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAAK,OAAAwF,GAAG,GAAGC,CAAC,CAACnC,aAAa;YAArB,CAAqB,EAAE,CAAC,CAAC,GAAG+B,aAAa,CAACxD,MAAM;YAAA;YAAA,CAAA9B,aAAA,GAAAmB,CAAA,WACjF,CAAC;YAAC;YAAAnB,aAAA,GAAAC,CAAA;YAGA0F,WAAW,GAAGL,aAAa,CAC9BzD,MAAM,CAAC,UAAA6D,CAAC;cAAA;cAAA1F,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAAI,OAAAyF,CAAC,CAACnC,aAAa,GAAG,GAAG;YAArB,CAAqB,CAAC,CAClCqC,IAAI,CAAC,UAACC,CAAC,EAAE1E,CAAC;cAAA;cAAAnB,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAAK,OAAAkB,CAAC,CAACoC,aAAa,GAAGsC,CAAC,CAACtC,aAAa;YAAjC,CAAiC,CAAC,CACjDG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;YAAC;YAAA1D,aAAA,GAAAC,CAAA;YAEhB,sBAAO;cACL6E,UAAU,EAAAA,UAAA;cACVC,sBAAsB,EAAAA,sBAAA;cACtBC,kBAAkB,EAAAA,kBAAA;cAClBC,gBAAgB,EAAAA,gBAAA;cAChBC,eAAe,EAAAA,eAAA;cACfC,iBAAiB,EAAAA,iBAAA;cACjBC,sBAAsB,EAAAA,sBAAA;cACtBG,YAAY,EAAAA,YAAA;cACZI,WAAW,EAAAA;aACZ;;;;;;;;YAGDhC,OAAO,CAACmC,KAAK,CAAC,+BAA+B,EAAEC,OAAK,CAAC;YAAC;YAAA/F,aAAA,GAAAC,CAAA;YACtD,MAAM8F,OAAK;;;;;;;;;GAEd;EAED;EAAA;EAAA/F,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAAsF,gBAAgB,GAAtB;IAAA;IAAAhG,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAA0BW,OAAO;MAAA;MAAAZ,aAAA,GAAAO,CAAA;;;;;;;;;;;;;;;;YAOvB0F,WAAW;YAAG;YAAA,CAAAjG,aAAA,GAAAmB,CAAA,WAAAH,OAAO,CAACkF,GAAG,CAACC,YAAY;YAAA;YAAA,CAAAnG,aAAA,GAAAmB,CAAA,WAAI,EAAE;YAAC;YAAAnB,aAAA,GAAAC,CAAA;iBAE/CgG,WAAW,CAACG,QAAQ,CAAC,YAAY,CAAC,EAAlC;cAAA;cAAApG,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAAA;YAAA,CAAkC;YAAA;YAAA;cAAAD,aAAA,GAAAmB,CAAA;YAAA;YACpC;YAAAnB,aAAA,GAAAC,CAAA;YACA,qBAAMF,QAAA,CAAAe,OAAM,CAACuF,WAAW;YAAA;YAAA,CAAArG,aAAA,GAAAmB,CAAA,WAAAmF,gBAAA;YAAA;YAAA,CAAAtG,aAAA,GAAAmB,CAAA,WAAAmF,gBAAA,GAAAC,oBAAA,wBAAS;;;;;YADjC;YACAjE,EAAA,CAAAC,IAAA,EAAiC;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YAClC,qBAAMF,QAAA,CAAAe,OAAM,CAACuF,WAAW;YAAA;YAAA,CAAArG,aAAA,GAAAmB,CAAA,WAAAqF,gBAAA;YAAA;YAAA,CAAAxG,aAAA,GAAAmB,CAAA,WAAAqF,gBAAA,GAAAD,oBAAA,sCAAgB;;;;;YAAxCjE,EAAA,CAAAC,IAAA,EAAwC;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YACzC,sBAAO;cACLmB,OAAO,EAAE,IAAI;cACbC,OAAO,EAAE;aACV;;;;;iBACQ4E,WAAW,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAA9B;cAAA;cAAApG,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAAA;YAAA,CAA8B;YAAA;YAAA;cAAAD,aAAA,GAAAmB,CAAA;YAAA;YACvC;YAAAnB,aAAA,GAAAC,CAAA;YACA,qBAAMF,QAAA,CAAAe,OAAM,CAACuF,WAAW;YAAA;YAAA,CAAArG,aAAA,GAAAmB,CAAA,WAAAsF,gBAAA;YAAA;YAAA,CAAAzG,aAAA,GAAAmB,CAAA,WAAAsF,gBAAA,GAAAF,oBAAA,wBAAS;;;;;YADjC;YACAjE,EAAA,CAAAC,IAAA,EAAiC;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YAClC,qBAAMF,QAAA,CAAAe,OAAM,CAACuF,WAAW;YAAA;YAAA,CAAArG,aAAA,GAAAmB,CAAA,WAAAuF,gBAAA;YAAA;YAAA,CAAA1G,aAAA,GAAAmB,CAAA,WAAAuF,gBAAA,GAAAH,oBAAA,sBAAQ;;;;;YAAhCjE,EAAA,CAAAC,IAAA,EAAgC;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YACjC,sBAAO;cACLmB,OAAO,EAAE,IAAI;cACbC,OAAO,EAAE;aACV;;;;;YAED,sBAAO;cACLD,OAAO,EAAE,KAAK;cACdC,OAAO,EAAE;aACV;;;;;;;;;;;;;YAIH,sBAAO;cACLD,OAAO,EAAE,KAAK;cACdC,OAAO,EAAE,iCAAAuB,MAAA,CAAiC+D,OAAK,YAAYjE,KAAK;cAAA;cAAA,CAAA1C,aAAA,GAAAmB,CAAA,WAAGwF,OAAK,CAACtF,OAAO;cAAA;cAAA,CAAArB,aAAA,GAAAmB,CAAA,WAAG,eAAe;aACnG;;;;;;;;;GAEJ;EAED;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAAkG,iBAAiB,GAAvB;IAAA;IAAA5G,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAA2BW,OAAO;MAAA;MAAAZ,aAAA,GAAAO,CAAA;;;;;;;;;;;;;;;;YAExB0F,WAAW;YAAG;YAAA,CAAAjG,aAAA,GAAAmB,CAAA,WAAAH,OAAO,CAACkF,GAAG,CAACC,YAAY;YAAA;YAAA,CAAAnG,aAAA,GAAAmB,CAAA,WAAI,EAAE;YAAC;YAAAnB,aAAA,GAAAC,CAAA;iBAE/CgG,WAAW,CAACG,QAAQ,CAAC,YAAY,CAAC,EAAlC;cAAA;cAAApG,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAAA;YAAA,CAAkC;YAAA;YAAA;cAAAD,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAEjB,qBAAMF,QAAA,CAAAe,OAAM,CAAC+F,SAAS;YAAA;YAAA,CAAA7G,aAAA,GAAAmB,CAAA,WAAA2F,gBAAA;YAAA;YAAA,CAAA9G,aAAA,GAAAmB,CAAA,WAAA2F,gBAAA,GAAAP,oBAAA,wiBAWxC;;;;;YAXKQ,UAAU,GAAGzE,EAAA,CAAAC,IAAA,EAWlB;YAAA;YAAAvC,aAAA,GAAAC,CAAA;YAED,sBAAO;cACL+G,IAAI,EAAE,YAAY;cAClBD,UAAU,EAAAA;aACX;;;;;iBACQd,WAAW,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAA9B;cAAA;cAAApG,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAAA;YAAA,CAA8B;YAAA;YAAA;cAAAD,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAGvB,qBAAMF,QAAA,CAAAe,OAAM,CAAC+F,SAAS;YAAA;YAAA,CAAA7G,aAAA,GAAAmB,CAAA,WAAA8F,gBAAA;YAAA;YAAA,CAAAjH,aAAA,GAAAmB,CAAA,WAAA8F,gBAAA,GAAAV,oBAAA,0VAMrC;;;;;YANKW,OAAO,GAAG5E,EAAA,CAAAC,IAAA,EAMf;YAAA;YAAAvC,aAAA,GAAAC,CAAA;YAED,sBAAO;cACL+G,IAAI,EAAE,QAAQ;cACdE,OAAO,EAAAA;aACR;;;;;YAED,sBAAO;cACLF,IAAI,EAAE,SAAS;cACf3F,OAAO,EAAE;aACV;;;;;;;;;;;;;YAIHsC,OAAO,CAACmC,KAAK,CAAC,8BAA8B,EAAEqB,OAAK,CAAC;YAAC;YAAAnH,aAAA,GAAAC,CAAA;YACrD,sBAAO;cACL6F,KAAK,EAAEqB,OAAK,YAAYzE,KAAK;cAAA;cAAA,CAAA1C,aAAA,GAAAmB,CAAA,WAAGgG,OAAK,CAAC9F,OAAO;cAAA;cAAA,CAAArB,aAAA,GAAAmB,CAAA,WAAG,eAAe;aAChE;;;;;;;;;GAEJ;EAED;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAA0G,aAAa,GAAnB;IAAA;IAAApH,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAuBW,OAAO;MAAA;MAAAZ,aAAA,GAAAO,CAAA;;;;;;;;;;;;;;;;;;;YAEpB8G,MAAM,GAAG,CACb,MAAM,EACN,YAAY,EACZ,oBAAoB,EACpB,kBAAkB,EAClB,sBAAsB,EACtB,cAAc,EACd,kBAAkB,EAClB,0BAA0B,EAC1B,WAAW,EACX,cAAc,EACd,YAAY,EACZ,OAAO,EACP,mBAAmB,EACnB,mBAAmB,CACpB;YAAC;YAAArH,aAAA,GAAAC,CAAA;YAEiB,qBAAMW,OAAO,CAACoD,GAAG,CAClCqD,MAAM,CAAC3F,GAAG,CAAC,UAAO4F,KAAK;cAAA;cAAAtH,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAAA,OAAAsH,SAAA,CAAAC,KAAA;gBAAA;gBAAAxH,aAAA,GAAAO,CAAA;;;;;;;;;;;;;;;;sBAGbkH,SAAS,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGL,KAAK,CAAC5D,KAAK,CAAC,CAAC,CAAC;sBAAC;sBAAA1D,aAAA,GAAAC,CAAA;sBACjE,IAAI,CAAEF,QAAA,CAAAe,OAAc,CAAC2G,SAAS,CAAC,EAAE;wBAAA;wBAAAzH,aAAA,GAAAmB,CAAA;wBAAAnB,aAAA,GAAAC,CAAA;wBAC/B,sBAAO;0BAAEqH,KAAK,EAAAA,KAAA;0BAAEpD,KAAK,EAAE,CAAC;0BAAE4B,KAAK,EAAE;wBAAkC,CAAE;sBACvE,CAAC;sBAAA;sBAAA;wBAAA9F,aAAA,GAAAmB,CAAA;sBAAA;sBAAAnB,aAAA,GAAAC,CAAA;sBACa,qBAAOF,QAAA,CAAAe,OAAc,CAAC2G,SAAS,CAAC,CAACvD,KAAK,EAAE;;;;;sBAAhDA,KAAK,GAAG5B,EAAA,CAAAC,IAAA,EAAwC;sBAAA;sBAAAvC,aAAA,GAAAC,CAAA;sBACtD,sBAAO;wBAAEqH,KAAK,EAAAA,KAAA;wBAAEpD,KAAK,EAAAA;sBAAA,CAAE;;;;;;;;sBAEvB,sBAAO;wBAAEoD,KAAK,EAAAA,KAAA;wBAAEpD,KAAK,EAAE,CAAC;wBAAE4B,KAAK,EAAE;sBAA+B,CAAE;;;;;;;;;aAErE,CAAC,CACH;;;;;YAdK8B,UAAU,GAAGtF,EAAA,CAAAC,IAAA,EAclB;YAAA;YAAAvC,aAAA,GAAAC,CAAA;YAED,sBAAO2H,UAAU,CAAChC,IAAI,CAAC,UAACC,CAAC,EAAE1E,CAAC;cAAA;cAAAnB,aAAA,GAAAO,CAAA;cAAAP,aAAA,GAAAC,CAAA;cAAK,OAAAkB,CAAC,CAAC+C,KAAK,GAAG2B,CAAC,CAAC3B,KAAK;YAAjB,CAAiB,CAAC;;;;;;;;YAGnDP,OAAO,CAACmC,KAAK,CAAC,4BAA4B,EAAE+B,OAAK,CAAC;YAAC;YAAA7H,aAAA,GAAAC,CAAA;YACnD,sBAAO,EAAE;;;;;;;;;GAEZ;EAED;EAAA;EAAAD,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAAoH,sBAAsB,GAA5B;IAAA;IAAA9H,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAgCW,OAAO;MAAA;MAAAZ,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;;;;;QACrC,IAAI;UAAA;UAAAD,aAAA,GAAAC,CAAA;UACF;UACA;UACA,sBAAO;YACL8H,iBAAiB,EAAE,yCAAyC;YAC5DC,cAAc;YAAE;YAAA,CAAAhI,aAAA,GAAAmB,CAAA,WAAAH,OAAO,CAACkF,GAAG,CAAC+B,6BAA6B;YAAA;YAAA,CAAAjI,aAAA,GAAAmB,CAAA,WAAI,SAAS;YACtE+G,iBAAiB;YAAE;YAAA,CAAAlI,aAAA,GAAAmB,CAAA,WAAAH,OAAO,CAACkF,GAAG,CAACiC,sBAAsB;YAAA;YAAA,CAAAnI,aAAA,GAAAmB,CAAA,WAAI,SAAS;YAClEE,OAAO,EAAE;WACV;QACH,CAAC,CAAC,OAAOyE,KAAK,EAAE;UAAA;UAAA9F,aAAA,GAAAC,CAAA;UACd,sBAAO;YACL6F,KAAK,EAAEA,KAAK,YAAYpD,KAAK;YAAA;YAAA,CAAA1C,aAAA,GAAAmB,CAAA,WAAG2E,KAAK,CAACzE,OAAO;YAAA;YAAA,CAAArB,aAAA,GAAAmB,CAAA,WAAG,eAAe;WAChE;QACH;QAAC;QAAAnB,aAAA,GAAAC,CAAA;;;;GACF;EAED;EAAA;EAAAD,aAAA,GAAAC,CAAA;EACMK,2BAAA,CAAAI,SAAA,CAAA0H,6BAA6B,GAAnC;IAAA;IAAApI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;mCAAuCW,OAAO;MAAA;MAAAZ,aAAA,GAAAO,CAAA;;;;;;;;;;;;;YACtC8H,eAAe,GAAa,EAAE;YAAC;YAAArI,aAAA,GAAAC,CAAA;;;;;;;;;YAGrB,qBAAM,IAAI,CAAC4D,gBAAgB,EAAE;;;;;YAArCyE,KAAK,GAAGhG,EAAA,CAAAC,IAAA,EAA6B;YAE3C;YAAA;YAAAvC,aAAA,GAAAC,CAAA;YACA,IAAIqI,KAAK,CAAC/C,YAAY,GAAG,GAAG,EAAE;cAAA;cAAAvF,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAC5BoI,eAAe,CAAC1F,IAAI,CAAC,iFAAiF,CAAC;YACzG,CAAC;YAAA;YAAA;cAAA3C,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAED,IAAIqI,KAAK,CAAC3C,WAAW,CAAC7D,MAAM,GAAG,CAAC,EAAE;cAAA;cAAA9B,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAChCoI,eAAe,CAAC1F,IAAI,CAAC,wEAAwE,CAAC;YAChG,CAAC;YAAA;YAAA;cAAA3C,aAAA,GAAAmB,CAAA;YAAA;YAED;YAAAnB,aAAA,GAAAC,CAAA;YACA,IAAIqI,KAAK,CAACxD,UAAU,GAAG,KAAK,EAAE;cAAA;cAAA9E,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAC5BoI,eAAe,CAAC1F,IAAI,CAAC,uFAAuF,CAAC;YAC/G,CAAC;YAAA;YAAA;cAAA3C,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAED,IAAIqI,KAAK,CAACvD,sBAAsB,GAAG,IAAI,EAAE;cAAA;cAAA/E,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cACvCoI,eAAe,CAAC1F,IAAI,CAAC,oFAAoF,CAAC;YAC5G,CAAC;YAAA;YAAA;cAAA3C,aAAA,GAAAmB,CAAA;YAAA;YAAAnB,aAAA,GAAAC,CAAA;YAED,IAAIqI,KAAK,CAACnD,iBAAiB,GAAG,IAAI,EAAE;cAAA;cAAAnF,aAAA,GAAAmB,CAAA;cAAAnB,aAAA,GAAAC,CAAA;cAClCoI,eAAe,CAAC1F,IAAI,CAAC,wFAAwF,CAAC;YAChH,CAAC;YAAA;YAAA;cAAA3C,aAAA,GAAAmB,CAAA;YAAA;YAED;YAAAnB,aAAA,GAAAC,CAAA;YACAoI,eAAe,CAAC1F,IAAI,CAAC,8EAA8E,CAAC;YAAC;YAAA3C,aAAA,GAAAC,CAAA;YACrGoI,eAAe,CAAC1F,IAAI,CAAC,kFAAkF,CAAC;YAAC;YAAA3C,aAAA,GAAAC,CAAA;YACzGoI,eAAe,CAAC1F,IAAI,CAAC,uEAAuE,CAAC;YAAC;YAAA3C,aAAA,GAAAC,CAAA;YAE9F,sBAAOoI,eAAe;;;;;;;;YAGtB,sBAAO,CAAC,gEAAgE,CAAC;;;;;;;;;GAE5E;EAED;EAAA;EAAArI,aAAA,GAAAC,CAAA;EACAK,2BAAA,CAAAI,SAAA,CAAA6H,YAAY,GAAZ;IAAA;IAAAvI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IACE,IAAI,CAACO,YAAY,GAAG,EAAE;EACxB,CAAC;EAED;EAAA;EAAAR,aAAA,GAAAC,CAAA;EACAK,2BAAA,CAAAI,SAAA,CAAA8H,gBAAgB,GAAhB,UAAiBC,KAAkB;IAAA;IAAAzI,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAAlB,IAAAwI,KAAA;MAAA;MAAAzI,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAC,CAAA;MAAAwI,KAAA,KAAkB;IAAA;IAAA;IAAA;MAAAzI,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAC,CAAA;IACjC,OAAO,IAAI,CAACO,YAAY,CAACkD,KAAK,CAAC,CAAC+E,KAAK,CAAC;EACxC,CAAC;EAAA;EAAAzI,aAAA,GAAAC,CAAA;EACH,OAAAK,2BAAC;AAAD,CAAC,CA9UD;AAgVA;AAAA;AAAAN,aAAA,GAAAC,CAAA;AACaJ,OAAA,CAAA6I,cAAc,GAAG,IAAIpI,2BAA2B,EAAE;AAE/D;AACA,SAAgBR,kBAAkBA,CAAA;EAAA;EAAAE,aAAA,GAAAO,CAAA;EAAlC,IAAAiH,KAAA;EAAA;EAAA,CAAAxH,aAAA,GAAAC,CAAA;EAYC;EAAAD,aAAA,GAAAC,CAAA;EAXCF,QAAA,CAAAe,OAAM,CAAC6H,IAAI,CAAC,UAAOC,MAAM,EAAEC,IAAI;IAAA;IAAA7I,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAC,CAAA;IAAA,OAAAsH,SAAA,CAAAC,KAAA;MAAA;MAAAxH,aAAA,GAAAO,CAAA;;;;;;;;;;;;;YACvBuI,KAAK,GAAGrF,IAAI,CAACM,GAAG,EAAE;YAAC;YAAA/D,aAAA,GAAAC,CAAA;YACV,qBAAM4I,IAAI,CAACD,MAAM,CAAC;;;;;YAA3BG,MAAM,GAAGzG,EAAA,CAAAC,IAAA,EAAkB;YAAA;YAAAvC,aAAA,GAAAC,CAAA;YAC3B+I,QAAQ,GAAGvF,IAAI,CAACM,GAAG,EAAE,GAAG+E,KAAK;YAAC;YAAA9I,aAAA,GAAAC,CAAA;YAG9BgJ,SAAS,GAAG,GAAArG,MAAA,CAAGgG,MAAM,CAACM,KAAK,OAAAtG,MAAA,CAAIgG,MAAM,CAACO,MAAM,CAAE;YAAC;YAAAnJ,aAAA,GAAAC,CAAA;YACrD,qBAAMJ,OAAA,CAAA6I,cAAc,CAAC1F,UAAU,CAACiG,SAAS,EAAED,QAAQ,CAAC;;;;;YAApD1G,EAAA,CAAAC,IAAA,EAAoD;YAAC;YAAAvC,aAAA,GAAAC,CAAA;YAErD,sBAAO8I,MAAM;;;;GACd,CAAC;AACJ;AAAC;AAAA/I,aAAA,GAAAC,CAAA;AAEDJ,OAAA,CAAAiB,OAAA,GAAejB,OAAA,CAAA6I,cAAc", "ignoreList": []}