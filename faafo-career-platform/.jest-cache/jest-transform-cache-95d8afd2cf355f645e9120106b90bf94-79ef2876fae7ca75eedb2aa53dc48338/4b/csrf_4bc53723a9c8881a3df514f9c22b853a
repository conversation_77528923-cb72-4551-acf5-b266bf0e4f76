6f2977b60cebf965e78d8912bae4a855
"use strict";

/* istanbul ignore next */
function cov_sf9egw5x0() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/csrf.ts";
  var hash = "8e79dbcaae02908660e5a76fd1b7a51b83244a19";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/csrf.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "68": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 46
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 36
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 46
        }
      },
      "71": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 48
        }
      },
      "72": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 52
        }
      },
      "73": {
        start: {
          line: 45,
          column: 15
        },
        end: {
          line: 45,
          column: 37
        }
      },
      "74": {
        start: {
          line: 46,
          column: 13
        },
        end: {
          line: 46,
          column: 38
        }
      },
      "75": {
        start: {
          line: 47,
          column: 13
        },
        end: {
          line: 47,
          column: 30
        }
      },
      "76": {
        start: {
          line: 49,
          column: 17
        },
        end: {
          line: 49,
          column: 90
        }
      },
      "77": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "78": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 41
        }
      },
      "79": {
        start: {
          line: 55,
          column: 15
        },
        end: {
          line: 55,
          column: 34
        }
      },
      "80": {
        start: {
          line: 56,
          column: 20
        },
        end: {
          line: 56,
          column: 43
        }
      },
      "81": {
        start: {
          line: 57,
          column: 22
        },
        end: {
          line: 57,
          column: 64
        }
      },
      "82": {
        start: {
          line: 58,
          column: 20
        },
        end: {
          line: 58,
          column: 116
        }
      },
      "83": {
        start: {
          line: 58,
          column: 62
        },
        end: {
          line: 58,
          column: 104
        }
      },
      "84": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 73
        }
      },
      "85": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 91,
          column: 7
        }
      },
      "86": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 90,
          column: 11
        }
      },
      "87": {
        start: {
          line: 66,
          column: 12
        },
        end: {
          line: 89,
          column: 13
        }
      },
      "88": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 70,
          column: 34
        }
      },
      "89": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 71,
          column: 79
        }
      },
      "90": {
        start: {
          line: 72,
          column: 20
        },
        end: {
          line: 72,
          column: 89
        }
      },
      "91": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 89
        }
      },
      "92": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 127
        }
      },
      "93": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 99
        }
      },
      "94": {
        start: {
          line: 75,
          column: 75
        },
        end: {
          line: 75,
          column: 99
        }
      },
      "95": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 48
        }
      },
      "96": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 77,
          column: 55
        }
      },
      "97": {
        start: {
          line: 78,
          column: 20
        },
        end: {
          line: 78,
          column: 80
        }
      },
      "98": {
        start: {
          line: 80,
          column: 20
        },
        end: {
          line: 80,
          column: 43
        }
      },
      "99": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 81,
          column: 71
        }
      },
      "100": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 82,
          column: 108
        }
      },
      "101": {
        start: {
          line: 82,
          column: 56
        },
        end: {
          line: 82,
          column: 95
        }
      },
      "102": {
        start: {
          line: 83,
          column: 20
        },
        end: {
          line: 83,
          column: 44
        }
      },
      "103": {
        start: {
          line: 85,
          column: 20
        },
        end: {
          line: 85,
          column: 47
        }
      },
      "104": {
        start: {
          line: 86,
          column: 20
        },
        end: {
          line: 86,
          column: 89
        }
      },
      "105": {
        start: {
          line: 87,
          column: 20
        },
        end: {
          line: 87,
          column: 33
        }
      },
      "106": {
        start: {
          line: 88,
          column: 24
        },
        end: {
          line: 88,
          column: 86
        }
      },
      "107": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 139,
          column: 7
        }
      },
      "108": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 138,
          column: 11
        }
      },
      "109": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 137,
          column: 13
        }
      },
      "110": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 99,
          column: 95
        }
      },
      "111": {
        start: {
          line: 101,
          column: 20
        },
        end: {
          line: 101,
          column: 40
        }
      },
      "112": {
        start: {
          line: 102,
          column: 20
        },
        end: {
          line: 102,
          column: 142
        }
      },
      "113": {
        start: {
          line: 103,
          column: 20
        },
        end: {
          line: 107,
          column: 23
        }
      },
      "114": {
        start: {
          line: 108,
          column: 20
        },
        end: {
          line: 108,
          column: 58
        }
      },
      "115": {
        start: {
          line: 108,
          column: 34
        },
        end: {
          line: 108,
          column: 58
        }
      },
      "116": {
        start: {
          line: 109,
          column: 20
        },
        end: {
          line: 109,
          column: 79
        }
      },
      "117": {
        start: {
          line: 111,
          column: 20
        },
        end: {
          line: 111,
          column: 43
        }
      },
      "118": {
        start: {
          line: 112,
          column: 20
        },
        end: {
          line: 112,
          column: 60
        }
      },
      "119": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 116,
          column: 21
        }
      },
      "120": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 73
        }
      },
      "121": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 115,
          column: 64
        }
      },
      "122": {
        start: {
          line: 117,
          column: 20
        },
        end: {
          line: 117,
          column: 50
        }
      },
      "123": {
        start: {
          line: 118,
          column: 20
        },
        end: {
          line: 121,
          column: 23
        }
      },
      "124": {
        start: {
          line: 122,
          column: 20
        },
        end: {
          line: 122,
          column: 147
        }
      },
      "125": {
        start: {
          line: 123,
          column: 20
        },
        end: {
          line: 123,
          column: 51
        }
      },
      "126": {
        start: {
          line: 125,
          column: 20
        },
        end: {
          line: 125,
          column: 54
        }
      },
      "127": {
        start: {
          line: 126,
          column: 20
        },
        end: {
          line: 129,
          column: 21
        }
      },
      "128": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 127,
          column: 72
        }
      },
      "129": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 128,
          column: 62
        }
      },
      "130": {
        start: {
          line: 130,
          column: 20
        },
        end: {
          line: 130,
          column: 48
        }
      },
      "131": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 134,
          column: 23
        }
      },
      "132": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 135,
          column: 138
        }
      },
      "133": {
        start: {
          line: 136,
          column: 20
        },
        end: {
          line: 136,
          column: 49
        }
      },
      "134": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 190,
          column: 7
        }
      },
      "135": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 189,
          column: 11
        }
      },
      "136": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 188,
          column: 13
        }
      },
      "137": {
        start: {
          line: 148,
          column: 20
        },
        end: {
          line: 152,
          column: 23
        }
      },
      "138": {
        start: {
          line: 153,
          column: 20
        },
        end: {
          line: 153,
          column: 91
        }
      },
      "139": {
        start: {
          line: 155,
          column: 20
        },
        end: {
          line: 155,
          column: 40
        }
      },
      "140": {
        start: {
          line: 156,
          column: 20
        },
        end: {
          line: 156,
          column: 142
        }
      },
      "141": {
        start: {
          line: 157,
          column: 20
        },
        end: {
          line: 161,
          column: 23
        }
      },
      "142": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 162,
          column: 58
        }
      },
      "143": {
        start: {
          line: 162,
          column: 34
        },
        end: {
          line: 162,
          column: 58
        }
      },
      "144": {
        start: {
          line: 163,
          column: 20
        },
        end: {
          line: 163,
          column: 79
        }
      },
      "145": {
        start: {
          line: 165,
          column: 20
        },
        end: {
          line: 165,
          column: 43
        }
      },
      "146": {
        start: {
          line: 166,
          column: 20
        },
        end: {
          line: 166,
          column: 138
        }
      },
      "147": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 167,
          column: 58
        }
      },
      "148": {
        start: {
          line: 168,
          column: 20
        },
        end: {
          line: 171,
          column: 21
        }
      },
      "149": {
        start: {
          line: 169,
          column: 24
        },
        end: {
          line: 169,
          column: 64
        }
      },
      "150": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 170,
          column: 52
        }
      },
      "151": {
        start: {
          line: 172,
          column: 20
        },
        end: {
          line: 172,
          column: 73
        }
      },
      "152": {
        start: {
          line: 173,
          column: 20
        },
        end: {
          line: 173,
          column: 49
        }
      },
      "153": {
        start: {
          line: 176,
          column: 20
        },
        end: {
          line: 176,
          column: 129
        }
      },
      "154": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 177,
          column: 52
        }
      },
      "155": {
        start: {
          line: 178,
          column: 20
        },
        end: {
          line: 181,
          column: 21
        }
      },
      "156": {
        start: {
          line: 179,
          column: 24
        },
        end: {
          line: 179,
          column: 63
        }
      },
      "157": {
        start: {
          line: 180,
          column: 24
        },
        end: {
          line: 180,
          column: 52
        }
      },
      "158": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 186,
          column: 23
        }
      },
      "159": {
        start: {
          line: 187,
          column: 20
        },
        end: {
          line: 187,
          column: 49
        }
      },
      "160": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 240,
          column: 7
        }
      },
      "161": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 239,
          column: 11
        }
      },
      "162": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 238,
          column: 13
        }
      },
      "163": {
        start: {
          line: 199,
          column: 20
        },
        end: {
          line: 201,
          column: 21
        }
      },
      "164": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 57
        }
      },
      "165": {
        start: {
          line: 202,
          column: 20
        },
        end: {
          line: 202,
          column: 75
        }
      },
      "166": {
        start: {
          line: 203,
          column: 20
        },
        end: {
          line: 203,
          column: 72
        }
      },
      "167": {
        start: {
          line: 204,
          column: 20
        },
        end: {
          line: 205,
          column: 58
        }
      },
      "168": {
        start: {
          line: 206,
          column: 20
        },
        end: {
          line: 206,
          column: 80
        }
      },
      "169": {
        start: {
          line: 206,
          column: 56
        },
        end: {
          line: 206,
          column: 80
        }
      },
      "170": {
        start: {
          line: 207,
          column: 20
        },
        end: {
          line: 207,
          column: 111
        }
      },
      "171": {
        start: {
          line: 208,
          column: 20
        },
        end: {
          line: 208,
          column: 64
        }
      },
      "172": {
        start: {
          line: 210,
          column: 20
        },
        end: {
          line: 210,
          column: 42
        }
      },
      "173": {
        start: {
          line: 211,
          column: 20
        },
        end: {
          line: 211,
          column: 122
        }
      },
      "174": {
        start: {
          line: 212,
          column: 20
        },
        end: {
          line: 212,
          column: 53
        }
      },
      "175": {
        start: {
          line: 215,
          column: 20
        },
        end: {
          line: 218,
          column: 21
        }
      },
      "176": {
        start: {
          line: 216,
          column: 24
        },
        end: {
          line: 216,
          column: 96
        }
      },
      "177": {
        start: {
          line: 217,
          column: 24
        },
        end: {
          line: 217,
          column: 57
        }
      },
      "178": {
        start: {
          line: 220,
          column: 20
        },
        end: {
          line: 222,
          column: 21
        }
      },
      "179": {
        start: {
          line: 221,
          column: 24
        },
        end: {
          line: 221,
          column: 124
        }
      },
      "180": {
        start: {
          line: 223,
          column: 20
        },
        end: {
          line: 223,
          column: 80
        }
      },
      "181": {
        start: {
          line: 225,
          column: 20
        },
        end: {
          line: 225,
          column: 40
        }
      },
      "182": {
        start: {
          line: 226,
          column: 20
        },
        end: {
          line: 236,
          column: 21
        }
      },
      "183": {
        start: {
          line: 228,
          column: 24
        },
        end: {
          line: 234,
          column: 25
        }
      },
      "184": {
        start: {
          line: 229,
          column: 28
        },
        end: {
          line: 233,
          column: 31
        }
      },
      "185": {
        start: {
          line: 235,
          column: 24
        },
        end: {
          line: 235,
          column: 124
        }
      },
      "186": {
        start: {
          line: 237,
          column: 20
        },
        end: {
          line: 237,
          column: 53
        }
      },
      "187": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 264,
          column: 7
        }
      },
      "188": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 263,
          column: 11
        }
      },
      "189": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 262,
          column: 13
        }
      },
      "190": {
        start: {
          line: 248,
          column: 24
        },
        end: {
          line: 248,
          column: 68
        }
      },
      "191": {
        start: {
          line: 250,
          column: 20
        },
        end: {
          line: 250,
          column: 38
        }
      },
      "192": {
        start: {
          line: 251,
          column: 20
        },
        end: {
          line: 261,
          column: 28
        }
      },
      "193": {
        start: {
          line: 267,
          column: 0
        },
        end: {
          line: 275,
          column: 19
        }
      },
      "194": {
        start: {
          line: 268,
          column: 14
        },
        end: {
          line: 268,
          column: 24
        }
      },
      "195": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 274,
          column: 7
        }
      },
      "196": {
        start: {
          line: 270,
          column: 18
        },
        end: {
          line: 270,
          column: 23
        }
      },
      "197": {
        start: {
          line: 270,
          column: 33
        },
        end: {
          line: 270,
          column: 38
        }
      },
      "198": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 273,
          column: 9
        }
      },
      "199": {
        start: {
          line: 272,
          column: 12
        },
        end: {
          line: 272,
          column: 35
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "generateCSRFToken",
        decl: {
          start: {
            line: 53,
            column: 9
          },
          end: {
            line: 53,
            column: 26
          }
        },
        loc: {
          start: {
            line: 53,
            column: 29
          },
          end: {
            line: 60,
            column: 1
          }
        },
        line: 53
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 58,
            column: 44
          },
          end: {
            line: 58,
            column: 45
          }
        },
        loc: {
          start: {
            line: 58,
            column: 60
          },
          end: {
            line: 58,
            column: 106
          }
        },
        line: 58
      },
      "15": {
        name: "createSecureGuestIdentifier",
        decl: {
          start: {
            line: 62,
            column: 9
          },
          end: {
            line: 62,
            column: 36
          }
        },
        loc: {
          start: {
            line: 62,
            column: 46
          },
          end: {
            line: 92,
            column: 1
          }
        },
        line: 62
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 63,
            column: 44
          },
          end: {
            line: 63,
            column: 45
          }
        },
        loc: {
          start: {
            line: 63,
            column: 56
          },
          end: {
            line: 91,
            column: 5
          }
        },
        line: 63
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 65,
            column: 33
          },
          end: {
            line: 65,
            column: 34
          }
        },
        loc: {
          start: {
            line: 65,
            column: 47
          },
          end: {
            line: 90,
            column: 9
          }
        },
        line: 65
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 82,
            column: 41
          },
          end: {
            line: 82,
            column: 42
          }
        },
        loc: {
          start: {
            line: 82,
            column: 54
          },
          end: {
            line: 82,
            column: 97
          }
        },
        line: 82
      },
      "19": {
        name: "getCSRFToken",
        decl: {
          start: {
            line: 93,
            column: 9
          },
          end: {
            line: 93,
            column: 21
          }
        },
        loc: {
          start: {
            line: 93,
            column: 31
          },
          end: {
            line: 140,
            column: 1
          }
        },
        line: 93
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 94,
            column: 44
          },
          end: {
            line: 94,
            column: 45
          }
        },
        loc: {
          start: {
            line: 94,
            column: 56
          },
          end: {
            line: 139,
            column: 5
          }
        },
        line: 94
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 97,
            column: 33
          },
          end: {
            line: 97,
            column: 34
          }
        },
        loc: {
          start: {
            line: 97,
            column: 47
          },
          end: {
            line: 138,
            column: 9
          }
        },
        line: 97
      },
      "22": {
        name: "validateCSRFToken",
        decl: {
          start: {
            line: 141,
            column: 9
          },
          end: {
            line: 141,
            column: 26
          }
        },
        loc: {
          start: {
            line: 141,
            column: 43
          },
          end: {
            line: 191,
            column: 1
          }
        },
        line: 141
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 142,
            column: 44
          },
          end: {
            line: 142,
            column: 45
          }
        },
        loc: {
          start: {
            line: 142,
            column: 56
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 142
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 145,
            column: 33
          },
          end: {
            line: 145,
            column: 34
          }
        },
        loc: {
          start: {
            line: 145,
            column: 47
          },
          end: {
            line: 189,
            column: 9
          }
        },
        line: 145
      },
      "25": {
        name: "withCSRFProtection",
        decl: {
          start: {
            line: 192,
            column: 9
          },
          end: {
            line: 192,
            column: 27
          }
        },
        loc: {
          start: {
            line: 192,
            column: 46
          },
          end: {
            line: 241,
            column: 1
          }
        },
        line: 192
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 193,
            column: 44
          },
          end: {
            line: 193,
            column: 45
          }
        },
        loc: {
          start: {
            line: 193,
            column: 56
          },
          end: {
            line: 240,
            column: 5
          }
        },
        line: 193
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 195,
            column: 33
          },
          end: {
            line: 195,
            column: 34
          }
        },
        loc: {
          start: {
            line: 195,
            column: 47
          },
          end: {
            line: 239,
            column: 9
          }
        },
        line: 195
      },
      "28": {
        name: "getCSRFTokenEndpoint",
        decl: {
          start: {
            line: 243,
            column: 9
          },
          end: {
            line: 243,
            column: 29
          }
        },
        loc: {
          start: {
            line: 243,
            column: 39
          },
          end: {
            line: 265,
            column: 1
          }
        },
        line: 243
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 244,
            column: 44
          },
          end: {
            line: 244,
            column: 45
          }
        },
        loc: {
          start: {
            line: 244,
            column: 56
          },
          end: {
            line: 264,
            column: 5
          }
        },
        line: 244
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 246,
            column: 33
          },
          end: {
            line: 246,
            column: 34
          }
        },
        loc: {
          start: {
            line: 246,
            column: 47
          },
          end: {
            line: 263,
            column: 9
          }
        },
        line: 246
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 267,
            column: 12
          },
          end: {
            line: 267,
            column: 13
          }
        },
        loc: {
          start: {
            line: 267,
            column: 24
          },
          end: {
            line: 275,
            column: 1
          }
        },
        line: 267
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 269,
            column: 45
          },
          end: {
            line: 269,
            column: 46
          }
        },
        loc: {
          start: {
            line: 269,
            column: 59
          },
          end: {
            line: 274,
            column: 5
          }
        },
        line: 269
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 49,
            column: 17
          },
          end: {
            line: 49,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 49,
            column: 76
          },
          end: {
            line: 49,
            column: 78
          }
        }, {
          start: {
            line: 49,
            column: 81
          },
          end: {
            line: 49,
            column: 90
          }
        }],
        line: 49
      },
      "33": {
        loc: {
          start: {
            line: 49,
            column: 17
          },
          end: {
            line: 49,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 17
          },
          end: {
            line: 49,
            column: 56
          }
        }, {
          start: {
            line: 49,
            column: 60
          },
          end: {
            line: 49,
            column: 73
          }
        }],
        line: 49
      },
      "34": {
        loc: {
          start: {
            line: 50,
            column: 0
          },
          end: {
            line: 52,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 0
          },
          end: {
            line: 52,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "35": {
        loc: {
          start: {
            line: 66,
            column: 12
          },
          end: {
            line: 89,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 78,
            column: 80
          }
        }, {
          start: {
            line: 79,
            column: 16
          },
          end: {
            line: 83,
            column: 44
          }
        }, {
          start: {
            line: 84,
            column: 16
          },
          end: {
            line: 87,
            column: 33
          }
        }, {
          start: {
            line: 88,
            column: 16
          },
          end: {
            line: 88,
            column: 86
          }
        }],
        line: 66
      },
      "36": {
        loc: {
          start: {
            line: 68,
            column: 25
          },
          end: {
            line: 70,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 25
          },
          end: {
            line: 68,
            column: 63
          }
        }, {
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 69,
            column: 56
          }
        }, {
          start: {
            line: 70,
            column: 24
          },
          end: {
            line: 70,
            column: 33
          }
        }],
        line: 68
      },
      "37": {
        loc: {
          start: {
            line: 71,
            column: 32
          },
          end: {
            line: 71,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 32
          },
          end: {
            line: 71,
            column: 65
          }
        }, {
          start: {
            line: 71,
            column: 69
          },
          end: {
            line: 71,
            column: 78
          }
        }],
        line: 71
      },
      "38": {
        loc: {
          start: {
            line: 72,
            column: 37
          },
          end: {
            line: 72,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 37
          },
          end: {
            line: 72,
            column: 75
          }
        }, {
          start: {
            line: 72,
            column: 79
          },
          end: {
            line: 72,
            column: 88
          }
        }],
        line: 72
      },
      "39": {
        loc: {
          start: {
            line: 73,
            column: 37
          },
          end: {
            line: 73,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 37
          },
          end: {
            line: 73,
            column: 75
          }
        }, {
          start: {
            line: 73,
            column: 79
          },
          end: {
            line: 73,
            column: 88
          }
        }],
        line: 73
      },
      "40": {
        loc: {
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 99
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 99
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "41": {
        loc: {
          start: {
            line: 75,
            column: 26
          },
          end: {
            line: 75,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 26
          },
          end: {
            line: 75,
            column: 55
          }
        }, {
          start: {
            line: 75,
            column: 59
          },
          end: {
            line: 75,
            column: 72
          }
        }],
        line: 75
      },
      "42": {
        loc: {
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 137,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 99,
            column: 95
          }
        }, {
          start: {
            line: 100,
            column: 16
          },
          end: {
            line: 109,
            column: 79
          }
        }, {
          start: {
            line: 110,
            column: 16
          },
          end: {
            line: 123,
            column: 51
          }
        }, {
          start: {
            line: 124,
            column: 16
          },
          end: {
            line: 136,
            column: 49
          }
        }],
        line: 98
      },
      "43": {
        loc: {
          start: {
            line: 102,
            column: 29
          },
          end: {
            line: 102,
            column: 141
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 102,
            column: 127
          },
          end: {
            line: 102,
            column: 133
          }
        }, {
          start: {
            line: 102,
            column: 136
          },
          end: {
            line: 102,
            column: 141
          }
        }],
        line: 102
      },
      "44": {
        loc: {
          start: {
            line: 102,
            column: 29
          },
          end: {
            line: 102,
            column: 124
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 29
          },
          end: {
            line: 102,
            column: 107
          }
        }, {
          start: {
            line: 102,
            column: 111
          },
          end: {
            line: 102,
            column: 124
          }
        }],
        line: 102
      },
      "45": {
        loc: {
          start: {
            line: 102,
            column: 35
          },
          end: {
            line: 102,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 102,
            column: 76
          },
          end: {
            line: 102,
            column: 82
          }
        }, {
          start: {
            line: 102,
            column: 85
          },
          end: {
            line: 102,
            column: 97
          }
        }],
        line: 102
      },
      "46": {
        loc: {
          start: {
            line: 102,
            column: 35
          },
          end: {
            line: 102,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 35
          },
          end: {
            line: 102,
            column: 51
          }
        }, {
          start: {
            line: 102,
            column: 55
          },
          end: {
            line: 102,
            column: 73
          }
        }],
        line: 102
      },
      "47": {
        loc: {
          start: {
            line: 105,
            column: 32
          },
          end: {
            line: 105,
            column: 123
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 32
          },
          end: {
            line: 105,
            column: 113
          }
        }, {
          start: {
            line: 105,
            column: 117
          },
          end: {
            line: 105,
            column: 123
          }
        }],
        line: 105
      },
      "48": {
        loc: {
          start: {
            line: 105,
            column: 33
          },
          end: {
            line: 105,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 105,
            column: 72
          },
          end: {
            line: 105,
            column: 78
          }
        }, {
          start: {
            line: 105,
            column: 81
          },
          end: {
            line: 105,
            column: 104
          }
        }],
        line: 105
      },
      "49": {
        loc: {
          start: {
            line: 105,
            column: 33
          },
          end: {
            line: 105,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 33
          },
          end: {
            line: 105,
            column: 48
          }
        }, {
          start: {
            line: 105,
            column: 52
          },
          end: {
            line: 105,
            column: 69
          }
        }],
        line: 105
      },
      "50": {
        loc: {
          start: {
            line: 106,
            column: 35
          },
          end: {
            line: 106,
            column: 162
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 106,
            column: 36
          },
          end: {
            line: 106,
            column: 151
          }
        }, {
          start: {
            line: 106,
            column: 156
          },
          end: {
            line: 106,
            column: 162
          }
        }],
        line: 106
      },
      "51": {
        loc: {
          start: {
            line: 106,
            column: 36
          },
          end: {
            line: 106,
            column: 151
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 134
          },
          end: {
            line: 106,
            column: 140
          }
        }, {
          start: {
            line: 106,
            column: 143
          },
          end: {
            line: 106,
            column: 151
          }
        }],
        line: 106
      },
      "52": {
        loc: {
          start: {
            line: 106,
            column: 36
          },
          end: {
            line: 106,
            column: 131
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 106,
            column: 36
          },
          end: {
            line: 106,
            column: 114
          }
        }, {
          start: {
            line: 106,
            column: 118
          },
          end: {
            line: 106,
            column: 131
          }
        }],
        line: 106
      },
      "53": {
        loc: {
          start: {
            line: 106,
            column: 42
          },
          end: {
            line: 106,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 83
          },
          end: {
            line: 106,
            column: 89
          }
        }, {
          start: {
            line: 106,
            column: 92
          },
          end: {
            line: 106,
            column: 104
          }
        }],
        line: 106
      },
      "54": {
        loc: {
          start: {
            line: 106,
            column: 42
          },
          end: {
            line: 106,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 106,
            column: 42
          },
          end: {
            line: 106,
            column: 58
          }
        }, {
          start: {
            line: 106,
            column: 62
          },
          end: {
            line: 106,
            column: 80
          }
        }],
        line: 106
      },
      "55": {
        loc: {
          start: {
            line: 108,
            column: 20
          },
          end: {
            line: 108,
            column: 58
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 20
          },
          end: {
            line: 108,
            column: 58
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "56": {
        loc: {
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 116,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 116,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "57": {
        loc: {
          start: {
            line: 113,
            column: 24
          },
          end: {
            line: 113,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 24
          },
          end: {
            line: 113,
            column: 34
          }
        }, {
          start: {
            line: 113,
            column: 38
          },
          end: {
            line: 113,
            column: 71
          }
        }],
        line: 113
      },
      "58": {
        loc: {
          start: {
            line: 126,
            column: 20
          },
          end: {
            line: 129,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 20
          },
          end: {
            line: 129,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "59": {
        loc: {
          start: {
            line: 126,
            column: 24
          },
          end: {
            line: 126,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 126,
            column: 24
          },
          end: {
            line: 126,
            column: 32
          }
        }, {
          start: {
            line: 126,
            column: 36
          },
          end: {
            line: 126,
            column: 67
          }
        }],
        line: 126
      },
      "60": {
        loc: {
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 188,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 153,
            column: 91
          }
        }, {
          start: {
            line: 154,
            column: 16
          },
          end: {
            line: 163,
            column: 79
          }
        }, {
          start: {
            line: 164,
            column: 16
          },
          end: {
            line: 173,
            column: 49
          }
        }, {
          start: {
            line: 174,
            column: 16
          },
          end: {
            line: 187,
            column: 49
          }
        }],
        line: 146
      },
      "61": {
        loc: {
          start: {
            line: 149,
            column: 32
          },
          end: {
            line: 149,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 149,
            column: 69
          },
          end: {
            line: 149,
            column: 75
          }
        }, {
          start: {
            line: 149,
            column: 78
          },
          end: {
            line: 149,
            column: 100
          }
        }],
        line: 149
      },
      "62": {
        loc: {
          start: {
            line: 149,
            column: 32
          },
          end: {
            line: 149,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 149,
            column: 32
          },
          end: {
            line: 149,
            column: 46
          }
        }, {
          start: {
            line: 149,
            column: 50
          },
          end: {
            line: 149,
            column: 66
          }
        }],
        line: 149
      },
      "63": {
        loc: {
          start: {
            line: 156,
            column: 29
          },
          end: {
            line: 156,
            column: 141
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 156,
            column: 127
          },
          end: {
            line: 156,
            column: 133
          }
        }, {
          start: {
            line: 156,
            column: 136
          },
          end: {
            line: 156,
            column: 141
          }
        }],
        line: 156
      },
      "64": {
        loc: {
          start: {
            line: 156,
            column: 29
          },
          end: {
            line: 156,
            column: 124
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 156,
            column: 29
          },
          end: {
            line: 156,
            column: 107
          }
        }, {
          start: {
            line: 156,
            column: 111
          },
          end: {
            line: 156,
            column: 124
          }
        }],
        line: 156
      },
      "65": {
        loc: {
          start: {
            line: 156,
            column: 35
          },
          end: {
            line: 156,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 156,
            column: 76
          },
          end: {
            line: 156,
            column: 82
          }
        }, {
          start: {
            line: 156,
            column: 85
          },
          end: {
            line: 156,
            column: 97
          }
        }],
        line: 156
      },
      "66": {
        loc: {
          start: {
            line: 156,
            column: 35
          },
          end: {
            line: 156,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 156,
            column: 35
          },
          end: {
            line: 156,
            column: 51
          }
        }, {
          start: {
            line: 156,
            column: 55
          },
          end: {
            line: 156,
            column: 73
          }
        }],
        line: 156
      },
      "67": {
        loc: {
          start: {
            line: 159,
            column: 32
          },
          end: {
            line: 159,
            column: 123
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 159,
            column: 32
          },
          end: {
            line: 159,
            column: 113
          }
        }, {
          start: {
            line: 159,
            column: 117
          },
          end: {
            line: 159,
            column: 123
          }
        }],
        line: 159
      },
      "68": {
        loc: {
          start: {
            line: 159,
            column: 33
          },
          end: {
            line: 159,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 159,
            column: 72
          },
          end: {
            line: 159,
            column: 78
          }
        }, {
          start: {
            line: 159,
            column: 81
          },
          end: {
            line: 159,
            column: 104
          }
        }],
        line: 159
      },
      "69": {
        loc: {
          start: {
            line: 159,
            column: 33
          },
          end: {
            line: 159,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 159,
            column: 33
          },
          end: {
            line: 159,
            column: 48
          }
        }, {
          start: {
            line: 159,
            column: 52
          },
          end: {
            line: 159,
            column: 69
          }
        }],
        line: 159
      },
      "70": {
        loc: {
          start: {
            line: 160,
            column: 35
          },
          end: {
            line: 160,
            column: 162
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 36
          },
          end: {
            line: 160,
            column: 151
          }
        }, {
          start: {
            line: 160,
            column: 156
          },
          end: {
            line: 160,
            column: 162
          }
        }],
        line: 160
      },
      "71": {
        loc: {
          start: {
            line: 160,
            column: 36
          },
          end: {
            line: 160,
            column: 151
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 160,
            column: 134
          },
          end: {
            line: 160,
            column: 140
          }
        }, {
          start: {
            line: 160,
            column: 143
          },
          end: {
            line: 160,
            column: 151
          }
        }],
        line: 160
      },
      "72": {
        loc: {
          start: {
            line: 160,
            column: 36
          },
          end: {
            line: 160,
            column: 131
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 36
          },
          end: {
            line: 160,
            column: 114
          }
        }, {
          start: {
            line: 160,
            column: 118
          },
          end: {
            line: 160,
            column: 131
          }
        }],
        line: 160
      },
      "73": {
        loc: {
          start: {
            line: 160,
            column: 42
          },
          end: {
            line: 160,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 160,
            column: 83
          },
          end: {
            line: 160,
            column: 89
          }
        }, {
          start: {
            line: 160,
            column: 92
          },
          end: {
            line: 160,
            column: 104
          }
        }],
        line: 160
      },
      "74": {
        loc: {
          start: {
            line: 160,
            column: 42
          },
          end: {
            line: 160,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 42
          },
          end: {
            line: 160,
            column: 58
          }
        }, {
          start: {
            line: 160,
            column: 62
          },
          end: {
            line: 160,
            column: 80
          }
        }],
        line: 160
      },
      "75": {
        loc: {
          start: {
            line: 162,
            column: 20
          },
          end: {
            line: 162,
            column: 58
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 20
          },
          end: {
            line: 162,
            column: 58
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "76": {
        loc: {
          start: {
            line: 168,
            column: 20
          },
          end: {
            line: 171,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 20
          },
          end: {
            line: 171,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "77": {
        loc: {
          start: {
            line: 168,
            column: 24
          },
          end: {
            line: 168,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 24
          },
          end: {
            line: 168,
            column: 32
          }
        }, {
          start: {
            line: 168,
            column: 36
          },
          end: {
            line: 168,
            column: 67
          }
        }, {
          start: {
            line: 168,
            column: 71
          },
          end: {
            line: 168,
            column: 95
          }
        }],
        line: 168
      },
      "78": {
        loc: {
          start: {
            line: 178,
            column: 20
          },
          end: {
            line: 181,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 20
          },
          end: {
            line: 181,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 178
      },
      "79": {
        loc: {
          start: {
            line: 178,
            column: 24
          },
          end: {
            line: 178,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 178,
            column: 24
          },
          end: {
            line: 178,
            column: 30
          }
        }, {
          start: {
            line: 178,
            column: 34
          },
          end: {
            line: 178,
            column: 63
          }
        }, {
          start: {
            line: 178,
            column: 67
          },
          end: {
            line: 178,
            column: 89
          }
        }],
        line: 178
      },
      "80": {
        loc: {
          start: {
            line: 184,
            column: 35
          },
          end: {
            line: 184,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 44
          },
          end: {
            line: 184,
            column: 74
          }
        }, {
          start: {
            line: 184,
            column: 77
          },
          end: {
            line: 184,
            column: 87
          }
        }],
        line: 184
      },
      "81": {
        loc: {
          start: {
            line: 185,
            column: 36
          },
          end: {
            line: 185,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 185,
            column: 45
          },
          end: {
            line: 185,
            column: 67
          }
        }, {
          start: {
            line: 185,
            column: 70
          },
          end: {
            line: 185,
            column: 80
          }
        }],
        line: 185
      },
      "82": {
        loc: {
          start: {
            line: 196,
            column: 12
          },
          end: {
            line: 238,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 197,
            column: 16
          },
          end: {
            line: 208,
            column: 64
          }
        }, {
          start: {
            line: 209,
            column: 16
          },
          end: {
            line: 212,
            column: 53
          }
        }, {
          start: {
            line: 213,
            column: 16
          },
          end: {
            line: 223,
            column: 80
          }
        }, {
          start: {
            line: 224,
            column: 16
          },
          end: {
            line: 237,
            column: 53
          }
        }],
        line: 196
      },
      "83": {
        loc: {
          start: {
            line: 199,
            column: 20
          },
          end: {
            line: 201,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 20
          },
          end: {
            line: 201,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 199
      },
      "84": {
        loc: {
          start: {
            line: 204,
            column: 32
          },
          end: {
            line: 205,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 32
          },
          end: {
            line: 204,
            column: 67
          }
        }, {
          start: {
            line: 205,
            column: 24
          },
          end: {
            line: 205,
            column: 57
          }
        }],
        line: 204
      },
      "85": {
        loc: {
          start: {
            line: 206,
            column: 20
          },
          end: {
            line: 206,
            column: 80
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 20
          },
          end: {
            line: 206,
            column: 80
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 206
      },
      "86": {
        loc: {
          start: {
            line: 206,
            column: 26
          },
          end: {
            line: 206,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 206,
            column: 26
          },
          end: {
            line: 206,
            column: 39
          }
        }, {
          start: {
            line: 206,
            column: 43
          },
          end: {
            line: 206,
            column: 53
          }
        }],
        line: 206
      },
      "87": {
        loc: {
          start: {
            line: 215,
            column: 20
          },
          end: {
            line: 218,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 20
          },
          end: {
            line: 218,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 215
      },
      "88": {
        loc: {
          start: {
            line: 215,
            column: 24
          },
          end: {
            line: 215,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 24
          },
          end: {
            line: 215,
            column: 41
          }
        }, {
          start: {
            line: 215,
            column: 45
          },
          end: {
            line: 215,
            column: 55
          }
        }],
        line: 215
      },
      "89": {
        loc: {
          start: {
            line: 220,
            column: 20
          },
          end: {
            line: 222,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 20
          },
          end: {
            line: 222,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "90": {
        loc: {
          start: {
            line: 226,
            column: 20
          },
          end: {
            line: 236,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 20
          },
          end: {
            line: 236,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "91": {
        loc: {
          start: {
            line: 228,
            column: 24
          },
          end: {
            line: 234,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 24
          },
          end: {
            line: 234,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "92": {
        loc: {
          start: {
            line: 247,
            column: 12
          },
          end: {
            line: 262,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 248,
            column: 16
          },
          end: {
            line: 248,
            column: 68
          }
        }, {
          start: {
            line: 249,
            column: 16
          },
          end: {
            line: 261,
            column: 28
          }
        }],
        line: 247
      },
      "93": {
        loc: {
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0],
      "36": [0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0, 0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0, 0],
      "78": [0, 0],
      "79": [0, 0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0, 0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/csrf.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,8CAQC;AAkCD,oCA8CC;AAED,8CAgDC;AAED,gDA0DC;AAGD,oDAiBC;AAzOD,sCAAwD;AACxD,uCAAkD;AAClD,+BAAqC;AAOrC,gEAAgE;AAChE,IAAM,UAAU,GAAG,MAAA,UAAU,CAAC,YAAY,mCAAI,IAAI,GAAG,EAAgD,CAAC;AACtG,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;IAC3C,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC;AACvC,CAAC;AAED,SAAgB,iBAAiB;IAC/B,uDAAuD;IACvD,IAAM,IAAI,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;IACjC,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC1C,IAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/D,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAlC,CAAkC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAE/F,OAAO,UAAG,IAAI,cAAI,SAAS,cAAI,SAAS,CAAE,CAAC;AAC7C,CAAC;AAED,oDAAoD;AACpD,SAAe,2BAA2B,CAAC,OAAoB;mCAAG,OAAO;;;;;oBAEjE,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;wBACtC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;wBAChC,SAAS,CAAC;oBACf,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;oBAC3D,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC;oBACrE,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC;oBAGrE,WAAW,GAAG,UAAG,EAAE,cAAI,SAAS,cAAI,cAAc,cAAI,cAAc,CAAE,CAAC;yBAKzE,CAAA,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,MAAM,CAAA,EAA9C,wBAA8C;oBAE1C,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;oBAC5B,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;oBACtB,qBAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,EAAA;;oBAAxD,UAAU,GAAG,SAA2C;oBACxD,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;oBACzD,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAA/B,CAA+B,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;;oBAG9D,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACjC,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;wBAGvE,sBAAO,gBAAS,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE,EAAC;;;;CACzC;AAED,SAAsB,YAAY,CAAC,OAAoB;mCAAG,OAAO;;;;;wBAE/C,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;oBAA7C,OAAO,GAAG,SAAmC;oBAC7C,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAC;oBAEjC,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;wBACvC,UAAU,EAAE,CAAC,CAAC,OAAO;wBACrB,MAAM,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK,IAAI,MAAM;wBAClD,SAAS,EAAE,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,KAAI,MAAM;qBAC1C,CAAC,CAAC;yBAEC,CAAC,MAAM,EAAP,wBAAO;oBAEU,qBAAM,2BAA2B,CAAC,OAAO,CAAC,EAAA;;oBAAvD,UAAU,GAAG,SAA0C;oBAEvD,aAAW,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAC5C,IAAI,UAAQ,IAAI,UAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;wBAChD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;wBACjD,sBAAO,UAAQ,CAAC,KAAK,EAAC;oBACxB,CAAC;oBAEK,UAAQ,iBAAiB,EAAE,CAAC;oBAClC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE;wBACzB,KAAK,SAAA;wBACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS;qBACnD,CAAC,CAAC;oBAEH,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC/H,sBAAO,OAAK,EAAC;;oBAIT,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACxC,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;wBAChD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;wBAChD,sBAAO,QAAQ,CAAC,KAAK,EAAC;oBACxB,CAAC;oBAEK,KAAK,GAAG,iBAAiB,EAAE,CAAC;oBAClC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE;wBACrB,KAAK,OAAA;wBACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS;qBACnD,CAAC,CAAC;oBAEH,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;oBACtH,sBAAO,KAAK,EAAC;;;;CACd;AAED,SAAsB,iBAAiB,CAAC,OAAoB,EAAE,KAAa;mCAAG,OAAO;;;;;;oBACnF,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;wBACvC,KAAK,EAAE,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK;wBACtC,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,GAAG,EAAE,OAAO,CAAC,GAAG;qBACjB,CAAC,CAAC;oBAGa,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;oBAA7C,OAAO,GAAG,SAAmC;oBAC7C,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAC;oBAEjC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;wBAC9B,UAAU,EAAE,CAAC,CAAC,OAAO;wBACrB,MAAM,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK,IAAI,MAAM;wBAClD,SAAS,EAAE,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,KAAI,MAAM;qBAC1C,CAAC,CAAC;yBAEC,CAAC,MAAM,EAAP,wBAAO;oBAEU,qBAAM,2BAA2B,CAAC,OAAO,CAAC,EAAA;;oBAAvD,UAAU,GAAG,SAA0C;oBAE7D,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;oBAEhH,WAAS,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAC1C,IAAI,QAAM,IAAI,QAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,QAAM,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;wBACtE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;wBACxC,sBAAO,IAAI,EAAC;oBACd,CAAC;oBACD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;oBACrD,sBAAO,KAAK,EAAC;;oBAGf,uCAAuC;oBACvC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;oBAEvG,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACtC,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;wBACtE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;wBACvC,sBAAO,IAAI,EAAC;oBACd,CAAC;oBAED,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE;wBAClD,SAAS,EAAE,CAAC,CAAC,MAAM;wBACnB,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU;wBAC/D,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU;qBACzD,CAAC,CAAC;oBAEH,sBAAO,KAAK,EAAC;;;;CACd;AAED,SAAsB,kBAAkB,CACtC,OAAoB,EACpB,OAAoC;mCACnC,OAAO;;;;;oBACR,6BAA6B;oBAC7B,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;wBAC7B,sBAAO,OAAO,EAAE,EAAC;oBACnB,CAAC;oBAGK,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;oBACvD,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC;oBAGpD,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;wBACpC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;yBAG/C,CAAA,aAAa,IAAI,CAAC,SAAS,CAAA,EAA3B,wBAA2B;oBAC7B,OAAO,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;oBAEzE,qBAAM,YAAY,CAAC,OAAO,CAAC,EAAA;;oBAAvC,SAAS,GAAG,SAA2B;oBAC7C,OAAO,CAAC,GAAG,CAAC,oDAAoD,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;oBACtG,sBAAO,OAAO,EAAE,EAAC;;oBAGnB,oEAAoE;oBACpE,IAAI,iBAAiB,IAAI,CAAC,SAAS,EAAE,CAAC;wBACpC,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;wBACxE,sBAAO,OAAO,EAAE,EAAC;oBACnB,CAAC;oBAED,0DAA0D;oBAC1D,IAAI,CAAC,SAAS,EAAE,CAAC;wBACf,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAC/B,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;oBACJ,CAAC;oBAEe,qBAAM,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,EAAA;;oBAArD,OAAO,GAAG,SAA2C;oBAC3D,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,iDAAiD;wBACjD,IAAI,aAAa,EAAE,CAAC;4BAClB,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE;gCACzD,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;gCACzC,GAAG,EAAE,OAAO,CAAC,GAAG;gCAChB,MAAM,EAAE,OAAO,CAAC,MAAM;6BACvB,CAAC,CAAC;wBACL,CAAC;wBAED,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAC/B,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;oBACJ,CAAC;oBAED,sBAAO,OAAO,EAAE,EAAC;;;;CAClB;AAED,iCAAiC;AACjC,SAAsB,oBAAoB,CAAC,OAAoB;mCAAG,OAAO;;;;wBACzD,qBAAM,YAAY,CAAC,OAAO,CAAC,EAAA;;oBAAnC,KAAK,GAAG,SAA2B;oBAEzC,sBAAO,qBAAY,CAAC,IAAI,CACtB;4BACE,OAAO,EAAE,IAAI;4BACb,SAAS,EAAE,KAAK;4BAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;yBACtB,EACD;4BACE,MAAM,EAAE,GAAG;4BACX,OAAO,EAAE;gCACP,eAAe,EAAE,qCAAqC;gCACtD,QAAQ,EAAE,UAAU;6BACrB;yBACF,CACF,EAAC;;;;CACH;AAED,sCAAsC;AACtC,WAAW,CAAC;IACV,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,EAAY;YAAX,GAAG,QAAA,EAAE,KAAK,QAAA;QACnD,IAAI,KAAK,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;YAC3B,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,4BAA4B",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/csrf.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from './auth';\n\n// Global persistent token storage for CSRF tokens\ndeclare global {\n  var __csrfTokens: Map<string, { token: string; expiresAt: number }> | undefined;\n}\n\n// Use global variable to persist across requests in development\nconst csrfTokens = globalThis.__csrfTokens ?? new Map<string, { token: string; expiresAt: number }>();\nif (process.env.NODE_ENV === 'development') {\n  globalThis.__csrfTokens = csrfTokens;\n}\n\nexport function generateCSRFToken(): string {\n  // Generate a more secure token with additional entropy\n  const uuid = crypto.randomUUID();\n  const timestamp = Date.now().toString(36);\n  const randomBytes = crypto.getRandomValues(new Uint8Array(16));\n  const randomHex = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');\n\n  return `${uuid}-${timestamp}-${randomHex}`;\n}\n\n// Helper function to create secure guest identifier\nasync function createSecureGuestIdentifier(request: NextRequest): Promise<string> {\n  // Get multiple identifying factors\n  const ip = request.headers.get('x-forwarded-for') ||\n             request.headers.get('x-real-ip') ||\n             'unknown';\n  const userAgent = request.headers.get('user-agent') || 'unknown';\n  const acceptLanguage = request.headers.get('accept-language') || 'unknown';\n  const acceptEncoding = request.headers.get('accept-encoding') || 'unknown';\n\n  // Create a more unique fingerprint\n  const fingerprint = `${ip}_${userAgent}_${acceptLanguage}_${acceptEncoding}`;\n\n  // Hash the fingerprint for privacy and security\n  let hash: string;\n\n  if (typeof crypto !== 'undefined' && crypto.subtle) {\n    // Use Web Crypto API when available (browser/edge runtime)\n    const encoder = new TextEncoder();\n    const data = encoder.encode(fingerprint);\n    const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n    const hashArray = Array.from(new Uint8Array(hashBuffer));\n    hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n  } else {\n    // Fallback for Node.js environment (tests)\n    const crypto = require('crypto');\n    hash = crypto.createHash('sha256').update(fingerprint).digest('hex');\n  }\n\n  return `guest_${hash.substring(0, 32)}`;\n}\n\nexport async function getCSRFToken(request: NextRequest): Promise<string> {\n  // Get user session to create a unique identifier\n  const session = await getServerSession(authOptions);\n  const userId = session?.user?.id;\n\n  console.log('\uD83D\uDD11 CSRF Token Generation:', {\n    hasSession: !!session,\n    userId: userId?.substring(0, 10) + '...' || 'none',\n    userEmail: session?.user?.email || 'none'\n  });\n\n  if (!userId) {\n    // For non-authenticated users, use secure fingerprinting\n    const identifier = await createSecureGuestIdentifier(request);\n\n    const existing = csrfTokens.get(identifier);\n    if (existing && existing.expiresAt > Date.now()) {\n      console.log('\uD83D\uDD04 Returning existing guest token');\n      return existing.token;\n    }\n\n    const token = generateCSRFToken();\n    csrfTokens.set(identifier, {\n      token,\n      expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour\n    });\n\n    console.log('\uD83C\uDD95 Generated new guest token:', { identifier: identifier.substring(0, 20) + '...', tokenCount: csrfTokens.size });\n    return token;\n  }\n\n  // For authenticated users, use user ID\n  const existing = csrfTokens.get(userId);\n  if (existing && existing.expiresAt > Date.now()) {\n    console.log('\uD83D\uDD04 Returning existing user token');\n    return existing.token;\n  }\n\n  const token = generateCSRFToken();\n  csrfTokens.set(userId, {\n    token,\n    expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour\n  });\n\n  console.log('\uD83C\uDD95 Generated new user token:', { userId: userId.substring(0, 10) + '...', tokenCount: csrfTokens.size });\n  return token;\n}\n\nexport async function validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {\n  console.log('\uD83D\uDD0D CSRF Validation Debug:', {\n    token: token?.substring(0, 10) + '...',\n    method: request.method,\n    url: request.url\n  });\n\n  // Get user session to create a unique identifier\n  const session = await getServerSession(authOptions);\n  const userId = session?.user?.id;\n\n  console.log('\uD83D\uDC64 Session Info:', {\n    hasSession: !!session,\n    userId: userId?.substring(0, 10) + '...' || 'none',\n    userEmail: session?.user?.email || 'none'\n  });\n\n  if (!userId) {\n    // For non-authenticated users, use secure fingerprinting\n    const identifier = await createSecureGuestIdentifier(request);\n\n    console.log('\uD83C\uDF10 Guest validation:', { identifier: identifier.substring(0, 20) + '...', tokenCount: csrfTokens.size });\n\n    const stored = csrfTokens.get(identifier);\n    if (stored && stored.expiresAt > Date.now() && stored.token === token) {\n      console.log('\u2705 Guest CSRF token valid');\n      return true;\n    }\n    console.log('\u274C Guest CSRF token invalid or expired');\n    return false;\n  }\n\n  // For authenticated users, use user ID\n  console.log('\uD83D\uDD10 User validation:', { userId: userId.substring(0, 10) + '...', tokenCount: csrfTokens.size });\n\n  const stored = csrfTokens.get(userId);\n  if (stored && stored.expiresAt > Date.now() && stored.token === token) {\n    console.log('\u2705 User CSRF token valid');\n    return true;\n  }\n\n  console.log('\u274C User CSRF token invalid or expired', {\n    hasStored: !!stored,\n    isExpired: stored ? stored.expiresAt <= Date.now() : 'no-token',\n    tokenMatch: stored ? stored.token === token : 'no-token'\n  });\n\n  return false;\n}\n\nexport async function withCSRFProtection(\n  request: NextRequest,\n  handler: () => Promise<NextResponse>\n): Promise<NextResponse> {\n  // Skip CSRF for GET requests\n  if (request.method === 'GET') {\n    return handler();\n  }\n\n  // Enhanced development mode - still validate but with relaxed requirements\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const isTestEnvironment = process.env.NODE_ENV === 'test';\n\n  // Get CSRF token from header or body\n  const csrfToken = request.headers.get('x-csrf-token') ||\n                   request.headers.get('csrf-token');\n\n  // In development, allow requests without CSRF token but log warning\n  if (isDevelopment && !csrfToken) {\n    console.warn('\u26A0\uFE0F  CSRF token missing in development mode - this would fail in production');\n    // Generate and validate a temporary token for development consistency\n    const tempToken = await getCSRFToken(request);\n    console.log('\uD83D\uDD27 Generated temporary CSRF token for development:', tempToken.substring(0, 10) + '...');\n    return handler();\n  }\n\n  // In test environment, be more lenient but still validate structure\n  if (isTestEnvironment && !csrfToken) {\n    console.log('\uD83E\uDDEA Test environment: Allowing request without CSRF token');\n    return handler();\n  }\n\n  // Production and development with token - always validate\n  if (!csrfToken) {\n    return NextResponse.json(\n      { error: 'CSRF token missing' },\n      { status: 403 }\n    );\n  }\n\n  const isValid = await validateCSRFToken(request, csrfToken);\n  if (!isValid) {\n    // In development, log more details for debugging\n    if (isDevelopment) {\n      console.error('\uD83D\uDD12 CSRF validation failed in development:', {\n        token: csrfToken.substring(0, 10) + '...',\n        url: request.url,\n        method: request.method\n      });\n    }\n\n    return NextResponse.json(\n      { error: 'Invalid CSRF token' },\n      { status: 403 }\n    );\n  }\n\n  return handler();\n}\n\n// API endpoint to get CSRF token\nexport async function getCSRFTokenEndpoint(request: NextRequest): Promise<NextResponse> {\n  const token = await getCSRFToken(request);\n\n  return NextResponse.json(\n    {\n      success: true,\n      csrfToken: token,\n      timestamp: Date.now()\n    },\n    {\n      status: 200,\n      headers: {\n        'Cache-Control': 'no-store, no-cache, must-revalidate',\n        'Pragma': 'no-cache'\n      }\n    }\n  );\n}\n\n// Cleanup expired tokens periodically\nsetInterval(() => {\n  const now = Date.now();\n  Array.from(csrfTokens.entries()).forEach(([key, value]) => {\n    if (value.expiresAt <= now) {\n      csrfTokens.delete(key);\n    }\n  });\n}, 15 * 60 * 1000); // Clean up every 15 minutes\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8e79dbcaae02908660e5a76fd1b7a51b83244a19"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_sf9egw5x0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_sf9egw5x0();
var __awaiter =
/* istanbul ignore next */
(cov_sf9egw5x0().s[0]++,
/* istanbul ignore next */
(cov_sf9egw5x0().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_sf9egw5x0().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_sf9egw5x0().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[1]++;
    cov_sf9egw5x0().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[2]++;
      cov_sf9egw5x0().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_sf9egw5x0().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_sf9egw5x0().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_sf9egw5x0().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[4]++;
      cov_sf9egw5x0().s[4]++;
      try {
        /* istanbul ignore next */
        cov_sf9egw5x0().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_sf9egw5x0().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[5]++;
      cov_sf9egw5x0().s[7]++;
      try {
        /* istanbul ignore next */
        cov_sf9egw5x0().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_sf9egw5x0().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[6]++;
      cov_sf9egw5x0().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_sf9egw5x0().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_sf9egw5x0().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_sf9egw5x0().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_sf9egw5x0().s[12]++,
/* istanbul ignore next */
(cov_sf9egw5x0().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_sf9egw5x0().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_sf9egw5x0().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_sf9egw5x0().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_sf9egw5x0().f[8]++;
        cov_sf9egw5x0().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_sf9egw5x0().b[6][0]++;
          cov_sf9egw5x0().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_sf9egw5x0().b[6][1]++;
        }
        cov_sf9egw5x0().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_sf9egw5x0().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_sf9egw5x0().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_sf9egw5x0().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_sf9egw5x0().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[9]++;
    cov_sf9egw5x0().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[10]++;
    cov_sf9egw5x0().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[11]++;
      cov_sf9egw5x0().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[12]++;
    cov_sf9egw5x0().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_sf9egw5x0().b[9][0]++;
      cov_sf9egw5x0().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_sf9egw5x0().b[9][1]++;
    }
    cov_sf9egw5x0().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_sf9egw5x0().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_sf9egw5x0().s[25]++;
      try {
        /* istanbul ignore next */
        cov_sf9egw5x0().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[15][0]++,
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[16][1]++,
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_sf9egw5x0().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_sf9egw5x0().b[12][0]++;
          cov_sf9egw5x0().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_sf9egw5x0().b[12][1]++;
        }
        cov_sf9egw5x0().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_sf9egw5x0().b[18][0]++;
          cov_sf9egw5x0().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_sf9egw5x0().b[18][1]++;
        }
        cov_sf9egw5x0().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_sf9egw5x0().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_sf9egw5x0().b[19][1]++;
            cov_sf9egw5x0().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_sf9egw5x0().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_sf9egw5x0().b[19][2]++;
            cov_sf9egw5x0().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_sf9egw5x0().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_sf9egw5x0().b[19][3]++;
            cov_sf9egw5x0().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_sf9egw5x0().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_sf9egw5x0().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_sf9egw5x0().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_sf9egw5x0().b[19][4]++;
            cov_sf9egw5x0().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_sf9egw5x0().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_sf9egw5x0().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_sf9egw5x0().b[19][5]++;
            cov_sf9egw5x0().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_sf9egw5x0().b[20][0]++;
              cov_sf9egw5x0().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_sf9egw5x0().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_sf9egw5x0().b[20][1]++;
            }
            cov_sf9egw5x0().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_sf9egw5x0().b[23][0]++;
              cov_sf9egw5x0().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_sf9egw5x0().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_sf9egw5x0().b[23][1]++;
            }
            cov_sf9egw5x0().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_sf9egw5x0().b[25][0]++;
              cov_sf9egw5x0().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_sf9egw5x0().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_sf9egw5x0().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_sf9egw5x0().b[25][1]++;
            }
            cov_sf9egw5x0().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_sf9egw5x0().b[27][0]++;
              cov_sf9egw5x0().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_sf9egw5x0().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_sf9egw5x0().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_sf9egw5x0().b[27][1]++;
            }
            cov_sf9egw5x0().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_sf9egw5x0().b[29][0]++;
              cov_sf9egw5x0().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_sf9egw5x0().b[29][1]++;
            }
            cov_sf9egw5x0().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_sf9egw5x0().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_sf9egw5x0().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_sf9egw5x0().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_sf9egw5x0().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_sf9egw5x0().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_sf9egw5x0().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_sf9egw5x0().b[30][0]++;
      cov_sf9egw5x0().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_sf9egw5x0().b[30][1]++;
    }
    cov_sf9egw5x0().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_sf9egw5x0().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_sf9egw5x0().b[31][1]++, void 0),
      done: true
    };
  }
}));
var _a;
/* istanbul ignore next */
cov_sf9egw5x0().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_sf9egw5x0().s[68]++;
exports.generateCSRFToken = generateCSRFToken;
/* istanbul ignore next */
cov_sf9egw5x0().s[69]++;
exports.getCSRFToken = getCSRFToken;
/* istanbul ignore next */
cov_sf9egw5x0().s[70]++;
exports.validateCSRFToken = validateCSRFToken;
/* istanbul ignore next */
cov_sf9egw5x0().s[71]++;
exports.withCSRFProtection = withCSRFProtection;
/* istanbul ignore next */
cov_sf9egw5x0().s[72]++;
exports.getCSRFTokenEndpoint = getCSRFTokenEndpoint;
var server_1 =
/* istanbul ignore next */
(cov_sf9egw5x0().s[73]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_sf9egw5x0().s[74]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_sf9egw5x0().s[75]++, require("./auth"));
// Use global variable to persist across requests in development
var csrfTokens =
/* istanbul ignore next */
(cov_sf9egw5x0().s[76]++,
/* istanbul ignore next */
(cov_sf9egw5x0().b[33][0]++, (_a = globalThis.__csrfTokens) !== null) &&
/* istanbul ignore next */
(cov_sf9egw5x0().b[33][1]++, _a !== void 0) ?
/* istanbul ignore next */
(cov_sf9egw5x0().b[32][0]++, _a) :
/* istanbul ignore next */
(cov_sf9egw5x0().b[32][1]++, new Map()));
/* istanbul ignore next */
cov_sf9egw5x0().s[77]++;
if (process.env.NODE_ENV === 'development') {
  /* istanbul ignore next */
  cov_sf9egw5x0().b[34][0]++;
  cov_sf9egw5x0().s[78]++;
  globalThis.__csrfTokens = csrfTokens;
} else
/* istanbul ignore next */
{
  cov_sf9egw5x0().b[34][1]++;
}
function generateCSRFToken() {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[13]++;
  // Generate a more secure token with additional entropy
  var uuid =
  /* istanbul ignore next */
  (cov_sf9egw5x0().s[79]++, crypto.randomUUID());
  var timestamp =
  /* istanbul ignore next */
  (cov_sf9egw5x0().s[80]++, Date.now().toString(36));
  var randomBytes =
  /* istanbul ignore next */
  (cov_sf9egw5x0().s[81]++, crypto.getRandomValues(new Uint8Array(16)));
  var randomHex =
  /* istanbul ignore next */
  (cov_sf9egw5x0().s[82]++, Array.from(randomBytes, function (byte) {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[14]++;
    cov_sf9egw5x0().s[83]++;
    return byte.toString(16).padStart(2, '0');
  }).join(''));
  /* istanbul ignore next */
  cov_sf9egw5x0().s[84]++;
  return "".concat(uuid, "-").concat(timestamp, "-").concat(randomHex);
}
// Helper function to create secure guest identifier
function createSecureGuestIdentifier(request) {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[15]++;
  cov_sf9egw5x0().s[85]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[16]++;
    var ip, userAgent, acceptLanguage, acceptEncoding, fingerprint, hash, encoder, data, hashBuffer, hashArray, crypto;
    /* istanbul ignore next */
    cov_sf9egw5x0().s[86]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[17]++;
      cov_sf9egw5x0().s[87]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[35][0]++;
          cov_sf9egw5x0().s[88]++;
          ip =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[36][0]++, request.headers.get('x-forwarded-for')) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[36][1]++, request.headers.get('x-real-ip')) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[36][2]++, 'unknown');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[89]++;
          userAgent =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[37][0]++, request.headers.get('user-agent')) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[37][1]++, 'unknown');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[90]++;
          acceptLanguage =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[38][0]++, request.headers.get('accept-language')) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[38][1]++, 'unknown');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[91]++;
          acceptEncoding =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[39][0]++, request.headers.get('accept-encoding')) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[39][1]++, 'unknown');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[92]++;
          fingerprint = "".concat(ip, "_").concat(userAgent, "_").concat(acceptLanguage, "_").concat(acceptEncoding);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[93]++;
          if (!(
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[41][0]++, typeof crypto !== 'undefined') &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[41][1]++, crypto.subtle))) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[40][0]++;
            cov_sf9egw5x0().s[94]++;
            return [3 /*break*/, 2];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[40][1]++;
          }
          cov_sf9egw5x0().s[95]++;
          encoder = new TextEncoder();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[96]++;
          data = encoder.encode(fingerprint);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[97]++;
          return [4 /*yield*/, crypto.subtle.digest('SHA-256', data)];
        case 1:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[35][1]++;
          cov_sf9egw5x0().s[98]++;
          hashBuffer = _a.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[99]++;
          hashArray = Array.from(new Uint8Array(hashBuffer));
          /* istanbul ignore next */
          cov_sf9egw5x0().s[100]++;
          hash = hashArray.map(function (b) {
            /* istanbul ignore next */
            cov_sf9egw5x0().f[18]++;
            cov_sf9egw5x0().s[101]++;
            return b.toString(16).padStart(2, '0');
          }).join('');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[102]++;
          return [3 /*break*/, 3];
        case 2:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[35][2]++;
          cov_sf9egw5x0().s[103]++;
          crypto = require('crypto');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[104]++;
          hash = crypto.createHash('sha256').update(fingerprint).digest('hex');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[105]++;
          _a.label = 3;
        case 3:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[35][3]++;
          cov_sf9egw5x0().s[106]++;
          return [2 /*return*/, "guest_".concat(hash.substring(0, 32))];
      }
    });
  });
}
function getCSRFToken(request) {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[19]++;
  cov_sf9egw5x0().s[107]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[20]++;
    var session, userId, identifier, existing_1, token_1, existing, token;
    var _a, _b;
    /* istanbul ignore next */
    cov_sf9egw5x0().s[108]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[21]++;
      cov_sf9egw5x0().s[109]++;
      switch (_c.label) {
        case 0:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[42][0]++;
          cov_sf9egw5x0().s[110]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[42][1]++;
          cov_sf9egw5x0().s[111]++;
          session = _c.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[112]++;
          userId =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[44][0]++, (_a =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[46][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[46][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[45][0]++, void 0) :
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[45][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[44][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[43][0]++, void 0) :
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[43][1]++, _a.id);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[113]++;
          console.log('🔑 CSRF Token Generation:', {
            hasSession: !!session,
            userId:
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[47][0]++, (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[49][0]++, userId === null) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[49][1]++, userId === void 0) ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[48][0]++, void 0) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[48][1]++, userId.substring(0, 10))) + '...') ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[47][1]++, 'none'),
            userEmail:
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[50][0]++,
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[52][0]++, (_b =
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[54][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[54][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[53][0]++, void 0) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[53][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[52][1]++, _b === void 0) ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[51][0]++, void 0) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[51][1]++, _b.email)) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[50][1]++, 'none')
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[114]++;
          if (!!userId) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[55][0]++;
            cov_sf9egw5x0().s[115]++;
            return [3 /*break*/, 3];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[55][1]++;
          }
          cov_sf9egw5x0().s[116]++;
          return [4 /*yield*/, createSecureGuestIdentifier(request)];
        case 2:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[42][2]++;
          cov_sf9egw5x0().s[117]++;
          identifier = _c.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[118]++;
          existing_1 = csrfTokens.get(identifier);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[119]++;
          if (
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[57][0]++, existing_1) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[57][1]++, existing_1.expiresAt > Date.now())) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[56][0]++;
            cov_sf9egw5x0().s[120]++;
            console.log('🔄 Returning existing guest token');
            /* istanbul ignore next */
            cov_sf9egw5x0().s[121]++;
            return [2 /*return*/, existing_1.token];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[56][1]++;
          }
          cov_sf9egw5x0().s[122]++;
          token_1 = generateCSRFToken();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[123]++;
          csrfTokens.set(identifier, {
            token: token_1,
            expiresAt: Date.now() + 60 * 60 * 1000 // 1 hour
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[124]++;
          console.log('🆕 Generated new guest token:', {
            identifier: identifier.substring(0, 20) + '...',
            tokenCount: csrfTokens.size
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[125]++;
          return [2 /*return*/, token_1];
        case 3:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[42][3]++;
          cov_sf9egw5x0().s[126]++;
          existing = csrfTokens.get(userId);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[127]++;
          if (
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[59][0]++, existing) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[59][1]++, existing.expiresAt > Date.now())) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[58][0]++;
            cov_sf9egw5x0().s[128]++;
            console.log('🔄 Returning existing user token');
            /* istanbul ignore next */
            cov_sf9egw5x0().s[129]++;
            return [2 /*return*/, existing.token];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[58][1]++;
          }
          cov_sf9egw5x0().s[130]++;
          token = generateCSRFToken();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[131]++;
          csrfTokens.set(userId, {
            token: token,
            expiresAt: Date.now() + 60 * 60 * 1000 // 1 hour
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[132]++;
          console.log('🆕 Generated new user token:', {
            userId: userId.substring(0, 10) + '...',
            tokenCount: csrfTokens.size
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[133]++;
          return [2 /*return*/, token];
      }
    });
  });
}
function validateCSRFToken(request, token) {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[22]++;
  cov_sf9egw5x0().s[134]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[23]++;
    var session, userId, identifier, stored_1, stored;
    var _a, _b;
    /* istanbul ignore next */
    cov_sf9egw5x0().s[135]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[24]++;
      cov_sf9egw5x0().s[136]++;
      switch (_c.label) {
        case 0:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[60][0]++;
          cov_sf9egw5x0().s[137]++;
          console.log('🔍 CSRF Validation Debug:', {
            token: (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[62][0]++, token === null) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[62][1]++, token === void 0) ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[61][0]++, void 0) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[61][1]++, token.substring(0, 10))) + '...',
            method: request.method,
            url: request.url
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[138]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[60][1]++;
          cov_sf9egw5x0().s[139]++;
          session = _c.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[140]++;
          userId =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[64][0]++, (_a =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[66][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[66][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[65][0]++, void 0) :
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[65][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[64][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[63][0]++, void 0) :
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[63][1]++, _a.id);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[141]++;
          console.log('👤 Session Info:', {
            hasSession: !!session,
            userId:
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[67][0]++, (
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[69][0]++, userId === null) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[69][1]++, userId === void 0) ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[68][0]++, void 0) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[68][1]++, userId.substring(0, 10))) + '...') ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[67][1]++, 'none'),
            userEmail:
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[70][0]++,
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[72][0]++, (_b =
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[74][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[74][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[73][0]++, void 0) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[73][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[72][1]++, _b === void 0) ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[71][0]++, void 0) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[71][1]++, _b.email)) ||
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[70][1]++, 'none')
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[142]++;
          if (!!userId) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[75][0]++;
            cov_sf9egw5x0().s[143]++;
            return [3 /*break*/, 3];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[75][1]++;
          }
          cov_sf9egw5x0().s[144]++;
          return [4 /*yield*/, createSecureGuestIdentifier(request)];
        case 2:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[60][2]++;
          cov_sf9egw5x0().s[145]++;
          identifier = _c.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[146]++;
          console.log('🌐 Guest validation:', {
            identifier: identifier.substring(0, 20) + '...',
            tokenCount: csrfTokens.size
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[147]++;
          stored_1 = csrfTokens.get(identifier);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[148]++;
          if (
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[77][0]++, stored_1) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[77][1]++, stored_1.expiresAt > Date.now()) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[77][2]++, stored_1.token === token)) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[76][0]++;
            cov_sf9egw5x0().s[149]++;
            console.log('✅ Guest CSRF token valid');
            /* istanbul ignore next */
            cov_sf9egw5x0().s[150]++;
            return [2 /*return*/, true];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[76][1]++;
          }
          cov_sf9egw5x0().s[151]++;
          console.log('❌ Guest CSRF token invalid or expired');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[152]++;
          return [2 /*return*/, false];
        case 3:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[60][3]++;
          cov_sf9egw5x0().s[153]++;
          // For authenticated users, use user ID
          console.log('🔐 User validation:', {
            userId: userId.substring(0, 10) + '...',
            tokenCount: csrfTokens.size
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[154]++;
          stored = csrfTokens.get(userId);
          /* istanbul ignore next */
          cov_sf9egw5x0().s[155]++;
          if (
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[79][0]++, stored) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[79][1]++, stored.expiresAt > Date.now()) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[79][2]++, stored.token === token)) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[78][0]++;
            cov_sf9egw5x0().s[156]++;
            console.log('✅ User CSRF token valid');
            /* istanbul ignore next */
            cov_sf9egw5x0().s[157]++;
            return [2 /*return*/, true];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[78][1]++;
          }
          cov_sf9egw5x0().s[158]++;
          console.log('❌ User CSRF token invalid or expired', {
            hasStored: !!stored,
            isExpired: stored ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[80][0]++, stored.expiresAt <= Date.now()) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[80][1]++, 'no-token'),
            tokenMatch: stored ?
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[81][0]++, stored.token === token) :
            /* istanbul ignore next */
            (cov_sf9egw5x0().b[81][1]++, 'no-token')
          });
          /* istanbul ignore next */
          cov_sf9egw5x0().s[159]++;
          return [2 /*return*/, false];
      }
    });
  });
}
function withCSRFProtection(request, handler) {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[25]++;
  cov_sf9egw5x0().s[160]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[26]++;
    var isDevelopment, isTestEnvironment, csrfToken, tempToken, isValid;
    /* istanbul ignore next */
    cov_sf9egw5x0().s[161]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[27]++;
      cov_sf9egw5x0().s[162]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[82][0]++;
          cov_sf9egw5x0().s[163]++;
          // Skip CSRF for GET requests
          if (request.method === 'GET') {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[83][0]++;
            cov_sf9egw5x0().s[164]++;
            return [2 /*return*/, handler()];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[83][1]++;
          }
          cov_sf9egw5x0().s[165]++;
          isDevelopment = process.env.NODE_ENV === 'development';
          /* istanbul ignore next */
          cov_sf9egw5x0().s[166]++;
          isTestEnvironment = process.env.NODE_ENV === 'test';
          /* istanbul ignore next */
          cov_sf9egw5x0().s[167]++;
          csrfToken =
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[84][0]++, request.headers.get('x-csrf-token')) ||
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[84][1]++, request.headers.get('csrf-token'));
          /* istanbul ignore next */
          cov_sf9egw5x0().s[168]++;
          if (!(
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[86][0]++, isDevelopment) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[86][1]++, !csrfToken))) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[85][0]++;
            cov_sf9egw5x0().s[169]++;
            return [3 /*break*/, 2];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[85][1]++;
          }
          cov_sf9egw5x0().s[170]++;
          console.warn('⚠️  CSRF token missing in development mode - this would fail in production');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[171]++;
          return [4 /*yield*/, getCSRFToken(request)];
        case 1:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[82][1]++;
          cov_sf9egw5x0().s[172]++;
          tempToken = _a.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[173]++;
          console.log('🔧 Generated temporary CSRF token for development:', tempToken.substring(0, 10) + '...');
          /* istanbul ignore next */
          cov_sf9egw5x0().s[174]++;
          return [2 /*return*/, handler()];
        case 2:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[82][2]++;
          cov_sf9egw5x0().s[175]++;
          // In test environment, be more lenient but still validate structure
          if (
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[88][0]++, isTestEnvironment) &&
          /* istanbul ignore next */
          (cov_sf9egw5x0().b[88][1]++, !csrfToken)) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[87][0]++;
            cov_sf9egw5x0().s[176]++;
            console.log('🧪 Test environment: Allowing request without CSRF token');
            /* istanbul ignore next */
            cov_sf9egw5x0().s[177]++;
            return [2 /*return*/, handler()];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[87][1]++;
          }
          // Production and development with token - always validate
          cov_sf9egw5x0().s[178]++;
          if (!csrfToken) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[89][0]++;
            cov_sf9egw5x0().s[179]++;
            return [2 /*return*/, server_1.NextResponse.json({
              error: 'CSRF token missing'
            }, {
              status: 403
            })];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[89][1]++;
          }
          cov_sf9egw5x0().s[180]++;
          return [4 /*yield*/, validateCSRFToken(request, csrfToken)];
        case 3:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[82][3]++;
          cov_sf9egw5x0().s[181]++;
          isValid = _a.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[182]++;
          if (!isValid) {
            /* istanbul ignore next */
            cov_sf9egw5x0().b[90][0]++;
            cov_sf9egw5x0().s[183]++;
            // In development, log more details for debugging
            if (isDevelopment) {
              /* istanbul ignore next */
              cov_sf9egw5x0().b[91][0]++;
              cov_sf9egw5x0().s[184]++;
              console.error('🔒 CSRF validation failed in development:', {
                token: csrfToken.substring(0, 10) + '...',
                url: request.url,
                method: request.method
              });
            } else
            /* istanbul ignore next */
            {
              cov_sf9egw5x0().b[91][1]++;
            }
            cov_sf9egw5x0().s[185]++;
            return [2 /*return*/, server_1.NextResponse.json({
              error: 'Invalid CSRF token'
            }, {
              status: 403
            })];
          } else
          /* istanbul ignore next */
          {
            cov_sf9egw5x0().b[90][1]++;
          }
          cov_sf9egw5x0().s[186]++;
          return [2 /*return*/, handler()];
      }
    });
  });
}
// API endpoint to get CSRF token
function getCSRFTokenEndpoint(request) {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[28]++;
  cov_sf9egw5x0().s[187]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[29]++;
    var token;
    /* istanbul ignore next */
    cov_sf9egw5x0().s[188]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_sf9egw5x0().f[30]++;
      cov_sf9egw5x0().s[189]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[92][0]++;
          cov_sf9egw5x0().s[190]++;
          return [4 /*yield*/, getCSRFToken(request)];
        case 1:
          /* istanbul ignore next */
          cov_sf9egw5x0().b[92][1]++;
          cov_sf9egw5x0().s[191]++;
          token = _a.sent();
          /* istanbul ignore next */
          cov_sf9egw5x0().s[192]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            csrfToken: token,
            timestamp: Date.now()
          }, {
            status: 200,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
              'Pragma': 'no-cache'
            }
          })];
      }
    });
  });
}
// Cleanup expired tokens periodically
/* istanbul ignore next */
cov_sf9egw5x0().s[193]++;
setInterval(function () {
  /* istanbul ignore next */
  cov_sf9egw5x0().f[31]++;
  var now =
  /* istanbul ignore next */
  (cov_sf9egw5x0().s[194]++, Date.now());
  /* istanbul ignore next */
  cov_sf9egw5x0().s[195]++;
  Array.from(csrfTokens.entries()).forEach(function (_a) {
    /* istanbul ignore next */
    cov_sf9egw5x0().f[32]++;
    var key =
      /* istanbul ignore next */
      (cov_sf9egw5x0().s[196]++, _a[0]),
      value =
      /* istanbul ignore next */
      (cov_sf9egw5x0().s[197]++, _a[1]);
    /* istanbul ignore next */
    cov_sf9egw5x0().s[198]++;
    if (value.expiresAt <= now) {
      /* istanbul ignore next */
      cov_sf9egw5x0().b[93][0]++;
      cov_sf9egw5x0().s[199]++;
      csrfTokens.delete(key);
    } else
    /* istanbul ignore next */
    {
      cov_sf9egw5x0().b[93][1]++;
    }
  });
}, 15 * 60 * 1000); // Clean up every 15 minutes
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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