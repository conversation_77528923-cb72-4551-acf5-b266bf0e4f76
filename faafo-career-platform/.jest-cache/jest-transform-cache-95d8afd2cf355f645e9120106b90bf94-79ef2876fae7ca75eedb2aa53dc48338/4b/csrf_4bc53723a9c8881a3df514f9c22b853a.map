{"version": 3, "names": ["exports", "generateCSRFToken", "cov_sf9egw5x0", "s", "getCSRFToken", "validateCSRFToken", "withCSRFProtection", "getCSRFTokenEndpoint", "server_1", "require", "next_1", "auth_1", "csrfTokens", "b", "_a", "globalThis", "__csrfTokens", "Map", "process", "env", "NODE_ENV", "f", "uuid", "crypto", "randomUUID", "timestamp", "Date", "now", "toString", "randomBytes", "getRandomValues", "Uint8Array", "randomHex", "Array", "from", "byte", "padStart", "join", "concat", "createSecureGuestIdentifier", "request", "Promise", "ip", "headers", "get", "userAgent", "acceptLanguage", "acceptEncoding", "fingerprint", "subtle", "encoder", "TextEncoder", "data", "encode", "digest", "hash<PERSON><PERSON><PERSON>", "sent", "hashArray", "hash", "map", "createHash", "update", "substring", "getServerSession", "authOptions", "session", "_c", "userId", "user", "id", "console", "log", "hasSession", "userEmail", "_b", "email", "identifier", "existing_1", "expiresAt", "token", "token_1", "set", "tokenCount", "size", "existing", "method", "url", "stored_1", "stored", "hasStored", "isExpired", "tokenMatch", "handler", "isDevelopment", "isTestEnvironment", "csrfToken", "warn", "tempToken", "NextResponse", "json", "error", "status", "<PERSON><PERSON><PERSON><PERSON>", "success", "setInterval", "entries", "for<PERSON>ach", "key", "value", "delete"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/csrf.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from './auth';\n\n// Global persistent token storage for CSRF tokens\ndeclare global {\n  var __csrfTokens: Map<string, { token: string; expiresAt: number }> | undefined;\n}\n\n// Use global variable to persist across requests in development\nconst csrfTokens = globalThis.__csrfTokens ?? new Map<string, { token: string; expiresAt: number }>();\nif (process.env.NODE_ENV === 'development') {\n  globalThis.__csrfTokens = csrfTokens;\n}\n\nexport function generateCSRFToken(): string {\n  // Generate a more secure token with additional entropy\n  const uuid = crypto.randomUUID();\n  const timestamp = Date.now().toString(36);\n  const randomBytes = crypto.getRandomValues(new Uint8Array(16));\n  const randomHex = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');\n\n  return `${uuid}-${timestamp}-${randomHex}`;\n}\n\n// Helper function to create secure guest identifier\nasync function createSecureGuestIdentifier(request: NextRequest): Promise<string> {\n  // Get multiple identifying factors\n  const ip = request.headers.get('x-forwarded-for') ||\n             request.headers.get('x-real-ip') ||\n             'unknown';\n  const userAgent = request.headers.get('user-agent') || 'unknown';\n  const acceptLanguage = request.headers.get('accept-language') || 'unknown';\n  const acceptEncoding = request.headers.get('accept-encoding') || 'unknown';\n\n  // Create a more unique fingerprint\n  const fingerprint = `${ip}_${userAgent}_${acceptLanguage}_${acceptEncoding}`;\n\n  // Hash the fingerprint for privacy and security\n  let hash: string;\n\n  if (typeof crypto !== 'undefined' && crypto.subtle) {\n    // Use Web Crypto API when available (browser/edge runtime)\n    const encoder = new TextEncoder();\n    const data = encoder.encode(fingerprint);\n    const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n    const hashArray = Array.from(new Uint8Array(hashBuffer));\n    hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n  } else {\n    // Fallback for Node.js environment (tests)\n    const crypto = require('crypto');\n    hash = crypto.createHash('sha256').update(fingerprint).digest('hex');\n  }\n\n  return `guest_${hash.substring(0, 32)}`;\n}\n\nexport async function getCSRFToken(request: NextRequest): Promise<string> {\n  // Get user session to create a unique identifier\n  const session = await getServerSession(authOptions);\n  const userId = session?.user?.id;\n\n  console.log('🔑 CSRF Token Generation:', {\n    hasSession: !!session,\n    userId: userId?.substring(0, 10) + '...' || 'none',\n    userEmail: session?.user?.email || 'none'\n  });\n\n  if (!userId) {\n    // For non-authenticated users, use secure fingerprinting\n    const identifier = await createSecureGuestIdentifier(request);\n\n    const existing = csrfTokens.get(identifier);\n    if (existing && existing.expiresAt > Date.now()) {\n      console.log('🔄 Returning existing guest token');\n      return existing.token;\n    }\n\n    const token = generateCSRFToken();\n    csrfTokens.set(identifier, {\n      token,\n      expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour\n    });\n\n    console.log('🆕 Generated new guest token:', { identifier: identifier.substring(0, 20) + '...', tokenCount: csrfTokens.size });\n    return token;\n  }\n\n  // For authenticated users, use user ID\n  const existing = csrfTokens.get(userId);\n  if (existing && existing.expiresAt > Date.now()) {\n    console.log('🔄 Returning existing user token');\n    return existing.token;\n  }\n\n  const token = generateCSRFToken();\n  csrfTokens.set(userId, {\n    token,\n    expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour\n  });\n\n  console.log('🆕 Generated new user token:', { userId: userId.substring(0, 10) + '...', tokenCount: csrfTokens.size });\n  return token;\n}\n\nexport async function validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {\n  console.log('🔍 CSRF Validation Debug:', {\n    token: token?.substring(0, 10) + '...',\n    method: request.method,\n    url: request.url\n  });\n\n  // Get user session to create a unique identifier\n  const session = await getServerSession(authOptions);\n  const userId = session?.user?.id;\n\n  console.log('👤 Session Info:', {\n    hasSession: !!session,\n    userId: userId?.substring(0, 10) + '...' || 'none',\n    userEmail: session?.user?.email || 'none'\n  });\n\n  if (!userId) {\n    // For non-authenticated users, use secure fingerprinting\n    const identifier = await createSecureGuestIdentifier(request);\n\n    console.log('🌐 Guest validation:', { identifier: identifier.substring(0, 20) + '...', tokenCount: csrfTokens.size });\n\n    const stored = csrfTokens.get(identifier);\n    if (stored && stored.expiresAt > Date.now() && stored.token === token) {\n      console.log('✅ Guest CSRF token valid');\n      return true;\n    }\n    console.log('❌ Guest CSRF token invalid or expired');\n    return false;\n  }\n\n  // For authenticated users, use user ID\n  console.log('🔐 User validation:', { userId: userId.substring(0, 10) + '...', tokenCount: csrfTokens.size });\n\n  const stored = csrfTokens.get(userId);\n  if (stored && stored.expiresAt > Date.now() && stored.token === token) {\n    console.log('✅ User CSRF token valid');\n    return true;\n  }\n\n  console.log('❌ User CSRF token invalid or expired', {\n    hasStored: !!stored,\n    isExpired: stored ? stored.expiresAt <= Date.now() : 'no-token',\n    tokenMatch: stored ? stored.token === token : 'no-token'\n  });\n\n  return false;\n}\n\nexport async function withCSRFProtection(\n  request: NextRequest,\n  handler: () => Promise<NextResponse>\n): Promise<NextResponse> {\n  // Skip CSRF for GET requests\n  if (request.method === 'GET') {\n    return handler();\n  }\n\n  // Enhanced development mode - still validate but with relaxed requirements\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const isTestEnvironment = process.env.NODE_ENV === 'test';\n\n  // Get CSRF token from header or body\n  const csrfToken = request.headers.get('x-csrf-token') ||\n                   request.headers.get('csrf-token');\n\n  // In development, allow requests without CSRF token but log warning\n  if (isDevelopment && !csrfToken) {\n    console.warn('⚠️  CSRF token missing in development mode - this would fail in production');\n    // Generate and validate a temporary token for development consistency\n    const tempToken = await getCSRFToken(request);\n    console.log('🔧 Generated temporary CSRF token for development:', tempToken.substring(0, 10) + '...');\n    return handler();\n  }\n\n  // In test environment, be more lenient but still validate structure\n  if (isTestEnvironment && !csrfToken) {\n    console.log('🧪 Test environment: Allowing request without CSRF token');\n    return handler();\n  }\n\n  // Production and development with token - always validate\n  if (!csrfToken) {\n    return NextResponse.json(\n      { error: 'CSRF token missing' },\n      { status: 403 }\n    );\n  }\n\n  const isValid = await validateCSRFToken(request, csrfToken);\n  if (!isValid) {\n    // In development, log more details for debugging\n    if (isDevelopment) {\n      console.error('🔒 CSRF validation failed in development:', {\n        token: csrfToken.substring(0, 10) + '...',\n        url: request.url,\n        method: request.method\n      });\n    }\n\n    return NextResponse.json(\n      { error: 'Invalid CSRF token' },\n      { status: 403 }\n    );\n  }\n\n  return handler();\n}\n\n// API endpoint to get CSRF token\nexport async function getCSRFTokenEndpoint(request: NextRequest): Promise<NextResponse> {\n  const token = await getCSRFToken(request);\n\n  return NextResponse.json(\n    {\n      success: true,\n      csrfToken: token,\n      timestamp: Date.now()\n    },\n    {\n      status: 200,\n      headers: {\n        'Cache-Control': 'no-store, no-cache, must-revalidate',\n        'Pragma': 'no-cache'\n      }\n    }\n  );\n}\n\n// Cleanup expired tokens periodically\nsetInterval(() => {\n  const now = Date.now();\n  Array.from(csrfTokens.entries()).forEach(([key, value]) => {\n    if (value.expiresAt <= now) {\n      csrfTokens.delete(key);\n    }\n  });\n}, 15 * 60 * 1000); // Clean up every 15 minutes\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeAA,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAQC;AAAAC,aAAA,GAAAC,CAAA;AAkCDH,OAAA,CAAAI,YAAA,GAAAA,YAAA;AA8CC;AAAAF,aAAA,GAAAC,CAAA;AAEDH,OAAA,CAAAK,iBAAA,GAAAA,iBAAA;AAgDC;AAAAH,aAAA,GAAAC,CAAA;AAEDH,OAAA,CAAAM,kBAAA,GAAAA,kBAAA;AA0DC;AAAAJ,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAO,oBAAA,GAAAA,oBAAA;AAxNA,IAAAC,QAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAM,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAM,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAT,aAAA,GAAAC,CAAA,QAAAM,OAAA;AAOA;AACA,IAAMG,UAAU;AAAA;AAAA,CAAAV,aAAA,GAAAC,CAAA;AAAG;AAAA,CAAAD,aAAA,GAAAW,CAAA,YAAAC,EAAA,GAAAC,UAAU,CAACC,YAAY;AAAA;AAAA,CAAAd,aAAA,GAAAW,CAAA,WAAAC,EAAA;AAAA;AAAA,CAAAZ,aAAA,GAAAW,CAAA,WAAAC,EAAA;AAAA;AAAA,CAAAZ,aAAA,GAAAW,CAAA,WAAI,IAAII,GAAG,EAAgD;AAAC;AAAAf,aAAA,GAAAC,CAAA;AACtG,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAAA;EAAAlB,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAC,CAAA;EAC1CY,UAAU,CAACC,YAAY,GAAGJ,UAAU;AACtC,CAAC;AAAA;AAAA;EAAAV,aAAA,GAAAW,CAAA;AAAA;AAED,SAAgBZ,iBAAiBA,CAAA;EAAA;EAAAC,aAAA,GAAAmB,CAAA;EAC/B;EACA,IAAMC,IAAI;EAAA;EAAA,CAAApB,aAAA,GAAAC,CAAA,QAAGoB,MAAM,CAACC,UAAU,EAAE;EAChC,IAAMC,SAAS;EAAA;EAAA,CAAAvB,aAAA,GAAAC,CAAA,QAAGuB,IAAI,CAACC,GAAG,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC;EACzC,IAAMC,WAAW;EAAA;EAAA,CAAA3B,aAAA,GAAAC,CAAA,QAAGoB,MAAM,CAACO,eAAe,CAAC,IAAIC,UAAU,CAAC,EAAE,CAAC,CAAC;EAC9D,IAAMC,SAAS;EAAA;EAAA,CAAA9B,aAAA,GAAAC,CAAA,QAAG8B,KAAK,CAACC,IAAI,CAACL,WAAW,EAAE,UAAAM,IAAI;IAAA;IAAAjC,aAAA,GAAAmB,CAAA;IAAAnB,aAAA,GAAAC,CAAA;IAAI,OAAAgC,IAAI,CAACP,QAAQ,CAAC,EAAE,CAAC,CAACQ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAAlC,CAAkC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAAC;EAAAnC,aAAA,GAAAC,CAAA;EAE/F,OAAO,GAAAmC,MAAA,CAAGhB,IAAI,OAAAgB,MAAA,CAAIb,SAAS,OAAAa,MAAA,CAAIN,SAAS,CAAE;AAC5C;AAEA;AACA,SAAeO,2BAA2BA,CAACC,OAAoB;EAAA;EAAAtC,aAAA,GAAAmB,CAAA;EAAAnB,aAAA,GAAAC,CAAA;iCAAGsC,OAAO;IAAA;IAAAvC,aAAA,GAAAmB,CAAA;;;;;;;;;;;;;UAEjEqB,EAAE;UAAG;UAAA,CAAAxC,aAAA,GAAAW,CAAA,WAAA2B,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAAA;UAAA,CAAA1C,aAAA,GAAAW,CAAA,WACtC2B,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UAAA;UAAA,CAAA1C,aAAA,GAAAW,CAAA,WAChC,SAAS;UAAC;UAAAX,aAAA,GAAAC,CAAA;UACf0C,SAAS;UAAG;UAAA,CAAA3C,aAAA,GAAAW,CAAA,WAAA2B,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;UAAA;UAAA,CAAA1C,aAAA,GAAAW,CAAA,WAAI,SAAS;UAAC;UAAAX,aAAA,GAAAC,CAAA;UAC3D2C,cAAc;UAAG;UAAA,CAAA5C,aAAA,GAAAW,CAAA,WAAA2B,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAAA;UAAA,CAAA1C,aAAA,GAAAW,CAAA,WAAI,SAAS;UAAC;UAAAX,aAAA,GAAAC,CAAA;UACrE4C,cAAc;UAAG;UAAA,CAAA7C,aAAA,GAAAW,CAAA,WAAA2B,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAAA;UAAA,CAAA1C,aAAA,GAAAW,CAAA,WAAI,SAAS;UAAC;UAAAX,aAAA,GAAAC,CAAA;UAGrE6C,WAAW,GAAG,GAAAV,MAAA,CAAGI,EAAE,OAAAJ,MAAA,CAAIO,SAAS,OAAAP,MAAA,CAAIQ,cAAc,OAAAR,MAAA,CAAIS,cAAc,CAAE;UAAC;UAAA7C,aAAA,GAAAC,CAAA;;UAKzE;UAAA,CAAAD,aAAA,GAAAW,CAAA,kBAAOU,MAAM,KAAK,WAAW;UAAA;UAAA,CAAArB,aAAA,GAAAW,CAAA,WAAIU,MAAM,CAAC0B,MAAM,IAA9C;YAAA;YAAA/C,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAAA;UAAA,CAA8C;UAAA;UAAA;YAAAD,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAE1C+C,OAAO,GAAG,IAAIC,WAAW,EAAE;UAAC;UAAAjD,aAAA,GAAAC,CAAA;UAC5BiD,IAAI,GAAGF,OAAO,CAACG,MAAM,CAACL,WAAW,CAAC;UAAC;UAAA9C,aAAA,GAAAC,CAAA;UACtB,qBAAMoB,MAAM,CAAC0B,MAAM,CAACK,MAAM,CAAC,SAAS,EAAEF,IAAI,CAAC;;;;;UAAxDG,UAAU,GAAGzC,EAAA,CAAA0C,IAAA,EAA2C;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UACxDsD,SAAS,GAAGxB,KAAK,CAACC,IAAI,CAAC,IAAIH,UAAU,CAACwB,UAAU,CAAC,CAAC;UAAC;UAAArD,aAAA,GAAAC,CAAA;UACzDuD,IAAI,GAAGD,SAAS,CAACE,GAAG,CAAC,UAAA9C,CAAC;YAAA;YAAAX,aAAA,GAAAmB,CAAA;YAAAnB,aAAA,GAAAC,CAAA;YAAI,OAAAU,CAAC,CAACe,QAAQ,CAAC,EAAE,CAAC,CAACQ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAA/B,CAA+B,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;UAAC;UAAAnC,aAAA,GAAAC,CAAA;;;;;;UAG9DoB,MAAM,GAAGd,OAAO,CAAC,QAAQ,CAAC;UAAC;UAAAP,aAAA,GAAAC,CAAA;UACjCuD,IAAI,GAAGnC,MAAM,CAACqC,UAAU,CAAC,QAAQ,CAAC,CAACC,MAAM,CAACb,WAAW,CAAC,CAACM,MAAM,CAAC,KAAK,CAAC;UAAC;UAAApD,aAAA,GAAAC,CAAA;;;;;;UAGvE,sBAAO,SAAAmC,MAAA,CAASoB,IAAI,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE;;;;;AAGzC,SAAsB1D,YAAYA,CAACoC,OAAoB;EAAA;EAAAtC,aAAA,GAAAmB,CAAA;EAAAnB,aAAA,GAAAC,CAAA;iCAAGsC,OAAO;IAAA;IAAAvC,aAAA,GAAAmB,CAAA;;;;;;;;;;;;;;UAE/C,qBAAM,IAAAX,MAAA,CAAAqD,gBAAgB,EAACpD,MAAA,CAAAqD,WAAW,CAAC;;;;;UAA7CC,OAAO,GAAGC,EAAA,CAAAV,IAAA,EAAmC;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAC7CgE,MAAM;UAAG;UAAA,CAAAjE,aAAA,GAAAW,CAAA,YAAAC,EAAA;UAAA;UAAA,CAAAZ,aAAA,GAAAW,CAAA,WAAAoD,OAAO;UAAA;UAAA,CAAA/D,aAAA,GAAAW,CAAA,WAAPoD,OAAO;UAAA;UAAA,CAAA/D,aAAA,GAAAW,CAAA;UAAA;UAAA,CAAAX,aAAA,GAAAW,CAAA,WAAPoD,OAAO,CAAEG,IAAI;UAAA;UAAA,CAAAlE,aAAA,GAAAW,CAAA,WAAAC,EAAA;UAAA;UAAA,CAAAZ,aAAA,GAAAW,CAAA;UAAA;UAAA,CAAAX,aAAA,GAAAW,CAAA,WAAAC,EAAA,CAAEuD,EAAE;UAAC;UAAAnE,aAAA,GAAAC,CAAA;UAEjCmE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;YACvCC,UAAU,EAAE,CAAC,CAACP,OAAO;YACrBE,MAAM;YAAE;YAAA,CAAAjE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAAAsD,MAAM;YAAA;YAAA,CAAAjE,aAAA,GAAAW,CAAA,WAANsD,MAAM;YAAA;YAAA,CAAAjE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAANsD,MAAM,CAAEL,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAG,KAAK;YAAA;YAAA,CAAA5D,aAAA,GAAAW,CAAA,WAAI,MAAM;YAClD4D,SAAS;YAAE;YAAA,CAAAvE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,YAAA6D,EAAA;YAAA;YAAA,CAAAxE,aAAA,GAAAW,CAAA,WAAAoD,OAAO;YAAA;YAAA,CAAA/D,aAAA,GAAAW,CAAA,WAAPoD,OAAO;YAAA;YAAA,CAAA/D,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAAPoD,OAAO,CAAEG,IAAI;YAAA;YAAA,CAAAlE,aAAA,GAAAW,CAAA,WAAA6D,EAAA;YAAA;YAAA,CAAAxE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAAA6D,EAAA,CAAEC,KAAK;YAAA;YAAA,CAAAzE,aAAA,GAAAW,CAAA,WAAI,MAAM;WAC1C,CAAC;UAAC;UAAAX,aAAA,GAAAC,CAAA;eAEC,CAACgE,MAAM,EAAP;YAAA;YAAAjE,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAO;UAAA;UAAA;YAAAD,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAEU,qBAAMoC,2BAA2B,CAACC,OAAO,CAAC;;;;;UAAvDoC,UAAU,GAAGV,EAAA,CAAAV,IAAA,EAA0C;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAEvD0E,UAAA,GAAWjE,UAAU,CAACgC,GAAG,CAACgC,UAAU,CAAC;UAAC;UAAA1E,aAAA,GAAAC,CAAA;UAC5C;UAAI;UAAA,CAAAD,aAAA,GAAAW,CAAA,WAAAgE,UAAQ;UAAA;UAAA,CAAA3E,aAAA,GAAAW,CAAA,WAAIgE,UAAQ,CAACC,SAAS,GAAGpD,IAAI,CAACC,GAAG,EAAE,GAAE;YAAA;YAAAzB,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAC/CmE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAAC;YAAArE,aAAA,GAAAC,CAAA;YACjD,sBAAO0E,UAAQ,CAACE,KAAK;UACvB,CAAC;UAAA;UAAA;YAAA7E,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAEK6E,OAAA,GAAQ/E,iBAAiB,EAAE;UAAC;UAAAC,aAAA,GAAAC,CAAA;UAClCS,UAAU,CAACqE,GAAG,CAACL,UAAU,EAAE;YACzBG,KAAK,EAAAC,OAAA;YACLF,SAAS,EAAEpD,IAAI,CAACC,GAAG,EAAE,GAAI,EAAE,GAAG,EAAE,GAAG,IAAK,CAAC;WAC1C,CAAC;UAAC;UAAAzB,aAAA,GAAAC,CAAA;UAEHmE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;YAAEK,UAAU,EAAEA,UAAU,CAACd,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAAEoB,UAAU,EAAEtE,UAAU,CAACuE;UAAI,CAAE,CAAC;UAAC;UAAAjF,aAAA,GAAAC,CAAA;UAC/H,sBAAO6E,OAAK;;;;;UAIRI,QAAQ,GAAGxE,UAAU,CAACgC,GAAG,CAACuB,MAAM,CAAC;UAAC;UAAAjE,aAAA,GAAAC,CAAA;UACxC;UAAI;UAAA,CAAAD,aAAA,GAAAW,CAAA,WAAAuE,QAAQ;UAAA;UAAA,CAAAlF,aAAA,GAAAW,CAAA,WAAIuE,QAAQ,CAACN,SAAS,GAAGpD,IAAI,CAACC,GAAG,EAAE,GAAE;YAAA;YAAAzB,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAC/CmE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;YAAC;YAAArE,aAAA,GAAAC,CAAA;YAChD,sBAAOiF,QAAQ,CAACL,KAAK;UACvB,CAAC;UAAA;UAAA;YAAA7E,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAEK4E,KAAK,GAAG9E,iBAAiB,EAAE;UAAC;UAAAC,aAAA,GAAAC,CAAA;UAClCS,UAAU,CAACqE,GAAG,CAACd,MAAM,EAAE;YACrBY,KAAK,EAAAA,KAAA;YACLD,SAAS,EAAEpD,IAAI,CAACC,GAAG,EAAE,GAAI,EAAE,GAAG,EAAE,GAAG,IAAK,CAAC;WAC1C,CAAC;UAAC;UAAAzB,aAAA,GAAAC,CAAA;UAEHmE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;YAAEJ,MAAM,EAAEA,MAAM,CAACL,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAAEoB,UAAU,EAAEtE,UAAU,CAACuE;UAAI,CAAE,CAAC;UAAC;UAAAjF,aAAA,GAAAC,CAAA;UACtH,sBAAO4E,KAAK;;;;;AAGd,SAAsB1E,iBAAiBA,CAACmC,OAAoB,EAAEuC,KAAa;EAAA;EAAA7E,aAAA,GAAAmB,CAAA;EAAAnB,aAAA,GAAAC,CAAA;iCAAGsC,OAAO;IAAA;IAAAvC,aAAA,GAAAmB,CAAA;;;;;;;;;;;;;;UACnFiD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;YACvCQ,KAAK,EAAE;YAAA;YAAA,CAAA7E,aAAA,GAAAW,CAAA,WAAAkE,KAAK;YAAA;YAAA,CAAA7E,aAAA,GAAAW,CAAA,WAALkE,KAAK;YAAA;YAAA,CAAA7E,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAALkE,KAAK,CAAEjB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAG,KAAK;YACtCuB,MAAM,EAAE7C,OAAO,CAAC6C,MAAM;YACtBC,GAAG,EAAE9C,OAAO,CAAC8C;WACd,CAAC;UAAC;UAAApF,aAAA,GAAAC,CAAA;UAGa,qBAAM,IAAAO,MAAA,CAAAqD,gBAAgB,EAACpD,MAAA,CAAAqD,WAAW,CAAC;;;;;UAA7CC,OAAO,GAAGC,EAAA,CAAAV,IAAA,EAAmC;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAC7CgE,MAAM;UAAG;UAAA,CAAAjE,aAAA,GAAAW,CAAA,YAAAC,EAAA;UAAA;UAAA,CAAAZ,aAAA,GAAAW,CAAA,WAAAoD,OAAO;UAAA;UAAA,CAAA/D,aAAA,GAAAW,CAAA,WAAPoD,OAAO;UAAA;UAAA,CAAA/D,aAAA,GAAAW,CAAA;UAAA;UAAA,CAAAX,aAAA,GAAAW,CAAA,WAAPoD,OAAO,CAAEG,IAAI;UAAA;UAAA,CAAAlE,aAAA,GAAAW,CAAA,WAAAC,EAAA;UAAA;UAAA,CAAAZ,aAAA,GAAAW,CAAA;UAAA;UAAA,CAAAX,aAAA,GAAAW,CAAA,WAAAC,EAAA,CAAEuD,EAAE;UAAC;UAAAnE,aAAA,GAAAC,CAAA;UAEjCmE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;YAC9BC,UAAU,EAAE,CAAC,CAACP,OAAO;YACrBE,MAAM;YAAE;YAAA,CAAAjE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAAAsD,MAAM;YAAA;YAAA,CAAAjE,aAAA,GAAAW,CAAA,WAANsD,MAAM;YAAA;YAAA,CAAAjE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAANsD,MAAM,CAAEL,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAG,KAAK;YAAA;YAAA,CAAA5D,aAAA,GAAAW,CAAA,WAAI,MAAM;YAClD4D,SAAS;YAAE;YAAA,CAAAvE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,YAAA6D,EAAA;YAAA;YAAA,CAAAxE,aAAA,GAAAW,CAAA,WAAAoD,OAAO;YAAA;YAAA,CAAA/D,aAAA,GAAAW,CAAA,WAAPoD,OAAO;YAAA;YAAA,CAAA/D,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAAPoD,OAAO,CAAEG,IAAI;YAAA;YAAA,CAAAlE,aAAA,GAAAW,CAAA,WAAA6D,EAAA;YAAA;YAAA,CAAAxE,aAAA,GAAAW,CAAA;YAAA;YAAA,CAAAX,aAAA,GAAAW,CAAA,WAAA6D,EAAA,CAAEC,KAAK;YAAA;YAAA,CAAAzE,aAAA,GAAAW,CAAA,WAAI,MAAM;WAC1C,CAAC;UAAC;UAAAX,aAAA,GAAAC,CAAA;eAEC,CAACgE,MAAM,EAAP;YAAA;YAAAjE,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAAA;UAAA,CAAO;UAAA;UAAA;YAAAD,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAEU,qBAAMoC,2BAA2B,CAACC,OAAO,CAAC;;;;;UAAvDoC,UAAU,GAAGV,EAAA,CAAAV,IAAA,EAA0C;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAE7DmE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;YAAEK,UAAU,EAAEA,UAAU,CAACd,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAAEoB,UAAU,EAAEtE,UAAU,CAACuE;UAAI,CAAE,CAAC;UAAC;UAAAjF,aAAA,GAAAC,CAAA;UAEhHoF,QAAA,GAAS3E,UAAU,CAACgC,GAAG,CAACgC,UAAU,CAAC;UAAC;UAAA1E,aAAA,GAAAC,CAAA;UAC1C;UAAI;UAAA,CAAAD,aAAA,GAAAW,CAAA,WAAA0E,QAAM;UAAA;UAAA,CAAArF,aAAA,GAAAW,CAAA,WAAI0E,QAAM,CAACT,SAAS,GAAGpD,IAAI,CAACC,GAAG,EAAE;UAAA;UAAA,CAAAzB,aAAA,GAAAW,CAAA,WAAI0E,QAAM,CAACR,KAAK,KAAKA,KAAK,GAAE;YAAA;YAAA7E,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YACrEmE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;YAAC;YAAArE,aAAA,GAAAC,CAAA;YACxC,sBAAO,IAAI;UACb,CAAC;UAAA;UAAA;YAAAD,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UACDmE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UAAC;UAAArE,aAAA,GAAAC,CAAA;UACrD,sBAAO,KAAK;;;;;UAGd;UACAmE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;YAAEJ,MAAM,EAAEA,MAAM,CAACL,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAAEoB,UAAU,EAAEtE,UAAU,CAACuE;UAAI,CAAE,CAAC;UAAC;UAAAjF,aAAA,GAAAC,CAAA;UAEvGqF,MAAM,GAAG5E,UAAU,CAACgC,GAAG,CAACuB,MAAM,CAAC;UAAC;UAAAjE,aAAA,GAAAC,CAAA;UACtC;UAAI;UAAA,CAAAD,aAAA,GAAAW,CAAA,WAAA2E,MAAM;UAAA;UAAA,CAAAtF,aAAA,GAAAW,CAAA,WAAI2E,MAAM,CAACV,SAAS,GAAGpD,IAAI,CAACC,GAAG,EAAE;UAAA;UAAA,CAAAzB,aAAA,GAAAW,CAAA,WAAI2E,MAAM,CAACT,KAAK,KAAKA,KAAK,GAAE;YAAA;YAAA7E,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YACrEmE,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YAAC;YAAArE,aAAA,GAAAC,CAAA;YACvC,sBAAO,IAAI;UACb,CAAC;UAAA;UAAA;YAAAD,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAEDmE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;YAClDkB,SAAS,EAAE,CAAC,CAACD,MAAM;YACnBE,SAAS,EAAEF,MAAM;YAAA;YAAA,CAAAtF,aAAA,GAAAW,CAAA,WAAG2E,MAAM,CAACV,SAAS,IAAIpD,IAAI,CAACC,GAAG,EAAE;YAAA;YAAA,CAAAzB,aAAA,GAAAW,CAAA,WAAG,UAAU;YAC/D8E,UAAU,EAAEH,MAAM;YAAA;YAAA,CAAAtF,aAAA,GAAAW,CAAA,WAAG2E,MAAM,CAACT,KAAK,KAAKA,KAAK;YAAA;YAAA,CAAA7E,aAAA,GAAAW,CAAA,WAAG,UAAU;WACzD,CAAC;UAAC;UAAAX,aAAA,GAAAC,CAAA;UAEH,sBAAO,KAAK;;;;;AAGd,SAAsBG,kBAAkBA,CACtCkC,OAAoB,EACpBoD,OAAoC;EAAA;EAAA1F,aAAA,GAAAmB,CAAA;EAAAnB,aAAA,GAAAC,CAAA;iCACnCsC,OAAO;IAAA;IAAAvC,aAAA,GAAAmB,CAAA;;;;;;;;;;;;;UACR;UACA,IAAImB,OAAO,CAAC6C,MAAM,KAAK,KAAK,EAAE;YAAA;YAAAnF,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAC5B,sBAAOyF,OAAO,EAAE;UAClB,CAAC;UAAA;UAAA;YAAA1F,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAGK0F,aAAa,GAAG3E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;UAAC;UAAAlB,aAAA,GAAAC,CAAA;UACvD2F,iBAAiB,GAAG5E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM;UAAC;UAAAlB,aAAA,GAAAC,CAAA;UAGpD4F,SAAS;UAAG;UAAA,CAAA7F,aAAA,GAAAW,CAAA,WAAA2B,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAAA;UAAA,CAAA1C,aAAA,GAAAW,CAAA,WACpC2B,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;UAAC;UAAA1C,aAAA,GAAAC,CAAA;;UAG/C;UAAA,CAAAD,aAAA,GAAAW,CAAA,WAAAgF,aAAa;UAAA;UAAA,CAAA3F,aAAA,GAAAW,CAAA,WAAI,CAACkF,SAAS,IAA3B;YAAA;YAAA7F,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YAAA;UAAA,CAA2B;UAAA;UAAA;YAAAD,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAC7BmE,OAAO,CAAC0B,IAAI,CAAC,4EAA4E,CAAC;UAAC;UAAA9F,aAAA,GAAAC,CAAA;UAEzE,qBAAMC,YAAY,CAACoC,OAAO,CAAC;;;;;UAAvCyD,SAAS,GAAGnF,EAAA,CAAA0C,IAAA,EAA2B;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAC7CmE,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE0B,SAAS,CAACnC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;UAAC;UAAA5D,aAAA,GAAAC,CAAA;UACtG,sBAAOyF,OAAO,EAAE;;;;;UAGlB;UACA;UAAI;UAAA,CAAA1F,aAAA,GAAAW,CAAA,WAAAiF,iBAAiB;UAAA;UAAA,CAAA5F,aAAA,GAAAW,CAAA,WAAI,CAACkF,SAAS,GAAE;YAAA;YAAA7F,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YACnCmE,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;YAAC;YAAArE,aAAA,GAAAC,CAAA;YACxE,sBAAOyF,OAAO,EAAE;UAClB,CAAC;UAAA;UAAA;YAAA1F,aAAA,GAAAW,CAAA;UAAA;UAED;UAAAX,aAAA,GAAAC,CAAA;UACA,IAAI,CAAC4F,SAAS,EAAE;YAAA;YAAA7F,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YACd,sBAAOK,QAAA,CAAA0F,YAAY,CAACC,IAAI,CACtB;cAAEC,KAAK,EAAE;YAAoB,CAAE,EAC/B;cAAEC,MAAM,EAAE;YAAG,CAAE,CAChB;UACH,CAAC;UAAA;UAAA;YAAAnG,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAEe,qBAAME,iBAAiB,CAACmC,OAAO,EAAEuD,SAAS,CAAC;;;;;UAArDO,OAAO,GAAGxF,EAAA,CAAA0C,IAAA,EAA2C;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAC3D,IAAI,CAACmG,OAAO,EAAE;YAAA;YAAApG,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAC,CAAA;YACZ;YACA,IAAI0F,aAAa,EAAE;cAAA;cAAA3F,aAAA,GAAAW,CAAA;cAAAX,aAAA,GAAAC,CAAA;cACjBmE,OAAO,CAAC8B,KAAK,CAAC,2CAA2C,EAAE;gBACzDrB,KAAK,EAAEgB,SAAS,CAACjC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;gBACzCwB,GAAG,EAAE9C,OAAO,CAAC8C,GAAG;gBAChBD,MAAM,EAAE7C,OAAO,CAAC6C;eACjB,CAAC;YACJ,CAAC;YAAA;YAAA;cAAAnF,aAAA,GAAAW,CAAA;YAAA;YAAAX,aAAA,GAAAC,CAAA;YAED,sBAAOK,QAAA,CAAA0F,YAAY,CAACC,IAAI,CACtB;cAAEC,KAAK,EAAE;YAAoB,CAAE,EAC/B;cAAEC,MAAM,EAAE;YAAG,CAAE,CAChB;UACH,CAAC;UAAA;UAAA;YAAAnG,aAAA,GAAAW,CAAA;UAAA;UAAAX,aAAA,GAAAC,CAAA;UAED,sBAAOyF,OAAO,EAAE;;;;;AAGlB;AACA,SAAsBrF,oBAAoBA,CAACiC,OAAoB;EAAA;EAAAtC,aAAA,GAAAmB,CAAA;EAAAnB,aAAA,GAAAC,CAAA;iCAAGsC,OAAO;IAAA;IAAAvC,aAAA,GAAAmB,CAAA;;;;;;;;;;;;;UACzD,qBAAMjB,YAAY,CAACoC,OAAO,CAAC;;;;;UAAnCuC,KAAK,GAAGjE,EAAA,CAAA0C,IAAA,EAA2B;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAEzC,sBAAOK,QAAA,CAAA0F,YAAY,CAACC,IAAI,CACtB;YACEI,OAAO,EAAE,IAAI;YACbR,SAAS,EAAEhB,KAAK;YAChBtD,SAAS,EAAEC,IAAI,CAACC,GAAG;WACpB,EACD;YACE0E,MAAM,EAAE,GAAG;YACX1D,OAAO,EAAE;cACP,eAAe,EAAE,qCAAqC;cACtD,QAAQ,EAAE;;WAEb,CACF;;;;;AAGH;AAAA;AAAAzC,aAAA,GAAAC,CAAA;AACAqG,WAAW,CAAC;EAAA;EAAAtG,aAAA,GAAAmB,CAAA;EACV,IAAMM,GAAG;EAAA;EAAA,CAAAzB,aAAA,GAAAC,CAAA,SAAGuB,IAAI,CAACC,GAAG,EAAE;EAAC;EAAAzB,aAAA,GAAAC,CAAA;EACvB8B,KAAK,CAACC,IAAI,CAACtB,UAAU,CAAC6F,OAAO,EAAE,CAAC,CAACC,OAAO,CAAC,UAAC5F,EAAY;IAAA;IAAAZ,aAAA,GAAAmB,CAAA;QAAXsF,GAAG;MAAA;MAAA,CAAAzG,aAAA,GAAAC,CAAA,SAAAW,EAAA;MAAE8F,KAAK;MAAA;MAAA,CAAA1G,aAAA,GAAAC,CAAA,SAAAW,EAAA;IAAA;IAAAZ,aAAA,GAAAC,CAAA;IACnD,IAAIyG,KAAK,CAAC9B,SAAS,IAAInD,GAAG,EAAE;MAAA;MAAAzB,aAAA,GAAAW,CAAA;MAAAX,aAAA,GAAAC,CAAA;MAC1BS,UAAU,CAACiG,MAAM,CAACF,GAAG,CAAC;IACxB,CAAC;IAAA;IAAA;MAAAzG,aAAA,GAAAW,CAAA;IAAA;EACH,CAAC,CAAC;AACJ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC", "ignoreList": []}