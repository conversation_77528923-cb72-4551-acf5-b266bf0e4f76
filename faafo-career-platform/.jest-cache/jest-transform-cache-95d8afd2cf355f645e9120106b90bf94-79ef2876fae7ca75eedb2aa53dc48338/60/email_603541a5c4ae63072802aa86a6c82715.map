{"version": 3, "names": ["exports", "sendEmail", "cov_twjp3ipar", "s", "sendPasswordResetEmail", "resend_1", "require", "render_1", "react_1", "__importDefault", "PasswordResetEmail_1", "resend", "process", "env", "RESEND_API_KEY", "b", "Resend", "_a", "f", "_b", "to", "subject", "template", "console", "warn", "success", "error", "render", "html", "_d", "sent", "emails", "send", "from", "EMAIL_FROM", "_c", "data", "message", "log", "error_1", "params", "url", "react", "default", "createElement", "PasswordResetEmail", "resetLink", "error_2"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/email.ts"], "sourcesContent": ["import { Resend } from 'resend';\nimport { render } from '@react-email/render';\nimport React from 'react';\nimport { PasswordResetEmail } from '../emails/PasswordResetEmail';\n\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\n\ninterface EmailOptions {\n  to: string;\n  subject: string;\n  template: React.ReactElement; // Use React.ReactElement for email templates\n}\n\nexport async function sendEmail({ to, subject, template }: EmailOptions) {\n  if (!resend) {\n    console.warn('Email service not configured - RESEND_API_KEY missing');\n    return { success: false, error: 'Email service not configured' };\n  }\n\n  try {\n    const html = await render(template);\n\n    const { data, error } = await resend.emails.send({\n      from: process.env.EMAIL_FROM || '<EMAIL>', // Use environment variable or fallback\n      to,\n      subject,\n      html,\n    });\n\n    if (error) {\n      console.error('Error sending email:', error);\n      return { success: false, error: error.message };\n    }\n\n    console.log('Email sent successfully:', data);\n    return { success: true, data };\n  } catch (error) {\n    console.error('Caught an exception while sending email:', error);\n    return { success: false, error: (error as Error).message };\n  }\n}\n\ninterface SendPasswordResetEmailParams {\n  to: string;\n  url: string;\n}\n\nexport async function sendPasswordResetEmail(params: SendPasswordResetEmailParams) {\n  if (!resend) {\n    console.warn('Email service not configured - RESEND_API_KEY missing');\n    return { success: false, error: 'Email service not configured' };\n  }\n\n  const { to, url } = params;\n  try {\n    await resend.emails.send({\n      from: '<EMAIL>', // Use a verified sender from your Resend domain\n      to,\n      subject: 'Reset your password',\n      react: React.createElement(PasswordResetEmail, { resetLink: url }),\n    });\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending password reset email:', error);\n    return { success: false, error: (error as Error).message };\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaAA,OAAA,CAAAC,SAAA,GAAAA,SAAA;AA2BC;AAAAC,aAAA,GAAAC,CAAA;AAODH,OAAA,CAAAI,sBAAA,GAAAA,sBAAA;AA/CA,IAAAC,QAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,OAAA;AACA,IAAAC,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAG,OAAA;AACA,IAAAE,OAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAM,eAAA,CAAAH,OAAA;AACA,IAAAI,oBAAA;AAAA;AAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAG,OAAA;AAEA,IAAMK,MAAM;AAAA;AAAA,CAAAT,aAAA,GAAAC,CAAA,QAAGS,OAAO,CAACC,GAAG,CAACC,cAAc;AAAA;AAAA,CAAAZ,aAAA,GAAAa,CAAA,WAAG,IAAIV,QAAA,CAAAW,MAAM,CAACJ,OAAO,CAACC,GAAG,CAACC,cAAc,CAAC;AAAA;AAAA,CAAAZ,aAAA,GAAAa,CAAA,WAAG,IAAI;AAQzF,SAAsBd,SAASA,CAAAgB,EAAA;EAAA;EAAAf,aAAA,GAAAgB,CAAA;EAAAhB,aAAA,GAAAC,CAAA;sDAACgB,EAAuC;IAAA;IAAAjB,aAAA,GAAAgB,CAAA;;QAArCE,EAAE;MAAA;MAAA,CAAAlB,aAAA,GAAAC,CAAA,QAAAgB,EAAA,CAAAC,EAAA;MAAEC,OAAO;MAAA;MAAA,CAAAnB,aAAA,GAAAC,CAAA,QAAAgB,EAAA,CAAAE,OAAA;MAAEC,QAAQ;MAAA;MAAA,CAAApB,aAAA,GAAAC,CAAA,QAAAgB,EAAA,CAAAG,QAAA;IAAA;IAAApB,aAAA,GAAAC,CAAA;;;;;;;;;;UACrD,IAAI,CAACQ,MAAM,EAAE;YAAA;YAAAT,aAAA,GAAAa,CAAA;YAAAb,aAAA,GAAAC,CAAA;YACXoB,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;YAAC;YAAAtB,aAAA,GAAAC,CAAA;YACtE,sBAAO;cAAEsB,OAAO,EAAE,KAAK;cAAEC,KAAK,EAAE;YAA8B,CAAE;UAClE,CAAC;UAAA;UAAA;YAAAxB,aAAA,GAAAa,CAAA;UAAA;UAAAb,aAAA,GAAAC,CAAA;;;;;;;;;UAGc,qBAAM,IAAAI,QAAA,CAAAoB,MAAM,EAACL,QAAQ,CAAC;;;;;UAA7BM,IAAI,GAAGC,EAAA,CAAAC,IAAA,EAAsB;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAEX,qBAAMQ,MAAM,CAACoB,MAAM,CAACC,IAAI,CAAC;YAC/CC,IAAI;YAAE;YAAA,CAAA/B,aAAA,GAAAa,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACqB,UAAU;YAAA;YAAA,CAAAhC,aAAA,GAAAa,CAAA,WAAI,uBAAuB;YAAE;YACzDK,EAAE,EAAAA,EAAA;YACFC,OAAO,EAAAA,OAAA;YACPO,IAAI,EAAAA;WACL,CAAC;;;;;UALIO,EAAA,GAAkBN,EAAA,CAAAC,IAAA,EAKtB,EALMM,IAAI,GAAAD,EAAA,CAAAC,IAAA,EAAEV,KAAK,GAAAS,EAAA,CAAAT,KAAA;UAAA;UAAAxB,aAAA,GAAAC,CAAA;UAOnB,IAAIuB,KAAK,EAAE;YAAA;YAAAxB,aAAA,GAAAa,CAAA;YAAAb,aAAA,GAAAC,CAAA;YACToB,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAAC;YAAAxB,aAAA,GAAAC,CAAA;YAC7C,sBAAO;cAAEsB,OAAO,EAAE,KAAK;cAAEC,KAAK,EAAEA,KAAK,CAACW;YAAO,CAAE;UACjD,CAAC;UAAA;UAAA;YAAAnC,aAAA,GAAAa,CAAA;UAAA;UAAAb,aAAA,GAAAC,CAAA;UAEDoB,OAAO,CAACe,GAAG,CAAC,0BAA0B,EAAEF,IAAI,CAAC;UAAC;UAAAlC,aAAA,GAAAC,CAAA;UAC9C,sBAAO;YAAEsB,OAAO,EAAE,IAAI;YAAEW,IAAI,EAAAA;UAAA,CAAE;;;;;;;;UAE9Bb,OAAO,CAACG,KAAK,CAAC,0CAA0C,EAAEa,OAAK,CAAC;UAAC;UAAArC,aAAA,GAAAC,CAAA;UACjE,sBAAO;YAAEsB,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAGa,OAAe,CAACF;UAAO,CAAE;;;;;;;;;;AAS9D,SAAsBjC,sBAAsBA,CAACoC,MAAoC;EAAA;EAAAtC,aAAA,GAAAgB,CAAA;EAAAhB,aAAA,GAAAC,CAAA;;;;;;;;;;;;;;;;UAC/E,IAAI,CAACQ,MAAM,EAAE;YAAA;YAAAT,aAAA,GAAAa,CAAA;YAAAb,aAAA,GAAAC,CAAA;YACXoB,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;YAAC;YAAAtB,aAAA,GAAAC,CAAA;YACtE,sBAAO;cAAEsB,OAAO,EAAE,KAAK;cAAEC,KAAK,EAAE;YAA8B,CAAE;UAClE,CAAC;UAAA;UAAA;YAAAxB,aAAA,GAAAa,CAAA;UAAA;UAAAb,aAAA,GAAAC,CAAA;UAEOiB,EAAE,GAAUoB,MAAM,CAAApB,EAAhB,EAAEqB,GAAG,GAAKD,MAAM,CAAAC,GAAX;UAAY;UAAAvC,aAAA,GAAAC,CAAA;;;;;;;;;UAEzB,qBAAMQ,MAAM,CAACoB,MAAM,CAACC,IAAI,CAAC;YACvBC,IAAI,EAAE,uBAAuB;YAAE;YAC/Bb,EAAE,EAAAA,EAAA;YACFC,OAAO,EAAE,qBAAqB;YAC9BqB,KAAK,EAAElC,OAAA,CAAAmC,OAAK,CAACC,aAAa,CAAClC,oBAAA,CAAAmC,kBAAkB,EAAE;cAAEC,SAAS,EAAEL;YAAG,CAAE;WAClE,CAAC;;;;;UALFxB,EAAA,CAAAa,IAAA,EAKE;UAAC;UAAA5B,aAAA,GAAAC,CAAA;UACH,sBAAO;YAAEsB,OAAO,EAAE;UAAI,CAAE;;;;;;;;UAExBF,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAEqB,OAAK,CAAC;UAAC;UAAA7C,aAAA,GAAAC,CAAA;UAC5D,sBAAO;YAAEsB,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAGqB,OAAe,CAACV;UAAO,CAAE", "ignoreList": []}