b7834f811427026a50251a7563f74b10
"use strict";

/* istanbul ignore next */
function cov_twjp3ipar() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/email.ts";
  var hash = "d8c9209ff1c0b8aa5e7134482eebf560e4c76a15";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/email.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 30
        }
      },
      "71": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 56
        }
      },
      "72": {
        start: {
          line: 44,
          column: 15
        },
        end: {
          line: 44,
          column: 32
        }
      },
      "73": {
        start: {
          line: 45,
          column: 15
        },
        end: {
          line: 45,
          column: 45
        }
      },
      "74": {
        start: {
          line: 46,
          column: 14
        },
        end: {
          line: 46,
          column: 47
        }
      },
      "75": {
        start: {
          line: 47,
          column: 27
        },
        end: {
          line: 47,
          column: 66
        }
      },
      "76": {
        start: {
          line: 48,
          column: 13
        },
        end: {
          line: 48,
          column: 96
        }
      },
      "77": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 87,
          column: 7
        }
      },
      "78": {
        start: {
          line: 52,
          column: 17
        },
        end: {
          line: 52,
          column: 22
        }
      },
      "79": {
        start: {
          line: 52,
          column: 34
        },
        end: {
          line: 52,
          column: 44
        }
      },
      "80": {
        start: {
          line: 52,
          column: 57
        },
        end: {
          line: 52,
          column: 68
        }
      },
      "81": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 86,
          column: 11
        }
      },
      "82": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 85,
          column: 13
        }
      },
      "83": {
        start: {
          line: 56,
          column: 20
        },
        end: {
          line: 59,
          column: 21
        }
      },
      "84": {
        start: {
          line: 57,
          column: 24
        },
        end: {
          line: 57,
          column: 94
        }
      },
      "85": {
        start: {
          line: 58,
          column: 24
        },
        end: {
          line: 58,
          column: 105
        }
      },
      "86": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 33
        }
      },
      "87": {
        start: {
          line: 62,
          column: 20
        },
        end: {
          line: 62,
          column: 46
        }
      },
      "88": {
        start: {
          line: 63,
          column: 20
        },
        end: {
          line: 63,
          column: 73
        }
      },
      "89": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 65,
          column: 37
        }
      },
      "90": {
        start: {
          line: 66,
          column: 20
        },
        end: {
          line: 71,
          column: 28
        }
      },
      "91": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 69
        }
      },
      "92": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 77,
          column: 21
        }
      },
      "93": {
        start: {
          line: 75,
          column: 24
        },
        end: {
          line: 75,
          column: 69
        }
      },
      "94": {
        start: {
          line: 76,
          column: 24
        },
        end: {
          line: 76,
          column: 88
        }
      },
      "95": {
        start: {
          line: 78,
          column: 20
        },
        end: {
          line: 78,
          column: 66
        }
      },
      "96": {
        start: {
          line: 79,
          column: 20
        },
        end: {
          line: 79,
          column: 73
        }
      },
      "97": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 81,
          column: 40
        }
      },
      "98": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 82,
          column: 87
        }
      },
      "99": {
        start: {
          line: 83,
          column: 20
        },
        end: {
          line: 83,
          column: 86
        }
      },
      "100": {
        start: {
          line: 84,
          column: 24
        },
        end: {
          line: 84,
          column: 46
        }
      },
      "101": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 119,
          column: 7
        }
      },
      "102": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 118,
          column: 11
        }
      },
      "103": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 117,
          column: 13
        }
      },
      "104": {
        start: {
          line: 95,
          column: 20
        },
        end: {
          line: 98,
          column: 21
        }
      },
      "105": {
        start: {
          line: 96,
          column: 24
        },
        end: {
          line: 96,
          column: 94
        }
      },
      "106": {
        start: {
          line: 97,
          column: 24
        },
        end: {
          line: 97,
          column: 105
        }
      },
      "107": {
        start: {
          line: 99,
          column: 20
        },
        end: {
          line: 99,
          column: 53
        }
      },
      "108": {
        start: {
          line: 100,
          column: 20
        },
        end: {
          line: 100,
          column: 33
        }
      },
      "109": {
        start: {
          line: 102,
          column: 20
        },
        end: {
          line: 102,
          column: 46
        }
      },
      "110": {
        start: {
          line: 103,
          column: 20
        },
        end: {
          line: 108,
          column: 28
        }
      },
      "111": {
        start: {
          line: 110,
          column: 20
        },
        end: {
          line: 110,
          column: 30
        }
      },
      "112": {
        start: {
          line: 111,
          column: 20
        },
        end: {
          line: 111,
          column: 61
        }
      },
      "113": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 113,
          column: 40
        }
      },
      "114": {
        start: {
          line: 114,
          column: 20
        },
        end: {
          line: 114,
          column: 82
        }
      },
      "115": {
        start: {
          line: 115,
          column: 20
        },
        end: {
          line: 115,
          column: 86
        }
      },
      "116": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 46
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "sendEmail",
        decl: {
          start: {
            line: 49,
            column: 9
          },
          end: {
            line: 49,
            column: 18
          }
        },
        loc: {
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 88,
            column: 1
          }
        },
        line: 49
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 50,
            column: 46
          },
          end: {
            line: 50,
            column: 47
          }
        },
        loc: {
          start: {
            line: 50,
            column: 60
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 50
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 53,
            column: 33
          },
          end: {
            line: 53,
            column: 34
          }
        },
        loc: {
          start: {
            line: 53,
            column: 47
          },
          end: {
            line: 86,
            column: 9
          }
        },
        line: 53
      },
      "17": {
        name: "sendPasswordResetEmail",
        decl: {
          start: {
            line: 89,
            column: 9
          },
          end: {
            line: 89,
            column: 31
          }
        },
        loc: {
          start: {
            line: 89,
            column: 40
          },
          end: {
            line: 120,
            column: 1
          }
        },
        line: 89
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 90,
            column: 43
          },
          end: {
            line: 90,
            column: 44
          }
        },
        loc: {
          start: {
            line: 90,
            column: 55
          },
          end: {
            line: 119,
            column: 5
          }
        },
        line: 90
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 92,
            column: 33
          },
          end: {
            line: 92,
            column: 34
          }
        },
        loc: {
          start: {
            line: 92,
            column: 47
          },
          end: {
            line: 118,
            column: 9
          }
        },
        line: 92
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 48,
            column: 13
          },
          end: {
            line: 48,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 48,
            column: 42
          },
          end: {
            line: 48,
            column: 89
          }
        }, {
          start: {
            line: 48,
            column: 92
          },
          end: {
            line: 48,
            column: 96
          }
        }],
        line: 48
      },
      "36": {
        loc: {
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 85,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 55,
            column: 16
          },
          end: {
            line: 60,
            column: 33
          }
        }, {
          start: {
            line: 61,
            column: 16
          },
          end: {
            line: 63,
            column: 73
          }
        }, {
          start: {
            line: 64,
            column: 16
          },
          end: {
            line: 71,
            column: 28
          }
        }, {
          start: {
            line: 72,
            column: 16
          },
          end: {
            line: 79,
            column: 73
          }
        }, {
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 83,
            column: 86
          }
        }, {
          start: {
            line: 84,
            column: 16
          },
          end: {
            line: 84,
            column: 46
          }
        }],
        line: 54
      },
      "37": {
        loc: {
          start: {
            line: 56,
            column: 20
          },
          end: {
            line: 59,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 20
          },
          end: {
            line: 59,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "38": {
        loc: {
          start: {
            line: 67,
            column: 34
          },
          end: {
            line: 67,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 34
          },
          end: {
            line: 67,
            column: 56
          }
        }, {
          start: {
            line: 67,
            column: 60
          },
          end: {
            line: 67,
            column: 83
          }
        }],
        line: 67
      },
      "39": {
        loc: {
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 77,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 77,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "40": {
        loc: {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 117,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 94,
            column: 16
          },
          end: {
            line: 100,
            column: 33
          }
        }, {
          start: {
            line: 101,
            column: 16
          },
          end: {
            line: 108,
            column: 28
          }
        }, {
          start: {
            line: 109,
            column: 16
          },
          end: {
            line: 111,
            column: 61
          }
        }, {
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 115,
            column: 86
          }
        }, {
          start: {
            line: 116,
            column: 16
          },
          end: {
            line: 116,
            column: 46
          }
        }],
        line: 93
      },
      "41": {
        loc: {
          start: {
            line: 95,
            column: 20
          },
          end: {
            line: 98,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 20
          },
          end: {
            line: 98,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0, 0, 0, 0],
      "41": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/email.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,8BA2BC;AAOD,wDAmBC;AAlED,iCAAgC;AAChC,8CAA6C;AAC7C,gDAA0B;AAC1B,mEAAkE;AAElE,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,eAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAQ1F,SAAsB,SAAS;wDAAC,EAAuC;;YAArC,EAAE,QAAA,EAAE,OAAO,aAAA,EAAE,QAAQ,cAAA;;;;oBACrD,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;wBACtE,sBAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAC;oBACnE,CAAC;;;;oBAGc,qBAAM,IAAA,eAAM,EAAC,QAAQ,CAAC,EAAA;;oBAA7B,IAAI,GAAG,SAAsB;oBAEX,qBAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;4BAC/C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,uBAAuB,EAAE,uCAAuC;4BAChG,EAAE,IAAA;4BACF,OAAO,SAAA;4BACP,IAAI,MAAA;yBACL,CAAC,EAAA;;oBALI,KAAkB,SAKtB,EALM,IAAI,UAAA,EAAE,KAAK,WAAA;oBAOnB,IAAI,KAAK,EAAE,CAAC;wBACV,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;wBAC7C,sBAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,EAAC;oBAClD,CAAC;oBAED,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;oBAC9C,sBAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,MAAA,EAAE,EAAC;;;oBAE/B,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,OAAK,CAAC,CAAC;oBACjE,sBAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,OAAe,CAAC,OAAO,EAAE,EAAC;;;;;CAE9D;AAOD,SAAsB,sBAAsB,CAAC,MAAoC;;;;;;oBAC/E,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;wBACtE,sBAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAC;oBACnE,CAAC;oBAEO,EAAE,GAAU,MAAM,GAAhB,EAAE,GAAG,GAAK,MAAM,IAAX,CAAY;;;;oBAEzB,qBAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;4BACvB,IAAI,EAAE,uBAAuB,EAAE,gDAAgD;4BAC/E,EAAE,IAAA;4BACF,OAAO,EAAE,qBAAqB;4BAC9B,KAAK,EAAE,eAAK,CAAC,aAAa,CAAC,uCAAkB,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;yBACnE,CAAC,EAAA;;oBALF,SAKE,CAAC;oBACH,sBAAO,EAAE,OAAO,EAAE,IAAI,EAAE,EAAC;;;oBAEzB,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,OAAK,CAAC,CAAC;oBAC5D,sBAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,OAAe,CAAC,OAAO,EAAE,EAAC;;;;;CAE9D",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/email.ts"],
      sourcesContent: ["import { Resend } from 'resend';\nimport { render } from '@react-email/render';\nimport React from 'react';\nimport { PasswordResetEmail } from '../emails/PasswordResetEmail';\n\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\n\ninterface EmailOptions {\n  to: string;\n  subject: string;\n  template: React.ReactElement; // Use React.ReactElement for email templates\n}\n\nexport async function sendEmail({ to, subject, template }: EmailOptions) {\n  if (!resend) {\n    console.warn('Email service not configured - RESEND_API_KEY missing');\n    return { success: false, error: 'Email service not configured' };\n  }\n\n  try {\n    const html = await render(template);\n\n    const { data, error } = await resend.emails.send({\n      from: process.env.EMAIL_FROM || '<EMAIL>', // Use environment variable or fallback\n      to,\n      subject,\n      html,\n    });\n\n    if (error) {\n      console.error('Error sending email:', error);\n      return { success: false, error: error.message };\n    }\n\n    console.log('Email sent successfully:', data);\n    return { success: true, data };\n  } catch (error) {\n    console.error('Caught an exception while sending email:', error);\n    return { success: false, error: (error as Error).message };\n  }\n}\n\ninterface SendPasswordResetEmailParams {\n  to: string;\n  url: string;\n}\n\nexport async function sendPasswordResetEmail(params: SendPasswordResetEmailParams) {\n  if (!resend) {\n    console.warn('Email service not configured - RESEND_API_KEY missing');\n    return { success: false, error: 'Email service not configured' };\n  }\n\n  const { to, url } = params;\n  try {\n    await resend.emails.send({\n      from: '<EMAIL>', // Use a verified sender from your Resend domain\n      to,\n      subject: 'Reset your password',\n      react: React.createElement(PasswordResetEmail, { resetLink: url }),\n    });\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending password reset email:', error);\n    return { success: false, error: (error as Error).message };\n  }\n}"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d8c9209ff1c0b8aa5e7134482eebf560e4c76a15"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_twjp3ipar = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_twjp3ipar();
var __awaiter =
/* istanbul ignore next */
(cov_twjp3ipar().s[0]++,
/* istanbul ignore next */
(cov_twjp3ipar().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_twjp3ipar().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_twjp3ipar().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_twjp3ipar().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_twjp3ipar().f[1]++;
    cov_twjp3ipar().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_twjp3ipar().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_twjp3ipar().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_twjp3ipar().f[2]++;
      cov_twjp3ipar().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_twjp3ipar().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_twjp3ipar().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_twjp3ipar().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_twjp3ipar().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_twjp3ipar().f[4]++;
      cov_twjp3ipar().s[4]++;
      try {
        /* istanbul ignore next */
        cov_twjp3ipar().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_twjp3ipar().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_twjp3ipar().f[5]++;
      cov_twjp3ipar().s[7]++;
      try {
        /* istanbul ignore next */
        cov_twjp3ipar().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_twjp3ipar().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_twjp3ipar().f[6]++;
      cov_twjp3ipar().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_twjp3ipar().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_twjp3ipar().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_twjp3ipar().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_twjp3ipar().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_twjp3ipar().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_twjp3ipar().s[12]++,
/* istanbul ignore next */
(cov_twjp3ipar().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_twjp3ipar().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_twjp3ipar().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_twjp3ipar().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_twjp3ipar().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_twjp3ipar().f[8]++;
        cov_twjp3ipar().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_twjp3ipar().b[6][0]++;
          cov_twjp3ipar().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_twjp3ipar().b[6][1]++;
        }
        cov_twjp3ipar().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_twjp3ipar().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_twjp3ipar().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_twjp3ipar().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_twjp3ipar().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_twjp3ipar().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_twjp3ipar().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_twjp3ipar().f[9]++;
    cov_twjp3ipar().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_twjp3ipar().f[10]++;
    cov_twjp3ipar().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_twjp3ipar().f[11]++;
      cov_twjp3ipar().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_twjp3ipar().f[12]++;
    cov_twjp3ipar().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_twjp3ipar().b[9][0]++;
      cov_twjp3ipar().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_twjp3ipar().b[9][1]++;
    }
    cov_twjp3ipar().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_twjp3ipar().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_twjp3ipar().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_twjp3ipar().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_twjp3ipar().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_twjp3ipar().s[25]++;
      try {
        /* istanbul ignore next */
        cov_twjp3ipar().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_twjp3ipar().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_twjp3ipar().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_twjp3ipar().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_twjp3ipar().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_twjp3ipar().b[15][0]++,
        /* istanbul ignore next */
        (cov_twjp3ipar().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_twjp3ipar().b[16][1]++,
        /* istanbul ignore next */
        (cov_twjp3ipar().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_twjp3ipar().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_twjp3ipar().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_twjp3ipar().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_twjp3ipar().b[12][0]++;
          cov_twjp3ipar().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_twjp3ipar().b[12][1]++;
        }
        cov_twjp3ipar().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_twjp3ipar().b[18][0]++;
          cov_twjp3ipar().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_twjp3ipar().b[18][1]++;
        }
        cov_twjp3ipar().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_twjp3ipar().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_twjp3ipar().b[19][1]++;
            cov_twjp3ipar().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_twjp3ipar().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_twjp3ipar().b[19][2]++;
            cov_twjp3ipar().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_twjp3ipar().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_twjp3ipar().b[19][3]++;
            cov_twjp3ipar().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_twjp3ipar().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_twjp3ipar().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_twjp3ipar().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_twjp3ipar().b[19][4]++;
            cov_twjp3ipar().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_twjp3ipar().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_twjp3ipar().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_twjp3ipar().b[19][5]++;
            cov_twjp3ipar().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_twjp3ipar().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_twjp3ipar().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_twjp3ipar().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_twjp3ipar().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_twjp3ipar().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_twjp3ipar().b[20][0]++;
              cov_twjp3ipar().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_twjp3ipar().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_twjp3ipar().b[20][1]++;
            }
            cov_twjp3ipar().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_twjp3ipar().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_twjp3ipar().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_twjp3ipar().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_twjp3ipar().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_twjp3ipar().b[23][0]++;
              cov_twjp3ipar().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_twjp3ipar().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_twjp3ipar().b[23][1]++;
            }
            cov_twjp3ipar().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_twjp3ipar().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_twjp3ipar().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_twjp3ipar().b[25][0]++;
              cov_twjp3ipar().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_twjp3ipar().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_twjp3ipar().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_twjp3ipar().b[25][1]++;
            }
            cov_twjp3ipar().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_twjp3ipar().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_twjp3ipar().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_twjp3ipar().b[27][0]++;
              cov_twjp3ipar().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_twjp3ipar().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_twjp3ipar().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_twjp3ipar().b[27][1]++;
            }
            cov_twjp3ipar().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_twjp3ipar().b[29][0]++;
              cov_twjp3ipar().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_twjp3ipar().b[29][1]++;
            }
            cov_twjp3ipar().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_twjp3ipar().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_twjp3ipar().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_twjp3ipar().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_twjp3ipar().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_twjp3ipar().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_twjp3ipar().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_twjp3ipar().b[30][0]++;
      cov_twjp3ipar().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_twjp3ipar().b[30][1]++;
    }
    cov_twjp3ipar().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_twjp3ipar().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_twjp3ipar().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_twjp3ipar().s[67]++,
/* istanbul ignore next */
(cov_twjp3ipar().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_twjp3ipar().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_twjp3ipar().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_twjp3ipar().f[13]++;
  cov_twjp3ipar().s[68]++;
  return /* istanbul ignore next */(cov_twjp3ipar().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_twjp3ipar().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_twjp3ipar().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_twjp3ipar().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_twjp3ipar().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_twjp3ipar().s[70]++;
exports.sendEmail = sendEmail;
/* istanbul ignore next */
cov_twjp3ipar().s[71]++;
exports.sendPasswordResetEmail = sendPasswordResetEmail;
var resend_1 =
/* istanbul ignore next */
(cov_twjp3ipar().s[72]++, require("resend"));
var render_1 =
/* istanbul ignore next */
(cov_twjp3ipar().s[73]++, require("@react-email/render"));
var react_1 =
/* istanbul ignore next */
(cov_twjp3ipar().s[74]++, __importDefault(require("react")));
var PasswordResetEmail_1 =
/* istanbul ignore next */
(cov_twjp3ipar().s[75]++, require("../emails/PasswordResetEmail"));
var resend =
/* istanbul ignore next */
(cov_twjp3ipar().s[76]++, process.env.RESEND_API_KEY ?
/* istanbul ignore next */
(cov_twjp3ipar().b[35][0]++, new resend_1.Resend(process.env.RESEND_API_KEY)) :
/* istanbul ignore next */
(cov_twjp3ipar().b[35][1]++, null));
function sendEmail(_a) {
  /* istanbul ignore next */
  cov_twjp3ipar().f[14]++;
  cov_twjp3ipar().s[77]++;
  return __awaiter(this, arguments, void 0, function (_b) {
    /* istanbul ignore next */
    cov_twjp3ipar().f[15]++;
    var html, _c, data, error, error_1;
    var to =
      /* istanbul ignore next */
      (cov_twjp3ipar().s[78]++, _b.to),
      subject =
      /* istanbul ignore next */
      (cov_twjp3ipar().s[79]++, _b.subject),
      template =
      /* istanbul ignore next */
      (cov_twjp3ipar().s[80]++, _b.template);
    /* istanbul ignore next */
    cov_twjp3ipar().s[81]++;
    return __generator(this, function (_d) {
      /* istanbul ignore next */
      cov_twjp3ipar().f[16]++;
      cov_twjp3ipar().s[82]++;
      switch (_d.label) {
        case 0:
          /* istanbul ignore next */
          cov_twjp3ipar().b[36][0]++;
          cov_twjp3ipar().s[83]++;
          if (!resend) {
            /* istanbul ignore next */
            cov_twjp3ipar().b[37][0]++;
            cov_twjp3ipar().s[84]++;
            console.warn('Email service not configured - RESEND_API_KEY missing');
            /* istanbul ignore next */
            cov_twjp3ipar().s[85]++;
            return [2 /*return*/, {
              success: false,
              error: 'Email service not configured'
            }];
          } else
          /* istanbul ignore next */
          {
            cov_twjp3ipar().b[37][1]++;
          }
          cov_twjp3ipar().s[86]++;
          _d.label = 1;
        case 1:
          /* istanbul ignore next */
          cov_twjp3ipar().b[36][1]++;
          cov_twjp3ipar().s[87]++;
          _d.trys.push([1, 4,, 5]);
          /* istanbul ignore next */
          cov_twjp3ipar().s[88]++;
          return [4 /*yield*/, (0, render_1.render)(template)];
        case 2:
          /* istanbul ignore next */
          cov_twjp3ipar().b[36][2]++;
          cov_twjp3ipar().s[89]++;
          html = _d.sent();
          /* istanbul ignore next */
          cov_twjp3ipar().s[90]++;
          return [4 /*yield*/, resend.emails.send({
            from:
            /* istanbul ignore next */
            (cov_twjp3ipar().b[38][0]++, process.env.EMAIL_FROM) ||
            /* istanbul ignore next */
            (cov_twjp3ipar().b[38][1]++, '<EMAIL>'),
            // Use environment variable or fallback
            to: to,
            subject: subject,
            html: html
          })];
        case 3:
          /* istanbul ignore next */
          cov_twjp3ipar().b[36][3]++;
          cov_twjp3ipar().s[91]++;
          _c = _d.sent(), data = _c.data, error = _c.error;
          /* istanbul ignore next */
          cov_twjp3ipar().s[92]++;
          if (error) {
            /* istanbul ignore next */
            cov_twjp3ipar().b[39][0]++;
            cov_twjp3ipar().s[93]++;
            console.error('Error sending email:', error);
            /* istanbul ignore next */
            cov_twjp3ipar().s[94]++;
            return [2 /*return*/, {
              success: false,
              error: error.message
            }];
          } else
          /* istanbul ignore next */
          {
            cov_twjp3ipar().b[39][1]++;
          }
          cov_twjp3ipar().s[95]++;
          console.log('Email sent successfully:', data);
          /* istanbul ignore next */
          cov_twjp3ipar().s[96]++;
          return [2 /*return*/, {
            success: true,
            data: data
          }];
        case 4:
          /* istanbul ignore next */
          cov_twjp3ipar().b[36][4]++;
          cov_twjp3ipar().s[97]++;
          error_1 = _d.sent();
          /* istanbul ignore next */
          cov_twjp3ipar().s[98]++;
          console.error('Caught an exception while sending email:', error_1);
          /* istanbul ignore next */
          cov_twjp3ipar().s[99]++;
          return [2 /*return*/, {
            success: false,
            error: error_1.message
          }];
        case 5:
          /* istanbul ignore next */
          cov_twjp3ipar().b[36][5]++;
          cov_twjp3ipar().s[100]++;
          return [2 /*return*/];
      }
    });
  });
}
function sendPasswordResetEmail(params) {
  /* istanbul ignore next */
  cov_twjp3ipar().f[17]++;
  cov_twjp3ipar().s[101]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_twjp3ipar().f[18]++;
    var to, url, error_2;
    /* istanbul ignore next */
    cov_twjp3ipar().s[102]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_twjp3ipar().f[19]++;
      cov_twjp3ipar().s[103]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_twjp3ipar().b[40][0]++;
          cov_twjp3ipar().s[104]++;
          if (!resend) {
            /* istanbul ignore next */
            cov_twjp3ipar().b[41][0]++;
            cov_twjp3ipar().s[105]++;
            console.warn('Email service not configured - RESEND_API_KEY missing');
            /* istanbul ignore next */
            cov_twjp3ipar().s[106]++;
            return [2 /*return*/, {
              success: false,
              error: 'Email service not configured'
            }];
          } else
          /* istanbul ignore next */
          {
            cov_twjp3ipar().b[41][1]++;
          }
          cov_twjp3ipar().s[107]++;
          to = params.to, url = params.url;
          /* istanbul ignore next */
          cov_twjp3ipar().s[108]++;
          _a.label = 1;
        case 1:
          /* istanbul ignore next */
          cov_twjp3ipar().b[40][1]++;
          cov_twjp3ipar().s[109]++;
          _a.trys.push([1, 3,, 4]);
          /* istanbul ignore next */
          cov_twjp3ipar().s[110]++;
          return [4 /*yield*/, resend.emails.send({
            from: '<EMAIL>',
            // Use a verified sender from your Resend domain
            to: to,
            subject: 'Reset your password',
            react: react_1.default.createElement(PasswordResetEmail_1.PasswordResetEmail, {
              resetLink: url
            })
          })];
        case 2:
          /* istanbul ignore next */
          cov_twjp3ipar().b[40][2]++;
          cov_twjp3ipar().s[111]++;
          _a.sent();
          /* istanbul ignore next */
          cov_twjp3ipar().s[112]++;
          return [2 /*return*/, {
            success: true
          }];
        case 3:
          /* istanbul ignore next */
          cov_twjp3ipar().b[40][3]++;
          cov_twjp3ipar().s[113]++;
          error_2 = _a.sent();
          /* istanbul ignore next */
          cov_twjp3ipar().s[114]++;
          console.error('Error sending password reset email:', error_2);
          /* istanbul ignore next */
          cov_twjp3ipar().s[115]++;
          return [2 /*return*/, {
            success: false,
            error: error_2.message
          }];
        case 4:
          /* istanbul ignore next */
          cov_twjp3ipar().b[40][4]++;
          cov_twjp3ipar().s[116]++;
          return [2 /*return*/];
      }
    });
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************