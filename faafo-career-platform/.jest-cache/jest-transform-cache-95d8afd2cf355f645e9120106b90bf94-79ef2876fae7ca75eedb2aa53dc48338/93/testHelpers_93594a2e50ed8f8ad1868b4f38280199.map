{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/utils/testHelpers.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAA2B;AAC3B,kDAA4B;AAG5B,0DAA0D;AAC1D;IAQE;QANQ,iBAAY,GAAgB,IAAI,GAAG,EAAE,CAAC;QACtC,qBAAgB,GAAgB,IAAI,GAAG,EAAE,CAAC;QAC1C,oBAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;QACzC,mBAAc,GAAgB,IAAI,GAAG,EAAE,CAAC;QACxC,cAAS,GAAW,CAAC,CAAC;QAG5B,oCAAoC;QACpC,IAAI,CAAC,UAAU,GAAI,MAAc,CAAC,UAAU,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;QAChG,CAAC;IACH,CAAC;IAEK,8BAAO,GAAb;;;gBACE,yDAAyD;gBACzD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;gBAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC7B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;gBACnB,sBAAO,OAAO,CAAC,OAAO,EAAE,EAAC;;;KAC1B;IAED,oDAAoD;IAC5C,0CAAmB,GAA3B,UAA4B,SAAiB;QAC3C,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAA,KAAsB,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAzC,SAAS,QAAA,EAAE,MAAM,QAAwB,CAAC;QACjD,OAAO,UAAG,SAAS,cAAI,SAAS,cAAI,MAAM,CAAE,CAAC;IAC/C,CAAC;IAEK,qCAAc,GAApB;4DAAqB,SAA4B;;;YAA5B,0BAAA,EAAA,cAA4B;;;;wBAEzC,SAAS,GAAG,SAAS,CAAC,KAAK,IAAI,kBAAkB,CAAC;wBACpD,KAAK,GAAG,SAAS,CAAC;wBAEtB,kEAAkE;wBAClE,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;4BACrC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;wBAC9C,CAAC;wBAGK,QAAQ,cACZ,KAAK,OAAA,EACL,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAC5B,IAAI,EAAE,SAAS,CAAC,IAAI,IACjB,SAAS,CACb,CAAC;wBAEF,uFAAuF;wBACvF,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;4BACpB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;wBACvC,CAAC;wBACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;4BAChE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;wBAC1C,CAAC;wBAGK,UAAU,GAAG,kDAAkD,CAAC;wBACtE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4BACtE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;wBAC1C,CAAC;wBAED,4BAA4B;wBAC5B,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,IAAI,CAAA,MAAA,QAAQ,CAAC,IAAI,0CAAE,MAAM,IAAG,IAAI,EAAE,CAAC;4BAChE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;wBACrC,CAAC;wBAED,yDAAyD;wBACzD,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;4BAC/F,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;wBAC5D,CAAC;6BAGsB,QAAQ,CAAC,QAAQ,EAAjB,wBAAiB;6BACnC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAApC,wBAAoC;wBACjC,KAAA,QAAQ,CAAC,QAAQ,CAAA;;4BACjB,qBAAM,gBAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAA;;wBAAxC,KAAA,SAAwC,CAAA;;;wBAF5C,KAAA,IAE6C,CAAA;;;wBAC7C,KAAA,IAAI,CAAA;;;wBAJF,cAAc,KAIZ;wBAEF,QAAQ,GAAG;4BACf,EAAE,EAAE,oBAAa,IAAI,CAAC,GAAG,EAAE,cAAI,EAAE,IAAI,CAAC,SAAS,CAAE;4BACjD,KAAK,EAAE,QAAQ,CAAC,KAAK;4BACrB,QAAQ,EAAE,cAAc;4BACxB,IAAI,EAAE,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;4BAC7D,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,IAAI;4BAC7B,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,IAAI;4BAC7C,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB,IAAI,CAAC;4BACtD,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,IAAI;4BACzC,kBAAkB,EAAE,QAAQ,CAAC,kBAAkB,IAAI,IAAI;4BACvD,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB,IAAI,IAAI;4BAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,IAAI;yBAC1C,CAAC;wBAEF,qBAAqB;wBACrB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBAEtC,oBAAoB;wBACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACxD,sBAAO,QAAQ,EAAC;;;;KACjB;IAED,0DAA0D;IACpD,8CAAuB,GAA7B,UAA8B,QAAa;;;;;;wBACnC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC;wBAEnD,4FAA4F;wBAC5F,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;4BACjC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;wBAC1C,CAAC;6BAGsB,QAAQ,CAAC,QAAQ,EAAjB,wBAAiB;6BACnC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAApC,wBAAoC;wBACjC,KAAA,QAAQ,CAAC,QAAQ,CAAA;;4BACjB,qBAAM,gBAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAA;;wBAAxC,KAAA,SAAwC,CAAA;;;wBAF5C,KAAA,IAE6C,CAAA;;;wBAC7C,KAAA,IAAI,CAAA;;;wBAJF,cAAc,KAIZ;wBAEF,QAAQ,GAAG;4BACf,EAAE,EAAE,oBAAa,IAAI,CAAC,GAAG,EAAE,cAAI,EAAE,IAAI,CAAC,SAAS,CAAE;4BACjD,KAAK,EAAE,KAAK;4BACZ,QAAQ,EAAE,cAAc;4BACxB,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,IAAI;4BAC3B,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,IAAI;4BAC7B,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,IAAI;4BAC7C,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB,IAAI,CAAC;4BACtD,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,IAAI;4BACzC,kBAAkB,EAAE,QAAQ,CAAC,kBAAkB,IAAI,IAAI;4BACvD,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB,IAAI,IAAI;4BAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,IAAI;yBAC1C,CAAC;wBAEF,qBAAqB;wBACrB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBAE7B,oBAAoB;wBACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACxD,sBAAO,QAAQ,EAAC;;;;KACjB;IAEK,2CAAoB,GAA1B;4DAA2B,MAAc,EAAE,SAA4B;;YAA5B,0BAAA,EAAA,cAA4B;;gBACrE,oEAAoE;gBACpE,iEAAiE;gBACjE,IAAI,SAAS,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,MAAM,KAAK,aAAa,EAAE,CAAC;oBACvG,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;gBACzC,CAAC;gBAGK,WAAW,GAAG,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC;gBAC/C,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;oBACxC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;gBACzC,CAAC;gBAED,kCAAkC;gBAClC,IAAI,SAAS,CAAC,kBAAkB,EAAE,CAAC;oBAC3B,aAAa,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;oBAChE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC9C,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;oBAC/C,CAAC;gBACH,CAAC;gBAEK,cAAc,cAClB,EAAE,EAAE,0BAAmB,IAAI,CAAC,GAAG,EAAE,cAAI,EAAE,IAAI,CAAC,SAAS,CAAE,EACvD,MAAM,QAAA,EACN,MAAM,EAAE,aAAa,EACrB,WAAW,aAAA,EACX,SAAS,EAAE,EAAE,EACb,KAAK,EAAE,CAAC,EACR,SAAS,EAAE,IAAI,IAAI,EAAE,EACrB,SAAS,EAAE,IAAI,IAAI,EAAE,EACrB,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,IAAI,IACvC,SAAS,CACb,CAAC;gBAEF,4CAA4C;gBAC5C,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBACpE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBACxE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBAEpE,sBAAO,cAAc,EAAC;;;KACvB;IAEK,iDAA0B,GAAhC;4DAAiC,SAA4B;;YAA5B,0BAAA,EAAA,cAA4B;;gBACrD,YAAY,cAChB,KAAK,EAAE,eAAe,EACtB,WAAW,EAAE,0BAA0B,EACvC,GAAG,EAAE,mCAAmC,EACxC,IAAI,EAAE,QAAQ,EACd,QAAQ,EAAE,eAAe,EACzB,UAAU,EAAE,UAAU,EACtB,cAAc,EAAE,EAAE,EAClB,QAAQ,EAAE,IAAI,IACX,SAAS,CACb,CAAC;gBAGI,UAAU,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACtE,IAAI,SAAS,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5E,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBAC3C,CAAC;gBAED,iCAAiC;gBACjC,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;oBAChD,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBACxC,CAAC;gBAEK,YAAY,uBAChB,EAAE,EAAE,wBAAiB,IAAI,CAAC,GAAG,EAAE,cAAI,EAAE,IAAI,CAAC,SAAS,CAAE,IAClD,YAAY,KACf,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,IAAI,EACnC,QAAQ,EAAE,YAAY,CAAC,QAAQ,IAAI,IAAI,EACvC,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,MAAM,EACjC,SAAS,EAAE,IAAI,IAAI,EAAE,EACrB,SAAS,EAAE,IAAI,IAAI,EAAE,GACtB,CAAC;gBAEF,yBAAyB;gBACzB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAE5C,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBACxE,sBAAO,YAAY,EAAC;;;KACrB;IAEK,yCAAkB,GAAxB;4DAAyB,MAAc,EAAE,UAAkB,EAAE,SAA4B;;YAA5B,0BAAA,EAAA,cAA4B;;gBAEjF,WAAW,GAAG,UAAG,MAAM,cAAI,UAAU,CAAE,CAAC;gBAC9C,IAAI,SAAS,CAAC,oBAAoB,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC5E,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;gBACvF,CAAC;gBAGK,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAG,WAAW,cAAI,IAAI,CAAC,GAAG,EAAE,CAAE,CAAC,CAAC,CAAC,WAAW,CAAC;gBAEjG,YAAY,cAChB,EAAE,EAAE,wBAAiB,IAAI,CAAC,GAAG,EAAE,cAAI,EAAE,IAAI,CAAC,SAAS,CAAE,EACrD,MAAM,QAAA,EACN,UAAU,YAAA,EACV,MAAM,EAAE,aAAa,EACrB,QAAQ,EAAE,CAAC,EACX,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,IAAI,EAC1C,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,IAAI,EAChC,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,IAAI,EAChC,SAAS,EAAE,IAAI,IAAI,EAAE,EACrB,SAAS,EAAE,IAAI,IAAI,EAAE,IAClB,SAAS,CACb,CAAC;gBAEF,yBAAyB;gBACzB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAEpC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBACpE,sBAAO,YAAY,EAAC;;;KACrB;IAEK,uCAAgB,GAAtB;4DAAuB,MAAc,EAAE,UAAkB,EAAE,SAA4B;;YAA5B,0BAAA,EAAA,cAA4B;;gBAC/E,UAAU,cACd,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,iBAAiB,EACzB,SAAS,EAAE,IAAI,IACZ,SAAS,CACb,CAAC;gBAEF,wBAAwB;gBACxB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACpD,CAAC;gBAGK,SAAS,GAAG,UAAG,MAAM,cAAI,UAAU,CAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBACzE,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;gBAC9E,CAAC;gBAGK,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAG,SAAS,cAAI,IAAI,CAAC,GAAG,EAAE,CAAE,CAAC,CAAC,CAAC,SAAS,CAAC;gBAE1F,UAAU,uBACd,EAAE,EAAE,sBAAe,IAAI,CAAC,GAAG,EAAE,cAAI,EAAE,IAAI,CAAC,SAAS,CAAE,EACnD,MAAM,QAAA,EACN,UAAU,YAAA,IACP,UAAU,KACb,SAAS,EAAE,IAAI,IAAI,EAAE,EACrB,SAAS,EAAE,IAAI,IAAI,EAAE,GACtB,CAAC;gBAEF,uBAAuB;gBACvB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAEnC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBACpE,sBAAO,UAAU,EAAC;;;KACnB;IAEK,iCAAU,GAAhB;;;gBACE,kBAAkB;gBAClB,sBAAO,OAAO,CAAC,OAAO,EAAE,EAAC;;;KAC1B;IACH,mBAAC;AAAD,CAAC,AAjTD,IAiTC;AAjTY,oCAAY;AAmTzB,sBAAsB;AACf,IAAM,iBAAiB,GAAG,UAAC,MAAc,EAAE,SAAsC;IAAtC,0BAAA,EAAA,8BAAsC;IAAK,OAAA,CAAC;QAC5F,IAAI,EAAE;YACJ,EAAE,EAAE,MAAM;YACV,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,WAAW;SAClB;QACD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;KAClE,CAAC;AAP2F,CAO3F,CAAC;AAPU,QAAA,iBAAiB,qBAO3B;AAEH,wBAAwB;AACxB;IAAA;IAsDA,CAAC;IArDQ,+BAAiB,GAAxB,UACE,MAAsB,EACtB,GAA8C,EAC9C,IAAU,EACV,OAAoC;QAHpC,uBAAA,EAAA,cAAsB;QACtB,oBAAA,EAAA,sCAA8C;QAE9C,wBAAA,EAAA,YAAoC;QAEpC,IAAM,WAAW,GAAgB;YAC/B,MAAM,QAAA;YACN,OAAO,aACL,cAAc,EAAE,kBAAkB,IAC/B,OAAO,CACX;SACF,CAAC;QAEF,IAAI,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YAC7B,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,4DAA4D;QAC5D,IAAM,WAAW,GAAG;YAClB,MAAM,QAAA;YACN,GAAG,KAAA;YACH,OAAO,EAAE,IAAI,OAAO,CAAC,WAAW,CAAC,OAAsB,CAAC;YACxD,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,IAAI,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAArB,CAAqB;YACjC,IAAI,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,EAAvC,CAAuC;YACnD,QAAQ,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,IAAI,QAAQ,EAAE,CAAC,EAA/B,CAA+B;YAC/C,OAAO,EAAE;gBACP,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;gBACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;gBACd,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gBACjB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;gBACd,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;aACjB;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ;gBAC/B,YAAY,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,YAAY;aACxC;YACD,GAAG,EAAE,EAAE;YACP,EAAE,EAAE,WAAW;SAChB,CAAC;QAEF,OAAO,WAAqC,CAAC;IAC/C,CAAC;IAEY,2BAAa,GAA1B,UAA2B,QAAsB;;;;;4BAClC,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAClC,IAAI,CAAC;4BACH,sBAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAC;wBAC1B,CAAC;wBAAC,WAAM,CAAC;4BACP,sBAAO,IAAI,EAAC;wBACd,CAAC;;;;;KACF;IACH,oBAAC;AAAD,CAAC,AAtDD,IAsDC;AAtDY,sCAAa;AAwD1B,uBAAuB;AAChB,IAAM,0BAA0B,GAAG,UAAC,SAAmC;IAAnC,0BAAA,EAAA,cAAmC;IAAK,OAAA,YACjF,wBAAwB,EAAE,CAAC,gBAAgB,EAAE,wBAAwB,CAAC,EACtE,wBAAwB,EAAE,MAAM,EAChC,wBAAwB,EAAE,QAAQ,EAClC,wBAAwB,EAAE,KAAK,EAC/B,2BAA2B,EAAE,QAAQ,EACrC,cAAc,EAAE,QAAQ,EACxB,cAAc,EAAE,UAAU,EAC1B,eAAe,EAAE,aAAa,IAC3B,SAAS,EACZ;AAViF,CAUjF,CAAC;AAVU,QAAA,0BAA0B,8BAUpC;AAEH,8BAA8B;AACjB,QAAA,cAAc,GAAG;IAC5B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;IACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;IACnB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;IACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,QAAQ,EAAE,GAAG;IACb,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,EAAE;IACT,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;QACN,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;QACb,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB;CACF,CAAC;AAEF,2BAA2B;AACpB,IAAM,eAAe,GAAG,UAAC,SAAkE;IAChG,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,UAAC,GAAW;QAC9C,IAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAA7B,CAA6B,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC;QAExF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG;YACtC,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,GAAG;YAClC,IAAI,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAtC,CAAsC;YAClD,IAAI,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAtD,CAAsD;SACnE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAXW,QAAA,eAAe,mBAW1B;AAEF,qBAAqB;AACd,IAAM,mBAAmB,GAAG,UAAC,QAAa,EAAE,cAAwB;IACzE,cAAc,CAAC,OAAO,CAAC,UAAA,KAAK;QAC1B,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAJW,QAAA,mBAAmB,uBAI9B;AAEK,IAAM,qBAAqB,GAAG,UAAC,QAAa,EAAE,cAA4B;IAA5B,+BAAA,EAAA,oBAA4B;IAC/E,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACzC,MAAM,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC,CAAC;AAHW,QAAA,qBAAqB,yBAGhC;AAEF,uBAAuB;AAChB,IAAM,iBAAiB,GAAG,UAAC,KAAiB;IAAjB,sBAAA,EAAA,SAAiB;IACjD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC;QAC9C,KAAK,EAAE,kBAAW,CAAC,GAAG,CAAC,iBAAc;QACrC,QAAQ,EAAE,iBAAiB;QAC3B,IAAI,EAAE,oBAAa,CAAC,GAAG,CAAC,CAAE;KAC3B,CAAC,EAJ6C,CAI7C,CAAC,CAAC;AACN,CAAC,CAAC;AANW,QAAA,iBAAiB,qBAM5B;AAEK,IAAM,qBAAqB,GAAG,UAAC,KAAiB;IAAjB,sBAAA,EAAA,SAAiB;IACrD,IAAM,UAAU,GAAG,CAAC,eAAe,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IACxE,IAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC7C,IAAM,WAAW,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IAE7D,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC;QAC9C,KAAK,EAAE,wBAAiB,CAAC,GAAG,CAAC,CAAE;QAC/B,WAAW,EAAE,wCAAiC,CAAC,GAAG,CAAC,CAAE;QACrD,GAAG,EAAE,uCAAgC,CAAC,GAAG,CAAC,CAAE;QAC5C,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7B,QAAQ,EAAE,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;QAC3C,UAAU,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;QAC/C,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,YAAY;KACrB,CAAC,EAT6C,CAS7C,CAAC,CAAC;AACN,CAAC,CAAC;AAfW,QAAA,qBAAqB,yBAehC;AAEF,gCAAgC;AACzB,IAAM,oBAAoB,GAAG,UAAO,EAAsB;;;;;gBACzD,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;gBACjB,qBAAM,EAAE,EAAE,EAAA;;gBAAnB,MAAM,GAAG,SAAU;gBACnB,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;gBAC9B,sBAAO;wBACL,MAAM,QAAA;wBACN,aAAa,EAAE,GAAG,GAAG,KAAK;qBAC3B,EAAC;;;KACH,CAAC;AARW,QAAA,oBAAoB,wBAQ/B;AAEF,6BAA6B;AACtB,IAAM,uBAAuB,GAAG,cAAM,OAAA,CAAC;IAC5C,YAAY,EAAE,CAAC,yBAAyB,EAAE,cAAc,EAAE,UAAU,CAAC;IACrE,GAAG,EAAE,CAAC,+BAA+B,EAAE,yBAAyB,EAAE,kCAAkC,CAAC;IACrG,aAAa,EAAE,CAAC,qBAAqB,EAAE,4CAA4C,CAAC;IACpF,cAAc,EAAE,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;IACjC,SAAS,EAAE,cAAc;IACzB,YAAY,EAAE,gCAAgC;CAC/C,CAAC,EAP2C,CAO3C,CAAC;AAPU,QAAA,uBAAuB,2BAOjC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/utils/testHelpers.ts"], "sourcesContent": ["// Mock imports for testing\nimport bcrypt from 'bcrypt';\nimport { NextRequest, NextResponse } from 'next/server';\n\n// Mock test database utilities with constraint simulation\nexport class TestDatabase {\n  private mockPrisma: any;\n  private createdUsers: Set<string> = new Set();\n  private createdResources: Set<string> = new Set();\n  private createdProgress: Set<string> = new Set();\n  private createdRatings: Set<string> = new Set();\n  private idCounter: number = 0;\n\n  constructor() {\n    // Use the global mock Prisma client\n    this.mockPrisma = (global as any).mockPrisma;\n\n    if (!this.mockPrisma) {\n      throw new Error('Mock Prisma not available. Make sure jest.setup.ts is properly configured.');\n    }\n  }\n\n  async cleanup() {\n    // Mock cleanup - reset the mocks and clear tracking sets\n    jest.clearAllMocks();\n    this.createdUsers.clear();\n    this.createdResources.clear();\n    this.createdProgress.clear();\n    this.createdRatings.clear();\n    this.idCounter = 0;\n    return Promise.resolve();\n  }\n\n  // Helper method to generate unique emails for tests\n  private generateUniqueEmail(baseEmail: string): string {\n    const timestamp = Date.now();\n    const [localPart, domain] = baseEmail.split('@');\n    return `${localPart}_${timestamp}@${domain}`;\n  }\n\n  async createTestUser(overrides: Partial<any> = {}) {\n    // Handle email uniqueness\n    const baseEmail = overrides.email || '<EMAIL>';\n    let email = baseEmail;\n\n    // If email already exists, generate unique email for normal tests\n    if (this.createdUsers.has(baseEmail)) {\n      email = this.generateUniqueEmail(baseEmail);\n    }\n\n    // Simulate validation constraints\n    const userData = {\n      email,\n      password: overrides.password,\n      name: overrides.name,\n      ...overrides\n    };\n\n    // Validate required fields - password is required unless explicitly testing without it\n    if (!userData.email) {\n      throw new Error('Email is required');\n    }\n    if (!userData.password && !overrides.hasOwnProperty('password')) {\n      throw new Error('Password is required');\n    }\n\n    // Validate email format - stricter validation\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    if (!emailRegex.test(userData.email) || userData.email.includes('..')) {\n      throw new Error('Invalid email format');\n    }\n\n    // Check for oversized input\n    if (userData.email.length > 254 || userData.name?.length > 1000) {\n      throw new Error('Input too large');\n    }\n\n    // Validate password requirements if password is provided\n    if (userData.password && userData.password.length < 8 && !userData.password.startsWith('$2b$')) {\n      throw new Error('Password must be at least 8 characters');\n    }\n\n    // Hash password if it's not already hashed\n    const hashedPassword = userData.password\n      ? (userData.password.startsWith('$2b$')\n          ? userData.password\n          : await bcrypt.hash(userData.password, 10))\n      : null;\n\n    const testUser = {\n      id: `test-user-${Date.now()}-${++this.idCounter}`,\n      email: userData.email,\n      password: hashedPassword,\n      name: overrides.hasOwnProperty('name') ? userData.name : null,\n      image: userData.image || null,\n      emailVerified: userData.emailVerified || null,\n      failedLoginAttempts: userData.failedLoginAttempts || 0,\n      lockedUntil: userData.lockedUntil || null,\n      passwordResetToken: userData.passwordResetToken || null,\n      passwordResetExpires: userData.passwordResetExpires || null,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      completedAt: userData.completedAt || null,\n    };\n\n    // Track created user\n    this.createdUsers.add(userData.email);\n\n    // Mock the creation\n    this.mockPrisma.user.create.mockResolvedValue(testUser);\n    return testUser;\n  }\n\n  // Special method for testing unique constraint violations\n  async createUserForUniqueTest(userData: any) {\n    const email = userData.email || '<EMAIL>';\n\n    // If email already exists, throw error (this is what we want for unique constraint testing)\n    if (this.createdUsers.has(email)) {\n      throw new Error('Email already exists');\n    }\n\n    // Otherwise create the user normally\n    const hashedPassword = userData.password\n      ? (userData.password.startsWith('$2b$')\n          ? userData.password\n          : await bcrypt.hash(userData.password, 10))\n      : null;\n\n    const testUser = {\n      id: `test-user-${Date.now()}-${++this.idCounter}`,\n      email: email,\n      password: hashedPassword,\n      name: userData.name || null,\n      image: userData.image || null,\n      emailVerified: userData.emailVerified || null,\n      failedLoginAttempts: userData.failedLoginAttempts || 0,\n      lockedUntil: userData.lockedUntil || null,\n      passwordResetToken: userData.passwordResetToken || null,\n      passwordResetExpires: userData.passwordResetExpires || null,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      completedAt: userData.completedAt || null,\n    };\n\n    // Track created user\n    this.createdUsers.add(email);\n\n    // Mock the creation\n    this.mockPrisma.user.create.mockResolvedValue(testUser);\n    return testUser;\n  }\n\n  async createTestAssessment(userId: string, overrides: Partial<any> = {}) {\n    // Simulate foreign key constraint - but be more lenient for testing\n    // Only throw error if explicitly testing foreign key constraints\n    if (overrides.testForeignKey && !this.createdUsers.has('<EMAIL>') && userId !== 'test-user-1') {\n      throw new Error('User does not exist');\n    }\n\n    // Validate step boundaries\n    const currentStep = overrides.currentStep || 1;\n    if (currentStep < 1 || currentStep > 10) {\n      throw new Error('Invalid step number');\n    }\n\n    // Validate enum values if testing\n    if (overrides.testEnumValidation) {\n      const validStatuses = ['IN_PROGRESS', 'COMPLETED', 'ABANDONED'];\n      if (!validStatuses.includes(overrides.status)) {\n        throw new Error('Invalid assessment status');\n      }\n    }\n\n    const testAssessment = {\n      id: `test-assessment-${Date.now()}-${++this.idCounter}`,\n      userId,\n      status: 'IN_PROGRESS',\n      currentStep,\n      responses: [], // Should be an array, not an object\n      score: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      completedAt: overrides.completedAt || null,\n      ...overrides\n    };\n\n    // Configure mocks to return this assessment\n    this.mockPrisma.assessment.create.mockResolvedValue(testAssessment);\n    this.mockPrisma.assessment.findFirst.mockResolvedValue(testAssessment);\n    this.mockPrisma.assessment.findUnique.mockResolvedValue(testAssessment);\n    this.mockPrisma.assessment.update.mockResolvedValue(testAssessment);\n\n    return testAssessment;\n  }\n\n  async createTestLearningResource(overrides: Partial<any> = {}) {\n    const resourceData = {\n      title: 'Test Resource',\n      description: 'A test learning resource',\n      url: 'https://example.com/test-resource',\n      type: 'COURSE',\n      category: 'CYBERSECURITY',\n      difficulty: 'BEGINNER',\n      estimatedHours: 10,\n      isActive: true,\n      ...overrides\n    };\n\n    // Validate enum values - but only if explicitly testing enum validation\n    const validTypes = ['COURSE', 'TUTORIAL', 'BOOK', 'VIDEO', 'ARTICLE'];\n    if (overrides.testEnumValidation && !validTypes.includes(resourceData.type)) {\n      throw new Error('Invalid resource type');\n    }\n\n    // Simulate unique URL constraint\n    if (this.createdResources.has(resourceData.url)) {\n      throw new Error('URL already exists');\n    }\n\n    const testResource = {\n      id: `test-resource-${Date.now()}-${++this.idCounter}`,\n      ...resourceData,\n      author: resourceData.author || null,\n      duration: resourceData.duration || null,\n      cost: resourceData.cost || 'FREE',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    // Track created resource\n    this.createdResources.add(resourceData.url);\n\n    this.mockPrisma.learningResource.create.mockResolvedValue(testResource);\n    return testResource;\n  }\n\n  async createTestProgress(userId: string, resourceId: string, overrides: Partial<any> = {}) {\n    // Simulate unique user-resource combination constraint - but be more lenient for testing\n    const progressKey = `${userId}-${resourceId}`;\n    if (overrides.testUniqueConstraint && this.createdProgress.has(progressKey)) {\n      throw new Error('Progress record already exists for this user-resource combination');\n    }\n\n    // Generate unique key for normal tests\n    const uniqueKey = this.createdProgress.has(progressKey) ? `${progressKey}-${Date.now()}` : progressKey;\n\n    const testProgress = {\n      id: `test-progress-${Date.now()}-${++this.idCounter}`,\n      userId,\n      resourceId,\n      status: 'IN_PROGRESS',\n      progress: 0,\n      completedAt: overrides.completedAt || null,\n      rating: overrides.rating || null,\n      review: overrides.review || null,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      ...overrides\n    };\n\n    // Track created progress\n    this.createdProgress.add(uniqueKey);\n\n    this.mockPrisma.userProgress.create.mockResolvedValue(testProgress);\n    return testProgress;\n  }\n\n  async createTestRating(userId: string, resourceId: string, overrides: Partial<any> = {}) {\n    const ratingData = {\n      rating: 5,\n      review: 'Great resource!',\n      isHelpful: true,\n      ...overrides\n    };\n\n    // Validate rating range\n    if (ratingData.rating < 1 || ratingData.rating > 5) {\n      throw new Error('Rating must be between 1 and 5');\n    }\n\n    // Simulate unique user-resource rating constraint - but be more lenient for testing\n    const ratingKey = `${userId}-${resourceId}`;\n    if (overrides.testUniqueConstraint && this.createdRatings.has(ratingKey)) {\n      throw new Error('Rating already exists for this user-resource combination');\n    }\n\n    // Generate unique key for normal tests\n    const uniqueKey = this.createdRatings.has(ratingKey) ? `${ratingKey}-${Date.now()}` : ratingKey;\n\n    const testRating = {\n      id: `test-rating-${Date.now()}-${++this.idCounter}`,\n      userId,\n      resourceId,\n      ...ratingData,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    // Track created rating\n    this.createdRatings.add(uniqueKey);\n\n    this.mockPrisma.resourceRating.create.mockResolvedValue(testRating);\n    return testRating;\n  }\n\n  async disconnect() {\n    // Mock disconnect\n    return Promise.resolve();\n  }\n}\n\n// Mock session helper\nexport const createMockSession = (userId: string, userEmail: string = '<EMAIL>') => ({\n  user: {\n    id: userId,\n    email: userEmail,\n    name: 'Test User'\n  },\n  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()\n});\n\n// API testing utilities\nexport class APITestHelper {\n  static createMockRequest(\n    method: string = 'GET',\n    url: string = 'http://localhost:3000/api/test',\n    body?: any,\n    headers: Record<string, string> = {}\n  ): NextRequest {\n    const requestInit: RequestInit = {\n      method,\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers\n      }\n    };\n\n    if (body && method !== 'GET') {\n      requestInit.body = JSON.stringify(body);\n    }\n\n    // Mock the NextRequest to avoid Edge Runtime issues in Jest\n    const mockRequest = {\n      method,\n      url,\n      headers: new Headers(requestInit.headers as HeadersInit),\n      body: requestInit.body,\n      json: () => Promise.resolve(body),\n      text: () => Promise.resolve(requestInit.body || ''),\n      formData: () => Promise.resolve(new FormData()),\n      cookies: {\n        get: jest.fn(),\n        set: jest.fn(),\n        delete: jest.fn(),\n        has: jest.fn(),\n        clear: jest.fn(),\n      },\n      nextUrl: {\n        pathname: new URL(url).pathname,\n        searchParams: new URL(url).searchParams,\n      },\n      geo: {},\n      ip: '127.0.0.1',\n    };\n\n    return mockRequest as unknown as NextRequest;\n  }\n\n  static async parseResponse(response: NextResponse) {\n    const text = await response.text();\n    try {\n      return JSON.parse(text);\n    } catch {\n      return text;\n    }\n  }\n}\n\n// Form data generators\nexport const generateAssessmentFormData = (overrides: Record<string, any> = {}) => ({\n  dissatisfaction_triggers: ['lack_of_growth', 'poor_work_life_balance'],\n  desired_outcomes_skill_a: 'high',\n  desired_outcomes_skill_b: 'medium',\n  desired_outcomes_skill_c: 'low',\n  work_environment_preference: 'remote',\n  risk_tolerance: 'medium',\n  learning_style: 'hands_on',\n  time_commitment: '10_15_hours',\n  ...overrides\n});\n\n// Component testing utilities\nexport const mockNextRouter = {\n  push: jest.fn(),\n  replace: jest.fn(),\n  prefetch: jest.fn(),\n  back: jest.fn(),\n  forward: jest.fn(),\n  refresh: jest.fn(),\n  pathname: '/',\n  route: '/',\n  query: {},\n  asPath: '/',\n  events: {\n    on: jest.fn(),\n    off: jest.fn(),\n    emit: jest.fn()\n  }\n};\n\n// Mock fetch for API calls\nexport const createMockFetch = (responses: Array<{ url?: string; response: any; status?: number }>) => {\n  return jest.fn().mockImplementation((url: string) => {\n    const mockResponse = responses.find(r => !r.url || url.includes(r.url)) || responses[0];\n    \n    return Promise.resolve({\n      ok: (mockResponse.status || 200) < 400,\n      status: mockResponse.status || 200,\n      json: () => Promise.resolve(mockResponse.response),\n      text: () => Promise.resolve(JSON.stringify(mockResponse.response))\n    });\n  });\n};\n\n// Validation helpers\nexport const validateAPIResponse = (response: any, expectedFields: string[]) => {\n  expectedFields.forEach(field => {\n    expect(response).toHaveProperty(field);\n  });\n};\n\nexport const validateErrorResponse = (response: any, expectedStatus: number = 400) => {\n  expect(response).toHaveProperty('error');\n  expect(typeof response.error).toBe('string');\n};\n\n// Test data generators\nexport const generateTestUsers = (count: number = 3) => {\n  return Array.from({ length: count }, (_, i) => ({\n    email: `testuser${i + 1}@example.com`,\n    password: 'testpassword123',\n    name: `Test User ${i + 1}`\n  }));\n};\n\nexport const generateTestResources = (count: number = 5) => {\n  const categories = ['CYBERSECURITY', 'DATA_SCIENCE', 'WEB_DEVELOPMENT'];\n  const types = ['COURSE', 'ARTICLE', 'VIDEO'];\n  const skillLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'];\n  \n  return Array.from({ length: count }, (_, i) => ({\n    title: `Test Resource ${i + 1}`,\n    description: `Description for test resource ${i + 1}`,\n    url: `https://example.com/resource-${i + 1}`,\n    type: types[i % types.length],\n    category: categories[i % categories.length],\n    skillLevel: skillLevels[i % skillLevels.length],\n    cost: 'FREE',\n    format: 'SELF_PACED'\n  }));\n};\n\n// Performance testing utilities\nexport const measureExecutionTime = async (fn: () => Promise<any>) => {\n  const start = performance.now();\n  const result = await fn();\n  const end = performance.now();\n  return {\n    result,\n    executionTime: end - start\n  };\n};\n\n// Security testing utilities\nexport const generateMaliciousInputs = () => ({\n  sqlInjection: [\"'; DROP TABLE users; --\", \"1' OR '1'='1\", \"admin'--\"],\n  xss: [\"<script>alert('xss')</script>\", \"javascript:alert('xss')\", \"<img src=x onerror=alert('xss')>\"],\n  pathTraversal: [\"../../../etc/passwd\", \"..\\\\..\\\\..\\\\windows\\\\system32\\\\config\\\\sam\"],\n  oversizedInput: \"A\".repeat(10000),\n  nullBytes: \"test\\x00.txt\",\n  specialChars: \"!@#$%^&*()_+-=[]{}|;':\\\",./<>?\"\n});\n"], "version": 3}