6f0c6387a948b2a2703d2bbd3db15229
"use strict";

/**
 * Centralized Error Tracking Service
 * Handles error reporting to external services like Sentry
 */
/* istanbul ignore next */
function cov_16r7n2ic7d() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/errorTracking.ts";
  var hash = "a19be9fab336cd89be8ede9fbe4e3d3e1c8f780b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/errorTracking.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "2": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "4": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "5": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "6": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "8": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "9": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "11": {
        start: {
          line: 17,
          column: 22
        },
        end: {
          line: 27,
          column: 3
        }
      },
      "12": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 33
        }
      },
      "13": {
        start: {
          line: 18,
          column: 26
        },
        end: {
          line: 18,
          column: 33
        }
      },
      "14": {
        start: {
          line: 19,
          column: 15
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "15": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 22,
          column: 5
        }
      },
      "16": {
        start: {
          line: 21,
          column: 6
        },
        end: {
          line: 21,
          column: 68
        }
      },
      "17": {
        start: {
          line: 21,
          column: 51
        },
        end: {
          line: 21,
          column: 63
        }
      },
      "18": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 39
        }
      },
      "19": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 33
        }
      },
      "20": {
        start: {
          line: 25,
          column: 26
        },
        end: {
          line: 25,
          column: 33
        }
      },
      "21": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 17
        }
      },
      "22": {
        start: {
          line: 28,
          column: 25
        },
        end: {
          line: 32,
          column: 2
        }
      },
      "23": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 72
        }
      },
      "24": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 21
        }
      },
      "25": {
        start: {
          line: 33,
          column: 19
        },
        end: {
          line: 49,
          column: 4
        }
      },
      "26": {
        start: {
          line: 34,
          column: 18
        },
        end: {
          line: 41,
          column: 5
        }
      },
      "27": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 39,
          column: 10
        }
      },
      "28": {
        start: {
          line: 36,
          column: 21
        },
        end: {
          line: 36,
          column: 23
        }
      },
      "29": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 95
        }
      },
      "30": {
        start: {
          line: 37,
          column: 29
        },
        end: {
          line: 37,
          column: 95
        }
      },
      "31": {
        start: {
          line: 37,
          column: 77
        },
        end: {
          line: 37,
          column: 95
        }
      },
      "32": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 22
        }
      },
      "33": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 40,
          column: 26
        }
      },
      "34": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 48,
          column: 6
        }
      },
      "35": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 46
        }
      },
      "36": {
        start: {
          line: 43,
          column: 35
        },
        end: {
          line: 43,
          column: 46
        }
      },
      "37": {
        start: {
          line: 44,
          column: 21
        },
        end: {
          line: 44,
          column: 23
        }
      },
      "38": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 137
        }
      },
      "39": {
        start: {
          line: 45,
          column: 25
        },
        end: {
          line: 45,
          column: 137
        }
      },
      "40": {
        start: {
          line: 45,
          column: 38
        },
        end: {
          line: 45,
          column: 50
        }
      },
      "41": {
        start: {
          line: 45,
          column: 56
        },
        end: {
          line: 45,
          column: 57
        }
      },
      "42": {
        start: {
          line: 45,
          column: 78
        },
        end: {
          line: 45,
          column: 137
        }
      },
      "43": {
        start: {
          line: 45,
          column: 102
        },
        end: {
          line: 45,
          column: 137
        }
      },
      "44": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 40
        }
      },
      "45": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 47,
          column: 22
        }
      },
      "46": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "47": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 51
        }
      },
      "48": {
        start: {
          line: 52,
          column: 13
        },
        end: {
          line: 52,
          column: 52
        }
      },
      "49": {
        start: {
          line: 53,
          column: 34
        },
        end: {
          line: 169,
          column: 3
        }
      },
      "50": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 66
        }
      },
      "51": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 56,
          column: 54
        }
      },
      "52": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 68
        }
      },
      "53": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 76,
          column: 6
        }
      },
      "54": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 75,
          column: 9
        }
      },
      "55": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 70,
          column: 15
        }
      },
      "56": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 74,
          column: 55
        }
      },
      "57": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 94,
          column: 6
        }
      },
      "58": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 93,
          column: 9
        }
      },
      "59": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 88,
          column: 15
        }
      },
      "60": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 46
        }
      },
      "61": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 102,
          column: 6
        }
      },
      "62": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "63": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 33
        }
      },
      "64": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 118,
          column: 6
        }
      },
      "65": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 58
        }
      },
      "66": {
        start: {
          line: 107,
          column: 35
        },
        end: {
          line: 107,
          column: 56
        }
      },
      "67": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "68": {
        start: {
          line: 109,
          column: 12
        },
        end: {
          line: 113,
          column: 15
        }
      },
      "69": {
        start: {
          line: 115,
          column: 13
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "70": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 116,
          column: 82
        }
      },
      "71": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 147,
          column: 6
        }
      },
      "72": {
        start: {
          line: 123,
          column: 22
        },
        end: {
          line: 134,
          column: 9
        }
      },
      "73": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "74": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 137,
          column: 51
        }
      },
      "75": {
        start: {
          line: 141,
          column: 12
        },
        end: {
          line: 141,
          column: 84
        }
      },
      "76": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 43
        }
      },
      "77": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 143,
          column: 45
        }
      },
      "78": {
        start: {
          line: 144,
          column: 12
        },
        end: {
          line: 144,
          column: 50
        }
      },
      "79": {
        start: {
          line: 145,
          column: 12
        },
        end: {
          line: 145,
          column: 31
        }
      },
      "80": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 167,
          column: 6
        }
      },
      "81": {
        start: {
          line: 152,
          column: 22
        },
        end: {
          line: 157,
          column: 9
        }
      },
      "82": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 166,
          column: 9
        }
      },
      "83": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 159,
          column: 51
        }
      },
      "84": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 162,
          column: 68
        }
      },
      "85": {
        start: {
          line: 163,
          column: 12
        },
        end: {
          line: 163,
          column: 45
        }
      },
      "86": {
        start: {
          line: 164,
          column: 12
        },
        end: {
          line: 164,
          column: 50
        }
      },
      "87": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 165,
          column: 31
        }
      },
      "88": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 168,
          column: 24
        }
      },
      "89": {
        start: {
          line: 171,
          column: 19
        },
        end: {
          line: 171,
          column: 37
        }
      },
      "90": {
        start: {
          line: 172,
          column: 0
        },
        end: {
          line: 172,
          column: 36
        }
      },
      "91": {
        start: {
          line: 174,
          column: 0
        },
        end: {
          line: 272,
          column: 2
        }
      },
      "92": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 191,
          column: 11
        }
      },
      "93": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 205,
          column: 11
        }
      },
      "94": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 221,
          column: 11
        }
      },
      "95": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 236,
          column: 11
        }
      },
      "96": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 252,
          column: 11
        }
      },
      "97": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 270,
          column: 11
        }
      },
      "98": {
        start: {
          line: 273,
          column: 0
        },
        end: {
          line: 273,
          column: 31
        }
      },
      "99": {
        start: {
          line: 275,
          column: 0
        },
        end: {
          line: 302,
          column: 1
        }
      },
      "100": {
        start: {
          line: 277,
          column: 4
        },
        end: {
          line: 288,
          column: 7
        }
      },
      "101": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 287,
          column: 11
        }
      },
      "102": {
        start: {
          line: 290,
          column: 4
        },
        end: {
          line: 301,
          column: 7
        }
      },
      "103": {
        start: {
          line: 291,
          column: 8
        },
        end: {
          line: 300,
          column: 11
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 75
          }
        },
        loc: {
          start: {
            line: 17,
            column: 96
          },
          end: {
            line: 24,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 39
          }
        },
        loc: {
          start: {
            line: 21,
            column: 49
          },
          end: {
            line: 21,
            column: 65
          }
        },
        line: 21
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 24,
            column: 6
          },
          end: {
            line: 24,
            column: 7
          }
        },
        loc: {
          start: {
            line: 24,
            column: 28
          },
          end: {
            line: 27,
            column: 1
          }
        },
        line: 24
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 28,
            column: 80
          },
          end: {
            line: 28,
            column: 81
          }
        },
        loc: {
          start: {
            line: 28,
            column: 95
          },
          end: {
            line: 30,
            column: 1
          }
        },
        line: 28
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 30,
            column: 5
          },
          end: {
            line: 30,
            column: 6
          }
        },
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 30
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 33,
            column: 51
          },
          end: {
            line: 33,
            column: 52
          }
        },
        loc: {
          start: {
            line: 33,
            column: 63
          },
          end: {
            line: 49,
            column: 1
          }
        },
        line: 33
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 34,
            column: 18
          },
          end: {
            line: 34,
            column: 19
          }
        },
        loc: {
          start: {
            line: 34,
            column: 30
          },
          end: {
            line: 41,
            column: 5
          }
        },
        line: 34
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 35,
            column: 48
          },
          end: {
            line: 35,
            column: 49
          }
        },
        loc: {
          start: {
            line: 35,
            column: 61
          },
          end: {
            line: 39,
            column: 9
          }
        },
        line: 35
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 42,
            column: 11
          },
          end: {
            line: 42,
            column: 12
          }
        },
        loc: {
          start: {
            line: 42,
            column: 26
          },
          end: {
            line: 48,
            column: 5
          }
        },
        line: 42
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 53,
            column: 34
          },
          end: {
            line: 53,
            column: 35
          }
        },
        loc: {
          start: {
            line: 53,
            column: 46
          },
          end: {
            line: 169,
            column: 1
          }
        },
        line: 53
      },
      "12": {
        name: "ErrorTracker",
        decl: {
          start: {
            line: 54,
            column: 13
          },
          end: {
            line: 54,
            column: 25
          }
        },
        loc: {
          start: {
            line: 54,
            column: 28
          },
          end: {
            line: 58,
            column: 5
          }
        },
        line: 54
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 62,
            column: 46
          },
          end: {
            line: 62,
            column: 47
          }
        },
        loc: {
          start: {
            line: 62,
            column: 72
          },
          end: {
            line: 76,
            column: 5
          }
        },
        line: 62
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 80,
            column: 44
          },
          end: {
            line: 80,
            column: 45
          }
        },
        loc: {
          start: {
            line: 80,
            column: 72
          },
          end: {
            line: 94,
            column: 5
          }
        },
        line: 80
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 98,
            column: 37
          },
          end: {
            line: 98,
            column: 38
          }
        },
        loc: {
          start: {
            line: 98,
            column: 53
          },
          end: {
            line: 102,
            column: 5
          }
        },
        line: 98
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 106,
            column: 43
          },
          end: {
            line: 106,
            column: 44
          }
        },
        loc: {
          start: {
            line: 106,
            column: 72
          },
          end: {
            line: 118,
            column: 5
          }
        },
        line: 106
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 122,
            column: 38
          },
          end: {
            line: 122,
            column: 39
          }
        },
        loc: {
          start: {
            line: 122,
            column: 70
          },
          end: {
            line: 147,
            column: 5
          }
        },
        line: 122
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 151,
            column: 40
          },
          end: {
            line: 151,
            column: 41
          }
        },
        loc: {
          start: {
            line: 151,
            column: 68
          },
          end: {
            line: 167,
            column: 5
          }
        },
        line: 151
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 178,
            column: 9
          },
          end: {
            line: 178,
            column: 10
          }
        },
        loc: {
          start: {
            line: 178,
            column: 56
          },
          end: {
            line: 192,
            column: 5
          }
        },
        line: 178
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 196,
            column: 10
          },
          end: {
            line: 196,
            column: 11
          }
        },
        loc: {
          start: {
            line: 196,
            column: 35
          },
          end: {
            line: 206,
            column: 5
          }
        },
        line: 196
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 210,
            column: 14
          },
          end: {
            line: 210,
            column: 15
          }
        },
        loc: {
          start: {
            line: 210,
            column: 49
          },
          end: {
            line: 222,
            column: 5
          }
        },
        line: 210
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 226,
            column: 16
          },
          end: {
            line: 226,
            column: 17
          }
        },
        loc: {
          start: {
            line: 226,
            column: 47
          },
          end: {
            line: 237,
            column: 5
          }
        },
        line: 226
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        },
        loc: {
          start: {
            line: 241,
            column: 44
          },
          end: {
            line: 253,
            column: 5
          }
        },
        line: 241
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 257,
            column: 17
          },
          end: {
            line: 257,
            column: 18
          }
        },
        loc: {
          start: {
            line: 257,
            column: 62
          },
          end: {
            line: 271,
            column: 5
          }
        },
        line: 257
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 277,
            column: 50
          },
          end: {
            line: 277,
            column: 51
          }
        },
        loc: {
          start: {
            line: 277,
            column: 67
          },
          end: {
            line: 288,
            column: 5
          }
        },
        line: 277
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 290,
            column: 37
          },
          end: {
            line: 290,
            column: 38
          }
        },
        loc: {
          start: {
            line: 290,
            column: 54
          },
          end: {
            line: 301,
            column: 5
          }
        },
        line: 290
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 27,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 23
          },
          end: {
            line: 17,
            column: 27
          }
        }, {
          start: {
            line: 17,
            column: 31
          },
          end: {
            line: 17,
            column: 51
          }
        }, {
          start: {
            line: 17,
            column: 57
          },
          end: {
            line: 27,
            column: 2
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 17,
            column: 57
          },
          end: {
            line: 27,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 24,
            column: 1
          }
        }, {
          start: {
            line: 24,
            column: 6
          },
          end: {
            line: 27,
            column: 1
          }
        }],
        line: 17
      },
      "5": {
        loc: {
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 18,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 18,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "6": {
        loc: {
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "7": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 13
          }
        }, {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 84
          }
        }],
        line: 20
      },
      "8": {
        loc: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 20,
            column: 34
          },
          end: {
            line: 20,
            column: 47
          }
        }, {
          start: {
            line: 20,
            column: 50
          },
          end: {
            line: 20,
            column: 84
          }
        }],
        line: 20
      },
      "9": {
        loc: {
          start: {
            line: 20,
            column: 50
          },
          end: {
            line: 20,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 50
          },
          end: {
            line: 20,
            column: 63
          }
        }, {
          start: {
            line: 20,
            column: 67
          },
          end: {
            line: 20,
            column: 84
          }
        }],
        line: 20
      },
      "10": {
        loc: {
          start: {
            line: 25,
            column: 4
          },
          end: {
            line: 25,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 25,
            column: 4
          },
          end: {
            line: 25,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 25
      },
      "11": {
        loc: {
          start: {
            line: 28,
            column: 25
          },
          end: {
            line: 32,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 26
          },
          end: {
            line: 28,
            column: 30
          }
        }, {
          start: {
            line: 28,
            column: 34
          },
          end: {
            line: 28,
            column: 57
          }
        }, {
          start: {
            line: 28,
            column: 63
          },
          end: {
            line: 32,
            column: 1
          }
        }],
        line: 28
      },
      "12": {
        loc: {
          start: {
            line: 28,
            column: 63
          },
          end: {
            line: 32,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 28,
            column: 80
          },
          end: {
            line: 30,
            column: 1
          }
        }, {
          start: {
            line: 30,
            column: 5
          },
          end: {
            line: 32,
            column: 1
          }
        }],
        line: 28
      },
      "13": {
        loc: {
          start: {
            line: 33,
            column: 19
          },
          end: {
            line: 49,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 24
          }
        }, {
          start: {
            line: 33,
            column: 28
          },
          end: {
            line: 33,
            column: 45
          }
        }, {
          start: {
            line: 33,
            column: 50
          },
          end: {
            line: 49,
            column: 4
          }
        }],
        line: 33
      },
      "14": {
        loc: {
          start: {
            line: 35,
            column: 18
          },
          end: {
            line: 39,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 18
          },
          end: {
            line: 35,
            column: 44
          }
        }, {
          start: {
            line: 35,
            column: 48
          },
          end: {
            line: 39,
            column: 9
          }
        }],
        line: 35
      },
      "15": {
        loc: {
          start: {
            line: 37,
            column: 29
          },
          end: {
            line: 37,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 29
          },
          end: {
            line: 37,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "16": {
        loc: {
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "17": {
        loc: {
          start: {
            line: 43,
            column: 12
          },
          end: {
            line: 43,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 12
          },
          end: {
            line: 43,
            column: 15
          }
        }, {
          start: {
            line: 43,
            column: 19
          },
          end: {
            line: 43,
            column: 33
          }
        }],
        line: 43
      },
      "18": {
        loc: {
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 45,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 45,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "19": {
        loc: {
          start: {
            line: 45,
            column: 78
          },
          end: {
            line: 45,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 78
          },
          end: {
            line: 45,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "20": {
        loc: {
          start: {
            line: 63,
            column: 8
          },
          end: {
            line: 75,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 8
          },
          end: {
            line: 75,
            column: 9
          }
        }, {
          start: {
            line: 72,
            column: 13
          },
          end: {
            line: 75,
            column: 9
          }
        }],
        line: 63
      },
      "21": {
        loc: {
          start: {
            line: 66,
            column: 23
          },
          end: {
            line: 66,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 66,
            column: 64
          },
          end: {
            line: 66,
            column: 70
          }
        }, {
          start: {
            line: 66,
            column: 73
          },
          end: {
            line: 66,
            column: 86
          }
        }],
        line: 66
      },
      "22": {
        loc: {
          start: {
            line: 66,
            column: 23
          },
          end: {
            line: 66,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 66,
            column: 23
          },
          end: {
            line: 66,
            column: 39
          }
        }, {
          start: {
            line: 66,
            column: 43
          },
          end: {
            line: 66,
            column: 61
          }
        }],
        line: 66
      },
      "23": {
        loc: {
          start: {
            line: 67,
            column: 44
          },
          end: {
            line: 67,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 85
          },
          end: {
            line: 67,
            column: 91
          }
        }, {
          start: {
            line: 67,
            column: 94
          },
          end: {
            line: 67,
            column: 106
          }
        }],
        line: 67
      },
      "24": {
        loc: {
          start: {
            line: 67,
            column: 44
          },
          end: {
            line: 67,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 44
          },
          end: {
            line: 67,
            column: 60
          }
        }, {
          start: {
            line: 67,
            column: 64
          },
          end: {
            line: 67,
            column: 82
          }
        }],
        line: 67
      },
      "25": {
        loc: {
          start: {
            line: 68,
            column: 22
          },
          end: {
            line: 68,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 68,
            column: 63
          },
          end: {
            line: 68,
            column: 69
          }
        }, {
          start: {
            line: 68,
            column: 72
          },
          end: {
            line: 68,
            column: 84
          }
        }],
        line: 68
      },
      "26": {
        loc: {
          start: {
            line: 68,
            column: 22
          },
          end: {
            line: 68,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 22
          },
          end: {
            line: 68,
            column: 38
          }
        }, {
          start: {
            line: 68,
            column: 42
          },
          end: {
            line: 68,
            column: 60
          }
        }],
        line: 68
      },
      "27": {
        loc: {
          start: {
            line: 69,
            column: 23
          },
          end: {
            line: 69,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 69,
            column: 87
          }
        }, {
          start: {
            line: 69,
            column: 92
          },
          end: {
            line: 69,
            column: 99
          }
        }],
        line: 69
      },
      "28": {
        loc: {
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 69,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 65
          },
          end: {
            line: 69,
            column: 71
          }
        }, {
          start: {
            line: 69,
            column: 74
          },
          end: {
            line: 69,
            column: 87
          }
        }],
        line: 69
      },
      "29": {
        loc: {
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 69,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 69,
            column: 40
          }
        }, {
          start: {
            line: 69,
            column: 44
          },
          end: {
            line: 69,
            column: 62
          }
        }],
        line: 69
      },
      "30": {
        loc: {
          start: {
            line: 81,
            column: 8
          },
          end: {
            line: 93,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 81,
            column: 8
          },
          end: {
            line: 93,
            column: 9
          }
        }, {
          start: {
            line: 90,
            column: 13
          },
          end: {
            line: 93,
            column: 9
          }
        }],
        line: 81
      },
      "31": {
        loc: {
          start: {
            line: 84,
            column: 23
          },
          end: {
            line: 84,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 84,
            column: 64
          },
          end: {
            line: 84,
            column: 70
          }
        }, {
          start: {
            line: 84,
            column: 73
          },
          end: {
            line: 84,
            column: 86
          }
        }],
        line: 84
      },
      "32": {
        loc: {
          start: {
            line: 84,
            column: 23
          },
          end: {
            line: 84,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 23
          },
          end: {
            line: 84,
            column: 39
          }
        }, {
          start: {
            line: 84,
            column: 43
          },
          end: {
            line: 84,
            column: 61
          }
        }],
        line: 84
      },
      "33": {
        loc: {
          start: {
            line: 85,
            column: 44
          },
          end: {
            line: 85,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 85
          },
          end: {
            line: 85,
            column: 91
          }
        }, {
          start: {
            line: 85,
            column: 94
          },
          end: {
            line: 85,
            column: 106
          }
        }],
        line: 85
      },
      "34": {
        loc: {
          start: {
            line: 85,
            column: 44
          },
          end: {
            line: 85,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 44
          },
          end: {
            line: 85,
            column: 60
          }
        }, {
          start: {
            line: 85,
            column: 64
          },
          end: {
            line: 85,
            column: 82
          }
        }],
        line: 85
      },
      "35": {
        loc: {
          start: {
            line: 86,
            column: 22
          },
          end: {
            line: 86,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 86,
            column: 63
          },
          end: {
            line: 86,
            column: 69
          }
        }, {
          start: {
            line: 86,
            column: 72
          },
          end: {
            line: 86,
            column: 84
          }
        }],
        line: 86
      },
      "36": {
        loc: {
          start: {
            line: 86,
            column: 22
          },
          end: {
            line: 86,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 22
          },
          end: {
            line: 86,
            column: 38
          }
        }, {
          start: {
            line: 86,
            column: 42
          },
          end: {
            line: 86,
            column: 60
          }
        }],
        line: 86
      },
      "37": {
        loc: {
          start: {
            line: 87,
            column: 23
          },
          end: {
            line: 87,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 24
          },
          end: {
            line: 87,
            column: 87
          }
        }, {
          start: {
            line: 87,
            column: 92
          },
          end: {
            line: 87,
            column: 99
          }
        }],
        line: 87
      },
      "38": {
        loc: {
          start: {
            line: 87,
            column: 24
          },
          end: {
            line: 87,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 87,
            column: 65
          },
          end: {
            line: 87,
            column: 71
          }
        }, {
          start: {
            line: 87,
            column: 74
          },
          end: {
            line: 87,
            column: 87
          }
        }],
        line: 87
      },
      "39": {
        loc: {
          start: {
            line: 87,
            column: 24
          },
          end: {
            line: 87,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 24
          },
          end: {
            line: 87,
            column: 40
          }
        }, {
          start: {
            line: 87,
            column: 44
          },
          end: {
            line: 87,
            column: 62
          }
        }],
        line: 87
      },
      "40": {
        loc: {
          start: {
            line: 99,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 99,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 99
      },
      "41": {
        loc: {
          start: {
            line: 107,
            column: 8
          },
          end: {
            line: 107,
            column: 58
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 8
          },
          end: {
            line: 107,
            column: 58
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "42": {
        loc: {
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        }, {
          start: {
            line: 115,
            column: 13
          },
          end: {
            line: 117,
            column: 9
          }
        }],
        line: 108
      },
      "43": {
        loc: {
          start: {
            line: 115,
            column: 13
          },
          end: {
            line: 117,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 13
          },
          end: {
            line: 117,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "44": {
        loc: {
          start: {
            line: 132,
            column: 17
          },
          end: {
            line: 132,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 132,
            column: 33
          },
          end: {
            line: 132,
            column: 53
          }
        }, {
          start: {
            line: 132,
            column: 56
          },
          end: {
            line: 132,
            column: 64
          }
        }],
        line: 132
      },
      "45": {
        loc: {
          start: {
            line: 133,
            column: 23
          },
          end: {
            line: 133,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 133,
            column: 39
          },
          end: {
            line: 133,
            column: 58
          }
        }, {
          start: {
            line: 133,
            column: 61
          },
          end: {
            line: 133,
            column: 69
          }
        }],
        line: 133
      },
      "46": {
        loc: {
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 146,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 146,
            column: 9
          }
        }, {
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        }],
        line: 135
      },
      "47": {
        loc: {
          start: {
            line: 156,
            column: 17
          },
          end: {
            line: 156,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 156,
            column: 33
          },
          end: {
            line: 156,
            column: 53
          }
        }, {
          start: {
            line: 156,
            column: 56
          },
          end: {
            line: 156,
            column: 64
          }
        }],
        line: 156
      },
      "48": {
        loc: {
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 166,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 166,
            column: 9
          }
        }, {
          start: {
            line: 161,
            column: 13
          },
          end: {
            line: 166,
            column: 9
          }
        }],
        line: 158
      },
      "49": {
        loc: {
          start: {
            line: 184,
            column: 28
          },
          end: {
            line: 184,
            column: 120
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 29
          },
          end: {
            line: 184,
            column: 106
          }
        }, {
          start: {
            line: 184,
            column: 111
          },
          end: {
            line: 184,
            column: 120
          }
        }],
        line: 184
      },
      "50": {
        loc: {
          start: {
            line: 184,
            column: 29
          },
          end: {
            line: 184,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 76
          },
          end: {
            line: 184,
            column: 82
          }
        }, {
          start: {
            line: 184,
            column: 85
          },
          end: {
            line: 184,
            column: 106
          }
        }],
        line: 184
      },
      "51": {
        loc: {
          start: {
            line: 184,
            column: 29
          },
          end: {
            line: 184,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 29
          },
          end: {
            line: 184,
            column: 48
          }
        }, {
          start: {
            line: 184,
            column: 52
          },
          end: {
            line: 184,
            column: 73
          }
        }],
        line: 184
      },
      "52": {
        loc: {
          start: {
            line: 215,
            column: 23
          },
          end: {
            line: 215,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 23
          },
          end: {
            line: 215,
            column: 28
          }
        }, {
          start: {
            line: 215,
            column: 32
          },
          end: {
            line: 215,
            column: 41
          }
        }],
        line: 215
      },
      "53": {
        loc: {
          start: {
            line: 234,
            column: 23
          },
          end: {
            line: 234,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 234,
            column: 51
          },
          end: {
            line: 234,
            column: 72
          }
        }, {
          start: {
            line: 234,
            column: 75
          },
          end: {
            line: 234,
            column: 80
          }
        }],
        line: 234
      },
      "54": {
        loc: {
          start: {
            line: 246,
            column: 24
          },
          end: {
            line: 246,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 246,
            column: 24
          },
          end: {
            line: 246,
            column: 30
          }
        }, {
          start: {
            line: 246,
            column: 34
          },
          end: {
            line: 246,
            column: 43
          }
        }],
        line: 246
      },
      "55": {
        loc: {
          start: {
            line: 275,
            column: 0
          },
          end: {
            line: 302,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 275,
            column: 0
          },
          end: {
            line: 302,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 275
      },
      "56": {
        loc: {
          start: {
            line: 285,
            column: 25
          },
          end: {
            line: 285,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 285,
            column: 74
          },
          end: {
            line: 285,
            column: 80
          }
        }, {
          start: {
            line: 285,
            column: 83
          },
          end: {
            line: 285,
            column: 96
          }
        }],
        line: 285
      },
      "57": {
        loc: {
          start: {
            line: 285,
            column: 25
          },
          end: {
            line: 285,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 25
          },
          end: {
            line: 285,
            column: 54
          }
        }, {
          start: {
            line: 285,
            column: 58
          },
          end: {
            line: 285,
            column: 71
          }
        }],
        line: 285
      },
      "58": {
        loc: {
          start: {
            line: 291,
            column: 38
          },
          end: {
            line: 291,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 38
          },
          end: {
            line: 291,
            column: 49
          }
        }, {
          start: {
            line: 291,
            column: 53
          },
          end: {
            line: 291,
            column: 77
          }
        }],
        line: 291
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/errorTracking.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,qDAAyC;AAmBzC;IAAA;QACU,iBAAY,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;QACrD,aAAQ,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;QACzC,oBAAe,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;IAwHjE,CAAC;IAtHC;;OAEG;IACH,uCAAgB,GAAhB,UAAiB,KAAY,EAAE,OAAsB;QACnD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,4BAA4B;YAC5B,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE;gBAC7B,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK;gBACrB,IAAI,wBACC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,KAChB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,EACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GACpC;gBACD,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI;gBACnB,KAAK,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,KAAI,OAAO;aACjC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,mBAAmB;YACnB,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qCAAc,GAAd,UAAe,OAAe,EAAE,OAAsB;QACpD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,4BAA4B;YAC5B,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE;gBAC7B,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK;gBACrB,IAAI,wBACC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,KAChB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,EACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GACpC;gBACD,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI;gBACnB,KAAK,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,KAAI,OAAO;aACjC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,mBAAmB;YACnB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,8BAAO,GAAP,UAAQ,IAAqC;QAC3C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oCAAa,GAAb,UAAc,OAAe,EAAE,QAAoB;QAApB,yBAAA,EAAA,oBAAoB;QACjD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,aAAa,CAAC;gBACnB,OAAO,SAAA;gBACP,QAAQ,UAAA;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI;aAC7B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,CAAC,KAAK,CAAC,uBAAgB,QAAQ,eAAK,OAAO,CAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,+BAAQ,GAAhB,UAAiB,IAAY,EAAE,KAAY,EAAE,OAAsB;QACjE,IAAM,OAAO,GAAG;YACd,IAAI,MAAA;YACJ,KAAK,EAAE;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB;YACD,OAAO,SAAA;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ;YACpD,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;SAC1D,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,wCAAwC;YACxC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,uCAAuC;YACvC,OAAO,CAAC,KAAK,CAAC,uBAAM,IAAI,eAAK,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC;YAC9C,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACtC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iCAAU,GAAlB,UAAmB,OAAe,EAAE,OAAsB;QACxD,IAAM,OAAO,GAAG;YACd,OAAO,SAAA;YACP,OAAO,SAAA;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ;SACrD,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,gCAAe,OAAO,CAAE,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACtC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IACH,mBAAC;AAAD,CAAC,AA3HD,IA2HC;AAED,qBAAqB;AACrB,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AA6G/B,oCAAY;AA3GrB,mDAAmD;AACtC,QAAA,UAAU,GAAG;IACxB;;OAEG;IACH,GAAG,EAAE,UAAC,KAAY,EAAE,QAAgB,EAAE,MAAc,EAAE,UAAmB;QACvE,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE;YACnC,IAAI,EAAE;gBACJ,SAAS,EAAE,KAAK;gBAChB,QAAQ,UAAA;gBACR,MAAM,QAAA;gBACN,UAAU,EAAE,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,EAAE,KAAI,SAAS;aAChD;YACD,KAAK,EAAE;gBACL,QAAQ,UAAA;gBACR,MAAM,QAAA;gBACN,UAAU,YAAA;aACX;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAI,EAAE,UAAC,KAAY,EAAE,MAAc;QACjC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE;YACnC,IAAI,EAAE;gBACJ,SAAS,EAAE,gBAAgB;gBAC3B,MAAM,QAAA;aACP;YACD,KAAK,EAAE;gBACL,MAAM,QAAA;aACP;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ,EAAE,UAAC,KAAY,EAAE,SAAiB,EAAE,KAAc;QACxD,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE;YACnC,IAAI,EAAE;gBACJ,SAAS,EAAE,UAAU;gBACrB,SAAS,WAAA;gBACT,KAAK,EAAE,KAAK,IAAI,SAAS;aAC1B;YACD,KAAK,EAAE;gBACL,SAAS,WAAA;gBACT,KAAK,OAAA;aACN;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,EAAE,UAAC,KAAY,EAAE,KAAa,EAAE,KAAW;QACnD,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE;YACnC,IAAI,EAAE;gBACJ,SAAS,EAAE,YAAY;gBACvB,KAAK,OAAA;aACN;YACD,KAAK,EAAE;gBACL,KAAK,OAAA;gBACL,KAAK,EAAE,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;aACjE;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,EAAE,EAAE,UAAC,KAAY,EAAE,SAAiB,EAAE,MAAe;QACnD,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE;YACnC,IAAI,EAAE;gBACJ,SAAS,EAAE,IAAI;gBACf,SAAS,WAAA;gBACT,MAAM,EAAE,MAAM,IAAI,SAAS;aAC5B;YACD,KAAK,EAAE;gBACL,SAAS,WAAA;gBACT,MAAM,QAAA;aACP;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,EAAE,UAAC,OAAe,EAAE,MAAc,EAAE,KAAa,EAAE,SAAiB;QAC7E,YAAY,CAAC,cAAc,CAAC,6BAAsB,OAAO,CAAE,EAAE;YAC3D,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE;gBACJ,SAAS,EAAE,aAAa;gBACxB,MAAM,QAAA;aACP;YACD,KAAK,EAAE;gBACL,MAAM,QAAA;gBACN,KAAK,OAAA;gBACL,SAAS,WAAA;gBACT,QAAQ,EAAE,KAAK,GAAG,SAAS;aAC5B;SACF,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAIF,kBAAe,YAAY,CAAC;AAE5B,6CAA6C;AAC7C,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAClC,sCAAsC;IACtC,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,UAAC,KAAK;;QAClD,YAAY,CAAC,gBAAgB,CAC3B,IAAI,KAAK,CAAC,uCAAgC,KAAK,CAAC,MAAM,CAAE,CAAC,EACzD;YACE,IAAI,EAAE;gBACJ,SAAS,EAAE,oBAAoB;aAChC;YACD,KAAK,EAAE;gBACL,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,OAAO,EAAE,MAAA,KAAK,CAAC,OAAO,0CAAE,QAAQ,EAAE;aACnC;SACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,KAAK;QACrC,YAAY,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YACrE,IAAI,EAAE;gBACJ,SAAS,EAAE,aAAa;aACzB;YACD,KAAK,EAAE;gBACL,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/errorTracking.ts"],
      sourcesContent: ["/**\n * Centralized Error Tracking Service\n * Handles error reporting to external services like Sentry\n */\n\nimport * as Sentry from \"@sentry/nextjs\";\n\ninterface ErrorContext {\n  user?: {\n    id?: string;\n    email?: string;\n  };\n  extra?: Record<string, any>;\n  tags?: Record<string, string>;\n  level?: 'error' | 'warning' | 'info' | 'debug';\n}\n\ninterface ErrorTrackingService {\n  captureException: (error: Error, context?: ErrorContext) => void;\n  captureMessage: (message: string, context?: ErrorContext) => void;\n  setUser: (user: { id?: string; email?: string }) => void;\n  addBreadcrumb: (message: string, category?: string) => void;\n}\n\nclass ErrorTracker implements ErrorTrackingService {\n  private isProduction = process.env.NODE_ENV === 'production';\n  private isClient = typeof window !== 'undefined';\n  private isSentryEnabled = !!process.env.NEXT_PUBLIC_SENTRY_DSN;\n\n  /**\n   * Capture an exception with context\n   */\n  captureException(error: Error, context?: ErrorContext): void {\n    if (this.isSentryEnabled) {\n      // Use Sentry when available\n      Sentry.captureException(error, {\n        extra: context?.extra,\n        tags: {\n          ...context?.tags,\n          environment: process.env.NODE_ENV,\n          timestamp: new Date().toISOString()\n        },\n        user: context?.user,\n        level: context?.level || 'error'\n      });\n    } else {\n      // Fallback logging\n      this.logError('Exception', error, context);\n    }\n  }\n\n  /**\n   * Capture a message with context\n   */\n  captureMessage(message: string, context?: ErrorContext): void {\n    if (this.isSentryEnabled) {\n      // Use Sentry when available\n      Sentry.captureMessage(message, {\n        extra: context?.extra,\n        tags: {\n          ...context?.tags,\n          environment: process.env.NODE_ENV,\n          timestamp: new Date().toISOString()\n        },\n        user: context?.user,\n        level: context?.level || 'error'\n      });\n    } else {\n      // Fallback logging\n      this.logMessage(message, context);\n    }\n  }\n\n  /**\n   * Set user context for error tracking\n   */\n  setUser(user: { id?: string; email?: string }): void {\n    if (this.isSentryEnabled) {\n      Sentry.setUser(user);\n    }\n  }\n\n  /**\n   * Add breadcrumb for debugging\n   */\n  addBreadcrumb(message: string, category = 'default'): void {\n    if (this.isSentryEnabled) {\n      Sentry.addBreadcrumb({\n        message,\n        category,\n        timestamp: Date.now() / 1000\n      });\n    } else if (!this.isProduction) {\n      console.debug(`[Breadcrumb] ${category}: ${message}`);\n    }\n  }\n\n  /**\n   * Fallback error logging\n   */\n  private logError(type: string, error: Error, context?: ErrorContext): void {\n    const logData = {\n      type,\n      error: {\n        name: error.name,\n        message: error.message,\n        stack: error.stack\n      },\n      context,\n      timestamp: new Date().toISOString(),\n      url: this.isClient ? window.location.href : 'server',\n      userAgent: this.isClient ? navigator.userAgent : 'server'\n    };\n\n    if (this.isProduction) {\n      // In production, use structured logging\n      console.error(JSON.stringify(logData));\n    } else {\n      // In development, use readable logging\n      console.group(`\uD83D\uDEA8 ${type}: ${error.message}`);\n      console.error('Error:', error);\n      console.log('Context:', context);\n      console.log('Full Details:', logData);\n      console.groupEnd();\n    }\n  }\n\n  /**\n   * Fallback message logging\n   */\n  private logMessage(message: string, context?: ErrorContext): void {\n    const logData = {\n      message,\n      context,\n      timestamp: new Date().toISOString(),\n      url: this.isClient ? window.location.href : 'server'\n    };\n\n    if (this.isProduction) {\n      console.error(JSON.stringify(logData));\n    } else {\n      console.group(`\uD83D\uDCDD Message: ${message}`);\n      console.log('Context:', context);\n      console.log('Full Details:', logData);\n      console.groupEnd();\n    }\n  }\n}\n\n// Singleton instance\nconst errorTracker = new ErrorTracker();\n\n// Convenience functions for common error scenarios\nexport const trackError = {\n  /**\n   * Track API errors\n   */\n  api: (error: Error, endpoint: string, method: string, statusCode?: number) => {\n    errorTracker.captureException(error, {\n      tags: {\n        errorType: 'api',\n        endpoint,\n        method,\n        statusCode: statusCode?.toString() || 'unknown'\n      },\n      extra: {\n        endpoint,\n        method,\n        statusCode\n      }\n    });\n  },\n\n  /**\n   * Track authentication errors\n   */\n  auth: (error: Error, action: string) => {\n    errorTracker.captureException(error, {\n      tags: {\n        errorType: 'authentication',\n        action\n      },\n      extra: {\n        action\n      }\n    });\n  },\n\n  /**\n   * Track database errors\n   */\n  database: (error: Error, operation: string, table?: string) => {\n    errorTracker.captureException(error, {\n      tags: {\n        errorType: 'database',\n        operation,\n        table: table || 'unknown'\n      },\n      extra: {\n        operation,\n        table\n      }\n    });\n  },\n\n  /**\n   * Track validation errors\n   */\n  validation: (error: Error, field: string, value?: any) => {\n    errorTracker.captureException(error, {\n      tags: {\n        errorType: 'validation',\n        field\n      },\n      extra: {\n        field,\n        value: typeof value === 'object' ? JSON.stringify(value) : value\n      }\n    });\n  },\n\n  /**\n   * Track UI/Component errors\n   */\n  ui: (error: Error, component: string, action?: string) => {\n    errorTracker.captureException(error, {\n      tags: {\n        errorType: 'ui',\n        component,\n        action: action || 'unknown'\n      },\n      extra: {\n        component,\n        action\n      }\n    });\n  },\n\n  /**\n   * Track performance issues\n   */\n  performance: (message: string, metric: string, value: number, threshold: number) => {\n    errorTracker.captureMessage(`Performance issue: ${message}`, {\n      level: 'warning',\n      tags: {\n        errorType: 'performance',\n        metric\n      },\n      extra: {\n        metric,\n        value,\n        threshold,\n        exceeded: value > threshold\n      }\n    });\n  }\n};\n\n// Export the main tracker and convenience functions\nexport { errorTracker };\nexport default errorTracker;\n\n// Global error handlers for unhandled errors\nif (typeof window !== 'undefined') {\n  // Handle unhandled promise rejections\n  window.addEventListener('unhandledrejection', (event) => {\n    errorTracker.captureException(\n      new Error(`Unhandled Promise Rejection: ${event.reason}`),\n      {\n        tags: {\n          errorType: 'unhandledRejection'\n        },\n        extra: {\n          reason: event.reason,\n          promise: event.promise?.toString()\n        }\n      }\n    );\n  });\n\n  // Handle global errors\n  window.addEventListener('error', (event) => {\n    errorTracker.captureException(event.error || new Error(event.message), {\n      tags: {\n        errorType: 'globalError'\n      },\n      extra: {\n        filename: event.filename,\n        lineno: event.lineno,\n        colno: event.colno\n      }\n    });\n  });\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a19be9fab336cd89be8ede9fbe4e3d3e1c8f780b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_16r7n2ic7d = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_16r7n2ic7d();
var __assign =
/* istanbul ignore next */
(cov_16r7n2ic7d().s[0]++,
/* istanbul ignore next */
(cov_16r7n2ic7d().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_16r7n2ic7d().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_16r7n2ic7d().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_16r7n2ic7d().f[0]++;
  cov_16r7n2ic7d().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[1]++;
    cov_16r7n2ic7d().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_16r7n2ic7d().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_16r7n2ic7d().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_16r7n2ic7d().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_16r7n2ic7d().b[2][0]++;
          cov_16r7n2ic7d().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_16r7n2ic7d().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[10]++;
  return __assign.apply(this, arguments);
}));
var __createBinding =
/* istanbul ignore next */
(cov_16r7n2ic7d().s[11]++,
/* istanbul ignore next */
(cov_16r7n2ic7d().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_16r7n2ic7d().b[3][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_16r7n2ic7d().b[3][2]++, Object.create ?
/* istanbul ignore next */
(cov_16r7n2ic7d().b[4][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_16r7n2ic7d().f[2]++;
  cov_16r7n2ic7d().s[12]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().b[5][0]++;
    cov_16r7n2ic7d().s[13]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_16r7n2ic7d().b[5][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_16r7n2ic7d().s[14]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[15]++;
  if (
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[7][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[7][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[8][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[8][1]++,
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[9][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_16r7n2ic7d().b[9][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().b[6][0]++;
    cov_16r7n2ic7d().s[16]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_16r7n2ic7d().f[3]++;
        cov_16r7n2ic7d().s[17]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_16r7n2ic7d().b[6][1]++;
  }
  cov_16r7n2ic7d().s[18]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_16r7n2ic7d().b[4][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_16r7n2ic7d().f[4]++;
  cov_16r7n2ic7d().s[19]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().b[10][0]++;
    cov_16r7n2ic7d().s[20]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_16r7n2ic7d().b[10][1]++;
  }
  cov_16r7n2ic7d().s[21]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_16r7n2ic7d().s[22]++,
/* istanbul ignore next */
(cov_16r7n2ic7d().b[11][0]++, this) &&
/* istanbul ignore next */
(cov_16r7n2ic7d().b[11][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_16r7n2ic7d().b[11][2]++, Object.create ?
/* istanbul ignore next */
(cov_16r7n2ic7d().b[12][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_16r7n2ic7d().f[5]++;
  cov_16r7n2ic7d().s[23]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_16r7n2ic7d().b[12][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_16r7n2ic7d().f[6]++;
  cov_16r7n2ic7d().s[24]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_16r7n2ic7d().s[25]++,
/* istanbul ignore next */
(cov_16r7n2ic7d().b[13][0]++, this) &&
/* istanbul ignore next */
(cov_16r7n2ic7d().b[13][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_16r7n2ic7d().b[13][2]++, function () {
  /* istanbul ignore next */
  cov_16r7n2ic7d().f[7]++;
  cov_16r7n2ic7d().s[26]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[8]++;
    cov_16r7n2ic7d().s[27]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_16r7n2ic7d().b[14][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_16r7n2ic7d().b[14][1]++, function (o) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().f[9]++;
      var ar =
      /* istanbul ignore next */
      (cov_16r7n2ic7d().s[28]++, []);
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[29]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_16r7n2ic7d().s[30]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_16r7n2ic7d().b[15][0]++;
          cov_16r7n2ic7d().s[31]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_16r7n2ic7d().b[15][1]++;
        }
      }
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[32]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[33]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[34]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[10]++;
    cov_16r7n2ic7d().s[35]++;
    if (
    /* istanbul ignore next */
    (cov_16r7n2ic7d().b[17][0]++, mod) &&
    /* istanbul ignore next */
    (cov_16r7n2ic7d().b[17][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[16][0]++;
      cov_16r7n2ic7d().s[36]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_16r7n2ic7d().b[16][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_16r7n2ic7d().s[37]++, {});
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[38]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[18][0]++;
      cov_16r7n2ic7d().s[39]++;
      for (var k =
        /* istanbul ignore next */
        (cov_16r7n2ic7d().s[40]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_16r7n2ic7d().s[41]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_16r7n2ic7d().s[42]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_16r7n2ic7d().b[19][0]++;
          cov_16r7n2ic7d().s[43]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_16r7n2ic7d().b[19][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_16r7n2ic7d().b[18][1]++;
    }
    cov_16r7n2ic7d().s[44]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[45]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_16r7n2ic7d().s[46]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_16r7n2ic7d().s[47]++;
exports.errorTracker = exports.trackError = void 0;
var Sentry =
/* istanbul ignore next */
(cov_16r7n2ic7d().s[48]++, __importStar(require("@sentry/nextjs")));
var ErrorTracker =
/* istanbul ignore next */
(/** @class */cov_16r7n2ic7d().s[49]++, function () {
  /* istanbul ignore next */
  cov_16r7n2ic7d().f[11]++;
  function ErrorTracker() {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[12]++;
    cov_16r7n2ic7d().s[50]++;
    this.isProduction = process.env.NODE_ENV === 'production';
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[51]++;
    this.isClient = typeof window !== 'undefined';
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[52]++;
    this.isSentryEnabled = !!process.env.NEXT_PUBLIC_SENTRY_DSN;
  }
  /**
   * Capture an exception with context
   */
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[53]++;
  ErrorTracker.prototype.captureException = function (error, context) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[13]++;
    cov_16r7n2ic7d().s[54]++;
    if (this.isSentryEnabled) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[20][0]++;
      cov_16r7n2ic7d().s[55]++;
      // Use Sentry when available
      Sentry.captureException(error, {
        extra:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[22][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[22][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[21][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[21][1]++, context.extra),
        tags: __assign(__assign({},
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[24][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[24][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[23][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[23][1]++, context.tags)), {
          environment: process.env.NODE_ENV,
          timestamp: new Date().toISOString()
        }),
        user:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[26][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[26][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[25][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[25][1]++, context.user),
        level:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[27][0]++,
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[29][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[29][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[28][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[28][1]++, context.level)) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[27][1]++, 'error')
      });
    } else {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[20][1]++;
      cov_16r7n2ic7d().s[56]++;
      // Fallback logging
      this.logError('Exception', error, context);
    }
  };
  /**
   * Capture a message with context
   */
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[57]++;
  ErrorTracker.prototype.captureMessage = function (message, context) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[14]++;
    cov_16r7n2ic7d().s[58]++;
    if (this.isSentryEnabled) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[30][0]++;
      cov_16r7n2ic7d().s[59]++;
      // Use Sentry when available
      Sentry.captureMessage(message, {
        extra:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[32][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[32][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[31][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[31][1]++, context.extra),
        tags: __assign(__assign({},
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[34][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[34][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[33][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[33][1]++, context.tags)), {
          environment: process.env.NODE_ENV,
          timestamp: new Date().toISOString()
        }),
        user:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[36][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[36][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[35][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[35][1]++, context.user),
        level:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[37][0]++,
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[39][0]++, context === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[39][1]++, context === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[38][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[38][1]++, context.level)) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[37][1]++, 'error')
      });
    } else {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[30][1]++;
      cov_16r7n2ic7d().s[60]++;
      // Fallback logging
      this.logMessage(message, context);
    }
  };
  /**
   * Set user context for error tracking
   */
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[61]++;
  ErrorTracker.prototype.setUser = function (user) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[15]++;
    cov_16r7n2ic7d().s[62]++;
    if (this.isSentryEnabled) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[40][0]++;
      cov_16r7n2ic7d().s[63]++;
      Sentry.setUser(user);
    } else
    /* istanbul ignore next */
    {
      cov_16r7n2ic7d().b[40][1]++;
    }
  };
  /**
   * Add breadcrumb for debugging
   */
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[64]++;
  ErrorTracker.prototype.addBreadcrumb = function (message, category) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[16]++;
    cov_16r7n2ic7d().s[65]++;
    if (category === void 0) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[41][0]++;
      cov_16r7n2ic7d().s[66]++;
      category = 'default';
    } else
    /* istanbul ignore next */
    {
      cov_16r7n2ic7d().b[41][1]++;
    }
    cov_16r7n2ic7d().s[67]++;
    if (this.isSentryEnabled) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[42][0]++;
      cov_16r7n2ic7d().s[68]++;
      Sentry.addBreadcrumb({
        message: message,
        category: category,
        timestamp: Date.now() / 1000
      });
    } else {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[42][1]++;
      cov_16r7n2ic7d().s[69]++;
      if (!this.isProduction) {
        /* istanbul ignore next */
        cov_16r7n2ic7d().b[43][0]++;
        cov_16r7n2ic7d().s[70]++;
        console.debug("[Breadcrumb] ".concat(category, ": ").concat(message));
      } else
      /* istanbul ignore next */
      {
        cov_16r7n2ic7d().b[43][1]++;
      }
    }
  };
  /**
   * Fallback error logging
   */
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[71]++;
  ErrorTracker.prototype.logError = function (type, error, context) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[17]++;
    var logData =
    /* istanbul ignore next */
    (cov_16r7n2ic7d().s[72]++, {
      type: type,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context: context,
      timestamp: new Date().toISOString(),
      url: this.isClient ?
      /* istanbul ignore next */
      (cov_16r7n2ic7d().b[44][0]++, window.location.href) :
      /* istanbul ignore next */
      (cov_16r7n2ic7d().b[44][1]++, 'server'),
      userAgent: this.isClient ?
      /* istanbul ignore next */
      (cov_16r7n2ic7d().b[45][0]++, navigator.userAgent) :
      /* istanbul ignore next */
      (cov_16r7n2ic7d().b[45][1]++, 'server')
    });
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[73]++;
    if (this.isProduction) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[46][0]++;
      cov_16r7n2ic7d().s[74]++;
      // In production, use structured logging
      console.error(JSON.stringify(logData));
    } else {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[46][1]++;
      cov_16r7n2ic7d().s[75]++;
      // In development, use readable logging
      console.group("\uD83D\uDEA8 ".concat(type, ": ").concat(error.message));
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[76]++;
      console.error('Error:', error);
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[77]++;
      console.log('Context:', context);
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[78]++;
      console.log('Full Details:', logData);
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[79]++;
      console.groupEnd();
    }
  };
  /**
   * Fallback message logging
   */
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[80]++;
  ErrorTracker.prototype.logMessage = function (message, context) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[18]++;
    var logData =
    /* istanbul ignore next */
    (cov_16r7n2ic7d().s[81]++, {
      message: message,
      context: context,
      timestamp: new Date().toISOString(),
      url: this.isClient ?
      /* istanbul ignore next */
      (cov_16r7n2ic7d().b[47][0]++, window.location.href) :
      /* istanbul ignore next */
      (cov_16r7n2ic7d().b[47][1]++, 'server')
    });
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[82]++;
    if (this.isProduction) {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[48][0]++;
      cov_16r7n2ic7d().s[83]++;
      console.error(JSON.stringify(logData));
    } else {
      /* istanbul ignore next */
      cov_16r7n2ic7d().b[48][1]++;
      cov_16r7n2ic7d().s[84]++;
      console.group("\uD83D\uDCDD Message: ".concat(message));
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[85]++;
      console.log('Context:', context);
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[86]++;
      console.log('Full Details:', logData);
      /* istanbul ignore next */
      cov_16r7n2ic7d().s[87]++;
      console.groupEnd();
    }
  };
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[88]++;
  return ErrorTracker;
}());
// Singleton instance
var errorTracker =
/* istanbul ignore next */
(cov_16r7n2ic7d().s[89]++, new ErrorTracker());
/* istanbul ignore next */
cov_16r7n2ic7d().s[90]++;
exports.errorTracker = errorTracker;
// Convenience functions for common error scenarios
/* istanbul ignore next */
cov_16r7n2ic7d().s[91]++;
exports.trackError = {
  /**
   * Track API errors
   */
  api: function (error, endpoint, method, statusCode) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[19]++;
    cov_16r7n2ic7d().s[92]++;
    errorTracker.captureException(error, {
      tags: {
        errorType: 'api',
        endpoint: endpoint,
        method: method,
        statusCode:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[49][0]++,
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[51][0]++, statusCode === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[51][1]++, statusCode === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[50][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[50][1]++, statusCode.toString())) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[49][1]++, 'unknown')
      },
      extra: {
        endpoint: endpoint,
        method: method,
        statusCode: statusCode
      }
    });
  },
  /**
   * Track authentication errors
   */
  auth: function (error, action) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[20]++;
    cov_16r7n2ic7d().s[93]++;
    errorTracker.captureException(error, {
      tags: {
        errorType: 'authentication',
        action: action
      },
      extra: {
        action: action
      }
    });
  },
  /**
   * Track database errors
   */
  database: function (error, operation, table) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[21]++;
    cov_16r7n2ic7d().s[94]++;
    errorTracker.captureException(error, {
      tags: {
        errorType: 'database',
        operation: operation,
        table:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[52][0]++, table) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[52][1]++, 'unknown')
      },
      extra: {
        operation: operation,
        table: table
      }
    });
  },
  /**
   * Track validation errors
   */
  validation: function (error, field, value) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[22]++;
    cov_16r7n2ic7d().s[95]++;
    errorTracker.captureException(error, {
      tags: {
        errorType: 'validation',
        field: field
      },
      extra: {
        field: field,
        value: typeof value === 'object' ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[53][0]++, JSON.stringify(value)) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[53][1]++, value)
      }
    });
  },
  /**
   * Track UI/Component errors
   */
  ui: function (error, component, action) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[23]++;
    cov_16r7n2ic7d().s[96]++;
    errorTracker.captureException(error, {
      tags: {
        errorType: 'ui',
        component: component,
        action:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[54][0]++, action) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[54][1]++, 'unknown')
      },
      extra: {
        component: component,
        action: action
      }
    });
  },
  /**
   * Track performance issues
   */
  performance: function (message, metric, value, threshold) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[24]++;
    cov_16r7n2ic7d().s[97]++;
    errorTracker.captureMessage("Performance issue: ".concat(message), {
      level: 'warning',
      tags: {
        errorType: 'performance',
        metric: metric
      },
      extra: {
        metric: metric,
        value: value,
        threshold: threshold,
        exceeded: value > threshold
      }
    });
  }
};
/* istanbul ignore next */
cov_16r7n2ic7d().s[98]++;
exports.default = errorTracker;
// Global error handlers for unhandled errors
/* istanbul ignore next */
cov_16r7n2ic7d().s[99]++;
if (typeof window !== 'undefined') {
  /* istanbul ignore next */
  cov_16r7n2ic7d().b[55][0]++;
  cov_16r7n2ic7d().s[100]++;
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', function (event) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[25]++;
    var _a;
    /* istanbul ignore next */
    cov_16r7n2ic7d().s[101]++;
    errorTracker.captureException(new Error("Unhandled Promise Rejection: ".concat(event.reason)), {
      tags: {
        errorType: 'unhandledRejection'
      },
      extra: {
        reason: event.reason,
        promise:
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[57][0]++, (_a = event.promise) === null) ||
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[57][1]++, _a === void 0) ?
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[56][0]++, void 0) :
        /* istanbul ignore next */
        (cov_16r7n2ic7d().b[56][1]++, _a.toString())
      }
    });
  });
  // Handle global errors
  /* istanbul ignore next */
  cov_16r7n2ic7d().s[102]++;
  window.addEventListener('error', function (event) {
    /* istanbul ignore next */
    cov_16r7n2ic7d().f[26]++;
    cov_16r7n2ic7d().s[103]++;
    errorTracker.captureException(
    /* istanbul ignore next */
    (cov_16r7n2ic7d().b[58][0]++, event.error) ||
    /* istanbul ignore next */
    (cov_16r7n2ic7d().b[58][1]++, new Error(event.message)), {
      tags: {
        errorType: 'globalError'
      },
      extra: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    });
  });
} else
/* istanbul ignore next */
{
  cov_16r7n2ic7d().b[55][1]++;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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