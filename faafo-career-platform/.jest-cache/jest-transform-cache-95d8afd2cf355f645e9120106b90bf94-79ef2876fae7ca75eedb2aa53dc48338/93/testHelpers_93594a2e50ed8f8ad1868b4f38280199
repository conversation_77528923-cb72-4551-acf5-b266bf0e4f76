d5101209e125eb5cc5ba212b745d0eb1
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateMaliciousInputs = exports.measureExecutionTime = exports.generateTestResources = exports.generateTestUsers = exports.validateErrorResponse = exports.validateAPIResponse = exports.createMockFetch = exports.mockNextRouter = exports.generateAssessmentFormData = exports.APITestHelper = exports.createMockSession = exports.TestDatabase = void 0;
// Mock imports for testing
var bcrypt_1 = __importDefault(require("bcrypt"));
// Mock test database utilities with constraint simulation
var TestDatabase = /** @class */ (function () {
    function TestDatabase() {
        this.createdUsers = new Set();
        this.createdResources = new Set();
        this.createdProgress = new Set();
        this.createdRatings = new Set();
        this.idCounter = 0;
        // Use the global mock Prisma client
        this.mockPrisma = global.mockPrisma;
        if (!this.mockPrisma) {
            throw new Error('Mock Prisma not available. Make sure jest.setup.ts is properly configured.');
        }
    }
    TestDatabase.prototype.cleanup = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                // Mock cleanup - reset the mocks and clear tracking sets
                jest.clearAllMocks();
                this.createdUsers.clear();
                this.createdResources.clear();
                this.createdProgress.clear();
                this.createdRatings.clear();
                this.idCounter = 0;
                return [2 /*return*/, Promise.resolve()];
            });
        });
    };
    // Helper method to generate unique emails for tests
    TestDatabase.prototype.generateUniqueEmail = function (baseEmail) {
        var timestamp = Date.now();
        var _a = baseEmail.split('@'), localPart = _a[0], domain = _a[1];
        return "".concat(localPart, "_").concat(timestamp, "@").concat(domain);
    };
    TestDatabase.prototype.createTestUser = function () {
        return __awaiter(this, arguments, void 0, function (overrides) {
            var baseEmail, email, userData, emailRegex, hashedPassword, _a, _b, testUser;
            var _c;
            if (overrides === void 0) { overrides = {}; }
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        baseEmail = overrides.email || '<EMAIL>';
                        email = baseEmail;
                        // If email already exists, generate unique email for normal tests
                        if (this.createdUsers.has(baseEmail)) {
                            email = this.generateUniqueEmail(baseEmail);
                        }
                        userData = __assign({ email: email, password: overrides.password, name: overrides.name }, overrides);
                        // Validate required fields - password is required unless explicitly testing without it
                        if (!userData.email) {
                            throw new Error('Email is required');
                        }
                        if (!userData.password && !overrides.hasOwnProperty('password')) {
                            throw new Error('Password is required');
                        }
                        emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        if (!emailRegex.test(userData.email) || userData.email.includes('..')) {
                            throw new Error('Invalid email format');
                        }
                        // Check for oversized input
                        if (userData.email.length > 254 || ((_c = userData.name) === null || _c === void 0 ? void 0 : _c.length) > 1000) {
                            throw new Error('Input too large');
                        }
                        // Validate password requirements if password is provided
                        if (userData.password && userData.password.length < 8 && !userData.password.startsWith('$2b$')) {
                            throw new Error('Password must be at least 8 characters');
                        }
                        if (!userData.password) return [3 /*break*/, 4];
                        if (!userData.password.startsWith('$2b$')) return [3 /*break*/, 1];
                        _b = userData.password;
                        return [3 /*break*/, 3];
                    case 1: return [4 /*yield*/, bcrypt_1.default.hash(userData.password, 10)];
                    case 2:
                        _b = _d.sent();
                        _d.label = 3;
                    case 3:
                        _a = (_b);
                        return [3 /*break*/, 5];
                    case 4:
                        _a = null;
                        _d.label = 5;
                    case 5:
                        hashedPassword = _a;
                        testUser = {
                            id: "test-user-".concat(Date.now(), "-").concat(++this.idCounter),
                            email: userData.email,
                            password: hashedPassword,
                            name: overrides.hasOwnProperty('name') ? userData.name : null,
                            image: userData.image || null,
                            emailVerified: userData.emailVerified || null,
                            failedLoginAttempts: userData.failedLoginAttempts || 0,
                            lockedUntil: userData.lockedUntil || null,
                            passwordResetToken: userData.passwordResetToken || null,
                            passwordResetExpires: userData.passwordResetExpires || null,
                            createdAt: new Date(),
                            updatedAt: new Date(),
                            completedAt: userData.completedAt || null,
                        };
                        // Track created user
                        this.createdUsers.add(userData.email);
                        // Mock the creation
                        this.mockPrisma.user.create.mockResolvedValue(testUser);
                        return [2 /*return*/, testUser];
                }
            });
        });
    };
    // Special method for testing unique constraint violations
    TestDatabase.prototype.createUserForUniqueTest = function (userData) {
        return __awaiter(this, void 0, void 0, function () {
            var email, hashedPassword, _a, _b, testUser;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        email = userData.email || '<EMAIL>';
                        // If email already exists, throw error (this is what we want for unique constraint testing)
                        if (this.createdUsers.has(email)) {
                            throw new Error('Email already exists');
                        }
                        if (!userData.password) return [3 /*break*/, 4];
                        if (!userData.password.startsWith('$2b$')) return [3 /*break*/, 1];
                        _b = userData.password;
                        return [3 /*break*/, 3];
                    case 1: return [4 /*yield*/, bcrypt_1.default.hash(userData.password, 10)];
                    case 2:
                        _b = _c.sent();
                        _c.label = 3;
                    case 3:
                        _a = (_b);
                        return [3 /*break*/, 5];
                    case 4:
                        _a = null;
                        _c.label = 5;
                    case 5:
                        hashedPassword = _a;
                        testUser = {
                            id: "test-user-".concat(Date.now(), "-").concat(++this.idCounter),
                            email: email,
                            password: hashedPassword,
                            name: userData.name || null,
                            image: userData.image || null,
                            emailVerified: userData.emailVerified || null,
                            failedLoginAttempts: userData.failedLoginAttempts || 0,
                            lockedUntil: userData.lockedUntil || null,
                            passwordResetToken: userData.passwordResetToken || null,
                            passwordResetExpires: userData.passwordResetExpires || null,
                            createdAt: new Date(),
                            updatedAt: new Date(),
                            completedAt: userData.completedAt || null,
                        };
                        // Track created user
                        this.createdUsers.add(email);
                        // Mock the creation
                        this.mockPrisma.user.create.mockResolvedValue(testUser);
                        return [2 /*return*/, testUser];
                }
            });
        });
    };
    TestDatabase.prototype.createTestAssessment = function (userId_1) {
        return __awaiter(this, arguments, void 0, function (userId, overrides) {
            var currentStep, validStatuses, testAssessment;
            if (overrides === void 0) { overrides = {}; }
            return __generator(this, function (_a) {
                // Simulate foreign key constraint - but be more lenient for testing
                // Only throw error if explicitly testing foreign key constraints
                if (overrides.testForeignKey && !this.createdUsers.has('<EMAIL>') && userId !== 'test-user-1') {
                    throw new Error('User does not exist');
                }
                currentStep = overrides.currentStep || 1;
                if (currentStep < 1 || currentStep > 10) {
                    throw new Error('Invalid step number');
                }
                // Validate enum values if testing
                if (overrides.testEnumValidation) {
                    validStatuses = ['IN_PROGRESS', 'COMPLETED', 'ABANDONED'];
                    if (!validStatuses.includes(overrides.status)) {
                        throw new Error('Invalid assessment status');
                    }
                }
                testAssessment = __assign({ id: "test-assessment-".concat(Date.now(), "-").concat(++this.idCounter), userId: userId, status: 'IN_PROGRESS', currentStep: currentStep, responses: [], score: 0, createdAt: new Date(), updatedAt: new Date(), completedAt: overrides.completedAt || null }, overrides);
                // Configure mocks to return this assessment
                this.mockPrisma.assessment.create.mockResolvedValue(testAssessment);
                this.mockPrisma.assessment.findFirst.mockResolvedValue(testAssessment);
                this.mockPrisma.assessment.findUnique.mockResolvedValue(testAssessment);
                this.mockPrisma.assessment.update.mockResolvedValue(testAssessment);
                return [2 /*return*/, testAssessment];
            });
        });
    };
    TestDatabase.prototype.createTestLearningResource = function () {
        return __awaiter(this, arguments, void 0, function (overrides) {
            var resourceData, validTypes, testResource;
            if (overrides === void 0) { overrides = {}; }
            return __generator(this, function (_a) {
                resourceData = __assign({ title: 'Test Resource', description: 'A test learning resource', url: 'https://example.com/test-resource', type: 'COURSE', category: 'CYBERSECURITY', difficulty: 'BEGINNER', estimatedHours: 10, isActive: true }, overrides);
                validTypes = ['COURSE', 'TUTORIAL', 'BOOK', 'VIDEO', 'ARTICLE'];
                if (overrides.testEnumValidation && !validTypes.includes(resourceData.type)) {
                    throw new Error('Invalid resource type');
                }
                // Simulate unique URL constraint
                if (this.createdResources.has(resourceData.url)) {
                    throw new Error('URL already exists');
                }
                testResource = __assign(__assign({ id: "test-resource-".concat(Date.now(), "-").concat(++this.idCounter) }, resourceData), { author: resourceData.author || null, duration: resourceData.duration || null, cost: resourceData.cost || 'FREE', createdAt: new Date(), updatedAt: new Date() });
                // Track created resource
                this.createdResources.add(resourceData.url);
                this.mockPrisma.learningResource.create.mockResolvedValue(testResource);
                return [2 /*return*/, testResource];
            });
        });
    };
    TestDatabase.prototype.createTestProgress = function (userId_1, resourceId_1) {
        return __awaiter(this, arguments, void 0, function (userId, resourceId, overrides) {
            var progressKey, uniqueKey, testProgress;
            if (overrides === void 0) { overrides = {}; }
            return __generator(this, function (_a) {
                progressKey = "".concat(userId, "-").concat(resourceId);
                if (overrides.testUniqueConstraint && this.createdProgress.has(progressKey)) {
                    throw new Error('Progress record already exists for this user-resource combination');
                }
                uniqueKey = this.createdProgress.has(progressKey) ? "".concat(progressKey, "-").concat(Date.now()) : progressKey;
                testProgress = __assign({ id: "test-progress-".concat(Date.now(), "-").concat(++this.idCounter), userId: userId, resourceId: resourceId, status: 'IN_PROGRESS', progress: 0, completedAt: overrides.completedAt || null, rating: overrides.rating || null, review: overrides.review || null, createdAt: new Date(), updatedAt: new Date() }, overrides);
                // Track created progress
                this.createdProgress.add(uniqueKey);
                this.mockPrisma.userProgress.create.mockResolvedValue(testProgress);
                return [2 /*return*/, testProgress];
            });
        });
    };
    TestDatabase.prototype.createTestRating = function (userId_1, resourceId_1) {
        return __awaiter(this, arguments, void 0, function (userId, resourceId, overrides) {
            var ratingData, ratingKey, uniqueKey, testRating;
            if (overrides === void 0) { overrides = {}; }
            return __generator(this, function (_a) {
                ratingData = __assign({ rating: 5, review: 'Great resource!', isHelpful: true }, overrides);
                // Validate rating range
                if (ratingData.rating < 1 || ratingData.rating > 5) {
                    throw new Error('Rating must be between 1 and 5');
                }
                ratingKey = "".concat(userId, "-").concat(resourceId);
                if (overrides.testUniqueConstraint && this.createdRatings.has(ratingKey)) {
                    throw new Error('Rating already exists for this user-resource combination');
                }
                uniqueKey = this.createdRatings.has(ratingKey) ? "".concat(ratingKey, "-").concat(Date.now()) : ratingKey;
                testRating = __assign(__assign({ id: "test-rating-".concat(Date.now(), "-").concat(++this.idCounter), userId: userId, resourceId: resourceId }, ratingData), { createdAt: new Date(), updatedAt: new Date() });
                // Track created rating
                this.createdRatings.add(uniqueKey);
                this.mockPrisma.resourceRating.create.mockResolvedValue(testRating);
                return [2 /*return*/, testRating];
            });
        });
    };
    TestDatabase.prototype.disconnect = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                // Mock disconnect
                return [2 /*return*/, Promise.resolve()];
            });
        });
    };
    return TestDatabase;
}());
exports.TestDatabase = TestDatabase;
// Mock session helper
var createMockSession = function (userId, userEmail) {
    if (userEmail === void 0) { userEmail = '<EMAIL>'; }
    return ({
        user: {
            id: userId,
            email: userEmail,
            name: 'Test User'
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    });
};
exports.createMockSession = createMockSession;
// API testing utilities
var APITestHelper = /** @class */ (function () {
    function APITestHelper() {
    }
    APITestHelper.createMockRequest = function (method, url, body, headers) {
        if (method === void 0) { method = 'GET'; }
        if (url === void 0) { url = 'http://localhost:3000/api/test'; }
        if (headers === void 0) { headers = {}; }
        var requestInit = {
            method: method,
            headers: __assign({ 'Content-Type': 'application/json' }, headers)
        };
        if (body && method !== 'GET') {
            requestInit.body = JSON.stringify(body);
        }
        // Mock the NextRequest to avoid Edge Runtime issues in Jest
        var mockRequest = {
            method: method,
            url: url,
            headers: new Headers(requestInit.headers),
            body: requestInit.body,
            json: function () { return Promise.resolve(body); },
            text: function () { return Promise.resolve(requestInit.body || ''); },
            formData: function () { return Promise.resolve(new FormData()); },
            cookies: {
                get: jest.fn(),
                set: jest.fn(),
                delete: jest.fn(),
                has: jest.fn(),
                clear: jest.fn(),
            },
            nextUrl: {
                pathname: new URL(url).pathname,
                searchParams: new URL(url).searchParams,
            },
            geo: {},
            ip: '127.0.0.1',
        };
        return mockRequest;
    };
    APITestHelper.parseResponse = function (response) {
        return __awaiter(this, void 0, void 0, function () {
            var text;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, response.text()];
                    case 1:
                        text = _a.sent();
                        try {
                            return [2 /*return*/, JSON.parse(text)];
                        }
                        catch (_b) {
                            return [2 /*return*/, text];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    return APITestHelper;
}());
exports.APITestHelper = APITestHelper;
// Form data generators
var generateAssessmentFormData = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return (__assign({ dissatisfaction_triggers: ['lack_of_growth', 'poor_work_life_balance'], desired_outcomes_skill_a: 'high', desired_outcomes_skill_b: 'medium', desired_outcomes_skill_c: 'low', work_environment_preference: 'remote', risk_tolerance: 'medium', learning_style: 'hands_on', time_commitment: '10_15_hours' }, overrides));
};
exports.generateAssessmentFormData = generateAssessmentFormData;
// Component testing utilities
exports.mockNextRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    pathname: '/',
    route: '/',
    query: {},
    asPath: '/',
    events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn()
    }
};
// Mock fetch for API calls
var createMockFetch = function (responses) {
    return jest.fn().mockImplementation(function (url) {
        var mockResponse = responses.find(function (r) { return !r.url || url.includes(r.url); }) || responses[0];
        return Promise.resolve({
            ok: (mockResponse.status || 200) < 400,
            status: mockResponse.status || 200,
            json: function () { return Promise.resolve(mockResponse.response); },
            text: function () { return Promise.resolve(JSON.stringify(mockResponse.response)); }
        });
    });
};
exports.createMockFetch = createMockFetch;
// Validation helpers
var validateAPIResponse = function (response, expectedFields) {
    expectedFields.forEach(function (field) {
        expect(response).toHaveProperty(field);
    });
};
exports.validateAPIResponse = validateAPIResponse;
var validateErrorResponse = function (response, expectedStatus) {
    if (expectedStatus === void 0) { expectedStatus = 400; }
    expect(response).toHaveProperty('error');
    expect(typeof response.error).toBe('string');
};
exports.validateErrorResponse = validateErrorResponse;
// Test data generators
var generateTestUsers = function (count) {
    if (count === void 0) { count = 3; }
    return Array.from({ length: count }, function (_, i) { return ({
        email: "testuser".concat(i + 1, "@example.com"),
        password: 'testpassword123',
        name: "Test User ".concat(i + 1)
    }); });
};
exports.generateTestUsers = generateTestUsers;
var generateTestResources = function (count) {
    if (count === void 0) { count = 5; }
    var categories = ['CYBERSECURITY', 'DATA_SCIENCE', 'WEB_DEVELOPMENT'];
    var types = ['COURSE', 'ARTICLE', 'VIDEO'];
    var skillLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'];
    return Array.from({ length: count }, function (_, i) { return ({
        title: "Test Resource ".concat(i + 1),
        description: "Description for test resource ".concat(i + 1),
        url: "https://example.com/resource-".concat(i + 1),
        type: types[i % types.length],
        category: categories[i % categories.length],
        skillLevel: skillLevels[i % skillLevels.length],
        cost: 'FREE',
        format: 'SELF_PACED'
    }); });
};
exports.generateTestResources = generateTestResources;
// Performance testing utilities
var measureExecutionTime = function (fn) { return __awaiter(void 0, void 0, void 0, function () {
    var start, result, end;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                start = performance.now();
                return [4 /*yield*/, fn()];
            case 1:
                result = _a.sent();
                end = performance.now();
                return [2 /*return*/, {
                        result: result,
                        executionTime: end - start
                    }];
        }
    });
}); };
exports.measureExecutionTime = measureExecutionTime;
// Security testing utilities
var generateMaliciousInputs = function () { return ({
    sqlInjection: ["'; DROP TABLE users; --", "1' OR '1'='1", "admin'--"],
    xss: ["<script>alert('xss')</script>", "javascript:alert('xss')", "<img src=x onerror=alert('xss')>"],
    pathTraversal: ["../../../etc/passwd", "..\\..\\..\\windows\\system32\\config\\sam"],
    oversizedInput: "A".repeat(10000),
    nullBytes: "test\x00.txt",
    specialChars: "!@#$%^&*()_+-=[]{}|;':\",./<>?"
}); };
exports.generateMaliciousInputs = generateMaliciousInputs;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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