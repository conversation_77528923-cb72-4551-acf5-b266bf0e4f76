{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/__mocks__/useFeedback.ts", "mappings": ";;;AAAa,QAAA,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;IACxC,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;IACtB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;IACpB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;IACnB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;IACxB,QAAQ,EAAE,IAAI;CACf,CAAC,EANuC,CAMvC,CAAC,CAAC;AAES,QAAA,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;IAC9C,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;CAClB,CAAC,EAH6C,CAG7C,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/__mocks__/useFeedback.ts"], "sourcesContent": ["export const useFeedback = jest.fn(() => ({\n  showSuccess: jest.fn(),\n  showError: jest.fn(),\n  showInfo: jest.fn(),\n  clearFeedback: jest.fn(),\n  feedback: null,\n}));\n\nexport const createRetryAction = jest.fn(() => ({\n  label: 'Retry',\n  action: jest.fn(),\n}));\n"], "version": 3}