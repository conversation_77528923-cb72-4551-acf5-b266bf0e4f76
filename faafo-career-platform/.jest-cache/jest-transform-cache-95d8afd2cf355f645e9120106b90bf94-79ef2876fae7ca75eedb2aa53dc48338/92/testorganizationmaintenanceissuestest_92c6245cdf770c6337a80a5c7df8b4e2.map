{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-organization-maintenance-issues.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;AAEH,yCAAuE;AACvE,0CAAoB;AACpB,8CAAwB;AAExB,IAAA,kBAAQ,EAAC,0CAA0C,EAAE;IACnD,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,mDAAmD,EAAE;QAC5D,IAAA,YAAE,EAAC,4DAA4D,EAAE;YAC/D,uCAAuC;YACvC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,YAAY,GAAG,EAAE,CAAC;YAExB,SAAS,wBAAwB,CAAC,GAAW;gBAC3C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,wBAAwB,CAAC,QAAQ,CAAC,CAAC;oBACrC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;wBACvD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBAEnE,8CAA8C;wBAC9C,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBACjD,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBAEjD,IAAI,gBAAgB,EAAE,CAAC;4BACrB,YAAY,CAAC,IAAI,CAAC;gCAChB,IAAI,EAAE,QAAQ;gCACd,KAAK,EAAE,yCAAyC;6BACjD,CAAC,CAAC;wBACL,CAAC;wBAED,yCAAyC;wBACzC,IAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;wBAEzD,qDAAqD;wBACrD,IAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;wBAC5C,IAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACjD,IAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;wBAE7C,IAAI,aAAa,EAAE,CAAC;4BAClB,YAAY,CAAC,IAAI,CAAC;gCAChB,IAAI,EAAE,QAAQ;gCACd,KAAK,EAAE,6DAA6D;6BACrE,CAAC,CAAC;wBACL,CAAC;wBAED,+BAA+B;wBAC/B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACxB,YAAY,CAAC,IAAI,CAAC;gCAChB,IAAI,EAAE,QAAQ;gCACd,KAAK,EAAE,uCAAuC;6BAC/C,CAAC,CAAC;wBACL,CAAC;wBAED,uCAAuC;wBACvC,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,8BAA8B;4BAC9B,IAAM,aAAa,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAClE,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAExD,IAAI,SAAS,GAAG,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;gCACzC,YAAY,CAAC,IAAI,CAAC;oCAChB,IAAI,EAAE,QAAQ;oCACd,KAAK,EAAE,wCAAwC;iCAChD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,wBAAwB,CAAC,aAAa,CAAC,CAAC;YAExC,6DAA6D;YAC7D,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,8DAA8D,EAAE;YACjE,iCAAiC;YACjC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,SAAS,uBAAuB,CAAC,GAAW;gBAC1C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,uBAAuB,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,4BAA4B;4BAC5B,IAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,IAAI,EAAE,CAAC;4BAC9E,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,IAAI,EAAE,CAAC;4BAElE,oCAAoC;4BACpC,eAAe,CAAC,OAAO,CAAC,UAAA,KAAK;gCAC3B,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;gCAE1D,IAAI,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;oCAC5B,iBAAiB,CAAC,IAAI,CAAC;wCACrB,IAAI,EAAE,QAAQ;wCACd,IAAI,EAAE,UAAU;wCAChB,WAAW,aAAA;wCACX,KAAK,EAAE,uBAAuB;qCAC/B,CAAC,CAAC;gCACL,CAAC;gCAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oCACjC,iBAAiB,CAAC,IAAI,CAAC;wCACrB,IAAI,EAAE,QAAQ;wCACd,IAAI,EAAE,UAAU;wCAChB,WAAW,aAAA;wCACX,KAAK,EAAE,8CAA8C;qCACtD,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,CAAC,CAAC;4BAEH,0BAA0B;4BAC1B,SAAS,CAAC,OAAO,CAAC,UAAA,KAAK;gCACrB,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;gCAE1D,IAAI,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;oCAC5B,iBAAiB,CAAC,IAAI,CAAC;wCACrB,IAAI,EAAE,QAAQ;wCACd,IAAI,EAAE,IAAI;wCACV,WAAW,aAAA;wCACX,KAAK,EAAE,4BAA4B;qCACpC,CAAC,CAAC;gCACL,CAAC;gCAED,+CAA+C;gCAC/C,IAAM,mBAAmB,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;gCACxE,IAAM,sBAAsB,GAAG,mBAAmB,CAAC,IAAI,CAAC,UAAA,IAAI;oCAC1D,OAAA,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;gCAAxC,CAAwC,CACzC,CAAC;gCAEF,IAAI,sBAAsB,EAAE,CAAC;oCAC3B,iBAAiB,CAAC,IAAI,CAAC;wCACrB,IAAI,EAAE,QAAQ;wCACd,IAAI,EAAE,IAAI;wCACV,WAAW,aAAA;wCACX,KAAK,EAAE,gDAAgD;qCACxD,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,CAAC,CAAC;wBACL,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAEvC,qEAAqE;YACrE,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,iDAAiD,EAAE;QAC1D,IAAA,YAAE,EAAC,iEAAiE,EAAE;YACpE,+BAA+B;YAC/B,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,mBAAmB,GAAG,EAAE,CAAC;YAE/B,SAAS,wBAAwB,CAAC,GAAW;gBAC3C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,wBAAwB,CAAC,QAAQ,CAAC,CAAC;oBACrC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,sCAAsC;4BACtC,IAAM,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;4BAC5E,IAAI,CAAC,aAAa,EAAE,CAAC;gCACnB,mBAAmB,CAAC,IAAI,CAAC;oCACvB,IAAI,EAAE,QAAQ;oCACd,KAAK,EAAE,mCAAmC;iCAC3C,CAAC,CAAC;4BACL,CAAC;4BAED,uCAAuC;4BACvC,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACxD,IAAM,YAAY,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAEvE,+CAA+C;4BAC/C,IAAM,YAAY,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;4BAClE,IAAI,SAAS,GAAG,CAAC,IAAI,YAAY,GAAG,GAAG,EAAE,CAAC;gCACxC,mBAAmB,CAAC,IAAI,CAAC;oCACvB,IAAI,EAAE,QAAQ;oCACd,SAAS,WAAA;oCACT,YAAY,cAAA;oCACZ,KAAK,EAAE,6CAA6C;iCACrD,CAAC,CAAC;4BACL,CAAC;4BAED,yCAAyC;4BACzC,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;4BACrD,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;4BAEnD,IAAI,CAAC,aAAa,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gCACxG,mBAAmB,CAAC,IAAI,CAAC;oCACvB,IAAI,EAAE,QAAQ;oCACd,KAAK,EAAE,iDAAiD;iCACzD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,wBAAwB,CAAC,aAAa,CAAC,CAAC;YAExC,kEAAkE;YAClE,IAAA,gBAAM,EAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,6EAA6E,EAAE;YAChF,uCAAuC;YACvC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,0BAA0B,GAAG,EAAE,CAAC;YAEtC,SAAS,kBAAkB,CAAC,GAAW;gBACrC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,kBAAkB,CAAC,QAAQ,CAAC,CAAC;oBAC/B,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,kCAAkC;4BAClC,IAAM,iBAAe,GAAG;gCACtB,QAAQ;gCACR,QAAQ;gCACR,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,WAAW;gCACX,UAAU;6BACX,CAAC;4BAEF,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,IAAI,EAAE,CAAC;4BAElE,SAAS,CAAC,OAAO,CAAC,UAAA,KAAK;gCACrB,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;gCAE1D,IAAM,iBAAiB,GAAG,iBAAe,CAAC,IAAI,CAAC,UAAA,OAAO;oCACpD,OAAA,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;gCAA3C,CAA2C,CAC5C,CAAC;gCAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;oCACvB,0BAA0B,CAAC,IAAI,CAAC;wCAC9B,IAAI,EAAE,QAAQ;wCACd,WAAW,aAAA;wCACX,KAAK,EAAE,gDAAgD;qCACxD,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,CAAC,CAAC;4BAEH,2CAA2C;4BAC3C,IAAM,WAAW,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAC9D,IAAM,qBAAqB,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAE1F,6CAA6C;4BAC7C,IAAI,WAAW,GAAG,EAAE,IAAI,qBAAqB,GAAG,WAAW,GAAG,GAAG,EAAE,CAAC;gCAClE,0BAA0B,CAAC,IAAI,CAAC;oCAC9B,IAAI,EAAE,QAAQ;oCACd,WAAW,aAAA;oCACX,qBAAqB,uBAAA;oCACrB,KAAK,EAAE,gDAAgD;iCACxD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAElC,8DAA8D;YAC9D,IAAA,gBAAM,EAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,wDAAwD,EAAE;QACjE,IAAA,YAAE,EAAC,0EAA0E,EAAE;YAC7E,oCAAoC;YACpC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,kBAAkB,GAAG,EAAE,CAAC;YAE9B,IAAI,YAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBACjC,IAAM,aAAa,GAAG,YAAE,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAM,aAAW,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,IAAI;oBAC3C,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;oBAChD,OAAO,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7C,CAAC,CAAC,CAAC;gBAEH,IAAM,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,IAAI;oBACzC,OAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAAvD,CAAuD,CACxD,CAAC;gBAEF,kDAAkD;gBAClD,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,IAAI,aAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpD,kBAAkB,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;gBAC/F,CAAC;gBAED,6BAA6B;gBAC7B,IAAM,mBAAmB,GAAG;oBAC1B,YAAY;oBACZ,KAAK;oBACL,UAAU;oBACV,OAAO;oBACP,aAAa;oBACb,MAAM;iBACP,CAAC;gBAEF,IAAM,kBAAkB,GAAG,mBAAmB,CAAC,MAAM,CAAC,UAAA,GAAG;oBACvD,OAAA,CAAC,aAAW,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAA1B,CAA0B,CAC3B,CAAC;gBAEF,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClC,kBAAkB,CAAC,IAAI,CAAC,6CAAsC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;gBACjG,CAAC;gBAED,+CAA+C;gBAC/C,aAAW,CAAC,OAAO,CAAC,UAAA,GAAG;oBACrB,IAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;oBAC9C,IAAM,QAAQ,GAAG,YAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAEzC,IAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAA,IAAI;wBACpC,OAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAAhD,CAAgD,CACjD,CAAC;oBACF,IAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAA,IAAI;wBAC3C,OAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;oBAA9D,CAA8D,CAC/D,CAAC;oBAEF,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACxD,kBAAkB,CAAC,IAAI,CAAC,oBAAa,GAAG,sCAAmC,CAAC,CAAC;oBAC/E,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,uDAAuD;YACvD,IAAA,gBAAM,EAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,kEAAkE,EAAE;YACrE,+CAA+C;YAC/C,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,SAAS,oBAAoB,CAAC,GAAW;gBACvC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,oBAAoB,CAAC,QAAQ,CAAC,CAAC;oBACjC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,+CAA+C;4BAC/C,IAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;4BAEzC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;gCACvC,IAAM,iBAAiB,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gCACrF,IAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gCAC/E,IAAM,gBAAgB,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gCAEpF,IAAM,WAAW,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;gCAE5D,0CAA0C;gCAC1C,IAAI,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,IAAI,aAAa,IAAI,gBAAgB,CAAC,EAAE,CAAC;oCACjF,eAAe,CAAC,IAAI,CAAC;wCACnB,IAAI,EAAE,QAAQ;wCACd,SAAS,EAAE,KAAK,GAAG,CAAC;wCACpB,KAAK,EAAE,yCAAyC;qCACjD,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,CAAC,CAAC;4BAEH,gDAAgD;4BAChD,IAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC,IAAI,EAAE,CAAC;4BACjF,IAAM,mBAAmB,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAEjF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,mBAAmB,KAAK,CAAC,EAAE,CAAC;gCAC3D,eAAe,CAAC,IAAI,CAAC;oCACnB,IAAI,EAAE,QAAQ;oCACd,KAAK,EAAE,kDAAkD;iCAC1D,CAAC,CAAC;4BACL,CAAC;4BAED,uCAAuC;4BACvC,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;4BACrD,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;4BACnD,IAAM,mBAAmB,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;4BAEnF,IAAI,mBAAmB,IAAI,aAAa,IAAI,CAAC,YAAY,EAAE,CAAC;gCAC1D,eAAe,CAAC,IAAI,CAAC;oCACnB,IAAI,EAAE,QAAQ;oCACd,KAAK,EAAE,qDAAqD;iCAC7D,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAEpC,wDAAwD;YACxD,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2DAA2D,EAAE;QACpE,IAAA,YAAE,EAAC,mEAAmE,EAAE;YACtE,sCAAsC;YACtC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,SAAS,sBAAsB,CAAC,GAAW;gBACzC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,sBAAsB,CAAC,QAAQ,CAAC,CAAC;oBACnC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,SAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,gCAAgC;4BAChC,IAAM,aAAa,GAAG;gCACpB,wBAAwB,EAAE,kBAAkB;gCAC5C,iCAAiC,EAAE,qBAAqB;gCACxD,2BAA2B,CAAC,eAAe;6BAC5C,CAAC;4BAEF,IAAI,kBAAgB,GAAG,CAAC,CAAC;4BACzB,aAAa,CAAC,OAAO,CAAC,UAAA,OAAO;gCAC3B,IAAM,OAAO,GAAG,SAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gCAC7C,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oCACvB,kBAAgB,IAAI,OAAO,CAAC,MAAM,CAAC;gCACrC,CAAC;4BACH,CAAC,CAAC,CAAC;4BAEH,IAAI,kBAAgB,GAAG,EAAE,EAAE,CAAC;gCAC1B,iBAAiB,CAAC,IAAI,CAAC;oCACrB,IAAI,EAAE,QAAQ;oCACd,gBAAgB,oBAAA;oCAChB,KAAK,EAAE,gCAAgC;iCACxC,CAAC,CAAC;4BACL,CAAC;4BAED,qCAAqC;4BACrC,IAAM,SAAS,GAAG,CAAC,SAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACxD,IAAM,eAAe,GAAG,CAAC,SAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAErF,IAAI,SAAS,GAAG,EAAE,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;gCAC1C,iBAAiB,CAAC,IAAI,CAAC;oCACrB,IAAI,EAAE,QAAQ;oCACd,SAAS,WAAA;oCACT,eAAe,iBAAA;oCACf,KAAK,EAAE,0CAA0C;iCAClD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAEtC,sDAAsD;YACtD,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,4DAA4D,EAAE;YAC/D,sDAAsD;YACtD,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,gBAAgB,GAAG,EAAE,CAAC;YAE5B,SAAS,uBAAuB,CAAC,GAAW;gBAC1C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,uBAAuB,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,SAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,8BAA8B;4BAC9B,IAAM,WAAW,GAAG,CAAC,SAAO,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAClE,IAAM,eAAe,GAAG,CAAC,SAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAEnF,IAAI,WAAW,GAAG,EAAE,IAAI,eAAe,GAAG,WAAW,GAAG,GAAG,EAAE,CAAC;gCAC5D,gBAAgB,CAAC,IAAI,CAAC;oCACpB,IAAI,EAAE,QAAQ;oCACd,WAAW,aAAA;oCACX,eAAe,iBAAA;oCACf,KAAK,EAAE,4CAA4C;iCACpD,CAAC,CAAC;4BACL,CAAC;4BAED,2CAA2C;4BAC3C,IAAM,mBAAmB,GAAG;gCAC1B,iCAAiC;gCACjC,kCAAkC;gCAClC,WAAW;gCACX,cAAc;6BACf,CAAC;4BAEF,IAAI,yBAAuB,GAAG,CAAC,CAAC;4BAChC,mBAAmB,CAAC,OAAO,CAAC,UAAA,OAAO;gCACjC,yBAAuB,IAAI,CAAC,SAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACnE,CAAC,CAAC,CAAC;4BAEH,IAAM,YAAY,GAAG,CAAC,SAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAC/D,IAAM,mBAAmB,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,yBAAuB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;4BAE1F,IAAI,mBAAmB,GAAG,GAAG,EAAE,CAAC;gCAC9B,gBAAgB,CAAC,IAAI,CAAC;oCACpB,IAAI,EAAE,QAAQ;oCACd,uBAAuB,2BAAA;oCACvB,YAAY,cAAA;oCACZ,KAAK,EAAE,6CAA6C;iCACrD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAEvC,wDAAwD;YACxD,IAAA,gBAAM,EAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-organization-maintenance-issues.test.ts"], "sourcesContent": ["/**\n * Test Organization and Maintenance Issues Tests\n * \n * These tests prove test organization issues including inconsistent naming\n * conventions, inadequate documentation, and poor test structure.\n * \n * EXPECTED TO FAIL - These tests demonstrate organization issues that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\nimport fs from 'fs';\nimport path from 'path';\n\ndescribe('Test Organization and Maintenance Issues', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Inconsistent Naming Conventions', () => {\n    it('should fail - test files have inconsistent naming patterns', () => {\n      // Analyze test file naming conventions\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const namingIssues = [];\n      \n      function analyzeNamingConventions(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeNamingConventions(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx') || \n                     file.endsWith('.spec.ts') || file.endsWith('.spec.tsx')) {\n            \n            // Check for inconsistent test file extensions\n            const hasTestExtension = file.includes('.test.');\n            const hasSpecExtension = file.includes('.spec.');\n            \n            if (hasSpecExtension) {\n              namingIssues.push({ \n                file: filePath, \n                issue: 'Uses .spec. instead of .test. extension' \n              });\n            }\n            \n            // Check for inconsistent naming patterns\n            const fileName = path.basename(file, path.extname(file));\n            \n            // Should follow kebab-case or camelCase consistently\n            const hasKebabCase = fileName.includes('-');\n            const hasCamelCase = /[a-z][A-Z]/.test(fileName);\n            const hasUnderscore = fileName.includes('_');\n            \n            if (hasUnderscore) {\n              namingIssues.push({ \n                file: filePath, \n                issue: 'Uses underscore naming (should use kebab-case or camelCase)' \n              });\n            }\n            \n            // Check for descriptive naming\n            if (fileName.length < 5) {\n              namingIssues.push({ \n                file: filePath, \n                issue: 'File name too short (not descriptive)' \n              });\n            }\n            \n            // Check for proper test file structure\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Should have describe blocks\n              const describeCount = (content.match(/describe\\(/g) || []).length;\n              const testCount = (content.match(/it\\(/g) || []).length;\n              \n              if (testCount > 5 && describeCount === 0) {\n                namingIssues.push({ \n                  file: filePath, \n                  issue: 'Multiple tests without describe blocks' \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeNamingConventions(testDirectory);\n      \n      // EXPECTED TO FAIL: Test files should have consistent naming\n      expect(namingIssues.length).toBe(0);\n    });\n\n    it('should fail - test descriptions are inconsistent and unclear', () => {\n      // Check test description quality\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const descriptionIssues = [];\n      \n      function analyzeTestDescriptions(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestDescriptions(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Extract test descriptions\n              const describeMatches = content.match(/describe\\(['\"`]([^'\"`]+)['\"`]/g) || [];\n              const itMatches = content.match(/it\\(['\"`]([^'\"`]+)['\"`]/g) || [];\n              \n              // Check describe block descriptions\n              describeMatches.forEach(match => {\n                const description = match.match(/['\"`]([^'\"`]+)['\"`]/)[1];\n                \n                if (description.length < 10) {\n                  descriptionIssues.push({ \n                    file: filePath, \n                    type: 'describe', \n                    description, \n                    issue: 'Description too short' \n                  });\n                }\n                \n                if (!description.match(/^[A-Z]/)) {\n                  descriptionIssues.push({ \n                    file: filePath, \n                    type: 'describe', \n                    description, \n                    issue: 'Description should start with capital letter' \n                  });\n                }\n              });\n              \n              // Check test descriptions\n              itMatches.forEach(match => {\n                const description = match.match(/['\"`]([^'\"`]+)['\"`]/)[1];\n                \n                if (description.length < 15) {\n                  descriptionIssues.push({ \n                    file: filePath, \n                    type: 'it', \n                    description, \n                    issue: 'Test description too short' \n                  });\n                }\n                \n                // Should describe behavior, not implementation\n                const implementationWords = ['function', 'method', 'class', 'variable'];\n                const hasImplementationFocus = implementationWords.some(word => \n                  description.toLowerCase().includes(word)\n                );\n                \n                if (hasImplementationFocus) {\n                  descriptionIssues.push({ \n                    file: filePath, \n                    type: 'it', \n                    description, \n                    issue: 'Focuses on implementation rather than behavior' \n                  });\n                }\n              });\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestDescriptions(testDirectory);\n      \n      // EXPECTED TO FAIL: Test descriptions should be clear and consistent\n      expect(descriptionIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: Inadequate Test Documentation', () => {\n    it('should fail - test files lack proper documentation and comments', () => {\n      // Check for test documentation\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const documentationIssues = [];\n      \n      function analyzeTestDocumentation(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestDocumentation(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for file header documentation\n              const hasFileHeader = content.startsWith('/**') || content.startsWith('/*');\n              if (!hasFileHeader) {\n                documentationIssues.push({ \n                  file: filePath, \n                  issue: 'Missing file header documentation' \n                });\n              }\n              \n              // Check for complex test documentation\n              const testCount = (content.match(/it\\(/g) || []).length;\n              const commentCount = (content.match(/\\/\\*\\*|\\/\\*|\\/\\//g) || []).length;\n              \n              // Should have reasonable comment-to-test ratio\n              const commentRatio = testCount > 0 ? commentCount / testCount : 0;\n              if (testCount > 5 && commentRatio < 0.3) {\n                documentationIssues.push({ \n                  file: filePath, \n                  testCount, \n                  commentCount, \n                  issue: 'Insufficient comments for complex test file' \n                });\n              }\n              \n              // Check for setup/teardown documentation\n              const hasBeforeEach = content.includes('beforeEach');\n              const hasAfterEach = content.includes('afterEach');\n              \n              if ((hasBeforeEach || hasAfterEach) && !content.includes('// Setup') && !content.includes('// Cleanup')) {\n                documentationIssues.push({ \n                  file: filePath, \n                  issue: 'Setup/teardown blocks lack explanatory comments' \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestDocumentation(testDirectory);\n      \n      // EXPECTED TO FAIL: Test files should have adequate documentation\n      expect(documentationIssues.length).toBe(0);\n    });\n\n    it('should fail - test purpose and expected outcomes are not clearly documented', () => {\n      // Check for test purpose documentation\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const purposeDocumentationIssues = [];\n      \n      function analyzeTestPurpose(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestPurpose(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for test purpose keywords\n              const purposeKeywords = [\n                'should',\n                'expect',\n                'verify',\n                'ensure',\n                'validate',\n                'test that',\n                'check if'\n              ];\n              \n              const itMatches = content.match(/it\\(['\"`]([^'\"`]+)['\"`]/g) || [];\n              \n              itMatches.forEach(match => {\n                const description = match.match(/['\"`]([^'\"`]+)['\"`]/)[1];\n                \n                const hasPurposeKeyword = purposeKeywords.some(keyword => \n                  description.toLowerCase().includes(keyword)\n                );\n                \n                if (!hasPurposeKeyword) {\n                  purposeDocumentationIssues.push({ \n                    file: filePath, \n                    description, \n                    issue: 'Test description lacks clear purpose statement' \n                  });\n                }\n              });\n              \n              // Check for expected outcome documentation\n              const expectCount = (content.match(/expect\\(/g) || []).length;\n              const commentedExpectations = (content.match(/\\/\\/.*expect|\\/\\*.*expect/gi) || []).length;\n              \n              // Complex tests should document expectations\n              if (expectCount > 10 && commentedExpectations < expectCount * 0.2) {\n                purposeDocumentationIssues.push({ \n                  file: filePath, \n                  expectCount, \n                  commentedExpectations, \n                  issue: 'Complex expectations lack explanatory comments' \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestPurpose(testDirectory);\n      \n      // EXPECTED TO FAIL: Test purpose should be clearly documented\n      expect(purposeDocumentationIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: Poor Test Structure and Organization', () => {\n    it('should fail - test files are not properly organized by feature or domain', () => {\n      // Check test organization structure\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const organizationIssues = [];\n      \n      if (fs.existsSync(testDirectory)) {\n        const topLevelItems = fs.readdirSync(testDirectory);\n        const directories = topLevelItems.filter(item => {\n          const itemPath = path.join(testDirectory, item);\n          return fs.statSync(itemPath).isDirectory();\n        });\n        \n        const testFiles = topLevelItems.filter(item => \n          item.endsWith('.test.ts') || item.endsWith('.test.tsx')\n        );\n        \n        // If many test files in root without organization\n        if (testFiles.length > 10 && directories.length < 3) {\n          organizationIssues.push('Too many test files in root directory without proper organization');\n        }\n        \n        // Check for logical grouping\n        const expectedDirectories = [\n          'components',\n          'api',\n          'services',\n          'utils',\n          'integration',\n          'unit'\n        ];\n        \n        const missingDirectories = expectedDirectories.filter(dir => \n          !directories.includes(dir)\n        );\n        \n        if (missingDirectories.length > 4) {\n          organizationIssues.push(`Missing expected test directories: ${missingDirectories.join(', ')}`);\n        }\n        \n        // Check for mixed test types in same directory\n        directories.forEach(dir => {\n          const dirPath = path.join(testDirectory, dir);\n          const dirFiles = fs.readdirSync(dirPath);\n          \n          const unitTests = dirFiles.filter(file => \n            file.includes('unit') || file.includes('.test.')\n          );\n          const integrationTests = dirFiles.filter(file => \n            file.includes('integration') || file.includes('.integration.')\n          );\n          \n          if (unitTests.length > 0 && integrationTests.length > 0) {\n            organizationIssues.push(`Directory ${dir} mixes unit and integration tests`);\n          }\n        });\n      }\n      \n      // EXPECTED TO FAIL: Tests should be properly organized\n      expect(organizationIssues.length).toBe(0);\n    });\n\n    it('should fail - test structure does not follow consistent patterns', () => {\n      // Check for consistent test structure patterns\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const structureIssues = [];\n      \n      function analyzeTestStructure(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestStructure(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for AAA pattern (Arrange, Act, Assert)\n              const testBlocks = content.split(/it\\(/);\n              \n              testBlocks.slice(1).forEach((block, index) => {\n                const hasArrangeComment = block.includes('// Arrange') || block.includes('// Setup');\n                const hasActComment = block.includes('// Act') || block.includes('// Execute');\n                const hasAssertComment = block.includes('// Assert') || block.includes('// Verify');\n                \n                const expectCount = (block.match(/expect\\(/g) || []).length;\n                \n                // Complex tests should follow AAA pattern\n                if (expectCount > 3 && !(hasArrangeComment || hasActComment || hasAssertComment)) {\n                  structureIssues.push({ \n                    file: filePath, \n                    testIndex: index + 1, \n                    issue: 'Complex test lacks AAA pattern comments' \n                  });\n                }\n              });\n              \n              // Check for consistent describe block structure\n              const describeBlocks = content.match(/describe\\(['\"`][^'\"`]+['\"`][^{]*{/g) || [];\n              const nestedDescribeCount = (content.match(/describe.*describe/gs) || []).length;\n              \n              if (describeBlocks.length > 3 && nestedDescribeCount === 0) {\n                structureIssues.push({ \n                  file: filePath, \n                  issue: 'Multiple describe blocks without logical nesting' \n                });\n              }\n              \n              // Check for setup/teardown consistency\n              const hasBeforeEach = content.includes('beforeEach');\n              const hasAfterEach = content.includes('afterEach');\n              const hasResourceCreation = content.includes('new ') || content.includes('create');\n              \n              if (hasResourceCreation && hasBeforeEach && !hasAfterEach) {\n                structureIssues.push({ \n                  file: filePath, \n                  issue: 'Has setup but missing teardown for resource cleanup' \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestStructure(testDirectory);\n      \n      // EXPECTED TO FAIL: Test structure should be consistent\n      expect(structureIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Test Maintenance and Refactoring Issues', () => {\n    it('should fail - tests have high duplication and low maintainability', () => {\n      // Check for code duplication in tests\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const duplicationIssues = [];\n      \n      function analyzeTestDuplication(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestDuplication(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for repeated setup code\n              const setupPatterns = [\n                /const.*=.*{[^}]{20,}}/g, // Object literals\n                /expect\\([^)]+\\)\\.toBe\\([^)]+\\)/g, // Similar assertions\n                /beforeEach.*{[^}]{50,}}/gs // Setup blocks\n              ];\n              \n              let duplicationScore = 0;\n              setupPatterns.forEach(pattern => {\n                const matches = content.match(pattern) || [];\n                if (matches.length > 3) {\n                  duplicationScore += matches.length;\n                }\n              });\n              \n              if (duplicationScore > 10) {\n                duplicationIssues.push({ \n                  file: filePath, \n                  duplicationScore, \n                  issue: 'High code duplication detected' \n                });\n              }\n              \n              // Check for lack of helper functions\n              const testCount = (content.match(/it\\(/g) || []).length;\n              const helperFunctions = (content.match(/function \\w+|const \\w+ = \\(/g) || []).length;\n              \n              if (testCount > 10 && helperFunctions < 2) {\n                duplicationIssues.push({ \n                  file: filePath, \n                  testCount, \n                  helperFunctions, \n                  issue: 'Large test file without helper functions' \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestDuplication(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should have low duplication\n      expect(duplicationIssues.length).toBe(0);\n    });\n\n    it('should fail - test dependencies make refactoring difficult', () => {\n      // Check for test dependencies that hinder refactoring\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const dependencyIssues = [];\n      \n      function analyzeTestDependencies(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestDependencies(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for hardcoded imports\n              const importCount = (content.match(/import.*from/g) || []).length;\n              const relativeImports = (content.match(/import.*from ['\"`]\\.\\.?\\//g) || []).length;\n              \n              if (importCount > 10 && relativeImports > importCount * 0.7) {\n                dependencyIssues.push({ \n                  file: filePath, \n                  importCount, \n                  relativeImports, \n                  issue: 'Too many relative imports (tight coupling)' \n                });\n              }\n              \n              // Check for implementation details testing\n              const implementationTests = [\n                /expect.*\\.toHaveBeenCalledWith/g,\n                /expect.*\\.toHaveBeenCalledTimes/g,\n                /\\.mock\\./g,\n                /jest\\.spyOn/g\n              ];\n              \n              let implementationTestCount = 0;\n              implementationTests.forEach(pattern => {\n                implementationTestCount += (content.match(pattern) || []).length;\n              });\n              \n              const totalExpects = (content.match(/expect\\(/g) || []).length;\n              const implementationRatio = totalExpects > 0 ? implementationTestCount / totalExpects : 0;\n              \n              if (implementationRatio > 0.5) {\n                dependencyIssues.push({ \n                  file: filePath, \n                  implementationTestCount, \n                  totalExpects, \n                  issue: 'Tests too focused on implementation details' \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestDependencies(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should not hinder refactoring\n      expect(dependencyIssues.length).toBe(0);\n    });\n  });\n});\n"], "version": 3}