97b1e887897ef20c2bfa43e244ef74e1
"use strict";
/**
 * Test Organization and Maintenance Issues Tests
 *
 * These tests prove test organization issues including inconsistent naming
 * conventions, inadequate documentation, and poor test structure.
 *
 * EXPECTED TO FAIL - These tests demonstrate organization issues that need fixing.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
(0, globals_1.describe)('Test Organization and Maintenance Issues', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Inconsistent Naming Conventions', function () {
        (0, globals_1.it)('should fail - test files have inconsistent naming patterns', function () {
            // Analyze test file naming conventions
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var namingIssues = [];
            function analyzeNamingConventions(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeNamingConventions(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx') ||
                        file.endsWith('.spec.ts') || file.endsWith('.spec.tsx')) {
                        // Check for inconsistent test file extensions
                        var hasTestExtension = file.includes('.test.');
                        var hasSpecExtension = file.includes('.spec.');
                        if (hasSpecExtension) {
                            namingIssues.push({
                                file: filePath,
                                issue: 'Uses .spec. instead of .test. extension'
                            });
                        }
                        // Check for inconsistent naming patterns
                        var fileName = path_1.default.basename(file, path_1.default.extname(file));
                        // Should follow kebab-case or camelCase consistently
                        var hasKebabCase = fileName.includes('-');
                        var hasCamelCase = /[a-z][A-Z]/.test(fileName);
                        var hasUnderscore = fileName.includes('_');
                        if (hasUnderscore) {
                            namingIssues.push({
                                file: filePath,
                                issue: 'Uses underscore naming (should use kebab-case or camelCase)'
                            });
                        }
                        // Check for descriptive naming
                        if (fileName.length < 5) {
                            namingIssues.push({
                                file: filePath,
                                issue: 'File name too short (not descriptive)'
                            });
                        }
                        // Check for proper test file structure
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Should have describe blocks
                            var describeCount = (content.match(/describe\(/g) || []).length;
                            var testCount = (content.match(/it\(/g) || []).length;
                            if (testCount > 5 && describeCount === 0) {
                                namingIssues.push({
                                    file: filePath,
                                    issue: 'Multiple tests without describe blocks'
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeNamingConventions(testDirectory);
            // EXPECTED TO FAIL: Test files should have consistent naming
            (0, globals_1.expect)(namingIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test descriptions are inconsistent and unclear', function () {
            // Check test description quality
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var descriptionIssues = [];
            function analyzeTestDescriptions(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestDescriptions(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Extract test descriptions
                            var describeMatches = content.match(/describe\(['"`]([^'"`]+)['"`]/g) || [];
                            var itMatches = content.match(/it\(['"`]([^'"`]+)['"`]/g) || [];
                            // Check describe block descriptions
                            describeMatches.forEach(function (match) {
                                var description = match.match(/['"`]([^'"`]+)['"`]/)[1];
                                if (description.length < 10) {
                                    descriptionIssues.push({
                                        file: filePath,
                                        type: 'describe',
                                        description: description,
                                        issue: 'Description too short'
                                    });
                                }
                                if (!description.match(/^[A-Z]/)) {
                                    descriptionIssues.push({
                                        file: filePath,
                                        type: 'describe',
                                        description: description,
                                        issue: 'Description should start with capital letter'
                                    });
                                }
                            });
                            // Check test descriptions
                            itMatches.forEach(function (match) {
                                var description = match.match(/['"`]([^'"`]+)['"`]/)[1];
                                if (description.length < 15) {
                                    descriptionIssues.push({
                                        file: filePath,
                                        type: 'it',
                                        description: description,
                                        issue: 'Test description too short'
                                    });
                                }
                                // Should describe behavior, not implementation
                                var implementationWords = ['function', 'method', 'class', 'variable'];
                                var hasImplementationFocus = implementationWords.some(function (word) {
                                    return description.toLowerCase().includes(word);
                                });
                                if (hasImplementationFocus) {
                                    descriptionIssues.push({
                                        file: filePath,
                                        type: 'it',
                                        description: description,
                                        issue: 'Focuses on implementation rather than behavior'
                                    });
                                }
                            });
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestDescriptions(testDirectory);
            // EXPECTED TO FAIL: Test descriptions should be clear and consistent
            (0, globals_1.expect)(descriptionIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Inadequate Test Documentation', function () {
        (0, globals_1.it)('should fail - test files lack proper documentation and comments', function () {
            // Check for test documentation
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var documentationIssues = [];
            function analyzeTestDocumentation(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestDocumentation(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for file header documentation
                            var hasFileHeader = content.startsWith('/**') || content.startsWith('/*');
                            if (!hasFileHeader) {
                                documentationIssues.push({
                                    file: filePath,
                                    issue: 'Missing file header documentation'
                                });
                            }
                            // Check for complex test documentation
                            var testCount = (content.match(/it\(/g) || []).length;
                            var commentCount = (content.match(/\/\*\*|\/\*|\/\//g) || []).length;
                            // Should have reasonable comment-to-test ratio
                            var commentRatio = testCount > 0 ? commentCount / testCount : 0;
                            if (testCount > 5 && commentRatio < 0.3) {
                                documentationIssues.push({
                                    file: filePath,
                                    testCount: testCount,
                                    commentCount: commentCount,
                                    issue: 'Insufficient comments for complex test file'
                                });
                            }
                            // Check for setup/teardown documentation
                            var hasBeforeEach = content.includes('beforeEach');
                            var hasAfterEach = content.includes('afterEach');
                            if ((hasBeforeEach || hasAfterEach) && !content.includes('// Setup') && !content.includes('// Cleanup')) {
                                documentationIssues.push({
                                    file: filePath,
                                    issue: 'Setup/teardown blocks lack explanatory comments'
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestDocumentation(testDirectory);
            // EXPECTED TO FAIL: Test files should have adequate documentation
            (0, globals_1.expect)(documentationIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test purpose and expected outcomes are not clearly documented', function () {
            // Check for test purpose documentation
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var purposeDocumentationIssues = [];
            function analyzeTestPurpose(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestPurpose(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for test purpose keywords
                            var purposeKeywords_1 = [
                                'should',
                                'expect',
                                'verify',
                                'ensure',
                                'validate',
                                'test that',
                                'check if'
                            ];
                            var itMatches = content.match(/it\(['"`]([^'"`]+)['"`]/g) || [];
                            itMatches.forEach(function (match) {
                                var description = match.match(/['"`]([^'"`]+)['"`]/)[1];
                                var hasPurposeKeyword = purposeKeywords_1.some(function (keyword) {
                                    return description.toLowerCase().includes(keyword);
                                });
                                if (!hasPurposeKeyword) {
                                    purposeDocumentationIssues.push({
                                        file: filePath,
                                        description: description,
                                        issue: 'Test description lacks clear purpose statement'
                                    });
                                }
                            });
                            // Check for expected outcome documentation
                            var expectCount = (content.match(/expect\(/g) || []).length;
                            var commentedExpectations = (content.match(/\/\/.*expect|\/\*.*expect/gi) || []).length;
                            // Complex tests should document expectations
                            if (expectCount > 10 && commentedExpectations < expectCount * 0.2) {
                                purposeDocumentationIssues.push({
                                    file: filePath,
                                    expectCount: expectCount,
                                    commentedExpectations: commentedExpectations,
                                    issue: 'Complex expectations lack explanatory comments'
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestPurpose(testDirectory);
            // EXPECTED TO FAIL: Test purpose should be clearly documented
            (0, globals_1.expect)(purposeDocumentationIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Poor Test Structure and Organization', function () {
        (0, globals_1.it)('should fail - test files are not properly organized by feature or domain', function () {
            // Check test organization structure
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var organizationIssues = [];
            if (fs_1.default.existsSync(testDirectory)) {
                var topLevelItems = fs_1.default.readdirSync(testDirectory);
                var directories_1 = topLevelItems.filter(function (item) {
                    var itemPath = path_1.default.join(testDirectory, item);
                    return fs_1.default.statSync(itemPath).isDirectory();
                });
                var testFiles = topLevelItems.filter(function (item) {
                    return item.endsWith('.test.ts') || item.endsWith('.test.tsx');
                });
                // If many test files in root without organization
                if (testFiles.length > 10 && directories_1.length < 3) {
                    organizationIssues.push('Too many test files in root directory without proper organization');
                }
                // Check for logical grouping
                var expectedDirectories = [
                    'components',
                    'api',
                    'services',
                    'utils',
                    'integration',
                    'unit'
                ];
                var missingDirectories = expectedDirectories.filter(function (dir) {
                    return !directories_1.includes(dir);
                });
                if (missingDirectories.length > 4) {
                    organizationIssues.push("Missing expected test directories: ".concat(missingDirectories.join(', ')));
                }
                // Check for mixed test types in same directory
                directories_1.forEach(function (dir) {
                    var dirPath = path_1.default.join(testDirectory, dir);
                    var dirFiles = fs_1.default.readdirSync(dirPath);
                    var unitTests = dirFiles.filter(function (file) {
                        return file.includes('unit') || file.includes('.test.');
                    });
                    var integrationTests = dirFiles.filter(function (file) {
                        return file.includes('integration') || file.includes('.integration.');
                    });
                    if (unitTests.length > 0 && integrationTests.length > 0) {
                        organizationIssues.push("Directory ".concat(dir, " mixes unit and integration tests"));
                    }
                });
            }
            // EXPECTED TO FAIL: Tests should be properly organized
            (0, globals_1.expect)(organizationIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test structure does not follow consistent patterns', function () {
            // Check for consistent test structure patterns
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var structureIssues = [];
            function analyzeTestStructure(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestStructure(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for AAA pattern (Arrange, Act, Assert)
                            var testBlocks = content.split(/it\(/);
                            testBlocks.slice(1).forEach(function (block, index) {
                                var hasArrangeComment = block.includes('// Arrange') || block.includes('// Setup');
                                var hasActComment = block.includes('// Act') || block.includes('// Execute');
                                var hasAssertComment = block.includes('// Assert') || block.includes('// Verify');
                                var expectCount = (block.match(/expect\(/g) || []).length;
                                // Complex tests should follow AAA pattern
                                if (expectCount > 3 && !(hasArrangeComment || hasActComment || hasAssertComment)) {
                                    structureIssues.push({
                                        file: filePath,
                                        testIndex: index + 1,
                                        issue: 'Complex test lacks AAA pattern comments'
                                    });
                                }
                            });
                            // Check for consistent describe block structure
                            var describeBlocks = content.match(/describe\(['"`][^'"`]+['"`][^{]*{/g) || [];
                            var nestedDescribeCount = (content.match(/describe.*describe/gs) || []).length;
                            if (describeBlocks.length > 3 && nestedDescribeCount === 0) {
                                structureIssues.push({
                                    file: filePath,
                                    issue: 'Multiple describe blocks without logical nesting'
                                });
                            }
                            // Check for setup/teardown consistency
                            var hasBeforeEach = content.includes('beforeEach');
                            var hasAfterEach = content.includes('afterEach');
                            var hasResourceCreation = content.includes('new ') || content.includes('create');
                            if (hasResourceCreation && hasBeforeEach && !hasAfterEach) {
                                structureIssues.push({
                                    file: filePath,
                                    issue: 'Has setup but missing teardown for resource cleanup'
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestStructure(testDirectory);
            // EXPECTED TO FAIL: Test structure should be consistent
            (0, globals_1.expect)(structureIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Test Maintenance and Refactoring Issues', function () {
        (0, globals_1.it)('should fail - tests have high duplication and low maintainability', function () {
            // Check for code duplication in tests
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var duplicationIssues = [];
            function analyzeTestDuplication(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestDuplication(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_1 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for repeated setup code
                            var setupPatterns = [
                                /const.*=.*{[^}]{20,}}/g, // Object literals
                                /expect\([^)]+\)\.toBe\([^)]+\)/g, // Similar assertions
                                /beforeEach.*{[^}]{50,}}/gs // Setup blocks
                            ];
                            var duplicationScore_1 = 0;
                            setupPatterns.forEach(function (pattern) {
                                var matches = content_1.match(pattern) || [];
                                if (matches.length > 3) {
                                    duplicationScore_1 += matches.length;
                                }
                            });
                            if (duplicationScore_1 > 10) {
                                duplicationIssues.push({
                                    file: filePath,
                                    duplicationScore: duplicationScore_1,
                                    issue: 'High code duplication detected'
                                });
                            }
                            // Check for lack of helper functions
                            var testCount = (content_1.match(/it\(/g) || []).length;
                            var helperFunctions = (content_1.match(/function \w+|const \w+ = \(/g) || []).length;
                            if (testCount > 10 && helperFunctions < 2) {
                                duplicationIssues.push({
                                    file: filePath,
                                    testCount: testCount,
                                    helperFunctions: helperFunctions,
                                    issue: 'Large test file without helper functions'
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestDuplication(testDirectory);
            // EXPECTED TO FAIL: Tests should have low duplication
            (0, globals_1.expect)(duplicationIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test dependencies make refactoring difficult', function () {
            // Check for test dependencies that hinder refactoring
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var dependencyIssues = [];
            function analyzeTestDependencies(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestDependencies(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_2 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for hardcoded imports
                            var importCount = (content_2.match(/import.*from/g) || []).length;
                            var relativeImports = (content_2.match(/import.*from ['"`]\.\.?\//g) || []).length;
                            if (importCount > 10 && relativeImports > importCount * 0.7) {
                                dependencyIssues.push({
                                    file: filePath,
                                    importCount: importCount,
                                    relativeImports: relativeImports,
                                    issue: 'Too many relative imports (tight coupling)'
                                });
                            }
                            // Check for implementation details testing
                            var implementationTests = [
                                /expect.*\.toHaveBeenCalledWith/g,
                                /expect.*\.toHaveBeenCalledTimes/g,
                                /\.mock\./g,
                                /jest\.spyOn/g
                            ];
                            var implementationTestCount_1 = 0;
                            implementationTests.forEach(function (pattern) {
                                implementationTestCount_1 += (content_2.match(pattern) || []).length;
                            });
                            var totalExpects = (content_2.match(/expect\(/g) || []).length;
                            var implementationRatio = totalExpects > 0 ? implementationTestCount_1 / totalExpects : 0;
                            if (implementationRatio > 0.5) {
                                dependencyIssues.push({
                                    file: filePath,
                                    implementationTestCount: implementationTestCount_1,
                                    totalExpects: totalExpects,
                                    issue: 'Tests too focused on implementation details'
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestDependencies(testDirectory);
            // EXPECTED TO FAIL: Tests should not hinder refactoring
            (0, globals_1.expect)(dependencyIssues.length).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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