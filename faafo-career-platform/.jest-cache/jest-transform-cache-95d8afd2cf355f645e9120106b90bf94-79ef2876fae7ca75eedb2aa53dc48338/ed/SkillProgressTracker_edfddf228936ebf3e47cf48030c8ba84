8517f8db86c4bca9ef5a0a73601c611d
"use strict";
'use client';

/* istanbul ignore next */
function cov_2038wth7ek() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillProgressTracker.tsx";
  var hash = "573849b00fd62e65b3c17a4af778a0ffa9dfa2b3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillProgressTracker.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 5,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 62
        }
      },
      "2": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 62
        }
      },
      "3": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 39
        }
      },
      "4": {
        start: {
          line: 8,
          column: 20
        },
        end: {
          line: 8,
          column: 48
        }
      },
      "5": {
        start: {
          line: 9,
          column: 14
        },
        end: {
          line: 9,
          column: 47
        }
      },
      "6": {
        start: {
          line: 10,
          column: 13
        },
        end: {
          line: 10,
          column: 44
        }
      },
      "7": {
        start: {
          line: 11,
          column: 17
        },
        end: {
          line: 11,
          column: 52
        }
      },
      "8": {
        start: {
          line: 12,
          column: 14
        },
        end: {
          line: 12,
          column: 46
        }
      },
      "9": {
        start: {
          line: 13,
          column: 21
        },
        end: {
          line: 13,
          column: 44
        }
      },
      "10": {
        start: {
          line: 15,
          column: 17
        },
        end: {
          line: 15,
          column: 26
        }
      },
      "11": {
        start: {
          line: 15,
          column: 33
        },
        end: {
          line: 15,
          column: 41
        }
      },
      "12": {
        start: {
          line: 15,
          column: 51
        },
        end: {
          line: 15,
          column: 100
        }
      },
      "13": {
        start: {
          line: 15,
          column: 107
        },
        end: {
          line: 15,
          column: 121
        }
      },
      "14": {
        start: {
          line: 15,
          column: 137
        },
        end: {
          line: 15,
          column: 216
        }
      },
      "15": {
        start: {
          line: 15,
          column: 223
        },
        end: {
          line: 15,
          column: 240
        }
      },
      "16": {
        start: {
          line: 15,
          column: 259
        },
        end: {
          line: 15,
          column: 284
        }
      },
      "17": {
        start: {
          line: 15,
          column: 291
        },
        end: {
          line: 15,
          column: 309
        }
      },
      "18": {
        start: {
          line: 15,
          column: 329
        },
        end: {
          line: 15,
          column: 355
        }
      },
      "19": {
        start: {
          line: 16,
          column: 24
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "20": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 26,
          column: 9
        }
      },
      "21": {
        start: {
          line: 19,
          column: 16
        },
        end: {
          line: 19,
          column: 115
        }
      },
      "22": {
        start: {
          line: 21,
          column: 16
        },
        end: {
          line: 21,
          column: 108
        }
      },
      "23": {
        start: {
          line: 23,
          column: 16
        },
        end: {
          line: 23,
          column: 113
        }
      },
      "24": {
        start: {
          line: 25,
          column: 16
        },
        end: {
          line: 25,
          column: 109
        }
      },
      "25": {
        start: {
          line: 28,
          column: 25
        },
        end: {
          line: 39,
          column: 5
        }
      },
      "26": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 38,
          column: 9
        }
      },
      "27": {
        start: {
          line: 31,
          column: 16
        },
        end: {
          line: 31,
          column: 94
        }
      },
      "28": {
        start: {
          line: 33,
          column: 16
        },
        end: {
          line: 33,
          column: 90
        }
      },
      "29": {
        start: {
          line: 35,
          column: 16
        },
        end: {
          line: 35,
          column: 86
        }
      },
      "30": {
        start: {
          line: 37,
          column: 16
        },
        end: {
          line: 37,
          column: 90
        }
      },
      "31": {
        start: {
          line: 40,
          column: 27
        },
        end: {
          line: 51,
          column: 5
        }
      },
      "32": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 50,
          column: 9
        }
      },
      "33": {
        start: {
          line: 43,
          column: 16
        },
        end: {
          line: 43,
          column: 86
        }
      },
      "34": {
        start: {
          line: 45,
          column: 16
        },
        end: {
          line: 45,
          column: 98
        }
      },
      "35": {
        start: {
          line: 47,
          column: 16
        },
        end: {
          line: 47,
          column: 98
        }
      },
      "36": {
        start: {
          line: 49,
          column: 16
        },
        end: {
          line: 49,
          column: 90
        }
      },
      "37": {
        start: {
          line: 52,
          column: 27
        },
        end: {
          line: 64,
          column: 5
        }
      },
      "38": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 54,
          column: 34
        }
      },
      "39": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 54,
          column: 34
        }
      },
      "40": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 56,
          column: 32
        }
      },
      "41": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 32
        }
      },
      "42": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 58,
          column: 34
        }
      },
      "43": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 34
        }
      },
      "44": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 60,
          column: 33
        }
      },
      "45": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 60,
          column: 33
        }
      },
      "46": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 62,
          column: 35
        }
      },
      "47": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 35
        }
      },
      "48": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 29
        }
      },
      "49": {
        start: {
          line: 65,
          column: 24
        },
        end: {
          line: 73,
          column: 34
        }
      },
      "50": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 69,
          column: 13
        }
      },
      "51": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 68,
          column: 41
        }
      },
      "52": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 70,
          column: 44
        }
      },
      "53": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 71,
          column: 23
        }
      },
      "54": {
        start: {
          line: 74,
          column: 26
        },
        end: {
          line: 76,
          column: 11
        }
      },
      "55": {
        start: {
          line: 75,
          column: 48
        },
        end: {
          line: 75,
          column: 76
        }
      },
      "56": {
        start: {
          line: 77,
          column: 26
        },
        end: {
          line: 77,
          column: 105
        }
      },
      "57": {
        start: {
          line: 77,
          column: 59
        },
        end: {
          line: 77,
          column: 95
        }
      },
      "58": {
        start: {
          line: 78,
          column: 23
        },
        end: {
          line: 78,
          column: 100
        }
      },
      "59": {
        start: {
          line: 78,
          column: 56
        },
        end: {
          line: 78,
          column: 90
        }
      },
      "60": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 84,
          column: 27
        }
      },
      "61": {
        start: {
          line: 80,
          column: 35
        },
        end: {
          line: 80,
          column: 40
        }
      },
      "62": {
        start: {
          line: 80,
          column: 59
        },
        end: {
          line: 80,
          column: 64
        }
      },
      "63": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 83,
          column: 801
        }
      },
      "64": {
        start: {
          line: 81,
          column: 358
        },
        end: {
          line: 83,
          column: 779
        }
      },
      "65": {
        start: {
          line: 81,
          column: 2229
        },
        end: {
          line: 83,
          column: 581
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 57
          }
        },
        loc: {
          start: {
            line: 3,
            column: 71
          },
          end: {
            line: 5,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "SkillProgressTracker",
        decl: {
          start: {
            line: 14,
            column: 9
          },
          end: {
            line: 14,
            column: 29
          }
        },
        loc: {
          start: {
            line: 14,
            column: 34
          },
          end: {
            line: 85,
            column: 1
          }
        },
        line: 14
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 16,
            column: 24
          },
          end: {
            line: 16,
            column: 25
          }
        },
        loc: {
          start: {
            line: 16,
            column: 42
          },
          end: {
            line: 27,
            column: 5
          }
        },
        line: 16
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 28,
            column: 25
          },
          end: {
            line: 28,
            column: 26
          }
        },
        loc: {
          start: {
            line: 28,
            column: 43
          },
          end: {
            line: 39,
            column: 5
          }
        },
        line: 28
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 40,
            column: 27
          },
          end: {
            line: 40,
            column: 28
          }
        },
        loc: {
          start: {
            line: 40,
            column: 47
          },
          end: {
            line: 51,
            column: 5
          }
        },
        line: 40
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 52,
            column: 27
          },
          end: {
            line: 52,
            column: 28
          }
        },
        loc: {
          start: {
            line: 52,
            column: 55
          },
          end: {
            line: 64,
            column: 5
          }
        },
        line: 52
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 66,
            column: 24
          },
          end: {
            line: 66,
            column: 25
          }
        },
        loc: {
          start: {
            line: 66,
            column: 46
          },
          end: {
            line: 72,
            column: 9
          }
        },
        line: 66
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 75,
            column: 24
          },
          end: {
            line: 75,
            column: 25
          }
        },
        loc: {
          start: {
            line: 75,
            column: 46
          },
          end: {
            line: 75,
            column: 78
          }
        },
        line: 75
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 77,
            column: 40
          },
          end: {
            line: 77,
            column: 41
          }
        },
        loc: {
          start: {
            line: 77,
            column: 57
          },
          end: {
            line: 77,
            column: 97
          }
        },
        line: 77
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 78,
            column: 37
          },
          end: {
            line: 78,
            column: 38
          }
        },
        loc: {
          start: {
            line: 78,
            column: 54
          },
          end: {
            line: 78,
            column: 92
          }
        },
        line: 78
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 79,
            column: 1783
          },
          end: {
            line: 79,
            column: 1784
          }
        },
        loc: {
          start: {
            line: 79,
            column: 1797
          },
          end: {
            line: 84,
            column: 17
          }
        },
        line: 79
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 81,
            column: 339
          },
          end: {
            line: 81,
            column: 340
          }
        },
        loc: {
          start: {
            line: 81,
            column: 356
          },
          end: {
            line: 83,
            column: 781
          }
        },
        line: 81
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 81,
            column: 2206
          },
          end: {
            line: 81,
            column: 2207
          }
        },
        loc: {
          start: {
            line: 81,
            column: 2227
          },
          end: {
            line: 83,
            column: 583
          }
        },
        line: 81
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 5,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 27
          }
        }, {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 3,
            column: 51
          }
        }, {
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 5,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 11
          },
          end: {
            line: 4,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 37
          },
          end: {
            line: 4,
            column: 40
          }
        }, {
          start: {
            line: 4,
            column: 43
          },
          end: {
            line: 4,
            column: 61
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 12
          },
          end: {
            line: 4,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 12
          },
          end: {
            line: 4,
            column: 15
          }
        }, {
          start: {
            line: 4,
            column: 19
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 15,
            column: 51
          },
          end: {
            line: 15,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 15,
            column: 67
          },
          end: {
            line: 15,
            column: 95
          }
        }, {
          start: {
            line: 15,
            column: 98
          },
          end: {
            line: 15,
            column: 100
          }
        }],
        line: 15
      },
      "4": {
        loc: {
          start: {
            line: 15,
            column: 137
          },
          end: {
            line: 15,
            column: 216
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 15,
            column: 153
          },
          end: {
            line: 15,
            column: 211
          }
        }, {
          start: {
            line: 15,
            column: 214
          },
          end: {
            line: 15,
            column: 216
          }
        }],
        line: 15
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 259
          },
          end: {
            line: 15,
            column: 284
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 15,
            column: 275
          },
          end: {
            line: 15,
            column: 279
          }
        }, {
          start: {
            line: 15,
            column: 282
          },
          end: {
            line: 15,
            column: 284
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 15,
            column: 329
          },
          end: {
            line: 15,
            column: 355
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 15,
            column: 345
          },
          end: {
            line: 15,
            column: 350
          }
        }, {
          start: {
            line: 15,
            column: 353
          },
          end: {
            line: 15,
            column: 355
          }
        }],
        line: 15
      },
      "7": {
        loc: {
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 26,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 19,
            column: 115
          }
        }, {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 21,
            column: 108
          }
        }, {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 23,
            column: 113
          }
        }, {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 25,
            column: 109
          }
        }],
        line: 17
      },
      "8": {
        loc: {
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 38,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 31,
            column: 94
          }
        }, {
          start: {
            line: 32,
            column: 12
          },
          end: {
            line: 33,
            column: 90
          }
        }, {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 35,
            column: 86
          }
        }, {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 37,
            column: 90
          }
        }],
        line: 29
      },
      "9": {
        loc: {
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 50,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 43,
            column: 86
          }
        }, {
          start: {
            line: 44,
            column: 12
          },
          end: {
            line: 45,
            column: 98
          }
        }, {
          start: {
            line: 46,
            column: 12
          },
          end: {
            line: 47,
            column: 98
          }
        }, {
          start: {
            line: 48,
            column: 12
          },
          end: {
            line: 49,
            column: 90
          }
        }],
        line: 41
      },
      "10": {
        loc: {
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 54,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 54,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "11": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 56,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 56,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "12": {
        loc: {
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 58,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 58,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "13": {
        loc: {
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 60,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 60,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "14": {
        loc: {
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 62,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 62,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "15": {
        loc: {
          start: {
            line: 65,
            column: 24
          },
          end: {
            line: 73,
            column: 34
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 66,
            column: 10
          },
          end: {
            line: 72,
            column: 14
          }
        }, {
          start: {
            line: 73,
            column: 10
          },
          end: {
            line: 73,
            column: 34
          }
        }],
        line: 65
      },
      "16": {
        loc: {
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 69,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 69,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "17": {
        loc: {
          start: {
            line: 74,
            column: 26
          },
          end: {
            line: 76,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 75,
            column: 10
          },
          end: {
            line: 75,
            column: 98
          }
        }, {
          start: {
            line: 76,
            column: 10
          },
          end: {
            line: 76,
            column: 11
          }
        }],
        line: 74
      },
      "18": {
        loc: {
          start: {
            line: 79,
            column: 299
          },
          end: {
            line: 79,
            column: 389
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 299
          },
          end: {
            line: 79,
            column: 310
          }
        }, {
          start: {
            line: 79,
            column: 315
          },
          end: {
            line: 79,
            column: 388
          }
        }],
        line: 79
      },
      "19": {
        loc: {
          start: {
            line: 81,
            column: 96
          },
          end: {
            line: 81,
            column: 252
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 96
          },
          end: {
            line: 81,
            column: 111
          }
        }, {
          start: {
            line: 81,
            column: 116
          },
          end: {
            line: 81,
            column: 251
          }
        }],
        line: 81
      },
      "20": {
        loc: {
          start: {
            line: 81,
            column: 1848
          },
          end: {
            line: 83,
            column: 592
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 1848
          },
          end: {
            line: 81,
            column: 1862
          }
        }, {
          start: {
            line: 81,
            column: 1866
          },
          end: {
            line: 81,
            column: 1893
          }
        }, {
          start: {
            line: 81,
            column: 1898
          },
          end: {
            line: 83,
            column: 591
          }
        }],
        line: 81
      },
      "21": {
        loc: {
          start: {
            line: 81,
            column: 2335
          },
          end: {
            line: 83,
            column: 127
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 82,
            column: 62
          },
          end: {
            line: 82,
            column: 131
          }
        }, {
          start: {
            line: 83,
            column: 62
          },
          end: {
            line: 83,
            column: 127
          }
        }],
        line: 81
      },
      "22": {
        loc: {
          start: {
            line: 83,
            column: 141
          },
          end: {
            line: 83,
            column: 336
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 83,
            column: 164
          },
          end: {
            line: 83,
            column: 240
          }
        }, {
          start: {
            line: 83,
            column: 245
          },
          end: {
            line: 83,
            column: 335
          }
        }],
        line: 83
      },
      "23": {
        loc: {
          start: {
            line: 83,
            column: 422
          },
          end: {
            line: 83,
            column: 561
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 422
          },
          end: {
            line: 83,
            column: 439
          }
        }, {
          start: {
            line: 83,
            column: 444
          },
          end: {
            line: 83,
            column: 560
          }
        }],
        line: 83
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0, 0],
      "8": [0, 0, 0, 0],
      "9": [0, 0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillProgressTracker.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;AAmCb,uCAmNC;;AApPD,gDAA0B;AAC1B,6CAAiG;AACjG,qDAAoD;AACpD,+CAA8C;AAC9C,6CAAmF;AA6BnF,SAAwB,oBAAoB,CAAC,EAMjB;QAL1B,MAAM,YAAA,EACN,aAAoC,EAApC,KAAK,mBAAG,4BAA4B,KAAA,EACpC,mBAAwE,EAAxE,WAAW,mBAAG,0DAA0D,KAAA,EACxE,sBAAqB,EAArB,cAAc,mBAAG,IAAI,KAAA,EACrB,uBAAuB,EAAvB,eAAe,mBAAG,KAAK,KAAA;IAEvB,IAAM,aAAa,GAAG,UAAC,MAAmC;QACxD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,WAAW;gBACd,OAAO,uBAAC,0BAAW,IAAC,SAAS,EAAC,wBAAwB,GAAG,CAAC;YAC5D,KAAK,aAAa;gBAChB,OAAO,uBAAC,oBAAK,IAAC,SAAS,EAAC,uBAAuB,GAAG,CAAC;YACrD,KAAK,SAAS;gBACZ,OAAO,uBAAC,0BAAW,IAAC,SAAS,EAAC,sBAAsB,GAAG,CAAC;YAC1D;gBACE,OAAO,uBAAC,qBAAM,IAAC,SAAS,EAAC,uBAAuB,GAAG,CAAC;QACxD,CAAC;IACH,CAAC,CAAC;IAEF,IAAM,cAAc,GAAG,UAAC,MAAmC;QACzD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,WAAW;gBACd,OAAO,sEAAsE,CAAC;YAChF,KAAK,aAAa;gBAChB,OAAO,kEAAkE,CAAC;YAC5E,KAAK,SAAS;gBACZ,OAAO,8DAA8D,CAAC;YACxE;gBACE,OAAO,kEAAkE,CAAC;QAC9E,CAAC;IACH,CAAC,CAAC;IAEF,IAAM,gBAAgB,GAAG,UAAC,QAAuC;QAC/D,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,UAAU;gBACb,OAAO,8DAA8D,CAAC;YACxE,KAAK,MAAM;gBACT,OAAO,0EAA0E,CAAC;YACpF,KAAK,QAAQ;gBACX,OAAO,0EAA0E,CAAC;YACpF;gBACE,OAAO,kEAAkE,CAAC;QAC9E,CAAC;IACH,CAAC,CAAC;IAEF,IAAM,gBAAgB,GAAG,UAAC,QAAgB,EAAE,MAAmC;QAC7E,IAAI,MAAM,KAAK,WAAW;YAAE,OAAO,cAAc,CAAC;QAClD,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,YAAY,CAAC;QAC9C,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,cAAc,CAAC;QAC1C,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,aAAa,CAAC;QACzC,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,eAAe,CAAC;QAC3C,OAAO,aAAa,CAAC;IACvB,CAAC,CAAC;IAEF,IAAM,aAAa,GAAG,eAAe;QACnC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK;YACvB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YAC3B,CAAC;YACD,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAyC,CAAC;QAC/C,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC;IAE7B,IAAM,eAAe,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC;QACvC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,CAAC,QAAQ,EAApB,CAAoB,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM;QACxE,CAAC,CAAC,CAAC,CAAC;IAEN,IAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,MAAM,KAAK,WAAW,EAA5B,CAA4B,CAAC,CAAC,MAAM,CAAC;IACpF,IAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,MAAM,KAAK,SAAS,EAA1B,CAA0B,CAAC,CAAC,MAAM,CAAC;IAE/E,OAAO,CACL,wBAAC,WAAI,eACH,wBAAC,iBAAU,eACT,wBAAC,gBAAS,IAAC,SAAS,EAAC,yBAAyB,aAC5C,uBAAC,yBAAU,IAAC,SAAS,EAAC,SAAS,GAAG,EACjC,KAAK,IACI,EACX,WAAW,IAAI,CACd,uBAAC,sBAAe,cAAE,WAAW,GAAmB,CACjD,EAGD,iCAAK,SAAS,EAAC,4CAA4C,aACzD,iCAAK,SAAS,EAAC,2DAA2D,aACxE,gCAAK,SAAS,EAAC,sDAAsD,iCAE/D,EACN,iCAAK,SAAS,EAAC,qDAAqD,aACjE,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,SACvB,IACF,EAEN,iCAAK,SAAS,EAAC,6DAA6D,aAC1E,gCAAK,SAAS,EAAC,wDAAwD,iCAEjE,EACN,iCAAK,SAAS,EAAC,uDAAuD,aACnE,eAAe,OAAG,MAAM,CAAC,MAAM,IAC5B,IACF,EAEN,iCAAK,SAAS,EAAC,yDAAyD,aACtE,gCAAK,SAAS,EAAC,oDAAoD,wBAE7D,EACN,gCAAK,SAAS,EAAC,mDAAmD,YAC/D,YAAY,GACT,IACF,IACF,IACK,EAEb,uBAAC,kBAAW,IAAC,SAAS,EAAC,WAAW,YAC/B,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,UAAC,EAA0B;wBAAzB,QAAQ,QAAA,EAAE,cAAc,QAAA;oBAAM,OAAA,CACjE,iCAAoB,SAAS,EAAC,WAAW,aACtC,eAAe,IAAI,CAClB,+BAAI,SAAS,EAAC,sEAAsE,YACjF,QAAQ,GACN,CACN,EAED,gCAAK,SAAS,EAAC,WAAW,YACvB,cAAc,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAC7B,iCAAoB,SAAS,EAAC,iCAAiC,aAE7D,iCAAK,SAAS,EAAC,mCAAmC,aAChD,iCAAK,SAAS,EAAC,yBAAyB,aACrC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,EAC5B,4CACE,+BAAI,SAAS,EAAC,8CAA8C,YACzD,KAAK,CAAC,IAAI,GACR,EACL,+BAAG,SAAS,EAAC,0CAA0C,uBAC9C,KAAK,CAAC,YAAY,cAAK,KAAK,CAAC,WAAW,IAC7C,IACA,IACF,EAEN,iCAAK,SAAS,EAAC,yBAAyB,aACtC,uBAAC,aAAK,IAAC,SAAS,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,YAC/C,KAAK,CAAC,QAAQ,GACT,EACR,uBAAC,aAAK,IAAC,SAAS,EAAE,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,YAC3C,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GACzB,IACJ,IACF,EAGN,iCAAK,SAAS,EAAC,WAAW,aACxB,iCAAK,SAAS,EAAC,8BAA8B,aAC3C,kCAAM,SAAS,EAAC,kCAAkC,2BACrC,KAAK,CAAC,QAAQ,SACpB,EACP,kCAAM,SAAS,EAAC,kCAAkC,sBAC1C,KAAK,CAAC,uBAAuB,cAC9B,IACH,EACN,uBAAC,mBAAQ,IACP,KAAK,EAAE,KAAK,CAAC,QAAQ,EACrB,SAAS,EAAC,KAAK,GACf,IACE,EAGL,cAAc,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAChD,iCAAK,SAAS,EAAC,WAAW,aACxB,+BAAI,SAAS,EAAC,sDAAsD,2BAE/D,EACL,gCAAK,SAAS,EAAC,uCAAuC,YACnD,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,UAAC,SAAS,IAAK,OAAA,CACnC,iCAEE,SAAS,EAAE,sDACT,SAAS,CAAC,SAAS;4DACjB,CAAC,CAAC,qEAAqE;4DACvE,CAAC,CAAC,iEAAiE,CACrE,aAED,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CACrB,uBAAC,0BAAW,IAAC,SAAS,EAAC,SAAS,GAAG,CACpC,CAAC,CAAC,CAAC,CACF,gCAAK,SAAS,EAAC,4CAA4C,GAAG,CAC/D,EACD,iCAAM,SAAS,EAAC,QAAQ,YAAE,SAAS,CAAC,KAAK,GAAQ,EAChD,SAAS,CAAC,OAAO,IAAI,CACpB,iCAAM,SAAS,EAAC,SAAS,YACtB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,GAC5C,CACR,KAjBI,SAAS,CAAC,EAAE,CAkBb,CACP,EArBoC,CAqBpC,CAAC,GACE,IACF,CACP,EAGD,iCAAK,SAAS,EAAC,0CAA0C,+BACxC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,kBAAkB,EAAE,IAC3D,KA7EE,KAAK,CAAC,EAAE,CA8EZ,CACP,EAhF8B,CAgF9B,CAAC,GACE,KAzFE,QAAQ,CA0FZ,CACP;gBA5FkE,CA4FlE,CAAC,GACU,IACT,CACR,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillProgressTracker.tsx"],
      sourcesContent: ["'use client';\n\nimport React from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Badge } from '@/components/ui/badge';\nimport { CheckCircle, Clock, AlertCircle, TrendingUp, Target } from 'lucide-react';\n\ninterface SkillProgressItem {\n  id: string;\n  name: string;\n  category: string;\n  currentLevel: number;\n  targetLevel: number;\n  progress: number; // 0-100\n  status: 'not_started' | 'in_progress' | 'completed' | 'at_risk';\n  estimatedTimeToComplete: number; // in weeks\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  lastUpdated: string;\n  milestones: {\n    id: string;\n    title: string;\n    completed: boolean;\n    dueDate?: string;\n  }[];\n}\n\ninterface SkillProgressTrackerProps {\n  skills: SkillProgressItem[];\n  title?: string;\n  description?: string;\n  showMilestones?: boolean;\n  groupByCategory?: boolean;\n}\n\nexport default function SkillProgressTracker({\n  skills,\n  title = \"Skill Development Progress\",\n  description = \"Track your progress towards your skill development goals\",\n  showMilestones = true,\n  groupByCategory = false,\n}: SkillProgressTrackerProps) {\n  const getStatusIcon = (status: SkillProgressItem['status']) => {\n    switch (status) {\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />;\n      case 'in_progress':\n        return <Clock className=\"h-4 w-4 text-blue-600\" />;\n      case 'at_risk':\n        return <AlertCircle className=\"h-4 w-4 text-red-600\" />;\n      default:\n        return <Target className=\"h-4 w-4 text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = (status: SkillProgressItem['status']) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';\n      case 'in_progress':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';\n      case 'at_risk':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';\n    }\n  };\n\n  const getPriorityColor = (priority: SkillProgressItem['priority']) => {\n    switch (priority) {\n      case 'critical':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';\n      case 'high':\n        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';\n      case 'medium':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';\n    }\n  };\n\n  const getProgressColor = (progress: number, status: SkillProgressItem['status']) => {\n    if (status === 'completed') return 'bg-green-600';\n    if (status === 'at_risk') return 'bg-red-600';\n    if (progress >= 75) return 'bg-green-600';\n    if (progress >= 50) return 'bg-blue-600';\n    if (progress >= 25) return 'bg-yellow-600';\n    return 'bg-gray-600';\n  };\n\n  const groupedSkills = groupByCategory\n    ? skills.reduce((acc, skill) => {\n        if (!acc[skill.category]) {\n          acc[skill.category] = [];\n        }\n        acc[skill.category].push(skill);\n        return acc;\n      }, {} as Record<string, SkillProgressItem[]>)\n    : { 'All Skills': skills };\n\n  const overallProgress = skills.length > 0\n    ? skills.reduce((sum, skill) => sum + skill.progress, 0) / skills.length\n    : 0;\n\n  const completedSkills = skills.filter(skill => skill.status === 'completed').length;\n  const atRiskSkills = skills.filter(skill => skill.status === 'at_risk').length;\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <TrendingUp className=\"h-5 w-5\" />\n          {title}\n        </CardTitle>\n        {description && (\n          <CardDescription>{description}</CardDescription>\n        )}\n        \n        {/* Overall Progress Summary */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4\">\n          <div className=\"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <div className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">\n              Overall Progress\n            </div>\n            <div className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n              {overallProgress.toFixed(1)}%\n            </div>\n          </div>\n          \n          <div className=\"text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n            <div className=\"text-sm font-medium text-green-700 dark:text-green-300\">\n              Completed Skills\n            </div>\n            <div className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\n              {completedSkills}/{skills.length}\n            </div>\n          </div>\n          \n          <div className=\"text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\">\n            <div className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n              At Risk\n            </div>\n            <div className=\"text-2xl font-bold text-red-900 dark:text-red-100\">\n              {atRiskSkills}\n            </div>\n          </div>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-6\">\n        {Object.entries(groupedSkills).map(([category, categorySkills]) => (\n          <div key={category} className=\"space-y-4\">\n            {groupByCategory && (\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 border-b pb-2\">\n                {category}\n              </h3>\n            )}\n            \n            <div className=\"space-y-4\">\n              {categorySkills.map((skill) => (\n                <div key={skill.id} className=\"border rounded-lg p-4 space-y-3\">\n                  {/* Skill Header */}\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      {getStatusIcon(skill.status)}\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">\n                          {skill.name}\n                        </h4>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          Level {skill.currentLevel} \u2192 {skill.targetLevel}\n                        </p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center gap-2\">\n                      <Badge className={getPriorityColor(skill.priority)}>\n                        {skill.priority}\n                      </Badge>\n                      <Badge className={getStatusColor(skill.status)}>\n                        {skill.status.replace('_', ' ')}\n                      </Badge>\n                    </div>\n                  </div>\n                  \n                  {/* Progress Bar */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">\n                        Progress: {skill.progress}%\n                      </span>\n                      <span className=\"text-gray-600 dark:text-gray-400\">\n                        Est. {skill.estimatedTimeToComplete} weeks\n                      </span>\n                    </div>\n                    <Progress \n                      value={skill.progress} \n                      className=\"h-2\"\n                    />\n                  </div>\n                  \n                  {/* Milestones */}\n                  {showMilestones && skill.milestones.length > 0 && (\n                    <div className=\"space-y-2\">\n                      <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Milestones\n                      </h5>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n                        {skill.milestones.map((milestone) => (\n                          <div\n                            key={milestone.id}\n                            className={`flex items-center gap-2 text-sm p-2 rounded ${\n                              milestone.completed\n                                ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'\n                                : 'bg-gray-50 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400'\n                            }`}\n                          >\n                            {milestone.completed ? (\n                              <CheckCircle className=\"h-3 w-3\" />\n                            ) : (\n                              <div className=\"h-3 w-3 border border-current rounded-full\" />\n                            )}\n                            <span className=\"flex-1\">{milestone.title}</span>\n                            {milestone.dueDate && (\n                              <span className=\"text-xs\">\n                                {new Date(milestone.dueDate).toLocaleDateString()}\n                              </span>\n                            )}\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                  \n                  {/* Last Updated */}\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Last updated: {new Date(skill.lastUpdated).toLocaleDateString()}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        ))}\n      </CardContent>\n    </Card>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "573849b00fd62e65b3c17a4af778a0ffa9dfa2b3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2038wth7ek = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2038wth7ek();
var __importDefault =
/* istanbul ignore next */
(cov_2038wth7ek().s[0]++,
/* istanbul ignore next */
(cov_2038wth7ek().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2038wth7ek().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2038wth7ek().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2038wth7ek().f[0]++;
  cov_2038wth7ek().s[1]++;
  return /* istanbul ignore next */(cov_2038wth7ek().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2038wth7ek().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2038wth7ek().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_2038wth7ek().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2038wth7ek().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2038wth7ek().s[3]++;
exports.default = SkillProgressTracker;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_2038wth7ek().s[4]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_2038wth7ek().s[5]++, __importDefault(require("react")));
var card_1 =
/* istanbul ignore next */
(cov_2038wth7ek().s[6]++, require("@/components/ui/card"));
var progress_1 =
/* istanbul ignore next */
(cov_2038wth7ek().s[7]++, require("@/components/ui/progress"));
var badge_1 =
/* istanbul ignore next */
(cov_2038wth7ek().s[8]++, require("@/components/ui/badge"));
var lucide_react_1 =
/* istanbul ignore next */
(cov_2038wth7ek().s[9]++, require("lucide-react"));
function SkillProgressTracker(_a) {
  /* istanbul ignore next */
  cov_2038wth7ek().f[1]++;
  var skills =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[10]++, _a.skills),
    _b =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[11]++, _a.title),
    title =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[12]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_2038wth7ek().b[3][0]++, "Skill Development Progress") :
    /* istanbul ignore next */
    (cov_2038wth7ek().b[3][1]++, _b)),
    _c =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[13]++, _a.description),
    description =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[14]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_2038wth7ek().b[4][0]++, "Track your progress towards your skill development goals") :
    /* istanbul ignore next */
    (cov_2038wth7ek().b[4][1]++, _c)),
    _d =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[15]++, _a.showMilestones),
    showMilestones =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[16]++, _d === void 0 ?
    /* istanbul ignore next */
    (cov_2038wth7ek().b[5][0]++, true) :
    /* istanbul ignore next */
    (cov_2038wth7ek().b[5][1]++, _d)),
    _e =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[17]++, _a.groupByCategory),
    groupByCategory =
    /* istanbul ignore next */
    (cov_2038wth7ek().s[18]++, _e === void 0 ?
    /* istanbul ignore next */
    (cov_2038wth7ek().b[6][0]++, false) :
    /* istanbul ignore next */
    (cov_2038wth7ek().b[6][1]++, _e));
  /* istanbul ignore next */
  cov_2038wth7ek().s[19]++;
  var getStatusIcon = function (status) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[2]++;
    cov_2038wth7ek().s[20]++;
    switch (status) {
      case 'completed':
        /* istanbul ignore next */
        cov_2038wth7ek().b[7][0]++;
        cov_2038wth7ek().s[21]++;
        return (0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, {
          className: "h-4 w-4 text-green-600"
        });
      case 'in_progress':
        /* istanbul ignore next */
        cov_2038wth7ek().b[7][1]++;
        cov_2038wth7ek().s[22]++;
        return (0, jsx_runtime_1.jsx)(lucide_react_1.Clock, {
          className: "h-4 w-4 text-blue-600"
        });
      case 'at_risk':
        /* istanbul ignore next */
        cov_2038wth7ek().b[7][2]++;
        cov_2038wth7ek().s[23]++;
        return (0, jsx_runtime_1.jsx)(lucide_react_1.AlertCircle, {
          className: "h-4 w-4 text-red-600"
        });
      default:
        /* istanbul ignore next */
        cov_2038wth7ek().b[7][3]++;
        cov_2038wth7ek().s[24]++;
        return (0, jsx_runtime_1.jsx)(lucide_react_1.Target, {
          className: "h-4 w-4 text-gray-400"
        });
    }
  };
  /* istanbul ignore next */
  cov_2038wth7ek().s[25]++;
  var getStatusColor = function (status) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[3]++;
    cov_2038wth7ek().s[26]++;
    switch (status) {
      case 'completed':
        /* istanbul ignore next */
        cov_2038wth7ek().b[8][0]++;
        cov_2038wth7ek().s[27]++;
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'in_progress':
        /* istanbul ignore next */
        cov_2038wth7ek().b[8][1]++;
        cov_2038wth7ek().s[28]++;
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'at_risk':
        /* istanbul ignore next */
        cov_2038wth7ek().b[8][2]++;
        cov_2038wth7ek().s[29]++;
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        /* istanbul ignore next */
        cov_2038wth7ek().b[8][3]++;
        cov_2038wth7ek().s[30]++;
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };
  /* istanbul ignore next */
  cov_2038wth7ek().s[31]++;
  var getPriorityColor = function (priority) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[4]++;
    cov_2038wth7ek().s[32]++;
    switch (priority) {
      case 'critical':
        /* istanbul ignore next */
        cov_2038wth7ek().b[9][0]++;
        cov_2038wth7ek().s[33]++;
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'high':
        /* istanbul ignore next */
        cov_2038wth7ek().b[9][1]++;
        cov_2038wth7ek().s[34]++;
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'medium':
        /* istanbul ignore next */
        cov_2038wth7ek().b[9][2]++;
        cov_2038wth7ek().s[35]++;
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      default:
        /* istanbul ignore next */
        cov_2038wth7ek().b[9][3]++;
        cov_2038wth7ek().s[36]++;
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };
  /* istanbul ignore next */
  cov_2038wth7ek().s[37]++;
  var getProgressColor = function (progress, status) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[5]++;
    cov_2038wth7ek().s[38]++;
    if (status === 'completed') {
      /* istanbul ignore next */
      cov_2038wth7ek().b[10][0]++;
      cov_2038wth7ek().s[39]++;
      return 'bg-green-600';
    } else
    /* istanbul ignore next */
    {
      cov_2038wth7ek().b[10][1]++;
    }
    cov_2038wth7ek().s[40]++;
    if (status === 'at_risk') {
      /* istanbul ignore next */
      cov_2038wth7ek().b[11][0]++;
      cov_2038wth7ek().s[41]++;
      return 'bg-red-600';
    } else
    /* istanbul ignore next */
    {
      cov_2038wth7ek().b[11][1]++;
    }
    cov_2038wth7ek().s[42]++;
    if (progress >= 75) {
      /* istanbul ignore next */
      cov_2038wth7ek().b[12][0]++;
      cov_2038wth7ek().s[43]++;
      return 'bg-green-600';
    } else
    /* istanbul ignore next */
    {
      cov_2038wth7ek().b[12][1]++;
    }
    cov_2038wth7ek().s[44]++;
    if (progress >= 50) {
      /* istanbul ignore next */
      cov_2038wth7ek().b[13][0]++;
      cov_2038wth7ek().s[45]++;
      return 'bg-blue-600';
    } else
    /* istanbul ignore next */
    {
      cov_2038wth7ek().b[13][1]++;
    }
    cov_2038wth7ek().s[46]++;
    if (progress >= 25) {
      /* istanbul ignore next */
      cov_2038wth7ek().b[14][0]++;
      cov_2038wth7ek().s[47]++;
      return 'bg-yellow-600';
    } else
    /* istanbul ignore next */
    {
      cov_2038wth7ek().b[14][1]++;
    }
    cov_2038wth7ek().s[48]++;
    return 'bg-gray-600';
  };
  var groupedSkills =
  /* istanbul ignore next */
  (cov_2038wth7ek().s[49]++, groupByCategory ?
  /* istanbul ignore next */
  (cov_2038wth7ek().b[15][0]++, skills.reduce(function (acc, skill) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[6]++;
    cov_2038wth7ek().s[50]++;
    if (!acc[skill.category]) {
      /* istanbul ignore next */
      cov_2038wth7ek().b[16][0]++;
      cov_2038wth7ek().s[51]++;
      acc[skill.category] = [];
    } else
    /* istanbul ignore next */
    {
      cov_2038wth7ek().b[16][1]++;
    }
    cov_2038wth7ek().s[52]++;
    acc[skill.category].push(skill);
    /* istanbul ignore next */
    cov_2038wth7ek().s[53]++;
    return acc;
  }, {})) :
  /* istanbul ignore next */
  (cov_2038wth7ek().b[15][1]++, {
    'All Skills': skills
  }));
  var overallProgress =
  /* istanbul ignore next */
  (cov_2038wth7ek().s[54]++, skills.length > 0 ?
  /* istanbul ignore next */
  (cov_2038wth7ek().b[17][0]++, skills.reduce(function (sum, skill) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[7]++;
    cov_2038wth7ek().s[55]++;
    return sum + skill.progress;
  }, 0) / skills.length) :
  /* istanbul ignore next */
  (cov_2038wth7ek().b[17][1]++, 0));
  var completedSkills =
  /* istanbul ignore next */
  (cov_2038wth7ek().s[56]++, skills.filter(function (skill) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[8]++;
    cov_2038wth7ek().s[57]++;
    return skill.status === 'completed';
  }).length);
  var atRiskSkills =
  /* istanbul ignore next */
  (cov_2038wth7ek().s[58]++, skills.filter(function (skill) {
    /* istanbul ignore next */
    cov_2038wth7ek().f[9]++;
    cov_2038wth7ek().s[59]++;
    return skill.status === 'at_risk';
  }).length);
  /* istanbul ignore next */
  cov_2038wth7ek().s[60]++;
  return (0, jsx_runtime_1.jsxs)(card_1.Card, {
    children: [(0, jsx_runtime_1.jsxs)(card_1.CardHeader, {
      children: [(0, jsx_runtime_1.jsxs)(card_1.CardTitle, {
        className: "flex items-center gap-2",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, {
          className: "h-5 w-5"
        }), title]
      }),
      /* istanbul ignore next */
      (cov_2038wth7ek().b[18][0]++, description) &&
      /* istanbul ignore next */
      (cov_2038wth7ek().b[18][1]++, (0, jsx_runtime_1.jsx)(card_1.CardDescription, {
        children: description
      })), (0, jsx_runtime_1.jsxs)("div", {
        className: "grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",
        children: [(0, jsx_runtime_1.jsxs)("div", {
          className: "text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "text-sm font-medium text-blue-700 dark:text-blue-300",
            children: "Overall Progress"
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-2xl font-bold text-blue-900 dark:text-blue-100",
            children: [overallProgress.toFixed(1), "%"]
          })]
        }), (0, jsx_runtime_1.jsxs)("div", {
          className: "text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "text-sm font-medium text-green-700 dark:text-green-300",
            children: "Completed Skills"
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-2xl font-bold text-green-900 dark:text-green-100",
            children: [completedSkills, "/", skills.length]
          })]
        }), (0, jsx_runtime_1.jsxs)("div", {
          className: "text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "text-sm font-medium text-red-700 dark:text-red-300",
            children: "At Risk"
          }), (0, jsx_runtime_1.jsx)("div", {
            className: "text-2xl font-bold text-red-900 dark:text-red-100",
            children: atRiskSkills
          })]
        })]
      })]
    }), (0, jsx_runtime_1.jsx)(card_1.CardContent, {
      className: "space-y-6",
      children: Object.entries(groupedSkills).map(function (_a) {
        /* istanbul ignore next */
        cov_2038wth7ek().f[10]++;
        var category =
          /* istanbul ignore next */
          (cov_2038wth7ek().s[61]++, _a[0]),
          categorySkills =
          /* istanbul ignore next */
          (cov_2038wth7ek().s[62]++, _a[1]);
        /* istanbul ignore next */
        cov_2038wth7ek().s[63]++;
        return (0, jsx_runtime_1.jsxs)("div", {
          className: "space-y-4",
          children: [
          /* istanbul ignore next */
          (cov_2038wth7ek().b[19][0]++, groupByCategory) &&
          /* istanbul ignore next */
          (cov_2038wth7ek().b[19][1]++, (0, jsx_runtime_1.jsx)("h3", {
            className: "text-lg font-semibold text-gray-900 dark:text-gray-100 border-b pb-2",
            children: category
          })), (0, jsx_runtime_1.jsx)("div", {
            className: "space-y-4",
            children: categorySkills.map(function (skill) {
              /* istanbul ignore next */
              cov_2038wth7ek().f[11]++;
              cov_2038wth7ek().s[64]++;
              return (0, jsx_runtime_1.jsxs)("div", {
                className: "border rounded-lg p-4 space-y-3",
                children: [(0, jsx_runtime_1.jsxs)("div", {
                  className: "flex items-center justify-between",
                  children: [(0, jsx_runtime_1.jsxs)("div", {
                    className: "flex items-center gap-3",
                    children: [getStatusIcon(skill.status), (0, jsx_runtime_1.jsxs)("div", {
                      children: [(0, jsx_runtime_1.jsx)("h4", {
                        className: "font-medium text-gray-900 dark:text-gray-100",
                        children: skill.name
                      }), (0, jsx_runtime_1.jsxs)("p", {
                        className: "text-sm text-gray-500 dark:text-gray-400",
                        children: ["Level ", skill.currentLevel, " \u2192 ", skill.targetLevel]
                      })]
                    })]
                  }), (0, jsx_runtime_1.jsxs)("div", {
                    className: "flex items-center gap-2",
                    children: [(0, jsx_runtime_1.jsx)(badge_1.Badge, {
                      className: getPriorityColor(skill.priority),
                      children: skill.priority
                    }), (0, jsx_runtime_1.jsx)(badge_1.Badge, {
                      className: getStatusColor(skill.status),
                      children: skill.status.replace('_', ' ')
                    })]
                  })]
                }), (0, jsx_runtime_1.jsxs)("div", {
                  className: "space-y-2",
                  children: [(0, jsx_runtime_1.jsxs)("div", {
                    className: "flex justify-between text-sm",
                    children: [(0, jsx_runtime_1.jsxs)("span", {
                      className: "text-gray-600 dark:text-gray-400",
                      children: ["Progress: ", skill.progress, "%"]
                    }), (0, jsx_runtime_1.jsxs)("span", {
                      className: "text-gray-600 dark:text-gray-400",
                      children: ["Est. ", skill.estimatedTimeToComplete, " weeks"]
                    })]
                  }), (0, jsx_runtime_1.jsx)(progress_1.Progress, {
                    value: skill.progress,
                    className: "h-2"
                  })]
                }),
                /* istanbul ignore next */
                (cov_2038wth7ek().b[20][0]++, showMilestones) &&
                /* istanbul ignore next */
                (cov_2038wth7ek().b[20][1]++, skill.milestones.length > 0) &&
                /* istanbul ignore next */
                (cov_2038wth7ek().b[20][2]++, (0, jsx_runtime_1.jsxs)("div", {
                  className: "space-y-2",
                  children: [(0, jsx_runtime_1.jsx)("h5", {
                    className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                    children: "Milestones"
                  }), (0, jsx_runtime_1.jsx)("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 gap-2",
                    children: skill.milestones.map(function (milestone) {
                      /* istanbul ignore next */
                      cov_2038wth7ek().f[12]++;
                      cov_2038wth7ek().s[65]++;
                      return (0, jsx_runtime_1.jsxs)("div", {
                        className: "flex items-center gap-2 text-sm p-2 rounded ".concat(milestone.completed ?
                        /* istanbul ignore next */
                        (cov_2038wth7ek().b[21][0]++, 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300') :
                        /* istanbul ignore next */
                        (cov_2038wth7ek().b[21][1]++, 'bg-gray-50 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400')),
                        children: [milestone.completed ?
                        /* istanbul ignore next */
                        (cov_2038wth7ek().b[22][0]++, (0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, {
                          className: "h-3 w-3"
                        })) :
                        /* istanbul ignore next */
                        (cov_2038wth7ek().b[22][1]++, (0, jsx_runtime_1.jsx)("div", {
                          className: "h-3 w-3 border border-current rounded-full"
                        })), (0, jsx_runtime_1.jsx)("span", {
                          className: "flex-1",
                          children: milestone.title
                        }),
                        /* istanbul ignore next */
                        (cov_2038wth7ek().b[23][0]++, milestone.dueDate) &&
                        /* istanbul ignore next */
                        (cov_2038wth7ek().b[23][1]++, (0, jsx_runtime_1.jsx)("span", {
                          className: "text-xs",
                          children: new Date(milestone.dueDate).toLocaleDateString()
                        }))]
                      }, milestone.id);
                    })
                  })]
                })), (0, jsx_runtime_1.jsxs)("div", {
                  className: "text-xs text-gray-500 dark:text-gray-400",
                  children: ["Last updated: ", new Date(skill.lastUpdated).toLocaleDateString()]
                })]
              }, skill.id);
            })
          })]
        }, category);
      })
    })]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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