37fac873bd716e6bd81158a8914cba56
"use strict";
'use client';

/* istanbul ignore next */
function cov_lqxrdek68() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/resume-builder/ResumePreview.tsx";
  var hash = "fe206639d3e04c18837ef3caf3976e9f51f868eb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/resume-builder/ResumePreview.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 5,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 62
        }
      },
      "2": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 62
        }
      },
      "3": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 38
        }
      },
      "4": {
        start: {
          line: 8,
          column: 20
        },
        end: {
          line: 8,
          column: 48
        }
      },
      "5": {
        start: {
          line: 9,
          column: 14
        },
        end: {
          line: 9,
          column: 47
        }
      },
      "6": {
        start: {
          line: 10,
          column: 13
        },
        end: {
          line: 10,
          column: 44
        }
      },
      "7": {
        start: {
          line: 11,
          column: 14
        },
        end: {
          line: 11,
          column: 46
        }
      },
      "8": {
        start: {
          line: 12,
          column: 18
        },
        end: {
          line: 12,
          column: 54
        }
      },
      "9": {
        start: {
          line: 13,
          column: 21
        },
        end: {
          line: 13,
          column: 44
        }
      },
      "10": {
        start: {
          line: 14,
          column: 22
        },
        end: {
          line: 14,
          column: 62
        }
      },
      "11": {
        start: {
          line: 16,
          column: 17
        },
        end: {
          line: 16,
          column: 26
        }
      },
      "12": {
        start: {
          line: 17,
          column: 21
        },
        end: {
          line: 22,
          column: 5
        }
      },
      "13": {
        start: {
          line: 18,
          column: 8
        },
        end: {
          line: 19,
          column: 22
        }
      },
      "14": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 22
        }
      },
      "15": {
        start: {
          line: 20,
          column: 19
        },
        end: {
          line: 20,
          column: 47
        }
      },
      "16": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 85
        }
      },
      "17": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "18": {
        start: {
          line: 24,
          column: 20
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "19": {
        start: {
          line: 25,
          column: 18
        },
        end: {
          line: 25,
          column: 59
        }
      },
      "20": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 71
        }
      },
      "21": {
        start: {
          line: 28,
          column: 29
        },
        end: {
          line: 41,
          column: 5
        }
      },
      "22": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 40,
          column: 9
        }
      },
      "23": {
        start: {
          line: 31,
          column: 16
        },
        end: {
          line: 31,
          column: 49
        }
      },
      "24": {
        start: {
          line: 33,
          column: 16
        },
        end: {
          line: 33,
          column: 55
        }
      },
      "25": {
        start: {
          line: 35,
          column: 16
        },
        end: {
          line: 35,
          column: 51
        }
      },
      "26": {
        start: {
          line: 37,
          column: 16
        },
        end: {
          line: 37,
          column: 53
        }
      },
      "27": {
        start: {
          line: 39,
          column: 16
        },
        end: {
          line: 39,
          column: 51
        }
      },
      "28": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 51,
          column: 315
        }
      },
      "29": {
        start: {
          line: 42,
          column: 2639
        },
        end: {
          line: 42,
          column: 3868
        }
      },
      "30": {
        start: {
          line: 42,
          column: 3775
        },
        end: {
          line: 42,
          column: 3847
        }
      },
      "31": {
        start: {
          line: 42,
          column: 4159
        },
        end: {
          line: 42,
          column: 5074
        }
      },
      "32": {
        start: {
          line: 43,
          column: 47
        },
        end: {
          line: 43,
          column: 72
        }
      },
      "33": {
        start: {
          line: 44,
          column: 32
        },
        end: {
          line: 45,
          column: 55
        }
      },
      "34": {
        start: {
          line: 45,
          column: 36
        },
        end: {
          line: 45,
          column: 55
        }
      },
      "35": {
        start: {
          line: 46,
          column: 32
        },
        end: {
          line: 46,
          column: 58
        }
      },
      "36": {
        start: {
          line: 47,
          column: 32
        },
        end: {
          line: 47,
          column: 43
        }
      },
      "37": {
        start: {
          line: 49,
          column: 47
        },
        end: {
          line: 49,
          column: 52
        }
      },
      "38": {
        start: {
          line: 49,
          column: 71
        },
        end: {
          line: 49,
          column: 76
        }
      },
      "39": {
        start: {
          line: 50,
          column: 32
        },
        end: {
          line: 50,
          column: 651
        }
      },
      "40": {
        start: {
          line: 50,
          column: 338
        },
        end: {
          line: 50,
          column: 629
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 57
          }
        },
        loc: {
          start: {
            line: 3,
            column: 71
          },
          end: {
            line: 5,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "ResumePreview",
        decl: {
          start: {
            line: 15,
            column: 9
          },
          end: {
            line: 15,
            column: 22
          }
        },
        loc: {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 15
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 22
          }
        },
        loc: {
          start: {
            line: 17,
            column: 43
          },
          end: {
            line: 22,
            column: 5
          }
        },
        line: 17
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 23,
            column: 26
          },
          end: {
            line: 23,
            column: 27
          }
        },
        loc: {
          start: {
            line: 23,
            column: 56
          },
          end: {
            line: 27,
            column: 5
          }
        },
        line: 23
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 28,
            column: 29
          },
          end: {
            line: 28,
            column: 30
          }
        },
        loc: {
          start: {
            line: 28,
            column: 46
          },
          end: {
            line: 41,
            column: 5
          }
        },
        line: 28
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 42,
            column: 2622
          },
          end: {
            line: 42,
            column: 2623
          }
        },
        loc: {
          start: {
            line: 42,
            column: 2637
          },
          end: {
            line: 42,
            column: 3870
          }
        },
        line: 42
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 42,
            column: 3743
          },
          end: {
            line: 42,
            column: 3744
          }
        },
        loc: {
          start: {
            line: 42,
            column: 3773
          },
          end: {
            line: 42,
            column: 3849
          }
        },
        line: 42
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 42,
            column: 4142
          },
          end: {
            line: 42,
            column: 4143
          }
        },
        loc: {
          start: {
            line: 42,
            column: 4157
          },
          end: {
            line: 42,
            column: 5076
          }
        },
        line: 42
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 42,
            column: 5357
          },
          end: {
            line: 42,
            column: 5358
          }
        },
        loc: {
          start: {
            line: 42,
            column: 5379
          },
          end: {
            line: 48,
            column: 29
          }
        },
        line: 42
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 48,
            column: 40
          },
          end: {
            line: 48,
            column: 41
          }
        },
        loc: {
          start: {
            line: 48,
            column: 54
          },
          end: {
            line: 51,
            column: 29
          }
        },
        line: 48
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 50,
            column: 319
          },
          end: {
            line: 50,
            column: 320
          }
        },
        loc: {
          start: {
            line: 50,
            column: 336
          },
          end: {
            line: 50,
            column: 631
          }
        },
        line: 50
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 5,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 27
          }
        }, {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 3,
            column: 51
          }
        }, {
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 5,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 11
          },
          end: {
            line: 4,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 37
          },
          end: {
            line: 4,
            column: 40
          }
        }, {
          start: {
            line: 4,
            column: 43
          },
          end: {
            line: 4,
            column: 61
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 12
          },
          end: {
            line: 4,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 12
          },
          end: {
            line: 4,
            column: 15
          }
        }, {
          start: {
            line: 4,
            column: 19
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 18,
            column: 8
          },
          end: {
            line: 19,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 8
          },
          end: {
            line: 19,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "4": {
        loc: {
          start: {
            line: 25,
            column: 18
          },
          end: {
            line: 25,
            column: 59
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 28
          },
          end: {
            line: 25,
            column: 47
          }
        }, {
          start: {
            line: 25,
            column: 50
          },
          end: {
            line: 25,
            column: 59
          }
        }],
        line: 25
      },
      "5": {
        loc: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 70
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 26,
            column: 30
          },
          end: {
            line: 26,
            column: 65
          }
        }, {
          start: {
            line: 26,
            column: 68
          },
          end: {
            line: 26,
            column: 70
          }
        }],
        line: 26
      },
      "6": {
        loc: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 20
          }
        }, {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 27
          }
        }],
        line: 26
      },
      "7": {
        loc: {
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 40,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 31,
            column: 49
          }
        }, {
          start: {
            line: 32,
            column: 12
          },
          end: {
            line: 33,
            column: 55
          }
        }, {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 35,
            column: 51
          }
        }, {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 37,
            column: 53
          }
        }, {
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 39,
            column: 51
          }
        }],
        line: 29
      },
      "8": {
        loc: {
          start: {
            line: 42,
            column: 631
          },
          end: {
            line: 42,
            column: 844
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 631
          },
          end: {
            line: 42,
            column: 656
          }
        }, {
          start: {
            line: 42,
            column: 661
          },
          end: {
            line: 42,
            column: 843
          }
        }],
        line: 42
      },
      "9": {
        loc: {
          start: {
            line: 42,
            column: 846
          },
          end: {
            line: 42,
            column: 1060
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 846
          },
          end: {
            line: 42,
            column: 871
          }
        }, {
          start: {
            line: 42,
            column: 876
          },
          end: {
            line: 42,
            column: 1059
          }
        }],
        line: 42
      },
      "10": {
        loc: {
          start: {
            line: 42,
            column: 1062
          },
          end: {
            line: 42,
            column: 1283
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 1062
          },
          end: {
            line: 42,
            column: 1090
          }
        }, {
          start: {
            line: 42,
            column: 1095
          },
          end: {
            line: 42,
            column: 1282
          }
        }],
        line: 42
      },
      "11": {
        loc: {
          start: {
            line: 42,
            column: 1285
          },
          end: {
            line: 42,
            column: 1639
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 1285
          },
          end: {
            line: 42,
            column: 1312
          }
        }, {
          start: {
            line: 42,
            column: 1317
          },
          end: {
            line: 42,
            column: 1638
          }
        }],
        line: 42
      },
      "12": {
        loc: {
          start: {
            line: 42,
            column: 1641
          },
          end: {
            line: 42,
            column: 2001
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 1641
          },
          end: {
            line: 42,
            column: 1669
          }
        }, {
          start: {
            line: 42,
            column: 1674
          },
          end: {
            line: 42,
            column: 2000
          }
        }],
        line: 42
      },
      "13": {
        loc: {
          start: {
            line: 42,
            column: 2011
          },
          end: {
            line: 42,
            column: 2351
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 2011
          },
          end: {
            line: 42,
            column: 2025
          }
        }, {
          start: {
            line: 42,
            column: 2030
          },
          end: {
            line: 42,
            column: 2350
          }
        }],
        line: 42
      },
      "14": {
        loc: {
          start: {
            line: 42,
            column: 2353
          },
          end: {
            line: 42,
            column: 3879
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 2353
          },
          end: {
            line: 42,
            column: 2381
          }
        }, {
          start: {
            line: 42,
            column: 2386
          },
          end: {
            line: 42,
            column: 3878
          }
        }],
        line: 42
      },
      "15": {
        loc: {
          start: {
            line: 42,
            column: 3370
          },
          end: {
            line: 42,
            column: 3559
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 3370
          },
          end: {
            line: 42,
            column: 3385
          }
        }, {
          start: {
            line: 42,
            column: 3390
          },
          end: {
            line: 42,
            column: 3558
          }
        }],
        line: 42
      },
      "16": {
        loc: {
          start: {
            line: 42,
            column: 3561
          },
          end: {
            line: 42,
            column: 3854
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 3561
          },
          end: {
            line: 42,
            column: 3577
          }
        }, {
          start: {
            line: 42,
            column: 3581
          },
          end: {
            line: 42,
            column: 3608
          }
        }, {
          start: {
            line: 42,
            column: 3613
          },
          end: {
            line: 42,
            column: 3853
          }
        }],
        line: 42
      },
      "17": {
        loc: {
          start: {
            line: 42,
            column: 3881
          },
          end: {
            line: 42,
            column: 5085
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 3881
          },
          end: {
            line: 42,
            column: 3908
          }
        }, {
          start: {
            line: 42,
            column: 3913
          },
          end: {
            line: 42,
            column: 5084
          }
        }],
        line: 42
      },
      "18": {
        loc: {
          start: {
            line: 42,
            column: 4435
          },
          end: {
            line: 42,
            column: 4546
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 4435
          },
          end: {
            line: 42,
            column: 4444
          }
        }, {
          start: {
            line: 42,
            column: 4448
          },
          end: {
            line: 42,
            column: 4546
          }
        }],
        line: 42
      },
      "19": {
        loc: {
          start: {
            line: 42,
            column: 4908
          },
          end: {
            line: 42,
            column: 4984
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 4908
          },
          end: {
            line: 42,
            column: 4915
          }
        }, {
          start: {
            line: 42,
            column: 4919
          },
          end: {
            line: 42,
            column: 4984
          }
        }],
        line: 42
      },
      "20": {
        loc: {
          start: {
            line: 42,
            column: 4986
          },
          end: {
            line: 42,
            column: 5056
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 4986
          },
          end: {
            line: 42,
            column: 4996
          }
        }, {
          start: {
            line: 42,
            column: 5000
          },
          end: {
            line: 42,
            column: 5056
          }
        }],
        line: 42
      },
      "21": {
        loc: {
          start: {
            line: 42,
            column: 5087
          },
          end: {
            line: 51,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 5087
          },
          end: {
            line: 42,
            column: 5111
          }
        }, {
          start: {
            line: 42,
            column: 5116
          },
          end: {
            line: 51,
            column: 37
          }
        }],
        line: 42
      },
      "22": {
        loc: {
          start: {
            line: 43,
            column: 47
          },
          end: {
            line: 43,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 47
          },
          end: {
            line: 43,
            column: 61
          }
        }, {
          start: {
            line: 43,
            column: 65
          },
          end: {
            line: 43,
            column: 72
          }
        }],
        line: 43
      },
      "23": {
        loc: {
          start: {
            line: 44,
            column: 32
          },
          end: {
            line: 45,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 32
          },
          end: {
            line: 45,
            column: 55
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "24": {
        loc: {
          start: {
            line: 50,
            column: 474
          },
          end: {
            line: 50,
            column: 613
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 474
          },
          end: {
            line: 50,
            column: 485
          }
        }, {
          start: {
            line: 50,
            column: 490
          },
          end: {
            line: 50,
            column: 612
          }
        }],
        line: 50
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0, 0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/resume-builder/ResumePreview.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;AAcb,sCA6MC;;AAzND,gDAA0B;AAC1B,6CAAyD;AACzD,+CAA8C;AAC9C,uDAAsD;AACtD,6CAAoE;AAEpE,+DAA8E;AAM9E,SAAgB,aAAa,CAAC,EAA8B;QAA5B,MAAM,YAAA;IACpC,IAAM,UAAU,GAAG,UAAC,UAAmB;QACrC,IAAI,CAAC,UAAU;YAAE,OAAO,EAAE,CAAC;QAC3B,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,6BAA6B;QACxE,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IAC/E,CAAC,CAAC;IAEF,IAAM,eAAe,GAAG,UAAC,SAAkB,EAAE,OAAgB;QAC3D,IAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACtD,OAAO,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,UAAG,KAAK,gBAAM,GAAG,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACjD,CAAC,CAAC;IAEF,IAAM,kBAAkB,GAAG,UAAC,KAAc;QACxC,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,UAAU;gBACb,OAAO,yBAAyB,CAAC;YACnC,KAAK,cAAc;gBACjB,OAAO,+BAA+B,CAAC;YACzC,KAAK,UAAU;gBACb,OAAO,2BAA2B,CAAC;YACrC,KAAK,QAAQ;gBACX,OAAO,6BAA6B,CAAC;YACvC;gBACE,OAAO,2BAA2B,CAAC;QACvC,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,uBAAC,WAAI,IAAC,SAAS,EAAC,6BAA6B,YAC3C,wBAAC,kBAAW,IAAC,SAAS,EAAC,4BAA4B,aAEjD,iCAAK,SAAS,EAAC,uBAAuB,aACpC,+BAAI,SAAS,EAAC,oBAAoB,YAChC,wBAAC,4BAAY,IAAC,QAAQ,EAAE,CAAC,aACtB,MAAM,CAAC,YAAY,CAAC,SAAS,OAAG,MAAM,CAAC,YAAY,CAAC,QAAQ,IAChD,GACZ,EACL,iCAAK,SAAS,EAAC,mEAAmE,aAC/E,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,CAC5B,iCAAK,SAAS,EAAC,yBAAyB,aACtC,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,EAC3B,MAAM,CAAC,YAAY,CAAC,KAAK,IACtB,CACP,EACA,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,CAC5B,iCAAK,SAAS,EAAC,yBAAyB,aACtC,uBAAC,oBAAK,IAAC,SAAS,EAAC,SAAS,GAAG,EAC5B,MAAM,CAAC,YAAY,CAAC,KAAK,IACtB,CACP,EACA,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,CAC/B,iCAAK,SAAS,EAAC,yBAAyB,aACtC,uBAAC,qBAAM,IAAC,SAAS,EAAC,SAAS,GAAG,EAC7B,MAAM,CAAC,YAAY,CAAC,QAAQ,IACzB,CACP,EACA,MAAM,CAAC,YAAY,CAAC,OAAO,IAAI,CAC9B,iCAAK,SAAS,EAAC,yBAAyB,aACtC,uBAAC,oBAAK,IAAC,SAAS,EAAC,SAAS,GAAG,EAC7B,8BAAG,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAC,QAAQ,EAAC,GAAG,EAAC,qBAAqB,EAAC,SAAS,EAAC,iBAAiB,wBAEvG,IACA,CACP,EACA,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,CAC/B,iCAAK,SAAS,EAAC,yBAAyB,aACtC,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,EAChC,8BAAG,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAC,QAAQ,EAAC,GAAG,EAAC,qBAAqB,EAAC,SAAS,EAAC,iBAAiB,yBAExG,IACA,CACP,IACG,IACF,EAGL,MAAM,CAAC,OAAO,IAAI,CACjB,4CACE,+BAAI,SAAS,EAAC,4BAA4B,qCAA0B,EACpE,uBAAC,+BAAe,IACd,IAAI,EAAE,MAAM,CAAC,OAAO,EACpB,SAAS,EAAE,IAAI,EACf,QAAQ,EAAE,CAAC,EACX,SAAS,EAAC,uCAAuC,GACjD,IACE,CACP,EAGA,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAC/B,4CACE,+BAAI,SAAS,EAAC,4BAA4B,gCAAqB,EAC/D,gCAAK,SAAS,EAAC,WAAW,YACvB,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,CAC9B,4CACE,iCAAK,SAAS,EAAC,uCAAuC,aACpD,iCAAK,SAAS,EAAC,aAAa,aAC1B,uBAAC,4BAAY,IAAC,QAAQ,EAAE,CAAC,YACvB,+BAAI,SAAS,EAAC,uBAAuB,YAAE,GAAG,CAAC,QAAQ,GAAM,GAC5C,EACf,uBAAC,4BAAY,IAAC,QAAQ,EAAE,CAAC,YACvB,8BAAG,SAAS,EAAC,mCAAmC,YAAE,GAAG,CAAC,OAAO,GAAK,GACrD,IACX,EACN,gCAAK,SAAS,EAAC,0CAA0C,YACtD,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,GACxC,IACF,EACL,GAAG,CAAC,WAAW,IAAI,CAClB,uBAAC,+BAAe,IACd,IAAI,EAAE,GAAG,CAAC,WAAW,EACrB,SAAS,EAAE,GAAG,EACd,QAAQ,EAAE,CAAC,EACX,SAAS,EAAC,4CAA4C,GACtD,CACH,EACA,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,CAClD,+BAAI,SAAS,EAAC,uDAAuD,YAClE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAC,WAAW,EAAE,KAAK,IAAK,OAAA,CAC5C,yCAAiB,WAAW,IAAnB,KAAK,CAAoB,CACnC,EAF6C,CAE7C,CAAC,GACC,CACN,KA5BO,GAAG,CAAC,EAAE,CA6BV,CACP,EA/B+B,CA+B/B,CAAC,GACE,IACF,CACP,EAGA,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAC9B,4CACE,+BAAI,SAAS,EAAC,4BAA4B,0BAAe,EACzD,gCAAK,SAAS,EAAC,WAAW,YACvB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,CAC7B,4CACE,iCAAK,SAAS,EAAC,uCAAuC,aACpD,4CACE,+BAAI,SAAS,EAAC,eAAe,YAAE,GAAG,CAAC,MAAM,GAAM,EAC9C,GAAG,CAAC,KAAK,IAAI,+BAAG,SAAS,EAAC,uBAAuB,oBAAK,GAAG,CAAC,KAAK,IAAK,EACrE,8BAAG,SAAS,EAAC,mCAAmC,YAAE,GAAG,CAAC,WAAW,GAAK,IAClE,EACN,gCAAK,SAAS,EAAC,0CAA0C,YACtD,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,GACxC,IACF,EACN,iCAAK,SAAS,EAAC,0CAA0C,aACtD,GAAG,CAAC,GAAG,IAAI,sDAAY,GAAG,CAAC,GAAG,IAAQ,EACtC,GAAG,CAAC,MAAM,IAAI,2CAAO,GAAG,CAAC,MAAM,GAAQ,IACpC,KAdE,GAAG,CAAC,EAAE,CAeV,CACP,EAjB8B,CAiB9B,CAAC,GACE,IACF,CACP,EAGA,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAC3B,4CACE,+BAAI,SAAS,EAAC,4BAA4B,uBAAY,EACtD,gCAAK,SAAS,EAAC,WAAW,YAEvB,MAAM,CAAC,OAAO,CACb,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK;gCAC9B,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,OAAO,CAAC;gCAC3C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oCAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;gCACvC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gCAC1B,OAAO,GAAG,CAAC;4BACb,CAAC,EAAE,EAA0C,CAAC,CAC/C,CAAC,GAAG,CAAC,UAAC,EAA0B;oCAAzB,QAAQ,QAAA,EAAE,cAAc,QAAA;gCAAM,OAAA,CACpC,4CACE,+BAAI,SAAS,EAAC,wEAAwE,YACnF,QAAQ,GACN,EACL,gCAAK,SAAS,EAAC,sBAAsB,YAClC,cAAc,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAC7B,wBAAC,aAAK,IAEJ,OAAO,EAAC,SAAS,EACjB,SAAS,EAAE,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,aAEzC,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,KAAK,IAAI,CACd,kCAAM,SAAS,EAAC,yBAAyB,kBACrC,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,SACtB,CACR,KATI,KAAK,CAAC,EAAE,CAUP,CACT,EAb8B,CAa9B,CAAC,GACE,KAnBE,QAAQ,CAoBZ,CACP;4BAtBqC,CAsBrC,CAAC,GACE,IACF,CACP,EAGD,uBAAC,qBAAS,KAAG,EACb,gCAAK,SAAS,EAAC,2CAA2C,YACxD,wDAAc,MAAM,CAAC,QAAQ,2CAAmC,GAC5D,IACM,GACT,CACR,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/resume-builder/ResumePreview.tsx"],
      sourcesContent: ["'use client';\n\nimport React from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { Mail, Phone, MapPin, Globe, Linkedin } from 'lucide-react';\nimport { Resume } from './ResumeBuilder';\nimport { SafeTextDisplay, TextOverflow } from '@/components/ui/text-truncate';\n\ninterface ResumePreviewProps {\n  resume: Resume;\n}\n\nexport function ResumePreview({ resume }: ResumePreviewProps) {\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return '';\n    const date = new Date(dateString + '-01'); // Add day for proper parsing\n    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });\n  };\n\n  const formatDateRange = (startDate?: string, endDate?: string) => {\n    const start = formatDate(startDate);\n    const end = endDate ? formatDate(endDate) : 'Present';\n    return start && end ? `${start} - ${end}` : '';\n  };\n\n  const getLevelBadgeColor = (level?: string) => {\n    switch (level) {\n      case 'BEGINNER':\n        return 'bg-red-100 text-red-800';\n      case 'INTERMEDIATE':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'ADVANCED':\n        return 'bg-blue-100 text-blue-800';\n      case 'EXPERT':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <Card className=\"max-w-4xl mx-auto card-safe\">\n      <CardContent className=\"p-8 space-y-6 content-safe\">\n        {/* Header */}\n        <div className=\"text-center space-y-2\">\n          <h1 className=\"text-3xl font-bold\">\n            <TextOverflow maxLines={2}>\n              {resume.personalInfo.firstName} {resume.personalInfo.lastName}\n            </TextOverflow>\n          </h1>\n          <div className=\"flex flex-wrap justify-center gap-4 text-sm text-muted-foreground\">\n            {resume.personalInfo.email && (\n              <div className=\"flex items-center gap-1\">\n                <Mail className=\"w-4 h-4\" />\n                {resume.personalInfo.email}\n              </div>\n            )}\n            {resume.personalInfo.phone && (\n              <div className=\"flex items-center gap-1\">\n                <Phone className=\"w-4 h-4\" />\n                {resume.personalInfo.phone}\n              </div>\n            )}\n            {resume.personalInfo.location && (\n              <div className=\"flex items-center gap-1\">\n                <MapPin className=\"w-4 h-4\" />\n                {resume.personalInfo.location}\n              </div>\n            )}\n            {resume.personalInfo.website && (\n              <div className=\"flex items-center gap-1\">\n                <Globe className=\"w-4 h-4\" />\n                <a href={resume.personalInfo.website} target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:underline\">\n                  Website\n                </a>\n              </div>\n            )}\n            {resume.personalInfo.linkedIn && (\n              <div className=\"flex items-center gap-1\">\n                <Linkedin className=\"w-4 h-4\" />\n                <a href={resume.personalInfo.linkedIn} target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:underline\">\n                  LinkedIn\n                </a>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Professional Summary */}\n        {resume.summary && (\n          <div>\n            <h2 className=\"text-xl font-semibold mb-3\">Professional Summary</h2>\n            <SafeTextDisplay\n              text={resume.summary}\n              maxLength={2000}\n              maxLines={6}\n              className=\"text-muted-foreground leading-relaxed\"\n            />\n          </div>\n        )}\n\n        {/* Experience */}\n        {resume.experience.length > 0 && (\n          <div>\n            <h2 className=\"text-xl font-semibold mb-4\">Work Experience</h2>\n            <div className=\"space-y-6\">\n              {resume.experience.map((exp) => (\n                <div key={exp.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div className=\"flex-1 mr-4\">\n                      <TextOverflow maxLines={2}>\n                        <h3 className=\"font-semibold text-lg\">{exp.position}</h3>\n                      </TextOverflow>\n                      <TextOverflow maxLines={1}>\n                        <p className=\"text-muted-foreground font-medium\">{exp.company}</p>\n                      </TextOverflow>\n                    </div>\n                    <div className=\"text-sm text-muted-foreground text-right\">\n                      {formatDateRange(exp.startDate, exp.endDate)}\n                    </div>\n                  </div>\n                  {exp.description && (\n                    <SafeTextDisplay\n                      text={exp.description}\n                      maxLength={500}\n                      maxLines={4}\n                      className=\"text-muted-foreground mb-2 leading-relaxed\"\n                    />\n                  )}\n                  {exp.achievements && exp.achievements.length > 0 && (\n                    <ul className=\"list-disc list-inside space-y-1 text-muted-foreground\">\n                      {exp.achievements.map((achievement, index) => (\n                        <li key={index}>{achievement}</li>\n                      ))}\n                    </ul>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Education */}\n        {resume.education.length > 0 && (\n          <div>\n            <h2 className=\"text-xl font-semibold mb-4\">Education</h2>\n            <div className=\"space-y-4\">\n              {resume.education.map((edu) => (\n                <div key={edu.id}>\n                  <div className=\"flex justify-between items-start mb-1\">\n                    <div>\n                      <h3 className=\"font-semibold\">{edu.degree}</h3>\n                      {edu.field && <p className=\"text-muted-foreground\">in {edu.field}</p>}\n                      <p className=\"text-muted-foreground font-medium\">{edu.institution}</p>\n                    </div>\n                    <div className=\"text-sm text-muted-foreground text-right\">\n                      {formatDateRange(edu.startDate, edu.endDate)}\n                    </div>\n                  </div>\n                  <div className=\"flex gap-4 text-sm text-muted-foreground\">\n                    {edu.gpa && <span>GPA: {edu.gpa}</span>}\n                    {edu.honors && <span>{edu.honors}</span>}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Skills */}\n        {resume.skills.length > 0 && (\n          <div>\n            <h2 className=\"text-xl font-semibold mb-4\">Skills</h2>\n            <div className=\"space-y-4\">\n              {/* Group skills by category */}\n              {Object.entries(\n                resume.skills.reduce((acc, skill) => {\n                  const category = skill.category || 'Other';\n                  if (!acc[category]) acc[category] = [];\n                  acc[category].push(skill);\n                  return acc;\n                }, {} as Record<string, typeof resume.skills>)\n              ).map(([category, categorySkills]) => (\n                <div key={category}>\n                  <h3 className=\"font-medium text-sm text-muted-foreground uppercase tracking-wide mb-2\">\n                    {category}\n                  </h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {categorySkills.map((skill) => (\n                      <Badge\n                        key={skill.id}\n                        variant=\"outline\"\n                        className={getLevelBadgeColor(skill.level)}\n                      >\n                        {skill.name}\n                        {skill.level && (\n                          <span className=\"ml-1 text-xs opacity-75\">\n                            ({skill.level.toLowerCase()})\n                          </span>\n                        )}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Template Info */}\n        <Separator />\n        <div className=\"text-center text-xs text-muted-foreground\">\n          <p>Template: {resume.template} \u2022 Created with Resume Builder</p>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fe206639d3e04c18837ef3caf3976e9f51f868eb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_lqxrdek68 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_lqxrdek68();
var __importDefault =
/* istanbul ignore next */
(cov_lqxrdek68().s[0]++,
/* istanbul ignore next */
(cov_lqxrdek68().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_lqxrdek68().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_lqxrdek68().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_lqxrdek68().f[0]++;
  cov_lqxrdek68().s[1]++;
  return /* istanbul ignore next */(cov_lqxrdek68().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_lqxrdek68().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_lqxrdek68().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_lqxrdek68().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_lqxrdek68().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_lqxrdek68().s[3]++;
exports.ResumePreview = ResumePreview;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_lqxrdek68().s[4]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_lqxrdek68().s[5]++, __importDefault(require("react")));
var card_1 =
/* istanbul ignore next */
(cov_lqxrdek68().s[6]++, require("@/components/ui/card"));
var badge_1 =
/* istanbul ignore next */
(cov_lqxrdek68().s[7]++, require("@/components/ui/badge"));
var separator_1 =
/* istanbul ignore next */
(cov_lqxrdek68().s[8]++, require("@/components/ui/separator"));
var lucide_react_1 =
/* istanbul ignore next */
(cov_lqxrdek68().s[9]++, require("lucide-react"));
var text_truncate_1 =
/* istanbul ignore next */
(cov_lqxrdek68().s[10]++, require("@/components/ui/text-truncate"));
function ResumePreview(_a) {
  /* istanbul ignore next */
  cov_lqxrdek68().f[1]++;
  var resume =
  /* istanbul ignore next */
  (cov_lqxrdek68().s[11]++, _a.resume);
  /* istanbul ignore next */
  cov_lqxrdek68().s[12]++;
  var formatDate = function (dateString) {
    /* istanbul ignore next */
    cov_lqxrdek68().f[2]++;
    cov_lqxrdek68().s[13]++;
    if (!dateString) {
      /* istanbul ignore next */
      cov_lqxrdek68().b[3][0]++;
      cov_lqxrdek68().s[14]++;
      return '';
    } else
    /* istanbul ignore next */
    {
      cov_lqxrdek68().b[3][1]++;
    }
    var date =
    /* istanbul ignore next */
    (cov_lqxrdek68().s[15]++, new Date(dateString + '-01')); // Add day for proper parsing
    /* istanbul ignore next */
    cov_lqxrdek68().s[16]++;
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short'
    });
  };
  /* istanbul ignore next */
  cov_lqxrdek68().s[17]++;
  var formatDateRange = function (startDate, endDate) {
    /* istanbul ignore next */
    cov_lqxrdek68().f[3]++;
    var start =
    /* istanbul ignore next */
    (cov_lqxrdek68().s[18]++, formatDate(startDate));
    var end =
    /* istanbul ignore next */
    (cov_lqxrdek68().s[19]++, endDate ?
    /* istanbul ignore next */
    (cov_lqxrdek68().b[4][0]++, formatDate(endDate)) :
    /* istanbul ignore next */
    (cov_lqxrdek68().b[4][1]++, 'Present'));
    /* istanbul ignore next */
    cov_lqxrdek68().s[20]++;
    return /* istanbul ignore next */(cov_lqxrdek68().b[6][0]++, start) &&
    /* istanbul ignore next */
    (cov_lqxrdek68().b[6][1]++, end) ?
    /* istanbul ignore next */
    (cov_lqxrdek68().b[5][0]++, "".concat(start, " - ").concat(end)) :
    /* istanbul ignore next */
    (cov_lqxrdek68().b[5][1]++, '');
  };
  /* istanbul ignore next */
  cov_lqxrdek68().s[21]++;
  var getLevelBadgeColor = function (level) {
    /* istanbul ignore next */
    cov_lqxrdek68().f[4]++;
    cov_lqxrdek68().s[22]++;
    switch (level) {
      case 'BEGINNER':
        /* istanbul ignore next */
        cov_lqxrdek68().b[7][0]++;
        cov_lqxrdek68().s[23]++;
        return 'bg-red-100 text-red-800';
      case 'INTERMEDIATE':
        /* istanbul ignore next */
        cov_lqxrdek68().b[7][1]++;
        cov_lqxrdek68().s[24]++;
        return 'bg-yellow-100 text-yellow-800';
      case 'ADVANCED':
        /* istanbul ignore next */
        cov_lqxrdek68().b[7][2]++;
        cov_lqxrdek68().s[25]++;
        return 'bg-blue-100 text-blue-800';
      case 'EXPERT':
        /* istanbul ignore next */
        cov_lqxrdek68().b[7][3]++;
        cov_lqxrdek68().s[26]++;
        return 'bg-green-100 text-green-800';
      default:
        /* istanbul ignore next */
        cov_lqxrdek68().b[7][4]++;
        cov_lqxrdek68().s[27]++;
        return 'bg-gray-100 text-gray-800';
    }
  };
  /* istanbul ignore next */
  cov_lqxrdek68().s[28]++;
  return (0, jsx_runtime_1.jsx)(card_1.Card, {
    className: "max-w-4xl mx-auto card-safe",
    children: (0, jsx_runtime_1.jsxs)(card_1.CardContent, {
      className: "p-8 space-y-6 content-safe",
      children: [(0, jsx_runtime_1.jsxs)("div", {
        className: "text-center space-y-2",
        children: [(0, jsx_runtime_1.jsx)("h1", {
          className: "text-3xl font-bold",
          children: (0, jsx_runtime_1.jsxs)(text_truncate_1.TextOverflow, {
            maxLines: 2,
            children: [resume.personalInfo.firstName, " ", resume.personalInfo.lastName]
          })
        }), (0, jsx_runtime_1.jsxs)("div", {
          className: "flex flex-wrap justify-center gap-4 text-sm text-muted-foreground",
          children: [
          /* istanbul ignore next */
          (cov_lqxrdek68().b[8][0]++, resume.personalInfo.email) &&
          /* istanbul ignore next */
          (cov_lqxrdek68().b[8][1]++, (0, jsx_runtime_1.jsxs)("div", {
            className: "flex items-center gap-1",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Mail, {
              className: "w-4 h-4"
            }), resume.personalInfo.email]
          })),
          /* istanbul ignore next */
          (cov_lqxrdek68().b[9][0]++, resume.personalInfo.phone) &&
          /* istanbul ignore next */
          (cov_lqxrdek68().b[9][1]++, (0, jsx_runtime_1.jsxs)("div", {
            className: "flex items-center gap-1",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Phone, {
              className: "w-4 h-4"
            }), resume.personalInfo.phone]
          })),
          /* istanbul ignore next */
          (cov_lqxrdek68().b[10][0]++, resume.personalInfo.location) &&
          /* istanbul ignore next */
          (cov_lqxrdek68().b[10][1]++, (0, jsx_runtime_1.jsxs)("div", {
            className: "flex items-center gap-1",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MapPin, {
              className: "w-4 h-4"
            }), resume.personalInfo.location]
          })),
          /* istanbul ignore next */
          (cov_lqxrdek68().b[11][0]++, resume.personalInfo.website) &&
          /* istanbul ignore next */
          (cov_lqxrdek68().b[11][1]++, (0, jsx_runtime_1.jsxs)("div", {
            className: "flex items-center gap-1",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Globe, {
              className: "w-4 h-4"
            }), (0, jsx_runtime_1.jsx)("a", {
              href: resume.personalInfo.website,
              target: "_blank",
              rel: "noopener noreferrer",
              className: "hover:underline",
              children: "Website"
            })]
          })),
          /* istanbul ignore next */
          (cov_lqxrdek68().b[12][0]++, resume.personalInfo.linkedIn) &&
          /* istanbul ignore next */
          (cov_lqxrdek68().b[12][1]++, (0, jsx_runtime_1.jsxs)("div", {
            className: "flex items-center gap-1",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Linkedin, {
              className: "w-4 h-4"
            }), (0, jsx_runtime_1.jsx)("a", {
              href: resume.personalInfo.linkedIn,
              target: "_blank",
              rel: "noopener noreferrer",
              className: "hover:underline",
              children: "LinkedIn"
            })]
          }))]
        })]
      }),
      /* istanbul ignore next */
      (cov_lqxrdek68().b[13][0]++, resume.summary) &&
      /* istanbul ignore next */
      (cov_lqxrdek68().b[13][1]++, (0, jsx_runtime_1.jsxs)("div", {
        children: [(0, jsx_runtime_1.jsx)("h2", {
          className: "text-xl font-semibold mb-3",
          children: "Professional Summary"
        }), (0, jsx_runtime_1.jsx)(text_truncate_1.SafeTextDisplay, {
          text: resume.summary,
          maxLength: 2000,
          maxLines: 6,
          className: "text-muted-foreground leading-relaxed"
        })]
      })),
      /* istanbul ignore next */
      (cov_lqxrdek68().b[14][0]++, resume.experience.length > 0) &&
      /* istanbul ignore next */
      (cov_lqxrdek68().b[14][1]++, (0, jsx_runtime_1.jsxs)("div", {
        children: [(0, jsx_runtime_1.jsx)("h2", {
          className: "text-xl font-semibold mb-4",
          children: "Work Experience"
        }), (0, jsx_runtime_1.jsx)("div", {
          className: "space-y-6",
          children: resume.experience.map(function (exp) {
            /* istanbul ignore next */
            cov_lqxrdek68().f[5]++;
            cov_lqxrdek68().s[29]++;
            return (0, jsx_runtime_1.jsxs)("div", {
              children: [(0, jsx_runtime_1.jsxs)("div", {
                className: "flex justify-between items-start mb-2",
                children: [(0, jsx_runtime_1.jsxs)("div", {
                  className: "flex-1 mr-4",
                  children: [(0, jsx_runtime_1.jsx)(text_truncate_1.TextOverflow, {
                    maxLines: 2,
                    children: (0, jsx_runtime_1.jsx)("h3", {
                      className: "font-semibold text-lg",
                      children: exp.position
                    })
                  }), (0, jsx_runtime_1.jsx)(text_truncate_1.TextOverflow, {
                    maxLines: 1,
                    children: (0, jsx_runtime_1.jsx)("p", {
                      className: "text-muted-foreground font-medium",
                      children: exp.company
                    })
                  })]
                }), (0, jsx_runtime_1.jsx)("div", {
                  className: "text-sm text-muted-foreground text-right",
                  children: formatDateRange(exp.startDate, exp.endDate)
                })]
              }),
              /* istanbul ignore next */
              (cov_lqxrdek68().b[15][0]++, exp.description) &&
              /* istanbul ignore next */
              (cov_lqxrdek68().b[15][1]++, (0, jsx_runtime_1.jsx)(text_truncate_1.SafeTextDisplay, {
                text: exp.description,
                maxLength: 500,
                maxLines: 4,
                className: "text-muted-foreground mb-2 leading-relaxed"
              })),
              /* istanbul ignore next */
              (cov_lqxrdek68().b[16][0]++, exp.achievements) &&
              /* istanbul ignore next */
              (cov_lqxrdek68().b[16][1]++, exp.achievements.length > 0) &&
              /* istanbul ignore next */
              (cov_lqxrdek68().b[16][2]++, (0, jsx_runtime_1.jsx)("ul", {
                className: "list-disc list-inside space-y-1 text-muted-foreground",
                children: exp.achievements.map(function (achievement, index) {
                  /* istanbul ignore next */
                  cov_lqxrdek68().f[6]++;
                  cov_lqxrdek68().s[30]++;
                  return (0, jsx_runtime_1.jsx)("li", {
                    children: achievement
                  }, index);
                })
              }))]
            }, exp.id);
          })
        })]
      })),
      /* istanbul ignore next */
      (cov_lqxrdek68().b[17][0]++, resume.education.length > 0) &&
      /* istanbul ignore next */
      (cov_lqxrdek68().b[17][1]++, (0, jsx_runtime_1.jsxs)("div", {
        children: [(0, jsx_runtime_1.jsx)("h2", {
          className: "text-xl font-semibold mb-4",
          children: "Education"
        }), (0, jsx_runtime_1.jsx)("div", {
          className: "space-y-4",
          children: resume.education.map(function (edu) {
            /* istanbul ignore next */
            cov_lqxrdek68().f[7]++;
            cov_lqxrdek68().s[31]++;
            return (0, jsx_runtime_1.jsxs)("div", {
              children: [(0, jsx_runtime_1.jsxs)("div", {
                className: "flex justify-between items-start mb-1",
                children: [(0, jsx_runtime_1.jsxs)("div", {
                  children: [(0, jsx_runtime_1.jsx)("h3", {
                    className: "font-semibold",
                    children: edu.degree
                  }),
                  /* istanbul ignore next */
                  (cov_lqxrdek68().b[18][0]++, edu.field) &&
                  /* istanbul ignore next */
                  (cov_lqxrdek68().b[18][1]++, (0, jsx_runtime_1.jsxs)("p", {
                    className: "text-muted-foreground",
                    children: ["in ", edu.field]
                  })), (0, jsx_runtime_1.jsx)("p", {
                    className: "text-muted-foreground font-medium",
                    children: edu.institution
                  })]
                }), (0, jsx_runtime_1.jsx)("div", {
                  className: "text-sm text-muted-foreground text-right",
                  children: formatDateRange(edu.startDate, edu.endDate)
                })]
              }), (0, jsx_runtime_1.jsxs)("div", {
                className: "flex gap-4 text-sm text-muted-foreground",
                children: [
                /* istanbul ignore next */
                (cov_lqxrdek68().b[19][0]++, edu.gpa) &&
                /* istanbul ignore next */
                (cov_lqxrdek68().b[19][1]++, (0, jsx_runtime_1.jsxs)("span", {
                  children: ["GPA: ", edu.gpa]
                })),
                /* istanbul ignore next */
                (cov_lqxrdek68().b[20][0]++, edu.honors) &&
                /* istanbul ignore next */
                (cov_lqxrdek68().b[20][1]++, (0, jsx_runtime_1.jsx)("span", {
                  children: edu.honors
                }))]
              })]
            }, edu.id);
          })
        })]
      })),
      /* istanbul ignore next */
      (cov_lqxrdek68().b[21][0]++, resume.skills.length > 0) &&
      /* istanbul ignore next */
      (cov_lqxrdek68().b[21][1]++, (0, jsx_runtime_1.jsxs)("div", {
        children: [(0, jsx_runtime_1.jsx)("h2", {
          className: "text-xl font-semibold mb-4",
          children: "Skills"
        }), (0, jsx_runtime_1.jsx)("div", {
          className: "space-y-4",
          children: Object.entries(resume.skills.reduce(function (acc, skill) {
            /* istanbul ignore next */
            cov_lqxrdek68().f[8]++;
            var category =
            /* istanbul ignore next */
            (cov_lqxrdek68().s[32]++,
            /* istanbul ignore next */
            (cov_lqxrdek68().b[22][0]++, skill.category) ||
            /* istanbul ignore next */
            (cov_lqxrdek68().b[22][1]++, 'Other'));
            /* istanbul ignore next */
            cov_lqxrdek68().s[33]++;
            if (!acc[category]) {
              /* istanbul ignore next */
              cov_lqxrdek68().b[23][0]++;
              cov_lqxrdek68().s[34]++;
              acc[category] = [];
            } else
            /* istanbul ignore next */
            {
              cov_lqxrdek68().b[23][1]++;
            }
            cov_lqxrdek68().s[35]++;
            acc[category].push(skill);
            /* istanbul ignore next */
            cov_lqxrdek68().s[36]++;
            return acc;
          }, {})).map(function (_a) {
            /* istanbul ignore next */
            cov_lqxrdek68().f[9]++;
            var category =
              /* istanbul ignore next */
              (cov_lqxrdek68().s[37]++, _a[0]),
              categorySkills =
              /* istanbul ignore next */
              (cov_lqxrdek68().s[38]++, _a[1]);
            /* istanbul ignore next */
            cov_lqxrdek68().s[39]++;
            return (0, jsx_runtime_1.jsxs)("div", {
              children: [(0, jsx_runtime_1.jsx)("h3", {
                className: "font-medium text-sm text-muted-foreground uppercase tracking-wide mb-2",
                children: category
              }), (0, jsx_runtime_1.jsx)("div", {
                className: "flex flex-wrap gap-2",
                children: categorySkills.map(function (skill) {
                  /* istanbul ignore next */
                  cov_lqxrdek68().f[10]++;
                  cov_lqxrdek68().s[40]++;
                  return (0, jsx_runtime_1.jsxs)(badge_1.Badge, {
                    variant: "outline",
                    className: getLevelBadgeColor(skill.level),
                    children: [skill.name,
                    /* istanbul ignore next */
                    (cov_lqxrdek68().b[24][0]++, skill.level) &&
                    /* istanbul ignore next */
                    (cov_lqxrdek68().b[24][1]++, (0, jsx_runtime_1.jsxs)("span", {
                      className: "ml-1 text-xs opacity-75",
                      children: ["(", skill.level.toLowerCase(), ")"]
                    }))]
                  }, skill.id);
                })
              })]
            }, category);
          })
        })]
      })), (0, jsx_runtime_1.jsx)(separator_1.Separator, {}), (0, jsx_runtime_1.jsx)("div", {
        className: "text-center text-xs text-muted-foreground",
        children: (0, jsx_runtime_1.jsxs)("p", {
          children: ["Template: ", resume.template, " \u2022 Created with Resume Builder"]
        })
      })]
    })
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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