135ce624163b6352c16c039834a8933f
"use strict";
/**
 * Gemini Service Skill Gap Tests
 *
 * Tests Gemini Service Skill Gap functionality, business logic, and edge cases.
 *
 * @category unit
 * @requires Unit testing utilities, mocking
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
// Mock the GeminiService class
globals_1.jest.mock('@/lib/services/geminiService', function () {
    var actualGeminiService = globals_1.jest.requireActual('@/lib/services/geminiService');
    return __assign(__assign({}, actualGeminiService), { geminiService: __assign(__assign({}, actualGeminiService.geminiService), { generateContent: mockGenerateContent, analyzeComprehensiveSkillGap: globals_1.jest.fn(), generatePersonalizedLearningPlan: globals_1.jest.fn(), analyzeSkillMarketTrends: globals_1.jest.fn(), validateSkillAssessment: globals_1.jest.fn() }) });
});
// Mock environment variables before importing geminiService
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-testing-purposes-only';
var geminiService_1 = require("@/lib/services/geminiService");
// Mock the generateContent method
var mockGenerateContent = globals_1.jest.fn();
var mockGeminiService = geminiService_1.geminiService;
(0, globals_1.describe)('GeminiService - Skill Gap Analysis', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.afterEach)(function () {
        globals_1.jest.resetAllMocks();
    });
    (0, globals_1.describe)('analyzeComprehensiveSkillGap', function () {
        var mockCurrentSkills = [
            {
                skillName: 'JavaScript',
                selfRating: 7,
                confidenceLevel: 8,
                yearsOfExperience: 3,
            },
            {
                skillName: 'React',
                selfRating: 6,
                confidenceLevel: 7,
                yearsOfExperience: 2,
            },
        ];
        var mockTargetCareerPath = {
            careerPathName: 'Full Stack Developer',
            targetLevel: 'ADVANCED',
        };
        var mockPreferences = {
            timeframe: 'ONE_YEAR',
            hoursPerWeek: 10,
            learningStyle: ['VISUAL', 'HANDS_ON'],
            budget: 'FREEMIUM',
            focusAreas: ['Backend Development'],
        };
        var mockCareerPathData = {
            requiredSkills: [
                { name: 'JavaScript' },
                { name: 'Node.js' },
                { name: 'PostgreSQL' },
            ],
            learningResources: [
                {
                    title: 'Node.js Fundamentals',
                    type: 'COURSE',
                    skillLevel: 'INTERMEDIATE',
                    skills: ['Node.js'],
                },
            ],
        };
        (0, globals_1.it)('should analyze comprehensive skill gap successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                skillGaps: [
                                    {
                                        skillId: 'generated-id-1',
                                        skillName: 'Node.js',
                                        currentLevel: 2,
                                        targetLevel: 8,
                                        gapSeverity: 'HIGH',
                                        priority: 90,
                                        estimatedLearningTime: 120,
                                        marketDemand: 'VERY_HIGH',
                                        salaryImpact: 15,
                                    },
                                ],
                                learningPlan: {
                                    totalEstimatedHours: 120,
                                    milestones: [
                                        {
                                            month: 3,
                                            skills: ['Node.js Basics'],
                                            estimatedHours: 60,
                                            learningPaths: ['Backend Development Path'],
                                        },
                                    ],
                                    recommendedResources: [
                                        {
                                            resourceId: 'resource-1',
                                            resourceType: 'COURSE',
                                            priority: 'HIGH',
                                            skillsAddressed: ['Node.js'],
                                            estimatedHours: 60,
                                        },
                                    ],
                                },
                                careerReadiness: {
                                    currentScore: 65,
                                    targetScore: 85,
                                    improvementPotential: 20,
                                    timeToTarget: 8,
                                },
                            },
                        };
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeComprehensiveSkillGap(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.skillGaps).toHaveLength(1);
                        (0, globals_1.expect)(result.data.skillGaps[0].skillName).toBe('Node.js');
                        (0, globals_1.expect)(result.data.skillGaps[0].gapSeverity).toBe('HIGH');
                        (0, globals_1.expect)(result.data.learningPlan.totalEstimatedHours).toBe(120);
                        (0, globals_1.expect)(result.data.careerReadiness.currentScore).toBe(65);
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle AI service failures', function () { return __awaiter(void 0, void 0, void 0, function () {
            var errorResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        errorResponse = {
                            success: false,
                            error: 'AI service temporarily unavailable',
                        };
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(errorResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeComprehensiveSkillGap(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('AI service temporarily unavailable');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should include market insights when requested', function () { return __awaiter(void 0, void 0, void 0, function () {
            var responseWithMarketData, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        responseWithMarketData = {
                            success: true,
                            data: {
                                skillGaps: [],
                                learningPlan: {
                                    totalEstimatedHours: 0,
                                    milestones: [],
                                    recommendedResources: [],
                                },
                                careerReadiness: {
                                    currentScore: 70,
                                    targetScore: 85,
                                    improvementPotential: 15,
                                    timeToTarget: 6,
                                },
                                marketInsights: {
                                    industryTrends: [
                                        {
                                            skill: 'Node.js',
                                            trend: 'GROWING',
                                            demandLevel: 'VERY_HIGH',
                                        },
                                    ],
                                    salaryProjections: {
                                        currentEstimate: 75000,
                                        targetEstimate: 95000,
                                        improvementPotential: 26.7,
                                    },
                                },
                            },
                        };
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(responseWithMarketData);
                        return [4 /*yield*/, mockGeminiService.analyzeComprehensiveSkillGap(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.marketInsights).toBeDefined();
                        (0, globals_1.expect)(result.data.marketInsights.industryTrends).toHaveLength(1);
                        (0, globals_1.expect)(result.data.marketInsights.salaryProjections.improvementPotential).toBe(26.7);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('generatePersonalizedLearningPlan', function () {
        var mockSkillGaps = [
            {
                skillName: 'Node.js',
                currentLevel: 3,
                targetLevel: 8,
                gapSeverity: 'HIGH',
                priority: 90,
                estimatedLearningTime: 120,
            },
        ];
        var mockUserPreferences = {
            timeframe: 'ONE_YEAR',
            hoursPerWeek: 10,
            learningStyle: ['VISUAL'],
            budget: 'FREEMIUM',
            focusAreas: ['Backend Development'],
        };
        var mockMarketData = [
            {
                skillName: 'Node.js',
                demandLevel: 'VERY_HIGH',
                growthTrend: 'GROWING',
                salaryImpact: 15,
            },
        ];
        (0, globals_1.it)('should generate personalized learning plan successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                learningPlan: {
                                    totalDuration: '12 months',
                                    totalHours: 120,
                                    weeklyCommitment: 10,
                                    phases: [
                                        {
                                            phaseNumber: 1,
                                            phaseName: 'Foundation',
                                            duration: '3 months',
                                            objectives: ['Learn Node.js fundamentals'],
                                            skills: [
                                                {
                                                    skillName: 'Node.js',
                                                    currentLevel: 3,
                                                    targetLevel: 6,
                                                    hoursAllocated: 60,
                                                    learningApproach: 'Hands-on projects with video tutorials',
                                                    resources: [
                                                        {
                                                            type: 'COURSE',
                                                            title: 'Node.js Complete Guide',
                                                            description: 'Comprehensive Node.js course',
                                                            estimatedHours: 40,
                                                            cost: 'FREEMIUM',
                                                            difficulty: 'INTERMEDIATE',
                                                            learningStyle: ['VISUAL'],
                                                            priority: 'HIGH',
                                                        },
                                                    ],
                                                    milestones: [
                                                        {
                                                            week: 4,
                                                            milestone: 'Build first Node.js API',
                                                            assessmentMethod: 'Project completion',
                                                        },
                                                    ],
                                                },
                                            ],
                                            practiceProjects: [
                                                {
                                                    projectName: 'REST API with Node.js',
                                                    description: 'Build a complete REST API',
                                                    skillsApplied: ['Node.js', 'Express.js'],
                                                    estimatedHours: 20,
                                                    difficulty: 'INTERMEDIATE',
                                                    deliverables: ['Working API', 'Documentation'],
                                                },
                                            ],
                                        },
                                    ],
                                },
                            },
                        };
                        mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.generatePersonalizedLearningPlan(mockSkillGaps, mockUserPreferences, mockMarketData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.learningPlan.totalHours).toBe(120);
                        (0, globals_1.expect)(result.data.learningPlan.phases).toHaveLength(1);
                        (0, globals_1.expect)(result.data.learningPlan.phases[0].skills[0].skillName).toBe('Node.js');
                        (0, globals_1.expect)(result.data.learningPlan.phases[0].practiceProjects).toHaveLength(1);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should optimize for user preferences', function () { return __awaiter(void 0, void 0, void 0, function () {
            var visualLearnerResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        visualLearnerResponse = {
                            success: true,
                            data: {
                                learningPlan: {
                                    phases: [
                                        {
                                            skills: [
                                                {
                                                    skillName: 'Node.js',
                                                    resources: [
                                                        {
                                                            type: 'COURSE',
                                                            learningStyle: ['VISUAL'],
                                                            priority: 'HIGH',
                                                        },
                                                    ],
                                                },
                                            ],
                                        },
                                    ],
                                },
                            },
                        };
                        mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(visualLearnerResponse);
                        return [4 /*yield*/, mockGeminiService.generatePersonalizedLearningPlan(mockSkillGaps, mockUserPreferences, mockMarketData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.learningPlan.phases[0].skills[0].resources[0].learningStyle).toContain('VISUAL');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('analyzeSkillMarketTrends', function () {
        var mockSkills = ['JavaScript', 'React', 'Node.js'];
        var mockTargetIndustry = 'Technology';
        var mockRegion = 'North America';
        (0, globals_1.it)('should analyze skill market trends successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                marketAnalysis: {
                                    analysisDate: globals_1.expect.any(String),
                                    region: 'North America',
                                    industry: 'Technology',
                                    overallMarketHealth: 'EXCELLENT',
                                },
                                skillTrends: [
                                    {
                                        skillName: 'JavaScript',
                                        demandLevel: 'VERY_HIGH',
                                        growthTrend: 'STABLE',
                                        marketSaturation: 'MODERATE',
                                        averageSalaryImpact: 20,
                                        jobPostingsGrowth: 5,
                                        futureOutlook: {
                                            nextYear: 'Continued high demand',
                                            nextFiveYears: 'Evolving with new frameworks',
                                            emergingOpportunities: ['Full-stack development'],
                                            potentialThreats: ['Framework fragmentation'],
                                        },
                                    },
                                ],
                            },
                        };
                        mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeSkillMarketTrends(mockSkills, mockTargetIndustry, mockRegion)];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.marketAnalysis.region).toBe('North America');
                        (0, globals_1.expect)(result.data.marketAnalysis.industry).toBe('Technology');
                        (0, globals_1.expect)(result.data.skillTrends).toHaveLength(1);
                        (0, globals_1.expect)(result.data.skillTrends[0].skillName).toBe('JavaScript');
                        (0, globals_1.expect)(result.data.skillTrends[0].demandLevel).toBe('VERY_HIGH');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle empty skills array', function () { return __awaiter(void 0, void 0, void 0, function () {
            var emptySkillsResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        emptySkillsResponse = {
                            success: true,
                            data: {
                                marketAnalysis: {
                                    region: 'GLOBAL',
                                    industry: 'Technology',
                                    overallMarketHealth: 'GOOD',
                                },
                                skillTrends: [],
                            },
                        };
                        mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(emptySkillsResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeSkillMarketTrends([], mockTargetIndustry, 'GLOBAL')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.skillTrends).toHaveLength(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('validateSkillAssessment', function () {
        var mockSkillName = 'JavaScript';
        var mockSelfRating = 7;
        var mockUserContext = {
            experienceLevel: 'INTERMEDIATE',
            industry: 'Technology',
            yearsOfExperience: 3,
            relatedSkills: ['React', 'Node.js'],
            previousAssessments: [
                {
                    skillName: 'React',
                    rating: 6,
                    date: '2024-01-01',
                },
            ],
        };
        (0, globals_1.it)('should validate skill assessment successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                validationResult: {
                                    assessmentAccuracy: 'ACCURATE',
                                    confidenceLevel: 85,
                                    reasoning: 'Rating aligns well with experience level and related skills',
                                    suggestedRating: 7,
                                    ratingJustification: 'Consistent with 3 years experience and related skill levels',
                                },
                                skillAnalysis: {
                                    skillComplexity: 'INTERMEDIATE',
                                    learningCurve: 'MODERATE',
                                    marketValue: 'VERY_HIGH',
                                    industryRelevance: 'CRITICAL',
                                    skillCategory: 'TECHNICAL',
                                },
                                assessmentGuidance: {
                                    ratingCriteria: {
                                        'level1to2': 'Basic syntax understanding',
                                        'level3to4': 'Can write simple programs',
                                        'level5to6': 'Comfortable with frameworks',
                                        'level7to8': 'Advanced patterns and optimization',
                                        'level9to10': 'Expert-level architecture and mentoring',
                                    },
                                    selfAssessmentTips: [
                                        'Compare with concrete examples',
                                        'Consider real-world project complexity',
                                    ],
                                    validationMethods: [
                                        {
                                            method: 'Code Review',
                                            description: 'Have experienced developer review your code',
                                            timeRequired: '2-3 hours',
                                            cost: 'FREE',
                                        },
                                    ],
                                },
                            },
                        };
                        mockGeminiService.validateSkillAssessment.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.validateSkillAssessment(mockSkillName, mockSelfRating, mockUserContext)];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.validationResult.assessmentAccuracy).toBe('ACCURATE');
                        (0, globals_1.expect)(result.data.validationResult.suggestedRating).toBe(7);
                        (0, globals_1.expect)(result.data.skillAnalysis.skillComplexity).toBe('INTERMEDIATE');
                        (0, globals_1.expect)(result.data.assessmentGuidance.validationMethods).toHaveLength(1);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should identify overestimated ratings', function () { return __awaiter(void 0, void 0, void 0, function () {
            var overestimatedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        overestimatedResponse = {
                            success: true,
                            data: {
                                validationResult: {
                                    assessmentAccuracy: 'SIGNIFICANTLY_HIGH',
                                    confidenceLevel: 60,
                                    reasoning: 'Rating seems high for stated experience level',
                                    suggestedRating: 5,
                                    ratingJustification: 'More realistic for 3 years experience',
                                },
                            },
                        };
                        mockGeminiService.validateSkillAssessment.mockResolvedValue(overestimatedResponse);
                        return [4 /*yield*/, mockGeminiService.validateSkillAssessment(mockSkillName, 9, __assign(__assign({}, mockUserContext), { yearsOfExperience: 1 }))];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.validationResult.assessmentAccuracy).toBe('SIGNIFICANTLY_HIGH');
                        (0, globals_1.expect)(result.data.validationResult.suggestedRating).toBe(5);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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