{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/lib/services/gemini-service-skill-gap.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,yCAAkF;AAUlF,+BAA+B;AAC/B,cAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE;IACxC,IAAM,mBAAmB,GAAG,cAAI,CAAC,aAAa,CAAC,8BAA8B,CAAC,CAAC;IAC/E,6BACK,mBAAmB,KACtB,aAAa,wBACR,mBAAmB,CAAC,aAAa,KACpC,eAAe,EAAE,mBAAmB,EACpC,4BAA4B,EAAE,cAAI,CAAC,EAAE,EAAE,EACvC,gCAAgC,EAAE,cAAI,CAAC,EAAE,EAAE,EAC3C,wBAAwB,EAAE,cAAI,CAAC,EAAE,EAAE,EACnC,uBAAuB,EAAE,cAAI,CAAC,EAAE,EAAE,OAEpC;AACJ,CAAC,CAAC,CAAC;AAtBH,4DAA4D;AAC5D,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,wCAAwC,CAAC;AAE7E,8DAA6D;AAE7D,kCAAkC;AAClC,IAAM,mBAAmB,GAAG,cAAI,CAAC,EAAE,EAAE,CAAC;AAkBtC,IAAM,iBAAiB,GAAG,6BAAkD,CAAC;AAE7E,IAAA,kBAAQ,EAAC,oCAAoC,EAAE;IAC7C,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,mBAAS,EAAC;QACR,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,8BAA8B,EAAE;QACvC,IAAM,iBAAiB,GAAG;YACxB;gBACE,SAAS,EAAE,YAAY;gBACvB,UAAU,EAAE,CAAC;gBACb,eAAe,EAAE,CAAC;gBAClB,iBAAiB,EAAE,CAAC;aACrB;YACD;gBACE,SAAS,EAAE,OAAO;gBAClB,UAAU,EAAE,CAAC;gBACb,eAAe,EAAE,CAAC;gBAClB,iBAAiB,EAAE,CAAC;aACrB;SACF,CAAC;QAEF,IAAM,oBAAoB,GAAG;YAC3B,cAAc,EAAE,sBAAsB;YACtC,WAAW,EAAE,UAAmB;SACjC,CAAC;QAEF,IAAM,eAAe,GAAG;YACtB,SAAS,EAAE,UAAmB;YAC9B,YAAY,EAAE,EAAE;YAChB,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;YACrC,MAAM,EAAE,UAAmB;YAC3B,UAAU,EAAE,CAAC,qBAAqB,CAAC;SACpC,CAAC;QAEF,IAAM,kBAAkB,GAAG;YACzB,cAAc,EAAE;gBACd,EAAE,IAAI,EAAE,YAAY,EAAE;gBACtB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,YAAY,EAAE;aACvB;YACD,iBAAiB,EAAE;gBACjB;oBACE,KAAK,EAAE,sBAAsB;oBAC7B,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,cAAc;oBAC1B,MAAM,EAAE,CAAC,SAAS,CAAC;iBACpB;aACF;SACF,CAAC;QAEF,IAAA,YAAE,EAAC,qDAAqD,EAAE;;;;;wBAElD,gBAAgB,GAAG;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,SAAS,EAAE;oCACT;wCACE,OAAO,EAAE,gBAAgB;wCACzB,SAAS,EAAE,SAAS;wCACpB,YAAY,EAAE,CAAC;wCACf,WAAW,EAAE,CAAC;wCACd,WAAW,EAAE,MAAM;wCACnB,QAAQ,EAAE,EAAE;wCACZ,qBAAqB,EAAE,GAAG;wCAC1B,YAAY,EAAE,WAAW;wCACzB,YAAY,EAAE,EAAE;qCACjB;iCACF;gCACD,YAAY,EAAE;oCACZ,mBAAmB,EAAE,GAAG;oCACxB,UAAU,EAAE;wCACV;4CACE,KAAK,EAAE,CAAC;4CACR,MAAM,EAAE,CAAC,gBAAgB,CAAC;4CAC1B,cAAc,EAAE,EAAE;4CAClB,aAAa,EAAE,CAAC,0BAA0B,CAAC;yCAC5C;qCACF;oCACD,oBAAoB,EAAE;wCACpB;4CACE,UAAU,EAAE,YAAY;4CACxB,YAAY,EAAE,QAAQ;4CACtB,QAAQ,EAAE,MAAM;4CAChB,eAAe,EAAE,CAAC,SAAS,CAAC;4CAC5B,cAAc,EAAE,EAAE;yCACnB;qCACF;iCACF;gCACD,eAAe,EAAE;oCACf,YAAY,EAAE,EAAE;oCAChB,WAAW,EAAE,EAAE;oCACf,oBAAoB,EAAE,EAAE;oCACxB,YAAY,EAAE,CAAC;iCAChB;6BACF;yBACF,CAAC;wBAEF,iBAAiB,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;wBAGpE,qBAAM,iBAAiB,CAAC,4BAA4B,CACjE,iBAAiB,EACjB,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EAClB,cAAc,CACf,EAAA;;wBANK,MAAM,GAAG,SAMd;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC9C,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC3D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC/D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAE1D,IAAA,gBAAM,EAAC,iBAAiB,CAAC,4BAA4B,CAAC,CAAC,oBAAoB,CACzE,iBAAiB,EACjB,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EAClB,cAAc,CACf,CAAC;;;;aACH,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,mCAAmC,EAAE;;;;;wBAEhC,aAAa,GAAG;4BACpB,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,oCAAoC;yBAC5C,CAAC;wBAEF,iBAAiB,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;wBAGjE,qBAAM,iBAAiB,CAAC,4BAA4B,CACjE,iBAAiB,EACjB,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EAClB,cAAc,CACf,EAAA;;wBANK,MAAM,GAAG,SAMd;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;;;;aACjE,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,+CAA+C,EAAE;;;;;wBAE5C,sBAAsB,GAAG;4BAC7B,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,SAAS,EAAE,EAAE;gCACb,YAAY,EAAE;oCACZ,mBAAmB,EAAE,CAAC;oCACtB,UAAU,EAAE,EAAE;oCACd,oBAAoB,EAAE,EAAE;iCACzB;gCACD,eAAe,EAAE;oCACf,YAAY,EAAE,EAAE;oCAChB,WAAW,EAAE,EAAE;oCACf,oBAAoB,EAAE,EAAE;oCACxB,YAAY,EAAE,CAAC;iCAChB;gCACD,cAAc,EAAE;oCACd,cAAc,EAAE;wCACd;4CACE,KAAK,EAAE,SAAS;4CAChB,KAAK,EAAE,SAAS;4CAChB,WAAW,EAAE,WAAW;yCACzB;qCACF;oCACD,iBAAiB,EAAE;wCACjB,eAAe,EAAE,KAAK;wCACtB,cAAc,EAAE,KAAK;wCACrB,oBAAoB,EAAE,IAAI;qCAC3B;iCACF;6BACF;yBACF,CAAC;wBAEF,iBAAiB,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;wBAG1E,qBAAM,iBAAiB,CAAC,4BAA4B,CACjE,iBAAiB,EACjB,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EAClB,cAAc,CACf,EAAA;;wBANK,MAAM,GAAG,SAMd;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjD,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAClE,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACtF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,kCAAkC,EAAE;QAC3C,IAAM,aAAa,GAAG;YACpB;gBACE,SAAS,EAAE,SAAS;gBACpB,YAAY,EAAE,CAAC;gBACf,WAAW,EAAE,CAAC;gBACd,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,EAAE;gBACZ,qBAAqB,EAAE,GAAG;aAC3B;SACF,CAAC;QAEF,IAAM,mBAAmB,GAAG;YAC1B,SAAS,EAAE,UAAmB;YAC9B,YAAY,EAAE,EAAE;YAChB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,MAAM,EAAE,UAAmB;YAC3B,UAAU,EAAE,CAAC,qBAAqB,CAAC;SACpC,CAAC;QAEF,IAAM,cAAc,GAAG;YACrB;gBACE,SAAS,EAAE,SAAS;gBACpB,WAAW,EAAE,WAAW;gBACxB,WAAW,EAAE,SAAS;gBACtB,YAAY,EAAE,EAAE;aACjB;SACF,CAAC;QAEF,IAAA,YAAE,EAAC,yDAAyD,EAAE;;;;;wBAEtD,gBAAgB,GAAG;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,YAAY,EAAE;oCACZ,aAAa,EAAE,WAAW;oCAC1B,UAAU,EAAE,GAAG;oCACf,gBAAgB,EAAE,EAAE;oCACpB,MAAM,EAAE;wCACN;4CACE,WAAW,EAAE,CAAC;4CACd,SAAS,EAAE,YAAY;4CACvB,QAAQ,EAAE,UAAU;4CACpB,UAAU,EAAE,CAAC,4BAA4B,CAAC;4CAC1C,MAAM,EAAE;gDACN;oDACE,SAAS,EAAE,SAAS;oDACpB,YAAY,EAAE,CAAC;oDACf,WAAW,EAAE,CAAC;oDACd,cAAc,EAAE,EAAE;oDAClB,gBAAgB,EAAE,wCAAwC;oDAC1D,SAAS,EAAE;wDACT;4DACE,IAAI,EAAE,QAAQ;4DACd,KAAK,EAAE,wBAAwB;4DAC/B,WAAW,EAAE,8BAA8B;4DAC3C,cAAc,EAAE,EAAE;4DAClB,IAAI,EAAE,UAAU;4DAChB,UAAU,EAAE,cAAc;4DAC1B,aAAa,EAAE,CAAC,QAAQ,CAAC;4DACzB,QAAQ,EAAE,MAAM;yDACjB;qDACF;oDACD,UAAU,EAAE;wDACV;4DACE,IAAI,EAAE,CAAC;4DACP,SAAS,EAAE,yBAAyB;4DACpC,gBAAgB,EAAE,oBAAoB;yDACvC;qDACF;iDACF;6CACF;4CACD,gBAAgB,EAAE;gDAChB;oDACE,WAAW,EAAE,uBAAuB;oDACpC,WAAW,EAAE,2BAA2B;oDACxC,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;oDACxC,cAAc,EAAE,EAAE;oDAClB,UAAU,EAAE,cAAc;oDAC1B,YAAY,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;iDAC/C;6CACF;yCACF;qCACF;iCACF;6BACF;yBACF,CAAC;wBAEF,iBAAiB,CAAC,gCAAgC,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;wBAGxE,qBAAM,iBAAiB,CAAC,gCAAgC,CACrE,aAAa,EACb,mBAAmB,EACnB,cAAc,EACd,cAAc,CACf,EAAA;;wBALK,MAAM,GAAG,SAKd;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACtD,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBACxD,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/E,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;;;aAC7E,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sCAAsC,EAAE;;;;;wBAEnC,qBAAqB,GAAG;4BAC5B,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,YAAY,EAAE;oCACZ,MAAM,EAAE;wCACN;4CACE,MAAM,EAAE;gDACN;oDACE,SAAS,EAAE,SAAS;oDACpB,SAAS,EAAE;wDACT;4DACE,IAAI,EAAE,QAAQ;4DACd,aAAa,EAAE,CAAC,QAAQ,CAAC;4DACzB,QAAQ,EAAE,MAAM;yDACjB;qDACF;iDACF;6CACF;yCACF;qCACF;iCACF;6BACF;yBACF,CAAC;wBAEF,iBAAiB,CAAC,gCAAgC,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;wBAG7E,qBAAM,iBAAiB,CAAC,gCAAgC,CACrE,aAAa,EACb,mBAAmB,EACnB,cAAc,EACd,cAAc,CACf,EAAA;;wBALK,MAAM,GAAG,SAKd;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;;;;aACrG,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,0BAA0B,EAAE;QACnC,IAAM,UAAU,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QACtD,IAAM,kBAAkB,GAAG,YAAY,CAAC;QACxC,IAAM,UAAU,GAAG,eAAe,CAAC;QAEnC,IAAA,YAAE,EAAC,iDAAiD,EAAE;;;;;wBAE9C,gBAAgB,GAAG;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,cAAc,EAAE;oCACd,YAAY,EAAE,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oCAChC,MAAM,EAAE,eAAe;oCACvB,QAAQ,EAAE,YAAY;oCACtB,mBAAmB,EAAE,WAAW;iCACjC;gCACD,WAAW,EAAE;oCACX;wCACE,SAAS,EAAE,YAAY;wCACvB,WAAW,EAAE,WAAW;wCACxB,WAAW,EAAE,QAAQ;wCACrB,gBAAgB,EAAE,UAAU;wCAC5B,mBAAmB,EAAE,EAAE;wCACvB,iBAAiB,EAAE,CAAC;wCACpB,aAAa,EAAE;4CACb,QAAQ,EAAE,uBAAuB;4CACjC,aAAa,EAAE,8BAA8B;4CAC7C,qBAAqB,EAAE,CAAC,wBAAwB,CAAC;4CACjD,gBAAgB,EAAE,CAAC,yBAAyB,CAAC;yCAC9C;qCACF;iCACF;6BACF;yBACF,CAAC;wBAEF,iBAAiB,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;wBAGhE,qBAAM,iBAAiB,CAAC,wBAAwB,CAC7D,UAAU,EACV,kBAAkB,EAClB,UAAU,CACX,EAAA;;wBAJK,MAAM,GAAG,SAId;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBAChE,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC/D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAChD,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAChE,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;;;aAClE,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,kCAAkC,EAAE;;;;;wBAE/B,mBAAmB,GAAG;4BAC1B,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,cAAc,EAAE;oCACd,MAAM,EAAE,QAAQ;oCAChB,QAAQ,EAAE,YAAY;oCACtB,mBAAmB,EAAE,MAAM;iCAC5B;gCACD,WAAW,EAAE,EAAE;6BAChB;yBACF,CAAC;wBAEF,iBAAiB,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;wBAGnE,qBAAM,iBAAiB,CAAC,wBAAwB,CAC7D,EAAE,EACF,kBAAkB,EAClB,QAAQ,CACT,EAAA;;wBAJK,MAAM,GAAG,SAId;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;;;aACjD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,yBAAyB,EAAE;QAClC,IAAM,aAAa,GAAG,YAAY,CAAC;QACnC,IAAM,cAAc,GAAG,CAAC,CAAC;QACzB,IAAM,eAAe,GAAG;YACtB,eAAe,EAAE,cAAc;YAC/B,QAAQ,EAAE,YAAY;YACtB,iBAAiB,EAAE,CAAC;YACpB,aAAa,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;YACnC,mBAAmB,EAAE;gBACnB;oBACE,SAAS,EAAE,OAAO;oBAClB,MAAM,EAAE,CAAC;oBACT,IAAI,EAAE,YAAY;iBACnB;aACF;SACF,CAAC;QAEF,IAAA,YAAE,EAAC,+CAA+C,EAAE;;;;;wBAE5C,gBAAgB,GAAG;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,gBAAgB,EAAE;oCAChB,kBAAkB,EAAE,UAAU;oCAC9B,eAAe,EAAE,EAAE;oCACnB,SAAS,EAAE,6DAA6D;oCACxE,eAAe,EAAE,CAAC;oCAClB,mBAAmB,EAAE,6DAA6D;iCACnF;gCACD,aAAa,EAAE;oCACb,eAAe,EAAE,cAAc;oCAC/B,aAAa,EAAE,UAAU;oCACzB,WAAW,EAAE,WAAW;oCACxB,iBAAiB,EAAE,UAAU;oCAC7B,aAAa,EAAE,WAAW;iCAC3B;gCACD,kBAAkB,EAAE;oCAClB,cAAc,EAAE;wCACd,WAAW,EAAE,4BAA4B;wCACzC,WAAW,EAAE,2BAA2B;wCACxC,WAAW,EAAE,6BAA6B;wCAC1C,WAAW,EAAE,oCAAoC;wCACjD,YAAY,EAAE,yCAAyC;qCACxD;oCACD,kBAAkB,EAAE;wCAClB,gCAAgC;wCAChC,wCAAwC;qCACzC;oCACD,iBAAiB,EAAE;wCACjB;4CACE,MAAM,EAAE,aAAa;4CACrB,WAAW,EAAE,6CAA6C;4CAC1D,YAAY,EAAE,WAAW;4CACzB,IAAI,EAAE,MAAM;yCACb;qCACF;iCACF;6BACF;yBACF,CAAC;wBAEF,iBAAiB,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;wBAG/D,qBAAM,iBAAiB,CAAC,uBAAuB,CAC5D,aAAa,EACb,cAAc,EACd,eAAe,CAChB,EAAA;;wBAJK,MAAM,GAAG,SAId;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACzE,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAC7D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACvE,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;;;aAC1E,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,uCAAuC,EAAE;;;;;wBAEpC,qBAAqB,GAAG;4BAC5B,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,gBAAgB,EAAE;oCAChB,kBAAkB,EAAE,oBAAoB;oCACxC,eAAe,EAAE,EAAE;oCACnB,SAAS,EAAE,+CAA+C;oCAC1D,eAAe,EAAE,CAAC;oCAClB,mBAAmB,EAAE,uCAAuC;iCAC7D;6BACF;yBACF,CAAC;wBAEF,iBAAiB,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;wBAGpE,qBAAM,iBAAiB,CAAC,uBAAuB,CAC5D,aAAa,EACb,CAAC,wBACI,eAAe,KAAE,iBAAiB,EAAE,CAAC,IAC3C,EAAA;;wBAJK,MAAM,GAAG,SAId;wBAED,SAAS;wBACT,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;wBACnF,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC9D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/lib/services/gemini-service-skill-gap.test.ts"], "sourcesContent": ["/**\n * Gemini Service Skill Gap Tests\n * \n * Tests Gemini Service Skill Gap functionality, business logic, and edge cases.\n * \n * @category unit\n * @requires Unit testing utilities, mocking\n */\n\nimport { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';\n\n// Mock environment variables before importing geminiService\nprocess.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-testing-purposes-only';\n\nimport { geminiService } from '@/lib/services/geminiService';\n\n// Mock the generateContent method\nconst mockGenerateContent = jest.fn();\n\n// Mock the GeminiService class\njest.mock('@/lib/services/geminiService', () => {\n  const actualGeminiService = jest.requireActual('@/lib/services/geminiService');\n  return {\n    ...actualGeminiService,\n    geminiService: {\n      ...actualGeminiService.geminiService,\n      generateContent: mockGenerateContent,\n      analyzeComprehensiveSkillGap: jest.fn(),\n      generatePersonalizedLearningPlan: jest.fn(),\n      analyzeSkillMarketTrends: jest.fn(),\n      validateSkillAssessment: jest.fn(),\n    },\n  };\n});\n\nconst mockGeminiService = geminiService as jest.Mocked<typeof geminiService>;\n\ndescribe('GeminiService - Skill Gap Analysis', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  afterEach(() => {\n    jest.resetAllMocks();\n  });\n\n  describe('analyzeComprehensiveSkillGap', () => {\n    const mockCurrentSkills = [\n      {\n        skillName: 'JavaScript',\n        selfRating: 7,\n        confidenceLevel: 8,\n        yearsOfExperience: 3,\n      },\n      {\n        skillName: 'React',\n        selfRating: 6,\n        confidenceLevel: 7,\n        yearsOfExperience: 2,\n      },\n    ];\n\n    const mockTargetCareerPath = {\n      careerPathName: 'Full Stack Developer',\n      targetLevel: 'ADVANCED' as const,\n    };\n\n    const mockPreferences = {\n      timeframe: 'ONE_YEAR' as const,\n      hoursPerWeek: 10,\n      learningStyle: ['VISUAL', 'HANDS_ON'],\n      budget: 'FREEMIUM' as const,\n      focusAreas: ['Backend Development'],\n    };\n\n    const mockCareerPathData = {\n      requiredSkills: [\n        { name: 'JavaScript' },\n        { name: 'Node.js' },\n        { name: 'PostgreSQL' },\n      ],\n      learningResources: [\n        {\n          title: 'Node.js Fundamentals',\n          type: 'COURSE',\n          skillLevel: 'INTERMEDIATE',\n          skills: ['Node.js'],\n        },\n      ],\n    };\n\n    it('should analyze comprehensive skill gap successfully', async () => {\n      // Arrange\n      const expectedResponse = {\n        success: true,\n        data: {\n          skillGaps: [\n            {\n              skillId: 'generated-id-1',\n              skillName: 'Node.js',\n              currentLevel: 2,\n              targetLevel: 8,\n              gapSeverity: 'HIGH',\n              priority: 90,\n              estimatedLearningTime: 120,\n              marketDemand: 'VERY_HIGH',\n              salaryImpact: 15,\n            },\n          ],\n          learningPlan: {\n            totalEstimatedHours: 120,\n            milestones: [\n              {\n                month: 3,\n                skills: ['Node.js Basics'],\n                estimatedHours: 60,\n                learningPaths: ['Backend Development Path'],\n              },\n            ],\n            recommendedResources: [\n              {\n                resourceId: 'resource-1',\n                resourceType: 'COURSE',\n                priority: 'HIGH',\n                skillsAddressed: ['Node.js'],\n                estimatedHours: 60,\n              },\n            ],\n          },\n          careerReadiness: {\n            currentScore: 65,\n            targetScore: 85,\n            improvementPotential: 20,\n            timeToTarget: 8,\n          },\n        },\n      };\n\n      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(expectedResponse);\n\n      // Act\n      const result = await mockGeminiService.analyzeComprehensiveSkillGap(\n        mockCurrentSkills,\n        mockTargetCareerPath,\n        mockPreferences,\n        mockCareerPathData,\n        'test-user-id'\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.skillGaps).toHaveLength(1);\n      expect(result.data.skillGaps[0].skillName).toBe('Node.js');\n      expect(result.data.skillGaps[0].gapSeverity).toBe('HIGH');\n      expect(result.data.learningPlan.totalEstimatedHours).toBe(120);\n      expect(result.data.careerReadiness.currentScore).toBe(65);\n\n      expect(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(\n        mockCurrentSkills,\n        mockTargetCareerPath,\n        mockPreferences,\n        mockCareerPathData,\n        'test-user-id'\n      );\n    });\n\n    it('should handle AI service failures', async () => {\n      // Arrange\n      const errorResponse = {\n        success: false,\n        error: 'AI service temporarily unavailable',\n      };\n\n      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(errorResponse);\n\n      // Act\n      const result = await mockGeminiService.analyzeComprehensiveSkillGap(\n        mockCurrentSkills,\n        mockTargetCareerPath,\n        mockPreferences,\n        mockCareerPathData,\n        'test-user-id'\n      );\n\n      // Assert\n      expect(result.success).toBe(false);\n      expect(result.error).toBe('AI service temporarily unavailable');\n    });\n\n    it('should include market insights when requested', async () => {\n      // Arrange\n      const responseWithMarketData = {\n        success: true,\n        data: {\n          skillGaps: [],\n          learningPlan: {\n            totalEstimatedHours: 0,\n            milestones: [],\n            recommendedResources: [],\n          },\n          careerReadiness: {\n            currentScore: 70,\n            targetScore: 85,\n            improvementPotential: 15,\n            timeToTarget: 6,\n          },\n          marketInsights: {\n            industryTrends: [\n              {\n                skill: 'Node.js',\n                trend: 'GROWING',\n                demandLevel: 'VERY_HIGH',\n              },\n            ],\n            salaryProjections: {\n              currentEstimate: 75000,\n              targetEstimate: 95000,\n              improvementPotential: 26.7,\n            },\n          },\n        },\n      };\n\n      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(responseWithMarketData);\n\n      // Act\n      const result = await mockGeminiService.analyzeComprehensiveSkillGap(\n        mockCurrentSkills,\n        mockTargetCareerPath,\n        mockPreferences,\n        mockCareerPathData,\n        'test-user-id'\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.marketInsights).toBeDefined();\n      expect(result.data.marketInsights.industryTrends).toHaveLength(1);\n      expect(result.data.marketInsights.salaryProjections.improvementPotential).toBe(26.7);\n    });\n  });\n\n  describe('generatePersonalizedLearningPlan', () => {\n    const mockSkillGaps = [\n      {\n        skillName: 'Node.js',\n        currentLevel: 3,\n        targetLevel: 8,\n        gapSeverity: 'HIGH',\n        priority: 90,\n        estimatedLearningTime: 120,\n      },\n    ];\n\n    const mockUserPreferences = {\n      timeframe: 'ONE_YEAR' as const,\n      hoursPerWeek: 10,\n      learningStyle: ['VISUAL'],\n      budget: 'FREEMIUM' as const,\n      focusAreas: ['Backend Development'],\n    };\n\n    const mockMarketData = [\n      {\n        skillName: 'Node.js',\n        demandLevel: 'VERY_HIGH',\n        growthTrend: 'GROWING',\n        salaryImpact: 15,\n      },\n    ];\n\n    it('should generate personalized learning plan successfully', async () => {\n      // Arrange\n      const expectedResponse = {\n        success: true,\n        data: {\n          learningPlan: {\n            totalDuration: '12 months',\n            totalHours: 120,\n            weeklyCommitment: 10,\n            phases: [\n              {\n                phaseNumber: 1,\n                phaseName: 'Foundation',\n                duration: '3 months',\n                objectives: ['Learn Node.js fundamentals'],\n                skills: [\n                  {\n                    skillName: 'Node.js',\n                    currentLevel: 3,\n                    targetLevel: 6,\n                    hoursAllocated: 60,\n                    learningApproach: 'Hands-on projects with video tutorials',\n                    resources: [\n                      {\n                        type: 'COURSE',\n                        title: 'Node.js Complete Guide',\n                        description: 'Comprehensive Node.js course',\n                        estimatedHours: 40,\n                        cost: 'FREEMIUM',\n                        difficulty: 'INTERMEDIATE',\n                        learningStyle: ['VISUAL'],\n                        priority: 'HIGH',\n                      },\n                    ],\n                    milestones: [\n                      {\n                        week: 4,\n                        milestone: 'Build first Node.js API',\n                        assessmentMethod: 'Project completion',\n                      },\n                    ],\n                  },\n                ],\n                practiceProjects: [\n                  {\n                    projectName: 'REST API with Node.js',\n                    description: 'Build a complete REST API',\n                    skillsApplied: ['Node.js', 'Express.js'],\n                    estimatedHours: 20,\n                    difficulty: 'INTERMEDIATE',\n                    deliverables: ['Working API', 'Documentation'],\n                  },\n                ],\n              },\n            ],\n          },\n        },\n      };\n\n      mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(expectedResponse);\n\n      // Act\n      const result = await mockGeminiService.generatePersonalizedLearningPlan(\n        mockSkillGaps,\n        mockUserPreferences,\n        mockMarketData,\n        'test-user-id'\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.learningPlan.totalHours).toBe(120);\n      expect(result.data.learningPlan.phases).toHaveLength(1);\n      expect(result.data.learningPlan.phases[0].skills[0].skillName).toBe('Node.js');\n      expect(result.data.learningPlan.phases[0].practiceProjects).toHaveLength(1);\n    });\n\n    it('should optimize for user preferences', async () => {\n      // Arrange\n      const visualLearnerResponse = {\n        success: true,\n        data: {\n          learningPlan: {\n            phases: [\n              {\n                skills: [\n                  {\n                    skillName: 'Node.js',\n                    resources: [\n                      {\n                        type: 'COURSE',\n                        learningStyle: ['VISUAL'],\n                        priority: 'HIGH',\n                      },\n                    ],\n                  },\n                ],\n              },\n            ],\n          },\n        },\n      };\n\n      mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(visualLearnerResponse);\n\n      // Act\n      const result = await mockGeminiService.generatePersonalizedLearningPlan(\n        mockSkillGaps,\n        mockUserPreferences,\n        mockMarketData,\n        'test-user-id'\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.learningPlan.phases[0].skills[0].resources[0].learningStyle).toContain('VISUAL');\n    });\n  });\n\n  describe('analyzeSkillMarketTrends', () => {\n    const mockSkills = ['JavaScript', 'React', 'Node.js'];\n    const mockTargetIndustry = 'Technology';\n    const mockRegion = 'North America';\n\n    it('should analyze skill market trends successfully', async () => {\n      // Arrange\n      const expectedResponse = {\n        success: true,\n        data: {\n          marketAnalysis: {\n            analysisDate: expect.any(String),\n            region: 'North America',\n            industry: 'Technology',\n            overallMarketHealth: 'EXCELLENT',\n          },\n          skillTrends: [\n            {\n              skillName: 'JavaScript',\n              demandLevel: 'VERY_HIGH',\n              growthTrend: 'STABLE',\n              marketSaturation: 'MODERATE',\n              averageSalaryImpact: 20,\n              jobPostingsGrowth: 5,\n              futureOutlook: {\n                nextYear: 'Continued high demand',\n                nextFiveYears: 'Evolving with new frameworks',\n                emergingOpportunities: ['Full-stack development'],\n                potentialThreats: ['Framework fragmentation'],\n              },\n            },\n          ],\n        },\n      };\n\n      mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(expectedResponse);\n\n      // Act\n      const result = await mockGeminiService.analyzeSkillMarketTrends(\n        mockSkills,\n        mockTargetIndustry,\n        mockRegion\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.marketAnalysis.region).toBe('North America');\n      expect(result.data.marketAnalysis.industry).toBe('Technology');\n      expect(result.data.skillTrends).toHaveLength(1);\n      expect(result.data.skillTrends[0].skillName).toBe('JavaScript');\n      expect(result.data.skillTrends[0].demandLevel).toBe('VERY_HIGH');\n    });\n\n    it('should handle empty skills array', async () => {\n      // Arrange\n      const emptySkillsResponse = {\n        success: true,\n        data: {\n          marketAnalysis: {\n            region: 'GLOBAL',\n            industry: 'Technology',\n            overallMarketHealth: 'GOOD',\n          },\n          skillTrends: [],\n        },\n      };\n\n      mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(emptySkillsResponse);\n\n      // Act\n      const result = await mockGeminiService.analyzeSkillMarketTrends(\n        [],\n        mockTargetIndustry,\n        'GLOBAL'\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.skillTrends).toHaveLength(0);\n    });\n  });\n\n  describe('validateSkillAssessment', () => {\n    const mockSkillName = 'JavaScript';\n    const mockSelfRating = 7;\n    const mockUserContext = {\n      experienceLevel: 'INTERMEDIATE',\n      industry: 'Technology',\n      yearsOfExperience: 3,\n      relatedSkills: ['React', 'Node.js'],\n      previousAssessments: [\n        {\n          skillName: 'React',\n          rating: 6,\n          date: '2024-01-01',\n        },\n      ],\n    };\n\n    it('should validate skill assessment successfully', async () => {\n      // Arrange\n      const expectedResponse = {\n        success: true,\n        data: {\n          validationResult: {\n            assessmentAccuracy: 'ACCURATE',\n            confidenceLevel: 85,\n            reasoning: 'Rating aligns well with experience level and related skills',\n            suggestedRating: 7,\n            ratingJustification: 'Consistent with 3 years experience and related skill levels',\n          },\n          skillAnalysis: {\n            skillComplexity: 'INTERMEDIATE',\n            learningCurve: 'MODERATE',\n            marketValue: 'VERY_HIGH',\n            industryRelevance: 'CRITICAL',\n            skillCategory: 'TECHNICAL',\n          },\n          assessmentGuidance: {\n            ratingCriteria: {\n              'level1to2': 'Basic syntax understanding',\n              'level3to4': 'Can write simple programs',\n              'level5to6': 'Comfortable with frameworks',\n              'level7to8': 'Advanced patterns and optimization',\n              'level9to10': 'Expert-level architecture and mentoring',\n            },\n            selfAssessmentTips: [\n              'Compare with concrete examples',\n              'Consider real-world project complexity',\n            ],\n            validationMethods: [\n              {\n                method: 'Code Review',\n                description: 'Have experienced developer review your code',\n                timeRequired: '2-3 hours',\n                cost: 'FREE',\n              },\n            ],\n          },\n        },\n      };\n\n      mockGeminiService.validateSkillAssessment.mockResolvedValue(expectedResponse);\n\n      // Act\n      const result = await mockGeminiService.validateSkillAssessment(\n        mockSkillName,\n        mockSelfRating,\n        mockUserContext\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.validationResult.assessmentAccuracy).toBe('ACCURATE');\n      expect(result.data.validationResult.suggestedRating).toBe(7);\n      expect(result.data.skillAnalysis.skillComplexity).toBe('INTERMEDIATE');\n      expect(result.data.assessmentGuidance.validationMethods).toHaveLength(1);\n    });\n\n    it('should identify overestimated ratings', async () => {\n      // Arrange\n      const overestimatedResponse = {\n        success: true,\n        data: {\n          validationResult: {\n            assessmentAccuracy: 'SIGNIFICANTLY_HIGH',\n            confidenceLevel: 60,\n            reasoning: 'Rating seems high for stated experience level',\n            suggestedRating: 5,\n            ratingJustification: 'More realistic for 3 years experience',\n          },\n        },\n      };\n\n      mockGeminiService.validateSkillAssessment.mockResolvedValue(overestimatedResponse);\n\n      // Act\n      const result = await mockGeminiService.validateSkillAssessment(\n        mockSkillName,\n        9, // High rating\n        { ...mockUserContext, yearsOfExperience: 1 } // Low experience\n      );\n\n      // Assert\n      expect(result.success).toBe(true);\n      expect(result.data.validationResult.assessmentAccuracy).toBe('SIGNIFICANTLY_HIGH');\n      expect(result.data.validationResult.suggestedRating).toBe(5);\n    });\n  });\n});\n"], "version": 3}