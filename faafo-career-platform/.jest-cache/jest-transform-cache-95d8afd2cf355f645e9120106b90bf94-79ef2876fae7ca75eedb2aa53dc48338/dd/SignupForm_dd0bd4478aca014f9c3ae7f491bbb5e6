498d5f9478e8e360cba0417a2368f525
"use strict";
"use client";

/* istanbul ignore next */
function cov_1ls1n4eps0() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/SignupForm.tsx";
  var hash = "73730e933b23c77264a61d2f24fa23ed6d5a009e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/SignupForm.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "2": {
        start: {
          line: 4,
          column: 26
        },
        end: {
          line: 4,
          column: 33
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 52
        }
      },
      "4": {
        start: {
          line: 6,
          column: 4
        },
        end: {
          line: 8,
          column: 5
        }
      },
      "5": {
        start: {
          line: 7,
          column: 6
        },
        end: {
          line: 7,
          column: 68
        }
      },
      "6": {
        start: {
          line: 7,
          column: 51
        },
        end: {
          line: 7,
          column: 63
        }
      },
      "7": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 39
        }
      },
      "8": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "9": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 33
        }
      },
      "10": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 17
        }
      },
      "11": {
        start: {
          line: 14,
          column: 25
        },
        end: {
          line: 18,
          column: 2
        }
      },
      "12": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 72
        }
      },
      "13": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 21
        }
      },
      "14": {
        start: {
          line: 19,
          column: 19
        },
        end: {
          line: 35,
          column: 4
        }
      },
      "15": {
        start: {
          line: 20,
          column: 18
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "16": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 25,
          column: 10
        }
      },
      "17": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 23
        }
      },
      "18": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "19": {
        start: {
          line: 23,
          column: 29
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "20": {
        start: {
          line: 23,
          column: 77
        },
        end: {
          line: 23,
          column: 95
        }
      },
      "21": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 24,
          column: 22
        }
      },
      "22": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 26
        }
      },
      "23": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 34,
          column: 6
        }
      },
      "24": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "25": {
        start: {
          line: 29,
          column: 35
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "26": {
        start: {
          line: 30,
          column: 21
        },
        end: {
          line: 30,
          column: 23
        }
      },
      "27": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "28": {
        start: {
          line: 31,
          column: 25
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "29": {
        start: {
          line: 31,
          column: 38
        },
        end: {
          line: 31,
          column: 50
        }
      },
      "30": {
        start: {
          line: 31,
          column: 56
        },
        end: {
          line: 31,
          column: 57
        }
      },
      "31": {
        start: {
          line: 31,
          column: 78
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "32": {
        start: {
          line: 31,
          column: 102
        },
        end: {
          line: 31,
          column: 137
        }
      },
      "33": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 40
        }
      },
      "34": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 22
        }
      },
      "35": {
        start: {
          line: 36,
          column: 16
        },
        end: {
          line: 44,
          column: 1
        }
      },
      "36": {
        start: {
          line: 37,
          column: 28
        },
        end: {
          line: 37,
          column: 110
        }
      },
      "37": {
        start: {
          line: 37,
          column: 91
        },
        end: {
          line: 37,
          column: 106
        }
      },
      "38": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 43,
          column: 7
        }
      },
      "39": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 97
        }
      },
      "40": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 39,
          column: 70
        }
      },
      "41": {
        start: {
          line: 39,
          column: 85
        },
        end: {
          line: 39,
          column: 95
        }
      },
      "42": {
        start: {
          line: 40,
          column: 35
        },
        end: {
          line: 40,
          column: 100
        }
      },
      "43": {
        start: {
          line: 40,
          column: 41
        },
        end: {
          line: 40,
          column: 73
        }
      },
      "44": {
        start: {
          line: 40,
          column: 88
        },
        end: {
          line: 40,
          column: 98
        }
      },
      "45": {
        start: {
          line: 41,
          column: 32
        },
        end: {
          line: 41,
          column: 116
        }
      },
      "46": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 78
        }
      },
      "47": {
        start: {
          line: 45,
          column: 18
        },
        end: {
          line: 71,
          column: 1
        }
      },
      "48": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 46,
          column: 104
        }
      },
      "49": {
        start: {
          line: 46,
          column: 43
        },
        end: {
          line: 46,
          column: 68
        }
      },
      "50": {
        start: {
          line: 46,
          column: 57
        },
        end: {
          line: 46,
          column: 68
        }
      },
      "51": {
        start: {
          line: 46,
          column: 69
        },
        end: {
          line: 46,
          column: 81
        }
      },
      "52": {
        start: {
          line: 46,
          column: 119
        },
        end: {
          line: 46,
          column: 196
        }
      },
      "53": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 160
        }
      },
      "54": {
        start: {
          line: 47,
          column: 141
        },
        end: {
          line: 47,
          column: 153
        }
      },
      "55": {
        start: {
          line: 48,
          column: 23
        },
        end: {
          line: 48,
          column: 68
        }
      },
      "56": {
        start: {
          line: 48,
          column: 45
        },
        end: {
          line: 48,
          column: 65
        }
      },
      "57": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 70
        }
      },
      "58": {
        start: {
          line: 50,
          column: 15
        },
        end: {
          line: 50,
          column: 70
        }
      },
      "59": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 68,
          column: 66
        }
      },
      "60": {
        start: {
          line: 51,
          column: 50
        },
        end: {
          line: 68,
          column: 66
        }
      },
      "61": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 169
        }
      },
      "62": {
        start: {
          line: 52,
          column: 160
        },
        end: {
          line: 52,
          column: 169
        }
      },
      "63": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "64": {
        start: {
          line: 53,
          column: 26
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "65": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 66,
          column: 13
        }
      },
      "66": {
        start: {
          line: 55,
          column: 32
        },
        end: {
          line: 55,
          column: 39
        }
      },
      "67": {
        start: {
          line: 55,
          column: 40
        },
        end: {
          line: 55,
          column: 46
        }
      },
      "68": {
        start: {
          line: 56,
          column: 24
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "69": {
        start: {
          line: 56,
          column: 35
        },
        end: {
          line: 56,
          column: 72
        }
      },
      "70": {
        start: {
          line: 57,
          column: 24
        },
        end: {
          line: 57,
          column: 34
        }
      },
      "71": {
        start: {
          line: 57,
          column: 35
        },
        end: {
          line: 57,
          column: 45
        }
      },
      "72": {
        start: {
          line: 57,
          column: 46
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "73": {
        start: {
          line: 57,
          column: 56
        },
        end: {
          line: 57,
          column: 65
        }
      },
      "74": {
        start: {
          line: 58,
          column: 24
        },
        end: {
          line: 58,
          column: 41
        }
      },
      "75": {
        start: {
          line: 58,
          column: 42
        },
        end: {
          line: 58,
          column: 55
        }
      },
      "76": {
        start: {
          line: 58,
          column: 56
        },
        end: {
          line: 58,
          column: 65
        }
      },
      "77": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 128
        }
      },
      "78": {
        start: {
          line: 60,
          column: 110
        },
        end: {
          line: 60,
          column: 116
        }
      },
      "79": {
        start: {
          line: 60,
          column: 117
        },
        end: {
          line: 60,
          column: 126
        }
      },
      "80": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 61,
          column: 106
        }
      },
      "81": {
        start: {
          line: 61,
          column: 81
        },
        end: {
          line: 61,
          column: 97
        }
      },
      "82": {
        start: {
          line: 61,
          column: 98
        },
        end: {
          line: 61,
          column: 104
        }
      },
      "83": {
        start: {
          line: 62,
          column: 20
        },
        end: {
          line: 62,
          column: 89
        }
      },
      "84": {
        start: {
          line: 62,
          column: 57
        },
        end: {
          line: 62,
          column: 72
        }
      },
      "85": {
        start: {
          line: 62,
          column: 73
        },
        end: {
          line: 62,
          column: 80
        }
      },
      "86": {
        start: {
          line: 62,
          column: 81
        },
        end: {
          line: 62,
          column: 87
        }
      },
      "87": {
        start: {
          line: 63,
          column: 20
        },
        end: {
          line: 63,
          column: 87
        }
      },
      "88": {
        start: {
          line: 63,
          column: 47
        },
        end: {
          line: 63,
          column: 62
        }
      },
      "89": {
        start: {
          line: 63,
          column: 63
        },
        end: {
          line: 63,
          column: 78
        }
      },
      "90": {
        start: {
          line: 63,
          column: 79
        },
        end: {
          line: 63,
          column: 85
        }
      },
      "91": {
        start: {
          line: 64,
          column: 20
        },
        end: {
          line: 64,
          column: 42
        }
      },
      "92": {
        start: {
          line: 64,
          column: 30
        },
        end: {
          line: 64,
          column: 42
        }
      },
      "93": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 65,
          column: 33
        }
      },
      "94": {
        start: {
          line: 65,
          column: 34
        },
        end: {
          line: 65,
          column: 43
        }
      },
      "95": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 67,
          column: 39
        }
      },
      "96": {
        start: {
          line: 68,
          column: 22
        },
        end: {
          line: 68,
          column: 34
        }
      },
      "97": {
        start: {
          line: 68,
          column: 35
        },
        end: {
          line: 68,
          column: 41
        }
      },
      "98": {
        start: {
          line: 68,
          column: 54
        },
        end: {
          line: 68,
          column: 64
        }
      },
      "99": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 35
        }
      },
      "100": {
        start: {
          line: 69,
          column: 23
        },
        end: {
          line: 69,
          column: 35
        }
      },
      "101": {
        start: {
          line: 69,
          column: 36
        },
        end: {
          line: 69,
          column: 89
        }
      },
      "102": {
        start: {
          line: 72,
          column: 0
        },
        end: {
          line: 72,
          column: 62
        }
      },
      "103": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 48
        }
      },
      "104": {
        start: {
          line: 74,
          column: 14
        },
        end: {
          line: 74,
          column: 44
        }
      },
      "105": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 26
        }
      },
      "106": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 76,
          column: 42
        }
      },
      "107": {
        start: {
          line: 77,
          column: 26
        },
        end: {
          line: 77,
          column: 60
        }
      },
      "108": {
        start: {
          line: 78,
          column: 17
        },
        end: {
          line: 227,
          column: 1
        }
      },
      "109": {
        start: {
          line: 79,
          column: 13
        },
        end: {
          line: 79,
          column: 38
        }
      },
      "110": {
        start: {
          line: 79,
          column: 48
        },
        end: {
          line: 79,
          column: 53
        }
      },
      "111": {
        start: {
          line: 79,
          column: 66
        },
        end: {
          line: 79,
          column: 71
        }
      },
      "112": {
        start: {
          line: 80,
          column: 13
        },
        end: {
          line: 80,
          column: 38
        }
      },
      "113": {
        start: {
          line: 80,
          column: 51
        },
        end: {
          line: 80,
          column: 56
        }
      },
      "114": {
        start: {
          line: 80,
          column: 72
        },
        end: {
          line: 80,
          column: 77
        }
      },
      "115": {
        start: {
          line: 81,
          column: 13
        },
        end: {
          line: 81,
          column: 40
        }
      },
      "116": {
        start: {
          line: 81,
          column: 56
        },
        end: {
          line: 81,
          column: 61
        }
      },
      "117": {
        start: {
          line: 81,
          column: 80
        },
        end: {
          line: 81,
          column: 85
        }
      },
      "118": {
        start: {
          line: 82,
          column: 13
        },
        end: {
          line: 82,
          column: 38
        }
      },
      "119": {
        start: {
          line: 82,
          column: 59
        },
        end: {
          line: 82,
          column: 64
        }
      },
      "120": {
        start: {
          line: 82,
          column: 88
        },
        end: {
          line: 82,
          column: 93
        }
      },
      "121": {
        start: {
          line: 83,
          column: 13
        },
        end: {
          line: 83,
          column: 41
        }
      },
      "122": {
        start: {
          line: 83,
          column: 58
        },
        end: {
          line: 83,
          column: 63
        }
      },
      "123": {
        start: {
          line: 83,
          column: 83
        },
        end: {
          line: 83,
          column: 88
        }
      },
      "124": {
        start: {
          line: 84,
          column: 13
        },
        end: {
          line: 84,
          column: 41
        }
      },
      "125": {
        start: {
          line: 84,
          column: 57
        },
        end: {
          line: 84,
          column: 62
        }
      },
      "126": {
        start: {
          line: 84,
          column: 81
        },
        end: {
          line: 84,
          column: 86
        }
      },
      "127": {
        start: {
          line: 85,
          column: 13
        },
        end: {
          line: 85,
          column: 37
        }
      },
      "128": {
        start: {
          line: 85,
          column: 52
        },
        end: {
          line: 85,
          column: 65
        }
      },
      "129": {
        start: {
          line: 85,
          column: 81
        },
        end: {
          line: 85,
          column: 93
        }
      },
      "130": {
        start: {
          line: 87,
          column: 23
        },
        end: {
          line: 108,
          column: 25
        }
      },
      "131": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 107,
          column: 9
        }
      },
      "132": {
        start: {
          line: 89,
          column: 12
        },
        end: {
          line: 89,
          column: 89
        }
      },
      "133": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 36
        }
      },
      "134": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 24
        }
      },
      "135": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 105,
          column: 13
        }
      },
      "136": {
        start: {
          line: 95,
          column: 31
        },
        end: {
          line: 95,
          column: 33
        }
      },
      "137": {
        start: {
          line: 96,
          column: 16
        },
        end: {
          line: 103,
          column: 19
        }
      },
      "138": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 102,
          column: 21
        }
      },
      "139": {
        start: {
          line: 98,
          column: 24
        },
        end: {
          line: 98,
          column: 53
        }
      },
      "140": {
        start: {
          line: 100,
          column: 25
        },
        end: {
          line: 102,
          column: 21
        }
      },
      "141": {
        start: {
          line: 101,
          column: 24
        },
        end: {
          line: 101,
          column: 56
        }
      },
      "142": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 104,
          column: 46
        }
      },
      "143": {
        start: {
          line: 106,
          column: 12
        },
        end: {
          line: 106,
          column: 25
        }
      },
      "144": {
        start: {
          line: 109,
          column: 23
        },
        end: {
          line: 171,
          column: 55
        }
      },
      "145": {
        start: {
          line: 109,
          column: 67
        },
        end: {
          line: 171,
          column: 7
        }
      },
      "146": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 170,
          column: 11
        }
      },
      "147": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 169,
          column: 13
        }
      },
      "148": {
        start: {
          line: 114,
          column: 20
        },
        end: {
          line: 114,
          column: 43
        }
      },
      "149": {
        start: {
          line: 115,
          column: 20
        },
        end: {
          line: 115,
          column: 44
        }
      },
      "150": {
        start: {
          line: 117,
          column: 20
        },
        end: {
          line: 119,
          column: 21
        }
      },
      "151": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 46
        }
      },
      "152": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 79
        }
      },
      "153": {
        start: {
          line: 121,
          column: 20
        },
        end: {
          line: 121,
          column: 33
        }
      },
      "154": {
        start: {
          line: 123,
          column: 20
        },
        end: {
          line: 123,
          column: 48
        }
      },
      "155": {
        start: {
          line: 124,
          column: 20
        },
        end: {
          line: 128,
          column: 28
        }
      },
      "156": {
        start: {
          line: 130,
          column: 20
        },
        end: {
          line: 130,
          column: 41
        }
      },
      "157": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 131,
          column: 71
        }
      },
      "158": {
        start: {
          line: 132,
          column: 20
        },
        end: {
          line: 132,
          column: 71
        }
      },
      "159": {
        start: {
          line: 133,
          column: 20
        },
        end: {
          line: 133,
          column: 108
        }
      },
      "160": {
        start: {
          line: 133,
          column: 84
        },
        end: {
          line: 133,
          column: 108
        }
      },
      "161": {
        start: {
          line: 134,
          column: 20
        },
        end: {
          line: 134,
          column: 33
        }
      },
      "162": {
        start: {
          line: 136,
          column: 20
        },
        end: {
          line: 136,
          column: 46
        }
      },
      "163": {
        start: {
          line: 137,
          column: 20
        },
        end: {
          line: 137,
          column: 58
        }
      },
      "164": {
        start: {
          line: 139,
          column: 20
        },
        end: {
          line: 139,
          column: 37
        }
      },
      "165": {
        start: {
          line: 140,
          column: 20
        },
        end: {
          line: 140,
          column: 44
        }
      },
      "166": {
        start: {
          line: 142,
          column: 20
        },
        end: {
          line: 142,
          column: 44
        }
      },
      "167": {
        start: {
          line: 143,
          column: 20
        },
        end: {
          line: 143,
          column: 81
        }
      },
      "168": {
        start: {
          line: 144,
          column: 20
        },
        end: {
          line: 144,
          column: 44
        }
      },
      "169": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 48
        }
      },
      "170": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 62
        }
      },
      "171": {
        start: {
          line: 148,
          column: 20
        },
        end: {
          line: 148,
          column: 45
        }
      },
      "172": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 149,
          column: 79
        }
      },
      "173": {
        start: {
          line: 150,
          column: 20
        },
        end: {
          line: 150,
          column: 77
        }
      },
      "174": {
        start: {
          line: 151,
          column: 20
        },
        end: {
          line: 151,
          column: 33
        }
      },
      "175": {
        start: {
          line: 153,
          column: 20
        },
        end: {
          line: 161,
          column: 21
        }
      },
      "176": {
        start: {
          line: 154,
          column: 24
        },
        end: {
          line: 154,
          column: 83
        }
      },
      "177": {
        start: {
          line: 155,
          column: 24
        },
        end: {
          line: 157,
          column: 25
        }
      },
      "178": {
        start: {
          line: 156,
          column: 28
        },
        end: {
          line: 156,
          column: 50
        }
      },
      "179": {
        start: {
          line: 160,
          column: 24
        },
        end: {
          line: 160,
          column: 129
        }
      },
      "180": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 162,
          column: 45
        }
      },
      "181": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 40
        }
      },
      "182": {
        start: {
          line: 165,
          column: 20
        },
        end: {
          line: 165,
          column: 60
        }
      },
      "183": {
        start: {
          line: 166,
          column: 20
        },
        end: {
          line: 166,
          column: 103
        }
      },
      "184": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 167,
          column: 45
        }
      },
      "185": {
        start: {
          line: 168,
          column: 25
        },
        end: {
          line: 168,
          column: 47
        }
      },
      "186": {
        start: {
          line: 172,
          column: 35
        },
        end: {
          line: 210,
          column: 31
        }
      },
      "187": {
        start: {
          line: 172,
          column: 74
        },
        end: {
          line: 210,
          column: 7
        }
      },
      "188": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 209,
          column: 11
        }
      },
      "189": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 208,
          column: 13
        }
      },
      "190": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 177,
          column: 41
        }
      },
      "191": {
        start: {
          line: 178,
          column: 20
        },
        end: {
          line: 178,
          column: 95
        }
      },
      "192": {
        start: {
          line: 179,
          column: 20
        },
        end: {
          line: 179,
          column: 33
        }
      },
      "193": {
        start: {
          line: 181,
          column: 20
        },
        end: {
          line: 181,
          column: 47
        }
      },
      "194": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 186,
          column: 28
        }
      },
      "195": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 188,
          column: 41
        }
      },
      "196": {
        start: {
          line: 189,
          column: 20
        },
        end: {
          line: 189,
          column: 58
        }
      },
      "197": {
        start: {
          line: 191,
          column: 20
        },
        end: {
          line: 191,
          column: 37
        }
      },
      "198": {
        start: {
          line: 192,
          column: 20
        },
        end: {
          line: 197,
          column: 21
        }
      },
      "199": {
        start: {
          line: 193,
          column: 24
        },
        end: {
          line: 193,
          column: 83
        }
      },
      "200": {
        start: {
          line: 196,
          column: 24
        },
        end: {
          line: 196,
          column: 132
        }
      },
      "201": {
        start: {
          line: 198,
          column: 20
        },
        end: {
          line: 198,
          column: 44
        }
      },
      "202": {
        start: {
          line: 200,
          column: 20
        },
        end: {
          line: 200,
          column: 40
        }
      },
      "203": {
        start: {
          line: 201,
          column: 20
        },
        end: {
          line: 201,
          column: 73
        }
      },
      "204": {
        start: {
          line: 202,
          column: 20
        },
        end: {
          line: 202,
          column: 103
        }
      },
      "205": {
        start: {
          line: 203,
          column: 20
        },
        end: {
          line: 203,
          column: 44
        }
      },
      "206": {
        start: {
          line: 205,
          column: 20
        },
        end: {
          line: 205,
          column: 42
        }
      },
      "207": {
        start: {
          line: 206,
          column: 20
        },
        end: {
          line: 206,
          column: 46
        }
      },
      "208": {
        start: {
          line: 207,
          column: 24
        },
        end: {
          line: 207,
          column: 46
        }
      },
      "209": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 221,
          column: 5
        }
      },
      "210": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 220,
          column: 136
        }
      },
      "211": {
        start: {
          line: 214,
          column: 56
        },
        end: {
          line: 214,
          column: 79
        }
      },
      "212": {
        start: {
          line: 215,
          column: 56
        },
        end: {
          line: 215,
          column: 69
        }
      },
      "213": {
        start: {
          line: 216,
          column: 56
        },
        end: {
          line: 216,
          column: 72
        }
      },
      "214": {
        start: {
          line: 217,
          column: 56
        },
        end: {
          line: 217,
          column: 77
        }
      },
      "215": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 226,
          column: 1620
        }
      },
      "216": {
        start: {
          line: 222,
          column: 522
        },
        end: {
          line: 222,
          column: 554
        }
      },
      "217": {
        start: {
          line: 224,
          column: 650
        },
        end: {
          line: 224,
          column: 685
        }
      },
      "218": {
        start: {
          line: 228,
          column: 0
        },
        end: {
          line: 228,
          column: 29
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 3,
            column: 75
          }
        },
        loc: {
          start: {
            line: 3,
            column: 96
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 38
          },
          end: {
            line: 7,
            column: 39
          }
        },
        loc: {
          start: {
            line: 7,
            column: 49
          },
          end: {
            line: 7,
            column: 65
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 10,
            column: 7
          }
        },
        loc: {
          start: {
            line: 10,
            column: 28
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 10
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 14,
            column: 81
          }
        },
        loc: {
          start: {
            line: 14,
            column: 95
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 16,
            column: 6
          }
        },
        loc: {
          start: {
            line: 16,
            column: 20
          },
          end: {
            line: 18,
            column: 1
          }
        },
        line: 16
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 52
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 19
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 19
          }
        },
        loc: {
          start: {
            line: 20,
            column: 30
          },
          end: {
            line: 27,
            column: 5
          }
        },
        line: 20
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 21,
            column: 49
          }
        },
        loc: {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 25,
            column: 9
          }
        },
        line: 21
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 28,
            column: 11
          },
          end: {
            line: 28,
            column: 12
          }
        },
        loc: {
          start: {
            line: 28,
            column: 26
          },
          end: {
            line: 34,
            column: 5
          }
        },
        line: 28
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 36,
            column: 44
          },
          end: {
            line: 36,
            column: 45
          }
        },
        loc: {
          start: {
            line: 36,
            column: 89
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 36
      },
      "10": {
        name: "adopt",
        decl: {
          start: {
            line: 37,
            column: 13
          },
          end: {
            line: 37,
            column: 18
          }
        },
        loc: {
          start: {
            line: 37,
            column: 26
          },
          end: {
            line: 37,
            column: 112
          }
        },
        line: 37
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 37,
            column: 70
          },
          end: {
            line: 37,
            column: 71
          }
        },
        loc: {
          start: {
            line: 37,
            column: 89
          },
          end: {
            line: 37,
            column: 108
          }
        },
        line: 37
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 38,
            column: 36
          },
          end: {
            line: 38,
            column: 37
          }
        },
        loc: {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 43,
            column: 5
          }
        },
        line: 38
      },
      "13": {
        name: "fulfilled",
        decl: {
          start: {
            line: 39,
            column: 17
          },
          end: {
            line: 39,
            column: 26
          }
        },
        loc: {
          start: {
            line: 39,
            column: 34
          },
          end: {
            line: 39,
            column: 99
          }
        },
        line: 39
      },
      "14": {
        name: "rejected",
        decl: {
          start: {
            line: 40,
            column: 17
          },
          end: {
            line: 40,
            column: 25
          }
        },
        loc: {
          start: {
            line: 40,
            column: 33
          },
          end: {
            line: 40,
            column: 102
          }
        },
        line: 40
      },
      "15": {
        name: "step",
        decl: {
          start: {
            line: 41,
            column: 17
          },
          end: {
            line: 41,
            column: 21
          }
        },
        loc: {
          start: {
            line: 41,
            column: 30
          },
          end: {
            line: 41,
            column: 118
          }
        },
        line: 41
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 45,
            column: 48
          },
          end: {
            line: 45,
            column: 49
          }
        },
        loc: {
          start: {
            line: 45,
            column: 73
          },
          end: {
            line: 71,
            column: 1
          }
        },
        line: 45
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 46,
            column: 30
          },
          end: {
            line: 46,
            column: 31
          }
        },
        loc: {
          start: {
            line: 46,
            column: 41
          },
          end: {
            line: 46,
            column: 83
          }
        },
        line: 46
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 47,
            column: 128
          },
          end: {
            line: 47,
            column: 129
          }
        },
        loc: {
          start: {
            line: 47,
            column: 139
          },
          end: {
            line: 47,
            column: 155
          }
        },
        line: 47
      },
      "19": {
        name: "verb",
        decl: {
          start: {
            line: 48,
            column: 13
          },
          end: {
            line: 48,
            column: 17
          }
        },
        loc: {
          start: {
            line: 48,
            column: 21
          },
          end: {
            line: 48,
            column: 70
          }
        },
        line: 48
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 48,
            column: 30
          },
          end: {
            line: 48,
            column: 31
          }
        },
        loc: {
          start: {
            line: 48,
            column: 43
          },
          end: {
            line: 48,
            column: 67
          }
        },
        line: 48
      },
      "21": {
        name: "step",
        decl: {
          start: {
            line: 49,
            column: 13
          },
          end: {
            line: 49,
            column: 17
          }
        },
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 49
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 78,
            column: 17
          },
          end: {
            line: 78,
            column: 18
          }
        },
        loc: {
          start: {
            line: 78,
            column: 29
          },
          end: {
            line: 227,
            column: 1
          }
        },
        line: 78
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 87,
            column: 48
          },
          end: {
            line: 87,
            column: 49
          }
        },
        loc: {
          start: {
            line: 87,
            column: 60
          },
          end: {
            line: 108,
            column: 5
          }
        },
        line: 87
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 96,
            column: 37
          },
          end: {
            line: 96,
            column: 38
          }
        },
        loc: {
          start: {
            line: 96,
            column: 52
          },
          end: {
            line: 103,
            column: 17
          }
        },
        line: 96
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 109,
            column: 48
          },
          end: {
            line: 109,
            column: 49
          }
        },
        loc: {
          start: {
            line: 109,
            column: 65
          },
          end: {
            line: 171,
            column: 9
          }
        },
        line: 109
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 109,
            column: 108
          },
          end: {
            line: 109,
            column: 109
          }
        },
        loc: {
          start: {
            line: 109,
            column: 120
          },
          end: {
            line: 171,
            column: 5
          }
        },
        line: 109
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 111,
            column: 33
          },
          end: {
            line: 111,
            column: 34
          }
        },
        loc: {
          start: {
            line: 111,
            column: 47
          },
          end: {
            line: 170,
            column: 9
          }
        },
        line: 111
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 172,
            column: 60
          },
          end: {
            line: 172,
            column: 61
          }
        },
        loc: {
          start: {
            line: 172,
            column: 72
          },
          end: {
            line: 210,
            column: 9
          }
        },
        line: 172
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 172,
            column: 115
          },
          end: {
            line: 172,
            column: 116
          }
        },
        loc: {
          start: {
            line: 172,
            column: 127
          },
          end: {
            line: 210,
            column: 5
          }
        },
        line: 172
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 174,
            column: 33
          },
          end: {
            line: 174,
            column: 34
          }
        },
        loc: {
          start: {
            line: 174,
            column: 47
          },
          end: {
            line: 209,
            column: 9
          }
        },
        line: 174
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 213,
            column: 1748
          },
          end: {
            line: 213,
            column: 1749
          }
        },
        loc: {
          start: {
            line: 213,
            column: 1760
          },
          end: {
            line: 218,
            column: 53
          }
        },
        line: 213
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 222,
            column: 507
          },
          end: {
            line: 222,
            column: 508
          }
        },
        loc: {
          start: {
            line: 222,
            column: 520
          },
          end: {
            line: 222,
            column: 556
          }
        },
        line: 222
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 224,
            column: 635
          },
          end: {
            line: 224,
            column: 636
          }
        },
        loc: {
          start: {
            line: 224,
            column: 648
          },
          end: {
            line: 224,
            column: 687
          }
        },
        line: 224
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 13,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 27
          }
        }, {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 3,
            column: 51
          }
        }, {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 57
          },
          end: {
            line: 13,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 74
          },
          end: {
            line: 10,
            column: 1
          }
        }, {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 13,
            column: 1
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 4,
            column: 4
          },
          end: {
            line: 4,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "4": {
        loc: {
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 8
          },
          end: {
            line: 6,
            column: 13
          }
        }, {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "5": {
        loc: {
          start: {
            line: 6,
            column: 18
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 6,
            column: 34
          },
          end: {
            line: 6,
            column: 47
          }
        }, {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "6": {
        loc: {
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 50
          },
          end: {
            line: 6,
            column: 63
          }
        }, {
          start: {
            line: 6,
            column: 67
          },
          end: {
            line: 6,
            column: 84
          }
        }],
        line: 6
      },
      "7": {
        loc: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "8": {
        loc: {
          start: {
            line: 14,
            column: 25
          },
          end: {
            line: 18,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 30
          }
        }, {
          start: {
            line: 14,
            column: 34
          },
          end: {
            line: 14,
            column: 57
          }
        }, {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "9": {
        loc: {
          start: {
            line: 14,
            column: 63
          },
          end: {
            line: 18,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 80
          },
          end: {
            line: 16,
            column: 1
          }
        }, {
          start: {
            line: 16,
            column: 5
          },
          end: {
            line: 18,
            column: 1
          }
        }],
        line: 14
      },
      "10": {
        loc: {
          start: {
            line: 19,
            column: 19
          },
          end: {
            line: 35,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 20
          },
          end: {
            line: 19,
            column: 24
          }
        }, {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 19,
            column: 45
          }
        }, {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 35,
            column: 4
          }
        }],
        line: 19
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 25,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 21,
            column: 44
          }
        }, {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 25,
            column: 9
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 29
          },
          end: {
            line: 23,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "13": {
        loc: {
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 29,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "14": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 15
          }
        }, {
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 29,
            column: 33
          }
        }],
        line: 29
      },
      "15": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "16": {
        loc: {
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 78
          },
          end: {
            line: 31,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "17": {
        loc: {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 44,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 17
          },
          end: {
            line: 36,
            column: 21
          }
        }, {
          start: {
            line: 36,
            column: 25
          },
          end: {
            line: 36,
            column: 39
          }
        }, {
          start: {
            line: 36,
            column: 44
          },
          end: {
            line: 44,
            column: 1
          }
        }],
        line: 36
      },
      "18": {
        loc: {
          start: {
            line: 37,
            column: 35
          },
          end: {
            line: 37,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 56
          },
          end: {
            line: 37,
            column: 61
          }
        }, {
          start: {
            line: 37,
            column: 64
          },
          end: {
            line: 37,
            column: 109
          }
        }],
        line: 37
      },
      "19": {
        loc: {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 17
          }
        }, {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 38,
            column: 33
          }
        }],
        line: 38
      },
      "20": {
        loc: {
          start: {
            line: 41,
            column: 32
          },
          end: {
            line: 41,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 41,
            column: 46
          },
          end: {
            line: 41,
            column: 67
          }
        }, {
          start: {
            line: 41,
            column: 70
          },
          end: {
            line: 41,
            column: 115
          }
        }],
        line: 41
      },
      "21": {
        loc: {
          start: {
            line: 42,
            column: 51
          },
          end: {
            line: 42,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 51
          },
          end: {
            line: 42,
            column: 61
          }
        }, {
          start: {
            line: 42,
            column: 65
          },
          end: {
            line: 42,
            column: 67
          }
        }],
        line: 42
      },
      "22": {
        loc: {
          start: {
            line: 45,
            column: 18
          },
          end: {
            line: 71,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 19
          },
          end: {
            line: 45,
            column: 23
          }
        }, {
          start: {
            line: 45,
            column: 27
          },
          end: {
            line: 45,
            column: 43
          }
        }, {
          start: {
            line: 45,
            column: 48
          },
          end: {
            line: 71,
            column: 1
          }
        }],
        line: 45
      },
      "23": {
        loc: {
          start: {
            line: 46,
            column: 43
          },
          end: {
            line: 46,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 43
          },
          end: {
            line: 46,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "24": {
        loc: {
          start: {
            line: 46,
            column: 134
          },
          end: {
            line: 46,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 167
          },
          end: {
            line: 46,
            column: 175
          }
        }, {
          start: {
            line: 46,
            column: 178
          },
          end: {
            line: 46,
            column: 184
          }
        }],
        line: 46
      },
      "25": {
        loc: {
          start: {
            line: 47,
            column: 74
          },
          end: {
            line: 47,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 74
          },
          end: {
            line: 47,
            column: 102
          }
        }, {
          start: {
            line: 47,
            column: 107
          },
          end: {
            line: 47,
            column: 155
          }
        }],
        line: 47
      },
      "26": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "27": {
        loc: {
          start: {
            line: 51,
            column: 15
          },
          end: {
            line: 51,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 15
          },
          end: {
            line: 51,
            column: 16
          }
        }, {
          start: {
            line: 51,
            column: 21
          },
          end: {
            line: 51,
            column: 44
          }
        }],
        line: 51
      },
      "28": {
        loc: {
          start: {
            line: 51,
            column: 28
          },
          end: {
            line: 51,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 28
          },
          end: {
            line: 51,
            column: 33
          }
        }, {
          start: {
            line: 51,
            column: 38
          },
          end: {
            line: 51,
            column: 43
          }
        }],
        line: 51
      },
      "29": {
        loc: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "30": {
        loc: {
          start: {
            line: 52,
            column: 23
          },
          end: {
            line: 52,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 23
          },
          end: {
            line: 52,
            column: 24
          }
        }, {
          start: {
            line: 52,
            column: 29
          },
          end: {
            line: 52,
            column: 125
          }
        }, {
          start: {
            line: 52,
            column: 130
          },
          end: {
            line: 52,
            column: 158
          }
        }],
        line: 52
      },
      "31": {
        loc: {
          start: {
            line: 52,
            column: 33
          },
          end: {
            line: 52,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 45
          },
          end: {
            line: 52,
            column: 56
          }
        }, {
          start: {
            line: 52,
            column: 59
          },
          end: {
            line: 52,
            column: 125
          }
        }],
        line: 52
      },
      "32": {
        loc: {
          start: {
            line: 52,
            column: 59
          },
          end: {
            line: 52,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 67
          },
          end: {
            line: 52,
            column: 116
          }
        }, {
          start: {
            line: 52,
            column: 119
          },
          end: {
            line: 52,
            column: 125
          }
        }],
        line: 52
      },
      "33": {
        loc: {
          start: {
            line: 52,
            column: 67
          },
          end: {
            line: 52,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 67
          },
          end: {
            line: 52,
            column: 77
          }
        }, {
          start: {
            line: 52,
            column: 82
          },
          end: {
            line: 52,
            column: 115
          }
        }],
        line: 52
      },
      "34": {
        loc: {
          start: {
            line: 52,
            column: 82
          },
          end: {
            line: 52,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 83
          },
          end: {
            line: 52,
            column: 98
          }
        }, {
          start: {
            line: 52,
            column: 103
          },
          end: {
            line: 52,
            column: 112
          }
        }],
        line: 52
      },
      "35": {
        loc: {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "36": {
        loc: {
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 66,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 55,
            column: 16
          },
          end: {
            line: 55,
            column: 23
          }
        }, {
          start: {
            line: 55,
            column: 24
          },
          end: {
            line: 55,
            column: 46
          }
        }, {
          start: {
            line: 56,
            column: 16
          },
          end: {
            line: 56,
            column: 72
          }
        }, {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 57,
            column: 65
          }
        }, {
          start: {
            line: 58,
            column: 16
          },
          end: {
            line: 58,
            column: 65
          }
        }, {
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 65,
            column: 43
          }
        }],
        line: 54
      },
      "37": {
        loc: {
          start: {
            line: 60,
            column: 20
          },
          end: {
            line: 60,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 60,
            column: 20
          },
          end: {
            line: 60,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 60
      },
      "38": {
        loc: {
          start: {
            line: 60,
            column: 24
          },
          end: {
            line: 60,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 24
          },
          end: {
            line: 60,
            column: 74
          }
        }, {
          start: {
            line: 60,
            column: 79
          },
          end: {
            line: 60,
            column: 90
          }
        }, {
          start: {
            line: 60,
            column: 94
          },
          end: {
            line: 60,
            column: 105
          }
        }],
        line: 60
      },
      "39": {
        loc: {
          start: {
            line: 60,
            column: 42
          },
          end: {
            line: 60,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 42
          },
          end: {
            line: 60,
            column: 54
          }
        }, {
          start: {
            line: 60,
            column: 58
          },
          end: {
            line: 60,
            column: 73
          }
        }],
        line: 60
      },
      "40": {
        loc: {
          start: {
            line: 61,
            column: 20
          },
          end: {
            line: 61,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 20
          },
          end: {
            line: 61,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "41": {
        loc: {
          start: {
            line: 61,
            column: 24
          },
          end: {
            line: 61,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 24
          },
          end: {
            line: 61,
            column: 35
          }
        }, {
          start: {
            line: 61,
            column: 40
          },
          end: {
            line: 61,
            column: 42
          }
        }, {
          start: {
            line: 61,
            column: 47
          },
          end: {
            line: 61,
            column: 59
          }
        }, {
          start: {
            line: 61,
            column: 63
          },
          end: {
            line: 61,
            column: 75
          }
        }],
        line: 61
      },
      "42": {
        loc: {
          start: {
            line: 62,
            column: 20
          },
          end: {
            line: 62,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 62,
            column: 20
          },
          end: {
            line: 62,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 62
      },
      "43": {
        loc: {
          start: {
            line: 62,
            column: 24
          },
          end: {
            line: 62,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 24
          },
          end: {
            line: 62,
            column: 35
          }
        }, {
          start: {
            line: 62,
            column: 39
          },
          end: {
            line: 62,
            column: 53
          }
        }],
        line: 62
      },
      "44": {
        loc: {
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "45": {
        loc: {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 25
          }
        }, {
          start: {
            line: 63,
            column: 29
          },
          end: {
            line: 63,
            column: 43
          }
        }],
        line: 63
      },
      "46": {
        loc: {
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 64,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 64,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "47": {
        loc: {
          start: {
            line: 69,
            column: 8
          },
          end: {
            line: 69,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 8
          },
          end: {
            line: 69,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "48": {
        loc: {
          start: {
            line: 69,
            column: 52
          },
          end: {
            line: 69,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 60
          },
          end: {
            line: 69,
            column: 65
          }
        }, {
          start: {
            line: 69,
            column: 68
          },
          end: {
            line: 69,
            column: 74
          }
        }],
        line: 69
      },
      "49": {
        loc: {
          start: {
            line: 94,
            column: 12
          },
          end: {
            line: 105,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 12
          },
          end: {
            line: 105,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "50": {
        loc: {
          start: {
            line: 97,
            column: 20
          },
          end: {
            line: 102,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 20
          },
          end: {
            line: 102,
            column: 21
          }
        }, {
          start: {
            line: 100,
            column: 25
          },
          end: {
            line: 102,
            column: 21
          }
        }],
        line: 97
      },
      "51": {
        loc: {
          start: {
            line: 100,
            column: 25
          },
          end: {
            line: 102,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 100,
            column: 25
          },
          end: {
            line: 102,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 100
      },
      "52": {
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 169,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 113,
            column: 16
          },
          end: {
            line: 121,
            column: 33
          }
        }, {
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 128,
            column: 28
          }
        }, {
          start: {
            line: 129,
            column: 16
          },
          end: {
            line: 134,
            column: 33
          }
        }, {
          start: {
            line: 135,
            column: 16
          },
          end: {
            line: 137,
            column: 58
          }
        }, {
          start: {
            line: 138,
            column: 16
          },
          end: {
            line: 140,
            column: 44
          }
        }, {
          start: {
            line: 141,
            column: 16
          },
          end: {
            line: 144,
            column: 44
          }
        }, {
          start: {
            line: 145,
            column: 16
          },
          end: {
            line: 145,
            column: 48
          }
        }, {
          start: {
            line: 146,
            column: 16
          },
          end: {
            line: 146,
            column: 62
          }
        }, {
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 151,
            column: 33
          }
        }, {
          start: {
            line: 152,
            column: 16
          },
          end: {
            line: 162,
            column: 45
          }
        }, {
          start: {
            line: 163,
            column: 16
          },
          end: {
            line: 167,
            column: 45
          }
        }, {
          start: {
            line: 168,
            column: 16
          },
          end: {
            line: 168,
            column: 47
          }
        }],
        line: 112
      },
      "53": {
        loc: {
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 119,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 119,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "54": {
        loc: {
          start: {
            line: 133,
            column: 20
          },
          end: {
            line: 133,
            column: 108
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 20
          },
          end: {
            line: 133,
            column: 108
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "55": {
        loc: {
          start: {
            line: 133,
            column: 26
          },
          end: {
            line: 133,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 26
          },
          end: {
            line: 133,
            column: 37
          }
        }, {
          start: {
            line: 133,
            column: 41
          },
          end: {
            line: 133,
            column: 81
          }
        }],
        line: 133
      },
      "56": {
        loc: {
          start: {
            line: 153,
            column: 20
          },
          end: {
            line: 161,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 20
          },
          end: {
            line: 161,
            column: 21
          }
        }, {
          start: {
            line: 159,
            column: 25
          },
          end: {
            line: 161,
            column: 21
          }
        }],
        line: 153
      },
      "57": {
        loc: {
          start: {
            line: 155,
            column: 24
          },
          end: {
            line: 157,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 24
          },
          end: {
            line: 157,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "58": {
        loc: {
          start: {
            line: 160,
            column: 78
          },
          end: {
            line: 160,
            column: 124
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 78
          },
          end: {
            line: 160,
            column: 90
          }
        }, {
          start: {
            line: 160,
            column: 94
          },
          end: {
            line: 160,
            column: 124
          }
        }],
        line: 160
      },
      "59": {
        loc: {
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 208,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 176,
            column: 16
          },
          end: {
            line: 179,
            column: 33
          }
        }, {
          start: {
            line: 180,
            column: 16
          },
          end: {
            line: 186,
            column: 28
          }
        }, {
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 189,
            column: 58
          }
        }, {
          start: {
            line: 190,
            column: 16
          },
          end: {
            line: 198,
            column: 44
          }
        }, {
          start: {
            line: 199,
            column: 16
          },
          end: {
            line: 203,
            column: 44
          }
        }, {
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 206,
            column: 46
          }
        }, {
          start: {
            line: 207,
            column: 16
          },
          end: {
            line: 207,
            column: 46
          }
        }],
        line: 175
      },
      "60": {
        loc: {
          start: {
            line: 192,
            column: 20
          },
          end: {
            line: 197,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 20
          },
          end: {
            line: 197,
            column: 21
          }
        }, {
          start: {
            line: 195,
            column: 25
          },
          end: {
            line: 197,
            column: 21
          }
        }],
        line: 192
      },
      "61": {
        loc: {
          start: {
            line: 196,
            column: 78
          },
          end: {
            line: 196,
            column: 127
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 196,
            column: 78
          },
          end: {
            line: 196,
            column: 88
          }
        }, {
          start: {
            line: 196,
            column: 92
          },
          end: {
            line: 196,
            column: 127
          }
        }],
        line: 196
      },
      "62": {
        loc: {
          start: {
            line: 212,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 212,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 212
      },
      "63": {
        loc: {
          start: {
            line: 213,
            column: 1601
          },
          end: {
            line: 213,
            column: 1657
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 213,
            column: 1615
          },
          end: {
            line: 213,
            column: 1627
          }
        }, {
          start: {
            line: 213,
            column: 1630
          },
          end: {
            line: 213,
            column: 1657
          }
        }],
        line: 213
      },
      "64": {
        loc: {
          start: {
            line: 218,
            column: 226
          },
          end: {
            line: 220,
            column: 130
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 218,
            column: 226
          },
          end: {
            line: 218,
            column: 237
          }
        }, {
          start: {
            line: 218,
            column: 242
          },
          end: {
            line: 220,
            column: 129
          }
        }],
        line: 218
      },
      "65": {
        loc: {
          start: {
            line: 218,
            column: 312
          },
          end: {
            line: 220,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 218,
            column: 345
          },
          end: {
            line: 218,
            column: 415
          }
        }, {
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 220,
            column: 94
          }
        }],
        line: 218
      },
      "66": {
        loc: {
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 220,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 219,
            column: 55
          },
          end: {
            line: 219,
            column: 117
          }
        }, {
          start: {
            line: 220,
            column: 28
          },
          end: {
            line: 220,
            column: 94
          }
        }],
        line: 219
      },
      "67": {
        loc: {
          start: {
            line: 222,
            column: 125
          },
          end: {
            line: 222,
            column: 172
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 222,
            column: 139
          },
          end: {
            line: 222,
            column: 160
          }
        }, {
          start: {
            line: 222,
            column: 163
          },
          end: {
            line: 222,
            column: 172
          }
        }],
        line: 222
      },
      "68": {
        loc: {
          start: {
            line: 222,
            column: 700
          },
          end: {
            line: 224,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 223,
            column: 30
          },
          end: {
            line: 223,
            column: 46
          }
        }, {
          start: {
            line: 224,
            column: 30
          },
          end: {
            line: 224,
            column: 68
          }
        }],
        line: 222
      },
      "69": {
        loc: {
          start: {
            line: 224,
            column: 162
          },
          end: {
            line: 224,
            column: 297
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 224,
            column: 162
          },
          end: {
            line: 224,
            column: 184
          }
        }, {
          start: {
            line: 224,
            column: 189
          },
          end: {
            line: 224,
            column: 296
          }
        }],
        line: 224
      },
      "70": {
        loc: {
          start: {
            line: 224,
            column: 831
          },
          end: {
            line: 226,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 30
          },
          end: {
            line: 225,
            column: 46
          }
        }, {
          start: {
            line: 226,
            column: 30
          },
          end: {
            line: 226,
            column: 68
          }
        }],
        line: 224
      },
      "71": {
        loc: {
          start: {
            line: 226,
            column: 170
          },
          end: {
            line: 226,
            column: 311
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 170
          },
          end: {
            line: 226,
            column: 195
          }
        }, {
          start: {
            line: 226,
            column: 200
          },
          end: {
            line: 226,
            column: 310
          }
        }],
        line: 226
      },
      "72": {
        loc: {
          start: {
            line: 226,
            column: 622
          },
          end: {
            line: 226,
            column: 660
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 226,
            column: 636
          },
          end: {
            line: 226,
            column: 648
          }
        }, {
          start: {
            line: 226,
            column: 651
          },
          end: {
            line: 226,
            column: 660
          }
        }],
        line: 226
      },
      "73": {
        loc: {
          start: {
            line: 226,
            column: 1102
          },
          end: {
            line: 226,
            column: 1307
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 1102
          },
          end: {
            line: 226,
            column: 1113
          }
        }, {
          start: {
            line: 226,
            column: 1117
          },
          end: {
            line: 226,
            column: 1145
          }
        }, {
          start: {
            line: 226,
            column: 1150
          },
          end: {
            line: 226,
            column: 1306
          }
        }],
        line: 226
      },
      "74": {
        loc: {
          start: {
            line: 226,
            column: 1309
          },
          end: {
            line: 226,
            column: 1614
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 1309
          },
          end: {
            line: 226,
            column: 1320
          }
        }, {
          start: {
            line: 226,
            column: 1325
          },
          end: {
            line: 226,
            column: 1355
          }
        }, {
          start: {
            line: 226,
            column: 1359
          },
          end: {
            line: 226,
            column: 1386
          }
        }, {
          start: {
            line: 226,
            column: 1392
          },
          end: {
            line: 226,
            column: 1613
          }
        }],
        line: 226
      },
      "75": {
        loc: {
          start: {
            line: 226,
            column: 1495
          },
          end: {
            line: 226,
            column: 1562
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 226,
            column: 1528
          },
          end: {
            line: 226,
            column: 1544
          }
        }, {
          start: {
            line: 226,
            column: 1547
          },
          end: {
            line: 226,
            column: 1562
          }
        }],
        line: 226
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0, 0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0, 0, 0, 0, 0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0, 0],
      "74": [0, 0, 0, 0],
      "75": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/SignupForm.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,6CAAqD;AACrD,2BAAwB;AACxB,2CAA0C;AAE1C,6DAA4E;AAE5E,IAAM,UAAU,GAAG;IACX,IAAA,KAAoB,IAAA,gBAAQ,EAAC,EAAE,CAAC,EAA/B,KAAK,QAAA,EAAE,QAAQ,QAAgB,CAAC;IACjC,IAAA,KAA0B,IAAA,gBAAQ,EAAC,EAAE,CAAC,EAArC,QAAQ,QAAA,EAAE,WAAW,QAAgB,CAAC;IACvC,IAAA,KAAgC,IAAA,gBAAQ,EAAiE,IAAI,CAAC,EAA7G,WAAW,QAAA,EAAE,cAAc,QAAkF,CAAC;IAC/G,IAAA,KAA0C,IAAA,gBAAQ,EAAsC,EAAE,CAAC,EAA1F,gBAAgB,QAAA,EAAE,mBAAmB,QAAqD,CAAC;IAC5F,IAAA,KAAkC,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAAhD,YAAY,QAAA,EAAE,eAAe,QAAmB,CAAC;IAClD,IAAA,KAAgC,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAA9C,WAAW,QAAA,EAAE,cAAc,QAAmB,CAAC;IAChD,IAAA,KAAyC,IAAA,iBAAO,GAAE,EAAhD,UAAU,gBAAA,EAAa,WAAW,eAAc,CAAC;IAEzD,sBAAsB;IACtB,IAAM,YAAY,GAAG,IAAA,mBAAW,EAAC;QAC/B,IAAI,CAAC;YACH,gCAAY,CAAC,KAAK,CAAC,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;YACxC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;gBAChC,IAAM,QAAM,GAAwC,EAAE,CAAC;gBACvD,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,GAAG;oBACvB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;wBAC5B,QAAM,CAAC,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC;oBAC7B,CAAC;yBAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE,CAAC;wBACtC,QAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC;oBAChC,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,mBAAmB,CAAC,QAAM,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEtB,IAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,UAAO,KAAuC;;;;;oBAC7E,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,mBAAmB,CAAC,EAAE,CAAC,CAAC;oBAExB,yBAAyB;oBACzB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;wBACpB,sBAAO;oBACT,CAAC;oBAED,cAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;;;;oBAGxC,qBAAM,KAAK,CAAC,aAAa,EAAE;4BAC1C,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE,UAAU,EAAE;4BACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,CAAC;yBAC1C,CAAC,EAAA;;oBAJI,QAAQ,GAAG,SAIf;oBAGI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;oBACrD,IAAI,GAAwD,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;yBAExG,CAAA,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAA,EAAvD,wBAAuD;;;;oBAEhD,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAA5B,IAAI,GAAG,SAAqB,CAAC;;;;oBAE7B,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,WAAS,CAAC,CAAC;;;wBAKxC,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAApC,YAAY,GAAG,SAAqB;oBAC1C,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,YAAY,CAAC,CAAC;oBAC3D,IAAI,CAAC,OAAO,GAAG,yCAAyC,CAAC;;;oBAG3D,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;wBAChB,cAAc,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC3D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAC9B,eAAe,CAAC,IAAI,CAAC,CAAC;wBACxB,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,8BAA8B,CAAC,EAAE,CAAC,CAAC;oBAC3G,CAAC;;;;oBAED,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,OAAK,CAAC,CAAC;oBACtC,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC,CAAC;;;;;SAEtF,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;IAEhD,IAAM,wBAAwB,GAAG,IAAA,mBAAW,EAAC;;;;;oBAC3C,cAAc,CAAC,IAAI,CAAC,CAAC;oBACrB,cAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC,CAAC;;;;oBAGxD,qBAAM,KAAK,CAAC,+BAA+B,EAAE;4BAC5D,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE,UAAU,EAAE;4BACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC;yBAChC,CAAC,EAAA;;oBAJI,QAAQ,GAAG,SAIf;oBAEW,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAA5B,IAAI,GAAG,SAAqB;oBAElC,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;wBAChB,cAAc,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC7D,CAAC;yBAAM,CAAC;wBACN,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,mCAAmC,CAAC,EAAE,CAAC,CAAC;oBAC9G,CAAC;;;;oBAED,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,OAAK,CAAC,CAAC;oBACnD,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC,CAAC;;;oBAEnF,cAAc,CAAC,KAAK,CAAC,CAAC;;;;;SAEzB,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;IAExB,4DAA4D;IAC5D,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,CACL,iCAAK,SAAS,EAAC,sBAAsB,aACnC,gCAAK,SAAS,EAAC,+FAA+F,YAC5G,iCAAK,SAAS,EAAC,MAAM,aACnB,gCAAK,SAAS,EAAC,eAAe,YAC5B,gCAAK,SAAS,EAAC,wBAAwB,EAAC,OAAO,EAAC,WAAW,EAAC,IAAI,EAAC,cAAc,YAC7E,iCAAM,QAAQ,EAAC,SAAS,EAAC,CAAC,EAAC,uIAAuI,EAAC,QAAQ,EAAC,SAAS,GAAG,GACpL,GACF,EACN,iCAAK,SAAS,EAAC,MAAM,aACnB,+BAAI,SAAS,EAAC,wDAAwD,yCAEjE,EACL,gCAAK,SAAS,EAAC,iDAAiD,YAC9D,iFACqC,6CAAS,KAAK,GAAU,2FAEzD,GACA,EACN,iCAAK,SAAS,EAAC,gBAAgB,aAC7B,mCACE,OAAO,EAAE,wBAAwB,EACjC,QAAQ,EAAE,WAAW,EACrB,SAAS,EAAC,qJAAqJ,YAE9J,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,2BAA2B,GAClD,EACT,0CACE,mCACE,OAAO,EAAE;wDACP,eAAe,CAAC,KAAK,CAAC,CAAC;wDACvB,QAAQ,CAAC,EAAE,CAAC,CAAC;wDACb,WAAW,CAAC,EAAE,CAAC,CAAC;wDAChB,cAAc,CAAC,IAAI,CAAC,CAAC;oDACvB,CAAC,EACD,SAAS,EAAC,iGAAiG,2CAGpG,GACL,IACF,IACF,IACF,GACF,EACL,WAAW,IAAI,CACd,gCAAK,SAAS,EAAE,2BACd,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,sEAAsE,CAAC,CAAC;wBACzG,WAAW,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,8DAA8D,CAAC,CAAC;4BAC/F,kEAAkE,CAClE,YACC,WAAW,CAAC,OAAO,GAChB,CACP,IACG,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,kCACE,QAAQ,EAAE,YAAY,EACtB,SAAS,EAAC,sBAAsB,sBACd,WAAW,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,aAEjE,iCAAK,SAAS,EAAC,MAAM,aACnB,kCAAO,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,+DAA+D,8BAAsB,EACtH,kCACE,IAAI,EAAC,OAAO,EACZ,EAAE,EAAC,OAAO,EACV,IAAI,EAAC,OAAO,EACZ,KAAK,EAAE,KAAK,EACZ,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAxB,CAAwB,EACzC,SAAS,EAAE,mIACT,gBAAgB,CAAC,KAAK;4BACpB,CAAC,CAAC,gBAAgB;4BAClB,CAAC,CAAC,sCAAsC,CAC1C,EACF,WAAW,EAAC,kBAAkB,EAC9B,YAAY,EAAC,OAAO,EACpB,QAAQ,QACR,SAAS,EAAE,GAAG,GACd,EACD,gBAAgB,CAAC,KAAK,IAAI,CACzB,gCAAK,SAAS,EAAC,2BAA2B,YAAE,gBAAgB,CAAC,KAAK,GAAO,CAC1E,IACG,EACN,iCAAK,SAAS,EAAC,MAAM,aACnB,kCAAO,OAAO,EAAC,UAAU,EAAC,SAAS,EAAC,+DAA+D,yBAAiB,EACpH,kCACE,IAAI,EAAC,UAAU,EACf,EAAE,EAAC,UAAU,EACb,IAAI,EAAC,UAAU,EACf,KAAK,EAAE,QAAQ,EACf,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAA3B,CAA2B,EAC5C,SAAS,EAAE,mIACT,gBAAgB,CAAC,QAAQ;4BACvB,CAAC,CAAC,gBAAgB;4BAClB,CAAC,CAAC,sCAAsC,CAC1C,EACF,WAAW,EAAC,mBAAmB,EAC/B,YAAY,EAAC,cAAc,EAC3B,QAAQ,QACR,SAAS,EAAE,GAAG,GACd,EACD,gBAAgB,CAAC,QAAQ,IAAI,CAC5B,gCAAK,SAAS,EAAC,2BAA2B,YAAE,gBAAgB,CAAC,QAAQ,GAAO,CAC7E,IACG,EACN,gCAAK,SAAS,EAAC,mCAAmC,YAChD,mCACE,IAAI,EAAC,QAAQ,EACb,SAAS,EAAC,sHAAsH,EAChI,QAAQ,EAAE,WAAW,YAEpB,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,GAChC,GACL,EACN,iCAAK,SAAS,EAAC,2DAA2D,iDACxC,8BAAG,IAAI,EAAC,mBAAmB,EAAC,SAAS,EAAC,+BAA+B,iCAAqB,WAAK,8BAAG,IAAI,EAAC,iBAAiB,EAAC,SAAS,EAAC,+BAA+B,+BAAmB,SACjN,EACL,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,IAAI,CAC9C,8BACE,EAAE,EAAC,qBAAqB,EACxB,SAAS,EAAC,uCAAuC,EACjD,IAAI,EAAC,OAAO,YAEX,WAAW,CAAC,OAAO,GAClB,CACL,EACA,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CACjF,8BACE,EAAE,EAAC,qBAAqB,EACxB,SAAS,EAAE,mCACT,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CACnE,EACF,IAAI,EAAC,QAAQ,YAEZ,WAAW,CAAC,OAAO,GAClB,CACL,IACI,CACR,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,UAAU,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/SignupForm.tsx"],
      sourcesContent: ["\"use client\";\n\nimport React, { useState, useCallback } from 'react';\nimport { z } from 'zod';\nimport { useCSRF } from '@/hooks/useCSRF';\nimport { useValidatedForm } from '@/hooks/useFormValidation';\nimport { FormValidationRules, signupSchema } from '@/lib/client-validation';\n\nconst SignupForm = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [formMessage, setFormMessage] = useState<{ type: 'info' | 'success' | 'error'; content: string } | null>(null);\n  const [validationErrors, setValidationErrors] = useState<{email?: string; password?: string}>({});\n  const [isRegistered, setIsRegistered] = useState(false);\n  const [isResending, setIsResending] = useState(false);\n  const { getHeaders, isLoading: csrfLoading } = useCSRF();\n\n  // Validation function\n  const validateForm = useCallback(() => {\n    try {\n      signupSchema.parse({ email, password });\n      setValidationErrors({});\n      return true;\n    } catch (error) {\n      if (error instanceof z.ZodError) {\n        const errors: {email?: string; password?: string} = {};\n        error.errors.forEach((err) => {\n          if (err.path[0] === 'email') {\n            errors.email = err.message;\n          } else if (err.path[0] === 'password') {\n            errors.password = err.message;\n          }\n        });\n        setValidationErrors(errors);\n      }\n      return false;\n    }\n  }, [email, password]);\n\n  const handleSubmit = useCallback(async (event: React.FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    setValidationErrors({});\n\n    // Client-side validation\n    if (!validateForm()) {\n      return;\n    }\n\n    setFormMessage({ type: 'info', content: 'Signing up...' });\n\n    try {\n      const response = await fetch('/api/signup', {\n        method: 'POST',\n        headers: getHeaders(),\n        body: JSON.stringify({ email, password }),\n      });\n\n      // Check if the response is JSON before parsing\n      const contentType = response.headers.get('content-type');\n      let data: { message: string; requiresVerification?: boolean } = { message: 'An unexpected error occurred' }; // Default error message\n\n      if (contentType && contentType.includes('application/json')) {\n        try {\n          data = await response.json();\n        } catch (jsonError) {\n          console.error('Failed to parse JSON response:', jsonError);\n          // If JSON parsing fails, data remains the default error message\n        }\n      } else {\n        // If response is not JSON, try to read as text\n        const textResponse = await response.text();\n        console.error('Non-JSON response received:', textResponse);\n        data.message = 'Received non-JSON response from server.';\n      }\n\n      if (response.ok) {\n        setFormMessage({ type: 'success', content: data.message });\n        if (data.requiresVerification) {\n          setIsRegistered(true);\n        }\n      } else {\n        setFormMessage({ type: 'error', content: 'Error: ' + (data.message || 'An unexpected error occurred') });\n      }\n    } catch (error) {\n      console.error('Signup error:', error);\n      setFormMessage({ type: 'error', content: 'Error: An unexpected error occurred.' });\n    }\n  }, [email, password, getHeaders, validateForm]);\n\n  const handleResendVerification = useCallback(async () => {\n    setIsResending(true);\n    setFormMessage({ type: 'info', content: 'Sending verification email...' });\n\n    try {\n      const response = await fetch('/api/auth/resend-verification', {\n        method: 'POST',\n        headers: getHeaders(),\n        body: JSON.stringify({ email }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setFormMessage({ type: 'success', content: data.message });\n      } else {\n        setFormMessage({ type: 'error', content: 'Error: ' + (data.error || 'Failed to send verification email') });\n      }\n    } catch (error) {\n      console.error('Resend verification error:', error);\n      setFormMessage({ type: 'error', content: 'Error: An unexpected error occurred.' });\n    } finally {\n      setIsResending(false);\n    }\n  }, [email, getHeaders]);\n\n  // Show verification success message if user just registered\n  if (isRegistered) {\n    return (\n      <div className=\"mt-8 w-full max-w-sm\">\n        <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                Registration Successful!\n              </h3>\n              <div className=\"mt-2 text-sm text-green-700 dark:text-green-300\">\n                <p>\n                  We've sent a verification email to <strong>{email}</strong>.\n                  Please check your inbox and click the verification link to activate your account.\n                </p>\n              </div>\n              <div className=\"mt-4 space-y-2\">\n                <button\n                  onClick={handleResendVerification}\n                  disabled={isResending}\n                  className=\"text-sm text-green-800 dark:text-green-200 underline hover:text-green-600 dark:hover:text-green-400 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isResending ? 'Sending...' : 'Resend verification email'}\n                </button>\n                <div>\n                  <button\n                    onClick={() => {\n                      setIsRegistered(false);\n                      setEmail('');\n                      setPassword('');\n                      setFormMessage(null);\n                    }}\n                    className=\"text-sm text-gray-600 dark:text-gray-400 underline hover:text-gray-800 dark:hover:text-gray-200\"\n                  >\n                    Register a different email\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        {formMessage && (\n          <div className={`mt-4 p-3 rounded ${\n            formMessage.type === 'success' ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' :\n            formMessage.type === 'error' ? 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300' :\n            'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'\n          }`}>\n            {formMessage.content}\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <form \n      onSubmit={handleSubmit} \n      className=\"mt-8 w-full max-w-sm\"\n      aria-describedby={formMessage ? \"signup-form-message\" : undefined}\n    >\n      <div className=\"mb-4\">\n        <label htmlFor=\"email\" className=\"block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2\">Email Address</label>\n        <input\n          type=\"email\"\n          id=\"email\"\n          name=\"email\"\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n          className={`appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight ${\n            validationErrors.email\n              ? 'border-red-500'\n              : 'border-gray-200 dark:border-gray-700'\n          }`}\n          placeholder=\"Enter your email\"\n          autoComplete=\"email\"\n          required\n          maxLength={254}\n        />\n        {validationErrors.email && (\n          <div className=\"text-red-500 text-sm mt-1\">{validationErrors.email}</div>\n        )}\n      </div>\n      <div className=\"mb-6\">\n        <label htmlFor=\"password\" className=\"block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2\">Password</label>\n        <input\n          type=\"password\"\n          id=\"password\"\n          name=\"password\"\n          value={password}\n          onChange={(e) => setPassword(e.target.value)}\n          className={`appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight ${\n            validationErrors.password\n              ? 'border-red-500'\n              : 'border-gray-200 dark:border-gray-700'\n          }`}\n          placeholder=\"Create a password\"\n          autoComplete=\"new-password\"\n          required\n          maxLength={128}\n        />\n        {validationErrors.password && (\n          <div className=\"text-red-500 text-sm mt-1\">{validationErrors.password}</div>\n        )}\n      </div>\n      <div className=\"flex items-center justify-between\">\n        <button\n          type=\"submit\"\n          className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed\"\n          disabled={csrfLoading}\n        >\n          {csrfLoading ? 'Loading...' : 'Sign Up'}\n        </button>\n      </div>\n      <div className=\"mt-4 text-center text-xs text-gray-500 dark:text-gray-400\">\n        By signing up, you agree to our <a href=\"/terms-of-service\" className=\"underline hover:text-blue-600\">Terms of Service</a> and <a href=\"/privacy-policy\" className=\"underline hover:text-blue-600\">Privacy Policy</a>.\n      </div>\n      {formMessage && formMessage.type === 'error' && (\n        <p\n          id=\"signup-form-message\"\n          className=\"mt-4 text-center text-sm text-red-600\"\n          role=\"alert\"\n        >\n          {formMessage.content}\n        </p>\n      )}\n      {formMessage && (formMessage.type === 'success' || formMessage.type === 'info') && (\n        <p\n          id=\"signup-form-message\"\n          className={`mt-4 text-center text-sm ${ \n            formMessage.type === 'success' ? 'text-green-600' : 'text-blue-600'\n          }`}\n          role=\"status\"\n        >\n          {formMessage.content}\n        </p>\n      )}\n    </form>\n  );\n};\n\nexport default SignupForm; "],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "73730e933b23c77264a61d2f24fa23ed6d5a009e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ls1n4eps0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ls1n4eps0();
var __createBinding =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[0]++,
/* istanbul ignore next */
(cov_1ls1n4eps0().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1ls1n4eps0().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_1ls1n4eps0().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_1ls1n4eps0().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[0]++;
  cov_1ls1n4eps0().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().b[2][0]++;
    cov_1ls1n4eps0().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_1ls1n4eps0().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_1ls1n4eps0().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_1ls1n4eps0().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[5][1]++,
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().b[3][0]++;
    cov_1ls1n4eps0().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_1ls1n4eps0().f[1]++;
        cov_1ls1n4eps0().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_1ls1n4eps0().b[3][1]++;
  }
  cov_1ls1n4eps0().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_1ls1n4eps0().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[2]++;
  cov_1ls1n4eps0().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().b[7][0]++;
    cov_1ls1n4eps0().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_1ls1n4eps0().b[7][1]++;
  }
  cov_1ls1n4eps0().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[11]++,
/* istanbul ignore next */
(cov_1ls1n4eps0().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1ls1n4eps0().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_1ls1n4eps0().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_1ls1n4eps0().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[3]++;
  cov_1ls1n4eps0().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_1ls1n4eps0().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[4]++;
  cov_1ls1n4eps0().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[14]++,
/* istanbul ignore next */
(cov_1ls1n4eps0().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_1ls1n4eps0().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_1ls1n4eps0().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[5]++;
  cov_1ls1n4eps0().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[6]++;
    cov_1ls1n4eps0().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_1ls1n4eps0().s[17]++, []);
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_1ls1n4eps0().b[12][0]++;
          cov_1ls1n4eps0().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_1ls1n4eps0().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_1ls1n4eps0().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_1ls1n4eps0().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[8]++;
    cov_1ls1n4eps0().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().b[13][0]++;
      cov_1ls1n4eps0().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_1ls1n4eps0().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[26]++, {});
    /* istanbul ignore next */
    cov_1ls1n4eps0().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().b[15][0]++;
      cov_1ls1n4eps0().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_1ls1n4eps0().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_1ls1n4eps0().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_1ls1n4eps0().b[16][0]++;
          cov_1ls1n4eps0().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_1ls1n4eps0().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_1ls1n4eps0().b[15][1]++;
    }
    cov_1ls1n4eps0().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_1ls1n4eps0().s[34]++;
    return result;
  };
}()));
var __awaiter =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[35]++,
/* istanbul ignore next */
(cov_1ls1n4eps0().b[17][0]++, this) &&
/* istanbul ignore next */
(cov_1ls1n4eps0().b[17][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1ls1n4eps0().b[17][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[9]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[10]++;
    cov_1ls1n4eps0().s[36]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[18][0]++, value) :
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[18][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[11]++;
      cov_1ls1n4eps0().s[37]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1ls1n4eps0().s[38]++;
  return new (
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[19][0]++, P) ||
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[19][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[12]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[13]++;
      cov_1ls1n4eps0().s[39]++;
      try {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[40]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[41]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[14]++;
      cov_1ls1n4eps0().s[42]++;
      try {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[43]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[44]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[15]++;
      cov_1ls1n4eps0().s[45]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[20][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[20][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1ls1n4eps0().s[46]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[21][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[21][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[47]++,
/* istanbul ignore next */
(cov_1ls1n4eps0().b[22][0]++, this) &&
/* istanbul ignore next */
(cov_1ls1n4eps0().b[22][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1ls1n4eps0().b[22][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[16]++;
  var _ =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[48]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1ls1n4eps0().f[17]++;
        cov_1ls1n4eps0().s[49]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1ls1n4eps0().b[23][0]++;
          cov_1ls1n4eps0().s[50]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1ls1n4eps0().b[23][1]++;
        }
        cov_1ls1n4eps0().s[51]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[52]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[24][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[24][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1ls1n4eps0().s[53]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[25][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1ls1n4eps0().b[25][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[18]++;
    cov_1ls1n4eps0().s[54]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[19]++;
    cov_1ls1n4eps0().s[55]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[20]++;
      cov_1ls1n4eps0().s[56]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[21]++;
    cov_1ls1n4eps0().s[57]++;
    if (f) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().b[26][0]++;
      cov_1ls1n4eps0().s[58]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1ls1n4eps0().b[26][1]++;
    }
    cov_1ls1n4eps0().s[59]++;
    while (
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[27][0]++, g) &&
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[27][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[28][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[28][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[60]++;
      try {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[61]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[30][0]++, y) &&
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[30][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[31][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[31][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[32][0]++,
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[33][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[33][1]++,
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[34][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[34][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[32][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[30][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1ls1n4eps0().b[29][0]++;
          cov_1ls1n4eps0().s[62]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1ls1n4eps0().b[29][1]++;
        }
        cov_1ls1n4eps0().s[63]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1ls1n4eps0().b[35][0]++;
          cov_1ls1n4eps0().s[64]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1ls1n4eps0().b[35][1]++;
        }
        cov_1ls1n4eps0().s[65]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[36][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[36][1]++;
            cov_1ls1n4eps0().s[66]++;
            t = op;
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[67]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[36][2]++;
            cov_1ls1n4eps0().s[68]++;
            _.label++;
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[69]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[36][3]++;
            cov_1ls1n4eps0().s[70]++;
            _.label++;
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[71]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[72]++;
            op = [0];
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[73]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[36][4]++;
            cov_1ls1n4eps0().s[74]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[75]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[76]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[36][5]++;
            cov_1ls1n4eps0().s[77]++;
            if (
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[38][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[39][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[39][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[38][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[38][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[37][0]++;
              cov_1ls1n4eps0().s[78]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1ls1n4eps0().s[79]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[37][1]++;
            }
            cov_1ls1n4eps0().s[80]++;
            if (
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[41][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[41][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[41][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[41][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[40][0]++;
              cov_1ls1n4eps0().s[81]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1ls1n4eps0().s[82]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[40][1]++;
            }
            cov_1ls1n4eps0().s[83]++;
            if (
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[43][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[43][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[42][0]++;
              cov_1ls1n4eps0().s[84]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1ls1n4eps0().s[85]++;
              t = op;
              /* istanbul ignore next */
              cov_1ls1n4eps0().s[86]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[42][1]++;
            }
            cov_1ls1n4eps0().s[87]++;
            if (
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[45][0]++, t) &&
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[45][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[44][0]++;
              cov_1ls1n4eps0().s[88]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1ls1n4eps0().s[89]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1ls1n4eps0().s[90]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[44][1]++;
            }
            cov_1ls1n4eps0().s[91]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[46][0]++;
              cov_1ls1n4eps0().s[92]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[46][1]++;
            }
            cov_1ls1n4eps0().s[93]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[94]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[95]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[96]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[97]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[98]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1ls1n4eps0().s[99]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().b[47][0]++;
      cov_1ls1n4eps0().s[100]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1ls1n4eps0().b[47][1]++;
    }
    cov_1ls1n4eps0().s[101]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[48][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[48][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1ls1n4eps0().s[102]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[103]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[104]++, __importStar(require("react")));
var zod_1 =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[105]++, require("zod"));
var useCSRF_1 =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[106]++, require("@/hooks/useCSRF"));
var client_validation_1 =
/* istanbul ignore next */
(cov_1ls1n4eps0().s[107]++, require("@/lib/client-validation"));
/* istanbul ignore next */
cov_1ls1n4eps0().s[108]++;
var SignupForm = function () {
  /* istanbul ignore next */
  cov_1ls1n4eps0().f[22]++;
  var _a =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[109]++, (0, react_1.useState)('')),
    email =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[110]++, _a[0]),
    setEmail =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[111]++, _a[1]);
  var _b =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[112]++, (0, react_1.useState)('')),
    password =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[113]++, _b[0]),
    setPassword =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[114]++, _b[1]);
  var _c =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[115]++, (0, react_1.useState)(null)),
    formMessage =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[116]++, _c[0]),
    setFormMessage =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[117]++, _c[1]);
  var _d =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[118]++, (0, react_1.useState)({})),
    validationErrors =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[119]++, _d[0]),
    setValidationErrors =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[120]++, _d[1]);
  var _e =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[121]++, (0, react_1.useState)(false)),
    isRegistered =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[122]++, _e[0]),
    setIsRegistered =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[123]++, _e[1]);
  var _f =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[124]++, (0, react_1.useState)(false)),
    isResending =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[125]++, _f[0]),
    setIsResending =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[126]++, _f[1]);
  var _g =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[127]++, (0, useCSRF_1.useCSRF)()),
    getHeaders =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[128]++, _g.getHeaders),
    csrfLoading =
    /* istanbul ignore next */
    (cov_1ls1n4eps0().s[129]++, _g.isLoading);
  // Validation function
  var validateForm =
  /* istanbul ignore next */
  (cov_1ls1n4eps0().s[130]++, (0, react_1.useCallback)(function () {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[23]++;
    cov_1ls1n4eps0().s[131]++;
    try {
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[132]++;
      client_validation_1.signupSchema.parse({
        email: email,
        password: password
      });
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[133]++;
      setValidationErrors({});
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[134]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[135]++;
      if (error instanceof zod_1.z.ZodError) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().b[49][0]++;
        var errors_1 =
        /* istanbul ignore next */
        (cov_1ls1n4eps0().s[136]++, {});
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[137]++;
        error.errors.forEach(function (err) {
          /* istanbul ignore next */
          cov_1ls1n4eps0().f[24]++;
          cov_1ls1n4eps0().s[138]++;
          if (err.path[0] === 'email') {
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[50][0]++;
            cov_1ls1n4eps0().s[139]++;
            errors_1.email = err.message;
          } else {
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[50][1]++;
            cov_1ls1n4eps0().s[140]++;
            if (err.path[0] === 'password') {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[51][0]++;
              cov_1ls1n4eps0().s[141]++;
              errors_1.password = err.message;
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[51][1]++;
            }
          }
        });
        /* istanbul ignore next */
        cov_1ls1n4eps0().s[142]++;
        setValidationErrors(errors_1);
      } else
      /* istanbul ignore next */
      {
        cov_1ls1n4eps0().b[49][1]++;
      }
      cov_1ls1n4eps0().s[143]++;
      return false;
    }
  }, [email, password]));
  var handleSubmit =
  /* istanbul ignore next */
  (cov_1ls1n4eps0().s[144]++, (0, react_1.useCallback)(function (event) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[25]++;
    cov_1ls1n4eps0().s[145]++;
    return __awaiter(void 0, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[26]++;
      var response, contentType, data, jsonError_1, textResponse, error_1;
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[146]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().f[27]++;
        cov_1ls1n4eps0().s[147]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][0]++;
            cov_1ls1n4eps0().s[148]++;
            event.preventDefault();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[149]++;
            setValidationErrors({});
            // Client-side validation
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[150]++;
            if (!validateForm()) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[53][0]++;
              cov_1ls1n4eps0().s[151]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[53][1]++;
            }
            cov_1ls1n4eps0().s[152]++;
            setFormMessage({
              type: 'info',
              content: 'Signing up...'
            });
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[153]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][1]++;
            cov_1ls1n4eps0().s[154]++;
            _a.trys.push([1, 10,, 11]);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[155]++;
            return [4 /*yield*/, fetch('/api/signup', {
              method: 'POST',
              headers: getHeaders(),
              body: JSON.stringify({
                email: email,
                password: password
              })
            })];
          case 2:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][2]++;
            cov_1ls1n4eps0().s[156]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[157]++;
            contentType = response.headers.get('content-type');
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[158]++;
            data = {
              message: 'An unexpected error occurred'
            };
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[159]++;
            if (!(
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[55][0]++, contentType) &&
            /* istanbul ignore next */
            (cov_1ls1n4eps0().b[55][1]++, contentType.includes('application/json')))) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[54][0]++;
              cov_1ls1n4eps0().s[160]++;
              return [3 /*break*/, 7];
            } else
            /* istanbul ignore next */
            {
              cov_1ls1n4eps0().b[54][1]++;
            }
            cov_1ls1n4eps0().s[161]++;
            _a.label = 3;
          case 3:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][3]++;
            cov_1ls1n4eps0().s[162]++;
            _a.trys.push([3, 5,, 6]);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[163]++;
            return [4 /*yield*/, response.json()];
          case 4:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][4]++;
            cov_1ls1n4eps0().s[164]++;
            data = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[165]++;
            return [3 /*break*/, 6];
          case 5:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][5]++;
            cov_1ls1n4eps0().s[166]++;
            jsonError_1 = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[167]++;
            console.error('Failed to parse JSON response:', jsonError_1);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[168]++;
            return [3 /*break*/, 6];
          case 6:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][6]++;
            cov_1ls1n4eps0().s[169]++;
            return [3 /*break*/, 9];
          case 7:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][7]++;
            cov_1ls1n4eps0().s[170]++;
            return [4 /*yield*/, response.text()];
          case 8:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][8]++;
            cov_1ls1n4eps0().s[171]++;
            textResponse = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[172]++;
            console.error('Non-JSON response received:', textResponse);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[173]++;
            data.message = 'Received non-JSON response from server.';
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[174]++;
            _a.label = 9;
          case 9:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][9]++;
            cov_1ls1n4eps0().s[175]++;
            if (response.ok) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[56][0]++;
              cov_1ls1n4eps0().s[176]++;
              setFormMessage({
                type: 'success',
                content: data.message
              });
              /* istanbul ignore next */
              cov_1ls1n4eps0().s[177]++;
              if (data.requiresVerification) {
                /* istanbul ignore next */
                cov_1ls1n4eps0().b[57][0]++;
                cov_1ls1n4eps0().s[178]++;
                setIsRegistered(true);
              } else
              /* istanbul ignore next */
              {
                cov_1ls1n4eps0().b[57][1]++;
              }
            } else {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[56][1]++;
              cov_1ls1n4eps0().s[179]++;
              setFormMessage({
                type: 'error',
                content: 'Error: ' + (
                /* istanbul ignore next */
                (cov_1ls1n4eps0().b[58][0]++, data.message) ||
                /* istanbul ignore next */
                (cov_1ls1n4eps0().b[58][1]++, 'An unexpected error occurred'))
              });
            }
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[180]++;
            return [3 /*break*/, 11];
          case 10:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][10]++;
            cov_1ls1n4eps0().s[181]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[182]++;
            console.error('Signup error:', error_1);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[183]++;
            setFormMessage({
              type: 'error',
              content: 'Error: An unexpected error occurred.'
            });
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[184]++;
            return [3 /*break*/, 11];
          case 11:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[52][11]++;
            cov_1ls1n4eps0().s[185]++;
            return [2 /*return*/];
        }
      });
    });
  }, [email, password, getHeaders, validateForm]));
  var handleResendVerification =
  /* istanbul ignore next */
  (cov_1ls1n4eps0().s[186]++, (0, react_1.useCallback)(function () {
    /* istanbul ignore next */
    cov_1ls1n4eps0().f[28]++;
    cov_1ls1n4eps0().s[187]++;
    return __awaiter(void 0, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_1ls1n4eps0().f[29]++;
      var response, data, error_2;
      /* istanbul ignore next */
      cov_1ls1n4eps0().s[188]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1ls1n4eps0().f[30]++;
        cov_1ls1n4eps0().s[189]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[59][0]++;
            cov_1ls1n4eps0().s[190]++;
            setIsResending(true);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[191]++;
            setFormMessage({
              type: 'info',
              content: 'Sending verification email...'
            });
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[192]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[59][1]++;
            cov_1ls1n4eps0().s[193]++;
            _a.trys.push([1, 4, 5, 6]);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[194]++;
            return [4 /*yield*/, fetch('/api/auth/resend-verification', {
              method: 'POST',
              headers: getHeaders(),
              body: JSON.stringify({
                email: email
              })
            })];
          case 2:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[59][2]++;
            cov_1ls1n4eps0().s[195]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[196]++;
            return [4 /*yield*/, response.json()];
          case 3:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[59][3]++;
            cov_1ls1n4eps0().s[197]++;
            data = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[198]++;
            if (response.ok) {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[60][0]++;
              cov_1ls1n4eps0().s[199]++;
              setFormMessage({
                type: 'success',
                content: data.message
              });
            } else {
              /* istanbul ignore next */
              cov_1ls1n4eps0().b[60][1]++;
              cov_1ls1n4eps0().s[200]++;
              setFormMessage({
                type: 'error',
                content: 'Error: ' + (
                /* istanbul ignore next */
                (cov_1ls1n4eps0().b[61][0]++, data.error) ||
                /* istanbul ignore next */
                (cov_1ls1n4eps0().b[61][1]++, 'Failed to send verification email'))
              });
            }
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[201]++;
            return [3 /*break*/, 6];
          case 4:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[59][4]++;
            cov_1ls1n4eps0().s[202]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[203]++;
            console.error('Resend verification error:', error_2);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[204]++;
            setFormMessage({
              type: 'error',
              content: 'Error: An unexpected error occurred.'
            });
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[205]++;
            return [3 /*break*/, 6];
          case 5:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[59][5]++;
            cov_1ls1n4eps0().s[206]++;
            setIsResending(false);
            /* istanbul ignore next */
            cov_1ls1n4eps0().s[207]++;
            return [7 /*endfinally*/];
          case 6:
            /* istanbul ignore next */
            cov_1ls1n4eps0().b[59][6]++;
            cov_1ls1n4eps0().s[208]++;
            return [2 /*return*/];
        }
      });
    });
  }, [email, getHeaders]));
  // Show verification success message if user just registered
  /* istanbul ignore next */
  cov_1ls1n4eps0().s[209]++;
  if (isRegistered) {
    /* istanbul ignore next */
    cov_1ls1n4eps0().b[62][0]++;
    cov_1ls1n4eps0().s[210]++;
    return (0, jsx_runtime_1.jsxs)("div", {
      className: "mt-8 w-full max-w-sm",
      children: [(0, jsx_runtime_1.jsx)("div", {
        className: "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4",
        children: (0, jsx_runtime_1.jsxs)("div", {
          className: "flex",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "flex-shrink-0",
            children: (0, jsx_runtime_1.jsx)("svg", {
              className: "h-5 w-5 text-green-400",
              viewBox: "0 0 20 20",
              fill: "currentColor",
              children: (0, jsx_runtime_1.jsx)("path", {
                fillRule: "evenodd",
                d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                clipRule: "evenodd"
              })
            })
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "ml-3",
            children: [(0, jsx_runtime_1.jsx)("h3", {
              className: "text-sm font-medium text-green-800 dark:text-green-200",
              children: "Registration Successful!"
            }), (0, jsx_runtime_1.jsx)("div", {
              className: "mt-2 text-sm text-green-700 dark:text-green-300",
              children: (0, jsx_runtime_1.jsxs)("p", {
                children: ["We've sent a verification email to ", (0, jsx_runtime_1.jsx)("strong", {
                  children: email
                }), ". Please check your inbox and click the verification link to activate your account."]
              })
            }), (0, jsx_runtime_1.jsxs)("div", {
              className: "mt-4 space-y-2",
              children: [(0, jsx_runtime_1.jsx)("button", {
                onClick: handleResendVerification,
                disabled: isResending,
                className: "text-sm text-green-800 dark:text-green-200 underline hover:text-green-600 dark:hover:text-green-400 disabled:opacity-50 disabled:cursor-not-allowed",
                children: isResending ?
                /* istanbul ignore next */
                (cov_1ls1n4eps0().b[63][0]++, 'Sending...') :
                /* istanbul ignore next */
                (cov_1ls1n4eps0().b[63][1]++, 'Resend verification email')
              }), (0, jsx_runtime_1.jsx)("div", {
                children: (0, jsx_runtime_1.jsx)("button", {
                  onClick: function () {
                    /* istanbul ignore next */
                    cov_1ls1n4eps0().f[31]++;
                    cov_1ls1n4eps0().s[211]++;
                    setIsRegistered(false);
                    /* istanbul ignore next */
                    cov_1ls1n4eps0().s[212]++;
                    setEmail('');
                    /* istanbul ignore next */
                    cov_1ls1n4eps0().s[213]++;
                    setPassword('');
                    /* istanbul ignore next */
                    cov_1ls1n4eps0().s[214]++;
                    setFormMessage(null);
                  },
                  className: "text-sm text-gray-600 dark:text-gray-400 underline hover:text-gray-800 dark:hover:text-gray-200",
                  children: "Register a different email"
                })
              })]
            })]
          })]
        })
      }),
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[64][0]++, formMessage) &&
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[64][1]++, (0, jsx_runtime_1.jsx)("div", {
        className: "mt-4 p-3 rounded ".concat(formMessage.type === 'success' ?
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[65][0]++, 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300') :
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[65][1]++, formMessage.type === 'error' ?
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[66][0]++, 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300') :
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[66][1]++, 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'))),
        children: formMessage.content
      }))]
    });
  } else
  /* istanbul ignore next */
  {
    cov_1ls1n4eps0().b[62][1]++;
  }
  cov_1ls1n4eps0().s[215]++;
  return (0, jsx_runtime_1.jsxs)("form", {
    onSubmit: handleSubmit,
    className: "mt-8 w-full max-w-sm",
    "aria-describedby": formMessage ?
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[67][0]++, "signup-form-message") :
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[67][1]++, undefined),
    children: [(0, jsx_runtime_1.jsxs)("div", {
      className: "mb-4",
      children: [(0, jsx_runtime_1.jsx)("label", {
        htmlFor: "email",
        className: "block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2",
        children: "Email Address"
      }), (0, jsx_runtime_1.jsx)("input", {
        type: "email",
        id: "email",
        name: "email",
        value: email,
        onChange: function (e) {
          /* istanbul ignore next */
          cov_1ls1n4eps0().f[32]++;
          cov_1ls1n4eps0().s[216]++;
          return setEmail(e.target.value);
        },
        className: "appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight ".concat(validationErrors.email ?
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[68][0]++, 'border-red-500') :
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[68][1]++, 'border-gray-200 dark:border-gray-700')),
        placeholder: "Enter your email",
        autoComplete: "email",
        required: true,
        maxLength: 254
      }),
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[69][0]++, validationErrors.email) &&
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[69][1]++, (0, jsx_runtime_1.jsx)("div", {
        className: "text-red-500 text-sm mt-1",
        children: validationErrors.email
      }))]
    }), (0, jsx_runtime_1.jsxs)("div", {
      className: "mb-6",
      children: [(0, jsx_runtime_1.jsx)("label", {
        htmlFor: "password",
        className: "block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2",
        children: "Password"
      }), (0, jsx_runtime_1.jsx)("input", {
        type: "password",
        id: "password",
        name: "password",
        value: password,
        onChange: function (e) {
          /* istanbul ignore next */
          cov_1ls1n4eps0().f[33]++;
          cov_1ls1n4eps0().s[217]++;
          return setPassword(e.target.value);
        },
        className: "appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight ".concat(validationErrors.password ?
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[70][0]++, 'border-red-500') :
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[70][1]++, 'border-gray-200 dark:border-gray-700')),
        placeholder: "Create a password",
        autoComplete: "new-password",
        required: true,
        maxLength: 128
      }),
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[71][0]++, validationErrors.password) &&
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[71][1]++, (0, jsx_runtime_1.jsx)("div", {
        className: "text-red-500 text-sm mt-1",
        children: validationErrors.password
      }))]
    }), (0, jsx_runtime_1.jsx)("div", {
      className: "flex items-center justify-between",
      children: (0, jsx_runtime_1.jsx)("button", {
        type: "submit",
        className: "bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed",
        disabled: csrfLoading,
        children: csrfLoading ?
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[72][0]++, 'Loading...') :
        /* istanbul ignore next */
        (cov_1ls1n4eps0().b[72][1]++, 'Sign Up')
      })
    }), (0, jsx_runtime_1.jsxs)("div", {
      className: "mt-4 text-center text-xs text-gray-500 dark:text-gray-400",
      children: ["By signing up, you agree to our ", (0, jsx_runtime_1.jsx)("a", {
        href: "/terms-of-service",
        className: "underline hover:text-blue-600",
        children: "Terms of Service"
      }), " and ", (0, jsx_runtime_1.jsx)("a", {
        href: "/privacy-policy",
        className: "underline hover:text-blue-600",
        children: "Privacy Policy"
      }), "."]
    }),
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[73][0]++, formMessage) &&
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[73][1]++, formMessage.type === 'error') &&
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[73][2]++, (0, jsx_runtime_1.jsx)("p", {
      id: "signup-form-message",
      className: "mt-4 text-center text-sm text-red-600",
      role: "alert",
      children: formMessage.content
    })),
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[74][0]++, formMessage) && (
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[74][1]++, formMessage.type === 'success') ||
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[74][2]++, formMessage.type === 'info')) &&
    /* istanbul ignore next */
    (cov_1ls1n4eps0().b[74][3]++, (0, jsx_runtime_1.jsx)("p", {
      id: "signup-form-message",
      className: "mt-4 text-center text-sm ".concat(formMessage.type === 'success' ?
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[75][0]++, 'text-green-600') :
      /* istanbul ignore next */
      (cov_1ls1n4eps0().b[75][1]++, 'text-blue-600')),
      role: "status",
      children: formMessage.content
    }))]
  });
};
/* istanbul ignore next */
cov_1ls1n4eps0().s[218]++;
exports.default = SignupForm;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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