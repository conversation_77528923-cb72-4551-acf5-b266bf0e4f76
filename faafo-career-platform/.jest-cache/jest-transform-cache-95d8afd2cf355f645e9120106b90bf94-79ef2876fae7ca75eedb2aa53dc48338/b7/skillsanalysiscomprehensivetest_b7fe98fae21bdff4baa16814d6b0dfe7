77c8359a8cdf11360d8a3ce75cbdc2c5
"use strict";
/**
 * Skills Analysis Comprehensive Tests
 *
 * Tests Skills Analysis Comprehensive API endpoints, request/response handling, validation, and error scenarios.
 *
 * @category unit
 * @requires API mocking, request simulation
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
// Mock dependencies
globals_1.jest.mock('@/lib/prisma', function () { return ({
    prisma: {
        skillAssessment: {
            findMany: globals_1.jest.fn(),
        },
        careerPath: {
            findFirst: globals_1.jest.fn(),
        },
        skillGapAnalysis: {
            create: globals_1.jest.fn(),
        },
    },
}); });
globals_1.jest.mock('next-auth', function () { return ({
    getServerSession: globals_1.jest.fn(),
}); });
globals_1.jest.mock('@/lib/services/geminiService', function () { return ({
    geminiService: {
        analyzeComprehensiveSkillGap: globals_1.jest.fn(),
    },
}); });
globals_1.jest.mock('@/lib/services/consolidated-cache-service', function () { return ({
    consolidatedCache: {
        get: globals_1.jest.fn(),
        set: globals_1.jest.fn(),
        generateAIKey: globals_1.jest.fn(),
    },
}); });
globals_1.jest.mock('@/lib/auth', function () { return ({
    authOptions: {},
}); });
globals_1.jest.mock('@/lib/errorHandler', function () { return ({
    withErrorHandler: function (handler) { return handler; },
}); });
globals_1.jest.mock('@/lib/rateLimit', function () { return ({
    withRateLimit: function (request, config, handler) { return handler(); },
}); });
globals_1.jest.mock('@/lib/csrf', function () { return ({
    withCSRFProtection: function (request, handler) { return handler(); },
}); });
var server_1 = require("next/server");
var route_1 = require("@/app/api/ai/skills-analysis/comprehensive/route");
var prisma_1 = require("@/lib/prisma");
var next_auth_1 = require("next-auth");
var geminiService_1 = require("@/lib/services/geminiService");
var consolidated_cache_service_1 = require("@/lib/services/consolidated-cache-service");
var mockPrisma = prisma_1.prisma;
var mockGetServerSession = next_auth_1.getServerSession;
var mockGeminiService = geminiService_1.geminiService;
var mockConsolidatedCache = consolidated_cache_service_1.consolidatedCache;
(0, globals_1.describe)('Comprehensive Skills Analysis API', function () {
    var mockUserId = 'test-user-id';
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
        mockGetServerSession.mockResolvedValue({
            user: { id: mockUserId },
        });
    });
    (0, globals_1.afterEach)(function () {
        globals_1.jest.resetAllMocks();
    });
    (0, globals_1.describe)('POST /api/ai/skills-analysis/comprehensive', function () {
        var validRequestData = {
            currentSkills: [
                {
                    skillName: 'JavaScript',
                    selfRating: 7,
                    confidenceLevel: 8,
                    yearsOfExperience: 3,
                },
                {
                    skillName: 'React',
                    selfRating: 6,
                    confidenceLevel: 7,
                    yearsOfExperience: 2,
                },
            ],
            targetCareerPath: {
                careerPathName: 'Full Stack Developer',
                targetLevel: 'ADVANCED',
            },
            preferences: {
                timeframe: 'ONE_YEAR',
                hoursPerWeek: 10,
                learningStyle: ['VISUAL', 'HANDS_ON'],
                budget: 'FREEMIUM',
                focusAreas: ['Backend Development', 'Database Design'],
            },
            includeMarketData: true,
            includePersonalizedPaths: true,
        };
        (0, globals_1.it)('should perform comprehensive skills analysis successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockCareerPathData, mockAIResponse, mockSkillGapAnalysis, request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockCareerPathData = {
                            id: 'fallback-career-path-id',
                            name: 'Full Stack Developer',
                            requiredSkills: [],
                            learningResources: [],
                            learningPaths: [],
                        };
                        mockAIResponse = {
                            success: true,
                            data: {
                                skillGaps: [
                                    {
                                        skillId: 'skill-2',
                                        skillName: 'Node.js',
                                        currentLevel: 3,
                                        targetLevel: 8,
                                        gapSeverity: 'HIGH',
                                        priority: 85,
                                        estimatedLearningTime: 120,
                                        marketDemand: 'VERY_HIGH',
                                        salaryImpact: 15,
                                    },
                                    {
                                        skillId: 'skill-3',
                                        skillName: 'PostgreSQL',
                                        currentLevel: 2,
                                        targetLevel: 7,
                                        gapSeverity: 'CRITICAL',
                                        priority: 95,
                                        estimatedLearningTime: 80,
                                        marketDemand: 'HIGH',
                                        salaryImpact: 12,
                                    },
                                ],
                                learningPlan: {
                                    totalEstimatedHours: 200,
                                    milestones: [
                                        {
                                            month: 3,
                                            skills: ['Node.js Basics'],
                                            estimatedHours: 60,
                                            learningPaths: ['Backend Development Path'],
                                        },
                                        {
                                            month: 6,
                                            skills: ['PostgreSQL Fundamentals'],
                                            estimatedHours: 40,
                                            learningPaths: ['Database Design Path'],
                                        },
                                    ],
                                    recommendedResources: [
                                        {
                                            resourceId: 'resource-1',
                                            resourceType: 'COURSE',
                                            priority: 'HIGH',
                                            skillsAddressed: ['Node.js'],
                                            estimatedHours: 60,
                                        },
                                    ],
                                },
                                careerReadiness: {
                                    currentScore: 65,
                                    targetScore: 85,
                                    improvementPotential: 20,
                                    timeToTarget: 8,
                                },
                                marketInsights: {
                                    industryTrends: [
                                        {
                                            skill: 'Node.js',
                                            trend: 'GROWING',
                                            demandLevel: 'VERY_HIGH',
                                        },
                                    ],
                                    salaryProjections: {
                                        currentEstimate: 75000,
                                        targetEstimate: 95000,
                                        improvementPotential: 26.7,
                                    },
                                },
                            },
                        };
                        mockSkillGapAnalysis = {
                            id: 'analysis-id',
                            userId: mockUserId,
                            targetCareerPathName: 'Full Stack Developer',
                            status: 'ACTIVE',
                            createdAt: new Date(),
                        };
                        mockConsolidatedCache.get.mockResolvedValue(null); // No cache
                        mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
                        mockPrisma.careerPath.findFirst.mockResolvedValue(mockCareerPathData);
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(mockAIResponse);
                        mockPrisma.skillGapAnalysis.create.mockResolvedValue(mockSkillGapAnalysis);
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(200);
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.analysisId).toBe('analysis-id');
                        (0, globals_1.expect)(result.data.skillGaps).toHaveLength(2);
                        (0, globals_1.expect)(result.data.skillGaps[0].skillName).toBe('Node.js');
                        (0, globals_1.expect)(result.data.skillGaps[1].skillName).toBe('PostgreSQL');
                        (0, globals_1.expect)(result.data.learningPlan.totalEstimatedHours).toBe(200);
                        (0, globals_1.expect)(result.data.careerReadiness.currentScore).toBe(65);
                        (0, globals_1.expect)(result.data.marketInsights).toBeDefined();
                        (0, globals_1.expect)(result.cached).toBe(false);
                        // Verify AI service was called with correct parameters
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(globals_1.expect.arrayContaining([
                            globals_1.expect.objectContaining({ skillName: 'JavaScript' }),
                            globals_1.expect.objectContaining({ skillName: 'React' }),
                        ]), globals_1.expect.objectContaining({
                            careerPathName: 'Full Stack Developer',
                            targetLevel: 'ADVANCED',
                        }), globals_1.expect.objectContaining({
                            timeframe: 'ONE_YEAR',
                            hoursPerWeek: 10,
                        }), globals_1.expect.objectContaining({
                            id: 'fallback-career-path-id',
                            name: 'Full Stack Developer',
                            requiredSkills: [],
                            learningResources: [],
                            learningPaths: [],
                        }), mockUserId);
                        // Verify skill gap analysis was created
                        (0, globals_1.expect)(mockPrisma.skillGapAnalysis.create).toHaveBeenCalledWith({
                            data: globals_1.expect.objectContaining({
                                userId: mockUserId,
                                targetCareerPathName: 'Full Stack Developer',
                                experienceLevel: 'ADVANCED',
                                timeframe: 'ONE_YEAR',
                                status: 'ACTIVE',
                            }),
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should return cached results when available', function () { return __awaiter(void 0, void 0, void 0, function () {
            var cachedData, request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        cachedData = {
                            analysisId: 'cached-analysis-id',
                            skillGaps: [],
                            learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },
                            careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },
                            generatedAt: '2024-01-01T00:00:00.000Z',
                        };
                        mockConsolidatedCache.get.mockResolvedValue(cachedData);
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(200);
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data).toEqual(cachedData);
                        (0, globals_1.expect)(result.cached).toBe(true);
                        (0, globals_1.expect)(result.generatedAt).toBe('2024-01-01T00:00:00.000Z');
                        // Verify AI service was not called
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).not.toHaveBeenCalled();
                        (0, globals_1.expect)(mockPrisma.skillGapAnalysis.create).not.toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should fail with invalid request data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var invalidRequestData, request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        invalidRequestData = {
                            currentSkills: [], // Empty array - should fail validation
                            targetCareerPath: {
                                careerPathName: 'A', // Too short
                                targetLevel: 'INVALID_LEVEL', // Invalid enum value
                            },
                            preferences: {
                                timeframe: 'INVALID_TIMEFRAME',
                                hoursPerWeek: -5, // Negative hours
                                learningStyle: [],
                                budget: 'INVALID_BUDGET',
                                focusAreas: [],
                            },
                        };
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(invalidRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(400);
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('Invalid request data');
                        (0, globals_1.expect)(result.details).toBeDefined();
                        (0, globals_1.expect)(Array.isArray(result.details)).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle AI service failures gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // Arrange
                        mockConsolidatedCache.get.mockResolvedValue(null);
                        mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
                        mockPrisma.careerPath.findFirst.mockResolvedValue(null);
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({
                            success: false,
                            error: 'AI service temporarily unavailable',
                        });
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(500);
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('AI service temporarily unavailable');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should require authentication', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // Arrange
                        mockGetServerSession.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(401);
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('Authentication required');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should merge existing skill assessments with current skills', function () { return __awaiter(void 0, void 0, void 0, function () {
            var existingAssessments, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        existingAssessments = [
                            {
                                skillId: 'skill-1',
                                skill: { name: 'TypeScript' },
                                selfRating: 8,
                                confidenceLevel: 9,
                                assessmentDate: new Date(),
                                assessmentType: 'SELF_ASSESSMENT',
                            },
                        ];
                        mockConsolidatedCache.get.mockResolvedValue(null);
                        mockPrisma.skillAssessment.findMany.mockResolvedValue(existingAssessments);
                        mockPrisma.careerPath.findFirst.mockResolvedValue(null);
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({
                            success: true,
                            data: {
                                skillGaps: [],
                                learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },
                                careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },
                            },
                        });
                        mockPrisma.skillGapAnalysis.create.mockResolvedValue({ id: 'analysis-id' });
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(200);
                        // Verify that AI service was called with merged skills (including TypeScript from assessments)
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(globals_1.expect.arrayContaining([
                            globals_1.expect.objectContaining({ skillName: 'JavaScript' }),
                            globals_1.expect.objectContaining({ skillName: 'React' }),
                            globals_1.expect.objectContaining({ skillName: 'TypeScript' }),
                        ]), globals_1.expect.any(Object), globals_1.expect.any(Object), globals_1.expect.objectContaining({
                            id: 'fallback-career-path-id',
                            name: 'Full Stack Developer',
                        }), mockUserId);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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