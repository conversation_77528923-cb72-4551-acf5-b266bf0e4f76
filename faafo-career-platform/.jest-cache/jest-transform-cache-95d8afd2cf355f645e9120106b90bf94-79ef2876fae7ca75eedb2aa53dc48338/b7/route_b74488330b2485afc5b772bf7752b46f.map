{"version": 3, "names": ["server_1", "cov_2757l6cm5l", "s", "require", "next_auth_1", "auth_1", "prisma_1", "__importDefault", "unified_api_error_handler_1", "sharp_1", "crypto_1", "blob_1", "logger_1", "csrf_1", "enhanced_rate_limiter_1", "MAX_FILE_SIZE", "ALLOWED_TYPES", "ALLOWED_EXTENSIONS", "AVATAR_SIZES", "thumbnail", "small", "medium", "large", "FILE_SIGNATURES", "MALICIOUS_PATTERNS", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "buffer", "mimeType", "fileName", "f", "Promise", "errors", "signatures", "b", "isValidSignature", "some", "signature", "every", "byte", "index", "push", "bufferString", "toString", "for<PERSON>ach", "pattern", "test", "fileExtension", "toLowerCase", "substring", "lastIndexOf", "includes", "expectedMimeTypes", "expectedTypes", "default", "metadata", "_a", "sent", "width", "height", "exif", "<PERSON><PERSON><PERSON>", "byteLength", "jpeg", "quality", "<PERSON><PERSON><PERSON><PERSON>", "sanitizedBuffer", "<PERSON><PERSON><PERSON><PERSON>", "length", "undefined", "processImage", "size", "resize", "fit", "position", "processed", "format", "generateFileName", "userId", "timestamp", "Date", "now", "hash", "createHash", "update", "concat", "digest", "uploadToStorage", "process", "env", "BLOB_READ_WRITE_TOKEN", "log", "warn", "component", "action", "fallback", "baseUrl", "NEXTAUTH_URL", "put", "access", "contentType", "blob", "url", "error", "error_2", "exports", "POST", "withUnifiedErrorHandling", "request", "__awaiter", "withCSRFProtection", "enhancedRateLimiters", "write", "checkLimit", "rateLimitResult", "_e", "allowed", "Error", "statusCode", "headers", "getServerSession", "authOptions", "session", "_c", "user", "email", "findUnique", "where", "formData", "file", "get", "type", "name", "_b", "from", "arrayBuffer", "apply", "validation", "id", "join", "data", "securityErrors", "all", "Object", "entries", "map", "sizeName", "sizePixels", "pixels", "processedImages", "primaryImageUrl", "_d", "find", "img", "profile", "upsert", "profilePictureUrl", "lastProfileUpdate", "updatedAt", "create", "updatedProfile", "NextResponse", "json", "success", "sizes", "reduce", "acc", "message", "DELETE", "GET", "pathname", "URL", "split", "pop", "error_3"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/photo/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport sharp from 'sharp';\nimport crypto from 'crypto';\nimport { put } from '@vercel/blob';\nimport { log } from '@/lib/logger';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';\n\n// SECURITY FIX: Enhanced file upload configuration\nconst MAX_FILE_SIZE = 2 * 1024 * 1024; // Reduced to 2MB for security\nconst ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];\nconst ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp'];\nconst AVATAR_SIZES = {\n  thumbnail: 64,\n  small: 128,\n  medium: 256,\n  large: 512\n};\n\n// SECURITY FIX: File signature validation (magic numbers)\nconst FILE_SIGNATURES = {\n  'image/jpeg': [\n    [0xFF, 0xD8, 0xFF], // JPEG\n  ],\n  'image/png': [\n    [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A], // PNG\n  ],\n  'image/webp': [\n    [0x52, 0x49, 0x46, 0x46], // RIFF (WebP container)\n  ]\n};\n\n// SECURITY FIX: Malicious file patterns to detect\nconst MALICIOUS_PATTERNS = [\n  // PHP tags\n  /<\\?php/i,\n  /<\\?=/i,\n  /<script/i,\n  // Executable signatures\n  /MZ/, // PE executable\n  /\\x7fELF/, // ELF executable\n  // Archive signatures\n  /PK\\x03\\x04/, // ZIP\n  /Rar!/, // RAR\n];\n\n// SECURITY FIX: Comprehensive file validation\nasync function validateFileContent(buffer: Buffer, mimeType: string, fileName: string): Promise<{\n  isValid: boolean;\n  errors: string[];\n  sanitizedBuffer?: Buffer;\n}> {\n  const errors: string[] = [];\n\n  // 1. File signature validation\n  const signatures = FILE_SIGNATURES[mimeType as keyof typeof FILE_SIGNATURES];\n  if (signatures) {\n    const isValidSignature = signatures.some(signature => {\n      return signature.every((byte, index) => buffer[index] === byte);\n    });\n\n    if (!isValidSignature) {\n      errors.push('File signature does not match declared MIME type');\n    }\n  }\n\n  // 2. Check for malicious patterns\n  const bufferString = buffer.toString('binary');\n  MALICIOUS_PATTERNS.forEach(pattern => {\n    if (pattern.test(bufferString)) {\n      errors.push('Malicious content detected in file');\n    }\n  });\n\n  // 3. File extension validation\n  const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n  if (!ALLOWED_EXTENSIONS.includes(fileExtension)) {\n    errors.push('Invalid file extension');\n  }\n\n  // 4. MIME type vs extension consistency\n  const expectedMimeTypes: Record<string, string[]> = {\n    '.jpg': ['image/jpeg'],\n    '.jpeg': ['image/jpeg'],\n    '.png': ['image/png'],\n    '.webp': ['image/webp']\n  };\n\n  const expectedTypes = expectedMimeTypes[fileExtension];\n  if (expectedTypes && !expectedTypes.includes(mimeType)) {\n    errors.push('File extension does not match MIME type');\n  }\n\n  // 5. Additional security checks using Sharp (validates image structure)\n  try {\n    const metadata = await sharp(buffer).metadata();\n\n    // Check for reasonable image dimensions\n    if (!metadata.width || !metadata.height) {\n      errors.push('Invalid image: missing dimensions');\n    } else if (metadata.width > 4096 || metadata.height > 4096) {\n      errors.push('Image dimensions too large (max 4096x4096)');\n    } else if (metadata.width < 32 || metadata.height < 32) {\n      errors.push('Image dimensions too small (min 32x32)');\n    }\n\n    // Check for excessive metadata (potential for hiding malicious content)\n    if (metadata.exif && Buffer.byteLength(metadata.exif) > 65536) { // 64KB limit\n      errors.push('Excessive EXIF data detected');\n    }\n\n    // Sanitize the image by re-encoding it (removes any embedded content)\n    const sanitizedBuffer = await sharp(buffer)\n      .jpeg({ quality: 85 }) // Always convert to JPEG for consistency\n      .toBuffer();\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      sanitizedBuffer: errors.length === 0 ? sanitizedBuffer : undefined\n    };\n\n  } catch (error) {\n    errors.push('Invalid image file structure');\n    return { isValid: false, errors };\n  }\n}\n\ninterface ProcessedImage {\n  buffer: Buffer;\n  size: number;\n  format: string;\n}\n\ninterface PhotoUploadResponse {\n  success: true;\n  profilePictureUrl: string;\n  sizes: Record<string, string>;\n  message: string;\n}\n\ninterface PhotoDeleteResponse {\n  success: true;\n  message: string;\n}\n\nasync function processImage(buffer: Buffer, size: number): Promise<ProcessedImage> {\n  const processed = await sharp(buffer)\n    .resize(size, size, {\n      fit: 'cover',\n      position: 'center'\n    })\n    .jpeg({ quality: 85 })\n    .toBuffer();\n\n  return {\n    buffer: processed,\n    size,\n    format: 'jpeg'\n  };\n}\n\nfunction generateFileName(userId: string, size: string): string {\n  const timestamp = Date.now();\n  const hash = crypto.createHash('md5').update(`${userId}-${timestamp}`).digest('hex').substring(0, 8);\n  return `profile-${userId}-${size}-${hash}.jpg`;\n}\n\n// Upload file to Vercel Blob storage\nasync function uploadToStorage(buffer: Buffer, fileName: string): Promise<string> {\n  try {\n    // Check if BLOB_READ_WRITE_TOKEN is available\n    if (!process.env.BLOB_READ_WRITE_TOKEN) {\n      log.warn('BLOB_READ_WRITE_TOKEN not found, using local storage fallback', {\n        component: 'photo_upload_api',\n        action: 'upload_to_storage',\n        metadata: { fallback: 'local_storage' }\n      });\n      // Fallback to local storage for development\n      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n      return `${baseUrl}/api/profile/photo/${fileName}`;\n    }\n\n    // Upload to Vercel Blob\n    const blob = await put(`profile-photos/${fileName}`, buffer, {\n      access: 'public',\n      contentType: 'image/jpeg', // All images are converted to JPEG\n    });\n\n    return blob.url;\n  } catch (error) {\n    log.error('Error uploading to Vercel Blob', error as Error, {\n      component: 'photo_upload_api',\n      action: 'upload_to_storage',\n      metadata: { fileName }\n    });\n    // Fallback to local storage\n    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n    return `${baseUrl}/api/profile/photo/${fileName}`;\n  }\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    // SECURITY FIX: Apply strict rate limiting for file uploads\n    const rateLimitResult = await enhancedRateLimiters.write.checkLimit(request);\n\n    if (!rateLimitResult.allowed) {\n      const error = new Error('Too many file upload attempts. Please try again later.') as any;\n      error.statusCode = 429;\n      error.headers = rateLimitResult.headers;\n      throw error;\n    }\n\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user?.email) {\n      const error = new Error('Not authenticated') as any;\n      error.statusCode = 401;\n      throw error;\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { email: session.user.email },\n    });\n\n    if (!user) {\n      const error = new Error('User not found') as any;\n      error.statusCode = 404;\n      throw error;\n    }\n\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      const error = new Error('No file provided') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    // SECURITY FIX: Enhanced file validation\n    if (!ALLOWED_TYPES.includes(file.type)) {\n      const error = new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    if (file.size > MAX_FILE_SIZE) {\n      const error = new Error(`File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`) as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    // SECURITY FIX: Validate file name\n    if (!file.name || file.name.length > 255) {\n      const error = new Error('Invalid file name') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    const buffer = Buffer.from(await file.arrayBuffer());\n\n    // SECURITY FIX: Comprehensive file content validation\n    const validation = await validateFileContent(buffer, file.type, file.name);\n\n    if (!validation.isValid) {\n      log.warn('File upload security validation failed', {\n        component: 'photo_upload_api',\n        userId: user.id,\n        metadata: { fileName: file.name, errors: validation.errors }\n      });\n\n      const error = new Error('File failed security validation: ' + validation.errors.join(', ')) as any;\n      error.statusCode = 400;\n      error.data = { securityErrors: validation.errors };\n      throw error;\n    }\n\n    // Use the sanitized buffer from validation\n    const sanitizedBuffer = validation.sanitizedBuffer!;\n\n    // SECURITY FIX: Process sanitized image for different sizes\n    const processedImages = await Promise.all(\n      Object.entries(AVATAR_SIZES).map(async ([sizeName, sizePixels]) => {\n        const processed = await processImage(sanitizedBuffer, sizePixels);\n        const fileName = generateFileName(user.id, sizeName);\n        const url = await uploadToStorage(processed.buffer, fileName);\n        return { size: sizeName, url, pixels: sizePixels };\n      })\n    );\n\n    // Use the medium size as the primary profile picture URL\n    const primaryImageUrl = processedImages.find(img => img.size === 'medium')?.url;\n\n    if (!primaryImageUrl) {\n      throw new Error('Failed to process primary image');\n    }\n\n    // Update user profile with new image URL\n    const updatedProfile = await prisma.profile.upsert({\n      where: { userId: user.id },\n      update: {\n        profilePictureUrl: primaryImageUrl,\n        lastProfileUpdate: new Date(),\n        updatedAt: new Date(),\n      },\n      create: {\n        userId: user.id,\n        profilePictureUrl: primaryImageUrl,\n        lastProfileUpdate: new Date(),\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        success: true,\n        profilePictureUrl: primaryImageUrl,\n        sizes: processedImages.reduce((acc, img) => {\n          acc[img.size] = img.url;\n          return acc;\n        }, {} as Record<string, string>),\n        message: 'Profile photo updated successfully'\n      }\n    });\n  });\n});\n\nexport const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    // SECURITY FIX: Apply rate limiting to DELETE operations\n    const rateLimitResult = await enhancedRateLimiters.write.checkLimit(request);\n\n    if (!rateLimitResult.allowed) {\n      const error = new Error('Too many delete attempts. Please try again later.') as any;\n      error.statusCode = 429;\n      error.headers = rateLimitResult.headers;\n      throw error;\n    }\n\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user?.email) {\n      const error = new Error('Not authenticated') as any;\n      error.statusCode = 401;\n      throw error;\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { email: session.user.email },\n    });\n\n    if (!user) {\n      const error = new Error('User not found') as any;\n      error.statusCode = 404;\n      throw error;\n    }\n\n    // Remove profile picture URL from database\n    await prisma.profile.upsert({\n      where: { userId: user.id },\n      update: {\n        profilePictureUrl: null,\n        lastProfileUpdate: new Date(),\n        updatedAt: new Date(),\n      },\n      create: {\n        userId: user.id,\n        profilePictureUrl: null,\n        lastProfileUpdate: new Date(),\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        success: true,\n        message: 'Profile photo removed successfully'\n      }\n    });\n  });\n});\n\n// GET endpoint for serving local photos (fallback when Vercel Blob is not available)\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  const { pathname } = new URL(request.url);\n  const fileName = pathname.split('/').pop();\n\n  if (!fileName) {\n    const error = new Error('File not found') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  // In a real implementation, you would serve from local storage or redirect to CDN\n  // For now, return a placeholder response indicating the feature needs Vercel Blob\n  const error = new Error('Photo serving requires Vercel Blob storage configuration') as any;\n  error.statusCode = 501;\n  throw error;\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,WAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAG,QAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AACA,IAAAK,2BAAA;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAM,OAAA;AAAA;AAAA,CAAAR,cAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AACA,IAAAO,QAAA;AAAA;AAAA,CAAAT,cAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AACA,IAAAQ,MAAA;AAAA;AAAA,CAAAV,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAS,QAAA;AAAA;AAAA,CAAAX,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAU,MAAA;AAAA;AAAA,CAAAZ,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAW,uBAAA;AAAA;AAAA,CAAAb,cAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA;AACA,IAAMY,aAAa;AAAA;AAAA,CAAAd,cAAA,GAAAC,CAAA,QAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAC,CAAC;AACvC,IAAMc,aAAa;AAAA;AAAA,CAAAf,cAAA,GAAAC,CAAA,QAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;AAC/D,IAAMe,kBAAkB;AAAA;AAAA,CAAAhB,cAAA,GAAAC,CAAA,QAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7D,IAAMgB,YAAY;AAAA;AAAA,CAAAjB,cAAA,GAAAC,CAAA,QAAG;EACnBiB,SAAS,EAAE,EAAE;EACbC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE;CACR;AAED;AACA,IAAMC,eAAe;AAAA;AAAA,CAAAtB,cAAA,GAAAC,CAAA,QAAG;EACtB,YAAY,EAAE,CACZ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAE;EAAA,CACrB;EACD,WAAW,EAAE,CACX,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAE;EAAA,CACnD;EACD,YAAY,EAAE,CACZ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAE;EAAA;CAE7B;AAED;AACA,IAAMsB,kBAAkB;AAAA;AAAA,CAAAvB,cAAA,GAAAC,CAAA,QAAG;AACzB;AACA,SAAS,EACT,OAAO,EACP,UAAU;AACV;AACA,IAAI;AAAE;AACN,SAAS;AAAE;AACX;AACA,YAAY;AAAE;AACd,MAAM,CAAE;AAAA,CACT;AAED;AACA,SAAeuB,mBAAmBA,CAACC,MAAc,EAAEC,QAAgB,EAAEC,QAAgB;EAAA;EAAA3B,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;iCAAG4B,OAAO;IAAA;IAAA7B,cAAA,GAAA4B,CAAA;;;;;;;;;;;;;UAKvFE,MAAM,GAAa,EAAE;UAAC;UAAA9B,cAAA,GAAAC,CAAA;UAGtB8B,UAAU,GAAGT,eAAe,CAACI,QAAwC,CAAC;UAAC;UAAA1B,cAAA,GAAAC,CAAA;UAC7E,IAAI8B,UAAU,EAAE;YAAA;YAAA/B,cAAA,GAAAgC,CAAA;YAAAhC,cAAA,GAAAC,CAAA;YACRgC,gBAAgB,GAAGF,UAAU,CAACG,IAAI,CAAC,UAAAC,SAAS;cAAA;cAAAnC,cAAA,GAAA4B,CAAA;cAAA5B,cAAA,GAAAC,CAAA;cAChD,OAAOkC,SAAS,CAACC,KAAK,CAAC,UAACC,IAAI,EAAEC,KAAK;gBAAA;gBAAAtC,cAAA,GAAA4B,CAAA;gBAAA5B,cAAA,GAAAC,CAAA;gBAAK,OAAAwB,MAAM,CAACa,KAAK,CAAC,KAAKD,IAAI;cAAtB,CAAsB,CAAC;YACjE,CAAC,CAAC;YAAC;YAAArC,cAAA,GAAAC,CAAA;YAEH,IAAI,CAACgC,gBAAgB,EAAE;cAAA;cAAAjC,cAAA,GAAAgC,CAAA;cAAAhC,cAAA,GAAAC,CAAA;cACrB6B,MAAM,CAACS,IAAI,CAAC,kDAAkD,CAAC;YACjE,CAAC;YAAA;YAAA;cAAAvC,cAAA,GAAAgC,CAAA;YAAA;UACH,CAAC;UAAA;UAAA;YAAAhC,cAAA,GAAAgC,CAAA;UAAA;UAAAhC,cAAA,GAAAC,CAAA;UAGKuC,YAAY,GAAGf,MAAM,CAACgB,QAAQ,CAAC,QAAQ,CAAC;UAAC;UAAAzC,cAAA,GAAAC,CAAA;UAC/CsB,kBAAkB,CAACmB,OAAO,CAAC,UAAAC,OAAO;YAAA;YAAA3C,cAAA,GAAA4B,CAAA;YAAA5B,cAAA,GAAAC,CAAA;YAChC,IAAI0C,OAAO,CAACC,IAAI,CAACJ,YAAY,CAAC,EAAE;cAAA;cAAAxC,cAAA,GAAAgC,CAAA;cAAAhC,cAAA,GAAAC,CAAA;cAC9B6B,MAAM,CAACS,IAAI,CAAC,oCAAoC,CAAC;YACnD,CAAC;YAAA;YAAA;cAAAvC,cAAA,GAAAgC,CAAA;YAAA;UACH,CAAC,CAAC;UAAC;UAAAhC,cAAA,GAAAC,CAAA;UAGG4C,aAAa,GAAGlB,QAAQ,CAACmB,WAAW,EAAE,CAACC,SAAS,CAACpB,QAAQ,CAACqB,WAAW,CAAC,GAAG,CAAC,CAAC;UAAC;UAAAhD,cAAA,GAAAC,CAAA;UAClF,IAAI,CAACe,kBAAkB,CAACiC,QAAQ,CAACJ,aAAa,CAAC,EAAE;YAAA;YAAA7C,cAAA,GAAAgC,CAAA;YAAAhC,cAAA,GAAAC,CAAA;YAC/C6B,MAAM,CAACS,IAAI,CAAC,wBAAwB,CAAC;UACvC,CAAC;UAAA;UAAA;YAAAvC,cAAA,GAAAgC,CAAA;UAAA;UAAAhC,cAAA,GAAAC,CAAA;UAGKiD,iBAAiB,GAA6B;YAClD,MAAM,EAAE,CAAC,YAAY,CAAC;YACtB,OAAO,EAAE,CAAC,YAAY,CAAC;YACvB,MAAM,EAAE,CAAC,WAAW,CAAC;YACrB,OAAO,EAAE,CAAC,YAAY;WACvB;UAAC;UAAAlD,cAAA,GAAAC,CAAA;UAEIkD,aAAa,GAAGD,iBAAiB,CAACL,aAAa,CAAC;UAAC;UAAA7C,cAAA,GAAAC,CAAA;UACvD;UAAI;UAAA,CAAAD,cAAA,GAAAgC,CAAA,WAAAmB,aAAa;UAAA;UAAA,CAAAnD,cAAA,GAAAgC,CAAA,WAAI,CAACmB,aAAa,CAACF,QAAQ,CAACvB,QAAQ,CAAC,GAAE;YAAA;YAAA1B,cAAA,GAAAgC,CAAA;YAAAhC,cAAA,GAAAC,CAAA;YACtD6B,MAAM,CAACS,IAAI,CAAC,yCAAyC,CAAC;UACxD,CAAC;UAAA;UAAA;YAAAvC,cAAA,GAAAgC,CAAA;UAAA;UAAAhC,cAAA,GAAAC,CAAA;;;;;;;;;UAIkB,qBAAM,IAAAO,OAAA,CAAA4C,OAAK,EAAC3B,MAAM,CAAC,CAAC4B,QAAQ,EAAE;;;;;UAAzCA,QAAQ,GAAGC,EAAA,CAAAC,IAAA,EAA8B;UAE/C;UAAA;UAAAvD,cAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,cAAA,GAAAgC,CAAA,YAACqB,QAAQ,CAACG,KAAK;UAAA;UAAA,CAAAxD,cAAA,GAAAgC,CAAA,WAAI,CAACqB,QAAQ,CAACI,MAAM,GAAE;YAAA;YAAAzD,cAAA,GAAAgC,CAAA;YAAAhC,cAAA,GAAAC,CAAA;YACvC6B,MAAM,CAACS,IAAI,CAAC,mCAAmC,CAAC;UAClD,CAAC,MAAM;YAAA;YAAAvC,cAAA,GAAAgC,CAAA;YAAAhC,cAAA,GAAAC,CAAA;YAAA;YAAI;YAAA,CAAAD,cAAA,GAAAgC,CAAA,WAAAqB,QAAQ,CAACG,KAAK,GAAG,IAAI;YAAA;YAAA,CAAAxD,cAAA,GAAAgC,CAAA,WAAIqB,QAAQ,CAACI,MAAM,GAAG,IAAI,GAAE;cAAA;cAAAzD,cAAA,GAAAgC,CAAA;cAAAhC,cAAA,GAAAC,CAAA;cAC1D6B,MAAM,CAACS,IAAI,CAAC,4CAA4C,CAAC;YAC3D,CAAC,MAAM;cAAA;cAAAvC,cAAA,GAAAgC,CAAA;cAAAhC,cAAA,GAAAC,CAAA;cAAA;cAAI;cAAA,CAAAD,cAAA,GAAAgC,CAAA,WAAAqB,QAAQ,CAACG,KAAK,GAAG,EAAE;cAAA;cAAA,CAAAxD,cAAA,GAAAgC,CAAA,WAAIqB,QAAQ,CAACI,MAAM,GAAG,EAAE,GAAE;gBAAA;gBAAAzD,cAAA,GAAAgC,CAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBACtD6B,MAAM,CAACS,IAAI,CAAC,wCAAwC,CAAC;cACvD,CAAC;cAAA;cAAA;gBAAAvC,cAAA,GAAAgC,CAAA;cAAA;YAAD;UAAA;UAEA;UAAA;UAAAhC,cAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,cAAA,GAAAgC,CAAA,WAAAqB,QAAQ,CAACK,IAAI;UAAA;UAAA,CAAA1D,cAAA,GAAAgC,CAAA,WAAI2B,MAAM,CAACC,UAAU,CAACP,QAAQ,CAACK,IAAI,CAAC,GAAG,KAAK,GAAE;YAAA;YAAA1D,cAAA,GAAAgC,CAAA;YAAAhC,cAAA,GAAAC,CAAA;YAAE;YAC/D6B,MAAM,CAACS,IAAI,CAAC,8BAA8B,CAAC;UAC7C,CAAC;UAAA;UAAA;YAAAvC,cAAA,GAAAgC,CAAA;UAAA;UAAAhC,cAAA,GAAAC,CAAA;UAGuB,qBAAM,IAAAO,OAAA,CAAA4C,OAAK,EAAC3B,MAAM,CAAC,CACxCoC,IAAI,CAAC;YAAEC,OAAO,EAAE;UAAE,CAAE,CAAC,CAAC;UAAA,CACtBC,QAAQ,EAAE;;;;;UAFPC,eAAe,GAAGV,EAAA,CAAAC,IAAA,EAEX;UAAA;UAAAvD,cAAA,GAAAC,CAAA;UAEb,sBAAO;YACLgE,OAAO,EAAEnC,MAAM,CAACoC,MAAM,KAAK,CAAC;YAC5BpC,MAAM,EAAAA,MAAA;YACNkC,eAAe,EAAElC,MAAM,CAACoC,MAAM,KAAK,CAAC;YAAA;YAAA,CAAAlE,cAAA,GAAAgC,CAAA,WAAGgC,eAAe;YAAA;YAAA,CAAAhE,cAAA,GAAAgC,CAAA,WAAGmC,SAAS;WACnE;;;;;;;;UAGDrC,MAAM,CAACS,IAAI,CAAC,8BAA8B,CAAC;UAAC;UAAAvC,cAAA,GAAAC,CAAA;UAC5C,sBAAO;YAAEgE,OAAO,EAAE,KAAK;YAAEnC,MAAM,EAAAA;UAAA,CAAE;;;;;;;;;;AAsBrC,SAAesC,YAAYA,CAAC3C,MAAc,EAAE4C,IAAY;EAAA;EAAArE,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;iCAAG4B,OAAO;IAAA;IAAA7B,cAAA,GAAA4B,CAAA;;;;;;;;;;;;;UAC9C,qBAAM,IAAApB,OAAA,CAAA4C,OAAK,EAAC3B,MAAM,CAAC,CAClC6C,MAAM,CAACD,IAAI,EAAEA,IAAI,EAAE;YAClBE,GAAG,EAAE,OAAO;YACZC,QAAQ,EAAE;WACX,CAAC,CACDX,IAAI,CAAC;YAAEC,OAAO,EAAE;UAAE,CAAE,CAAC,CACrBC,QAAQ,EAAE;;;;;UANPU,SAAS,GAAGnB,EAAA,CAAAC,IAAA,EAML;UAAA;UAAAvD,cAAA,GAAAC,CAAA;UAEb,sBAAO;YACLwB,MAAM,EAAEgD,SAAS;YACjBJ,IAAI,EAAAA,IAAA;YACJK,MAAM,EAAE;WACT;;;;;AAGH,SAASC,gBAAgBA,CAACC,MAAc,EAAEP,IAAY;EAAA;EAAArE,cAAA,GAAA4B,CAAA;EACpD,IAAMiD,SAAS;EAAA;EAAA,CAAA7E,cAAA,GAAAC,CAAA,SAAG6E,IAAI,CAACC,GAAG,EAAE;EAC5B,IAAMC,IAAI;EAAA;EAAA,CAAAhF,cAAA,GAAAC,CAAA,SAAGQ,QAAA,CAAA2C,OAAM,CAAC6B,UAAU,CAAC,KAAK,CAAC,CAACC,MAAM,CAAC,GAAAC,MAAA,CAAGP,MAAM,OAAAO,MAAA,CAAIN,SAAS,CAAE,CAAC,CAACO,MAAM,CAAC,KAAK,CAAC,CAACrC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAAC;EAAA/C,cAAA,GAAAC,CAAA;EACrG,OAAO,WAAAkF,MAAA,CAAWP,MAAM,OAAAO,MAAA,CAAId,IAAI,OAAAc,MAAA,CAAIH,IAAI,SAAM;AAChD;AAEA;AACA,SAAeK,eAAeA,CAAC5D,MAAc,EAAEE,QAAgB;EAAA;EAAA3B,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;iCAAG4B,OAAO;IAAA;IAAA7B,cAAA,GAAA4B,CAAA;;;;;;;;;;;;;;UAErE;UAAA;UAAA5B,cAAA,GAAAC,CAAA;UACA,IAAI,CAACqF,OAAO,CAACC,GAAG,CAACC,qBAAqB,EAAE;YAAA;YAAAxF,cAAA,GAAAgC,CAAA;YAAAhC,cAAA,GAAAC,CAAA;YACtCU,QAAA,CAAA8E,GAAG,CAACC,IAAI,CAAC,+DAA+D,EAAE;cACxEC,SAAS,EAAE,kBAAkB;cAC7BC,MAAM,EAAE,mBAAmB;cAC3BvC,QAAQ,EAAE;gBAAEwC,QAAQ,EAAE;cAAe;aACtC,CAAC;YAAC;YAAA7F,cAAA,GAAAC,CAAA;YAEG6F,OAAO;YAAG;YAAA,CAAA9F,cAAA,GAAAgC,CAAA,WAAAsD,OAAO,CAACC,GAAG,CAACQ,YAAY;YAAA;YAAA,CAAA/F,cAAA,GAAAgC,CAAA,WAAI,uBAAuB;YAAC;YAAAhC,cAAA,GAAAC,CAAA;YACpE,sBAAO,GAAAkF,MAAA,CAAGW,OAAO,yBAAAX,MAAA,CAAsBxD,QAAQ,CAAE;UACnD,CAAC;UAAA;UAAA;YAAA3B,cAAA,GAAAgC,CAAA;UAAA;UAAAhC,cAAA,GAAAC,CAAA;UAGY,qBAAM,IAAAS,MAAA,CAAAsF,GAAG,EAAC,kBAAAb,MAAA,CAAkBxD,QAAQ,CAAE,EAAEF,MAAM,EAAE;YAC3DwE,MAAM,EAAE,QAAQ;YAChBC,WAAW,EAAE,YAAY,CAAE;WAC5B,CAAC;;;;;UAHIC,IAAI,GAAG7C,EAAA,CAAAC,IAAA,EAGX;UAAA;UAAAvD,cAAA,GAAAC,CAAA;UAEF,sBAAOkG,IAAI,CAACC,GAAG;;;;;;;;UAEfzF,QAAA,CAAA8E,GAAG,CAACY,KAAK,CAAC,gCAAgC,EAAEC,OAAc,EAAE;YAC1DX,SAAS,EAAE,kBAAkB;YAC7BC,MAAM,EAAE,mBAAmB;YAC3BvC,QAAQ,EAAE;cAAE1B,QAAQ,EAAAA;YAAA;WACrB,CAAC;UAAC;UAAA3B,cAAA,GAAAC,CAAA;UAEG6F,OAAO;UAAG;UAAA,CAAA9F,cAAA,GAAAgC,CAAA,WAAAsD,OAAO,CAACC,GAAG,CAACQ,YAAY;UAAA;UAAA,CAAA/F,cAAA,GAAAgC,CAAA,WAAI,uBAAuB;UAAC;UAAAhC,cAAA,GAAAC,CAAA;UACpE,sBAAO,GAAAkF,MAAA,CAAGW,OAAO,yBAAAX,MAAA,CAAsBxD,QAAQ,CAAE;;;;;;;;;;AAEpD;AAAA3B,cAAA,GAAAC,CAAA;AAEYsG,OAAA,CAAAC,IAAI,GAAG,IAAAjG,2BAAA,CAAAkG,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAA1G,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EAAA,OAAA0G,SAAA;IAAA;IAAA3G,cAAA,GAAA4B,CAAA;IAAA5B,cAAA,GAAAC,CAAA;;;;;MACtE,sBAAO,IAAAW,MAAA,CAAAgG,kBAAkB,EAACF,OAAO,EAAE;QAAA;QAAA1G,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAA0G,SAAA;UAAA;UAAA3G,cAAA,GAAA4B,CAAA;;;;;;;;;;;;;;gBAET,qBAAMf,uBAAA,CAAAgG,oBAAoB,CAACC,KAAK,CAACC,UAAU,CAACL,OAAO,CAAC;;;;;gBAAtEM,eAAe,GAAGC,EAAA,CAAA1D,IAAA,EAAoD;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAE5E,IAAI,CAAC+G,eAAe,CAACE,OAAO,EAAE;kBAAA;kBAAAlH,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACtBoG,KAAK,GAAG,IAAIc,KAAK,CAAC,wDAAwD,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACzFoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvBoG,KAAK,CAACgB,OAAO,GAAGL,eAAe,CAACK,OAAO;kBAAC;kBAAArH,cAAA,GAAAC,CAAA;kBACxC,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAEe,qBAAM,IAAAE,WAAA,CAAAmH,gBAAgB,EAAClH,MAAA,CAAAmH,WAAW,CAAC;;;;;gBAA7CC,OAAO,GAAGP,EAAA,CAAA1D,IAAA,EAAmC;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAEnD,IAAI;gBAAC;gBAAA,CAAAD,cAAA,GAAAgC,CAAA,YAAAyF,EAAA;gBAAA;gBAAA,CAAAzH,cAAA,GAAAgC,CAAA,WAAAwF,OAAO;gBAAA;gBAAA,CAAAxH,cAAA,GAAAgC,CAAA,WAAPwF,OAAO;gBAAA;gBAAA,CAAAxH,cAAA,GAAAgC,CAAA;gBAAA;gBAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAPwF,OAAO,CAAEE,IAAI;gBAAA;gBAAA,CAAA1H,cAAA,GAAAgC,CAAA,WAAAyF,EAAA;gBAAA;gBAAA,CAAAzH,cAAA,GAAAgC,CAAA;gBAAA;gBAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAAyF,EAAA,CAAEE,KAAK,IAAE;kBAAA;kBAAA3H,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACnBoG,KAAK,GAAG,IAAIc,KAAK,CAAC,mBAAmB,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACpDoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAEY,qBAAMI,QAAA,CAAA+C,OAAM,CAACsE,IAAI,CAACE,UAAU,CAAC;kBACxCC,KAAK,EAAE;oBAAEF,KAAK,EAAEH,OAAO,CAACE,IAAI,CAACC;kBAAK;iBACnC,CAAC;;;;;gBAFID,IAAI,GAAGT,EAAA,CAAA1D,IAAA,EAEX;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAEF,IAAI,CAACyH,IAAI,EAAE;kBAAA;kBAAA1H,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACHoG,KAAK,GAAG,IAAIc,KAAK,CAAC,gBAAgB,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACjDoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAEgB,qBAAMyG,OAAO,CAACoB,QAAQ,EAAE;;;;;gBAAnCA,QAAQ,GAAGb,EAAA,CAAA1D,IAAA,EAAwB;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBACnC8H,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAS;gBAAC;gBAAAhI,cAAA,GAAAC,CAAA;gBAE1C,IAAI,CAAC8H,IAAI,EAAE;kBAAA;kBAAA/H,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACHoG,KAAK,GAAG,IAAIc,KAAK,CAAC,kBAAkB,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACnDoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAED;gBAAAhC,cAAA,GAAAC,CAAA;gBACA,IAAI,CAACc,aAAa,CAACkC,QAAQ,CAAC8E,IAAI,CAACE,IAAI,CAAC,EAAE;kBAAA;kBAAAjI,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBAChCoG,KAAK,GAAG,IAAIc,KAAK,CAAC,0DAA0D,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBAC3FoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAED,IAAI8H,IAAI,CAAC1D,IAAI,GAAGvD,aAAa,EAAE;kBAAA;kBAAAd,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACvBoG,KAAK,GAAG,IAAIc,KAAK,CAAC,mCAAAhC,MAAA,CAAmCrE,aAAa,IAAI,IAAI,GAAG,IAAI,CAAC,QAAK,CAAQ;kBAAC;kBAAAd,cAAA,GAAAC,CAAA;kBACtGoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAED;gBAAAhC,cAAA,GAAAC,CAAA;gBACA;gBAAI;gBAAA,CAAAD,cAAA,GAAAgC,CAAA,YAAC+F,IAAI,CAACG,IAAI;gBAAA;gBAAA,CAAAlI,cAAA,GAAAgC,CAAA,WAAI+F,IAAI,CAACG,IAAI,CAAChE,MAAM,GAAG,GAAG,GAAE;kBAAA;kBAAAlE,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBAClCoG,KAAK,GAAG,IAAIc,KAAK,CAAC,mBAAmB,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACpDoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAEckI,EAAA,IAAA7E,EAAA,GAAAK,MAAM,EAACyE,IAAI;gBAAA;gBAAApI,cAAA,GAAAC,CAAA;gBAAC,qBAAM8H,IAAI,CAACM,WAAW,EAAE;;;;;gBAA7C5G,MAAM,GAAG0G,EAAA,CAAAG,KAAA,CAAAhF,EAAA,GAAY2D,EAAA,CAAA1D,IAAA,EAAwB,EAAC;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAGjC,qBAAMuB,mBAAmB,CAACC,MAAM,EAAEsG,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACG,IAAI,CAAC;;;;;gBAApEK,UAAU,GAAGtB,EAAA,CAAA1D,IAAA,EAAuD;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAE1E,IAAI,CAACsI,UAAU,CAACtE,OAAO,EAAE;kBAAA;kBAAAjE,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACvBU,QAAA,CAAA8E,GAAG,CAACC,IAAI,CAAC,wCAAwC,EAAE;oBACjDC,SAAS,EAAE,kBAAkB;oBAC7Bf,MAAM,EAAE8C,IAAI,CAACc,EAAE;oBACfnF,QAAQ,EAAE;sBAAE1B,QAAQ,EAAEoG,IAAI,CAACG,IAAI;sBAAEpG,MAAM,EAAEyG,UAAU,CAACzG;oBAAM;mBAC3D,CAAC;kBAAC;kBAAA9B,cAAA,GAAAC,CAAA;kBAEGoG,KAAK,GAAG,IAAIc,KAAK,CAAC,mCAAmC,GAAGoB,UAAU,CAACzG,MAAM,CAAC2G,IAAI,CAAC,IAAI,CAAC,CAAQ;kBAAC;kBAAAzI,cAAA,GAAAC,CAAA;kBACnGoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvBoG,KAAK,CAACqC,IAAI,GAAG;oBAAEC,cAAc,EAAEJ,UAAU,CAACzG;kBAAM,CAAE;kBAAC;kBAAA9B,cAAA,GAAAC,CAAA;kBACnD,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAGK+D,eAAe,GAAGuE,UAAU,CAACvE,eAAgB;gBAAC;gBAAAhE,cAAA,GAAAC,CAAA;gBAG5B,qBAAM4B,OAAO,CAAC+G,GAAG,CACvCC,MAAM,CAACC,OAAO,CAAC7H,YAAY,CAAC,CAAC8H,GAAG,CAAC,UAAAzF,EAAA;kBAAA;kBAAAtD,cAAA,GAAA4B,CAAA;kBAAA5B,cAAA,GAAAC,CAAA;kBAAA,OAAA0G,SAAA,UAAArD,EAAA,qBAAO6E,EAAsB;oBAAA;oBAAAnI,cAAA,GAAA4B,CAAA;;wBAArBoH,QAAQ;sBAAA;sBAAA,CAAAhJ,cAAA,GAAAC,CAAA,SAAAkI,EAAA;sBAAEc,UAAU;sBAAA;sBAAA,CAAAjJ,cAAA,GAAAC,CAAA,SAAAkI,EAAA;oBAAA;oBAAAnI,cAAA,GAAAC,CAAA;;;;;;;;;;0BACzC,qBAAMmE,YAAY,CAACJ,eAAe,EAAEiF,UAAU,CAAC;;;;;0BAA3DxE,SAAS,GAAGgD,EAAA,CAAAlE,IAAA,EAA+C;0BAAA;0BAAAvD,cAAA,GAAAC,CAAA;0BAC3D0B,QAAQ,GAAGgD,gBAAgB,CAAC+C,IAAI,CAACc,EAAE,EAAEQ,QAAQ,CAAC;0BAAC;0BAAAhJ,cAAA,GAAAC,CAAA;0BACzC,qBAAMoF,eAAe,CAACZ,SAAS,CAAChD,MAAM,EAAEE,QAAQ,CAAC;;;;;0BAAvDyE,GAAG,GAAGqB,EAAA,CAAAlE,IAAA,EAAiD;0BAAA;0BAAAvD,cAAA,GAAAC,CAAA;0BAC7D,sBAAO;4BAAEoE,IAAI,EAAE2E,QAAQ;4BAAE5C,GAAG,EAAAA,GAAA;4BAAE8C,MAAM,EAAED;0BAAU,CAAE;;;;iBACnD,CAAC,CACH;;;;;gBAPKE,eAAe,GAAGlC,EAAA,CAAA1D,IAAA,EAOvB;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAGKmJ,eAAe;gBAAG;gBAAA,CAAApJ,cAAA,GAAAgC,CAAA,YAAAqH,EAAA,GAAAF,eAAe,CAACG,IAAI,CAAC,UAAAC,GAAG;kBAAA;kBAAAvJ,cAAA,GAAA4B,CAAA;kBAAA5B,cAAA,GAAAC,CAAA;kBAAI,OAAAsJ,GAAG,CAAClF,IAAI,KAAK,QAAQ;gBAArB,CAAqB,CAAC;gBAAA;gBAAA,CAAArE,cAAA,GAAAgC,CAAA,WAAAqH,EAAA;gBAAA;gBAAA,CAAArJ,cAAA,GAAAgC,CAAA;gBAAA;gBAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAAqH,EAAA,CAAEjD,GAAG;gBAAC;gBAAApG,cAAA,GAAAC,CAAA;gBAEhF,IAAI,CAACmJ,eAAe,EAAE;kBAAA;kBAAApJ,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACpB,MAAM,IAAIkH,KAAK,CAAC,iCAAiC,CAAC;gBACpD,CAAC;gBAAA;gBAAA;kBAAAnH,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAGsB,qBAAMI,QAAA,CAAA+C,OAAM,CAACoG,OAAO,CAACC,MAAM,CAAC;kBACjD5B,KAAK,EAAE;oBAAEjD,MAAM,EAAE8C,IAAI,CAACc;kBAAE,CAAE;kBAC1BtD,MAAM,EAAE;oBACNwE,iBAAiB,EAAEN,eAAe;oBAClCO,iBAAiB,EAAE,IAAI7E,IAAI,EAAE;oBAC7B8E,SAAS,EAAE,IAAI9E,IAAI;mBACpB;kBACD+E,MAAM,EAAE;oBACNjF,MAAM,EAAE8C,IAAI,CAACc,EAAE;oBACfkB,iBAAiB,EAAEN,eAAe;oBAClCO,iBAAiB,EAAE,IAAI7E,IAAI;;iBAE9B,CAAC;;;;;gBAZIgF,cAAc,GAAG7C,EAAA,CAAA1D,IAAA,EAYrB;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAEF,sBAAOF,QAAA,CAAAgK,YAAY,CAACC,IAAI,CAAC;kBACvBC,OAAO,EAAE,IAAI;kBACbvB,IAAI,EAAE;oBACJuB,OAAO,EAAE,IAAI;oBACbP,iBAAiB,EAAEN,eAAe;oBAClCc,KAAK,EAAEf,eAAe,CAACgB,MAAM,CAAC,UAACC,GAAG,EAAEb,GAAG;sBAAA;sBAAAvJ,cAAA,GAAA4B,CAAA;sBAAA5B,cAAA,GAAAC,CAAA;sBACrCmK,GAAG,CAACb,GAAG,CAAClF,IAAI,CAAC,GAAGkF,GAAG,CAACnD,GAAG;sBAAC;sBAAApG,cAAA,GAAAC,CAAA;sBACxB,OAAOmK,GAAG;oBACZ,CAAC,EAAE,EAA4B,CAAC;oBAChCC,OAAO,EAAE;;iBAEZ,CAAC;;;;OACH,CAAC;;;CACH,CAAC;AAAC;AAAArK,cAAA,GAAAC,CAAA;AAEUsG,OAAA,CAAA+D,MAAM,GAAG,IAAA/J,2BAAA,CAAAkG,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAA1G,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EAAA,OAAA0G,SAAA;IAAA;IAAA3G,cAAA,GAAA4B,CAAA;IAAA5B,cAAA,GAAAC,CAAA;;;;;MACxE,sBAAO,IAAAW,MAAA,CAAAgG,kBAAkB,EAACF,OAAO,EAAE;QAAA;QAAA1G,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAA0G,SAAA;UAAA;UAAA3G,cAAA,GAAA4B,CAAA;;;;;;;;;;;;;;gBAET,qBAAMf,uBAAA,CAAAgG,oBAAoB,CAACC,KAAK,CAACC,UAAU,CAACL,OAAO,CAAC;;;;;gBAAtEM,eAAe,GAAGmB,EAAA,CAAA5E,IAAA,EAAoD;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAE5E,IAAI,CAAC+G,eAAe,CAACE,OAAO,EAAE;kBAAA;kBAAAlH,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACtBoG,KAAK,GAAG,IAAIc,KAAK,CAAC,mDAAmD,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACpFoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvBoG,KAAK,CAACgB,OAAO,GAAGL,eAAe,CAACK,OAAO;kBAAC;kBAAArH,cAAA,GAAAC,CAAA;kBACxC,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAEe,qBAAM,IAAAE,WAAA,CAAAmH,gBAAgB,EAAClH,MAAA,CAAAmH,WAAW,CAAC;;;;;gBAA7CC,OAAO,GAAGW,EAAA,CAAA5E,IAAA,EAAmC;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAEnD,IAAI;gBAAC;gBAAA,CAAAD,cAAA,GAAAgC,CAAA,YAAAsB,EAAA;gBAAA;gBAAA,CAAAtD,cAAA,GAAAgC,CAAA,WAAAwF,OAAO;gBAAA;gBAAA,CAAAxH,cAAA,GAAAgC,CAAA,WAAPwF,OAAO;gBAAA;gBAAA,CAAAxH,cAAA,GAAAgC,CAAA;gBAAA;gBAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAPwF,OAAO,CAAEE,IAAI;gBAAA;gBAAA,CAAA1H,cAAA,GAAAgC,CAAA,WAAAsB,EAAA;gBAAA;gBAAA,CAAAtD,cAAA,GAAAgC,CAAA;gBAAA;gBAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAAsB,EAAA,CAAEqE,KAAK,IAAE;kBAAA;kBAAA3H,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACnBoG,KAAK,GAAG,IAAIc,KAAK,CAAC,mBAAmB,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACpDoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAAAhC,cAAA,GAAAC,CAAA;gBAEY,qBAAMI,QAAA,CAAA+C,OAAM,CAACsE,IAAI,CAACE,UAAU,CAAC;kBACxCC,KAAK,EAAE;oBAAEF,KAAK,EAAEH,OAAO,CAACE,IAAI,CAACC;kBAAK;iBACnC,CAAC;;;;;gBAFID,IAAI,GAAGS,EAAA,CAAA5E,IAAA,EAEX;gBAAA;gBAAAvD,cAAA,GAAAC,CAAA;gBAEF,IAAI,CAACyH,IAAI,EAAE;kBAAA;kBAAA1H,cAAA,GAAAgC,CAAA;kBAAAhC,cAAA,GAAAC,CAAA;kBACHoG,KAAK,GAAG,IAAIc,KAAK,CAAC,gBAAgB,CAAQ;kBAAC;kBAAAnH,cAAA,GAAAC,CAAA;kBACjDoG,KAAK,CAACe,UAAU,GAAG,GAAG;kBAAC;kBAAApH,cAAA,GAAAC,CAAA;kBACvB,MAAMoG,KAAK;gBACb,CAAC;gBAAA;gBAAA;kBAAArG,cAAA,GAAAgC,CAAA;gBAAA;gBAED;gBAAAhC,cAAA,GAAAC,CAAA;gBACA,qBAAMI,QAAA,CAAA+C,OAAM,CAACoG,OAAO,CAACC,MAAM,CAAC;kBAC1B5B,KAAK,EAAE;oBAAEjD,MAAM,EAAE8C,IAAI,CAACc;kBAAE,CAAE;kBAC1BtD,MAAM,EAAE;oBACNwE,iBAAiB,EAAE,IAAI;oBACvBC,iBAAiB,EAAE,IAAI7E,IAAI,EAAE;oBAC7B8E,SAAS,EAAE,IAAI9E,IAAI;mBACpB;kBACD+E,MAAM,EAAE;oBACNjF,MAAM,EAAE8C,IAAI,CAACc,EAAE;oBACfkB,iBAAiB,EAAE,IAAI;oBACvBC,iBAAiB,EAAE,IAAI7E,IAAI;;iBAE9B,CAAC;;;;;gBAbF;gBACAqD,EAAA,CAAA5E,IAAA,EAYE;gBAAC;gBAAAvD,cAAA,GAAAC,CAAA;gBAEH,sBAAOF,QAAA,CAAAgK,YAAY,CAACC,IAAI,CAAC;kBACvBC,OAAO,EAAE,IAAI;kBACbvB,IAAI,EAAE;oBACJuB,OAAO,EAAE,IAAI;oBACbI,OAAO,EAAE;;iBAEZ,CAAC;;;;OACH,CAAC;;;CACH,CAAC;AAEF;AAAA;AAAArK,cAAA,GAAAC,CAAA;AACasG,OAAA,CAAAgE,GAAG,GAAG,IAAAhK,2BAAA,CAAAkG,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAA1G,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EAAA,OAAA0G,SAAA;IAAA;IAAA3G,cAAA,GAAA4B,CAAA;;;;;;;;MAC7D4I,QAAQ,GAAK,IAAIC,GAAG,CAAC/D,OAAO,CAACN,GAAG,CAAC,CAAAoE,QAAzB;MAA0B;MAAAxK,cAAA,GAAAC,CAAA;MACpC0B,QAAQ,GAAG6I,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;MAAC;MAAA3K,cAAA,GAAAC,CAAA;MAE3C,IAAI,CAAC0B,QAAQ,EAAE;QAAA;QAAA3B,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAC,CAAA;QACP2K,OAAA,GAAQ,IAAIzD,KAAK,CAAC,gBAAgB,CAAQ;QAAC;QAAAnH,cAAA,GAAAC,CAAA;QACjD2K,OAAK,CAACxD,UAAU,GAAG,GAAG;QAAC;QAAApH,cAAA,GAAAC,CAAA;QACvB,MAAM2K,OAAK;MACb,CAAC;MAAA;MAAA;QAAA5K,cAAA,GAAAgC,CAAA;MAAA;MAAAhC,cAAA,GAAAC,CAAA;MAIKoG,KAAK,GAAG,IAAIc,KAAK,CAAC,0DAA0D,CAAQ;MAAC;MAAAnH,cAAA,GAAAC,CAAA;MAC3FoG,KAAK,CAACe,UAAU,GAAG,GAAG;MAAC;MAAApH,cAAA,GAAAC,CAAA;MACvB,MAAMoG,KAAK;;;CACZ,CAAC", "ignoreList": []}