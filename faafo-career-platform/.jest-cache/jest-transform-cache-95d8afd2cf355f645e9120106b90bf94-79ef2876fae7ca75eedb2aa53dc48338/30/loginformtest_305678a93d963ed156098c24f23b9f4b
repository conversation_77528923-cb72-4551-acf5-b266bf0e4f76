a8cf70b43bf25cb4ca89bd963418310d
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock dependencies
jest.mock('next/navigation', function () { return ({
    useRouter: jest.fn(),
}); });
jest.mock('next-auth/react', function () { return ({
    signIn: jest.fn(),
    getSession: jest.fn(),
}); });
// Mock hooks with proper return values
jest.mock('@/hooks/useCSRF');
jest.mock('@/hooks/useFormValidation', function () { return ({
    useValidatedForm: jest.fn(function (initialData, rules, onSubmit) {
        if (initialData === void 0) { initialData = { email: '', password: '' }; }
        return ({
            data: initialData || { email: '', password: '' },
            updateField: jest.fn(),
            handleSubmit: jest.fn(function (e) {
                e.preventDefault();
                if (onSubmit) {
                    onSubmit(initialData || { email: '', password: '' }, initialData || { email: '', password: '' });
                }
            }),
            isSubmitting: false,
            validation: {
                errors: {},
                isValid: true,
            },
            validationActions: {},
        });
    }),
}); });
jest.mock('@/hooks/useFeedback', function () { return ({
    useFeedback: jest.fn(function () { return ({
        messages: [],
        showError: jest.fn(),
        showSuccess: jest.fn(),
        dismissFeedback: jest.fn(),
    }); }),
    createRetryAction: jest.fn(),
}); });
/**
 * Comprehensive LoginForm Component Tests
 * Tests authentication UI, form validation, error handling, and user interactions
 */
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
var navigation_1 = require("next/navigation");
var react_3 = require("next-auth/react");
var LoginForm_1 = __importDefault(require("@/components/LoginForm"));
// Import the mocked hooks to ensure proper typing
var useCSRF_1 = require("@/hooks/useCSRF");
var useFormValidation_1 = require("@/hooks/useFormValidation");
var useFeedback_1 = require("@/hooks/useFeedback");
var mockUseCSRF = useCSRF_1.useCSRF;
var mockUseValidatedForm = useFormValidation_1.useValidatedForm;
var mockUseFeedback = useFeedback_1.useFeedback;
// Mock fetch for resend verification
global.fetch = jest.fn();
var mockPush = jest.fn();
var mockSignIn = react_3.signIn;
var mockGetSession = react_3.getSession;
var mockUseRouter = navigation_1.useRouter;
describe('LoginForm', function () {
    afterEach(function () {
        (0, react_2.cleanup)();
    });
    beforeEach(function () {
        jest.clearAllMocks();
        // Setup useCSRF mock
        mockUseCSRF.mockReturnValue({
            csrfToken: 'test-csrf-token',
            isLoading: false,
            error: null,
            getHeaders: jest.fn(function (additionalHeaders) {
                if (additionalHeaders === void 0) { additionalHeaders = {}; }
                return (__assign({ 'Content-Type': 'application/json', 'X-CSRF-Token': 'test-csrf-token' }, additionalHeaders));
            }),
        });
        // Setup useValidatedForm mock
        mockUseValidatedForm.mockReturnValue({
            data: { email: '', password: '' },
            updateField: jest.fn(),
            handleSubmit: jest.fn(function (e) {
                e.preventDefault();
            }),
            isSubmitting: false,
            validation: {
                errors: {},
                isValid: true,
            },
            validationActions: {},
        });
        // Setup useFeedback mock
        mockUseFeedback.mockReturnValue({
            messages: [],
            showError: jest.fn(),
            showSuccess: jest.fn(),
            showInfo: jest.fn(),
            showWarning: jest.fn(),
            dismissFeedback: jest.fn(),
            clearAll: jest.fn(),
        });
        mockUseRouter.mockReturnValue({
            push: mockPush,
            back: jest.fn(),
            forward: jest.fn(),
            refresh: jest.fn(),
            replace: jest.fn(),
            prefetch: jest.fn(),
        });
        mockGetSession.mockResolvedValue({ user: { id: '1', email: '<EMAIL>' } });
        // Setup fetch mock for CSRF token endpoint
        fetch.mockImplementation(function (url) {
            if (typeof url === 'string' && url.includes('/api/csrf-token')) {
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    json: function () { return Promise.resolve({ csrfToken: 'test-csrf-token' }); },
                });
            }
            return Promise.resolve({
                ok: true,
                status: 200,
                json: function () { return Promise.resolve({}); },
            });
        });
    });
    it('should render login form correctly', function () {
        (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
        expect(react_2.screen.getByLabelText(/email/i)).toBeInTheDocument();
        expect(react_2.screen.getByLabelText(/password/i)).toBeInTheDocument();
        expect(react_2.screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
        expect(react_2.screen.getByText(/forgot your password/i)).toBeInTheDocument();
    });
    it('should handle successful login', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mockSignIn.mockResolvedValue({
                        error: null,
                        status: 200,
                        ok: true,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    // Debug: Let's see what's actually rendered
                    react_2.screen.debug();
                    emailInput = react_2.screen.getByLabelText(/email/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(mockSignIn).toHaveBeenCalledWith('credentials', {
                                redirect: false,
                                email: '<EMAIL>',
                                password: 'password123',
                            });
                        })];
                case 1:
                    _a.sent();
                    expect(mockPush).toHaveBeenCalledWith('/');
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle login error', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mockSignIn.mockResolvedValue({
                        error: 'Invalid credentials',
                        status: 401,
                        ok: false,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/invalid credentials/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    expect(mockPush).not.toHaveBeenCalled();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle email verification error and shows resend option', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mockSignIn.mockResolvedValue({
                        error: 'Please verify your email address before signing in. Check your inbox for a verification link.',
                        status: 401,
                        ok: false,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/please verify your email address/i)).toBeInTheDocument();
                            expect(react_2.screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle resend verification email successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton, resendResponse, resendButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // First, trigger email verification error
                    mockSignIn.mockResolvedValue({
                        error: 'Please verify your email address before signing in. Check your inbox for a verification link.',
                        status: 401,
                        ok: false,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    resendResponse = {
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'Verification email sent successfully.',
                                    })];
                            });
                        }); },
                    };
                    fetch.mockResolvedValue(resendResponse);
                    resendButton = react_2.screen.getByRole('button', { name: /resend verification email/i });
                    react_2.fireEvent.click(resendButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/verification email sent! please check your inbox/i)).toBeInTheDocument();
                        })];
                case 2:
                    _a.sent();
                    expect(fetch).toHaveBeenCalledWith('/api/auth/resend-verification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                        }),
                    });
                    // Resend button should be hidden after successful send
                    expect(react_2.screen.queryByRole('button', { name: /resend verification email/i })).not.toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should handle resend verification email error', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton, resendResponse, resendButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // First, trigger email verification error
                    mockSignIn.mockResolvedValue({
                        error: 'Please verify your email address before signing in. Check your inbox for a verification link.',
                        status: 401,
                        ok: false,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    resendResponse = {
                        ok: false,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        error: 'A verification email was recently sent. Please wait 5 minutes before requesting another.',
                                    })];
                            });
                        }); },
                    };
                    fetch.mockResolvedValue(resendResponse);
                    resendButton = react_2.screen.getByRole('button', { name: /resend verification email/i });
                    react_2.fireEvent.click(resendButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/a verification email was recently sent/i)).toBeInTheDocument();
                        })];
                case 2:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should show loading state during resend verification', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton, resolvePromise, promise, resendButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // First, trigger email verification error
                    mockSignIn.mockResolvedValue({
                        error: 'Please verify your email address before signing in. Check your inbox for a verification link.',
                        status: 401,
                        ok: false,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    promise = new Promise(function (resolve) {
                        resolvePromise = resolve;
                    });
                    fetch.mockReturnValue(promise);
                    resendButton = react_2.screen.getByRole('button', { name: /resend verification email/i });
                    react_2.fireEvent.click(resendButton);
                    // Should show loading state
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByRole('button', { name: /sending.../i })).toBeInTheDocument();
                        })];
                case 2:
                    // Should show loading state
                    _a.sent();
                    // Button should be disabled during loading
                    expect(react_2.screen.getByRole('button', { name: /sending.../i })).toBeDisabled();
                    // Resolve the promise
                    resolvePromise({
                        ok: true,
                        json: function () { return __awaiter(void 0, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                return [2 /*return*/, ({
                                        message: 'Verification email sent successfully.',
                                    })];
                            });
                        }); },
                    });
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/verification email sent! please check your inbox/i)).toBeInTheDocument();
                        })];
                case 3:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    it('should clear error when starting new login attempt', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mockSignIn.mockResolvedValue({
                        error: 'Invalid credentials',
                        status: 401,
                        ok: false,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    // First login attempt with error
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByText(/invalid credentials/i)).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    // Second login attempt should clear error
                    mockSignIn.mockResolvedValue({
                        error: null,
                        status: 200,
                        ok: true,
                        url: null,
                    });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'correctpassword' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.queryByText(/invalid credentials/i)).not.toBeInTheDocument();
                        })];
                case 2:
                    _a.sent();
                    expect(mockPush).toHaveBeenCalledWith('/');
                    return [2 /*return*/];
            }
        });
    }); });
    it('should require email and password fields', function () {
        (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
        var emailInput = react_2.screen.getByLabelText(/email/i);
        var passwordInput = react_2.screen.getByLabelText(/password/i);
        expect(emailInput).toBeRequired();
        expect(passwordInput).toBeRequired();
    });
    it('should have correct input types', function () {
        (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
        var emailInput = react_2.screen.getByLabelText(/email/i);
        var passwordInput = react_2.screen.getByLabelText(/password/i);
        expect(emailInput).toHaveAttribute('type', 'email');
        expect(passwordInput).toHaveAttribute('type', 'password');
    });
    it.skip('handles network error during resend verification', function () { return __awaiter(void 0, void 0, void 0, function () {
        var emailInput, passwordInput, submitButton, resendButton;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // TODO: Fix this test - the error handling might not be working as expected
                    // First, trigger email verification error
                    mockSignIn.mockResolvedValue({
                        error: 'Please verify your email address before signing in. Check your inbox for a verification link.',
                        status: 401,
                        ok: false,
                        url: null,
                    });
                    (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                    emailInput = react_2.screen.getByLabelText(/email address/i);
                    passwordInput = react_2.screen.getByLabelText(/password/i);
                    submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                    react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                    react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                    react_2.fireEvent.click(submitButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            expect(react_2.screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
                        })];
                case 1:
                    _a.sent();
                    // Mock network error for the resend verification endpoint
                    fetch.mockRejectedValueOnce(new Error('Network error'));
                    resendButton = react_2.screen.getByRole('button', { name: /resend verification email/i });
                    react_2.fireEvent.click(resendButton);
                    return [4 /*yield*/, (0, react_2.waitFor)(function () {
                            // Check if the error message appears or if the button text changes
                            var errorElement = react_2.screen.queryByText('An unexpected error occurred.');
                            var buttonElement = react_2.screen.queryByText('Resend verification email');
                            expect(errorElement || buttonElement).toBeInTheDocument();
                        }, { timeout: 3000 })];
                case 2:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    describe('Security and Edge Cases', function () {
        it('should prevent XSS in error messages', function () { return __awaiter(void 0, void 0, void 0, function () {
            var xssPayload, emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        xssPayload = '<script>alert("xss")</script>';
                        mockSignIn.mockResolvedValue({
                            error: xssPayload,
                            status: 401,
                            ok: false,
                            url: null,
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                        react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                        react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                        react_2.fireEvent.click(submitButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                // Error should be displayed as text, not executed as HTML
                                expect(react_2.screen.getByText(xssPayload)).toBeInTheDocument();
                                expect(document.querySelector('script')).toBeNull();
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle extremely long email addresses', function () { return __awaiter(void 0, void 0, void 0, function () {
            var longEmail, emailInput;
            return __generator(this, function (_a) {
                longEmail = 'a'.repeat(250) + '@example.com';
                (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                emailInput = react_2.screen.getByLabelText(/email/i);
                react_2.fireEvent.change(emailInput, { target: { value: longEmail } });
                expect(emailInput).toHaveValue(longEmail);
                return [2 /*return*/];
            });
        }); });
        it('should handle special characters in credentials', function () { return __awaiter(void 0, void 0, void 0, function () {
            var specialEmail, specialPassword, emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        specialEmail = '<EMAIL>';
                        specialPassword = 'P@ssw0rd!#$%';
                        mockSignIn.mockResolvedValue({
                            error: null,
                            status: 200,
                            ok: true,
                            url: null,
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                        react_2.fireEvent.change(emailInput, { target: { value: specialEmail } });
                        react_2.fireEvent.change(passwordInput, { target: { value: specialPassword } });
                        react_2.fireEvent.click(submitButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockSignIn).toHaveBeenCalledWith('credentials', {
                                    redirect: false,
                                    email: specialEmail,
                                    password: specialPassword,
                                });
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle rapid form submissions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSignIn.mockImplementation(function () { return new Promise(function (resolve) {
                            return setTimeout(function () { return resolve({ error: null, status: 200, ok: true, url: null }); }, 100);
                        }); });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                        react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                        react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                        // Rapid clicks
                        react_2.fireEvent.click(submitButton);
                        react_2.fireEvent.click(submitButton);
                        react_2.fireEvent.click(submitButton);
                        // Should only call signIn once due to form submission protection
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockSignIn).toHaveBeenCalledTimes(1);
                            })];
                    case 1:
                        // Should only call signIn once due to form submission protection
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should maintain form state during loading', function () { return __awaiter(void 0, void 0, void 0, function () {
            var emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                mockSignIn.mockImplementation(function () { return new Promise(function (resolve) {
                    return setTimeout(function () { return resolve({ error: null, status: 200, ok: true, url: null }); }, 100);
                }); });
                (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                emailInput = react_2.screen.getByLabelText(/email/i);
                passwordInput = react_2.screen.getByLabelText(/password/i);
                submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                react_2.fireEvent.change(passwordInput, { target: { value: 'password123' } });
                react_2.fireEvent.click(submitButton);
                // Form should maintain values during submission
                expect(emailInput).toHaveValue('<EMAIL>');
                expect(passwordInput).toHaveValue('password123');
                expect(submitButton).toBeDisabled();
                return [2 /*return*/];
            });
        }); });
    });
    describe('Accessibility', function () {
        it('should have proper ARIA labels and roles', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
            var emailInput = react_2.screen.getByLabelText(/email/i);
            var passwordInput = react_2.screen.getByLabelText(/password/i);
            var submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
            expect(emailInput).toHaveAttribute('aria-required', 'true');
            expect(passwordInput).toHaveAttribute('aria-required', 'true');
            expect(submitButton).toHaveAttribute('type', 'submit');
        });
        it('should announce errors to screen readers', function () { return __awaiter(void 0, void 0, void 0, function () {
            var emailInput, passwordInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSignIn.mockResolvedValue({
                            error: 'Invalid credentials',
                            status: 401,
                            ok: false,
                            url: null,
                        });
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
                        emailInput = react_2.screen.getByLabelText(/email/i);
                        passwordInput = react_2.screen.getByLabelText(/password/i);
                        submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
                        react_2.fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
                        react_2.fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
                        react_2.fireEvent.click(submitButton);
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                var errorElement = react_2.screen.getByText(/invalid credentials/i);
                                expect(errorElement).toHaveAttribute('role', 'alert');
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should support keyboard navigation', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(LoginForm_1.default, {}));
            var emailInput = react_2.screen.getByLabelText(/email/i);
            var passwordInput = react_2.screen.getByLabelText(/password/i);
            var submitButton = react_2.screen.getByRole('button', { name: /sign in/i });
            // Tab order should be email -> password -> submit
            emailInput.focus();
            expect(document.activeElement).toBe(emailInput);
            react_2.fireEvent.keyDown(emailInput, { key: 'Tab' });
            expect(document.activeElement).toBe(passwordInput);
            react_2.fireEvent.keyDown(passwordInput, { key: 'Tab' });
            expect(document.activeElement).toBe(submitButton);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************