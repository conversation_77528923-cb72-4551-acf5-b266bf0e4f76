{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useFeedback.ts", "mappings": ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBb,kCA2FC;AA0BD,kDAwBC;AAGD,8CAyDC;AAzND,+BAA8C;AAgB9C,SAAgB,WAAW;IACnB,IAAA,KAA0B,IAAA,gBAAQ,EAAoB,EAAE,CAAC,EAAxD,QAAQ,QAAA,EAAE,WAAW,QAAmC,CAAC;IAEhE,IAAM,UAAU,GAAG,IAAA,mBAAW,EAAC;QAC7B,OAAO,mBAAY,IAAI,CAAC,GAAG,EAAE,cAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC;IAC7E,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,UAAC,OAAoC;;QACpE,IAAM,EAAE,GAAG,UAAU,EAAE,CAAC;QACxB,IAAM,UAAU,yBACX,OAAO,KACV,EAAE,IAAA,EACF,WAAW,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,IAAI,EACxC,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,EACrF,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,GAC/G,CAAC;QAEF,WAAW,CAAC,UAAA,IAAI,IAAI,uCAAI,IAAI,UAAE,UAAU,WAApB,CAAqB,CAAC,CAAC;QAC3C,OAAO,EAAE,CAAC;IACZ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB,IAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,UAAC,OAAe,EAAE,OAAsC;QAAtC,wBAAA,EAAA,YAAsC;QACtF,OAAO,YAAY,YACjB,IAAI,EAAE,SAAS,EACf,OAAO,SAAA,IACJ,OAAO,EACV,CAAC;IACL,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IAEnB,IAAM,SAAS,GAAG,IAAA,mBAAW,EAAC,UAAC,OAAe,EAAE,OAAsC;QAAtC,wBAAA,EAAA,YAAsC;QACpF,OAAO,YAAY,YACjB,IAAI,EAAE,OAAO,EACb,OAAO,SAAA,EACP,QAAQ,EAAE,KAAK,IACZ,OAAO,EACV,CAAC;IACL,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IAEnB,IAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,UAAC,OAAe,EAAE,OAAsC;QAAtC,wBAAA,EAAA,YAAsC;QACtF,OAAO,YAAY,YACjB,IAAI,EAAE,SAAS,EACf,OAAO,SAAA,EACP,QAAQ,EAAE,KAAK,IACZ,OAAO,EACV,CAAC;IACL,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IAEnB,IAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,UAAC,OAAe,EAAE,OAAsC;QAAtC,wBAAA,EAAA,YAAsC;QACnF,OAAO,YAAY,YACjB,IAAI,EAAE,MAAM,EACZ,OAAO,SAAA,IACJ,OAAO,EACV,CAAC;IACL,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IAEnB,IAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,UAAC,OAAe,EAAE,OAAsC;QAAtC,wBAAA,EAAA,YAAsC;QACtF,OAAO,YAAY,YACjB,IAAI,EAAE,SAAS,EACf,OAAO,SAAA,EACP,WAAW,EAAE,KAAK,EAClB,QAAQ,EAAE,KAAK,IACZ,OAAO,EACV,CAAC;IACL,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IAEnB,IAAM,eAAe,GAAG,IAAA,mBAAW,EAAC,UAAC,EAAU;QAC7C,WAAW,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,EAAE,KAAK,EAAE,EAAb,CAAa,CAAC,EAAjC,CAAiC,CAAC,CAAC;IACzD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC;QAC3B,WAAW,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,UAAC,EAAU,EAAE,OAAiC;QAC/E,WAAW,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG;YAC9B,OAAA,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,uBAAM,GAAG,GAAK,OAAO,EAAG,CAAC,CAAC,GAAG;QAA5C,CAA4C,CAC7C,EAFmB,CAEnB,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,QAAQ,UAAA;QACR,YAAY,cAAA;QACZ,WAAW,aAAA;QACX,SAAS,WAAA;QACT,WAAW,aAAA;QACX,QAAQ,UAAA;QACR,WAAW,aAAA;QACX,eAAe,iBAAA;QACf,QAAQ,UAAA;QACR,cAAc,gBAAA;KACf,CAAC;AACJ,CAAC;AAED,iDAAiD;AAC1C,IAAM,iBAAiB,GAAG,UAAC,OAAmB,EAAE,OAAe;IAAf,wBAAA,EAAA,eAAe;IAAqB,OAAA,CAAC;QAC1F,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,OAAO;QAChB,OAAO,EAAE,SAAS;QAClB,OAAO,SAAA;KACR,CAAC;AALyF,CAKzF,CAAC;AALU,QAAA,iBAAiB,qBAK3B;AAEI,IAAM,mBAAmB,GAAG,UAAC,SAAqB,IAAqB,OAAA,CAAC;IAC7E,KAAK,EAAE,cAAc;IACrB,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;CACnB,CAAC,EAJ4E,CAI5E,CAAC;AAJU,QAAA,mBAAmB,uBAI7B;AAEI,IAAM,0BAA0B,GAAG,cAAsB,OAAA,CAAC;IAC/D,KAAK,EAAE,iBAAiB;IACxB,OAAO,EAAE;QACP,iFAAiF;QACjF,MAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IACD,OAAO,EAAE,OAAO;CACjB,CAAC,EAP8D,CAO9D,CAAC;AAPU,QAAA,0BAA0B,8BAOpC;AAEH,oEAAoE;AACpE,SAAgB,mBAAmB,CAAC,KAAU;;IAC5C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED,IAAI,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,0CAAE,IAAI,EAAE,CAAC;QAC1B,IAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;QACjC,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,IAAI,mBAAmB;YAC1D,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;SACvF,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,CAAC;QACnB,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;SACpE,CAAC;IACJ,CAAC;IAED,OAAO;QACL,OAAO,EAAE,8BAA8B;QACvC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACjC,CAAC;AACJ,CAAC;AAED,mDAAmD;AACnD,SAAgB,iBAAiB;IAAjC,iBAyDC;IAxDC,IAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;IACzB,IAAA,KAA4B,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAA1C,SAAS,QAAA,EAAE,YAAY,QAAmB,CAAC;IAElD,IAAM,OAAO,GAAG,IAAA,mBAAW,EAAC;;;;;4EASzB,OAAO,YARR,SAA2B,EAC3B,OAMM;;;YANN,wBAAA,EAAA,YAMM;;;;wBAEN,YAAY,CAAC,IAAI,CAAC,CAAC;wBAGnB,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;4BAC3B,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;wBAC3D,CAAC;;;;wBAGgB,qBAAM,SAAS,EAAE,EAAA;;wBAA1B,MAAM,GAAG,SAAiB;wBAEhC,IAAI,SAAS,EAAE,CAAC;4BACd,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;wBACtC,CAAC;wBAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;4BAC3B,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;wBAC/C,CAAC;wBAED,MAAA,OAAO,CAAC,SAAS,wDAAG,MAAM,CAAC,CAAC;wBAC5B,sBAAO,MAAM,EAAC;;;wBAEd,IAAI,SAAS,EAAE,CAAC;4BACd,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;wBACtC,CAAC;wBAEK,YAAY,GAAG,mBAAmB,CAAC,OAAK,CAAC,CAAC;wBAChD,QAAQ,CAAC,SAAS,CAChB,OAAO,CAAC,YAAY,IAAI,YAAY,CAAC,OAAO,EAC5C,EAAE,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,CAClC,CAAC;wBAEF,MAAA,OAAO,CAAC,OAAO,wDAAG,OAAK,CAAC,CAAC;wBACzB,sBAAO,IAAI,EAAC;;wBAEZ,YAAY,CAAC,KAAK,CAAC,CAAC;;;;;;KAEvB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,kBACE,OAAO,SAAA,EACP,SAAS,WAAA,IACN,QAAQ,EACX;AACJ,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/hooks/useFeedback.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport { FeedbackMessage, FeedbackType, FeedbackAction } from '@/components/ui/error-feedback';\n\nexport interface UseFeedbackReturn {\n  messages: FeedbackMessage[];\n  showFeedback: (message: Omit<FeedbackMessage, 'id'>) => string;\n  showSuccess: (message: string, options?: Partial<FeedbackMessage>) => string;\n  showError: (message: string, options?: Partial<FeedbackMessage>) => string;\n  showWarning: (message: string, options?: Partial<FeedbackMessage>) => string;\n  showInfo: (message: string, options?: Partial<FeedbackMessage>) => string;\n  showLoading: (message: string, options?: Partial<FeedbackMessage>) => string;\n  dismissFeedback: (id: string) => void;\n  clearAll: () => void;\n  updateFeedback: (id: string, updates: Partial<FeedbackMessage>) => void;\n}\n\nexport function useFeedback(): UseFeedbackReturn {\n  const [messages, setMessages] = useState<FeedbackMessage[]>([]);\n\n  const generateId = useCallback(() => {\n    return `feedback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n  }, []);\n\n  const showFeedback = useCallback((message: Omit<FeedbackMessage, 'id'>) => {\n    const id = generateId();\n    const newMessage: FeedbackMessage = {\n      ...message,\n      id,\n      dismissible: message.dismissible ?? true,\n      autoHide: message.autoHide ?? (message.type === 'success' || message.type === 'info'),\n      duration: message.duration ?? (message.type === 'success' ? 5000 : message.type === 'info' ? 7000 : undefined)\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    return id;\n  }, [generateId]);\n\n  const showSuccess = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {\n    return showFeedback({\n      type: 'success',\n      message,\n      ...options\n    });\n  }, [showFeedback]);\n\n  const showError = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {\n    return showFeedback({\n      type: 'error',\n      message,\n      autoHide: false, // Errors should not auto-hide by default\n      ...options\n    });\n  }, [showFeedback]);\n\n  const showWarning = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {\n    return showFeedback({\n      type: 'warning',\n      message,\n      autoHide: false, // Warnings should not auto-hide by default\n      ...options\n    });\n  }, [showFeedback]);\n\n  const showInfo = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {\n    return showFeedback({\n      type: 'info',\n      message,\n      ...options\n    });\n  }, [showFeedback]);\n\n  const showLoading = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {\n    return showFeedback({\n      type: 'loading',\n      message,\n      dismissible: false,\n      autoHide: false,\n      ...options\n    });\n  }, [showFeedback]);\n\n  const dismissFeedback = useCallback((id: string) => {\n    setMessages(prev => prev.filter(msg => msg.id !== id));\n  }, []);\n\n  const clearAll = useCallback(() => {\n    setMessages([]);\n  }, []);\n\n  const updateFeedback = useCallback((id: string, updates: Partial<FeedbackMessage>) => {\n    setMessages(prev => prev.map(msg => \n      msg.id === id ? { ...msg, ...updates } : msg\n    ));\n  }, []);\n\n  return {\n    messages,\n    showFeedback,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n    showLoading,\n    dismissFeedback,\n    clearAll,\n    updateFeedback\n  };\n}\n\n// Utility functions for common feedback patterns\nexport const createRetryAction = (onRetry: () => void, loading = false): FeedbackAction => ({\n  label: 'Retry',\n  onClick: onRetry,\n  variant: 'default',\n  loading\n});\n\nexport const createRefreshAction = (onRefresh: () => void): FeedbackAction => ({\n  label: 'Refresh Page',\n  onClick: onRefresh,\n  variant: 'outline'\n});\n\nexport const createContactSupportAction = (): FeedbackAction => ({\n  label: 'Contact Support',\n  onClick: () => {\n    // You can customize this to open a support modal, redirect to support page, etc.\n    window.open('mailto:<EMAIL>?subject=Application Error', '_blank');\n  },\n  variant: 'ghost'\n});\n\n// Helper function to extract error details from various error types\nexport function extractErrorDetails(error: any): { message: string; details?: string[] } {\n  if (typeof error === 'string') {\n    return { message: error };\n  }\n\n  if (error?.response?.data) {\n    const data = error.response.data;\n    return {\n      message: data.error || data.message || 'An error occurred',\n      details: data.details || (data.errors ? Object.values(data.errors).flat() : undefined)\n    };\n  }\n\n  if (error?.message) {\n    return {\n      message: error.message,\n      details: error.details || (error.stack ? [error.stack] : undefined)\n    };\n  }\n\n  return {\n    message: 'An unexpected error occurred',\n    details: [JSON.stringify(error)]\n  };\n}\n\n// Hook for handling async operations with feedback\nexport function useAsyncOperation() {\n  const feedback = useFeedback();\n  const [isLoading, setIsLoading] = useState(false);\n\n  const execute = useCallback(async <T>(\n    operation: () => Promise<T>,\n    options: {\n      loadingMessage?: string;\n      successMessage?: string;\n      errorMessage?: string;\n      onSuccess?: (result: T) => void;\n      onError?: (error: any) => void;\n    } = {}\n  ): Promise<T | null> => {\n    setIsLoading(true);\n    \n    let loadingId: string | undefined;\n    if (options.loadingMessage) {\n      loadingId = feedback.showLoading(options.loadingMessage);\n    }\n\n    try {\n      const result = await operation();\n      \n      if (loadingId) {\n        feedback.dismissFeedback(loadingId);\n      }\n      \n      if (options.successMessage) {\n        feedback.showSuccess(options.successMessage);\n      }\n      \n      options.onSuccess?.(result);\n      return result;\n    } catch (error) {\n      if (loadingId) {\n        feedback.dismissFeedback(loadingId);\n      }\n      \n      const errorDetails = extractErrorDetails(error);\n      feedback.showError(\n        options.errorMessage || errorDetails.message,\n        { details: errorDetails.details }\n      );\n      \n      options.onError?.(error);\n      return null;\n    } finally {\n      setIsLoading(false);\n    }\n  }, [feedback]);\n\n  return {\n    execute,\n    isLoading,\n    ...feedback\n  };\n}\n"], "version": 3}