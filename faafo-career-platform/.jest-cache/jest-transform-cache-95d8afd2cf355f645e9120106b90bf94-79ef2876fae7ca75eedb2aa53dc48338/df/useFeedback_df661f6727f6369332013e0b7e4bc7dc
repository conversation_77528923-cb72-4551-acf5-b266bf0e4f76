8ab0d948e09ff3b8cb1c808db721a82f
"use strict";
'use client';
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createContactSupportAction = exports.createRefreshAction = exports.createRetryAction = void 0;
exports.useFeedback = useFeedback;
exports.extractErrorDetails = extractErrorDetails;
exports.useAsyncOperation = useAsyncOperation;
var react_1 = require("react");
function useFeedback() {
    var _a = (0, react_1.useState)([]), messages = _a[0], setMessages = _a[1];
    var generateId = (0, react_1.useCallback)(function () {
        return "feedback-".concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9));
    }, []);
    var showFeedback = (0, react_1.useCallback)(function (message) {
        var _a, _b, _c;
        var id = generateId();
        var newMessage = __assign(__assign({}, message), { id: id, dismissible: (_a = message.dismissible) !== null && _a !== void 0 ? _a : true, autoHide: (_b = message.autoHide) !== null && _b !== void 0 ? _b : (message.type === 'success' || message.type === 'info'), duration: (_c = message.duration) !== null && _c !== void 0 ? _c : (message.type === 'success' ? 5000 : message.type === 'info' ? 7000 : undefined) });
        setMessages(function (prev) { return __spreadArray(__spreadArray([], prev, true), [newMessage], false); });
        return id;
    }, [generateId]);
    var showSuccess = (0, react_1.useCallback)(function (message, options) {
        if (options === void 0) { options = {}; }
        return showFeedback(__assign({ type: 'success', message: message }, options));
    }, [showFeedback]);
    var showError = (0, react_1.useCallback)(function (message, options) {
        if (options === void 0) { options = {}; }
        return showFeedback(__assign({ type: 'error', message: message, autoHide: false }, options));
    }, [showFeedback]);
    var showWarning = (0, react_1.useCallback)(function (message, options) {
        if (options === void 0) { options = {}; }
        return showFeedback(__assign({ type: 'warning', message: message, autoHide: false }, options));
    }, [showFeedback]);
    var showInfo = (0, react_1.useCallback)(function (message, options) {
        if (options === void 0) { options = {}; }
        return showFeedback(__assign({ type: 'info', message: message }, options));
    }, [showFeedback]);
    var showLoading = (0, react_1.useCallback)(function (message, options) {
        if (options === void 0) { options = {}; }
        return showFeedback(__assign({ type: 'loading', message: message, dismissible: false, autoHide: false }, options));
    }, [showFeedback]);
    var dismissFeedback = (0, react_1.useCallback)(function (id) {
        setMessages(function (prev) { return prev.filter(function (msg) { return msg.id !== id; }); });
    }, []);
    var clearAll = (0, react_1.useCallback)(function () {
        setMessages([]);
    }, []);
    var updateFeedback = (0, react_1.useCallback)(function (id, updates) {
        setMessages(function (prev) { return prev.map(function (msg) {
            return msg.id === id ? __assign(__assign({}, msg), updates) : msg;
        }); });
    }, []);
    return {
        messages: messages,
        showFeedback: showFeedback,
        showSuccess: showSuccess,
        showError: showError,
        showWarning: showWarning,
        showInfo: showInfo,
        showLoading: showLoading,
        dismissFeedback: dismissFeedback,
        clearAll: clearAll,
        updateFeedback: updateFeedback
    };
}
// Utility functions for common feedback patterns
var createRetryAction = function (onRetry, loading) {
    if (loading === void 0) { loading = false; }
    return ({
        label: 'Retry',
        onClick: onRetry,
        variant: 'default',
        loading: loading
    });
};
exports.createRetryAction = createRetryAction;
var createRefreshAction = function (onRefresh) { return ({
    label: 'Refresh Page',
    onClick: onRefresh,
    variant: 'outline'
}); };
exports.createRefreshAction = createRefreshAction;
var createContactSupportAction = function () { return ({
    label: 'Contact Support',
    onClick: function () {
        // You can customize this to open a support modal, redirect to support page, etc.
        window.open('mailto:<EMAIL>?subject=Application Error', '_blank');
    },
    variant: 'ghost'
}); };
exports.createContactSupportAction = createContactSupportAction;
// Helper function to extract error details from various error types
function extractErrorDetails(error) {
    var _a;
    if (typeof error === 'string') {
        return { message: error };
    }
    if ((_a = error === null || error === void 0 ? void 0 : error.response) === null || _a === void 0 ? void 0 : _a.data) {
        var data = error.response.data;
        return {
            message: data.error || data.message || 'An error occurred',
            details: data.details || (data.errors ? Object.values(data.errors).flat() : undefined)
        };
    }
    if (error === null || error === void 0 ? void 0 : error.message) {
        return {
            message: error.message,
            details: error.details || (error.stack ? [error.stack] : undefined)
        };
    }
    return {
        message: 'An unexpected error occurred',
        details: [JSON.stringify(error)]
    };
}
// Hook for handling async operations with feedback
function useAsyncOperation() {
    var _this = this;
    var feedback = useFeedback();
    var _a = (0, react_1.useState)(false), isLoading = _a[0], setIsLoading = _a[1];
    var execute = (0, react_1.useCallback)(function (operation_1) {
        var args_1 = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args_1[_i - 1] = arguments[_i];
        }
        return __awaiter(_this, __spreadArray([operation_1], args_1, true), Promise, function (operation, options) {
            var loadingId, result, error_1, errorDetails;
            var _a, _b;
            if (options === void 0) { options = {}; }
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        setIsLoading(true);
                        if (options.loadingMessage) {
                            loadingId = feedback.showLoading(options.loadingMessage);
                        }
                        _c.label = 1;
                    case 1:
                        _c.trys.push([1, 3, 4, 5]);
                        return [4 /*yield*/, operation()];
                    case 2:
                        result = _c.sent();
                        if (loadingId) {
                            feedback.dismissFeedback(loadingId);
                        }
                        if (options.successMessage) {
                            feedback.showSuccess(options.successMessage);
                        }
                        (_a = options.onSuccess) === null || _a === void 0 ? void 0 : _a.call(options, result);
                        return [2 /*return*/, result];
                    case 3:
                        error_1 = _c.sent();
                        if (loadingId) {
                            feedback.dismissFeedback(loadingId);
                        }
                        errorDetails = extractErrorDetails(error_1);
                        feedback.showError(options.errorMessage || errorDetails.message, { details: errorDetails.details });
                        (_b = options.onError) === null || _b === void 0 ? void 0 : _b.call(options, error_1);
                        return [2 /*return*/, null];
                    case 4:
                        setIsLoading(false);
                        return [7 /*endfinally*/];
                    case 5: return [2 /*return*/];
                }
            });
        });
    }, [feedback]);
    return __assign({ execute: execute, isLoading: isLoading }, feedback);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9ob29rcy91c2VGZWVkYmFjay50cyIsIm1hcHBpbmdzIjoiO0FBQUEsWUFBWSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWtCYixrQ0EyRkM7QUEwQkQsa0RBd0JDO0FBR0QsOENBeURDO0FBek5ELCtCQUE4QztBQWdCOUMsU0FBZ0IsV0FBVztJQUNuQixJQUFBLEtBQTBCLElBQUEsZ0JBQVEsRUFBb0IsRUFBRSxDQUFDLEVBQXhELFFBQVEsUUFBQSxFQUFFLFdBQVcsUUFBbUMsQ0FBQztJQUVoRSxJQUFNLFVBQVUsR0FBRyxJQUFBLG1CQUFXLEVBQUM7UUFDN0IsT0FBTyxtQkFBWSxJQUFJLENBQUMsR0FBRyxFQUFFLGNBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFFLENBQUM7SUFDN0UsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRVAsSUFBTSxZQUFZLEdBQUcsSUFBQSxtQkFBVyxFQUFDLFVBQUMsT0FBb0M7O1FBQ3BFLElBQU0sRUFBRSxHQUFHLFVBQVUsRUFBRSxDQUFDO1FBQ3hCLElBQU0sVUFBVSx5QkFDWCxPQUFPLEtBQ1YsRUFBRSxJQUFBLEVBQ0YsV0FBVyxFQUFFLE1BQUEsT0FBTyxDQUFDLFdBQVcsbUNBQUksSUFBSSxFQUN4QyxRQUFRLEVBQUUsTUFBQSxPQUFPLENBQUMsUUFBUSxtQ0FBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEtBQUssU0FBUyxJQUFJLE9BQU8sQ0FBQyxJQUFJLEtBQUssTUFBTSxDQUFDLEVBQ3JGLFFBQVEsRUFBRSxNQUFBLE9BQU8sQ0FBQyxRQUFRLG1DQUFJLENBQUMsT0FBTyxDQUFDLElBQUksS0FBSyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksS0FBSyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLEdBQy9HLENBQUM7UUFFRixXQUFXLENBQUMsVUFBQSxJQUFJLElBQUksdUNBQUksSUFBSSxVQUFFLFVBQVUsV0FBcEIsQ0FBcUIsQ0FBQyxDQUFDO1FBQzNDLE9BQU8sRUFBRSxDQUFDO0lBQ1osQ0FBQyxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztJQUVqQixJQUFNLFdBQVcsR0FBRyxJQUFBLG1CQUFXLEVBQUMsVUFBQyxPQUFlLEVBQUUsT0FBc0M7UUFBdEMsd0JBQUEsRUFBQSxZQUFzQztRQUN0RixPQUFPLFlBQVksWUFDakIsSUFBSSxFQUFFLFNBQVMsRUFDZixPQUFPLFNBQUEsSUFDSixPQUFPLEVBQ1YsQ0FBQztJQUNMLENBQUMsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUM7SUFFbkIsSUFBTSxTQUFTLEdBQUcsSUFBQSxtQkFBVyxFQUFDLFVBQUMsT0FBZSxFQUFFLE9BQXNDO1FBQXRDLHdCQUFBLEVBQUEsWUFBc0M7UUFDcEYsT0FBTyxZQUFZLFlBQ2pCLElBQUksRUFBRSxPQUFPLEVBQ2IsT0FBTyxTQUFBLEVBQ1AsUUFBUSxFQUFFLEtBQUssSUFDWixPQUFPLEVBQ1YsQ0FBQztJQUNMLENBQUMsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUM7SUFFbkIsSUFBTSxXQUFXLEdBQUcsSUFBQSxtQkFBVyxFQUFDLFVBQUMsT0FBZSxFQUFFLE9BQXNDO1FBQXRDLHdCQUFBLEVBQUEsWUFBc0M7UUFDdEYsT0FBTyxZQUFZLFlBQ2pCLElBQUksRUFBRSxTQUFTLEVBQ2YsT0FBTyxTQUFBLEVBQ1AsUUFBUSxFQUFFLEtBQUssSUFDWixPQUFPLEVBQ1YsQ0FBQztJQUNMLENBQUMsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUM7SUFFbkIsSUFBTSxRQUFRLEdBQUcsSUFBQSxtQkFBVyxFQUFDLFVBQUMsT0FBZSxFQUFFLE9BQXNDO1FBQXRDLHdCQUFBLEVBQUEsWUFBc0M7UUFDbkYsT0FBTyxZQUFZLFlBQ2pCLElBQUksRUFBRSxNQUFNLEVBQ1osT0FBTyxTQUFBLElBQ0osT0FBTyxFQUNWLENBQUM7SUFDTCxDQUFDLEVBQUUsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDO0lBRW5CLElBQU0sV0FBVyxHQUFHLElBQUEsbUJBQVcsRUFBQyxVQUFDLE9BQWUsRUFBRSxPQUFzQztRQUF0Qyx3QkFBQSxFQUFBLFlBQXNDO1FBQ3RGLE9BQU8sWUFBWSxZQUNqQixJQUFJLEVBQUUsU0FBUyxFQUNmLE9BQU8sU0FBQSxFQUNQLFdBQVcsRUFBRSxLQUFLLEVBQ2xCLFFBQVEsRUFBRSxLQUFLLElBQ1osT0FBTyxFQUNWLENBQUM7SUFDTCxDQUFDLEVBQUUsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDO0lBRW5CLElBQU0sZUFBZSxHQUFHLElBQUEsbUJBQVcsRUFBQyxVQUFDLEVBQVU7UUFDN0MsV0FBVyxDQUFDLFVBQUEsSUFBSSxJQUFJLE9BQUEsSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFBLEdBQUcsSUFBSSxPQUFBLEdBQUcsQ0FBQyxFQUFFLEtBQUssRUFBRSxFQUFiLENBQWEsQ0FBQyxFQUFqQyxDQUFpQyxDQUFDLENBQUM7SUFDekQsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRVAsSUFBTSxRQUFRLEdBQUcsSUFBQSxtQkFBVyxFQUFDO1FBQzNCLFdBQVcsQ0FBQyxFQUFFLENBQUMsQ0FBQztJQUNsQixDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFFUCxJQUFNLGNBQWMsR0FBRyxJQUFBLG1CQUFXLEVBQUMsVUFBQyxFQUFVLEVBQUUsT0FBaUM7UUFDL0UsV0FBVyxDQUFDLFVBQUEsSUFBSSxJQUFJLE9BQUEsSUFBSSxDQUFDLEdBQUcsQ0FBQyxVQUFBLEdBQUc7WUFDOUIsT0FBQSxHQUFHLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDLHVCQUFNLEdBQUcsR0FBSyxPQUFPLEVBQUcsQ0FBQyxDQUFDLEdBQUc7UUFBNUMsQ0FBNEMsQ0FDN0MsRUFGbUIsQ0FFbkIsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRVAsT0FBTztRQUNMLFFBQVEsVUFBQTtRQUNSLFlBQVksY0FBQTtRQUNaLFdBQVcsYUFBQTtRQUNYLFNBQVMsV0FBQTtRQUNULFdBQVcsYUFBQTtRQUNYLFFBQVEsVUFBQTtRQUNSLFdBQVcsYUFBQTtRQUNYLGVBQWUsaUJBQUE7UUFDZixRQUFRLFVBQUE7UUFDUixjQUFjLGdCQUFBO0tBQ2YsQ0FBQztBQUNKLENBQUM7QUFFRCxpREFBaUQ7QUFDMUMsSUFBTSxpQkFBaUIsR0FBRyxVQUFDLE9BQW1CLEVBQUUsT0FBZTtJQUFmLHdCQUFBLEVBQUEsZUFBZTtJQUFxQixPQUFBLENBQUM7UUFDMUYsS0FBSyxFQUFFLE9BQU87UUFDZCxPQUFPLEVBQUUsT0FBTztRQUNoQixPQUFPLEVBQUUsU0FBUztRQUNsQixPQUFPLFNBQUE7S0FDUixDQUFDO0FBTHlGLENBS3pGLENBQUM7QUFMVSxRQUFBLGlCQUFpQixxQkFLM0I7QUFFSSxJQUFNLG1CQUFtQixHQUFHLFVBQUMsU0FBcUIsSUFBcUIsT0FBQSxDQUFDO0lBQzdFLEtBQUssRUFBRSxjQUFjO0lBQ3JCLE9BQU8sRUFBRSxTQUFTO0lBQ2xCLE9BQU8sRUFBRSxTQUFTO0NBQ25CLENBQUMsRUFKNEUsQ0FJNUUsQ0FBQztBQUpVLFFBQUEsbUJBQW1CLHVCQUk3QjtBQUVJLElBQU0sMEJBQTBCLEdBQUcsY0FBc0IsT0FBQSxDQUFDO0lBQy9ELEtBQUssRUFBRSxpQkFBaUI7SUFDeEIsT0FBTyxFQUFFO1FBQ1AsaUZBQWlGO1FBQ2pGLE1BQU0sQ0FBQyxJQUFJLENBQUMsb0RBQW9ELEVBQUUsUUFBUSxDQUFDLENBQUM7SUFDOUUsQ0FBQztJQUNELE9BQU8sRUFBRSxPQUFPO0NBQ2pCLENBQUMsRUFQOEQsQ0FPOUQsQ0FBQztBQVBVLFFBQUEsMEJBQTBCLDhCQU9wQztBQUVILG9FQUFvRTtBQUNwRSxTQUFnQixtQkFBbUIsQ0FBQyxLQUFVOztJQUM1QyxJQUFJLE9BQU8sS0FBSyxLQUFLLFFBQVEsRUFBRSxDQUFDO1FBQzlCLE9BQU8sRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLENBQUM7SUFDNUIsQ0FBQztJQUVELElBQUksTUFBQSxLQUFLLGFBQUwsS0FBSyx1QkFBTCxLQUFLLENBQUUsUUFBUSwwQ0FBRSxJQUFJLEVBQUUsQ0FBQztRQUMxQixJQUFNLElBQUksR0FBRyxLQUFLLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQztRQUNqQyxPQUFPO1lBQ0wsT0FBTyxFQUFFLElBQUksQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLE9BQU8sSUFBSSxtQkFBbUI7WUFDMUQsT0FBTyxFQUFFLElBQUksQ0FBQyxPQUFPLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDO1NBQ3ZGLENBQUM7SUFDSixDQUFDO0lBRUQsSUFBSSxLQUFLLGFBQUwsS0FBSyx1QkFBTCxLQUFLLENBQUUsT0FBTyxFQUFFLENBQUM7UUFDbkIsT0FBTztZQUNMLE9BQU8sRUFBRSxLQUFLLENBQUMsT0FBTztZQUN0QixPQUFPLEVBQUUsS0FBSyxDQUFDLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUM7U0FDcEUsQ0FBQztJQUNKLENBQUM7SUFFRCxPQUFPO1FBQ0wsT0FBTyxFQUFFLDhCQUE4QjtRQUN2QyxPQUFPLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO0tBQ2pDLENBQUM7QUFDSixDQUFDO0FBRUQsbURBQW1EO0FBQ25ELFNBQWdCLGlCQUFpQjtJQUFqQyxpQkF5REM7SUF4REMsSUFBTSxRQUFRLEdBQUcsV0FBVyxFQUFFLENBQUM7SUFDekIsSUFBQSxLQUE0QixJQUFBLGdCQUFRLEVBQUMsS0FBSyxDQUFDLEVBQTFDLFNBQVMsUUFBQSxFQUFFLFlBQVksUUFBbUIsQ0FBQztJQUVsRCxJQUFNLE9BQU8sR0FBRyxJQUFBLG1CQUFXLEVBQUM7Ozs7OzRFQVN6QixPQUFPLFlBUlIsU0FBMkIsRUFDM0IsT0FNTTs7O1lBTk4sd0JBQUEsRUFBQSxZQU1NOzs7O3dCQUVOLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQzt3QkFHbkIsSUFBSSxPQUFPLENBQUMsY0FBYyxFQUFFLENBQUM7NEJBQzNCLFNBQVMsR0FBRyxRQUFRLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxjQUFjLENBQUMsQ0FBQzt3QkFDM0QsQ0FBQzs7Ozt3QkFHZ0IscUJBQU0sU0FBUyxFQUFFLEVBQUE7O3dCQUExQixNQUFNLEdBQUcsU0FBaUI7d0JBRWhDLElBQUksU0FBUyxFQUFFLENBQUM7NEJBQ2QsUUFBUSxDQUFDLGVBQWUsQ0FBQyxTQUFTLENBQUMsQ0FBQzt3QkFDdEMsQ0FBQzt3QkFFRCxJQUFJLE9BQU8sQ0FBQyxjQUFjLEVBQUUsQ0FBQzs0QkFDM0IsUUFBUSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsY0FBYyxDQUFDLENBQUM7d0JBQy9DLENBQUM7d0JBRUQsTUFBQSxPQUFPLENBQUMsU0FBUyx3REFBRyxNQUFNLENBQUMsQ0FBQzt3QkFDNUIsc0JBQU8sTUFBTSxFQUFDOzs7d0JBRWQsSUFBSSxTQUFTLEVBQUUsQ0FBQzs0QkFDZCxRQUFRLENBQUMsZUFBZSxDQUFDLFNBQVMsQ0FBQyxDQUFDO3dCQUN0QyxDQUFDO3dCQUVLLFlBQVksR0FBRyxtQkFBbUIsQ0FBQyxPQUFLLENBQUMsQ0FBQzt3QkFDaEQsUUFBUSxDQUFDLFNBQVMsQ0FDaEIsT0FBTyxDQUFDLFlBQVksSUFBSSxZQUFZLENBQUMsT0FBTyxFQUM1QyxFQUFFLE9BQU8sRUFBRSxZQUFZLENBQUMsT0FBTyxFQUFFLENBQ2xDLENBQUM7d0JBRUYsTUFBQSxPQUFPLENBQUMsT0FBTyx3REFBRyxPQUFLLENBQUMsQ0FBQzt3QkFDekIsc0JBQU8sSUFBSSxFQUFDOzt3QkFFWixZQUFZLENBQUMsS0FBSyxDQUFDLENBQUM7Ozs7OztLQUV2QixFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztJQUVmLGtCQUNFLE9BQU8sU0FBQSxFQUNQLFNBQVMsV0FBQSxJQUNOLFFBQVEsRUFDWDtBQUNKLENBQUMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9ob29rcy91c2VGZWVkYmFjay50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEZlZWRiYWNrTWVzc2FnZSwgRmVlZGJhY2tUeXBlLCBGZWVkYmFja0FjdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9lcnJvci1mZWVkYmFjayc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlRmVlZGJhY2tSZXR1cm4ge1xuICBtZXNzYWdlczogRmVlZGJhY2tNZXNzYWdlW107XG4gIHNob3dGZWVkYmFjazogKG1lc3NhZ2U6IE9taXQ8RmVlZGJhY2tNZXNzYWdlLCAnaWQnPikgPT4gc3RyaW5nO1xuICBzaG93U3VjY2VzczogKG1lc3NhZ2U6IHN0cmluZywgb3B0aW9ucz86IFBhcnRpYWw8RmVlZGJhY2tNZXNzYWdlPikgPT4gc3RyaW5nO1xuICBzaG93RXJyb3I6IChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBQYXJ0aWFsPEZlZWRiYWNrTWVzc2FnZT4pID0+IHN0cmluZztcbiAgc2hvd1dhcm5pbmc6IChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBQYXJ0aWFsPEZlZWRiYWNrTWVzc2FnZT4pID0+IHN0cmluZztcbiAgc2hvd0luZm86IChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBQYXJ0aWFsPEZlZWRiYWNrTWVzc2FnZT4pID0+IHN0cmluZztcbiAgc2hvd0xvYWRpbmc6IChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBQYXJ0aWFsPEZlZWRiYWNrTWVzc2FnZT4pID0+IHN0cmluZztcbiAgZGlzbWlzc0ZlZWRiYWNrOiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgY2xlYXJBbGw6ICgpID0+IHZvaWQ7XG4gIHVwZGF0ZUZlZWRiYWNrOiAoaWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxGZWVkYmFja01lc3NhZ2U+KSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlRmVlZGJhY2soKTogVXNlRmVlZGJhY2tSZXR1cm4ge1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPEZlZWRiYWNrTWVzc2FnZVtdPihbXSk7XG5cbiAgY29uc3QgZ2VuZXJhdGVJZCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICByZXR1cm4gYGZlZWRiYWNrLSR7RGF0ZS5ub3coKX0tJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YDtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHNob3dGZWVkYmFjayA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBPbWl0PEZlZWRiYWNrTWVzc2FnZSwgJ2lkJz4pID0+IHtcbiAgICBjb25zdCBpZCA9IGdlbmVyYXRlSWQoKTtcbiAgICBjb25zdCBuZXdNZXNzYWdlOiBGZWVkYmFja01lc3NhZ2UgPSB7XG4gICAgICAuLi5tZXNzYWdlLFxuICAgICAgaWQsXG4gICAgICBkaXNtaXNzaWJsZTogbWVzc2FnZS5kaXNtaXNzaWJsZSA/PyB0cnVlLFxuICAgICAgYXV0b0hpZGU6IG1lc3NhZ2UuYXV0b0hpZGUgPz8gKG1lc3NhZ2UudHlwZSA9PT0gJ3N1Y2Nlc3MnIHx8IG1lc3NhZ2UudHlwZSA9PT0gJ2luZm8nKSxcbiAgICAgIGR1cmF0aW9uOiBtZXNzYWdlLmR1cmF0aW9uID8/IChtZXNzYWdlLnR5cGUgPT09ICdzdWNjZXNzJyA/IDUwMDAgOiBtZXNzYWdlLnR5cGUgPT09ICdpbmZvJyA/IDcwMDAgOiB1bmRlZmluZWQpXG4gICAgfTtcblxuICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIG5ld01lc3NhZ2VdKTtcbiAgICByZXR1cm4gaWQ7XG4gIH0sIFtnZW5lcmF0ZUlkXSk7XG5cbiAgY29uc3Qgc2hvd1N1Y2Nlc3MgPSB1c2VDYWxsYmFjaygobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zOiBQYXJ0aWFsPEZlZWRiYWNrTWVzc2FnZT4gPSB7fSkgPT4ge1xuICAgIHJldHVybiBzaG93RmVlZGJhY2soe1xuICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIC4uLm9wdGlvbnNcbiAgICB9KTtcbiAgfSwgW3Nob3dGZWVkYmFja10pO1xuXG4gIGNvbnN0IHNob3dFcnJvciA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM6IFBhcnRpYWw8RmVlZGJhY2tNZXNzYWdlPiA9IHt9KSA9PiB7XG4gICAgcmV0dXJuIHNob3dGZWVkYmFjayh7XG4gICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIGF1dG9IaWRlOiBmYWxzZSwgLy8gRXJyb3JzIHNob3VsZCBub3QgYXV0by1oaWRlIGJ5IGRlZmF1bHRcbiAgICAgIC4uLm9wdGlvbnNcbiAgICB9KTtcbiAgfSwgW3Nob3dGZWVkYmFja10pO1xuXG4gIGNvbnN0IHNob3dXYXJuaW5nID0gdXNlQ2FsbGJhY2soKG1lc3NhZ2U6IHN0cmluZywgb3B0aW9uczogUGFydGlhbDxGZWVkYmFja01lc3NhZ2U+ID0ge30pID0+IHtcbiAgICByZXR1cm4gc2hvd0ZlZWRiYWNrKHtcbiAgICAgIHR5cGU6ICd3YXJuaW5nJyxcbiAgICAgIG1lc3NhZ2UsXG4gICAgICBhdXRvSGlkZTogZmFsc2UsIC8vIFdhcm5pbmdzIHNob3VsZCBub3QgYXV0by1oaWRlIGJ5IGRlZmF1bHRcbiAgICAgIC4uLm9wdGlvbnNcbiAgICB9KTtcbiAgfSwgW3Nob3dGZWVkYmFja10pO1xuXG4gIGNvbnN0IHNob3dJbmZvID0gdXNlQ2FsbGJhY2soKG1lc3NhZ2U6IHN0cmluZywgb3B0aW9uczogUGFydGlhbDxGZWVkYmFja01lc3NhZ2U+ID0ge30pID0+IHtcbiAgICByZXR1cm4gc2hvd0ZlZWRiYWNrKHtcbiAgICAgIHR5cGU6ICdpbmZvJyxcbiAgICAgIG1lc3NhZ2UsXG4gICAgICAuLi5vcHRpb25zXG4gICAgfSk7XG4gIH0sIFtzaG93RmVlZGJhY2tdKTtcblxuICBjb25zdCBzaG93TG9hZGluZyA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM6IFBhcnRpYWw8RmVlZGJhY2tNZXNzYWdlPiA9IHt9KSA9PiB7XG4gICAgcmV0dXJuIHNob3dGZWVkYmFjayh7XG4gICAgICB0eXBlOiAnbG9hZGluZycsXG4gICAgICBtZXNzYWdlLFxuICAgICAgZGlzbWlzc2libGU6IGZhbHNlLFxuICAgICAgYXV0b0hpZGU6IGZhbHNlLFxuICAgICAgLi4ub3B0aW9uc1xuICAgIH0pO1xuICB9LCBbc2hvd0ZlZWRiYWNrXSk7XG5cbiAgY29uc3QgZGlzbWlzc0ZlZWRiYWNrID0gdXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYuZmlsdGVyKG1zZyA9PiBtc2cuaWQgIT09IGlkKSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBjbGVhckFsbCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRNZXNzYWdlcyhbXSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB1cGRhdGVGZWVkYmFjayA9IHVzZUNhbGxiYWNrKChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPEZlZWRiYWNrTWVzc2FnZT4pID0+IHtcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PiBcbiAgICAgIG1zZy5pZCA9PT0gaWQgPyB7IC4uLm1zZywgLi4udXBkYXRlcyB9IDogbXNnXG4gICAgKSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4ge1xuICAgIG1lc3NhZ2VzLFxuICAgIHNob3dGZWVkYmFjayxcbiAgICBzaG93U3VjY2VzcyxcbiAgICBzaG93RXJyb3IsXG4gICAgc2hvd1dhcm5pbmcsXG4gICAgc2hvd0luZm8sXG4gICAgc2hvd0xvYWRpbmcsXG4gICAgZGlzbWlzc0ZlZWRiYWNrLFxuICAgIGNsZWFyQWxsLFxuICAgIHVwZGF0ZUZlZWRiYWNrXG4gIH07XG59XG5cbi8vIFV0aWxpdHkgZnVuY3Rpb25zIGZvciBjb21tb24gZmVlZGJhY2sgcGF0dGVybnNcbmV4cG9ydCBjb25zdCBjcmVhdGVSZXRyeUFjdGlvbiA9IChvblJldHJ5OiAoKSA9PiB2b2lkLCBsb2FkaW5nID0gZmFsc2UpOiBGZWVkYmFja0FjdGlvbiA9PiAoe1xuICBsYWJlbDogJ1JldHJ5JyxcbiAgb25DbGljazogb25SZXRyeSxcbiAgdmFyaWFudDogJ2RlZmF1bHQnLFxuICBsb2FkaW5nXG59KTtcblxuZXhwb3J0IGNvbnN0IGNyZWF0ZVJlZnJlc2hBY3Rpb24gPSAob25SZWZyZXNoOiAoKSA9PiB2b2lkKTogRmVlZGJhY2tBY3Rpb24gPT4gKHtcbiAgbGFiZWw6ICdSZWZyZXNoIFBhZ2UnLFxuICBvbkNsaWNrOiBvblJlZnJlc2gsXG4gIHZhcmlhbnQ6ICdvdXRsaW5lJ1xufSk7XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVDb250YWN0U3VwcG9ydEFjdGlvbiA9ICgpOiBGZWVkYmFja0FjdGlvbiA9PiAoe1xuICBsYWJlbDogJ0NvbnRhY3QgU3VwcG9ydCcsXG4gIG9uQ2xpY2s6ICgpID0+IHtcbiAgICAvLyBZb3UgY2FuIGN1c3RvbWl6ZSB0aGlzIHRvIG9wZW4gYSBzdXBwb3J0IG1vZGFsLCByZWRpcmVjdCB0byBzdXBwb3J0IHBhZ2UsIGV0Yy5cbiAgICB3aW5kb3cub3BlbignbWFpbHRvOnN1cHBvcnRAZmFhZm8uY29tP3N1YmplY3Q9QXBwbGljYXRpb24gRXJyb3InLCAnX2JsYW5rJyk7XG4gIH0sXG4gIHZhcmlhbnQ6ICdnaG9zdCdcbn0pO1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZXh0cmFjdCBlcnJvciBkZXRhaWxzIGZyb20gdmFyaW91cyBlcnJvciB0eXBlc1xuZXhwb3J0IGZ1bmN0aW9uIGV4dHJhY3RFcnJvckRldGFpbHMoZXJyb3I6IGFueSk6IHsgbWVzc2FnZTogc3RyaW5nOyBkZXRhaWxzPzogc3RyaW5nW10gfSB7XG4gIGlmICh0eXBlb2YgZXJyb3IgPT09ICdzdHJpbmcnKSB7XG4gICAgcmV0dXJuIHsgbWVzc2FnZTogZXJyb3IgfTtcbiAgfVxuXG4gIGlmIChlcnJvcj8ucmVzcG9uc2U/LmRhdGEpIHtcbiAgICBjb25zdCBkYXRhID0gZXJyb3IucmVzcG9uc2UuZGF0YTtcbiAgICByZXR1cm4ge1xuICAgICAgbWVzc2FnZTogZGF0YS5lcnJvciB8fCBkYXRhLm1lc3NhZ2UgfHwgJ0FuIGVycm9yIG9jY3VycmVkJyxcbiAgICAgIGRldGFpbHM6IGRhdGEuZGV0YWlscyB8fCAoZGF0YS5lcnJvcnMgPyBPYmplY3QudmFsdWVzKGRhdGEuZXJyb3JzKS5mbGF0KCkgOiB1bmRlZmluZWQpXG4gICAgfTtcbiAgfVxuXG4gIGlmIChlcnJvcj8ubWVzc2FnZSkge1xuICAgIHJldHVybiB7XG4gICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgZGV0YWlsczogZXJyb3IuZGV0YWlscyB8fCAoZXJyb3Iuc3RhY2sgPyBbZXJyb3Iuc3RhY2tdIDogdW5kZWZpbmVkKVxuICAgIH07XG4gIH1cblxuICByZXR1cm4ge1xuICAgIG1lc3NhZ2U6ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyxcbiAgICBkZXRhaWxzOiBbSlNPTi5zdHJpbmdpZnkoZXJyb3IpXVxuICB9O1xufVxuXG4vLyBIb29rIGZvciBoYW5kbGluZyBhc3luYyBvcGVyYXRpb25zIHdpdGggZmVlZGJhY2tcbmV4cG9ydCBmdW5jdGlvbiB1c2VBc3luY09wZXJhdGlvbigpIHtcbiAgY29uc3QgZmVlZGJhY2sgPSB1c2VGZWVkYmFjaygpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGV4ZWN1dGUgPSB1c2VDYWxsYmFjayhhc3luYyA8VD4oXG4gICAgb3BlcmF0aW9uOiAoKSA9PiBQcm9taXNlPFQ+LFxuICAgIG9wdGlvbnM6IHtcbiAgICAgIGxvYWRpbmdNZXNzYWdlPzogc3RyaW5nO1xuICAgICAgc3VjY2Vzc01lc3NhZ2U/OiBzdHJpbmc7XG4gICAgICBlcnJvck1lc3NhZ2U/OiBzdHJpbmc7XG4gICAgICBvblN1Y2Nlc3M/OiAocmVzdWx0OiBUKSA9PiB2b2lkO1xuICAgICAgb25FcnJvcj86IChlcnJvcjogYW55KSA9PiB2b2lkO1xuICAgIH0gPSB7fVxuICApOiBQcm9taXNlPFQgfCBudWxsPiA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIFxuICAgIGxldCBsb2FkaW5nSWQ6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgICBpZiAob3B0aW9ucy5sb2FkaW5nTWVzc2FnZSkge1xuICAgICAgbG9hZGluZ0lkID0gZmVlZGJhY2suc2hvd0xvYWRpbmcob3B0aW9ucy5sb2FkaW5nTWVzc2FnZSk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG9wZXJhdGlvbigpO1xuICAgICAgXG4gICAgICBpZiAobG9hZGluZ0lkKSB7XG4gICAgICAgIGZlZWRiYWNrLmRpc21pc3NGZWVkYmFjayhsb2FkaW5nSWQpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAob3B0aW9ucy5zdWNjZXNzTWVzc2FnZSkge1xuICAgICAgICBmZWVkYmFjay5zaG93U3VjY2VzcyhvcHRpb25zLnN1Y2Nlc3NNZXNzYWdlKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgb3B0aW9ucy5vblN1Y2Nlc3M/LihyZXN1bHQpO1xuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgaWYgKGxvYWRpbmdJZCkge1xuICAgICAgICBmZWVkYmFjay5kaXNtaXNzRmVlZGJhY2sobG9hZGluZ0lkKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgY29uc3QgZXJyb3JEZXRhaWxzID0gZXh0cmFjdEVycm9yRGV0YWlscyhlcnJvcik7XG4gICAgICBmZWVkYmFjay5zaG93RXJyb3IoXG4gICAgICAgIG9wdGlvbnMuZXJyb3JNZXNzYWdlIHx8IGVycm9yRGV0YWlscy5tZXNzYWdlLFxuICAgICAgICB7IGRldGFpbHM6IGVycm9yRGV0YWlscy5kZXRhaWxzIH1cbiAgICAgICk7XG4gICAgICBcbiAgICAgIG9wdGlvbnMub25FcnJvcj8uKGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW2ZlZWRiYWNrXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBleGVjdXRlLFxuICAgIGlzTG9hZGluZyxcbiAgICAuLi5mZWVkYmFja1xuICB9O1xufVxuIl0sInZlcnNpb24iOjN9