b300e2f0aec78f4e2d554aa798e83826
"use strict";
/**
 * Interview Question Generation Business Logic Flaws Tests
 *
 * These tests prove critical inconsistencies and flaws in interview question generation
 * algorithms and scoring systems across multiple services in the FAAFO Career Platform.
 *
 * EXPECTED TO FAIL - These tests demonstrate business logic flaws that need fixing.
 */
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
(0, globals_1.describe)('Interview Question Generation Business Logic Flaws', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Multiple Question Generation Services with Conflicting Logic', function () {
        (0, globals_1.it)('should fail - multiple question generation services produce different results', function () {
            // We have at least 3 different question generation services:
            // 1. EnhancedQuestionGenerator (enhanced-question-generator.ts)
            // 2. Legacy algorithm (selectQuestionsForContextLegacy)
            // 3. SelfHealingAIService (self-healing-ai-service.ts)
            // 4. SkillAssessmentEngine (SkillAssessmentEngine.ts)
            var enhancedGeneratorExists = true;
            var legacyAlgorithmExists = true;
            var selfHealingAIExists = true;
            var skillAssessmentEngineExists = true;
            var totalQuestionGenerators = [
                enhancedGeneratorExists,
                legacyAlgorithmExists,
                selfHealingAIExists,
                skillAssessmentEngineExists
            ].filter(Boolean).length;
            // EXPECTED TO FAIL: Having multiple question generation services creates inconsistency
            // There should be ONE unified question generation service
            (0, globals_1.expect)(totalQuestionGenerators).toBe(1);
        });
        (0, globals_1.it)('should fail - question generation services use different scoring algorithms', function () {
            // EnhancedQuestionGenerator uses context-aware weighting with multiple factors
            var enhancedScoringFactors = [
                'contextRelevance',
                'difficultyAlignment',
                'categoryBalance',
                'diversityScore',
                'userPreferences'
            ];
            // Legacy algorithm uses simple random selection
            var legacyScoringFactors = ['random'];
            // AI service uses different scoring criteria
            var aiScoringFactors = [
                'aiRelevance',
                'complexityScore',
                'industryAlignment'
            ];
            // EXPECTED TO FAIL: All question generation should use consistent scoring criteria
            (0, globals_1.expect)(enhancedScoringFactors).toEqual(legacyScoringFactors);
            (0, globals_1.expect)(legacyScoringFactors).toEqual(aiScoringFactors);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Inconsistent Question Difficulty Algorithms', function () {
        (0, globals_1.it)('should fail - difficulty calculation methods are inconsistent', function () {
            // Different services calculate difficulty differently
            var enhancedDifficultyScale = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']; // 4 levels
            var legacyDifficultyScale = ['easy', 'medium', 'hard']; // 3 levels
            var aiDifficultyScale = [1, 2, 3, 4, 5]; // Numeric scale
            var skillAssessmentScale = ['beginner', 'intermediate', 'advanced']; // 3 levels, different casing
            // EXPECTED TO FAIL: All services should use the same difficulty scale and calculation
            (0, globals_1.expect)(enhancedDifficultyScale).toEqual(legacyDifficultyScale);
            (0, globals_1.expect)(legacyDifficultyScale).toEqual(aiDifficultyScale);
            (0, globals_1.expect)(aiDifficultyScale).toEqual(skillAssessmentScale);
        });
        (0, globals_1.it)('should fail - difficulty progression logic is flawed', function () {
            // Question difficulty should progress logically within a session
            var sessionQuestions = [
                { difficulty: 'ADVANCED', order: 1 },
                { difficulty: 'BEGINNER', order: 2 },
                { difficulty: 'EXPERT', order: 3 },
                { difficulty: 'INTERMEDIATE', order: 4 }
            ];
            // EXPECTED TO FAIL: Questions should progress from easier to harder
            var isProgressivelyHarder = sessionQuestions.every(function (q, i) {
                if (i === 0)
                    return true;
                var prevDifficulty = sessionQuestions[i - 1].difficulty;
                var currentDifficulty = q.difficulty;
                var difficultyOrder = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
                var prevIndex = difficultyOrder.indexOf(prevDifficulty);
                var currentIndex = difficultyOrder.indexOf(currentDifficulty);
                return currentIndex >= prevIndex;
            });
            (0, globals_1.expect)(isProgressivelyHarder).toBe(true);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: AI Score Validation Flaws', function () {
        (0, globals_1.it)('should fail - AI scores can be invalid but still get stored', function () {
            // AI scores should be between 0-10 but validation allows invalid scores
            var invalidScores = [-1, 11, NaN, null, undefined, 'invalid', 15.5];
            invalidScores.forEach(function (score) {
                // Mock the AI score validation logic from the codebase
                var isValidScore = typeof score === 'number' &&
                    !isNaN(score) &&
                    score >= 0 &&
                    score <= 10;
                // EXPECTED TO FAIL: Invalid scores should be rejected
                (0, globals_1.expect)(isValidScore).toBe(true);
            });
        });
        (0, globals_1.it)('should fail - AI score calculation is inconsistent across question types', function () {
            // Different question types should use consistent scoring algorithms
            var technicalQuestionScore = 8.5; // Technical questions scored differently
            var behavioralQuestionScore = 7.2; // Behavioral questions scored differently  
            var systemDesignScore = 9.1; // System design scored differently
            // Mock responses with similar quality
            var similarQualityResponse = {
                responseLength: 150,
                responseTime: 120000, // 2 minutes
                keywordMatches: 5,
                coherenceScore: 0.8
            };
            // EXPECTED TO FAIL: Similar quality responses should get similar scores regardless of question type
            var scoreDifference1 = Math.abs(technicalQuestionScore - behavioralQuestionScore);
            var scoreDifference2 = Math.abs(behavioralQuestionScore - systemDesignScore);
            (0, globals_1.expect)(scoreDifference1).toBeLessThan(0.5); // Should be within 0.5 points
            (0, globals_1.expect)(scoreDifference2).toBeLessThan(0.5);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Question Bank Management Inconsistencies', function () {
        (0, globals_1.it)('should fail - question banks have duplicate questions with different IDs', function () {
            // Mock question banks with duplicates
            var questionBank1 = [
                { id: 'q1', question: 'What is React?', category: 'technical' },
                { id: 'q2', question: 'Explain closures in JavaScript', category: 'technical' }
            ];
            var questionBank2 = [
                { id: 'q3', question: 'What is React?', category: 'technical' }, // Duplicate content, different ID
                { id: 'q4', question: 'Describe your leadership style', category: 'behavioral' }
            ];
            // Check for duplicate questions across banks
            var allQuestions = __spreadArray(__spreadArray([], questionBank1, true), questionBank2, true);
            var uniqueQuestions = new Set(allQuestions.map(function (q) { return q.question.toLowerCase(); }));
            // EXPECTED TO FAIL: Should not have duplicate questions
            (0, globals_1.expect)(allQuestions.length).toBe(uniqueQuestions.size);
        });
        (0, globals_1.it)('should fail - question categorization is inconsistent', function () {
            // Different services use different category names for similar questions
            var enhancedGeneratorCategories = ['technical', 'behavioral', 'system-design', 'leadership'];
            var legacyCategories = ['tech', 'soft-skills', 'architecture', 'management'];
            var aiServiceCategories = ['coding', 'personality', 'design', 'team-lead'];
            // EXPECTED TO FAIL: All services should use consistent category names
            (0, globals_1.expect)(enhancedGeneratorCategories).toEqual(legacyCategories);
            (0, globals_1.expect)(legacyCategories).toEqual(aiServiceCategories);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: Session State Management Problems', function () {
        (0, globals_1.it)('should fail - interview session state can become inconsistent', function () {
            // Mock interview session with inconsistent state
            var interviewSession = {
                totalQuestions: 10,
                completedQuestions: 8,
                questions: new Array(12), // More questions than totalQuestions
                status: 'IN_PROGRESS',
                progress: {
                    completed: 7, // Different from completedQuestions
                    total: 10,
                    percentage: 80 // Doesn't match completed/total ratio
                }
            };
            // EXPECTED TO FAIL: Session state should be consistent
            (0, globals_1.expect)(interviewSession.questions.length).toBe(interviewSession.totalQuestions);
            (0, globals_1.expect)(interviewSession.completedQuestions).toBe(interviewSession.progress.completed);
            (0, globals_1.expect)(interviewSession.progress.percentage).toBe(Math.round((interviewSession.progress.completed / interviewSession.progress.total) * 100));
        });
        (0, globals_1.it)('should fail - question order can be corrupted during session', function () {
            // Mock questions with corrupted order
            var sessionQuestions = [
                { id: 'q1', questionOrder: 1 },
                { id: 'q2', questionOrder: 3 }, // Missing order 2
                { id: 'q3', questionOrder: 3 }, // Duplicate order
                { id: 'q4', questionOrder: 5 } // Gap in sequence
            ];
            // EXPECTED TO FAIL: Question order should be sequential without gaps or duplicates
            var orders = sessionQuestions.map(function (q) { return q.questionOrder; }).sort(function (a, b) { return a - b; });
            var expectedOrders = Array.from({ length: sessionQuestions.length }, function (_, i) { return i + 1; });
            (0, globals_1.expect)(orders).toEqual(expectedOrders);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 6: Fallback Logic Failures', function () {
        (0, globals_1.it)('should fail - fallback question generation can produce empty results', function () {
            // Mock scenario where all question generation services fail
            var enhancedGeneratorResult = [];
            var legacyAlgorithmResult = [];
            var aiServiceResult = { success: false, error: 'AI service unavailable' };
            // Final fallback should never return empty questions
            var finalQuestions = enhancedGeneratorResult.length > 0
                ? enhancedGeneratorResult
                : legacyAlgorithmResult.length > 0
                    ? legacyAlgorithmResult
                    : []; // This is the problem - no ultimate fallback
            // EXPECTED TO FAIL: Should always have fallback questions available
            (0, globals_1.expect)(finalQuestions.length).toBeGreaterThan(0);
        });
        (0, globals_1.it)('should fail - fallback questions dont match session configuration', function () {
            // Mock fallback questions that don't match the session requirements
            var sessionConfig = {
                sessionType: 'technical',
                experienceLevel: 'SENIOR',
                difficulty: 'ADVANCED',
                focusAreas: ['algorithms', 'system-design']
            };
            var fallbackQuestions = [
                { category: 'behavioral', difficulty: 'BEGINNER', focusArea: 'communication' },
                { category: 'general', difficulty: 'INTERMEDIATE', focusArea: 'teamwork' }
            ];
            // EXPECTED TO FAIL: Fallback questions should match session configuration
            var questionsMatchConfig = fallbackQuestions.every(function (q) {
                return sessionConfig.focusAreas.includes(q.focusArea) &&
                    q.difficulty === sessionConfig.difficulty;
            });
            (0, globals_1.expect)(questionsMatchConfig).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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