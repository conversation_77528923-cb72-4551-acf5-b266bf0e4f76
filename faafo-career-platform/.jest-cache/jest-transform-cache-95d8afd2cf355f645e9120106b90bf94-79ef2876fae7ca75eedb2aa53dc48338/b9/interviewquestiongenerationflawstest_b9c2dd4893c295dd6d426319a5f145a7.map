{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/interview-question-generation-flaws.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;AAEH,yCAAuE;AAEvE,IAAA,kBAAQ,EAAC,oDAAoD,EAAE;IAC7D,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,gFAAgF,EAAE;QACzF,IAAA,YAAE,EAAC,+EAA+E,EAAE;YAClF,6DAA6D;YAC7D,gEAAgE;YAChE,wDAAwD;YACxD,uDAAuD;YACvD,sDAAsD;YAEtD,IAAM,uBAAuB,GAAG,IAAI,CAAC;YACrC,IAAM,qBAAqB,GAAG,IAAI,CAAC;YACnC,IAAM,mBAAmB,GAAG,IAAI,CAAC;YACjC,IAAM,2BAA2B,GAAG,IAAI,CAAC;YAEzC,IAAM,uBAAuB,GAAG;gBAC9B,uBAAuB;gBACvB,qBAAqB;gBACrB,mBAAmB;gBACnB,2BAA2B;aAC5B,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YAEzB,uFAAuF;YACvF,0DAA0D;YAC1D,IAAA,gBAAM,EAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,6EAA6E,EAAE;YAChF,+EAA+E;YAC/E,IAAM,sBAAsB,GAAG;gBAC7B,kBAAkB;gBAClB,qBAAqB;gBACrB,iBAAiB;gBACjB,gBAAgB;gBAChB,iBAAiB;aAClB,CAAC;YAEF,gDAAgD;YAChD,IAAM,oBAAoB,GAAG,CAAC,QAAQ,CAAC,CAAC;YAExC,6CAA6C;YAC7C,IAAM,gBAAgB,GAAG;gBACvB,aAAa;gBACb,iBAAiB;gBACjB,mBAAmB;aACpB,CAAC;YAEF,mFAAmF;YACnF,IAAA,gBAAM,EAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC7D,IAAA,gBAAM,EAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,+DAA+D,EAAE;QACxE,IAAA,YAAE,EAAC,+DAA+D,EAAE;YAClE,sDAAsD;YACtD,IAAM,uBAAuB,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,WAAW;YAC/F,IAAM,qBAAqB,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,WAAW;YACrE,IAAM,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,gBAAgB;YAC3D,IAAM,oBAAoB,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,6BAA6B;YAEpG,sFAAsF;YACtF,IAAA,gBAAM,EAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YAC/D,IAAA,gBAAM,EAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACzD,IAAA,gBAAM,EAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sDAAsD,EAAE;YACzD,iEAAiE;YACjE,IAAM,gBAAgB,GAAG;gBACvB,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,EAAE;gBACpC,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,EAAE;gBACpC,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE;gBAClC,EAAE,UAAU,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,EAAE;aACzC,CAAC;YAEF,oEAAoE;YACpE,IAAM,qBAAqB,GAAG,gBAAgB,CAAC,KAAK,CAAC,UAAC,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,KAAK,CAAC;oBAAE,OAAO,IAAI,CAAC;gBACzB,IAAM,cAAc,GAAG,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC;gBAC1D,IAAM,iBAAiB,GAAG,CAAC,CAAC,UAAU,CAAC;gBAEvC,IAAM,eAAe,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAC3E,IAAM,SAAS,GAAG,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBAC1D,IAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBAEhE,OAAO,YAAY,IAAI,SAAS,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,IAAA,gBAAM,EAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,6CAA6C,EAAE;QACtD,IAAA,YAAE,EAAC,6DAA6D,EAAE;YAChE,wEAAwE;YACxE,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAEtE,aAAa,CAAC,OAAO,CAAC,UAAA,KAAK;gBACzB,uDAAuD;gBACvD,IAAM,YAAY,GAAG,OAAO,KAAK,KAAK,QAAQ;oBAC3B,CAAC,KAAK,CAAC,KAAK,CAAC;oBACb,KAAK,IAAI,CAAC;oBACV,KAAK,IAAI,EAAE,CAAC;gBAE/B,sDAAsD;gBACtD,IAAA,gBAAM,EAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,0EAA0E,EAAE;YAC7E,oEAAoE;YACpE,IAAM,sBAAsB,GAAG,GAAG,CAAC,CAAC,yCAAyC;YAC7E,IAAM,uBAAuB,GAAG,GAAG,CAAC,CAAC,4CAA4C;YACjF,IAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,mCAAmC;YAElE,sCAAsC;YACtC,IAAM,sBAAsB,GAAG;gBAC7B,cAAc,EAAE,GAAG;gBACnB,YAAY,EAAE,MAAM,EAAE,YAAY;gBAClC,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,GAAG;aACpB,CAAC;YAEF,oGAAoG;YACpG,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,sBAAsB,GAAG,uBAAuB,CAAC,CAAC;YACpF,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,uBAAuB,GAAG,iBAAiB,CAAC,CAAC;YAE/E,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,8BAA8B;YAC1E,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,4DAA4D,EAAE;QACrE,IAAA,YAAE,EAAC,0EAA0E,EAAE;YAC7E,sCAAsC;YACtC,IAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,WAAW,EAAE;gBAC/D,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,gCAAgC,EAAE,QAAQ,EAAE,WAAW,EAAE;aAChF,CAAC;YAEF,IAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,kCAAkC;gBACnG,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,gCAAgC,EAAE,QAAQ,EAAE,YAAY,EAAE;aACjF,CAAC;YAEF,6CAA6C;YAC7C,IAAM,YAAY,mCAAO,aAAa,SAAK,aAAa,OAAC,CAAC;YAC1D,IAAM,eAAe,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAxB,CAAwB,CAAC,CAAC,CAAC;YAEjF,wDAAwD;YACxD,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,uDAAuD,EAAE;YAC1D,wEAAwE;YACxE,IAAM,2BAA2B,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YAC/F,IAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;YAC/E,IAAM,mBAAmB,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAE7E,sEAAsE;YACtE,IAAA,gBAAM,EAAC,2BAA2B,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAC9D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,qDAAqD,EAAE;QAC9D,IAAA,YAAE,EAAC,+DAA+D,EAAE;YAClE,iDAAiD;YACjD,IAAM,gBAAgB,GAAG;gBACvB,cAAc,EAAE,EAAE;gBAClB,kBAAkB,EAAE,CAAC;gBACrB,SAAS,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,qCAAqC;gBAC/D,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,CAAC,EAAE,oCAAoC;oBAClD,KAAK,EAAE,EAAE;oBACT,UAAU,EAAE,EAAE,CAAC,sCAAsC;iBACtD;aACF,CAAC;YAEF,uDAAuD;YACvD,IAAA,gBAAM,EAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAChF,IAAA,gBAAM,EAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACtF,IAAA,gBAAM,EAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAC/C,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,GAAG,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAC1F,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,8DAA8D,EAAE;YACjE,sCAAsC;YACtC,IAAM,gBAAgB,GAAG;gBACvB,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE;gBAC9B,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,kBAAkB;gBAClD,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,kBAAkB;gBAClD,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE,CAAE,kBAAkB;aACnD,CAAC;YAEF,mFAAmF;YACnF,IAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,EAAf,CAAe,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAC,CAAC;YAChF,IAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAC,CAAC;YAExF,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2CAA2C,EAAE;QACpD,IAAA,YAAE,EAAC,sEAAsE,EAAE;YACzE,4DAA4D;YAC5D,IAAM,uBAAuB,GAAG,EAAE,CAAC;YACnC,IAAM,qBAAqB,GAAG,EAAE,CAAC;YACjC,IAAM,eAAe,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC;YAE5E,qDAAqD;YACrD,IAAM,cAAc,GAAG,uBAAuB,CAAC,MAAM,GAAG,CAAC;gBACvD,CAAC,CAAC,uBAAuB;gBACzB,CAAC,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC;oBAClC,CAAC,CAAC,qBAAqB;oBACvB,CAAC,CAAC,EAAE,CAAC,CAAC,6CAA6C;YAErD,oEAAoE;YACpE,IAAA,gBAAM,EAAC,cAAc,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,mEAAmE,EAAE;YACtE,oEAAoE;YACpE,IAAM,aAAa,GAAG;gBACpB,WAAW,EAAE,WAAW;gBACxB,eAAe,EAAE,QAAQ;gBACzB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;aAC5C,CAAC;YAEF,IAAM,iBAAiB,GAAG;gBACxB,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC9E,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE;aAC3E,CAAC;YAEF,0EAA0E;YAC1E,IAAM,oBAAoB,GAAG,iBAAiB,CAAC,KAAK,CAAC,UAAA,CAAC;gBACpD,OAAA,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;oBAC9C,CAAC,CAAC,UAAU,KAAK,aAAa,CAAC,UAAU;YADzC,CACyC,CAC1C,CAAC;YAEF,IAAA,gBAAM,EAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/interview-question-generation-flaws.test.ts"], "sourcesContent": ["/**\n * Interview Question Generation Business Logic Flaws Tests\n * \n * These tests prove critical inconsistencies and flaws in interview question generation\n * algorithms and scoring systems across multiple services in the FAAFO Career Platform.\n * \n * EXPECTED TO FAIL - These tests demonstrate business logic flaws that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\n\ndescribe('Interview Question Generation Business Logic Flaws', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Multiple Question Generation Services with Conflicting Logic', () => {\n    it('should fail - multiple question generation services produce different results', () => {\n      // We have at least 3 different question generation services:\n      // 1. EnhancedQuestionGenerator (enhanced-question-generator.ts)\n      // 2. Legacy algorithm (selectQuestionsForContextLegacy)\n      // 3. SelfHealingAIService (self-healing-ai-service.ts)\n      // 4. SkillAssessmentEngine (SkillAssessmentEngine.ts)\n      \n      const enhancedGeneratorExists = true;\n      const legacyAlgorithmExists = true;\n      const selfHealingAIExists = true;\n      const skillAssessmentEngineExists = true;\n      \n      const totalQuestionGenerators = [\n        enhancedGeneratorExists,\n        legacyAlgorithmExists, \n        selfHealingAIExists,\n        skillAssessmentEngineExists\n      ].filter(Boolean).length;\n      \n      // EXPECTED TO FAIL: Having multiple question generation services creates inconsistency\n      // There should be ONE unified question generation service\n      expect(totalQuestionGenerators).toBe(1);\n    });\n\n    it('should fail - question generation services use different scoring algorithms', () => {\n      // EnhancedQuestionGenerator uses context-aware weighting with multiple factors\n      const enhancedScoringFactors = [\n        'contextRelevance',\n        'difficultyAlignment', \n        'categoryBalance',\n        'diversityScore',\n        'userPreferences'\n      ];\n      \n      // Legacy algorithm uses simple random selection\n      const legacyScoringFactors = ['random'];\n      \n      // AI service uses different scoring criteria\n      const aiScoringFactors = [\n        'aiRelevance',\n        'complexityScore',\n        'industryAlignment'\n      ];\n      \n      // EXPECTED TO FAIL: All question generation should use consistent scoring criteria\n      expect(enhancedScoringFactors).toEqual(legacyScoringFactors);\n      expect(legacyScoringFactors).toEqual(aiScoringFactors);\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: Inconsistent Question Difficulty Algorithms', () => {\n    it('should fail - difficulty calculation methods are inconsistent', () => {\n      // Different services calculate difficulty differently\n      const enhancedDifficultyScale = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']; // 4 levels\n      const legacyDifficultyScale = ['easy', 'medium', 'hard']; // 3 levels\n      const aiDifficultyScale = [1, 2, 3, 4, 5]; // Numeric scale\n      const skillAssessmentScale = ['beginner', 'intermediate', 'advanced']; // 3 levels, different casing\n      \n      // EXPECTED TO FAIL: All services should use the same difficulty scale and calculation\n      expect(enhancedDifficultyScale).toEqual(legacyDifficultyScale);\n      expect(legacyDifficultyScale).toEqual(aiDifficultyScale);\n      expect(aiDifficultyScale).toEqual(skillAssessmentScale);\n    });\n\n    it('should fail - difficulty progression logic is flawed', () => {\n      // Question difficulty should progress logically within a session\n      const sessionQuestions = [\n        { difficulty: 'ADVANCED', order: 1 },\n        { difficulty: 'BEGINNER', order: 2 },\n        { difficulty: 'EXPERT', order: 3 },\n        { difficulty: 'INTERMEDIATE', order: 4 }\n      ];\n      \n      // EXPECTED TO FAIL: Questions should progress from easier to harder\n      const isProgressivelyHarder = sessionQuestions.every((q, i) => {\n        if (i === 0) return true;\n        const prevDifficulty = sessionQuestions[i - 1].difficulty;\n        const currentDifficulty = q.difficulty;\n        \n        const difficultyOrder = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];\n        const prevIndex = difficultyOrder.indexOf(prevDifficulty);\n        const currentIndex = difficultyOrder.indexOf(currentDifficulty);\n        \n        return currentIndex >= prevIndex;\n      });\n      \n      expect(isProgressivelyHarder).toBe(true);\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: AI Score Validation Flaws', () => {\n    it('should fail - AI scores can be invalid but still get stored', () => {\n      // AI scores should be between 0-10 but validation allows invalid scores\n      const invalidScores = [-1, 11, NaN, null, undefined, 'invalid', 15.5];\n      \n      invalidScores.forEach(score => {\n        // Mock the AI score validation logic from the codebase\n        const isValidScore = typeof score === 'number' && \n                           !isNaN(score) && \n                           score >= 0 && \n                           score <= 10;\n        \n        // EXPECTED TO FAIL: Invalid scores should be rejected\n        expect(isValidScore).toBe(true);\n      });\n    });\n\n    it('should fail - AI score calculation is inconsistent across question types', () => {\n      // Different question types should use consistent scoring algorithms\n      const technicalQuestionScore = 8.5; // Technical questions scored differently\n      const behavioralQuestionScore = 7.2; // Behavioral questions scored differently  \n      const systemDesignScore = 9.1; // System design scored differently\n      \n      // Mock responses with similar quality\n      const similarQualityResponse = {\n        responseLength: 150,\n        responseTime: 120000, // 2 minutes\n        keywordMatches: 5,\n        coherenceScore: 0.8\n      };\n      \n      // EXPECTED TO FAIL: Similar quality responses should get similar scores regardless of question type\n      const scoreDifference1 = Math.abs(technicalQuestionScore - behavioralQuestionScore);\n      const scoreDifference2 = Math.abs(behavioralQuestionScore - systemDesignScore);\n      \n      expect(scoreDifference1).toBeLessThan(0.5); // Should be within 0.5 points\n      expect(scoreDifference2).toBeLessThan(0.5);\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Question Bank Management Inconsistencies', () => {\n    it('should fail - question banks have duplicate questions with different IDs', () => {\n      // Mock question banks with duplicates\n      const questionBank1 = [\n        { id: 'q1', question: 'What is React?', category: 'technical' },\n        { id: 'q2', question: 'Explain closures in JavaScript', category: 'technical' }\n      ];\n      \n      const questionBank2 = [\n        { id: 'q3', question: 'What is React?', category: 'technical' }, // Duplicate content, different ID\n        { id: 'q4', question: 'Describe your leadership style', category: 'behavioral' }\n      ];\n      \n      // Check for duplicate questions across banks\n      const allQuestions = [...questionBank1, ...questionBank2];\n      const uniqueQuestions = new Set(allQuestions.map(q => q.question.toLowerCase()));\n      \n      // EXPECTED TO FAIL: Should not have duplicate questions\n      expect(allQuestions.length).toBe(uniqueQuestions.size);\n    });\n\n    it('should fail - question categorization is inconsistent', () => {\n      // Different services use different category names for similar questions\n      const enhancedGeneratorCategories = ['technical', 'behavioral', 'system-design', 'leadership'];\n      const legacyCategories = ['tech', 'soft-skills', 'architecture', 'management'];\n      const aiServiceCategories = ['coding', 'personality', 'design', 'team-lead'];\n      \n      // EXPECTED TO FAIL: All services should use consistent category names\n      expect(enhancedGeneratorCategories).toEqual(legacyCategories);\n      expect(legacyCategories).toEqual(aiServiceCategories);\n    });\n  });\n\n  describe('CRITICAL ISSUE 5: Session State Management Problems', () => {\n    it('should fail - interview session state can become inconsistent', () => {\n      // Mock interview session with inconsistent state\n      const interviewSession = {\n        totalQuestions: 10,\n        completedQuestions: 8,\n        questions: new Array(12), // More questions than totalQuestions\n        status: 'IN_PROGRESS',\n        progress: {\n          completed: 7, // Different from completedQuestions\n          total: 10,\n          percentage: 80 // Doesn't match completed/total ratio\n        }\n      };\n      \n      // EXPECTED TO FAIL: Session state should be consistent\n      expect(interviewSession.questions.length).toBe(interviewSession.totalQuestions);\n      expect(interviewSession.completedQuestions).toBe(interviewSession.progress.completed);\n      expect(interviewSession.progress.percentage).toBe(\n        Math.round((interviewSession.progress.completed / interviewSession.progress.total) * 100)\n      );\n    });\n\n    it('should fail - question order can be corrupted during session', () => {\n      // Mock questions with corrupted order\n      const sessionQuestions = [\n        { id: 'q1', questionOrder: 1 },\n        { id: 'q2', questionOrder: 3 }, // Missing order 2\n        { id: 'q3', questionOrder: 3 }, // Duplicate order\n        { id: 'q4', questionOrder: 5 }  // Gap in sequence\n      ];\n      \n      // EXPECTED TO FAIL: Question order should be sequential without gaps or duplicates\n      const orders = sessionQuestions.map(q => q.questionOrder).sort((a, b) => a - b);\n      const expectedOrders = Array.from({ length: sessionQuestions.length }, (_, i) => i + 1);\n      \n      expect(orders).toEqual(expectedOrders);\n    });\n  });\n\n  describe('CRITICAL ISSUE 6: Fallback Logic Failures', () => {\n    it('should fail - fallback question generation can produce empty results', () => {\n      // Mock scenario where all question generation services fail\n      const enhancedGeneratorResult = [];\n      const legacyAlgorithmResult = [];\n      const aiServiceResult = { success: false, error: 'AI service unavailable' };\n      \n      // Final fallback should never return empty questions\n      const finalQuestions = enhancedGeneratorResult.length > 0 \n        ? enhancedGeneratorResult\n        : legacyAlgorithmResult.length > 0\n        ? legacyAlgorithmResult\n        : []; // This is the problem - no ultimate fallback\n      \n      // EXPECTED TO FAIL: Should always have fallback questions available\n      expect(finalQuestions.length).toBeGreaterThan(0);\n    });\n\n    it('should fail - fallback questions dont match session configuration', () => {\n      // Mock fallback questions that don't match the session requirements\n      const sessionConfig = {\n        sessionType: 'technical',\n        experienceLevel: 'SENIOR',\n        difficulty: 'ADVANCED',\n        focusAreas: ['algorithms', 'system-design']\n      };\n      \n      const fallbackQuestions = [\n        { category: 'behavioral', difficulty: 'BEGINNER', focusArea: 'communication' },\n        { category: 'general', difficulty: 'INTERMEDIATE', focusArea: 'teamwork' }\n      ];\n      \n      // EXPECTED TO FAIL: Fallback questions should match session configuration\n      const questionsMatchConfig = fallbackQuestions.every(q => \n        sessionConfig.focusAreas.includes(q.focusArea) &&\n        q.difficulty === sessionConfig.difficulty\n      );\n      \n      expect(questionsMatchConfig).toBe(true);\n    });\n  });\n});\n"], "version": 3}