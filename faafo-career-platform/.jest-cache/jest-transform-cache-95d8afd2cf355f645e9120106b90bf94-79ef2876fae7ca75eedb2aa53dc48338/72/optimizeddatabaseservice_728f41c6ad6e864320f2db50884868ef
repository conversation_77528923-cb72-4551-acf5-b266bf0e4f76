590eb01f76faffb58c2219df5ac9e941
"use strict";

/**
 * Optimized Database Service
 * Leverages new composite indexes for maximum query performance
 * Part of Phase 2 Database Query Optimization
 */
/* istanbul ignore next */
function cov_4u5r76xgr() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/optimized-database-service.ts";
  var hash = "6be5d2664934170e4a7a0b0a8dadc5ed3077b88d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/optimized-database-service.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 15
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "1": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 15,
          column: 6
        }
      },
      "2": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 13,
          column: 9
        }
      },
      "3": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 9,
          column: 25
        }
      },
      "4": {
        start: {
          line: 9,
          column: 31
        },
        end: {
          line: 9,
          column: 47
        }
      },
      "5": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 10,
          column: 29
        }
      },
      "6": {
        start: {
          line: 11,
          column: 12
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "7": {
        start: {
          line: 11,
          column: 29
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "8": {
        start: {
          line: 12,
          column: 16
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "9": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 17
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 43
        }
      },
      "11": {
        start: {
          line: 18,
          column: 16
        },
        end: {
          line: 26,
          column: 1
        }
      },
      "12": {
        start: {
          line: 19,
          column: 28
        },
        end: {
          line: 19,
          column: 110
        }
      },
      "13": {
        start: {
          line: 19,
          column: 91
        },
        end: {
          line: 19,
          column: 106
        }
      },
      "14": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 25,
          column: 7
        }
      },
      "15": {
        start: {
          line: 21,
          column: 36
        },
        end: {
          line: 21,
          column: 97
        }
      },
      "16": {
        start: {
          line: 21,
          column: 42
        },
        end: {
          line: 21,
          column: 70
        }
      },
      "17": {
        start: {
          line: 21,
          column: 85
        },
        end: {
          line: 21,
          column: 95
        }
      },
      "18": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 100
        }
      },
      "19": {
        start: {
          line: 22,
          column: 41
        },
        end: {
          line: 22,
          column: 73
        }
      },
      "20": {
        start: {
          line: 22,
          column: 88
        },
        end: {
          line: 22,
          column: 98
        }
      },
      "21": {
        start: {
          line: 23,
          column: 32
        },
        end: {
          line: 23,
          column: 116
        }
      },
      "22": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 78
        }
      },
      "23": {
        start: {
          line: 27,
          column: 18
        },
        end: {
          line: 53,
          column: 1
        }
      },
      "24": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 104
        }
      },
      "25": {
        start: {
          line: 28,
          column: 43
        },
        end: {
          line: 28,
          column: 68
        }
      },
      "26": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 68
        }
      },
      "27": {
        start: {
          line: 28,
          column: 69
        },
        end: {
          line: 28,
          column: 81
        }
      },
      "28": {
        start: {
          line: 28,
          column: 119
        },
        end: {
          line: 28,
          column: 196
        }
      },
      "29": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 160
        }
      },
      "30": {
        start: {
          line: 29,
          column: 141
        },
        end: {
          line: 29,
          column: 153
        }
      },
      "31": {
        start: {
          line: 30,
          column: 23
        },
        end: {
          line: 30,
          column: 68
        }
      },
      "32": {
        start: {
          line: 30,
          column: 45
        },
        end: {
          line: 30,
          column: 65
        }
      },
      "33": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 70
        }
      },
      "34": {
        start: {
          line: 32,
          column: 15
        },
        end: {
          line: 32,
          column: 70
        }
      },
      "35": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 50,
          column: 66
        }
      },
      "36": {
        start: {
          line: 33,
          column: 50
        },
        end: {
          line: 50,
          column: 66
        }
      },
      "37": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 169
        }
      },
      "38": {
        start: {
          line: 34,
          column: 160
        },
        end: {
          line: 34,
          column: 169
        }
      },
      "39": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 52
        }
      },
      "40": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 35,
          column: 52
        }
      },
      "41": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 48,
          column: 13
        }
      },
      "42": {
        start: {
          line: 37,
          column: 32
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "43": {
        start: {
          line: 37,
          column: 40
        },
        end: {
          line: 37,
          column: 46
        }
      },
      "44": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "45": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 72
        }
      },
      "46": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 34
        }
      },
      "47": {
        start: {
          line: 39,
          column: 35
        },
        end: {
          line: 39,
          column: 45
        }
      },
      "48": {
        start: {
          line: 39,
          column: 46
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "49": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "50": {
        start: {
          line: 40,
          column: 24
        },
        end: {
          line: 40,
          column: 41
        }
      },
      "51": {
        start: {
          line: 40,
          column: 42
        },
        end: {
          line: 40,
          column: 55
        }
      },
      "52": {
        start: {
          line: 40,
          column: 56
        },
        end: {
          line: 40,
          column: 65
        }
      },
      "53": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 128
        }
      },
      "54": {
        start: {
          line: 42,
          column: 110
        },
        end: {
          line: 42,
          column: 116
        }
      },
      "55": {
        start: {
          line: 42,
          column: 117
        },
        end: {
          line: 42,
          column: 126
        }
      },
      "56": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 106
        }
      },
      "57": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 97
        }
      },
      "58": {
        start: {
          line: 43,
          column: 98
        },
        end: {
          line: 43,
          column: 104
        }
      },
      "59": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 89
        }
      },
      "60": {
        start: {
          line: 44,
          column: 57
        },
        end: {
          line: 44,
          column: 72
        }
      },
      "61": {
        start: {
          line: 44,
          column: 73
        },
        end: {
          line: 44,
          column: 80
        }
      },
      "62": {
        start: {
          line: 44,
          column: 81
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "63": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 87
        }
      },
      "64": {
        start: {
          line: 45,
          column: 47
        },
        end: {
          line: 45,
          column: 62
        }
      },
      "65": {
        start: {
          line: 45,
          column: 63
        },
        end: {
          line: 45,
          column: 78
        }
      },
      "66": {
        start: {
          line: 45,
          column: 79
        },
        end: {
          line: 45,
          column: 85
        }
      },
      "67": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 42
        }
      },
      "68": {
        start: {
          line: 46,
          column: 30
        },
        end: {
          line: 46,
          column: 42
        }
      },
      "69": {
        start: {
          line: 47,
          column: 20
        },
        end: {
          line: 47,
          column: 33
        }
      },
      "70": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 43
        }
      },
      "71": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 39
        }
      },
      "72": {
        start: {
          line: 50,
          column: 22
        },
        end: {
          line: 50,
          column: 34
        }
      },
      "73": {
        start: {
          line: 50,
          column: 35
        },
        end: {
          line: 50,
          column: 41
        }
      },
      "74": {
        start: {
          line: 50,
          column: 54
        },
        end: {
          line: 50,
          column: 64
        }
      },
      "75": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "76": {
        start: {
          line: 51,
          column: 23
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "77": {
        start: {
          line: 51,
          column: 36
        },
        end: {
          line: 51,
          column: 89
        }
      },
      "78": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 62
        }
      },
      "79": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 42
        }
      },
      "80": {
        start: {
          line: 56,
          column: 15
        },
        end: {
          line: 56,
          column: 38
        }
      },
      "81": {
        start: {
          line: 57,
          column: 46
        },
        end: {
          line: 411,
          column: 3
        }
      },
      "82": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 37
        }
      },
      "83": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 127,
          column: 6
        }
      },
      "84": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 126,
          column: 11
        }
      },
      "85": {
        start: {
          line: 68,
          column: 12
        },
        end: {
          line: 68,
          column: 49
        }
      },
      "86": {
        start: {
          line: 68,
          column: 36
        },
        end: {
          line: 68,
          column: 47
        }
      },
      "87": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 72
        }
      },
      "88": {
        start: {
          line: 69,
          column: 46
        },
        end: {
          line: 69,
          column: 70
        }
      },
      "89": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 125,
          column: 15
        }
      },
      "90": {
        start: {
          line: 71,
          column: 16
        },
        end: {
          line: 124,
          column: 17
        }
      },
      "91": {
        start: {
          line: 73,
          column: 24
        },
        end: {
          line: 73,
          column: 47
        }
      },
      "92": {
        start: {
          line: 74,
          column: 24
        },
        end: {
          line: 74,
          column: 37
        }
      },
      "93": {
        start: {
          line: 76,
          column: 24
        },
        end: {
          line: 76,
          column: 50
        }
      },
      "94": {
        start: {
          line: 77,
          column: 24
        },
        end: {
          line: 77,
          column: 112
        }
      },
      "95": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 99,
          column: 32
        }
      },
      "96": {
        start: {
          line: 101,
          column: 24
        },
        end: {
          line: 101,
          column: 48
        }
      },
      "97": {
        start: {
          line: 102,
          column: 24
        },
        end: {
          line: 102,
          column: 63
        }
      },
      "98": {
        start: {
          line: 103,
          column: 24
        },
        end: {
          line: 109,
          column: 27
        }
      },
      "99": {
        start: {
          line: 110,
          column: 24
        },
        end: {
          line: 118,
          column: 36
        }
      },
      "100": {
        start: {
          line: 110,
          column: 86
        },
        end: {
          line: 118,
          column: 31
        }
      },
      "101": {
        start: {
          line: 120,
          column: 24
        },
        end: {
          line: 120,
          column: 44
        }
      },
      "102": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 121,
          column: 93
        }
      },
      "103": {
        start: {
          line: 122,
          column: 24
        },
        end: {
          line: 122,
          column: 38
        }
      },
      "104": {
        start: {
          line: 123,
          column: 28
        },
        end: {
          line: 123,
          column: 50
        }
      },
      "105": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 182,
          column: 6
        }
      },
      "106": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 181,
          column: 11
        }
      },
      "107": {
        start: {
          line: 135,
          column: 12
        },
        end: {
          line: 135,
          column: 60
        }
      },
      "108": {
        start: {
          line: 135,
          column: 37
        },
        end: {
          line: 135,
          column: 58
        }
      },
      "109": {
        start: {
          line: 136,
          column: 12
        },
        end: {
          line: 180,
          column: 15
        }
      },
      "110": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 179,
          column: 17
        }
      },
      "111": {
        start: {
          line: 139,
          column: 24
        },
        end: {
          line: 139,
          column: 47
        }
      },
      "112": {
        start: {
          line: 140,
          column: 24
        },
        end: {
          line: 140,
          column: 37
        }
      },
      "113": {
        start: {
          line: 142,
          column: 24
        },
        end: {
          line: 142,
          column: 50
        }
      },
      "114": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 162,
          column: 32
        }
      },
      "115": {
        start: {
          line: 164,
          column: 24
        },
        end: {
          line: 164,
          column: 47
        }
      },
      "116": {
        start: {
          line: 165,
          column: 24
        },
        end: {
          line: 165,
          column: 63
        }
      },
      "117": {
        start: {
          line: 166,
          column: 24
        },
        end: {
          line: 172,
          column: 27
        }
      },
      "118": {
        start: {
          line: 173,
          column: 24
        },
        end: {
          line: 173,
          column: 58
        }
      },
      "119": {
        start: {
          line: 175,
          column: 24
        },
        end: {
          line: 175,
          column: 44
        }
      },
      "120": {
        start: {
          line: 176,
          column: 24
        },
        end: {
          line: 176,
          column: 89
        }
      },
      "121": {
        start: {
          line: 177,
          column: 24
        },
        end: {
          line: 177,
          column: 38
        }
      },
      "122": {
        start: {
          line: 178,
          column: 28
        },
        end: {
          line: 178,
          column: 50
        }
      },
      "123": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 251,
          column: 6
        }
      },
      "124": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 250,
          column: 11
        }
      },
      "125": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 63
        }
      },
      "126": {
        start: {
          line: 190,
          column: 41
        },
        end: {
          line: 190,
          column: 61
        }
      },
      "127": {
        start: {
          line: 191,
          column: 12
        },
        end: {
          line: 249,
          column: 15
        }
      },
      "128": {
        start: {
          line: 192,
          column: 16
        },
        end: {
          line: 248,
          column: 17
        }
      },
      "129": {
        start: {
          line: 194,
          column: 24
        },
        end: {
          line: 194,
          column: 47
        }
      },
      "130": {
        start: {
          line: 195,
          column: 24
        },
        end: {
          line: 195,
          column: 37
        }
      },
      "131": {
        start: {
          line: 197,
          column: 24
        },
        end: {
          line: 197,
          column: 50
        }
      },
      "132": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 45
        }
      },
      "133": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 218,
          column: 25
        }
      },
      "134": {
        start: {
          line: 200,
          column: 28
        },
        end: {
          line: 203,
          column: 30
        }
      },
      "135": {
        start: {
          line: 205,
          column: 29
        },
        end: {
          line: 218,
          column: 25
        }
      },
      "136": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 209,
          column: 30
        }
      },
      "137": {
        start: {
          line: 212,
          column: 28
        },
        end: {
          line: 217,
          column: 30
        }
      },
      "138": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 231,
          column: 32
        }
      },
      "139": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 47
        }
      },
      "140": {
        start: {
          line: 234,
          column: 24
        },
        end: {
          line: 234,
          column: 63
        }
      },
      "141": {
        start: {
          line: 235,
          column: 24
        },
        end: {
          line: 241,
          column: 27
        }
      },
      "142": {
        start: {
          line: 242,
          column: 24
        },
        end: {
          line: 242,
          column: 58
        }
      },
      "143": {
        start: {
          line: 244,
          column: 24
        },
        end: {
          line: 244,
          column: 44
        }
      },
      "144": {
        start: {
          line: 245,
          column: 24
        },
        end: {
          line: 245,
          column: 84
        }
      },
      "145": {
        start: {
          line: 246,
          column: 24
        },
        end: {
          line: 246,
          column: 38
        }
      },
      "146": {
        start: {
          line: 247,
          column: 28
        },
        end: {
          line: 247,
          column: 50
        }
      },
      "147": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 309,
          column: 6
        }
      },
      "148": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 308,
          column: 11
        }
      },
      "149": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 259,
          column: 57
        }
      },
      "150": {
        start: {
          line: 259,
          column: 37
        },
        end: {
          line: 259,
          column: 55
        }
      },
      "151": {
        start: {
          line: 260,
          column: 12
        },
        end: {
          line: 260,
          column: 49
        }
      },
      "152": {
        start: {
          line: 260,
          column: 36
        },
        end: {
          line: 260,
          column: 47
        }
      },
      "153": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 307,
          column: 15
        }
      },
      "154": {
        start: {
          line: 262,
          column: 16
        },
        end: {
          line: 306,
          column: 17
        }
      },
      "155": {
        start: {
          line: 264,
          column: 24
        },
        end: {
          line: 264,
          column: 47
        }
      },
      "156": {
        start: {
          line: 265,
          column: 24
        },
        end: {
          line: 265,
          column: 37
        }
      },
      "157": {
        start: {
          line: 267,
          column: 24
        },
        end: {
          line: 267,
          column: 50
        }
      },
      "158": {
        start: {
          line: 268,
          column: 24
        },
        end: {
          line: 289,
          column: 32
        }
      },
      "159": {
        start: {
          line: 291,
          column: 24
        },
        end: {
          line: 291,
          column: 45
        }
      },
      "160": {
        start: {
          line: 292,
          column: 24
        },
        end: {
          line: 292,
          column: 63
        }
      },
      "161": {
        start: {
          line: 293,
          column: 24
        },
        end: {
          line: 299,
          column: 27
        }
      },
      "162": {
        start: {
          line: 300,
          column: 24
        },
        end: {
          line: 300,
          column: 56
        }
      },
      "163": {
        start: {
          line: 302,
          column: 24
        },
        end: {
          line: 302,
          column: 44
        }
      },
      "164": {
        start: {
          line: 303,
          column: 24
        },
        end: {
          line: 303,
          column: 93
        }
      },
      "165": {
        start: {
          line: 304,
          column: 24
        },
        end: {
          line: 304,
          column: 38
        }
      },
      "166": {
        start: {
          line: 305,
          column: 28
        },
        end: {
          line: 305,
          column: 50
        }
      },
      "167": {
        start: {
          line: 314,
          column: 4
        },
        end: {
          line: 370,
          column: 6
        }
      },
      "168": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 369,
          column: 11
        }
      },
      "169": {
        start: {
          line: 317,
          column: 12
        },
        end: {
          line: 317,
          column: 57
        }
      },
      "170": {
        start: {
          line: 317,
          column: 37
        },
        end: {
          line: 317,
          column: 55
        }
      },
      "171": {
        start: {
          line: 318,
          column: 12
        },
        end: {
          line: 318,
          column: 50
        }
      },
      "172": {
        start: {
          line: 318,
          column: 36
        },
        end: {
          line: 318,
          column: 48
        }
      },
      "173": {
        start: {
          line: 319,
          column: 12
        },
        end: {
          line: 368,
          column: 15
        }
      },
      "174": {
        start: {
          line: 320,
          column: 16
        },
        end: {
          line: 367,
          column: 17
        }
      },
      "175": {
        start: {
          line: 322,
          column: 24
        },
        end: {
          line: 322,
          column: 47
        }
      },
      "176": {
        start: {
          line: 323,
          column: 24
        },
        end: {
          line: 323,
          column: 37
        }
      },
      "177": {
        start: {
          line: 325,
          column: 24
        },
        end: {
          line: 325,
          column: 50
        }
      },
      "178": {
        start: {
          line: 326,
          column: 24
        },
        end: {
          line: 350,
          column: 32
        }
      },
      "179": {
        start: {
          line: 352,
          column: 24
        },
        end: {
          line: 352,
          column: 47
        }
      },
      "180": {
        start: {
          line: 353,
          column: 24
        },
        end: {
          line: 353,
          column: 63
        }
      },
      "181": {
        start: {
          line: 354,
          column: 24
        },
        end: {
          line: 360,
          column: 27
        }
      },
      "182": {
        start: {
          line: 361,
          column: 24
        },
        end: {
          line: 361,
          column: 58
        }
      },
      "183": {
        start: {
          line: 363,
          column: 24
        },
        end: {
          line: 363,
          column: 44
        }
      },
      "184": {
        start: {
          line: 364,
          column: 24
        },
        end: {
          line: 364,
          column: 88
        }
      },
      "185": {
        start: {
          line: 365,
          column: 24
        },
        end: {
          line: 365,
          column: 38
        }
      },
      "186": {
        start: {
          line: 366,
          column: 28
        },
        end: {
          line: 366,
          column: 50
        }
      },
      "187": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 384,
          column: 6
        }
      },
      "188": {
        start: {
          line: 375,
          column: 8
        },
        end: {
          line: 375,
          column: 46
        }
      },
      "189": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 379,
          column: 9
        }
      },
      "190": {
        start: {
          line: 378,
          column: 12
        },
        end: {
          line: 378,
          column: 75
        }
      },
      "191": {
        start: {
          line: 381,
          column: 8
        },
        end: {
          line: 383,
          column: 9
        }
      },
      "192": {
        start: {
          line: 382,
          column: 12
        },
        end: {
          line: 382,
          column: 122
        }
      },
      "193": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 409,
          column: 6
        }
      },
      "194": {
        start: {
          line: 389,
          column: 27
        },
        end: {
          line: 389,
          column: 57
        }
      },
      "195": {
        start: {
          line: 390,
          column: 24
        },
        end: {
          line: 390,
          column: 110
        }
      },
      "196": {
        start: {
          line: 390,
          column: 75
        },
        end: {
          line: 390,
          column: 104
        }
      },
      "197": {
        start: {
          line: 391,
          column: 35
        },
        end: {
          line: 391,
          column: 82
        }
      },
      "198": {
        start: {
          line: 392,
          column: 26
        },
        end: {
          line: 392,
          column: 111
        }
      },
      "199": {
        start: {
          line: 392,
          column: 72
        },
        end: {
          line: 392,
          column: 101
        }
      },
      "200": {
        start: {
          line: 393,
          column: 29
        },
        end: {
          line: 393,
          column: 31
        }
      },
      "201": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 402,
          column: 11
        }
      },
      "202": {
        start: {
          line: 395,
          column: 12
        },
        end: {
          line: 397,
          column: 13
        }
      },
      "203": {
        start: {
          line: 396,
          column: 16
        },
        end: {
          line: 396,
          column: 76
        }
      },
      "204": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 398,
          column: 53
        }
      },
      "205": {
        start: {
          line: 399,
          column: 12
        },
        end: {
          line: 401,
          column: 59
        }
      },
      "206": {
        start: {
          line: 403,
          column: 8
        },
        end: {
          line: 408,
          column: 10
        }
      },
      "207": {
        start: {
          line: 410,
          column: 4
        },
        end: {
          line: 410,
          column: 36
        }
      },
      "208": {
        start: {
          line: 412,
          column: 0
        },
        end: {
          line: 412,
          column: 66
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 42
          },
          end: {
            line: 7,
            column: 43
          }
        },
        loc: {
          start: {
            line: 7,
            column: 54
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 7
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 8,
            column: 33
          }
        },
        loc: {
          start: {
            line: 8,
            column: 44
          },
          end: {
            line: 15,
            column: 5
          }
        },
        line: 8
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 18,
            column: 44
          },
          end: {
            line: 18,
            column: 45
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 18
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 18
          }
        },
        loc: {
          start: {
            line: 19,
            column: 26
          },
          end: {
            line: 19,
            column: 112
          }
        },
        line: 19
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 19,
            column: 70
          },
          end: {
            line: 19,
            column: 71
          }
        },
        loc: {
          start: {
            line: 19,
            column: 89
          },
          end: {
            line: 19,
            column: 108
          }
        },
        line: 19
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 20,
            column: 36
          },
          end: {
            line: 20,
            column: 37
          }
        },
        loc: {
          start: {
            line: 20,
            column: 63
          },
          end: {
            line: 25,
            column: 5
          }
        },
        line: 20
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 26
          }
        },
        loc: {
          start: {
            line: 21,
            column: 34
          },
          end: {
            line: 21,
            column: 99
          }
        },
        line: 21
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 25
          }
        },
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 102
          }
        },
        line: 22
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 23,
            column: 17
          },
          end: {
            line: 23,
            column: 21
          }
        },
        loc: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 118
          }
        },
        line: 23
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 27,
            column: 48
          },
          end: {
            line: 27,
            column: 49
          }
        },
        loc: {
          start: {
            line: 27,
            column: 73
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 27
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 28,
            column: 30
          },
          end: {
            line: 28,
            column: 31
          }
        },
        loc: {
          start: {
            line: 28,
            column: 41
          },
          end: {
            line: 28,
            column: 83
          }
        },
        line: 28
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 29,
            column: 128
          },
          end: {
            line: 29,
            column: 129
          }
        },
        loc: {
          start: {
            line: 29,
            column: 139
          },
          end: {
            line: 29,
            column: 155
          }
        },
        line: 29
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 21
          },
          end: {
            line: 30,
            column: 70
          }
        },
        line: 30
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 30,
            column: 30
          },
          end: {
            line: 30,
            column: 31
          }
        },
        loc: {
          start: {
            line: 30,
            column: 43
          },
          end: {
            line: 30,
            column: 67
          }
        },
        line: 30
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 31,
            column: 13
          },
          end: {
            line: 31,
            column: 17
          }
        },
        loc: {
          start: {
            line: 31,
            column: 22
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 31
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 57,
            column: 46
          },
          end: {
            line: 57,
            column: 47
          }
        },
        loc: {
          start: {
            line: 57,
            column: 58
          },
          end: {
            line: 411,
            column: 1
          }
        },
        line: 57
      },
      "16": {
        name: "OptimizedDatabaseService",
        decl: {
          start: {
            line: 58,
            column: 13
          },
          end: {
            line: 58,
            column: 37
          }
        },
        loc: {
          start: {
            line: 58,
            column: 40
          },
          end: {
            line: 60,
            column: 5
          }
        },
        line: 58
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 65,
            column: 74
          },
          end: {
            line: 65,
            column: 75
          }
        },
        loc: {
          start: {
            line: 65,
            column: 94
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 65
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 66,
            column: 50
          },
          end: {
            line: 66,
            column: 51
          }
        },
        loc: {
          start: {
            line: 66,
            column: 92
          },
          end: {
            line: 126,
            column: 9
          }
        },
        line: 66
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 70,
            column: 37
          },
          end: {
            line: 70,
            column: 38
          }
        },
        loc: {
          start: {
            line: 70,
            column: 51
          },
          end: {
            line: 125,
            column: 13
          }
        },
        line: 70
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 110,
            column: 62
          },
          end: {
            line: 110,
            column: 63
          }
        },
        loc: {
          start: {
            line: 110,
            column: 84
          },
          end: {
            line: 118,
            column: 33
          }
        },
        line: 110
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 132,
            column: 70
          },
          end: {
            line: 132,
            column: 71
          }
        },
        loc: {
          start: {
            line: 132,
            column: 90
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 132
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 133,
            column: 50
          },
          end: {
            line: 133,
            column: 51
          }
        },
        loc: {
          start: {
            line: 133,
            column: 76
          },
          end: {
            line: 181,
            column: 9
          }
        },
        line: 133
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 136,
            column: 37
          },
          end: {
            line: 136,
            column: 38
          }
        },
        loc: {
          start: {
            line: 136,
            column: 51
          },
          end: {
            line: 180,
            column: 13
          }
        },
        line: 136
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 187,
            column: 65
          },
          end: {
            line: 187,
            column: 66
          }
        },
        loc: {
          start: {
            line: 187,
            column: 89
          },
          end: {
            line: 251,
            column: 5
          }
        },
        line: 187
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 188,
            column: 50
          },
          end: {
            line: 188,
            column: 51
          }
        },
        loc: {
          start: {
            line: 188,
            column: 84
          },
          end: {
            line: 250,
            column: 9
          }
        },
        line: 188
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 191,
            column: 37
          },
          end: {
            line: 191,
            column: 38
          }
        },
        loc: {
          start: {
            line: 191,
            column: 51
          },
          end: {
            line: 249,
            column: 13
          }
        },
        line: 191
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 256,
            column: 74
          },
          end: {
            line: 256,
            column: 75
          }
        },
        loc: {
          start: {
            line: 256,
            column: 94
          },
          end: {
            line: 309,
            column: 5
          }
        },
        line: 256
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 257,
            column: 50
          },
          end: {
            line: 257,
            column: 51
          }
        },
        loc: {
          start: {
            line: 257,
            column: 83
          },
          end: {
            line: 308,
            column: 9
          }
        },
        line: 257
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 261,
            column: 37
          },
          end: {
            line: 261,
            column: 38
          }
        },
        loc: {
          start: {
            line: 261,
            column: 51
          },
          end: {
            line: 307,
            column: 13
          }
        },
        line: 261
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 314,
            column: 69
          },
          end: {
            line: 314,
            column: 70
          }
        },
        loc: {
          start: {
            line: 314,
            column: 91
          },
          end: {
            line: 370,
            column: 5
          }
        },
        line: 314
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 315,
            column: 50
          },
          end: {
            line: 315,
            column: 51
          }
        },
        loc: {
          start: {
            line: 315,
            column: 85
          },
          end: {
            line: 369,
            column: 9
          }
        },
        line: 315
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 319,
            column: 37
          },
          end: {
            line: 319,
            column: 38
          }
        },
        loc: {
          start: {
            line: 319,
            column: 51
          },
          end: {
            line: 368,
            column: 13
          }
        },
        line: 319
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 374,
            column: 66
          },
          end: {
            line: 374,
            column: 67
          }
        },
        loc: {
          start: {
            line: 374,
            column: 85
          },
          end: {
            line: 384,
            column: 5
          }
        },
        line: 374
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 388,
            column: 63
          },
          end: {
            line: 388,
            column: 64
          }
        },
        loc: {
          start: {
            line: 388,
            column: 75
          },
          end: {
            line: 409,
            column: 5
          }
        },
        line: 388
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 390,
            column: 55
          },
          end: {
            line: 390,
            column: 56
          }
        },
        loc: {
          start: {
            line: 390,
            column: 73
          },
          end: {
            line: 390,
            column: 106
          }
        },
        line: 390
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 392,
            column: 57
          },
          end: {
            line: 392,
            column: 58
          }
        },
        loc: {
          start: {
            line: 392,
            column: 70
          },
          end: {
            line: 392,
            column: 103
          }
        },
        line: 392
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 394,
            column: 40
          },
          end: {
            line: 394,
            column: 41
          }
        },
        loc: {
          start: {
            line: 394,
            column: 58
          },
          end: {
            line: 402,
            column: 9
          }
        },
        line: 394
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 16
          },
          end: {
            line: 7,
            column: 20
          }
        }, {
          start: {
            line: 7,
            column: 24
          },
          end: {
            line: 7,
            column: 37
          }
        }, {
          start: {
            line: 7,
            column: 42
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 7
      },
      "1": {
        loc: {
          start: {
            line: 8,
            column: 15
          },
          end: {
            line: 15,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 15
          },
          end: {
            line: 8,
            column: 28
          }
        }, {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 15,
            column: 5
          }
        }],
        line: 8
      },
      "2": {
        loc: {
          start: {
            line: 11,
            column: 29
          },
          end: {
            line: 12,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 29
          },
          end: {
            line: 12,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "3": {
        loc: {
          start: {
            line: 18,
            column: 16
          },
          end: {
            line: 26,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        }, {
          start: {
            line: 18,
            column: 25
          },
          end: {
            line: 18,
            column: 39
          }
        }, {
          start: {
            line: 18,
            column: 44
          },
          end: {
            line: 26,
            column: 1
          }
        }],
        line: 18
      },
      "4": {
        loc: {
          start: {
            line: 19,
            column: 35
          },
          end: {
            line: 19,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 56
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 64
          },
          end: {
            line: 19,
            column: 109
          }
        }],
        line: 19
      },
      "5": {
        loc: {
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 17
          }
        }, {
          start: {
            line: 20,
            column: 22
          },
          end: {
            line: 20,
            column: 33
          }
        }],
        line: 20
      },
      "6": {
        loc: {
          start: {
            line: 23,
            column: 32
          },
          end: {
            line: 23,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 46
          },
          end: {
            line: 23,
            column: 67
          }
        }, {
          start: {
            line: 23,
            column: 70
          },
          end: {
            line: 23,
            column: 115
          }
        }],
        line: 23
      },
      "7": {
        loc: {
          start: {
            line: 24,
            column: 51
          },
          end: {
            line: 24,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 51
          },
          end: {
            line: 24,
            column: 61
          }
        }, {
          start: {
            line: 24,
            column: 65
          },
          end: {
            line: 24,
            column: 67
          }
        }],
        line: 24
      },
      "8": {
        loc: {
          start: {
            line: 27,
            column: 18
          },
          end: {
            line: 53,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 19
          },
          end: {
            line: 27,
            column: 23
          }
        }, {
          start: {
            line: 27,
            column: 27
          },
          end: {
            line: 27,
            column: 43
          }
        }, {
          start: {
            line: 27,
            column: 48
          },
          end: {
            line: 53,
            column: 1
          }
        }],
        line: 27
      },
      "9": {
        loc: {
          start: {
            line: 28,
            column: 43
          },
          end: {
            line: 28,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 43
          },
          end: {
            line: 28,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "10": {
        loc: {
          start: {
            line: 28,
            column: 134
          },
          end: {
            line: 28,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 28,
            column: 167
          },
          end: {
            line: 28,
            column: 175
          }
        }, {
          start: {
            line: 28,
            column: 178
          },
          end: {
            line: 28,
            column: 184
          }
        }],
        line: 28
      },
      "11": {
        loc: {
          start: {
            line: 29,
            column: 74
          },
          end: {
            line: 29,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 74
          },
          end: {
            line: 29,
            column: 102
          }
        }, {
          start: {
            line: 29,
            column: 107
          },
          end: {
            line: 29,
            column: 155
          }
        }],
        line: 29
      },
      "12": {
        loc: {
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "13": {
        loc: {
          start: {
            line: 33,
            column: 15
          },
          end: {
            line: 33,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 15
          },
          end: {
            line: 33,
            column: 16
          }
        }, {
          start: {
            line: 33,
            column: 21
          },
          end: {
            line: 33,
            column: 44
          }
        }],
        line: 33
      },
      "14": {
        loc: {
          start: {
            line: 33,
            column: 28
          },
          end: {
            line: 33,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 28
          },
          end: {
            line: 33,
            column: 33
          }
        }, {
          start: {
            line: 33,
            column: 38
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "15": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "16": {
        loc: {
          start: {
            line: 34,
            column: 23
          },
          end: {
            line: 34,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 23
          },
          end: {
            line: 34,
            column: 24
          }
        }, {
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 125
          }
        }, {
          start: {
            line: 34,
            column: 130
          },
          end: {
            line: 34,
            column: 158
          }
        }],
        line: 34
      },
      "17": {
        loc: {
          start: {
            line: 34,
            column: 33
          },
          end: {
            line: 34,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 45
          },
          end: {
            line: 34,
            column: 56
          }
        }, {
          start: {
            line: 34,
            column: 59
          },
          end: {
            line: 34,
            column: 125
          }
        }],
        line: 34
      },
      "18": {
        loc: {
          start: {
            line: 34,
            column: 59
          },
          end: {
            line: 34,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 116
          }
        }, {
          start: {
            line: 34,
            column: 119
          },
          end: {
            line: 34,
            column: 125
          }
        }],
        line: 34
      },
      "19": {
        loc: {
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 77
          }
        }, {
          start: {
            line: 34,
            column: 82
          },
          end: {
            line: 34,
            column: 115
          }
        }],
        line: 34
      },
      "20": {
        loc: {
          start: {
            line: 34,
            column: 82
          },
          end: {
            line: 34,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 83
          },
          end: {
            line: 34,
            column: 98
          }
        }, {
          start: {
            line: 34,
            column: 103
          },
          end: {
            line: 34,
            column: 112
          }
        }],
        line: 34
      },
      "21": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "22": {
        loc: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 48,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 23
          }
        }, {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 46
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 72
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 40,
            column: 65
          }
        }, {
          start: {
            line: 41,
            column: 16
          },
          end: {
            line: 47,
            column: 43
          }
        }],
        line: 36
      },
      "23": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "24": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 74
          }
        }, {
          start: {
            line: 42,
            column: 79
          },
          end: {
            line: 42,
            column: 90
          }
        }, {
          start: {
            line: 42,
            column: 94
          },
          end: {
            line: 42,
            column: 105
          }
        }],
        line: 42
      },
      "25": {
        loc: {
          start: {
            line: 42,
            column: 42
          },
          end: {
            line: 42,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 42
          },
          end: {
            line: 42,
            column: 54
          }
        }, {
          start: {
            line: 42,
            column: 58
          },
          end: {
            line: 42,
            column: 73
          }
        }],
        line: 42
      },
      "26": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "27": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 40
          },
          end: {
            line: 43,
            column: 42
          }
        }, {
          start: {
            line: 43,
            column: 47
          },
          end: {
            line: 43,
            column: 59
          }
        }, {
          start: {
            line: 43,
            column: 63
          },
          end: {
            line: 43,
            column: 75
          }
        }],
        line: 43
      },
      "28": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "29": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 35
          }
        }, {
          start: {
            line: 44,
            column: 39
          },
          end: {
            line: 44,
            column: 53
          }
        }],
        line: 44
      },
      "30": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "31": {
        loc: {
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 25
          }
        }, {
          start: {
            line: 45,
            column: 29
          },
          end: {
            line: 45,
            column: 43
          }
        }],
        line: 45
      },
      "32": {
        loc: {
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "33": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "34": {
        loc: {
          start: {
            line: 51,
            column: 52
          },
          end: {
            line: 51,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 51,
            column: 60
          },
          end: {
            line: 51,
            column: 65
          }
        }, {
          start: {
            line: 51,
            column: 68
          },
          end: {
            line: 51,
            column: 74
          }
        }],
        line: 51
      },
      "35": {
        loc: {
          start: {
            line: 68,
            column: 12
          },
          end: {
            line: 68,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 12
          },
          end: {
            line: 68,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "36": {
        loc: {
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 69,
            column: 72
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 69,
            column: 72
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "37": {
        loc: {
          start: {
            line: 71,
            column: 16
          },
          end: {
            line: 124,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 72,
            column: 20
          },
          end: {
            line: 74,
            column: 37
          }
        }, {
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 99,
            column: 32
          }
        }, {
          start: {
            line: 100,
            column: 20
          },
          end: {
            line: 118,
            column: 36
          }
        }, {
          start: {
            line: 119,
            column: 20
          },
          end: {
            line: 122,
            column: 38
          }
        }, {
          start: {
            line: 123,
            column: 20
          },
          end: {
            line: 123,
            column: 50
          }
        }],
        line: 71
      },
      "38": {
        loc: {
          start: {
            line: 77,
            column: 68
          },
          end: {
            line: 77,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 77,
            column: 86
          },
          end: {
            line: 77,
            column: 88
          }
        }, {
          start: {
            line: 77,
            column: 91
          },
          end: {
            line: 77,
            column: 109
          }
        }],
        line: 77
      },
      "39": {
        loc: {
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 60
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 60
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "40": {
        loc: {
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 179,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 138,
            column: 20
          },
          end: {
            line: 140,
            column: 37
          }
        }, {
          start: {
            line: 141,
            column: 20
          },
          end: {
            line: 162,
            column: 32
          }
        }, {
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 173,
            column: 58
          }
        }, {
          start: {
            line: 174,
            column: 20
          },
          end: {
            line: 177,
            column: 38
          }
        }, {
          start: {
            line: 178,
            column: 20
          },
          end: {
            line: 178,
            column: 50
          }
        }],
        line: 137
      },
      "41": {
        loc: {
          start: {
            line: 169,
            column: 42
          },
          end: {
            line: 169,
            column: 60
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 55
          },
          end: {
            line: 169,
            column: 56
          }
        }, {
          start: {
            line: 169,
            column: 59
          },
          end: {
            line: 169,
            column: 60
          }
        }],
        line: 169
      },
      "42": {
        loc: {
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 190,
            column: 63
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 190,
            column: 63
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 190
      },
      "43": {
        loc: {
          start: {
            line: 192,
            column: 16
          },
          end: {
            line: 248,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 193,
            column: 20
          },
          end: {
            line: 195,
            column: 37
          }
        }, {
          start: {
            line: 196,
            column: 20
          },
          end: {
            line: 231,
            column: 32
          }
        }, {
          start: {
            line: 232,
            column: 20
          },
          end: {
            line: 242,
            column: 58
          }
        }, {
          start: {
            line: 243,
            column: 20
          },
          end: {
            line: 246,
            column: 38
          }
        }, {
          start: {
            line: 247,
            column: 20
          },
          end: {
            line: 247,
            column: 50
          }
        }],
        line: 192
      },
      "44": {
        loc: {
          start: {
            line: 199,
            column: 24
          },
          end: {
            line: 218,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 24
          },
          end: {
            line: 218,
            column: 25
          }
        }, {
          start: {
            line: 205,
            column: 29
          },
          end: {
            line: 218,
            column: 25
          }
        }],
        line: 199
      },
      "45": {
        loc: {
          start: {
            line: 205,
            column: 29
          },
          end: {
            line: 218,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 29
          },
          end: {
            line: 218,
            column: 25
          }
        }, {
          start: {
            line: 211,
            column: 29
          },
          end: {
            line: 218,
            column: 25
          }
        }],
        line: 205
      },
      "46": {
        loc: {
          start: {
            line: 238,
            column: 42
          },
          end: {
            line: 238,
            column: 60
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 238,
            column: 55
          },
          end: {
            line: 238,
            column: 56
          }
        }, {
          start: {
            line: 238,
            column: 59
          },
          end: {
            line: 238,
            column: 60
          }
        }],
        line: 238
      },
      "47": {
        loc: {
          start: {
            line: 259,
            column: 12
          },
          end: {
            line: 259,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 12
          },
          end: {
            line: 259,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "48": {
        loc: {
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 260,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 260,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 260
      },
      "49": {
        loc: {
          start: {
            line: 262,
            column: 16
          },
          end: {
            line: 306,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 263,
            column: 20
          },
          end: {
            line: 265,
            column: 37
          }
        }, {
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 289,
            column: 32
          }
        }, {
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 300,
            column: 56
          }
        }, {
          start: {
            line: 301,
            column: 20
          },
          end: {
            line: 304,
            column: 38
          }
        }, {
          start: {
            line: 305,
            column: 20
          },
          end: {
            line: 305,
            column: 50
          }
        }],
        line: 262
      },
      "50": {
        loc: {
          start: {
            line: 317,
            column: 12
          },
          end: {
            line: 317,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 317,
            column: 12
          },
          end: {
            line: 317,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 317
      },
      "51": {
        loc: {
          start: {
            line: 318,
            column: 12
          },
          end: {
            line: 318,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 318,
            column: 12
          },
          end: {
            line: 318,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 318
      },
      "52": {
        loc: {
          start: {
            line: 320,
            column: 16
          },
          end: {
            line: 367,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 321,
            column: 20
          },
          end: {
            line: 323,
            column: 37
          }
        }, {
          start: {
            line: 324,
            column: 20
          },
          end: {
            line: 350,
            column: 32
          }
        }, {
          start: {
            line: 351,
            column: 20
          },
          end: {
            line: 361,
            column: 58
          }
        }, {
          start: {
            line: 362,
            column: 20
          },
          end: {
            line: 365,
            column: 38
          }
        }, {
          start: {
            line: 366,
            column: 20
          },
          end: {
            line: 366,
            column: 50
          }
        }],
        line: 320
      },
      "53": {
        loc: {
          start: {
            line: 377,
            column: 8
          },
          end: {
            line: 379,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 377,
            column: 8
          },
          end: {
            line: 379,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 377
      },
      "54": {
        loc: {
          start: {
            line: 381,
            column: 8
          },
          end: {
            line: 383,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 381,
            column: 8
          },
          end: {
            line: 383,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 381
      },
      "55": {
        loc: {
          start: {
            line: 391,
            column: 35
          },
          end: {
            line: 391,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 391,
            column: 54
          },
          end: {
            line: 391,
            column: 78
          }
        }, {
          start: {
            line: 391,
            column: 81
          },
          end: {
            line: 391,
            column: 82
          }
        }],
        line: 391
      },
      "56": {
        loc: {
          start: {
            line: 395,
            column: 12
          },
          end: {
            line: 397,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 395,
            column: 12
          },
          end: {
            line: 397,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 395
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0, 0, 0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0, 0, 0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0, 0, 0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0, 0, 0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0, 0, 0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/optimized-database-service.ts",
      mappings: ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAsC;AAWtC;IAAA;QACU,uBAAkB,GAA8B,EAAE,CAAC;IA0U7D,CAAC;IAxUC;;;OAGG;IACG,mEAAgC,GAAtC;4DACE,MAAc,EACd,KAAkB,EAClB,eAAgC;;YADhC,sBAAA,EAAA,UAAkB;YAClB,gCAAA,EAAA,uBAAgC;;;;wBAE1B,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAGrB,WAAW,cACf,MAAM,QAAA,IACH,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAC/C,CAAC;wBAEkB,qBAAM,eAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;gCACxD,KAAK,EAAE,WAAW;gCAClB,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,OAAO,EAAE,IAAI;oCACb,UAAU,EAAE,IAAI;oCAChB,eAAe,EAAE,IAAI;oCACrB,cAAc,EAAE,IAAI;oCACpB,cAAc,EAAE,IAAI;oCACpB,KAAK,EAAE;wCACL,MAAM,EAAE;4CACN,EAAE,EAAE,IAAI;4CACR,IAAI,EAAE,IAAI;4CACV,QAAQ,EAAE,IAAI;yCACf;qCACF;iCACF;gCACD,OAAO,EAAE;oCACP,cAAc,EAAE,MAAM;iCACvB;gCACD,IAAI,EAAE,KAAK;6BACZ,CAAC,EAAA;;wBArBI,WAAW,GAAG,SAqBlB;wBAEI,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC7C,IAAI,CAAC,wBAAwB,CAAC;4BAC5B,SAAS,EAAE,kCAAkC;4BAC7C,aAAa,eAAA;4BACb,YAAY,EAAE,WAAW,CAAC,MAAM;4BAChC,WAAW,EAAE,CAAC,gCAAgC,CAAC;4BAC/C,MAAM,EAAE,KAAK;yBACd,CAAC,CAAC;wBAEH,sBAAO,WAAW,CAAC,GAAG,CAAC,UAAA,UAAU,IAAI,OAAA,CAAC;gCACpC,OAAO,EAAE,UAAU,CAAC,OAAO;gCAC3B,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI;gCAChC,aAAa,EAAE,UAAU,CAAC,KAAK,CAAC,QAAQ;gCACxC,UAAU,EAAE,UAAU,CAAC,UAAU;gCACjC,eAAe,EAAE,UAAU,CAAC,eAAe;gCAC3C,YAAY,EAAE,UAAU,CAAC,cAAc;gCACvC,cAAc,EAAE,UAAU,CAAC,cAAc;6BAC1C,CAAC,EARmC,CAQnC,CAAC,EAAC;;;wBAEJ,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,OAAK,CAAC,CAAC;wBACnE,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;;OAGG;IACG,+DAA4B,GAAlC;4DACE,MAAc,EACd,MAAiD;;YAAjD,uBAAA,EAAA,oBAAiD;;;;wBAE3C,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAGR,qBAAM,eAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gCACnD,KAAK,EAAE;oCACL,MAAM,QAAA;oCACN,MAAM,QAAA;iCACP;gCACD,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,MAAM,EAAE,IAAI;oCACZ,WAAW,EAAE,IAAI;oCACjB,SAAS,EAAE;wCACT,MAAM,EAAE;4CACN,WAAW,EAAE,IAAI;4CACjB,WAAW,EAAE,IAAI;yCAClB;qCACF;iCACF;gCACD,OAAO,EAAE;oCACP,WAAW,EAAE,MAAM;iCACpB;6BACF,CAAC,EAAA;;wBAnBI,UAAU,GAAG,SAmBjB;wBAEI,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC7C,IAAI,CAAC,wBAAwB,CAAC;4BAC5B,SAAS,EAAE,8BAA8B;4BACzC,aAAa,eAAA;4BACb,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChC,WAAW,EAAE,CAAC,2BAA2B,CAAC;4BAC1C,MAAM,EAAE,KAAK;yBACd,CAAC,CAAC;wBAEH,sBAAO,UAAU,EAAC;;;wBAElB,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,OAAK,CAAC,CAAC;wBAC/D,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;;OAGG;IACG,0DAAuB,GAA7B;4DACE,UAAkB,EAClB,UAA6C;;YAA7C,2BAAA,EAAA,mBAA6C;;;;wBAEvC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAGvB,WAAW,SAA6B,CAAC;wBAE7C,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;4BAC1B,WAAW,GAAG;gCACZ,IAAI,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE;gCACnD,QAAQ,EAAE,IAAI;6BACf,CAAC;wBACJ,CAAC;6BAAM,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;4BACjC,WAAW,GAAG;gCACZ,IAAI,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;gCACnD,QAAQ,EAAE,IAAI;6BACf,CAAC;wBACJ,CAAC;6BAAM,CAAC;4BACN,WAAW,GAAG;gCACZ,EAAE,EAAE;oCACF,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;oCACvE,EAAE,IAAI,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;iCACxE;6BACF,CAAC;wBACJ,CAAC;wBAEkB,qBAAM,eAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gCACnD,KAAK,EAAE,WAAW;gCAClB,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,IAAI,EAAE,IAAI;oCACV,IAAI,EAAE,IAAI;oCACV,QAAQ,EAAE,IAAI;oCACd,IAAI,EAAE,IAAI;oCACV,IAAI,EAAE,IAAI;oCACV,eAAe,EAAE,IAAI;oCACrB,QAAQ,EAAE,IAAI;iCACf;6BACF,CAAC,EAAA;;wBAZI,UAAU,GAAG,SAYjB;wBAEI,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC7C,IAAI,CAAC,wBAAwB,CAAC;4BAC5B,SAAS,EAAE,yBAAyB;4BACpC,aAAa,eAAA;4BACb,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChC,WAAW,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;4BAC/C,MAAM,EAAE,KAAK;yBACd,CAAC,CAAC;wBAEH,sBAAO,UAAU,EAAC;;;wBAElB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,OAAK,CAAC,CAAC;wBAC1D,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;;OAGG;IACG,mEAAgC,GAAtC;4DACE,MAAc,EACd,MAAqD,EACrD,KAAkB;;YADlB,uBAAA,EAAA,iBAAqD;YACrD,sBAAA,EAAA,UAAkB;;;;wBAEZ,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAGV,qBAAM,eAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gCACtD,KAAK,EAAE;oCACL,MAAM,QAAA;oCACN,MAAM,QAAA;iCACP;gCACD,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,oBAAoB,EAAE,IAAI;oCAC1B,eAAe,EAAE,IAAI;oCACrB,SAAS,EAAE,IAAI;oCACf,YAAY,EAAE,IAAI;oCAClB,SAAS,EAAE,IAAI;oCACf,YAAY,EAAE,IAAI;oCAClB,oBAAoB,EAAE,IAAI;oCAC1B,WAAW,EAAE,IAAI;oCACjB,SAAS,EAAE,IAAI;iCAChB;gCACD,OAAO,EAAE;oCACP,WAAW,EAAE,MAAM;iCACpB;gCACD,IAAI,EAAE,KAAK;6BACZ,CAAC,EAAA;;wBArBI,QAAQ,GAAG,SAqBf;wBAEI,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC7C,IAAI,CAAC,wBAAwB,CAAC;4BAC5B,SAAS,EAAE,kCAAkC;4BAC7C,aAAa,eAAA;4BACb,YAAY,EAAE,QAAQ,CAAC,MAAM;4BAC7B,WAAW,EAAE,CAAC,2BAA2B,CAAC;4BAC1C,MAAM,EAAE,KAAK;yBACd,CAAC,CAAC;wBAEH,sBAAO,QAAQ,EAAC;;;wBAEhB,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,OAAK,CAAC,CAAC;wBACnE,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;;OAGG;IACG,8DAA2B,GAAjC;4DACE,QAAkB,EAClB,MAAyB,EACzB,KAAmB;;YADnB,uBAAA,EAAA,iBAAyB;YACzB,sBAAA,EAAA,WAAmB;;;;wBAEb,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAGR,qBAAM,eAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;gCACvD,KAAK,EAAE;oCACL,OAAO,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;oCACzB,MAAM,QAAA;oCACN,QAAQ,EAAE,IAAI;iCACf;gCACD,MAAM,EAAE;oCACN,OAAO,EAAE,IAAI;oCACb,WAAW,EAAE,IAAI;oCACjB,mBAAmB,EAAE,IAAI;oCACzB,gBAAgB,EAAE,IAAI;oCACtB,WAAW,EAAE,IAAI;oCACjB,QAAQ,EAAE,IAAI;oCACd,KAAK,EAAE;wCACL,MAAM,EAAE;4CACN,IAAI,EAAE,IAAI;4CACV,QAAQ,EAAE,IAAI;yCACf;qCACF;iCACF;gCACD,OAAO,EAAE;oCACP,QAAQ,EAAE,MAAM;iCACjB;gCACD,IAAI,EAAE,KAAK;6BACZ,CAAC,EAAA;;wBAxBI,UAAU,GAAG,SAwBjB;wBAEI,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC7C,IAAI,CAAC,wBAAwB,CAAC;4BAC5B,SAAS,EAAE,6BAA6B;4BACxC,aAAa,eAAA;4BACb,YAAY,EAAE,UAAU,CAAC,MAAM;4BAC/B,WAAW,EAAE,CAAC,+BAA+B,CAAC;4BAC9C,MAAM,EAAE,KAAK;yBACd,CAAC,CAAC;wBAEH,sBAAO,UAAU,EAAC;;;wBAElB,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,OAAK,CAAC,CAAC;wBAC9D,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;OAEG;IACK,2DAAwB,GAAhC,UAAiC,OAAgC;QAC/D,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtC,8BAA8B;QAC9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,+BAAwB,OAAO,CAAC,SAAS,mBAAS,OAAO,CAAC,aAAa,OAAI,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,wDAAqB,GAArB;QAME,IAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QACpD,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,aAAa,EAArB,CAAqB,EAAE,CAAC,CAAC,CAAC;QACvF,IAAM,oBAAoB,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,IAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,GAAG,GAAG,EAArB,CAAqB,CAAC,CAAC,MAAM,CAAC;QAEtF,IAAM,cAAc,GAAuD,EAAE,CAAC;QAC9E,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAA,MAAM;YACpC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;YAC9D,CAAC;YACD,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;YACzC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO;gBACtC,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC;oBAChH,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,YAAY,cAAA;YACZ,oBAAoB,sBAAA;YACpB,WAAW,aAAA;YACX,cAAc,gBAAA;SACf,CAAC;IACJ,CAAC;IACH,+BAAC;AAAD,CAAC,AA3UD,IA2UC;AAEY,QAAA,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/optimized-database-service.ts"],
      sourcesContent: ["/**\n * Optimized Database Service\n * Leverages new composite indexes for maximum query performance\n * Part of Phase 2 Database Query Optimization\n */\n\nimport { prisma } from '@/lib/prisma';\nimport { Prisma } from '@prisma/client';\n\ninterface QueryPerformanceMetrics {\n  queryName: string;\n  executionTime: number;\n  rowsReturned: number;\n  indexesUsed: string[];\n  cached: boolean;\n}\n\nclass OptimizedDatabaseService {\n  private performanceMetrics: QueryPerformanceMetrics[] = [];\n\n  /**\n   * Optimized user skill assessments query\n   * Uses composite index: [userId, isActive, assessmentDate]\n   */\n  async getUserSkillAssessmentsOptimized(\n    userId: string,\n    limit: number = 50,\n    includeInactive: boolean = false\n  ) {\n    const startTime = Date.now();\n    \n    try {\n      const whereClause: Prisma.SkillAssessmentWhereInput = {\n        userId,\n        ...(includeInactive ? {} : { isActive: true }),\n      };\n\n      const assessments = await prisma.skillAssessment.findMany({\n        where: whereClause,\n        select: {\n          id: true,\n          skillId: true,\n          selfRating: true,\n          confidenceLevel: true,\n          assessmentDate: true,\n          assessmentType: true,\n          skill: {\n            select: {\n              id: true,\n              name: true,\n              category: true,\n            },\n          },\n        },\n        orderBy: {\n          assessmentDate: 'desc',\n        },\n        take: limit,\n      });\n\n      const executionTime = Date.now() - startTime;\n      this.recordPerformanceMetrics({\n        queryName: 'getUserSkillAssessmentsOptimized',\n        executionTime,\n        rowsReturned: assessments.length,\n        indexesUsed: ['userId_isActive_assessmentDate'],\n        cached: false,\n      });\n\n      return assessments.map(assessment => ({\n        skillId: assessment.skillId,\n        skillName: assessment.skill.name,\n        skillCategory: assessment.skill.category,\n        selfRating: assessment.selfRating,\n        confidenceLevel: assessment.confidenceLevel,\n        lastAssessed: assessment.assessmentDate,\n        assessmentType: assessment.assessmentType,\n      }));\n    } catch (error) {\n      console.error('Error in getUserSkillAssessmentsOptimized:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Optimized career assessment query\n   * Uses composite index: [userId, status, completedAt]\n   */\n  async getCareerAssessmentOptimized(\n    userId: string,\n    status: 'COMPLETED' | 'IN_PROGRESS' = 'COMPLETED'\n  ) {\n    const startTime = Date.now();\n    \n    try {\n      const assessment = await prisma.assessment.findFirst({\n        where: {\n          userId,\n          status,\n        },\n        select: {\n          id: true,\n          status: true,\n          completedAt: true,\n          responses: {\n            select: {\n              questionKey: true,\n              answerValue: true,\n            },\n          },\n        },\n        orderBy: {\n          completedAt: 'desc',\n        },\n      });\n\n      const executionTime = Date.now() - startTime;\n      this.recordPerformanceMetrics({\n        queryName: 'getCareerAssessmentOptimized',\n        executionTime,\n        rowsReturned: assessment ? 1 : 0,\n        indexesUsed: ['userId_status_completedAt'],\n        cached: false,\n      });\n\n      return assessment;\n    } catch (error) {\n      console.error('Error in getCareerAssessmentOptimized:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Optimized career path search\n   * Uses composite index: [name, isActive] and [slug, isActive]\n   */\n  async findCareerPathOptimized(\n    searchTerm: string,\n    searchType: 'name' | 'slug' | 'both' = 'both'\n  ) {\n    const startTime = Date.now();\n    \n    try {\n      let whereClause: Prisma.CareerPathWhereInput;\n\n      if (searchType === 'name') {\n        whereClause = {\n          name: { contains: searchTerm, mode: 'insensitive' },\n          isActive: true,\n        };\n      } else if (searchType === 'slug') {\n        whereClause = {\n          slug: searchTerm.toLowerCase().replace(/\\s+/g, '-'),\n          isActive: true,\n        };\n      } else {\n        whereClause = {\n          OR: [\n            { name: { contains: searchTerm, mode: 'insensitive' }, isActive: true },\n            { slug: searchTerm.toLowerCase().replace(/\\s+/g, '-'), isActive: true },\n          ],\n        };\n      }\n\n      const careerPath = await prisma.careerPath.findFirst({\n        where: whereClause,\n        select: {\n          id: true,\n          name: true,\n          slug: true,\n          overview: true,\n          pros: true,\n          cons: true,\n          actionableSteps: true,\n          isActive: true,\n        },\n      });\n\n      const executionTime = Date.now() - startTime;\n      this.recordPerformanceMetrics({\n        queryName: 'findCareerPathOptimized',\n        executionTime,\n        rowsReturned: careerPath ? 1 : 0,\n        indexesUsed: ['name_isActive', 'slug_isActive'],\n        cached: false,\n      });\n\n      return careerPath;\n    } catch (error) {\n      console.error('Error in findCareerPathOptimized:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Optimized skill gap analysis query\n   * Uses composite index: [userId, status, lastUpdated]\n   */\n  async getUserSkillGapAnalysisOptimized(\n    userId: string,\n    status: 'ACTIVE' | 'COMPLETED' | 'EXPIRED' = 'ACTIVE',\n    limit: number = 10\n  ) {\n    const startTime = Date.now();\n    \n    try {\n      const analyses = await prisma.skillGapAnalysis.findMany({\n        where: {\n          userId,\n          status,\n        },\n        select: {\n          id: true,\n          targetCareerPathName: true,\n          experienceLevel: true,\n          timeframe: true,\n          analysisData: true,\n          skillGaps: true,\n          learningPlan: true,\n          completionPercentage: true,\n          lastUpdated: true,\n          createdAt: true,\n        },\n        orderBy: {\n          lastUpdated: 'desc',\n        },\n        take: limit,\n      });\n\n      const executionTime = Date.now() - startTime;\n      this.recordPerformanceMetrics({\n        queryName: 'getUserSkillGapAnalysisOptimized',\n        executionTime,\n        rowsReturned: analyses.length,\n        indexesUsed: ['userId_status_lastUpdated'],\n        cached: false,\n      });\n\n      return analyses;\n    } catch (error) {\n      console.error('Error in getUserSkillGapAnalysisOptimized:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Optimized market data query\n   * Uses composite index: [isActive, dataDate, demandLevel]\n   */\n  async getSkillMarketDataOptimized(\n    skillIds: string[],\n    region: string = 'GLOBAL',\n    limit: number = 100\n  ) {\n    const startTime = Date.now();\n    \n    try {\n      const marketData = await prisma.skillMarketData.findMany({\n        where: {\n          skillId: { in: skillIds },\n          region,\n          isActive: true,\n        },\n        select: {\n          skillId: true,\n          demandLevel: true,\n          averageSalaryImpact: true,\n          jobPostingsCount: true,\n          growthTrend: true,\n          dataDate: true,\n          skill: {\n            select: {\n              name: true,\n              category: true,\n            },\n          },\n        },\n        orderBy: {\n          dataDate: 'desc',\n        },\n        take: limit,\n      });\n\n      const executionTime = Date.now() - startTime;\n      this.recordPerformanceMetrics({\n        queryName: 'getSkillMarketDataOptimized',\n        executionTime,\n        rowsReturned: marketData.length,\n        indexesUsed: ['isActive_dataDate_demandLevel'],\n        cached: false,\n      });\n\n      return marketData;\n    } catch (error) {\n      console.error('Error in getSkillMarketDataOptimized:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Record performance metrics for monitoring\n   */\n  private recordPerformanceMetrics(metrics: QueryPerformanceMetrics): void {\n    this.performanceMetrics.push(metrics);\n    \n    // Keep only last 1000 metrics\n    if (this.performanceMetrics.length > 1000) {\n      this.performanceMetrics = this.performanceMetrics.slice(-1000);\n    }\n\n    // Log slow queries (>500ms)\n    if (metrics.executionTime > 500) {\n      console.warn(`Slow query detected: ${metrics.queryName} took ${metrics.executionTime}ms`);\n    }\n  }\n\n  /**\n   * Get performance metrics summary\n   */\n  getPerformanceMetrics(): {\n    totalQueries: number;\n    averageExecutionTime: number;\n    slowQueries: number;\n    queryBreakdown: Record<string, { count: number; avgTime: number }>;\n  } {\n    const totalQueries = this.performanceMetrics.length;\n    const totalTime = this.performanceMetrics.reduce((sum, m) => sum + m.executionTime, 0);\n    const averageExecutionTime = totalQueries > 0 ? totalTime / totalQueries : 0;\n    const slowQueries = this.performanceMetrics.filter(m => m.executionTime > 500).length;\n\n    const queryBreakdown: Record<string, { count: number; avgTime: number }> = {};\n    this.performanceMetrics.forEach(metric => {\n      if (!queryBreakdown[metric.queryName]) {\n        queryBreakdown[metric.queryName] = { count: 0, avgTime: 0 };\n      }\n      queryBreakdown[metric.queryName].count++;\n      queryBreakdown[metric.queryName].avgTime = \n        (queryBreakdown[metric.queryName].avgTime * (queryBreakdown[metric.queryName].count - 1) + metric.executionTime) / \n        queryBreakdown[metric.queryName].count;\n    });\n\n    return {\n      totalQueries,\n      averageExecutionTime,\n      slowQueries,\n      queryBreakdown,\n    };\n  }\n}\n\nexport const optimizedDatabaseService = new OptimizedDatabaseService();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6be5d2664934170e4a7a0b0a8dadc5ed3077b88d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_4u5r76xgr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_4u5r76xgr();
var __assign =
/* istanbul ignore next */
(cov_4u5r76xgr().s[0]++,
/* istanbul ignore next */
(cov_4u5r76xgr().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_4u5r76xgr().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_4u5r76xgr().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_4u5r76xgr().f[0]++;
  cov_4u5r76xgr().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_4u5r76xgr().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_4u5r76xgr().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[1]++;
    cov_4u5r76xgr().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_4u5r76xgr().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_4u5r76xgr().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_4u5r76xgr().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_4u5r76xgr().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_4u5r76xgr().b[2][0]++;
          cov_4u5r76xgr().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_4u5r76xgr().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_4u5r76xgr().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_4u5r76xgr().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_4u5r76xgr().s[11]++,
/* istanbul ignore next */
(cov_4u5r76xgr().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_4u5r76xgr().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_4u5r76xgr().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_4u5r76xgr().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[3]++;
    cov_4u5r76xgr().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[4]++;
      cov_4u5r76xgr().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_4u5r76xgr().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_4u5r76xgr().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_4u5r76xgr().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[6]++;
      cov_4u5r76xgr().s[15]++;
      try {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[7]++;
      cov_4u5r76xgr().s[18]++;
      try {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[8]++;
      cov_4u5r76xgr().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_4u5r76xgr().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_4u5r76xgr().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_4u5r76xgr().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_4u5r76xgr().s[23]++,
/* istanbul ignore next */
(cov_4u5r76xgr().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_4u5r76xgr().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_4u5r76xgr().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_4u5r76xgr().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_4u5r76xgr().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_4u5r76xgr().f[10]++;
        cov_4u5r76xgr().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_4u5r76xgr().b[9][0]++;
          cov_4u5r76xgr().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_4u5r76xgr().b[9][1]++;
        }
        cov_4u5r76xgr().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_4u5r76xgr().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_4u5r76xgr().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_4u5r76xgr().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_4u5r76xgr().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[11]++;
    cov_4u5r76xgr().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[12]++;
    cov_4u5r76xgr().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[13]++;
      cov_4u5r76xgr().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[14]++;
    cov_4u5r76xgr().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_4u5r76xgr().b[12][0]++;
      cov_4u5r76xgr().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_4u5r76xgr().b[12][1]++;
    }
    cov_4u5r76xgr().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_4u5r76xgr().s[36]++;
      try {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[18][0]++,
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[19][1]++,
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_4u5r76xgr().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_4u5r76xgr().b[15][0]++;
          cov_4u5r76xgr().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_4u5r76xgr().b[15][1]++;
        }
        cov_4u5r76xgr().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_4u5r76xgr().b[21][0]++;
          cov_4u5r76xgr().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_4u5r76xgr().b[21][1]++;
        }
        cov_4u5r76xgr().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[22][1]++;
            cov_4u5r76xgr().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[22][2]++;
            cov_4u5r76xgr().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[22][3]++;
            cov_4u5r76xgr().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_4u5r76xgr().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_4u5r76xgr().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[22][4]++;
            cov_4u5r76xgr().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[22][5]++;
            cov_4u5r76xgr().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_4u5r76xgr().b[23][0]++;
              cov_4u5r76xgr().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_4u5r76xgr().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_4u5r76xgr().b[23][1]++;
            }
            cov_4u5r76xgr().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_4u5r76xgr().b[26][0]++;
              cov_4u5r76xgr().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_4u5r76xgr().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_4u5r76xgr().b[26][1]++;
            }
            cov_4u5r76xgr().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_4u5r76xgr().b[28][0]++;
              cov_4u5r76xgr().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_4u5r76xgr().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_4u5r76xgr().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_4u5r76xgr().b[28][1]++;
            }
            cov_4u5r76xgr().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_4u5r76xgr().b[30][0]++;
              cov_4u5r76xgr().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_4u5r76xgr().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_4u5r76xgr().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_4u5r76xgr().b[30][1]++;
            }
            cov_4u5r76xgr().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_4u5r76xgr().b[32][0]++;
              cov_4u5r76xgr().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_4u5r76xgr().b[32][1]++;
            }
            cov_4u5r76xgr().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_4u5r76xgr().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_4u5r76xgr().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_4u5r76xgr().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_4u5r76xgr().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_4u5r76xgr().b[33][0]++;
      cov_4u5r76xgr().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_4u5r76xgr().b[33][1]++;
    }
    cov_4u5r76xgr().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_4u5r76xgr().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_4u5r76xgr().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_4u5r76xgr().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_4u5r76xgr().s[79]++;
exports.optimizedDatabaseService = void 0;
var prisma_1 =
/* istanbul ignore next */
(cov_4u5r76xgr().s[80]++, require("@/lib/prisma"));
var OptimizedDatabaseService =
/* istanbul ignore next */
(/** @class */cov_4u5r76xgr().s[81]++, function () {
  /* istanbul ignore next */
  cov_4u5r76xgr().f[15]++;
  function OptimizedDatabaseService() {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[16]++;
    cov_4u5r76xgr().s[82]++;
    this.performanceMetrics = [];
  }
  /**
   * Optimized user skill assessments query
   * Uses composite index: [userId, isActive, assessmentDate]
   */
  /* istanbul ignore next */
  cov_4u5r76xgr().s[83]++;
  OptimizedDatabaseService.prototype.getUserSkillAssessmentsOptimized = function (userId_1) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[17]++;
    cov_4u5r76xgr().s[84]++;
    return __awaiter(this, arguments, void 0, function (userId, limit, includeInactive) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[18]++;
      var startTime, whereClause, assessments, executionTime, error_1;
      /* istanbul ignore next */
      cov_4u5r76xgr().s[85]++;
      if (limit === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[35][0]++;
        cov_4u5r76xgr().s[86]++;
        limit = 50;
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[35][1]++;
      }
      cov_4u5r76xgr().s[87]++;
      if (includeInactive === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[36][0]++;
        cov_4u5r76xgr().s[88]++;
        includeInactive = false;
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[36][1]++;
      }
      cov_4u5r76xgr().s[89]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_4u5r76xgr().f[19]++;
        cov_4u5r76xgr().s[90]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[37][0]++;
            cov_4u5r76xgr().s[91]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[92]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[37][1]++;
            cov_4u5r76xgr().s[93]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[94]++;
            whereClause = __assign({
              userId: userId
            }, includeInactive ?
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[38][0]++, {}) :
            /* istanbul ignore next */
            (cov_4u5r76xgr().b[38][1]++, {
              isActive: true
            }));
            /* istanbul ignore next */
            cov_4u5r76xgr().s[95]++;
            return [4 /*yield*/, prisma_1.prisma.skillAssessment.findMany({
              where: whereClause,
              select: {
                id: true,
                skillId: true,
                selfRating: true,
                confidenceLevel: true,
                assessmentDate: true,
                assessmentType: true,
                skill: {
                  select: {
                    id: true,
                    name: true,
                    category: true
                  }
                }
              },
              orderBy: {
                assessmentDate: 'desc'
              },
              take: limit
            })];
          case 2:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[37][2]++;
            cov_4u5r76xgr().s[96]++;
            assessments = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[97]++;
            executionTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[98]++;
            this.recordPerformanceMetrics({
              queryName: 'getUserSkillAssessmentsOptimized',
              executionTime: executionTime,
              rowsReturned: assessments.length,
              indexesUsed: ['userId_isActive_assessmentDate'],
              cached: false
            });
            /* istanbul ignore next */
            cov_4u5r76xgr().s[99]++;
            return [2 /*return*/, assessments.map(function (assessment) {
              /* istanbul ignore next */
              cov_4u5r76xgr().f[20]++;
              cov_4u5r76xgr().s[100]++;
              return {
                skillId: assessment.skillId,
                skillName: assessment.skill.name,
                skillCategory: assessment.skill.category,
                selfRating: assessment.selfRating,
                confidenceLevel: assessment.confidenceLevel,
                lastAssessed: assessment.assessmentDate,
                assessmentType: assessment.assessmentType
              };
            })];
          case 3:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[37][3]++;
            cov_4u5r76xgr().s[101]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[102]++;
            console.error('Error in getUserSkillAssessmentsOptimized:', error_1);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[103]++;
            throw error_1;
          case 4:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[37][4]++;
            cov_4u5r76xgr().s[104]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Optimized career assessment query
   * Uses composite index: [userId, status, completedAt]
   */
  /* istanbul ignore next */
  cov_4u5r76xgr().s[105]++;
  OptimizedDatabaseService.prototype.getCareerAssessmentOptimized = function (userId_1) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[21]++;
    cov_4u5r76xgr().s[106]++;
    return __awaiter(this, arguments, void 0, function (userId, status) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[22]++;
      var startTime, assessment, executionTime, error_2;
      /* istanbul ignore next */
      cov_4u5r76xgr().s[107]++;
      if (status === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[39][0]++;
        cov_4u5r76xgr().s[108]++;
        status = 'COMPLETED';
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[39][1]++;
      }
      cov_4u5r76xgr().s[109]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_4u5r76xgr().f[23]++;
        cov_4u5r76xgr().s[110]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[40][0]++;
            cov_4u5r76xgr().s[111]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[112]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[40][1]++;
            cov_4u5r76xgr().s[113]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[114]++;
            return [4 /*yield*/, prisma_1.prisma.assessment.findFirst({
              where: {
                userId: userId,
                status: status
              },
              select: {
                id: true,
                status: true,
                completedAt: true,
                responses: {
                  select: {
                    questionKey: true,
                    answerValue: true
                  }
                }
              },
              orderBy: {
                completedAt: 'desc'
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[40][2]++;
            cov_4u5r76xgr().s[115]++;
            assessment = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[116]++;
            executionTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[117]++;
            this.recordPerformanceMetrics({
              queryName: 'getCareerAssessmentOptimized',
              executionTime: executionTime,
              rowsReturned: assessment ?
              /* istanbul ignore next */
              (cov_4u5r76xgr().b[41][0]++, 1) :
              /* istanbul ignore next */
              (cov_4u5r76xgr().b[41][1]++, 0),
              indexesUsed: ['userId_status_completedAt'],
              cached: false
            });
            /* istanbul ignore next */
            cov_4u5r76xgr().s[118]++;
            return [2 /*return*/, assessment];
          case 3:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[40][3]++;
            cov_4u5r76xgr().s[119]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[120]++;
            console.error('Error in getCareerAssessmentOptimized:', error_2);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[121]++;
            throw error_2;
          case 4:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[40][4]++;
            cov_4u5r76xgr().s[122]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Optimized career path search
   * Uses composite index: [name, isActive] and [slug, isActive]
   */
  /* istanbul ignore next */
  cov_4u5r76xgr().s[123]++;
  OptimizedDatabaseService.prototype.findCareerPathOptimized = function (searchTerm_1) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[24]++;
    cov_4u5r76xgr().s[124]++;
    return __awaiter(this, arguments, void 0, function (searchTerm, searchType) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[25]++;
      var startTime, whereClause, careerPath, executionTime, error_3;
      /* istanbul ignore next */
      cov_4u5r76xgr().s[125]++;
      if (searchType === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[42][0]++;
        cov_4u5r76xgr().s[126]++;
        searchType = 'both';
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[42][1]++;
      }
      cov_4u5r76xgr().s[127]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_4u5r76xgr().f[26]++;
        cov_4u5r76xgr().s[128]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[43][0]++;
            cov_4u5r76xgr().s[129]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[130]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[43][1]++;
            cov_4u5r76xgr().s[131]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[132]++;
            whereClause = void 0;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[133]++;
            if (searchType === 'name') {
              /* istanbul ignore next */
              cov_4u5r76xgr().b[44][0]++;
              cov_4u5r76xgr().s[134]++;
              whereClause = {
                name: {
                  contains: searchTerm,
                  mode: 'insensitive'
                },
                isActive: true
              };
            } else {
              /* istanbul ignore next */
              cov_4u5r76xgr().b[44][1]++;
              cov_4u5r76xgr().s[135]++;
              if (searchType === 'slug') {
                /* istanbul ignore next */
                cov_4u5r76xgr().b[45][0]++;
                cov_4u5r76xgr().s[136]++;
                whereClause = {
                  slug: searchTerm.toLowerCase().replace(/\s+/g, '-'),
                  isActive: true
                };
              } else {
                /* istanbul ignore next */
                cov_4u5r76xgr().b[45][1]++;
                cov_4u5r76xgr().s[137]++;
                whereClause = {
                  OR: [{
                    name: {
                      contains: searchTerm,
                      mode: 'insensitive'
                    },
                    isActive: true
                  }, {
                    slug: searchTerm.toLowerCase().replace(/\s+/g, '-'),
                    isActive: true
                  }]
                };
              }
            }
            /* istanbul ignore next */
            cov_4u5r76xgr().s[138]++;
            return [4 /*yield*/, prisma_1.prisma.careerPath.findFirst({
              where: whereClause,
              select: {
                id: true,
                name: true,
                slug: true,
                overview: true,
                pros: true,
                cons: true,
                actionableSteps: true,
                isActive: true
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[43][2]++;
            cov_4u5r76xgr().s[139]++;
            careerPath = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[140]++;
            executionTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[141]++;
            this.recordPerformanceMetrics({
              queryName: 'findCareerPathOptimized',
              executionTime: executionTime,
              rowsReturned: careerPath ?
              /* istanbul ignore next */
              (cov_4u5r76xgr().b[46][0]++, 1) :
              /* istanbul ignore next */
              (cov_4u5r76xgr().b[46][1]++, 0),
              indexesUsed: ['name_isActive', 'slug_isActive'],
              cached: false
            });
            /* istanbul ignore next */
            cov_4u5r76xgr().s[142]++;
            return [2 /*return*/, careerPath];
          case 3:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[43][3]++;
            cov_4u5r76xgr().s[143]++;
            error_3 = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[144]++;
            console.error('Error in findCareerPathOptimized:', error_3);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[145]++;
            throw error_3;
          case 4:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[43][4]++;
            cov_4u5r76xgr().s[146]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Optimized skill gap analysis query
   * Uses composite index: [userId, status, lastUpdated]
   */
  /* istanbul ignore next */
  cov_4u5r76xgr().s[147]++;
  OptimizedDatabaseService.prototype.getUserSkillGapAnalysisOptimized = function (userId_1) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[27]++;
    cov_4u5r76xgr().s[148]++;
    return __awaiter(this, arguments, void 0, function (userId, status, limit) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[28]++;
      var startTime, analyses, executionTime, error_4;
      /* istanbul ignore next */
      cov_4u5r76xgr().s[149]++;
      if (status === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[47][0]++;
        cov_4u5r76xgr().s[150]++;
        status = 'ACTIVE';
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[47][1]++;
      }
      cov_4u5r76xgr().s[151]++;
      if (limit === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[48][0]++;
        cov_4u5r76xgr().s[152]++;
        limit = 10;
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[48][1]++;
      }
      cov_4u5r76xgr().s[153]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_4u5r76xgr().f[29]++;
        cov_4u5r76xgr().s[154]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[49][0]++;
            cov_4u5r76xgr().s[155]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[156]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[49][1]++;
            cov_4u5r76xgr().s[157]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[158]++;
            return [4 /*yield*/, prisma_1.prisma.skillGapAnalysis.findMany({
              where: {
                userId: userId,
                status: status
              },
              select: {
                id: true,
                targetCareerPathName: true,
                experienceLevel: true,
                timeframe: true,
                analysisData: true,
                skillGaps: true,
                learningPlan: true,
                completionPercentage: true,
                lastUpdated: true,
                createdAt: true
              },
              orderBy: {
                lastUpdated: 'desc'
              },
              take: limit
            })];
          case 2:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[49][2]++;
            cov_4u5r76xgr().s[159]++;
            analyses = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[160]++;
            executionTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[161]++;
            this.recordPerformanceMetrics({
              queryName: 'getUserSkillGapAnalysisOptimized',
              executionTime: executionTime,
              rowsReturned: analyses.length,
              indexesUsed: ['userId_status_lastUpdated'],
              cached: false
            });
            /* istanbul ignore next */
            cov_4u5r76xgr().s[162]++;
            return [2 /*return*/, analyses];
          case 3:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[49][3]++;
            cov_4u5r76xgr().s[163]++;
            error_4 = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[164]++;
            console.error('Error in getUserSkillGapAnalysisOptimized:', error_4);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[165]++;
            throw error_4;
          case 4:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[49][4]++;
            cov_4u5r76xgr().s[166]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Optimized market data query
   * Uses composite index: [isActive, dataDate, demandLevel]
   */
  /* istanbul ignore next */
  cov_4u5r76xgr().s[167]++;
  OptimizedDatabaseService.prototype.getSkillMarketDataOptimized = function (skillIds_1) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[30]++;
    cov_4u5r76xgr().s[168]++;
    return __awaiter(this, arguments, void 0, function (skillIds, region, limit) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[31]++;
      var startTime, marketData, executionTime, error_5;
      /* istanbul ignore next */
      cov_4u5r76xgr().s[169]++;
      if (region === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[50][0]++;
        cov_4u5r76xgr().s[170]++;
        region = 'GLOBAL';
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[50][1]++;
      }
      cov_4u5r76xgr().s[171]++;
      if (limit === void 0) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[51][0]++;
        cov_4u5r76xgr().s[172]++;
        limit = 100;
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[51][1]++;
      }
      cov_4u5r76xgr().s[173]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_4u5r76xgr().f[32]++;
        cov_4u5r76xgr().s[174]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[52][0]++;
            cov_4u5r76xgr().s[175]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[176]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[52][1]++;
            cov_4u5r76xgr().s[177]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[178]++;
            return [4 /*yield*/, prisma_1.prisma.skillMarketData.findMany({
              where: {
                skillId: {
                  in: skillIds
                },
                region: region,
                isActive: true
              },
              select: {
                skillId: true,
                demandLevel: true,
                averageSalaryImpact: true,
                jobPostingsCount: true,
                growthTrend: true,
                dataDate: true,
                skill: {
                  select: {
                    name: true,
                    category: true
                  }
                }
              },
              orderBy: {
                dataDate: 'desc'
              },
              take: limit
            })];
          case 2:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[52][2]++;
            cov_4u5r76xgr().s[179]++;
            marketData = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[180]++;
            executionTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_4u5r76xgr().s[181]++;
            this.recordPerformanceMetrics({
              queryName: 'getSkillMarketDataOptimized',
              executionTime: executionTime,
              rowsReturned: marketData.length,
              indexesUsed: ['isActive_dataDate_demandLevel'],
              cached: false
            });
            /* istanbul ignore next */
            cov_4u5r76xgr().s[182]++;
            return [2 /*return*/, marketData];
          case 3:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[52][3]++;
            cov_4u5r76xgr().s[183]++;
            error_5 = _a.sent();
            /* istanbul ignore next */
            cov_4u5r76xgr().s[184]++;
            console.error('Error in getSkillMarketDataOptimized:', error_5);
            /* istanbul ignore next */
            cov_4u5r76xgr().s[185]++;
            throw error_5;
          case 4:
            /* istanbul ignore next */
            cov_4u5r76xgr().b[52][4]++;
            cov_4u5r76xgr().s[186]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Record performance metrics for monitoring
   */
  /* istanbul ignore next */
  cov_4u5r76xgr().s[187]++;
  OptimizedDatabaseService.prototype.recordPerformanceMetrics = function (metrics) {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[33]++;
    cov_4u5r76xgr().s[188]++;
    this.performanceMetrics.push(metrics);
    // Keep only last 1000 metrics
    /* istanbul ignore next */
    cov_4u5r76xgr().s[189]++;
    if (this.performanceMetrics.length > 1000) {
      /* istanbul ignore next */
      cov_4u5r76xgr().b[53][0]++;
      cov_4u5r76xgr().s[190]++;
      this.performanceMetrics = this.performanceMetrics.slice(-1000);
    } else
    /* istanbul ignore next */
    {
      cov_4u5r76xgr().b[53][1]++;
    }
    // Log slow queries (>500ms)
    cov_4u5r76xgr().s[191]++;
    if (metrics.executionTime > 500) {
      /* istanbul ignore next */
      cov_4u5r76xgr().b[54][0]++;
      cov_4u5r76xgr().s[192]++;
      console.warn("Slow query detected: ".concat(metrics.queryName, " took ").concat(metrics.executionTime, "ms"));
    } else
    /* istanbul ignore next */
    {
      cov_4u5r76xgr().b[54][1]++;
    }
  };
  /**
   * Get performance metrics summary
   */
  /* istanbul ignore next */
  cov_4u5r76xgr().s[193]++;
  OptimizedDatabaseService.prototype.getPerformanceMetrics = function () {
    /* istanbul ignore next */
    cov_4u5r76xgr().f[34]++;
    var totalQueries =
    /* istanbul ignore next */
    (cov_4u5r76xgr().s[194]++, this.performanceMetrics.length);
    var totalTime =
    /* istanbul ignore next */
    (cov_4u5r76xgr().s[195]++, this.performanceMetrics.reduce(function (sum, m) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[35]++;
      cov_4u5r76xgr().s[196]++;
      return sum + m.executionTime;
    }, 0));
    var averageExecutionTime =
    /* istanbul ignore next */
    (cov_4u5r76xgr().s[197]++, totalQueries > 0 ?
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[55][0]++, totalTime / totalQueries) :
    /* istanbul ignore next */
    (cov_4u5r76xgr().b[55][1]++, 0));
    var slowQueries =
    /* istanbul ignore next */
    (cov_4u5r76xgr().s[198]++, this.performanceMetrics.filter(function (m) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[36]++;
      cov_4u5r76xgr().s[199]++;
      return m.executionTime > 500;
    }).length);
    var queryBreakdown =
    /* istanbul ignore next */
    (cov_4u5r76xgr().s[200]++, {});
    /* istanbul ignore next */
    cov_4u5r76xgr().s[201]++;
    this.performanceMetrics.forEach(function (metric) {
      /* istanbul ignore next */
      cov_4u5r76xgr().f[37]++;
      cov_4u5r76xgr().s[202]++;
      if (!queryBreakdown[metric.queryName]) {
        /* istanbul ignore next */
        cov_4u5r76xgr().b[56][0]++;
        cov_4u5r76xgr().s[203]++;
        queryBreakdown[metric.queryName] = {
          count: 0,
          avgTime: 0
        };
      } else
      /* istanbul ignore next */
      {
        cov_4u5r76xgr().b[56][1]++;
      }
      cov_4u5r76xgr().s[204]++;
      queryBreakdown[metric.queryName].count++;
      /* istanbul ignore next */
      cov_4u5r76xgr().s[205]++;
      queryBreakdown[metric.queryName].avgTime = (queryBreakdown[metric.queryName].avgTime * (queryBreakdown[metric.queryName].count - 1) + metric.executionTime) / queryBreakdown[metric.queryName].count;
    });
    /* istanbul ignore next */
    cov_4u5r76xgr().s[206]++;
    return {
      totalQueries: totalQueries,
      averageExecutionTime: averageExecutionTime,
      slowQueries: slowQueries,
      queryBreakdown: queryBreakdown
    };
  };
  /* istanbul ignore next */
  cov_4u5r76xgr().s[207]++;
  return OptimizedDatabaseService;
}());
/* istanbul ignore next */
cov_4u5r76xgr().s[208]++;
exports.optimizedDatabaseService = new OptimizedDatabaseService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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