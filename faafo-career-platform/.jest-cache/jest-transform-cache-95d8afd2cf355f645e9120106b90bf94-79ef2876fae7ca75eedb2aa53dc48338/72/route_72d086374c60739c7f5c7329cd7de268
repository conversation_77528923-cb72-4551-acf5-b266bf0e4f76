c9220bbaa5c3ceb1e8b2be70044a8740
"use strict";

/* istanbul ignore next */
function cov_6mjfsucij() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/route.ts";
  var hash = "8fb316054540089b9fe1c85ac636cda2e23ed3eb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 54
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 15
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 34
        },
        end: {
          line: 56,
          column: 76
        }
      },
      "85": {
        start: {
          line: 57,
          column: 18
        },
        end: {
          line: 57,
          column: 44
        }
      },
      "86": {
        start: {
          line: 58,
          column: 13
        },
        end: {
          line: 58,
          column: 34
        }
      },
      "87": {
        start: {
          line: 59,
          column: 25
        },
        end: {
          line: 59,
          column: 75
        }
      },
      "88": {
        start: {
          line: 60,
          column: 32
        },
        end: {
          line: 60,
          column: 72
        }
      },
      "89": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 26
        }
      },
      "90": {
        start: {
          line: 63,
          column: 26
        },
        end: {
          line: 74,
          column: 2
        }
      },
      "91": {
        start: {
          line: 76,
          column: 0
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "92": {
        start: {
          line: 76,
          column: 99
        },
        end: {
          line: 170,
          column: 3
        }
      },
      "93": {
        start: {
          line: 77,
          column: 17
        },
        end: {
          line: 77,
          column: 26
        }
      },
      "94": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 169,
          column: 7
        }
      },
      "95": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 168,
          column: 20
        }
      },
      "96": {
        start: {
          line: 82,
          column: 29
        },
        end: {
          line: 168,
          column: 15
        }
      },
      "97": {
        start: {
          line: 84,
          column: 16
        },
        end: {
          line: 167,
          column: 19
        }
      },
      "98": {
        start: {
          line: 85,
          column: 20
        },
        end: {
          line: 166,
          column: 21
        }
      },
      "99": {
        start: {
          line: 86,
          column: 32
        },
        end: {
          line: 86,
          column: 61
        }
      },
      "100": {
        start: {
          line: 88,
          column: 28
        },
        end: {
          line: 88,
          column: 62
        }
      },
      "101": {
        start: {
          line: 89,
          column: 28
        },
        end: {
          line: 92,
          column: 36
        }
      },
      "102": {
        start: {
          line: 94,
          column: 28
        },
        end: {
          line: 94,
          column: 55
        }
      },
      "103": {
        start: {
          line: 95,
          column: 28
        },
        end: {
          line: 99,
          column: 29
        }
      },
      "104": {
        start: {
          line: 96,
          column: 32
        },
        end: {
          line: 96,
          column: 72
        }
      },
      "105": {
        start: {
          line: 97,
          column: 32
        },
        end: {
          line: 97,
          column: 84
        }
      },
      "106": {
        start: {
          line: 98,
          column: 32
        },
        end: {
          line: 98,
          column: 44
        }
      },
      "107": {
        start: {
          line: 100,
          column: 28
        },
        end: {
          line: 100,
          column: 180
        }
      },
      "108": {
        start: {
          line: 102,
          column: 28
        },
        end: {
          line: 102,
          column: 58
        }
      },
      "109": {
        start: {
          line: 103,
          column: 28
        },
        end: {
          line: 107,
          column: 29
        }
      },
      "110": {
        start: {
          line: 104,
          column: 32
        },
        end: {
          line: 104,
          column: 75
        }
      },
      "111": {
        start: {
          line: 105,
          column: 32
        },
        end: {
          line: 105,
          column: 87
        }
      },
      "112": {
        start: {
          line: 106,
          column: 32
        },
        end: {
          line: 106,
          column: 44
        }
      },
      "113": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 147,
          column: 36
        }
      },
      "114": {
        start: {
          line: 149,
          column: 28
        },
        end: {
          line: 149,
          column: 57
        }
      },
      "115": {
        start: {
          line: 150,
          column: 28
        },
        end: {
          line: 152,
          column: 29
        }
      },
      "116": {
        start: {
          line: 151,
          column: 32
        },
        end: {
          line: 151,
          column: 79
        }
      },
      "117": {
        start: {
          line: 153,
          column: 28
        },
        end: {
          line: 153,
          column: 130
        }
      },
      "118": {
        start: {
          line: 153,
          column: 98
        },
        end: {
          line: 153,
          column: 119
        }
      },
      "119": {
        start: {
          line: 154,
          column: 28
        },
        end: {
          line: 156,
          column: 36
        }
      },
      "120": {
        start: {
          line: 157,
          column: 28
        },
        end: {
          line: 161,
          column: 37
        }
      },
      "121": {
        start: {
          line: 162,
          column: 28
        },
        end: {
          line: 165,
          column: 36
        }
      },
      "122": {
        start: {
          line: 172,
          column: 0
        },
        end: {
          line: 262,
          column: 7
        }
      },
      "123": {
        start: {
          line: 172,
          column: 101
        },
        end: {
          line: 262,
          column: 3
        }
      },
      "124": {
        start: {
          line: 173,
          column: 17
        },
        end: {
          line: 173,
          column: 26
        }
      },
      "125": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 261,
          column: 7
        }
      },
      "126": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 260,
          column: 20
        }
      },
      "127": {
        start: {
          line: 175,
          column: 84
        },
        end: {
          line: 260,
          column: 15
        }
      },
      "128": {
        start: {
          line: 176,
          column: 16
        },
        end: {
          line: 259,
          column: 19
        }
      },
      "129": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 258,
          column: 32
        }
      },
      "130": {
        start: {
          line: 180,
          column: 41
        },
        end: {
          line: 258,
          column: 27
        }
      },
      "131": {
        start: {
          line: 182,
          column: 28
        },
        end: {
          line: 257,
          column: 31
        }
      },
      "132": {
        start: {
          line: 183,
          column: 32
        },
        end: {
          line: 256,
          column: 33
        }
      },
      "133": {
        start: {
          line: 184,
          column: 44
        },
        end: {
          line: 184,
          column: 73
        }
      },
      "134": {
        start: {
          line: 186,
          column: 40
        },
        end: {
          line: 186,
          column: 74
        }
      },
      "135": {
        start: {
          line: 187,
          column: 40
        },
        end: {
          line: 187,
          column: 144
        }
      },
      "136": {
        start: {
          line: 189,
          column: 40
        },
        end: {
          line: 189,
          column: 70
        }
      },
      "137": {
        start: {
          line: 190,
          column: 40
        },
        end: {
          line: 194,
          column: 41
        }
      },
      "138": {
        start: {
          line: 191,
          column: 44
        },
        end: {
          line: 191,
          column: 87
        }
      },
      "139": {
        start: {
          line: 192,
          column: 44
        },
        end: {
          line: 192,
          column: 99
        }
      },
      "140": {
        start: {
          line: 193,
          column: 44
        },
        end: {
          line: 193,
          column: 56
        }
      },
      "141": {
        start: {
          line: 195,
          column: 40
        },
        end: {
          line: 195,
          column: 77
        }
      },
      "142": {
        start: {
          line: 197,
          column: 40
        },
        end: {
          line: 197,
          column: 57
        }
      },
      "143": {
        start: {
          line: 198,
          column: 40
        },
        end: {
          line: 198,
          column: 89
        }
      },
      "144": {
        start: {
          line: 199,
          column: 40
        },
        end: {
          line: 204,
          column: 41
        }
      },
      "145": {
        start: {
          line: 200,
          column: 44
        },
        end: {
          line: 200,
          column: 86
        }
      },
      "146": {
        start: {
          line: 201,
          column: 44
        },
        end: {
          line: 201,
          column: 67
        }
      },
      "147": {
        start: {
          line: 202,
          column: 44
        },
        end: {
          line: 202,
          column: 84
        }
      },
      "148": {
        start: {
          line: 203,
          column: 44
        },
        end: {
          line: 203,
          column: 56
        }
      },
      "149": {
        start: {
          line: 205,
          column: 40
        },
        end: {
          line: 205,
          column: 69
        }
      },
      "150": {
        start: {
          line: 206,
          column: 40
        },
        end: {
          line: 206,
          column: 88
        }
      },
      "151": {
        start: {
          line: 206,
          column: 64
        },
        end: {
          line: 206,
          column: 88
        }
      },
      "152": {
        start: {
          line: 207,
          column: 40
        },
        end: {
          line: 210,
          column: 48
        }
      },
      "153": {
        start: {
          line: 212,
          column: 40
        },
        end: {
          line: 212,
          column: 67
        }
      },
      "154": {
        start: {
          line: 213,
          column: 40
        },
        end: {
          line: 220,
          column: 41
        }
      },
      "155": {
        start: {
          line: 214,
          column: 44
        },
        end: {
          line: 214,
          column: 180
        }
      },
      "156": {
        start: {
          line: 215,
          column: 44
        },
        end: {
          line: 219,
          column: 45
        }
      },
      "157": {
        start: {
          line: 216,
          column: 48
        },
        end: {
          line: 216,
          column: 94
        }
      },
      "158": {
        start: {
          line: 217,
          column: 48
        },
        end: {
          line: 217,
          column: 71
        }
      },
      "159": {
        start: {
          line: 218,
          column: 48
        },
        end: {
          line: 218,
          column: 60
        }
      },
      "160": {
        start: {
          line: 221,
          column: 40
        },
        end: {
          line: 221,
          column: 53
        }
      },
      "161": {
        start: {
          line: 222,
          column: 44
        },
        end: {
          line: 248,
          column: 44
        }
      },
      "162": {
        start: {
          line: 250,
          column: 40
        },
        end: {
          line: 250,
          column: 67
        }
      },
      "163": {
        start: {
          line: 251,
          column: 40
        },
        end: {
          line: 255,
          column: 48
        }
      },
      "164": {
        start: {
          line: 264,
          column: 0
        },
        end: {
          line: 302,
          column: 7
        }
      },
      "165": {
        start: {
          line: 264,
          column: 102
        },
        end: {
          line: 302,
          column: 3
        }
      },
      "166": {
        start: {
          line: 265,
          column: 17
        },
        end: {
          line: 265,
          column: 26
        }
      },
      "167": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 301,
          column: 7
        }
      },
      "168": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 300,
          column: 20
        }
      },
      "169": {
        start: {
          line: 267,
          column: 84
        },
        end: {
          line: 300,
          column: 15
        }
      },
      "170": {
        start: {
          line: 268,
          column: 16
        },
        end: {
          line: 299,
          column: 19
        }
      },
      "171": {
        start: {
          line: 269,
          column: 20
        },
        end: {
          line: 298,
          column: 32
        }
      },
      "172": {
        start: {
          line: 270,
          column: 38
        },
        end: {
          line: 298,
          column: 27
        }
      },
      "173": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 297,
          column: 31
        }
      },
      "174": {
        start: {
          line: 273,
          column: 32
        },
        end: {
          line: 296,
          column: 33
        }
      },
      "175": {
        start: {
          line: 274,
          column: 44
        },
        end: {
          line: 274,
          column: 73
        }
      },
      "176": {
        start: {
          line: 276,
          column: 40
        },
        end: {
          line: 276,
          column: 74
        }
      },
      "177": {
        start: {
          line: 277,
          column: 40
        },
        end: {
          line: 277,
          column: 144
        }
      },
      "178": {
        start: {
          line: 279,
          column: 40
        },
        end: {
          line: 279,
          column: 63
        }
      },
      "179": {
        start: {
          line: 280,
          column: 40
        },
        end: {
          line: 284,
          column: 41
        }
      },
      "180": {
        start: {
          line: 281,
          column: 44
        },
        end: {
          line: 281,
          column: 80
        }
      },
      "181": {
        start: {
          line: 282,
          column: 44
        },
        end: {
          line: 282,
          column: 92
        }
      },
      "182": {
        start: {
          line: 283,
          column: 44
        },
        end: {
          line: 283,
          column: 56
        }
      },
      "183": {
        start: {
          line: 286,
          column: 40
        },
        end: {
          line: 288,
          column: 48
        }
      },
      "184": {
        start: {
          line: 291,
          column: 40
        },
        end: {
          line: 291,
          column: 50
        }
      },
      "185": {
        start: {
          line: 292,
          column: 40
        },
        end: {
          line: 295,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 76,
            column: 72
          },
          end: {
            line: 76,
            column: 73
          }
        },
        loc: {
          start: {
            line: 76,
            column: 97
          },
          end: {
            line: 170,
            column: 5
          }
        },
        line: 76
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 76,
            column: 149
          },
          end: {
            line: 76,
            column: 150
          }
        },
        loc: {
          start: {
            line: 76,
            column: 172
          },
          end: {
            line: 170,
            column: 1
          }
        },
        line: 76
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 78,
            column: 29
          },
          end: {
            line: 78,
            column: 30
          }
        },
        loc: {
          start: {
            line: 78,
            column: 43
          },
          end: {
            line: 169,
            column: 5
          }
        },
        line: 78
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 82,
            column: 15
          },
          end: {
            line: 82,
            column: 16
          }
        },
        loc: {
          start: {
            line: 82,
            column: 27
          },
          end: {
            line: 168,
            column: 17
          }
        },
        line: 82
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 82,
            column: 70
          },
          end: {
            line: 82,
            column: 71
          }
        },
        loc: {
          start: {
            line: 82,
            column: 82
          },
          end: {
            line: 168,
            column: 13
          }
        },
        line: 82
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 84,
            column: 41
          },
          end: {
            line: 84,
            column: 42
          }
        },
        loc: {
          start: {
            line: 84,
            column: 55
          },
          end: {
            line: 167,
            column: 17
          }
        },
        line: 84
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 153,
            column: 83
          },
          end: {
            line: 153,
            column: 84
          }
        },
        loc: {
          start: {
            line: 153,
            column: 96
          },
          end: {
            line: 153,
            column: 121
          }
        },
        line: 153
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 172,
            column: 74
          },
          end: {
            line: 172,
            column: 75
          }
        },
        loc: {
          start: {
            line: 172,
            column: 99
          },
          end: {
            line: 262,
            column: 5
          }
        },
        line: 172
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 172,
            column: 151
          },
          end: {
            line: 172,
            column: 152
          }
        },
        loc: {
          start: {
            line: 172,
            column: 174
          },
          end: {
            line: 262,
            column: 1
          }
        },
        line: 172
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 174,
            column: 29
          },
          end: {
            line: 174,
            column: 30
          }
        },
        loc: {
          start: {
            line: 174,
            column: 43
          },
          end: {
            line: 261,
            column: 5
          }
        },
        line: 174
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 175,
            column: 70
          },
          end: {
            line: 175,
            column: 71
          }
        },
        loc: {
          start: {
            line: 175,
            column: 82
          },
          end: {
            line: 260,
            column: 17
          }
        },
        line: 175
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 175,
            column: 125
          },
          end: {
            line: 175,
            column: 126
          }
        },
        loc: {
          start: {
            line: 175,
            column: 137
          },
          end: {
            line: 260,
            column: 13
          }
        },
        line: 175
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 176,
            column: 41
          },
          end: {
            line: 176,
            column: 42
          }
        },
        loc: {
          start: {
            line: 176,
            column: 55
          },
          end: {
            line: 259,
            column: 17
          }
        },
        line: 176
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 180,
            column: 27
          },
          end: {
            line: 180,
            column: 28
          }
        },
        loc: {
          start: {
            line: 180,
            column: 39
          },
          end: {
            line: 258,
            column: 29
          }
        },
        line: 180
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 180,
            column: 82
          },
          end: {
            line: 180,
            column: 83
          }
        },
        loc: {
          start: {
            line: 180,
            column: 94
          },
          end: {
            line: 258,
            column: 25
          }
        },
        line: 180
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 182,
            column: 53
          },
          end: {
            line: 182,
            column: 54
          }
        },
        loc: {
          start: {
            line: 182,
            column: 67
          },
          end: {
            line: 257,
            column: 29
          }
        },
        line: 182
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 264,
            column: 75
          },
          end: {
            line: 264,
            column: 76
          }
        },
        loc: {
          start: {
            line: 264,
            column: 100
          },
          end: {
            line: 302,
            column: 5
          }
        },
        line: 264
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 264,
            column: 152
          },
          end: {
            line: 264,
            column: 153
          }
        },
        loc: {
          start: {
            line: 264,
            column: 175
          },
          end: {
            line: 302,
            column: 1
          }
        },
        line: 264
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 266,
            column: 29
          },
          end: {
            line: 266,
            column: 30
          }
        },
        loc: {
          start: {
            line: 266,
            column: 43
          },
          end: {
            line: 301,
            column: 5
          }
        },
        line: 266
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 267,
            column: 70
          },
          end: {
            line: 267,
            column: 71
          }
        },
        loc: {
          start: {
            line: 267,
            column: 82
          },
          end: {
            line: 300,
            column: 17
          }
        },
        line: 267
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 267,
            column: 125
          },
          end: {
            line: 267,
            column: 126
          }
        },
        loc: {
          start: {
            line: 267,
            column: 137
          },
          end: {
            line: 300,
            column: 13
          }
        },
        line: 267
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 268,
            column: 41
          },
          end: {
            line: 268,
            column: 42
          }
        },
        loc: {
          start: {
            line: 268,
            column: 55
          },
          end: {
            line: 299,
            column: 17
          }
        },
        line: 268
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 270,
            column: 24
          },
          end: {
            line: 270,
            column: 25
          }
        },
        loc: {
          start: {
            line: 270,
            column: 36
          },
          end: {
            line: 298,
            column: 29
          }
        },
        line: 270
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 270,
            column: 79
          },
          end: {
            line: 270,
            column: 80
          }
        },
        loc: {
          start: {
            line: 270,
            column: 91
          },
          end: {
            line: 298,
            column: 25
          }
        },
        line: 270
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 272,
            column: 53
          },
          end: {
            line: 272,
            column: 54
          }
        },
        loc: {
          start: {
            line: 272,
            column: 67
          },
          end: {
            line: 297,
            column: 29
          }
        },
        line: 272
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 81,
            column: 29
          },
          end: {
            line: 81,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 81,
            column: 70
          },
          end: {
            line: 81,
            column: 73
          }
        }, {
          start: {
            line: 81,
            column: 76
          },
          end: {
            line: 81,
            column: 78
          }
        }],
        line: 81
      },
      "39": {
        loc: {
          start: {
            line: 85,
            column: 20
          },
          end: {
            line: 166,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 86,
            column: 24
          },
          end: {
            line: 86,
            column: 61
          }
        }, {
          start: {
            line: 87,
            column: 24
          },
          end: {
            line: 92,
            column: 36
          }
        }, {
          start: {
            line: 93,
            column: 24
          },
          end: {
            line: 100,
            column: 180
          }
        }, {
          start: {
            line: 101,
            column: 24
          },
          end: {
            line: 147,
            column: 36
          }
        }, {
          start: {
            line: 148,
            column: 24
          },
          end: {
            line: 165,
            column: 36
          }
        }],
        line: 85
      },
      "40": {
        loc: {
          start: {
            line: 95,
            column: 28
          },
          end: {
            line: 99,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 28
          },
          end: {
            line: 99,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "41": {
        loc: {
          start: {
            line: 97,
            column: 51
          },
          end: {
            line: 97,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 97,
            column: 51
          },
          end: {
            line: 97,
            column: 76
          }
        }, {
          start: {
            line: 97,
            column: 80
          },
          end: {
            line: 97,
            column: 83
          }
        }],
        line: 97
      },
      "42": {
        loc: {
          start: {
            line: 103,
            column: 28
          },
          end: {
            line: 107,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 28
          },
          end: {
            line: 107,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "43": {
        loc: {
          start: {
            line: 105,
            column: 51
          },
          end: {
            line: 105,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 51
          },
          end: {
            line: 105,
            column: 79
          }
        }, {
          start: {
            line: 105,
            column: 83
          },
          end: {
            line: 105,
            column: 86
          }
        }],
        line: 105
      },
      "44": {
        loc: {
          start: {
            line: 150,
            column: 28
          },
          end: {
            line: 152,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 28
          },
          end: {
            line: 152,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "45": {
        loc: {
          start: {
            line: 154,
            column: 49
          },
          end: {
            line: 156,
            column: 35
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 155,
            column: 34
          },
          end: {
            line: 155,
            column: 106
          }
        }, {
          start: {
            line: 156,
            column: 34
          },
          end: {
            line: 156,
            column: 35
          }
        }],
        line: 154
      },
      "46": {
        loc: {
          start: {
            line: 179,
            column: 41
          },
          end: {
            line: 179,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 179,
            column: 82
          },
          end: {
            line: 179,
            column: 85
          }
        }, {
          start: {
            line: 179,
            column: 88
          },
          end: {
            line: 179,
            column: 90
          }
        }],
        line: 179
      },
      "47": {
        loc: {
          start: {
            line: 183,
            column: 32
          },
          end: {
            line: 256,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 184,
            column: 36
          },
          end: {
            line: 184,
            column: 73
          }
        }, {
          start: {
            line: 185,
            column: 36
          },
          end: {
            line: 187,
            column: 144
          }
        }, {
          start: {
            line: 188,
            column: 36
          },
          end: {
            line: 195,
            column: 77
          }
        }, {
          start: {
            line: 196,
            column: 36
          },
          end: {
            line: 210,
            column: 48
          }
        }, {
          start: {
            line: 211,
            column: 36
          },
          end: {
            line: 221,
            column: 53
          }
        }, {
          start: {
            line: 222,
            column: 36
          },
          end: {
            line: 248,
            column: 44
          }
        }, {
          start: {
            line: 249,
            column: 36
          },
          end: {
            line: 255,
            column: 48
          }
        }],
        line: 183
      },
      "48": {
        loc: {
          start: {
            line: 190,
            column: 40
          },
          end: {
            line: 194,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 40
          },
          end: {
            line: 194,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 190
      },
      "49": {
        loc: {
          start: {
            line: 192,
            column: 63
          },
          end: {
            line: 192,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 192,
            column: 63
          },
          end: {
            line: 192,
            column: 91
          }
        }, {
          start: {
            line: 192,
            column: 95
          },
          end: {
            line: 192,
            column: 98
          }
        }],
        line: 192
      },
      "50": {
        loc: {
          start: {
            line: 199,
            column: 40
          },
          end: {
            line: 204,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 40
          },
          end: {
            line: 204,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 199
      },
      "51": {
        loc: {
          start: {
            line: 206,
            column: 40
          },
          end: {
            line: 206,
            column: 88
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 40
          },
          end: {
            line: 206,
            column: 88
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 206
      },
      "52": {
        loc: {
          start: {
            line: 213,
            column: 40
          },
          end: {
            line: 220,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 40
          },
          end: {
            line: 220,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "53": {
        loc: {
          start: {
            line: 215,
            column: 44
          },
          end: {
            line: 219,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 44
          },
          end: {
            line: 219,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 215
      },
      "54": {
        loc: {
          start: {
            line: 224,
            column: 126
          },
          end: {
            line: 226,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 50
          },
          end: {
            line: 225,
            column: 77
          }
        }, {
          start: {
            line: 226,
            column: 50
          },
          end: {
            line: 226,
            column: 52
          }
        }],
        line: 224
      },
      "55": {
        loc: {
          start: {
            line: 273,
            column: 32
          },
          end: {
            line: 296,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 274,
            column: 36
          },
          end: {
            line: 274,
            column: 73
          }
        }, {
          start: {
            line: 275,
            column: 36
          },
          end: {
            line: 277,
            column: 144
          }
        }, {
          start: {
            line: 278,
            column: 36
          },
          end: {
            line: 288,
            column: 48
          }
        }, {
          start: {
            line: 289,
            column: 36
          },
          end: {
            line: 295,
            column: 48
          }
        }],
        line: 273
      },
      "56": {
        loc: {
          start: {
            line: 280,
            column: 40
          },
          end: {
            line: 284,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 40
          },
          end: {
            line: 284,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 280
      },
      "57": {
        loc: {
          start: {
            line: 282,
            column: 63
          },
          end: {
            line: 282,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 282,
            column: 63
          },
          end: {
            line: 282,
            column: 84
          }
        }, {
          start: {
            line: 282,
            column: 88
          },
          end: {
            line: 282,
            column: 91
          }
        }],
        line: 282
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0, 0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0, 0, 0, 0, 0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0, 0, 0],
      "56": [0, 0],
      "57": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAsC;AACtC,6EAA2E;AAC3E,6CAAgD;AAChD,mCAAgD;AAChD,4EAAqD;AACrD,yEAAsE;AACtE,2BAAwB;AAExB,yCAAyC;AACzC,IAAM,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IACnC,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC9E,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAClD,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC;QACnB,SAAS,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;QACzC,YAAY,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC5C,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QAChD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC3C,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;KAChD,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH,4CAA4C;AAC/B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAAsD;QAApD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP;gBACE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;gBACxB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,+BAA+B;aAC/F,EACD;;;;gCACwB,qBAAM,MAAM,EAAA;;4BAA1B,SAAS,GAAK,CAAA,SAAY,CAAA,UAAjB;4BAGM,qBAAM,+CAAqB,CAAC,mBAAmB,CAAC,OAAO,EAAE;oCAC9E,kBAAkB,EAAE,IAAI;oCACxB,gBAAgB,EAAE,IAAI;iCACvB,CAAC,EAAA;;4BAHI,cAAc,GAAG,SAGrB;4BAEF,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gCACtB,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gCAC7C,KAAa,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,IAAI,GAAG,CAAC;gCAC7D,MAAM,KAAK,CAAC;4BACd,CAAC;4BAGyB,qBAAM,+CAAqB,CAAC,0BAA0B,CAC9E,cAAc,CAAC,MAAO,EACtB,mBAAmB,EACnB,SAAS,CACV,EAAA;;4BAJK,iBAAiB,GAAG,SAIzB;4BAED,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;gCACzB,KAAK,GAAG,IAAI,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gCAChD,KAAa,CAAC,UAAU,GAAG,iBAAiB,CAAC,UAAU,IAAI,GAAG,CAAC;gCAChE,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEwB,qBAAM,eAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;oCAC/D,KAAK,EAAE;wCACL,EAAE,EAAE,SAAS;wCACb,MAAM,EAAE,iBAAiB,CAAC,MAAM;qCACjC;oCACD,OAAO,EAAE;wCACP,SAAS,EAAE;4CACT,OAAO,EAAE;gDACP,SAAS,EAAE;oDACT,KAAK,EAAE,EAAE,MAAM,EAAE,iBAAiB,CAAC,MAAM,EAAE;oDAC3C,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,YAAY,EAAE,IAAI;wDAClB,QAAQ,EAAE,IAAI;wDACd,YAAY,EAAE,IAAI;wDAClB,eAAe,EAAE,IAAI;wDACrB,OAAO,EAAE,IAAI;wDACb,UAAU,EAAE,IAAI;wDAChB,QAAQ,EAAE,IAAI;wDACd,WAAW,EAAE,IAAI;wDACjB,SAAS,EAAE,IAAI;wDACf,SAAS,EAAE,IAAI;wDACf,SAAS,EAAE,IAAI;qDAChB;iDACF;6CACF;4CACD,OAAO,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE;yCAClC;wCACD,SAAS,EAAE;4CACT,KAAK,EAAE,EAAE,MAAM,EAAE,iBAAiB,CAAC,MAAM,EAAE;4CAC3C,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,UAAU,EAAE,IAAI;gDAChB,WAAW,EAAE,IAAI;gDACjB,OAAO,EAAE,IAAI;gDACb,YAAY,EAAE,IAAI;6CACnB;yCACF;qCACF;iCACF,CAAC,EAAA;;4BAvCI,gBAAgB,GAAG,SAuCvB;4BAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;gCACtB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;4BACjD,CAAC;4BAGK,kBAAkB,GAAG,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,EAAb,CAAa,CAAC,CAAC,MAAM,CAAC;4BAClF,kBAAkB,GAAG,gBAAgB,CAAC,cAAc,GAAG,CAAC;gCAC5D,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC;gCAC1E,CAAC,CAAC,CAAC,CAAC;4BAEA,mBAAmB,yBACpB,gBAAgB,KACnB,QAAQ,EAAE;oCACR,SAAS,EAAE,kBAAkB;oCAC7B,KAAK,EAAE,gBAAgB,CAAC,cAAc;oCACtC,UAAU,EAAE,kBAAkB;iCAC/B,GACF,CAAC;4BAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,mBAAmB;iCAC1B,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,mCAAmC;AACtB,QAAA,KAAK,GAAG,IAAA,oDAAwB,EAAC,uFAC5C,OAAoB,EACpB,EAAsD;QAApD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP;4BACE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;4BACxB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,+BAA+B;yBAC/F,EACD;;;;4CACwB,qBAAM,MAAM,EAAA;;wCAA1B,SAAS,GAAK,CAAA,SAAY,CAAA,UAAjB;wCAGS,qBAAM,0BAAe,CAAC,qBAAqB,CACnE,OAAO,EACP,SAAS,EACT,WAAW,CACZ,EAAA;;wCAJK,iBAAiB,GAAG,SAIzB;wCAED,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;4CACzB,KAAK,GAAG,IAAI,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;4CAChD,KAAa,CAAC,UAAU,GAAG,iBAAiB,CAAC,UAAU,IAAI,GAAG,CAAC;4CAChE,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAA3B,IAAI,GAAG,SAAoB;wCAC3B,UAAU,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wCAEvD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;4CAClB,KAAK,GAAG,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;4CAC/C,KAAa,CAAC,UAAU,GAAG,GAAG,CAAC;4CAC/B,KAAa,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC;4CACjD,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEK,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;6CAG/B,UAAU,CAAC,MAAM,EAAjB,wBAAiB;wCACI,qBAAM,eAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;gDAC9D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gDACxB,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;6CACzB,CAAC,EAAA;;wCAHI,cAAc,GAAG,SAGrB;wCAEF,IAAI,cAAc,EAAE,CAAC;4CACb,oBAAoB,GAAG,0BAAe,CAAC,8BAA8B,CACzE,cAAc,CAAC,MAAM,EACrB,UAAU,CAAC,MAAM,EACjB,WAAW,CACZ,CAAC;4CAEF,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;gDAC5B,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;gDACnD,KAAa,CAAC,UAAU,GAAG,GAAG,CAAC;gDAChC,MAAM,KAAK,CAAC;4CACd,CAAC;wCACH,CAAC;;4CAIoB,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;4CAC1D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;4CACxB,IAAI,iCACC,UAAU,KACb,YAAY,EAAE,IAAI,IAAI,EAAE,KACrB,CAAC,UAAU,CAAC,MAAM,KAAK,WAAW;gDACnC,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE;gDAC7B,CAAC,CAAC,EAAE,CAAC,CACR;4CACD,OAAO,EAAE;gDACP,SAAS,EAAE;oDACT,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,YAAY,EAAE,IAAI;wDAClB,QAAQ,EAAE,IAAI;wDACd,UAAU,EAAE,IAAI;wDAChB,aAAa,EAAE,IAAI;qDACpB;oDACD,OAAO,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE;iDAClC;gDACD,SAAS,EAAE;oDACT,KAAK,EAAE,EAAE,MAAM,EAAE,iBAAiB,CAAC,MAAM,EAAE;oDAC3C,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,UAAU,EAAE,IAAI;wDAChB,WAAW,EAAE,IAAI;wDACjB,OAAO,EAAE,IAAI;qDACd;iDACF;6CACF;yCACF,CAAC,EAAA;;wCA9BI,cAAc,GAAG,SA8BrB;wCAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,cAAc;gDACpB,OAAO,EAAE,wCAAwC;6CAClD,CAAC,EAAC;;;6BACJ,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC;AAEH,oCAAoC;AACvB,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,uFAC7C,OAAoB,EACpB,EAAsD;QAApD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,8BAA8B;wBAC7E;;;;4CACwB,qBAAM,MAAM,EAAA;;wCAA1B,SAAS,GAAK,CAAA,SAAY,CAAA,UAAjB;wCAGE,qBAAM,0BAAe,CAAC,qBAAqB,CAC5D,OAAO,EACP,SAAS,EACT,WAAW,CACZ,EAAA;;wCAJK,UAAU,GAAG,SAIlB;wCAED,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;4CAClB,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;4CACzC,KAAa,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,GAAG,CAAC;4CACzD,MAAM,KAAK,CAAC;wCACd,CAAC;wCAED,+DAA+D;wCAC/D,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;gDACnC,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;6CACzB,CAAC,EAAA;;wCAHF,+DAA+D;wCAC/D,SAEE,CAAC;wCAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,OAAO,EAAE,wCAAwC;6CAClD,CAAC,EAAC;;;6BACJ,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport SessionSecurity from '@/lib/session-security';\nimport { UserValidationService } from '@/lib/user-validation-service';\nimport { z } from 'zod';\n\n// Validation schema for updating session\nconst updateSessionSchema = z.object({\n  status: z.enum(['IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED']).optional(),\n  timeSpent: z.number().min(0).optional(),\n  overallScore: z.number().min(0).max(10).optional(),\n  aiInsights: z.object({\n    strengths: z.array(z.string()).optional(),\n    improvements: z.array(z.string()).optional(),\n    overallFeedback: z.string().max(2000).optional(),\n    score: z.number().min(0).max(10).optional(),\n    recommendations: z.array(z.string()).optional(),\n  }).optional(),\n});\n\n// GET - Retrieve specific interview session\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ sessionId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    {\n      windowMs: 15 * 60 * 1000,\n      maxRequests: process.env.NODE_ENV === 'development' ? 200 : 50 // Higher limit for development\n    },\n    async () => {\n      const { sessionId } = await params;\n\n      // Enhanced user validation first\n      const userValidation = await UserValidationService.validateUserSession(request, {\n        validateUserExists: true,\n        checkAccountLock: true\n      });\n\n      if (!userValidation.isValid) {\n        const error = new Error(userValidation.error);\n        (error as any).statusCode = userValidation.statusCode || 401;\n        throw error;\n      }\n\n      // Then validate session access\n      const sessionValidation = await UserValidationService.validateUserResourceAccess(\n        userValidation.userId!,\n        'interview_session',\n        sessionId\n      );\n\n      if (!sessionValidation.isValid) {\n        const error = new Error(sessionValidation.error);\n        (error as any).statusCode = sessionValidation.statusCode || 404;\n        throw error;\n      }\n\n      const interviewSession = await prisma.interviewSession.findFirst({\n        where: {\n          id: sessionId,\n          userId: sessionValidation.userId,\n        },\n        include: {\n          questions: {\n            include: {\n              responses: {\n                where: { userId: sessionValidation.userId },\n                select: {\n                  id: true,\n                  responseText: true,\n                  audioUrl: true,\n                  responseTime: true,\n                  preparationTime: true,\n                  aiScore: true,\n                  aiAnalysis: true,\n                  feedback: true,\n                  isCompleted: true,\n                  userNotes: true,\n                  createdAt: true,\n                  updatedAt: true,\n                },\n              },\n            },\n            orderBy: { questionOrder: 'asc' },\n          },\n          responses: {\n            where: { userId: sessionValidation.userId },\n            select: {\n              id: true,\n              questionId: true,\n              isCompleted: true,\n              aiScore: true,\n              responseTime: true,\n            },\n          },\n        },\n      });\n\n      if (!interviewSession) {\n        throw new Error('Interview session not found');\n      }\n\n      // Calculate progress\n      const completedQuestions = interviewSession.responses.filter(r => r.isCompleted).length;\n      const progressPercentage = interviewSession.totalQuestions > 0\n        ? Math.round((completedQuestions / interviewSession.totalQuestions) * 100)\n        : 0;\n\n      const sessionWithProgress = {\n        ...interviewSession,\n        progress: {\n          completed: completedQuestions,\n          total: interviewSession.totalQuestions,\n          percentage: progressPercentage,\n        },\n      };\n\n      return NextResponse.json({\n        success: true,\n        data: sessionWithProgress,\n      });\n    }\n  );\n});\n\n// PATCH - Update interview session\nexport const PATCH = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ sessionId: string }> }\n) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      {\n        windowMs: 15 * 60 * 1000,\n        maxRequests: process.env.NODE_ENV === 'development' ? 100 : 20 // Higher limit for development\n      },\n      async () => {\n        const { sessionId } = await params;\n\n        // Enhanced session validation\n        const sessionValidation = await SessionSecurity.validateSessionAccess(\n          request,\n          sessionId,\n          'interview'\n        );\n\n        if (!sessionValidation.isValid) {\n          const error = new Error(sessionValidation.error);\n          (error as any).statusCode = sessionValidation.statusCode || 400;\n          throw error;\n        }\n\n        const body = await request.json();\n        const validation = updateSessionSchema.safeParse(body);\n\n        if (!validation.success) {\n          const error = new Error('Invalid request data');\n          (error as any).statusCode = 400;\n          (error as any).details = validation.error.errors;\n          throw error;\n        }\n\n        const updateData = validation.data;\n\n        // Validate status transition if status is being updated\n        if (updateData.status) {\n          const currentSession = await prisma.interviewSession.findUnique({\n            where: { id: sessionId },\n            select: { status: true }\n          });\n\n          if (currentSession) {\n            const transitionValidation = SessionSecurity.validateSessionStateTransition(\n              currentSession.status,\n              updateData.status,\n              'interview'\n            );\n\n            if (!transitionValidation.isValid) {\n              const error = new Error(transitionValidation.error);\n              (error as any).statusCode = 400;\n              throw error;\n            }\n          }\n        }\n\n        // Update session\n        const updatedSession = await prisma.interviewSession.update({\n          where: { id: sessionId },\n          data: {\n            ...updateData,\n            lastActiveAt: new Date(),\n            ...(updateData.status === 'COMPLETED'\n              ? { completedAt: new Date() }\n              : {}),\n          },\n          include: {\n            questions: {\n              select: {\n                id: true,\n                questionType: true,\n                category: true,\n                difficulty: true,\n                questionOrder: true,\n              },\n              orderBy: { questionOrder: 'asc' }\n            },\n            responses: {\n              where: { userId: sessionValidation.userId },\n              select: {\n                id: true,\n                questionId: true,\n                isCompleted: true,\n                aiScore: true,\n              },\n            },\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: updatedSession,\n          message: 'Interview session updated successfully',\n        });\n      }\n    );\n  });\n});\n\n// DELETE - Delete interview session\nexport const DELETE = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ sessionId: string }> }\n) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes\n      async () => {\n        const { sessionId } = await params;\n\n        // Enhanced session validation\n        const validation = await SessionSecurity.validateSessionAccess(\n          request,\n          sessionId,\n          'interview'\n        );\n\n        if (!validation.isValid) {\n          const error = new Error(validation.error);\n          (error as any).statusCode = validation.statusCode || 400;\n          throw error;\n        }\n\n        // Delete session (cascade will handle questions and responses)\n        await prisma.interviewSession.delete({\n          where: { id: sessionId },\n        });\n\n        return NextResponse.json({\n          success: true,\n          message: 'Interview session deleted successfully',\n        });\n      }\n    );\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8fb316054540089b9fe1c85ac636cda2e23ed3eb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_6mjfsucij = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_6mjfsucij();
var __assign =
/* istanbul ignore next */
(cov_6mjfsucij().s[0]++,
/* istanbul ignore next */
(cov_6mjfsucij().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_6mjfsucij().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_6mjfsucij().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_6mjfsucij().f[0]++;
  cov_6mjfsucij().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_6mjfsucij().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_6mjfsucij().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[1]++;
    cov_6mjfsucij().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_6mjfsucij().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_6mjfsucij().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_6mjfsucij().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_6mjfsucij().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_6mjfsucij().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_6mjfsucij().b[2][0]++;
          cov_6mjfsucij().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_6mjfsucij().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_6mjfsucij().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_6mjfsucij().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_6mjfsucij().s[11]++,
/* istanbul ignore next */
(cov_6mjfsucij().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_6mjfsucij().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_6mjfsucij().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_6mjfsucij().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[3]++;
    cov_6mjfsucij().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_6mjfsucij().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_6mjfsucij().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[4]++;
      cov_6mjfsucij().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_6mjfsucij().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_6mjfsucij().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_6mjfsucij().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[6]++;
      cov_6mjfsucij().s[15]++;
      try {
        /* istanbul ignore next */
        cov_6mjfsucij().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_6mjfsucij().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[7]++;
      cov_6mjfsucij().s[18]++;
      try {
        /* istanbul ignore next */
        cov_6mjfsucij().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_6mjfsucij().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[8]++;
      cov_6mjfsucij().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_6mjfsucij().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_6mjfsucij().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_6mjfsucij().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_6mjfsucij().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_6mjfsucij().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_6mjfsucij().s[23]++,
/* istanbul ignore next */
(cov_6mjfsucij().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_6mjfsucij().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_6mjfsucij().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_6mjfsucij().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_6mjfsucij().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_6mjfsucij().f[10]++;
        cov_6mjfsucij().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_6mjfsucij().b[9][0]++;
          cov_6mjfsucij().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_6mjfsucij().b[9][1]++;
        }
        cov_6mjfsucij().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_6mjfsucij().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_6mjfsucij().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_6mjfsucij().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_6mjfsucij().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_6mjfsucij().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_6mjfsucij().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_6mjfsucij().f[11]++;
    cov_6mjfsucij().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[12]++;
    cov_6mjfsucij().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[13]++;
      cov_6mjfsucij().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[14]++;
    cov_6mjfsucij().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_6mjfsucij().b[12][0]++;
      cov_6mjfsucij().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_6mjfsucij().b[12][1]++;
    }
    cov_6mjfsucij().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_6mjfsucij().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_6mjfsucij().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_6mjfsucij().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_6mjfsucij().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_6mjfsucij().s[36]++;
      try {
        /* istanbul ignore next */
        cov_6mjfsucij().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_6mjfsucij().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_6mjfsucij().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_6mjfsucij().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_6mjfsucij().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_6mjfsucij().b[18][0]++,
        /* istanbul ignore next */
        (cov_6mjfsucij().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_6mjfsucij().b[19][1]++,
        /* istanbul ignore next */
        (cov_6mjfsucij().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_6mjfsucij().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_6mjfsucij().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_6mjfsucij().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_6mjfsucij().b[15][0]++;
          cov_6mjfsucij().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_6mjfsucij().b[15][1]++;
        }
        cov_6mjfsucij().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_6mjfsucij().b[21][0]++;
          cov_6mjfsucij().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_6mjfsucij().b[21][1]++;
        }
        cov_6mjfsucij().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_6mjfsucij().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_6mjfsucij().b[22][1]++;
            cov_6mjfsucij().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_6mjfsucij().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_6mjfsucij().b[22][2]++;
            cov_6mjfsucij().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_6mjfsucij().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_6mjfsucij().b[22][3]++;
            cov_6mjfsucij().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_6mjfsucij().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_6mjfsucij().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_6mjfsucij().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_6mjfsucij().b[22][4]++;
            cov_6mjfsucij().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_6mjfsucij().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_6mjfsucij().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_6mjfsucij().b[22][5]++;
            cov_6mjfsucij().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_6mjfsucij().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_6mjfsucij().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_6mjfsucij().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_6mjfsucij().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_6mjfsucij().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_6mjfsucij().b[23][0]++;
              cov_6mjfsucij().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_6mjfsucij().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_6mjfsucij().b[23][1]++;
            }
            cov_6mjfsucij().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_6mjfsucij().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_6mjfsucij().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_6mjfsucij().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_6mjfsucij().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_6mjfsucij().b[26][0]++;
              cov_6mjfsucij().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_6mjfsucij().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_6mjfsucij().b[26][1]++;
            }
            cov_6mjfsucij().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_6mjfsucij().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_6mjfsucij().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_6mjfsucij().b[28][0]++;
              cov_6mjfsucij().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_6mjfsucij().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_6mjfsucij().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_6mjfsucij().b[28][1]++;
            }
            cov_6mjfsucij().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_6mjfsucij().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_6mjfsucij().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_6mjfsucij().b[30][0]++;
              cov_6mjfsucij().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_6mjfsucij().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_6mjfsucij().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_6mjfsucij().b[30][1]++;
            }
            cov_6mjfsucij().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_6mjfsucij().b[32][0]++;
              cov_6mjfsucij().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_6mjfsucij().b[32][1]++;
            }
            cov_6mjfsucij().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_6mjfsucij().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_6mjfsucij().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_6mjfsucij().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_6mjfsucij().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_6mjfsucij().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_6mjfsucij().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_6mjfsucij().b[33][0]++;
      cov_6mjfsucij().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_6mjfsucij().b[33][1]++;
    }
    cov_6mjfsucij().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_6mjfsucij().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_6mjfsucij().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_6mjfsucij().s[78]++,
/* istanbul ignore next */
(cov_6mjfsucij().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_6mjfsucij().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_6mjfsucij().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_6mjfsucij().f[15]++;
  cov_6mjfsucij().s[79]++;
  return /* istanbul ignore next */(cov_6mjfsucij().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_6mjfsucij().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_6mjfsucij().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_6mjfsucij().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_6mjfsucij().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_6mjfsucij().s[81]++;
exports.DELETE = exports.PATCH = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[82]++, require("next/server"));
var prisma_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[83]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[84]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[85]++, require("@/lib/rateLimit"));
var csrf_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[86]++, require("@/lib/csrf"));
var session_security_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[87]++, __importDefault(require("@/lib/session-security")));
var user_validation_service_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[88]++, require("@/lib/user-validation-service"));
var zod_1 =
/* istanbul ignore next */
(cov_6mjfsucij().s[89]++, require("zod"));
// Validation schema for updating session
var updateSessionSchema =
/* istanbul ignore next */
(cov_6mjfsucij().s[90]++, zod_1.z.object({
  status: zod_1.z.enum(['IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED']).optional(),
  timeSpent: zod_1.z.number().min(0).optional(),
  overallScore: zod_1.z.number().min(0).max(10).optional(),
  aiInsights: zod_1.z.object({
    strengths: zod_1.z.array(zod_1.z.string()).optional(),
    improvements: zod_1.z.array(zod_1.z.string()).optional(),
    overallFeedback: zod_1.z.string().max(2000).optional(),
    score: zod_1.z.number().min(0).max(10).optional(),
    recommendations: zod_1.z.array(zod_1.z.string()).optional()
  }).optional()
}));
// GET - Retrieve specific interview session
/* istanbul ignore next */
cov_6mjfsucij().s[91]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_6mjfsucij().f[16]++;
  cov_6mjfsucij().s[92]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[17]++;
    var params =
    /* istanbul ignore next */
    (cov_6mjfsucij().s[93]++, _b.params);
    /* istanbul ignore next */
    cov_6mjfsucij().s[94]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[18]++;
      cov_6mjfsucij().s[95]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_6mjfsucij().b[38][0]++, 200) :
        /* istanbul ignore next */
        (cov_6mjfsucij().b[38][1]++, 50) // Higher limit for development
      }, function () {
        /* istanbul ignore next */
        cov_6mjfsucij().f[19]++;
        cov_6mjfsucij().s[96]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_6mjfsucij().f[20]++;
          var sessionId, userValidation, error, sessionValidation, error, interviewSession, completedQuestions, progressPercentage, sessionWithProgress;
          /* istanbul ignore next */
          cov_6mjfsucij().s[97]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_6mjfsucij().f[21]++;
            cov_6mjfsucij().s[98]++;
            switch (_a.label) {
              case 0:
                /* istanbul ignore next */
                cov_6mjfsucij().b[39][0]++;
                cov_6mjfsucij().s[99]++;
                return [4 /*yield*/, params];
              case 1:
                /* istanbul ignore next */
                cov_6mjfsucij().b[39][1]++;
                cov_6mjfsucij().s[100]++;
                sessionId = _a.sent().sessionId;
                /* istanbul ignore next */
                cov_6mjfsucij().s[101]++;
                return [4 /*yield*/, user_validation_service_1.UserValidationService.validateUserSession(request, {
                  validateUserExists: true,
                  checkAccountLock: true
                })];
              case 2:
                /* istanbul ignore next */
                cov_6mjfsucij().b[39][2]++;
                cov_6mjfsucij().s[102]++;
                userValidation = _a.sent();
                /* istanbul ignore next */
                cov_6mjfsucij().s[103]++;
                if (!userValidation.isValid) {
                  /* istanbul ignore next */
                  cov_6mjfsucij().b[40][0]++;
                  cov_6mjfsucij().s[104]++;
                  error = new Error(userValidation.error);
                  /* istanbul ignore next */
                  cov_6mjfsucij().s[105]++;
                  error.statusCode =
                  /* istanbul ignore next */
                  (cov_6mjfsucij().b[41][0]++, userValidation.statusCode) ||
                  /* istanbul ignore next */
                  (cov_6mjfsucij().b[41][1]++, 401);
                  /* istanbul ignore next */
                  cov_6mjfsucij().s[106]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_6mjfsucij().b[40][1]++;
                }
                cov_6mjfsucij().s[107]++;
                return [4 /*yield*/, user_validation_service_1.UserValidationService.validateUserResourceAccess(userValidation.userId, 'interview_session', sessionId)];
              case 3:
                /* istanbul ignore next */
                cov_6mjfsucij().b[39][3]++;
                cov_6mjfsucij().s[108]++;
                sessionValidation = _a.sent();
                /* istanbul ignore next */
                cov_6mjfsucij().s[109]++;
                if (!sessionValidation.isValid) {
                  /* istanbul ignore next */
                  cov_6mjfsucij().b[42][0]++;
                  cov_6mjfsucij().s[110]++;
                  error = new Error(sessionValidation.error);
                  /* istanbul ignore next */
                  cov_6mjfsucij().s[111]++;
                  error.statusCode =
                  /* istanbul ignore next */
                  (cov_6mjfsucij().b[43][0]++, sessionValidation.statusCode) ||
                  /* istanbul ignore next */
                  (cov_6mjfsucij().b[43][1]++, 404);
                  /* istanbul ignore next */
                  cov_6mjfsucij().s[112]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_6mjfsucij().b[42][1]++;
                }
                cov_6mjfsucij().s[113]++;
                return [4 /*yield*/, prisma_1.prisma.interviewSession.findFirst({
                  where: {
                    id: sessionId,
                    userId: sessionValidation.userId
                  },
                  include: {
                    questions: {
                      include: {
                        responses: {
                          where: {
                            userId: sessionValidation.userId
                          },
                          select: {
                            id: true,
                            responseText: true,
                            audioUrl: true,
                            responseTime: true,
                            preparationTime: true,
                            aiScore: true,
                            aiAnalysis: true,
                            feedback: true,
                            isCompleted: true,
                            userNotes: true,
                            createdAt: true,
                            updatedAt: true
                          }
                        }
                      },
                      orderBy: {
                        questionOrder: 'asc'
                      }
                    },
                    responses: {
                      where: {
                        userId: sessionValidation.userId
                      },
                      select: {
                        id: true,
                        questionId: true,
                        isCompleted: true,
                        aiScore: true,
                        responseTime: true
                      }
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_6mjfsucij().b[39][4]++;
                cov_6mjfsucij().s[114]++;
                interviewSession = _a.sent();
                /* istanbul ignore next */
                cov_6mjfsucij().s[115]++;
                if (!interviewSession) {
                  /* istanbul ignore next */
                  cov_6mjfsucij().b[44][0]++;
                  cov_6mjfsucij().s[116]++;
                  throw new Error('Interview session not found');
                } else
                /* istanbul ignore next */
                {
                  cov_6mjfsucij().b[44][1]++;
                }
                cov_6mjfsucij().s[117]++;
                completedQuestions = interviewSession.responses.filter(function (r) {
                  /* istanbul ignore next */
                  cov_6mjfsucij().f[22]++;
                  cov_6mjfsucij().s[118]++;
                  return r.isCompleted;
                }).length;
                /* istanbul ignore next */
                cov_6mjfsucij().s[119]++;
                progressPercentage = interviewSession.totalQuestions > 0 ?
                /* istanbul ignore next */
                (cov_6mjfsucij().b[45][0]++, Math.round(completedQuestions / interviewSession.totalQuestions * 100)) :
                /* istanbul ignore next */
                (cov_6mjfsucij().b[45][1]++, 0);
                /* istanbul ignore next */
                cov_6mjfsucij().s[120]++;
                sessionWithProgress = __assign(__assign({}, interviewSession), {
                  progress: {
                    completed: completedQuestions,
                    total: interviewSession.totalQuestions,
                    percentage: progressPercentage
                  }
                });
                /* istanbul ignore next */
                cov_6mjfsucij().s[121]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: sessionWithProgress
                })];
            }
          });
        });
      })];
    });
  });
});
// PATCH - Update interview session
/* istanbul ignore next */
cov_6mjfsucij().s[122]++;
exports.PATCH = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_6mjfsucij().f[23]++;
  cov_6mjfsucij().s[123]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[24]++;
    var params =
    /* istanbul ignore next */
    (cov_6mjfsucij().s[124]++, _b.params);
    /* istanbul ignore next */
    cov_6mjfsucij().s[125]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[25]++;
      cov_6mjfsucij().s[126]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_6mjfsucij().f[26]++;
        cov_6mjfsucij().s[127]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_6mjfsucij().f[27]++;
          cov_6mjfsucij().s[128]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_6mjfsucij().f[28]++;
            cov_6mjfsucij().s[129]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: process.env.NODE_ENV === 'development' ?
              /* istanbul ignore next */
              (cov_6mjfsucij().b[46][0]++, 100) :
              /* istanbul ignore next */
              (cov_6mjfsucij().b[46][1]++, 20) // Higher limit for development
            }, function () {
              /* istanbul ignore next */
              cov_6mjfsucij().f[29]++;
              cov_6mjfsucij().s[130]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_6mjfsucij().f[30]++;
                var sessionId, sessionValidation, error, body, validation, error, updateData, currentSession, transitionValidation, error, updatedSession;
                /* istanbul ignore next */
                cov_6mjfsucij().s[131]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_6mjfsucij().f[31]++;
                  cov_6mjfsucij().s[132]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[47][0]++;
                      cov_6mjfsucij().s[133]++;
                      return [4 /*yield*/, params];
                    case 1:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[47][1]++;
                      cov_6mjfsucij().s[134]++;
                      sessionId = _a.sent().sessionId;
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[135]++;
                      return [4 /*yield*/, session_security_1.default.validateSessionAccess(request, sessionId, 'interview')];
                    case 2:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[47][2]++;
                      cov_6mjfsucij().s[136]++;
                      sessionValidation = _a.sent();
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[137]++;
                      if (!sessionValidation.isValid) {
                        /* istanbul ignore next */
                        cov_6mjfsucij().b[48][0]++;
                        cov_6mjfsucij().s[138]++;
                        error = new Error(sessionValidation.error);
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[139]++;
                        error.statusCode =
                        /* istanbul ignore next */
                        (cov_6mjfsucij().b[49][0]++, sessionValidation.statusCode) ||
                        /* istanbul ignore next */
                        (cov_6mjfsucij().b[49][1]++, 400);
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[140]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_6mjfsucij().b[48][1]++;
                      }
                      cov_6mjfsucij().s[141]++;
                      return [4 /*yield*/, request.json()];
                    case 3:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[47][3]++;
                      cov_6mjfsucij().s[142]++;
                      body = _a.sent();
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[143]++;
                      validation = updateSessionSchema.safeParse(body);
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[144]++;
                      if (!validation.success) {
                        /* istanbul ignore next */
                        cov_6mjfsucij().b[50][0]++;
                        cov_6mjfsucij().s[145]++;
                        error = new Error('Invalid request data');
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[146]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[147]++;
                        error.details = validation.error.errors;
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[148]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_6mjfsucij().b[50][1]++;
                      }
                      cov_6mjfsucij().s[149]++;
                      updateData = validation.data;
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[150]++;
                      if (!updateData.status) {
                        /* istanbul ignore next */
                        cov_6mjfsucij().b[51][0]++;
                        cov_6mjfsucij().s[151]++;
                        return [3 /*break*/, 5];
                      } else
                      /* istanbul ignore next */
                      {
                        cov_6mjfsucij().b[51][1]++;
                      }
                      cov_6mjfsucij().s[152]++;
                      return [4 /*yield*/, prisma_1.prisma.interviewSession.findUnique({
                        where: {
                          id: sessionId
                        },
                        select: {
                          status: true
                        }
                      })];
                    case 4:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[47][4]++;
                      cov_6mjfsucij().s[153]++;
                      currentSession = _a.sent();
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[154]++;
                      if (currentSession) {
                        /* istanbul ignore next */
                        cov_6mjfsucij().b[52][0]++;
                        cov_6mjfsucij().s[155]++;
                        transitionValidation = session_security_1.default.validateSessionStateTransition(currentSession.status, updateData.status, 'interview');
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[156]++;
                        if (!transitionValidation.isValid) {
                          /* istanbul ignore next */
                          cov_6mjfsucij().b[53][0]++;
                          cov_6mjfsucij().s[157]++;
                          error = new Error(transitionValidation.error);
                          /* istanbul ignore next */
                          cov_6mjfsucij().s[158]++;
                          error.statusCode = 400;
                          /* istanbul ignore next */
                          cov_6mjfsucij().s[159]++;
                          throw error;
                        } else
                        /* istanbul ignore next */
                        {
                          cov_6mjfsucij().b[53][1]++;
                        }
                      } else
                      /* istanbul ignore next */
                      {
                        cov_6mjfsucij().b[52][1]++;
                      }
                      cov_6mjfsucij().s[160]++;
                      _a.label = 5;
                    case 5:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[47][5]++;
                      cov_6mjfsucij().s[161]++;
                      return [4 /*yield*/, prisma_1.prisma.interviewSession.update({
                        where: {
                          id: sessionId
                        },
                        data: __assign(__assign(__assign({}, updateData), {
                          lastActiveAt: new Date()
                        }), updateData.status === 'COMPLETED' ?
                        /* istanbul ignore next */
                        (cov_6mjfsucij().b[54][0]++, {
                          completedAt: new Date()
                        }) :
                        /* istanbul ignore next */
                        (cov_6mjfsucij().b[54][1]++, {})),
                        include: {
                          questions: {
                            select: {
                              id: true,
                              questionType: true,
                              category: true,
                              difficulty: true,
                              questionOrder: true
                            },
                            orderBy: {
                              questionOrder: 'asc'
                            }
                          },
                          responses: {
                            where: {
                              userId: sessionValidation.userId
                            },
                            select: {
                              id: true,
                              questionId: true,
                              isCompleted: true,
                              aiScore: true
                            }
                          }
                        }
                      })];
                    case 6:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[47][6]++;
                      cov_6mjfsucij().s[162]++;
                      updatedSession = _a.sent();
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[163]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: updatedSession,
                        message: 'Interview session updated successfully'
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
// DELETE - Delete interview session
/* istanbul ignore next */
cov_6mjfsucij().s[164]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_6mjfsucij().f[32]++;
  cov_6mjfsucij().s[165]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_6mjfsucij().f[33]++;
    var params =
    /* istanbul ignore next */
    (cov_6mjfsucij().s[166]++, _b.params);
    /* istanbul ignore next */
    cov_6mjfsucij().s[167]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_6mjfsucij().f[34]++;
      cov_6mjfsucij().s[168]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_6mjfsucij().f[35]++;
        cov_6mjfsucij().s[169]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_6mjfsucij().f[36]++;
          cov_6mjfsucij().s[170]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_6mjfsucij().f[37]++;
            cov_6mjfsucij().s[171]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 10
            },
            // 10 deletions per 15 minutes
            function () {
              /* istanbul ignore next */
              cov_6mjfsucij().f[38]++;
              cov_6mjfsucij().s[172]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_6mjfsucij().f[39]++;
                var sessionId, validation, error;
                /* istanbul ignore next */
                cov_6mjfsucij().s[173]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_6mjfsucij().f[40]++;
                  cov_6mjfsucij().s[174]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[55][0]++;
                      cov_6mjfsucij().s[175]++;
                      return [4 /*yield*/, params];
                    case 1:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[55][1]++;
                      cov_6mjfsucij().s[176]++;
                      sessionId = _a.sent().sessionId;
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[177]++;
                      return [4 /*yield*/, session_security_1.default.validateSessionAccess(request, sessionId, 'interview')];
                    case 2:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[55][2]++;
                      cov_6mjfsucij().s[178]++;
                      validation = _a.sent();
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[179]++;
                      if (!validation.isValid) {
                        /* istanbul ignore next */
                        cov_6mjfsucij().b[56][0]++;
                        cov_6mjfsucij().s[180]++;
                        error = new Error(validation.error);
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[181]++;
                        error.statusCode =
                        /* istanbul ignore next */
                        (cov_6mjfsucij().b[57][0]++, validation.statusCode) ||
                        /* istanbul ignore next */
                        (cov_6mjfsucij().b[57][1]++, 400);
                        /* istanbul ignore next */
                        cov_6mjfsucij().s[182]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_6mjfsucij().b[56][1]++;
                      }
                      // Delete session (cascade will handle questions and responses)
                      cov_6mjfsucij().s[183]++;
                      return [4 /*yield*/, prisma_1.prisma.interviewSession.delete({
                        where: {
                          id: sessionId
                        }
                      })];
                    case 3:
                      /* istanbul ignore next */
                      cov_6mjfsucij().b[55][3]++;
                      cov_6mjfsucij().s[184]++;
                      // Delete session (cascade will handle questions and responses)
                      _a.sent();
                      /* istanbul ignore next */
                      cov_6mjfsucij().s[185]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        message: 'Interview session deleted successfully'
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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