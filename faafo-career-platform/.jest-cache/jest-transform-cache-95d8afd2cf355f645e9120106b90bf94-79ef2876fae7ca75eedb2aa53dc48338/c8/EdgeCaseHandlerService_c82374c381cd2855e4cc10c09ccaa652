9bb54a8e62bc968e7c207733ff95e527
"use strict";

/* istanbul ignore next */
function cov_10oh7b63u() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/EdgeCaseHandlerService.ts";
  var hash = "ae58b0498986cc3d3a625d21df9639315ef947ef";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/EdgeCaseHandlerService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "68": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 73
        }
      },
      "69": {
        start: {
          line: 40,
          column: 23
        },
        end: {
          line: 40,
          column: 50
        }
      },
      "70": {
        start: {
          line: 41,
          column: 15
        },
        end: {
          line: 41,
          column: 38
        }
      },
      "71": {
        start: {
          line: 42,
          column: 37
        },
        end: {
          line: 42,
          column: 94
        }
      },
      "72": {
        start: {
          line: 46,
          column: 44
        },
        end: {
          line: 466,
          column: 3
        }
      },
      "73": {
        start: {
          line: 49,
          column: 23
        },
        end: {
          line: 49,
          column: 73
        }
      },
      "74": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 56
        }
      },
      "75": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 58
        }
      },
      "76": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 60
        }
      },
      "77": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 64
        }
      },
      "78": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 63,
          column: 6
        }
      },
      "79": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 61,
          column: 9
        }
      },
      "80": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 60,
          column: 75
        }
      },
      "81": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 47
        }
      },
      "82": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 74,
          column: 6
        }
      },
      "83": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 73,
          column: 11
        }
      },
      "84": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 53
        }
      },
      "85": {
        start: {
          line: 69,
          column: 38
        },
        end: {
          line: 69,
          column: 51
        }
      },
      "86": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 72,
          column: 15
        }
      },
      "87": {
        start: {
          line: 71,
          column: 16
        },
        end: {
          line: 71,
          column: 100
        }
      },
      "88": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 85,
          column: 6
        }
      },
      "89": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 84,
          column: 11
        }
      },
      "90": {
        start: {
          line: 80,
          column: 12
        },
        end: {
          line: 80,
          column: 53
        }
      },
      "91": {
        start: {
          line: 80,
          column: 38
        },
        end: {
          line: 80,
          column: 51
        }
      },
      "92": {
        start: {
          line: 81,
          column: 12
        },
        end: {
          line: 83,
          column: 15
        }
      },
      "93": {
        start: {
          line: 82,
          column: 16
        },
        end: {
          line: 82,
          column: 107
        }
      },
      "94": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 96,
          column: 6
        }
      },
      "95": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 95,
          column: 11
        }
      },
      "96": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 53
        }
      },
      "97": {
        start: {
          line: 91,
          column: 38
        },
        end: {
          line: 91,
          column: 51
        }
      },
      "98": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 94,
          column: 15
        }
      },
      "99": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 93,
          column: 102
        }
      },
      "100": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 170,
          column: 6
        }
      },
      "101": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 169,
          column: 11
        }
      },
      "102": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 103,
          column: 53
        }
      },
      "103": {
        start: {
          line: 103,
          column: 38
        },
        end: {
          line: 103,
          column: 51
        }
      },
      "104": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 168,
          column: 15
        }
      },
      "105": {
        start: {
          line: 105,
          column: 16
        },
        end: {
          line: 167,
          column: 17
        }
      },
      "106": {
        start: {
          line: 107,
          column: 24
        },
        end: {
          line: 107,
          column: 50
        }
      },
      "107": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 118
        }
      },
      "108": {
        start: {
          line: 109,
          column: 24
        },
        end: {
          line: 116,
          column: 25
        }
      },
      "109": {
        start: {
          line: 110,
          column: 28
        },
        end: {
          line: 115,
          column: 35
        }
      },
      "110": {
        start: {
          line: 117,
          column: 24
        },
        end: {
          line: 117,
          column: 38
        }
      },
      "111": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 38
        }
      },
      "112": {
        start: {
          line: 119,
          column: 24
        },
        end: {
          line: 123,
          column: 25
        }
      },
      "113": {
        start: {
          line: 120,
          column: 48
        },
        end: {
          line: 120,
          column: 72
        }
      },
      "114": {
        start: {
          line: 121,
          column: 54
        },
        end: {
          line: 121,
          column: 78
        }
      },
      "115": {
        start: {
          line: 122,
          column: 50
        },
        end: {
          line: 122,
          column: 74
        }
      },
      "116": {
        start: {
          line: 124,
          column: 24
        },
        end: {
          line: 124,
          column: 48
        }
      },
      "117": {
        start: {
          line: 125,
          column: 28
        },
        end: {
          line: 131,
          column: 28
        }
      },
      "118": {
        start: {
          line: 133,
          column: 24
        },
        end: {
          line: 133,
          column: 41
        }
      },
      "119": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 48
        }
      },
      "120": {
        start: {
          line: 135,
          column: 28
        },
        end: {
          line: 139,
          column: 28
        }
      },
      "121": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 141,
          column: 41
        }
      },
      "122": {
        start: {
          line: 142,
          column: 24
        },
        end: {
          line: 142,
          column: 48
        }
      },
      "123": {
        start: {
          line: 143,
          column: 28
        },
        end: {
          line: 149,
          column: 28
        }
      },
      "124": {
        start: {
          line: 151,
          column: 24
        },
        end: {
          line: 151,
          column: 41
        }
      },
      "125": {
        start: {
          line: 152,
          column: 24
        },
        end: {
          line: 152,
          column: 37
        }
      },
      "126": {
        start: {
          line: 153,
          column: 28
        },
        end: {
          line: 157,
          column: 27
        }
      },
      "127": {
        start: {
          line: 159,
          column: 24
        },
        end: {
          line: 159,
          column: 44
        }
      },
      "128": {
        start: {
          line: 160,
          column: 24
        },
        end: {
          line: 165,
          column: 31
        }
      },
      "129": {
        start: {
          line: 166,
          column: 28
        },
        end: {
          line: 166,
          column: 50
        }
      },
      "130": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 180,
          column: 6
        }
      },
      "131": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 179,
          column: 11
        }
      },
      "132": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 178,
          column: 15
        }
      },
      "133": {
        start: {
          line: 177,
          column: 16
        },
        end: {
          line: 177,
          column: 81
        }
      },
      "134": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 190,
          column: 6
        }
      },
      "135": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 189,
          column: 11
        }
      },
      "136": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 188,
          column: 15
        }
      },
      "137": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 187,
          column: 78
        }
      },
      "138": {
        start: {
          line: 194,
          column: 4
        },
        end: {
          line: 199,
          column: 6
        }
      },
      "139": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 198,
          column: 10
        }
      },
      "140": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 205,
          column: 6
        }
      },
      "141": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 204,
          column: 82
        }
      },
      "142": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 294,
          column: 6
        }
      },
      "143": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 293,
          column: 11
        }
      },
      "144": {
        start: {
          line: 213,
          column: 12
        },
        end: {
          line: 292,
          column: 15
        }
      },
      "145": {
        start: {
          line: 214,
          column: 16
        },
        end: {
          line: 291,
          column: 17
        }
      },
      "146": {
        start: {
          line: 216,
          column: 24
        },
        end: {
          line: 216,
          column: 50
        }
      },
      "147": {
        start: {
          line: 217,
          column: 24
        },
        end: {
          line: 217,
          column: 129
        }
      },
      "148": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 41
        }
      },
      "149": {
        start: {
          line: 221,
          column: 24
        },
        end: {
          line: 229,
          column: 25
        }
      },
      "150": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 106
        }
      },
      "151": {
        start: {
          line: 223,
          column: 28
        },
        end: {
          line: 228,
          column: 35
        }
      },
      "152": {
        start: {
          line: 230,
          column: 24
        },
        end: {
          line: 230,
          column: 133
        }
      },
      "153": {
        start: {
          line: 232,
          column: 24
        },
        end: {
          line: 232,
          column: 43
        }
      },
      "154": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 97
        }
      },
      "155": {
        start: {
          line: 233,
          column: 73
        },
        end: {
          line: 233,
          column: 97
        }
      },
      "156": {
        start: {
          line: 234,
          column: 24
        },
        end: {
          line: 234,
          column: 84
        }
      },
      "157": {
        start: {
          line: 234,
          column: 68
        },
        end: {
          line: 234,
          column: 80
        }
      },
      "158": {
        start: {
          line: 235,
          column: 24
        },
        end: {
          line: 235,
          column: 122
        }
      },
      "159": {
        start: {
          line: 235,
          column: 81
        },
        end: {
          line: 235,
          column: 118
        }
      },
      "160": {
        start: {
          line: 236,
          column: 24
        },
        end: {
          line: 240,
          column: 26
        }
      },
      "161": {
        start: {
          line: 241,
          column: 24
        },
        end: {
          line: 241,
          column: 98
        }
      },
      "162": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 243,
          column: 33
        }
      },
      "163": {
        start: {
          line: 244,
          column: 28
        },
        end: {
          line: 249,
          column: 28
        }
      },
      "164": {
        start: {
          line: 251,
          column: 24
        },
        end: {
          line: 251,
          column: 43
        }
      },
      "165": {
        start: {
          line: 252,
          column: 24
        },
        end: {
          line: 252,
          column: 86
        }
      },
      "166": {
        start: {
          line: 252,
          column: 62
        },
        end: {
          line: 252,
          column: 86
        }
      },
      "167": {
        start: {
          line: 253,
          column: 24
        },
        end: {
          line: 276,
          column: 32
        }
      },
      "168": {
        start: {
          line: 278,
          column: 24
        },
        end: {
          line: 278,
          column: 47
        }
      },
      "169": {
        start: {
          line: 279,
          column: 24
        },
        end: {
          line: 279,
          column: 63
        }
      },
      "170": {
        start: {
          line: 280,
          column: 24
        },
        end: {
          line: 280,
          column: 37
        }
      },
      "171": {
        start: {
          line: 281,
          column: 28
        },
        end: {
          line: 281,
          column: 58
        }
      },
      "172": {
        start: {
          line: 283,
          column: 24
        },
        end: {
          line: 283,
          column: 44
        }
      },
      "173": {
        start: {
          line: 284,
          column: 24
        },
        end: {
          line: 289,
          column: 31
        }
      },
      "174": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 50
        }
      },
      "175": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 370,
          column: 6
        }
      },
      "176": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 369,
          column: 11
        }
      },
      "177": {
        start: {
          line: 301,
          column: 12
        },
        end: {
          line: 368,
          column: 15
        }
      },
      "178": {
        start: {
          line: 302,
          column: 16
        },
        end: {
          line: 367,
          column: 17
        }
      },
      "179": {
        start: {
          line: 304,
          column: 24
        },
        end: {
          line: 304,
          column: 50
        }
      },
      "180": {
        start: {
          line: 305,
          column: 24
        },
        end: {
          line: 305,
          column: 129
        }
      },
      "181": {
        start: {
          line: 307,
          column: 24
        },
        end: {
          line: 307,
          column: 41
        }
      },
      "182": {
        start: {
          line: 308,
          column: 24
        },
        end: {
          line: 315,
          column: 25
        }
      },
      "183": {
        start: {
          line: 309,
          column: 28
        },
        end: {
          line: 314,
          column: 35
        }
      },
      "184": {
        start: {
          line: 316,
          column: 24
        },
        end: {
          line: 326,
          column: 32
        }
      },
      "185": {
        start: {
          line: 328,
          column: 24
        },
        end: {
          line: 328,
          column: 47
        }
      },
      "186": {
        start: {
          line: 329,
          column: 24
        },
        end: {
          line: 337,
          column: 32
        }
      },
      "187": {
        start: {
          line: 339,
          column: 24
        },
        end: {
          line: 339,
          column: 43
        }
      },
      "188": {
        start: {
          line: 340,
          column: 24
        },
        end: {
          line: 340,
          column: 86
        }
      },
      "189": {
        start: {
          line: 340,
          column: 62
        },
        end: {
          line: 340,
          column: 86
        }
      },
      "190": {
        start: {
          line: 341,
          column: 24
        },
        end: {
          line: 352,
          column: 32
        }
      },
      "191": {
        start: {
          line: 354,
          column: 24
        },
        end: {
          line: 354,
          column: 49
        }
      },
      "192": {
        start: {
          line: 355,
          column: 24
        },
        end: {
          line: 355,
          column: 65
        }
      },
      "193": {
        start: {
          line: 356,
          column: 24
        },
        end: {
          line: 356,
          column: 37
        }
      },
      "194": {
        start: {
          line: 357,
          column: 28
        },
        end: {
          line: 357,
          column: 58
        }
      },
      "195": {
        start: {
          line: 359,
          column: 24
        },
        end: {
          line: 359,
          column: 44
        }
      },
      "196": {
        start: {
          line: 360,
          column: 24
        },
        end: {
          line: 365,
          column: 31
        }
      },
      "197": {
        start: {
          line: 366,
          column: 28
        },
        end: {
          line: 366,
          column: 50
        }
      },
      "198": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 431,
          column: 6
        }
      },
      "199": {
        start: {
          line: 375,
          column: 8
        },
        end: {
          line: 430,
          column: 11
        }
      },
      "200": {
        start: {
          line: 377,
          column: 12
        },
        end: {
          line: 429,
          column: 15
        }
      },
      "201": {
        start: {
          line: 378,
          column: 16
        },
        end: {
          line: 428,
          column: 17
        }
      },
      "202": {
        start: {
          line: 380,
          column: 24
        },
        end: {
          line: 380,
          column: 50
        }
      },
      "203": {
        start: {
          line: 381,
          column: 24
        },
        end: {
          line: 381,
          column: 151
        }
      },
      "204": {
        start: {
          line: 383,
          column: 24
        },
        end: {
          line: 383,
          column: 47
        }
      },
      "205": {
        start: {
          line: 384,
          column: 24
        },
        end: {
          line: 389,
          column: 32
        }
      },
      "206": {
        start: {
          line: 391,
          column: 24
        },
        end: {
          line: 391,
          column: 43
        }
      },
      "207": {
        start: {
          line: 392,
          column: 24
        },
        end: {
          line: 392,
          column: 86
        }
      },
      "208": {
        start: {
          line: 392,
          column: 62
        },
        end: {
          line: 392,
          column: 86
        }
      },
      "209": {
        start: {
          line: 393,
          column: 24
        },
        end: {
          line: 393,
          column: 135
        }
      },
      "210": {
        start: {
          line: 395,
          column: 24
        },
        end: {
          line: 395,
          column: 42
        }
      },
      "211": {
        start: {
          line: 396,
          column: 24
        },
        end: {
          line: 396,
          column: 60
        }
      },
      "212": {
        start: {
          line: 396,
          column: 36
        },
        end: {
          line: 396,
          column: 60
        }
      },
      "213": {
        start: {
          line: 397,
          column: 24
        },
        end: {
          line: 414,
          column: 32
        }
      },
      "214": {
        start: {
          line: 416,
          column: 24
        },
        end: {
          line: 416,
          column: 34
        }
      },
      "215": {
        start: {
          line: 417,
          column: 24
        },
        end: {
          line: 417,
          column: 37
        }
      },
      "216": {
        start: {
          line: 418,
          column: 28
        },
        end: {
          line: 418,
          column: 58
        }
      },
      "217": {
        start: {
          line: 420,
          column: 24
        },
        end: {
          line: 420,
          column: 44
        }
      },
      "218": {
        start: {
          line: 421,
          column: 24
        },
        end: {
          line: 426,
          column: 31
        }
      },
      "219": {
        start: {
          line: 427,
          column: 28
        },
        end: {
          line: 427,
          column: 50
        }
      },
      "220": {
        start: {
          line: 435,
          column: 4
        },
        end: {
          line: 464,
          column: 6
        }
      },
      "221": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 463,
          column: 11
        }
      },
      "222": {
        start: {
          line: 438,
          column: 12
        },
        end: {
          line: 462,
          column: 15
        }
      },
      "223": {
        start: {
          line: 439,
          column: 16
        },
        end: {
          line: 461,
          column: 17
        }
      },
      "224": {
        start: {
          line: 441,
          column: 24
        },
        end: {
          line: 441,
          column: 50
        }
      },
      "225": {
        start: {
          line: 442,
          column: 24
        },
        end: {
          line: 452,
          column: 32
        }
      },
      "226": {
        start: {
          line: 444,
          column: 81
        },
        end: {
          line: 449,
          column: 39
        }
      },
      "227": {
        start: {
          line: 454,
          column: 24
        },
        end: {
          line: 454,
          column: 50
        }
      },
      "228": {
        start: {
          line: 455,
          column: 24
        },
        end: {
          line: 455,
          column: 106
        }
      },
      "229": {
        start: {
          line: 455,
          column: 83
        },
        end: {
          line: 455,
          column: 101
        }
      },
      "230": {
        start: {
          line: 457,
          column: 24
        },
        end: {
          line: 457,
          column: 44
        }
      },
      "231": {
        start: {
          line: 459,
          column: 24
        },
        end: {
          line: 459,
          column: 106
        }
      },
      "232": {
        start: {
          line: 460,
          column: 28
        },
        end: {
          line: 460,
          column: 50
        }
      },
      "233": {
        start: {
          line: 465,
          column: 4
        },
        end: {
          line: 465,
          column: 34
        }
      },
      "234": {
        start: {
          line: 467,
          column: 0
        },
        end: {
          line: 467,
          column: 56
        }
      },
      "235": {
        start: {
          line: 469,
          column: 0
        },
        end: {
          line: 469,
          column: 70
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 46,
            column: 44
          },
          end: {
            line: 46,
            column: 45
          }
        },
        loc: {
          start: {
            line: 46,
            column: 56
          },
          end: {
            line: 466,
            column: 1
          }
        },
        line: 46
      },
      "14": {
        name: "EdgeCaseHandlerService",
        decl: {
          start: {
            line: 47,
            column: 13
          },
          end: {
            line: 47,
            column: 35
          }
        },
        loc: {
          start: {
            line: 47,
            column: 38
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 58,
            column: 41
          },
          end: {
            line: 58,
            column: 42
          }
        },
        loc: {
          start: {
            line: 58,
            column: 53
          },
          end: {
            line: 63,
            column: 5
          }
        },
        line: 58
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 67,
            column: 61
          },
          end: {
            line: 67,
            column: 62
          }
        },
        loc: {
          start: {
            line: 67,
            column: 82
          },
          end: {
            line: 74,
            column: 5
          }
        },
        line: 67
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 68,
            column: 51
          },
          end: {
            line: 68,
            column: 52
          }
        },
        loc: {
          start: {
            line: 68,
            column: 79
          },
          end: {
            line: 73,
            column: 9
          }
        },
        line: 68
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 70,
            column: 37
          },
          end: {
            line: 70,
            column: 38
          }
        },
        loc: {
          start: {
            line: 70,
            column: 51
          },
          end: {
            line: 72,
            column: 13
          }
        },
        line: 70
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 78,
            column: 68
          },
          end: {
            line: 78,
            column: 69
          }
        },
        loc: {
          start: {
            line: 78,
            column: 89
          },
          end: {
            line: 85,
            column: 5
          }
        },
        line: 78
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 79,
            column: 51
          },
          end: {
            line: 79,
            column: 52
          }
        },
        loc: {
          start: {
            line: 79,
            column: 79
          },
          end: {
            line: 84,
            column: 9
          }
        },
        line: 79
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 81,
            column: 37
          },
          end: {
            line: 81,
            column: 38
          }
        },
        loc: {
          start: {
            line: 81,
            column: 51
          },
          end: {
            line: 83,
            column: 13
          }
        },
        line: 81
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 89,
            column: 65
          },
          end: {
            line: 89,
            column: 66
          }
        },
        loc: {
          start: {
            line: 89,
            column: 86
          },
          end: {
            line: 96,
            column: 5
          }
        },
        line: 89
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 90,
            column: 51
          },
          end: {
            line: 90,
            column: 52
          }
        },
        loc: {
          start: {
            line: 90,
            column: 79
          },
          end: {
            line: 95,
            column: 9
          }
        },
        line: 90
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 92,
            column: 37
          },
          end: {
            line: 92,
            column: 38
          }
        },
        loc: {
          start: {
            line: 92,
            column: 51
          },
          end: {
            line: 94,
            column: 13
          }
        },
        line: 92
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 100,
            column: 63
          },
          end: {
            line: 100,
            column: 64
          }
        },
        loc: {
          start: {
            line: 100,
            column: 84
          },
          end: {
            line: 170,
            column: 5
          }
        },
        line: 100
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 101,
            column: 51
          },
          end: {
            line: 101,
            column: 52
          }
        },
        loc: {
          start: {
            line: 101,
            column: 79
          },
          end: {
            line: 169,
            column: 9
          }
        },
        line: 101
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 104,
            column: 37
          },
          end: {
            line: 104,
            column: 38
          }
        },
        loc: {
          start: {
            line: 104,
            column: 51
          },
          end: {
            line: 168,
            column: 13
          }
        },
        line: 104
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 174,
            column: 58
          },
          end: {
            line: 174,
            column: 59
          }
        },
        loc: {
          start: {
            line: 174,
            column: 70
          },
          end: {
            line: 180,
            column: 5
          }
        },
        line: 174
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 175,
            column: 47
          },
          end: {
            line: 175,
            column: 48
          }
        },
        loc: {
          start: {
            line: 175,
            column: 59
          },
          end: {
            line: 179,
            column: 9
          }
        },
        line: 175
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 176,
            column: 37
          },
          end: {
            line: 176,
            column: 38
          }
        },
        loc: {
          start: {
            line: 176,
            column: 51
          },
          end: {
            line: 178,
            column: 13
          }
        },
        line: 176
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 184,
            column: 55
          },
          end: {
            line: 184,
            column: 56
          }
        },
        loc: {
          start: {
            line: 184,
            column: 67
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 184
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 185,
            column: 47
          },
          end: {
            line: 185,
            column: 48
          }
        },
        loc: {
          start: {
            line: 185,
            column: 59
          },
          end: {
            line: 189,
            column: 9
          }
        },
        line: 185
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 186,
            column: 37
          },
          end: {
            line: 186,
            column: 38
          }
        },
        loc: {
          start: {
            line: 186,
            column: 51
          },
          end: {
            line: 188,
            column: 13
          }
        },
        line: 186
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 194,
            column: 61
          },
          end: {
            line: 194,
            column: 62
          }
        },
        loc: {
          start: {
            line: 194,
            column: 73
          },
          end: {
            line: 199,
            column: 5
          }
        },
        line: 194
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 203,
            column: 62
          },
          end: {
            line: 203,
            column: 63
          }
        },
        loc: {
          start: {
            line: 203,
            column: 74
          },
          end: {
            line: 205,
            column: 5
          }
        },
        line: 203
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 209,
            column: 73
          },
          end: {
            line: 209,
            column: 74
          }
        },
        loc: {
          start: {
            line: 209,
            column: 91
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 209
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 210,
            column: 48
          },
          end: {
            line: 210,
            column: 49
          }
        },
        loc: {
          start: {
            line: 210,
            column: 60
          },
          end: {
            line: 293,
            column: 9
          }
        },
        line: 210
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 213,
            column: 37
          },
          end: {
            line: 213,
            column: 38
          }
        },
        loc: {
          start: {
            line: 213,
            column: 51
          },
          end: {
            line: 292,
            column: 13
          }
        },
        line: 213
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 234,
            column: 53
          },
          end: {
            line: 234,
            column: 54
          }
        },
        loc: {
          start: {
            line: 234,
            column: 66
          },
          end: {
            line: 234,
            column: 82
          }
        },
        line: 234
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 235,
            column: 65
          },
          end: {
            line: 235,
            column: 66
          }
        },
        loc: {
          start: {
            line: 235,
            column: 79
          },
          end: {
            line: 235,
            column: 120
          }
        },
        line: 235
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 298,
            column: 72
          },
          end: {
            line: 298,
            column: 73
          }
        },
        loc: {
          start: {
            line: 298,
            column: 90
          },
          end: {
            line: 370,
            column: 5
          }
        },
        line: 298
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 299,
            column: 48
          },
          end: {
            line: 299,
            column: 49
          }
        },
        loc: {
          start: {
            line: 299,
            column: 60
          },
          end: {
            line: 369,
            column: 9
          }
        },
        line: 299
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 301,
            column: 37
          },
          end: {
            line: 301,
            column: 38
          }
        },
        loc: {
          start: {
            line: 301,
            column: 51
          },
          end: {
            line: 368,
            column: 13
          }
        },
        line: 301
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 374,
            column: 65
          },
          end: {
            line: 374,
            column: 66
          }
        },
        loc: {
          start: {
            line: 374,
            column: 83
          },
          end: {
            line: 431,
            column: 5
          }
        },
        line: 374
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 375,
            column: 48
          },
          end: {
            line: 375,
            column: 49
          }
        },
        loc: {
          start: {
            line: 375,
            column: 60
          },
          end: {
            line: 430,
            column: 9
          }
        },
        line: 375
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 377,
            column: 37
          },
          end: {
            line: 377,
            column: 38
          }
        },
        loc: {
          start: {
            line: 377,
            column: 51
          },
          end: {
            line: 429,
            column: 13
          }
        },
        line: 377
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 435,
            column: 69
          },
          end: {
            line: 435,
            column: 70
          }
        },
        loc: {
          start: {
            line: 435,
            column: 96
          },
          end: {
            line: 464,
            column: 5
          }
        },
        line: 435
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 436,
            column: 48
          },
          end: {
            line: 436,
            column: 49
          }
        },
        loc: {
          start: {
            line: 436,
            column: 60
          },
          end: {
            line: 463,
            column: 9
          }
        },
        line: 436
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 438,
            column: 37
          },
          end: {
            line: 438,
            column: 38
          }
        },
        loc: {
          start: {
            line: 438,
            column: 51
          },
          end: {
            line: 462,
            column: 13
          }
        },
        line: 438
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 444,
            column: 60
          },
          end: {
            line: 444,
            column: 61
          }
        },
        loc: {
          start: {
            line: 444,
            column: 79
          },
          end: {
            line: 449,
            column: 41
          }
        },
        line: 444
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 455,
            column: 64
          },
          end: {
            line: 455,
            column: 65
          }
        },
        loc: {
          start: {
            line: 455,
            column: 81
          },
          end: {
            line: 455,
            column: 103
          }
        },
        line: 455
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "33": {
        loc: {
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 69,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 69,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "34": {
        loc: {
          start: {
            line: 80,
            column: 12
          },
          end: {
            line: 80,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 12
          },
          end: {
            line: 80,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "35": {
        loc: {
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 91,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 91,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "36": {
        loc: {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 103,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 103,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "37": {
        loc: {
          start: {
            line: 105,
            column: 16
          },
          end: {
            line: 167,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 106,
            column: 20
          },
          end: {
            line: 124,
            column: 48
          }
        }, {
          start: {
            line: 125,
            column: 20
          },
          end: {
            line: 131,
            column: 28
          }
        }, {
          start: {
            line: 132,
            column: 20
          },
          end: {
            line: 134,
            column: 48
          }
        }, {
          start: {
            line: 135,
            column: 20
          },
          end: {
            line: 139,
            column: 28
          }
        }, {
          start: {
            line: 140,
            column: 20
          },
          end: {
            line: 142,
            column: 48
          }
        }, {
          start: {
            line: 143,
            column: 20
          },
          end: {
            line: 149,
            column: 28
          }
        }, {
          start: {
            line: 150,
            column: 20
          },
          end: {
            line: 152,
            column: 37
          }
        }, {
          start: {
            line: 153,
            column: 20
          },
          end: {
            line: 157,
            column: 27
          }
        }, {
          start: {
            line: 158,
            column: 20
          },
          end: {
            line: 165,
            column: 31
          }
        }, {
          start: {
            line: 166,
            column: 20
          },
          end: {
            line: 166,
            column: 50
          }
        }],
        line: 105
      },
      "38": {
        loc: {
          start: {
            line: 108,
            column: 83
          },
          end: {
            line: 108,
            column: 117
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 108,
            column: 99
          },
          end: {
            line: 108,
            column: 112
          }
        }, {
          start: {
            line: 108,
            column: 115
          },
          end: {
            line: 108,
            column: 117
          }
        }],
        line: 108
      },
      "39": {
        loc: {
          start: {
            line: 109,
            column: 24
          },
          end: {
            line: 116,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 24
          },
          end: {
            line: 116,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "40": {
        loc: {
          start: {
            line: 119,
            column: 24
          },
          end: {
            line: 123,
            column: 25
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 120,
            column: 28
          },
          end: {
            line: 120,
            column: 72
          }
        }, {
          start: {
            line: 121,
            column: 28
          },
          end: {
            line: 121,
            column: 78
          }
        }, {
          start: {
            line: 122,
            column: 28
          },
          end: {
            line: 122,
            column: 74
          }
        }],
        line: 119
      },
      "41": {
        loc: {
          start: {
            line: 162,
            column: 39
          },
          end: {
            line: 162,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 162,
            column: 66
          },
          end: {
            line: 162,
            column: 81
          }
        }, {
          start: {
            line: 162,
            column: 84
          },
          end: {
            line: 162,
            column: 99
          }
        }],
        line: 162
      },
      "42": {
        loc: {
          start: {
            line: 214,
            column: 16
          },
          end: {
            line: 291,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 215,
            column: 20
          },
          end: {
            line: 217,
            column: 129
          }
        }, {
          start: {
            line: 218,
            column: 20
          },
          end: {
            line: 230,
            column: 133
          }
        }, {
          start: {
            line: 231,
            column: 20
          },
          end: {
            line: 241,
            column: 98
          }
        }, {
          start: {
            line: 242,
            column: 20
          },
          end: {
            line: 243,
            column: 33
          }
        }, {
          start: {
            line: 244,
            column: 20
          },
          end: {
            line: 249,
            column: 28
          }
        }, {
          start: {
            line: 250,
            column: 20
          },
          end: {
            line: 276,
            column: 32
          }
        }, {
          start: {
            line: 277,
            column: 20
          },
          end: {
            line: 280,
            column: 37
          }
        }, {
          start: {
            line: 281,
            column: 20
          },
          end: {
            line: 281,
            column: 58
          }
        }, {
          start: {
            line: 282,
            column: 20
          },
          end: {
            line: 289,
            column: 31
          }
        }, {
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 290,
            column: 50
          }
        }],
        line: 214
      },
      "43": {
        loc: {
          start: {
            line: 221,
            column: 24
          },
          end: {
            line: 229,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 24
          },
          end: {
            line: 229,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "44": {
        loc: {
          start: {
            line: 233,
            column: 24
          },
          end: {
            line: 233,
            column: 97
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 24
          },
          end: {
            line: 233,
            column: 97
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "45": {
        loc: {
          start: {
            line: 248,
            column: 44
          },
          end: {
            line: 248,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 248,
            column: 44
          },
          end: {
            line: 248,
            column: 65
          }
        }, {
          start: {
            line: 248,
            column: 69
          },
          end: {
            line: 248,
            column: 84
          }
        }],
        line: 248
      },
      "46": {
        loc: {
          start: {
            line: 252,
            column: 24
          },
          end: {
            line: 252,
            column: 86
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 24
          },
          end: {
            line: 252,
            column: 86
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "47": {
        loc: {
          start: {
            line: 252,
            column: 30
          },
          end: {
            line: 252,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 30
          },
          end: {
            line: 252,
            column: 44
          }
        }, {
          start: {
            line: 252,
            column: 48
          },
          end: {
            line: 252,
            column: 59
          }
        }],
        line: 252
      },
      "48": {
        loc: {
          start: {
            line: 258,
            column: 56
          },
          end: {
            line: 258,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 258,
            column: 56
          },
          end: {
            line: 258,
            column: 77
          }
        }, {
          start: {
            line: 258,
            column: 81
          },
          end: {
            line: 258,
            column: 98
          }
        }],
        line: 258
      },
      "49": {
        loc: {
          start: {
            line: 262,
            column: 48
          },
          end: {
            line: 262,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 262,
            column: 48
          },
          end: {
            line: 262,
            column: 72
          }
        }, {
          start: {
            line: 262,
            column: 76
          },
          end: {
            line: 262,
            column: 77
          }
        }],
        line: 262
      },
      "50": {
        loc: {
          start: {
            line: 263,
            column: 53
          },
          end: {
            line: 263,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 53
          },
          end: {
            line: 263,
            column: 82
          }
        }, {
          start: {
            line: 263,
            column: 86
          },
          end: {
            line: 263,
            column: 87
          }
        }],
        line: 263
      },
      "51": {
        loc: {
          start: {
            line: 270,
            column: 48
          },
          end: {
            line: 270,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 48
          },
          end: {
            line: 270,
            column: 72
          }
        }, {
          start: {
            line: 270,
            column: 76
          },
          end: {
            line: 270,
            column: 77
          }
        }],
        line: 270
      },
      "52": {
        loc: {
          start: {
            line: 271,
            column: 53
          },
          end: {
            line: 271,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 271,
            column: 53
          },
          end: {
            line: 271,
            column: 82
          }
        }, {
          start: {
            line: 271,
            column: 86
          },
          end: {
            line: 271,
            column: 87
          }
        }],
        line: 271
      },
      "53": {
        loc: {
          start: {
            line: 272,
            column: 52
          },
          end: {
            line: 272,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 272,
            column: 52
          },
          end: {
            line: 272,
            column: 73
          }
        }, {
          start: {
            line: 272,
            column: 77
          },
          end: {
            line: 272,
            column: 94
          }
        }],
        line: 272
      },
      "54": {
        loc: {
          start: {
            line: 286,
            column: 39
          },
          end: {
            line: 286,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 286,
            column: 66
          },
          end: {
            line: 286,
            column: 81
          }
        }, {
          start: {
            line: 286,
            column: 84
          },
          end: {
            line: 286,
            column: 99
          }
        }],
        line: 286
      },
      "55": {
        loc: {
          start: {
            line: 302,
            column: 16
          },
          end: {
            line: 367,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 303,
            column: 20
          },
          end: {
            line: 305,
            column: 129
          }
        }, {
          start: {
            line: 306,
            column: 20
          },
          end: {
            line: 326,
            column: 32
          }
        }, {
          start: {
            line: 327,
            column: 20
          },
          end: {
            line: 337,
            column: 32
          }
        }, {
          start: {
            line: 338,
            column: 20
          },
          end: {
            line: 352,
            column: 32
          }
        }, {
          start: {
            line: 353,
            column: 20
          },
          end: {
            line: 356,
            column: 37
          }
        }, {
          start: {
            line: 357,
            column: 20
          },
          end: {
            line: 357,
            column: 58
          }
        }, {
          start: {
            line: 358,
            column: 20
          },
          end: {
            line: 365,
            column: 31
          }
        }, {
          start: {
            line: 366,
            column: 20
          },
          end: {
            line: 366,
            column: 50
          }
        }],
        line: 302
      },
      "56": {
        loc: {
          start: {
            line: 308,
            column: 24
          },
          end: {
            line: 315,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 308,
            column: 24
          },
          end: {
            line: 315,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 308
      },
      "57": {
        loc: {
          start: {
            line: 340,
            column: 24
          },
          end: {
            line: 340,
            column: 86
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 340,
            column: 24
          },
          end: {
            line: 340,
            column: 86
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 340
      },
      "58": {
        loc: {
          start: {
            line: 340,
            column: 30
          },
          end: {
            line: 340,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 340,
            column: 30
          },
          end: {
            line: 340,
            column: 44
          }
        }, {
          start: {
            line: 340,
            column: 48
          },
          end: {
            line: 340,
            column: 59
          }
        }],
        line: 340
      },
      "59": {
        loc: {
          start: {
            line: 347,
            column: 52
          },
          end: {
            line: 347,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 347,
            column: 52
          },
          end: {
            line: 347,
            column: 81
          }
        }, {
          start: {
            line: 347,
            column: 85
          },
          end: {
            line: 347,
            column: 87
          }
        }],
        line: 347
      },
      "60": {
        loc: {
          start: {
            line: 362,
            column: 39
          },
          end: {
            line: 362,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 362,
            column: 66
          },
          end: {
            line: 362,
            column: 81
          }
        }, {
          start: {
            line: 362,
            column: 84
          },
          end: {
            line: 362,
            column: 99
          }
        }],
        line: 362
      },
      "61": {
        loc: {
          start: {
            line: 378,
            column: 16
          },
          end: {
            line: 428,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 379,
            column: 20
          },
          end: {
            line: 381,
            column: 151
          }
        }, {
          start: {
            line: 382,
            column: 20
          },
          end: {
            line: 389,
            column: 32
          }
        }, {
          start: {
            line: 390,
            column: 20
          },
          end: {
            line: 393,
            column: 135
          }
        }, {
          start: {
            line: 394,
            column: 20
          },
          end: {
            line: 414,
            column: 32
          }
        }, {
          start: {
            line: 415,
            column: 20
          },
          end: {
            line: 417,
            column: 37
          }
        }, {
          start: {
            line: 418,
            column: 20
          },
          end: {
            line: 418,
            column: 58
          }
        }, {
          start: {
            line: 419,
            column: 20
          },
          end: {
            line: 426,
            column: 31
          }
        }, {
          start: {
            line: 427,
            column: 20
          },
          end: {
            line: 427,
            column: 50
          }
        }],
        line: 378
      },
      "62": {
        loc: {
          start: {
            line: 392,
            column: 24
          },
          end: {
            line: 392,
            column: 86
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 392,
            column: 24
          },
          end: {
            line: 392,
            column: 86
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 392
      },
      "63": {
        loc: {
          start: {
            line: 392,
            column: 30
          },
          end: {
            line: 392,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 392,
            column: 30
          },
          end: {
            line: 392,
            column: 44
          }
        }, {
          start: {
            line: 392,
            column: 48
          },
          end: {
            line: 392,
            column: 59
          }
        }],
        line: 392
      },
      "64": {
        loc: {
          start: {
            line: 396,
            column: 24
          },
          end: {
            line: 396,
            column: 60
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 396,
            column: 24
          },
          end: {
            line: 396,
            column: 60
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 396
      },
      "65": {
        loc: {
          start: {
            line: 400,
            column: 44
          },
          end: {
            line: 400,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 400,
            column: 44
          },
          end: {
            line: 400,
            column: 59
          }
        }, {
          start: {
            line: 400,
            column: 63
          },
          end: {
            line: 400,
            column: 71
          }
        }],
        line: 400
      },
      "66": {
        loc: {
          start: {
            line: 401,
            column: 49
          },
          end: {
            line: 403,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 401,
            column: 75
          },
          end: {
            line: 401,
            column: 86
          }
        }, {
          start: {
            line: 402,
            column: 40
          },
          end: {
            line: 403,
            column: 88
          }
        }],
        line: 401
      },
      "67": {
        loc: {
          start: {
            line: 402,
            column: 40
          },
          end: {
            line: 403,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 402,
            column: 66
          },
          end: {
            line: 402,
            column: 72
          }
        }, {
          start: {
            line: 403,
            column: 44
          },
          end: {
            line: 403,
            column: 88
          }
        }],
        line: 402
      },
      "68": {
        loc: {
          start: {
            line: 403,
            column: 44
          },
          end: {
            line: 403,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 403,
            column: 70
          },
          end: {
            line: 403,
            column: 80
          }
        }, {
          start: {
            line: 403,
            column: 83
          },
          end: {
            line: 403,
            column: 88
          }
        }],
        line: 403
      },
      "69": {
        loc: {
          start: {
            line: 404,
            column: 57
          },
          end: {
            line: 404,
            column: 133
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 404,
            column: 85
          },
          end: {
            line: 404,
            column: 126
          }
        }, {
          start: {
            line: 404,
            column: 129
          },
          end: {
            line: 404,
            column: 133
          }
        }],
        line: 404
      },
      "70": {
        loc: {
          start: {
            line: 405,
            column: 54
          },
          end: {
            line: 405,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 405,
            column: 54
          },
          end: {
            line: 405,
            column: 77
          }
        }, {
          start: {
            line: 405,
            column: 81
          },
          end: {
            line: 405,
            column: 82
          }
        }],
        line: 405
      },
      "71": {
        loc: {
          start: {
            line: 406,
            column: 49
          },
          end: {
            line: 408,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 406,
            column: 75
          },
          end: {
            line: 406,
            column: 92
          }
        }, {
          start: {
            line: 407,
            column: 40
          },
          end: {
            line: 408,
            column: 91
          }
        }],
        line: 406
      },
      "72": {
        loc: {
          start: {
            line: 407,
            column: 40
          },
          end: {
            line: 408,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 407,
            column: 65
          },
          end: {
            line: 407,
            column: 74
          }
        }, {
          start: {
            line: 408,
            column: 44
          },
          end: {
            line: 408,
            column: 91
          }
        }],
        line: 407
      },
      "73": {
        loc: {
          start: {
            line: 408,
            column: 44
          },
          end: {
            line: 408,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 408,
            column: 69
          },
          end: {
            line: 408,
            column: 77
          }
        }, {
          start: {
            line: 408,
            column: 80
          },
          end: {
            line: 408,
            column: 91
          }
        }],
        line: 408
      },
      "74": {
        loc: {
          start: {
            line: 409,
            column: 55
          },
          end: {
            line: 409,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 409,
            column: 55
          },
          end: {
            line: 409,
            column: 77
          }
        }, {
          start: {
            line: 409,
            column: 81
          },
          end: {
            line: 409,
            column: 95
          }
        }],
        line: 409
      },
      "75": {
        loc: {
          start: {
            line: 423,
            column: 39
          },
          end: {
            line: 423,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 423,
            column: 66
          },
          end: {
            line: 423,
            column: 81
          }
        }, {
          start: {
            line: 423,
            column: 84
          },
          end: {
            line: 423,
            column: 99
          }
        }],
        line: 423
      },
      "76": {
        loc: {
          start: {
            line: 439,
            column: 16
          },
          end: {
            line: 461,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 440,
            column: 20
          },
          end: {
            line: 452,
            column: 32
          }
        }, {
          start: {
            line: 453,
            column: 20
          },
          end: {
            line: 455,
            column: 106
          }
        }, {
          start: {
            line: 456,
            column: 20
          },
          end: {
            line: 459,
            column: 106
          }
        }, {
          start: {
            line: 460,
            column: 20
          },
          end: {
            line: 460,
            column: 50
          }
        }],
        line: 439
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0, 0, 0, 0, 0, 0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0, 0, 0, 0, 0, 0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/EdgeCaseHandlerService.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,mDAAuD;AACvD,uCAAsC;AAEtC,+FAA8F;AAE9F;;GAEG;AACH;IAOE;QACE,gDAAgD;QAChD,IAAM,QAAQ,GAAG,oCAAmB,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;QAChD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;QAClD,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;IAC1D,CAAC;IAED;;OAEG;IACW,kCAAW,GAAzB;QACE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACrC,sBAAsB,CAAC,QAAQ,GAAG,IAAI,sBAAsB,EAAE,CAAC;QACjE,CAAC;QACD,OAAO,sBAAsB,CAAC,QAAQ,CAAC;IACzC,CAAC;IAED;;OAEG;IACG,sDAAqB,GAA3B;0CAA0E,OAAO,YAArD,OAAY,EAAE,OAA6B;YAA7B,wBAAA,EAAA,YAA6B;;gBACrE,sBAAO,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAC;;;KACrE;IAED;;OAEG;IACG,6DAA4B,GAAlC;0CAAiF,OAAO,YAArD,OAAY,EAAE,OAA6B;YAA7B,wBAAA,EAAA,YAA6B;;gBAC5E,sBAAO,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,OAAO,EAAE,OAAO,CAAC,EAAC;;;KAC5E;IAED;;OAEG;IACG,0DAAyB,GAA/B;0CAA8E,OAAO,YAArD,OAAY,EAAE,OAA6B;YAA7B,wBAAA,EAAA,YAA6B;;gBACzE,sBAAO,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAC;;;KACvE;IAED;;OAEG;IACG,wDAAuB,GAA7B;0CAA4E,OAAO,YAArD,OAAY,EAAE,OAA6B;;YAA7B,wBAAA,EAAA,YAA6B;;;;;wBAE7D,MAAM,GAA+B,OAAO,OAAtC,EAAE,KAA6B,OAAO,SAAZ,EAAxB,QAAQ,mBAAG,aAAa,KAAA,CAAa;wBAErD,IAAI,CAAC,MAAM,EAAE,CAAC;4BACZ,sBAAO;oCACL,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,qBAAqB;oCAC5B,SAAS,EAAE,kBAAkB;oCAC7B,YAAY,EAAE,IAAI;iCACnB,EAAC;wBACJ,CAAC;wBAGG,IAAI,SAAA,CAAC;wBACD,KAAA,QAAQ,CAAA;;iCACT,aAAa,CAAC,CAAd,wBAAa;iCACb,mBAAmB,CAAC,CAApB,wBAAmB;iCASnB,eAAe,CAAC,CAAhB,wBAAe;;;4BARX,qBAAM,eAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;4BAC3C,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE,QAAQ,EAAE,IAAI,EAAE;4BACjC,OAAO,EAAE;gCACP,KAAK,EAAE,IAAI;6BACZ;4BACD,OAAO,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE;yBACpC,CAAC,EAAA;;wBANF,IAAI,GAAG,SAML,CAAC;wBACH,wBAAM;4BAEC,qBAAM,eAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;4BAC5C,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE;4BACjB,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;4BAC/B,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;yBAC/B,CAAC,EAAA;;wBAJF,IAAI,GAAG,SAIL,CAAC;wBACH,wBAAM;4BAEC,qBAAM,eAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;4BAC3C,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE,QAAQ,EAAE,IAAI,EAAE;4BACjC,OAAO,EAAE;gCACP,KAAK,EAAE,IAAI;6BACZ;4BACD,OAAO,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE;yBACpC,CAAC,EAAA;;wBANF,IAAI,GAAG,SAML,CAAC;;4BAGP,sBAAO;4BACL,OAAO,EAAE,IAAI;4BACb,IAAI,MAAA;4BACJ,YAAY,EAAE,IAAI;yBACnB,EAAC;;;wBAEF,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gCAC/D,SAAS,EAAE,cAAc;gCACzB,YAAY,EAAE,EAAE;6BACjB,EAAC;;;;;KAEL;IAED;;OAEG;IACG,mDAAkB,GAAxB;;;gBACE,sBAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,EAAC;;;KAClD;IAED;;OAEG;IACG,gDAAe,GAArB;;;gBACE,sBAAO,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,EAAC;;;KAC/C;IAED;;OAEG;IACH,sDAAqB,GAArB;QACE,OAAO;YACL,OAAO,EAAE,2DAA4B,CAAC,qBAAqB,EAAE;YAC7D,UAAU,EAAE,2DAA4B,CAAC,aAAa,EAAE;SACzD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,uDAAsB,GAAtB;QACE,2DAA4B,CAAC,WAAW,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACG,kEAAiC,GAAvC,UAAwC,MAKvC;uCAAG,OAAO;;;;;;;wBAGI,qBAAM,2DAA4B,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAA;;wBAAhE,IAAI,GAAG,SAAyD;wBAEpE,oFAAoF;wBACpF,IAAI,CAAC,IAAI,EAAE,CAAC;4BACV,OAAO,CAAC,GAAG,CAAC,iDAAiD,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;4BAC9E,sBAAO;oCACL,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,0CAA0C;oCACjD,SAAS,EAAE,sBAA+B;oCAC1C,SAAS,EAAE,KAAK;iCACjB,EAAC;wBACJ,CAAC;wBAGc,qBAAM,2DAA4B,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAA;;wBAAtE,MAAM,GAAG,SAA6D;6BAExE,CAAA,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAA,EAAxC,wBAAwC;wBACpC,kBAAgB,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,EAAJ,CAAI,CAAC,CAAC;wBACtC,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,eAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,EAA3B,CAA2B,CAAC,CAAC;;4BAGhF,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,4BAAqB,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE;4BACxD,SAAS,EAAE,sBAAsB;;wBACV,qBAAM,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,EAAA;4BAJlF,uBAIE,wBAAqB,GAAE,SAAyD;iCAChF;4BAIW,qBAAM,IAAI,CAAC,qBAAqB,CAAC;4BAC9C,MAAM,EAAE,MAAM,CAAC,MAAM;4BACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;4BACzB,YAAY,EAAE,MAAM,CAAC,YAAY;4BACjC,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,eAAe;yBACzD,CAAC,EAAA;;wBALI,MAAM,GAAG,SAKb;6BAGE,CAAA,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAA,EAA7B,wBAA6B;wBACZ,qBAAM,eAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gCACrD,KAAK,EAAE;oCACL,6BAA6B,EAAE;wCAC7B,MAAM,EAAE,MAAM,CAAC,MAAM;wCACrB,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,+BAA+B;wCAC5D,cAAc,EAAG,MAAM,CAAC,cAAsB,IAAI,iBAAiB;qCACpE;iCACF;gCACD,MAAM,EAAE;oCACN,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC;oCACzC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC;oCACnD,cAAc,EAAE,IAAI,IAAI,EAAE;oCAC1B,KAAK,EAAE,2CAAoC,MAAM,CAAC,IAAI,CAAC,EAAE,uBAAa,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,4BAAkB,MAAM,CAAC,YAAY,CAAE;iCACxI;gCACD,MAAM,EAAE;oCACN,MAAM,EAAE,MAAM,CAAC,MAAM;oCACrB,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,+BAA+B;oCAC5D,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC;oCACzC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC;oCACnD,cAAc,EAAG,MAAM,CAAC,cAAsB,IAAI,iBAAiB;oCACnE,cAAc,EAAE,IAAI,IAAI,EAAE;oCAC1B,KAAK,EAAE,2CAAoC,MAAM,CAAC,IAAI,CAAC,EAAE,uBAAa,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,4BAAkB,MAAM,CAAC,YAAY,CAAE;iCACxI;6BACF,CAAC,EAAA;;wBAvBI,UAAU,GAAG,SAuBjB;wBAEF,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,EAAE,CAAC;;4BAGzC,sBAAO,MAAM,EAAC;;;wBAEd,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gCAC/D,SAAS,EAAE,cAAc;gCACzB,YAAY,EAAE,IAAI;6BACnB,EAAC;;;;;KAEL;IAED;;OAEG;IACG,iEAAgC,GAAtC,UAAuC,MAOtC;uCAAG,OAAO;;;;;;wBAGM,qBAAM,2DAA4B,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAA;;wBAAhE,IAAI,GAAG,SAAyD;wBAEtE,IAAI,CAAC,IAAI,EAAE,CAAC;4BACV,sBAAO;oCACL,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,gBAAgB;oCACvB,SAAS,EAAE,kBAAkB;oCAC7B,YAAY,EAAE,IAAI;iCACnB,EAAC;wBACJ,CAAC;wBAGkB,qBAAM,eAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gCACnD,KAAK,EAAE;oCACL,IAAI,EAAE;wCACJ,QAAQ,EAAE,MAAM,CAAC,UAAU;wCAC3B,IAAI,EAAE,aAAa;qCACpB;iCACF;gCACD,OAAO,EAAE;oCACP,aAAa,EAAE,IAAI;iCACpB;6BACF,CAAC,EAAA;;wBAVI,UAAU,GAAG,SAUjB;wBAGa,qBAAM,IAAI,CAAC,4BAA4B,CAAC;gCACrD,MAAM,EAAE,MAAM,CAAC,MAAM;gCACrB,UAAU,EAAE,MAAM,CAAC,UAAU;gCAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;gCACnC,SAAS,EAAE,MAAM,CAAC,SAAS;gCAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;gCACrB,YAAY,EAAE,MAAM,CAAC,YAAY;gCACjC,cAAc,EAAE,UAAU;6BAC3B,CAAC,EAAA;;wBARI,MAAM,GAAG,SAQb;6BAGE,CAAA,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAA,EAA7B,wBAA6B;wBACV,qBAAM,eAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gCACpD,IAAI,EAAE;oCACJ,KAAK,EAAE,4BAAqB,MAAM,CAAC,UAAU,CAAE;oCAC/C,WAAW,EAAE,iDAA0C,MAAM,CAAC,UAAU,CAAE;oCAC1E,IAAI,EAAE,wBAAiB,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,cAAI,IAAI,CAAC,GAAG,EAAE,CAAE;oCAC3F,UAAU,EAAE,cAAc;oCAC1B,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,EAAE;oCACnD,QAAQ,EAAE,iBAAiB,EAAE,mBAAmB;oCAChD,SAAS,EAAE,MAAM,CAAC,MAAM;oCACxB,QAAQ,EAAE,IAAI;iCACf;6BACF,CAAC,EAAA;;wBAXI,YAAY,GAAG,SAWnB;wBAEF,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,EAAE,CAAC;;4BAG3C,sBAAO,MAAM,EAAC;;;wBAEd,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gCAC/D,SAAS,EAAE,cAAc;gCACzB,YAAY,EAAE,IAAI;6BACnB,EAAC;;;;;KAEL;IAED;;OAEG;IACG,0DAAyB,GAA/B,UAAgC,MAI/B;uCAAG,OAAO;;;;;;wBAGY,qBAAM,2DAA4B,CAAC,aAAa,CACjE,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,QAAQ,CAChB,EAAA;;wBAHK,UAAU,GAAG,SAGlB;wBAGc,qBAAM,IAAI,CAAC,yBAAyB,CAAC;gCAClD,KAAK,EAAE,MAAM,CAAC,KAAK;gCACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gCACzB,YAAY,EAAE,MAAM,CAAC,YAAY;gCACjC,YAAY,EAAE,UAAU;6BACzB,CAAC,EAAA;;wBALI,MAAM,GAAG,SAKb;6BAGE,CAAA,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAA,EAA7B,wBAA6B;wBACjB,qBAAM,2DAA4B,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAA;;wBAAvE,KAAK,GAAG,SAA+D;6BACzE,KAAK,EAAL,wBAAK;wBACP,qBAAM,eAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gCAClC,IAAI,EAAE;oCACJ,OAAO,EAAE,KAAK,CAAC,EAAE;oCACjB,MAAM,EAAE,MAAM,CAAC,QAAQ,IAAI,QAAQ;oCACnC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;wCACxC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;4CAClC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK;oCACxD,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI;oCACjG,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC;oCAC9C,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;wCAC9C,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;4CACpC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW;oCAC3D,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,CAAC;oCAC3D,UAAU,EAAE,iBAAiB;oCAC7B,QAAQ,EAAE,IAAI,IAAI,EAAE;oCACpB,QAAQ,EAAE,IAAI;iCACf;6BACF,CAAC,EAAA;;wBAjBF,SAiBE,CAAC;;4BAIP,sBAAO,MAAM,EAAC;;;wBAEd,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gCAC/D,SAAS,EAAE,cAAc;gCACzB,YAAY,EAAE,IAAI;6BACnB,EAAC;;;;;KAEL;IAED;;OAEG;IACW,8DAA6B,GAA3C,UAA4C,eAAyB;uCAAG,OAAO;;;;;;wBAGrD,qBAAM,eAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gCAChD,KAAK,EAAE;oCACL,EAAE,EAAE,eAAe,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,CAAC;wCAClC,IAAI,EAAE;4CACJ,QAAQ,EAAE,OAAO;4CACjB,IAAI,EAAE,aAAa;yCACpB;qCACF,CAAC,EALiC,CAKjC,CAAC;iCACJ;gCACD,IAAI,EAAE,CAAC;6BACR,CAAC,EAAA;;wBAVI,aAAa,GAAG,SAUpB;wBAEF,sBAAO,aAAa,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,EAAV,CAAU,CAAC,EAAC;;;wBAE9C,8BAA8B;wBAC9B,sBAAO,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAC;;;;;KAErE;IACH,6BAAC;AAAD,CAAC,AAnYD,IAmYC;AAnYY,wDAAsB;AAqYnC,4BAA4B;AACf,QAAA,sBAAsB,GAAG,sBAAsB,CAAC,WAAW,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/EdgeCaseHandlerService.ts"],
      sourcesContent: ["import { EdgeCaseHandler, EdgeCaseResult, EdgeCaseOptions } from './EdgeCaseHandler';\nimport { SkillAssessmentEngine } from './SkillAssessmentEngine';\nimport { SkillMarketDataService } from './SkillMarketDataService';\nimport { PersonalizedLearningPathService } from './PersonalizedLearningPathService';\nimport { skillServiceFactory } from './ServiceFactory';\nimport { prisma } from '@/lib/prisma';\nimport { geminiService } from '@/lib/services/geminiService';\nimport { skillGapPerformanceOptimizer } from '@/lib/performance/SkillGapPerformanceOptimizer';\n\n/**\n * Service factory for creating EdgeCaseHandler with proper dependencies\n */\nexport class EdgeCaseHandlerService {\n  private static instance: EdgeCaseHandlerService;\n  private edgeCaseHandler: EdgeCaseHandler;\n  private assessmentEngine: SkillAssessmentEngine;\n  private marketDataService: SkillMarketDataService;\n  private learningPathService: PersonalizedLearningPathService;\n\n  private constructor() {\n    // Get properly integrated services from factory\n    const services = skillServiceFactory.getServices();\n    this.edgeCaseHandler = services.edgeCaseHandler;\n    this.assessmentEngine = services.assessmentEngine;\n    this.marketDataService = services.marketDataService;\n    this.learningPathService = services.learningPathService;\n  }\n\n  /**\n   * Get singleton instance of EdgeCaseHandlerService\n   */\n  public static getInstance(): EdgeCaseHandlerService {\n    if (!EdgeCaseHandlerService.instance) {\n      EdgeCaseHandlerService.instance = new EdgeCaseHandlerService();\n    }\n    return EdgeCaseHandlerService.instance;\n  }\n\n  /**\n   * Handle skill assessment with comprehensive error handling\n   */\n  async handleSkillAssessment(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {\n    return this.edgeCaseHandler.handleSkillAssessment(request, options);\n  }\n\n  /**\n   * Handle learning path generation with comprehensive error handling\n   */\n  async handleLearningPathGeneration(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {\n    return this.edgeCaseHandler.handleLearningPathGeneration(request, options);\n  }\n\n  /**\n   * Handle market data retrieval with comprehensive error handling\n   */\n  async handleMarketDataRetrieval(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {\n    return this.edgeCaseHandler.handleMarketDataRequest(request, options);\n  }\n\n  /**\n   * Handle user data retrieval with comprehensive error handling\n   */\n  async handleUserDataRetrieval(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {\n    try {\n      const { userId, dataType = 'assessments' } = request;\n\n      if (!userId) {\n        return {\n          success: false,\n          error: 'User ID is required',\n          errorType: 'VALIDATION_ERROR',\n          fallbackData: null\n        };\n      }\n\n      // Get user data based on type\n      let data;\n      switch (dataType) {\n        case 'assessments':\n        case 'skill_assessments':\n          data = await prisma.skillAssessment.findMany({\n            where: { userId, isActive: true },\n            include: {\n              skill: true\n            },\n            orderBy: { assessmentDate: 'desc' }\n          });\n          break;\n        case 'learningPaths':\n          data = await prisma.userLearningPath.findMany({\n            where: { userId },\n            include: { learningPath: true },\n            orderBy: { createdAt: 'desc' }\n          });\n          break;\n        default:\n          data = await prisma.skillAssessment.findMany({\n            where: { userId, isActive: true },\n            include: {\n              skill: true\n            },\n            orderBy: { assessmentDate: 'desc' }\n          });\n      }\n\n      return {\n        success: true,\n        data,\n        fallbackData: null\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        errorType: 'SYSTEM_ERROR',\n        fallbackData: []\n      };\n    }\n  }\n\n  /**\n   * Get error statistics from EdgeCaseHandler\n   */\n  async getErrorStatistics() {\n    return this.edgeCaseHandler.getErrorStatistics();\n  }\n\n  /**\n   * Get health status from EdgeCaseHandler\n   */\n  async getHealthStatus() {\n    return this.edgeCaseHandler.getHealthStatus();\n  }\n\n  /**\n   * Get performance metrics and cache statistics\n   */\n  getPerformanceMetrics() {\n    return {\n      metrics: skillGapPerformanceOptimizer.getPerformanceMetrics(),\n      cacheStats: skillGapPerformanceOptimizer.getCacheStats(),\n    };\n  }\n\n  /**\n   * Clear all performance caches\n   */\n  clearPerformanceCaches() {\n    skillGapPerformanceOptimizer.clearCaches();\n  }\n\n  /**\n   * Enhanced skill assessment that integrates with database and AI services\n   */\n  async createSkillAssessmentWithDatabase(params: {\n    userId: string;\n    skillIds: string[];\n    careerPathId?: string;\n    assessmentType?: string;\n  }): Promise<EdgeCaseResult> {\n    try {\n      // Validate user exists (with performance optimization)\n      let user = await skillGapPerformanceOptimizer.getUser(params.userId);\n\n      // No automatic user creation in development - <EMAIL> has been eliminated\n      if (!user) {\n        console.log('User not found and automatic creation disabled:', params.userId);\n        return {\n          success: false,\n          error: 'User not found - authentication required',\n          errorType: 'AUTHENTICATION_ERROR' as const,\n          retryable: false\n        };\n      }\n\n      // Validate skills exist (with batch optimization)\n      const skills = await skillGapPerformanceOptimizer.getSkills(params.skillIds);\n\n      if (skills.length !== params.skillIds.length) {\n        const foundSkillIds = skills.map(s => s.id);\n        const missingSkillIds = params.skillIds.filter(id => !foundSkillIds.includes(id));\n\n        return {\n          success: false,\n          error: `Skills not found: ${missingSkillIds.join(', ')}`,\n          errorType: 'BUSINESS_LOGIC_ERROR',\n          suggestedAlternatives: await this.getSuggestedSkillAlternatives(missingSkillIds)\n        };\n      }\n\n      // Use EdgeCaseHandler for comprehensive error handling\n      const result = await this.handleSkillAssessment({\n        userId: params.userId,\n        skillIds: params.skillIds,\n        careerPathId: params.careerPathId,\n        assessmentType: params.assessmentType || 'comprehensive'\n      });\n\n      // If successful, save to database using upsert to handle unique constraints\n      if (result.success && result.data) {\n        const assessment = await prisma.skillAssessment.upsert({\n          where: {\n            userId_skillId_assessmentType: {\n              userId: params.userId,\n              skillId: params.skillIds[0], // For now, handle single skill\n              assessmentType: (params.assessmentType as any) || 'SELF_ASSESSMENT',\n            },\n          },\n          update: {\n            selfRating: result.data.overallScore || 5,\n            confidenceLevel: result.data.averageConfidence || 5,\n            assessmentDate: new Date(),\n            notes: `EdgeCaseHandler assessment - ID: ${result.data.id}, Skills: ${params.skillIds.join(', ')}, Career Path: ${params.careerPathId}`\n          },\n          create: {\n            userId: params.userId,\n            skillId: params.skillIds[0], // For now, handle single skill\n            selfRating: result.data.overallScore || 5,\n            confidenceLevel: result.data.averageConfidence || 5,\n            assessmentType: (params.assessmentType as any) || 'SELF_ASSESSMENT',\n            assessmentDate: new Date(),\n            notes: `EdgeCaseHandler assessment - ID: ${result.data.id}, Skills: ${params.skillIds.join(', ')}, Career Path: ${params.careerPathId}`\n          }\n        });\n\n        result.data.databaseId = assessment.id;\n      }\n\n      return result;\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        errorType: 'SYSTEM_ERROR',\n        fallbackData: null\n      };\n    }\n  }\n\n  /**\n   * Enhanced learning path generation that integrates with database and AI services\n   */\n  async generateLearningPathWithDatabase(params: {\n    userId: string;\n    targetRole: string;\n    currentSkills: Array<{ skill: string; level: number }>;\n    timeframe?: number;\n    budget?: number;\n    availability?: number;\n  }): Promise<EdgeCaseResult> {\n    try {\n      // Validate user exists (with performance optimization)\n      const user = await skillGapPerformanceOptimizer.getUser(params.userId);\n\n      if (!user) {\n        return {\n          success: false,\n          error: 'User not found',\n          errorType: 'VALIDATION_ERROR',\n          fallbackData: null\n        };\n      }\n\n      // Get career path from database\n      const careerPath = await prisma.careerPath.findFirst({\n        where: {\n          name: {\n            contains: params.targetRole,\n            mode: 'insensitive'\n          }\n        },\n        include: {\n          relatedSkills: true\n        }\n      });\n\n      // Use EdgeCaseHandler for comprehensive error handling\n      const result = await this.handleLearningPathGeneration({\n        userId: params.userId,\n        targetRole: params.targetRole,\n        currentSkills: params.currentSkills,\n        timeframe: params.timeframe,\n        budget: params.budget,\n        availability: params.availability,\n        careerPathData: careerPath\n      });\n\n      // If successful, save to database\n      if (result.success && result.data) {\n        const learningPath = await prisma.learningPath.create({\n          data: {\n            title: `Learning Path for ${params.targetRole}`,\n            description: `Personalized learning path to become a ${params.targetRole}`,\n            slug: `learning-path-${params.targetRole.toLowerCase().replace(/\\s+/g, '-')}-${Date.now()}`,\n            difficulty: 'INTERMEDIATE',\n            estimatedHours: result.data.estimatedDuration || 12,\n            category: 'WEB_DEVELOPMENT', // Default category\n            createdBy: params.userId,\n            isActive: true\n          }\n        });\n\n        result.data.databaseId = learningPath.id;\n      }\n\n      return result;\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        errorType: 'SYSTEM_ERROR',\n        fallbackData: null\n      };\n    }\n  }\n\n  /**\n   * Enhanced market data retrieval that integrates with database\n   */\n  async getMarketDataWithDatabase(params: {\n    skill: string;\n    location?: string;\n    forceRefresh?: boolean;\n  }): Promise<EdgeCaseResult> {\n    try {\n      // Use performance-optimized market data retrieval\n      const marketData = await skillGapPerformanceOptimizer.getMarketData(\n        params.skill,\n        params.location\n      );\n\n      // Use EdgeCaseHandler for comprehensive error handling\n      const result = await this.handleMarketDataRetrieval({\n        skill: params.skill,\n        location: params.location,\n        forceRefresh: params.forceRefresh,\n        existingData: marketData\n      });\n\n      // If successful and we have new data, save to database\n      if (result.success && result.data) {\n        const skill = await skillGapPerformanceOptimizer.getSkillByName(params.skill);\n        if (skill) {\n          await prisma.skillMarketData.create({\n            data: {\n              skillId: skill.id,\n              region: params.location || 'GLOBAL',\n              demandLevel: result.data.demand > 70 ? 'VERY_HIGH' :\n                          result.data.demand > 50 ? 'HIGH' :\n                          result.data.demand > 30 ? 'MODERATE' : 'LOW',\n              averageSalaryImpact: result.data.averageSalary ? (result.data.averageSalary / 75000) * 100 : null,\n              jobPostingsCount: result.data.jobPostings || 0,\n              growthTrend: result.data.growth > 10 ? 'RAPIDLY_GROWING' :\n                          result.data.growth > 5 ? 'GROWING' :\n                          result.data.growth > 0 ? 'STABLE' : 'DECLINING',\n              industryRelevance: result.data.industries || ['Technology'],\n              dataSource: 'EdgeCaseHandler',\n              dataDate: new Date(),\n              isActive: true\n            }\n          });\n        }\n      }\n\n      return result;\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        errorType: 'SYSTEM_ERROR',\n        fallbackData: null\n      };\n    }\n  }\n\n  /**\n   * Get suggested skill alternatives for missing skills\n   */\n  private async getSuggestedSkillAlternatives(missingSkillIds: string[]): Promise<string[]> {\n    try {\n      // Get similar skills from database\n      const similarSkills = await prisma.skill.findMany({\n        where: {\n          OR: missingSkillIds.map(skillId => ({\n            name: {\n              contains: skillId,\n              mode: 'insensitive'\n            }\n          }))\n        },\n        take: 5\n      });\n\n      return similarSkills.map(skill => skill.name);\n    } catch (error) {\n      // Return default alternatives\n      return ['JavaScript', 'React', 'Node.js', 'Python', 'TypeScript'];\n    }\n  }\n}\n\n// Export singleton instance\nexport const edgeCaseHandlerService = EdgeCaseHandlerService.getInstance();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ae58b0498986cc3d3a625d21df9639315ef947ef"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_10oh7b63u = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_10oh7b63u();
var __awaiter =
/* istanbul ignore next */
(cov_10oh7b63u().s[0]++,
/* istanbul ignore next */
(cov_10oh7b63u().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_10oh7b63u().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_10oh7b63u().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_10oh7b63u().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[1]++;
    cov_10oh7b63u().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_10oh7b63u().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_10oh7b63u().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[2]++;
      cov_10oh7b63u().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_10oh7b63u().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_10oh7b63u().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_10oh7b63u().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[4]++;
      cov_10oh7b63u().s[4]++;
      try {
        /* istanbul ignore next */
        cov_10oh7b63u().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_10oh7b63u().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[5]++;
      cov_10oh7b63u().s[7]++;
      try {
        /* istanbul ignore next */
        cov_10oh7b63u().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_10oh7b63u().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[6]++;
      cov_10oh7b63u().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_10oh7b63u().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_10oh7b63u().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_10oh7b63u().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_10oh7b63u().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_10oh7b63u().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_10oh7b63u().s[12]++,
/* istanbul ignore next */
(cov_10oh7b63u().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_10oh7b63u().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_10oh7b63u().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_10oh7b63u().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_10oh7b63u().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_10oh7b63u().f[8]++;
        cov_10oh7b63u().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_10oh7b63u().b[6][0]++;
          cov_10oh7b63u().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_10oh7b63u().b[6][1]++;
        }
        cov_10oh7b63u().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_10oh7b63u().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_10oh7b63u().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_10oh7b63u().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_10oh7b63u().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_10oh7b63u().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_10oh7b63u().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_10oh7b63u().f[9]++;
    cov_10oh7b63u().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[10]++;
    cov_10oh7b63u().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[11]++;
      cov_10oh7b63u().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[12]++;
    cov_10oh7b63u().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_10oh7b63u().b[9][0]++;
      cov_10oh7b63u().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_10oh7b63u().b[9][1]++;
    }
    cov_10oh7b63u().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_10oh7b63u().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_10oh7b63u().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_10oh7b63u().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_10oh7b63u().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_10oh7b63u().s[25]++;
      try {
        /* istanbul ignore next */
        cov_10oh7b63u().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_10oh7b63u().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_10oh7b63u().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_10oh7b63u().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_10oh7b63u().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_10oh7b63u().b[15][0]++,
        /* istanbul ignore next */
        (cov_10oh7b63u().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_10oh7b63u().b[16][1]++,
        /* istanbul ignore next */
        (cov_10oh7b63u().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_10oh7b63u().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_10oh7b63u().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_10oh7b63u().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_10oh7b63u().b[12][0]++;
          cov_10oh7b63u().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_10oh7b63u().b[12][1]++;
        }
        cov_10oh7b63u().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_10oh7b63u().b[18][0]++;
          cov_10oh7b63u().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_10oh7b63u().b[18][1]++;
        }
        cov_10oh7b63u().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_10oh7b63u().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_10oh7b63u().b[19][1]++;
            cov_10oh7b63u().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_10oh7b63u().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_10oh7b63u().b[19][2]++;
            cov_10oh7b63u().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_10oh7b63u().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_10oh7b63u().b[19][3]++;
            cov_10oh7b63u().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_10oh7b63u().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_10oh7b63u().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_10oh7b63u().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_10oh7b63u().b[19][4]++;
            cov_10oh7b63u().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_10oh7b63u().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_10oh7b63u().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_10oh7b63u().b[19][5]++;
            cov_10oh7b63u().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_10oh7b63u().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_10oh7b63u().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_10oh7b63u().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_10oh7b63u().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_10oh7b63u().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[20][0]++;
              cov_10oh7b63u().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_10oh7b63u().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[20][1]++;
            }
            cov_10oh7b63u().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_10oh7b63u().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_10oh7b63u().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_10oh7b63u().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_10oh7b63u().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[23][0]++;
              cov_10oh7b63u().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_10oh7b63u().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[23][1]++;
            }
            cov_10oh7b63u().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_10oh7b63u().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_10oh7b63u().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[25][0]++;
              cov_10oh7b63u().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_10oh7b63u().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_10oh7b63u().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[25][1]++;
            }
            cov_10oh7b63u().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_10oh7b63u().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_10oh7b63u().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[27][0]++;
              cov_10oh7b63u().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_10oh7b63u().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_10oh7b63u().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[27][1]++;
            }
            cov_10oh7b63u().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[29][0]++;
              cov_10oh7b63u().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[29][1]++;
            }
            cov_10oh7b63u().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_10oh7b63u().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_10oh7b63u().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_10oh7b63u().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_10oh7b63u().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_10oh7b63u().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_10oh7b63u().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_10oh7b63u().b[30][0]++;
      cov_10oh7b63u().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_10oh7b63u().b[30][1]++;
    }
    cov_10oh7b63u().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_10oh7b63u().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_10oh7b63u().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_10oh7b63u().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_10oh7b63u().s[68]++;
exports.edgeCaseHandlerService = exports.EdgeCaseHandlerService = void 0;
var ServiceFactory_1 =
/* istanbul ignore next */
(cov_10oh7b63u().s[69]++, require("./ServiceFactory"));
var prisma_1 =
/* istanbul ignore next */
(cov_10oh7b63u().s[70]++, require("@/lib/prisma"));
var SkillGapPerformanceOptimizer_1 =
/* istanbul ignore next */
(cov_10oh7b63u().s[71]++, require("@/lib/performance/SkillGapPerformanceOptimizer"));
/**
 * Service factory for creating EdgeCaseHandler with proper dependencies
 */
var EdgeCaseHandlerService =
/* istanbul ignore next */
(/** @class */cov_10oh7b63u().s[72]++, function () {
  /* istanbul ignore next */
  cov_10oh7b63u().f[13]++;
  function EdgeCaseHandlerService() {
    /* istanbul ignore next */
    cov_10oh7b63u().f[14]++;
    // Get properly integrated services from factory
    var services =
    /* istanbul ignore next */
    (cov_10oh7b63u().s[73]++, ServiceFactory_1.skillServiceFactory.getServices());
    /* istanbul ignore next */
    cov_10oh7b63u().s[74]++;
    this.edgeCaseHandler = services.edgeCaseHandler;
    /* istanbul ignore next */
    cov_10oh7b63u().s[75]++;
    this.assessmentEngine = services.assessmentEngine;
    /* istanbul ignore next */
    cov_10oh7b63u().s[76]++;
    this.marketDataService = services.marketDataService;
    /* istanbul ignore next */
    cov_10oh7b63u().s[77]++;
    this.learningPathService = services.learningPathService;
  }
  /**
   * Get singleton instance of EdgeCaseHandlerService
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[78]++;
  EdgeCaseHandlerService.getInstance = function () {
    /* istanbul ignore next */
    cov_10oh7b63u().f[15]++;
    cov_10oh7b63u().s[79]++;
    if (!EdgeCaseHandlerService.instance) {
      /* istanbul ignore next */
      cov_10oh7b63u().b[32][0]++;
      cov_10oh7b63u().s[80]++;
      EdgeCaseHandlerService.instance = new EdgeCaseHandlerService();
    } else
    /* istanbul ignore next */
    {
      cov_10oh7b63u().b[32][1]++;
    }
    cov_10oh7b63u().s[81]++;
    return EdgeCaseHandlerService.instance;
  };
  /**
   * Handle skill assessment with comprehensive error handling
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[82]++;
  EdgeCaseHandlerService.prototype.handleSkillAssessment = function (request_1) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[16]++;
    cov_10oh7b63u().s[83]++;
    return __awaiter(this, arguments, Promise, function (request, options) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[17]++;
      cov_10oh7b63u().s[84]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_10oh7b63u().b[33][0]++;
        cov_10oh7b63u().s[85]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_10oh7b63u().b[33][1]++;
      }
      cov_10oh7b63u().s[86]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[18]++;
        cov_10oh7b63u().s[87]++;
        return [2 /*return*/, this.edgeCaseHandler.handleSkillAssessment(request, options)];
      });
    });
  };
  /**
   * Handle learning path generation with comprehensive error handling
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[88]++;
  EdgeCaseHandlerService.prototype.handleLearningPathGeneration = function (request_1) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[19]++;
    cov_10oh7b63u().s[89]++;
    return __awaiter(this, arguments, Promise, function (request, options) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[20]++;
      cov_10oh7b63u().s[90]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_10oh7b63u().b[34][0]++;
        cov_10oh7b63u().s[91]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_10oh7b63u().b[34][1]++;
      }
      cov_10oh7b63u().s[92]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[21]++;
        cov_10oh7b63u().s[93]++;
        return [2 /*return*/, this.edgeCaseHandler.handleLearningPathGeneration(request, options)];
      });
    });
  };
  /**
   * Handle market data retrieval with comprehensive error handling
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[94]++;
  EdgeCaseHandlerService.prototype.handleMarketDataRetrieval = function (request_1) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[22]++;
    cov_10oh7b63u().s[95]++;
    return __awaiter(this, arguments, Promise, function (request, options) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[23]++;
      cov_10oh7b63u().s[96]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_10oh7b63u().b[35][0]++;
        cov_10oh7b63u().s[97]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_10oh7b63u().b[35][1]++;
      }
      cov_10oh7b63u().s[98]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[24]++;
        cov_10oh7b63u().s[99]++;
        return [2 /*return*/, this.edgeCaseHandler.handleMarketDataRequest(request, options)];
      });
    });
  };
  /**
   * Handle user data retrieval with comprehensive error handling
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[100]++;
  EdgeCaseHandlerService.prototype.handleUserDataRetrieval = function (request_1) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[25]++;
    cov_10oh7b63u().s[101]++;
    return __awaiter(this, arguments, Promise, function (request, options) {
      /* istanbul ignore next */
      cov_10oh7b63u().f[26]++;
      var userId, _a, dataType, data, _b, error_1;
      /* istanbul ignore next */
      cov_10oh7b63u().s[102]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_10oh7b63u().b[36][0]++;
        cov_10oh7b63u().s[103]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_10oh7b63u().b[36][1]++;
      }
      cov_10oh7b63u().s[104]++;
      return __generator(this, function (_c) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[27]++;
        cov_10oh7b63u().s[105]++;
        switch (_c.label) {
          case 0:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][0]++;
            cov_10oh7b63u().s[106]++;
            _c.trys.push([0, 8,, 9]);
            /* istanbul ignore next */
            cov_10oh7b63u().s[107]++;
            userId = request.userId, _a = request.dataType, dataType = _a === void 0 ?
            /* istanbul ignore next */
            (cov_10oh7b63u().b[38][0]++, 'assessments') :
            /* istanbul ignore next */
            (cov_10oh7b63u().b[38][1]++, _a);
            /* istanbul ignore next */
            cov_10oh7b63u().s[108]++;
            if (!userId) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[39][0]++;
              cov_10oh7b63u().s[109]++;
              return [2 /*return*/, {
                success: false,
                error: 'User ID is required',
                errorType: 'VALIDATION_ERROR',
                fallbackData: null
              }];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[39][1]++;
            }
            cov_10oh7b63u().s[110]++;
            data = void 0;
            /* istanbul ignore next */
            cov_10oh7b63u().s[111]++;
            _b = dataType;
            /* istanbul ignore next */
            cov_10oh7b63u().s[112]++;
            switch (_b) {
              case 'assessments':
                /* istanbul ignore next */
                cov_10oh7b63u().b[40][0]++;
                cov_10oh7b63u().s[113]++;
                return [3 /*break*/, 1];
              case 'skill_assessments':
                /* istanbul ignore next */
                cov_10oh7b63u().b[40][1]++;
                cov_10oh7b63u().s[114]++;
                return [3 /*break*/, 1];
              case 'learningPaths':
                /* istanbul ignore next */
                cov_10oh7b63u().b[40][2]++;
                cov_10oh7b63u().s[115]++;
                return [3 /*break*/, 3];
            }
            /* istanbul ignore next */
            cov_10oh7b63u().s[116]++;
            return [3 /*break*/, 5];
          case 1:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][1]++;
            cov_10oh7b63u().s[117]++;
            return [4 /*yield*/, prisma_1.prisma.skillAssessment.findMany({
              where: {
                userId: userId,
                isActive: true
              },
              include: {
                skill: true
              },
              orderBy: {
                assessmentDate: 'desc'
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][2]++;
            cov_10oh7b63u().s[118]++;
            data = _c.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[119]++;
            return [3 /*break*/, 7];
          case 3:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][3]++;
            cov_10oh7b63u().s[120]++;
            return [4 /*yield*/, prisma_1.prisma.userLearningPath.findMany({
              where: {
                userId: userId
              },
              include: {
                learningPath: true
              },
              orderBy: {
                createdAt: 'desc'
              }
            })];
          case 4:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][4]++;
            cov_10oh7b63u().s[121]++;
            data = _c.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[122]++;
            return [3 /*break*/, 7];
          case 5:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][5]++;
            cov_10oh7b63u().s[123]++;
            return [4 /*yield*/, prisma_1.prisma.skillAssessment.findMany({
              where: {
                userId: userId,
                isActive: true
              },
              include: {
                skill: true
              },
              orderBy: {
                assessmentDate: 'desc'
              }
            })];
          case 6:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][6]++;
            cov_10oh7b63u().s[124]++;
            data = _c.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[125]++;
            _c.label = 7;
          case 7:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][7]++;
            cov_10oh7b63u().s[126]++;
            return [2 /*return*/, {
              success: true,
              data: data,
              fallbackData: null
            }];
          case 8:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][8]++;
            cov_10oh7b63u().s[127]++;
            error_1 = _c.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[128]++;
            return [2 /*return*/, {
              success: false,
              error: error_1 instanceof Error ?
              /* istanbul ignore next */
              (cov_10oh7b63u().b[41][0]++, error_1.message) :
              /* istanbul ignore next */
              (cov_10oh7b63u().b[41][1]++, 'Unknown error'),
              errorType: 'SYSTEM_ERROR',
              fallbackData: []
            }];
          case 9:
            /* istanbul ignore next */
            cov_10oh7b63u().b[37][9]++;
            cov_10oh7b63u().s[129]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Get error statistics from EdgeCaseHandler
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[130]++;
  EdgeCaseHandlerService.prototype.getErrorStatistics = function () {
    /* istanbul ignore next */
    cov_10oh7b63u().f[28]++;
    cov_10oh7b63u().s[131]++;
    return __awaiter(this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_10oh7b63u().f[29]++;
      cov_10oh7b63u().s[132]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[30]++;
        cov_10oh7b63u().s[133]++;
        return [2 /*return*/, this.edgeCaseHandler.getErrorStatistics()];
      });
    });
  };
  /**
   * Get health status from EdgeCaseHandler
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[134]++;
  EdgeCaseHandlerService.prototype.getHealthStatus = function () {
    /* istanbul ignore next */
    cov_10oh7b63u().f[31]++;
    cov_10oh7b63u().s[135]++;
    return __awaiter(this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_10oh7b63u().f[32]++;
      cov_10oh7b63u().s[136]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[33]++;
        cov_10oh7b63u().s[137]++;
        return [2 /*return*/, this.edgeCaseHandler.getHealthStatus()];
      });
    });
  };
  /**
   * Get performance metrics and cache statistics
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[138]++;
  EdgeCaseHandlerService.prototype.getPerformanceMetrics = function () {
    /* istanbul ignore next */
    cov_10oh7b63u().f[34]++;
    cov_10oh7b63u().s[139]++;
    return {
      metrics: SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.getPerformanceMetrics(),
      cacheStats: SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.getCacheStats()
    };
  };
  /**
   * Clear all performance caches
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[140]++;
  EdgeCaseHandlerService.prototype.clearPerformanceCaches = function () {
    /* istanbul ignore next */
    cov_10oh7b63u().f[35]++;
    cov_10oh7b63u().s[141]++;
    SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.clearCaches();
  };
  /**
   * Enhanced skill assessment that integrates with database and AI services
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[142]++;
  EdgeCaseHandlerService.prototype.createSkillAssessmentWithDatabase = function (params) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[36]++;
    cov_10oh7b63u().s[143]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_10oh7b63u().f[37]++;
      var user, skills, foundSkillIds_1, missingSkillIds, result, assessment, error_2;
      var _a;
      /* istanbul ignore next */
      cov_10oh7b63u().s[144]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[38]++;
        cov_10oh7b63u().s[145]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][0]++;
            cov_10oh7b63u().s[146]++;
            _b.trys.push([0, 8,, 9]);
            /* istanbul ignore next */
            cov_10oh7b63u().s[147]++;
            return [4 /*yield*/, SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.getUser(params.userId)];
          case 1:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][1]++;
            cov_10oh7b63u().s[148]++;
            user = _b.sent();
            // No automatic user creation in development - <EMAIL> has been eliminated
            /* istanbul ignore next */
            cov_10oh7b63u().s[149]++;
            if (!user) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[43][0]++;
              cov_10oh7b63u().s[150]++;
              console.log('User not found and automatic creation disabled:', params.userId);
              /* istanbul ignore next */
              cov_10oh7b63u().s[151]++;
              return [2 /*return*/, {
                success: false,
                error: 'User not found - authentication required',
                errorType: 'AUTHENTICATION_ERROR',
                retryable: false
              }];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[43][1]++;
            }
            cov_10oh7b63u().s[152]++;
            return [4 /*yield*/, SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.getSkills(params.skillIds)];
          case 2:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][2]++;
            cov_10oh7b63u().s[153]++;
            skills = _b.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[154]++;
            if (!(skills.length !== params.skillIds.length)) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[44][0]++;
              cov_10oh7b63u().s[155]++;
              return [3 /*break*/, 4];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[44][1]++;
            }
            cov_10oh7b63u().s[156]++;
            foundSkillIds_1 = skills.map(function (s) {
              /* istanbul ignore next */
              cov_10oh7b63u().f[39]++;
              cov_10oh7b63u().s[157]++;
              return s.id;
            });
            /* istanbul ignore next */
            cov_10oh7b63u().s[158]++;
            missingSkillIds = params.skillIds.filter(function (id) {
              /* istanbul ignore next */
              cov_10oh7b63u().f[40]++;
              cov_10oh7b63u().s[159]++;
              return !foundSkillIds_1.includes(id);
            });
            /* istanbul ignore next */
            cov_10oh7b63u().s[160]++;
            _a = {
              success: false,
              error: "Skills not found: ".concat(missingSkillIds.join(', ')),
              errorType: 'BUSINESS_LOGIC_ERROR'
            };
            /* istanbul ignore next */
            cov_10oh7b63u().s[161]++;
            return [4 /*yield*/, this.getSuggestedSkillAlternatives(missingSkillIds)];
          case 3:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][3]++;
            cov_10oh7b63u().s[162]++;
            return [2 /*return*/, (_a.suggestedAlternatives = _b.sent(), _a)];
          case 4:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][4]++;
            cov_10oh7b63u().s[163]++;
            return [4 /*yield*/, this.handleSkillAssessment({
              userId: params.userId,
              skillIds: params.skillIds,
              careerPathId: params.careerPathId,
              assessmentType:
              /* istanbul ignore next */
              (cov_10oh7b63u().b[45][0]++, params.assessmentType) ||
              /* istanbul ignore next */
              (cov_10oh7b63u().b[45][1]++, 'comprehensive')
            })];
          case 5:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][5]++;
            cov_10oh7b63u().s[164]++;
            result = _b.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[165]++;
            if (!(
            /* istanbul ignore next */
            (cov_10oh7b63u().b[47][0]++, result.success) &&
            /* istanbul ignore next */
            (cov_10oh7b63u().b[47][1]++, result.data))) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[46][0]++;
              cov_10oh7b63u().s[166]++;
              return [3 /*break*/, 7];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[46][1]++;
            }
            cov_10oh7b63u().s[167]++;
            return [4 /*yield*/, prisma_1.prisma.skillAssessment.upsert({
              where: {
                userId_skillId_assessmentType: {
                  userId: params.userId,
                  skillId: params.skillIds[0],
                  // For now, handle single skill
                  assessmentType:
                  /* istanbul ignore next */
                  (cov_10oh7b63u().b[48][0]++, params.assessmentType) ||
                  /* istanbul ignore next */
                  (cov_10oh7b63u().b[48][1]++, 'SELF_ASSESSMENT')
                }
              },
              update: {
                selfRating:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[49][0]++, result.data.overallScore) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[49][1]++, 5),
                confidenceLevel:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[50][0]++, result.data.averageConfidence) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[50][1]++, 5),
                assessmentDate: new Date(),
                notes: "EdgeCaseHandler assessment - ID: ".concat(result.data.id, ", Skills: ").concat(params.skillIds.join(', '), ", Career Path: ").concat(params.careerPathId)
              },
              create: {
                userId: params.userId,
                skillId: params.skillIds[0],
                // For now, handle single skill
                selfRating:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[51][0]++, result.data.overallScore) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[51][1]++, 5),
                confidenceLevel:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[52][0]++, result.data.averageConfidence) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[52][1]++, 5),
                assessmentType:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[53][0]++, params.assessmentType) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[53][1]++, 'SELF_ASSESSMENT'),
                assessmentDate: new Date(),
                notes: "EdgeCaseHandler assessment - ID: ".concat(result.data.id, ", Skills: ").concat(params.skillIds.join(', '), ", Career Path: ").concat(params.careerPathId)
              }
            })];
          case 6:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][6]++;
            cov_10oh7b63u().s[168]++;
            assessment = _b.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[169]++;
            result.data.databaseId = assessment.id;
            /* istanbul ignore next */
            cov_10oh7b63u().s[170]++;
            _b.label = 7;
          case 7:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][7]++;
            cov_10oh7b63u().s[171]++;
            return [2 /*return*/, result];
          case 8:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][8]++;
            cov_10oh7b63u().s[172]++;
            error_2 = _b.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[173]++;
            return [2 /*return*/, {
              success: false,
              error: error_2 instanceof Error ?
              /* istanbul ignore next */
              (cov_10oh7b63u().b[54][0]++, error_2.message) :
              /* istanbul ignore next */
              (cov_10oh7b63u().b[54][1]++, 'Unknown error'),
              errorType: 'SYSTEM_ERROR',
              fallbackData: null
            }];
          case 9:
            /* istanbul ignore next */
            cov_10oh7b63u().b[42][9]++;
            cov_10oh7b63u().s[174]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Enhanced learning path generation that integrates with database and AI services
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[175]++;
  EdgeCaseHandlerService.prototype.generateLearningPathWithDatabase = function (params) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[41]++;
    cov_10oh7b63u().s[176]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_10oh7b63u().f[42]++;
      var user, careerPath, result, learningPath, error_3;
      /* istanbul ignore next */
      cov_10oh7b63u().s[177]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[43]++;
        cov_10oh7b63u().s[178]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][0]++;
            cov_10oh7b63u().s[179]++;
            _a.trys.push([0, 6,, 7]);
            /* istanbul ignore next */
            cov_10oh7b63u().s[180]++;
            return [4 /*yield*/, SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.getUser(params.userId)];
          case 1:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][1]++;
            cov_10oh7b63u().s[181]++;
            user = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[182]++;
            if (!user) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[56][0]++;
              cov_10oh7b63u().s[183]++;
              return [2 /*return*/, {
                success: false,
                error: 'User not found',
                errorType: 'VALIDATION_ERROR',
                fallbackData: null
              }];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[56][1]++;
            }
            cov_10oh7b63u().s[184]++;
            return [4 /*yield*/, prisma_1.prisma.careerPath.findFirst({
              where: {
                name: {
                  contains: params.targetRole,
                  mode: 'insensitive'
                }
              },
              include: {
                relatedSkills: true
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][2]++;
            cov_10oh7b63u().s[185]++;
            careerPath = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[186]++;
            return [4 /*yield*/, this.handleLearningPathGeneration({
              userId: params.userId,
              targetRole: params.targetRole,
              currentSkills: params.currentSkills,
              timeframe: params.timeframe,
              budget: params.budget,
              availability: params.availability,
              careerPathData: careerPath
            })];
          case 3:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][3]++;
            cov_10oh7b63u().s[187]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[188]++;
            if (!(
            /* istanbul ignore next */
            (cov_10oh7b63u().b[58][0]++, result.success) &&
            /* istanbul ignore next */
            (cov_10oh7b63u().b[58][1]++, result.data))) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[57][0]++;
              cov_10oh7b63u().s[189]++;
              return [3 /*break*/, 5];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[57][1]++;
            }
            cov_10oh7b63u().s[190]++;
            return [4 /*yield*/, prisma_1.prisma.learningPath.create({
              data: {
                title: "Learning Path for ".concat(params.targetRole),
                description: "Personalized learning path to become a ".concat(params.targetRole),
                slug: "learning-path-".concat(params.targetRole.toLowerCase().replace(/\s+/g, '-'), "-").concat(Date.now()),
                difficulty: 'INTERMEDIATE',
                estimatedHours:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[59][0]++, result.data.estimatedDuration) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[59][1]++, 12),
                category: 'WEB_DEVELOPMENT',
                // Default category
                createdBy: params.userId,
                isActive: true
              }
            })];
          case 4:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][4]++;
            cov_10oh7b63u().s[191]++;
            learningPath = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[192]++;
            result.data.databaseId = learningPath.id;
            /* istanbul ignore next */
            cov_10oh7b63u().s[193]++;
            _a.label = 5;
          case 5:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][5]++;
            cov_10oh7b63u().s[194]++;
            return [2 /*return*/, result];
          case 6:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][6]++;
            cov_10oh7b63u().s[195]++;
            error_3 = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[196]++;
            return [2 /*return*/, {
              success: false,
              error: error_3 instanceof Error ?
              /* istanbul ignore next */
              (cov_10oh7b63u().b[60][0]++, error_3.message) :
              /* istanbul ignore next */
              (cov_10oh7b63u().b[60][1]++, 'Unknown error'),
              errorType: 'SYSTEM_ERROR',
              fallbackData: null
            }];
          case 7:
            /* istanbul ignore next */
            cov_10oh7b63u().b[55][7]++;
            cov_10oh7b63u().s[197]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Enhanced market data retrieval that integrates with database
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[198]++;
  EdgeCaseHandlerService.prototype.getMarketDataWithDatabase = function (params) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[44]++;
    cov_10oh7b63u().s[199]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_10oh7b63u().f[45]++;
      var marketData, result, skill, error_4;
      /* istanbul ignore next */
      cov_10oh7b63u().s[200]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[46]++;
        cov_10oh7b63u().s[201]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][0]++;
            cov_10oh7b63u().s[202]++;
            _a.trys.push([0, 6,, 7]);
            /* istanbul ignore next */
            cov_10oh7b63u().s[203]++;
            return [4 /*yield*/, SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.getMarketData(params.skill, params.location)];
          case 1:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][1]++;
            cov_10oh7b63u().s[204]++;
            marketData = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[205]++;
            return [4 /*yield*/, this.handleMarketDataRetrieval({
              skill: params.skill,
              location: params.location,
              forceRefresh: params.forceRefresh,
              existingData: marketData
            })];
          case 2:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][2]++;
            cov_10oh7b63u().s[206]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[207]++;
            if (!(
            /* istanbul ignore next */
            (cov_10oh7b63u().b[63][0]++, result.success) &&
            /* istanbul ignore next */
            (cov_10oh7b63u().b[63][1]++, result.data))) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[62][0]++;
              cov_10oh7b63u().s[208]++;
              return [3 /*break*/, 5];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[62][1]++;
            }
            cov_10oh7b63u().s[209]++;
            return [4 /*yield*/, SkillGapPerformanceOptimizer_1.skillGapPerformanceOptimizer.getSkillByName(params.skill)];
          case 3:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][3]++;
            cov_10oh7b63u().s[210]++;
            skill = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[211]++;
            if (!skill) {
              /* istanbul ignore next */
              cov_10oh7b63u().b[64][0]++;
              cov_10oh7b63u().s[212]++;
              return [3 /*break*/, 5];
            } else
            /* istanbul ignore next */
            {
              cov_10oh7b63u().b[64][1]++;
            }
            cov_10oh7b63u().s[213]++;
            return [4 /*yield*/, prisma_1.prisma.skillMarketData.create({
              data: {
                skillId: skill.id,
                region:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[65][0]++, params.location) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[65][1]++, 'GLOBAL'),
                demandLevel: result.data.demand > 70 ?
                /* istanbul ignore next */
                (cov_10oh7b63u().b[66][0]++, 'VERY_HIGH') :
                /* istanbul ignore next */
                (cov_10oh7b63u().b[66][1]++, result.data.demand > 50 ?
                /* istanbul ignore next */
                (cov_10oh7b63u().b[67][0]++, 'HIGH') :
                /* istanbul ignore next */
                (cov_10oh7b63u().b[67][1]++, result.data.demand > 30 ?
                /* istanbul ignore next */
                (cov_10oh7b63u().b[68][0]++, 'MODERATE') :
                /* istanbul ignore next */
                (cov_10oh7b63u().b[68][1]++, 'LOW'))),
                averageSalaryImpact: result.data.averageSalary ?
                /* istanbul ignore next */
                (cov_10oh7b63u().b[69][0]++, result.data.averageSalary / 75000 * 100) :
                /* istanbul ignore next */
                (cov_10oh7b63u().b[69][1]++, null),
                jobPostingsCount:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[70][0]++, result.data.jobPostings) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[70][1]++, 0),
                growthTrend: result.data.growth > 10 ?
                /* istanbul ignore next */
                (cov_10oh7b63u().b[71][0]++, 'RAPIDLY_GROWING') :
                /* istanbul ignore next */
                (cov_10oh7b63u().b[71][1]++, result.data.growth > 5 ?
                /* istanbul ignore next */
                (cov_10oh7b63u().b[72][0]++, 'GROWING') :
                /* istanbul ignore next */
                (cov_10oh7b63u().b[72][1]++, result.data.growth > 0 ?
                /* istanbul ignore next */
                (cov_10oh7b63u().b[73][0]++, 'STABLE') :
                /* istanbul ignore next */
                (cov_10oh7b63u().b[73][1]++, 'DECLINING'))),
                industryRelevance:
                /* istanbul ignore next */
                (cov_10oh7b63u().b[74][0]++, result.data.industries) ||
                /* istanbul ignore next */
                (cov_10oh7b63u().b[74][1]++, ['Technology']),
                dataSource: 'EdgeCaseHandler',
                dataDate: new Date(),
                isActive: true
              }
            })];
          case 4:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][4]++;
            cov_10oh7b63u().s[214]++;
            _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[215]++;
            _a.label = 5;
          case 5:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][5]++;
            cov_10oh7b63u().s[216]++;
            return [2 /*return*/, result];
          case 6:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][6]++;
            cov_10oh7b63u().s[217]++;
            error_4 = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[218]++;
            return [2 /*return*/, {
              success: false,
              error: error_4 instanceof Error ?
              /* istanbul ignore next */
              (cov_10oh7b63u().b[75][0]++, error_4.message) :
              /* istanbul ignore next */
              (cov_10oh7b63u().b[75][1]++, 'Unknown error'),
              errorType: 'SYSTEM_ERROR',
              fallbackData: null
            }];
          case 7:
            /* istanbul ignore next */
            cov_10oh7b63u().b[61][7]++;
            cov_10oh7b63u().s[219]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Get suggested skill alternatives for missing skills
   */
  /* istanbul ignore next */
  cov_10oh7b63u().s[220]++;
  EdgeCaseHandlerService.prototype.getSuggestedSkillAlternatives = function (missingSkillIds) {
    /* istanbul ignore next */
    cov_10oh7b63u().f[47]++;
    cov_10oh7b63u().s[221]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_10oh7b63u().f[48]++;
      var similarSkills, error_5;
      /* istanbul ignore next */
      cov_10oh7b63u().s[222]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_10oh7b63u().f[49]++;
        cov_10oh7b63u().s[223]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_10oh7b63u().b[76][0]++;
            cov_10oh7b63u().s[224]++;
            _a.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_10oh7b63u().s[225]++;
            return [4 /*yield*/, prisma_1.prisma.skill.findMany({
              where: {
                OR: missingSkillIds.map(function (skillId) {
                  /* istanbul ignore next */
                  cov_10oh7b63u().f[50]++;
                  cov_10oh7b63u().s[226]++;
                  return {
                    name: {
                      contains: skillId,
                      mode: 'insensitive'
                    }
                  };
                })
              },
              take: 5
            })];
          case 1:
            /* istanbul ignore next */
            cov_10oh7b63u().b[76][1]++;
            cov_10oh7b63u().s[227]++;
            similarSkills = _a.sent();
            /* istanbul ignore next */
            cov_10oh7b63u().s[228]++;
            return [2 /*return*/, similarSkills.map(function (skill) {
              /* istanbul ignore next */
              cov_10oh7b63u().f[51]++;
              cov_10oh7b63u().s[229]++;
              return skill.name;
            })];
          case 2:
            /* istanbul ignore next */
            cov_10oh7b63u().b[76][2]++;
            cov_10oh7b63u().s[230]++;
            error_5 = _a.sent();
            // Return default alternatives
            /* istanbul ignore next */
            cov_10oh7b63u().s[231]++;
            return [2 /*return*/, ['JavaScript', 'React', 'Node.js', 'Python', 'TypeScript']];
          case 3:
            /* istanbul ignore next */
            cov_10oh7b63u().b[76][3]++;
            cov_10oh7b63u().s[232]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_10oh7b63u().s[233]++;
  return EdgeCaseHandlerService;
}());
/* istanbul ignore next */
cov_10oh7b63u().s[234]++;
exports.EdgeCaseHandlerService = EdgeCaseHandlerService;
// Export singleton instance
/* istanbul ignore next */
cov_10oh7b63u().s[235]++;
exports.edgeCaseHandlerService = EdgeCaseHandlerService.getInstance();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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