{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/resume-builder-comprehensive.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EH,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,UAAU,EAAV,CAAU,CAAC,CAAC;AAC5C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,cAAM,OAAA,CAAC;IACjC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,WAAW,CAAC;CAC3D,CAAC,EAFgC,CAEhC,CAAC,CAAC;AA5EJ,wEAAmF;AAEnF,wDAAwD;AACxD,IAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;AAC9B,IAAI,mBAAmB,GAAG,CAAC,CAAC;AAE5B,yCAAyC;AACzC,IAAM,UAAU,GAAG,IAAI,gCAAY,CACjC,cAAM,OAAA,CAAC;IACL,EAAE,EAAE,iBAAU,mBAAmB,EAAE,CAAE;IACrC,MAAM,EAAE,QAAQ;IAChB,KAAK,EAAE,aAAa;IACpB,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE,IAAI;IACd,WAAW,EAAE,CAAC;IACd,SAAS,EAAE,IAAI,IAAI,EAAE;IACrB,SAAS,EAAE,IAAI,IAAI,EAAE;CACtB,CAAC,EATI,CASJ,EACF,UAAC,MAAM;IACL,MAAM,CAAC,EAAE,GAAG,iBAAU,mBAAmB,EAAE,CAAE,CAAC;IAC9C,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;IACvB,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,CAAC,CAAC,+BAA+B;CAClC,CAAC;AAEF,gDAAgD;AAChD,IAAM,UAAU,GAAG;IACjB,IAAI,EAAE;QACJ,UAAU,EAAE,IAAA,mCAAe,GAAE,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;KAClE;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,IAAA,mCAAe,GAAE,CAAC,kBAAkB,CAAC;YAC7C,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAV,CAAU,CAAC,CAAC,CAAC;QACnF,CAAC,CAAC;QACF,SAAS,EAAE,IAAA,mCAAe,GAAE,CAAC,kBAAkB,CAAC,UAAC,EAAS;gBAAP,KAAK,WAAA;YACtD,IAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,CAAC;gBACpD,OAAA,CAAC,CAAC,EAAE,MAAK,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,CAAA,IAAI,CAAC,CAAC,MAAM,MAAK,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAA,IAAI,CAAC,CAAC,QAAQ;YAA9D,CAA8D,CAC/D,CAAC;YACF,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,MAAM,EAAE,IAAA,mCAAe,GAAE,CAAC,kBAAkB,CAAC,UAAC,EAAQ;gBAAN,IAAI,UAAA;YAClD,IAAM,SAAS,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC7B,EAAE,EAAE,iBAAU,mBAAmB,EAAE,CAAE;gBACrC,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YACzC,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC;QACF,MAAM,EAAE,IAAA,mCAAe,GAAE,CAAC,kBAAkB,CAAC,UAAC,EAAe;gBAAb,KAAK,WAAA,EAAE,IAAI,UAAA;YACzD,IAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,CAAC,CAAC;YAC5C,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAM,OAAO,kCAAQ,QAAQ,GAAK,IAAI,KAAE,SAAS,EAAE,IAAI,IAAI,EAAE,GAAE,CAAC;gBAChE,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBACnC,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC;KACH;CACF,CAAC;AAEF,eAAe;AACf,IAAM,WAAW,GAAG;IAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;CACpC,CAAC;AAOF,QAAQ,CAAC,sCAAsC,EAAE;IAC/C,UAAU,CAAC;QACT,mCAAmC;QACnC,WAAW,CAAC,KAAK,EAAE,CAAC;QACpB,mBAAmB,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC;QACR,iCAAiC;QACjC,WAAW,CAAC,KAAK,EAAE,CAAC;QACpB,UAAU,CAAC,KAAK,EAAE,CAAC;QAEnB,yDAAyD;QACzD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAC3C,IAAI,IAAI,CAAC,SAAS;gBAAE,IAAI,CAAC,SAAS,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YACzC,IAAI,IAAI,CAAC,SAAS;gBAAE,IAAI,CAAC,SAAS,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,mDAAmD,EAAE;;;;;wBAChD,UAAU,GAAG;4BACjB,KAAK,EAAE,6BAA6B;4BACpC,YAAY,EAAE;gCACZ,SAAS,EAAE,MAAM;gCACjB,QAAQ,EAAE,OAAO;gCACjB,KAAK,EAAE,wBAAwB;gCAC/B,KAAK,EAAE,aAAa;gCACpB,QAAQ,EAAE,cAAc;gCACxB,OAAO,EAAE,uBAAuB;gCAChC,QAAQ,EAAE,mCAAmC;6BAC9C;4BACD,OAAO,EAAE,gGAAgG;4BACzG,UAAU,EAAE;gCACV;oCACE,OAAO,EAAE,WAAW;oCACpB,QAAQ,EAAE,sBAAsB;oCAChC,SAAS,EAAE,SAAS;oCACpB,OAAO,EAAE,SAAS;oCAClB,WAAW,EAAE,6EAA6E;oCAC1F,YAAY,EAAE;wCACZ,wDAAwD;wCACxD,qDAAqD;wCACrD,4DAA4D;qCAC7D;iCACF;gCACD;oCACE,OAAO,EAAE,iBAAiB;oCAC1B,QAAQ,EAAE,kBAAkB;oCAC5B,SAAS,EAAE,SAAS;oCACpB,WAAW,EAAE,oCAAoC;iCAClD;6BACF;4BACD,SAAS,EAAE;gCACT;oCACE,WAAW,EAAE,kBAAkB;oCAC/B,MAAM,EAAE,qBAAqB;oCAC7B,KAAK,EAAE,kBAAkB;oCACzB,SAAS,EAAE,SAAS;oCACpB,OAAO,EAAE,SAAS;oCAClB,GAAG,EAAE,KAAK;oCACV,MAAM,EAAE,iBAAiB;iCAC1B;6BACF;4BACD,MAAM,EAAE;gCACN,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE;gCAC1D,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE;gCAC/D,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE;gCACnE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE;6BACtD;4BACD,QAAQ,EAAE,QAAQ;4BAClB,QAAQ,EAAE,KAAK;yBAChB,CAAC;wBAEa,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC5C,IAAI,wBAAO,UAAU,KAAE,MAAM,EAAE,QAAQ,GAAE;6BAC1C,CAAC,EAAA;;wBAFI,MAAM,GAAG,SAEb;wBAEF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;wBACzD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC1C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACpC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,iBAAiB,GAAG;4BACxB,KAAK,EAAE,gBAAgB;4BACvB,YAAY,EAAE;gCACZ,SAAS,EAAE,KAAK;gCAChB,QAAQ,EAAE,MAAM;gCAChB,KAAK,EAAE,iBAAiB;6BACzB;4BACD,UAAU,EAAE,EAAE;4BACd,SAAS,EAAE,EAAE;4BACb,MAAM,EAAE,EAAE;yBACX,CAAC;wBAEa,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC5C,IAAI,wBAAO,iBAAiB,KAAE,MAAM,EAAE,QAAQ,GAAE;6BACjD,CAAC,EAAA;;wBAFI,MAAM,GAAG,SAEb;wBAEF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC1C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;;;;;wBAE5B,WAAW,GAAG;4BAClB,KAAK,EAAE,gBAAgB;4BACvB,YAAY,EAAE;gCACZ,SAAS,EAAE,MAAM;gCACjB,QAAQ,EAAE,MAAM;gCAChB,KAAK,EAAE,kBAAkB;6BAC1B;4BACD,UAAU,EAAE,EAAE;4BACd,SAAS,EAAE,EAAE;4BACb,MAAM,EAAE,EAAE;yBACX,CAAC;wBAEc,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7C,IAAI,wBAAO,WAAW,KAAE,MAAM,EAAE,QAAQ,GAAE;6BAC3C,CAAC,EAAA;;wBAFI,OAAO,GAAG,SAEd;wBAGI,UAAU,GAAG;4BACjB,KAAK,EAAE,gBAAgB;4BACvB,OAAO,EAAE,eAAe;4BACxB,UAAU,EAAE;gCACV;oCACE,OAAO,EAAE,aAAa;oCACtB,QAAQ,EAAE,WAAW;oCACrB,SAAS,EAAE,SAAS;oCACpB,WAAW,EAAE,UAAU;iCACxB;6BACF;yBACF,CAAC;wBAEc,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;gCACzB,IAAI,EAAE,UAAU;6BACjB,CAAC,EAAA;;wBAHI,OAAO,GAAG,SAGd;wBAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAC7C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBAC9C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC3C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;;;aAC3D,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE;;;;;wBAExB,UAAU,GAAG;4BACjB,KAAK,EAAE,eAAe;4BACtB,YAAY,EAAE;gCACZ,SAAS,EAAE,QAAQ;gCACnB,QAAQ,EAAE,IAAI;gCACd,KAAK,EAAE,oBAAoB;6BAC5B;4BACD,UAAU,EAAE,EAAE;4BACd,SAAS,EAAE,EAAE;4BACb,MAAM,EAAE,EAAE;yBACX,CAAC;wBAEc,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7C,IAAI,wBAAO,UAAU,KAAE,MAAM,EAAE,QAAQ,GAAE;6BAC1C,CAAC,EAAA;;wBAFI,OAAO,GAAG,SAEd;wBAGc,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;gCACzB,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;6BAC1B,CAAC,EAAA;;wBAHI,OAAO,GAAG,SAGd;wBAEF,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAGf,qBAAM,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAA;;wBAAlD,aAAa,GAAG,SAAkC;wBACxD,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE;;;;;wBAEvB,WAAW,GAAG;4BAClB,KAAK,EAAE,UAAU;4BACjB,YAAY,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE;4BAChF,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;yBAC1C,CAAC;wBAEI,WAAW,GAAG;4BAClB,KAAK,EAAE,UAAU;4BACjB,YAAY,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE;4BAChF,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;yBAC1C,CAAC;wBAEF,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,wBAAO,WAAW,KAAE,MAAM,EAAE,QAAQ,GAAE,EAAE,CAAC,EAAA;;wBAA9E,SAA8E,CAAC;wBAC/E,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,wBAAO,WAAW,KAAE,MAAM,EAAE,QAAQ,GAAE,EAAE,CAAC,EAAA;;wBAA9E,SAA8E,CAAC;wBAE/D,qBAAM,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAA;;wBAA5C,OAAO,GAAG,SAAkC;wBAClD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC1C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;;;;aAC3C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,EAAE,CAAC,8DAA8D,EAAE;YACjE,IAAM,iBAAiB,GAAG;gBACxB,OAAO,EAAE,iBAAiB;gBAC1B,QAAQ,EAAE,kBAAkB;gBAC5B,SAAS,EAAE,SAAS;gBACpB,gCAAgC;gBAChC,WAAW,EAAE,wBAAwB;aACtC,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE;YACjC,IAAM,WAAW,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEvE,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;gBACvB,IAAM,KAAK,GAAG;oBACZ,IAAI,EAAE,YAAY;oBAClB,KAAK,OAAA;oBACL,QAAQ,EAAE,eAAe;iBAC1B,CAAC;gBAEF,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;YAC5C,IAAM,mBAAmB,GAAG;gBAC1B,KAAK,EAAE,uBAAuB;gBAC9B,YAAY,EAAE;oBACZ,SAAS,EAAE,MAAM;oBACjB,QAAQ,EAAE,MAAM;oBAChB,KAAK,EAAE,kBAAkB;oBACzB,KAAK,EAAE,aAAa;oBACpB,QAAQ,EAAE,WAAW;oBACrB,OAAO,EAAE,kBAAkB;oBAC3B,QAAQ,EAAE,8BAA8B;iBACzC;gBACD,OAAO,EAAE,cAAc;gBACvB,UAAU,EAAE;oBACV;wBACE,OAAO,EAAE,cAAc;wBACvB,QAAQ,EAAE,eAAe;wBACzB,SAAS,EAAE,SAAS;wBACpB,OAAO,EAAE,SAAS;wBAClB,WAAW,EAAE,kBAAkB;wBAC/B,YAAY,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;qBACjD;iBACF;gBACD,SAAS,EAAE;oBACT;wBACE,WAAW,EAAE,iBAAiB;wBAC9B,MAAM,EAAE,aAAa;wBACrB,KAAK,EAAE,YAAY;wBACnB,SAAS,EAAE,SAAS;wBACpB,OAAO,EAAE,SAAS;wBAClB,GAAG,EAAE,KAAK;wBACV,MAAM,EAAE,aAAa;qBACtB;iBACF;gBACD,MAAM,EAAE;oBACN;wBACE,IAAI,EAAE,YAAY;wBAClB,KAAK,EAAE,UAAU;wBACjB,QAAQ,EAAE,eAAe;qBAC1B;iBACF;gBACD,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,yCAAyC;YACzC,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7D,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/D,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACpE,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACrE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7D,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9D,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YAE/D,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;gBACxB,IAAM,kBAAkB,GAAG;oBACzB,KAAK,EAAE,UAAG,QAAQ,YAAS;oBAC3B,YAAY,EAAE;wBACZ,SAAS,EAAE,UAAU;wBACrB,QAAQ,EAAE,MAAM;wBAChB,KAAK,EAAE,sBAAsB;qBAC9B;oBACD,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE,EAAE;oBACV,QAAQ,UAAA;iBACT,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,oDAAoD,EAAE;;;;;wBACjD,UAAU,GAAG;4BACjB,KAAK,EAAE,kBAAkB;4BACzB,YAAY,EAAE;gCACZ,SAAS,EAAE,YAAY;gCACvB,QAAQ,EAAE,MAAM;gCAChB,KAAK,EAAE,wBAAwB;6BAChC;4BACD,UAAU,EAAE;gCACV;oCACE,OAAO,EAAE,cAAc;oCACvB,QAAQ,EAAE,eAAe;oCACzB,SAAS,EAAE,SAAS;iCACrB;6BACF;4BACD,SAAS,EAAE,EAAE;4BACb,MAAM,EAAE,EAAE;yBACX,CAAC;wBAGc,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7C,IAAI,wBAAO,UAAU,KAAE,MAAM,EAAE,QAAQ,GAAE;6BAC1C,CAAC,EAAA;;wBAFI,OAAO,GAAG,SAEd;wBAGgB,qBAAM,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC;gCAClD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;6BAC5D,CAAC,EAAA;;wBAFI,SAAS,GAAG,SAEhB;wBAGc,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;gCACzB,IAAI,EAAE,EAAE,KAAK,EAAE,0BAA0B,EAAE;6BAC5C,CAAC,EAAA;;wBAHI,OAAO,GAAG,SAGd;wBAEF,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;wBAChC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBACjD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;wBACvD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC1D,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;;;aAC5C,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;;;;wBACzC,UAAU,GAAG;4BACjB,KAAK,EAAE,iBAAiB;4BACxB,YAAY,EAAE;gCACZ,SAAS,EAAE,YAAY;gCACvB,QAAQ,EAAE,MAAM;gCAChB,KAAK,EAAE,wBAAwB;6BAChC;4BACD,UAAU,EAAE,EAAE;4BACd,SAAS,EAAE,EAAE;4BACb,MAAM,EAAE,EAAE;yBACX,CAAC;wBAEc,qBAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7C,IAAI,wBAAO,UAAU,KAAE,MAAM,EAAE,QAAQ,GAAE;6BAC1C,CAAC,EAAA;;wBAFI,OAAO,GAAG,SAEd;wBAGI,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;4BAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;4BACzB,IAAI,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE;yBAC5B,CAAC,CAAC;wBAEG,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;4BAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;4BACzB,IAAI,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE;yBACtC,CAAC,CAAC;wBAEwB,qBAAM,OAAO,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC,EAAA;;wBAAxE,KAAqB,SAAmD,EAAvE,OAAO,QAAA,EAAE,OAAO,QAAA;wBAEvB,2DAA2D;wBAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC9B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC/B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/components/resume-builder-comprehensive.test.ts"], "sourcesContent": ["/**\n * Memory-Optimized Resume Builder Tests\n *\n * Tests for the complete Resume Builder functionality including\n * data flow, validation, and business logic\n *\n * MEMORY OPTIMIZATION: Reduced test data sizes and improved cleanup\n */\n\nimport { z } from 'zod';\nimport { createCleanMock, ResourcePool } from '../../test-utils/memory-leak-fixes';\n\n// Memory-optimized mock database with automatic cleanup\nconst mockResumes = new Map();\nlet mockResumeIdCounter = 1;\n\n// Resource pool for reusing test objects\nconst resumePool = new ResourcePool(\n  () => ({\n    id: `resume-${mockResumeIdCounter++}`,\n    userId: 'user-1',\n    title: 'Test Resume',\n    content: {},\n    isActive: true,\n    exportCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  }),\n  (resume) => {\n    resume.id = `resume-${mockResumeIdCounter++}`;\n    resume.content = {};\n    resume.exportCount = 0;\n    resume.createdAt = new Date();\n    resume.updatedAt = new Date();\n    return resume;\n  },\n  5 // Limit pool size to 5 objects\n);\n\n// Memory-optimized Mock Prisma with clean mocks\nconst mockPrisma = {\n  user: {\n    findUnique: createCleanMock().mockResolvedValue({ id: 'user-1' })\n  },\n  resume: {\n    findMany: createCleanMock().mockImplementation(() => {\n      return Promise.resolve(Array.from(mockResumes.values()).filter(r => r.isActive));\n    }),\n    findFirst: createCleanMock().mockImplementation(({ where }) => {\n      const resume = Array.from(mockResumes.values()).find(r =>\n        r.id === where?.id && r.userId === where?.userId && r.isActive\n      );\n      return Promise.resolve(resume || null);\n    }),\n    create: createCleanMock().mockImplementation(({ data }) => {\n      const newResume = resumePool.acquire();\n      Object.assign(newResume, data, {\n        id: `resume-${mockResumeIdCounter++}`,\n        isActive: true,\n        exportCount: 0,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      });\n      mockResumes.set(newResume.id, newResume);\n      return Promise.resolve(newResume);\n    }),\n    update: createCleanMock().mockImplementation(({ where, data }) => {\n      const existing = mockResumes.get(where?.id);\n      if (existing) {\n        const updated = { ...existing, ...data, updatedAt: new Date() };\n        mockResumes.set(where.id, updated);\n        return Promise.resolve(updated);\n      }\n      return Promise.resolve(null);\n    })\n  }\n};\n\n// Mock session\nconst mockSession = {\n  user: { email: '<EMAIL>' }\n};\n\njest.mock('@/lib/prisma', () => mockPrisma);\njest.mock('next-auth/next', () => ({\n  getServerSession: jest.fn().mockResolvedValue(mockSession)\n}));\n\ndescribe('Resume Builder - Comprehensive Tests', () => {\n  beforeEach(() => {\n    // Clear mock data before each test\n    mockResumes.clear();\n    mockResumeIdCounter = 1;\n    jest.clearAllMocks();\n  });\n\n  afterEach(() => {\n    // Memory cleanup after each test\n    mockResumes.clear();\n    resumePool.clear();\n\n    // Clear all mock implementations to prevent memory leaks\n    Object.values(mockPrisma.resume).forEach(mock => {\n      if (mock.mockClear) mock.mockClear();\n    });\n    Object.values(mockPrisma.user).forEach(mock => {\n      if (mock.mockClear) mock.mockClear();\n    });\n  });\n\n  describe('Resume Data Management', () => {\n    it('should create a complete resume with all sections', async () => {\n      const resumeData = {\n        title: 'Full Stack Developer Resume',\n        personalInfo: {\n          firstName: 'Jane',\n          lastName: 'Smith',\n          email: '<EMAIL>',\n          phone: '******-0456',\n          location: 'New York, NY',\n          website: 'https://janesmith.dev',\n          linkedIn: 'https://linkedin.com/in/janesmith'\n        },\n        summary: 'Passionate full-stack developer with 3 years of experience building scalable web applications.',\n        experience: [\n          {\n            company: 'StartupCo',\n            position: 'Full Stack Developer',\n            startDate: '2021-06',\n            endDate: '2024-01',\n            description: 'Develop and maintain web applications using React, Node.js, and PostgreSQL.',\n            achievements: [\n              'Built user authentication system serving 10,000+ users',\n              'Reduced page load times by 60% through optimization',\n              'Implemented CI/CD pipeline reducing deployment time by 80%'\n            ]\n          },\n          {\n            company: 'Current Company',\n            position: 'Senior Developer',\n            startDate: '2024-01',\n            description: 'Leading frontend development team.'\n          }\n        ],\n        education: [\n          {\n            institution: 'State University',\n            degree: 'Bachelor of Science',\n            field: 'Computer Science',\n            startDate: '2017-09',\n            endDate: '2021-05',\n            gpa: '3.7',\n            honors: 'Magna Cum Laude'\n          }\n        ],\n        skills: [\n          { name: 'React', level: 'ADVANCED', category: 'Frontend' },\n          { name: 'Node.js', level: 'INTERMEDIATE', category: 'Backend' },\n          { name: 'PostgreSQL', level: 'INTERMEDIATE', category: 'Database' },\n          { name: 'Git', level: 'ADVANCED', category: 'Tools' }\n        ],\n        template: 'modern',\n        isPublic: false\n      };\n\n      const result = await mockPrisma.resume.create({\n        data: { ...resumeData, userId: 'user-1' }\n      });\n\n      expect(result).toBeDefined();\n      expect(result.id).toBe('resume-1');\n      expect(result.title).toBe('Full Stack Developer Resume');\n      expect(result.personalInfo.firstName).toBe('Jane');\n      expect(result.experience).toHaveLength(2);\n      expect(result.education).toHaveLength(1);\n      expect(result.skills).toHaveLength(4);\n      expect(result.isActive).toBe(true);\n    });\n\n    it('should handle minimal resume data', async () => {\n      const minimalResumeData = {\n        title: 'Minimal Resume',\n        personalInfo: {\n          firstName: 'Min',\n          lastName: 'User',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const result = await mockPrisma.resume.create({\n        data: { ...minimalResumeData, userId: 'user-1' }\n      });\n\n      expect(result).toBeDefined();\n      expect(result.title).toBe('Minimal Resume');\n      expect(result.personalInfo.firstName).toBe('Min');\n      expect(result.experience).toHaveLength(0);\n      expect(result.education).toHaveLength(0);\n      expect(result.skills).toHaveLength(0);\n    });\n\n    it('should update existing resume', async () => {\n      // Create initial resume\n      const initialData = {\n        title: 'Initial Resume',\n        personalInfo: {\n          firstName: 'Test',\n          lastName: 'User',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const created = await mockPrisma.resume.create({\n        data: { ...initialData, userId: 'user-1' }\n      });\n\n      // Update the resume\n      const updateData = {\n        title: 'Updated Resume',\n        summary: 'Added summary',\n        experience: [\n          {\n            company: 'New Company',\n            position: 'Developer',\n            startDate: '2024-01',\n            description: 'New role'\n          }\n        ]\n      };\n\n      const updated = await mockPrisma.resume.update({\n        where: { id: created.id },\n        data: updateData\n      });\n\n      expect(updated.title).toBe('Updated Resume');\n      expect(updated.summary).toBe('Added summary');\n      expect(updated.experience).toHaveLength(1);\n      expect(updated.experience[0].company).toBe('New Company');\n    });\n\n    it('should soft delete resume', async () => {\n      // Create resume\n      const resumeData = {\n        title: 'To Be Deleted',\n        personalInfo: {\n          firstName: 'Delete',\n          lastName: 'Me',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const created = await mockPrisma.resume.create({\n        data: { ...resumeData, userId: 'user-1' }\n      });\n\n      // Soft delete\n      const deleted = await mockPrisma.resume.update({\n        where: { id: created.id },\n        data: { isActive: false }\n      });\n\n      expect(deleted.isActive).toBe(false);\n\n      // Verify it doesn't appear in active resumes\n      const activeResumes = await mockPrisma.resume.findMany();\n      expect(activeResumes).toHaveLength(0);\n    });\n\n    it('should list user resumes', async () => {\n      // Create multiple resumes\n      const resume1Data = {\n        title: 'Resume 1',\n        personalInfo: { firstName: 'User', lastName: 'One', email: '<EMAIL>' },\n        experience: [], education: [], skills: []\n      };\n\n      const resume2Data = {\n        title: 'Resume 2',\n        personalInfo: { firstName: 'User', lastName: 'Two', email: '<EMAIL>' },\n        experience: [], education: [], skills: []\n      };\n\n      await mockPrisma.resume.create({ data: { ...resume1Data, userId: 'user-1' } });\n      await mockPrisma.resume.create({ data: { ...resume2Data, userId: 'user-1' } });\n\n      const resumes = await mockPrisma.resume.findMany();\n      expect(resumes).toHaveLength(2);\n      expect(resumes[0].title).toBe('Resume 1');\n      expect(resumes[1].title).toBe('Resume 2');\n    });\n  });\n\n  describe('Resume Business Logic', () => {\n    it('should handle experience with no end date (current position)', () => {\n      const currentExperience = {\n        company: 'Current Company',\n        position: 'Senior Developer',\n        startDate: '2024-01',\n        // No endDate - current position\n        description: 'Currently working here'\n      };\n\n      expect(currentExperience.endDate).toBeUndefined();\n      expect(currentExperience.startDate).toBe('2024-01');\n    });\n\n    it('should validate skill levels', () => {\n      const validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];\n      \n      validLevels.forEach(level => {\n        const skill = {\n          name: 'Test Skill',\n          level,\n          category: 'Test Category'\n        };\n        \n        expect(validLevels).toContain(skill.level);\n      });\n    });\n\n    it('should handle optional fields correctly', () => {\n      const resumeWithOptionals = {\n        title: 'Resume with Optionals',\n        personalInfo: {\n          firstName: 'Test',\n          lastName: 'User',\n          email: '<EMAIL>',\n          phone: '******-0123',\n          location: 'Test City',\n          website: 'https://test.com',\n          linkedIn: 'https://linkedin.com/in/test'\n        },\n        summary: 'Test summary',\n        experience: [\n          {\n            company: 'Test Company',\n            position: 'Test Position',\n            startDate: '2024-01',\n            endDate: '2024-12',\n            description: 'Test description',\n            achievements: ['Achievement 1', 'Achievement 2']\n          }\n        ],\n        education: [\n          {\n            institution: 'Test University',\n            degree: 'Test Degree',\n            field: 'Test Field',\n            startDate: '2020-09',\n            endDate: '2024-05',\n            gpa: '3.8',\n            honors: 'Test Honors'\n          }\n        ],\n        skills: [\n          {\n            name: 'Test Skill',\n            level: 'ADVANCED',\n            category: 'Test Category'\n          }\n        ],\n        template: 'modern',\n        isPublic: false\n      };\n\n      // All fields should be present and valid\n      expect(resumeWithOptionals.personalInfo.phone).toBeDefined();\n      expect(resumeWithOptionals.personalInfo.location).toBeDefined();\n      expect(resumeWithOptionals.personalInfo.website).toBeDefined();\n      expect(resumeWithOptionals.personalInfo.linkedIn).toBeDefined();\n      expect(resumeWithOptionals.summary).toBeDefined();\n      expect(resumeWithOptionals.experience[0].endDate).toBeDefined();\n      expect(resumeWithOptionals.experience[0].description).toBeDefined();\n      expect(resumeWithOptionals.experience[0].achievements).toBeDefined();\n      expect(resumeWithOptionals.education[0].field).toBeDefined();\n      expect(resumeWithOptionals.education[0].gpa).toBeDefined();\n      expect(resumeWithOptionals.education[0].honors).toBeDefined();\n      expect(resumeWithOptionals.skills[0].level).toBeDefined();\n      expect(resumeWithOptionals.skills[0].category).toBeDefined();\n    });\n\n    it('should handle template selection', () => {\n      const templates = ['modern', 'classic', 'minimal', 'creative'];\n      \n      templates.forEach(template => {\n        const resumeWithTemplate = {\n          title: `${template} Resume`,\n          personalInfo: {\n            firstName: 'Template',\n            lastName: 'User',\n            email: '<EMAIL>'\n          },\n          experience: [],\n          education: [],\n          skills: [],\n          template\n        };\n\n        expect(templates).toContain(resumeWithTemplate.template);\n      });\n    });\n  });\n\n  describe('Data Integrity', () => {\n    it('should maintain data consistency across operations', async () => {\n      const resumeData = {\n        title: 'Consistency Test',\n        personalInfo: {\n          firstName: 'Consistent',\n          lastName: 'User',\n          email: '<EMAIL>'\n        },\n        experience: [\n          {\n            company: 'Test Company',\n            position: 'Test Position',\n            startDate: '2024-01'\n          }\n        ],\n        education: [],\n        skills: []\n      };\n\n      // Create\n      const created = await mockPrisma.resume.create({\n        data: { ...resumeData, userId: 'user-1' }\n      });\n\n      // Read\n      const retrieved = await mockPrisma.resume.findFirst({\n        where: { id: created.id, userId: 'user-1', isActive: true }\n      });\n\n      // Update\n      const updated = await mockPrisma.resume.update({\n        where: { id: created.id },\n        data: { title: 'Updated Consistency Test' }\n      });\n\n      expect(retrieved).toBeDefined();\n      expect(retrieved.title).toBe('Consistency Test');\n      expect(updated.title).toBe('Updated Consistency Test');\n      expect(updated.personalInfo.firstName).toBe('Consistent');\n      expect(updated.experience).toHaveLength(1);\n    });\n\n    it('should handle concurrent operations safely', async () => {\n      const resumeData = {\n        title: 'Concurrent Test',\n        personalInfo: {\n          firstName: 'Concurrent',\n          lastName: 'User',\n          email: '<EMAIL>'\n        },\n        experience: [],\n        education: [],\n        skills: []\n      };\n\n      const created = await mockPrisma.resume.create({\n        data: { ...resumeData, userId: 'user-1' }\n      });\n\n      // Simulate concurrent updates\n      const update1Promise = mockPrisma.resume.update({\n        where: { id: created.id },\n        data: { title: 'Update 1' }\n      });\n\n      const update2Promise = mockPrisma.resume.update({\n        where: { id: created.id },\n        data: { summary: 'Update 2 summary' }\n      });\n\n      const [result1, result2] = await Promise.all([update1Promise, update2Promise]);\n\n      // Both updates should succeed (in our mock implementation)\n      expect(result1).toBeDefined();\n      expect(result2).toBeDefined();\n    });\n  });\n});\n"], "version": 3}