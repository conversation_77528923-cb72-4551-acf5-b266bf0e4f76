b48d9e6d18e07c04b4a5ec04fb73d3d3
"use strict";
/**
 * Memory-Optimized Resume Builder Tests
 *
 * Tests for the complete Resume Builder functionality including
 * data flow, validation, and business logic
 *
 * MEMORY OPTIMIZATION: Reduced test data sizes and improved cleanup
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
jest.mock('@/lib/prisma', function () { return mockPrisma; });
jest.mock('next-auth/next', function () { return ({
    getServerSession: jest.fn().mockResolvedValue(mockSession)
}); });
var memory_leak_fixes_1 = require("../../test-utils/memory-leak-fixes");
// Memory-optimized mock database with automatic cleanup
var mockResumes = new Map();
var mockResumeIdCounter = 1;
// Resource pool for reusing test objects
var resumePool = new memory_leak_fixes_1.ResourcePool(function () { return ({
    id: "resume-".concat(mockResumeIdCounter++),
    userId: 'user-1',
    title: 'Test Resume',
    content: {},
    isActive: true,
    exportCount: 0,
    createdAt: new Date(),
    updatedAt: new Date()
}); }, function (resume) {
    resume.id = "resume-".concat(mockResumeIdCounter++);
    resume.content = {};
    resume.exportCount = 0;
    resume.createdAt = new Date();
    resume.updatedAt = new Date();
    return resume;
}, 5 // Limit pool size to 5 objects
);
// Memory-optimized Mock Prisma with clean mocks
var mockPrisma = {
    user: {
        findUnique: (0, memory_leak_fixes_1.createCleanMock)().mockResolvedValue({ id: 'user-1' })
    },
    resume: {
        findMany: (0, memory_leak_fixes_1.createCleanMock)().mockImplementation(function () {
            return Promise.resolve(Array.from(mockResumes.values()).filter(function (r) { return r.isActive; }));
        }),
        findFirst: (0, memory_leak_fixes_1.createCleanMock)().mockImplementation(function (_a) {
            var where = _a.where;
            var resume = Array.from(mockResumes.values()).find(function (r) {
                return r.id === (where === null || where === void 0 ? void 0 : where.id) && r.userId === (where === null || where === void 0 ? void 0 : where.userId) && r.isActive;
            });
            return Promise.resolve(resume || null);
        }),
        create: (0, memory_leak_fixes_1.createCleanMock)().mockImplementation(function (_a) {
            var data = _a.data;
            var newResume = resumePool.acquire();
            Object.assign(newResume, data, {
                id: "resume-".concat(mockResumeIdCounter++),
                isActive: true,
                exportCount: 0,
                createdAt: new Date(),
                updatedAt: new Date()
            });
            mockResumes.set(newResume.id, newResume);
            return Promise.resolve(newResume);
        }),
        update: (0, memory_leak_fixes_1.createCleanMock)().mockImplementation(function (_a) {
            var where = _a.where, data = _a.data;
            var existing = mockResumes.get(where === null || where === void 0 ? void 0 : where.id);
            if (existing) {
                var updated = __assign(__assign(__assign({}, existing), data), { updatedAt: new Date() });
                mockResumes.set(where.id, updated);
                return Promise.resolve(updated);
            }
            return Promise.resolve(null);
        })
    }
};
// Mock session
var mockSession = {
    user: { email: '<EMAIL>' }
};
describe('Resume Builder - Comprehensive Tests', function () {
    beforeEach(function () {
        // Clear mock data before each test
        mockResumes.clear();
        mockResumeIdCounter = 1;
        jest.clearAllMocks();
    });
    afterEach(function () {
        // Memory cleanup after each test
        mockResumes.clear();
        resumePool.clear();
        // Clear all mock implementations to prevent memory leaks
        Object.values(mockPrisma.resume).forEach(function (mock) {
            if (mock.mockClear)
                mock.mockClear();
        });
        Object.values(mockPrisma.user).forEach(function (mock) {
            if (mock.mockClear)
                mock.mockClear();
        });
    });
    describe('Resume Data Management', function () {
        it('should create a complete resume with all sections', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resumeData, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        resumeData = {
                            title: 'Full Stack Developer Resume',
                            personalInfo: {
                                firstName: 'Jane',
                                lastName: 'Smith',
                                email: '<EMAIL>',
                                phone: '******-0456',
                                location: 'New York, NY',
                                website: 'https://janesmith.dev',
                                linkedIn: 'https://linkedin.com/in/janesmith'
                            },
                            summary: 'Passionate full-stack developer with 3 years of experience building scalable web applications.',
                            experience: [
                                {
                                    company: 'StartupCo',
                                    position: 'Full Stack Developer',
                                    startDate: '2021-06',
                                    endDate: '2024-01',
                                    description: 'Develop and maintain web applications using React, Node.js, and PostgreSQL.',
                                    achievements: [
                                        'Built user authentication system serving 10,000+ users',
                                        'Reduced page load times by 60% through optimization',
                                        'Implemented CI/CD pipeline reducing deployment time by 80%'
                                    ]
                                },
                                {
                                    company: 'Current Company',
                                    position: 'Senior Developer',
                                    startDate: '2024-01',
                                    description: 'Leading frontend development team.'
                                }
                            ],
                            education: [
                                {
                                    institution: 'State University',
                                    degree: 'Bachelor of Science',
                                    field: 'Computer Science',
                                    startDate: '2017-09',
                                    endDate: '2021-05',
                                    gpa: '3.7',
                                    honors: 'Magna Cum Laude'
                                }
                            ],
                            skills: [
                                { name: 'React', level: 'ADVANCED', category: 'Frontend' },
                                { name: 'Node.js', level: 'INTERMEDIATE', category: 'Backend' },
                                { name: 'PostgreSQL', level: 'INTERMEDIATE', category: 'Database' },
                                { name: 'Git', level: 'ADVANCED', category: 'Tools' }
                            ],
                            template: 'modern',
                            isPublic: false
                        };
                        return [4 /*yield*/, mockPrisma.resume.create({
                                data: __assign(__assign({}, resumeData), { userId: 'user-1' })
                            })];
                    case 1:
                        result = _a.sent();
                        expect(result).toBeDefined();
                        expect(result.id).toBe('resume-1');
                        expect(result.title).toBe('Full Stack Developer Resume');
                        expect(result.personalInfo.firstName).toBe('Jane');
                        expect(result.experience).toHaveLength(2);
                        expect(result.education).toHaveLength(1);
                        expect(result.skills).toHaveLength(4);
                        expect(result.isActive).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle minimal resume data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var minimalResumeData, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        minimalResumeData = {
                            title: 'Minimal Resume',
                            personalInfo: {
                                firstName: 'Min',
                                lastName: 'User',
                                email: '<EMAIL>'
                            },
                            experience: [],
                            education: [],
                            skills: []
                        };
                        return [4 /*yield*/, mockPrisma.resume.create({
                                data: __assign(__assign({}, minimalResumeData), { userId: 'user-1' })
                            })];
                    case 1:
                        result = _a.sent();
                        expect(result).toBeDefined();
                        expect(result.title).toBe('Minimal Resume');
                        expect(result.personalInfo.firstName).toBe('Min');
                        expect(result.experience).toHaveLength(0);
                        expect(result.education).toHaveLength(0);
                        expect(result.skills).toHaveLength(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update existing resume', function () { return __awaiter(void 0, void 0, void 0, function () {
            var initialData, created, updateData, updated;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        initialData = {
                            title: 'Initial Resume',
                            personalInfo: {
                                firstName: 'Test',
                                lastName: 'User',
                                email: '<EMAIL>'
                            },
                            experience: [],
                            education: [],
                            skills: []
                        };
                        return [4 /*yield*/, mockPrisma.resume.create({
                                data: __assign(__assign({}, initialData), { userId: 'user-1' })
                            })];
                    case 1:
                        created = _a.sent();
                        updateData = {
                            title: 'Updated Resume',
                            summary: 'Added summary',
                            experience: [
                                {
                                    company: 'New Company',
                                    position: 'Developer',
                                    startDate: '2024-01',
                                    description: 'New role'
                                }
                            ]
                        };
                        return [4 /*yield*/, mockPrisma.resume.update({
                                where: { id: created.id },
                                data: updateData
                            })];
                    case 2:
                        updated = _a.sent();
                        expect(updated.title).toBe('Updated Resume');
                        expect(updated.summary).toBe('Added summary');
                        expect(updated.experience).toHaveLength(1);
                        expect(updated.experience[0].company).toBe('New Company');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should soft delete resume', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resumeData, created, deleted, activeResumes;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        resumeData = {
                            title: 'To Be Deleted',
                            personalInfo: {
                                firstName: 'Delete',
                                lastName: 'Me',
                                email: '<EMAIL>'
                            },
                            experience: [],
                            education: [],
                            skills: []
                        };
                        return [4 /*yield*/, mockPrisma.resume.create({
                                data: __assign(__assign({}, resumeData), { userId: 'user-1' })
                            })];
                    case 1:
                        created = _a.sent();
                        return [4 /*yield*/, mockPrisma.resume.update({
                                where: { id: created.id },
                                data: { isActive: false }
                            })];
                    case 2:
                        deleted = _a.sent();
                        expect(deleted.isActive).toBe(false);
                        return [4 /*yield*/, mockPrisma.resume.findMany()];
                    case 3:
                        activeResumes = _a.sent();
                        expect(activeResumes).toHaveLength(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should list user resumes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resume1Data, resume2Data, resumes;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        resume1Data = {
                            title: 'Resume 1',
                            personalInfo: { firstName: 'User', lastName: 'One', email: '<EMAIL>' },
                            experience: [], education: [], skills: []
                        };
                        resume2Data = {
                            title: 'Resume 2',
                            personalInfo: { firstName: 'User', lastName: 'Two', email: '<EMAIL>' },
                            experience: [], education: [], skills: []
                        };
                        return [4 /*yield*/, mockPrisma.resume.create({ data: __assign(__assign({}, resume1Data), { userId: 'user-1' }) })];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, mockPrisma.resume.create({ data: __assign(__assign({}, resume2Data), { userId: 'user-1' }) })];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, mockPrisma.resume.findMany()];
                    case 3:
                        resumes = _a.sent();
                        expect(resumes).toHaveLength(2);
                        expect(resumes[0].title).toBe('Resume 1');
                        expect(resumes[1].title).toBe('Resume 2');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Resume Business Logic', function () {
        it('should handle experience with no end date (current position)', function () {
            var currentExperience = {
                company: 'Current Company',
                position: 'Senior Developer',
                startDate: '2024-01',
                // No endDate - current position
                description: 'Currently working here'
            };
            expect(currentExperience.endDate).toBeUndefined();
            expect(currentExperience.startDate).toBe('2024-01');
        });
        it('should validate skill levels', function () {
            var validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
            validLevels.forEach(function (level) {
                var skill = {
                    name: 'Test Skill',
                    level: level,
                    category: 'Test Category'
                };
                expect(validLevels).toContain(skill.level);
            });
        });
        it('should handle optional fields correctly', function () {
            var resumeWithOptionals = {
                title: 'Resume with Optionals',
                personalInfo: {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                    phone: '******-0123',
                    location: 'Test City',
                    website: 'https://test.com',
                    linkedIn: 'https://linkedin.com/in/test'
                },
                summary: 'Test summary',
                experience: [
                    {
                        company: 'Test Company',
                        position: 'Test Position',
                        startDate: '2024-01',
                        endDate: '2024-12',
                        description: 'Test description',
                        achievements: ['Achievement 1', 'Achievement 2']
                    }
                ],
                education: [
                    {
                        institution: 'Test University',
                        degree: 'Test Degree',
                        field: 'Test Field',
                        startDate: '2020-09',
                        endDate: '2024-05',
                        gpa: '3.8',
                        honors: 'Test Honors'
                    }
                ],
                skills: [
                    {
                        name: 'Test Skill',
                        level: 'ADVANCED',
                        category: 'Test Category'
                    }
                ],
                template: 'modern',
                isPublic: false
            };
            // All fields should be present and valid
            expect(resumeWithOptionals.personalInfo.phone).toBeDefined();
            expect(resumeWithOptionals.personalInfo.location).toBeDefined();
            expect(resumeWithOptionals.personalInfo.website).toBeDefined();
            expect(resumeWithOptionals.personalInfo.linkedIn).toBeDefined();
            expect(resumeWithOptionals.summary).toBeDefined();
            expect(resumeWithOptionals.experience[0].endDate).toBeDefined();
            expect(resumeWithOptionals.experience[0].description).toBeDefined();
            expect(resumeWithOptionals.experience[0].achievements).toBeDefined();
            expect(resumeWithOptionals.education[0].field).toBeDefined();
            expect(resumeWithOptionals.education[0].gpa).toBeDefined();
            expect(resumeWithOptionals.education[0].honors).toBeDefined();
            expect(resumeWithOptionals.skills[0].level).toBeDefined();
            expect(resumeWithOptionals.skills[0].category).toBeDefined();
        });
        it('should handle template selection', function () {
            var templates = ['modern', 'classic', 'minimal', 'creative'];
            templates.forEach(function (template) {
                var resumeWithTemplate = {
                    title: "".concat(template, " Resume"),
                    personalInfo: {
                        firstName: 'Template',
                        lastName: 'User',
                        email: '<EMAIL>'
                    },
                    experience: [],
                    education: [],
                    skills: [],
                    template: template
                };
                expect(templates).toContain(resumeWithTemplate.template);
            });
        });
    });
    describe('Data Integrity', function () {
        it('should maintain data consistency across operations', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resumeData, created, retrieved, updated;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        resumeData = {
                            title: 'Consistency Test',
                            personalInfo: {
                                firstName: 'Consistent',
                                lastName: 'User',
                                email: '<EMAIL>'
                            },
                            experience: [
                                {
                                    company: 'Test Company',
                                    position: 'Test Position',
                                    startDate: '2024-01'
                                }
                            ],
                            education: [],
                            skills: []
                        };
                        return [4 /*yield*/, mockPrisma.resume.create({
                                data: __assign(__assign({}, resumeData), { userId: 'user-1' })
                            })];
                    case 1:
                        created = _a.sent();
                        return [4 /*yield*/, mockPrisma.resume.findFirst({
                                where: { id: created.id, userId: 'user-1', isActive: true }
                            })];
                    case 2:
                        retrieved = _a.sent();
                        return [4 /*yield*/, mockPrisma.resume.update({
                                where: { id: created.id },
                                data: { title: 'Updated Consistency Test' }
                            })];
                    case 3:
                        updated = _a.sent();
                        expect(retrieved).toBeDefined();
                        expect(retrieved.title).toBe('Consistency Test');
                        expect(updated.title).toBe('Updated Consistency Test');
                        expect(updated.personalInfo.firstName).toBe('Consistent');
                        expect(updated.experience).toHaveLength(1);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle concurrent operations safely', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resumeData, created, update1Promise, update2Promise, _a, result1, result2;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        resumeData = {
                            title: 'Concurrent Test',
                            personalInfo: {
                                firstName: 'Concurrent',
                                lastName: 'User',
                                email: '<EMAIL>'
                            },
                            experience: [],
                            education: [],
                            skills: []
                        };
                        return [4 /*yield*/, mockPrisma.resume.create({
                                data: __assign(__assign({}, resumeData), { userId: 'user-1' })
                            })];
                    case 1:
                        created = _b.sent();
                        update1Promise = mockPrisma.resume.update({
                            where: { id: created.id },
                            data: { title: 'Update 1' }
                        });
                        update2Promise = mockPrisma.resume.update({
                            where: { id: created.id },
                            data: { summary: 'Update 2 summary' }
                        });
                        return [4 /*yield*/, Promise.all([update1Promise, update2Promise])];
                    case 2:
                        _a = _b.sent(), result1 = _a[0], result2 = _a[1];
                        // Both updates should succeed (in our mock implementation)
                        expect(result1).toBeDefined();
                        expect(result2).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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