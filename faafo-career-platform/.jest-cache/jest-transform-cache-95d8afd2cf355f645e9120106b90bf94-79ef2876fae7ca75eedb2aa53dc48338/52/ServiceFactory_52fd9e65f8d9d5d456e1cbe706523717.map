{"version": 3, "names": ["cov_xaptozvnk", "actualCoverage", "s", "SkillAssessmentEngine_1", "require", "SkillMarketDataService_1", "PersonalizedLearningPathService_1", "EdgeCaseHandler_1", "SkillServiceFactory", "f", "assessmentEngine", "marketDataService", "learningPathService", "edgeCaseHandler", "getInstance", "instance", "b", "prototype", "initializeServices", "SkillAssessmentEngine", "SkillMarketDataService", "PersonalizedLearningPathService", "EdgeCaseHandler", "getServices", "getAssessmentEngine", "getMarketDataService", "getLearningPathService", "getEdgeCaseHandler", "reset", "exports", "skillServiceFactory", "getSkillAssessmentEngine", "getSkillMarketDataService", "getPersonalizedLearningPathService"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/ServiceFactory.ts"], "sourcesContent": ["import { SkillAssessmentEngine } from './SkillAssessmentEngine';\nimport { SkillMarketDataService } from './SkillMarketDataService';\nimport { PersonalizedLearningPathService } from './PersonalizedLearningPathService';\nimport { EdgeCaseHandler } from './EdgeCaseHandler';\n\n/**\n * Service Factory for creating properly integrated skill services\n * with EdgeCaseHandler support\n */\nexport class SkillServiceFactory {\n  private static instance: SkillServiceFactory;\n  private assessmentEngine: SkillAssessmentEngine | null = null;\n  private marketDataService: SkillMarketDataService | null = null;\n  private learningPathService: PersonalizedLearningPathService | null = null;\n  private edgeCaseHandler: EdgeCaseHandler | null = null;\n\n  private constructor() {}\n\n  static getInstance(): SkillServiceFactory {\n    if (!SkillServiceFactory.instance) {\n      SkillServiceFactory.instance = new SkillServiceFactory();\n    }\n    return SkillServiceFactory.instance;\n  }\n\n  /**\n   * Initialize all services with proper EdgeCaseHandler integration\n   */\n  initializeServices(): {\n    assessmentEngine: SkillAssessmentEngine;\n    marketDataService: SkillMarketDataService;\n    learningPathService: PersonalizedLearningPathService;\n    edgeCaseHandler: EdgeCaseHandler;\n  } {\n    // Create services first\n    this.assessmentEngine = new SkillAssessmentEngine();\n    this.marketDataService = new SkillMarketDataService();\n    this.learningPathService = new PersonalizedLearningPathService();\n\n    // Create EdgeCaseHandler with service references\n    this.edgeCaseHandler = new EdgeCaseHandler(\n      this.assessmentEngine,\n      this.marketDataService,\n      this.learningPathService\n    );\n\n    return {\n      assessmentEngine: this.assessmentEngine,\n      marketDataService: this.marketDataService,\n      learningPathService: this.learningPathService,\n      edgeCaseHandler: this.edgeCaseHandler,\n    };\n  }\n\n  /**\n   * Get existing services or initialize if not already done\n   */\n  getServices(): {\n    assessmentEngine: SkillAssessmentEngine;\n    marketDataService: SkillMarketDataService;\n    learningPathService: PersonalizedLearningPathService;\n    edgeCaseHandler: EdgeCaseHandler;\n  } {\n    if (!this.assessmentEngine || !this.marketDataService || !this.learningPathService || !this.edgeCaseHandler) {\n      return this.initializeServices();\n    }\n\n    return {\n      assessmentEngine: this.assessmentEngine,\n      marketDataService: this.marketDataService,\n      learningPathService: this.learningPathService,\n      edgeCaseHandler: this.edgeCaseHandler,\n    };\n  }\n\n  /**\n   * Get individual services\n   */\n  getAssessmentEngine(): SkillAssessmentEngine {\n    return this.getServices().assessmentEngine;\n  }\n\n  getMarketDataService(): SkillMarketDataService {\n    return this.getServices().marketDataService;\n  }\n\n  getLearningPathService(): PersonalizedLearningPathService {\n    return this.getServices().learningPathService;\n  }\n\n  getEdgeCaseHandler(): EdgeCaseHandler {\n    return this.getServices().edgeCaseHandler;\n  }\n\n  /**\n   * Reset all services (useful for testing)\n   */\n  reset(): void {\n    this.assessmentEngine = null;\n    this.marketDataService = null;\n    this.learningPathService = null;\n    this.edgeCaseHandler = null;\n  }\n}\n\n// Export singleton instance\nexport const skillServiceFactory = SkillServiceFactory.getInstance();\n\n// Export individual service getters for convenience\nexport const getSkillAssessmentEngine = () => skillServiceFactory.getAssessmentEngine();\nexport const getSkillMarketDataService = () => skillServiceFactory.getMarketDataService();\nexport const getPersonalizedLearningPathService = () => skillServiceFactory.getLearningPathService();\nexport const getEdgeCaseHandler = () => skillServiceFactory.getEdgeCaseHandler();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAaU;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AAbV,IAAAC,uBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,IAAAC,wBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,IAAAE,iCAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,IAAAG,iBAAA;AAAA;AAAA,CAAAP,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA;;;;AAIA,IAAAI,mBAAA;AAAA;AAAA,cAAAR,aAAA,GAAAE,CAAA;EAAA;EAAAF,aAAA,GAAAS,CAAA;EAOE,SAAAD,oBAAA;IAAA;IAAAR,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IALQ,KAAAQ,gBAAgB,GAAiC,IAAI;IAAC;IAAAV,aAAA,GAAAE,CAAA;IACtD,KAAAS,iBAAiB,GAAkC,IAAI;IAAC;IAAAX,aAAA,GAAAE,CAAA;IACxD,KAAAU,mBAAmB,GAA2C,IAAI;IAAC;IAAAZ,aAAA,GAAAE,CAAA;IACnE,KAAAW,eAAe,GAA2B,IAAI;EAE/B;EAAC;EAAAb,aAAA,GAAAE,CAAA;EAEjBM,mBAAA,CAAAM,WAAW,GAAlB;IAAA;IAAAd,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IACE,IAAI,CAACM,mBAAmB,CAACO,QAAQ,EAAE;MAAA;MAAAf,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAE,CAAA;MACjCM,mBAAmB,CAACO,QAAQ,GAAG,IAAIP,mBAAmB,EAAE;IAC1D,CAAC;IAAA;IAAA;MAAAR,aAAA,GAAAgB,CAAA;IAAA;IAAAhB,aAAA,GAAAE,CAAA;IACD,OAAOM,mBAAmB,CAACO,QAAQ;EACrC,CAAC;EAED;;;EAAA;EAAAf,aAAA,GAAAE,CAAA;EAGAM,mBAAA,CAAAS,SAAA,CAAAC,kBAAkB,GAAlB;IAAA;IAAAlB,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IAME;IACA,IAAI,CAACQ,gBAAgB,GAAG,IAAIP,uBAAA,CAAAgB,qBAAqB,EAAE;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IACpD,IAAI,CAACS,iBAAiB,GAAG,IAAIN,wBAAA,CAAAe,sBAAsB,EAAE;IAAC;IAAApB,aAAA,GAAAE,CAAA;IACtD,IAAI,CAACU,mBAAmB,GAAG,IAAIN,iCAAA,CAAAe,+BAA+B,EAAE;IAEhE;IAAA;IAAArB,aAAA,GAAAE,CAAA;IACA,IAAI,CAACW,eAAe,GAAG,IAAIN,iBAAA,CAAAe,eAAe,CACxC,IAAI,CAACZ,gBAAgB,EACrB,IAAI,CAACC,iBAAiB,EACtB,IAAI,CAACC,mBAAmB,CACzB;IAAC;IAAAZ,aAAA,GAAAE,CAAA;IAEF,OAAO;MACLQ,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,eAAe,EAAE,IAAI,CAACA;KACvB;EACH,CAAC;EAED;;;EAAA;EAAAb,aAAA,GAAAE,CAAA;EAGAM,mBAAA,CAAAS,SAAA,CAAAM,WAAW,GAAX;IAAA;IAAAvB,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IAME;IAAI;IAAA,CAAAF,aAAA,GAAAgB,CAAA,WAAC,IAAI,CAACN,gBAAgB;IAAA;IAAA,CAAAV,aAAA,GAAAgB,CAAA,UAAI,CAAC,IAAI,CAACL,iBAAiB;IAAA;IAAA,CAAAX,aAAA,GAAAgB,CAAA,UAAI,CAAC,IAAI,CAACJ,mBAAmB;IAAA;IAAA,CAAAZ,aAAA,GAAAgB,CAAA,UAAI,CAAC,IAAI,CAACH,eAAe,GAAE;MAAA;MAAAb,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAE,CAAA;MAC3G,OAAO,IAAI,CAACgB,kBAAkB,EAAE;IAClC,CAAC;IAAA;IAAA;MAAAlB,aAAA,GAAAgB,CAAA;IAAA;IAAAhB,aAAA,GAAAE,CAAA;IAED,OAAO;MACLQ,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,eAAe,EAAE,IAAI,CAACA;KACvB;EACH,CAAC;EAED;;;EAAA;EAAAb,aAAA,GAAAE,CAAA;EAGAM,mBAAA,CAAAS,SAAA,CAAAO,mBAAmB,GAAnB;IAAA;IAAAxB,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IACE,OAAO,IAAI,CAACqB,WAAW,EAAE,CAACb,gBAAgB;EAC5C,CAAC;EAAA;EAAAV,aAAA,GAAAE,CAAA;EAEDM,mBAAA,CAAAS,SAAA,CAAAQ,oBAAoB,GAApB;IAAA;IAAAzB,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IACE,OAAO,IAAI,CAACqB,WAAW,EAAE,CAACZ,iBAAiB;EAC7C,CAAC;EAAA;EAAAX,aAAA,GAAAE,CAAA;EAEDM,mBAAA,CAAAS,SAAA,CAAAS,sBAAsB,GAAtB;IAAA;IAAA1B,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IACE,OAAO,IAAI,CAACqB,WAAW,EAAE,CAACX,mBAAmB;EAC/C,CAAC;EAAA;EAAAZ,aAAA,GAAAE,CAAA;EAEDM,mBAAA,CAAAS,SAAA,CAAAU,kBAAkB,GAAlB;IAAA;IAAA3B,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IACE,OAAO,IAAI,CAACqB,WAAW,EAAE,CAACV,eAAe;EAC3C,CAAC;EAED;;;EAAA;EAAAb,aAAA,GAAAE,CAAA;EAGAM,mBAAA,CAAAS,SAAA,CAAAW,KAAK,GAAL;IAAA;IAAA5B,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IACE,IAAI,CAACQ,gBAAgB,GAAG,IAAI;IAAC;IAAAV,aAAA,GAAAE,CAAA;IAC7B,IAAI,CAACS,iBAAiB,GAAG,IAAI;IAAC;IAAAX,aAAA,GAAAE,CAAA;IAC9B,IAAI,CAACU,mBAAmB,GAAG,IAAI;IAAC;IAAAZ,aAAA,GAAAE,CAAA;IAChC,IAAI,CAACW,eAAe,GAAG,IAAI;EAC7B,CAAC;EAAA;EAAAb,aAAA,GAAAE,CAAA;EACH,OAAAM,mBAAC;AAAD,CAAC,CA9FD;AA8FC;AAAAR,aAAA,GAAAE,CAAA;AA9FY2B,OAAA,CAAArB,mBAAA,GAAAA,mBAAA;AAgGb;AAAA;AAAAR,aAAA,GAAAE,CAAA;AACa2B,OAAA,CAAAC,mBAAmB,GAAGtB,mBAAmB,CAACM,WAAW,EAAE;AAEpE;AAAA;AAAAd,aAAA,GAAAE,CAAA;AACO,IAAM6B,wBAAwB,GAAG,SAAAA,CAAA;EAAA;EAAA/B,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EAAM,OAAA2B,OAAA,CAAAC,mBAAmB,CAACN,mBAAmB,EAAE;AAAzC,CAAyC;AAAC;AAAAxB,aAAA,GAAAE,CAAA;AAA3E2B,OAAA,CAAAE,wBAAwB,GAAAA,wBAAA;AAAmD;AAAA/B,aAAA,GAAAE,CAAA;AACjF,IAAM8B,yBAAyB,GAAG,SAAAA,CAAA;EAAA;EAAAhC,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EAAM,OAAA2B,OAAA,CAAAC,mBAAmB,CAACL,oBAAoB,EAAE;AAA1C,CAA0C;AAAC;AAAAzB,aAAA,GAAAE,CAAA;AAA7E2B,OAAA,CAAAG,yBAAyB,GAAAA,yBAAA;AAAoD;AAAAhC,aAAA,GAAAE,CAAA;AACnF,IAAM+B,kCAAkC,GAAG,SAAAA,CAAA;EAAA;EAAAjC,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EAAM,OAAA2B,OAAA,CAAAC,mBAAmB,CAACJ,sBAAsB,EAAE;AAA5C,CAA4C;AAAC;AAAA1B,aAAA,GAAAE,CAAA;AAAxF2B,OAAA,CAAAI,kCAAkC,GAAAA,kCAAA;AAAsD;AAAAjC,aAAA,GAAAE,CAAA;AAC9F,IAAMyB,kBAAkB,GAAG,SAAAA,CAAA;EAAA;EAAA3B,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EAAM,OAAA2B,OAAA,CAAAC,mBAAmB,CAACH,kBAAkB,EAAE;AAAxC,CAAwC;AAAC;AAAA3B,aAAA,GAAAE,CAAA;AAApE2B,OAAA,CAAAF,kBAAkB,GAAAA,kBAAA", "ignoreList": []}