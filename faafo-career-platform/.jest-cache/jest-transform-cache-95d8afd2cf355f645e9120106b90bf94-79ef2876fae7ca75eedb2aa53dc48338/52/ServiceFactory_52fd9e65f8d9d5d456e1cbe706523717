57352bf345b7828bf4824c891ff34b33
"use strict";

/* istanbul ignore next */
function cov_xaptozvnk() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/ServiceFactory.ts";
  var hash = "1bbe08ed8e5cf2dd4bc524fa2d00a2cb4d2e8e99";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/ServiceFactory.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 212
        }
      },
      "2": {
        start: {
          line: 4,
          column: 30
        },
        end: {
          line: 4,
          column: 64
        }
      },
      "3": {
        start: {
          line: 5,
          column: 31
        },
        end: {
          line: 5,
          column: 66
        }
      },
      "4": {
        start: {
          line: 6,
          column: 40
        },
        end: {
          line: 6,
          column: 84
        }
      },
      "5": {
        start: {
          line: 7,
          column: 24
        },
        end: {
          line: 7,
          column: 52
        }
      },
      "6": {
        start: {
          line: 12,
          column: 41
        },
        end: {
          line: 81,
          column: 3
        }
      },
      "7": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 37
        }
      },
      "8": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 15,
          column: 38
        }
      },
      "9": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 40
        }
      },
      "10": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 17,
          column: 36
        }
      },
      "11": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 24,
          column: 6
        }
      },
      "12": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 22,
          column: 9
        }
      },
      "13": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 21,
          column: 69
        }
      },
      "14": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 44
        }
      },
      "15": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 41,
          column: 6
        }
      },
      "16": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 84
        }
      },
      "17": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 87
        }
      },
      "18": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 107
        }
      },
      "19": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 34,
          column: 142
        }
      },
      "20": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 40,
          column: 10
        }
      },
      "21": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 55,
          column: 6
        }
      },
      "22": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 48,
          column: 9
        }
      },
      "23": {
        start: {
          line: 47,
          column: 12
        },
        end: {
          line: 47,
          column: 45
        }
      },
      "24": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 54,
          column: 10
        }
      },
      "25": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 61,
          column: 6
        }
      },
      "26": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 51
        }
      },
      "27": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 64,
          column: 6
        }
      },
      "28": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 52
        }
      },
      "29": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 67,
          column: 6
        }
      },
      "30": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 66,
          column: 54
        }
      },
      "31": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 70,
          column: 6
        }
      },
      "32": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 50
        }
      },
      "33": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 79,
          column: 6
        }
      },
      "34": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 37
        }
      },
      "35": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 38
        }
      },
      "36": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 77,
          column: 40
        }
      },
      "37": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 78,
          column: 36
        }
      },
      "38": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 80,
          column: 31
        }
      },
      "39": {
        start: {
          line: 82,
          column: 0
        },
        end: {
          line: 82,
          column: 50
        }
      },
      "40": {
        start: {
          line: 84,
          column: 0
        },
        end: {
          line: 84,
          column: 64
        }
      },
      "41": {
        start: {
          line: 86,
          column: 31
        },
        end: {
          line: 86,
          column: 104
        }
      },
      "42": {
        start: {
          line: 86,
          column: 45
        },
        end: {
          line: 86,
          column: 102
        }
      },
      "43": {
        start: {
          line: 87,
          column: 0
        },
        end: {
          line: 87,
          column: 60
        }
      },
      "44": {
        start: {
          line: 88,
          column: 32
        },
        end: {
          line: 88,
          column: 106
        }
      },
      "45": {
        start: {
          line: 88,
          column: 46
        },
        end: {
          line: 88,
          column: 104
        }
      },
      "46": {
        start: {
          line: 89,
          column: 0
        },
        end: {
          line: 89,
          column: 62
        }
      },
      "47": {
        start: {
          line: 90,
          column: 41
        },
        end: {
          line: 90,
          column: 117
        }
      },
      "48": {
        start: {
          line: 90,
          column: 55
        },
        end: {
          line: 90,
          column: 115
        }
      },
      "49": {
        start: {
          line: 91,
          column: 0
        },
        end: {
          line: 91,
          column: 80
        }
      },
      "50": {
        start: {
          line: 92,
          column: 25
        },
        end: {
          line: 92,
          column: 97
        }
      },
      "51": {
        start: {
          line: 92,
          column: 39
        },
        end: {
          line: 92,
          column: 95
        }
      },
      "52": {
        start: {
          line: 93,
          column: 0
        },
        end: {
          line: 93,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 42
          }
        },
        loc: {
          start: {
            line: 12,
            column: 53
          },
          end: {
            line: 81,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "SkillServiceFactory",
        decl: {
          start: {
            line: 13,
            column: 13
          },
          end: {
            line: 13,
            column: 32
          }
        },
        loc: {
          start: {
            line: 13,
            column: 35
          },
          end: {
            line: 18,
            column: 5
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 38
          },
          end: {
            line: 19,
            column: 39
          }
        },
        loc: {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 24,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 28,
            column: 55
          },
          end: {
            line: 28,
            column: 56
          }
        },
        loc: {
          start: {
            line: 28,
            column: 67
          },
          end: {
            line: 41,
            column: 5
          }
        },
        line: 28
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 45,
            column: 48
          },
          end: {
            line: 45,
            column: 49
          }
        },
        loc: {
          start: {
            line: 45,
            column: 60
          },
          end: {
            line: 55,
            column: 5
          }
        },
        line: 45
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 59,
            column: 56
          },
          end: {
            line: 59,
            column: 57
          }
        },
        loc: {
          start: {
            line: 59,
            column: 68
          },
          end: {
            line: 61,
            column: 5
          }
        },
        line: 59
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 62,
            column: 57
          },
          end: {
            line: 62,
            column: 58
          }
        },
        loc: {
          start: {
            line: 62,
            column: 69
          },
          end: {
            line: 64,
            column: 5
          }
        },
        line: 62
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 65,
            column: 59
          },
          end: {
            line: 65,
            column: 60
          }
        },
        loc: {
          start: {
            line: 65,
            column: 71
          },
          end: {
            line: 67,
            column: 5
          }
        },
        line: 65
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 68,
            column: 55
          },
          end: {
            line: 68,
            column: 56
          }
        },
        loc: {
          start: {
            line: 68,
            column: 67
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 68
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 74,
            column: 42
          },
          end: {
            line: 74,
            column: 43
          }
        },
        loc: {
          start: {
            line: 74,
            column: 54
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 74
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 86,
            column: 31
          },
          end: {
            line: 86,
            column: 32
          }
        },
        loc: {
          start: {
            line: 86,
            column: 43
          },
          end: {
            line: 86,
            column: 104
          }
        },
        line: 86
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 88,
            column: 32
          },
          end: {
            line: 88,
            column: 33
          }
        },
        loc: {
          start: {
            line: 88,
            column: 44
          },
          end: {
            line: 88,
            column: 106
          }
        },
        line: 88
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 90,
            column: 41
          },
          end: {
            line: 90,
            column: 42
          }
        },
        loc: {
          start: {
            line: 90,
            column: 53
          },
          end: {
            line: 90,
            column: 117
          }
        },
        line: 90
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 92,
            column: 25
          },
          end: {
            line: 92,
            column: 26
          }
        },
        loc: {
          start: {
            line: 92,
            column: 37
          },
          end: {
            line: 92,
            column: 97
          }
        },
        line: 92
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 22,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 22,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "1": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 48,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 48,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "2": {
        loc: {
          start: {
            line: 46,
            column: 12
          },
          end: {
            line: 46,
            column: 115
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 12
          },
          end: {
            line: 46,
            column: 34
          }
        }, {
          start: {
            line: 46,
            column: 38
          },
          end: {
            line: 46,
            column: 61
          }
        }, {
          start: {
            line: 46,
            column: 65
          },
          end: {
            line: 46,
            column: 90
          }
        }, {
          start: {
            line: 46,
            column: 94
          },
          end: {
            line: 46,
            column: 115
          }
        }],
        line: 46
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/ServiceFactory.ts",
      mappings: ";;;AAAA,iEAAgE;AAChE,mEAAkE;AAClE,qFAAoF;AACpF,qDAAoD;AAEpD;;;GAGG;AACH;IAOE;QALQ,qBAAgB,GAAiC,IAAI,CAAC;QACtD,sBAAiB,GAAkC,IAAI,CAAC;QACxD,wBAAmB,GAA2C,IAAI,CAAC;QACnE,oBAAe,GAA2B,IAAI,CAAC;IAEhC,CAAC;IAEjB,+BAAW,GAAlB;QACE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YAClC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC3D,CAAC;QACD,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,gDAAkB,GAAlB;QAME,wBAAwB;QACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,6CAAqB,EAAE,CAAC;QACpD,IAAI,CAAC,iBAAiB,GAAG,IAAI,+CAAsB,EAAE,CAAC;QACtD,IAAI,CAAC,mBAAmB,GAAG,IAAI,iEAA+B,EAAE,CAAC;QAEjE,iDAAiD;QACjD,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CACxC,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,mBAAmB,CACzB,CAAC;QAEF,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,yCAAW,GAAX;QAME,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5G,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnC,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iDAAmB,GAAnB;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,gBAAgB,CAAC;IAC7C,CAAC;IAED,kDAAoB,GAApB;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,iBAAiB,CAAC;IAC9C,CAAC;IAED,oDAAsB,GAAtB;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,mBAAmB,CAAC;IAChD,CAAC;IAED,gDAAkB,GAAlB;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,mCAAK,GAAL;QACE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;IACH,0BAAC;AAAD,CAAC,AA9FD,IA8FC;AA9FY,kDAAmB;AAgGhC,4BAA4B;AACf,QAAA,mBAAmB,GAAG,mBAAmB,CAAC,WAAW,EAAE,CAAC;AAErE,oDAAoD;AAC7C,IAAM,wBAAwB,GAAG,cAAM,OAAA,2BAAmB,CAAC,mBAAmB,EAAE,EAAzC,CAAyC,CAAC;AAA3E,QAAA,wBAAwB,4BAAmD;AACjF,IAAM,yBAAyB,GAAG,cAAM,OAAA,2BAAmB,CAAC,oBAAoB,EAAE,EAA1C,CAA0C,CAAC;AAA7E,QAAA,yBAAyB,6BAAoD;AACnF,IAAM,kCAAkC,GAAG,cAAM,OAAA,2BAAmB,CAAC,sBAAsB,EAAE,EAA5C,CAA4C,CAAC;AAAxF,QAAA,kCAAkC,sCAAsD;AAC9F,IAAM,kBAAkB,GAAG,cAAM,OAAA,2BAAmB,CAAC,kBAAkB,EAAE,EAAxC,CAAwC,CAAC;AAApE,QAAA,kBAAkB,sBAAkD",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/ServiceFactory.ts"],
      sourcesContent: ["import { SkillAssessmentEngine } from './SkillAssessmentEngine';\nimport { SkillMarketDataService } from './SkillMarketDataService';\nimport { PersonalizedLearningPathService } from './PersonalizedLearningPathService';\nimport { EdgeCaseHandler } from './EdgeCaseHandler';\n\n/**\n * Service Factory for creating properly integrated skill services\n * with EdgeCaseHandler support\n */\nexport class SkillServiceFactory {\n  private static instance: SkillServiceFactory;\n  private assessmentEngine: SkillAssessmentEngine | null = null;\n  private marketDataService: SkillMarketDataService | null = null;\n  private learningPathService: PersonalizedLearningPathService | null = null;\n  private edgeCaseHandler: EdgeCaseHandler | null = null;\n\n  private constructor() {}\n\n  static getInstance(): SkillServiceFactory {\n    if (!SkillServiceFactory.instance) {\n      SkillServiceFactory.instance = new SkillServiceFactory();\n    }\n    return SkillServiceFactory.instance;\n  }\n\n  /**\n   * Initialize all services with proper EdgeCaseHandler integration\n   */\n  initializeServices(): {\n    assessmentEngine: SkillAssessmentEngine;\n    marketDataService: SkillMarketDataService;\n    learningPathService: PersonalizedLearningPathService;\n    edgeCaseHandler: EdgeCaseHandler;\n  } {\n    // Create services first\n    this.assessmentEngine = new SkillAssessmentEngine();\n    this.marketDataService = new SkillMarketDataService();\n    this.learningPathService = new PersonalizedLearningPathService();\n\n    // Create EdgeCaseHandler with service references\n    this.edgeCaseHandler = new EdgeCaseHandler(\n      this.assessmentEngine,\n      this.marketDataService,\n      this.learningPathService\n    );\n\n    return {\n      assessmentEngine: this.assessmentEngine,\n      marketDataService: this.marketDataService,\n      learningPathService: this.learningPathService,\n      edgeCaseHandler: this.edgeCaseHandler,\n    };\n  }\n\n  /**\n   * Get existing services or initialize if not already done\n   */\n  getServices(): {\n    assessmentEngine: SkillAssessmentEngine;\n    marketDataService: SkillMarketDataService;\n    learningPathService: PersonalizedLearningPathService;\n    edgeCaseHandler: EdgeCaseHandler;\n  } {\n    if (!this.assessmentEngine || !this.marketDataService || !this.learningPathService || !this.edgeCaseHandler) {\n      return this.initializeServices();\n    }\n\n    return {\n      assessmentEngine: this.assessmentEngine,\n      marketDataService: this.marketDataService,\n      learningPathService: this.learningPathService,\n      edgeCaseHandler: this.edgeCaseHandler,\n    };\n  }\n\n  /**\n   * Get individual services\n   */\n  getAssessmentEngine(): SkillAssessmentEngine {\n    return this.getServices().assessmentEngine;\n  }\n\n  getMarketDataService(): SkillMarketDataService {\n    return this.getServices().marketDataService;\n  }\n\n  getLearningPathService(): PersonalizedLearningPathService {\n    return this.getServices().learningPathService;\n  }\n\n  getEdgeCaseHandler(): EdgeCaseHandler {\n    return this.getServices().edgeCaseHandler;\n  }\n\n  /**\n   * Reset all services (useful for testing)\n   */\n  reset(): void {\n    this.assessmentEngine = null;\n    this.marketDataService = null;\n    this.learningPathService = null;\n    this.edgeCaseHandler = null;\n  }\n}\n\n// Export singleton instance\nexport const skillServiceFactory = SkillServiceFactory.getInstance();\n\n// Export individual service getters for convenience\nexport const getSkillAssessmentEngine = () => skillServiceFactory.getAssessmentEngine();\nexport const getSkillMarketDataService = () => skillServiceFactory.getMarketDataService();\nexport const getPersonalizedLearningPathService = () => skillServiceFactory.getLearningPathService();\nexport const getEdgeCaseHandler = () => skillServiceFactory.getEdgeCaseHandler();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1bbe08ed8e5cf2dd4bc524fa2d00a2cb4d2e8e99"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_xaptozvnk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xaptozvnk();
cov_xaptozvnk().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_xaptozvnk().s[1]++;
exports.getEdgeCaseHandler = exports.getPersonalizedLearningPathService = exports.getSkillMarketDataService = exports.getSkillAssessmentEngine = exports.skillServiceFactory = exports.SkillServiceFactory = void 0;
var SkillAssessmentEngine_1 =
/* istanbul ignore next */
(cov_xaptozvnk().s[2]++, require("./SkillAssessmentEngine"));
var SkillMarketDataService_1 =
/* istanbul ignore next */
(cov_xaptozvnk().s[3]++, require("./SkillMarketDataService"));
var PersonalizedLearningPathService_1 =
/* istanbul ignore next */
(cov_xaptozvnk().s[4]++, require("./PersonalizedLearningPathService"));
var EdgeCaseHandler_1 =
/* istanbul ignore next */
(cov_xaptozvnk().s[5]++, require("./EdgeCaseHandler"));
/**
 * Service Factory for creating properly integrated skill services
 * with EdgeCaseHandler support
 */
var SkillServiceFactory =
/* istanbul ignore next */
(/** @class */cov_xaptozvnk().s[6]++, function () {
  /* istanbul ignore next */
  cov_xaptozvnk().f[0]++;
  function SkillServiceFactory() {
    /* istanbul ignore next */
    cov_xaptozvnk().f[1]++;
    cov_xaptozvnk().s[7]++;
    this.assessmentEngine = null;
    /* istanbul ignore next */
    cov_xaptozvnk().s[8]++;
    this.marketDataService = null;
    /* istanbul ignore next */
    cov_xaptozvnk().s[9]++;
    this.learningPathService = null;
    /* istanbul ignore next */
    cov_xaptozvnk().s[10]++;
    this.edgeCaseHandler = null;
  }
  /* istanbul ignore next */
  cov_xaptozvnk().s[11]++;
  SkillServiceFactory.getInstance = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[2]++;
    cov_xaptozvnk().s[12]++;
    if (!SkillServiceFactory.instance) {
      /* istanbul ignore next */
      cov_xaptozvnk().b[0][0]++;
      cov_xaptozvnk().s[13]++;
      SkillServiceFactory.instance = new SkillServiceFactory();
    } else
    /* istanbul ignore next */
    {
      cov_xaptozvnk().b[0][1]++;
    }
    cov_xaptozvnk().s[14]++;
    return SkillServiceFactory.instance;
  };
  /**
   * Initialize all services with proper EdgeCaseHandler integration
   */
  /* istanbul ignore next */
  cov_xaptozvnk().s[15]++;
  SkillServiceFactory.prototype.initializeServices = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[3]++;
    cov_xaptozvnk().s[16]++;
    // Create services first
    this.assessmentEngine = new SkillAssessmentEngine_1.SkillAssessmentEngine();
    /* istanbul ignore next */
    cov_xaptozvnk().s[17]++;
    this.marketDataService = new SkillMarketDataService_1.SkillMarketDataService();
    /* istanbul ignore next */
    cov_xaptozvnk().s[18]++;
    this.learningPathService = new PersonalizedLearningPathService_1.PersonalizedLearningPathService();
    // Create EdgeCaseHandler with service references
    /* istanbul ignore next */
    cov_xaptozvnk().s[19]++;
    this.edgeCaseHandler = new EdgeCaseHandler_1.EdgeCaseHandler(this.assessmentEngine, this.marketDataService, this.learningPathService);
    /* istanbul ignore next */
    cov_xaptozvnk().s[20]++;
    return {
      assessmentEngine: this.assessmentEngine,
      marketDataService: this.marketDataService,
      learningPathService: this.learningPathService,
      edgeCaseHandler: this.edgeCaseHandler
    };
  };
  /**
   * Get existing services or initialize if not already done
   */
  /* istanbul ignore next */
  cov_xaptozvnk().s[21]++;
  SkillServiceFactory.prototype.getServices = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[4]++;
    cov_xaptozvnk().s[22]++;
    if (
    /* istanbul ignore next */
    (cov_xaptozvnk().b[2][0]++, !this.assessmentEngine) ||
    /* istanbul ignore next */
    (cov_xaptozvnk().b[2][1]++, !this.marketDataService) ||
    /* istanbul ignore next */
    (cov_xaptozvnk().b[2][2]++, !this.learningPathService) ||
    /* istanbul ignore next */
    (cov_xaptozvnk().b[2][3]++, !this.edgeCaseHandler)) {
      /* istanbul ignore next */
      cov_xaptozvnk().b[1][0]++;
      cov_xaptozvnk().s[23]++;
      return this.initializeServices();
    } else
    /* istanbul ignore next */
    {
      cov_xaptozvnk().b[1][1]++;
    }
    cov_xaptozvnk().s[24]++;
    return {
      assessmentEngine: this.assessmentEngine,
      marketDataService: this.marketDataService,
      learningPathService: this.learningPathService,
      edgeCaseHandler: this.edgeCaseHandler
    };
  };
  /**
   * Get individual services
   */
  /* istanbul ignore next */
  cov_xaptozvnk().s[25]++;
  SkillServiceFactory.prototype.getAssessmentEngine = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[5]++;
    cov_xaptozvnk().s[26]++;
    return this.getServices().assessmentEngine;
  };
  /* istanbul ignore next */
  cov_xaptozvnk().s[27]++;
  SkillServiceFactory.prototype.getMarketDataService = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[6]++;
    cov_xaptozvnk().s[28]++;
    return this.getServices().marketDataService;
  };
  /* istanbul ignore next */
  cov_xaptozvnk().s[29]++;
  SkillServiceFactory.prototype.getLearningPathService = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[7]++;
    cov_xaptozvnk().s[30]++;
    return this.getServices().learningPathService;
  };
  /* istanbul ignore next */
  cov_xaptozvnk().s[31]++;
  SkillServiceFactory.prototype.getEdgeCaseHandler = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[8]++;
    cov_xaptozvnk().s[32]++;
    return this.getServices().edgeCaseHandler;
  };
  /**
   * Reset all services (useful for testing)
   */
  /* istanbul ignore next */
  cov_xaptozvnk().s[33]++;
  SkillServiceFactory.prototype.reset = function () {
    /* istanbul ignore next */
    cov_xaptozvnk().f[9]++;
    cov_xaptozvnk().s[34]++;
    this.assessmentEngine = null;
    /* istanbul ignore next */
    cov_xaptozvnk().s[35]++;
    this.marketDataService = null;
    /* istanbul ignore next */
    cov_xaptozvnk().s[36]++;
    this.learningPathService = null;
    /* istanbul ignore next */
    cov_xaptozvnk().s[37]++;
    this.edgeCaseHandler = null;
  };
  /* istanbul ignore next */
  cov_xaptozvnk().s[38]++;
  return SkillServiceFactory;
}());
/* istanbul ignore next */
cov_xaptozvnk().s[39]++;
exports.SkillServiceFactory = SkillServiceFactory;
// Export singleton instance
/* istanbul ignore next */
cov_xaptozvnk().s[40]++;
exports.skillServiceFactory = SkillServiceFactory.getInstance();
// Export individual service getters for convenience
/* istanbul ignore next */
cov_xaptozvnk().s[41]++;
var getSkillAssessmentEngine = function () {
  /* istanbul ignore next */
  cov_xaptozvnk().f[10]++;
  cov_xaptozvnk().s[42]++;
  return exports.skillServiceFactory.getAssessmentEngine();
};
/* istanbul ignore next */
cov_xaptozvnk().s[43]++;
exports.getSkillAssessmentEngine = getSkillAssessmentEngine;
/* istanbul ignore next */
cov_xaptozvnk().s[44]++;
var getSkillMarketDataService = function () {
  /* istanbul ignore next */
  cov_xaptozvnk().f[11]++;
  cov_xaptozvnk().s[45]++;
  return exports.skillServiceFactory.getMarketDataService();
};
/* istanbul ignore next */
cov_xaptozvnk().s[46]++;
exports.getSkillMarketDataService = getSkillMarketDataService;
/* istanbul ignore next */
cov_xaptozvnk().s[47]++;
var getPersonalizedLearningPathService = function () {
  /* istanbul ignore next */
  cov_xaptozvnk().f[12]++;
  cov_xaptozvnk().s[48]++;
  return exports.skillServiceFactory.getLearningPathService();
};
/* istanbul ignore next */
cov_xaptozvnk().s[49]++;
exports.getPersonalizedLearningPathService = getPersonalizedLearningPathService;
/* istanbul ignore next */
cov_xaptozvnk().s[50]++;
var getEdgeCaseHandler = function () {
  /* istanbul ignore next */
  cov_xaptozvnk().f[13]++;
  cov_xaptozvnk().s[51]++;
  return exports.skillServiceFactory.getEdgeCaseHandler();
};
/* istanbul ignore next */
cov_xaptozvnk().s[52]++;
exports.getEdgeCaseHandler = getEdgeCaseHandler;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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