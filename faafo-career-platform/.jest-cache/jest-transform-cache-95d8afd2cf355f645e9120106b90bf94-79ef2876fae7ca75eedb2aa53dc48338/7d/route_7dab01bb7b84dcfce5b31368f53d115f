ca3d9c39212c335329c070d2d1fd0e93
"use strict";

/* istanbul ignore next */
function cov_2757l6cm5l() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/photo/route.ts";
  var hash = "a4b832349cbc177ddb7e003007a10dc57037fc21";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/photo/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 53
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 18
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "73": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "74": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "75": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 76
        }
      },
      "76": {
        start: {
          line: 48,
          column: 14
        },
        end: {
          line: 48,
          column: 47
        }
      },
      "77": {
        start: {
          line: 49,
          column: 15
        },
        end: {
          line: 49,
          column: 49
        }
      },
      "78": {
        start: {
          line: 50,
          column: 13
        },
        end: {
          line: 50,
          column: 36
        }
      },
      "79": {
        start: {
          line: 51,
          column: 15
        },
        end: {
          line: 51,
          column: 38
        }
      },
      "80": {
        start: {
          line: 52,
          column: 13
        },
        end: {
          line: 52,
          column: 34
        }
      },
      "81": {
        start: {
          line: 53,
          column: 30
        },
        end: {
          line: 53,
          column: 68
        }
      },
      "82": {
        start: {
          line: 55,
          column: 20
        },
        end: {
          line: 55,
          column: 35
        }
      },
      "83": {
        start: {
          line: 56,
          column: 20
        },
        end: {
          line: 56,
          column: 61
        }
      },
      "84": {
        start: {
          line: 57,
          column: 25
        },
        end: {
          line: 57,
          column: 59
        }
      },
      "85": {
        start: {
          line: 58,
          column: 19
        },
        end: {
          line: 63,
          column: 1
        }
      },
      "86": {
        start: {
          line: 65,
          column: 22
        },
        end: {
          line: 75,
          column: 1
        }
      },
      "87": {
        start: {
          line: 77,
          column: 25
        },
        end: {
          line: 88,
          column: 1
        }
      },
      "88": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 163,
          column: 7
        }
      },
      "89": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 162,
          column: 11
        }
      },
      "90": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 161,
          column: 13
        }
      },
      "91": {
        start: {
          line: 96,
          column: 20
        },
        end: {
          line: 96,
          column: 32
        }
      },
      "92": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 97,
          column: 59
        }
      },
      "93": {
        start: {
          line: 98,
          column: 20
        },
        end: {
          line: 105,
          column: 21
        }
      },
      "94": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 101,
          column: 27
        }
      },
      "95": {
        start: {
          line: 100,
          column: 28
        },
        end: {
          line: 100,
          column: 110
        }
      },
      "96": {
        start: {
          line: 100,
          column: 76
        },
        end: {
          line: 100,
          column: 106
        }
      },
      "97": {
        start: {
          line: 102,
          column: 24
        },
        end: {
          line: 104,
          column: 25
        }
      },
      "98": {
        start: {
          line: 103,
          column: 28
        },
        end: {
          line: 103,
          column: 92
        }
      },
      "99": {
        start: {
          line: 106,
          column: 20
        },
        end: {
          line: 106,
          column: 61
        }
      },
      "100": {
        start: {
          line: 107,
          column: 20
        },
        end: {
          line: 111,
          column: 23
        }
      },
      "101": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 110,
          column: 25
        }
      },
      "102": {
        start: {
          line: 109,
          column: 28
        },
        end: {
          line: 109,
          column: 78
        }
      },
      "103": {
        start: {
          line: 112,
          column: 20
        },
        end: {
          line: 112,
          column: 96
        }
      },
      "104": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 115,
          column: 21
        }
      },
      "105": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 62
        }
      },
      "106": {
        start: {
          line: 116,
          column: 20
        },
        end: {
          line: 121,
          column: 22
        }
      },
      "107": {
        start: {
          line: 122,
          column: 20
        },
        end: {
          line: 122,
          column: 69
        }
      },
      "108": {
        start: {
          line: 123,
          column: 20
        },
        end: {
          line: 125,
          column: 21
        }
      },
      "109": {
        start: {
          line: 124,
          column: 24
        },
        end: {
          line: 124,
          column: 79
        }
      },
      "110": {
        start: {
          line: 126,
          column: 20
        },
        end: {
          line: 126,
          column: 33
        }
      },
      "111": {
        start: {
          line: 128,
          column: 20
        },
        end: {
          line: 128,
          column: 46
        }
      },
      "112": {
        start: {
          line: 129,
          column: 20
        },
        end: {
          line: 129,
          column: 82
        }
      },
      "113": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 131,
          column: 41
        }
      },
      "114": {
        start: {
          line: 133,
          column: 20
        },
        end: {
          line: 141,
          column: 21
        }
      },
      "115": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 73
        }
      },
      "116": {
        start: {
          line: 136,
          column: 25
        },
        end: {
          line: 141,
          column: 21
        }
      },
      "117": {
        start: {
          line: 137,
          column: 24
        },
        end: {
          line: 137,
          column: 82
        }
      },
      "118": {
        start: {
          line: 139,
          column: 25
        },
        end: {
          line: 141,
          column: 21
        }
      },
      "119": {
        start: {
          line: 140,
          column: 24
        },
        end: {
          line: 140,
          column: 78
        }
      },
      "120": {
        start: {
          line: 143,
          column: 20
        },
        end: {
          line: 145,
          column: 21
        }
      },
      "121": {
        start: {
          line: 144,
          column: 24
        },
        end: {
          line: 144,
          column: 68
        }
      },
      "122": {
        start: {
          line: 146,
          column: 20
        },
        end: {
          line: 148,
          column: 41
        }
      },
      "123": {
        start: {
          line: 150,
          column: 20
        },
        end: {
          line: 150,
          column: 48
        }
      },
      "124": {
        start: {
          line: 151,
          column: 20
        },
        end: {
          line: 155,
          column: 27
        }
      },
      "125": {
        start: {
          line: 157,
          column: 20
        },
        end: {
          line: 157,
          column: 40
        }
      },
      "126": {
        start: {
          line: 158,
          column: 20
        },
        end: {
          line: 158,
          column: 64
        }
      },
      "127": {
        start: {
          line: 159,
          column: 20
        },
        end: {
          line: 159,
          column: 78
        }
      },
      "128": {
        start: {
          line: 160,
          column: 24
        },
        end: {
          line: 160,
          column: 46
        }
      },
      "129": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 186,
          column: 7
        }
      },
      "130": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 185,
          column: 11
        }
      },
      "131": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 184,
          column: 13
        }
      },
      "132": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 176,
          column: 37
        }
      },
      "133": {
        start: {
          line: 178,
          column: 20
        },
        end: {
          line: 178,
          column: 42
        }
      },
      "134": {
        start: {
          line: 179,
          column: 20
        },
        end: {
          line: 183,
          column: 27
        }
      },
      "135": {
        start: {
          line: 189,
          column: 20
        },
        end: {
          line: 189,
          column: 30
        }
      },
      "136": {
        start: {
          line: 190,
          column: 15
        },
        end: {
          line: 190,
          column: 128
        }
      },
      "137": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 191,
          column: 81
        }
      },
      "138": {
        start: {
          line: 195,
          column: 4
        },
        end: {
          line: 230,
          column: 7
        }
      },
      "139": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 229,
          column: 11
        }
      },
      "140": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 228,
          column: 13
        }
      },
      "141": {
        start: {
          line: 200,
          column: 20
        },
        end: {
          line: 200,
          column: 46
        }
      },
      "142": {
        start: {
          line: 202,
          column: 20
        },
        end: {
          line: 210,
          column: 21
        }
      },
      "143": {
        start: {
          line: 203,
          column: 24
        },
        end: {
          line: 207,
          column: 27
        }
      },
      "144": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 86
        }
      },
      "145": {
        start: {
          line: 209,
          column: 24
        },
        end: {
          line: 209,
          column: 106
        }
      },
      "146": {
        start: {
          line: 211,
          column: 20
        },
        end: {
          line: 214,
          column: 28
        }
      },
      "147": {
        start: {
          line: 216,
          column: 20
        },
        end: {
          line: 216,
          column: 37
        }
      },
      "148": {
        start: {
          line: 217,
          column: 20
        },
        end: {
          line: 217,
          column: 52
        }
      },
      "149": {
        start: {
          line: 219,
          column: 20
        },
        end: {
          line: 219,
          column: 40
        }
      },
      "150": {
        start: {
          line: 220,
          column: 20
        },
        end: {
          line: 224,
          column: 23
        }
      },
      "151": {
        start: {
          line: 225,
          column: 20
        },
        end: {
          line: 225,
          column: 82
        }
      },
      "152": {
        start: {
          line: 226,
          column: 20
        },
        end: {
          line: 226,
          column: 102
        }
      },
      "153": {
        start: {
          line: 227,
          column: 24
        },
        end: {
          line: 227,
          column: 46
        }
      },
      "154": {
        start: {
          line: 232,
          column: 0
        },
        end: {
          line: 364,
          column: 7
        }
      },
      "155": {
        start: {
          line: 232,
          column: 94
        },
        end: {
          line: 364,
          column: 3
        }
      },
      "156": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 363,
          column: 7
        }
      },
      "157": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 362,
          column: 20
        }
      },
      "158": {
        start: {
          line: 234,
          column: 84
        },
        end: {
          line: 362,
          column: 15
        }
      },
      "159": {
        start: {
          line: 237,
          column: 16
        },
        end: {
          line: 361,
          column: 19
        }
      },
      "160": {
        start: {
          line: 238,
          column: 20
        },
        end: {
          line: 360,
          column: 21
        }
      },
      "161": {
        start: {
          line: 239,
          column: 32
        },
        end: {
          line: 239,
          column: 125
        }
      },
      "162": {
        start: {
          line: 241,
          column: 28
        },
        end: {
          line: 241,
          column: 56
        }
      },
      "163": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 247,
          column: 29
        }
      },
      "164": {
        start: {
          line: 243,
          column: 32
        },
        end: {
          line: 243,
          column: 108
        }
      },
      "165": {
        start: {
          line: 244,
          column: 32
        },
        end: {
          line: 244,
          column: 55
        }
      },
      "166": {
        start: {
          line: 245,
          column: 32
        },
        end: {
          line: 245,
          column: 72
        }
      },
      "167": {
        start: {
          line: 246,
          column: 32
        },
        end: {
          line: 246,
          column: 44
        }
      },
      "168": {
        start: {
          line: 248,
          column: 28
        },
        end: {
          line: 248,
          column: 104
        }
      },
      "169": {
        start: {
          line: 250,
          column: 28
        },
        end: {
          line: 250,
          column: 48
        }
      },
      "170": {
        start: {
          line: 251,
          column: 28
        },
        end: {
          line: 255,
          column: 29
        }
      },
      "171": {
        start: {
          line: 252,
          column: 32
        },
        end: {
          line: 252,
          column: 71
        }
      },
      "172": {
        start: {
          line: 253,
          column: 32
        },
        end: {
          line: 253,
          column: 55
        }
      },
      "173": {
        start: {
          line: 254,
          column: 32
        },
        end: {
          line: 254,
          column: 44
        }
      },
      "174": {
        start: {
          line: 256,
          column: 28
        },
        end: {
          line: 258,
          column: 36
        }
      },
      "175": {
        start: {
          line: 260,
          column: 28
        },
        end: {
          line: 260,
          column: 45
        }
      },
      "176": {
        start: {
          line: 261,
          column: 28
        },
        end: {
          line: 265,
          column: 29
        }
      },
      "177": {
        start: {
          line: 262,
          column: 32
        },
        end: {
          line: 262,
          column: 68
        }
      },
      "178": {
        start: {
          line: 263,
          column: 32
        },
        end: {
          line: 263,
          column: 55
        }
      },
      "179": {
        start: {
          line: 264,
          column: 32
        },
        end: {
          line: 264,
          column: 44
        }
      },
      "180": {
        start: {
          line: 266,
          column: 28
        },
        end: {
          line: 266,
          column: 69
        }
      },
      "181": {
        start: {
          line: 268,
          column: 28
        },
        end: {
          line: 268,
          column: 49
        }
      },
      "182": {
        start: {
          line: 269,
          column: 28
        },
        end: {
          line: 269,
          column: 56
        }
      },
      "183": {
        start: {
          line: 270,
          column: 28
        },
        end: {
          line: 274,
          column: 29
        }
      },
      "184": {
        start: {
          line: 271,
          column: 32
        },
        end: {
          line: 271,
          column: 70
        }
      },
      "185": {
        start: {
          line: 272,
          column: 32
        },
        end: {
          line: 272,
          column: 55
        }
      },
      "186": {
        start: {
          line: 273,
          column: 32
        },
        end: {
          line: 273,
          column: 44
        }
      },
      "187": {
        start: {
          line: 276,
          column: 28
        },
        end: {
          line: 280,
          column: 29
        }
      },
      "188": {
        start: {
          line: 277,
          column: 32
        },
        end: {
          line: 277,
          column: 110
        }
      },
      "189": {
        start: {
          line: 278,
          column: 32
        },
        end: {
          line: 278,
          column: 55
        }
      },
      "190": {
        start: {
          line: 279,
          column: 32
        },
        end: {
          line: 279,
          column: 44
        }
      },
      "191": {
        start: {
          line: 281,
          column: 28
        },
        end: {
          line: 285,
          column: 29
        }
      },
      "192": {
        start: {
          line: 282,
          column: 32
        },
        end: {
          line: 282,
          column: 131
        }
      },
      "193": {
        start: {
          line: 283,
          column: 32
        },
        end: {
          line: 283,
          column: 55
        }
      },
      "194": {
        start: {
          line: 284,
          column: 32
        },
        end: {
          line: 284,
          column: 44
        }
      },
      "195": {
        start: {
          line: 287,
          column: 28
        },
        end: {
          line: 291,
          column: 29
        }
      },
      "196": {
        start: {
          line: 288,
          column: 32
        },
        end: {
          line: 288,
          column: 71
        }
      },
      "197": {
        start: {
          line: 289,
          column: 32
        },
        end: {
          line: 289,
          column: 55
        }
      },
      "198": {
        start: {
          line: 290,
          column: 32
        },
        end: {
          line: 290,
          column: 44
        }
      },
      "199": {
        start: {
          line: 292,
          column: 28
        },
        end: {
          line: 292,
          column: 52
        }
      },
      "200": {
        start: {
          line: 293,
          column: 28
        },
        end: {
          line: 293,
          column: 69
        }
      },
      "201": {
        start: {
          line: 295,
          column: 28
        },
        end: {
          line: 295,
          column: 63
        }
      },
      "202": {
        start: {
          line: 296,
          column: 28
        },
        end: {
          line: 296,
          column: 100
        }
      },
      "203": {
        start: {
          line: 298,
          column: 28
        },
        end: {
          line: 298,
          column: 51
        }
      },
      "204": {
        start: {
          line: 299,
          column: 28
        },
        end: {
          line: 309,
          column: 29
        }
      },
      "205": {
        start: {
          line: 300,
          column: 32
        },
        end: {
          line: 304,
          column: 35
        }
      },
      "206": {
        start: {
          line: 305,
          column: 32
        },
        end: {
          line: 305,
          column: 118
        }
      },
      "207": {
        start: {
          line: 306,
          column: 32
        },
        end: {
          line: 306,
          column: 55
        }
      },
      "208": {
        start: {
          line: 307,
          column: 32
        },
        end: {
          line: 307,
          column: 83
        }
      },
      "209": {
        start: {
          line: 308,
          column: 32
        },
        end: {
          line: 308,
          column: 44
        }
      },
      "210": {
        start: {
          line: 310,
          column: 28
        },
        end: {
          line: 310,
          column: 73
        }
      },
      "211": {
        start: {
          line: 311,
          column: 28
        },
        end: {
          line: 326,
          column: 41
        }
      },
      "212": {
        start: {
          line: 311,
          column: 110
        },
        end: {
          line: 326,
          column: 35
        }
      },
      "213": {
        start: {
          line: 313,
          column: 51
        },
        end: {
          line: 313,
          column: 56
        }
      },
      "214": {
        start: {
          line: 313,
          column: 71
        },
        end: {
          line: 313,
          column: 76
        }
      },
      "215": {
        start: {
          line: 314,
          column: 36
        },
        end: {
          line: 325,
          column: 39
        }
      },
      "216": {
        start: {
          line: 315,
          column: 40
        },
        end: {
          line: 324,
          column: 41
        }
      },
      "217": {
        start: {
          line: 316,
          column: 52
        },
        end: {
          line: 316,
          column: 116
        }
      },
      "218": {
        start: {
          line: 318,
          column: 48
        },
        end: {
          line: 318,
          column: 70
        }
      },
      "219": {
        start: {
          line: 319,
          column: 48
        },
        end: {
          line: 319,
          column: 95
        }
      },
      "220": {
        start: {
          line: 320,
          column: 48
        },
        end: {
          line: 320,
          column: 114
        }
      },
      "221": {
        start: {
          line: 322,
          column: 48
        },
        end: {
          line: 322,
          column: 64
        }
      },
      "222": {
        start: {
          line: 323,
          column: 48
        },
        end: {
          line: 323,
          column: 120
        }
      },
      "223": {
        start: {
          line: 328,
          column: 28
        },
        end: {
          line: 328,
          column: 56
        }
      },
      "224": {
        start: {
          line: 329,
          column: 28
        },
        end: {
          line: 329,
          column: 168
        }
      },
      "225": {
        start: {
          line: 329,
          column: 90
        },
        end: {
          line: 329,
          column: 119
        }
      },
      "226": {
        start: {
          line: 330,
          column: 28
        },
        end: {
          line: 332,
          column: 29
        }
      },
      "227": {
        start: {
          line: 331,
          column: 32
        },
        end: {
          line: 331,
          column: 83
        }
      },
      "228": {
        start: {
          line: 333,
          column: 28
        },
        end: {
          line: 345,
          column: 36
        }
      },
      "229": {
        start: {
          line: 347,
          column: 28
        },
        end: {
          line: 347,
          column: 55
        }
      },
      "230": {
        start: {
          line: 348,
          column: 28
        },
        end: {
          line: 359,
          column: 36
        }
      },
      "231": {
        start: {
          line: 354,
          column: 44
        },
        end: {
          line: 354,
          column: 68
        }
      },
      "232": {
        start: {
          line: 355,
          column: 44
        },
        end: {
          line: 355,
          column: 55
        }
      },
      "233": {
        start: {
          line: 365,
          column: 0
        },
        end: {
          line: 427,
          column: 7
        }
      },
      "234": {
        start: {
          line: 365,
          column: 96
        },
        end: {
          line: 427,
          column: 3
        }
      },
      "235": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 426,
          column: 7
        }
      },
      "236": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 425,
          column: 20
        }
      },
      "237": {
        start: {
          line: 367,
          column: 84
        },
        end: {
          line: 425,
          column: 15
        }
      },
      "238": {
        start: {
          line: 370,
          column: 16
        },
        end: {
          line: 424,
          column: 19
        }
      },
      "239": {
        start: {
          line: 371,
          column: 20
        },
        end: {
          line: 423,
          column: 21
        }
      },
      "240": {
        start: {
          line: 372,
          column: 32
        },
        end: {
          line: 372,
          column: 125
        }
      },
      "241": {
        start: {
          line: 374,
          column: 28
        },
        end: {
          line: 374,
          column: 56
        }
      },
      "242": {
        start: {
          line: 375,
          column: 28
        },
        end: {
          line: 380,
          column: 29
        }
      },
      "243": {
        start: {
          line: 376,
          column: 32
        },
        end: {
          line: 376,
          column: 103
        }
      },
      "244": {
        start: {
          line: 377,
          column: 32
        },
        end: {
          line: 377,
          column: 55
        }
      },
      "245": {
        start: {
          line: 378,
          column: 32
        },
        end: {
          line: 378,
          column: 72
        }
      },
      "246": {
        start: {
          line: 379,
          column: 32
        },
        end: {
          line: 379,
          column: 44
        }
      },
      "247": {
        start: {
          line: 381,
          column: 28
        },
        end: {
          line: 381,
          column: 104
        }
      },
      "248": {
        start: {
          line: 383,
          column: 28
        },
        end: {
          line: 383,
          column: 48
        }
      },
      "249": {
        start: {
          line: 384,
          column: 28
        },
        end: {
          line: 388,
          column: 29
        }
      },
      "250": {
        start: {
          line: 385,
          column: 32
        },
        end: {
          line: 385,
          column: 71
        }
      },
      "251": {
        start: {
          line: 386,
          column: 32
        },
        end: {
          line: 386,
          column: 55
        }
      },
      "252": {
        start: {
          line: 387,
          column: 32
        },
        end: {
          line: 387,
          column: 44
        }
      },
      "253": {
        start: {
          line: 389,
          column: 28
        },
        end: {
          line: 391,
          column: 36
        }
      },
      "254": {
        start: {
          line: 393,
          column: 28
        },
        end: {
          line: 393,
          column: 45
        }
      },
      "255": {
        start: {
          line: 394,
          column: 28
        },
        end: {
          line: 398,
          column: 29
        }
      },
      "256": {
        start: {
          line: 395,
          column: 32
        },
        end: {
          line: 395,
          column: 68
        }
      },
      "257": {
        start: {
          line: 396,
          column: 32
        },
        end: {
          line: 396,
          column: 55
        }
      },
      "258": {
        start: {
          line: 397,
          column: 32
        },
        end: {
          line: 397,
          column: 44
        }
      },
      "259": {
        start: {
          line: 400,
          column: 28
        },
        end: {
          line: 412,
          column: 36
        }
      },
      "260": {
        start: {
          line: 415,
          column: 28
        },
        end: {
          line: 415,
          column: 38
        }
      },
      "261": {
        start: {
          line: 416,
          column: 28
        },
        end: {
          line: 422,
          column: 36
        }
      },
      "262": {
        start: {
          line: 429,
          column: 0
        },
        end: {
          line: 443,
          column: 7
        }
      },
      "263": {
        start: {
          line: 429,
          column: 93
        },
        end: {
          line: 443,
          column: 3
        }
      },
      "264": {
        start: {
          line: 431,
          column: 4
        },
        end: {
          line: 442,
          column: 7
        }
      },
      "265": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 432,
          column: 49
        }
      },
      "266": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 433,
          column: 45
        }
      },
      "267": {
        start: {
          line: 434,
          column: 8
        },
        end: {
          line: 438,
          column: 9
        }
      },
      "268": {
        start: {
          line: 435,
          column: 12
        },
        end: {
          line: 435,
          column: 50
        }
      },
      "269": {
        start: {
          line: 436,
          column: 12
        },
        end: {
          line: 436,
          column: 37
        }
      },
      "270": {
        start: {
          line: 437,
          column: 12
        },
        end: {
          line: 437,
          column: 26
        }
      },
      "271": {
        start: {
          line: 439,
          column: 8
        },
        end: {
          line: 439,
          column: 86
        }
      },
      "272": {
        start: {
          line: 440,
          column: 8
        },
        end: {
          line: 440,
          column: 31
        }
      },
      "273": {
        start: {
          line: 441,
          column: 8
        },
        end: {
          line: 441,
          column: 20
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "validateFileContent",
        decl: {
          start: {
            line: 90,
            column: 9
          },
          end: {
            line: 90,
            column: 28
          }
        },
        loc: {
          start: {
            line: 90,
            column: 57
          },
          end: {
            line: 164,
            column: 1
          }
        },
        line: 90
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 91,
            column: 44
          },
          end: {
            line: 91,
            column: 45
          }
        },
        loc: {
          start: {
            line: 91,
            column: 56
          },
          end: {
            line: 163,
            column: 5
          }
        },
        line: 91
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 93,
            column: 33
          },
          end: {
            line: 93,
            column: 34
          }
        },
        loc: {
          start: {
            line: 93,
            column: 47
          },
          end: {
            line: 162,
            column: 9
          }
        },
        line: 93
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 99,
            column: 59
          },
          end: {
            line: 99,
            column: 60
          }
        },
        loc: {
          start: {
            line: 99,
            column: 80
          },
          end: {
            line: 101,
            column: 25
          }
        },
        line: 99
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 100,
            column: 51
          },
          end: {
            line: 100,
            column: 52
          }
        },
        loc: {
          start: {
            line: 100,
            column: 74
          },
          end: {
            line: 100,
            column: 108
          }
        },
        line: 100
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 107,
            column: 47
          },
          end: {
            line: 107,
            column: 48
          }
        },
        loc: {
          start: {
            line: 107,
            column: 66
          },
          end: {
            line: 111,
            column: 21
          }
        },
        line: 107
      },
      "20": {
        name: "processImage",
        decl: {
          start: {
            line: 165,
            column: 9
          },
          end: {
            line: 165,
            column: 21
          }
        },
        loc: {
          start: {
            line: 165,
            column: 36
          },
          end: {
            line: 187,
            column: 1
          }
        },
        line: 165
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 166,
            column: 44
          },
          end: {
            line: 166,
            column: 45
          }
        },
        loc: {
          start: {
            line: 166,
            column: 56
          },
          end: {
            line: 186,
            column: 5
          }
        },
        line: 166
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 168,
            column: 33
          },
          end: {
            line: 168,
            column: 34
          }
        },
        loc: {
          start: {
            line: 168,
            column: 47
          },
          end: {
            line: 185,
            column: 9
          }
        },
        line: 168
      },
      "23": {
        name: "generateFileName",
        decl: {
          start: {
            line: 188,
            column: 9
          },
          end: {
            line: 188,
            column: 25
          }
        },
        loc: {
          start: {
            line: 188,
            column: 40
          },
          end: {
            line: 192,
            column: 1
          }
        },
        line: 188
      },
      "24": {
        name: "uploadToStorage",
        decl: {
          start: {
            line: 194,
            column: 9
          },
          end: {
            line: 194,
            column: 24
          }
        },
        loc: {
          start: {
            line: 194,
            column: 43
          },
          end: {
            line: 231,
            column: 1
          }
        },
        line: 194
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 195,
            column: 44
          },
          end: {
            line: 195,
            column: 45
          }
        },
        loc: {
          start: {
            line: 195,
            column: 56
          },
          end: {
            line: 230,
            column: 5
          }
        },
        line: 195
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 197,
            column: 33
          },
          end: {
            line: 197,
            column: 34
          }
        },
        loc: {
          start: {
            line: 197,
            column: 47
          },
          end: {
            line: 229,
            column: 9
          }
        },
        line: 197
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 232,
            column: 73
          },
          end: {
            line: 232,
            column: 74
          }
        },
        loc: {
          start: {
            line: 232,
            column: 92
          },
          end: {
            line: 364,
            column: 5
          }
        },
        line: 232
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 232,
            column: 135
          },
          end: {
            line: 232,
            column: 136
          }
        },
        loc: {
          start: {
            line: 232,
            column: 147
          },
          end: {
            line: 364,
            column: 1
          }
        },
        line: 232
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 233,
            column: 29
          },
          end: {
            line: 233,
            column: 30
          }
        },
        loc: {
          start: {
            line: 233,
            column: 43
          },
          end: {
            line: 363,
            column: 5
          }
        },
        line: 233
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 234,
            column: 70
          },
          end: {
            line: 234,
            column: 71
          }
        },
        loc: {
          start: {
            line: 234,
            column: 82
          },
          end: {
            line: 362,
            column: 17
          }
        },
        line: 234
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 234,
            column: 125
          },
          end: {
            line: 234,
            column: 126
          }
        },
        loc: {
          start: {
            line: 234,
            column: 137
          },
          end: {
            line: 362,
            column: 13
          }
        },
        line: 234
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 237,
            column: 41
          },
          end: {
            line: 237,
            column: 42
          }
        },
        loc: {
          start: {
            line: 237,
            column: 55
          },
          end: {
            line: 361,
            column: 17
          }
        },
        line: 237
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 311,
            column: 94
          },
          end: {
            line: 311,
            column: 95
          }
        },
        loc: {
          start: {
            line: 311,
            column: 108
          },
          end: {
            line: 326,
            column: 37
          }
        },
        line: 311
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 311,
            column: 149
          },
          end: {
            line: 311,
            column: 150
          }
        },
        loc: {
          start: {
            line: 311,
            column: 163
          },
          end: {
            line: 326,
            column: 33
          }
        },
        line: 311
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 314,
            column: 61
          },
          end: {
            line: 314,
            column: 62
          }
        },
        loc: {
          start: {
            line: 314,
            column: 75
          },
          end: {
            line: 325,
            column: 37
          }
        },
        line: 314
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 329,
            column: 73
          },
          end: {
            line: 329,
            column: 74
          }
        },
        loc: {
          start: {
            line: 329,
            column: 88
          },
          end: {
            line: 329,
            column: 121
          }
        },
        line: 329
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 353,
            column: 70
          },
          end: {
            line: 353,
            column: 71
          }
        },
        loc: {
          start: {
            line: 353,
            column: 90
          },
          end: {
            line: 356,
            column: 41
          }
        },
        line: 353
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 365,
            column: 75
          },
          end: {
            line: 365,
            column: 76
          }
        },
        loc: {
          start: {
            line: 365,
            column: 94
          },
          end: {
            line: 427,
            column: 5
          }
        },
        line: 365
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 365,
            column: 137
          },
          end: {
            line: 365,
            column: 138
          }
        },
        loc: {
          start: {
            line: 365,
            column: 149
          },
          end: {
            line: 427,
            column: 1
          }
        },
        line: 365
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 366,
            column: 29
          },
          end: {
            line: 366,
            column: 30
          }
        },
        loc: {
          start: {
            line: 366,
            column: 43
          },
          end: {
            line: 426,
            column: 5
          }
        },
        line: 366
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 367,
            column: 70
          },
          end: {
            line: 367,
            column: 71
          }
        },
        loc: {
          start: {
            line: 367,
            column: 82
          },
          end: {
            line: 425,
            column: 17
          }
        },
        line: 367
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 367,
            column: 125
          },
          end: {
            line: 367,
            column: 126
          }
        },
        loc: {
          start: {
            line: 367,
            column: 137
          },
          end: {
            line: 425,
            column: 13
          }
        },
        line: 367
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 370,
            column: 41
          },
          end: {
            line: 370,
            column: 42
          }
        },
        loc: {
          start: {
            line: 370,
            column: 55
          },
          end: {
            line: 424,
            column: 17
          }
        },
        line: 370
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 429,
            column: 72
          },
          end: {
            line: 429,
            column: 73
          }
        },
        loc: {
          start: {
            line: 429,
            column: 91
          },
          end: {
            line: 443,
            column: 5
          }
        },
        line: 429
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 429,
            column: 134
          },
          end: {
            line: 429,
            column: 135
          }
        },
        loc: {
          start: {
            line: 429,
            column: 146
          },
          end: {
            line: 443,
            column: 1
          }
        },
        line: 429
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 431,
            column: 29
          },
          end: {
            line: 431,
            column: 30
          }
        },
        loc: {
          start: {
            line: 431,
            column: 43
          },
          end: {
            line: 442,
            column: 5
          }
        },
        line: 431
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 94,
            column: 12
          },
          end: {
            line: 161,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 126,
            column: 33
          }
        }, {
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 129,
            column: 82
          }
        }, {
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 148,
            column: 41
          }
        }, {
          start: {
            line: 149,
            column: 16
          },
          end: {
            line: 155,
            column: 27
          }
        }, {
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 159,
            column: 78
          }
        }, {
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 160,
            column: 46
          }
        }],
        line: 94
      },
      "36": {
        loc: {
          start: {
            line: 98,
            column: 20
          },
          end: {
            line: 105,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 20
          },
          end: {
            line: 105,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "37": {
        loc: {
          start: {
            line: 102,
            column: 24
          },
          end: {
            line: 104,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 24
          },
          end: {
            line: 104,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "38": {
        loc: {
          start: {
            line: 108,
            column: 24
          },
          end: {
            line: 110,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 24
          },
          end: {
            line: 110,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "39": {
        loc: {
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 115,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 115,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "40": {
        loc: {
          start: {
            line: 123,
            column: 20
          },
          end: {
            line: 125,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 20
          },
          end: {
            line: 125,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "41": {
        loc: {
          start: {
            line: 123,
            column: 24
          },
          end: {
            line: 123,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 24
          },
          end: {
            line: 123,
            column: 37
          }
        }, {
          start: {
            line: 123,
            column: 41
          },
          end: {
            line: 123,
            column: 74
          }
        }],
        line: 123
      },
      "42": {
        loc: {
          start: {
            line: 133,
            column: 20
          },
          end: {
            line: 141,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 20
          },
          end: {
            line: 141,
            column: 21
          }
        }, {
          start: {
            line: 136,
            column: 25
          },
          end: {
            line: 141,
            column: 21
          }
        }],
        line: 133
      },
      "43": {
        loc: {
          start: {
            line: 133,
            column: 24
          },
          end: {
            line: 133,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 24
          },
          end: {
            line: 133,
            column: 39
          }
        }, {
          start: {
            line: 133,
            column: 43
          },
          end: {
            line: 133,
            column: 59
          }
        }],
        line: 133
      },
      "44": {
        loc: {
          start: {
            line: 136,
            column: 25
          },
          end: {
            line: 141,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 25
          },
          end: {
            line: 141,
            column: 21
          }
        }, {
          start: {
            line: 139,
            column: 25
          },
          end: {
            line: 141,
            column: 21
          }
        }],
        line: 136
      },
      "45": {
        loc: {
          start: {
            line: 136,
            column: 29
          },
          end: {
            line: 136,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 136,
            column: 29
          },
          end: {
            line: 136,
            column: 50
          }
        }, {
          start: {
            line: 136,
            column: 54
          },
          end: {
            line: 136,
            column: 76
          }
        }],
        line: 136
      },
      "46": {
        loc: {
          start: {
            line: 139,
            column: 25
          },
          end: {
            line: 141,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 25
          },
          end: {
            line: 141,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "47": {
        loc: {
          start: {
            line: 139,
            column: 29
          },
          end: {
            line: 139,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 29
          },
          end: {
            line: 139,
            column: 48
          }
        }, {
          start: {
            line: 139,
            column: 52
          },
          end: {
            line: 139,
            column: 72
          }
        }],
        line: 139
      },
      "48": {
        loc: {
          start: {
            line: 143,
            column: 20
          },
          end: {
            line: 145,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 20
          },
          end: {
            line: 145,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "49": {
        loc: {
          start: {
            line: 143,
            column: 24
          },
          end: {
            line: 143,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 24
          },
          end: {
            line: 143,
            column: 37
          }
        }, {
          start: {
            line: 143,
            column: 41
          },
          end: {
            line: 143,
            column: 81
          }
        }],
        line: 143
      },
      "50": {
        loc: {
          start: {
            line: 154,
            column: 45
          },
          end: {
            line: 154,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 67
          },
          end: {
            line: 154,
            column: 82
          }
        }, {
          start: {
            line: 154,
            column: 85
          },
          end: {
            line: 154,
            column: 94
          }
        }],
        line: 154
      },
      "51": {
        loc: {
          start: {
            line: 169,
            column: 12
          },
          end: {
            line: 184,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 170,
            column: 16
          },
          end: {
            line: 176,
            column: 37
          }
        }, {
          start: {
            line: 177,
            column: 16
          },
          end: {
            line: 183,
            column: 27
          }
        }],
        line: 169
      },
      "52": {
        loc: {
          start: {
            line: 198,
            column: 12
          },
          end: {
            line: 228,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 199,
            column: 16
          },
          end: {
            line: 214,
            column: 28
          }
        }, {
          start: {
            line: 215,
            column: 16
          },
          end: {
            line: 217,
            column: 52
          }
        }, {
          start: {
            line: 218,
            column: 16
          },
          end: {
            line: 226,
            column: 102
          }
        }, {
          start: {
            line: 227,
            column: 16
          },
          end: {
            line: 227,
            column: 46
          }
        }],
        line: 198
      },
      "53": {
        loc: {
          start: {
            line: 202,
            column: 20
          },
          end: {
            line: 210,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 20
          },
          end: {
            line: 210,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "54": {
        loc: {
          start: {
            line: 208,
            column: 34
          },
          end: {
            line: 208,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 208,
            column: 34
          },
          end: {
            line: 208,
            column: 58
          }
        }, {
          start: {
            line: 208,
            column: 62
          },
          end: {
            line: 208,
            column: 85
          }
        }],
        line: 208
      },
      "55": {
        loc: {
          start: {
            line: 225,
            column: 30
          },
          end: {
            line: 225,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 225,
            column: 30
          },
          end: {
            line: 225,
            column: 54
          }
        }, {
          start: {
            line: 225,
            column: 58
          },
          end: {
            line: 225,
            column: 81
          }
        }],
        line: 225
      },
      "56": {
        loc: {
          start: {
            line: 238,
            column: 20
          },
          end: {
            line: 360,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 239,
            column: 24
          },
          end: {
            line: 239,
            column: 125
          }
        }, {
          start: {
            line: 240,
            column: 24
          },
          end: {
            line: 248,
            column: 104
          }
        }, {
          start: {
            line: 249,
            column: 24
          },
          end: {
            line: 258,
            column: 36
          }
        }, {
          start: {
            line: 259,
            column: 24
          },
          end: {
            line: 266,
            column: 69
          }
        }, {
          start: {
            line: 267,
            column: 24
          },
          end: {
            line: 293,
            column: 69
          }
        }, {
          start: {
            line: 294,
            column: 24
          },
          end: {
            line: 296,
            column: 100
          }
        }, {
          start: {
            line: 297,
            column: 24
          },
          end: {
            line: 326,
            column: 41
          }
        }, {
          start: {
            line: 327,
            column: 24
          },
          end: {
            line: 345,
            column: 36
          }
        }, {
          start: {
            line: 346,
            column: 24
          },
          end: {
            line: 359,
            column: 36
          }
        }],
        line: 238
      },
      "57": {
        loc: {
          start: {
            line: 242,
            column: 28
          },
          end: {
            line: 247,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 28
          },
          end: {
            line: 247,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "58": {
        loc: {
          start: {
            line: 251,
            column: 28
          },
          end: {
            line: 255,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 28
          },
          end: {
            line: 255,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "59": {
        loc: {
          start: {
            line: 251,
            column: 34
          },
          end: {
            line: 251,
            column: 149
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 251,
            column: 132
          },
          end: {
            line: 251,
            column: 138
          }
        }, {
          start: {
            line: 251,
            column: 141
          },
          end: {
            line: 251,
            column: 149
          }
        }],
        line: 251
      },
      "60": {
        loc: {
          start: {
            line: 251,
            column: 34
          },
          end: {
            line: 251,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 251,
            column: 34
          },
          end: {
            line: 251,
            column: 112
          }
        }, {
          start: {
            line: 251,
            column: 116
          },
          end: {
            line: 251,
            column: 129
          }
        }],
        line: 251
      },
      "61": {
        loc: {
          start: {
            line: 251,
            column: 40
          },
          end: {
            line: 251,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 251,
            column: 81
          },
          end: {
            line: 251,
            column: 87
          }
        }, {
          start: {
            line: 251,
            column: 90
          },
          end: {
            line: 251,
            column: 102
          }
        }],
        line: 251
      },
      "62": {
        loc: {
          start: {
            line: 251,
            column: 40
          },
          end: {
            line: 251,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 251,
            column: 40
          },
          end: {
            line: 251,
            column: 56
          }
        }, {
          start: {
            line: 251,
            column: 60
          },
          end: {
            line: 251,
            column: 78
          }
        }],
        line: 251
      },
      "63": {
        loc: {
          start: {
            line: 261,
            column: 28
          },
          end: {
            line: 265,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 261,
            column: 28
          },
          end: {
            line: 265,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 261
      },
      "64": {
        loc: {
          start: {
            line: 270,
            column: 28
          },
          end: {
            line: 274,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 28
          },
          end: {
            line: 274,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "65": {
        loc: {
          start: {
            line: 276,
            column: 28
          },
          end: {
            line: 280,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 28
          },
          end: {
            line: 280,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "66": {
        loc: {
          start: {
            line: 281,
            column: 28
          },
          end: {
            line: 285,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 28
          },
          end: {
            line: 285,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "67": {
        loc: {
          start: {
            line: 287,
            column: 28
          },
          end: {
            line: 291,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 28
          },
          end: {
            line: 291,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 287
      },
      "68": {
        loc: {
          start: {
            line: 287,
            column: 32
          },
          end: {
            line: 287,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 287,
            column: 32
          },
          end: {
            line: 287,
            column: 42
          }
        }, {
          start: {
            line: 287,
            column: 46
          },
          end: {
            line: 287,
            column: 68
          }
        }],
        line: 287
      },
      "69": {
        loc: {
          start: {
            line: 299,
            column: 28
          },
          end: {
            line: 309,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 28
          },
          end: {
            line: 309,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "70": {
        loc: {
          start: {
            line: 315,
            column: 40
          },
          end: {
            line: 324,
            column: 41
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 316,
            column: 44
          },
          end: {
            line: 316,
            column: 116
          }
        }, {
          start: {
            line: 317,
            column: 44
          },
          end: {
            line: 320,
            column: 114
          }
        }, {
          start: {
            line: 321,
            column: 44
          },
          end: {
            line: 323,
            column: 120
          }
        }],
        line: 315
      },
      "71": {
        loc: {
          start: {
            line: 329,
            column: 46
          },
          end: {
            line: 329,
            column: 167
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 329,
            column: 152
          },
          end: {
            line: 329,
            column: 158
          }
        }, {
          start: {
            line: 329,
            column: 161
          },
          end: {
            line: 329,
            column: 167
          }
        }],
        line: 329
      },
      "72": {
        loc: {
          start: {
            line: 329,
            column: 46
          },
          end: {
            line: 329,
            column: 149
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 329,
            column: 46
          },
          end: {
            line: 329,
            column: 132
          }
        }, {
          start: {
            line: 329,
            column: 136
          },
          end: {
            line: 329,
            column: 149
          }
        }],
        line: 329
      },
      "73": {
        loc: {
          start: {
            line: 330,
            column: 28
          },
          end: {
            line: 332,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 330,
            column: 28
          },
          end: {
            line: 332,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 330
      },
      "74": {
        loc: {
          start: {
            line: 371,
            column: 20
          },
          end: {
            line: 423,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 372,
            column: 24
          },
          end: {
            line: 372,
            column: 125
          }
        }, {
          start: {
            line: 373,
            column: 24
          },
          end: {
            line: 381,
            column: 104
          }
        }, {
          start: {
            line: 382,
            column: 24
          },
          end: {
            line: 391,
            column: 36
          }
        }, {
          start: {
            line: 392,
            column: 24
          },
          end: {
            line: 412,
            column: 36
          }
        }, {
          start: {
            line: 413,
            column: 24
          },
          end: {
            line: 422,
            column: 36
          }
        }],
        line: 371
      },
      "75": {
        loc: {
          start: {
            line: 375,
            column: 28
          },
          end: {
            line: 380,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 375,
            column: 28
          },
          end: {
            line: 380,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 375
      },
      "76": {
        loc: {
          start: {
            line: 384,
            column: 28
          },
          end: {
            line: 388,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 384,
            column: 28
          },
          end: {
            line: 388,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 384
      },
      "77": {
        loc: {
          start: {
            line: 384,
            column: 34
          },
          end: {
            line: 384,
            column: 149
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 384,
            column: 132
          },
          end: {
            line: 384,
            column: 138
          }
        }, {
          start: {
            line: 384,
            column: 141
          },
          end: {
            line: 384,
            column: 149
          }
        }],
        line: 384
      },
      "78": {
        loc: {
          start: {
            line: 384,
            column: 34
          },
          end: {
            line: 384,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 384,
            column: 34
          },
          end: {
            line: 384,
            column: 112
          }
        }, {
          start: {
            line: 384,
            column: 116
          },
          end: {
            line: 384,
            column: 129
          }
        }],
        line: 384
      },
      "79": {
        loc: {
          start: {
            line: 384,
            column: 40
          },
          end: {
            line: 384,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 384,
            column: 81
          },
          end: {
            line: 384,
            column: 87
          }
        }, {
          start: {
            line: 384,
            column: 90
          },
          end: {
            line: 384,
            column: 102
          }
        }],
        line: 384
      },
      "80": {
        loc: {
          start: {
            line: 384,
            column: 40
          },
          end: {
            line: 384,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 384,
            column: 40
          },
          end: {
            line: 384,
            column: 56
          }
        }, {
          start: {
            line: 384,
            column: 60
          },
          end: {
            line: 384,
            column: 78
          }
        }],
        line: 384
      },
      "81": {
        loc: {
          start: {
            line: 394,
            column: 28
          },
          end: {
            line: 398,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 394,
            column: 28
          },
          end: {
            line: 398,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 394
      },
      "82": {
        loc: {
          start: {
            line: 434,
            column: 8
          },
          end: {
            line: 438,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 434,
            column: 8
          },
          end: {
            line: 438,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 434
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0, 0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0, 0, 0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/photo/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,wDAAkC;AAClC,6EAAwF;AACxF,gDAA0B;AAC1B,kDAA4B;AAC5B,qCAAmC;AACnC,uCAAmC;AACnC,mCAAgD;AAChD,qEAAmE;AAEnE,mDAAmD;AACnD,IAAM,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,8BAA8B;AACrE,IAAM,aAAa,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;AAChE,IAAM,kBAAkB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9D,IAAM,YAAY,GAAG;IACnB,SAAS,EAAE,EAAE;IACb,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG;CACX,CAAC;AAEF,0DAA0D;AAC1D,IAAM,eAAe,GAAG;IACtB,YAAY,EAAE;QACZ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,OAAO;KAC5B;IACD,WAAW,EAAE;QACX,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,MAAM;KACzD;IACD,YAAY,EAAE;QACZ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,wBAAwB;KACnD;CACF,CAAC;AAEF,kDAAkD;AAClD,IAAM,kBAAkB,GAAG;IACzB,WAAW;IACX,SAAS;IACT,OAAO;IACP,UAAU;IACV,wBAAwB;IACxB,IAAI,EAAE,gBAAgB;IACtB,SAAS,EAAE,iBAAiB;IAC5B,qBAAqB;IACrB,YAAY,EAAE,MAAM;IACpB,MAAM,EAAE,MAAM;CACf,CAAC;AAEF,8CAA8C;AAC9C,SAAe,mBAAmB,CAAC,MAAc,EAAE,QAAgB,EAAE,QAAgB;mCAAG,OAAO;;;;;oBAKvF,MAAM,GAAa,EAAE,CAAC;oBAGtB,UAAU,GAAG,eAAe,CAAC,QAAwC,CAAC,CAAC;oBAC7E,IAAI,UAAU,EAAE,CAAC;wBACT,gBAAgB,GAAG,UAAU,CAAC,IAAI,CAAC,UAAA,SAAS;4BAChD,OAAO,SAAS,CAAC,KAAK,CAAC,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,EAAtB,CAAsB,CAAC,CAAC;wBAClE,CAAC,CAAC,CAAC;wBAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BACtB,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;wBAClE,CAAC;oBACH,CAAC;oBAGK,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAC/C,kBAAkB,CAAC,OAAO,CAAC,UAAA,OAAO;wBAChC,IAAI,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;4BAC/B,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;wBACpD,CAAC;oBACH,CAAC,CAAC,CAAC;oBAGG,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;oBAClF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;wBAChD,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBACxC,CAAC;oBAGK,iBAAiB,GAA6B;wBAClD,MAAM,EAAE,CAAC,YAAY,CAAC;wBACtB,OAAO,EAAE,CAAC,YAAY,CAAC;wBACvB,MAAM,EAAE,CAAC,WAAW,CAAC;wBACrB,OAAO,EAAE,CAAC,YAAY,CAAC;qBACxB,CAAC;oBAEI,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;oBACvD,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACvD,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;oBACzD,CAAC;;;;oBAIkB,qBAAM,IAAA,eAAK,EAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAA;;oBAAzC,QAAQ,GAAG,SAA8B;oBAE/C,wCAAwC;oBACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;wBACxC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;oBACnD,CAAC;yBAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;wBAC3D,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;oBAC5D,CAAC;yBAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,EAAE,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;wBACvD,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;oBACxD,CAAC;oBAED,wEAAwE;oBACxE,IAAI,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,aAAa;wBAC5E,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;oBAC9C,CAAC;oBAGuB,qBAAM,IAAA,eAAK,EAAC,MAAM,CAAC;6BACxC,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,yCAAyC;6BAC/D,QAAQ,EAAE,EAAA;;oBAFP,eAAe,GAAG,SAEX;oBAEb,sBAAO;4BACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;4BAC5B,MAAM,QAAA;4BACN,eAAe,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS;yBACnE,EAAC;;;oBAGF,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;oBAC5C,sBAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,QAAA,EAAE,EAAC;;;;;CAErC;AAoBD,SAAe,YAAY,CAAC,MAAc,EAAE,IAAY;mCAAG,OAAO;;;;wBAC9C,qBAAM,IAAA,eAAK,EAAC,MAAM,CAAC;yBAClC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE;wBAClB,GAAG,EAAE,OAAO;wBACZ,QAAQ,EAAE,QAAQ;qBACnB,CAAC;yBACD,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;yBACrB,QAAQ,EAAE,EAAA;;oBANP,SAAS,GAAG,SAML;oBAEb,sBAAO;4BACL,MAAM,EAAE,SAAS;4BACjB,IAAI,MAAA;4BACJ,MAAM,EAAE,MAAM;yBACf,EAAC;;;;CACH;AAED,SAAS,gBAAgB,CAAC,MAAc,EAAE,IAAY;IACpD,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,IAAM,IAAI,GAAG,gBAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,UAAG,MAAM,cAAI,SAAS,CAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrG,OAAO,kBAAW,MAAM,cAAI,IAAI,cAAI,IAAI,SAAM,CAAC;AACjD,CAAC;AAED,qCAAqC;AACrC,SAAe,eAAe,CAAC,MAAc,EAAE,QAAgB;mCAAG,OAAO;;;;;;oBAErE,8CAA8C;oBAC9C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;wBACvC,YAAG,CAAC,IAAI,CAAC,+DAA+D,EAAE;4BACxE,SAAS,EAAE,kBAAkB;4BAC7B,MAAM,EAAE,mBAAmB;4BAC3B,QAAQ,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;yBACxC,CAAC,CAAC;wBAEG,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB,CAAC;wBACpE,sBAAO,UAAG,OAAO,gCAAsB,QAAQ,CAAE,EAAC;oBACpD,CAAC;oBAGY,qBAAM,IAAA,UAAG,EAAC,yBAAkB,QAAQ,CAAE,EAAE,MAAM,EAAE;4BAC3D,MAAM,EAAE,QAAQ;4BAChB,WAAW,EAAE,YAAY,EAAE,mCAAmC;yBAC/D,CAAC,EAAA;;oBAHI,IAAI,GAAG,SAGX;oBAEF,sBAAO,IAAI,CAAC,GAAG,EAAC;;;oBAEhB,YAAG,CAAC,KAAK,CAAC,gCAAgC,EAAE,OAAc,EAAE;wBAC1D,SAAS,EAAE,kBAAkB;wBAC7B,MAAM,EAAE,mBAAmB;wBAC3B,QAAQ,EAAE,EAAE,QAAQ,UAAA,EAAE;qBACvB,CAAC,CAAC;oBAEG,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB,CAAC;oBACpE,sBAAO,UAAG,OAAO,gCAAsB,QAAQ,CAAE,EAAC;;;;;CAErD;AAEY,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACtE,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;;;;gCAET,qBAAM,4CAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAA;;4BAAtE,eAAe,GAAG,SAAoD;4BAE5E,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gCACvB,KAAK,GAAG,IAAI,KAAK,CAAC,wDAAwD,CAAQ,CAAC;gCACzF,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;gCACxC,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEe,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;gCACpB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;gCACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oCACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;iCACrC,CAAC,EAAA;;4BAFI,IAAI,GAAG,SAEX;4BAEF,IAAI,CAAC,IAAI,EAAE,CAAC;gCACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;gCACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEgB,qBAAM,OAAO,CAAC,QAAQ,EAAE,EAAA;;4BAAnC,QAAQ,GAAG,SAAwB;4BACnC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAS,CAAC;4BAE1C,IAAI,CAAC,IAAI,EAAE,CAAC;gCACJ,KAAK,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAQ,CAAC;gCACnD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,yCAAyC;4BACzC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gCACjC,KAAK,GAAG,IAAI,KAAK,CAAC,0DAA0D,CAAQ,CAAC;gCAC3F,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,IAAI,IAAI,CAAC,IAAI,GAAG,aAAa,EAAE,CAAC;gCACxB,KAAK,GAAG,IAAI,KAAK,CAAC,0CAAmC,aAAa,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,QAAK,CAAQ,CAAC;gCACtG,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,mCAAmC;4BACnC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;gCACnC,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;gCACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEc,KAAA,CAAA,KAAA,MAAM,CAAA,CAAC,IAAI,CAAA;4BAAC,qBAAM,IAAI,CAAC,WAAW,EAAE,EAAA;;4BAA7C,MAAM,GAAG,cAAY,SAAwB,EAAC;4BAGjC,qBAAM,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAA;;4BAApE,UAAU,GAAG,SAAuD;4BAE1E,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gCACxB,YAAG,CAAC,IAAI,CAAC,wCAAwC,EAAE;oCACjD,SAAS,EAAE,kBAAkB;oCAC7B,MAAM,EAAE,IAAI,CAAC,EAAE;oCACf,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE;iCAC7D,CAAC,CAAC;gCAEG,KAAK,GAAG,IAAI,KAAK,CAAC,mCAAmC,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAQ,CAAC;gCACnG,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,KAAK,CAAC,IAAI,GAAG,EAAE,cAAc,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC;gCACnD,MAAM,KAAK,CAAC;4BACd,CAAC;4BAGK,eAAe,GAAG,UAAU,CAAC,eAAgB,CAAC;4BAG5B,qBAAM,OAAO,CAAC,GAAG,CACvC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,iEAAO,EAAsB;;wCAArB,QAAQ,QAAA,EAAE,UAAU,QAAA;;;oDACzC,qBAAM,YAAY,CAAC,eAAe,EAAE,UAAU,CAAC,EAAA;;gDAA3D,SAAS,GAAG,SAA+C;gDAC3D,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gDACzC,qBAAM,eAAe,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAA;;gDAAvD,GAAG,GAAG,SAAiD;gDAC7D,sBAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,KAAA,EAAE,MAAM,EAAE,UAAU,EAAE,EAAC;;;qCACpD,CAAC,CACH,EAAA;;4BAPK,eAAe,GAAG,SAOvB;4BAGK,eAAe,GAAG,MAAA,eAAe,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,IAAI,KAAK,QAAQ,EAArB,CAAqB,CAAC,0CAAE,GAAG,CAAC;4BAEhF,IAAI,CAAC,eAAe,EAAE,CAAC;gCACrB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;4BACrD,CAAC;4BAGsB,qBAAM,gBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oCACjD,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;oCAC1B,MAAM,EAAE;wCACN,iBAAiB,EAAE,eAAe;wCAClC,iBAAiB,EAAE,IAAI,IAAI,EAAE;wCAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;qCACtB;oCACD,MAAM,EAAE;wCACN,MAAM,EAAE,IAAI,CAAC,EAAE;wCACf,iBAAiB,EAAE,eAAe;wCAClC,iBAAiB,EAAE,IAAI,IAAI,EAAE;qCAC9B;iCACF,CAAC,EAAA;;4BAZI,cAAc,GAAG,SAYrB;4BAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE;wCACJ,OAAO,EAAE,IAAI;wCACb,iBAAiB,EAAE,eAAe;wCAClC,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,GAAG;4CACrC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC;4CACxB,OAAO,GAAG,CAAC;wCACb,CAAC,EAAE,EAA4B,CAAC;wCAChC,OAAO,EAAE,oCAAoC;qCAC9C;iCACF,CAAC,EAAC;;;iBACJ,CAAC,EAAC;;KACJ,CAAC,CAAC;AAEU,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACxE,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;;;;gCAET,qBAAM,4CAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAA;;4BAAtE,eAAe,GAAG,SAAoD;4BAE5E,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gCACvB,KAAK,GAAG,IAAI,KAAK,CAAC,mDAAmD,CAAQ,CAAC;gCACpF,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;gCACxC,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEe,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;gCACpB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;gCACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oCACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;iCACrC,CAAC,EAAA;;4BAFI,IAAI,GAAG,SAEX;4BAEF,IAAI,CAAC,IAAI,EAAE,CAAC;gCACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;gCACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,2CAA2C;4BAC3C,qBAAM,gBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oCAC1B,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;oCAC1B,MAAM,EAAE;wCACN,iBAAiB,EAAE,IAAI;wCACvB,iBAAiB,EAAE,IAAI,IAAI,EAAE;wCAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;qCACtB;oCACD,MAAM,EAAE;wCACN,MAAM,EAAE,IAAI,CAAC,EAAE;wCACf,iBAAiB,EAAE,IAAI;wCACvB,iBAAiB,EAAE,IAAI,IAAI,EAAE;qCAC9B;iCACF,CAAC,EAAA;;4BAbF,2CAA2C;4BAC3C,SAYE,CAAC;4BAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE;wCACJ,OAAO,EAAE,IAAI;wCACb,OAAO,EAAE,oCAAoC;qCAC9C;iCACF,CAAC,EAAC;;;iBACJ,CAAC,EAAC;;KACJ,CAAC,CAAC;AAEH,qFAAqF;AACxE,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;;QAC7D,QAAQ,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,SAAzB,CAA0B;QACpC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAE3C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACR,UAAQ,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;YACjD,OAAK,CAAC,UAAU,GAAG,GAAG,CAAC;YACvB,MAAM,OAAK,CAAC;QACd,CAAC;QAIK,KAAK,GAAG,IAAI,KAAK,CAAC,0DAA0D,CAAQ,CAAC;QAC3F,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;QACvB,MAAM,KAAK,CAAC;;KACb,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/photo/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport sharp from 'sharp';\nimport crypto from 'crypto';\nimport { put } from '@vercel/blob';\nimport { log } from '@/lib/logger';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';\n\n// SECURITY FIX: Enhanced file upload configuration\nconst MAX_FILE_SIZE = 2 * 1024 * 1024; // Reduced to 2MB for security\nconst ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];\nconst ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp'];\nconst AVATAR_SIZES = {\n  thumbnail: 64,\n  small: 128,\n  medium: 256,\n  large: 512\n};\n\n// SECURITY FIX: File signature validation (magic numbers)\nconst FILE_SIGNATURES = {\n  'image/jpeg': [\n    [0xFF, 0xD8, 0xFF], // JPEG\n  ],\n  'image/png': [\n    [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A], // PNG\n  ],\n  'image/webp': [\n    [0x52, 0x49, 0x46, 0x46], // RIFF (WebP container)\n  ]\n};\n\n// SECURITY FIX: Malicious file patterns to detect\nconst MALICIOUS_PATTERNS = [\n  // PHP tags\n  /<\\?php/i,\n  /<\\?=/i,\n  /<script/i,\n  // Executable signatures\n  /MZ/, // PE executable\n  /\\x7fELF/, // ELF executable\n  // Archive signatures\n  /PK\\x03\\x04/, // ZIP\n  /Rar!/, // RAR\n];\n\n// SECURITY FIX: Comprehensive file validation\nasync function validateFileContent(buffer: Buffer, mimeType: string, fileName: string): Promise<{\n  isValid: boolean;\n  errors: string[];\n  sanitizedBuffer?: Buffer;\n}> {\n  const errors: string[] = [];\n\n  // 1. File signature validation\n  const signatures = FILE_SIGNATURES[mimeType as keyof typeof FILE_SIGNATURES];\n  if (signatures) {\n    const isValidSignature = signatures.some(signature => {\n      return signature.every((byte, index) => buffer[index] === byte);\n    });\n\n    if (!isValidSignature) {\n      errors.push('File signature does not match declared MIME type');\n    }\n  }\n\n  // 2. Check for malicious patterns\n  const bufferString = buffer.toString('binary');\n  MALICIOUS_PATTERNS.forEach(pattern => {\n    if (pattern.test(bufferString)) {\n      errors.push('Malicious content detected in file');\n    }\n  });\n\n  // 3. File extension validation\n  const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n  if (!ALLOWED_EXTENSIONS.includes(fileExtension)) {\n    errors.push('Invalid file extension');\n  }\n\n  // 4. MIME type vs extension consistency\n  const expectedMimeTypes: Record<string, string[]> = {\n    '.jpg': ['image/jpeg'],\n    '.jpeg': ['image/jpeg'],\n    '.png': ['image/png'],\n    '.webp': ['image/webp']\n  };\n\n  const expectedTypes = expectedMimeTypes[fileExtension];\n  if (expectedTypes && !expectedTypes.includes(mimeType)) {\n    errors.push('File extension does not match MIME type');\n  }\n\n  // 5. Additional security checks using Sharp (validates image structure)\n  try {\n    const metadata = await sharp(buffer).metadata();\n\n    // Check for reasonable image dimensions\n    if (!metadata.width || !metadata.height) {\n      errors.push('Invalid image: missing dimensions');\n    } else if (metadata.width > 4096 || metadata.height > 4096) {\n      errors.push('Image dimensions too large (max 4096x4096)');\n    } else if (metadata.width < 32 || metadata.height < 32) {\n      errors.push('Image dimensions too small (min 32x32)');\n    }\n\n    // Check for excessive metadata (potential for hiding malicious content)\n    if (metadata.exif && Buffer.byteLength(metadata.exif) > 65536) { // 64KB limit\n      errors.push('Excessive EXIF data detected');\n    }\n\n    // Sanitize the image by re-encoding it (removes any embedded content)\n    const sanitizedBuffer = await sharp(buffer)\n      .jpeg({ quality: 85 }) // Always convert to JPEG for consistency\n      .toBuffer();\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      sanitizedBuffer: errors.length === 0 ? sanitizedBuffer : undefined\n    };\n\n  } catch (error) {\n    errors.push('Invalid image file structure');\n    return { isValid: false, errors };\n  }\n}\n\ninterface ProcessedImage {\n  buffer: Buffer;\n  size: number;\n  format: string;\n}\n\ninterface PhotoUploadResponse {\n  success: true;\n  profilePictureUrl: string;\n  sizes: Record<string, string>;\n  message: string;\n}\n\ninterface PhotoDeleteResponse {\n  success: true;\n  message: string;\n}\n\nasync function processImage(buffer: Buffer, size: number): Promise<ProcessedImage> {\n  const processed = await sharp(buffer)\n    .resize(size, size, {\n      fit: 'cover',\n      position: 'center'\n    })\n    .jpeg({ quality: 85 })\n    .toBuffer();\n\n  return {\n    buffer: processed,\n    size,\n    format: 'jpeg'\n  };\n}\n\nfunction generateFileName(userId: string, size: string): string {\n  const timestamp = Date.now();\n  const hash = crypto.createHash('md5').update(`${userId}-${timestamp}`).digest('hex').substring(0, 8);\n  return `profile-${userId}-${size}-${hash}.jpg`;\n}\n\n// Upload file to Vercel Blob storage\nasync function uploadToStorage(buffer: Buffer, fileName: string): Promise<string> {\n  try {\n    // Check if BLOB_READ_WRITE_TOKEN is available\n    if (!process.env.BLOB_READ_WRITE_TOKEN) {\n      log.warn('BLOB_READ_WRITE_TOKEN not found, using local storage fallback', {\n        component: 'photo_upload_api',\n        action: 'upload_to_storage',\n        metadata: { fallback: 'local_storage' }\n      });\n      // Fallback to local storage for development\n      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n      return `${baseUrl}/api/profile/photo/${fileName}`;\n    }\n\n    // Upload to Vercel Blob\n    const blob = await put(`profile-photos/${fileName}`, buffer, {\n      access: 'public',\n      contentType: 'image/jpeg', // All images are converted to JPEG\n    });\n\n    return blob.url;\n  } catch (error) {\n    log.error('Error uploading to Vercel Blob', error as Error, {\n      component: 'photo_upload_api',\n      action: 'upload_to_storage',\n      metadata: { fileName }\n    });\n    // Fallback to local storage\n    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n    return `${baseUrl}/api/profile/photo/${fileName}`;\n  }\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    // SECURITY FIX: Apply strict rate limiting for file uploads\n    const rateLimitResult = await enhancedRateLimiters.write.checkLimit(request);\n\n    if (!rateLimitResult.allowed) {\n      const error = new Error('Too many file upload attempts. Please try again later.') as any;\n      error.statusCode = 429;\n      error.headers = rateLimitResult.headers;\n      throw error;\n    }\n\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user?.email) {\n      const error = new Error('Not authenticated') as any;\n      error.statusCode = 401;\n      throw error;\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { email: session.user.email },\n    });\n\n    if (!user) {\n      const error = new Error('User not found') as any;\n      error.statusCode = 404;\n      throw error;\n    }\n\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      const error = new Error('No file provided') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    // SECURITY FIX: Enhanced file validation\n    if (!ALLOWED_TYPES.includes(file.type)) {\n      const error = new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    if (file.size > MAX_FILE_SIZE) {\n      const error = new Error(`File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`) as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    // SECURITY FIX: Validate file name\n    if (!file.name || file.name.length > 255) {\n      const error = new Error('Invalid file name') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    const buffer = Buffer.from(await file.arrayBuffer());\n\n    // SECURITY FIX: Comprehensive file content validation\n    const validation = await validateFileContent(buffer, file.type, file.name);\n\n    if (!validation.isValid) {\n      log.warn('File upload security validation failed', {\n        component: 'photo_upload_api',\n        userId: user.id,\n        metadata: { fileName: file.name, errors: validation.errors }\n      });\n\n      const error = new Error('File failed security validation: ' + validation.errors.join(', ')) as any;\n      error.statusCode = 400;\n      error.data = { securityErrors: validation.errors };\n      throw error;\n    }\n\n    // Use the sanitized buffer from validation\n    const sanitizedBuffer = validation.sanitizedBuffer!;\n\n    // SECURITY FIX: Process sanitized image for different sizes\n    const processedImages = await Promise.all(\n      Object.entries(AVATAR_SIZES).map(async ([sizeName, sizePixels]) => {\n        const processed = await processImage(sanitizedBuffer, sizePixels);\n        const fileName = generateFileName(user.id, sizeName);\n        const url = await uploadToStorage(processed.buffer, fileName);\n        return { size: sizeName, url, pixels: sizePixels };\n      })\n    );\n\n    // Use the medium size as the primary profile picture URL\n    const primaryImageUrl = processedImages.find(img => img.size === 'medium')?.url;\n\n    if (!primaryImageUrl) {\n      throw new Error('Failed to process primary image');\n    }\n\n    // Update user profile with new image URL\n    const updatedProfile = await prisma.profile.upsert({\n      where: { userId: user.id },\n      update: {\n        profilePictureUrl: primaryImageUrl,\n        lastProfileUpdate: new Date(),\n        updatedAt: new Date(),\n      },\n      create: {\n        userId: user.id,\n        profilePictureUrl: primaryImageUrl,\n        lastProfileUpdate: new Date(),\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        success: true,\n        profilePictureUrl: primaryImageUrl,\n        sizes: processedImages.reduce((acc, img) => {\n          acc[img.size] = img.url;\n          return acc;\n        }, {} as Record<string, string>),\n        message: 'Profile photo updated successfully'\n      }\n    });\n  });\n});\n\nexport const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    // SECURITY FIX: Apply rate limiting to DELETE operations\n    const rateLimitResult = await enhancedRateLimiters.write.checkLimit(request);\n\n    if (!rateLimitResult.allowed) {\n      const error = new Error('Too many delete attempts. Please try again later.') as any;\n      error.statusCode = 429;\n      error.headers = rateLimitResult.headers;\n      throw error;\n    }\n\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user?.email) {\n      const error = new Error('Not authenticated') as any;\n      error.statusCode = 401;\n      throw error;\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { email: session.user.email },\n    });\n\n    if (!user) {\n      const error = new Error('User not found') as any;\n      error.statusCode = 404;\n      throw error;\n    }\n\n    // Remove profile picture URL from database\n    await prisma.profile.upsert({\n      where: { userId: user.id },\n      update: {\n        profilePictureUrl: null,\n        lastProfileUpdate: new Date(),\n        updatedAt: new Date(),\n      },\n      create: {\n        userId: user.id,\n        profilePictureUrl: null,\n        lastProfileUpdate: new Date(),\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        success: true,\n        message: 'Profile photo removed successfully'\n      }\n    });\n  });\n});\n\n// GET endpoint for serving local photos (fallback when Vercel Blob is not available)\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  const { pathname } = new URL(request.url);\n  const fileName = pathname.split('/').pop();\n\n  if (!fileName) {\n    const error = new Error('File not found') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  // In a real implementation, you would serve from local storage or redirect to CDN\n  // For now, return a placeholder response indicating the feature needs Vercel Blob\n  const error = new Error('Photo serving requires Vercel Blob storage configuration') as any;\n  error.statusCode = 501;\n  throw error;\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a4b832349cbc177ddb7e003007a10dc57037fc21"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2757l6cm5l = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2757l6cm5l();
var __awaiter =
/* istanbul ignore next */
(cov_2757l6cm5l().s[0]++,
/* istanbul ignore next */
(cov_2757l6cm5l().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2757l6cm5l().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2757l6cm5l().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[1]++;
    cov_2757l6cm5l().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[2]++;
      cov_2757l6cm5l().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2757l6cm5l().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_2757l6cm5l().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_2757l6cm5l().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[4]++;
      cov_2757l6cm5l().s[4]++;
      try {
        /* istanbul ignore next */
        cov_2757l6cm5l().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2757l6cm5l().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[5]++;
      cov_2757l6cm5l().s[7]++;
      try {
        /* istanbul ignore next */
        cov_2757l6cm5l().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2757l6cm5l().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[6]++;
      cov_2757l6cm5l().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2757l6cm5l().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2757l6cm5l().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2757l6cm5l().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2757l6cm5l().s[12]++,
/* istanbul ignore next */
(cov_2757l6cm5l().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_2757l6cm5l().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2757l6cm5l().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_2757l6cm5l().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2757l6cm5l().f[8]++;
        cov_2757l6cm5l().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2757l6cm5l().b[6][0]++;
          cov_2757l6cm5l().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2757l6cm5l().b[6][1]++;
        }
        cov_2757l6cm5l().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2757l6cm5l().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2757l6cm5l().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2757l6cm5l().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2757l6cm5l().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[9]++;
    cov_2757l6cm5l().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[10]++;
    cov_2757l6cm5l().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[11]++;
      cov_2757l6cm5l().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[12]++;
    cov_2757l6cm5l().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_2757l6cm5l().b[9][0]++;
      cov_2757l6cm5l().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2757l6cm5l().b[9][1]++;
    }
    cov_2757l6cm5l().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2757l6cm5l().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2757l6cm5l().s[25]++;
      try {
        /* istanbul ignore next */
        cov_2757l6cm5l().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[15][0]++,
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[16][1]++,
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2757l6cm5l().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2757l6cm5l().b[12][0]++;
          cov_2757l6cm5l().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2757l6cm5l().b[12][1]++;
        }
        cov_2757l6cm5l().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2757l6cm5l().b[18][0]++;
          cov_2757l6cm5l().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2757l6cm5l().b[18][1]++;
        }
        cov_2757l6cm5l().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2757l6cm5l().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2757l6cm5l().b[19][1]++;
            cov_2757l6cm5l().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_2757l6cm5l().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2757l6cm5l().b[19][2]++;
            cov_2757l6cm5l().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_2757l6cm5l().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2757l6cm5l().b[19][3]++;
            cov_2757l6cm5l().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_2757l6cm5l().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2757l6cm5l().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_2757l6cm5l().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2757l6cm5l().b[19][4]++;
            cov_2757l6cm5l().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2757l6cm5l().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2757l6cm5l().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2757l6cm5l().b[19][5]++;
            cov_2757l6cm5l().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[20][0]++;
              cov_2757l6cm5l().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2757l6cm5l().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2757l6cm5l().b[20][1]++;
            }
            cov_2757l6cm5l().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[23][0]++;
              cov_2757l6cm5l().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2757l6cm5l().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2757l6cm5l().b[23][1]++;
            }
            cov_2757l6cm5l().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[25][0]++;
              cov_2757l6cm5l().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2757l6cm5l().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_2757l6cm5l().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2757l6cm5l().b[25][1]++;
            }
            cov_2757l6cm5l().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[27][0]++;
              cov_2757l6cm5l().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2757l6cm5l().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2757l6cm5l().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2757l6cm5l().b[27][1]++;
            }
            cov_2757l6cm5l().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[29][0]++;
              cov_2757l6cm5l().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2757l6cm5l().b[29][1]++;
            }
            cov_2757l6cm5l().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2757l6cm5l().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2757l6cm5l().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2757l6cm5l().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2757l6cm5l().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2757l6cm5l().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2757l6cm5l().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2757l6cm5l().b[30][0]++;
      cov_2757l6cm5l().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2757l6cm5l().b[30][1]++;
    }
    cov_2757l6cm5l().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2757l6cm5l().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2757l6cm5l().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_2757l6cm5l().s[67]++,
/* istanbul ignore next */
(cov_2757l6cm5l().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_2757l6cm5l().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2757l6cm5l().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[13]++;
  cov_2757l6cm5l().s[68]++;
  return /* istanbul ignore next */(cov_2757l6cm5l().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2757l6cm5l().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2757l6cm5l().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_2757l6cm5l().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2757l6cm5l().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2757l6cm5l().s[70]++;
exports.GET = exports.DELETE = exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[71]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[72]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[73]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[74]++, __importDefault(require("@/lib/prisma")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[75]++, require("@/lib/unified-api-error-handler"));
var sharp_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[76]++, __importDefault(require("sharp")));
var crypto_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[77]++, __importDefault(require("crypto")));
var blob_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[78]++, require("@vercel/blob"));
var logger_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[79]++, require("@/lib/logger"));
var csrf_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[80]++, require("@/lib/csrf"));
var enhanced_rate_limiter_1 =
/* istanbul ignore next */
(cov_2757l6cm5l().s[81]++, require("@/lib/enhanced-rate-limiter"));
// SECURITY FIX: Enhanced file upload configuration
var MAX_FILE_SIZE =
/* istanbul ignore next */
(cov_2757l6cm5l().s[82]++, 2 * 1024 * 1024); // Reduced to 2MB for security
var ALLOWED_TYPES =
/* istanbul ignore next */
(cov_2757l6cm5l().s[83]++, ['image/jpeg', 'image/png', 'image/webp']);
var ALLOWED_EXTENSIONS =
/* istanbul ignore next */
(cov_2757l6cm5l().s[84]++, ['.jpg', '.jpeg', '.png', '.webp']);
var AVATAR_SIZES =
/* istanbul ignore next */
(cov_2757l6cm5l().s[85]++, {
  thumbnail: 64,
  small: 128,
  medium: 256,
  large: 512
});
// SECURITY FIX: File signature validation (magic numbers)
var FILE_SIGNATURES =
/* istanbul ignore next */
(cov_2757l6cm5l().s[86]++, {
  'image/jpeg': [[0xFF, 0xD8, 0xFF] // JPEG
  ],
  'image/png': [[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A] // PNG
  ],
  'image/webp': [[0x52, 0x49, 0x46, 0x46] // RIFF (WebP container)
  ]
});
// SECURITY FIX: Malicious file patterns to detect
var MALICIOUS_PATTERNS =
/* istanbul ignore next */
(cov_2757l6cm5l().s[87]++, [
// PHP tags
/<\?php/i, /<\?=/i, /<script/i,
// Executable signatures
/MZ/,
// PE executable
/\x7fELF/,
// ELF executable
// Archive signatures
/PK\x03\x04/,
// ZIP
/Rar!/ // RAR
]);
// SECURITY FIX: Comprehensive file validation
function validateFileContent(buffer, mimeType, fileName) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[14]++;
  cov_2757l6cm5l().s[88]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[15]++;
    var errors, signatures, isValidSignature, bufferString, fileExtension, expectedMimeTypes, expectedTypes, metadata, sanitizedBuffer, error_1;
    /* istanbul ignore next */
    cov_2757l6cm5l().s[89]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[16]++;
      cov_2757l6cm5l().s[90]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[35][0]++;
          cov_2757l6cm5l().s[91]++;
          errors = [];
          /* istanbul ignore next */
          cov_2757l6cm5l().s[92]++;
          signatures = FILE_SIGNATURES[mimeType];
          /* istanbul ignore next */
          cov_2757l6cm5l().s[93]++;
          if (signatures) {
            /* istanbul ignore next */
            cov_2757l6cm5l().b[36][0]++;
            cov_2757l6cm5l().s[94]++;
            isValidSignature = signatures.some(function (signature) {
              /* istanbul ignore next */
              cov_2757l6cm5l().f[17]++;
              cov_2757l6cm5l().s[95]++;
              return signature.every(function (byte, index) {
                /* istanbul ignore next */
                cov_2757l6cm5l().f[18]++;
                cov_2757l6cm5l().s[96]++;
                return buffer[index] === byte;
              });
            });
            /* istanbul ignore next */
            cov_2757l6cm5l().s[97]++;
            if (!isValidSignature) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[37][0]++;
              cov_2757l6cm5l().s[98]++;
              errors.push('File signature does not match declared MIME type');
            } else
            /* istanbul ignore next */
            {
              cov_2757l6cm5l().b[37][1]++;
            }
          } else
          /* istanbul ignore next */
          {
            cov_2757l6cm5l().b[36][1]++;
          }
          cov_2757l6cm5l().s[99]++;
          bufferString = buffer.toString('binary');
          /* istanbul ignore next */
          cov_2757l6cm5l().s[100]++;
          MALICIOUS_PATTERNS.forEach(function (pattern) {
            /* istanbul ignore next */
            cov_2757l6cm5l().f[19]++;
            cov_2757l6cm5l().s[101]++;
            if (pattern.test(bufferString)) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[38][0]++;
              cov_2757l6cm5l().s[102]++;
              errors.push('Malicious content detected in file');
            } else
            /* istanbul ignore next */
            {
              cov_2757l6cm5l().b[38][1]++;
            }
          });
          /* istanbul ignore next */
          cov_2757l6cm5l().s[103]++;
          fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
          /* istanbul ignore next */
          cov_2757l6cm5l().s[104]++;
          if (!ALLOWED_EXTENSIONS.includes(fileExtension)) {
            /* istanbul ignore next */
            cov_2757l6cm5l().b[39][0]++;
            cov_2757l6cm5l().s[105]++;
            errors.push('Invalid file extension');
          } else
          /* istanbul ignore next */
          {
            cov_2757l6cm5l().b[39][1]++;
          }
          cov_2757l6cm5l().s[106]++;
          expectedMimeTypes = {
            '.jpg': ['image/jpeg'],
            '.jpeg': ['image/jpeg'],
            '.png': ['image/png'],
            '.webp': ['image/webp']
          };
          /* istanbul ignore next */
          cov_2757l6cm5l().s[107]++;
          expectedTypes = expectedMimeTypes[fileExtension];
          /* istanbul ignore next */
          cov_2757l6cm5l().s[108]++;
          if (
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[41][0]++, expectedTypes) &&
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[41][1]++, !expectedTypes.includes(mimeType))) {
            /* istanbul ignore next */
            cov_2757l6cm5l().b[40][0]++;
            cov_2757l6cm5l().s[109]++;
            errors.push('File extension does not match MIME type');
          } else
          /* istanbul ignore next */
          {
            cov_2757l6cm5l().b[40][1]++;
          }
          cov_2757l6cm5l().s[110]++;
          _a.label = 1;
        case 1:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[35][1]++;
          cov_2757l6cm5l().s[111]++;
          _a.trys.push([1, 4,, 5]);
          /* istanbul ignore next */
          cov_2757l6cm5l().s[112]++;
          return [4 /*yield*/, (0, sharp_1.default)(buffer).metadata()];
        case 2:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[35][2]++;
          cov_2757l6cm5l().s[113]++;
          metadata = _a.sent();
          // Check for reasonable image dimensions
          /* istanbul ignore next */
          cov_2757l6cm5l().s[114]++;
          if (
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[43][0]++, !metadata.width) ||
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[43][1]++, !metadata.height)) {
            /* istanbul ignore next */
            cov_2757l6cm5l().b[42][0]++;
            cov_2757l6cm5l().s[115]++;
            errors.push('Invalid image: missing dimensions');
          } else {
            /* istanbul ignore next */
            cov_2757l6cm5l().b[42][1]++;
            cov_2757l6cm5l().s[116]++;
            if (
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[45][0]++, metadata.width > 4096) ||
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[45][1]++, metadata.height > 4096)) {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[44][0]++;
              cov_2757l6cm5l().s[117]++;
              errors.push('Image dimensions too large (max 4096x4096)');
            } else {
              /* istanbul ignore next */
              cov_2757l6cm5l().b[44][1]++;
              cov_2757l6cm5l().s[118]++;
              if (
              /* istanbul ignore next */
              (cov_2757l6cm5l().b[47][0]++, metadata.width < 32) ||
              /* istanbul ignore next */
              (cov_2757l6cm5l().b[47][1]++, metadata.height < 32)) {
                /* istanbul ignore next */
                cov_2757l6cm5l().b[46][0]++;
                cov_2757l6cm5l().s[119]++;
                errors.push('Image dimensions too small (min 32x32)');
              } else
              /* istanbul ignore next */
              {
                cov_2757l6cm5l().b[46][1]++;
              }
            }
          }
          // Check for excessive metadata (potential for hiding malicious content)
          /* istanbul ignore next */
          cov_2757l6cm5l().s[120]++;
          if (
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[49][0]++, metadata.exif) &&
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[49][1]++, Buffer.byteLength(metadata.exif) > 65536)) {
            /* istanbul ignore next */
            cov_2757l6cm5l().b[48][0]++;
            cov_2757l6cm5l().s[121]++;
            // 64KB limit
            errors.push('Excessive EXIF data detected');
          } else
          /* istanbul ignore next */
          {
            cov_2757l6cm5l().b[48][1]++;
          }
          cov_2757l6cm5l().s[122]++;
          return [4 /*yield*/, (0, sharp_1.default)(buffer).jpeg({
            quality: 85
          }) // Always convert to JPEG for consistency
          .toBuffer()];
        case 3:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[35][3]++;
          cov_2757l6cm5l().s[123]++;
          sanitizedBuffer = _a.sent();
          /* istanbul ignore next */
          cov_2757l6cm5l().s[124]++;
          return [2 /*return*/, {
            isValid: errors.length === 0,
            errors: errors,
            sanitizedBuffer: errors.length === 0 ?
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[50][0]++, sanitizedBuffer) :
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[50][1]++, undefined)
          }];
        case 4:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[35][4]++;
          cov_2757l6cm5l().s[125]++;
          error_1 = _a.sent();
          /* istanbul ignore next */
          cov_2757l6cm5l().s[126]++;
          errors.push('Invalid image file structure');
          /* istanbul ignore next */
          cov_2757l6cm5l().s[127]++;
          return [2 /*return*/, {
            isValid: false,
            errors: errors
          }];
        case 5:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[35][5]++;
          cov_2757l6cm5l().s[128]++;
          return [2 /*return*/];
      }
    });
  });
}
function processImage(buffer, size) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[20]++;
  cov_2757l6cm5l().s[129]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[21]++;
    var processed;
    /* istanbul ignore next */
    cov_2757l6cm5l().s[130]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[22]++;
      cov_2757l6cm5l().s[131]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[51][0]++;
          cov_2757l6cm5l().s[132]++;
          return [4 /*yield*/, (0, sharp_1.default)(buffer).resize(size, size, {
            fit: 'cover',
            position: 'center'
          }).jpeg({
            quality: 85
          }).toBuffer()];
        case 1:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[51][1]++;
          cov_2757l6cm5l().s[133]++;
          processed = _a.sent();
          /* istanbul ignore next */
          cov_2757l6cm5l().s[134]++;
          return [2 /*return*/, {
            buffer: processed,
            size: size,
            format: 'jpeg'
          }];
      }
    });
  });
}
function generateFileName(userId, size) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[23]++;
  var timestamp =
  /* istanbul ignore next */
  (cov_2757l6cm5l().s[135]++, Date.now());
  var hash =
  /* istanbul ignore next */
  (cov_2757l6cm5l().s[136]++, crypto_1.default.createHash('md5').update("".concat(userId, "-").concat(timestamp)).digest('hex').substring(0, 8));
  /* istanbul ignore next */
  cov_2757l6cm5l().s[137]++;
  return "profile-".concat(userId, "-").concat(size, "-").concat(hash, ".jpg");
}
// Upload file to Vercel Blob storage
function uploadToStorage(buffer, fileName) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[24]++;
  cov_2757l6cm5l().s[138]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[25]++;
    var baseUrl, blob, error_2, baseUrl;
    /* istanbul ignore next */
    cov_2757l6cm5l().s[139]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[26]++;
      cov_2757l6cm5l().s[140]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[52][0]++;
          cov_2757l6cm5l().s[141]++;
          _a.trys.push([0, 2,, 3]);
          // Check if BLOB_READ_WRITE_TOKEN is available
          /* istanbul ignore next */
          cov_2757l6cm5l().s[142]++;
          if (!process.env.BLOB_READ_WRITE_TOKEN) {
            /* istanbul ignore next */
            cov_2757l6cm5l().b[53][0]++;
            cov_2757l6cm5l().s[143]++;
            logger_1.log.warn('BLOB_READ_WRITE_TOKEN not found, using local storage fallback', {
              component: 'photo_upload_api',
              action: 'upload_to_storage',
              metadata: {
                fallback: 'local_storage'
              }
            });
            /* istanbul ignore next */
            cov_2757l6cm5l().s[144]++;
            baseUrl =
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[54][0]++, process.env.NEXTAUTH_URL) ||
            /* istanbul ignore next */
            (cov_2757l6cm5l().b[54][1]++, 'http://localhost:3000');
            /* istanbul ignore next */
            cov_2757l6cm5l().s[145]++;
            return [2 /*return*/, "".concat(baseUrl, "/api/profile/photo/").concat(fileName)];
          } else
          /* istanbul ignore next */
          {
            cov_2757l6cm5l().b[53][1]++;
          }
          cov_2757l6cm5l().s[146]++;
          return [4 /*yield*/, (0, blob_1.put)("profile-photos/".concat(fileName), buffer, {
            access: 'public',
            contentType: 'image/jpeg' // All images are converted to JPEG
          })];
        case 1:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[52][1]++;
          cov_2757l6cm5l().s[147]++;
          blob = _a.sent();
          /* istanbul ignore next */
          cov_2757l6cm5l().s[148]++;
          return [2 /*return*/, blob.url];
        case 2:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[52][2]++;
          cov_2757l6cm5l().s[149]++;
          error_2 = _a.sent();
          /* istanbul ignore next */
          cov_2757l6cm5l().s[150]++;
          logger_1.log.error('Error uploading to Vercel Blob', error_2, {
            component: 'photo_upload_api',
            action: 'upload_to_storage',
            metadata: {
              fileName: fileName
            }
          });
          /* istanbul ignore next */
          cov_2757l6cm5l().s[151]++;
          baseUrl =
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[55][0]++, process.env.NEXTAUTH_URL) ||
          /* istanbul ignore next */
          (cov_2757l6cm5l().b[55][1]++, 'http://localhost:3000');
          /* istanbul ignore next */
          cov_2757l6cm5l().s[152]++;
          return [2 /*return*/, "".concat(baseUrl, "/api/profile/photo/").concat(fileName)];
        case 3:
          /* istanbul ignore next */
          cov_2757l6cm5l().b[52][3]++;
          cov_2757l6cm5l().s[153]++;
          return [2 /*return*/];
      }
    });
  });
}
/* istanbul ignore next */
cov_2757l6cm5l().s[154]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[27]++;
  cov_2757l6cm5l().s[155]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[28]++;
    cov_2757l6cm5l().s[156]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[29]++;
      cov_2757l6cm5l().s[157]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_2757l6cm5l().f[30]++;
        cov_2757l6cm5l().s[158]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2757l6cm5l().f[31]++;
          var rateLimitResult, error, session, error, user, error, formData, file, error, error, error, error, buffer, _a, _b, validation, error, sanitizedBuffer, processedImages, primaryImageUrl, updatedProfile;
          var _c, _d;
          /* istanbul ignore next */
          cov_2757l6cm5l().s[159]++;
          return __generator(this, function (_e) {
            /* istanbul ignore next */
            cov_2757l6cm5l().f[32]++;
            cov_2757l6cm5l().s[160]++;
            switch (_e.label) {
              case 0:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][0]++;
                cov_2757l6cm5l().s[161]++;
                return [4 /*yield*/, enhanced_rate_limiter_1.enhancedRateLimiters.write.checkLimit(request)];
              case 1:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][1]++;
                cov_2757l6cm5l().s[162]++;
                rateLimitResult = _e.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[163]++;
                if (!rateLimitResult.allowed) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[57][0]++;
                  cov_2757l6cm5l().s[164]++;
                  error = new Error('Too many file upload attempts. Please try again later.');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[165]++;
                  error.statusCode = 429;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[166]++;
                  error.headers = rateLimitResult.headers;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[167]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[57][1]++;
                }
                cov_2757l6cm5l().s[168]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 2:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][2]++;
                cov_2757l6cm5l().s[169]++;
                session = _e.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[170]++;
                if (!(
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[60][0]++, (_c =
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[62][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[62][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[61][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[61][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[60][1]++, _c === void 0) ?
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[59][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[59][1]++, _c.email))) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[58][0]++;
                  cov_2757l6cm5l().s[171]++;
                  error = new Error('Not authenticated');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[172]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[173]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[58][1]++;
                }
                cov_2757l6cm5l().s[174]++;
                return [4 /*yield*/, prisma_1.default.user.findUnique({
                  where: {
                    email: session.user.email
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][3]++;
                cov_2757l6cm5l().s[175]++;
                user = _e.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[176]++;
                if (!user) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[63][0]++;
                  cov_2757l6cm5l().s[177]++;
                  error = new Error('User not found');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[178]++;
                  error.statusCode = 404;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[179]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[63][1]++;
                }
                cov_2757l6cm5l().s[180]++;
                return [4 /*yield*/, request.formData()];
              case 4:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][4]++;
                cov_2757l6cm5l().s[181]++;
                formData = _e.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[182]++;
                file = formData.get('file');
                /* istanbul ignore next */
                cov_2757l6cm5l().s[183]++;
                if (!file) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[64][0]++;
                  cov_2757l6cm5l().s[184]++;
                  error = new Error('No file provided');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[185]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[186]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[64][1]++;
                }
                // SECURITY FIX: Enhanced file validation
                cov_2757l6cm5l().s[187]++;
                if (!ALLOWED_TYPES.includes(file.type)) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[65][0]++;
                  cov_2757l6cm5l().s[188]++;
                  error = new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[189]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[190]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[65][1]++;
                }
                cov_2757l6cm5l().s[191]++;
                if (file.size > MAX_FILE_SIZE) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[66][0]++;
                  cov_2757l6cm5l().s[192]++;
                  error = new Error("File too large. Maximum size is ".concat(MAX_FILE_SIZE / (1024 * 1024), "MB."));
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[193]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[194]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[66][1]++;
                }
                // SECURITY FIX: Validate file name
                cov_2757l6cm5l().s[195]++;
                if (
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[68][0]++, !file.name) ||
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[68][1]++, file.name.length > 255)) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[67][0]++;
                  cov_2757l6cm5l().s[196]++;
                  error = new Error('Invalid file name');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[197]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[198]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[67][1]++;
                }
                cov_2757l6cm5l().s[199]++;
                _b = (_a = Buffer).from;
                /* istanbul ignore next */
                cov_2757l6cm5l().s[200]++;
                return [4 /*yield*/, file.arrayBuffer()];
              case 5:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][5]++;
                cov_2757l6cm5l().s[201]++;
                buffer = _b.apply(_a, [_e.sent()]);
                /* istanbul ignore next */
                cov_2757l6cm5l().s[202]++;
                return [4 /*yield*/, validateFileContent(buffer, file.type, file.name)];
              case 6:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][6]++;
                cov_2757l6cm5l().s[203]++;
                validation = _e.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[204]++;
                if (!validation.isValid) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[69][0]++;
                  cov_2757l6cm5l().s[205]++;
                  logger_1.log.warn('File upload security validation failed', {
                    component: 'photo_upload_api',
                    userId: user.id,
                    metadata: {
                      fileName: file.name,
                      errors: validation.errors
                    }
                  });
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[206]++;
                  error = new Error('File failed security validation: ' + validation.errors.join(', '));
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[207]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[208]++;
                  error.data = {
                    securityErrors: validation.errors
                  };
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[209]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[69][1]++;
                }
                cov_2757l6cm5l().s[210]++;
                sanitizedBuffer = validation.sanitizedBuffer;
                /* istanbul ignore next */
                cov_2757l6cm5l().s[211]++;
                return [4 /*yield*/, Promise.all(Object.entries(AVATAR_SIZES).map(function (_a) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().f[33]++;
                  cov_2757l6cm5l().s[212]++;
                  return __awaiter(void 0, [_a], void 0, function (_b) {
                    /* istanbul ignore next */
                    cov_2757l6cm5l().f[34]++;
                    var processed, fileName, url;
                    var sizeName =
                      /* istanbul ignore next */
                      (cov_2757l6cm5l().s[213]++, _b[0]),
                      sizePixels =
                      /* istanbul ignore next */
                      (cov_2757l6cm5l().s[214]++, _b[1]);
                    /* istanbul ignore next */
                    cov_2757l6cm5l().s[215]++;
                    return __generator(this, function (_c) {
                      /* istanbul ignore next */
                      cov_2757l6cm5l().f[35]++;
                      cov_2757l6cm5l().s[216]++;
                      switch (_c.label) {
                        case 0:
                          /* istanbul ignore next */
                          cov_2757l6cm5l().b[70][0]++;
                          cov_2757l6cm5l().s[217]++;
                          return [4 /*yield*/, processImage(sanitizedBuffer, sizePixels)];
                        case 1:
                          /* istanbul ignore next */
                          cov_2757l6cm5l().b[70][1]++;
                          cov_2757l6cm5l().s[218]++;
                          processed = _c.sent();
                          /* istanbul ignore next */
                          cov_2757l6cm5l().s[219]++;
                          fileName = generateFileName(user.id, sizeName);
                          /* istanbul ignore next */
                          cov_2757l6cm5l().s[220]++;
                          return [4 /*yield*/, uploadToStorage(processed.buffer, fileName)];
                        case 2:
                          /* istanbul ignore next */
                          cov_2757l6cm5l().b[70][2]++;
                          cov_2757l6cm5l().s[221]++;
                          url = _c.sent();
                          /* istanbul ignore next */
                          cov_2757l6cm5l().s[222]++;
                          return [2 /*return*/, {
                            size: sizeName,
                            url: url,
                            pixels: sizePixels
                          }];
                      }
                    });
                  });
                }))];
              case 7:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][7]++;
                cov_2757l6cm5l().s[223]++;
                processedImages = _e.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[224]++;
                primaryImageUrl =
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[72][0]++, (_d = processedImages.find(function (img) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().f[36]++;
                  cov_2757l6cm5l().s[225]++;
                  return img.size === 'medium';
                })) === null) ||
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[72][1]++, _d === void 0) ?
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[71][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[71][1]++, _d.url);
                /* istanbul ignore next */
                cov_2757l6cm5l().s[226]++;
                if (!primaryImageUrl) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[73][0]++;
                  cov_2757l6cm5l().s[227]++;
                  throw new Error('Failed to process primary image');
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[73][1]++;
                }
                cov_2757l6cm5l().s[228]++;
                return [4 /*yield*/, prisma_1.default.profile.upsert({
                  where: {
                    userId: user.id
                  },
                  update: {
                    profilePictureUrl: primaryImageUrl,
                    lastProfileUpdate: new Date(),
                    updatedAt: new Date()
                  },
                  create: {
                    userId: user.id,
                    profilePictureUrl: primaryImageUrl,
                    lastProfileUpdate: new Date()
                  }
                })];
              case 8:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[56][8]++;
                cov_2757l6cm5l().s[229]++;
                updatedProfile = _e.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[230]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: {
                    success: true,
                    profilePictureUrl: primaryImageUrl,
                    sizes: processedImages.reduce(function (acc, img) {
                      /* istanbul ignore next */
                      cov_2757l6cm5l().f[37]++;
                      cov_2757l6cm5l().s[231]++;
                      acc[img.size] = img.url;
                      /* istanbul ignore next */
                      cov_2757l6cm5l().s[232]++;
                      return acc;
                    }, {}),
                    message: 'Profile photo updated successfully'
                  }
                })];
            }
          });
        });
      })];
    });
  });
});
/* istanbul ignore next */
cov_2757l6cm5l().s[233]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[38]++;
  cov_2757l6cm5l().s[234]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[39]++;
    cov_2757l6cm5l().s[235]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[40]++;
      cov_2757l6cm5l().s[236]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_2757l6cm5l().f[41]++;
        cov_2757l6cm5l().s[237]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2757l6cm5l().f[42]++;
          var rateLimitResult, error, session, error, user, error;
          var _a;
          /* istanbul ignore next */
          cov_2757l6cm5l().s[238]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_2757l6cm5l().f[43]++;
            cov_2757l6cm5l().s[239]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[74][0]++;
                cov_2757l6cm5l().s[240]++;
                return [4 /*yield*/, enhanced_rate_limiter_1.enhancedRateLimiters.write.checkLimit(request)];
              case 1:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[74][1]++;
                cov_2757l6cm5l().s[241]++;
                rateLimitResult = _b.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[242]++;
                if (!rateLimitResult.allowed) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[75][0]++;
                  cov_2757l6cm5l().s[243]++;
                  error = new Error('Too many delete attempts. Please try again later.');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[244]++;
                  error.statusCode = 429;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[245]++;
                  error.headers = rateLimitResult.headers;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[246]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[75][1]++;
                }
                cov_2757l6cm5l().s[247]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 2:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[74][2]++;
                cov_2757l6cm5l().s[248]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[249]++;
                if (!(
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[78][0]++, (_a =
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[80][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[80][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[79][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[79][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[78][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[77][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2757l6cm5l().b[77][1]++, _a.email))) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[76][0]++;
                  cov_2757l6cm5l().s[250]++;
                  error = new Error('Not authenticated');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[251]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[252]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[76][1]++;
                }
                cov_2757l6cm5l().s[253]++;
                return [4 /*yield*/, prisma_1.default.user.findUnique({
                  where: {
                    email: session.user.email
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[74][3]++;
                cov_2757l6cm5l().s[254]++;
                user = _b.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[255]++;
                if (!user) {
                  /* istanbul ignore next */
                  cov_2757l6cm5l().b[81][0]++;
                  cov_2757l6cm5l().s[256]++;
                  error = new Error('User not found');
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[257]++;
                  error.statusCode = 404;
                  /* istanbul ignore next */
                  cov_2757l6cm5l().s[258]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2757l6cm5l().b[81][1]++;
                }
                // Remove profile picture URL from database
                cov_2757l6cm5l().s[259]++;
                return [4 /*yield*/, prisma_1.default.profile.upsert({
                  where: {
                    userId: user.id
                  },
                  update: {
                    profilePictureUrl: null,
                    lastProfileUpdate: new Date(),
                    updatedAt: new Date()
                  },
                  create: {
                    userId: user.id,
                    profilePictureUrl: null,
                    lastProfileUpdate: new Date()
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_2757l6cm5l().b[74][4]++;
                cov_2757l6cm5l().s[260]++;
                // Remove profile picture URL from database
                _b.sent();
                /* istanbul ignore next */
                cov_2757l6cm5l().s[261]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: {
                    success: true,
                    message: 'Profile photo removed successfully'
                  }
                })];
            }
          });
        });
      })];
    });
  });
});
// GET endpoint for serving local photos (fallback when Vercel Blob is not available)
/* istanbul ignore next */
cov_2757l6cm5l().s[262]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_2757l6cm5l().f[44]++;
  cov_2757l6cm5l().s[263]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_2757l6cm5l().f[45]++;
    var pathname, fileName, error_3, error;
    /* istanbul ignore next */
    cov_2757l6cm5l().s[264]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2757l6cm5l().f[46]++;
      cov_2757l6cm5l().s[265]++;
      pathname = new URL(request.url).pathname;
      /* istanbul ignore next */
      cov_2757l6cm5l().s[266]++;
      fileName = pathname.split('/').pop();
      /* istanbul ignore next */
      cov_2757l6cm5l().s[267]++;
      if (!fileName) {
        /* istanbul ignore next */
        cov_2757l6cm5l().b[82][0]++;
        cov_2757l6cm5l().s[268]++;
        error_3 = new Error('File not found');
        /* istanbul ignore next */
        cov_2757l6cm5l().s[269]++;
        error_3.statusCode = 404;
        /* istanbul ignore next */
        cov_2757l6cm5l().s[270]++;
        throw error_3;
      } else
      /* istanbul ignore next */
      {
        cov_2757l6cm5l().b[82][1]++;
      }
      cov_2757l6cm5l().s[271]++;
      error = new Error('Photo serving requires Vercel Blob storage configuration');
      /* istanbul ignore next */
      cov_2757l6cm5l().s[272]++;
      error.statusCode = 501;
      /* istanbul ignore next */
      cov_2757l6cm5l().s[273]++;
      throw error;
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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