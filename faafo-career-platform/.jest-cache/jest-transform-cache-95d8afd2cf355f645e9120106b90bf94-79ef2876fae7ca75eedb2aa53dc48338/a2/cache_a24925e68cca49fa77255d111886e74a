7d5841cc24b8be95927681fe97932b2b
"use strict";

// Caching utilities for improved performance
/* istanbul ignore next */
function cov_oaf5a5bfi() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/cache.ts";
  var hash = "5d993ef694eb31470638fb5af27b0564f108e624";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/cache.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 16
        },
        end: {
          line: 11,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 28
        },
        end: {
          line: 4,
          column: 110
        }
      },
      "2": {
        start: {
          line: 4,
          column: 91
        },
        end: {
          line: 4,
          column: 106
        }
      },
      "3": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 10,
          column: 7
        }
      },
      "4": {
        start: {
          line: 6,
          column: 36
        },
        end: {
          line: 6,
          column: 97
        }
      },
      "5": {
        start: {
          line: 6,
          column: 42
        },
        end: {
          line: 6,
          column: 70
        }
      },
      "6": {
        start: {
          line: 6,
          column: 85
        },
        end: {
          line: 6,
          column: 95
        }
      },
      "7": {
        start: {
          line: 7,
          column: 35
        },
        end: {
          line: 7,
          column: 100
        }
      },
      "8": {
        start: {
          line: 7,
          column: 41
        },
        end: {
          line: 7,
          column: 73
        }
      },
      "9": {
        start: {
          line: 7,
          column: 88
        },
        end: {
          line: 7,
          column: 98
        }
      },
      "10": {
        start: {
          line: 8,
          column: 32
        },
        end: {
          line: 8,
          column: 116
        }
      },
      "11": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 78
        }
      },
      "12": {
        start: {
          line: 12,
          column: 18
        },
        end: {
          line: 38,
          column: 1
        }
      },
      "13": {
        start: {
          line: 13,
          column: 12
        },
        end: {
          line: 13,
          column: 104
        }
      },
      "14": {
        start: {
          line: 13,
          column: 43
        },
        end: {
          line: 13,
          column: 68
        }
      },
      "15": {
        start: {
          line: 13,
          column: 57
        },
        end: {
          line: 13,
          column: 68
        }
      },
      "16": {
        start: {
          line: 13,
          column: 69
        },
        end: {
          line: 13,
          column: 81
        }
      },
      "17": {
        start: {
          line: 13,
          column: 119
        },
        end: {
          line: 13,
          column: 196
        }
      },
      "18": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 160
        }
      },
      "19": {
        start: {
          line: 14,
          column: 141
        },
        end: {
          line: 14,
          column: 153
        }
      },
      "20": {
        start: {
          line: 15,
          column: 23
        },
        end: {
          line: 15,
          column: 68
        }
      },
      "21": {
        start: {
          line: 15,
          column: 45
        },
        end: {
          line: 15,
          column: 65
        }
      },
      "22": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 17,
          column: 70
        }
      },
      "23": {
        start: {
          line: 17,
          column: 15
        },
        end: {
          line: 17,
          column: 70
        }
      },
      "24": {
        start: {
          line: 18,
          column: 8
        },
        end: {
          line: 35,
          column: 66
        }
      },
      "25": {
        start: {
          line: 18,
          column: 50
        },
        end: {
          line: 35,
          column: 66
        }
      },
      "26": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 169
        }
      },
      "27": {
        start: {
          line: 19,
          column: 160
        },
        end: {
          line: 19,
          column: 169
        }
      },
      "28": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 20,
          column: 52
        }
      },
      "29": {
        start: {
          line: 20,
          column: 26
        },
        end: {
          line: 20,
          column: 52
        }
      },
      "30": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 33,
          column: 13
        }
      },
      "31": {
        start: {
          line: 22,
          column: 32
        },
        end: {
          line: 22,
          column: 39
        }
      },
      "32": {
        start: {
          line: 22,
          column: 40
        },
        end: {
          line: 22,
          column: 46
        }
      },
      "33": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "34": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 72
        }
      },
      "35": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 34
        }
      },
      "36": {
        start: {
          line: 24,
          column: 35
        },
        end: {
          line: 24,
          column: 45
        }
      },
      "37": {
        start: {
          line: 24,
          column: 46
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "38": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "39": {
        start: {
          line: 25,
          column: 24
        },
        end: {
          line: 25,
          column: 41
        }
      },
      "40": {
        start: {
          line: 25,
          column: 42
        },
        end: {
          line: 25,
          column: 55
        }
      },
      "41": {
        start: {
          line: 25,
          column: 56
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "42": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 128
        }
      },
      "43": {
        start: {
          line: 27,
          column: 110
        },
        end: {
          line: 27,
          column: 116
        }
      },
      "44": {
        start: {
          line: 27,
          column: 117
        },
        end: {
          line: 27,
          column: 126
        }
      },
      "45": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 106
        }
      },
      "46": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 97
        }
      },
      "47": {
        start: {
          line: 28,
          column: 98
        },
        end: {
          line: 28,
          column: 104
        }
      },
      "48": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 89
        }
      },
      "49": {
        start: {
          line: 29,
          column: 57
        },
        end: {
          line: 29,
          column: 72
        }
      },
      "50": {
        start: {
          line: 29,
          column: 73
        },
        end: {
          line: 29,
          column: 80
        }
      },
      "51": {
        start: {
          line: 29,
          column: 81
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "52": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 87
        }
      },
      "53": {
        start: {
          line: 30,
          column: 47
        },
        end: {
          line: 30,
          column: 62
        }
      },
      "54": {
        start: {
          line: 30,
          column: 63
        },
        end: {
          line: 30,
          column: 78
        }
      },
      "55": {
        start: {
          line: 30,
          column: 79
        },
        end: {
          line: 30,
          column: 85
        }
      },
      "56": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 42
        }
      },
      "57": {
        start: {
          line: 31,
          column: 30
        },
        end: {
          line: 31,
          column: 42
        }
      },
      "58": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 33
        }
      },
      "59": {
        start: {
          line: 32,
          column: 34
        },
        end: {
          line: 32,
          column: 43
        }
      },
      "60": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 39
        }
      },
      "61": {
        start: {
          line: 35,
          column: 22
        },
        end: {
          line: 35,
          column: 34
        }
      },
      "62": {
        start: {
          line: 35,
          column: 35
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "63": {
        start: {
          line: 35,
          column: 54
        },
        end: {
          line: 35,
          column: 64
        }
      },
      "64": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 36,
          column: 35
        }
      },
      "65": {
        start: {
          line: 36,
          column: 23
        },
        end: {
          line: 36,
          column: 35
        }
      },
      "66": {
        start: {
          line: 36,
          column: 36
        },
        end: {
          line: 36,
          column: 89
        }
      },
      "67": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "68": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 249
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 30
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 42
        }
      },
      "71": {
        start: {
          line: 43,
          column: 33
        },
        end: {
          line: 117,
          column: 3
        }
      },
      "72": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 49
        }
      },
      "73": {
        start: {
          line: 45,
          column: 34
        },
        end: {
          line: 45,
          column: 47
        }
      },
      "74": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 31
        }
      },
      "75": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 47,
          column: 55
        }
      },
      "76": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 48,
          column: 47
        }
      },
      "77": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 68,
          column: 6
        }
      },
      "78": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 61,
          column: 9
        }
      },
      "79": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 27
        }
      },
      "80": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 60,
          column: 13
        }
      },
      "81": {
        start: {
          line: 56,
          column: 32
        },
        end: {
          line: 56,
          column: 62
        }
      },
      "82": {
        start: {
          line: 57,
          column: 16
        },
        end: {
          line: 59,
          column: 17
        }
      },
      "83": {
        start: {
          line: 58,
          column: 20
        },
        end: {
          line: 58,
          column: 49
        }
      },
      "84": {
        start: {
          line: 62,
          column: 20
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "85": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 67,
          column: 35
        }
      },
      "86": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 80,
          column: 6
        }
      },
      "87": {
        start: {
          line: 70,
          column: 20
        },
        end: {
          line: 70,
          column: 39
        }
      },
      "88": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 73,
          column: 9
        }
      },
      "89": {
        start: {
          line: 72,
          column: 12
        },
        end: {
          line: 72,
          column: 24
        }
      },
      "90": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 78,
          column: 9
        }
      },
      "91": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 76,
          column: 35
        }
      },
      "92": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 77,
          column: 24
        }
      },
      "93": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 26
        }
      },
      "94": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 83,
          column: 6
        }
      },
      "95": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 82,
          column: 38
        }
      },
      "96": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 86,
          column: 6
        }
      },
      "97": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 85,
          column: 38
        }
      },
      "98": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 89,
          column: 6
        }
      },
      "99": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 27
        }
      },
      "100": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 100,
          column: 6
        }
      },
      "101": {
        start: {
          line: 92,
          column: 20
        },
        end: {
          line: 92,
          column: 24
        }
      },
      "102": {
        start: {
          line: 93,
          column: 18
        },
        end: {
          line: 93,
          column: 28
        }
      },
      "103": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 99,
          column: 11
        }
      },
      "104": {
        start: {
          line: 95,
          column: 22
        },
        end: {
          line: 95,
          column: 27
        }
      },
      "105": {
        start: {
          line: 95,
          column: 37
        },
        end: {
          line: 95,
          column: 42
        }
      },
      "106": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 98,
          column: 13
        }
      },
      "107": {
        start: {
          line: 97,
          column: 16
        },
        end: {
          line: 97,
          column: 40
        }
      },
      "108": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 108,
          column: 6
        }
      },
      "109": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 107,
          column: 10
        }
      },
      "110": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 112,
          column: 6
        }
      },
      "111": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 29
        }
      },
      "112": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 115,
          column: 6
        }
      },
      "113": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 33
        }
      },
      "114": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 116,
          column: 23
        }
      },
      "115": {
        start: {
          line: 118,
          column: 0
        },
        end: {
          line: 118,
          column: 34
        }
      },
      "116": {
        start: {
          line: 120,
          column: 0
        },
        end: {
          line: 120,
          column: 73
        }
      },
      "117": {
        start: {
          line: 121,
          column: 0
        },
        end: {
          line: 121,
          column: 75
        }
      },
      "118": {
        start: {
          line: 122,
          column: 0
        },
        end: {
          line: 122,
          column: 77
        }
      },
      "119": {
        start: {
          line: 124,
          column: 0
        },
        end: {
          line: 133,
          column: 2
        }
      },
      "120": {
        start: {
          line: 125,
          column: 26
        },
        end: {
          line: 125,
          column: 52
        }
      },
      "121": {
        start: {
          line: 126,
          column: 33
        },
        end: {
          line: 126,
          column: 67
        }
      },
      "122": {
        start: {
          line: 127,
          column: 36
        },
        end: {
          line: 127,
          column: 72
        }
      },
      "123": {
        start: {
          line: 128,
          column: 31
        },
        end: {
          line: 128,
          column: 57
        }
      },
      "124": {
        start: {
          line: 129,
          column: 44
        },
        end: {
          line: 129,
          column: 89
        }
      },
      "125": {
        start: {
          line: 130,
          column: 38
        },
        end: {
          line: 130,
          column: 72
        }
      },
      "126": {
        start: {
          line: 131,
          column: 37
        },
        end: {
          line: 131,
          column: 75
        }
      },
      "127": {
        start: {
          line: 132,
          column: 41
        },
        end: {
          line: 132,
          column: 82
        }
      },
      "128": {
        start: {
          line: 136,
          column: 16
        },
        end: {
          line: 136,
          column: 20
        }
      },
      "129": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 137,
          column: 55
        }
      },
      "130": {
        start: {
          line: 137,
          column: 28
        },
        end: {
          line: 137,
          column: 53
        }
      },
      "131": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 169,
          column: 7
        }
      },
      "132": {
        start: {
          line: 139,
          column: 19
        },
        end: {
          line: 139,
          column: 21
        }
      },
      "133": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 142,
          column: 9
        }
      },
      "134": {
        start: {
          line: 140,
          column: 22
        },
        end: {
          line: 140,
          column: 23
        }
      },
      "135": {
        start: {
          line: 141,
          column: 12
        },
        end: {
          line: 141,
          column: 37
        }
      },
      "136": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 168,
          column: 11
        }
      },
      "137": {
        start: {
          line: 145,
          column: 12
        },
        end: {
          line: 167,
          column: 15
        }
      },
      "138": {
        start: {
          line: 146,
          column: 16
        },
        end: {
          line: 166,
          column: 17
        }
      },
      "139": {
        start: {
          line: 148,
          column: 24
        },
        end: {
          line: 148,
          column: 63
        }
      },
      "140": {
        start: {
          line: 149,
          column: 24
        },
        end: {
          line: 149,
          column: 48
        }
      },
      "141": {
        start: {
          line: 150,
          column: 24
        },
        end: {
          line: 152,
          column: 25
        }
      },
      "142": {
        start: {
          line: 151,
          column: 28
        },
        end: {
          line: 151,
          column: 58
        }
      },
      "143": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 153,
          column: 37
        }
      },
      "144": {
        start: {
          line: 155,
          column: 24
        },
        end: {
          line: 155,
          column: 50
        }
      },
      "145": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 69
        }
      },
      "146": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 43
        }
      },
      "147": {
        start: {
          line: 159,
          column: 24
        },
        end: {
          line: 159,
          column: 52
        }
      },
      "148": {
        start: {
          line: 160,
          column: 24
        },
        end: {
          line: 160,
          column: 54
        }
      },
      "149": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 44
        }
      },
      "150": {
        start: {
          line: 164,
          column: 24
        },
        end: {
          line: 164,
          column: 38
        }
      },
      "151": {
        start: {
          line: 165,
          column: 28
        },
        end: {
          line: 165,
          column: 50
        }
      },
      "152": {
        start: {
          line: 172,
          column: 0
        },
        end: {
          line: 194,
          column: 2
        }
      },
      "153": {
        start: {
          line: 174,
          column: 39
        },
        end: {
          line: 179,
          column: 7
        }
      },
      "154": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 178,
          column: 11
        }
      },
      "155": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 177,
          column: 64
        }
      },
      "156": {
        start: {
          line: 179,
          column: 27
        },
        end: {
          line: 179,
          column: 61
        }
      },
      "157": {
        start: {
          line: 181,
          column: 44
        },
        end: {
          line: 186,
          column: 7
        }
      },
      "158": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 185,
          column: 11
        }
      },
      "159": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 72
        }
      },
      "160": {
        start: {
          line: 186,
          column: 25
        },
        end: {
          line: 186,
          column: 64
        }
      },
      "161": {
        start: {
          line: 188,
          column: 57
        },
        end: {
          line: 193,
          column: 7
        }
      },
      "162": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 192,
          column: 11
        }
      },
      "163": {
        start: {
          line: 191,
          column: 12
        },
        end: {
          line: 191,
          column: 78
        }
      },
      "164": {
        start: {
          line: 193,
          column: 32
        },
        end: {
          line: 193,
          column: 100
        }
      },
      "165": {
        start: {
          line: 196,
          column: 0
        },
        end: {
          line: 222,
          column: 2
        }
      },
      "166": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 199,
          column: 65
        }
      },
      "167": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 200,
          column: 72
        }
      },
      "168": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 201,
          column: 70
        }
      },
      "169": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 202,
          column: 72
        }
      },
      "170": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 203,
          column: 71
        }
      },
      "171": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 204,
          column: 75
        }
      },
      "172": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 208,
          column: 70
        }
      },
      "173": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 209,
          column: 75
        }
      },
      "174": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 213,
          column: 72
        }
      },
      "175": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 214,
          column: 75
        }
      },
      "176": {
        start: {
          line: 218,
          column: 8
        },
        end: {
          line: 218,
          column: 68
        }
      },
      "177": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 36
        }
      },
      "178": {
        start: {
          line: 224,
          column: 34
        },
        end: {
          line: 292,
          column: 3
        }
      },
      "179": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 226,
          column: 59
        }
      },
      "180": {
        start: {
          line: 226,
          column: 33
        },
        end: {
          line: 226,
          column: 57
        }
      },
      "181": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 227,
          column: 29
        }
      },
      "182": {
        start: {
          line: 229,
          column: 4
        },
        end: {
          line: 243,
          column: 6
        }
      },
      "183": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 56
        }
      },
      "184": {
        start: {
          line: 230,
          column: 32
        },
        end: {
          line: 230,
          column: 54
        }
      },
      "185": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 232,
          column: 19
        }
      },
      "186": {
        start: {
          line: 232,
          column: 12
        },
        end: {
          line: 232,
          column: 19
        }
      },
      "187": {
        start: {
          line: 233,
          column: 20
        },
        end: {
          line: 236,
          column: 9
        }
      },
      "188": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 242,
          column: 9
        }
      },
      "189": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 238,
          column: 75
        }
      },
      "190": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 241,
          column: 64
        }
      },
      "191": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 262,
          column: 6
        }
      },
      "192": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 246,
          column: 24
        }
      },
      "193": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 246,
          column: 24
        }
      },
      "194": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 261,
          column: 9
        }
      },
      "195": {
        start: {
          line: 248,
          column: 23
        },
        end: {
          line: 248,
          column: 62
        }
      },
      "196": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 250,
          column: 28
        }
      },
      "197": {
        start: {
          line: 250,
          column: 16
        },
        end: {
          line: 250,
          column: 28
        }
      },
      "198": {
        start: {
          line: 251,
          column: 24
        },
        end: {
          line: 251,
          column: 40
        }
      },
      "199": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 255,
          column: 13
        }
      },
      "200": {
        start: {
          line: 253,
          column: 16
        },
        end: {
          line: 253,
          column: 59
        }
      },
      "201": {
        start: {
          line: 254,
          column: 16
        },
        end: {
          line: 254,
          column: 28
        }
      },
      "202": {
        start: {
          line: 256,
          column: 12
        },
        end: {
          line: 256,
          column: 30
        }
      },
      "203": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 259,
          column: 64
        }
      },
      "204": {
        start: {
          line: 260,
          column: 12
        },
        end: {
          line: 260,
          column: 24
        }
      },
      "205": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 267,
          column: 6
        }
      },
      "206": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 265,
          column: 19
        }
      },
      "207": {
        start: {
          line: 265,
          column: 12
        },
        end: {
          line: 265,
          column: 19
        }
      },
      "208": {
        start: {
          line: 266,
          column: 8
        },
        end: {
          line: 266,
          column: 51
        }
      },
      "209": {
        start: {
          line: 268,
          column: 4
        },
        end: {
          line: 278,
          column: 6
        }
      },
      "210": {
        start: {
          line: 269,
          column: 20
        },
        end: {
          line: 269,
          column: 24
        }
      },
      "211": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 271,
          column: 19
        }
      },
      "212": {
        start: {
          line: 271,
          column: 12
        },
        end: {
          line: 271,
          column: 19
        }
      },
      "213": {
        start: {
          line: 272,
          column: 19
        },
        end: {
          line: 272,
          column: 44
        }
      },
      "214": {
        start: {
          line: 273,
          column: 8
        },
        end: {
          line: 277,
          column: 11
        }
      },
      "215": {
        start: {
          line: 274,
          column: 12
        },
        end: {
          line: 276,
          column: 13
        }
      },
      "216": {
        start: {
          line: 275,
          column: 16
        },
        end: {
          line: 275,
          column: 45
        }
      },
      "217": {
        start: {
          line: 280,
          column: 4
        },
        end: {
          line: 290,
          column: 6
        }
      },
      "218": {
        start: {
          line: 281,
          column: 20
        },
        end: {
          line: 281,
          column: 24
        }
      },
      "219": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 283,
          column: 19
        }
      },
      "220": {
        start: {
          line: 283,
          column: 12
        },
        end: {
          line: 283,
          column: 19
        }
      },
      "221": {
        start: {
          line: 284,
          column: 19
        },
        end: {
          line: 284,
          column: 44
        }
      },
      "222": {
        start: {
          line: 285,
          column: 8
        },
        end: {
          line: 289,
          column: 11
        }
      },
      "223": {
        start: {
          line: 286,
          column: 12
        },
        end: {
          line: 288,
          column: 13
        }
      },
      "224": {
        start: {
          line: 287,
          column: 16
        },
        end: {
          line: 287,
          column: 57
        }
      },
      "225": {
        start: {
          line: 291,
          column: 4
        },
        end: {
          line: 291,
          column: 24
        }
      },
      "226": {
        start: {
          line: 293,
          column: 0
        },
        end: {
          line: 293,
          column: 36
        }
      },
      "227": {
        start: {
          line: 294,
          column: 0
        },
        end: {
          line: 294,
          column: 42
        }
      },
      "228": {
        start: {
          line: 297,
          column: 4
        },
        end: {
          line: 297,
          column: 52
        }
      },
      "229": {
        start: {
          line: 297,
          column: 28
        },
        end: {
          line: 297,
          column: 50
        }
      },
      "230": {
        start: {
          line: 298,
          column: 19
        },
        end: {
          line: 300,
          column: 5
        }
      },
      "231": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 299,
          column: 51
        }
      },
      "232": {
        start: {
          line: 301,
          column: 19
        },
        end: {
          line: 303,
          column: 5
        }
      },
      "233": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 302,
          column: 45
        }
      },
      "234": {
        start: {
          line: 304,
          column: 22
        },
        end: {
          line: 306,
          column: 5
        }
      },
      "235": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 305,
          column: 41
        }
      },
      "236": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 307,
          column: 80
        }
      },
      "237": {
        start: {
          line: 310,
          column: 0
        },
        end: {
          line: 318,
          column: 1
        }
      },
      "238": {
        start: {
          line: 312,
          column: 4
        },
        end: {
          line: 317,
          column: 23
        }
      },
      "239": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 313,
          column: 35
        }
      },
      "240": {
        start: {
          line: 314,
          column: 8
        },
        end: {
          line: 314,
          column: 36
        }
      },
      "241": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 315,
          column: 38
        }
      },
      "242": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 39
        }
      },
      "243": {
        start: {
          line: 320,
          column: 0
        },
        end: {
          line: 320,
          column: 33
        }
      },
      "244": {
        start: {
          line: 322,
          column: 0
        },
        end: {
          line: 322,
          column: 40
        }
      },
      "245": {
        start: {
          line: 323,
          column: 0
        },
        end: {
          line: 333,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 3,
            column: 45
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 11,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 4,
            column: 13
          },
          end: {
            line: 4,
            column: 18
          }
        },
        loc: {
          start: {
            line: 4,
            column: 26
          },
          end: {
            line: 4,
            column: 112
          }
        },
        line: 4
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 4,
            column: 70
          },
          end: {
            line: 4,
            column: 71
          }
        },
        loc: {
          start: {
            line: 4,
            column: 89
          },
          end: {
            line: 4,
            column: 108
          }
        },
        line: 4
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 5,
            column: 36
          },
          end: {
            line: 5,
            column: 37
          }
        },
        loc: {
          start: {
            line: 5,
            column: 63
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 5
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 26
          }
        },
        loc: {
          start: {
            line: 6,
            column: 34
          },
          end: {
            line: 6,
            column: 99
          }
        },
        line: 6
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 25
          }
        },
        loc: {
          start: {
            line: 7,
            column: 33
          },
          end: {
            line: 7,
            column: 102
          }
        },
        line: 7
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 8,
            column: 17
          },
          end: {
            line: 8,
            column: 21
          }
        },
        loc: {
          start: {
            line: 8,
            column: 30
          },
          end: {
            line: 8,
            column: 118
          }
        },
        line: 8
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 12,
            column: 48
          },
          end: {
            line: 12,
            column: 49
          }
        },
        loc: {
          start: {
            line: 12,
            column: 73
          },
          end: {
            line: 38,
            column: 1
          }
        },
        line: 12
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 13,
            column: 30
          },
          end: {
            line: 13,
            column: 31
          }
        },
        loc: {
          start: {
            line: 13,
            column: 41
          },
          end: {
            line: 13,
            column: 83
          }
        },
        line: 13
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 14,
            column: 128
          },
          end: {
            line: 14,
            column: 129
          }
        },
        loc: {
          start: {
            line: 14,
            column: 139
          },
          end: {
            line: 14,
            column: 155
          }
        },
        line: 14
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 21
          },
          end: {
            line: 15,
            column: 70
          }
        },
        line: 15
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 15,
            column: 30
          },
          end: {
            line: 15,
            column: 31
          }
        },
        loc: {
          start: {
            line: 15,
            column: 43
          },
          end: {
            line: 15,
            column: 67
          }
        },
        line: 15
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 16,
            column: 13
          },
          end: {
            line: 16,
            column: 17
          }
        },
        loc: {
          start: {
            line: 16,
            column: 22
          },
          end: {
            line: 37,
            column: 5
          }
        },
        line: 16
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 43,
            column: 33
          },
          end: {
            line: 43,
            column: 34
          }
        },
        loc: {
          start: {
            line: 43,
            column: 45
          },
          end: {
            line: 117,
            column: 1
          }
        },
        line: 43
      },
      "14": {
        name: "MemoryCache",
        decl: {
          start: {
            line: 44,
            column: 13
          },
          end: {
            line: 44,
            column: 24
          }
        },
        loc: {
          start: {
            line: 44,
            column: 34
          },
          end: {
            line: 49,
            column: 5
          }
        },
        line: 44
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 50,
            column: 32
          },
          end: {
            line: 50,
            column: 33
          }
        },
        loc: {
          start: {
            line: 50,
            column: 58
          },
          end: {
            line: 68,
            column: 5
          }
        },
        line: 50
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 69,
            column: 32
          },
          end: {
            line: 69,
            column: 33
          }
        },
        loc: {
          start: {
            line: 69,
            column: 47
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 69
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 81,
            column: 32
          },
          end: {
            line: 81,
            column: 33
          }
        },
        loc: {
          start: {
            line: 81,
            column: 47
          },
          end: {
            line: 83,
            column: 5
          }
        },
        line: 81
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 84,
            column: 35
          },
          end: {
            line: 84,
            column: 36
          }
        },
        loc: {
          start: {
            line: 84,
            column: 50
          },
          end: {
            line: 86,
            column: 5
          }
        },
        line: 84
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 87,
            column: 34
          },
          end: {
            line: 87,
            column: 35
          }
        },
        loc: {
          start: {
            line: 87,
            column: 46
          },
          end: {
            line: 89,
            column: 5
          }
        },
        line: 87
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 91,
            column: 36
          },
          end: {
            line: 91,
            column: 37
          }
        },
        loc: {
          start: {
            line: 91,
            column: 48
          },
          end: {
            line: 100,
            column: 5
          }
        },
        line: 91
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 94,
            column: 49
          },
          end: {
            line: 94,
            column: 50
          }
        },
        loc: {
          start: {
            line: 94,
            column: 63
          },
          end: {
            line: 99,
            column: 9
          }
        },
        line: 94
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 102,
            column: 37
          },
          end: {
            line: 102,
            column: 38
          }
        },
        loc: {
          start: {
            line: 102,
            column: 49
          },
          end: {
            line: 108,
            column: 5
          }
        },
        line: 102
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 110,
            column: 36
          },
          end: {
            line: 110,
            column: 37
          }
        },
        loc: {
          start: {
            line: 110,
            column: 51
          },
          end: {
            line: 112,
            column: 5
          }
        },
        line: 110
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 113,
            column: 36
          },
          end: {
            line: 113,
            column: 37
          }
        },
        loc: {
          start: {
            line: 113,
            column: 62
          },
          end: {
            line: 115,
            column: 5
          }
        },
        line: 113
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 125,
            column: 10
          },
          end: {
            line: 125,
            column: 11
          }
        },
        loc: {
          start: {
            line: 125,
            column: 24
          },
          end: {
            line: 125,
            column: 54
          }
        },
        line: 125
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 126,
            column: 17
          },
          end: {
            line: 126,
            column: 18
          }
        },
        loc: {
          start: {
            line: 126,
            column: 31
          },
          end: {
            line: 126,
            column: 69
          }
        },
        line: 126
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 127,
            column: 17
          }
        },
        loc: {
          start: {
            line: 127,
            column: 34
          },
          end: {
            line: 127,
            column: 74
          }
        },
        line: 127
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 128,
            column: 17
          },
          end: {
            line: 128,
            column: 18
          }
        },
        loc: {
          start: {
            line: 128,
            column: 29
          },
          end: {
            line: 128,
            column: 59
          }
        },
        line: 128
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 129,
            column: 23
          },
          end: {
            line: 129,
            column: 24
          }
        },
        loc: {
          start: {
            line: 129,
            column: 42
          },
          end: {
            line: 129,
            column: 91
          }
        },
        line: 129
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 130,
            column: 18
          },
          end: {
            line: 130,
            column: 19
          }
        },
        loc: {
          start: {
            line: 130,
            column: 36
          },
          end: {
            line: 130,
            column: 74
          }
        },
        line: 130
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 131,
            column: 17
          },
          end: {
            line: 131,
            column: 18
          }
        },
        loc: {
          start: {
            line: 131,
            column: 35
          },
          end: {
            line: 131,
            column: 77
          }
        },
        line: 131
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 132,
            column: 21
          },
          end: {
            line: 132,
            column: 22
          }
        },
        loc: {
          start: {
            line: 132,
            column: 39
          },
          end: {
            line: 132,
            column: 84
          }
        },
        line: 132
      },
      "33": {
        name: "withCache",
        decl: {
          start: {
            line: 135,
            column: 9
          },
          end: {
            line: 135,
            column: 18
          }
        },
        loc: {
          start: {
            line: 135,
            column: 49
          },
          end: {
            line: 170,
            column: 1
          }
        },
        line: 135
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 138,
            column: 12
          },
          end: {
            line: 138,
            column: 13
          }
        },
        loc: {
          start: {
            line: 138,
            column: 24
          },
          end: {
            line: 169,
            column: 5
          }
        },
        line: 138
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 143,
            column: 48
          },
          end: {
            line: 143,
            column: 49
          }
        },
        loc: {
          start: {
            line: 143,
            column: 60
          },
          end: {
            line: 168,
            column: 9
          }
        },
        line: 143
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 145,
            column: 37
          },
          end: {
            line: 145,
            column: 38
          }
        },
        loc: {
          start: {
            line: 145,
            column: 51
          },
          end: {
            line: 167,
            column: 13
          }
        },
        line: 145
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 174,
            column: 23
          },
          end: {
            line: 174,
            column: 24
          }
        },
        loc: {
          start: {
            line: 174,
            column: 37
          },
          end: {
            line: 179,
            column: 9
          }
        },
        line: 174
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 174,
            column: 80
          },
          end: {
            line: 174,
            column: 81
          }
        },
        loc: {
          start: {
            line: 174,
            column: 92
          },
          end: {
            line: 179,
            column: 5
          }
        },
        line: 174
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 175,
            column: 33
          },
          end: {
            line: 175,
            column: 34
          }
        },
        loc: {
          start: {
            line: 175,
            column: 47
          },
          end: {
            line: 178,
            column: 9
          }
        },
        line: 175
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 179,
            column: 11
          },
          end: {
            line: 179,
            column: 12
          }
        },
        loc: {
          start: {
            line: 179,
            column: 25
          },
          end: {
            line: 179,
            column: 63
          }
        },
        line: 179
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 181,
            column: 30
          },
          end: {
            line: 181,
            column: 31
          }
        },
        loc: {
          start: {
            line: 181,
            column: 42
          },
          end: {
            line: 186,
            column: 9
          }
        },
        line: 181
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 181,
            column: 85
          },
          end: {
            line: 181,
            column: 86
          }
        },
        loc: {
          start: {
            line: 181,
            column: 97
          },
          end: {
            line: 186,
            column: 5
          }
        },
        line: 181
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 182,
            column: 33
          },
          end: {
            line: 182,
            column: 34
          }
        },
        loc: {
          start: {
            line: 182,
            column: 47
          },
          end: {
            line: 185,
            column: 9
          }
        },
        line: 182
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 186,
            column: 11
          },
          end: {
            line: 186,
            column: 12
          }
        },
        loc: {
          start: {
            line: 186,
            column: 23
          },
          end: {
            line: 186,
            column: 66
          }
        },
        line: 186
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 188,
            column: 36
          },
          end: {
            line: 188,
            column: 37
          }
        },
        loc: {
          start: {
            line: 188,
            column: 55
          },
          end: {
            line: 193,
            column: 9
          }
        },
        line: 188
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 188,
            column: 98
          },
          end: {
            line: 188,
            column: 99
          }
        },
        loc: {
          start: {
            line: 188,
            column: 110
          },
          end: {
            line: 193,
            column: 5
          }
        },
        line: 188
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 189,
            column: 33
          },
          end: {
            line: 189,
            column: 34
          }
        },
        loc: {
          start: {
            line: 189,
            column: 47
          },
          end: {
            line: 192,
            column: 9
          }
        },
        line: 189
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 193,
            column: 11
          },
          end: {
            line: 193,
            column: 12
          }
        },
        loc: {
          start: {
            line: 193,
            column: 30
          },
          end: {
            line: 193,
            column: 102
          }
        },
        line: 193
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 198,
            column: 21
          }
        },
        loc: {
          start: {
            line: 198,
            column: 38
          },
          end: {
            line: 205,
            column: 5
          }
        },
        line: 198
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 207,
            column: 26
          },
          end: {
            line: 207,
            column: 27
          }
        },
        loc: {
          start: {
            line: 207,
            column: 44
          },
          end: {
            line: 210,
            column: 5
          }
        },
        line: 207
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 212,
            column: 24
          },
          end: {
            line: 212,
            column: 25
          }
        },
        loc: {
          start: {
            line: 212,
            column: 42
          },
          end: {
            line: 215,
            column: 5
          }
        },
        line: 212
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 217,
            column: 22
          },
          end: {
            line: 217,
            column: 23
          }
        },
        loc: {
          start: {
            line: 217,
            column: 34
          },
          end: {
            line: 221,
            column: 5
          }
        },
        line: 217
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 224,
            column: 34
          },
          end: {
            line: 224,
            column: 35
          }
        },
        loc: {
          start: {
            line: 224,
            column: 46
          },
          end: {
            line: 292,
            column: 1
          }
        },
        line: 224
      },
      "54": {
        name: "BrowserCache",
        decl: {
          start: {
            line: 225,
            column: 13
          },
          end: {
            line: 225,
            column: 25
          }
        },
        loc: {
          start: {
            line: 225,
            column: 34
          },
          end: {
            line: 228,
            column: 5
          }
        },
        line: 225
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 229,
            column: 33
          },
          end: {
            line: 229,
            column: 34
          }
        },
        loc: {
          start: {
            line: 229,
            column: 61
          },
          end: {
            line: 243,
            column: 5
          }
        },
        line: 229
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 244,
            column: 33
          },
          end: {
            line: 244,
            column: 34
          }
        },
        loc: {
          start: {
            line: 244,
            column: 48
          },
          end: {
            line: 262,
            column: 5
          }
        },
        line: 244
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 263,
            column: 36
          },
          end: {
            line: 263,
            column: 37
          }
        },
        loc: {
          start: {
            line: 263,
            column: 51
          },
          end: {
            line: 267,
            column: 5
          }
        },
        line: 263
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 268,
            column: 35
          },
          end: {
            line: 268,
            column: 36
          }
        },
        loc: {
          start: {
            line: 268,
            column: 47
          },
          end: {
            line: 278,
            column: 5
          }
        },
        line: 268
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 273,
            column: 21
          },
          end: {
            line: 273,
            column: 22
          }
        },
        loc: {
          start: {
            line: 273,
            column: 36
          },
          end: {
            line: 277,
            column: 9
          }
        },
        line: 273
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 280,
            column: 37
          },
          end: {
            line: 280,
            column: 38
          }
        },
        loc: {
          start: {
            line: 280,
            column: 49
          },
          end: {
            line: 290,
            column: 5
          }
        },
        line: 280
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 285,
            column: 21
          },
          end: {
            line: 285,
            column: 22
          }
        },
        loc: {
          start: {
            line: 285,
            column: 36
          },
          end: {
            line: 289,
            column: 9
          }
        },
        line: 285
      },
      "62": {
        name: "useBrowserCache",
        decl: {
          start: {
            line: 296,
            column: 9
          },
          end: {
            line: 296,
            column: 24
          }
        },
        loc: {
          start: {
            line: 296,
            column: 37
          },
          end: {
            line: 308,
            column: 1
          }
        },
        line: 296
      },
      "63": {
        name: "(anonymous_63)",
        decl: {
          start: {
            line: 298,
            column: 19
          },
          end: {
            line: 298,
            column: 20
          }
        },
        loc: {
          start: {
            line: 298,
            column: 35
          },
          end: {
            line: 300,
            column: 5
          }
        },
        line: 298
      },
      "64": {
        name: "(anonymous_64)",
        decl: {
          start: {
            line: 301,
            column: 19
          },
          end: {
            line: 301,
            column: 20
          }
        },
        loc: {
          start: {
            line: 301,
            column: 31
          },
          end: {
            line: 303,
            column: 5
          }
        },
        line: 301
      },
      "65": {
        name: "(anonymous_65)",
        decl: {
          start: {
            line: 304,
            column: 22
          },
          end: {
            line: 304,
            column: 23
          }
        },
        loc: {
          start: {
            line: 304,
            column: 34
          },
          end: {
            line: 306,
            column: 5
          }
        },
        line: 304
      },
      "66": {
        name: "(anonymous_66)",
        decl: {
          start: {
            line: 312,
            column: 16
          },
          end: {
            line: 312,
            column: 17
          }
        },
        loc: {
          start: {
            line: 312,
            column: 28
          },
          end: {
            line: 317,
            column: 5
          }
        },
        line: 312
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 16
          },
          end: {
            line: 11,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 17
          },
          end: {
            line: 3,
            column: 21
          }
        }, {
          start: {
            line: 3,
            column: 25
          },
          end: {
            line: 3,
            column: 39
          }
        }, {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 11,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 35
          },
          end: {
            line: 4,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 56
          },
          end: {
            line: 4,
            column: 61
          }
        }, {
          start: {
            line: 4,
            column: 64
          },
          end: {
            line: 4,
            column: 109
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 5,
            column: 16
          },
          end: {
            line: 5,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 16
          },
          end: {
            line: 5,
            column: 17
          }
        }, {
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 5,
            column: 33
          }
        }],
        line: 5
      },
      "3": {
        loc: {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 8,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 8,
            column: 46
          },
          end: {
            line: 8,
            column: 67
          }
        }, {
          start: {
            line: 8,
            column: 70
          },
          end: {
            line: 8,
            column: 115
          }
        }],
        line: 8
      },
      "4": {
        loc: {
          start: {
            line: 9,
            column: 51
          },
          end: {
            line: 9,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 9,
            column: 51
          },
          end: {
            line: 9,
            column: 61
          }
        }, {
          start: {
            line: 9,
            column: 65
          },
          end: {
            line: 9,
            column: 67
          }
        }],
        line: 9
      },
      "5": {
        loc: {
          start: {
            line: 12,
            column: 18
          },
          end: {
            line: 38,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 19
          },
          end: {
            line: 12,
            column: 23
          }
        }, {
          start: {
            line: 12,
            column: 27
          },
          end: {
            line: 12,
            column: 43
          }
        }, {
          start: {
            line: 12,
            column: 48
          },
          end: {
            line: 38,
            column: 1
          }
        }],
        line: 12
      },
      "6": {
        loc: {
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 13,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 13,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 13
      },
      "7": {
        loc: {
          start: {
            line: 13,
            column: 134
          },
          end: {
            line: 13,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 167
          },
          end: {
            line: 13,
            column: 175
          }
        }, {
          start: {
            line: 13,
            column: 178
          },
          end: {
            line: 13,
            column: 184
          }
        }],
        line: 13
      },
      "8": {
        loc: {
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 14,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 14,
            column: 102
          }
        }, {
          start: {
            line: 14,
            column: 107
          },
          end: {
            line: 14,
            column: 155
          }
        }],
        line: 14
      },
      "9": {
        loc: {
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 15
          },
          end: {
            line: 18,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 15
          },
          end: {
            line: 18,
            column: 16
          }
        }, {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 44
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 33
          }
        }, {
          start: {
            line: 18,
            column: 38
          },
          end: {
            line: 18,
            column: 43
          }
        }],
        line: 18
      },
      "12": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "13": {
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 19,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 19,
            column: 24
          }
        }, {
          start: {
            line: 19,
            column: 29
          },
          end: {
            line: 19,
            column: 125
          }
        }, {
          start: {
            line: 19,
            column: 130
          },
          end: {
            line: 19,
            column: 158
          }
        }],
        line: 19
      },
      "14": {
        loc: {
          start: {
            line: 19,
            column: 33
          },
          end: {
            line: 19,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 45
          },
          end: {
            line: 19,
            column: 56
          }
        }, {
          start: {
            line: 19,
            column: 59
          },
          end: {
            line: 19,
            column: 125
          }
        }],
        line: 19
      },
      "15": {
        loc: {
          start: {
            line: 19,
            column: 59
          },
          end: {
            line: 19,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 67
          },
          end: {
            line: 19,
            column: 116
          }
        }, {
          start: {
            line: 19,
            column: 119
          },
          end: {
            line: 19,
            column: 125
          }
        }],
        line: 19
      },
      "16": {
        loc: {
          start: {
            line: 19,
            column: 67
          },
          end: {
            line: 19,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 67
          },
          end: {
            line: 19,
            column: 77
          }
        }, {
          start: {
            line: 19,
            column: 82
          },
          end: {
            line: 19,
            column: 115
          }
        }],
        line: 19
      },
      "17": {
        loc: {
          start: {
            line: 19,
            column: 82
          },
          end: {
            line: 19,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 83
          },
          end: {
            line: 19,
            column: 98
          }
        }, {
          start: {
            line: 19,
            column: 103
          },
          end: {
            line: 19,
            column: 112
          }
        }],
        line: 19
      },
      "18": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 20,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 20,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "19": {
        loc: {
          start: {
            line: 21,
            column: 12
          },
          end: {
            line: 33,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 24
          },
          end: {
            line: 22,
            column: 46
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 72
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 65
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 32,
            column: 43
          }
        }],
        line: 21
      },
      "20": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "21": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 74
          }
        }, {
          start: {
            line: 27,
            column: 79
          },
          end: {
            line: 27,
            column: 90
          }
        }, {
          start: {
            line: 27,
            column: 94
          },
          end: {
            line: 27,
            column: 105
          }
        }],
        line: 27
      },
      "22": {
        loc: {
          start: {
            line: 27,
            column: 42
          },
          end: {
            line: 27,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 42
          },
          end: {
            line: 27,
            column: 54
          }
        }, {
          start: {
            line: 27,
            column: 58
          },
          end: {
            line: 27,
            column: 73
          }
        }],
        line: 27
      },
      "23": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "24": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 40
          },
          end: {
            line: 28,
            column: 42
          }
        }, {
          start: {
            line: 28,
            column: 47
          },
          end: {
            line: 28,
            column: 59
          }
        }, {
          start: {
            line: 28,
            column: 63
          },
          end: {
            line: 28,
            column: 75
          }
        }],
        line: 28
      },
      "25": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "26": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 35
          }
        }, {
          start: {
            line: 29,
            column: 39
          },
          end: {
            line: 29,
            column: 53
          }
        }],
        line: 29
      },
      "27": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "28": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 25
          }
        }, {
          start: {
            line: 30,
            column: 29
          },
          end: {
            line: 30,
            column: 43
          }
        }],
        line: 30
      },
      "29": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "30": {
        loc: {
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 36,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 36,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "31": {
        loc: {
          start: {
            line: 36,
            column: 52
          },
          end: {
            line: 36,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 36,
            column: 60
          },
          end: {
            line: 36,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 68
          },
          end: {
            line: 36,
            column: 74
          }
        }],
        line: 36
      },
      "32": {
        loc: {
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 45,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 45,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "33": {
        loc: {
          start: {
            line: 47,
            column: 26
          },
          end: {
            line: 47,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 26
          },
          end: {
            line: 47,
            column: 37
          }
        }, {
          start: {
            line: 47,
            column: 41
          },
          end: {
            line: 47,
            column: 54
          }
        }],
        line: 47
      },
      "34": {
        loc: {
          start: {
            line: 48,
            column: 23
          },
          end: {
            line: 48,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 23
          },
          end: {
            line: 48,
            column: 38
          }
        }, {
          start: {
            line: 48,
            column: 42
          },
          end: {
            line: 48,
            column: 46
          }
        }],
        line: 48
      },
      "35": {
        loc: {
          start: {
            line: 52,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "36": {
        loc: {
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 60,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 60,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "37": {
        loc: {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 59,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 59,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "38": {
        loc: {
          start: {
            line: 65,
            column: 17
          },
          end: {
            line: 65,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 65,
            column: 17
          },
          end: {
            line: 65,
            column: 20
          }
        }, {
          start: {
            line: 65,
            column: 24
          },
          end: {
            line: 65,
            column: 39
          }
        }],
        line: 65
      },
      "39": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 73,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 73,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "40": {
        loc: {
          start: {
            line: 75,
            column: 8
          },
          end: {
            line: 78,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 8
          },
          end: {
            line: 78,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "41": {
        loc: {
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 98,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 98,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "42": {
        loc: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 55
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "43": {
        loc: {
          start: {
            line: 146,
            column: 16
          },
          end: {
            line: 166,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 147,
            column: 20
          },
          end: {
            line: 153,
            column: 37
          }
        }, {
          start: {
            line: 154,
            column: 20
          },
          end: {
            line: 156,
            column: 69
          }
        }, {
          start: {
            line: 157,
            column: 20
          },
          end: {
            line: 160,
            column: 54
          }
        }, {
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 164,
            column: 38
          }
        }, {
          start: {
            line: 165,
            column: 20
          },
          end: {
            line: 165,
            column: 50
          }
        }],
        line: 146
      },
      "44": {
        loc: {
          start: {
            line: 150,
            column: 24
          },
          end: {
            line: 152,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 24
          },
          end: {
            line: 152,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "45": {
        loc: {
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 59
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 59
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "46": {
        loc: {
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 230,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 230,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "47": {
        loc: {
          start: {
            line: 231,
            column: 8
          },
          end: {
            line: 232,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 8
          },
          end: {
            line: 232,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 231
      },
      "48": {
        loc: {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 246,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 246,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "49": {
        loc: {
          start: {
            line: 249,
            column: 12
          },
          end: {
            line: 250,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 12
          },
          end: {
            line: 250,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      },
      "50": {
        loc: {
          start: {
            line: 252,
            column: 12
          },
          end: {
            line: 255,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 12
          },
          end: {
            line: 255,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "51": {
        loc: {
          start: {
            line: 264,
            column: 8
          },
          end: {
            line: 265,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 264,
            column: 8
          },
          end: {
            line: 265,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 264
      },
      "52": {
        loc: {
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 271,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 271,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "53": {
        loc: {
          start: {
            line: 274,
            column: 12
          },
          end: {
            line: 276,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 12
          },
          end: {
            line: 276,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "54": {
        loc: {
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 283,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 283,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "55": {
        loc: {
          start: {
            line: 286,
            column: 12
          },
          end: {
            line: 288,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 286,
            column: 12
          },
          end: {
            line: 288,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 286
      },
      "56": {
        loc: {
          start: {
            line: 297,
            column: 4
          },
          end: {
            line: 297,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 297,
            column: 4
          },
          end: {
            line: 297,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 297
      },
      "57": {
        loc: {
          start: {
            line: 310,
            column: 0
          },
          end: {
            line: 318,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 0
          },
          end: {
            line: 318,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 310
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0, 0, 0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/cache.ts",
      mappings: ";AAAA,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyH7C,8BAyBC;AA+ID,0CAcC;AAlSD;IAKE,qBAAY,OAA0B;QAA1B,wBAAA,EAAA,YAA0B;QAJ9B,UAAK,GAAG,IAAI,GAAG,EAA2B,CAAC;QAKjD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,oBAAoB;QACpE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;IACzC,CAAC;IAED,yBAAG,GAAH,UAAO,GAAW,EAAE,IAAO,EAAE,GAAY;QACvC,0CAA0C;QAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEf,mDAAmD;YACnD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACpC,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;gBACjD,IAAI,SAAS,EAAE,CAAC;oBACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAM,KAAK,GAAkB;YAC3B,IAAI,MAAA;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,UAAU;SAC5B,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,yBAAG,GAAH,UAAO,GAAW;QAChB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC,IAAS,CAAC;IACzB,CAAC;IAED,yBAAG,GAAH,UAAI,GAAW;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC;IAChC,CAAC;IAED,4BAAM,GAAN,UAAO,GAAW;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,2BAAK,GAAL;QACE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED,yBAAyB;IACzB,6BAAO,GAAP;QAAA,iBAOC;QANC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,EAAY;gBAAX,GAAG,QAAA,EAAE,KAAK,QAAA;YACnD,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBACtC,KAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,uBAAuB;IACvB,8BAAQ,GAAR;QACE,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;IAED,wCAAwC;IACxC,6BAAO,GAAP,UAAW,GAAW;QACpB,OAAO,IAAI,CAAC,GAAG,CAAI,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,6BAAO,GAAP,UAAW,GAAW,EAAE,IAAO,EAAE,GAAY;QAC3C,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAC3B,CAAC;IACH,kBAAC;AAAD,CAAC,AAxFD,IAwFC;AA8NQ,kCAAW;AA5NpB,kDAAkD;AACrC,QAAA,QAAQ,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,YAAY;AAC9E,QAAA,SAAS,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,aAAa;AACjF,QAAA,WAAW,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS;AAE5F,uBAAuB;AACV,QAAA,SAAS,GAAG;IACvB,IAAI,EAAE,UAAC,EAAU,IAAK,OAAA,eAAQ,EAAE,CAAE,EAAZ,CAAY;IAClC,WAAW,EAAE,UAAC,EAAU,IAAK,OAAA,uBAAgB,EAAE,CAAE,EAApB,CAAoB;IACjD,UAAU,EAAE,UAAC,MAAc,IAAK,OAAA,qBAAc,MAAM,CAAE,EAAtB,CAAsB;IACtD,WAAW,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;IACrC,iBAAiB,EAAE,UAAC,OAAe,IAAK,OAAA,6BAAsB,OAAO,CAAE,EAA/B,CAA+B;IACvE,YAAY,EAAE,UAAC,MAAc,IAAK,OAAA,mBAAY,MAAM,CAAE,EAApB,CAAoB;IACtD,WAAW,EAAE,UAAC,MAAc,IAAK,OAAA,uBAAgB,MAAM,CAAE,EAAxB,CAAwB;IACzD,eAAe,EAAE,UAAC,MAAc,IAAK,OAAA,0BAAmB,MAAM,CAAE,EAA3B,CAA2B;CACjE,CAAC;AAEF,6DAA6D;AAC7D,SAAgB,SAAS,CACvB,EAAK,EACL,YAAgD,EAChD,KAA6B,EAC7B,GAAY;IAJd,iBAyBC;IAtBC,sBAAA,EAAA,QAAqB,gBAAQ;IAG7B,OAAO,CAAC;QAAO,cAAsB;aAAtB,UAAsB,EAAtB,qBAAsB,EAAtB,IAAsB;YAAtB,yBAAsB;;;;;;;wBAC7B,GAAG,GAAG,YAAY,eAAI,IAAI,CAAC,CAAC;wBAG5B,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBAC9B,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,sBAAO,MAAM,EAAC;wBAChB,CAAC;;;;wBAIgB,qBAAM,EAAE,eAAI,IAAI,GAAC;;wBAA1B,MAAM,GAAG,SAAiB;wBAChC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;wBAC5B,sBAAO,MAAM,EAAC;;;wBAEd,qBAAqB;wBACrB,MAAM,OAAK,CAAC;;;;;KAEf,CAAM,CAAC;AACV,CAAC;AAED,mDAAmD;AACtC,QAAA,eAAe,GAAG;IAC7B,kBAAkB;IAClB,OAAO,EAAE,SAAS,CAChB,UAAO,EAAU;;YACf,uDAAuD;YACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;;SACrD,EACD,UAAC,EAAU,IAAK,OAAA,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,EAAlB,CAAkB,EAClC,iBAAS,CACV;IAED,qBAAqB;IACrB,cAAc,EAAE,SAAS,CACvB;;YACE,+DAA+D;YAC/D,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;;SAC7D,EACD,cAAM,OAAA,iBAAS,CAAC,WAAW,EAAE,EAAvB,CAAuB,EAC7B,mBAAW,CACZ;IAED,wCAAwC;IACxC,oBAAoB,EAAE,SAAS,CAC7B,UAAO,OAA4B;;YACjC,qEAAqE;YACrE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;;SACnE,EACD,UAAC,OAA4B,IAAK,OAAA,iBAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAApD,CAAoD,EACtF,gBAAQ,CACT;CACF,CAAC;AAEF,+BAA+B;AAClB,QAAA,iBAAiB,GAAG;IAC/B,iCAAiC;IACjC,cAAc,EAAE,UAAC,MAAc;QAC7B,iBAAS,CAAC,MAAM,CAAC,iBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QACzC,iBAAS,CAAC,MAAM,CAAC,iBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QAChD,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9C,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAChD,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/C,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,8BAA8B;IAC9B,oBAAoB,EAAE,UAAC,MAAc;QACnC,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9C,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,uCAAuC;IAC7F,CAAC;IAED,+BAA+B;IAC/B,kBAAkB,EAAE,UAAC,MAAc;QACjC,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAChD,gBAAQ,CAAC,MAAM,CAAC,iBAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,qCAAqC;IAC3F,CAAC;IAED,8CAA8C;IAC9C,gBAAgB,EAAE;QAChB,mBAAW,CAAC,MAAM,CAAC,iBAAS,CAAC,WAAW,EAAE,CAAC,CAAC;QAC5C,6CAA6C;QAC7C,mBAAW,CAAC,KAAK,EAAE,CAAC,CAAC,0CAA0C;IACjE,CAAC;CACF,CAAC;AAEF,4DAA4D;AAC5D;IAGE,sBAAY,MAA+B;QAA/B,uBAAA,EAAA,uBAA+B;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,0BAAG,GAAH,UAAO,GAAW,EAAE,IAAO,EAAE,KAA6B;QAA7B,sBAAA,EAAA,QAAgB,CAAC,GAAG,EAAE,GAAG,IAAI;QACxD,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO;QAE1C,IAAM,KAAK,GAAG;YACZ,IAAI,MAAA;YACJ,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK;SAC5B,CAAC;QAEF,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,0BAAG,GAAH,UAAO,GAAW;QAChB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,IAAI,CAAC;QAE/C,IAAI,CAAC;YACH,IAAM,IAAI,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEvB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC/B,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC,IAAS,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,6BAAM,GAAN,UAAO,GAAW;QAChB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO;QAC1C,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED,4BAAK,GAAL;QAAA,iBASC;QARC,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO;QAE1C,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG;YACd,IAAI,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,2BAA2B;IAC3B,8BAAO,GAAP;QAAA,iBASC;QARC,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO;QAE1C,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG;YACd,IAAI,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChC,KAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,KAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,mCAAmC;YAC7E,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACH,mBAAC;AAAD,CAAC,AAtED,IAsEC;AAtEY,oCAAY;AAwEZ,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAE/C,iCAAiC;AACjC,SAAgB,eAAe,CAAI,GAAW,EAAE,KAA6B;IAA7B,sBAAA,EAAA,QAAgB,CAAC,GAAG,EAAE,GAAG,IAAI;IAC3E,IAAM,QAAQ,GAAG,UAAC,IAAO;QACvB,oBAAY,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,IAAM,QAAQ,GAAG;QACf,OAAO,oBAAY,CAAC,GAAG,CAAI,GAAG,CAAC,CAAC;IAClC,CAAC,CAAC;IAEF,IAAM,WAAW,GAAG;QAClB,oBAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,OAAO,EAAE,QAAQ,UAAA,EAAE,QAAQ,UAAA,EAAE,WAAW,aAAA,EAAE,CAAC;AAC7C,CAAC;AAED,mBAAmB;AACnB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAClC,kDAAkD;IAClD,WAAW,CAAC;QACV,gBAAQ,CAAC,OAAO,EAAE,CAAC;QACnB,iBAAS,CAAC,OAAO,EAAE,CAAC;QACpB,mBAAW,CAAC,OAAO,EAAE,CAAC;QACtB,oBAAY,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AACrB,CAAC;AAED,6DAA6D;AAChD,QAAA,KAAK,GAAG,gBAAQ,CAAC;AAE9B,qCAAqC;AACxB,QAAA,YAAY,GAAG,gBAAQ,CAAC;AAKrC,kBAAe;IACb,QAAQ,kBAAA;IACR,SAAS,mBAAA;IACT,WAAW,qBAAA;IACX,YAAY,sBAAA;IACZ,SAAS,WAAA;IACT,SAAS,mBAAA;IACT,iBAAiB,2BAAA;IACjB,YAAY,cAAA;IACZ,WAAW,aAAA;CACZ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/cache.ts"],
      sourcesContent: ["// Caching utilities for improved performance\n\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n  ttl: number; // Time to live in milliseconds\n}\n\ninterface CacheOptions {\n  ttl?: number; // Default TTL in milliseconds\n  maxSize?: number; // Maximum number of entries\n}\n\nclass MemoryCache {\n  private cache = new Map<string, CacheEntry<any>>();\n  private defaultTTL: number;\n  private maxSize: number;\n\n  constructor(options: CacheOptions = {}) {\n    this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes default\n    this.maxSize = options.maxSize || 1000;\n  }\n\n  set<T>(key: string, data: T, ttl?: number): void {\n    // Remove expired entries if cache is full\n    if (this.cache.size >= this.maxSize) {\n      this.cleanup();\n      \n      // If still full after cleanup, remove oldest entry\n      if (this.cache.size >= this.maxSize) {\n        const oldestKey = this.cache.keys().next().value;\n        if (oldestKey) {\n          this.cache.delete(oldestKey);\n        }\n      }\n    }\n\n    const entry: CacheEntry<T> = {\n      data,\n      timestamp: Date.now(),\n      ttl: ttl || this.defaultTTL\n    };\n\n    this.cache.set(key, entry);\n  }\n\n  get<T>(key: string): T | null {\n    const entry = this.cache.get(key);\n    \n    if (!entry) {\n      return null;\n    }\n\n    // Check if entry has expired\n    if (Date.now() - entry.timestamp > entry.ttl) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data as T;\n  }\n\n  has(key: string): boolean {\n    return this.get(key) !== null;\n  }\n\n  delete(key: string): boolean {\n    return this.cache.delete(key);\n  }\n\n  clear(): void {\n    this.cache.clear();\n  }\n\n  // Remove expired entries\n  cleanup(): void {\n    const now = Date.now();\n    Array.from(this.cache.entries()).forEach(([key, entry]) => {\n      if (now - entry.timestamp > entry.ttl) {\n        this.cache.delete(key);\n      }\n    });\n  }\n\n  // Get cache statistics\n  getStats() {\n    return {\n      size: this.cache.size,\n      maxSize: this.maxSize,\n      defaultTTL: this.defaultTTL\n    };\n  }\n\n  // JSON-specific methods for convenience\n  getJSON<T>(key: string): T | null {\n    return this.get<T>(key);\n  }\n\n  setJSON<T>(key: string, data: T, ttl?: number): void {\n    this.set(key, data, ttl);\n  }\n}\n\n// Create cache instances for different data types\nexport const apiCache = new MemoryCache({ ttl: 5 * 60 * 1000, maxSize: 500 }); // 5 minutes\nexport const userCache = new MemoryCache({ ttl: 15 * 60 * 1000, maxSize: 100 }); // 15 minutes\nexport const staticCache = new MemoryCache({ ttl: 60 * 60 * 1000, maxSize: 200 }); // 1 hour\n\n// Cache key generators\nexport const cacheKeys = {\n  user: (id: string) => `user:${id}`,\n  userProfile: (id: string) => `user:profile:${id}`,\n  assessment: (userId: string) => `assessment:${userId}`,\n  careerPaths: () => 'career-paths:all',\n  learningResources: (filters: string) => `learning-resources:${filters}`,\n  userProgress: (userId: string) => `progress:${userId}`,\n  freedomFund: (userId: string) => `freedom-fund:${userId}`,\n  recommendations: (userId: string) => `recommendations:${userId}`,\n};\n\n// Higher-order function to add caching to any async function\nexport function withCache<T extends (...args: any[]) => Promise<any>>(\n  fn: T,\n  keyGenerator: (...args: Parameters<T>) => string,\n  cache: MemoryCache = apiCache,\n  ttl?: number\n): T {\n  return (async (...args: Parameters<T>) => {\n    const key = keyGenerator(...args);\n    \n    // Try to get from cache first\n    const cached = cache.get(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // If not in cache, execute function and cache result\n    try {\n      const result = await fn(...args);\n      cache.set(key, result, ttl);\n      return result;\n    } catch (error) {\n      // Don't cache errors\n      throw error;\n    }\n  }) as T;\n}\n\n// Specific caching functions for common operations\nexport const cachedFunctions = {\n  // Cache user data\n  getUser: withCache(\n    async (id: string) => {\n      // This would be replaced with actual user service call\n      throw new Error('Replace with actual user service');\n    },\n    (id: string) => cacheKeys.user(id),\n    userCache\n  ),\n\n  // Cache career paths\n  getCareerPaths: withCache(\n    async () => {\n      // This would be replaced with actual career paths service call\n      throw new Error('Replace with actual career paths service');\n    },\n    () => cacheKeys.careerPaths(),\n    staticCache\n  ),\n\n  // Cache learning resources with filters\n  getLearningResources: withCache(\n    async (filters: Record<string, any>) => {\n      // This would be replaced with actual learning resources service call\n      throw new Error('Replace with actual learning resources service');\n    },\n    (filters: Record<string, any>) => cacheKeys.learningResources(JSON.stringify(filters)),\n    apiCache\n  ),\n};\n\n// Cache invalidation utilities\nexport const cacheInvalidation = {\n  // Invalidate user-related caches\n  invalidateUser: (userId: string) => {\n    userCache.delete(cacheKeys.user(userId));\n    userCache.delete(cacheKeys.userProfile(userId));\n    apiCache.delete(cacheKeys.assessment(userId));\n    apiCache.delete(cacheKeys.userProgress(userId));\n    apiCache.delete(cacheKeys.freedomFund(userId));\n    apiCache.delete(cacheKeys.recommendations(userId));\n  },\n\n  // Invalidate assessment cache\n  invalidateAssessment: (userId: string) => {\n    apiCache.delete(cacheKeys.assessment(userId));\n    apiCache.delete(cacheKeys.recommendations(userId)); // Recommendations depend on assessment\n  },\n\n  // Invalidate learning progress\n  invalidateProgress: (userId: string) => {\n    apiCache.delete(cacheKeys.userProgress(userId));\n    apiCache.delete(cacheKeys.recommendations(userId)); // Recommendations depend on progress\n  },\n\n  // Invalidate static data (when admin updates)\n  invalidateStatic: () => {\n    staticCache.delete(cacheKeys.careerPaths());\n    // Clear all learning resources cache entries\n    staticCache.clear(); // Simple approach, could be more granular\n  },\n};\n\n// Browser-side caching using localStorage (with expiration)\nexport class BrowserCache {\n  private prefix: string;\n\n  constructor(prefix: string = 'faafo_cache_') {\n    this.prefix = prefix;\n  }\n\n  set<T>(key: string, data: T, ttlMs: number = 5 * 60 * 1000): void {\n    if (typeof window === 'undefined') return;\n\n    const entry = {\n      data,\n      expires: Date.now() + ttlMs\n    };\n\n    try {\n      localStorage.setItem(this.prefix + key, JSON.stringify(entry));\n    } catch (error) {\n      console.warn('Failed to set browser cache:', error);\n    }\n  }\n\n  get<T>(key: string): T | null {\n    if (typeof window === 'undefined') return null;\n\n    try {\n      const item = localStorage.getItem(this.prefix + key);\n      if (!item) return null;\n\n      const entry = JSON.parse(item);\n      \n      if (Date.now() > entry.expires) {\n        localStorage.removeItem(this.prefix + key);\n        return null;\n      }\n\n      return entry.data as T;\n    } catch (error) {\n      console.warn('Failed to get browser cache:', error);\n      return null;\n    }\n  }\n\n  delete(key: string): void {\n    if (typeof window === 'undefined') return;\n    localStorage.removeItem(this.prefix + key);\n  }\n\n  clear(): void {\n    if (typeof window === 'undefined') return;\n    \n    const keys = Object.keys(localStorage);\n    keys.forEach(key => {\n      if (key.startsWith(this.prefix)) {\n        localStorage.removeItem(key);\n      }\n    });\n  }\n\n  // Clean up expired entries\n  cleanup(): void {\n    if (typeof window === 'undefined') return;\n\n    const keys = Object.keys(localStorage);\n    keys.forEach(key => {\n      if (key.startsWith(this.prefix)) {\n        this.get(key.replace(this.prefix, '')); // This will remove expired entries\n      }\n    });\n  }\n}\n\nexport const browserCache = new BrowserCache();\n\n// React hook for browser caching\nexport function useBrowserCache<T>(key: string, ttlMs: number = 5 * 60 * 1000) {\n  const setCache = (data: T) => {\n    browserCache.set(key, data, ttlMs);\n  };\n\n  const getCache = (): T | null => {\n    return browserCache.get<T>(key);\n  };\n\n  const deleteCache = () => {\n    browserCache.delete(key);\n  };\n\n  return { setCache, getCache, deleteCache };\n}\n\n// Periodic cleanup\nif (typeof window !== 'undefined') {\n  // Clean up expired cache entries every 10 minutes\n  setInterval(() => {\n    apiCache.cleanup();\n    userCache.cleanup();\n    staticCache.cleanup();\n    browserCache.cleanup();\n  }, 10 * 60 * 1000);\n}\n\n// Export a general cache instance for backward compatibility\nexport const cache = apiCache;\n\n// Export cacheService for API routes\nexport const cacheService = apiCache;\n\n// Export MemoryCache as named export\nexport { MemoryCache };\n\nexport default {\n  apiCache,\n  userCache,\n  staticCache,\n  browserCache,\n  withCache,\n  cacheKeys,\n  cacheInvalidation,\n  BrowserCache,\n  MemoryCache\n};\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5d993ef694eb31470638fb5af27b0564f108e624"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_oaf5a5bfi = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_oaf5a5bfi();
var __awaiter =
/* istanbul ignore next */
(cov_oaf5a5bfi().s[0]++,
/* istanbul ignore next */
(cov_oaf5a5bfi().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_oaf5a5bfi().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_oaf5a5bfi().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_oaf5a5bfi().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[1]++;
    cov_oaf5a5bfi().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[2]++;
      cov_oaf5a5bfi().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_oaf5a5bfi().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_oaf5a5bfi().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[4]++;
      cov_oaf5a5bfi().s[4]++;
      try {
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[5]++;
      cov_oaf5a5bfi().s[7]++;
      try {
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[6]++;
      cov_oaf5a5bfi().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_oaf5a5bfi().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_oaf5a5bfi().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_oaf5a5bfi().s[12]++,
/* istanbul ignore next */
(cov_oaf5a5bfi().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_oaf5a5bfi().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_oaf5a5bfi().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_oaf5a5bfi().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_oaf5a5bfi().f[8]++;
        cov_oaf5a5bfi().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_oaf5a5bfi().b[6][0]++;
          cov_oaf5a5bfi().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_oaf5a5bfi().b[6][1]++;
        }
        cov_oaf5a5bfi().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_oaf5a5bfi().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_oaf5a5bfi().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[9]++;
    cov_oaf5a5bfi().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[10]++;
    cov_oaf5a5bfi().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[11]++;
      cov_oaf5a5bfi().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[12]++;
    cov_oaf5a5bfi().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[9][0]++;
      cov_oaf5a5bfi().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[9][1]++;
    }
    cov_oaf5a5bfi().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[25]++;
      try {
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[15][0]++,
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[16][1]++,
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_oaf5a5bfi().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_oaf5a5bfi().b[12][0]++;
          cov_oaf5a5bfi().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_oaf5a5bfi().b[12][1]++;
        }
        cov_oaf5a5bfi().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_oaf5a5bfi().b[18][0]++;
          cov_oaf5a5bfi().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_oaf5a5bfi().b[18][1]++;
        }
        cov_oaf5a5bfi().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[19][1]++;
            cov_oaf5a5bfi().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[19][2]++;
            cov_oaf5a5bfi().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[19][3]++;
            cov_oaf5a5bfi().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[19][4]++;
            cov_oaf5a5bfi().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[19][5]++;
            cov_oaf5a5bfi().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_oaf5a5bfi().b[20][0]++;
              cov_oaf5a5bfi().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_oaf5a5bfi().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_oaf5a5bfi().b[20][1]++;
            }
            cov_oaf5a5bfi().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_oaf5a5bfi().b[23][0]++;
              cov_oaf5a5bfi().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_oaf5a5bfi().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_oaf5a5bfi().b[23][1]++;
            }
            cov_oaf5a5bfi().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_oaf5a5bfi().b[25][0]++;
              cov_oaf5a5bfi().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_oaf5a5bfi().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_oaf5a5bfi().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_oaf5a5bfi().b[25][1]++;
            }
            cov_oaf5a5bfi().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_oaf5a5bfi().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_oaf5a5bfi().b[27][0]++;
              cov_oaf5a5bfi().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_oaf5a5bfi().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_oaf5a5bfi().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_oaf5a5bfi().b[27][1]++;
            }
            cov_oaf5a5bfi().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_oaf5a5bfi().b[29][0]++;
              cov_oaf5a5bfi().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_oaf5a5bfi().b[29][1]++;
            }
            cov_oaf5a5bfi().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[30][0]++;
      cov_oaf5a5bfi().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[30][1]++;
    }
    cov_oaf5a5bfi().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_oaf5a5bfi().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_oaf5a5bfi().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_oaf5a5bfi().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_oaf5a5bfi().s[68]++;
exports.MemoryCache = exports.cacheService = exports.cache = exports.browserCache = exports.BrowserCache = exports.cacheInvalidation = exports.cachedFunctions = exports.cacheKeys = exports.staticCache = exports.userCache = exports.apiCache = void 0;
/* istanbul ignore next */
cov_oaf5a5bfi().s[69]++;
exports.withCache = withCache;
/* istanbul ignore next */
cov_oaf5a5bfi().s[70]++;
exports.useBrowserCache = useBrowserCache;
var MemoryCache =
/* istanbul ignore next */
(/** @class */cov_oaf5a5bfi().s[71]++, function () {
  /* istanbul ignore next */
  cov_oaf5a5bfi().f[13]++;
  function MemoryCache(options) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[14]++;
    cov_oaf5a5bfi().s[72]++;
    if (options === void 0) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[32][0]++;
      cov_oaf5a5bfi().s[73]++;
      options = {};
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[32][1]++;
    }
    cov_oaf5a5bfi().s[74]++;
    this.cache = new Map();
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[75]++;
    this.defaultTTL =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[33][0]++, options.ttl) ||
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[33][1]++, 5 * 60 * 1000); // 5 minutes default
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[76]++;
    this.maxSize =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[34][0]++, options.maxSize) ||
    /* istanbul ignore next */
    (cov_oaf5a5bfi().b[34][1]++, 1000);
  }
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[77]++;
  MemoryCache.prototype.set = function (key, data, ttl) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[15]++;
    cov_oaf5a5bfi().s[78]++;
    // Remove expired entries if cache is full
    if (this.cache.size >= this.maxSize) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[35][0]++;
      cov_oaf5a5bfi().s[79]++;
      this.cleanup();
      // If still full after cleanup, remove oldest entry
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[80]++;
      if (this.cache.size >= this.maxSize) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().b[36][0]++;
        var oldestKey =
        /* istanbul ignore next */
        (cov_oaf5a5bfi().s[81]++, this.cache.keys().next().value);
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[82]++;
        if (oldestKey) {
          /* istanbul ignore next */
          cov_oaf5a5bfi().b[37][0]++;
          cov_oaf5a5bfi().s[83]++;
          this.cache.delete(oldestKey);
        } else
        /* istanbul ignore next */
        {
          cov_oaf5a5bfi().b[37][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_oaf5a5bfi().b[36][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[35][1]++;
    }
    var entry =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[84]++, {
      data: data,
      timestamp: Date.now(),
      ttl:
      /* istanbul ignore next */
      (cov_oaf5a5bfi().b[38][0]++, ttl) ||
      /* istanbul ignore next */
      (cov_oaf5a5bfi().b[38][1]++, this.defaultTTL)
    });
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[85]++;
    this.cache.set(key, entry);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[86]++;
  MemoryCache.prototype.get = function (key) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[16]++;
    var entry =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[87]++, this.cache.get(key));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[88]++;
    if (!entry) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[39][0]++;
      cov_oaf5a5bfi().s[89]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[39][1]++;
    }
    // Check if entry has expired
    cov_oaf5a5bfi().s[90]++;
    if (Date.now() - entry.timestamp > entry.ttl) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[40][0]++;
      cov_oaf5a5bfi().s[91]++;
      this.cache.delete(key);
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[92]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[40][1]++;
    }
    cov_oaf5a5bfi().s[93]++;
    return entry.data;
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[94]++;
  MemoryCache.prototype.has = function (key) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[17]++;
    cov_oaf5a5bfi().s[95]++;
    return this.get(key) !== null;
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[96]++;
  MemoryCache.prototype.delete = function (key) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[18]++;
    cov_oaf5a5bfi().s[97]++;
    return this.cache.delete(key);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[98]++;
  MemoryCache.prototype.clear = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[19]++;
    cov_oaf5a5bfi().s[99]++;
    this.cache.clear();
  };
  // Remove expired entries
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[100]++;
  MemoryCache.prototype.cleanup = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[20]++;
    var _this =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[101]++, this);
    var now =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[102]++, Date.now());
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[103]++;
    Array.from(this.cache.entries()).forEach(function (_a) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[21]++;
      var key =
        /* istanbul ignore next */
        (cov_oaf5a5bfi().s[104]++, _a[0]),
        entry =
        /* istanbul ignore next */
        (cov_oaf5a5bfi().s[105]++, _a[1]);
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[106]++;
      if (now - entry.timestamp > entry.ttl) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().b[41][0]++;
        cov_oaf5a5bfi().s[107]++;
        _this.cache.delete(key);
      } else
      /* istanbul ignore next */
      {
        cov_oaf5a5bfi().b[41][1]++;
      }
    });
  };
  // Get cache statistics
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[108]++;
  MemoryCache.prototype.getStats = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[22]++;
    cov_oaf5a5bfi().s[109]++;
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      defaultTTL: this.defaultTTL
    };
  };
  // JSON-specific methods for convenience
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[110]++;
  MemoryCache.prototype.getJSON = function (key) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[23]++;
    cov_oaf5a5bfi().s[111]++;
    return this.get(key);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[112]++;
  MemoryCache.prototype.setJSON = function (key, data, ttl) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[24]++;
    cov_oaf5a5bfi().s[113]++;
    this.set(key, data, ttl);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[114]++;
  return MemoryCache;
}());
/* istanbul ignore next */
cov_oaf5a5bfi().s[115]++;
exports.MemoryCache = MemoryCache;
// Create cache instances for different data types
/* istanbul ignore next */
cov_oaf5a5bfi().s[116]++;
exports.apiCache = new MemoryCache({
  ttl: 5 * 60 * 1000,
  maxSize: 500
}); // 5 minutes
/* istanbul ignore next */
cov_oaf5a5bfi().s[117]++;
exports.userCache = new MemoryCache({
  ttl: 15 * 60 * 1000,
  maxSize: 100
}); // 15 minutes
/* istanbul ignore next */
cov_oaf5a5bfi().s[118]++;
exports.staticCache = new MemoryCache({
  ttl: 60 * 60 * 1000,
  maxSize: 200
}); // 1 hour
// Cache key generators
/* istanbul ignore next */
cov_oaf5a5bfi().s[119]++;
exports.cacheKeys = {
  user: function (id) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[25]++;
    cov_oaf5a5bfi().s[120]++;
    return "user:".concat(id);
  },
  userProfile: function (id) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[26]++;
    cov_oaf5a5bfi().s[121]++;
    return "user:profile:".concat(id);
  },
  assessment: function (userId) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[27]++;
    cov_oaf5a5bfi().s[122]++;
    return "assessment:".concat(userId);
  },
  careerPaths: function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[28]++;
    cov_oaf5a5bfi().s[123]++;
    return 'career-paths:all';
  },
  learningResources: function (filters) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[29]++;
    cov_oaf5a5bfi().s[124]++;
    return "learning-resources:".concat(filters);
  },
  userProgress: function (userId) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[30]++;
    cov_oaf5a5bfi().s[125]++;
    return "progress:".concat(userId);
  },
  freedomFund: function (userId) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[31]++;
    cov_oaf5a5bfi().s[126]++;
    return "freedom-fund:".concat(userId);
  },
  recommendations: function (userId) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[32]++;
    cov_oaf5a5bfi().s[127]++;
    return "recommendations:".concat(userId);
  }
};
// Higher-order function to add caching to any async function
function withCache(fn, keyGenerator, cache, ttl) {
  /* istanbul ignore next */
  cov_oaf5a5bfi().f[33]++;
  var _this =
  /* istanbul ignore next */
  (cov_oaf5a5bfi().s[128]++, this);
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[129]++;
  if (cache === void 0) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().b[42][0]++;
    cov_oaf5a5bfi().s[130]++;
    cache = exports.apiCache;
  } else
  /* istanbul ignore next */
  {
    cov_oaf5a5bfi().b[42][1]++;
  }
  cov_oaf5a5bfi().s[131]++;
  return function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[34]++;
    var args =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[132]++, []);
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[133]++;
    for (var _i =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[134]++, 0); _i < arguments.length; _i++) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[135]++;
      args[_i] = arguments[_i];
    }
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[136]++;
    return __awaiter(_this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[35]++;
      var key, cached, result, error_1;
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[137]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().f[36]++;
        cov_oaf5a5bfi().s[138]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[43][0]++;
            cov_oaf5a5bfi().s[139]++;
            key = keyGenerator.apply(void 0, args);
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[140]++;
            cached = cache.get(key);
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[141]++;
            if (cached !== null) {
              /* istanbul ignore next */
              cov_oaf5a5bfi().b[44][0]++;
              cov_oaf5a5bfi().s[142]++;
              return [2 /*return*/, cached];
            } else
            /* istanbul ignore next */
            {
              cov_oaf5a5bfi().b[44][1]++;
            }
            cov_oaf5a5bfi().s[143]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[43][1]++;
            cov_oaf5a5bfi().s[144]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[145]++;
            return [4 /*yield*/, fn.apply(void 0, args)];
          case 2:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[43][2]++;
            cov_oaf5a5bfi().s[146]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[147]++;
            cache.set(key, result, ttl);
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[148]++;
            return [2 /*return*/, result];
          case 3:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[43][3]++;
            cov_oaf5a5bfi().s[149]++;
            error_1 = _a.sent();
            // Don't cache errors
            /* istanbul ignore next */
            cov_oaf5a5bfi().s[150]++;
            throw error_1;
          case 4:
            /* istanbul ignore next */
            cov_oaf5a5bfi().b[43][4]++;
            cov_oaf5a5bfi().s[151]++;
            return [2 /*return*/];
        }
      });
    });
  };
}
// Specific caching functions for common operations
/* istanbul ignore next */
cov_oaf5a5bfi().s[152]++;
exports.cachedFunctions = {
  // Cache user data
  getUser: withCache(function (id) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[37]++;
    cov_oaf5a5bfi().s[153]++;
    return __awaiter(void 0, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[38]++;
      cov_oaf5a5bfi().s[154]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().f[39]++;
        cov_oaf5a5bfi().s[155]++;
        // This would be replaced with actual user service call
        throw new Error('Replace with actual user service');
      });
    });
  }, function (id) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[40]++;
    cov_oaf5a5bfi().s[156]++;
    return exports.cacheKeys.user(id);
  }, exports.userCache),
  // Cache career paths
  getCareerPaths: withCache(function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[41]++;
    cov_oaf5a5bfi().s[157]++;
    return __awaiter(void 0, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[42]++;
      cov_oaf5a5bfi().s[158]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().f[43]++;
        cov_oaf5a5bfi().s[159]++;
        // This would be replaced with actual career paths service call
        throw new Error('Replace with actual career paths service');
      });
    });
  }, function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[44]++;
    cov_oaf5a5bfi().s[160]++;
    return exports.cacheKeys.careerPaths();
  }, exports.staticCache),
  // Cache learning resources with filters
  getLearningResources: withCache(function (filters) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[45]++;
    cov_oaf5a5bfi().s[161]++;
    return __awaiter(void 0, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[46]++;
      cov_oaf5a5bfi().s[162]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().f[47]++;
        cov_oaf5a5bfi().s[163]++;
        // This would be replaced with actual learning resources service call
        throw new Error('Replace with actual learning resources service');
      });
    });
  }, function (filters) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[48]++;
    cov_oaf5a5bfi().s[164]++;
    return exports.cacheKeys.learningResources(JSON.stringify(filters));
  }, exports.apiCache)
};
// Cache invalidation utilities
/* istanbul ignore next */
cov_oaf5a5bfi().s[165]++;
exports.cacheInvalidation = {
  // Invalidate user-related caches
  invalidateUser: function (userId) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[49]++;
    cov_oaf5a5bfi().s[166]++;
    exports.userCache.delete(exports.cacheKeys.user(userId));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[167]++;
    exports.userCache.delete(exports.cacheKeys.userProfile(userId));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[168]++;
    exports.apiCache.delete(exports.cacheKeys.assessment(userId));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[169]++;
    exports.apiCache.delete(exports.cacheKeys.userProgress(userId));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[170]++;
    exports.apiCache.delete(exports.cacheKeys.freedomFund(userId));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[171]++;
    exports.apiCache.delete(exports.cacheKeys.recommendations(userId));
  },
  // Invalidate assessment cache
  invalidateAssessment: function (userId) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[50]++;
    cov_oaf5a5bfi().s[172]++;
    exports.apiCache.delete(exports.cacheKeys.assessment(userId));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[173]++;
    exports.apiCache.delete(exports.cacheKeys.recommendations(userId)); // Recommendations depend on assessment
  },
  // Invalidate learning progress
  invalidateProgress: function (userId) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[51]++;
    cov_oaf5a5bfi().s[174]++;
    exports.apiCache.delete(exports.cacheKeys.userProgress(userId));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[175]++;
    exports.apiCache.delete(exports.cacheKeys.recommendations(userId)); // Recommendations depend on progress
  },
  // Invalidate static data (when admin updates)
  invalidateStatic: function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[52]++;
    cov_oaf5a5bfi().s[176]++;
    exports.staticCache.delete(exports.cacheKeys.careerPaths());
    // Clear all learning resources cache entries
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[177]++;
    exports.staticCache.clear(); // Simple approach, could be more granular
  }
};
// Browser-side caching using localStorage (with expiration)
var BrowserCache =
/* istanbul ignore next */
(/** @class */cov_oaf5a5bfi().s[178]++, function () {
  /* istanbul ignore next */
  cov_oaf5a5bfi().f[53]++;
  function BrowserCache(prefix) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[54]++;
    cov_oaf5a5bfi().s[179]++;
    if (prefix === void 0) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[45][0]++;
      cov_oaf5a5bfi().s[180]++;
      prefix = 'faafo_cache_';
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[45][1]++;
    }
    cov_oaf5a5bfi().s[181]++;
    this.prefix = prefix;
  }
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[182]++;
  BrowserCache.prototype.set = function (key, data, ttlMs) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[55]++;
    cov_oaf5a5bfi().s[183]++;
    if (ttlMs === void 0) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[46][0]++;
      cov_oaf5a5bfi().s[184]++;
      ttlMs = 5 * 60 * 1000;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[46][1]++;
    }
    cov_oaf5a5bfi().s[185]++;
    if (typeof window === 'undefined') {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[47][0]++;
      cov_oaf5a5bfi().s[186]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[47][1]++;
    }
    var entry =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[187]++, {
      data: data,
      expires: Date.now() + ttlMs
    });
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[188]++;
    try {
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[189]++;
      localStorage.setItem(this.prefix + key, JSON.stringify(entry));
    } catch (error) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[190]++;
      console.warn('Failed to set browser cache:', error);
    }
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[191]++;
  BrowserCache.prototype.get = function (key) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[56]++;
    cov_oaf5a5bfi().s[192]++;
    if (typeof window === 'undefined') {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[48][0]++;
      cov_oaf5a5bfi().s[193]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[48][1]++;
    }
    cov_oaf5a5bfi().s[194]++;
    try {
      var item =
      /* istanbul ignore next */
      (cov_oaf5a5bfi().s[195]++, localStorage.getItem(this.prefix + key));
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[196]++;
      if (!item) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().b[49][0]++;
        cov_oaf5a5bfi().s[197]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_oaf5a5bfi().b[49][1]++;
      }
      var entry =
      /* istanbul ignore next */
      (cov_oaf5a5bfi().s[198]++, JSON.parse(item));
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[199]++;
      if (Date.now() > entry.expires) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().b[50][0]++;
        cov_oaf5a5bfi().s[200]++;
        localStorage.removeItem(this.prefix + key);
        /* istanbul ignore next */
        cov_oaf5a5bfi().s[201]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_oaf5a5bfi().b[50][1]++;
      }
      cov_oaf5a5bfi().s[202]++;
      return entry.data;
    } catch (error) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[203]++;
      console.warn('Failed to get browser cache:', error);
      /* istanbul ignore next */
      cov_oaf5a5bfi().s[204]++;
      return null;
    }
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[205]++;
  BrowserCache.prototype.delete = function (key) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[57]++;
    cov_oaf5a5bfi().s[206]++;
    if (typeof window === 'undefined') {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[51][0]++;
      cov_oaf5a5bfi().s[207]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[51][1]++;
    }
    cov_oaf5a5bfi().s[208]++;
    localStorage.removeItem(this.prefix + key);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[209]++;
  BrowserCache.prototype.clear = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[58]++;
    var _this =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[210]++, this);
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[211]++;
    if (typeof window === 'undefined') {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[52][0]++;
      cov_oaf5a5bfi().s[212]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[52][1]++;
    }
    var keys =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[213]++, Object.keys(localStorage));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[214]++;
    keys.forEach(function (key) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[59]++;
      cov_oaf5a5bfi().s[215]++;
      if (key.startsWith(_this.prefix)) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().b[53][0]++;
        cov_oaf5a5bfi().s[216]++;
        localStorage.removeItem(key);
      } else
      /* istanbul ignore next */
      {
        cov_oaf5a5bfi().b[53][1]++;
      }
    });
  };
  // Clean up expired entries
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[217]++;
  BrowserCache.prototype.cleanup = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[60]++;
    var _this =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[218]++, this);
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[219]++;
    if (typeof window === 'undefined') {
      /* istanbul ignore next */
      cov_oaf5a5bfi().b[54][0]++;
      cov_oaf5a5bfi().s[220]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_oaf5a5bfi().b[54][1]++;
    }
    var keys =
    /* istanbul ignore next */
    (cov_oaf5a5bfi().s[221]++, Object.keys(localStorage));
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[222]++;
    keys.forEach(function (key) {
      /* istanbul ignore next */
      cov_oaf5a5bfi().f[61]++;
      cov_oaf5a5bfi().s[223]++;
      if (key.startsWith(_this.prefix)) {
        /* istanbul ignore next */
        cov_oaf5a5bfi().b[55][0]++;
        cov_oaf5a5bfi().s[224]++;
        _this.get(key.replace(_this.prefix, '')); // This will remove expired entries
      } else
      /* istanbul ignore next */
      {
        cov_oaf5a5bfi().b[55][1]++;
      }
    });
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[225]++;
  return BrowserCache;
}());
/* istanbul ignore next */
cov_oaf5a5bfi().s[226]++;
exports.BrowserCache = BrowserCache;
/* istanbul ignore next */
cov_oaf5a5bfi().s[227]++;
exports.browserCache = new BrowserCache();
// React hook for browser caching
function useBrowserCache(key, ttlMs) {
  /* istanbul ignore next */
  cov_oaf5a5bfi().f[62]++;
  cov_oaf5a5bfi().s[228]++;
  if (ttlMs === void 0) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().b[56][0]++;
    cov_oaf5a5bfi().s[229]++;
    ttlMs = 5 * 60 * 1000;
  } else
  /* istanbul ignore next */
  {
    cov_oaf5a5bfi().b[56][1]++;
  }
  cov_oaf5a5bfi().s[230]++;
  var setCache = function (data) {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[63]++;
    cov_oaf5a5bfi().s[231]++;
    exports.browserCache.set(key, data, ttlMs);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[232]++;
  var getCache = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[64]++;
    cov_oaf5a5bfi().s[233]++;
    return exports.browserCache.get(key);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[234]++;
  var deleteCache = function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[65]++;
    cov_oaf5a5bfi().s[235]++;
    exports.browserCache.delete(key);
  };
  /* istanbul ignore next */
  cov_oaf5a5bfi().s[236]++;
  return {
    setCache: setCache,
    getCache: getCache,
    deleteCache: deleteCache
  };
}
// Periodic cleanup
/* istanbul ignore next */
cov_oaf5a5bfi().s[237]++;
if (typeof window !== 'undefined') {
  /* istanbul ignore next */
  cov_oaf5a5bfi().b[57][0]++;
  cov_oaf5a5bfi().s[238]++;
  // Clean up expired cache entries every 10 minutes
  setInterval(function () {
    /* istanbul ignore next */
    cov_oaf5a5bfi().f[66]++;
    cov_oaf5a5bfi().s[239]++;
    exports.apiCache.cleanup();
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[240]++;
    exports.userCache.cleanup();
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[241]++;
    exports.staticCache.cleanup();
    /* istanbul ignore next */
    cov_oaf5a5bfi().s[242]++;
    exports.browserCache.cleanup();
  }, 10 * 60 * 1000);
} else
/* istanbul ignore next */
{
  cov_oaf5a5bfi().b[57][1]++;
}
// Export a general cache instance for backward compatibility
cov_oaf5a5bfi().s[243]++;
exports.cache = exports.apiCache;
// Export cacheService for API routes
/* istanbul ignore next */
cov_oaf5a5bfi().s[244]++;
exports.cacheService = exports.apiCache;
/* istanbul ignore next */
cov_oaf5a5bfi().s[245]++;
exports.default = {
  apiCache: exports.apiCache,
  userCache: exports.userCache,
  staticCache: exports.staticCache,
  browserCache: exports.browserCache,
  withCache: withCache,
  cacheKeys: exports.cacheKeys,
  cacheInvalidation: exports.cacheInvalidation,
  BrowserCache: BrowserCache,
  MemoryCache: MemoryCache
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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