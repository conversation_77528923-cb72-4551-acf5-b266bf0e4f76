{"version": 3, "names": ["exports", "rateLimit", "cov_lbx2ehtpw", "s", "getRateLimitConfig", "withRateLimit", "generateCSRFToken", "validateCSRFToken", "sanitizeInput", "sanitizeEmail", "escapeSQLString", "escapeHtml", "validatePasswordStrength", "generateSecureSessionId", "isValidSessionId", "server_1", "require", "security_storage_1", "__importDefault", "config", "f", "_this", "windowMs", "maxRequests", "_a", "message", "b", "request", "__awaiter", "Promise", "enableDevRateLimit", "process", "env", "ENABLE_DEV_RATE_LIMIT", "isDevelopment", "NODE_ENV", "console", "log", "devMultiplier", "adjustedMaxRequests", "default", "checkRateLimit", "result", "sent", "allowed", "dev<PERSON><PERSON><PERSON>", "NextResponse", "json", "error", "status", "headers", "toString", "remaining", "resetTime", "Math", "ceil", "Date", "now", "response", "next", "set", "getClientIP", "forwarded", "get", "split", "trim", "realIP", "cfConnectingIP", "ip", "rateLimitConfigs", "auth", "api", "contact", "signup", "devRateLimitConfigs", "type", "handler", "rateLimitResponse", "crypto", "randomUUID", "token", "sessionToken", "input", "replace", "slice", "email", "toLowerCase", "str", "unsafe", "password", "errors", "length", "push", "test", "<PERSON><PERSON><PERSON><PERSON>", "sessionId"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rateLimit.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport securityStorage from './security-storage';\n\nexport interface RateLimitConfig {\n  windowMs: number;\n  maxRequests: number;\n  message?: string;\n  keyGenerator?: (request: NextRequest) => string;\n}\n\nexport function rateLimit(config: RateLimitConfig) {\n  const {\n    windowMs,\n    maxRequests,\n    message = 'Too many requests'\n  } = config;\n\n  return async (request: NextRequest): Promise<NextResponse | null> => {\n    // Check if rate limiting should be enabled in development\n    const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';\n    const isDevelopment = process.env.NODE_ENV === 'development';\n\n    // Skip rate limiting only if in development AND dev rate limiting is disabled\n    if (isDevelopment && !enableDevRateLimit) {\n      console.log('🚫 Rate limiting disabled in development mode');\n      return null; // Continue to next middleware/handler\n    }\n\n    // Apply more lenient limits in development for testing\n    const devMultiplier = isDevelopment ? 2 : 1; // Double the limits in dev\n    const adjustedMaxRequests = maxRequests * devMultiplier;\n\n    const result = await securityStorage.checkRateLimit(request, windowMs, adjustedMaxRequests);\n\n    if (!result.allowed) {\n      const devWarning = isDevelopment ? ' (Development Mode - Limits Relaxed)' : '';\n      return NextResponse.json(\n        { error: message + devWarning },\n        {\n          status: 429,\n          headers: {\n            'X-RateLimit-Limit': adjustedMaxRequests.toString(),\n            'X-RateLimit-Remaining': result.remaining.toString(),\n            'X-RateLimit-Reset': result.resetTime.toString(),\n            'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),\n            'X-RateLimit-Dev-Mode': isDevelopment.toString()\n          }\n        }\n      );\n    }\n\n    // Add rate limit headers to successful responses\n    const response = NextResponse.next();\n    response.headers.set('X-RateLimit-Limit', adjustedMaxRequests.toString());\n    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());\n    response.headers.set('X-RateLimit-Reset', result.resetTime.toString());\n    if (isDevelopment) {\n      response.headers.set('X-RateLimit-Dev-Mode', 'true');\n    }\n\n    return null; // Continue to next middleware/handler\n  };\n}\n\nfunction getClientIP(request: NextRequest): string | null {\n  // Try various headers for client IP\n  const forwarded = request.headers.get('x-forwarded-for');\n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n\n  const realIP = request.headers.get('x-real-ip');\n  if (realIP) {\n    return realIP;\n  }\n\n  const cfConnectingIP = request.headers.get('cf-connecting-ip');\n  if (cfConnectingIP) {\n    return cfConnectingIP;\n  }\n\n  // Fallback to connection remote address (may not be available in all environments)\n  return (request as any).ip || null;\n}\n\n// Predefined rate limit configurations\nexport const rateLimitConfigs = {\n  auth: {\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    maxRequests: 5,\n    message: 'Too many authentication attempts. Please try again later.'\n  },\n  api: {\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    maxRequests: 100,\n    message: 'Too many API requests. Please try again later.'\n  },\n  contact: {\n    windowMs: 60 * 60 * 1000, // 1 hour\n    maxRequests: 3,\n    message: 'Too many contact form submissions. Please try again later.'\n  },\n  signup: {\n    windowMs: 60 * 60 * 1000, // 1 hour\n    maxRequests: 3,\n    message: 'Too many signup attempts. Please try again later.'\n  }\n} as const;\n\n// Development-friendly rate limit configurations (more lenient for testing)\nexport const devRateLimitConfigs = {\n  auth: {\n    windowMs: 5 * 60 * 1000, // 5 minutes (shorter window)\n    maxRequests: 10, // Double the requests\n    message: 'Too many authentication attempts. Please try again later. (Dev Mode)'\n  },\n  api: {\n    windowMs: 10 * 60 * 1000, // 10 minutes (shorter window)\n    maxRequests: 200, // Double the requests\n    message: 'Too many API requests. Please try again later. (Dev Mode)'\n  },\n  contact: {\n    windowMs: 30 * 60 * 1000, // 30 minutes (shorter window)\n    maxRequests: 6, // Double the requests\n    message: 'Too many contact form submissions. Please try again later. (Dev Mode)'\n  },\n  signup: {\n    windowMs: 30 * 60 * 1000, // 30 minutes (shorter window)\n    maxRequests: 6, // Double the requests\n    message: 'Too many signup attempts. Please try again later. (Dev Mode)'\n  }\n} as const;\n\n// Helper to get appropriate config based on environment\nexport function getRateLimitConfig(type: keyof typeof rateLimitConfigs): RateLimitConfig {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';\n\n  if (isDevelopment && enableDevRateLimit) {\n    return devRateLimitConfigs[type];\n  }\n\n  return rateLimitConfigs[type];\n}\n\n// Helper function to apply rate limiting to API routes\nexport async function withRateLimit(\n  request: NextRequest,\n  config: RateLimitConfig,\n  handler: () => Promise<NextResponse>\n): Promise<NextResponse> {\n  // Check if rate limiting should be enabled in development\n  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';\n  const isDevelopment = process.env.NODE_ENV === 'development';\n\n  // Skip rate limiting only if in development AND dev rate limiting is disabled\n  if (isDevelopment && !enableDevRateLimit) {\n    console.log('🚫 Rate limiting disabled for API route in development mode');\n    return handler();\n  }\n\n  const rateLimitResponse = await rateLimit(config)(request);\n\n  if (rateLimitResponse) {\n    return rateLimitResponse;\n  }\n\n  return handler();\n}\n\n// CSRF Protection\nexport function generateCSRFToken(): string {\n  return crypto.randomUUID();\n}\n\nexport function validateCSRFToken(token: string, sessionToken: string): boolean {\n  // In a real implementation, you'd store CSRF tokens in session/database\n  // For now, we'll use a simple validation\n  return token === sessionToken;\n}\n\n// Input sanitization helpers\nexport function sanitizeInput(input: string): string {\n  return input\n    .replace(/[<>]/g, '') // Remove potential HTML tags\n    .trim()\n    .slice(0, 1000); // Limit length\n}\n\nexport function sanitizeEmail(email: string): string {\n  return email.toLowerCase().trim();\n}\n\n// SQL injection prevention (Prisma handles this, but good to have)\nexport function escapeSQLString(str: string): string {\n  return str.replace(/'/g, \"''\");\n}\n\n// XSS prevention helpers\nexport function escapeHtml(unsafe: string): string {\n  return unsafe\n    .replace(/&/g, \"&amp;\")\n    .replace(/</g, \"&lt;\")\n    .replace(/>/g, \"&gt;\")\n    .replace(/\"/g, \"&quot;\")\n    .replace(/'/g, \"&#039;\");\n}\n\n// Password strength validation\nexport function validatePasswordStrength(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[@$!%*?&]/.test(password)) {\n    errors.push('Password must contain at least one special character (@$!%*?&)');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\n// Session security helpers\nexport function generateSecureSessionId(): string {\n  return crypto.randomUUID() + '-' + Date.now().toString(36);\n}\n\nexport function isValidSessionId(sessionId: string): boolean {\n  // Basic validation for session ID format\n  return /^[a-f0-9-]+$/i.test(sessionId) && sessionId.length > 20;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUAA,OAAA,CAAAC,SAAA,GAAAA,SAAA;AAoDC;AAAAC,aAAA,GAAAC,CAAA;AAwEDH,OAAA,CAAAI,kBAAA,GAAAA,kBAAA;AASC;AAAAF,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAK,aAAA,GAAAA,aAAA;AAsBC;AAAAH,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAM,iBAAA,GAAAA,iBAAA;AAEC;AAAAJ,aAAA,GAAAC,CAAA;AAEDH,OAAA,CAAAO,iBAAA,GAAAA,iBAAA;AAIC;AAAAL,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAQ,aAAA,GAAAA,aAAA;AAKC;AAAAN,aAAA,GAAAC,CAAA;AAEDH,OAAA,CAAAS,aAAA,GAAAA,aAAA;AAEC;AAAAP,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAU,eAAA,GAAAA,eAAA;AAEC;AAAAR,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAW,UAAA,GAAAA,UAAA;AAOC;AAAAT,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAY,wBAAA,GAAAA,wBAAA;AA8BC;AAAAV,aAAA,GAAAC,CAAA;AAGDH,OAAA,CAAAa,uBAAA,GAAAA,uBAAA;AAEC;AAAAX,aAAA,GAAAC,CAAA;AAEDH,OAAA,CAAAc,gBAAA,GAAAA,gBAAA;AAtPA,IAAAC,QAAA;AAAA;AAAA,CAAAb,aAAA,GAAAC,CAAA,QAAAa,OAAA;AACA,IAAAC,kBAAA;AAAA;AAAA,CAAAf,aAAA,GAAAC,CAAA,QAAAe,eAAA,CAAAF,OAAA;AASA,SAAgBf,SAASA,CAACkB,MAAuB;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAjD,IAAAC,KAAA;EAAA;EAAA,CAAAnB,aAAA,GAAAC,CAAA;EAEI,IAAAmB,QAAQ;IAAA;IAAA,CAAApB,aAAA,GAAAC,CAAA,QAGNgB,MAAM,CAAAG,QAHA;IACRC,WAAW;IAAA;IAAA,CAAArB,aAAA,GAAAC,CAAA,QAETgB,MAAM,CAAAI,WAFG;IACXC,EAAA;IAAA;IAAA,CAAAtB,aAAA,GAAAC,CAAA,QACEgB,MAAM,CAAAM,OADqB;IAA7BA,OAAO;IAAA;IAAA,CAAAvB,aAAA,GAAAC,CAAA,QAAAqB,EAAA;IAAA;IAAA,CAAAtB,aAAA,GAAAwB,CAAA,WAAG,mBAAmB;IAAA;IAAA,CAAAxB,aAAA,GAAAwB,CAAA,WAAAF,EAAA;EACpB;EAAAtB,aAAA,GAAAC,CAAA;EAEX,OAAO,UAAOwB,OAAoB;IAAA;IAAAzB,aAAA,GAAAkB,CAAA;IAAAlB,aAAA,GAAAC,CAAA;IAAA,OAAAyB,SAAA,CAAAP,KAAA,UAAGQ,OAAO;MAAA;MAAA3B,aAAA,GAAAkB,CAAA;;;;;;;;;;;;;YAEpCU,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,KAAK,MAAM;YAAC;YAAA/B,aAAA,GAAAC,CAAA;YAClE+B,aAAa,GAAGH,OAAO,CAACC,GAAG,CAACG,QAAQ,KAAK,aAAa;YAE5D;YAAA;YAAAjC,aAAA,GAAAC,CAAA;YACA;YAAI;YAAA,CAAAD,aAAA,GAAAwB,CAAA,WAAAQ,aAAa;YAAA;YAAA,CAAAhC,aAAA,GAAAwB,CAAA,WAAI,CAACI,kBAAkB,GAAE;cAAA;cAAA5B,aAAA,GAAAwB,CAAA;cAAAxB,aAAA,GAAAC,CAAA;cACxCiC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;cAAC;cAAAnC,aAAA,GAAAC,CAAA;cAC7D,sBAAO,IAAI,EAAC,CAAC;YACf,CAAC;YAAA;YAAA;cAAAD,aAAA,GAAAwB,CAAA;YAAA;YAAAxB,aAAA,GAAAC,CAAA;YAGKmC,aAAa,GAAGJ,aAAa;YAAA;YAAA,CAAAhC,aAAA,GAAAwB,CAAA,WAAG,CAAC;YAAA;YAAA,CAAAxB,aAAA,GAAAwB,CAAA,WAAG,CAAC;YAAC;YAAAxB,aAAA,GAAAC,CAAA;YACtCoC,mBAAmB,GAAGhB,WAAW,GAAGe,aAAa;YAAC;YAAApC,aAAA,GAAAC,CAAA;YAEzC,qBAAMc,kBAAA,CAAAuB,OAAe,CAACC,cAAc,CAACd,OAAO,EAAEL,QAAQ,EAAEiB,mBAAmB,CAAC;;;;;YAArFG,MAAM,GAAGlB,EAAA,CAAAmB,IAAA,EAA4E;YAAA;YAAAzC,aAAA,GAAAC,CAAA;YAE3F,IAAI,CAACuC,MAAM,CAACE,OAAO,EAAE;cAAA;cAAA1C,aAAA,GAAAwB,CAAA;cAAAxB,aAAA,GAAAC,CAAA;cACb0C,UAAU,GAAGX,aAAa;cAAA;cAAA,CAAAhC,aAAA,GAAAwB,CAAA,WAAG,sCAAsC;cAAA;cAAA,CAAAxB,aAAA,GAAAwB,CAAA,WAAG,EAAE;cAAC;cAAAxB,aAAA,GAAAC,CAAA;cAC/E,sBAAOY,QAAA,CAAA+B,YAAY,CAACC,IAAI,CACtB;gBAAEC,KAAK,EAAEvB,OAAO,GAAGoB;cAAU,CAAE,EAC/B;gBACEI,MAAM,EAAE,GAAG;gBACXC,OAAO,EAAE;kBACP,mBAAmB,EAAEX,mBAAmB,CAACY,QAAQ,EAAE;kBACnD,uBAAuB,EAAET,MAAM,CAACU,SAAS,CAACD,QAAQ,EAAE;kBACpD,mBAAmB,EAAET,MAAM,CAACW,SAAS,CAACF,QAAQ,EAAE;kBAChD,aAAa,EAAEG,IAAI,CAACC,IAAI,CAAC,CAACb,MAAM,CAACW,SAAS,GAAGG,IAAI,CAACC,GAAG,EAAE,IAAI,IAAI,CAAC,CAACN,QAAQ,EAAE;kBAC3E,sBAAsB,EAAEjB,aAAa,CAACiB,QAAQ;;eAEjD,CACF;YACH,CAAC;YAAA;YAAA;cAAAjD,aAAA,GAAAwB,CAAA;YAAA;YAAAxB,aAAA,GAAAC,CAAA;YAGKuD,QAAQ,GAAG3C,QAAA,CAAA+B,YAAY,CAACa,IAAI,EAAE;YAAC;YAAAzD,aAAA,GAAAC,CAAA;YACrCuD,QAAQ,CAACR,OAAO,CAACU,GAAG,CAAC,mBAAmB,EAAErB,mBAAmB,CAACY,QAAQ,EAAE,CAAC;YAAC;YAAAjD,aAAA,GAAAC,CAAA;YAC1EuD,QAAQ,CAACR,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAElB,MAAM,CAACU,SAAS,CAACD,QAAQ,EAAE,CAAC;YAAC;YAAAjD,aAAA,GAAAC,CAAA;YAC3EuD,QAAQ,CAACR,OAAO,CAACU,GAAG,CAAC,mBAAmB,EAAElB,MAAM,CAACW,SAAS,CAACF,QAAQ,EAAE,CAAC;YAAC;YAAAjD,aAAA,GAAAC,CAAA;YACvE,IAAI+B,aAAa,EAAE;cAAA;cAAAhC,aAAA,GAAAwB,CAAA;cAAAxB,aAAA,GAAAC,CAAA;cACjBuD,QAAQ,CAACR,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAE,MAAM,CAAC;YACtD,CAAC;YAAA;YAAA;cAAA1D,aAAA,GAAAwB,CAAA;YAAA;YAAAxB,aAAA,GAAAC,CAAA;YAED,sBAAO,IAAI;UAAE;;;;GACd;AACH;AAEA,SAAS0D,WAAWA,CAAClC,OAAoB;EAAA;EAAAzB,aAAA,GAAAkB,CAAA;EACvC;EACA,IAAM0C,SAAS;EAAA;EAAA,CAAA5D,aAAA,GAAAC,CAAA,SAAGwB,OAAO,CAACuB,OAAO,CAACa,GAAG,CAAC,iBAAiB,CAAC;EAAC;EAAA7D,aAAA,GAAAC,CAAA;EACzD,IAAI2D,SAAS,EAAE;IAAA;IAAA5D,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IACb,OAAO2D,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;EACvC,CAAC;EAAA;EAAA;IAAA/D,aAAA,GAAAwB,CAAA;EAAA;EAED,IAAMwC,MAAM;EAAA;EAAA,CAAAhE,aAAA,GAAAC,CAAA,SAAGwB,OAAO,CAACuB,OAAO,CAACa,GAAG,CAAC,WAAW,CAAC;EAAC;EAAA7D,aAAA,GAAAC,CAAA;EAChD,IAAI+D,MAAM,EAAE;IAAA;IAAAhE,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IACV,OAAO+D,MAAM;EACf,CAAC;EAAA;EAAA;IAAAhE,aAAA,GAAAwB,CAAA;EAAA;EAED,IAAMyC,cAAc;EAAA;EAAA,CAAAjE,aAAA,GAAAC,CAAA,SAAGwB,OAAO,CAACuB,OAAO,CAACa,GAAG,CAAC,kBAAkB,CAAC;EAAC;EAAA7D,aAAA,GAAAC,CAAA;EAC/D,IAAIgE,cAAc,EAAE;IAAA;IAAAjE,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IAClB,OAAOgE,cAAc;EACvB,CAAC;EAAA;EAAA;IAAAjE,aAAA,GAAAwB,CAAA;EAAA;EAED;EAAAxB,aAAA,GAAAC,CAAA;EACA,OAAQ,2BAAAD,aAAA,GAAAwB,CAAA,WAAAC,OAAe,CAACyC,EAAE;EAAA;EAAA,CAAAlE,aAAA,GAAAwB,CAAA,WAAI,IAAI;AACpC;AAEA;AAAA;AAAAxB,aAAA,GAAAC,CAAA;AACaH,OAAA,CAAAqE,gBAAgB,GAAG;EAC9BC,IAAI,EAAE;IACJhD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,WAAW,EAAE,CAAC;IACdE,OAAO,EAAE;GACV;EACD8C,GAAG,EAAE;IACHjD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,WAAW,EAAE,GAAG;IAChBE,OAAO,EAAE;GACV;EACD+C,OAAO,EAAE;IACPlD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,WAAW,EAAE,CAAC;IACdE,OAAO,EAAE;GACV;EACDgD,MAAM,EAAE;IACNnD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,WAAW,EAAE,CAAC;IACdE,OAAO,EAAE;;CAEH;AAEV;AAAA;AAAAvB,aAAA,GAAAC,CAAA;AACaH,OAAA,CAAA0E,mBAAmB,GAAG;EACjCJ,IAAI,EAAE;IACJhD,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IACzBC,WAAW,EAAE,EAAE;IAAE;IACjBE,OAAO,EAAE;GACV;EACD8C,GAAG,EAAE;IACHjD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,WAAW,EAAE,GAAG;IAAE;IAClBE,OAAO,EAAE;GACV;EACD+C,OAAO,EAAE;IACPlD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,WAAW,EAAE,CAAC;IAAE;IAChBE,OAAO,EAAE;GACV;EACDgD,MAAM,EAAE;IACNnD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,WAAW,EAAE,CAAC;IAAE;IAChBE,OAAO,EAAE;;CAEH;AAEV;AACA,SAAgBrB,kBAAkBA,CAACuE,IAAmC;EAAA;EAAAzE,aAAA,GAAAkB,CAAA;EACpE,IAAMc,aAAa;EAAA;EAAA,CAAAhC,aAAA,GAAAC,CAAA,SAAG4B,OAAO,CAACC,GAAG,CAACG,QAAQ,KAAK,aAAa;EAC5D,IAAML,kBAAkB;EAAA;EAAA,CAAA5B,aAAA,GAAAC,CAAA,SAAG4B,OAAO,CAACC,GAAG,CAACC,qBAAqB,KAAK,MAAM;EAAC;EAAA/B,aAAA,GAAAC,CAAA;EAExE;EAAI;EAAA,CAAAD,aAAA,GAAAwB,CAAA,WAAAQ,aAAa;EAAA;EAAA,CAAAhC,aAAA,GAAAwB,CAAA,WAAII,kBAAkB,GAAE;IAAA;IAAA5B,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IACvC,OAAOH,OAAA,CAAA0E,mBAAmB,CAACC,IAAI,CAAC;EAClC,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAwB,CAAA;EAAA;EAAAxB,aAAA,GAAAC,CAAA;EAED,OAAOH,OAAA,CAAAqE,gBAAgB,CAACM,IAAI,CAAC;AAC/B;AAEA;AACA,SAAsBtE,aAAaA,CACjCsB,OAAoB,EACpBR,MAAuB,EACvByD,OAAoC;EAAA;EAAA1E,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;iCACnC0B,OAAO;IAAA;IAAA3B,aAAA,GAAAkB,CAAA;;;;;;;;;;;;;UAEFU,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,KAAK,MAAM;UAAC;UAAA/B,aAAA,GAAAC,CAAA;UAClE+B,aAAa,GAAGH,OAAO,CAACC,GAAG,CAACG,QAAQ,KAAK,aAAa;UAE5D;UAAA;UAAAjC,aAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,aAAA,GAAAwB,CAAA,WAAAQ,aAAa;UAAA;UAAA,CAAAhC,aAAA,GAAAwB,CAAA,WAAI,CAACI,kBAAkB,GAAE;YAAA;YAAA5B,aAAA,GAAAwB,CAAA;YAAAxB,aAAA,GAAAC,CAAA;YACxCiC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;YAAC;YAAAnC,aAAA,GAAAC,CAAA;YAC3E,sBAAOyE,OAAO,EAAE;UAClB,CAAC;UAAA;UAAA;YAAA1E,aAAA,GAAAwB,CAAA;UAAA;UAAAxB,aAAA,GAAAC,CAAA;UAEyB,qBAAMF,SAAS,CAACkB,MAAM,CAAC,CAACQ,OAAO,CAAC;;;;;UAApDkD,iBAAiB,GAAGrD,EAAA,CAAAmB,IAAA,EAAgC;UAAA;UAAAzC,aAAA,GAAAC,CAAA;UAE1D,IAAI0E,iBAAiB,EAAE;YAAA;YAAA3E,aAAA,GAAAwB,CAAA;YAAAxB,aAAA,GAAAC,CAAA;YACrB,sBAAO0E,iBAAiB;UAC1B,CAAC;UAAA;UAAA;YAAA3E,aAAA,GAAAwB,CAAA;UAAA;UAAAxB,aAAA,GAAAC,CAAA;UAED,sBAAOyE,OAAO,EAAE;;;;;AAGlB;AACA,SAAgBtE,iBAAiBA,CAAA;EAAA;EAAAJ,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EAC/B,OAAO2E,MAAM,CAACC,UAAU,EAAE;AAC5B;AAEA,SAAgBxE,iBAAiBA,CAACyE,KAAa,EAAEC,YAAoB;EAAA;EAAA/E,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EACnE;EACA;EACA,OAAO6E,KAAK,KAAKC,YAAY;AAC/B;AAEA;AACA,SAAgBzE,aAAaA,CAAC0E,KAAa;EAAA;EAAAhF,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EACzC,OAAO+E,KAAK,CACTC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;EAAA,CACrBlB,IAAI,EAAE,CACNmB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACrB;AAEA,SAAgB3E,aAAaA,CAAC4E,KAAa;EAAA;EAAAnF,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EACzC,OAAOkF,KAAK,CAACC,WAAW,EAAE,CAACrB,IAAI,EAAE;AACnC;AAEA;AACA,SAAgBvD,eAAeA,CAAC6E,GAAW;EAAA;EAAArF,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EACzC,OAAOoF,GAAG,CAACJ,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;AAChC;AAEA;AACA,SAAgBxE,UAAUA,CAAC6E,MAAc;EAAA;EAAAtF,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EACvC,OAAOqF,MAAM,CACVL,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC5B;AAEA;AACA,SAAgBvE,wBAAwBA,CAAC6E,QAAgB;EAAA;EAAAvF,aAAA,GAAAkB,CAAA;EAIvD,IAAMsE,MAAM;EAAA;EAAA,CAAAxF,aAAA,GAAAC,CAAA,SAAa,EAAE;EAAC;EAAAD,aAAA,GAAAC,CAAA;EAE5B,IAAIsF,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IACvBuF,MAAM,CAACE,IAAI,CAAC,6CAA6C,CAAC;EAC5D,CAAC;EAAA;EAAA;IAAA1F,aAAA,GAAAwB,CAAA;EAAA;EAAAxB,aAAA,GAAAC,CAAA;EAED,IAAI,CAAC,OAAO,CAAC0F,IAAI,CAACJ,QAAQ,CAAC,EAAE;IAAA;IAAAvF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IAC3BuF,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;EACpE,CAAC;EAAA;EAAA;IAAA1F,aAAA,GAAAwB,CAAA;EAAA;EAAAxB,aAAA,GAAAC,CAAA;EAED,IAAI,CAAC,OAAO,CAAC0F,IAAI,CAACJ,QAAQ,CAAC,EAAE;IAAA;IAAAvF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IAC3BuF,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;EACpE,CAAC;EAAA;EAAA;IAAA1F,aAAA,GAAAwB,CAAA;EAAA;EAAAxB,aAAA,GAAAC,CAAA;EAED,IAAI,CAAC,IAAI,CAAC0F,IAAI,CAACJ,QAAQ,CAAC,EAAE;IAAA;IAAAvF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IACxBuF,MAAM,CAACE,IAAI,CAAC,2CAA2C,CAAC;EAC1D,CAAC;EAAA;EAAA;IAAA1F,aAAA,GAAAwB,CAAA;EAAA;EAAAxB,aAAA,GAAAC,CAAA;EAED,IAAI,CAAC,WAAW,CAAC0F,IAAI,CAACJ,QAAQ,CAAC,EAAE;IAAA;IAAAvF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAC,CAAA;IAC/BuF,MAAM,CAACE,IAAI,CAAC,gEAAgE,CAAC;EAC/E,CAAC;EAAA;EAAA;IAAA1F,aAAA,GAAAwB,CAAA;EAAA;EAAAxB,aAAA,GAAAC,CAAA;EAED,OAAO;IACL2F,OAAO,EAAEJ,MAAM,CAACC,MAAM,KAAK,CAAC;IAC5BD,MAAM,EAAAA;GACP;AACH;AAEA;AACA,SAAgB7E,uBAAuBA,CAAA;EAAA;EAAAX,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EACrC,OAAO2E,MAAM,CAACC,UAAU,EAAE,GAAG,GAAG,GAAGvB,IAAI,CAACC,GAAG,EAAE,CAACN,QAAQ,CAAC,EAAE,CAAC;AAC5D;AAEA,SAAgBrC,gBAAgBA,CAACiF,SAAiB;EAAA;EAAA7F,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAC,CAAA;EAChD;EACA,OAAO,2BAAAD,aAAA,GAAAwB,CAAA,0BAAe,CAACmE,IAAI,CAACE,SAAS,CAAC;EAAA;EAAA,CAAA7F,aAAA,GAAAwB,CAAA,WAAIqE,SAAS,CAACJ,MAAM,GAAG,EAAE;AACjE", "ignoreList": []}