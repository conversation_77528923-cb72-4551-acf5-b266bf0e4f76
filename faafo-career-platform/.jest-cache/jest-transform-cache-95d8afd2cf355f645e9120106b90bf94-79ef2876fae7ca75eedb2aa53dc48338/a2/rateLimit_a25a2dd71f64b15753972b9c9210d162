66bbdabb7112a7d8c4df520176f8ff7f
"use strict";

/* istanbul ignore next */
function cov_lbx2ehtpw() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rateLimit.ts";
  var hash = "789da6b2eef716a67b19d044bb944076871f2c1e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rateLimit.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 64
        }
      },
      "71": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 30
        }
      },
      "72": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 48
        }
      },
      "73": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 38
        }
      },
      "74": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 46,
          column: 46
        }
      },
      "75": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 46
        }
      },
      "76": {
        start: {
          line: 48,
          column: 0
        },
        end: {
          line: 48,
          column: 38
        }
      },
      "77": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 38
        }
      },
      "78": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 42
        }
      },
      "79": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 32
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 60
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 58
        }
      },
      "82": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 44
        }
      },
      "83": {
        start: {
          line: 55,
          column: 15
        },
        end: {
          line: 55,
          column: 37
        }
      },
      "84": {
        start: {
          line: 56,
          column: 25
        },
        end: {
          line: 56,
          column: 71
        }
      },
      "85": {
        start: {
          line: 58,
          column: 16
        },
        end: {
          line: 58,
          column: 20
        }
      },
      "86": {
        start: {
          line: 59,
          column: 19
        },
        end: {
          line: 59,
          column: 34
        }
      },
      "87": {
        start: {
          line: 59,
          column: 50
        },
        end: {
          line: 59,
          column: 68
        }
      },
      "88": {
        start: {
          line: 59,
          column: 75
        },
        end: {
          line: 59,
          column: 89
        }
      },
      "89": {
        start: {
          line: 59,
          column: 101
        },
        end: {
          line: 59,
          column: 141
        }
      },
      "90": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 100,
          column: 10
        }
      },
      "91": {
        start: {
          line: 60,
          column: 32
        },
        end: {
          line: 100,
          column: 7
        }
      },
      "92": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 99,
          column: 11
        }
      },
      "93": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 98,
          column: 13
        }
      },
      "94": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 65,
          column: 86
        }
      },
      "95": {
        start: {
          line: 66,
          column: 20
        },
        end: {
          line: 66,
          column: 75
        }
      },
      "96": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 71,
          column: 21
        }
      },
      "97": {
        start: {
          line: 69,
          column: 24
        },
        end: {
          line: 69,
          column: 85
        }
      },
      "98": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 70,
          column: 52
        }
      },
      "99": {
        start: {
          line: 72,
          column: 20
        },
        end: {
          line: 72,
          column: 58
        }
      },
      "100": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 70
        }
      },
      "101": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 124
        }
      },
      "102": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 39
        }
      },
      "103": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 89,
          column: 21
        }
      },
      "104": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 97
        }
      },
      "105": {
        start: {
          line: 79,
          column: 24
        },
        end: {
          line: 88,
          column: 32
        }
      },
      "106": {
        start: {
          line: 90,
          column: 20
        },
        end: {
          line: 90,
          column: 60
        }
      },
      "107": {
        start: {
          line: 91,
          column: 20
        },
        end: {
          line: 91,
          column: 94
        }
      },
      "108": {
        start: {
          line: 92,
          column: 20
        },
        end: {
          line: 92,
          column: 95
        }
      },
      "109": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 93,
          column: 91
        }
      },
      "110": {
        start: {
          line: 94,
          column: 20
        },
        end: {
          line: 96,
          column: 21
        }
      },
      "111": {
        start: {
          line: 95,
          column: 24
        },
        end: {
          line: 95,
          column: 77
        }
      },
      "112": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 97,
          column: 48
        }
      },
      "113": {
        start: {
          line: 104,
          column: 20
        },
        end: {
          line: 104,
          column: 58
        }
      },
      "114": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 107,
          column: 5
        }
      },
      "115": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 46
        }
      },
      "116": {
        start: {
          line: 108,
          column: 17
        },
        end: {
          line: 108,
          column: 49
        }
      },
      "117": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 111,
          column: 5
        }
      },
      "118": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 110,
          column: 22
        }
      },
      "119": {
        start: {
          line: 112,
          column: 25
        },
        end: {
          line: 112,
          column: 64
        }
      },
      "120": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 115,
          column: 5
        }
      },
      "121": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 30
        }
      },
      "122": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 117,
          column: 30
        }
      },
      "123": {
        start: {
          line: 120,
          column: 0
        },
        end: {
          line: 141,
          column: 2
        }
      },
      "124": {
        start: {
          line: 143,
          column: 0
        },
        end: {
          line: 164,
          column: 2
        }
      },
      "125": {
        start: {
          line: 167,
          column: 24
        },
        end: {
          line: 167,
          column: 62
        }
      },
      "126": {
        start: {
          line: 168,
          column: 29
        },
        end: {
          line: 168,
          column: 73
        }
      },
      "127": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 171,
          column: 5
        }
      },
      "128": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 170,
          column: 49
        }
      },
      "129": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 42
        }
      },
      "130": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 197,
          column: 7
        }
      },
      "131": {
        start: {
          line: 178,
          column: 8
        },
        end: {
          line: 196,
          column: 11
        }
      },
      "132": {
        start: {
          line: 179,
          column: 12
        },
        end: {
          line: 195,
          column: 13
        }
      },
      "133": {
        start: {
          line: 181,
          column: 20
        },
        end: {
          line: 181,
          column: 86
        }
      },
      "134": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 182,
          column: 75
        }
      },
      "135": {
        start: {
          line: 184,
          column: 20
        },
        end: {
          line: 187,
          column: 21
        }
      },
      "136": {
        start: {
          line: 185,
          column: 24
        },
        end: {
          line: 185,
          column: 99
        }
      },
      "137": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 186,
          column: 57
        }
      },
      "138": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 188,
          column: 69
        }
      },
      "139": {
        start: {
          line: 190,
          column: 20
        },
        end: {
          line: 190,
          column: 50
        }
      },
      "140": {
        start: {
          line: 191,
          column: 20
        },
        end: {
          line: 193,
          column: 21
        }
      },
      "141": {
        start: {
          line: 192,
          column: 24
        },
        end: {
          line: 192,
          column: 65
        }
      },
      "142": {
        start: {
          line: 194,
          column: 20
        },
        end: {
          line: 194,
          column: 53
        }
      },
      "143": {
        start: {
          line: 201,
          column: 4
        },
        end: {
          line: 201,
          column: 31
        }
      },
      "144": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 206,
          column: 34
        }
      },
      "145": {
        start: {
          line: 210,
          column: 4
        },
        end: {
          line: 213,
          column: 24
        }
      },
      "146": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 216,
          column: 38
        }
      },
      "147": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 220,
          column: 35
        }
      },
      "148": {
        start: {
          line: 224,
          column: 4
        },
        end: {
          line: 229,
          column: 33
        }
      },
      "149": {
        start: {
          line: 233,
          column: 17
        },
        end: {
          line: 233,
          column: 19
        }
      },
      "150": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 236,
          column: 5
        }
      },
      "151": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 67
        }
      },
      "152": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 239,
          column: 5
        }
      },
      "153": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 238,
          column: 75
        }
      },
      "154": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 242,
          column: 5
        }
      },
      "155": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 241,
          column: 75
        }
      },
      "156": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 245,
          column: 5
        }
      },
      "157": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 244,
          column: 65
        }
      },
      "158": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 248,
          column: 5
        }
      },
      "159": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 86
        }
      },
      "160": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 252,
          column: 6
        }
      },
      "161": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 256,
          column: 63
        }
      },
      "162": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 260,
          column: 68
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "rateLimit",
        decl: {
          start: {
            line: 57,
            column: 9
          },
          end: {
            line: 57,
            column: 18
          }
        },
        loc: {
          start: {
            line: 57,
            column: 27
          },
          end: {
            line: 101,
            column: 1
          }
        },
        line: 57
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 60,
            column: 11
          },
          end: {
            line: 60,
            column: 12
          }
        },
        loc: {
          start: {
            line: 60,
            column: 30
          },
          end: {
            line: 100,
            column: 9
          }
        },
        line: 60
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 60,
            column: 73
          },
          end: {
            line: 60,
            column: 74
          }
        },
        loc: {
          start: {
            line: 60,
            column: 85
          },
          end: {
            line: 100,
            column: 5
          }
        },
        line: 60
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 62,
            column: 33
          },
          end: {
            line: 62,
            column: 34
          }
        },
        loc: {
          start: {
            line: 62,
            column: 47
          },
          end: {
            line: 99,
            column: 9
          }
        },
        line: 62
      },
      "18": {
        name: "getClientIP",
        decl: {
          start: {
            line: 102,
            column: 9
          },
          end: {
            line: 102,
            column: 20
          }
        },
        loc: {
          start: {
            line: 102,
            column: 30
          },
          end: {
            line: 118,
            column: 1
          }
        },
        line: 102
      },
      "19": {
        name: "getRateLimitConfig",
        decl: {
          start: {
            line: 166,
            column: 9
          },
          end: {
            line: 166,
            column: 27
          }
        },
        loc: {
          start: {
            line: 166,
            column: 34
          },
          end: {
            line: 173,
            column: 1
          }
        },
        line: 166
      },
      "20": {
        name: "withRateLimit",
        decl: {
          start: {
            line: 175,
            column: 9
          },
          end: {
            line: 175,
            column: 22
          }
        },
        loc: {
          start: {
            line: 175,
            column: 49
          },
          end: {
            line: 198,
            column: 1
          }
        },
        line: 175
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 176,
            column: 44
          },
          end: {
            line: 176,
            column: 45
          }
        },
        loc: {
          start: {
            line: 176,
            column: 56
          },
          end: {
            line: 197,
            column: 5
          }
        },
        line: 176
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 178,
            column: 33
          },
          end: {
            line: 178,
            column: 34
          }
        },
        loc: {
          start: {
            line: 178,
            column: 47
          },
          end: {
            line: 196,
            column: 9
          }
        },
        line: 178
      },
      "23": {
        name: "generateCSRFToken",
        decl: {
          start: {
            line: 200,
            column: 9
          },
          end: {
            line: 200,
            column: 26
          }
        },
        loc: {
          start: {
            line: 200,
            column: 29
          },
          end: {
            line: 202,
            column: 1
          }
        },
        line: 200
      },
      "24": {
        name: "validateCSRFToken",
        decl: {
          start: {
            line: 203,
            column: 9
          },
          end: {
            line: 203,
            column: 26
          }
        },
        loc: {
          start: {
            line: 203,
            column: 48
          },
          end: {
            line: 207,
            column: 1
          }
        },
        line: 203
      },
      "25": {
        name: "sanitizeInput",
        decl: {
          start: {
            line: 209,
            column: 9
          },
          end: {
            line: 209,
            column: 22
          }
        },
        loc: {
          start: {
            line: 209,
            column: 30
          },
          end: {
            line: 214,
            column: 1
          }
        },
        line: 209
      },
      "26": {
        name: "sanitizeEmail",
        decl: {
          start: {
            line: 215,
            column: 9
          },
          end: {
            line: 215,
            column: 22
          }
        },
        loc: {
          start: {
            line: 215,
            column: 30
          },
          end: {
            line: 217,
            column: 1
          }
        },
        line: 215
      },
      "27": {
        name: "escapeSQLString",
        decl: {
          start: {
            line: 219,
            column: 9
          },
          end: {
            line: 219,
            column: 24
          }
        },
        loc: {
          start: {
            line: 219,
            column: 30
          },
          end: {
            line: 221,
            column: 1
          }
        },
        line: 219
      },
      "28": {
        name: "escapeHtml",
        decl: {
          start: {
            line: 223,
            column: 9
          },
          end: {
            line: 223,
            column: 19
          }
        },
        loc: {
          start: {
            line: 223,
            column: 28
          },
          end: {
            line: 230,
            column: 1
          }
        },
        line: 223
      },
      "29": {
        name: "validatePasswordStrength",
        decl: {
          start: {
            line: 232,
            column: 9
          },
          end: {
            line: 232,
            column: 33
          }
        },
        loc: {
          start: {
            line: 232,
            column: 44
          },
          end: {
            line: 253,
            column: 1
          }
        },
        line: 232
      },
      "30": {
        name: "generateSecureSessionId",
        decl: {
          start: {
            line: 255,
            column: 9
          },
          end: {
            line: 255,
            column: 32
          }
        },
        loc: {
          start: {
            line: 255,
            column: 35
          },
          end: {
            line: 257,
            column: 1
          }
        },
        line: 255
      },
      "31": {
        name: "isValidSessionId",
        decl: {
          start: {
            line: 258,
            column: 9
          },
          end: {
            line: 258,
            column: 25
          }
        },
        loc: {
          start: {
            line: 258,
            column: 37
          },
          end: {
            line: 261,
            column: 1
          }
        },
        line: 258
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 59,
            column: 101
          },
          end: {
            line: 59,
            column: 141
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 117
          },
          end: {
            line: 59,
            column: 136
          }
        }, {
          start: {
            line: 59,
            column: 139
          },
          end: {
            line: 59,
            column: 141
          }
        }],
        line: 59
      },
      "36": {
        loc: {
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 98,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 64,
            column: 16
          },
          end: {
            line: 74,
            column: 124
          }
        }, {
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 97,
            column: 48
          }
        }],
        line: 63
      },
      "37": {
        loc: {
          start: {
            line: 68,
            column: 20
          },
          end: {
            line: 71,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 20
          },
          end: {
            line: 71,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "38": {
        loc: {
          start: {
            line: 68,
            column: 24
          },
          end: {
            line: 68,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 24
          },
          end: {
            line: 68,
            column: 37
          }
        }, {
          start: {
            line: 68,
            column: 41
          },
          end: {
            line: 68,
            column: 60
          }
        }],
        line: 68
      },
      "39": {
        loc: {
          start: {
            line: 72,
            column: 36
          },
          end: {
            line: 72,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 52
          },
          end: {
            line: 72,
            column: 53
          }
        }, {
          start: {
            line: 72,
            column: 56
          },
          end: {
            line: 72,
            column: 57
          }
        }],
        line: 72
      },
      "40": {
        loc: {
          start: {
            line: 77,
            column: 20
          },
          end: {
            line: 89,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 20
          },
          end: {
            line: 89,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "41": {
        loc: {
          start: {
            line: 78,
            column: 37
          },
          end: {
            line: 78,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 78,
            column: 53
          },
          end: {
            line: 78,
            column: 91
          }
        }, {
          start: {
            line: 78,
            column: 94
          },
          end: {
            line: 78,
            column: 96
          }
        }],
        line: 78
      },
      "42": {
        loc: {
          start: {
            line: 94,
            column: 20
          },
          end: {
            line: 96,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 20
          },
          end: {
            line: 96,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "43": {
        loc: {
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 107,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 107,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "44": {
        loc: {
          start: {
            line: 109,
            column: 4
          },
          end: {
            line: 111,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 4
          },
          end: {
            line: 111,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "45": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 115,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 115,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "46": {
        loc: {
          start: {
            line: 117,
            column: 11
          },
          end: {
            line: 117,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 117,
            column: 11
          },
          end: {
            line: 117,
            column: 21
          }
        }, {
          start: {
            line: 117,
            column: 25
          },
          end: {
            line: 117,
            column: 29
          }
        }],
        line: 117
      },
      "47": {
        loc: {
          start: {
            line: 169,
            column: 4
          },
          end: {
            line: 171,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 4
          },
          end: {
            line: 171,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 169
      },
      "48": {
        loc: {
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 169,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 169,
            column: 21
          }
        }, {
          start: {
            line: 169,
            column: 25
          },
          end: {
            line: 169,
            column: 43
          }
        }],
        line: 169
      },
      "49": {
        loc: {
          start: {
            line: 179,
            column: 12
          },
          end: {
            line: 195,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 180,
            column: 16
          },
          end: {
            line: 188,
            column: 69
          }
        }, {
          start: {
            line: 189,
            column: 16
          },
          end: {
            line: 194,
            column: 53
          }
        }],
        line: 179
      },
      "50": {
        loc: {
          start: {
            line: 184,
            column: 20
          },
          end: {
            line: 187,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 20
          },
          end: {
            line: 187,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "51": {
        loc: {
          start: {
            line: 184,
            column: 24
          },
          end: {
            line: 184,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 24
          },
          end: {
            line: 184,
            column: 37
          }
        }, {
          start: {
            line: 184,
            column: 41
          },
          end: {
            line: 184,
            column: 60
          }
        }],
        line: 184
      },
      "52": {
        loc: {
          start: {
            line: 191,
            column: 20
          },
          end: {
            line: 193,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 20
          },
          end: {
            line: 193,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "53": {
        loc: {
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "54": {
        loc: {
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 239,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 239,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "55": {
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 242,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 242,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "56": {
        loc: {
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "57": {
        loc: {
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "58": {
        loc: {
          start: {
            line: 260,
            column: 11
          },
          end: {
            line: 260,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 260,
            column: 11
          },
          end: {
            line: 260,
            column: 42
          }
        }, {
          start: {
            line: 260,
            column: 46
          },
          end: {
            line: 260,
            column: 67
          }
        }],
        line: 260
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rateLimit.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,8BAoDC;AAwED,gDASC;AAGD,sCAsBC;AAGD,8CAEC;AAED,8CAIC;AAGD,sCAKC;AAED,sCAEC;AAGD,0CAEC;AAGD,gCAOC;AAGD,4DA8BC;AAGD,0DAEC;AAED,4CAGC;AAzPD,sCAAwD;AACxD,wEAAiD;AASjD,SAAgB,SAAS,CAAC,MAAuB;IAAjD,iBAoDC;IAlDG,IAAA,QAAQ,GAGN,MAAM,SAHA,EACR,WAAW,GAET,MAAM,YAFG,EACX,KACE,MAAM,QADqB,EAA7B,OAAO,mBAAG,mBAAmB,KAAA,CACpB;IAEX,OAAO,UAAO,OAAoB,oCAAG,OAAO;;;;;oBAEpC,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;oBAClE,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;oBAE7D,8EAA8E;oBAC9E,IAAI,aAAa,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBACzC,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;wBAC7D,sBAAO,IAAI,EAAC,CAAC,sCAAsC;oBACrD,CAAC;oBAGK,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtC,mBAAmB,GAAG,WAAW,GAAG,aAAa,CAAC;oBAEzC,qBAAM,0BAAe,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,mBAAmB,CAAC,EAAA;;oBAArF,MAAM,GAAG,SAA4E;oBAE3F,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;wBACd,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC/E,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,OAAO,GAAG,UAAU,EAAE,EAC/B;gCACE,MAAM,EAAE,GAAG;gCACX,OAAO,EAAE;oCACP,mBAAmB,EAAE,mBAAmB,CAAC,QAAQ,EAAE;oCACnD,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;oCACpD,mBAAmB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;oCAChD,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;oCAC3E,sBAAsB,EAAE,aAAa,CAAC,QAAQ,EAAE;iCACjD;6BACF,CACF,EAAC;oBACJ,CAAC;oBAGK,QAAQ,GAAG,qBAAY,CAAC,IAAI,EAAE,CAAC;oBACrC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC1E,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC3E,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACvE,IAAI,aAAa,EAAE,CAAC;wBAClB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;oBACvD,CAAC;oBAED,sBAAO,IAAI,EAAC,CAAC,sCAAsC;;;SACpD,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,OAAoB;IACvC,oCAAoC;IACpC,IAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACzD,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;IAED,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAChD,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC/D,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,mFAAmF;IACnF,OAAQ,OAAe,CAAC,EAAE,IAAI,IAAI,CAAC;AACrC,CAAC;AAED,uCAAuC;AAC1B,QAAA,gBAAgB,GAAG;IAC9B,IAAI,EAAE;QACJ,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,WAAW,EAAE,CAAC;QACd,OAAO,EAAE,2DAA2D;KACrE;IACD,GAAG,EAAE;QACH,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,WAAW,EAAE,GAAG;QAChB,OAAO,EAAE,gDAAgD;KAC1D;IACD,OAAO,EAAE;QACP,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;QACnC,WAAW,EAAE,CAAC;QACd,OAAO,EAAE,4DAA4D;KACtE;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;QACnC,WAAW,EAAE,CAAC;QACd,OAAO,EAAE,mDAAmD;KAC7D;CACO,CAAC;AAEX,4EAA4E;AAC/D,QAAA,mBAAmB,GAAG;IACjC,IAAI,EAAE;QACJ,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,6BAA6B;QACtD,WAAW,EAAE,EAAE,EAAE,sBAAsB;QACvC,OAAO,EAAE,sEAAsE;KAChF;IACD,GAAG,EAAE;QACH,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,8BAA8B;QACxD,WAAW,EAAE,GAAG,EAAE,sBAAsB;QACxC,OAAO,EAAE,2DAA2D;KACrE;IACD,OAAO,EAAE;QACP,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,8BAA8B;QACxD,WAAW,EAAE,CAAC,EAAE,sBAAsB;QACtC,OAAO,EAAE,uEAAuE;KACjF;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,8BAA8B;QACxD,WAAW,EAAE,CAAC,EAAE,sBAAsB;QACtC,OAAO,EAAE,8DAA8D;KACxE;CACO,CAAC;AAEX,wDAAwD;AACxD,SAAgB,kBAAkB,CAAC,IAAmC;IACpE,IAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAC7D,IAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;IAExE,IAAI,aAAa,IAAI,kBAAkB,EAAE,CAAC;QACxC,OAAO,2BAAmB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,wBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAED,uDAAuD;AACvD,SAAsB,aAAa,CACjC,OAAoB,EACpB,MAAuB,EACvB,OAAoC;mCACnC,OAAO;;;;;oBAEF,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;oBAClE,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;oBAE7D,8EAA8E;oBAC9E,IAAI,aAAa,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBACzC,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;wBAC3E,sBAAO,OAAO,EAAE,EAAC;oBACnB,CAAC;oBAEyB,qBAAM,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAA;;oBAApD,iBAAiB,GAAG,SAAgC;oBAE1D,IAAI,iBAAiB,EAAE,CAAC;wBACtB,sBAAO,iBAAiB,EAAC;oBAC3B,CAAC;oBAED,sBAAO,OAAO,EAAE,EAAC;;;;CAClB;AAED,kBAAkB;AAClB,SAAgB,iBAAiB;IAC/B,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC;AAC7B,CAAC;AAED,SAAgB,iBAAiB,CAAC,KAAa,EAAE,YAAoB;IACnE,wEAAwE;IACxE,yCAAyC;IACzC,OAAO,KAAK,KAAK,YAAY,CAAC;AAChC,CAAC;AAED,6BAA6B;AAC7B,SAAgB,aAAa,CAAC,KAAa;IACzC,OAAO,KAAK;SACT,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,6BAA6B;SAClD,IAAI,EAAE;SACN,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,eAAe;AACpC,CAAC;AAED,SAAgB,aAAa,CAAC,KAAa;IACzC,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;AACpC,CAAC;AAED,mEAAmE;AACnE,SAAgB,eAAe,CAAC,GAAW;IACzC,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjC,CAAC;AAED,yBAAyB;AACzB,SAAgB,UAAU,CAAC,MAAc;IACvC,OAAO,MAAM;SACV,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;SACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;SACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;SACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;SACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC7B,CAAC;AAED,+BAA+B;AAC/B,SAAgB,wBAAwB,CAAC,QAAgB;IAIvD,IAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAChC,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;IAChF,CAAC;IAED,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QAC5B,MAAM,QAAA;KACP,CAAC;AACJ,CAAC;AAED,2BAA2B;AAC3B,SAAgB,uBAAuB;IACrC,OAAO,MAAM,CAAC,UAAU,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC7D,CAAC;AAED,SAAgB,gBAAgB,CAAC,SAAiB;IAChD,yCAAyC;IACzC,OAAO,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;AAClE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/rateLimit.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport securityStorage from './security-storage';\n\nexport interface RateLimitConfig {\n  windowMs: number;\n  maxRequests: number;\n  message?: string;\n  keyGenerator?: (request: NextRequest) => string;\n}\n\nexport function rateLimit(config: RateLimitConfig) {\n  const {\n    windowMs,\n    maxRequests,\n    message = 'Too many requests'\n  } = config;\n\n  return async (request: NextRequest): Promise<NextResponse | null> => {\n    // Check if rate limiting should be enabled in development\n    const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';\n    const isDevelopment = process.env.NODE_ENV === 'development';\n\n    // Skip rate limiting only if in development AND dev rate limiting is disabled\n    if (isDevelopment && !enableDevRateLimit) {\n      console.log('\uD83D\uDEAB Rate limiting disabled in development mode');\n      return null; // Continue to next middleware/handler\n    }\n\n    // Apply more lenient limits in development for testing\n    const devMultiplier = isDevelopment ? 2 : 1; // Double the limits in dev\n    const adjustedMaxRequests = maxRequests * devMultiplier;\n\n    const result = await securityStorage.checkRateLimit(request, windowMs, adjustedMaxRequests);\n\n    if (!result.allowed) {\n      const devWarning = isDevelopment ? ' (Development Mode - Limits Relaxed)' : '';\n      return NextResponse.json(\n        { error: message + devWarning },\n        {\n          status: 429,\n          headers: {\n            'X-RateLimit-Limit': adjustedMaxRequests.toString(),\n            'X-RateLimit-Remaining': result.remaining.toString(),\n            'X-RateLimit-Reset': result.resetTime.toString(),\n            'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),\n            'X-RateLimit-Dev-Mode': isDevelopment.toString()\n          }\n        }\n      );\n    }\n\n    // Add rate limit headers to successful responses\n    const response = NextResponse.next();\n    response.headers.set('X-RateLimit-Limit', adjustedMaxRequests.toString());\n    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());\n    response.headers.set('X-RateLimit-Reset', result.resetTime.toString());\n    if (isDevelopment) {\n      response.headers.set('X-RateLimit-Dev-Mode', 'true');\n    }\n\n    return null; // Continue to next middleware/handler\n  };\n}\n\nfunction getClientIP(request: NextRequest): string | null {\n  // Try various headers for client IP\n  const forwarded = request.headers.get('x-forwarded-for');\n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n\n  const realIP = request.headers.get('x-real-ip');\n  if (realIP) {\n    return realIP;\n  }\n\n  const cfConnectingIP = request.headers.get('cf-connecting-ip');\n  if (cfConnectingIP) {\n    return cfConnectingIP;\n  }\n\n  // Fallback to connection remote address (may not be available in all environments)\n  return (request as any).ip || null;\n}\n\n// Predefined rate limit configurations\nexport const rateLimitConfigs = {\n  auth: {\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    maxRequests: 5,\n    message: 'Too many authentication attempts. Please try again later.'\n  },\n  api: {\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    maxRequests: 100,\n    message: 'Too many API requests. Please try again later.'\n  },\n  contact: {\n    windowMs: 60 * 60 * 1000, // 1 hour\n    maxRequests: 3,\n    message: 'Too many contact form submissions. Please try again later.'\n  },\n  signup: {\n    windowMs: 60 * 60 * 1000, // 1 hour\n    maxRequests: 3,\n    message: 'Too many signup attempts. Please try again later.'\n  }\n} as const;\n\n// Development-friendly rate limit configurations (more lenient for testing)\nexport const devRateLimitConfigs = {\n  auth: {\n    windowMs: 5 * 60 * 1000, // 5 minutes (shorter window)\n    maxRequests: 10, // Double the requests\n    message: 'Too many authentication attempts. Please try again later. (Dev Mode)'\n  },\n  api: {\n    windowMs: 10 * 60 * 1000, // 10 minutes (shorter window)\n    maxRequests: 200, // Double the requests\n    message: 'Too many API requests. Please try again later. (Dev Mode)'\n  },\n  contact: {\n    windowMs: 30 * 60 * 1000, // 30 minutes (shorter window)\n    maxRequests: 6, // Double the requests\n    message: 'Too many contact form submissions. Please try again later. (Dev Mode)'\n  },\n  signup: {\n    windowMs: 30 * 60 * 1000, // 30 minutes (shorter window)\n    maxRequests: 6, // Double the requests\n    message: 'Too many signup attempts. Please try again later. (Dev Mode)'\n  }\n} as const;\n\n// Helper to get appropriate config based on environment\nexport function getRateLimitConfig(type: keyof typeof rateLimitConfigs): RateLimitConfig {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';\n\n  if (isDevelopment && enableDevRateLimit) {\n    return devRateLimitConfigs[type];\n  }\n\n  return rateLimitConfigs[type];\n}\n\n// Helper function to apply rate limiting to API routes\nexport async function withRateLimit(\n  request: NextRequest,\n  config: RateLimitConfig,\n  handler: () => Promise<NextResponse>\n): Promise<NextResponse> {\n  // Check if rate limiting should be enabled in development\n  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';\n  const isDevelopment = process.env.NODE_ENV === 'development';\n\n  // Skip rate limiting only if in development AND dev rate limiting is disabled\n  if (isDevelopment && !enableDevRateLimit) {\n    console.log('\uD83D\uDEAB Rate limiting disabled for API route in development mode');\n    return handler();\n  }\n\n  const rateLimitResponse = await rateLimit(config)(request);\n\n  if (rateLimitResponse) {\n    return rateLimitResponse;\n  }\n\n  return handler();\n}\n\n// CSRF Protection\nexport function generateCSRFToken(): string {\n  return crypto.randomUUID();\n}\n\nexport function validateCSRFToken(token: string, sessionToken: string): boolean {\n  // In a real implementation, you'd store CSRF tokens in session/database\n  // For now, we'll use a simple validation\n  return token === sessionToken;\n}\n\n// Input sanitization helpers\nexport function sanitizeInput(input: string): string {\n  return input\n    .replace(/[<>]/g, '') // Remove potential HTML tags\n    .trim()\n    .slice(0, 1000); // Limit length\n}\n\nexport function sanitizeEmail(email: string): string {\n  return email.toLowerCase().trim();\n}\n\n// SQL injection prevention (Prisma handles this, but good to have)\nexport function escapeSQLString(str: string): string {\n  return str.replace(/'/g, \"''\");\n}\n\n// XSS prevention helpers\nexport function escapeHtml(unsafe: string): string {\n  return unsafe\n    .replace(/&/g, \"&amp;\")\n    .replace(/</g, \"&lt;\")\n    .replace(/>/g, \"&gt;\")\n    .replace(/\"/g, \"&quot;\")\n    .replace(/'/g, \"&#039;\");\n}\n\n// Password strength validation\nexport function validatePasswordStrength(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[@$!%*?&]/.test(password)) {\n    errors.push('Password must contain at least one special character (@$!%*?&)');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\n// Session security helpers\nexport function generateSecureSessionId(): string {\n  return crypto.randomUUID() + '-' + Date.now().toString(36);\n}\n\nexport function isValidSessionId(sessionId: string): boolean {\n  // Basic validation for session ID format\n  return /^[a-f0-9-]+$/i.test(sessionId) && sessionId.length > 20;\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "789da6b2eef716a67b19d044bb944076871f2c1e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_lbx2ehtpw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_lbx2ehtpw();
var __awaiter =
/* istanbul ignore next */
(cov_lbx2ehtpw().s[0]++,
/* istanbul ignore next */
(cov_lbx2ehtpw().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_lbx2ehtpw().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_lbx2ehtpw().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().f[1]++;
    cov_lbx2ehtpw().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().f[2]++;
      cov_lbx2ehtpw().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().f[4]++;
      cov_lbx2ehtpw().s[4]++;
      try {
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().f[5]++;
      cov_lbx2ehtpw().s[7]++;
      try {
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().f[6]++;
      cov_lbx2ehtpw().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_lbx2ehtpw().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_lbx2ehtpw().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_lbx2ehtpw().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_lbx2ehtpw().s[12]++,
/* istanbul ignore next */
(cov_lbx2ehtpw().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_lbx2ehtpw().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_lbx2ehtpw().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_lbx2ehtpw().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_lbx2ehtpw().f[8]++;
        cov_lbx2ehtpw().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_lbx2ehtpw().b[6][0]++;
          cov_lbx2ehtpw().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_lbx2ehtpw().b[6][1]++;
        }
        cov_lbx2ehtpw().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_lbx2ehtpw().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_lbx2ehtpw().f[9]++;
    cov_lbx2ehtpw().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().f[10]++;
    cov_lbx2ehtpw().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().f[11]++;
      cov_lbx2ehtpw().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().f[12]++;
    cov_lbx2ehtpw().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().b[9][0]++;
      cov_lbx2ehtpw().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_lbx2ehtpw().b[9][1]++;
    }
    cov_lbx2ehtpw().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().s[25]++;
      try {
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[15][0]++,
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[16][1]++,
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_lbx2ehtpw().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_lbx2ehtpw().b[12][0]++;
          cov_lbx2ehtpw().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_lbx2ehtpw().b[12][1]++;
        }
        cov_lbx2ehtpw().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_lbx2ehtpw().b[18][0]++;
          cov_lbx2ehtpw().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_lbx2ehtpw().b[18][1]++;
        }
        cov_lbx2ehtpw().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[19][1]++;
            cov_lbx2ehtpw().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[19][2]++;
            cov_lbx2ehtpw().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[19][3]++;
            cov_lbx2ehtpw().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[19][4]++;
            cov_lbx2ehtpw().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[19][5]++;
            cov_lbx2ehtpw().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[20][0]++;
              cov_lbx2ehtpw().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[20][1]++;
            }
            cov_lbx2ehtpw().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[23][0]++;
              cov_lbx2ehtpw().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[23][1]++;
            }
            cov_lbx2ehtpw().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[25][0]++;
              cov_lbx2ehtpw().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[25][1]++;
            }
            cov_lbx2ehtpw().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[27][0]++;
              cov_lbx2ehtpw().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[27][1]++;
            }
            cov_lbx2ehtpw().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[29][0]++;
              cov_lbx2ehtpw().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[29][1]++;
            }
            cov_lbx2ehtpw().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_lbx2ehtpw().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_lbx2ehtpw().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().b[30][0]++;
      cov_lbx2ehtpw().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_lbx2ehtpw().b[30][1]++;
    }
    cov_lbx2ehtpw().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_lbx2ehtpw().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_lbx2ehtpw().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_lbx2ehtpw().s[67]++,
/* istanbul ignore next */
(cov_lbx2ehtpw().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_lbx2ehtpw().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_lbx2ehtpw().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[13]++;
  cov_lbx2ehtpw().s[68]++;
  return /* istanbul ignore next */(cov_lbx2ehtpw().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_lbx2ehtpw().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_lbx2ehtpw().s[70]++;
exports.devRateLimitConfigs = exports.rateLimitConfigs = void 0;
/* istanbul ignore next */
cov_lbx2ehtpw().s[71]++;
exports.rateLimit = rateLimit;
/* istanbul ignore next */
cov_lbx2ehtpw().s[72]++;
exports.getRateLimitConfig = getRateLimitConfig;
/* istanbul ignore next */
cov_lbx2ehtpw().s[73]++;
exports.withRateLimit = withRateLimit;
/* istanbul ignore next */
cov_lbx2ehtpw().s[74]++;
exports.generateCSRFToken = generateCSRFToken;
/* istanbul ignore next */
cov_lbx2ehtpw().s[75]++;
exports.validateCSRFToken = validateCSRFToken;
/* istanbul ignore next */
cov_lbx2ehtpw().s[76]++;
exports.sanitizeInput = sanitizeInput;
/* istanbul ignore next */
cov_lbx2ehtpw().s[77]++;
exports.sanitizeEmail = sanitizeEmail;
/* istanbul ignore next */
cov_lbx2ehtpw().s[78]++;
exports.escapeSQLString = escapeSQLString;
/* istanbul ignore next */
cov_lbx2ehtpw().s[79]++;
exports.escapeHtml = escapeHtml;
/* istanbul ignore next */
cov_lbx2ehtpw().s[80]++;
exports.validatePasswordStrength = validatePasswordStrength;
/* istanbul ignore next */
cov_lbx2ehtpw().s[81]++;
exports.generateSecureSessionId = generateSecureSessionId;
/* istanbul ignore next */
cov_lbx2ehtpw().s[82]++;
exports.isValidSessionId = isValidSessionId;
var server_1 =
/* istanbul ignore next */
(cov_lbx2ehtpw().s[83]++, require("next/server"));
var security_storage_1 =
/* istanbul ignore next */
(cov_lbx2ehtpw().s[84]++, __importDefault(require("./security-storage")));
function rateLimit(config) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[14]++;
  var _this =
  /* istanbul ignore next */
  (cov_lbx2ehtpw().s[85]++, this);
  var windowMs =
    /* istanbul ignore next */
    (cov_lbx2ehtpw().s[86]++, config.windowMs),
    maxRequests =
    /* istanbul ignore next */
    (cov_lbx2ehtpw().s[87]++, config.maxRequests),
    _a =
    /* istanbul ignore next */
    (cov_lbx2ehtpw().s[88]++, config.message),
    message =
    /* istanbul ignore next */
    (cov_lbx2ehtpw().s[89]++, _a === void 0 ?
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[35][0]++, 'Too many requests') :
    /* istanbul ignore next */
    (cov_lbx2ehtpw().b[35][1]++, _a));
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[90]++;
  return function (request) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().f[15]++;
    cov_lbx2ehtpw().s[91]++;
    return __awaiter(_this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_lbx2ehtpw().f[16]++;
      var enableDevRateLimit, isDevelopment, devMultiplier, adjustedMaxRequests, result, devWarning, response;
      /* istanbul ignore next */
      cov_lbx2ehtpw().s[92]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_lbx2ehtpw().f[17]++;
        cov_lbx2ehtpw().s[93]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[36][0]++;
            cov_lbx2ehtpw().s[94]++;
            enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[95]++;
            isDevelopment = process.env.NODE_ENV === 'development';
            // Skip rate limiting only if in development AND dev rate limiting is disabled
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[96]++;
            if (
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[38][0]++, isDevelopment) &&
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[38][1]++, !enableDevRateLimit)) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[37][0]++;
              cov_lbx2ehtpw().s[97]++;
              console.log('🚫 Rate limiting disabled in development mode');
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[98]++;
              return [2 /*return*/, null]; // Continue to next middleware/handler
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[37][1]++;
            }
            cov_lbx2ehtpw().s[99]++;
            devMultiplier = isDevelopment ?
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[39][0]++, 2) :
            /* istanbul ignore next */
            (cov_lbx2ehtpw().b[39][1]++, 1);
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[100]++;
            adjustedMaxRequests = maxRequests * devMultiplier;
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[101]++;
            return [4 /*yield*/, security_storage_1.default.checkRateLimit(request, windowMs, adjustedMaxRequests)];
          case 1:
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[36][1]++;
            cov_lbx2ehtpw().s[102]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[103]++;
            if (!result.allowed) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[40][0]++;
              cov_lbx2ehtpw().s[104]++;
              devWarning = isDevelopment ?
              /* istanbul ignore next */
              (cov_lbx2ehtpw().b[41][0]++, ' (Development Mode - Limits Relaxed)') :
              /* istanbul ignore next */
              (cov_lbx2ehtpw().b[41][1]++, '');
              /* istanbul ignore next */
              cov_lbx2ehtpw().s[105]++;
              return [2 /*return*/, server_1.NextResponse.json({
                error: message + devWarning
              }, {
                status: 429,
                headers: {
                  'X-RateLimit-Limit': adjustedMaxRequests.toString(),
                  'X-RateLimit-Remaining': result.remaining.toString(),
                  'X-RateLimit-Reset': result.resetTime.toString(),
                  'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),
                  'X-RateLimit-Dev-Mode': isDevelopment.toString()
                }
              })];
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[40][1]++;
            }
            cov_lbx2ehtpw().s[106]++;
            response = server_1.NextResponse.next();
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[107]++;
            response.headers.set('X-RateLimit-Limit', adjustedMaxRequests.toString());
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[108]++;
            response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[109]++;
            response.headers.set('X-RateLimit-Reset', result.resetTime.toString());
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[110]++;
            if (isDevelopment) {
              /* istanbul ignore next */
              cov_lbx2ehtpw().b[42][0]++;
              cov_lbx2ehtpw().s[111]++;
              response.headers.set('X-RateLimit-Dev-Mode', 'true');
            } else
            /* istanbul ignore next */
            {
              cov_lbx2ehtpw().b[42][1]++;
            }
            cov_lbx2ehtpw().s[112]++;
            return [2 /*return*/, null];
          // Continue to next middleware/handler
        }
      });
    });
  };
}
function getClientIP(request) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[18]++;
  // Try various headers for client IP
  var forwarded =
  /* istanbul ignore next */
  (cov_lbx2ehtpw().s[113]++, request.headers.get('x-forwarded-for'));
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[114]++;
  if (forwarded) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[43][0]++;
    cov_lbx2ehtpw().s[115]++;
    return forwarded.split(',')[0].trim();
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[43][1]++;
  }
  var realIP =
  /* istanbul ignore next */
  (cov_lbx2ehtpw().s[116]++, request.headers.get('x-real-ip'));
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[117]++;
  if (realIP) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[44][0]++;
    cov_lbx2ehtpw().s[118]++;
    return realIP;
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[44][1]++;
  }
  var cfConnectingIP =
  /* istanbul ignore next */
  (cov_lbx2ehtpw().s[119]++, request.headers.get('cf-connecting-ip'));
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[120]++;
  if (cfConnectingIP) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[45][0]++;
    cov_lbx2ehtpw().s[121]++;
    return cfConnectingIP;
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[45][1]++;
  }
  // Fallback to connection remote address (may not be available in all environments)
  cov_lbx2ehtpw().s[122]++;
  return /* istanbul ignore next */(cov_lbx2ehtpw().b[46][0]++, request.ip) ||
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[46][1]++, null);
}
// Predefined rate limit configurations
/* istanbul ignore next */
cov_lbx2ehtpw().s[123]++;
exports.rateLimitConfigs = {
  auth: {
    windowMs: 15 * 60 * 1000,
    // 15 minutes
    maxRequests: 5,
    message: 'Too many authentication attempts. Please try again later.'
  },
  api: {
    windowMs: 15 * 60 * 1000,
    // 15 minutes
    maxRequests: 100,
    message: 'Too many API requests. Please try again later.'
  },
  contact: {
    windowMs: 60 * 60 * 1000,
    // 1 hour
    maxRequests: 3,
    message: 'Too many contact form submissions. Please try again later.'
  },
  signup: {
    windowMs: 60 * 60 * 1000,
    // 1 hour
    maxRequests: 3,
    message: 'Too many signup attempts. Please try again later.'
  }
};
// Development-friendly rate limit configurations (more lenient for testing)
/* istanbul ignore next */
cov_lbx2ehtpw().s[124]++;
exports.devRateLimitConfigs = {
  auth: {
    windowMs: 5 * 60 * 1000,
    // 5 minutes (shorter window)
    maxRequests: 10,
    // Double the requests
    message: 'Too many authentication attempts. Please try again later. (Dev Mode)'
  },
  api: {
    windowMs: 10 * 60 * 1000,
    // 10 minutes (shorter window)
    maxRequests: 200,
    // Double the requests
    message: 'Too many API requests. Please try again later. (Dev Mode)'
  },
  contact: {
    windowMs: 30 * 60 * 1000,
    // 30 minutes (shorter window)
    maxRequests: 6,
    // Double the requests
    message: 'Too many contact form submissions. Please try again later. (Dev Mode)'
  },
  signup: {
    windowMs: 30 * 60 * 1000,
    // 30 minutes (shorter window)
    maxRequests: 6,
    // Double the requests
    message: 'Too many signup attempts. Please try again later. (Dev Mode)'
  }
};
// Helper to get appropriate config based on environment
function getRateLimitConfig(type) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[19]++;
  var isDevelopment =
  /* istanbul ignore next */
  (cov_lbx2ehtpw().s[125]++, process.env.NODE_ENV === 'development');
  var enableDevRateLimit =
  /* istanbul ignore next */
  (cov_lbx2ehtpw().s[126]++, process.env.ENABLE_DEV_RATE_LIMIT === 'true');
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[127]++;
  if (
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[48][0]++, isDevelopment) &&
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[48][1]++, enableDevRateLimit)) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[47][0]++;
    cov_lbx2ehtpw().s[128]++;
    return exports.devRateLimitConfigs[type];
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[47][1]++;
  }
  cov_lbx2ehtpw().s[129]++;
  return exports.rateLimitConfigs[type];
}
// Helper function to apply rate limiting to API routes
function withRateLimit(request, config, handler) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[20]++;
  cov_lbx2ehtpw().s[130]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_lbx2ehtpw().f[21]++;
    var enableDevRateLimit, isDevelopment, rateLimitResponse;
    /* istanbul ignore next */
    cov_lbx2ehtpw().s[131]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_lbx2ehtpw().f[22]++;
      cov_lbx2ehtpw().s[132]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_lbx2ehtpw().b[49][0]++;
          cov_lbx2ehtpw().s[133]++;
          enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';
          /* istanbul ignore next */
          cov_lbx2ehtpw().s[134]++;
          isDevelopment = process.env.NODE_ENV === 'development';
          // Skip rate limiting only if in development AND dev rate limiting is disabled
          /* istanbul ignore next */
          cov_lbx2ehtpw().s[135]++;
          if (
          /* istanbul ignore next */
          (cov_lbx2ehtpw().b[51][0]++, isDevelopment) &&
          /* istanbul ignore next */
          (cov_lbx2ehtpw().b[51][1]++, !enableDevRateLimit)) {
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[50][0]++;
            cov_lbx2ehtpw().s[136]++;
            console.log('🚫 Rate limiting disabled for API route in development mode');
            /* istanbul ignore next */
            cov_lbx2ehtpw().s[137]++;
            return [2 /*return*/, handler()];
          } else
          /* istanbul ignore next */
          {
            cov_lbx2ehtpw().b[50][1]++;
          }
          cov_lbx2ehtpw().s[138]++;
          return [4 /*yield*/, rateLimit(config)(request)];
        case 1:
          /* istanbul ignore next */
          cov_lbx2ehtpw().b[49][1]++;
          cov_lbx2ehtpw().s[139]++;
          rateLimitResponse = _a.sent();
          /* istanbul ignore next */
          cov_lbx2ehtpw().s[140]++;
          if (rateLimitResponse) {
            /* istanbul ignore next */
            cov_lbx2ehtpw().b[52][0]++;
            cov_lbx2ehtpw().s[141]++;
            return [2 /*return*/, rateLimitResponse];
          } else
          /* istanbul ignore next */
          {
            cov_lbx2ehtpw().b[52][1]++;
          }
          cov_lbx2ehtpw().s[142]++;
          return [2 /*return*/, handler()];
      }
    });
  });
}
// CSRF Protection
function generateCSRFToken() {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[23]++;
  cov_lbx2ehtpw().s[143]++;
  return crypto.randomUUID();
}
function validateCSRFToken(token, sessionToken) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[24]++;
  cov_lbx2ehtpw().s[144]++;
  // In a real implementation, you'd store CSRF tokens in session/database
  // For now, we'll use a simple validation
  return token === sessionToken;
}
// Input sanitization helpers
function sanitizeInput(input) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[25]++;
  cov_lbx2ehtpw().s[145]++;
  return input.replace(/[<>]/g, '') // Remove potential HTML tags
  .trim().slice(0, 1000); // Limit length
}
function sanitizeEmail(email) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[26]++;
  cov_lbx2ehtpw().s[146]++;
  return email.toLowerCase().trim();
}
// SQL injection prevention (Prisma handles this, but good to have)
function escapeSQLString(str) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[27]++;
  cov_lbx2ehtpw().s[147]++;
  return str.replace(/'/g, "''");
}
// XSS prevention helpers
function escapeHtml(unsafe) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[28]++;
  cov_lbx2ehtpw().s[148]++;
  return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
}
// Password strength validation
function validatePasswordStrength(password) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[29]++;
  var errors =
  /* istanbul ignore next */
  (cov_lbx2ehtpw().s[149]++, []);
  /* istanbul ignore next */
  cov_lbx2ehtpw().s[150]++;
  if (password.length < 8) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[53][0]++;
    cov_lbx2ehtpw().s[151]++;
    errors.push('Password must be at least 8 characters long');
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[53][1]++;
  }
  cov_lbx2ehtpw().s[152]++;
  if (!/[a-z]/.test(password)) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[54][0]++;
    cov_lbx2ehtpw().s[153]++;
    errors.push('Password must contain at least one lowercase letter');
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[54][1]++;
  }
  cov_lbx2ehtpw().s[154]++;
  if (!/[A-Z]/.test(password)) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[55][0]++;
    cov_lbx2ehtpw().s[155]++;
    errors.push('Password must contain at least one uppercase letter');
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[55][1]++;
  }
  cov_lbx2ehtpw().s[156]++;
  if (!/\d/.test(password)) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[56][0]++;
    cov_lbx2ehtpw().s[157]++;
    errors.push('Password must contain at least one number');
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[56][1]++;
  }
  cov_lbx2ehtpw().s[158]++;
  if (!/[@$!%*?&]/.test(password)) {
    /* istanbul ignore next */
    cov_lbx2ehtpw().b[57][0]++;
    cov_lbx2ehtpw().s[159]++;
    errors.push('Password must contain at least one special character (@$!%*?&)');
  } else
  /* istanbul ignore next */
  {
    cov_lbx2ehtpw().b[57][1]++;
  }
  cov_lbx2ehtpw().s[160]++;
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}
// Session security helpers
function generateSecureSessionId() {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[30]++;
  cov_lbx2ehtpw().s[161]++;
  return crypto.randomUUID() + '-' + Date.now().toString(36);
}
function isValidSessionId(sessionId) {
  /* istanbul ignore next */
  cov_lbx2ehtpw().f[31]++;
  cov_lbx2ehtpw().s[162]++;
  // Basic validation for session ID format
  return /* istanbul ignore next */(cov_lbx2ehtpw().b[58][0]++, /^[a-f0-9-]+$/i.test(sessionId)) &&
  /* istanbul ignore next */
  (cov_lbx2ehtpw().b[58][1]++, sessionId.length > 20);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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