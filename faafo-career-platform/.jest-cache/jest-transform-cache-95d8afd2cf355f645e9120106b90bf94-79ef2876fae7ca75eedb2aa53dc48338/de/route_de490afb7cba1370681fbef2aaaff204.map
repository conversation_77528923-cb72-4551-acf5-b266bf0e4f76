{"version": 3, "names": ["server_1", "cov_2l59hl9onv", "s", "require", "uuid_1", "react_1", "__importDefault", "prisma_1", "email_1", "VerificationEmail_1", "unified_api_error_handler_1", "exports", "POST", "withUnifiedErrorHandling", "request", "f", "__awaiter", "Promise", "json", "email", "_a", "sent", "b", "error", "Error", "statusCode", "default", "user", "findUnique", "where", "NextResponse", "success", "data", "message", "emailVerified", "verificationToken", "<PERSON><PERSON><PERSON><PERSON>", "identifier", "expires", "gt", "Date", "now", "orderBy", "recentToken", "deleteMany", "lt", "v4", "tokenExpiry", "create", "token", "verificationUrl", "concat", "process", "env", "NEXTAUTH_URL", "encodeURIComponent", "sendEmail", "to", "subject", "template", "createElement", "VerificationEmail", "username", "name", "verificationLink", "console", "log", "emailError_1"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/resend-verification/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { v4 as uuidv4 } from 'uuid';\nimport React from 'react';\nimport prisma from '@/lib/prisma';\nimport { sendEmail } from '@/lib/email';\nimport { VerificationEmail } from '@/emails/VerificationEmail';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface ResendVerificationResponse {\n  message: string;\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ResendVerificationResponse>>> => {\n  const { email } = await request.json();\n\n  if (!email) {\n    const error = new Error('Email is required.') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Find the user\n  const user = await prisma.user.findUnique({\n    where: { email: email },\n  });\n\n  if (!user) {\n    // For security reasons, don't reveal if the user exists or not\n    return NextResponse.json({\n      success: true,\n      data: { message: 'If an account with that email exists and is unverified, a verification email has been sent.' }\n    });\n  }\n\n  // Check if user is already verified\n  if (user.emailVerified) {\n    return NextResponse.json({\n      success: true,\n      data: { message: 'Email is already verified.' }\n    });\n  }\n\n  // Check for rate limiting - only allow one verification email per 5 minutes\n  const recentToken = await prisma.verificationToken.findFirst({\n    where: {\n      identifier: email,\n      expires: { gt: new Date(Date.now() - 5 * 60 * 1000) }, // Created within last 5 minutes\n    },\n    orderBy: {\n      expires: 'desc',\n    },\n  });\n\n  if (recentToken) {\n    const error = new Error('A verification email was recently sent. Please wait 5 minutes before requesting another.') as any;\n    error.statusCode = 429;\n    throw error;\n  }\n\n  // Clean up any existing expired tokens for this email\n  await prisma.verificationToken.deleteMany({\n    where: {\n      identifier: email,\n      expires: { lt: new Date() },\n    },\n  });\n\n  // Generate new verification token\n  const verificationToken = uuidv4();\n  const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  // Store verification token (replace any existing valid tokens)\n  await prisma.verificationToken.deleteMany({\n    where: {\n      identifier: email,\n    },\n  });\n\n  await prisma.verificationToken.create({\n    data: {\n      identifier: email,\n      token: verificationToken,\n      expires: tokenExpiry,\n    },\n  });\n\n  // Send verification email\n  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(email)}`;\n\n  try {\n    await sendEmail({\n      to: email,\n      subject: \"Verify your email for FAAFO Career Platform\",\n      template: React.createElement(VerificationEmail, { username: user.name || email, verificationLink: verificationUrl }),\n    });\n    console.log(`Verification email resent to ${email}`);\n  } catch (emailError) {\n    console.error('Failed to send verification email:', emailError);\n    const error = new Error('Failed to send verification email.') as any;\n    error.statusCode = 500;\n    throw error;\n  }\n\n  return NextResponse.json({\n    success: true,\n    data: { message: 'Verification email sent successfully.' }\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,OAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAI,eAAA,CAAAH,OAAA;AACA,IAAAI,QAAA;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,QAAAI,eAAA,CAAAH,OAAA;AACA,IAAAK,OAAA;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAM,mBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAO,2BAAA;AAAA;AAAA,CAAAT,cAAA,GAAAC,CAAA,QAAAC,OAAA;AAAwF;AAAAF,cAAA,GAAAC,CAAA;AAM3ES,OAAA,CAAAC,IAAI,GAAG,IAAAF,2BAAA,CAAAG,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAb,cAAA,GAAAc,CAAA;EAAAd,cAAA,GAAAC,CAAA;EAAA,OAAAc,SAAA,iBAAGC,OAAO;IAAA;IAAAhB,cAAA,GAAAc,CAAA;;;;;;;;;;;;;UAC9D,qBAAMD,OAAO,CAACI,IAAI,EAAE;;;;;UAA9BC,KAAK,GAAKC,EAAA,CAAAC,IAAA,EAAoB,CAAAF,KAAzB;UAAA;UAAAlB,cAAA,GAAAC,CAAA;UAEb,IAAI,CAACiB,KAAK,EAAE;YAAA;YAAAlB,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAC,CAAA;YACJqB,KAAK,GAAG,IAAIC,KAAK,CAAC,oBAAoB,CAAQ;YAAC;YAAAvB,cAAA,GAAAC,CAAA;YACrDqB,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAAxB,cAAA,GAAAC,CAAA;YACvB,MAAMqB,KAAK;UACb,CAAC;UAAA;UAAA;YAAAtB,cAAA,GAAAqB,CAAA;UAAA;UAAArB,cAAA,GAAAC,CAAA;UAGY,qBAAMK,QAAA,CAAAmB,OAAM,CAACC,IAAI,CAACC,UAAU,CAAC;YACxCC,KAAK,EAAE;cAAEV,KAAK,EAAEA;YAAK;WACtB,CAAC;;;;;UAFIQ,IAAI,GAAGP,EAAA,CAAAC,IAAA,EAEX;UAAA;UAAApB,cAAA,GAAAC,CAAA;UAEF,IAAI,CAACyB,IAAI,EAAE;YAAA;YAAA1B,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAC,CAAA;YACT;YACA,sBAAOF,QAAA,CAAA8B,YAAY,CAACZ,IAAI,CAAC;cACvBa,OAAO,EAAE,IAAI;cACbC,IAAI,EAAE;gBAAEC,OAAO,EAAE;cAA6F;aAC/G,CAAC;UACJ,CAAC;UAAA;UAAA;YAAAhC,cAAA,GAAAqB,CAAA;UAAA;UAED;UAAArB,cAAA,GAAAC,CAAA;UACA,IAAIyB,IAAI,CAACO,aAAa,EAAE;YAAA;YAAAjC,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAC,CAAA;YACtB,sBAAOF,QAAA,CAAA8B,YAAY,CAACZ,IAAI,CAAC;cACvBa,OAAO,EAAE,IAAI;cACbC,IAAI,EAAE;gBAAEC,OAAO,EAAE;cAA4B;aAC9C,CAAC;UACJ,CAAC;UAAA;UAAA;YAAAhC,cAAA,GAAAqB,CAAA;UAAA;UAAArB,cAAA,GAAAC,CAAA;UAGmB,qBAAMK,QAAA,CAAAmB,OAAM,CAACS,iBAAiB,CAACC,SAAS,CAAC;YAC3DP,KAAK,EAAE;cACLQ,UAAU,EAAElB,KAAK;cACjBmB,OAAO,EAAE;gBAAEC,EAAE,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;cAAC,CAAE,CAAE;aACxD;YACDC,OAAO,EAAE;cACPJ,OAAO,EAAE;;WAEZ,CAAC;;;;;UARIK,WAAW,GAAGvB,EAAA,CAAAC,IAAA,EAQlB;UAAA;UAAApB,cAAA,GAAAC,CAAA;UAEF,IAAIyC,WAAW,EAAE;YAAA;YAAA1C,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAC,CAAA;YACTqB,KAAK,GAAG,IAAIC,KAAK,CAAC,0FAA0F,CAAQ;YAAC;YAAAvB,cAAA,GAAAC,CAAA;YAC3HqB,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAAxB,cAAA,GAAAC,CAAA;YACvB,MAAMqB,KAAK;UACb,CAAC;UAAA;UAAA;YAAAtB,cAAA,GAAAqB,CAAA;UAAA;UAED;UAAArB,cAAA,GAAAC,CAAA;UACA,qBAAMK,QAAA,CAAAmB,OAAM,CAACS,iBAAiB,CAACS,UAAU,CAAC;YACxCf,KAAK,EAAE;cACLQ,UAAU,EAAElB,KAAK;cACjBmB,OAAO,EAAE;gBAAEO,EAAE,EAAE,IAAIL,IAAI;cAAE;;WAE5B,CAAC;;;;;UANF;UACApB,EAAA,CAAAC,IAAA,EAKE;UAAC;UAAApB,cAAA,GAAAC,CAAA;UAGGiC,iBAAiB,GAAG,IAAA/B,MAAA,CAAA0C,EAAM,GAAE;UAAC;UAAA7C,cAAA,GAAAC,CAAA;UAC7B6C,WAAW,GAAG,IAAIP,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;UAE9D;UAAA;UAAAxC,cAAA,GAAAC,CAAA;UACA,qBAAMK,QAAA,CAAAmB,OAAM,CAACS,iBAAiB,CAACS,UAAU,CAAC;YACxCf,KAAK,EAAE;cACLQ,UAAU,EAAElB;;WAEf,CAAC;;;;;UALF;UACAC,EAAA,CAAAC,IAAA,EAIE;UAAC;UAAApB,cAAA,GAAAC,CAAA;UAEH,qBAAMK,QAAA,CAAAmB,OAAM,CAACS,iBAAiB,CAACa,MAAM,CAAC;YACpChB,IAAI,EAAE;cACJK,UAAU,EAAElB,KAAK;cACjB8B,KAAK,EAAEd,iBAAiB;cACxBG,OAAO,EAAES;;WAEZ,CAAC;;;;;UANF3B,EAAA,CAAAC,IAAA,EAME;UAAC;UAAApB,cAAA,GAAAC,CAAA;UAGGgD,eAAe,GAAG,GAAAC,MAAA,CAAGC,OAAO,CAACC,GAAG,CAACC,YAAY,+BAAAH,MAAA,CAA4BhB,iBAAiB,aAAAgB,MAAA,CAAUI,kBAAkB,CAACpC,KAAK,CAAC,CAAE;UAAC;UAAAlB,cAAA,GAAAC,CAAA;;;;;;;;;UAGpI,qBAAM,IAAAM,OAAA,CAAAgD,SAAS,EAAC;YACdC,EAAE,EAAEtC,KAAK;YACTuC,OAAO,EAAE,6CAA6C;YACtDC,QAAQ,EAAEtD,OAAA,CAAAqB,OAAK,CAACkC,aAAa,CAACnD,mBAAA,CAAAoD,iBAAiB,EAAE;cAAEC,QAAQ;cAAE;cAAA,CAAA7D,cAAA,GAAAqB,CAAA,WAAAK,IAAI,CAACoC,IAAI;cAAA;cAAA,CAAA9D,cAAA,GAAAqB,CAAA,WAAIH,KAAK;cAAE6C,gBAAgB,EAAEd;YAAe,CAAE;WACrH,CAAC;;;;;UAJF9B,EAAA,CAAAC,IAAA,EAIE;UAAC;UAAApB,cAAA,GAAAC,CAAA;UACH+D,OAAO,CAACC,GAAG,CAAC,gCAAAf,MAAA,CAAgChC,KAAK,CAAE,CAAC;UAAC;UAAAlB,cAAA,GAAAC,CAAA;;;;;;;;;UAErD+D,OAAO,CAAC1C,KAAK,CAAC,oCAAoC,EAAE4C,YAAU,CAAC;UAAC;UAAAlE,cAAA,GAAAC,CAAA;UAC1DqB,KAAK,GAAG,IAAIC,KAAK,CAAC,oCAAoC,CAAQ;UAAC;UAAAvB,cAAA,GAAAC,CAAA;UACrEqB,KAAK,CAACE,UAAU,GAAG,GAAG;UAAC;UAAAxB,cAAA,GAAAC,CAAA;UACvB,MAAMqB,KAAK;;;;;UAGb,sBAAOvB,QAAA,CAAA8B,YAAY,CAACZ,IAAI,CAAC;YACvBa,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cAAEC,OAAO,EAAE;YAAuC;WACzD,CAAC;;;;CACH,CAAC", "ignoreList": []}