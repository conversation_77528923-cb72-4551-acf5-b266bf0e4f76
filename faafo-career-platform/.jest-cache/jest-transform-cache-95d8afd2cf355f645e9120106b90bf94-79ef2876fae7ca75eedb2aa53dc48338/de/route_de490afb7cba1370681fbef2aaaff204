97e9715e2cd25b0645b57a2b38b2baac
"use strict";

/* istanbul ignore next */
function cov_2l59hl9onv() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/resend-verification/route.ts";
  var hash = "4f4120b46a95e2cddc8705a6ec86e5d80a58d050";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/resend-verification/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 22
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 13
        },
        end: {
          line: 44,
          column: 28
        }
      },
      "73": {
        start: {
          line: 45,
          column: 14
        },
        end: {
          line: 45,
          column: 47
        }
      },
      "74": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "75": {
        start: {
          line: 47,
          column: 14
        },
        end: {
          line: 47,
          column: 36
        }
      },
      "76": {
        start: {
          line: 48,
          column: 26
        },
        end: {
          line: 48,
          column: 63
        }
      },
      "77": {
        start: {
          line: 49,
          column: 34
        },
        end: {
          line: 49,
          column: 76
        }
      },
      "78": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 152,
          column: 7
        }
      },
      "79": {
        start: {
          line: 50,
          column: 94
        },
        end: {
          line: 152,
          column: 3
        }
      },
      "80": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 151,
          column: 7
        }
      },
      "81": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 150,
          column: 9
        }
      },
      "82": {
        start: {
          line: 54,
          column: 20
        },
        end: {
          line: 54,
          column: 57
        }
      },
      "83": {
        start: {
          line: 56,
          column: 16
        },
        end: {
          line: 56,
          column: 42
        }
      },
      "84": {
        start: {
          line: 57,
          column: 16
        },
        end: {
          line: 61,
          column: 17
        }
      },
      "85": {
        start: {
          line: 58,
          column: 20
        },
        end: {
          line: 58,
          column: 60
        }
      },
      "86": {
        start: {
          line: 59,
          column: 20
        },
        end: {
          line: 59,
          column: 43
        }
      },
      "87": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 32
        }
      },
      "88": {
        start: {
          line: 62,
          column: 16
        },
        end: {
          line: 64,
          column: 24
        }
      },
      "89": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 33
        }
      },
      "90": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 73,
          column: 17
        }
      },
      "91": {
        start: {
          line: 69,
          column: 20
        },
        end: {
          line: 72,
          column: 28
        }
      },
      "92": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 80,
          column: 17
        }
      },
      "93": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 79,
          column: 28
        }
      },
      "94": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 89,
          column: 24
        }
      },
      "95": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 40
        }
      },
      "96": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 96,
          column: 17
        }
      },
      "97": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 93,
          column: 130
        }
      },
      "98": {
        start: {
          line: 94,
          column: 20
        },
        end: {
          line: 94,
          column: 43
        }
      },
      "99": {
        start: {
          line: 95,
          column: 20
        },
        end: {
          line: 95,
          column: 32
        }
      },
      "100": {
        start: {
          line: 98,
          column: 16
        },
        end: {
          line: 103,
          column: 24
        }
      },
      "101": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 106,
          column: 26
        }
      },
      "102": {
        start: {
          line: 107,
          column: 16
        },
        end: {
          line: 107,
          column: 53
        }
      },
      "103": {
        start: {
          line: 108,
          column: 16
        },
        end: {
          line: 108,
          column: 73
        }
      },
      "104": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 114,
          column: 24
        }
      },
      "105": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 26
        }
      },
      "106": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 124,
          column: 24
        }
      },
      "107": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 126,
          column: 26
        }
      },
      "108": {
        start: {
          line: 127,
          column: 16
        },
        end: {
          line: 127,
          column: 170
        }
      },
      "109": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 128,
          column: 29
        }
      },
      "110": {
        start: {
          line: 130,
          column: 16
        },
        end: {
          line: 130,
          column: 43
        }
      },
      "111": {
        start: {
          line: 131,
          column: 16
        },
        end: {
          line: 135,
          column: 24
        }
      },
      "112": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 137,
          column: 26
        }
      },
      "113": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 138,
          column: 75
        }
      },
      "114": {
        start: {
          line: 139,
          column: 16
        },
        end: {
          line: 139,
          column: 41
        }
      },
      "115": {
        start: {
          line: 141,
          column: 16
        },
        end: {
          line: 141,
          column: 41
        }
      },
      "116": {
        start: {
          line: 142,
          column: 16
        },
        end: {
          line: 142,
          column: 82
        }
      },
      "117": {
        start: {
          line: 143,
          column: 16
        },
        end: {
          line: 143,
          column: 72
        }
      },
      "118": {
        start: {
          line: 144,
          column: 16
        },
        end: {
          line: 144,
          column: 39
        }
      },
      "119": {
        start: {
          line: 145,
          column: 16
        },
        end: {
          line: 145,
          column: 28
        }
      },
      "120": {
        start: {
          line: 146,
          column: 21
        },
        end: {
          line: 149,
          column: 20
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 50,
            column: 73
          },
          end: {
            line: 50,
            column: 74
          }
        },
        loc: {
          start: {
            line: 50,
            column: 92
          },
          end: {
            line: 152,
            column: 5
          }
        },
        line: 50
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 50,
            column: 136
          },
          end: {
            line: 50,
            column: 137
          }
        },
        loc: {
          start: {
            line: 50,
            column: 148
          },
          end: {
            line: 152,
            column: 1
          }
        },
        line: 50
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 52,
            column: 29
          },
          end: {
            line: 52,
            column: 30
          }
        },
        loc: {
          start: {
            line: 52,
            column: 43
          },
          end: {
            line: 151,
            column: 5
          }
        },
        line: 52
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 150,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 57
          }
        }, {
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 64,
            column: 24
          }
        }, {
          start: {
            line: 65,
            column: 12
          },
          end: {
            line: 89,
            column: 24
          }
        }, {
          start: {
            line: 90,
            column: 12
          },
          end: {
            line: 103,
            column: 24
          }
        }, {
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 114,
            column: 24
          }
        }, {
          start: {
            line: 115,
            column: 12
          },
          end: {
            line: 124,
            column: 24
          }
        }, {
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 128,
            column: 29
          }
        }, {
          start: {
            line: 129,
            column: 12
          },
          end: {
            line: 135,
            column: 24
          }
        }, {
          start: {
            line: 136,
            column: 12
          },
          end: {
            line: 139,
            column: 41
          }
        }, {
          start: {
            line: 140,
            column: 12
          },
          end: {
            line: 145,
            column: 28
          }
        }, {
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 149,
            column: 20
          }
        }],
        line: 53
      },
      "36": {
        loc: {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 61,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 61,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "37": {
        loc: {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 73,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 73,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "38": {
        loc: {
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 80,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 80,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "39": {
        loc: {
          start: {
            line: 92,
            column: 16
          },
          end: {
            line: 96,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 16
          },
          end: {
            line: 96,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "40": {
        loc: {
          start: {
            line: 134,
            column: 115
          },
          end: {
            line: 134,
            column: 133
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 134,
            column: 115
          },
          end: {
            line: 134,
            column: 124
          }
        }, {
          start: {
            line: 134,
            column: 128
          },
          end: {
            line: 134,
            column: 133
          }
        }],
        line: 134
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/resend-verification/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,6BAAoC;AACpC,gDAA0B;AAC1B,wDAAkC;AAClC,qCAAwC;AACxC,gEAA+D;AAC/D,6EAAwF;AAM3E,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;oBAC9D,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAA9B,KAAK,GAAK,CAAA,SAAoB,CAAA,MAAzB;gBAEb,IAAI,CAAC,KAAK,EAAE,CAAC;oBACL,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAQ,CAAC;oBACrD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACxB,CAAC,EAAA;;gBAFI,IAAI,GAAG,SAEX;gBAEF,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,+DAA+D;oBAC/D,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,OAAO,EAAE,6FAA6F,EAAE;yBACjH,CAAC,EAAC;gBACL,CAAC;gBAED,oCAAoC;gBACpC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE;yBAChD,CAAC,EAAC;gBACL,CAAC;gBAGmB,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC;wBAC3D,KAAK,EAAE;4BACL,UAAU,EAAE,KAAK;4BACjB,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,gCAAgC;yBACxF;wBACD,OAAO,EAAE;4BACP,OAAO,EAAE,MAAM;yBAChB;qBACF,CAAC,EAAA;;gBARI,WAAW,GAAG,SAQlB;gBAEF,IAAI,WAAW,EAAE,CAAC;oBACV,KAAK,GAAG,IAAI,KAAK,CAAC,0FAA0F,CAAQ,CAAC;oBAC3H,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,sDAAsD;gBACtD,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE;4BACL,UAAU,EAAE,KAAK;4BACjB,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;yBAC5B;qBACF,CAAC,EAAA;;gBANF,sDAAsD;gBACtD,SAKE,CAAC;gBAGG,iBAAiB,GAAG,IAAA,SAAM,GAAE,CAAC;gBAC7B,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAE/D,+DAA+D;gBAC/D,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE;4BACL,UAAU,EAAE,KAAK;yBAClB;qBACF,CAAC,EAAA;;gBALF,+DAA+D;gBAC/D,SAIE,CAAC;gBAEH,qBAAM,gBAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;wBACpC,IAAI,EAAE;4BACJ,UAAU,EAAE,KAAK;4BACjB,KAAK,EAAE,iBAAiB;4BACxB,OAAO,EAAE,WAAW;yBACrB;qBACF,CAAC,EAAA;;gBANF,SAME,CAAC;gBAGG,eAAe,GAAG,UAAG,OAAO,CAAC,GAAG,CAAC,YAAY,sCAA4B,iBAAiB,oBAAU,kBAAkB,CAAC,KAAK,CAAC,CAAE,CAAC;;;;gBAGpI,qBAAM,IAAA,iBAAS,EAAC;wBACd,EAAE,EAAE,KAAK;wBACT,OAAO,EAAE,6CAA6C;wBACtD,QAAQ,EAAE,eAAK,CAAC,aAAa,CAAC,qCAAiB,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,gBAAgB,EAAE,eAAe,EAAE,CAAC;qBACtH,CAAC,EAAA;;gBAJF,SAIE,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,uCAAgC,KAAK,CAAE,CAAC,CAAC;;;;gBAErD,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,YAAU,CAAC,CAAC;gBAC1D,KAAK,GAAG,IAAI,KAAK,CAAC,oCAAoC,CAAQ,CAAC;gBACrE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBACvB,MAAM,KAAK,CAAC;qBAGd,sBAAO,qBAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,EAAE,OAAO,EAAE,uCAAuC,EAAE;iBAC3D,CAAC,EAAC;;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/resend-verification/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { v4 as uuidv4 } from 'uuid';\nimport React from 'react';\nimport prisma from '@/lib/prisma';\nimport { sendEmail } from '@/lib/email';\nimport { VerificationEmail } from '@/emails/VerificationEmail';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface ResendVerificationResponse {\n  message: string;\n}\n\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ResendVerificationResponse>>> => {\n  const { email } = await request.json();\n\n  if (!email) {\n    const error = new Error('Email is required.') as any;\n    error.statusCode = 400;\n    throw error;\n  }\n\n  // Find the user\n  const user = await prisma.user.findUnique({\n    where: { email: email },\n  });\n\n  if (!user) {\n    // For security reasons, don't reveal if the user exists or not\n    return NextResponse.json({\n      success: true,\n      data: { message: 'If an account with that email exists and is unverified, a verification email has been sent.' }\n    });\n  }\n\n  // Check if user is already verified\n  if (user.emailVerified) {\n    return NextResponse.json({\n      success: true,\n      data: { message: 'Email is already verified.' }\n    });\n  }\n\n  // Check for rate limiting - only allow one verification email per 5 minutes\n  const recentToken = await prisma.verificationToken.findFirst({\n    where: {\n      identifier: email,\n      expires: { gt: new Date(Date.now() - 5 * 60 * 1000) }, // Created within last 5 minutes\n    },\n    orderBy: {\n      expires: 'desc',\n    },\n  });\n\n  if (recentToken) {\n    const error = new Error('A verification email was recently sent. Please wait 5 minutes before requesting another.') as any;\n    error.statusCode = 429;\n    throw error;\n  }\n\n  // Clean up any existing expired tokens for this email\n  await prisma.verificationToken.deleteMany({\n    where: {\n      identifier: email,\n      expires: { lt: new Date() },\n    },\n  });\n\n  // Generate new verification token\n  const verificationToken = uuidv4();\n  const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  // Store verification token (replace any existing valid tokens)\n  await prisma.verificationToken.deleteMany({\n    where: {\n      identifier: email,\n    },\n  });\n\n  await prisma.verificationToken.create({\n    data: {\n      identifier: email,\n      token: verificationToken,\n      expires: tokenExpiry,\n    },\n  });\n\n  // Send verification email\n  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(email)}`;\n\n  try {\n    await sendEmail({\n      to: email,\n      subject: \"Verify your email for FAAFO Career Platform\",\n      template: React.createElement(VerificationEmail, { username: user.name || email, verificationLink: verificationUrl }),\n    });\n    console.log(`Verification email resent to ${email}`);\n  } catch (emailError) {\n    console.error('Failed to send verification email:', emailError);\n    const error = new Error('Failed to send verification email.') as any;\n    error.statusCode = 500;\n    throw error;\n  }\n\n  return NextResponse.json({\n    success: true,\n    data: { message: 'Verification email sent successfully.' }\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4f4120b46a95e2cddc8705a6ec86e5d80a58d050"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2l59hl9onv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2l59hl9onv();
var __awaiter =
/* istanbul ignore next */
(cov_2l59hl9onv().s[0]++,
/* istanbul ignore next */
(cov_2l59hl9onv().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2l59hl9onv().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2l59hl9onv().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2l59hl9onv().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2l59hl9onv().f[1]++;
    cov_2l59hl9onv().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2l59hl9onv().f[2]++;
      cov_2l59hl9onv().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2l59hl9onv().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_2l59hl9onv().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_2l59hl9onv().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2l59hl9onv().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2l59hl9onv().f[4]++;
      cov_2l59hl9onv().s[4]++;
      try {
        /* istanbul ignore next */
        cov_2l59hl9onv().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2l59hl9onv().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2l59hl9onv().f[5]++;
      cov_2l59hl9onv().s[7]++;
      try {
        /* istanbul ignore next */
        cov_2l59hl9onv().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2l59hl9onv().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2l59hl9onv().f[6]++;
      cov_2l59hl9onv().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2l59hl9onv().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2l59hl9onv().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2l59hl9onv().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2l59hl9onv().s[12]++,
/* istanbul ignore next */
(cov_2l59hl9onv().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_2l59hl9onv().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2l59hl9onv().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2l59hl9onv().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_2l59hl9onv().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2l59hl9onv().f[8]++;
        cov_2l59hl9onv().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2l59hl9onv().b[6][0]++;
          cov_2l59hl9onv().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2l59hl9onv().b[6][1]++;
        }
        cov_2l59hl9onv().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2l59hl9onv().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2l59hl9onv().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2l59hl9onv().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2l59hl9onv().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2l59hl9onv().f[9]++;
    cov_2l59hl9onv().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2l59hl9onv().f[10]++;
    cov_2l59hl9onv().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2l59hl9onv().f[11]++;
      cov_2l59hl9onv().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2l59hl9onv().f[12]++;
    cov_2l59hl9onv().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_2l59hl9onv().b[9][0]++;
      cov_2l59hl9onv().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2l59hl9onv().b[9][1]++;
    }
    cov_2l59hl9onv().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2l59hl9onv().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2l59hl9onv().s[25]++;
      try {
        /* istanbul ignore next */
        cov_2l59hl9onv().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[15][0]++,
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[16][1]++,
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2l59hl9onv().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2l59hl9onv().b[12][0]++;
          cov_2l59hl9onv().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2l59hl9onv().b[12][1]++;
        }
        cov_2l59hl9onv().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2l59hl9onv().b[18][0]++;
          cov_2l59hl9onv().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2l59hl9onv().b[18][1]++;
        }
        cov_2l59hl9onv().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2l59hl9onv().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2l59hl9onv().b[19][1]++;
            cov_2l59hl9onv().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_2l59hl9onv().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2l59hl9onv().b[19][2]++;
            cov_2l59hl9onv().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_2l59hl9onv().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2l59hl9onv().b[19][3]++;
            cov_2l59hl9onv().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_2l59hl9onv().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2l59hl9onv().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_2l59hl9onv().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2l59hl9onv().b[19][4]++;
            cov_2l59hl9onv().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2l59hl9onv().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2l59hl9onv().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2l59hl9onv().b[19][5]++;
            cov_2l59hl9onv().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2l59hl9onv().b[20][0]++;
              cov_2l59hl9onv().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2l59hl9onv().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2l59hl9onv().b[20][1]++;
            }
            cov_2l59hl9onv().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2l59hl9onv().b[23][0]++;
              cov_2l59hl9onv().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2l59hl9onv().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2l59hl9onv().b[23][1]++;
            }
            cov_2l59hl9onv().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2l59hl9onv().b[25][0]++;
              cov_2l59hl9onv().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2l59hl9onv().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_2l59hl9onv().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2l59hl9onv().b[25][1]++;
            }
            cov_2l59hl9onv().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_2l59hl9onv().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2l59hl9onv().b[27][0]++;
              cov_2l59hl9onv().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2l59hl9onv().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2l59hl9onv().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2l59hl9onv().b[27][1]++;
            }
            cov_2l59hl9onv().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2l59hl9onv().b[29][0]++;
              cov_2l59hl9onv().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2l59hl9onv().b[29][1]++;
            }
            cov_2l59hl9onv().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2l59hl9onv().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2l59hl9onv().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2l59hl9onv().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2l59hl9onv().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2l59hl9onv().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2l59hl9onv().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2l59hl9onv().b[30][0]++;
      cov_2l59hl9onv().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2l59hl9onv().b[30][1]++;
    }
    cov_2l59hl9onv().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2l59hl9onv().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2l59hl9onv().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_2l59hl9onv().s[67]++,
/* istanbul ignore next */
(cov_2l59hl9onv().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_2l59hl9onv().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2l59hl9onv().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2l59hl9onv().f[13]++;
  cov_2l59hl9onv().s[68]++;
  return /* istanbul ignore next */(cov_2l59hl9onv().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2l59hl9onv().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2l59hl9onv().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_2l59hl9onv().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2l59hl9onv().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2l59hl9onv().s[70]++;
exports.POST = void 0;
var server_1 =
/* istanbul ignore next */
(cov_2l59hl9onv().s[71]++, require("next/server"));
var uuid_1 =
/* istanbul ignore next */
(cov_2l59hl9onv().s[72]++, require("uuid"));
var react_1 =
/* istanbul ignore next */
(cov_2l59hl9onv().s[73]++, __importDefault(require("react")));
var prisma_1 =
/* istanbul ignore next */
(cov_2l59hl9onv().s[74]++, __importDefault(require("@/lib/prisma")));
var email_1 =
/* istanbul ignore next */
(cov_2l59hl9onv().s[75]++, require("@/lib/email"));
var VerificationEmail_1 =
/* istanbul ignore next */
(cov_2l59hl9onv().s[76]++, require("@/emails/VerificationEmail"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_2l59hl9onv().s[77]++, require("@/lib/unified-api-error-handler"));
/* istanbul ignore next */
cov_2l59hl9onv().s[78]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_2l59hl9onv().f[14]++;
  cov_2l59hl9onv().s[79]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_2l59hl9onv().f[15]++;
    var email, error, user, recentToken, error, verificationToken, tokenExpiry, verificationUrl, emailError_1, error;
    /* istanbul ignore next */
    cov_2l59hl9onv().s[80]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_2l59hl9onv().f[16]++;
      cov_2l59hl9onv().s[81]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][0]++;
          cov_2l59hl9onv().s[82]++;
          return [4 /*yield*/, request.json()];
        case 1:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][1]++;
          cov_2l59hl9onv().s[83]++;
          email = _a.sent().email;
          /* istanbul ignore next */
          cov_2l59hl9onv().s[84]++;
          if (!email) {
            /* istanbul ignore next */
            cov_2l59hl9onv().b[36][0]++;
            cov_2l59hl9onv().s[85]++;
            error = new Error('Email is required.');
            /* istanbul ignore next */
            cov_2l59hl9onv().s[86]++;
            error.statusCode = 400;
            /* istanbul ignore next */
            cov_2l59hl9onv().s[87]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_2l59hl9onv().b[36][1]++;
          }
          cov_2l59hl9onv().s[88]++;
          return [4 /*yield*/, prisma_1.default.user.findUnique({
            where: {
              email: email
            }
          })];
        case 2:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][2]++;
          cov_2l59hl9onv().s[89]++;
          user = _a.sent();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[90]++;
          if (!user) {
            /* istanbul ignore next */
            cov_2l59hl9onv().b[37][0]++;
            cov_2l59hl9onv().s[91]++;
            // For security reasons, don't reveal if the user exists or not
            return [2 /*return*/, server_1.NextResponse.json({
              success: true,
              data: {
                message: 'If an account with that email exists and is unverified, a verification email has been sent.'
              }
            })];
          } else
          /* istanbul ignore next */
          {
            cov_2l59hl9onv().b[37][1]++;
          }
          // Check if user is already verified
          cov_2l59hl9onv().s[92]++;
          if (user.emailVerified) {
            /* istanbul ignore next */
            cov_2l59hl9onv().b[38][0]++;
            cov_2l59hl9onv().s[93]++;
            return [2 /*return*/, server_1.NextResponse.json({
              success: true,
              data: {
                message: 'Email is already verified.'
              }
            })];
          } else
          /* istanbul ignore next */
          {
            cov_2l59hl9onv().b[38][1]++;
          }
          cov_2l59hl9onv().s[94]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.findFirst({
            where: {
              identifier: email,
              expires: {
                gt: new Date(Date.now() - 5 * 60 * 1000)
              } // Created within last 5 minutes
            },
            orderBy: {
              expires: 'desc'
            }
          })];
        case 3:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][3]++;
          cov_2l59hl9onv().s[95]++;
          recentToken = _a.sent();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[96]++;
          if (recentToken) {
            /* istanbul ignore next */
            cov_2l59hl9onv().b[39][0]++;
            cov_2l59hl9onv().s[97]++;
            error = new Error('A verification email was recently sent. Please wait 5 minutes before requesting another.');
            /* istanbul ignore next */
            cov_2l59hl9onv().s[98]++;
            error.statusCode = 429;
            /* istanbul ignore next */
            cov_2l59hl9onv().s[99]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_2l59hl9onv().b[39][1]++;
          }
          // Clean up any existing expired tokens for this email
          cov_2l59hl9onv().s[100]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.deleteMany({
            where: {
              identifier: email,
              expires: {
                lt: new Date()
              }
            }
          })];
        case 4:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][4]++;
          cov_2l59hl9onv().s[101]++;
          // Clean up any existing expired tokens for this email
          _a.sent();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[102]++;
          verificationToken = (0, uuid_1.v4)();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[103]++;
          tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
          // Store verification token (replace any existing valid tokens)
          /* istanbul ignore next */
          cov_2l59hl9onv().s[104]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.deleteMany({
            where: {
              identifier: email
            }
          })];
        case 5:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][5]++;
          cov_2l59hl9onv().s[105]++;
          // Store verification token (replace any existing valid tokens)
          _a.sent();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[106]++;
          return [4 /*yield*/, prisma_1.default.verificationToken.create({
            data: {
              identifier: email,
              token: verificationToken,
              expires: tokenExpiry
            }
          })];
        case 6:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][6]++;
          cov_2l59hl9onv().s[107]++;
          _a.sent();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[108]++;
          verificationUrl = "".concat(process.env.NEXTAUTH_URL, "/auth/verify-email?token=").concat(verificationToken, "&email=").concat(encodeURIComponent(email));
          /* istanbul ignore next */
          cov_2l59hl9onv().s[109]++;
          _a.label = 7;
        case 7:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][7]++;
          cov_2l59hl9onv().s[110]++;
          _a.trys.push([7, 9,, 10]);
          /* istanbul ignore next */
          cov_2l59hl9onv().s[111]++;
          return [4 /*yield*/, (0, email_1.sendEmail)({
            to: email,
            subject: "Verify your email for FAAFO Career Platform",
            template: react_1.default.createElement(VerificationEmail_1.VerificationEmail, {
              username:
              /* istanbul ignore next */
              (cov_2l59hl9onv().b[40][0]++, user.name) ||
              /* istanbul ignore next */
              (cov_2l59hl9onv().b[40][1]++, email),
              verificationLink: verificationUrl
            })
          })];
        case 8:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][8]++;
          cov_2l59hl9onv().s[112]++;
          _a.sent();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[113]++;
          console.log("Verification email resent to ".concat(email));
          /* istanbul ignore next */
          cov_2l59hl9onv().s[114]++;
          return [3 /*break*/, 10];
        case 9:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][9]++;
          cov_2l59hl9onv().s[115]++;
          emailError_1 = _a.sent();
          /* istanbul ignore next */
          cov_2l59hl9onv().s[116]++;
          console.error('Failed to send verification email:', emailError_1);
          /* istanbul ignore next */
          cov_2l59hl9onv().s[117]++;
          error = new Error('Failed to send verification email.');
          /* istanbul ignore next */
          cov_2l59hl9onv().s[118]++;
          error.statusCode = 500;
          /* istanbul ignore next */
          cov_2l59hl9onv().s[119]++;
          throw error;
        case 10:
          /* istanbul ignore next */
          cov_2l59hl9onv().b[35][10]++;
          cov_2l59hl9onv().s[120]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              message: 'Verification email sent successfully.'
            }
          })];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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