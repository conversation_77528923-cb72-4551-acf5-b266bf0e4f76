{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/skill-assessment-form.test.tsx", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,aAAa;AACb,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAM,OAAA,CAAC;IACzB,KAAK,EAAE;QACL,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;KACnB;CACF,CAAC,EALwB,CAKxB,CAAC,CAAC;AAEJ,gDAAgD;AAChD,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,cAAM,OAAA,CAAC;IACzC,MAAM,EAAE,UAAC,EAAuC;QAArC,IAAA,KAAK,WAAA,EAAE,aAAa,mBAAA,EAAK,KAAK,cAAhC,0BAAkC,CAAF;QAAY,OAAA,CACnD,2CACE,IAAI,EAAC,OAAO,EACZ,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EACf,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAzC,CAAyC,iBAC9C,QAAQ,IAChB,KAAK,EACT,CACH,CAAA;KAAA;CACF,CAAC,EAVwC,CAUxC,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,cAAM,OAAA,CAAC;IACvC,IAAI,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,wDAAiB,MAAM,IAAK,KAAK,cAAG,QAAQ,IAAO,CAAA;KAAA;IAC1F,WAAW,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,wDAAiB,cAAc,IAAK,KAAK,cAAG,QAAQ,IAAO,CAAA;KAAA;IACzG,eAAe,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,wDAAiB,kBAAkB,IAAK,KAAK,cAAG,QAAQ,IAAO,CAAA;KAAA;IACjH,UAAU,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,wDAAiB,aAAa,IAAK,KAAK,cAAG,QAAQ,IAAO,CAAA;KAAA;IACvG,SAAS,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,wDAAiB,YAAY,IAAK,KAAK,cAAG,QAAQ,IAAO,CAAA;KAAA;CACtG,CAAC,EANsC,CAMtC,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,cAAM,OAAA,CAAC;IACzC,MAAM,EAAE,UAAC,EAA8C;QAA5C,IAAA,QAAQ,cAAA,EAAE,OAAO,aAAA,EAAE,QAAQ,cAAA,EAAK,KAAK,cAAvC,mCAAyC,CAAF;QAAY,OAAA,CAC1D,4CAAQ,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,iBAAc,QAAQ,IAAK,KAAK,cACzE,QAAQ,IACF,CACV,CAAA;KAAA;CACF,CAAC,EANwC,CAMxC,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,cAAM,OAAA,CAAC;IACxC,KAAK,EAAE,UAAC,KAAU,IAAK,OAAA,0DAAmB,OAAO,IAAK,KAAK,EAAI,EAAxC,CAAwC;CAChE,CAAC,EAFuC,CAEvC,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,cAAM,OAAA,CAAC;IACxC,KAAK,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,0DAAmB,OAAO,IAAK,KAAK,cAAG,QAAQ,IAAS,CAAA;KAAA;CACjG,CAAC,EAFuC,CAEvC,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,cAAM,OAAA,CAAC;IAC3C,QAAQ,EAAE,UAAC,KAAU,IAAK,OAAA,6DAAsB,UAAU,IAAK,KAAK,EAAI,EAA9C,CAA8C;CACzE,CAAC,EAF0C,CAE1C,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,cAAM,OAAA,CAAC;IACxC,KAAK,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,yDAAkB,OAAO,IAAK,KAAK,cAAG,QAAQ,IAAQ,CAAA;KAAA;CAC/F,CAAC,EAFuC,CAEvC,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,cAAM,OAAA,CAAC;IAC3C,QAAQ,EAAE,UAAC,EAAwB;QAAtB,IAAA,KAAK,WAAA,EAAK,KAAK,cAAjB,SAAmB,CAAF;QAAY,OAAA,wDAAiB,UAAU,gBAAa,KAAK,IAAM,KAAK,EAAI,CAAA;KAAA;CACrG,CAAC,EAF0C,CAE1C,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,cAAM,OAAA,CAAC;IACxC,KAAK,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,wDAAiB,OAAO,IAAK,KAAK,cAAG,QAAQ,IAAO,CAAA;KAAA;IAC5F,gBAAgB,EAAE,UAAC,EAA2B;QAAzB,IAAA,QAAQ,cAAA,EAAK,KAAK,cAApB,YAAsB,CAAF;QAAY,OAAA,wDAAiB,mBAAmB,IAAK,KAAK,cAAG,QAAQ,IAAO,CAAA;KAAA;CACpH,CAAC,EAHuC,CAGvC,CAAC,CAAC;AAEJ,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,CAAC;IAC/B,OAAO,EAAE,cAAM,OAAA,+CAAiB,cAAc,GAAG,EAAlC,CAAkC;IACjD,IAAI,EAAE,cAAM,OAAA,+CAAiB,WAAW,GAAG,EAA/B,CAA+B;IAC3C,UAAU,EAAE,cAAM,OAAA,+CAAiB,kBAAkB,GAAG,EAAtC,CAAsC;IACxD,QAAQ,EAAE,cAAM,OAAA,+CAAiB,gBAAgB,GAAG,EAApC,CAAoC;IACpD,KAAK,EAAE,cAAM,OAAA,+CAAiB,YAAY,GAAG,EAAhC,CAAgC;CAC9C,CAAC,EAN8B,CAM9B,CAAC,CAAC;AApFJ;;;;;;;GAOG;AAEH,gDAA0B;AAC1B,gDAAiF;AACjF,2EAAoD;AACpD,qCAAmC;AACnC,gGAA0E;AAyE1E,kBAAkB;AAClB,IAAM,UAAU,GAAG;IACjB;QACE,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,aAAa;QACvB,WAAW,EAAE,iCAAiC;KAC/C;IACD;QACE,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,UAAU;QACpB,WAAW,EAAE,iBAAiB;KAC/B;IACD;QACE,EAAE,EAAE,UAAU;QACd,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,aAAa;QACvB,WAAW,EAAE,6BAA6B;KAC3C;CACF,CAAC;AAEF,IAAM,sBAAsB,GAAG;IAC7B;QACE,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,CAAC;QACb,eAAe,EAAE,CAAC;QAClB,cAAc,EAAE,iBAA0B;QAC1C,iBAAiB,EAAE,CAAC;QACpB,QAAQ,EAAE,iBAAiB;QAC3B,KAAK,EAAE,gBAAgB;KACxB;CACF,CAAC;AAEF,QAAQ,CAAC,qDAAqD,EAAE;IAC9D,IAAM,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IAC/B,IAAM,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IACpC,IAAM,uBAAuB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IAE1C,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,iBAAiB,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE;QACnD,EAAE,CAAC,kEAAkE,EAAE;;;;wBACrE,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,kBAAkB,EAAE,sBAAsB,EAC1C,mBAAmB,EAAE,uBAAuB,GAC5C,CACH,CAAC;wBAEF,8CAA8C;wBAC9C,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACrE,CAAC,CAAC,EAAA;;wBAHF,8CAA8C;wBAC9C,SAEE,CAAC;wBAEH,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBACxE,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aACxE,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE;;;;;wBAC9D,QAAQ,GAAK,IAAA,cAAM,EACzB,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,kBAAkB,EAAE,EAAE,EACtB,mBAAmB,EAAE,uBAAuB,GAC5C,CACH,SAPe,CAOd;wBAEF,mCAAmC;wBACnC,MAAM,CAAC,cAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;wBAEvF,kCAAkC;wBAClC,QAAQ,CACN,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,kBAAkB,EAAE,sBAAsB,EAC1C,mBAAmB,EAAE,uBAAuB,GAC5C,CACH,CAAC;wBAEF,sCAAsC;wBACtC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACrE,CAAC,CAAC,EAAA;;wBAHF,sCAAsC;wBACtC,SAEE,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE;;;;;wBAChE,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,kBAAkB,EAAE,EAAE,EACtB,mBAAmB,EAAE,uBAAuB,GAC5C,CACH,CAAC;wBAEI,UAAU,GAAG,cAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,CAAC;wBAElF,qBAAM,oBAAS,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,EAAA;;wBAA9C,SAA8C,CAAC;wBAE/C,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,uBAAuB,CAAC,CAAC,gBAAgB,EAAE,CAAC;4BACrD,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,wEAAwE,EAAE;;;;;wBAC3E,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,mBAAmB,EAAE,uBAAuB,GAC5C,CACH,CAAC;wBAEI,WAAW,GAAG,cAAM,CAAC,oBAAoB,CAAC,gCAAgC,CAAC,CAAC;wBAElF,yBAAyB;wBACzB,qBAAM,oBAAS,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,EAAA;;wBADzC,yBAAyB;wBACzB,SAAyC,CAAC;wBAE1C,0BAA0B;wBAC1B,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;4BACzD,CAAC,CAAC,EAAA;;wBAHF,0BAA0B;wBAC1B,SAEE,CAAC;wBAGkB,qBAAM,cAAM,CAAC,UAAU,CAAC,YAAY,CAAC,EAAA;;wBAApD,YAAY,GAAG,SAAqC;wBAC1D,qBAAM,oBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAA;;wBAAnC,SAAmC,CAAC;wBAEpC,iCAAiC;wBACjC,qBAAM,IAAA,eAAO,EAAC;gCACZ,IAAM,cAAc,GAAG,cAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,CAAC;gCACtF,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;4BACnD,CAAC,CAAC,EAAA;;wBAJF,iCAAiC;wBACjC,SAGE,CAAC;wBAEH,2BAA2B;wBAC3B,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;;;;aACrC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE;;;;;wBAC3D,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,mBAAmB,EAAE,uBAAuB,GAC5C,CACH,CAAC;wBAEI,WAAW,GAAG,cAAM,CAAC,oBAAoB,CAAC,gCAAgC,CAAC,CAAC;wBAElF,qBAAM,oBAAS,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAA;;wBAA3C,SAA2C,CAAC;wBAE5C,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;4BAC3D,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAGkB,qBAAM,cAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAA;;wBAAhD,YAAY,GAAG,SAAiC;wBACtD,qBAAM,oBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAA;;wBAAnC,SAAmC,CAAC;wBAEpC,iCAAiC;wBACjC,qBAAM,IAAA,eAAO,EAAC;gCACZ,IAAM,cAAc,GAAG,cAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,CAAC;gCACtF,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;4BAC/C,CAAC,CAAC,EAAA;;wBAJF,iCAAiC;wBACjC,SAGE,CAAC;wBAEH,0DAA0D;wBAC1D,MAAM,CAAC,uBAAuB,CAAC,CAAC,gBAAgB,EAAE,CAAC;;;;aACpD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE;QAC1B,EAAE,CAAC,qCAAqC,EAAE;;;;;wBACxC,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,GAChC,CACH,CAAC;wBAEI,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;wBAC3D,qBAAM,oBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAA;;wBAAnC,SAAmC,CAAC;wBAEpC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;4BACzE,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEH,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;;;;aAC7C,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;;;;;wBAClC,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,GAChC,CACH,CAAC;wBAEI,UAAU,GAAG,cAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,CAAC;wBAClF,qBAAM,oBAAS,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,EAAA;;wBAA9C,SAA8C,CAAC;wBAIzC,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;wBAC3D,qBAAM,oBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAA;;wBAAnC,SAAmC,CAAC;wBAEpC,oDAAoD;wBACpD,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;4BAC1C,CAAC,CAAC,EAAA;;wBAHF,oDAAoD;wBACpD,SAEE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE;QACpB,EAAE,CAAC,uDAAuD,EAAE;;;;;wBAC1D,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,IAAI,EAAC,MAAM,EACX,cAAc,EAAE,CAAC,GACjB,CACH,CAAC;wBAEF,mCAAmC;wBACnC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBAGvD,SAAS,GAAG,cAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;wBACnE,qBAAM,oBAAS,CAAC,KAAK,CAAC,SAAS,CAAC,EAAA;;wBAAhC,SAAgC,CAAC;wBAEjC,kCAAkC;wBAClC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aAC9D,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;;;;;wBACjD,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,iBAAiB,EAChC,IAAI,EAAC,MAAM,EACX,qBAAqB,EAAE,IAAI,GAC3B,CACH,CAAC;wBAEI,UAAU,GAAG,cAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,CAAC;wBAClF,qBAAM,oBAAS,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,EAAA;;wBAA9C,SAA8C,CAAC;wBAEzC,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;wBAC3D,qBAAM,oBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAA;;wBAAnC,SAAmC,CAAC;wBAEpC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;4BAC1C,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEH,kCAAkC;wBAClC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;;;;aAC9C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,wCAAwC,EAAE;;;;;wBACrC,sBAAsB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;wBAEvF,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,YAAY,EACtB,aAAa,EAAE,sBAAsB,GACrC,CACH,CAAC;wBAEI,WAAW,GAAG,cAAM,CAAC,oBAAoB,CAAC,gCAAgC,CAAC,CAAC;wBAClF,qBAAM,oBAAS,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,EAAA;;wBAAzC,SAAyC,CAAC;wBAE1C,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;4BAC9D,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEH,sDAAsD;wBACtD,MAAM,CAAC,WAAW,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aACzC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;;wBACrC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;wBAElF,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,QAAQ,EAAE,iBAAiB,EAC3B,aAAa,EAAE,iBAAiB,GAChC,CACH,CAAC;wBAEI,UAAU,GAAG,cAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,CAAC;wBAClF,qBAAM,oBAAS,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,EAAA;;wBAA9C,SAA8C,CAAC;wBAEzC,YAAY,GAAG,cAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;wBAC3D,qBAAM,oBAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAA;;wBAAnC,SAAmC,CAAC;wBAEpC,qBAAM,IAAA,eAAO,EAAC;gCACZ,MAAM,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,EAAE,CAAC;4BAC/C,CAAC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBAEH,8CAA8C;wBAC9C,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;;;;aACnE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/skill-assessment-form.test.tsx"], "sourcesContent": ["/**\n * Skill Assessment Form Tests\n * \n * Tests Skill Assessment Form component functionality, rendering, user interactions, and edge cases.\n * \n * @category unit\n * @requires React Testing Library, component mocking\n */\n\nimport React from 'react';\nimport { render, screen, fireEvent, waitFor, act } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport '@testing-library/jest-dom';\nimport SkillAssessmentForm from '@/components/skills/SkillAssessmentForm';\n\n// Mock toast\njest.mock('sonner', () => ({\n  toast: {\n    error: jest.fn(),\n    success: jest.fn(),\n  },\n}));\n\n// Mock UI components that cause issues in tests\njest.mock('@/components/ui/slider', () => ({\n  Slider: ({ value, onValueChange, ...props }: any) => (\n    <input\n      type=\"range\"\n      value={value[0]}\n      onChange={(e) => onValueChange([parseInt(e.target.value)])}\n      data-testid=\"slider\"\n      {...props}\n    />\n  ),\n}));\n\njest.mock('@/components/ui/card', () => ({\n  Card: ({ children, ...props }: any) => <div data-testid=\"card\" {...props}>{children}</div>,\n  CardContent: ({ children, ...props }: any) => <div data-testid=\"card-content\" {...props}>{children}</div>,\n  CardDescription: ({ children, ...props }: any) => <div data-testid=\"card-description\" {...props}>{children}</div>,\n  CardHeader: ({ children, ...props }: any) => <div data-testid=\"card-header\" {...props}>{children}</div>,\n  CardTitle: ({ children, ...props }: any) => <div data-testid=\"card-title\" {...props}>{children}</div>,\n}));\n\njest.mock('@/components/ui/button', () => ({\n  Button: ({ children, onClick, disabled, ...props }: any) => (\n    <button onClick={onClick} disabled={disabled} data-testid=\"button\" {...props}>\n      {children}\n    </button>\n  ),\n}));\n\njest.mock('@/components/ui/input', () => ({\n  Input: (props: any) => <input data-testid=\"input\" {...props} />,\n}));\n\njest.mock('@/components/ui/label', () => ({\n  Label: ({ children, ...props }: any) => <label data-testid=\"label\" {...props}>{children}</label>,\n}));\n\njest.mock('@/components/ui/textarea', () => ({\n  Textarea: (props: any) => <textarea data-testid=\"textarea\" {...props} />,\n}));\n\njest.mock('@/components/ui/badge', () => ({\n  Badge: ({ children, ...props }: any) => <span data-testid=\"badge\" {...props}>{children}</span>,\n}));\n\njest.mock('@/components/ui/progress', () => ({\n  Progress: ({ value, ...props }: any) => <div data-testid=\"progress\" data-value={value} {...props} />,\n}));\n\njest.mock('@/components/ui/alert', () => ({\n  Alert: ({ children, ...props }: any) => <div data-testid=\"alert\" {...props}>{children}</div>,\n  AlertDescription: ({ children, ...props }: any) => <div data-testid=\"alert-description\" {...props}>{children}</div>,\n}));\n\n// Mock Lucide icons\njest.mock('lucide-react', () => ({\n  Loader2: () => <div data-testid=\"loader2-icon\" />,\n  Star: () => <div data-testid=\"star-icon\" />,\n  TrendingUp: () => <div data-testid=\"trending-up-icon\" />,\n  BookOpen: () => <div data-testid=\"book-open-icon\" />,\n  Award: () => <div data-testid=\"award-icon\" />,\n}));\n\n// Mock skill data\nconst mockSkills = [\n  {\n    id: 'skill-1',\n    name: 'JavaScript',\n    category: 'Programming',\n    description: 'JavaScript programming language',\n  },\n  {\n    id: 'skill-2',\n    name: 'React',\n    category: 'Frontend',\n    description: 'React framework',\n  },\n  {\n    id: 'common-1',\n    name: 'Python',\n    category: 'Programming',\n    description: 'Python programming language',\n  },\n];\n\nconst mockInitialAssessments = [\n  {\n    skillId: 'skill-1',\n    skillName: 'JavaScript',\n    selfRating: 7,\n    confidenceLevel: 8,\n    assessmentType: 'SELF_ASSESSMENT' as const,\n    yearsOfExperience: 3,\n    lastUsed: 'Currently using',\n    notes: 'Strong in ES6+',\n  },\n];\n\ndescribe('SkillAssessmentForm - TDD State Synchronization Fix', () => {\n  const mockOnSubmit = jest.fn();\n  const mockOnSkillSearch = jest.fn();\n  const mockOnAssessmentsChange = jest.fn();\n\n  beforeEach(() => {\n    jest.clearAllMocks();\n    mockOnSkillSearch.mockResolvedValue(mockSkills);\n  });\n\n  describe('State Synchronization (Critical Bug Fix)', () => {\n    it('should sync internal state with initialAssessments prop on mount', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          initialAssessments={mockInitialAssessments}\n          onAssessmentsChange={mockOnAssessmentsChange}\n        />\n      );\n\n      // Verify initial assessment data is displayed\n      await waitFor(() => {\n        expect(screen.getByDisplayValue('JavaScript')).toBeInTheDocument();\n      });\n\n      expect(screen.getByDisplayValue('Currently using')).toBeInTheDocument();\n      expect(screen.getByDisplayValue('Strong in ES6+')).toBeInTheDocument();\n    });\n\n    it('should update internal state when initialAssessments prop changes', async () => {\n      const { rerender } = render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          initialAssessments={[]}\n          onAssessmentsChange={mockOnAssessmentsChange}\n        />\n      );\n\n      // Initially should show empty form\n      expect(screen.getByPlaceholderText('e.g., JavaScript, React, Python')).toHaveValue('');\n\n      // Update with initial assessments\n      rerender(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          initialAssessments={mockInitialAssessments}\n          onAssessmentsChange={mockOnAssessmentsChange}\n        />\n      );\n\n      // Should now show the assessment data\n      await waitFor(() => {\n        expect(screen.getByDisplayValue('JavaScript')).toBeInTheDocument();\n      });\n    });\n\n    it('should call onAssessmentsChange when internal state updates', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          initialAssessments={[]}\n          onAssessmentsChange={mockOnAssessmentsChange}\n        />\n      );\n\n      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');\n      \n      await userEvent.type(skillInput, 'TypeScript');\n\n      await waitFor(() => {\n        expect(mockOnAssessmentsChange).toHaveBeenCalled();\n      });\n    });\n  });\n\n  describe('Search Result Selection', () => {\n    it('should populate form fields when selecting a skill from search results', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          onAssessmentsChange={mockOnAssessmentsChange}\n        />\n      );\n\n      const searchInput = screen.getByPlaceholderText('Search for skills to assess...');\n      \n      // Type to trigger search\n      await userEvent.type(searchInput, 'Java');\n\n      // Wait for search results\n      await waitFor(() => {\n        expect(mockOnSkillSearch).toHaveBeenCalledWith('Java');\n      });\n\n      // Click on a search result\n      const searchResult = await screen.findByText('JavaScript');\n      await userEvent.click(searchResult);\n\n      // Verify form field is populated\n      await waitFor(() => {\n        const skillNameInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');\n        expect(skillNameInput).toHaveValue('JavaScript');\n      });\n\n      // Verify search is cleared\n      expect(searchInput).toHaveValue('');\n    });\n\n    it('should handle fallback skills (common-* ids) correctly', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          onAssessmentsChange={mockOnAssessmentsChange}\n        />\n      );\n\n      const searchInput = screen.getByPlaceholderText('Search for skills to assess...');\n      \n      await userEvent.type(searchInput, 'Python');\n\n      await waitFor(() => {\n        expect(mockOnSkillSearch).toHaveBeenCalledWith('Python');\n      });\n\n      // Click on fallback skill (common-1)\n      const searchResult = await screen.findByText('Python');\n      await userEvent.click(searchResult);\n\n      // Verify form field is populated\n      await waitFor(() => {\n        const skillNameInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');\n        expect(skillNameInput).toHaveValue('Python');\n      });\n\n      // Verify onAssessmentsChange was called with updated data\n      expect(mockOnAssessmentsChange).toHaveBeenCalled();\n    });\n  });\n\n  describe('Form Validation', () => {\n    it('should validate required skill name', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n        />\n      );\n\n      const submitButton = screen.getByText('Submit Assessment');\n      await userEvent.click(submitButton);\n\n      await waitFor(() => {\n        expect(screen.getByText('Skill name is required')).toBeInTheDocument();\n      });\n\n      expect(mockOnSubmit).not.toHaveBeenCalled();\n    });\n\n    it('should validate rating ranges', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n        />\n      );\n\n      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');\n      await userEvent.type(skillInput, 'JavaScript');\n\n      // Try to submit with invalid ratings (this would require manipulating the slider values)\n      // For now, we'll test the validation function indirectly\n      const submitButton = screen.getByText('Submit Assessment');\n      await userEvent.click(submitButton);\n\n      // Should pass validation with default values (5/10)\n      await waitFor(() => {\n        expect(mockOnSubmit).toHaveBeenCalled();\n      });\n    });\n  });\n\n  describe('Bulk Mode', () => {\n    it('should allow adding multiple assessments in bulk mode', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          mode=\"bulk\"\n          maxAssessments={5}\n        />\n      );\n\n      // Should start with one assessment\n      expect(screen.getByText('Assessment 1')).toBeInTheDocument();\n\n      // Add another assessment\n      const addButton = screen.getByText('Add Another Skill Assessment');\n      await userEvent.click(addButton);\n\n      // Should now have two assessments\n      expect(screen.getByText('Assessment 2')).toBeInTheDocument();\n    });\n\n    it('should preserve state on submit in bulk mode', async () => {\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearch}\n          mode=\"bulk\"\n          preserveStateOnSubmit={true}\n        />\n      );\n\n      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');\n      await userEvent.type(skillInput, 'JavaScript');\n\n      const submitButton = screen.getByText('Submit Assessment');\n      await userEvent.click(submitButton);\n\n      await waitFor(() => {\n        expect(mockOnSubmit).toHaveBeenCalled();\n      });\n\n      // Form should still have the data\n      expect(skillInput).toHaveValue('JavaScript');\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle search errors gracefully', async () => {\n      const mockOnSkillSearchError = jest.fn().mockRejectedValue(new Error('Search failed'));\n\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmit}\n          onSkillSearch={mockOnSkillSearchError}\n        />\n      );\n\n      const searchInput = screen.getByPlaceholderText('Search for skills to assess...');\n      await userEvent.type(searchInput, 'Java');\n\n      await waitFor(() => {\n        expect(mockOnSkillSearchError).toHaveBeenCalledWith('Java');\n      });\n\n      // Should not crash and should handle error gracefully\n      expect(searchInput).toBeInTheDocument();\n    });\n\n    it('should handle submit errors gracefully', async () => {\n      const mockOnSubmitError = jest.fn().mockRejectedValue(new Error('Submit failed'));\n\n      render(\n        <SkillAssessmentForm\n          onSubmit={mockOnSubmitError}\n          onSkillSearch={mockOnSkillSearch}\n        />\n      );\n\n      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');\n      await userEvent.type(skillInput, 'JavaScript');\n\n      const submitButton = screen.getByText('Submit Assessment');\n      await userEvent.click(submitButton);\n\n      await waitFor(() => {\n        expect(mockOnSubmitError).toHaveBeenCalled();\n      });\n\n      // Button should not be stuck in loading state\n      expect(screen.getByText('Submit Assessment')).toBeInTheDocument();\n    });\n  });\n});\n"], "version": 3}