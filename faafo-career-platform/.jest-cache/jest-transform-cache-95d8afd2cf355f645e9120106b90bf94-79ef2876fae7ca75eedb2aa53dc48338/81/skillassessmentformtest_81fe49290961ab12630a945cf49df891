5593af5c1408797004c60a3010d66859
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock toast
jest.mock('sonner', function () { return ({
    toast: {
        error: jest.fn(),
        success: jest.fn(),
    },
}); });
// Mock UI components that cause issues in tests
jest.mock('@/components/ui/slider', function () { return ({
    Slider: function (_a) {
        var value = _a.value, onValueChange = _a.onValueChange, props = __rest(_a, ["value", "onValueChange"]);
        return ((0, jsx_runtime_1.jsx)("input", __assign({ type: "range", value: value[0], onChange: function (e) { return onValueChange([parseInt(e.target.value)]); }, "data-testid": "slider" }, props)));
    },
}); });
jest.mock('@/components/ui/card', function () { return ({
    Card: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card" }, props, { children: children }));
    },
    CardContent: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-content" }, props, { children: children }));
    },
    CardDescription: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-description" }, props, { children: children }));
    },
    CardHeader: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-header" }, props, { children: children }));
    },
    CardTitle: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-title" }, props, { children: children }));
    },
}); });
jest.mock('@/components/ui/button', function () { return ({
    Button: function (_a) {
        var children = _a.children, onClick = _a.onClick, disabled = _a.disabled, props = __rest(_a, ["children", "onClick", "disabled"]);
        return ((0, jsx_runtime_1.jsx)("button", __assign({ onClick: onClick, disabled: disabled, "data-testid": "button" }, props, { children: children })));
    },
}); });
jest.mock('@/components/ui/input', function () { return ({
    Input: function (props) { return (0, jsx_runtime_1.jsx)("input", __assign({ "data-testid": "input" }, props)); },
}); });
jest.mock('@/components/ui/label', function () { return ({
    Label: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("label", __assign({ "data-testid": "label" }, props, { children: children }));
    },
}); });
jest.mock('@/components/ui/textarea', function () { return ({
    Textarea: function (props) { return (0, jsx_runtime_1.jsx)("textarea", __assign({ "data-testid": "textarea" }, props)); },
}); });
jest.mock('@/components/ui/badge', function () { return ({
    Badge: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("span", __assign({ "data-testid": "badge" }, props, { children: children }));
    },
}); });
jest.mock('@/components/ui/progress', function () { return ({
    Progress: function (_a) {
        var value = _a.value, props = __rest(_a, ["value"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "progress", "data-value": value }, props));
    },
}); });
jest.mock('@/components/ui/alert', function () { return ({
    Alert: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "alert" }, props, { children: children }));
    },
    AlertDescription: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "alert-description" }, props, { children: children }));
    },
}); });
// Mock Lucide icons
jest.mock('lucide-react', function () { return ({
    Loader2: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "loader2-icon" }); },
    Star: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "star-icon" }); },
    TrendingUp: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "trending-up-icon" }); },
    BookOpen: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "book-open-icon" }); },
    Award: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "award-icon" }); },
}); });
/**
 * Skill Assessment Form Tests
 *
 * Tests Skill Assessment Form component functionality, rendering, user interactions, and edge cases.
 *
 * @category unit
 * @requires React Testing Library, component mocking
 */
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
var user_event_1 = __importDefault(require("@testing-library/user-event"));
require("@testing-library/jest-dom");
var SkillAssessmentForm_1 = __importDefault(require("@/components/skills/SkillAssessmentForm"));
// Mock skill data
var mockSkills = [
    {
        id: 'skill-1',
        name: 'JavaScript',
        category: 'Programming',
        description: 'JavaScript programming language',
    },
    {
        id: 'skill-2',
        name: 'React',
        category: 'Frontend',
        description: 'React framework',
    },
    {
        id: 'common-1',
        name: 'Python',
        category: 'Programming',
        description: 'Python programming language',
    },
];
var mockInitialAssessments = [
    {
        skillId: 'skill-1',
        skillName: 'JavaScript',
        selfRating: 7,
        confidenceLevel: 8,
        assessmentType: 'SELF_ASSESSMENT',
        yearsOfExperience: 3,
        lastUsed: 'Currently using',
        notes: 'Strong in ES6+',
    },
];
describe('SkillAssessmentForm - TDD State Synchronization Fix', function () {
    var mockOnSubmit = jest.fn();
    var mockOnSkillSearch = jest.fn();
    var mockOnAssessmentsChange = jest.fn();
    beforeEach(function () {
        jest.clearAllMocks();
        mockOnSkillSearch.mockResolvedValue(mockSkills);
    });
    describe('State Synchronization (Critical Bug Fix)', function () {
        it('should sync internal state with initialAssessments prop on mount', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: mockInitialAssessments, onAssessmentsChange: mockOnAssessmentsChange }));
                        // Verify initial assessment data is displayed
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByDisplayValue('JavaScript')).toBeInTheDocument();
                            })];
                    case 1:
                        // Verify initial assessment data is displayed
                        _a.sent();
                        expect(react_2.screen.getByDisplayValue('Currently using')).toBeInTheDocument();
                        expect(react_2.screen.getByDisplayValue('Strong in ES6+')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update internal state when initialAssessments prop changes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var rerender;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        rerender = (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: [], onAssessmentsChange: mockOnAssessmentsChange })).rerender;
                        // Initially should show empty form
                        expect(react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python')).toHaveValue('');
                        // Update with initial assessments
                        rerender((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: mockInitialAssessments, onAssessmentsChange: mockOnAssessmentsChange }));
                        // Should now show the assessment data
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByDisplayValue('JavaScript')).toBeInTheDocument();
                            })];
                    case 1:
                        // Should now show the assessment data
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should call onAssessmentsChange when internal state updates', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skillInput;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: [], onAssessmentsChange: mockOnAssessmentsChange }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'TypeScript')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnAssessmentsChange).toHaveBeenCalled();
                            })];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Search Result Selection', function () {
        it('should populate form fields when selecting a skill from search results', function () { return __awaiter(void 0, void 0, void 0, function () {
            var searchInput, searchResult;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, onAssessmentsChange: mockOnAssessmentsChange }));
                        searchInput = react_2.screen.getByPlaceholderText('Search for skills to assess...');
                        // Type to trigger search
                        return [4 /*yield*/, user_event_1.default.type(searchInput, 'Java')];
                    case 1:
                        // Type to trigger search
                        _a.sent();
                        // Wait for search results
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSkillSearch).toHaveBeenCalledWith('Java');
                            })];
                    case 2:
                        // Wait for search results
                        _a.sent();
                        return [4 /*yield*/, react_2.screen.findByText('JavaScript')];
                    case 3:
                        searchResult = _a.sent();
                        return [4 /*yield*/, user_event_1.default.click(searchResult)];
                    case 4:
                        _a.sent();
                        // Verify form field is populated
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                var skillNameInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                                expect(skillNameInput).toHaveValue('JavaScript');
                            })];
                    case 5:
                        // Verify form field is populated
                        _a.sent();
                        // Verify search is cleared
                        expect(searchInput).toHaveValue('');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle fallback skills (common-* ids) correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var searchInput, searchResult;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, onAssessmentsChange: mockOnAssessmentsChange }));
                        searchInput = react_2.screen.getByPlaceholderText('Search for skills to assess...');
                        return [4 /*yield*/, user_event_1.default.type(searchInput, 'Python')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSkillSearch).toHaveBeenCalledWith('Python');
                            })];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, react_2.screen.findByText('Python')];
                    case 3:
                        searchResult = _a.sent();
                        return [4 /*yield*/, user_event_1.default.click(searchResult)];
                    case 4:
                        _a.sent();
                        // Verify form field is populated
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                var skillNameInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                                expect(skillNameInput).toHaveValue('Python');
                            })];
                    case 5:
                        // Verify form field is populated
                        _a.sent();
                        // Verify onAssessmentsChange was called with updated data
                        expect(mockOnAssessmentsChange).toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Form Validation', function () {
        it('should validate required skill name', function () { return __awaiter(void 0, void 0, void 0, function () {
            var submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch }));
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Skill name is required')).toBeInTheDocument();
                            })];
                    case 2:
                        _a.sent();
                        expect(mockOnSubmit).not.toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate rating ranges', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skillInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'JavaScript')];
                    case 1:
                        _a.sent();
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 2:
                        _a.sent();
                        // Should pass validation with default values (5/10)
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSubmit).toHaveBeenCalled();
                            })];
                    case 3:
                        // Should pass validation with default values (5/10)
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Bulk Mode', function () {
        it('should allow adding multiple assessments in bulk mode', function () { return __awaiter(void 0, void 0, void 0, function () {
            var addButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, mode: "bulk", maxAssessments: 5 }));
                        // Should start with one assessment
                        expect(react_2.screen.getByText('Assessment 1')).toBeInTheDocument();
                        addButton = react_2.screen.getByText('Add Another Skill Assessment');
                        return [4 /*yield*/, user_event_1.default.click(addButton)];
                    case 1:
                        _a.sent();
                        // Should now have two assessments
                        expect(react_2.screen.getByText('Assessment 2')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should preserve state on submit in bulk mode', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skillInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, mode: "bulk", preserveStateOnSubmit: true }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'JavaScript')];
                    case 1:
                        _a.sent();
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSubmit).toHaveBeenCalled();
                            })];
                    case 3:
                        _a.sent();
                        // Form should still have the data
                        expect(skillInput).toHaveValue('JavaScript');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Error Handling', function () {
        it('should handle search errors gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockOnSkillSearchError, searchInput;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockOnSkillSearchError = jest.fn().mockRejectedValue(new Error('Search failed'));
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearchError }));
                        searchInput = react_2.screen.getByPlaceholderText('Search for skills to assess...');
                        return [4 /*yield*/, user_event_1.default.type(searchInput, 'Java')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSkillSearchError).toHaveBeenCalledWith('Java');
                            })];
                    case 2:
                        _a.sent();
                        // Should not crash and should handle error gracefully
                        expect(searchInput).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle submit errors gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockOnSubmitError, skillInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockOnSubmitError = jest.fn().mockRejectedValue(new Error('Submit failed'));
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmitError, onSkillSearch: mockOnSkillSearch }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'JavaScript')];
                    case 1:
                        _a.sent();
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSubmitError).toHaveBeenCalled();
                            })];
                    case 3:
                        _a.sent();
                        // Button should not be stuck in loading state
                        expect(react_2.screen.getByText('Submit Assessment')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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