14c3ce129bb28c0e2b3e32f4b5702342
"use strict";

/* istanbul ignore next */
function cov_80mlh5lud() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/VerificationEmail.tsx";
  var hash = "5ee7d2f21f2b12b35a61bb5293285ae378d45a44";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/VerificationEmail.tsx",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 3,
          column: 26
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "3": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "4": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 7,
          column: 5
        }
      },
      "5": {
        start: {
          line: 6,
          column: 6
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "6": {
        start: {
          line: 6,
          column: 51
        },
        end: {
          line: 6,
          column: 63
        }
      },
      "7": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 39
        }
      },
      "8": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "9": {
        start: {
          line: 10,
          column: 26
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 17
        }
      },
      "11": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 17,
          column: 2
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 72
        }
      },
      "13": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 21
        }
      },
      "14": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 34,
          column: 4
        }
      },
      "15": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "16": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 24,
          column: 10
        }
      },
      "17": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "18": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "19": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "20": {
        start: {
          line: 22,
          column: 77
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "21": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 22
        }
      },
      "22": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "23": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 33,
          column: 6
        }
      },
      "24": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "25": {
        start: {
          line: 28,
          column: 35
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "26": {
        start: {
          line: 29,
          column: 21
        },
        end: {
          line: 29,
          column: 23
        }
      },
      "27": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "28": {
        start: {
          line: 30,
          column: 25
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "29": {
        start: {
          line: 30,
          column: 38
        },
        end: {
          line: 30,
          column: 50
        }
      },
      "30": {
        start: {
          line: 30,
          column: 56
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "31": {
        start: {
          line: 30,
          column: 78
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "32": {
        start: {
          line: 30,
          column: 102
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 40
        }
      },
      "34": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 22
        }
      },
      "35": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 62
        }
      },
      "36": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 35
        }
      },
      "37": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 48
        }
      },
      "38": {
        start: {
          line: 38,
          column: 19
        },
        end: {
          line: 38,
          column: 53
        }
      },
      "39": {
        start: {
          line: 39,
          column: 12
        },
        end: {
          line: 39,
          column: 42
        }
      },
      "40": {
        start: {
          line: 40,
          column: 24
        },
        end: {
          line: 43,
          column: 1
        }
      },
      "41": {
        start: {
          line: 41,
          column: 19
        },
        end: {
          line: 41,
          column: 30
        }
      },
      "42": {
        start: {
          line: 41,
          column: 51
        },
        end: {
          line: 41,
          column: 70
        }
      },
      "43": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 2117
        }
      },
      "44": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 46
        }
      },
      "45": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 44
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 2,
            column: 75
          }
        },
        loc: {
          start: {
            line: 2,
            column: 96
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 38
          },
          end: {
            line: 6,
            column: 39
          }
        },
        loc: {
          start: {
            line: 6,
            column: 49
          },
          end: {
            line: 6,
            column: 65
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 7
          }
        },
        loc: {
          start: {
            line: 9,
            column: 28
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 9
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 13,
            column: 81
          }
        },
        loc: {
          start: {
            line: 13,
            column: 95
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 13
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 15,
            column: 6
          }
        },
        loc: {
          start: {
            line: 15,
            column: 20
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 15
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 18,
            column: 51
          },
          end: {
            line: 18,
            column: 52
          }
        },
        loc: {
          start: {
            line: 18,
            column: 63
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 18
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 19
          }
        },
        loc: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 26,
            column: 5
          }
        },
        line: 19
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 20,
            column: 49
          }
        },
        loc: {
          start: {
            line: 20,
            column: 61
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 20
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 27,
            column: 11
          },
          end: {
            line: 27,
            column: 12
          }
        },
        loc: {
          start: {
            line: 27,
            column: 26
          },
          end: {
            line: 33,
            column: 5
          }
        },
        line: 27
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        },
        loc: {
          start: {
            line: 40,
            column: 38
          },
          end: {
            line: 43,
            column: 1
          }
        },
        line: 40
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 12,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 9,
            column: 1
          }
        }, {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      },
      "4": {
        loc: {
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 13
          }
        }, {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "5": {
        loc: {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 47
          }
        }, {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "6": {
        loc: {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 63
          }
        }, {
          start: {
            line: 5,
            column: 67
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "7": {
        loc: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 17,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 26
          },
          end: {
            line: 13,
            column: 30
          }
        }, {
          start: {
            line: 13,
            column: 34
          },
          end: {
            line: 13,
            column: 57
          }
        }, {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 15,
            column: 1
          }
        }, {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 34,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 20
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 45
          }
        }, {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 34,
            column: 4
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 24,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 44
          }
        }, {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 24,
            column: 9
          }
        }],
        line: 20
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 15
          }
        }, {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 28,
            column: 33
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "16": {
        loc: {
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/VerificationEmail.tsx",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sDAAoF;AACpF,2CAA+B;AAOxB,IAAM,iBAAiB,GAAG,UAAC,EAAsD;QAApD,QAAQ,cAAA,EAAE,gBAAgB,sBAAA;IAA+B,OAAA,CAC3F,wBAAC,iBAAI,eACH,uBAAC,iBAAI,cACH,4CAAQ,08BAsCP,GAAS,GACL,EACP,uBAAC,iBAAI,cACH,wBAAC,sBAAS,IAAC,SAAS,EAAC,WAAW,aAC9B,uBAAC,iBAAI,IAAC,SAAS,EAAC,OAAO,sCAA6B,EACpD,wBAAC,iBAAI,IAAC,SAAS,EAAC,MAAM,oBAAK,QAAQ,SAAS,EAC5C,uBAAC,iBAAI,IAAC,SAAS,EAAC,MAAM,iIAEf,EACP,uBAAC,mBAAM,IACL,SAAS,EAAC,QAAQ,EAClB,IAAI,EAAE,gBAAgB,6BAGf,EACT,uBAAC,iBAAI,IAAC,SAAS,EAAC,MAAM,iFAEf,EACP,wBAAC,iBAAI,IAAC,SAAS,EAAC,MAAM,8BAEpB,gCAAM,sCAED,IACG,GACP,IACF,CACR;AAnE4F,CAmE5F,CAAC;AAnEW,QAAA,iBAAiB,qBAmE5B;AAEF,kBAAe,yBAAiB,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/VerificationEmail.tsx"],
      sourcesContent: ["import { Html, Head, Body, Container, Text, Button } from '@react-email/components';\nimport * as React from 'react';\n\ninterface VerificationEmailProps {\n  username: string;\n  verificationLink: string;\n}\n\nexport const VerificationEmail = ({ username, verificationLink }: VerificationEmailProps) => (\n  <Html>\n    <Head>\n      <style>{`\n        body {\n          background-color: white;\n          margin: auto;\n          font-family: sans-serif;\n          padding: 8px;\n        }\n        .container {\n          border: 1px solid #eaeaea;\n          border-radius: 8px;\n          margin: 32px auto;\n          padding: 32px;\n          max-width: 600px;\n        }\n        .title {\n          color: black;\n          font-size: 20px;\n          font-weight: bold;\n          margin-bottom: 16px;\n        }\n        .text {\n          color: black;\n          font-size: 16px;\n          line-height: 1.5;\n          margin-bottom: 16px;\n        }\n        .button {\n          background-color: #1f2937;\n          border-radius: 6px;\n          color: white;\n          font-size: 16px;\n          font-weight: 600;\n          text-decoration: none;\n          text-align: center;\n          padding: 12px 20px;\n          display: inline-block;\n          margin: 16px 0;\n        }\n      `}</style>\n    </Head>\n    <Body>\n      <Container className=\"container\">\n        <Text className=\"title\">FAAFO Career Platform</Text>\n        <Text className=\"text\">Hi {username},</Text>\n        <Text className=\"text\">\n          Welcome to FAAFO Career Platform! To get started, please verify your email address by clicking the button below:\n        </Text>\n        <Button\n          className=\"button\"\n          href={verificationLink}\n        >\n          Verify Email\n        </Button>\n        <Text className=\"text\">\n          If you did not sign up for an account, please ignore this email.\n        </Text>\n        <Text className=\"text\">\n          Best regards,\n          <br />\n          The FAAFO Career Platform Team\n        </Text>\n      </Container>\n    </Body>\n  </Html>\n);\n\nexport default VerificationEmail; "],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5ee7d2f21f2b12b35a61bb5293285ae378d45a44"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_80mlh5lud = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_80mlh5lud();
var __createBinding =
/* istanbul ignore next */
(cov_80mlh5lud().s[0]++,
/* istanbul ignore next */
(cov_80mlh5lud().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_80mlh5lud().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_80mlh5lud().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_80mlh5lud().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_80mlh5lud().f[0]++;
  cov_80mlh5lud().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_80mlh5lud().b[2][0]++;
    cov_80mlh5lud().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_80mlh5lud().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_80mlh5lud().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_80mlh5lud().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_80mlh5lud().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_80mlh5lud().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_80mlh5lud().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_80mlh5lud().b[5][1]++,
  /* istanbul ignore next */
  (cov_80mlh5lud().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_80mlh5lud().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_80mlh5lud().b[3][0]++;
    cov_80mlh5lud().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_80mlh5lud().f[1]++;
        cov_80mlh5lud().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_80mlh5lud().b[3][1]++;
  }
  cov_80mlh5lud().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_80mlh5lud().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_80mlh5lud().f[2]++;
  cov_80mlh5lud().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_80mlh5lud().b[7][0]++;
    cov_80mlh5lud().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_80mlh5lud().b[7][1]++;
  }
  cov_80mlh5lud().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_80mlh5lud().s[11]++,
/* istanbul ignore next */
(cov_80mlh5lud().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_80mlh5lud().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_80mlh5lud().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_80mlh5lud().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_80mlh5lud().f[3]++;
  cov_80mlh5lud().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_80mlh5lud().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_80mlh5lud().f[4]++;
  cov_80mlh5lud().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_80mlh5lud().s[14]++,
/* istanbul ignore next */
(cov_80mlh5lud().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_80mlh5lud().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_80mlh5lud().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_80mlh5lud().f[5]++;
  cov_80mlh5lud().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_80mlh5lud().f[6]++;
    cov_80mlh5lud().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_80mlh5lud().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_80mlh5lud().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_80mlh5lud().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_80mlh5lud().s[17]++, []);
      /* istanbul ignore next */
      cov_80mlh5lud().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_80mlh5lud().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_80mlh5lud().b[12][0]++;
          cov_80mlh5lud().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_80mlh5lud().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_80mlh5lud().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_80mlh5lud().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_80mlh5lud().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_80mlh5lud().f[8]++;
    cov_80mlh5lud().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_80mlh5lud().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_80mlh5lud().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_80mlh5lud().b[13][0]++;
      cov_80mlh5lud().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_80mlh5lud().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_80mlh5lud().s[26]++, {});
    /* istanbul ignore next */
    cov_80mlh5lud().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_80mlh5lud().b[15][0]++;
      cov_80mlh5lud().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_80mlh5lud().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_80mlh5lud().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_80mlh5lud().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_80mlh5lud().b[16][0]++;
          cov_80mlh5lud().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_80mlh5lud().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_80mlh5lud().b[15][1]++;
    }
    cov_80mlh5lud().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_80mlh5lud().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_80mlh5lud().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_80mlh5lud().s[36]++;
exports.VerificationEmail = void 0;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_80mlh5lud().s[37]++, require("react/jsx-runtime"));
var components_1 =
/* istanbul ignore next */
(cov_80mlh5lud().s[38]++, require("@react-email/components"));
var React =
/* istanbul ignore next */
(cov_80mlh5lud().s[39]++, __importStar(require("react")));
/* istanbul ignore next */
cov_80mlh5lud().s[40]++;
var VerificationEmail = function (_a) {
  /* istanbul ignore next */
  cov_80mlh5lud().f[9]++;
  var username =
    /* istanbul ignore next */
    (cov_80mlh5lud().s[41]++, _a.username),
    verificationLink =
    /* istanbul ignore next */
    (cov_80mlh5lud().s[42]++, _a.verificationLink);
  /* istanbul ignore next */
  cov_80mlh5lud().s[43]++;
  return (0, jsx_runtime_1.jsxs)(components_1.Html, {
    children: [(0, jsx_runtime_1.jsx)(components_1.Head, {
      children: (0, jsx_runtime_1.jsx)("style", {
        children: "\n        body {\n          background-color: white;\n          margin: auto;\n          font-family: sans-serif;\n          padding: 8px;\n        }\n        .container {\n          border: 1px solid #eaeaea;\n          border-radius: 8px;\n          margin: 32px auto;\n          padding: 32px;\n          max-width: 600px;\n        }\n        .title {\n          color: black;\n          font-size: 20px;\n          font-weight: bold;\n          margin-bottom: 16px;\n        }\n        .text {\n          color: black;\n          font-size: 16px;\n          line-height: 1.5;\n          margin-bottom: 16px;\n        }\n        .button {\n          background-color: #1f2937;\n          border-radius: 6px;\n          color: white;\n          font-size: 16px;\n          font-weight: 600;\n          text-decoration: none;\n          text-align: center;\n          padding: 12px 20px;\n          display: inline-block;\n          margin: 16px 0;\n        }\n      "
      })
    }), (0, jsx_runtime_1.jsx)(components_1.Body, {
      children: (0, jsx_runtime_1.jsxs)(components_1.Container, {
        className: "container",
        children: [(0, jsx_runtime_1.jsx)(components_1.Text, {
          className: "title",
          children: "FAAFO Career Platform"
        }), (0, jsx_runtime_1.jsxs)(components_1.Text, {
          className: "text",
          children: ["Hi ", username, ","]
        }), (0, jsx_runtime_1.jsx)(components_1.Text, {
          className: "text",
          children: "Welcome to FAAFO Career Platform! To get started, please verify your email address by clicking the button below:"
        }), (0, jsx_runtime_1.jsx)(components_1.Button, {
          className: "button",
          href: verificationLink,
          children: "Verify Email"
        }), (0, jsx_runtime_1.jsx)(components_1.Text, {
          className: "text",
          children: "If you did not sign up for an account, please ignore this email."
        }), (0, jsx_runtime_1.jsxs)(components_1.Text, {
          className: "text",
          children: ["Best regards,", (0, jsx_runtime_1.jsx)("br", {}), "The FAAFO Career Platform Team"]
        })]
      })
    })]
  });
};
/* istanbul ignore next */
cov_80mlh5lud().s[44]++;
exports.VerificationEmail = VerificationEmail;
/* istanbul ignore next */
cov_80mlh5lud().s[45]++;
exports.default = exports.VerificationEmail;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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