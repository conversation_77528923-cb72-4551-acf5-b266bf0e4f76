{"version": 3, "names": ["components_1", "cov_80mlh5lud", "s", "require", "React", "__importStar", "VerificationEmail", "_a", "f", "username", "verificationLink", "jsx_runtime_1", "jsxs", "Html", "children", "jsx", "Head", "Body", "Container", "className", "Text", "<PERSON><PERSON>", "href", "exports", "default"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/VerificationEmail.tsx"], "sourcesContent": ["import { Html, Head, Body, Container, Text, Button } from '@react-email/components';\nimport * as React from 'react';\n\ninterface VerificationEmailProps {\n  username: string;\n  verificationLink: string;\n}\n\nexport const VerificationEmail = ({ username, verificationLink }: VerificationEmailProps) => (\n  <Html>\n    <Head>\n      <style>{`\n        body {\n          background-color: white;\n          margin: auto;\n          font-family: sans-serif;\n          padding: 8px;\n        }\n        .container {\n          border: 1px solid #eaeaea;\n          border-radius: 8px;\n          margin: 32px auto;\n          padding: 32px;\n          max-width: 600px;\n        }\n        .title {\n          color: black;\n          font-size: 20px;\n          font-weight: bold;\n          margin-bottom: 16px;\n        }\n        .text {\n          color: black;\n          font-size: 16px;\n          line-height: 1.5;\n          margin-bottom: 16px;\n        }\n        .button {\n          background-color: #1f2937;\n          border-radius: 6px;\n          color: white;\n          font-size: 16px;\n          font-weight: 600;\n          text-decoration: none;\n          text-align: center;\n          padding: 12px 20px;\n          display: inline-block;\n          margin: 16px 0;\n        }\n      `}</style>\n    </Head>\n    <Body>\n      <Container className=\"container\">\n        <Text className=\"title\">FAAFO Career Platform</Text>\n        <Text className=\"text\">Hi {username},</Text>\n        <Text className=\"text\">\n          Welcome to FAAFO Career Platform! To get started, please verify your email address by clicking the button below:\n        </Text>\n        <Button\n          className=\"button\"\n          href={verificationLink}\n        >\n          Verify Email\n        </Button>\n        <Text className=\"text\">\n          If you did not sign up for an account, please ignore this email.\n        </Text>\n        <Text className=\"text\">\n          Best regards,\n          <br />\n          The FAAFO Career Platform Team\n        </Text>\n      </Container>\n    </Body>\n  </Html>\n);\n\nexport default VerificationEmail; "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,YAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,KAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,YAAA,CAAAF,OAAA;AAA+B;AAAAF,aAAA,GAAAC,CAAA;AAOxB,IAAMI,iBAAiB,GAAG,SAAAA,CAACC,EAAsD;EAAA;EAAAN,aAAA,GAAAO,CAAA;MAApDC,QAAQ;IAAA;IAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAK,EAAA,CAAAE,QAAA;IAAEC,gBAAgB;IAAA;IAAA,CAAAT,aAAA,GAAAC,CAAA,QAAAK,EAAA,CAAAG,gBAAA;EAAA;EAAAT,aAAA,GAAAC,CAAA;EAA+B,OAC3F,IAAAS,aAAA,CAAAC,IAAA,EAACZ,YAAA,CAAAa,IAAI;IAAAC,QAAA,GACH,IAAAH,aAAA,CAAAI,GAAA,EAACf,YAAA,CAAAgB,IAAI;MAAAF,QAAA,EACH,IAAAH,aAAA,CAAAI,GAAA;QAAAD,QAAA,EAAQ;MAsCP;IAAS,EACL,EACP,IAAAH,aAAA,CAAAI,GAAA,EAACf,YAAA,CAAAiB,IAAI;MAAAH,QAAA,EACH,IAAAH,aAAA,CAAAC,IAAA,EAACZ,YAAA,CAAAkB,SAAS;QAACC,SAAS,EAAC,WAAW;QAAAL,QAAA,GAC9B,IAAAH,aAAA,CAAAI,GAAA,EAACf,YAAA,CAAAoB,IAAI;UAACD,SAAS,EAAC,OAAO;UAAAL,QAAA;QAAA,EAA6B,EACpD,IAAAH,aAAA,CAAAC,IAAA,EAACZ,YAAA,CAAAoB,IAAI;UAACD,SAAS,EAAC,MAAM;UAAAL,QAAA,UAAKL,QAAQ;QAAA,EAAS,EAC5C,IAAAE,aAAA,CAAAI,GAAA,EAACf,YAAA,CAAAoB,IAAI;UAACD,SAAS,EAAC,MAAM;UAAAL,QAAA;QAAA,EAEf,EACP,IAAAH,aAAA,CAAAI,GAAA,EAACf,YAAA,CAAAqB,MAAM;UACLF,SAAS,EAAC,QAAQ;UAClBG,IAAI,EAAEZ,gBAAgB;UAAAI,QAAA;QAAA,EAGf,EACT,IAAAH,aAAA,CAAAI,GAAA,EAACf,YAAA,CAAAoB,IAAI;UAACD,SAAS,EAAC,MAAM;UAAAL,QAAA;QAAA,EAEf,EACP,IAAAH,aAAA,CAAAC,IAAA,EAACZ,YAAA,CAAAoB,IAAI;UAACD,SAAS,EAAC,MAAM;UAAAL,QAAA,oBAEpB,IAAAH,aAAA,CAAAI,GAAA,WAAM;QAAA,EAED;MAAA;IACG,EACP;EAAA,EACF;AAlEoF,CAmE5F;AAAC;AAAAd,aAAA,GAAAC,CAAA;AAnEWqB,OAAA,CAAAjB,iBAAiB,GAAAA,iBAAA;AAmE5B;AAAAL,aAAA,GAAAC,CAAA;AAEFqB,OAAA,CAAAC,OAAA,GAAeD,OAAA,CAAAjB,iBAAiB", "ignoreList": []}