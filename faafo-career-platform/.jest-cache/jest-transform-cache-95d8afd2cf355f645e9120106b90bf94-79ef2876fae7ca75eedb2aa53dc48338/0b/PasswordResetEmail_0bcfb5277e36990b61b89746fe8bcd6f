a30170fc7a93802181a0240fe7116ffc
"use strict";

/* istanbul ignore next */
function cov_ehekyoroj() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/PasswordResetEmail.tsx";
  var hash = "34d342383805eee4dfa4677e453b2e14cdc28180";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/PasswordResetEmail.tsx",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 36
        }
      },
      "4": {
        start: {
          line: 7,
          column: 20
        },
        end: {
          line: 7,
          column: 48
        }
      },
      "5": {
        start: {
          line: 8,
          column: 19
        },
        end: {
          line: 8,
          column: 53
        }
      },
      "6": {
        start: {
          line: 9,
          column: 14
        },
        end: {
          line: 9,
          column: 47
        }
      },
      "7": {
        start: {
          line: 10,
          column: 25
        },
        end: {
          line: 13,
          column: 1
        }
      },
      "8": {
        start: {
          line: 11,
          column: 20
        },
        end: {
          line: 11,
          column: 32
        }
      },
      "9": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 1220
        }
      },
      "10": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 48
        }
      },
      "11": {
        start: {
          line: 15,
          column: 11
        },
        end: {
          line: 18,
          column: 1
        }
      },
      "12": {
        start: {
          line: 19,
          column: 16
        },
        end: {
          line: 24,
          column: 1
        }
      },
      "13": {
        start: {
          line: 25,
          column: 11
        },
        end: {
          line: 27,
          column: 1
        }
      },
      "14": {
        start: {
          line: 28,
          column: 16
        },
        end: {
          line: 33,
          column: 1
        }
      },
      "15": {
        start: {
          line: 34,
          column: 13
        },
        end: {
          line: 46,
          column: 1
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 25
          },
          end: {
            line: 10,
            column: 26
          }
        },
        loc: {
          start: {
            line: 10,
            column: 39
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/PasswordResetEmail.tsx",
      mappings: ";;;;;;;AAAA,sDAAuF;AACvF,gDAA0B;AAMnB,IAAM,kBAAkB,GAAG,UAAC,EAAsC;QAApC,SAAS,eAAA;IAAgC,OAAA,CAC5E,wBAAC,iBAAI,IAAC,IAAI,EAAC,IAAI,aACb,uBAAC,iBAAI,KAAG,EACR,uBAAC,iBAAI,IAAC,KAAK,EAAE,IAAI,YACf,wBAAC,sBAAS,IAAC,KAAK,EAAE,SAAS,aACzB,uBAAC,gBAAG,IACF,GAAG,EAAC,sEAAsE,EAC1E,KAAK,EAAC,KAAK,EACX,MAAM,EAAC,IAAI,EACX,GAAG,EAAC,YAAY,EAChB,KAAK,EAAE,IAAI,GACX,EACF,uBAAC,iBAAI,IAAC,KAAK,EAAE,SAAS,0BAAkB,EACxC,uBAAC,iBAAI,IAAC,KAAK,EAAE,SAAS,0HAGf,EACP,uBAAC,iBAAI,IAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,oCAE7B,EACP,uBAAC,iBAAI,IAAC,KAAK,EAAE,SAAS,gIAGf,EACP,uBAAC,iBAAI,IAAC,KAAK,EAAE,SAAS,wBAAgB,EACtC,uBAAC,iBAAI,IAAC,KAAK,EAAE,SAAS,+BAAuB,IACnC,GACP,IACF,CACR;AA7B6E,CA6B7E,CAAC;AA7BW,QAAA,kBAAkB,sBA6B7B;AAEF,IAAM,IAAI,GAAG;IACX,eAAe,EAAE,SAAS;IAC1B,UAAU,EAAE,uFAAuF;CACpG,CAAC;AAEF,IAAM,SAAS,GAAG;IAChB,eAAe,EAAE,SAAS;IAC1B,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,aAAa;IACtB,YAAY,EAAE,MAAM;CACrB,CAAC;AAEF,IAAM,IAAI,GAAG;IACX,MAAM,EAAE,QAAQ;CACjB,CAAC;AAEF,IAAM,SAAS,GAAG;IAChB,KAAK,EAAE,MAAM;IACb,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE,MAAe;CAC3B,CAAC;AAEF,IAAM,MAAM,GAAG;IACb,eAAe,EAAE,SAAS;IAC1B,YAAY,EAAE,KAAK;IACnB,KAAK,EAAE,MAAM;IACb,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAE,MAAM;IAClB,cAAc,EAAE,MAAM;IACtB,SAAS,EAAE,QAAiB;IAC5B,OAAO,EAAE,OAAO;IAChB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,QAAQ;IACjB,MAAM,EAAE,WAAW;CACpB,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/PasswordResetEmail.tsx"],
      sourcesContent: ["import { Html, Head, Body, Container, Text, Link, Img } from '@react-email/components';\nimport React from 'react';\n\ninterface PasswordResetEmailProps {\n  resetLink: string;\n}\n\nexport const PasswordResetEmail = ({ resetLink }: PasswordResetEmailProps) => (\n  <Html lang=\"en\">\n    <Head />\n    <Body style={main}>\n      <Container style={container}>\n        <Img\n          src=\"https://res.cloudinary.com/example/image/upload/v1678901234/logo.png\"\n          width=\"170\"\n          height=\"50\"\n          alt=\"FAAFO Logo\"\n          style={logo}\n        />\n        <Text style={paragraph}>Hi there,</Text>\n        <Text style={paragraph}>\n          You recently requested to reset your password for your FAAFO account.\n          Click the button below to reset it.\n        </Text>\n        <Link href={resetLink} style={button}>\n          Reset your password\n        </Link>\n        <Text style={paragraph}>\n          If you did not request a password reset, please ignore this email or\n          contact support if you have any questions.\n        </Text>\n        <Text style={paragraph}>Thanks,</Text>\n        <Text style={paragraph}>The FAAFO Team</Text>\n      </Container>\n    </Body>\n  </Html>\n);\n\nconst main = {\n  backgroundColor: '#f6f9fc',\n  fontFamily: '-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Ubuntu,sans-serif',\n};\n\nconst container = {\n  backgroundColor: '#ffffff',\n  margin: '0 auto',\n  padding: '20px 0 48px',\n  marginBottom: '64px',\n};\n\nconst logo = {\n  margin: '0 auto',\n};\n\nconst paragraph = {\n  color: '#444',\n  fontSize: '15px',\n  lineHeight: '24px',\n  textAlign: 'left' as const,\n};\n\nconst button = {\n  backgroundColor: '#374151',\n  borderRadius: '5px',\n  color: '#fff',\n  fontSize: '15px',\n  fontWeight: 'bold',\n  textDecoration: 'none',\n  textAlign: 'center' as const,\n  display: 'block',\n  width: '210px',\n  padding: '10px 0',\n  margin: '20px auto',\n};"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "34d342383805eee4dfa4677e453b2e14cdc28180"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_ehekyoroj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ehekyoroj();
var __importDefault =
/* istanbul ignore next */
(cov_ehekyoroj().s[0]++,
/* istanbul ignore next */
(cov_ehekyoroj().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_ehekyoroj().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_ehekyoroj().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_ehekyoroj().f[0]++;
  cov_ehekyoroj().s[1]++;
  return /* istanbul ignore next */(cov_ehekyoroj().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_ehekyoroj().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_ehekyoroj().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_ehekyoroj().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_ehekyoroj().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_ehekyoroj().s[3]++;
exports.PasswordResetEmail = void 0;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_ehekyoroj().s[4]++, require("react/jsx-runtime"));
var components_1 =
/* istanbul ignore next */
(cov_ehekyoroj().s[5]++, require("@react-email/components"));
var react_1 =
/* istanbul ignore next */
(cov_ehekyoroj().s[6]++, __importDefault(require("react")));
/* istanbul ignore next */
cov_ehekyoroj().s[7]++;
var PasswordResetEmail = function (_a) {
  /* istanbul ignore next */
  cov_ehekyoroj().f[1]++;
  var resetLink =
  /* istanbul ignore next */
  (cov_ehekyoroj().s[8]++, _a.resetLink);
  /* istanbul ignore next */
  cov_ehekyoroj().s[9]++;
  return (0, jsx_runtime_1.jsxs)(components_1.Html, {
    lang: "en",
    children: [(0, jsx_runtime_1.jsx)(components_1.Head, {}), (0, jsx_runtime_1.jsx)(components_1.Body, {
      style: main,
      children: (0, jsx_runtime_1.jsxs)(components_1.Container, {
        style: container,
        children: [(0, jsx_runtime_1.jsx)(components_1.Img, {
          src: "https://res.cloudinary.com/example/image/upload/v1678901234/logo.png",
          width: "170",
          height: "50",
          alt: "FAAFO Logo",
          style: logo
        }), (0, jsx_runtime_1.jsx)(components_1.Text, {
          style: paragraph,
          children: "Hi there,"
        }), (0, jsx_runtime_1.jsx)(components_1.Text, {
          style: paragraph,
          children: "You recently requested to reset your password for your FAAFO account. Click the button below to reset it."
        }), (0, jsx_runtime_1.jsx)(components_1.Link, {
          href: resetLink,
          style: button,
          children: "Reset your password"
        }), (0, jsx_runtime_1.jsx)(components_1.Text, {
          style: paragraph,
          children: "If you did not request a password reset, please ignore this email or contact support if you have any questions."
        }), (0, jsx_runtime_1.jsx)(components_1.Text, {
          style: paragraph,
          children: "Thanks,"
        }), (0, jsx_runtime_1.jsx)(components_1.Text, {
          style: paragraph,
          children: "The FAAFO Team"
        })]
      })
    })]
  });
};
/* istanbul ignore next */
cov_ehekyoroj().s[10]++;
exports.PasswordResetEmail = PasswordResetEmail;
var main =
/* istanbul ignore next */
(cov_ehekyoroj().s[11]++, {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif'
});
var container =
/* istanbul ignore next */
(cov_ehekyoroj().s[12]++, {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px'
});
var logo =
/* istanbul ignore next */
(cov_ehekyoroj().s[13]++, {
  margin: '0 auto'
});
var paragraph =
/* istanbul ignore next */
(cov_ehekyoroj().s[14]++, {
  color: '#444',
  fontSize: '15px',
  lineHeight: '24px',
  textAlign: 'left'
});
var button =
/* istanbul ignore next */
(cov_ehekyoroj().s[15]++, {
  backgroundColor: '#374151',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '15px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center',
  display: 'block',
  width: '210px',
  padding: '10px 0',
  margin: '20px auto'
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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