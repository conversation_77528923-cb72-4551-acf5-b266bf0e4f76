3224a68c4fb8da0f01c94a72598e3e9d
"use strict";

/**
 * Unified API Error Handler
 *
 * This module provides a standardized error handling system for all API routes.
 * It consolidates the various error handling patterns into a single, secure,
 * and consistent approach.
 */
/* istanbul ignore next */
function cov_2mexa19fo() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/unified-api-error-handler.ts";
  var hash = "b06bdc1e02a33e19a27d940cd47202f06c1c6885";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/unified-api-error-handler.ts",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 16
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "1": {
        start: {
          line: 10,
          column: 28
        },
        end: {
          line: 10,
          column: 110
        }
      },
      "2": {
        start: {
          line: 10,
          column: 91
        },
        end: {
          line: 10,
          column: 106
        }
      },
      "3": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 16,
          column: 7
        }
      },
      "4": {
        start: {
          line: 12,
          column: 36
        },
        end: {
          line: 12,
          column: 97
        }
      },
      "5": {
        start: {
          line: 12,
          column: 42
        },
        end: {
          line: 12,
          column: 70
        }
      },
      "6": {
        start: {
          line: 12,
          column: 85
        },
        end: {
          line: 12,
          column: 95
        }
      },
      "7": {
        start: {
          line: 13,
          column: 35
        },
        end: {
          line: 13,
          column: 100
        }
      },
      "8": {
        start: {
          line: 13,
          column: 41
        },
        end: {
          line: 13,
          column: 73
        }
      },
      "9": {
        start: {
          line: 13,
          column: 88
        },
        end: {
          line: 13,
          column: 98
        }
      },
      "10": {
        start: {
          line: 14,
          column: 32
        },
        end: {
          line: 14,
          column: 116
        }
      },
      "11": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 15,
          column: 78
        }
      },
      "12": {
        start: {
          line: 18,
          column: 18
        },
        end: {
          line: 44,
          column: 1
        }
      },
      "13": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 104
        }
      },
      "14": {
        start: {
          line: 19,
          column: 43
        },
        end: {
          line: 19,
          column: 68
        }
      },
      "15": {
        start: {
          line: 19,
          column: 57
        },
        end: {
          line: 19,
          column: 68
        }
      },
      "16": {
        start: {
          line: 19,
          column: 69
        },
        end: {
          line: 19,
          column: 81
        }
      },
      "17": {
        start: {
          line: 19,
          column: 119
        },
        end: {
          line: 19,
          column: 196
        }
      },
      "18": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 160
        }
      },
      "19": {
        start: {
          line: 20,
          column: 141
        },
        end: {
          line: 20,
          column: 153
        }
      },
      "20": {
        start: {
          line: 21,
          column: 23
        },
        end: {
          line: 21,
          column: 68
        }
      },
      "21": {
        start: {
          line: 21,
          column: 45
        },
        end: {
          line: 21,
          column: 65
        }
      },
      "22": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 70
        }
      },
      "23": {
        start: {
          line: 23,
          column: 15
        },
        end: {
          line: 23,
          column: 70
        }
      },
      "24": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 41,
          column: 66
        }
      },
      "25": {
        start: {
          line: 24,
          column: 50
        },
        end: {
          line: 41,
          column: 66
        }
      },
      "26": {
        start: {
          line: 25,
          column: 12
        },
        end: {
          line: 25,
          column: 169
        }
      },
      "27": {
        start: {
          line: 25,
          column: 160
        },
        end: {
          line: 25,
          column: 169
        }
      },
      "28": {
        start: {
          line: 26,
          column: 12
        },
        end: {
          line: 26,
          column: 52
        }
      },
      "29": {
        start: {
          line: 26,
          column: 26
        },
        end: {
          line: 26,
          column: 52
        }
      },
      "30": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 39,
          column: 13
        }
      },
      "31": {
        start: {
          line: 28,
          column: 32
        },
        end: {
          line: 28,
          column: 39
        }
      },
      "32": {
        start: {
          line: 28,
          column: 40
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "33": {
        start: {
          line: 29,
          column: 24
        },
        end: {
          line: 29,
          column: 34
        }
      },
      "34": {
        start: {
          line: 29,
          column: 35
        },
        end: {
          line: 29,
          column: 72
        }
      },
      "35": {
        start: {
          line: 30,
          column: 24
        },
        end: {
          line: 30,
          column: 34
        }
      },
      "36": {
        start: {
          line: 30,
          column: 35
        },
        end: {
          line: 30,
          column: 45
        }
      },
      "37": {
        start: {
          line: 30,
          column: 46
        },
        end: {
          line: 30,
          column: 55
        }
      },
      "38": {
        start: {
          line: 30,
          column: 56
        },
        end: {
          line: 30,
          column: 65
        }
      },
      "39": {
        start: {
          line: 31,
          column: 24
        },
        end: {
          line: 31,
          column: 41
        }
      },
      "40": {
        start: {
          line: 31,
          column: 42
        },
        end: {
          line: 31,
          column: 55
        }
      },
      "41": {
        start: {
          line: 31,
          column: 56
        },
        end: {
          line: 31,
          column: 65
        }
      },
      "42": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 128
        }
      },
      "43": {
        start: {
          line: 33,
          column: 110
        },
        end: {
          line: 33,
          column: 116
        }
      },
      "44": {
        start: {
          line: 33,
          column: 117
        },
        end: {
          line: 33,
          column: 126
        }
      },
      "45": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 106
        }
      },
      "46": {
        start: {
          line: 34,
          column: 81
        },
        end: {
          line: 34,
          column: 97
        }
      },
      "47": {
        start: {
          line: 34,
          column: 98
        },
        end: {
          line: 34,
          column: 104
        }
      },
      "48": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "49": {
        start: {
          line: 35,
          column: 57
        },
        end: {
          line: 35,
          column: 72
        }
      },
      "50": {
        start: {
          line: 35,
          column: 73
        },
        end: {
          line: 35,
          column: 80
        }
      },
      "51": {
        start: {
          line: 35,
          column: 81
        },
        end: {
          line: 35,
          column: 87
        }
      },
      "52": {
        start: {
          line: 36,
          column: 20
        },
        end: {
          line: 36,
          column: 87
        }
      },
      "53": {
        start: {
          line: 36,
          column: 47
        },
        end: {
          line: 36,
          column: 62
        }
      },
      "54": {
        start: {
          line: 36,
          column: 63
        },
        end: {
          line: 36,
          column: 78
        }
      },
      "55": {
        start: {
          line: 36,
          column: 79
        },
        end: {
          line: 36,
          column: 85
        }
      },
      "56": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 42
        }
      },
      "57": {
        start: {
          line: 37,
          column: 30
        },
        end: {
          line: 37,
          column: 42
        }
      },
      "58": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 33
        }
      },
      "59": {
        start: {
          line: 38,
          column: 34
        },
        end: {
          line: 38,
          column: 43
        }
      },
      "60": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 39
        }
      },
      "61": {
        start: {
          line: 41,
          column: 22
        },
        end: {
          line: 41,
          column: 34
        }
      },
      "62": {
        start: {
          line: 41,
          column: 35
        },
        end: {
          line: 41,
          column: 41
        }
      },
      "63": {
        start: {
          line: 41,
          column: 54
        },
        end: {
          line: 41,
          column: 64
        }
      },
      "64": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 35
        }
      },
      "65": {
        start: {
          line: 42,
          column: 23
        },
        end: {
          line: 42,
          column: 35
        }
      },
      "66": {
        start: {
          line: 42,
          column: 36
        },
        end: {
          line: 42,
          column: 89
        }
      },
      "67": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 53,
          column: 1
        }
      },
      "68": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 51,
          column: 5
        }
      },
      "69": {
        start: {
          line: 46,
          column: 40
        },
        end: {
          line: 51,
          column: 5
        }
      },
      "70": {
        start: {
          line: 46,
          column: 53
        },
        end: {
          line: 46,
          column: 54
        }
      },
      "71": {
        start: {
          line: 46,
          column: 60
        },
        end: {
          line: 46,
          column: 71
        }
      },
      "72": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 50,
          column: 9
        }
      },
      "73": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 48,
          column: 65
        }
      },
      "74": {
        start: {
          line: 48,
          column: 21
        },
        end: {
          line: 48,
          column: 65
        }
      },
      "75": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 28
        }
      },
      "76": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 61
        }
      },
      "77": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 62
        }
      },
      "78": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 66
        }
      },
      "79": {
        start: {
          line: 56,
          column: 0
        },
        end: {
          line: 56,
          column: 60
        }
      },
      "80": {
        start: {
          line: 57,
          column: 15
        },
        end: {
          line: 57,
          column: 37
        }
      },
      "81": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 26
        }
      },
      "82": {
        start: {
          line: 59,
          column: 15
        },
        end: {
          line: 59,
          column: 40
        }
      },
      "83": {
        start: {
          line: 60,
          column: 18
        },
        end: {
          line: 60,
          column: 38
        }
      },
      "84": {
        start: {
          line: 61,
          column: 13
        },
        end: {
          line: 61,
          column: 34
        }
      },
      "85": {
        start: {
          line: 62,
          column: 15
        },
        end: {
          line: 62,
          column: 38
        }
      },
      "86": {
        start: {
          line: 63,
          column: 22
        },
        end: {
          line: 63,
          column: 52
        }
      },
      "87": {
        start: {
          line: 64,
          column: 23
        },
        end: {
          line: 64,
          column: 54
        }
      },
      "88": {
        start: {
          line: 66,
          column: 0
        },
        end: {
          line: 86,
          column: 2
        }
      },
      "89": {
        start: {
          line: 87,
          column: 44
        },
        end: {
          line: 292,
          column: 3
        }
      },
      "90": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 92,
          column: 6
        }
      },
      "91": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 91,
          column: 94
        }
      },
      "92": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 97,
          column: 6
        }
      },
      "93": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 96,
          column: 22
        }
      },
      "94": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 132,
          column: 6
        }
      },
      "95": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 131,
          column: 11
        }
      },
      "96": {
        start: {
          line: 102,
          column: 12
        },
        end: {
          line: 130,
          column: 15
        }
      },
      "97": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 129,
          column: 17
        }
      },
      "98": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 105,
          column: 61
        }
      },
      "99": {
        start: {
          line: 106,
          column: 24
        },
        end: {
          line: 106,
          column: 61
        }
      },
      "100": {
        start: {
          line: 107,
          column: 24
        },
        end: {
          line: 107,
          column: 37
        }
      },
      "101": {
        start: {
          line: 109,
          column: 24
        },
        end: {
          line: 109,
          column: 50
        }
      },
      "102": {
        start: {
          line: 110,
          column: 24
        },
        end: {
          line: 110,
          column: 100
        }
      },
      "103": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 112,
          column: 44
        }
      },
      "104": {
        start: {
          line: 113,
          column: 24
        },
        end: {
          line: 113,
          column: 161
        }
      },
      "105": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 167
        }
      },
      "106": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 115,
          column: 48
        }
      },
      "107": {
        start: {
          line: 117,
          column: 24
        },
        end: {
          line: 117,
          column: 39
        }
      },
      "108": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 48
        }
      },
      "109": {
        start: {
          line: 119,
          column: 28
        },
        end: {
          line: 128,
          column: 27
        }
      },
      "110": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 150,
          column: 6
        }
      },
      "111": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 140,
          column: 11
        }
      },
      "112": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 142,
          column: 85
        }
      },
      "113": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 149,
          column: 11
        }
      },
      "114": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 162,
          column: 6
        }
      },
      "115": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 161,
          column: 10
        }
      },
      "116": {
        start: {
          line: 163,
          column: 4
        },
        end: {
          line: 170,
          column: 6
        }
      },
      "117": {
        start: {
          line: 164,
          column: 31
        },
        end: {
          line: 168,
          column: 14
        }
      },
      "118": {
        start: {
          line: 164,
          column: 65
        },
        end: {
          line: 168,
          column: 11
        }
      },
      "119": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 143
        }
      },
      "120": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 182,
          column: 6
        }
      },
      "121": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 181,
          column: 9
        }
      },
      "122": {
        start: {
          line: 174,
          column: 16
        },
        end: {
          line: 174,
          column: 160
        }
      },
      "123": {
        start: {
          line: 176,
          column: 16
        },
        end: {
          line: 176,
          column: 145
        }
      },
      "124": {
        start: {
          line: 178,
          column: 16
        },
        end: {
          line: 178,
          column: 143
        }
      },
      "125": {
        start: {
          line: 180,
          column: 16
        },
        end: {
          line: 180,
          column: 207
        }
      },
      "126": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 192,
          column: 6
        }
      },
      "127": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 187,
          column: 9
        }
      },
      "128": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 186,
          column: 131
        }
      },
      "129": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 190,
          column: 9
        }
      },
      "130": {
        start: {
          line: 189,
          column: 12
        },
        end: {
          line: 189,
          column: 118
        }
      },
      "131": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 191,
          column: 125
        }
      },
      "132": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 257,
          column: 6
        }
      },
      "133": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 256,
          column: 11
        }
      },
      "134": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 255,
          column: 15
        }
      },
      "135": {
        start: {
          line: 197,
          column: 16
        },
        end: {
          line: 254,
          column: 17
        }
      },
      "136": {
        start: {
          line: 198,
          column: 28
        },
        end: {
          line: 198,
          column: 80
        }
      },
      "137": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 44
        }
      },
      "138": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 202,
          column: 54
        }
      },
      "139": {
        start: {
          line: 204,
          column: 24
        },
        end: {
          line: 251,
          column: 25
        }
      },
      "140": {
        start: {
          line: 205,
          column: 28
        },
        end: {
          line: 205,
          column: 109
        }
      },
      "141": {
        start: {
          line: 207,
          column: 29
        },
        end: {
          line: 251,
          column: 25
        }
      },
      "142": {
        start: {
          line: 208,
          column: 28
        },
        end: {
          line: 208,
          column: 112
        }
      },
      "143": {
        start: {
          line: 210,
          column: 29
        },
        end: {
          line: 251,
          column: 25
        }
      },
      "144": {
        start: {
          line: 211,
          column: 28
        },
        end: {
          line: 211,
          column: 175
        }
      },
      "145": {
        start: {
          line: 213,
          column: 29
        },
        end: {
          line: 251,
          column: 25
        }
      },
      "146": {
        start: {
          line: 217,
          column: 28
        },
        end: {
          line: 217,
          column: 110
        }
      },
      "147": {
        start: {
          line: 219,
          column: 29
        },
        end: {
          line: 251,
          column: 25
        }
      },
      "148": {
        start: {
          line: 220,
          column: 28
        },
        end: {
          line: 220,
          column: 60
        }
      },
      "149": {
        start: {
          line: 221,
          column: 28
        },
        end: {
          line: 221,
          column: 47
        }
      },
      "150": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 243,
          column: 29
        }
      },
      "151": {
        start: {
          line: 224,
          column: 36
        },
        end: {
          line: 224,
          column: 82
        }
      },
      "152": {
        start: {
          line: 225,
          column: 36
        },
        end: {
          line: 225,
          column: 42
        }
      },
      "153": {
        start: {
          line: 227,
          column: 36
        },
        end: {
          line: 227,
          column: 85
        }
      },
      "154": {
        start: {
          line: 228,
          column: 36
        },
        end: {
          line: 228,
          column: 42
        }
      },
      "155": {
        start: {
          line: 230,
          column: 36
        },
        end: {
          line: 230,
          column: 82
        }
      },
      "156": {
        start: {
          line: 231,
          column: 36
        },
        end: {
          line: 231,
          column: 42
        }
      },
      "157": {
        start: {
          line: 233,
          column: 36
        },
        end: {
          line: 233,
          column: 89
        }
      },
      "158": {
        start: {
          line: 234,
          column: 36
        },
        end: {
          line: 234,
          column: 42
        }
      },
      "159": {
        start: {
          line: 236,
          column: 36
        },
        end: {
          line: 236,
          column: 88
        }
      },
      "160": {
        start: {
          line: 237,
          column: 36
        },
        end: {
          line: 237,
          column: 42
        }
      },
      "161": {
        start: {
          line: 239,
          column: 36
        },
        end: {
          line: 239,
          column: 92
        }
      },
      "162": {
        start: {
          line: 240,
          column: 36
        },
        end: {
          line: 240,
          column: 42
        }
      },
      "163": {
        start: {
          line: 242,
          column: 36
        },
        end: {
          line: 242,
          column: 87
        }
      },
      "164": {
        start: {
          line: 244,
          column: 28
        },
        end: {
          line: 244,
          column: 199
        }
      },
      "165": {
        start: {
          line: 248,
          column: 28
        },
        end: {
          line: 250,
          column: 189
        }
      },
      "166": {
        start: {
          line: 252,
          column: 24
        },
        end: {
          line: 252,
          column: 86
        }
      },
      "167": {
        start: {
          line: 253,
          column: 24
        },
        end: {
          line: 253,
          column: 113
        }
      },
      "168": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 282,
          column: 6
        }
      },
      "169": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 281,
          column: 9
        }
      },
      "170": {
        start: {
          line: 262,
          column: 16
        },
        end: {
          line: 262,
          column: 27
        }
      },
      "171": {
        start: {
          line: 265,
          column: 16
        },
        end: {
          line: 265,
          column: 27
        }
      },
      "172": {
        start: {
          line: 267,
          column: 16
        },
        end: {
          line: 267,
          column: 27
        }
      },
      "173": {
        start: {
          line: 270,
          column: 16
        },
        end: {
          line: 270,
          column: 27
        }
      },
      "174": {
        start: {
          line: 272,
          column: 16
        },
        end: {
          line: 272,
          column: 27
        }
      },
      "175": {
        start: {
          line: 274,
          column: 16
        },
        end: {
          line: 274,
          column: 27
        }
      },
      "176": {
        start: {
          line: 276,
          column: 16
        },
        end: {
          line: 276,
          column: 27
        }
      },
      "177": {
        start: {
          line: 278,
          column: 16
        },
        end: {
          line: 278,
          column: 27
        }
      },
      "178": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 280,
          column: 27
        }
      },
      "179": {
        start: {
          line: 283,
          column: 4
        },
        end: {
          line: 290,
          column: 6
        }
      },
      "180": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 289,
          column: 10
        }
      },
      "181": {
        start: {
          line: 291,
          column: 4
        },
        end: {
          line: 291,
          column: 34
        }
      },
      "182": {
        start: {
          line: 293,
          column: 0
        },
        end: {
          line: 293,
          column: 56
        }
      },
      "183": {
        start: {
          line: 299,
          column: 16
        },
        end: {
          line: 299,
          column: 20
        }
      },
      "184": {
        start: {
          line: 300,
          column: 4
        },
        end: {
          line: 321,
          column: 6
        }
      },
      "185": {
        start: {
          line: 301,
          column: 19
        },
        end: {
          line: 301,
          column: 21
        }
      },
      "186": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 304,
          column: 9
        }
      },
      "187": {
        start: {
          line: 302,
          column: 22
        },
        end: {
          line: 302,
          column: 23
        }
      },
      "188": {
        start: {
          line: 303,
          column: 12
        },
        end: {
          line: 303,
          column: 41
        }
      },
      "189": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 320,
          column: 11
        }
      },
      "190": {
        start: {
          line: 307,
          column: 12
        },
        end: {
          line: 319,
          column: 15
        }
      },
      "191": {
        start: {
          line: 308,
          column: 16
        },
        end: {
          line: 318,
          column: 17
        }
      },
      "192": {
        start: {
          line: 310,
          column: 24
        },
        end: {
          line: 310,
          column: 50
        }
      },
      "193": {
        start: {
          line: 311,
          column: 24
        },
        end: {
          line: 311,
          column: 107
        }
      },
      "194": {
        start: {
          line: 312,
          column: 28
        },
        end: {
          line: 312,
          column: 61
        }
      },
      "195": {
        start: {
          line: 314,
          column: 24
        },
        end: {
          line: 314,
          column: 44
        }
      },
      "196": {
        start: {
          line: 315,
          column: 24
        },
        end: {
          line: 315,
          column: 99
        }
      },
      "197": {
        start: {
          line: 316,
          column: 28
        },
        end: {
          line: 316,
          column: 61
        }
      },
      "198": {
        start: {
          line: 317,
          column: 28
        },
        end: {
          line: 317,
          column: 50
        }
      },
      "199": {
        start: {
          line: 323,
          column: 0
        },
        end: {
          line: 323,
          column: 41
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 9,
            column: 44
          },
          end: {
            line: 9,
            column: 45
          }
        },
        loc: {
          start: {
            line: 9,
            column: 89
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 10,
            column: 13
          },
          end: {
            line: 10,
            column: 18
          }
        },
        loc: {
          start: {
            line: 10,
            column: 26
          },
          end: {
            line: 10,
            column: 112
          }
        },
        line: 10
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 10,
            column: 70
          },
          end: {
            line: 10,
            column: 71
          }
        },
        loc: {
          start: {
            line: 10,
            column: 89
          },
          end: {
            line: 10,
            column: 108
          }
        },
        line: 10
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 11,
            column: 36
          },
          end: {
            line: 11,
            column: 37
          }
        },
        loc: {
          start: {
            line: 11,
            column: 63
          },
          end: {
            line: 16,
            column: 5
          }
        },
        line: 11
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 12,
            column: 17
          },
          end: {
            line: 12,
            column: 26
          }
        },
        loc: {
          start: {
            line: 12,
            column: 34
          },
          end: {
            line: 12,
            column: 99
          }
        },
        line: 12
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 25
          }
        },
        loc: {
          start: {
            line: 13,
            column: 33
          },
          end: {
            line: 13,
            column: 102
          }
        },
        line: 13
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 14,
            column: 17
          },
          end: {
            line: 14,
            column: 21
          }
        },
        loc: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 118
          }
        },
        line: 14
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 18,
            column: 48
          },
          end: {
            line: 18,
            column: 49
          }
        },
        loc: {
          start: {
            line: 18,
            column: 73
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 18
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 19,
            column: 31
          }
        },
        loc: {
          start: {
            line: 19,
            column: 41
          },
          end: {
            line: 19,
            column: 83
          }
        },
        line: 19
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 20,
            column: 128
          },
          end: {
            line: 20,
            column: 129
          }
        },
        loc: {
          start: {
            line: 20,
            column: 139
          },
          end: {
            line: 20,
            column: 155
          }
        },
        line: 20
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 21,
            column: 13
          },
          end: {
            line: 21,
            column: 17
          }
        },
        loc: {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 70
          }
        },
        line: 21
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 21,
            column: 30
          },
          end: {
            line: 21,
            column: 31
          }
        },
        loc: {
          start: {
            line: 21,
            column: 43
          },
          end: {
            line: 21,
            column: 67
          }
        },
        line: 21
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 22,
            column: 13
          },
          end: {
            line: 22,
            column: 17
          }
        },
        loc: {
          start: {
            line: 22,
            column: 22
          },
          end: {
            line: 43,
            column: 5
          }
        },
        line: 22
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 45,
            column: 52
          },
          end: {
            line: 45,
            column: 53
          }
        },
        loc: {
          start: {
            line: 45,
            column: 78
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 45
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 87,
            column: 44
          },
          end: {
            line: 87,
            column: 45
          }
        },
        loc: {
          start: {
            line: 87,
            column: 56
          },
          end: {
            line: 292,
            column: 1
          }
        },
        line: 87
      },
      "15": {
        name: "UnifiedApiErrorHandler",
        decl: {
          start: {
            line: 88,
            column: 13
          },
          end: {
            line: 88,
            column: 35
          }
        },
        loc: {
          start: {
            line: 88,
            column: 38
          },
          end: {
            line: 89,
            column: 5
          }
        },
        line: 88
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 90,
            column: 47
          },
          end: {
            line: 90,
            column: 48
          }
        },
        loc: {
          start: {
            line: 90,
            column: 59
          },
          end: {
            line: 92,
            column: 5
          }
        },
        line: 90
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 93,
            column: 41
          },
          end: {
            line: 93,
            column: 42
          }
        },
        loc: {
          start: {
            line: 93,
            column: 60
          },
          end: {
            line: 97,
            column: 5
          }
        },
        line: 93
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 98,
            column: 45
          },
          end: {
            line: 98,
            column: 46
          }
        },
        loc: {
          start: {
            line: 98,
            column: 64
          },
          end: {
            line: 132,
            column: 5
          }
        },
        line: 98
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 99,
            column: 48
          },
          end: {
            line: 99,
            column: 49
          }
        },
        loc: {
          start: {
            line: 99,
            column: 60
          },
          end: {
            line: 131,
            column: 9
          }
        },
        line: 99
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 102,
            column: 37
          },
          end: {
            line: 102,
            column: 38
          }
        },
        loc: {
          start: {
            line: 102,
            column: 51
          },
          end: {
            line: 130,
            column: 13
          }
        },
        line: 102
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 133,
            column: 38
          },
          end: {
            line: 133,
            column: 39
          }
        },
        loc: {
          start: {
            line: 133,
            column: 64
          },
          end: {
            line: 150,
            column: 5
          }
        },
        line: 133
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 151,
            column: 49
          },
          end: {
            line: 151,
            column: 50
          }
        },
        loc: {
          start: {
            line: 151,
            column: 105
          },
          end: {
            line: 162,
            column: 5
          }
        },
        line: 151
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 163,
            column: 44
          },
          end: {
            line: 163,
            column: 45
          }
        },
        loc: {
          start: {
            line: 163,
            column: 83
          },
          end: {
            line: 170,
            column: 5
          }
        },
        line: 163
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 164,
            column: 48
          },
          end: {
            line: 164,
            column: 49
          }
        },
        loc: {
          start: {
            line: 164,
            column: 63
          },
          end: {
            line: 168,
            column: 13
          }
        },
        line: 164
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 171,
            column: 47
          },
          end: {
            line: 171,
            column: 48
          }
        },
        loc: {
          start: {
            line: 171,
            column: 86
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 171
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 183,
            column: 45
          },
          end: {
            line: 183,
            column: 46
          }
        },
        loc: {
          start: {
            line: 183,
            column: 84
          },
          end: {
            line: 192,
            column: 5
          }
        },
        line: 183
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 193,
            column: 41
          },
          end: {
            line: 193,
            column: 42
          }
        },
        loc: {
          start: {
            line: 193,
            column: 67
          },
          end: {
            line: 257,
            column: 5
          }
        },
        line: 193
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 194,
            column: 48
          },
          end: {
            line: 194,
            column: 49
          }
        },
        loc: {
          start: {
            line: 194,
            column: 60
          },
          end: {
            line: 256,
            column: 9
          }
        },
        line: 194
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 196,
            column: 37
          },
          end: {
            line: 196,
            column: 38
          }
        },
        loc: {
          start: {
            line: 196,
            column: 51
          },
          end: {
            line: 255,
            column: 13
          }
        },
        line: 196
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 258,
            column: 47
          },
          end: {
            line: 258,
            column: 48
          }
        },
        loc: {
          start: {
            line: 258,
            column: 68
          },
          end: {
            line: 282,
            column: 5
          }
        },
        line: 258
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 283,
            column: 51
          },
          end: {
            line: 283,
            column: 52
          }
        },
        loc: {
          start: {
            line: 283,
            column: 78
          },
          end: {
            line: 290,
            column: 5
          }
        },
        line: 283
      },
      "32": {
        name: "withUnifiedErrorHandling",
        decl: {
          start: {
            line: 298,
            column: 9
          },
          end: {
            line: 298,
            column: 33
          }
        },
        loc: {
          start: {
            line: 298,
            column: 43
          },
          end: {
            line: 322,
            column: 1
          }
        },
        line: 298
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 300,
            column: 11
          },
          end: {
            line: 300,
            column: 12
          }
        },
        loc: {
          start: {
            line: 300,
            column: 30
          },
          end: {
            line: 321,
            column: 5
          }
        },
        line: 300
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 305,
            column: 49
          },
          end: {
            line: 305,
            column: 50
          }
        },
        loc: {
          start: {
            line: 305,
            column: 61
          },
          end: {
            line: 320,
            column: 9
          }
        },
        line: 305
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 307,
            column: 37
          },
          end: {
            line: 307,
            column: 38
          }
        },
        loc: {
          start: {
            line: 307,
            column: 51
          },
          end: {
            line: 319,
            column: 13
          }
        },
        line: 307
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 9,
            column: 16
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 21
          }
        }, {
          start: {
            line: 9,
            column: 25
          },
          end: {
            line: 9,
            column: 39
          }
        }, {
          start: {
            line: 9,
            column: 44
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 9
      },
      "1": {
        loc: {
          start: {
            line: 10,
            column: 35
          },
          end: {
            line: 10,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 10,
            column: 56
          },
          end: {
            line: 10,
            column: 61
          }
        }, {
          start: {
            line: 10,
            column: 64
          },
          end: {
            line: 10,
            column: 109
          }
        }],
        line: 10
      },
      "2": {
        loc: {
          start: {
            line: 11,
            column: 16
          },
          end: {
            line: 11,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 16
          },
          end: {
            line: 11,
            column: 17
          }
        }, {
          start: {
            line: 11,
            column: 22
          },
          end: {
            line: 11,
            column: 33
          }
        }],
        line: 11
      },
      "3": {
        loc: {
          start: {
            line: 14,
            column: 32
          },
          end: {
            line: 14,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 46
          },
          end: {
            line: 14,
            column: 67
          }
        }, {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 115
          }
        }],
        line: 14
      },
      "4": {
        loc: {
          start: {
            line: 15,
            column: 51
          },
          end: {
            line: 15,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 51
          },
          end: {
            line: 15,
            column: 61
          }
        }, {
          start: {
            line: 15,
            column: 65
          },
          end: {
            line: 15,
            column: 67
          }
        }],
        line: 15
      },
      "5": {
        loc: {
          start: {
            line: 18,
            column: 18
          },
          end: {
            line: 44,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 18,
            column: 23
          }
        }, {
          start: {
            line: 18,
            column: 27
          },
          end: {
            line: 18,
            column: 43
          }
        }, {
          start: {
            line: 18,
            column: 48
          },
          end: {
            line: 44,
            column: 1
          }
        }],
        line: 18
      },
      "6": {
        loc: {
          start: {
            line: 19,
            column: 43
          },
          end: {
            line: 19,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 43
          },
          end: {
            line: 19,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 134
          },
          end: {
            line: 19,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 167
          },
          end: {
            line: 19,
            column: 175
          }
        }, {
          start: {
            line: 19,
            column: 178
          },
          end: {
            line: 19,
            column: 184
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 20,
            column: 74
          },
          end: {
            line: 20,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 74
          },
          end: {
            line: 20,
            column: 102
          }
        }, {
          start: {
            line: 20,
            column: 107
          },
          end: {
            line: 20,
            column: 155
          }
        }],
        line: 20
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 23,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 23,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 24,
            column: 15
          },
          end: {
            line: 24,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 15
          },
          end: {
            line: 24,
            column: 16
          }
        }, {
          start: {
            line: 24,
            column: 21
          },
          end: {
            line: 24,
            column: 44
          }
        }],
        line: 24
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 28
          },
          end: {
            line: 24,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 28
          },
          end: {
            line: 24,
            column: 33
          }
        }, {
          start: {
            line: 24,
            column: 38
          },
          end: {
            line: 24,
            column: 43
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 25,
            column: 12
          },
          end: {
            line: 25,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 25,
            column: 12
          },
          end: {
            line: 25,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 25
      },
      "13": {
        loc: {
          start: {
            line: 25,
            column: 23
          },
          end: {
            line: 25,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 23
          },
          end: {
            line: 25,
            column: 24
          }
        }, {
          start: {
            line: 25,
            column: 29
          },
          end: {
            line: 25,
            column: 125
          }
        }, {
          start: {
            line: 25,
            column: 130
          },
          end: {
            line: 25,
            column: 158
          }
        }],
        line: 25
      },
      "14": {
        loc: {
          start: {
            line: 25,
            column: 33
          },
          end: {
            line: 25,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 45
          },
          end: {
            line: 25,
            column: 56
          }
        }, {
          start: {
            line: 25,
            column: 59
          },
          end: {
            line: 25,
            column: 125
          }
        }],
        line: 25
      },
      "15": {
        loc: {
          start: {
            line: 25,
            column: 59
          },
          end: {
            line: 25,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 67
          },
          end: {
            line: 25,
            column: 116
          }
        }, {
          start: {
            line: 25,
            column: 119
          },
          end: {
            line: 25,
            column: 125
          }
        }],
        line: 25
      },
      "16": {
        loc: {
          start: {
            line: 25,
            column: 67
          },
          end: {
            line: 25,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 67
          },
          end: {
            line: 25,
            column: 77
          }
        }, {
          start: {
            line: 25,
            column: 82
          },
          end: {
            line: 25,
            column: 115
          }
        }],
        line: 25
      },
      "17": {
        loc: {
          start: {
            line: 25,
            column: 82
          },
          end: {
            line: 25,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 83
          },
          end: {
            line: 25,
            column: 98
          }
        }, {
          start: {
            line: 25,
            column: 103
          },
          end: {
            line: 25,
            column: 112
          }
        }],
        line: 25
      },
      "18": {
        loc: {
          start: {
            line: 26,
            column: 12
          },
          end: {
            line: 26,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 12
          },
          end: {
            line: 26,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "19": {
        loc: {
          start: {
            line: 27,
            column: 12
          },
          end: {
            line: 39,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 23
          }
        }, {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 29,
            column: 72
          }
        }, {
          start: {
            line: 30,
            column: 16
          },
          end: {
            line: 30,
            column: 65
          }
        }, {
          start: {
            line: 31,
            column: 16
          },
          end: {
            line: 31,
            column: 65
          }
        }, {
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 38,
            column: 43
          }
        }],
        line: 27
      },
      "20": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "21": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 74
          }
        }, {
          start: {
            line: 33,
            column: 79
          },
          end: {
            line: 33,
            column: 90
          }
        }, {
          start: {
            line: 33,
            column: 94
          },
          end: {
            line: 33,
            column: 105
          }
        }],
        line: 33
      },
      "22": {
        loc: {
          start: {
            line: 33,
            column: 42
          },
          end: {
            line: 33,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 42
          },
          end: {
            line: 33,
            column: 54
          }
        }, {
          start: {
            line: 33,
            column: 58
          },
          end: {
            line: 33,
            column: 73
          }
        }],
        line: 33
      },
      "23": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "24": {
        loc: {
          start: {
            line: 34,
            column: 24
          },
          end: {
            line: 34,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 24
          },
          end: {
            line: 34,
            column: 35
          }
        }, {
          start: {
            line: 34,
            column: 40
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: 34,
            column: 47
          },
          end: {
            line: 34,
            column: 59
          }
        }, {
          start: {
            line: 34,
            column: 63
          },
          end: {
            line: 34,
            column: 75
          }
        }],
        line: 34
      },
      "25": {
        loc: {
          start: {
            line: 35,
            column: 20
          },
          end: {
            line: 35,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 20
          },
          end: {
            line: 35,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "26": {
        loc: {
          start: {
            line: 35,
            column: 24
          },
          end: {
            line: 35,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 24
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: 35,
            column: 39
          },
          end: {
            line: 35,
            column: 53
          }
        }],
        line: 35
      },
      "27": {
        loc: {
          start: {
            line: 36,
            column: 20
          },
          end: {
            line: 36,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 20
          },
          end: {
            line: 36,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "28": {
        loc: {
          start: {
            line: 36,
            column: 24
          },
          end: {
            line: 36,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 24
          },
          end: {
            line: 36,
            column: 25
          }
        }, {
          start: {
            line: 36,
            column: 29
          },
          end: {
            line: 36,
            column: 43
          }
        }],
        line: 36
      },
      "29": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "30": {
        loc: {
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "31": {
        loc: {
          start: {
            line: 42,
            column: 52
          },
          end: {
            line: 42,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 42,
            column: 60
          },
          end: {
            line: 42,
            column: 65
          }
        }, {
          start: {
            line: 42,
            column: 68
          },
          end: {
            line: 42,
            column: 74
          }
        }],
        line: 42
      },
      "32": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 53,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 21
          },
          end: {
            line: 45,
            column: 25
          }
        }, {
          start: {
            line: 45,
            column: 29
          },
          end: {
            line: 45,
            column: 47
          }
        }, {
          start: {
            line: 45,
            column: 52
          },
          end: {
            line: 53,
            column: 1
          }
        }],
        line: 45
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 51,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 51,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 12
          }
        }, {
          start: {
            line: 46,
            column: 16
          },
          end: {
            line: 46,
            column: 38
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 47,
            column: 8
          },
          end: {
            line: 50,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 47,
            column: 8
          },
          end: {
            line: 50,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 47
      },
      "36": {
        loc: {
          start: {
            line: 47,
            column: 12
          },
          end: {
            line: 47,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 12
          },
          end: {
            line: 47,
            column: 14
          }
        }, {
          start: {
            line: 47,
            column: 18
          },
          end: {
            line: 47,
            column: 30
          }
        }],
        line: 47
      },
      "37": {
        loc: {
          start: {
            line: 48,
            column: 12
          },
          end: {
            line: 48,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 12
          },
          end: {
            line: 48,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "38": {
        loc: {
          start: {
            line: 52,
            column: 21
          },
          end: {
            line: 52,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 21
          },
          end: {
            line: 52,
            column: 23
          }
        }, {
          start: {
            line: 52,
            column: 27
          },
          end: {
            line: 52,
            column: 59
          }
        }],
        line: 52
      },
      "39": {
        loc: {
          start: {
            line: 94,
            column: 15
          },
          end: {
            line: 96,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 15
          },
          end: {
            line: 94,
            column: 53
          }
        }, {
          start: {
            line: 95,
            column: 12
          },
          end: {
            line: 95,
            column: 44
          }
        }, {
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 96,
            column: 21
          }
        }],
        line: 94
      },
      "40": {
        loc: {
          start: {
            line: 103,
            column: 16
          },
          end: {
            line: 129,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 104,
            column: 20
          },
          end: {
            line: 107,
            column: 37
          }
        }, {
          start: {
            line: 108,
            column: 20
          },
          end: {
            line: 110,
            column: 100
          }
        }, {
          start: {
            line: 111,
            column: 20
          },
          end: {
            line: 115,
            column: 48
          }
        }, {
          start: {
            line: 116,
            column: 20
          },
          end: {
            line: 118,
            column: 48
          }
        }, {
          start: {
            line: 119,
            column: 20
          },
          end: {
            line: 128,
            column: 27
          }
        }],
        line: 103
      },
      "41": {
        loc: {
          start: {
            line: 113,
            column: 33
          },
          end: {
            line: 113,
            column: 160
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 34
          },
          end: {
            line: 113,
            column: 146
          }
        }, {
          start: {
            line: 113,
            column: 151
          },
          end: {
            line: 113,
            column: 160
          }
        }],
        line: 113
      },
      "42": {
        loc: {
          start: {
            line: 113,
            column: 34
          },
          end: {
            line: 113,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 113,
            column: 132
          },
          end: {
            line: 113,
            column: 138
          }
        }, {
          start: {
            line: 113,
            column: 141
          },
          end: {
            line: 113,
            column: 146
          }
        }],
        line: 113
      },
      "43": {
        loc: {
          start: {
            line: 113,
            column: 34
          },
          end: {
            line: 113,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 34
          },
          end: {
            line: 113,
            column: 112
          }
        }, {
          start: {
            line: 113,
            column: 116
          },
          end: {
            line: 113,
            column: 129
          }
        }],
        line: 113
      },
      "44": {
        loc: {
          start: {
            line: 113,
            column: 40
          },
          end: {
            line: 113,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 113,
            column: 81
          },
          end: {
            line: 113,
            column: 87
          }
        }, {
          start: {
            line: 113,
            column: 90
          },
          end: {
            line: 113,
            column: 102
          }
        }],
        line: 113
      },
      "45": {
        loc: {
          start: {
            line: 113,
            column: 40
          },
          end: {
            line: 113,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 40
          },
          end: {
            line: 113,
            column: 56
          }
        }, {
          start: {
            line: 113,
            column: 60
          },
          end: {
            line: 113,
            column: 78
          }
        }],
        line: 113
      },
      "46": {
        loc: {
          start: {
            line: 114,
            column: 36
          },
          end: {
            line: 114,
            column: 166
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 114,
            column: 37
          },
          end: {
            line: 114,
            column: 152
          }
        }, {
          start: {
            line: 114,
            column: 157
          },
          end: {
            line: 114,
            column: 166
          }
        }],
        line: 114
      },
      "47": {
        loc: {
          start: {
            line: 114,
            column: 37
          },
          end: {
            line: 114,
            column: 152
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 114,
            column: 135
          },
          end: {
            line: 114,
            column: 141
          }
        }, {
          start: {
            line: 114,
            column: 144
          },
          end: {
            line: 114,
            column: 152
          }
        }],
        line: 114
      },
      "48": {
        loc: {
          start: {
            line: 114,
            column: 37
          },
          end: {
            line: 114,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 114,
            column: 37
          },
          end: {
            line: 114,
            column: 115
          }
        }, {
          start: {
            line: 114,
            column: 119
          },
          end: {
            line: 114,
            column: 132
          }
        }],
        line: 114
      },
      "49": {
        loc: {
          start: {
            line: 114,
            column: 43
          },
          end: {
            line: 114,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 114,
            column: 84
          },
          end: {
            line: 114,
            column: 90
          }
        }, {
          start: {
            line: 114,
            column: 93
          },
          end: {
            line: 114,
            column: 105
          }
        }],
        line: 114
      },
      "50": {
        loc: {
          start: {
            line: 114,
            column: 43
          },
          end: {
            line: 114,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 114,
            column: 43
          },
          end: {
            line: 114,
            column: 59
          }
        }, {
          start: {
            line: 114,
            column: 63
          },
          end: {
            line: 114,
            column: 81
          }
        }],
        line: 114
      },
      "51": {
        loc: {
          start: {
            line: 124,
            column: 39
          },
          end: {
            line: 124,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 124,
            column: 39
          },
          end: {
            line: 124,
            column: 72
          }
        }, {
          start: {
            line: 124,
            column: 76
          },
          end: {
            line: 124,
            column: 85
          }
        }],
        line: 124
      },
      "52": {
        loc: {
          start: {
            line: 157,
            column: 25
          },
          end: {
            line: 157,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 157,
            column: 66
          },
          end: {
            line: 157,
            column: 73
          }
        }, {
          start: {
            line: 157,
            column: 76
          },
          end: {
            line: 157,
            column: 85
          }
        }],
        line: 157
      },
      "53": {
        loc: {
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 181,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 173,
            column: 12
          },
          end: {
            line: 174,
            column: 160
          }
        }, {
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 176,
            column: 145
          }
        }, {
          start: {
            line: 177,
            column: 12
          },
          end: {
            line: 178,
            column: 143
          }
        }, {
          start: {
            line: 179,
            column: 12
          },
          end: {
            line: 180,
            column: 207
          }
        }],
        line: 172
      },
      "54": {
        loc: {
          start: {
            line: 180,
            column: 139
          },
          end: {
            line: 180,
            column: 205
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 180,
            column: 180
          },
          end: {
            line: 180,
            column: 193
          }
        }, {
          start: {
            line: 180,
            column: 196
          },
          end: {
            line: 180,
            column: 205
          }
        }],
        line: 180
      },
      "55": {
        loc: {
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "56": {
        loc: {
          start: {
            line: 185,
            column: 12
          },
          end: {
            line: 185,
            column: 190
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 13
          },
          end: {
            line: 185,
            column: 98
          }
        }, {
          start: {
            line: 185,
            column: 104
          },
          end: {
            line: 185,
            column: 189
          }
        }],
        line: 185
      },
      "57": {
        loc: {
          start: {
            line: 185,
            column: 13
          },
          end: {
            line: 185,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 185,
            column: 62
          },
          end: {
            line: 185,
            column: 68
          }
        }, {
          start: {
            line: 185,
            column: 71
          },
          end: {
            line: 185,
            column: 98
          }
        }],
        line: 185
      },
      "58": {
        loc: {
          start: {
            line: 185,
            column: 13
          },
          end: {
            line: 185,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 13
          },
          end: {
            line: 185,
            column: 42
          }
        }, {
          start: {
            line: 185,
            column: 46
          },
          end: {
            line: 185,
            column: 59
          }
        }],
        line: 185
      },
      "59": {
        loc: {
          start: {
            line: 185,
            column: 104
          },
          end: {
            line: 185,
            column: 189
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 185,
            column: 153
          },
          end: {
            line: 185,
            column: 159
          }
        }, {
          start: {
            line: 185,
            column: 162
          },
          end: {
            line: 185,
            column: 189
          }
        }],
        line: 185
      },
      "60": {
        loc: {
          start: {
            line: 185,
            column: 104
          },
          end: {
            line: 185,
            column: 150
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 104
          },
          end: {
            line: 185,
            column: 133
          }
        }, {
          start: {
            line: 185,
            column: 137
          },
          end: {
            line: 185,
            column: 150
          }
        }],
        line: 185
      },
      "61": {
        loc: {
          start: {
            line: 188,
            column: 8
          },
          end: {
            line: 190,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 8
          },
          end: {
            line: 190,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "62": {
        loc: {
          start: {
            line: 188,
            column: 12
          },
          end: {
            line: 188,
            column: 184
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 13
          },
          end: {
            line: 188,
            column: 95
          }
        }, {
          start: {
            line: 188,
            column: 101
          },
          end: {
            line: 188,
            column: 183
          }
        }],
        line: 188
      },
      "63": {
        loc: {
          start: {
            line: 188,
            column: 13
          },
          end: {
            line: 188,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 188,
            column: 62
          },
          end: {
            line: 188,
            column: 68
          }
        }, {
          start: {
            line: 188,
            column: 71
          },
          end: {
            line: 188,
            column: 95
          }
        }],
        line: 188
      },
      "64": {
        loc: {
          start: {
            line: 188,
            column: 13
          },
          end: {
            line: 188,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 13
          },
          end: {
            line: 188,
            column: 42
          }
        }, {
          start: {
            line: 188,
            column: 46
          },
          end: {
            line: 188,
            column: 59
          }
        }],
        line: 188
      },
      "65": {
        loc: {
          start: {
            line: 188,
            column: 101
          },
          end: {
            line: 188,
            column: 183
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 188,
            column: 150
          },
          end: {
            line: 188,
            column: 156
          }
        }, {
          start: {
            line: 188,
            column: 159
          },
          end: {
            line: 188,
            column: 183
          }
        }],
        line: 188
      },
      "66": {
        loc: {
          start: {
            line: 188,
            column: 101
          },
          end: {
            line: 188,
            column: 147
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 101
          },
          end: {
            line: 188,
            column: 130
          }
        }, {
          start: {
            line: 188,
            column: 134
          },
          end: {
            line: 188,
            column: 147
          }
        }],
        line: 188
      },
      "67": {
        loc: {
          start: {
            line: 197,
            column: 16
          },
          end: {
            line: 254,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 198,
            column: 80
          }
        }, {
          start: {
            line: 199,
            column: 20
          },
          end: {
            line: 253,
            column: 113
          }
        }],
        line: 197
      },
      "68": {
        loc: {
          start: {
            line: 204,
            column: 24
          },
          end: {
            line: 251,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 24
          },
          end: {
            line: 251,
            column: 25
          }
        }, {
          start: {
            line: 207,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }],
        line: 204
      },
      "69": {
        loc: {
          start: {
            line: 207,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }, {
          start: {
            line: 210,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }],
        line: 207
      },
      "70": {
        loc: {
          start: {
            line: 210,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }, {
          start: {
            line: 213,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }],
        line: 210
      },
      "71": {
        loc: {
          start: {
            line: 213,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }, {
          start: {
            line: 219,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }],
        line: 213
      },
      "72": {
        loc: {
          start: {
            line: 213,
            column: 33
          },
          end: {
            line: 216,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 213,
            column: 33
          },
          end: {
            line: 213,
            column: 55
          }
        }, {
          start: {
            line: 213,
            column: 60
          },
          end: {
            line: 213,
            column: 98
          }
        }, {
          start: {
            line: 214,
            column: 28
          },
          end: {
            line: 214,
            column: 63
          }
        }, {
          start: {
            line: 215,
            column: 28
          },
          end: {
            line: 215,
            column: 68
          }
        }, {
          start: {
            line: 216,
            column: 28
          },
          end: {
            line: 216,
            column: 66
          }
        }],
        line: 213
      },
      "73": {
        loc: {
          start: {
            line: 219,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }, {
          start: {
            line: 246,
            column: 29
          },
          end: {
            line: 251,
            column: 25
          }
        }],
        line: 219
      },
      "74": {
        loc: {
          start: {
            line: 219,
            column: 33
          },
          end: {
            line: 219,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 219,
            column: 33
          },
          end: {
            line: 219,
            column: 55
          }
        }, {
          start: {
            line: 219,
            column: 59
          },
          end: {
            line: 219,
            column: 75
          }
        }],
        line: 219
      },
      "75": {
        loc: {
          start: {
            line: 222,
            column: 28
          },
          end: {
            line: 243,
            column: 29
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 223,
            column: 32
          },
          end: {
            line: 225,
            column: 42
          }
        }, {
          start: {
            line: 226,
            column: 32
          },
          end: {
            line: 228,
            column: 42
          }
        }, {
          start: {
            line: 229,
            column: 32
          },
          end: {
            line: 231,
            column: 42
          }
        }, {
          start: {
            line: 232,
            column: 32
          },
          end: {
            line: 234,
            column: 42
          }
        }, {
          start: {
            line: 235,
            column: 32
          },
          end: {
            line: 237,
            column: 42
          }
        }, {
          start: {
            line: 238,
            column: 32
          },
          end: {
            line: 240,
            column: 42
          }
        }, {
          start: {
            line: 241,
            column: 32
          },
          end: {
            line: 242,
            column: 87
          }
        }],
        line: 222
      },
      "76": {
        loc: {
          start: {
            line: 244,
            column: 133
          },
          end: {
            line: 244,
            column: 197
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 244,
            column: 174
          },
          end: {
            line: 244,
            column: 185
          }
        }, {
          start: {
            line: 244,
            column: 188
          },
          end: {
            line: 244,
            column: 197
          }
        }],
        line: 244
      },
      "77": {
        loc: {
          start: {
            line: 248,
            column: 109
          },
          end: {
            line: 250,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 35
          },
          end: {
            line: 249,
            column: 106
          }
        }, {
          start: {
            line: 250,
            column: 34
          },
          end: {
            line: 250,
            column: 57
          }
        }],
        line: 248
      },
      "78": {
        loc: {
          start: {
            line: 249,
            column: 35
          },
          end: {
            line: 249,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 60
          },
          end: {
            line: 249,
            column: 73
          }
        }, {
          start: {
            line: 249,
            column: 76
          },
          end: {
            line: 249,
            column: 106
          }
        }],
        line: 249
      },
      "79": {
        loc: {
          start: {
            line: 250,
            column: 97
          },
          end: {
            line: 250,
            column: 187
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 250,
            column: 164
          },
          end: {
            line: 250,
            column: 175
          }
        }, {
          start: {
            line: 250,
            column: 178
          },
          end: {
            line: 250,
            column: 187
          }
        }],
        line: 250
      },
      "80": {
        loc: {
          start: {
            line: 250,
            column: 97
          },
          end: {
            line: 250,
            column: 161
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 97
          },
          end: {
            line: 250,
            column: 135
          }
        }, {
          start: {
            line: 250,
            column: 139
          },
          end: {
            line: 250,
            column: 161
          }
        }],
        line: 250
      },
      "81": {
        loc: {
          start: {
            line: 259,
            column: 8
          },
          end: {
            line: 281,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 260,
            column: 54
          }
        }, {
          start: {
            line: 261,
            column: 12
          },
          end: {
            line: 262,
            column: 27
          }
        }, {
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 263,
            column: 51
          }
        }, {
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 265,
            column: 27
          }
        }, {
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 267,
            column: 27
          }
        }, {
          start: {
            line: 268,
            column: 12
          },
          end: {
            line: 268,
            column: 58
          }
        }, {
          start: {
            line: 269,
            column: 12
          },
          end: {
            line: 270,
            column: 27
          }
        }, {
          start: {
            line: 271,
            column: 12
          },
          end: {
            line: 272,
            column: 27
          }
        }, {
          start: {
            line: 273,
            column: 12
          },
          end: {
            line: 274,
            column: 27
          }
        }, {
          start: {
            line: 275,
            column: 12
          },
          end: {
            line: 276,
            column: 27
          }
        }, {
          start: {
            line: 277,
            column: 12
          },
          end: {
            line: 278,
            column: 27
          }
        }, {
          start: {
            line: 279,
            column: 12
          },
          end: {
            line: 280,
            column: 27
          }
        }],
        line: 259
      },
      "82": {
        loc: {
          start: {
            line: 308,
            column: 16
          },
          end: {
            line: 318,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 309,
            column: 20
          },
          end: {
            line: 311,
            column: 107
          }
        }, {
          start: {
            line: 312,
            column: 20
          },
          end: {
            line: 312,
            column: 61
          }
        }, {
          start: {
            line: 313,
            column: 20
          },
          end: {
            line: 315,
            column: 99
          }
        }, {
          start: {
            line: 316,
            column: 20
          },
          end: {
            line: 316,
            column: 61
          }
        }, {
          start: {
            line: 317,
            column: 20
          },
          end: {
            line: 317,
            column: 50
          }
        }],
        line: 308
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0],
      "40": [0, 0, 0, 0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0, 0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0, 0, 0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0, 0, 0, 0, 0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "82": [0, 0, 0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/unified-api-error-handler.ts",
      mappings: ";AAAA;;;;;;GAMG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgWH,4DAUC;AAxWD,sCAAwD;AACxD,2BAA+B;AAC/B,yCAAwC;AACxC,uCAA6C;AAC7C,mCAAyC;AACzC,uCAAmC;AACnC,qDAAiD;AACjD,uDAAqD;AAErD,uBAAuB;AACV,QAAA,eAAe,GAAG;IAC7B,iCAAiC;IACjC,YAAY,EAAE,cAAc;IAC5B,SAAS,EAAE,WAAW;IACtB,eAAe,EAAE,iBAAiB;IAElC,aAAa;IACb,gBAAgB,EAAE,kBAAkB;IACpC,aAAa,EAAE,eAAe;IAE9B,WAAW;IACX,SAAS,EAAE,WAAW;IACtB,eAAe,EAAE,iBAAiB;IAClC,cAAc,EAAE,gBAAgB;IAEhC,gBAAgB;IAChB,mBAAmB,EAAE,qBAAqB;IAE1C,gBAAgB;IAChB,cAAc,EAAE,gBAAgB;IAChC,mBAAmB,EAAE,qBAAqB;IAE1C,iBAAiB;IACjB,uBAAuB,EAAE,yBAAyB;IAClD,wBAAwB,EAAE,0BAA0B;CAC5C,CAAC;AAsCX;IAAA;IA+QA,CAAC;IA9QgB,wCAAiB,GAAhC;QACE,OAAO,cAAO,IAAI,CAAC,GAAG,EAAE,cAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC;IACxE,CAAC;IAEc,kCAAW,GAA1B,UAA2B,OAAoB;QAC7C,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;YACtC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;YAChC,SAAS,CAAC;IACnB,CAAC;IAEoB,sCAAe,GAApC,UAAqC,OAAoB;uCAAG,OAAO;;;;;;wBAC3D,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACrC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;;;;wBAOzB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBACnD,MAAM,GAAG,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,KAAI,SAAS,CAAC;wBACxC,SAAS,GAAG,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,KAAI,SAAS,CAAC;;;;;4BAKhD,sBAAO;4BACL,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;4BAClC,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,MAAM,QAAA;4BACN,SAAS,WAAA;4BACT,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS;4BACzD,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;4BAC7B,SAAS,WAAA;4BACT,SAAS,WAAA;yBACV,EAAC;;;;KACH;IAEc,+BAAQ,GAAvB,UAAwB,KAAc,EAAE,OAAqB;QAC3D,+BAA+B;QAC/B,YAAG,CAAC,KAAK,CAAC,WAAW,EAAE,KAAc,EAAE;YACrC,SAAS,EAAE,2BAA2B;YACtC,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,OAAO,CAAC,SAAS;YACzB,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;QAEH,6BAA6B;QAC7B,0BAAU,CAAC,GAAG,CAAC,KAAc,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAEtE,mCAAmC;QACnC,8BAAa,CAAC,YAAY,CAAC,KAAc,EAAE;YACzC,MAAM,EAAE,OAAO,CAAC,SAAS;YACzB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,2BAA2B;SACvC,CAAC,CAAC;IACL,CAAC;IAEc,0CAAmB,GAAlC,UACE,IAAkB,EAClB,OAAe,EACf,SAAiB,EACjB,SAAiB,EACjB,OAAa;QAEb,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,MAAA;gBACJ,OAAO,SAAA;gBACP,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;gBACrE,SAAS,WAAA;gBACT,SAAS,WAAA;aACV;SACF,CAAC;IACJ,CAAC;IAEc,qCAAc,GAA7B,UAA8B,KAAe,EAAE,SAAiB,EAAE,SAAiB;QACjF,IAAM,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,CAAC;YAChD,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACzB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,IAAI,EAAE,GAAG,CAAC,IAAI;SACf,CAAC,EAJ+C,CAI/C,CAAC,CAAC;QAEJ,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,gBAAgB,EAChC,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,gBAAgB,CACjB,CAAC;IACJ,CAAC;IAEc,wCAAiB,GAAhC,UAAiC,KAA2C,EAAE,SAAiB,EAAE,SAAiB;QAChH,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,eAAe,EAC/B,+CAA+C,EAC/C,SAAS,EACT,SAAS,CACV,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,SAAS,EACzB,sCAAsC,EACtC,SAAS,EACT,SAAS,CACV,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,cAAc,EAC9B,+BAA+B,EAC/B,SAAS,EACT,SAAS,CACV,CAAC;YACJ;gBACE,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,cAAc,EAC9B,2BAA2B,EAC3B,SAAS,EACT,SAAS,EACT,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CACnE,CAAC;QACN,CAAC;IACH,CAAC;IAEc,sCAAe,GAA9B,UAA+B,KAAU,EAAE,SAAiB,EAAE,SAAiB;;QAC7E,IAAI,CAAA,MAAA,KAAK,CAAC,OAAO,0CAAE,QAAQ,CAAC,cAAc,CAAC,MAAI,MAAA,KAAK,CAAC,OAAO,0CAAE,QAAQ,CAAC,cAAc,CAAC,CAAA,EAAE,CAAC;YACvF,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,YAAY,EAC5B,yBAAyB,EACzB,SAAS,EACT,SAAS,CACV,CAAC;QACJ,CAAC;QAED,IAAI,CAAA,MAAA,KAAK,CAAC,OAAO,0CAAE,QAAQ,CAAC,WAAW,CAAC,MAAI,MAAA,KAAK,CAAC,OAAO,0CAAE,QAAQ,CAAC,WAAW,CAAC,CAAA,EAAE,CAAC;YACjF,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,SAAS,EACzB,eAAe,EACf,SAAS,EACT,SAAS,CACV,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAC7B,uBAAe,CAAC,YAAY,EAC5B,uBAAuB,EACvB,SAAS,EACT,SAAS,CACV,CAAC;IACJ,CAAC;IAEmB,kCAAW,GAA/B,UAAgC,KAAc,EAAE,OAAoB;uCAAG,OAAO;;;;4BAC5D,qBAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBAEnD,gBAAgB;wBAChB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;wBAI9B,+BAA+B;wBAC/B,IAAI,KAAK,YAAY,cAAQ,EAAE,CAAC;4BAC9B,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;wBACnF,CAAC;6BAAM,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;4BACjE,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;wBACtF,CAAC;6BAAM,IAAI,KAAK,YAAY,eAAM,CAAC,2BAA2B,EAAE,CAAC;4BAC/D,aAAa,GAAG,IAAI,CAAC,mBAAmB,CACtC,uBAAe,CAAC,gBAAgB,EAChC,wBAAwB,EACxB,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,SAAS,CAClB,CAAC;wBACJ,CAAC;6BAAM,IAAI,KAAK,YAAY,KAAK,IAAI,CACnC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;4BACtC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;4BACnC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;4BACxC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CACvC,EAAE,CAAC;4BACF,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;wBACpF,CAAC;6BAAM,IAAI,KAAK,YAAY,KAAK,IAAK,KAAa,CAAC,UAAU,EAAE,CAAC;4BAEzD,eAAc,KAAa,CAAC,UAAU,CAAC;4BACzC,SAAS,SAAc,CAAC;4BAE5B,QAAQ,YAAU,EAAE,CAAC;gCACnB,KAAK,GAAG;oCACN,SAAS,GAAG,uBAAe,CAAC,SAAS,CAAC;oCACtC,MAAM;gCACR,KAAK,GAAG;oCACN,SAAS,GAAG,uBAAe,CAAC,YAAY,CAAC;oCACzC,MAAM;gCACR,KAAK,GAAG;oCACN,SAAS,GAAG,uBAAe,CAAC,SAAS,CAAC;oCACtC,MAAM;gCACR,KAAK,GAAG;oCACN,SAAS,GAAG,uBAAe,CAAC,gBAAgB,CAAC;oCAC7C,MAAM;gCACR,KAAK,GAAG;oCACN,SAAS,GAAG,uBAAe,CAAC,eAAe,CAAC;oCAC5C,MAAM;gCACR,KAAK,GAAG;oCACN,SAAS,GAAG,uBAAe,CAAC,mBAAmB,CAAC;oCAChD,MAAM;gCACR;oCACE,SAAS,GAAG,uBAAe,CAAC,cAAc,CAAC;4BAC/C,CAAC;4BAED,aAAa,GAAG,IAAI,CAAC,mBAAmB,CACtC,SAAS,EACT,KAAK,CAAC,OAAO,EACb,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CACjE,CAAC;wBACJ,CAAC;6BAAM,CAAC;4BACN,yBAAyB;4BACzB,aAAa,GAAG,IAAI,CAAC,mBAAmB,CACtC,uBAAe,CAAC,cAAc,EAC9B,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;gCACpC,CAAC,CAAC,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC;gCAC3E,CAAC,CAAC,uBAAuB,EAC3B,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAC3F,CAAC;wBACJ,CAAC;wBAGK,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAEpE,sBAAO,qBAAY,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,EAAC;;;;KACjE;IAEc,wCAAiB,GAAhC,UAAiC,SAAuB;QACtD,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,uBAAe,CAAC,YAAY,CAAC;YAClC,KAAK,uBAAe,CAAC,eAAe;gBAClC,OAAO,GAAG,CAAC;YACb,KAAK,uBAAe,CAAC,SAAS,CAAC;YAC/B,KAAK,uBAAe,CAAC,wBAAwB;gBAC3C,OAAO,GAAG,CAAC;YACb,KAAK,uBAAe,CAAC,SAAS;gBAC5B,OAAO,GAAG,CAAC;YACb,KAAK,uBAAe,CAAC,gBAAgB,CAAC;YACtC,KAAK,uBAAe,CAAC,aAAa;gBAChC,OAAO,GAAG,CAAC;YACb,KAAK,uBAAe,CAAC,eAAe;gBAClC,OAAO,GAAG,CAAC;YACb,KAAK,uBAAe,CAAC,mBAAmB;gBACtC,OAAO,GAAG,CAAC;YACb,KAAK,uBAAe,CAAC,mBAAmB;gBACtC,OAAO,GAAG,CAAC;YACb,KAAK,uBAAe,CAAC,uBAAuB;gBAC1C,OAAO,GAAG,CAAC;YACb;gBACE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC;IAEa,4CAAqB,GAAnC,UAAuC,IAAO,EAAE,SAAkB;QAChE,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,MAAA;YACJ,SAAS,WAAA;YACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IACH,6BAAC;AAAD,CAAC,AA/QD,IA+QC;AAkBQ,wDAAsB;AAhB/B;;;GAGG;AACH,SAAgB,wBAAwB,CACtC,OAAoE;IADtE,iBAUC;IAPC,OAAO,UAAO,OAAoB;QAAE,cAAU;aAAV,UAAU,EAAV,qBAAU,EAAV,IAAU;YAAV,6BAAU;;wCAAG,OAAO;;;;;;wBAE7C,qBAAM,OAAO,8BAAC,OAAO,GAAK,IAAI,WAAC;4BAAtC,sBAAO,SAA+B,EAAC;;;wBAEhC,qBAAM,sBAAsB,CAAC,WAAW,CAAC,OAAK,EAAE,OAAO,CAAC,EAAA;4BAA/D,sBAAO,SAAwD,EAAC;;;;;KAEnE,CAAC;AACJ,CAAC;AAGD,kBAAe,sBAAsB,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/unified-api-error-handler.ts"],
      sourcesContent: ["/**\n * Unified API Error Handler\n * \n * This module provides a standardized error handling system for all API routes.\n * It consolidates the various error handling patterns into a single, secure,\n * and consistent approach.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { ZodError } from 'zod';\nimport { Prisma } from '@prisma/client';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { log } from '@/lib/logger';\nimport { trackError } from '@/lib/errorTracking';\nimport { ErrorReporter } from '@/lib/errorReporting';\n\n// Standard error codes\nexport const API_ERROR_CODES = {\n  // Authentication & Authorization\n  UNAUTHORIZED: 'UNAUTHORIZED',\n  FORBIDDEN: 'FORBIDDEN',\n  SESSION_EXPIRED: 'SESSION_EXPIRED',\n  \n  // Validation\n  VALIDATION_ERROR: 'VALIDATION_ERROR',\n  INVALID_INPUT: 'INVALID_INPUT',\n  \n  // Database\n  NOT_FOUND: 'NOT_FOUND',\n  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',\n  DATABASE_ERROR: 'DATABASE_ERROR',\n  \n  // Rate Limiting\n  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',\n  \n  // Server Errors\n  INTERNAL_ERROR: 'INTERNAL_ERROR',\n  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',\n  \n  // Business Logic\n  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',\n  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS'\n} as const;\n\nexport type ApiErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES];\n\n// Standard error response interface\nexport interface ApiErrorResponse {\n  success: false;\n  error: {\n    code: ApiErrorCode;\n    message: string;\n    details?: any;\n    requestId: string;\n    timestamp: string;\n  };\n}\n\n// Standard success response interface\nexport interface ApiSuccessResponse<T = any> {\n  success: true;\n  data: T;\n  requestId?: string;\n  timestamp?: string;\n}\n\nexport type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;\n\n// Error context for logging\ninterface ErrorContext {\n  endpoint: string;\n  method: string;\n  userId?: string;\n  userEmail?: string;\n  userAgent?: string;\n  ip?: string;\n  requestId: string;\n  timestamp: string;\n}\n\nclass UnifiedApiErrorHandler {\n  private static generateRequestId(): string {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private static getClientIp(request: NextRequest): string | undefined {\n    return request.headers.get('x-forwarded-for') || \n           request.headers.get('x-real-ip') || \n           undefined;\n  }\n\n  private static async getErrorContext(request: NextRequest): Promise<ErrorContext> {\n    const requestId = this.generateRequestId();\n    const timestamp = new Date().toISOString();\n    \n    // Try to get session for user context (but don't fail if it's not available)\n    let userId: string | undefined;\n    let userEmail: string | undefined;\n    \n    try {\n      const session = await getServerSession(authOptions);\n      userId = session?.user?.id || undefined;\n      userEmail = session?.user?.email || undefined;\n    } catch {\n      // Session retrieval failed, continue without user context\n    }\n\n    return {\n      endpoint: request.nextUrl.pathname,\n      method: request.method,\n      userId,\n      userEmail,\n      userAgent: request.headers.get('user-agent') || undefined,\n      ip: this.getClientIp(request),\n      requestId,\n      timestamp\n    };\n  }\n\n  private static logError(error: unknown, context: ErrorContext): void {\n    // Log to our structured logger\n    log.error('API Error', error as Error, {\n      component: 'unified_api_error_handler',\n      action: 'handle_error',\n      userId: context.userEmail,\n      requestId: context.requestId\n    });\n\n    // Track error for monitoring\n    trackError.api(error as Error, context.endpoint, context.method, 500);\n\n    // Report to error tracking service\n    ErrorReporter.captureError(error as Error, {\n      userId: context.userEmail,\n      userEmail: context.userEmail,\n      action: 'api_error',\n      component: 'unified_api_error_handler'\n    });\n  }\n\n  private static createErrorResponse(\n    code: ApiErrorCode,\n    message: string,\n    requestId: string,\n    timestamp: string,\n    details?: any\n  ): ApiErrorResponse {\n    return {\n      success: false,\n      error: {\n        code,\n        message,\n        details: process.env.NODE_ENV === 'development' ? details : undefined,\n        requestId,\n        timestamp\n      }\n    };\n  }\n\n  private static handleZodError(error: ZodError, requestId: string, timestamp: string): ApiErrorResponse {\n    const validationErrors = error.errors.map(err => ({\n      field: err.path.join('.'),\n      message: err.message,\n      code: err.code\n    }));\n\n    return this.createErrorResponse(\n      API_ERROR_CODES.VALIDATION_ERROR,\n      'Validation failed',\n      requestId,\n      timestamp,\n      validationErrors\n    );\n  }\n\n  private static handlePrismaError(error: Prisma.PrismaClientKnownRequestError, requestId: string, timestamp: string): ApiErrorResponse {\n    switch (error.code) {\n      case 'P2002':\n        return this.createErrorResponse(\n          API_ERROR_CODES.DUPLICATE_ENTRY,\n          'A record with this information already exists',\n          requestId,\n          timestamp\n        );\n      case 'P2025':\n        return this.createErrorResponse(\n          API_ERROR_CODES.NOT_FOUND,\n          'The requested resource was not found',\n          requestId,\n          timestamp\n        );\n      case 'P2003':\n        return this.createErrorResponse(\n          API_ERROR_CODES.DATABASE_ERROR,\n          'Foreign key constraint failed',\n          requestId,\n          timestamp\n        );\n      default:\n        return this.createErrorResponse(\n          API_ERROR_CODES.DATABASE_ERROR,\n          'Database operation failed',\n          requestId,\n          timestamp,\n          process.env.NODE_ENV === 'development' ? error.message : undefined\n        );\n    }\n  }\n\n  private static handleAuthError(error: any, requestId: string, timestamp: string): ApiErrorResponse {\n    if (error.message?.includes('unauthorized') || error.message?.includes('Unauthorized')) {\n      return this.createErrorResponse(\n        API_ERROR_CODES.UNAUTHORIZED,\n        'Authentication required',\n        requestId,\n        timestamp\n      );\n    }\n\n    if (error.message?.includes('forbidden') || error.message?.includes('Forbidden')) {\n      return this.createErrorResponse(\n        API_ERROR_CODES.FORBIDDEN,\n        'Access denied',\n        requestId,\n        timestamp\n      );\n    }\n\n    return this.createErrorResponse(\n      API_ERROR_CODES.UNAUTHORIZED,\n      'Authentication failed',\n      requestId,\n      timestamp\n    );\n  }\n\n  public static async handleError(error: unknown, request: NextRequest): Promise<NextResponse<ApiErrorResponse>> {\n    const context = await this.getErrorContext(request);\n    \n    // Log the error\n    this.logError(error, context);\n\n    let errorResponse: ApiErrorResponse;\n\n    // Handle different error types\n    if (error instanceof ZodError) {\n      errorResponse = this.handleZodError(error, context.requestId, context.timestamp);\n    } else if (error instanceof Prisma.PrismaClientKnownRequestError) {\n      errorResponse = this.handlePrismaError(error, context.requestId, context.timestamp);\n    } else if (error instanceof Prisma.PrismaClientValidationError) {\n      errorResponse = this.createErrorResponse(\n        API_ERROR_CODES.VALIDATION_ERROR,\n        'Invalid database query',\n        context.requestId,\n        context.timestamp\n      );\n    } else if (error instanceof Error && (\n      error.message.includes('unauthorized') ||\n      error.message.includes('forbidden') ||\n      error.message.includes('Authentication') ||\n      error.message.includes('Unauthorized')\n    )) {\n      errorResponse = this.handleAuthError(error, context.requestId, context.timestamp);\n    } else if (error instanceof Error && (error as any).statusCode) {\n      // Handle errors with custom status codes\n      const statusCode = (error as any).statusCode;\n      let errorCode: ApiErrorCode;\n\n      switch (statusCode) {\n        case 404:\n          errorCode = API_ERROR_CODES.NOT_FOUND;\n          break;\n        case 401:\n          errorCode = API_ERROR_CODES.UNAUTHORIZED;\n          break;\n        case 403:\n          errorCode = API_ERROR_CODES.FORBIDDEN;\n          break;\n        case 400:\n          errorCode = API_ERROR_CODES.VALIDATION_ERROR;\n          break;\n        case 409:\n          errorCode = API_ERROR_CODES.DUPLICATE_ENTRY;\n          break;\n        case 429:\n          errorCode = API_ERROR_CODES.RATE_LIMIT_EXCEEDED;\n          break;\n        default:\n          errorCode = API_ERROR_CODES.INTERNAL_ERROR;\n      }\n\n      errorResponse = this.createErrorResponse(\n        errorCode,\n        error.message,\n        context.requestId,\n        context.timestamp,\n        process.env.NODE_ENV === 'development' ? error.stack : undefined\n      );\n    } else {\n      // Generic error handling\n      errorResponse = this.createErrorResponse(\n        API_ERROR_CODES.INTERNAL_ERROR,\n        process.env.NODE_ENV === 'development'\n          ? (error instanceof Error ? error.message : 'An unexpected error occurred')\n          : 'Internal server error',\n        context.requestId,\n        context.timestamp,\n        process.env.NODE_ENV === 'development' && error instanceof Error ? error.stack : undefined\n      );\n    }\n\n    // Determine HTTP status code\n    const statusCode = this.getHttpStatusCode(errorResponse.error.code);\n\n    return NextResponse.json(errorResponse, { status: statusCode });\n  }\n\n  private static getHttpStatusCode(errorCode: ApiErrorCode): number {\n    switch (errorCode) {\n      case API_ERROR_CODES.UNAUTHORIZED:\n      case API_ERROR_CODES.SESSION_EXPIRED:\n        return 401;\n      case API_ERROR_CODES.FORBIDDEN:\n      case API_ERROR_CODES.INSUFFICIENT_PERMISSIONS:\n        return 403;\n      case API_ERROR_CODES.NOT_FOUND:\n        return 404;\n      case API_ERROR_CODES.VALIDATION_ERROR:\n      case API_ERROR_CODES.INVALID_INPUT:\n        return 400;\n      case API_ERROR_CODES.DUPLICATE_ENTRY:\n        return 409;\n      case API_ERROR_CODES.RATE_LIMIT_EXCEEDED:\n        return 429;\n      case API_ERROR_CODES.SERVICE_UNAVAILABLE:\n        return 503;\n      case API_ERROR_CODES.BUSINESS_RULE_VIOLATION:\n        return 422;\n      default:\n        return 500;\n    }\n  }\n\n  public static createSuccessResponse<T>(data: T, requestId?: string): ApiSuccessResponse<T> {\n    return {\n      success: true,\n      data,\n      requestId,\n      timestamp: new Date().toISOString()\n    };\n  }\n}\n\n/**\n * Unified error handling wrapper for API routes\n * This replaces withErrorHandler, withSecureErrorHandling, and manual try-catch blocks\n */\nexport function withUnifiedErrorHandling<T extends any[]>(\n  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>\n) {\n  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {\n    try {\n      return await handler(request, ...args);\n    } catch (error) {\n      return await UnifiedApiErrorHandler.handleError(error, request);\n    }\n  };\n}\n\nexport { UnifiedApiErrorHandler };\nexport default UnifiedApiErrorHandler;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b06bdc1e02a33e19a27d940cd47202f06c1c6885"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2mexa19fo = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2mexa19fo();
var __awaiter =
/* istanbul ignore next */
(cov_2mexa19fo().s[0]++,
/* istanbul ignore next */
(cov_2mexa19fo().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2mexa19fo().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2mexa19fo().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2mexa19fo().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[1]++;
    cov_2mexa19fo().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2mexa19fo().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_2mexa19fo().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2mexa19fo().f[2]++;
      cov_2mexa19fo().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2mexa19fo().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_2mexa19fo().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_2mexa19fo().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2mexa19fo().f[4]++;
      cov_2mexa19fo().s[4]++;
      try {
        /* istanbul ignore next */
        cov_2mexa19fo().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2mexa19fo().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2mexa19fo().f[5]++;
      cov_2mexa19fo().s[7]++;
      try {
        /* istanbul ignore next */
        cov_2mexa19fo().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2mexa19fo().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2mexa19fo().f[6]++;
      cov_2mexa19fo().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2mexa19fo().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2mexa19fo().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2mexa19fo().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2mexa19fo().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2mexa19fo().s[12]++,
/* istanbul ignore next */
(cov_2mexa19fo().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_2mexa19fo().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2mexa19fo().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2mexa19fo().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_2mexa19fo().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2mexa19fo().f[8]++;
        cov_2mexa19fo().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2mexa19fo().b[6][0]++;
          cov_2mexa19fo().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2mexa19fo().b[6][1]++;
        }
        cov_2mexa19fo().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2mexa19fo().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2mexa19fo().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2mexa19fo().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2mexa19fo().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2mexa19fo().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2mexa19fo().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2mexa19fo().f[9]++;
    cov_2mexa19fo().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[10]++;
    cov_2mexa19fo().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2mexa19fo().f[11]++;
      cov_2mexa19fo().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[12]++;
    cov_2mexa19fo().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_2mexa19fo().b[9][0]++;
      cov_2mexa19fo().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2mexa19fo().b[9][1]++;
    }
    cov_2mexa19fo().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_2mexa19fo().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_2mexa19fo().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2mexa19fo().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2mexa19fo().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2mexa19fo().s[25]++;
      try {
        /* istanbul ignore next */
        cov_2mexa19fo().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2mexa19fo().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_2mexa19fo().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2mexa19fo().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2mexa19fo().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2mexa19fo().b[15][0]++,
        /* istanbul ignore next */
        (cov_2mexa19fo().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2mexa19fo().b[16][1]++,
        /* istanbul ignore next */
        (cov_2mexa19fo().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2mexa19fo().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2mexa19fo().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2mexa19fo().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2mexa19fo().b[12][0]++;
          cov_2mexa19fo().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2mexa19fo().b[12][1]++;
        }
        cov_2mexa19fo().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2mexa19fo().b[18][0]++;
          cov_2mexa19fo().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2mexa19fo().b[18][1]++;
        }
        cov_2mexa19fo().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2mexa19fo().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2mexa19fo().b[19][1]++;
            cov_2mexa19fo().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_2mexa19fo().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2mexa19fo().b[19][2]++;
            cov_2mexa19fo().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_2mexa19fo().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2mexa19fo().b[19][3]++;
            cov_2mexa19fo().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_2mexa19fo().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2mexa19fo().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_2mexa19fo().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2mexa19fo().b[19][4]++;
            cov_2mexa19fo().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2mexa19fo().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2mexa19fo().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2mexa19fo().b[19][5]++;
            cov_2mexa19fo().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_2mexa19fo().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2mexa19fo().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2mexa19fo().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2mexa19fo().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2mexa19fo().b[20][0]++;
              cov_2mexa19fo().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2mexa19fo().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2mexa19fo().b[20][1]++;
            }
            cov_2mexa19fo().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_2mexa19fo().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2mexa19fo().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2mexa19fo().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2mexa19fo().b[23][0]++;
              cov_2mexa19fo().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2mexa19fo().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2mexa19fo().b[23][1]++;
            }
            cov_2mexa19fo().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_2mexa19fo().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2mexa19fo().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2mexa19fo().b[25][0]++;
              cov_2mexa19fo().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2mexa19fo().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_2mexa19fo().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2mexa19fo().b[25][1]++;
            }
            cov_2mexa19fo().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_2mexa19fo().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_2mexa19fo().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2mexa19fo().b[27][0]++;
              cov_2mexa19fo().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2mexa19fo().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2mexa19fo().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2mexa19fo().b[27][1]++;
            }
            cov_2mexa19fo().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2mexa19fo().b[29][0]++;
              cov_2mexa19fo().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2mexa19fo().b[29][1]++;
            }
            cov_2mexa19fo().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2mexa19fo().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2mexa19fo().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2mexa19fo().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2mexa19fo().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2mexa19fo().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2mexa19fo().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2mexa19fo().b[30][0]++;
      cov_2mexa19fo().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2mexa19fo().b[30][1]++;
    }
    cov_2mexa19fo().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2mexa19fo().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2mexa19fo().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_2mexa19fo().s[67]++,
/* istanbul ignore next */
(cov_2mexa19fo().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_2mexa19fo().b[32][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_2mexa19fo().b[32][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_2mexa19fo().f[13]++;
  cov_2mexa19fo().s[68]++;
  if (
  /* istanbul ignore next */
  (cov_2mexa19fo().b[34][0]++, pack) ||
  /* istanbul ignore next */
  (cov_2mexa19fo().b[34][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_2mexa19fo().b[33][0]++;
    cov_2mexa19fo().s[69]++;
    for (var i =
      /* istanbul ignore next */
      (cov_2mexa19fo().s[70]++, 0), l =
      /* istanbul ignore next */
      (cov_2mexa19fo().s[71]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_2mexa19fo().s[72]++;
      if (
      /* istanbul ignore next */
      (cov_2mexa19fo().b[36][0]++, ar) ||
      /* istanbul ignore next */
      (cov_2mexa19fo().b[36][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_2mexa19fo().b[35][0]++;
        cov_2mexa19fo().s[73]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_2mexa19fo().b[37][0]++;
          cov_2mexa19fo().s[74]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_2mexa19fo().b[37][1]++;
        }
        cov_2mexa19fo().s[75]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_2mexa19fo().b[35][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_2mexa19fo().b[33][1]++;
  }
  cov_2mexa19fo().s[76]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_2mexa19fo().b[38][0]++, ar) ||
  /* istanbul ignore next */
  (cov_2mexa19fo().b[38][1]++, Array.prototype.slice.call(from)));
}));
/* istanbul ignore next */
cov_2mexa19fo().s[77]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2mexa19fo().s[78]++;
exports.UnifiedApiErrorHandler = exports.API_ERROR_CODES = void 0;
/* istanbul ignore next */
cov_2mexa19fo().s[79]++;
exports.withUnifiedErrorHandling = withUnifiedErrorHandling;
var server_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[80]++, require("next/server"));
var zod_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[81]++, require("zod"));
var client_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[82]++, require("@prisma/client"));
var next_auth_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[83]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[84]++, require("@/lib/auth"));
var logger_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[85]++, require("@/lib/logger"));
var errorTracking_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[86]++, require("@/lib/errorTracking"));
var errorReporting_1 =
/* istanbul ignore next */
(cov_2mexa19fo().s[87]++, require("@/lib/errorReporting"));
// Standard error codes
/* istanbul ignore next */
cov_2mexa19fo().s[88]++;
exports.API_ERROR_CODES = {
  // Authentication & Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  // Database
  NOT_FOUND: 'NOT_FOUND',
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
  DATABASE_ERROR: 'DATABASE_ERROR',
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  // Server Errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  // Business Logic
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS'
};
var UnifiedApiErrorHandler =
/* istanbul ignore next */
(/** @class */cov_2mexa19fo().s[89]++, function () {
  /* istanbul ignore next */
  cov_2mexa19fo().f[14]++;
  function UnifiedApiErrorHandler() {
    /* istanbul ignore next */
    cov_2mexa19fo().f[15]++;
  }
  /* istanbul ignore next */
  cov_2mexa19fo().s[90]++;
  UnifiedApiErrorHandler.generateRequestId = function () {
    /* istanbul ignore next */
    cov_2mexa19fo().f[16]++;
    cov_2mexa19fo().s[91]++;
    return "req_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[92]++;
  UnifiedApiErrorHandler.getClientIp = function (request) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[17]++;
    cov_2mexa19fo().s[93]++;
    return /* istanbul ignore next */(cov_2mexa19fo().b[39][0]++, request.headers.get('x-forwarded-for')) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[39][1]++, request.headers.get('x-real-ip')) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[39][2]++, undefined);
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[94]++;
  UnifiedApiErrorHandler.getErrorContext = function (request) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[18]++;
    cov_2mexa19fo().s[95]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2mexa19fo().f[19]++;
      var requestId, timestamp, userId, userEmail, session, _a;
      var _b, _c;
      /* istanbul ignore next */
      cov_2mexa19fo().s[96]++;
      return __generator(this, function (_d) {
        /* istanbul ignore next */
        cov_2mexa19fo().f[20]++;
        cov_2mexa19fo().s[97]++;
        switch (_d.label) {
          case 0:
            /* istanbul ignore next */
            cov_2mexa19fo().b[40][0]++;
            cov_2mexa19fo().s[98]++;
            requestId = this.generateRequestId();
            /* istanbul ignore next */
            cov_2mexa19fo().s[99]++;
            timestamp = new Date().toISOString();
            /* istanbul ignore next */
            cov_2mexa19fo().s[100]++;
            _d.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_2mexa19fo().b[40][1]++;
            cov_2mexa19fo().s[101]++;
            _d.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_2mexa19fo().s[102]++;
            return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
          case 2:
            /* istanbul ignore next */
            cov_2mexa19fo().b[40][2]++;
            cov_2mexa19fo().s[103]++;
            session = _d.sent();
            /* istanbul ignore next */
            cov_2mexa19fo().s[104]++;
            userId =
            /* istanbul ignore next */
            (cov_2mexa19fo().b[41][0]++,
            /* istanbul ignore next */
            (cov_2mexa19fo().b[43][0]++, (_b =
            /* istanbul ignore next */
            (cov_2mexa19fo().b[45][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[45][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_2mexa19fo().b[44][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2mexa19fo().b[44][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[43][1]++, _b === void 0) ?
            /* istanbul ignore next */
            (cov_2mexa19fo().b[42][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2mexa19fo().b[42][1]++, _b.id)) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[41][1]++, undefined);
            /* istanbul ignore next */
            cov_2mexa19fo().s[105]++;
            userEmail =
            /* istanbul ignore next */
            (cov_2mexa19fo().b[46][0]++,
            /* istanbul ignore next */
            (cov_2mexa19fo().b[48][0]++, (_c =
            /* istanbul ignore next */
            (cov_2mexa19fo().b[50][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[50][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_2mexa19fo().b[49][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2mexa19fo().b[49][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[48][1]++, _c === void 0) ?
            /* istanbul ignore next */
            (cov_2mexa19fo().b[47][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2mexa19fo().b[47][1]++, _c.email)) ||
            /* istanbul ignore next */
            (cov_2mexa19fo().b[46][1]++, undefined);
            /* istanbul ignore next */
            cov_2mexa19fo().s[106]++;
            return [3 /*break*/, 4];
          case 3:
            /* istanbul ignore next */
            cov_2mexa19fo().b[40][3]++;
            cov_2mexa19fo().s[107]++;
            _a = _d.sent();
            /* istanbul ignore next */
            cov_2mexa19fo().s[108]++;
            return [3 /*break*/, 4];
          case 4:
            /* istanbul ignore next */
            cov_2mexa19fo().b[40][4]++;
            cov_2mexa19fo().s[109]++;
            return [2 /*return*/, {
              endpoint: request.nextUrl.pathname,
              method: request.method,
              userId: userId,
              userEmail: userEmail,
              userAgent:
              /* istanbul ignore next */
              (cov_2mexa19fo().b[51][0]++, request.headers.get('user-agent')) ||
              /* istanbul ignore next */
              (cov_2mexa19fo().b[51][1]++, undefined),
              ip: this.getClientIp(request),
              requestId: requestId,
              timestamp: timestamp
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[110]++;
  UnifiedApiErrorHandler.logError = function (error, context) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[21]++;
    cov_2mexa19fo().s[111]++;
    // Log to our structured logger
    logger_1.log.error('API Error', error, {
      component: 'unified_api_error_handler',
      action: 'handle_error',
      userId: context.userEmail,
      requestId: context.requestId
    });
    // Track error for monitoring
    /* istanbul ignore next */
    cov_2mexa19fo().s[112]++;
    errorTracking_1.trackError.api(error, context.endpoint, context.method, 500);
    // Report to error tracking service
    /* istanbul ignore next */
    cov_2mexa19fo().s[113]++;
    errorReporting_1.ErrorReporter.captureError(error, {
      userId: context.userEmail,
      userEmail: context.userEmail,
      action: 'api_error',
      component: 'unified_api_error_handler'
    });
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[114]++;
  UnifiedApiErrorHandler.createErrorResponse = function (code, message, requestId, timestamp, details) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[22]++;
    cov_2mexa19fo().s[115]++;
    return {
      success: false,
      error: {
        code: code,
        message: message,
        details: process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_2mexa19fo().b[52][0]++, details) :
        /* istanbul ignore next */
        (cov_2mexa19fo().b[52][1]++, undefined),
        requestId: requestId,
        timestamp: timestamp
      }
    };
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[116]++;
  UnifiedApiErrorHandler.handleZodError = function (error, requestId, timestamp) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[23]++;
    var validationErrors =
    /* istanbul ignore next */
    (cov_2mexa19fo().s[117]++, error.errors.map(function (err) {
      /* istanbul ignore next */
      cov_2mexa19fo().f[24]++;
      cov_2mexa19fo().s[118]++;
      return {
        field: err.path.join('.'),
        message: err.message,
        code: err.code
      };
    }));
    /* istanbul ignore next */
    cov_2mexa19fo().s[119]++;
    return this.createErrorResponse(exports.API_ERROR_CODES.VALIDATION_ERROR, 'Validation failed', requestId, timestamp, validationErrors);
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[120]++;
  UnifiedApiErrorHandler.handlePrismaError = function (error, requestId, timestamp) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[25]++;
    cov_2mexa19fo().s[121]++;
    switch (error.code) {
      case 'P2002':
        /* istanbul ignore next */
        cov_2mexa19fo().b[53][0]++;
        cov_2mexa19fo().s[122]++;
        return this.createErrorResponse(exports.API_ERROR_CODES.DUPLICATE_ENTRY, 'A record with this information already exists', requestId, timestamp);
      case 'P2025':
        /* istanbul ignore next */
        cov_2mexa19fo().b[53][1]++;
        cov_2mexa19fo().s[123]++;
        return this.createErrorResponse(exports.API_ERROR_CODES.NOT_FOUND, 'The requested resource was not found', requestId, timestamp);
      case 'P2003':
        /* istanbul ignore next */
        cov_2mexa19fo().b[53][2]++;
        cov_2mexa19fo().s[124]++;
        return this.createErrorResponse(exports.API_ERROR_CODES.DATABASE_ERROR, 'Foreign key constraint failed', requestId, timestamp);
      default:
        /* istanbul ignore next */
        cov_2mexa19fo().b[53][3]++;
        cov_2mexa19fo().s[125]++;
        return this.createErrorResponse(exports.API_ERROR_CODES.DATABASE_ERROR, 'Database operation failed', requestId, timestamp, process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_2mexa19fo().b[54][0]++, error.message) :
        /* istanbul ignore next */
        (cov_2mexa19fo().b[54][1]++, undefined));
    }
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[126]++;
  UnifiedApiErrorHandler.handleAuthError = function (error, requestId, timestamp) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[26]++;
    var _a, _b, _c, _d;
    /* istanbul ignore next */
    cov_2mexa19fo().s[127]++;
    if (
    /* istanbul ignore next */
    (cov_2mexa19fo().b[56][0]++,
    /* istanbul ignore next */
    (cov_2mexa19fo().b[58][0]++, (_a = error.message) === null) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[58][1]++, _a === void 0) ?
    /* istanbul ignore next */
    (cov_2mexa19fo().b[57][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2mexa19fo().b[57][1]++, _a.includes('unauthorized'))) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[56][1]++,
    /* istanbul ignore next */
    (cov_2mexa19fo().b[60][0]++, (_b = error.message) === null) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[60][1]++, _b === void 0) ?
    /* istanbul ignore next */
    (cov_2mexa19fo().b[59][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2mexa19fo().b[59][1]++, _b.includes('Unauthorized')))) {
      /* istanbul ignore next */
      cov_2mexa19fo().b[55][0]++;
      cov_2mexa19fo().s[128]++;
      return this.createErrorResponse(exports.API_ERROR_CODES.UNAUTHORIZED, 'Authentication required', requestId, timestamp);
    } else
    /* istanbul ignore next */
    {
      cov_2mexa19fo().b[55][1]++;
    }
    cov_2mexa19fo().s[129]++;
    if (
    /* istanbul ignore next */
    (cov_2mexa19fo().b[62][0]++,
    /* istanbul ignore next */
    (cov_2mexa19fo().b[64][0]++, (_c = error.message) === null) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[64][1]++, _c === void 0) ?
    /* istanbul ignore next */
    (cov_2mexa19fo().b[63][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2mexa19fo().b[63][1]++, _c.includes('forbidden'))) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[62][1]++,
    /* istanbul ignore next */
    (cov_2mexa19fo().b[66][0]++, (_d = error.message) === null) ||
    /* istanbul ignore next */
    (cov_2mexa19fo().b[66][1]++, _d === void 0) ?
    /* istanbul ignore next */
    (cov_2mexa19fo().b[65][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2mexa19fo().b[65][1]++, _d.includes('Forbidden')))) {
      /* istanbul ignore next */
      cov_2mexa19fo().b[61][0]++;
      cov_2mexa19fo().s[130]++;
      return this.createErrorResponse(exports.API_ERROR_CODES.FORBIDDEN, 'Access denied', requestId, timestamp);
    } else
    /* istanbul ignore next */
    {
      cov_2mexa19fo().b[61][1]++;
    }
    cov_2mexa19fo().s[131]++;
    return this.createErrorResponse(exports.API_ERROR_CODES.UNAUTHORIZED, 'Authentication failed', requestId, timestamp);
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[132]++;
  UnifiedApiErrorHandler.handleError = function (error, request) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[27]++;
    cov_2mexa19fo().s[133]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2mexa19fo().f[28]++;
      var context, errorResponse, statusCode_1, errorCode, statusCode;
      /* istanbul ignore next */
      cov_2mexa19fo().s[134]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_2mexa19fo().f[29]++;
        cov_2mexa19fo().s[135]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_2mexa19fo().b[67][0]++;
            cov_2mexa19fo().s[136]++;
            return [4 /*yield*/, this.getErrorContext(request)];
          case 1:
            /* istanbul ignore next */
            cov_2mexa19fo().b[67][1]++;
            cov_2mexa19fo().s[137]++;
            context = _a.sent();
            // Log the error
            /* istanbul ignore next */
            cov_2mexa19fo().s[138]++;
            this.logError(error, context);
            // Handle different error types
            /* istanbul ignore next */
            cov_2mexa19fo().s[139]++;
            if (error instanceof zod_1.ZodError) {
              /* istanbul ignore next */
              cov_2mexa19fo().b[68][0]++;
              cov_2mexa19fo().s[140]++;
              errorResponse = this.handleZodError(error, context.requestId, context.timestamp);
            } else {
              /* istanbul ignore next */
              cov_2mexa19fo().b[68][1]++;
              cov_2mexa19fo().s[141]++;
              if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                /* istanbul ignore next */
                cov_2mexa19fo().b[69][0]++;
                cov_2mexa19fo().s[142]++;
                errorResponse = this.handlePrismaError(error, context.requestId, context.timestamp);
              } else {
                /* istanbul ignore next */
                cov_2mexa19fo().b[69][1]++;
                cov_2mexa19fo().s[143]++;
                if (error instanceof client_1.Prisma.PrismaClientValidationError) {
                  /* istanbul ignore next */
                  cov_2mexa19fo().b[70][0]++;
                  cov_2mexa19fo().s[144]++;
                  errorResponse = this.createErrorResponse(exports.API_ERROR_CODES.VALIDATION_ERROR, 'Invalid database query', context.requestId, context.timestamp);
                } else {
                  /* istanbul ignore next */
                  cov_2mexa19fo().b[70][1]++;
                  cov_2mexa19fo().s[145]++;
                  if (
                  /* istanbul ignore next */
                  (cov_2mexa19fo().b[72][0]++, error instanceof Error) && (
                  /* istanbul ignore next */
                  (cov_2mexa19fo().b[72][1]++, error.message.includes('unauthorized')) ||
                  /* istanbul ignore next */
                  (cov_2mexa19fo().b[72][2]++, error.message.includes('forbidden')) ||
                  /* istanbul ignore next */
                  (cov_2mexa19fo().b[72][3]++, error.message.includes('Authentication')) ||
                  /* istanbul ignore next */
                  (cov_2mexa19fo().b[72][4]++, error.message.includes('Unauthorized')))) {
                    /* istanbul ignore next */
                    cov_2mexa19fo().b[71][0]++;
                    cov_2mexa19fo().s[146]++;
                    errorResponse = this.handleAuthError(error, context.requestId, context.timestamp);
                  } else {
                    /* istanbul ignore next */
                    cov_2mexa19fo().b[71][1]++;
                    cov_2mexa19fo().s[147]++;
                    if (
                    /* istanbul ignore next */
                    (cov_2mexa19fo().b[74][0]++, error instanceof Error) &&
                    /* istanbul ignore next */
                    (cov_2mexa19fo().b[74][1]++, error.statusCode)) {
                      /* istanbul ignore next */
                      cov_2mexa19fo().b[73][0]++;
                      cov_2mexa19fo().s[148]++;
                      statusCode_1 = error.statusCode;
                      /* istanbul ignore next */
                      cov_2mexa19fo().s[149]++;
                      errorCode = void 0;
                      /* istanbul ignore next */
                      cov_2mexa19fo().s[150]++;
                      switch (statusCode_1) {
                        case 404:
                          /* istanbul ignore next */
                          cov_2mexa19fo().b[75][0]++;
                          cov_2mexa19fo().s[151]++;
                          errorCode = exports.API_ERROR_CODES.NOT_FOUND;
                          /* istanbul ignore next */
                          cov_2mexa19fo().s[152]++;
                          break;
                        case 401:
                          /* istanbul ignore next */
                          cov_2mexa19fo().b[75][1]++;
                          cov_2mexa19fo().s[153]++;
                          errorCode = exports.API_ERROR_CODES.UNAUTHORIZED;
                          /* istanbul ignore next */
                          cov_2mexa19fo().s[154]++;
                          break;
                        case 403:
                          /* istanbul ignore next */
                          cov_2mexa19fo().b[75][2]++;
                          cov_2mexa19fo().s[155]++;
                          errorCode = exports.API_ERROR_CODES.FORBIDDEN;
                          /* istanbul ignore next */
                          cov_2mexa19fo().s[156]++;
                          break;
                        case 400:
                          /* istanbul ignore next */
                          cov_2mexa19fo().b[75][3]++;
                          cov_2mexa19fo().s[157]++;
                          errorCode = exports.API_ERROR_CODES.VALIDATION_ERROR;
                          /* istanbul ignore next */
                          cov_2mexa19fo().s[158]++;
                          break;
                        case 409:
                          /* istanbul ignore next */
                          cov_2mexa19fo().b[75][4]++;
                          cov_2mexa19fo().s[159]++;
                          errorCode = exports.API_ERROR_CODES.DUPLICATE_ENTRY;
                          /* istanbul ignore next */
                          cov_2mexa19fo().s[160]++;
                          break;
                        case 429:
                          /* istanbul ignore next */
                          cov_2mexa19fo().b[75][5]++;
                          cov_2mexa19fo().s[161]++;
                          errorCode = exports.API_ERROR_CODES.RATE_LIMIT_EXCEEDED;
                          /* istanbul ignore next */
                          cov_2mexa19fo().s[162]++;
                          break;
                        default:
                          /* istanbul ignore next */
                          cov_2mexa19fo().b[75][6]++;
                          cov_2mexa19fo().s[163]++;
                          errorCode = exports.API_ERROR_CODES.INTERNAL_ERROR;
                      }
                      /* istanbul ignore next */
                      cov_2mexa19fo().s[164]++;
                      errorResponse = this.createErrorResponse(errorCode, error.message, context.requestId, context.timestamp, process.env.NODE_ENV === 'development' ?
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[76][0]++, error.stack) :
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[76][1]++, undefined));
                    } else {
                      /* istanbul ignore next */
                      cov_2mexa19fo().b[73][1]++;
                      cov_2mexa19fo().s[165]++;
                      // Generic error handling
                      errorResponse = this.createErrorResponse(exports.API_ERROR_CODES.INTERNAL_ERROR, process.env.NODE_ENV === 'development' ?
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[77][0]++, error instanceof Error ?
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[78][0]++, error.message) :
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[78][1]++, 'An unexpected error occurred')) :
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[77][1]++, 'Internal server error'), context.requestId, context.timestamp,
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[80][0]++, process.env.NODE_ENV === 'development') &&
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[80][1]++, error instanceof Error) ?
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[79][0]++, error.stack) :
                      /* istanbul ignore next */
                      (cov_2mexa19fo().b[79][1]++, undefined));
                    }
                  }
                }
              }
            }
            /* istanbul ignore next */
            cov_2mexa19fo().s[166]++;
            statusCode = this.getHttpStatusCode(errorResponse.error.code);
            /* istanbul ignore next */
            cov_2mexa19fo().s[167]++;
            return [2 /*return*/, server_1.NextResponse.json(errorResponse, {
              status: statusCode
            })];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[168]++;
  UnifiedApiErrorHandler.getHttpStatusCode = function (errorCode) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[30]++;
    cov_2mexa19fo().s[169]++;
    switch (errorCode) {
      case exports.API_ERROR_CODES.UNAUTHORIZED:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][0]++;
      case exports.API_ERROR_CODES.SESSION_EXPIRED:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][1]++;
        cov_2mexa19fo().s[170]++;
        return 401;
      case exports.API_ERROR_CODES.FORBIDDEN:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][2]++;
      case exports.API_ERROR_CODES.INSUFFICIENT_PERMISSIONS:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][3]++;
        cov_2mexa19fo().s[171]++;
        return 403;
      case exports.API_ERROR_CODES.NOT_FOUND:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][4]++;
        cov_2mexa19fo().s[172]++;
        return 404;
      case exports.API_ERROR_CODES.VALIDATION_ERROR:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][5]++;
      case exports.API_ERROR_CODES.INVALID_INPUT:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][6]++;
        cov_2mexa19fo().s[173]++;
        return 400;
      case exports.API_ERROR_CODES.DUPLICATE_ENTRY:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][7]++;
        cov_2mexa19fo().s[174]++;
        return 409;
      case exports.API_ERROR_CODES.RATE_LIMIT_EXCEEDED:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][8]++;
        cov_2mexa19fo().s[175]++;
        return 429;
      case exports.API_ERROR_CODES.SERVICE_UNAVAILABLE:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][9]++;
        cov_2mexa19fo().s[176]++;
        return 503;
      case exports.API_ERROR_CODES.BUSINESS_RULE_VIOLATION:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][10]++;
        cov_2mexa19fo().s[177]++;
        return 422;
      default:
        /* istanbul ignore next */
        cov_2mexa19fo().b[81][11]++;
        cov_2mexa19fo().s[178]++;
        return 500;
    }
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[179]++;
  UnifiedApiErrorHandler.createSuccessResponse = function (data, requestId) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[31]++;
    cov_2mexa19fo().s[180]++;
    return {
      success: true,
      data: data,
      requestId: requestId,
      timestamp: new Date().toISOString()
    };
  };
  /* istanbul ignore next */
  cov_2mexa19fo().s[181]++;
  return UnifiedApiErrorHandler;
}());
/* istanbul ignore next */
cov_2mexa19fo().s[182]++;
exports.UnifiedApiErrorHandler = UnifiedApiErrorHandler;
/**
 * Unified error handling wrapper for API routes
 * This replaces withErrorHandler, withSecureErrorHandling, and manual try-catch blocks
 */
function withUnifiedErrorHandling(handler) {
  /* istanbul ignore next */
  cov_2mexa19fo().f[32]++;
  var _this =
  /* istanbul ignore next */
  (cov_2mexa19fo().s[183]++, this);
  /* istanbul ignore next */
  cov_2mexa19fo().s[184]++;
  return function (request) {
    /* istanbul ignore next */
    cov_2mexa19fo().f[33]++;
    var args =
    /* istanbul ignore next */
    (cov_2mexa19fo().s[185]++, []);
    /* istanbul ignore next */
    cov_2mexa19fo().s[186]++;
    for (var _i =
    /* istanbul ignore next */
    (cov_2mexa19fo().s[187]++, 1); _i < arguments.length; _i++) {
      /* istanbul ignore next */
      cov_2mexa19fo().s[188]++;
      args[_i - 1] = arguments[_i];
    }
    /* istanbul ignore next */
    cov_2mexa19fo().s[189]++;
    return __awaiter(_this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2mexa19fo().f[34]++;
      var error_1;
      /* istanbul ignore next */
      cov_2mexa19fo().s[190]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_2mexa19fo().f[35]++;
        cov_2mexa19fo().s[191]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_2mexa19fo().b[82][0]++;
            cov_2mexa19fo().s[192]++;
            _a.trys.push([0, 2,, 4]);
            /* istanbul ignore next */
            cov_2mexa19fo().s[193]++;
            return [4 /*yield*/, handler.apply(void 0, __spreadArray([request], args, false))];
          case 1:
            /* istanbul ignore next */
            cov_2mexa19fo().b[82][1]++;
            cov_2mexa19fo().s[194]++;
            return [2 /*return*/, _a.sent()];
          case 2:
            /* istanbul ignore next */
            cov_2mexa19fo().b[82][2]++;
            cov_2mexa19fo().s[195]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_2mexa19fo().s[196]++;
            return [4 /*yield*/, UnifiedApiErrorHandler.handleError(error_1, request)];
          case 3:
            /* istanbul ignore next */
            cov_2mexa19fo().b[82][3]++;
            cov_2mexa19fo().s[197]++;
            return [2 /*return*/, _a.sent()];
          case 4:
            /* istanbul ignore next */
            cov_2mexa19fo().b[82][4]++;
            cov_2mexa19fo().s[198]++;
            return [2 /*return*/];
        }
      });
    });
  };
}
/* istanbul ignore next */
cov_2mexa19fo().s[199]++;
exports.default = UnifiedApiErrorHandler;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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