{"version": 3, "names": ["cov_eh<PERSON><PERSON><PERSON><PERSON>", "actualCoverage", "components_1", "s", "require", "react_1", "__importDefault", "PasswordResetEmail", "_a", "f", "resetLink", "jsx_runtime_1", "jsxs", "Html", "lang", "children", "jsx", "Head", "Body", "style", "main", "Container", "container", "Img", "src", "width", "height", "alt", "logo", "Text", "paragraph", "Link", "href", "button", "exports", "backgroundColor", "fontFamily", "margin", "padding", "marginBottom", "color", "fontSize", "lineHeight", "textAlign", "borderRadius", "fontWeight", "textDecoration", "display"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/emails/PasswordResetEmail.tsx"], "sourcesContent": ["import { Html, Head, Body, Container, Text, Link, Img } from '@react-email/components';\nimport React from 'react';\n\ninterface PasswordResetEmailProps {\n  resetLink: string;\n}\n\nexport const PasswordResetEmail = ({ resetLink }: PasswordResetEmailProps) => (\n  <Html lang=\"en\">\n    <Head />\n    <Body style={main}>\n      <Container style={container}>\n        <Img\n          src=\"https://res.cloudinary.com/example/image/upload/v1678901234/logo.png\"\n          width=\"170\"\n          height=\"50\"\n          alt=\"FAAFO Logo\"\n          style={logo}\n        />\n        <Text style={paragraph}>Hi there,</Text>\n        <Text style={paragraph}>\n          You recently requested to reset your password for your FAAFO account.\n          Click the button below to reset it.\n        </Text>\n        <Link href={resetLink} style={button}>\n          Reset your password\n        </Link>\n        <Text style={paragraph}>\n          If you did not request a password reset, please ignore this email or\n          contact support if you have any questions.\n        </Text>\n        <Text style={paragraph}>Thanks,</Text>\n        <Text style={paragraph}>The FAAFO Team</Text>\n      </Container>\n    </Body>\n  </Html>\n);\n\nconst main = {\n  backgroundColor: '#f6f9fc',\n  fontFamily: '-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Ubuntu,sans-serif',\n};\n\nconst container = {\n  backgroundColor: '#ffffff',\n  margin: '0 auto',\n  padding: '20px 0 48px',\n  marginBottom: '64px',\n};\n\nconst logo = {\n  margin: '0 auto',\n};\n\nconst paragraph = {\n  color: '#444',\n  fontSize: '15px',\n  lineHeight: '24px',\n  textAlign: 'left' as const,\n};\n\nconst button = {\n  backgroundColor: '#374151',\n  borderRadius: '5px',\n  color: '#fff',\n  fontSize: '15px',\n  fontWeight: 'bold',\n  textDecoration: 'none',\n  textAlign: 'center' as const,\n  display: 'block',\n  width: '210px',\n  padding: '10px 0',\n  margin: '20px auto',\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuCE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvCF,IAAAE,YAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,OAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AAA0B;AAAAJ,aAAA,GAAAG,CAAA;AAMnB,IAAMI,kBAAkB,GAAG,SAAAA,CAACC,EAAsC;EAAA;EAAAR,aAAA,GAAAS,CAAA;MAApCC,SAAS;EAAA;EAAA,CAAAV,aAAA,GAAAG,CAAA,OAAAK,EAAA,CAAAE,SAAA;EAAA;EAAAV,aAAA,GAAAG,CAAA;EAAgC,OAC5E,IAAAQ,aAAA,CAAAC,IAAA,EAACV,YAAA,CAAAW,IAAI;IAACC,IAAI,EAAC,IAAI;IAAAC,QAAA,GACb,IAAAJ,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAAe,IAAI,KAAG,EACR,IAAAN,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAAgB,IAAI;MAACC,KAAK,EAAEC,IAAI;MAAAL,QAAA,EACf,IAAAJ,aAAA,CAAAC,IAAA,EAACV,YAAA,CAAAmB,SAAS;QAACF,KAAK,EAAEG,SAAS;QAAAP,QAAA,GACzB,IAAAJ,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAAqB,GAAG;UACFC,GAAG,EAAC,sEAAsE;UAC1EC,KAAK,EAAC,KAAK;UACXC,MAAM,EAAC,IAAI;UACXC,GAAG,EAAC,YAAY;UAChBR,KAAK,EAAES;QAAI,EACX,EACF,IAAAjB,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAA2B,IAAI;UAACV,KAAK,EAAEW,SAAS;UAAAf,QAAA;QAAA,EAAkB,EACxC,IAAAJ,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAA2B,IAAI;UAACV,KAAK,EAAEW,SAAS;UAAAf,QAAA;QAAA,EAGf,EACP,IAAAJ,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAA6B,IAAI;UAACC,IAAI,EAAEtB,SAAS;UAAES,KAAK,EAAEc,MAAM;UAAAlB,QAAA;QAAA,EAE7B,EACP,IAAAJ,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAA2B,IAAI;UAACV,KAAK,EAAEW,SAAS;UAAAf,QAAA;QAAA,EAGf,EACP,IAAAJ,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAA2B,IAAI;UAACV,KAAK,EAAEW,SAAS;UAAAf,QAAA;QAAA,EAAgB,EACtC,IAAAJ,aAAA,CAAAK,GAAA,EAACd,YAAA,CAAA2B,IAAI;UAACV,KAAK,EAAEW,SAAS;UAAAf,QAAA;QAAA,EAAuB;MAAA;IACnC,EACP;EAAA,EACF;AA5BqE,CA6B7E;AAAC;AAAAf,aAAA,GAAAG,CAAA;AA7BW+B,OAAA,CAAA3B,kBAAkB,GAAAA,kBAAA;AA+B/B,IAAMa,IAAI;AAAA;AAAA,CAAApB,aAAA,GAAAG,CAAA,QAAG;EACXgC,eAAe,EAAE,SAAS;EAC1BC,UAAU,EAAE;CACb;AAED,IAAMd,SAAS;AAAA;AAAA,CAAAtB,aAAA,GAAAG,CAAA,QAAG;EAChBgC,eAAe,EAAE,SAAS;EAC1BE,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,aAAa;EACtBC,YAAY,EAAE;CACf;AAED,IAAMX,IAAI;AAAA;AAAA,CAAA5B,aAAA,GAAAG,CAAA,QAAG;EACXkC,MAAM,EAAE;CACT;AAED,IAAMP,SAAS;AAAA;AAAA,CAAA9B,aAAA,GAAAG,CAAA,QAAG;EAChBqC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,MAAM;EAChBC,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE;CACZ;AAED,IAAMV,MAAM;AAAA;AAAA,CAAAjC,aAAA,GAAAG,CAAA,QAAG;EACbgC,eAAe,EAAE,SAAS;EAC1BS,YAAY,EAAE,KAAK;EACnBJ,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,MAAM;EAChBI,UAAU,EAAE,MAAM;EAClBC,cAAc,EAAE,MAAM;EACtBH,SAAS,EAAE,QAAiB;EAC5BI,OAAO,EAAE,OAAO;EAChBtB,KAAK,EAAE,OAAO;EACda,OAAO,EAAE,QAAQ;EACjBD,MAAM,EAAE;CACT", "ignoreList": []}