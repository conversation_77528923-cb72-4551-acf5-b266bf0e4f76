43f9637b460dd917f1d172747f61778a
"use strict";

/* istanbul ignore next */
function cov_1uqmgm6g68() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/route.ts";
  var hash = "061843e24a4ba9f88756dd0a616192aeeb80b860";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 36
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 13
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "85": {
        start: {
          line: 57,
          column: 15
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "86": {
        start: {
          line: 58,
          column: 15
        },
        end: {
          line: 58,
          column: 38
        }
      },
      "87": {
        start: {
          line: 59,
          column: 13
        },
        end: {
          line: 59,
          column: 34
        }
      },
      "88": {
        start: {
          line: 60,
          column: 18
        },
        end: {
          line: 60,
          column: 44
        }
      },
      "89": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 26
        }
      },
      "90": {
        start: {
          line: 62,
          column: 28
        },
        end: {
          line: 62,
          column: 64
        }
      },
      "91": {
        start: {
          line: 63,
          column: 34
        },
        end: {
          line: 63,
          column: 76
        }
      },
      "92": {
        start: {
          line: 65,
          column: 25
        },
        end: {
          line: 95,
          column: 2
        }
      },
      "93": {
        start: {
          line: 96,
          column: 23
        },
        end: {
          line: 103,
          column: 2
        }
      },
      "94": {
        start: {
          line: 104,
          column: 22
        },
        end: {
          line: 112,
          column: 2
        }
      },
      "95": {
        start: {
          line: 113,
          column: 18
        },
        end: {
          line: 117,
          column: 2
        }
      },
      "96": {
        start: {
          line: 118,
          column: 25
        },
        end: {
          line: 140,
          column: 2
        }
      },
      "97": {
        start: {
          line: 142,
          column: 0
        },
        end: {
          line: 220,
          column: 7
        }
      },
      "98": {
        start: {
          line: 142,
          column: 93
        },
        end: {
          line: 220,
          column: 3
        }
      },
      "99": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 219,
          column: 7
        }
      },
      "100": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 218,
          column: 20
        }
      },
      "101": {
        start: {
          line: 145,
          column: 26
        },
        end: {
          line: 218,
          column: 15
        }
      },
      "102": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 217,
          column: 19
        }
      },
      "103": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 216,
          column: 21
        }
      },
      "104": {
        start: {
          line: 151,
          column: 28
        },
        end: {
          line: 151,
          column: 51
        }
      },
      "105": {
        start: {
          line: 152,
          column: 28
        },
        end: {
          line: 152,
          column: 99
        }
      },
      "106": {
        start: {
          line: 154,
          column: 28
        },
        end: {
          line: 154,
          column: 48
        }
      },
      "107": {
        start: {
          line: 155,
          column: 28
        },
        end: {
          line: 163,
          column: 29
        }
      },
      "108": {
        start: {
          line: 156,
          column: 32
        },
        end: {
          line: 159,
          column: 35
        }
      },
      "109": {
        start: {
          line: 160,
          column: 32
        },
        end: {
          line: 160,
          column: 71
        }
      },
      "110": {
        start: {
          line: 161,
          column: 32
        },
        end: {
          line: 161,
          column: 55
        }
      },
      "111": {
        start: {
          line: 162,
          column: 32
        },
        end: {
          line: 162,
          column: 44
        }
      },
      "112": {
        start: {
          line: 164,
          column: 28
        },
        end: {
          line: 168,
          column: 31
        }
      },
      "113": {
        start: {
          line: 169,
          column: 28
        },
        end: {
          line: 169,
          column: 53
        }
      },
      "114": {
        start: {
          line: 170,
          column: 28
        },
        end: {
          line: 173,
          column: 36
        }
      },
      "115": {
        start: {
          line: 175,
          column: 28
        },
        end: {
          line: 175,
          column: 45
        }
      },
      "116": {
        start: {
          line: 176,
          column: 28
        },
        end: {
          line: 180,
          column: 29
        }
      },
      "117": {
        start: {
          line: 177,
          column: 32
        },
        end: {
          line: 177,
          column: 68
        }
      },
      "118": {
        start: {
          line: 178,
          column: 32
        },
        end: {
          line: 178,
          column: 55
        }
      },
      "119": {
        start: {
          line: 179,
          column: 32
        },
        end: {
          line: 179,
          column: 44
        }
      },
      "120": {
        start: {
          line: 181,
          column: 28
        },
        end: {
          line: 197,
          column: 36
        }
      },
      "121": {
        start: {
          line: 199,
          column: 28
        },
        end: {
          line: 199,
          column: 48
        }
      },
      "122": {
        start: {
          line: 200,
          column: 28
        },
        end: {
          line: 200,
          column: 66
        }
      },
      "123": {
        start: {
          line: 201,
          column: 28
        },
        end: {
          line: 203,
          column: 31
        }
      },
      "124": {
        start: {
          line: 204,
          column: 28
        },
        end: {
          line: 204,
          column: 67
        }
      },
      "125": {
        start: {
          line: 205,
          column: 28
        },
        end: {
          line: 208,
          column: 31
        }
      },
      "126": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 212,
          column: 31
        }
      },
      "127": {
        start: {
          line: 214,
          column: 28
        },
        end: {
          line: 214,
          column: 117
        }
      },
      "128": {
        start: {
          line: 215,
          column: 28
        },
        end: {
          line: 215,
          column: 60
        }
      },
      "129": {
        start: {
          line: 222,
          column: 0
        },
        end: {
          line: 319,
          column: 7
        }
      },
      "130": {
        start: {
          line: 222,
          column: 94
        },
        end: {
          line: 319,
          column: 3
        }
      },
      "131": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 318,
          column: 7
        }
      },
      "132": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 317,
          column: 20
        }
      },
      "133": {
        start: {
          line: 224,
          column: 84
        },
        end: {
          line: 317,
          column: 15
        }
      },
      "134": {
        start: {
          line: 225,
          column: 16
        },
        end: {
          line: 316,
          column: 19
        }
      },
      "135": {
        start: {
          line: 226,
          column: 20
        },
        end: {
          line: 315,
          column: 32
        }
      },
      "136": {
        start: {
          line: 227,
          column: 38
        },
        end: {
          line: 315,
          column: 27
        }
      },
      "137": {
        start: {
          line: 230,
          column: 28
        },
        end: {
          line: 314,
          column: 31
        }
      },
      "138": {
        start: {
          line: 231,
          column: 32
        },
        end: {
          line: 313,
          column: 33
        }
      },
      "139": {
        start: {
          line: 233,
          column: 40
        },
        end: {
          line: 233,
          column: 63
        }
      },
      "140": {
        start: {
          line: 234,
          column: 40
        },
        end: {
          line: 234,
          column: 111
        }
      },
      "141": {
        start: {
          line: 236,
          column: 40
        },
        end: {
          line: 236,
          column: 60
        }
      },
      "142": {
        start: {
          line: 237,
          column: 40
        },
        end: {
          line: 241,
          column: 41
        }
      },
      "143": {
        start: {
          line: 238,
          column: 44
        },
        end: {
          line: 238,
          column: 83
        }
      },
      "144": {
        start: {
          line: 239,
          column: 44
        },
        end: {
          line: 239,
          column: 67
        }
      },
      "145": {
        start: {
          line: 240,
          column: 44
        },
        end: {
          line: 240,
          column: 56
        }
      },
      "146": {
        start: {
          line: 242,
          column: 40
        },
        end: {
          line: 245,
          column: 48
        }
      },
      "147": {
        start: {
          line: 247,
          column: 40
        },
        end: {
          line: 247,
          column: 57
        }
      },
      "148": {
        start: {
          line: 248,
          column: 40
        },
        end: {
          line: 252,
          column: 41
        }
      },
      "149": {
        start: {
          line: 249,
          column: 44
        },
        end: {
          line: 249,
          column: 80
        }
      },
      "150": {
        start: {
          line: 250,
          column: 44
        },
        end: {
          line: 250,
          column: 67
        }
      },
      "151": {
        start: {
          line: 251,
          column: 44
        },
        end: {
          line: 251,
          column: 56
        }
      },
      "152": {
        start: {
          line: 253,
          column: 40
        },
        end: {
          line: 253,
          column: 77
        }
      },
      "153": {
        start: {
          line: 255,
          column: 40
        },
        end: {
          line: 255,
          column: 57
        }
      },
      "154": {
        start: {
          line: 256,
          column: 40
        },
        end: {
          line: 256,
          column: 134
        }
      },
      "155": {
        start: {
          line: 257,
          column: 40
        },
        end: {
          line: 257,
          column: 122
        }
      },
      "156": {
        start: {
          line: 258,
          column: 40
        },
        end: {
          line: 258,
          column: 111
        }
      },
      "157": {
        start: {
          line: 260,
          column: 40
        },
        end: {
          line: 260,
          column: 71
        }
      },
      "158": {
        start: {
          line: 261,
          column: 40
        },
        end: {
          line: 266,
          column: 41
        }
      },
      "159": {
        start: {
          line: 262,
          column: 44
        },
        end: {
          line: 262,
          column: 104
        }
      },
      "160": {
        start: {
          line: 263,
          column: 44
        },
        end: {
          line: 263,
          column: 67
        }
      },
      "161": {
        start: {
          line: 264,
          column: 44
        },
        end: {
          line: 264,
          column: 86
        }
      },
      "162": {
        start: {
          line: 265,
          column: 44
        },
        end: {
          line: 265,
          column: 56
        }
      },
      "163": {
        start: {
          line: 267,
          column: 40
        },
        end: {
          line: 267,
          column: 92
        }
      },
      "164": {
        start: {
          line: 269,
          column: 40
        },
        end: {
          line: 269,
          column: 65
        }
      },
      "165": {
        start: {
          line: 270,
          column: 40
        },
        end: {
          line: 275,
          column: 41
        }
      },
      "166": {
        start: {
          line: 271,
          column: 44
        },
        end: {
          line: 271,
          column: 95
        }
      },
      "167": {
        start: {
          line: 272,
          column: 44
        },
        end: {
          line: 272,
          column: 67
        }
      },
      "168": {
        start: {
          line: 273,
          column: 44
        },
        end: {
          line: 273,
          column: 80
        }
      },
      "169": {
        start: {
          line: 274,
          column: 44
        },
        end: {
          line: 274,
          column: 56
        }
      },
      "170": {
        start: {
          line: 276,
          column: 40
        },
        end: {
          line: 276,
          column: 159
        }
      },
      "171": {
        start: {
          line: 277,
          column: 40
        },
        end: {
          line: 277,
          column: 96
        }
      },
      "172": {
        start: {
          line: 278,
          column: 40
        },
        end: {
          line: 282,
          column: 43
        }
      },
      "173": {
        start: {
          line: 283,
          column: 40
        },
        end: {
          line: 283,
          column: 65
        }
      },
      "174": {
        start: {
          line: 284,
          column: 40
        },
        end: {
          line: 297,
          column: 48
        }
      },
      "175": {
        start: {
          line: 299,
          column: 40
        },
        end: {
          line: 299,
          column: 59
        }
      },
      "176": {
        start: {
          line: 300,
          column: 40
        },
        end: {
          line: 300,
          column: 78
        }
      },
      "177": {
        start: {
          line: 301,
          column: 40
        },
        end: {
          line: 303,
          column: 43
        }
      },
      "178": {
        start: {
          line: 304,
          column: 40
        },
        end: {
          line: 304,
          column: 79
        }
      },
      "179": {
        start: {
          line: 305,
          column: 40
        },
        end: {
          line: 308,
          column: 43
        }
      },
      "180": {
        start: {
          line: 309,
          column: 40
        },
        end: {
          line: 312,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 142,
            column: 72
          },
          end: {
            line: 142,
            column: 73
          }
        },
        loc: {
          start: {
            line: 142,
            column: 91
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 142
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 142,
            column: 134
          },
          end: {
            line: 142,
            column: 135
          }
        },
        loc: {
          start: {
            line: 142,
            column: 146
          },
          end: {
            line: 220,
            column: 1
          }
        },
        line: 142
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 143,
            column: 29
          },
          end: {
            line: 143,
            column: 30
          }
        },
        loc: {
          start: {
            line: 143,
            column: 43
          },
          end: {
            line: 219,
            column: 5
          }
        },
        line: 143
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 145,
            column: 13
          }
        },
        loc: {
          start: {
            line: 145,
            column: 24
          },
          end: {
            line: 218,
            column: 17
          }
        },
        line: 145
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 145,
            column: 67
          },
          end: {
            line: 145,
            column: 68
          }
        },
        loc: {
          start: {
            line: 145,
            column: 79
          },
          end: {
            line: 218,
            column: 13
          }
        },
        line: 145
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 148,
            column: 41
          },
          end: {
            line: 148,
            column: 42
          }
        },
        loc: {
          start: {
            line: 148,
            column: 55
          },
          end: {
            line: 217,
            column: 17
          }
        },
        line: 148
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 222,
            column: 73
          },
          end: {
            line: 222,
            column: 74
          }
        },
        loc: {
          start: {
            line: 222,
            column: 92
          },
          end: {
            line: 319,
            column: 5
          }
        },
        line: 222
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 222,
            column: 136
          },
          end: {
            line: 222,
            column: 137
          }
        },
        loc: {
          start: {
            line: 222,
            column: 148
          },
          end: {
            line: 319,
            column: 1
          }
        },
        line: 222
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 223,
            column: 29
          },
          end: {
            line: 223,
            column: 30
          }
        },
        loc: {
          start: {
            line: 223,
            column: 43
          },
          end: {
            line: 318,
            column: 5
          }
        },
        line: 223
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 224,
            column: 70
          },
          end: {
            line: 224,
            column: 71
          }
        },
        loc: {
          start: {
            line: 224,
            column: 82
          },
          end: {
            line: 317,
            column: 17
          }
        },
        line: 224
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 224,
            column: 125
          },
          end: {
            line: 224,
            column: 126
          }
        },
        loc: {
          start: {
            line: 224,
            column: 137
          },
          end: {
            line: 317,
            column: 13
          }
        },
        line: 224
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 225,
            column: 41
          },
          end: {
            line: 225,
            column: 42
          }
        },
        loc: {
          start: {
            line: 225,
            column: 55
          },
          end: {
            line: 316,
            column: 17
          }
        },
        line: 225
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 227,
            column: 24
          },
          end: {
            line: 227,
            column: 25
          }
        },
        loc: {
          start: {
            line: 227,
            column: 36
          },
          end: {
            line: 315,
            column: 29
          }
        },
        line: 227
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 227,
            column: 79
          },
          end: {
            line: 227,
            column: 80
          }
        },
        loc: {
          start: {
            line: 227,
            column: 91
          },
          end: {
            line: 315,
            column: 25
          }
        },
        line: 227
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 230,
            column: 53
          },
          end: {
            line: 230,
            column: 54
          }
        },
        loc: {
          start: {
            line: 230,
            column: 67
          },
          end: {
            line: 314,
            column: 29
          }
        },
        line: 230
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 149,
            column: 20
          },
          end: {
            line: 216,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 150,
            column: 24
          },
          end: {
            line: 152,
            column: 99
          }
        }, {
          start: {
            line: 153,
            column: 24
          },
          end: {
            line: 173,
            column: 36
          }
        }, {
          start: {
            line: 174,
            column: 24
          },
          end: {
            line: 197,
            column: 36
          }
        }, {
          start: {
            line: 198,
            column: 24
          },
          end: {
            line: 215,
            column: 60
          }
        }],
        line: 149
      },
      "39": {
        loc: {
          start: {
            line: 155,
            column: 28
          },
          end: {
            line: 163,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 28
          },
          end: {
            line: 163,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "40": {
        loc: {
          start: {
            line: 155,
            column: 34
          },
          end: {
            line: 155,
            column: 149
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 155,
            column: 132
          },
          end: {
            line: 155,
            column: 138
          }
        }, {
          start: {
            line: 155,
            column: 141
          },
          end: {
            line: 155,
            column: 149
          }
        }],
        line: 155
      },
      "41": {
        loc: {
          start: {
            line: 155,
            column: 34
          },
          end: {
            line: 155,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 155,
            column: 34
          },
          end: {
            line: 155,
            column: 112
          }
        }, {
          start: {
            line: 155,
            column: 116
          },
          end: {
            line: 155,
            column: 129
          }
        }],
        line: 155
      },
      "42": {
        loc: {
          start: {
            line: 155,
            column: 40
          },
          end: {
            line: 155,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 155,
            column: 81
          },
          end: {
            line: 155,
            column: 87
          }
        }, {
          start: {
            line: 155,
            column: 90
          },
          end: {
            line: 155,
            column: 102
          }
        }],
        line: 155
      },
      "43": {
        loc: {
          start: {
            line: 155,
            column: 40
          },
          end: {
            line: 155,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 155,
            column: 40
          },
          end: {
            line: 155,
            column: 56
          }
        }, {
          start: {
            line: 155,
            column: 60
          },
          end: {
            line: 155,
            column: 78
          }
        }],
        line: 155
      },
      "44": {
        loc: {
          start: {
            line: 176,
            column: 28
          },
          end: {
            line: 180,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 28
          },
          end: {
            line: 180,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "45": {
        loc: {
          start: {
            line: 231,
            column: 32
          },
          end: {
            line: 313,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 232,
            column: 36
          },
          end: {
            line: 234,
            column: 111
          }
        }, {
          start: {
            line: 235,
            column: 36
          },
          end: {
            line: 245,
            column: 48
          }
        }, {
          start: {
            line: 246,
            column: 36
          },
          end: {
            line: 253,
            column: 77
          }
        }, {
          start: {
            line: 254,
            column: 36
          },
          end: {
            line: 258,
            column: 111
          }
        }, {
          start: {
            line: 259,
            column: 36
          },
          end: {
            line: 267,
            column: 92
          }
        }, {
          start: {
            line: 268,
            column: 36
          },
          end: {
            line: 297,
            column: 48
          }
        }, {
          start: {
            line: 298,
            column: 36
          },
          end: {
            line: 312,
            column: 48
          }
        }],
        line: 231
      },
      "46": {
        loc: {
          start: {
            line: 237,
            column: 40
          },
          end: {
            line: 241,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 40
          },
          end: {
            line: 241,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "47": {
        loc: {
          start: {
            line: 237,
            column: 46
          },
          end: {
            line: 237,
            column: 161
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 237,
            column: 144
          },
          end: {
            line: 237,
            column: 150
          }
        }, {
          start: {
            line: 237,
            column: 153
          },
          end: {
            line: 237,
            column: 161
          }
        }],
        line: 237
      },
      "48": {
        loc: {
          start: {
            line: 237,
            column: 46
          },
          end: {
            line: 237,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 46
          },
          end: {
            line: 237,
            column: 124
          }
        }, {
          start: {
            line: 237,
            column: 128
          },
          end: {
            line: 237,
            column: 141
          }
        }],
        line: 237
      },
      "49": {
        loc: {
          start: {
            line: 237,
            column: 52
          },
          end: {
            line: 237,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 237,
            column: 93
          },
          end: {
            line: 237,
            column: 99
          }
        }, {
          start: {
            line: 237,
            column: 102
          },
          end: {
            line: 237,
            column: 114
          }
        }],
        line: 237
      },
      "50": {
        loc: {
          start: {
            line: 237,
            column: 52
          },
          end: {
            line: 237,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 52
          },
          end: {
            line: 237,
            column: 68
          }
        }, {
          start: {
            line: 237,
            column: 72
          },
          end: {
            line: 237,
            column: 90
          }
        }],
        line: 237
      },
      "51": {
        loc: {
          start: {
            line: 248,
            column: 40
          },
          end: {
            line: 252,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 248,
            column: 40
          },
          end: {
            line: 252,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 248
      },
      "52": {
        loc: {
          start: {
            line: 261,
            column: 40
          },
          end: {
            line: 266,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 261,
            column: 40
          },
          end: {
            line: 266,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 261
      },
      "53": {
        loc: {
          start: {
            line: 270,
            column: 40
          },
          end: {
            line: 275,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 40
          },
          end: {
            line: 275,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "54": {
        loc: {
          start: {
            line: 290,
            column: 64
          },
          end: {
            line: 290,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 64
          },
          end: {
            line: 290,
            column: 88
          }
        }, {
          start: {
            line: 290,
            column: 92
          },
          end: {
            line: 290,
            column: 94
          }
        }],
        line: 290
      },
      "55": {
        loc: {
          start: {
            line: 291,
            column: 63
          },
          end: {
            line: 291,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 63
          },
          end: {
            line: 291,
            column: 86
          }
        }, {
          start: {
            line: 291,
            column: 90
          },
          end: {
            line: 291,
            column: 92
          }
        }],
        line: 291
      },
      "56": {
        loc: {
          start: {
            line: 292,
            column: 60
          },
          end: {
            line: 292,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 60
          },
          end: {
            line: 292,
            column: 80
          }
        }, {
          start: {
            line: 292,
            column: 84
          },
          end: {
            line: 292,
            column: 86
          }
        }],
        line: 292
      },
      "57": {
        loc: {
          start: {
            line: 293,
            column: 62
          },
          end: {
            line: 293,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 62
          },
          end: {
            line: 293,
            column: 84
          }
        }, {
          start: {
            line: 293,
            column: 88
          },
          end: {
            line: 293,
            column: 90
          }
        }],
        line: 293
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0, 0, 0, 0, 0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAElC,uCAAmC;AAEnC,mCAAgD;AAChD,6CAAgD;AAChD,2BAAwB;AACxB,iEAAgE;AAChE,6EAAwF;AAExF,+CAA+C;AAC/C,IAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;SAClB,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;SAChC,GAAG,CAAC,EAAE,EAAE,4CAA4C,CAAC;SACrD,KAAK,CAAC,iBAAiB,EAAE,uEAAuE,CAAC;IACpG,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;SAC/B,GAAG,CAAC,EAAE,EAAE,2CAA2C,CAAC;SACpD,KAAK,CAAC,iBAAiB,EAAE,sEAAsE,CAAC;IACnG,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,KAAK,CAAC,yBAAyB,CAAC;SAChC,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;IAChC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,GAAG,CAAC,EAAE,EAAE,0BAA0B,CAAC;SACnC,KAAK,CAAC,gCAAgC,EAAE,mCAAmC,CAAC;SAC5E,QAAQ,EAAE;SACV,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,GAAG,EAAE,2CAA2C,CAAC;SACrD,QAAQ,EAAE;IACb,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SAChB,GAAG,CAAC,kCAAkC,CAAC;SACvC,GAAG,CAAC,GAAG,EAAE,yBAAyB,CAAC;SACnC,QAAQ,EAAE;SACV,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,mCAAmC,CAAC;SACxC,GAAG,CAAC,GAAG,EAAE,0BAA0B,CAAC;SACpC,QAAQ,EAAE;SACV,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;CACrB,CAAC,CAAC;AAEH,IAAM,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IACtD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;IACnD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACtD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,YAAY,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;CAC7C,CAAC,CAAC;AAEH,IAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC;IACzD,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC;IAC/C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC9B,CAAC,CAAC;AAEH,IAAM,WAAW,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACjD,KAAK,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC5E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAChC,CAAC,CAAC;AAEH,IAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;SAClC,GAAG,CAAC,GAAG,EAAE,+CAA+C,CAAC;IAC5D,YAAY,EAAE,kBAAkB;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SAChB,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC;SACtD,QAAQ,EAAE;IACb,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;SAClC,GAAG,CAAC,EAAE,EAAE,uCAAuC,CAAC;SAChD,QAAQ,EAAE;IACb,SAAS,EAAE,OAAC,CAAC,KAAK,CAAC,eAAe,CAAC;SAChC,GAAG,CAAC,EAAE,EAAE,sCAAsC,CAAC;SAC/C,QAAQ,EAAE;IACb,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,WAAW,CAAC;SACzB,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC;SACpC,QAAQ,EAAE;IACb,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACtC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC;SACpC,OAAO,CAAC,QAAQ,CAAC;IACpB,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACrC,CAAC,CAAC;AAEH,4BAA4B;AACf,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACrE,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE,8BAA8B;YAC9E;;;;;;4BACQ,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;4BACb,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;gCAC1B,YAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,SAAS,EAAE,KAAK,EAAE;oCACjD,SAAS,EAAE,oBAAoB;oCAC/B,MAAM,EAAE,cAAc;iCACvB,CAAC,CAAC;gCACG,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;gCACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEC,YAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE;gCAChC,SAAS,EAAE,oBAAoB;gCAC/B,MAAM,EAAE,cAAc;gCACtB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;6BAC3B,CAAC,CAAC;4BAEG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;4BAClB,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oCACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;oCACpC,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;iCACrB,CAAC,EAAA;;4BAHI,IAAI,GAAG,SAGX;4BAEF,IAAI,CAAC,IAAI,EAAE,CAAC;gCACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;gCACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEe,qBAAM,gBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;oCAC3C,KAAK,EAAE;wCACL,MAAM,EAAE,IAAI,CAAC,EAAE;wCACf,QAAQ,EAAE,IAAI;qCACf;oCACD,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,KAAK,EAAE,IAAI;wCACX,QAAQ,EAAE,IAAI;wCACd,QAAQ,EAAE,IAAI;wCACd,YAAY,EAAE,IAAI;wCAClB,WAAW,EAAE,IAAI;wCACjB,SAAS,EAAE,IAAI;wCACf,SAAS,EAAE,IAAI;qCAChB;oCACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;iCAC/B,CAAC,EAAA;;4BAhBI,OAAO,GAAG,SAgBd;4BAEI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC;4BAC5C,YAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE;gCAC7C,MAAM,EAAE,IAAI,CAAC,EAAE;6BAChB,CAAC,CAAC;4BAEG,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;4BAC7C,YAAG,CAAC,GAAG,CAAC,KAAK,EAAE,qBAAqB,EAAE,GAAG,EAAE,aAAa,EAAE;gCACxD,SAAS,EAAE,oBAAoB;gCAC/B,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;6BAC3B,CAAC,CAAC;4BAEG,QAAQ,GAAG,qBAAY,CAAC,IAAI,CAAC;gCACjC,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE,OAAO;6BACd,CAAC,CAAC;4BAEH,6CAA6C;4BAC7C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,iDAAiD,CAAC,CAAC;4BAEzF,sBAAO,QAAQ,EAAC;;;iBACnB,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,2BAA2B;AACd,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;QAChF,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,4BAA4B;wBAC3E;;;;;;wCACQ,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wCACb,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;4CACpB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;4CACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gDACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;gDACpC,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;6CACrB,CAAC,EAAA;;wCAHI,IAAI,GAAG,SAGX;wCAEF,IAAI,CAAC,IAAI,EAAE,CAAC;4CACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;4CACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAA3B,IAAI,GAAG,SAAoB;wCAG3B,oBAAoB,GAAG,yCAAmB,CAAC,0BAA0B,EAAE,CAAC;wCACxE,cAAc,GAAG,yCAAmB,CAAC,oBAAoB,EAAE,CAAC;wCAGvC,qBAAM,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAA;;wCAA3E,kBAAkB,GAAG,SAAsD;wCACjF,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;4CAC1B,KAAK,GAAG,IAAI,KAAK,CAAC,wCAAwC,CAAQ,CAAC;4CACzE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,KAAK,CAAC,OAAO,GAAG,kBAAkB,CAAC,MAAM,CAAC;4CAC1C,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGoB,qBAAM,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAA;;wCAAlD,YAAY,GAAG,SAAmC;wCACxD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;4CACpB,KAAK,GAAG,IAAI,KAAK,CAAC,+BAA+B,CAAQ,CAAC;4CAChE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC;4CACpC,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGK,aAAa,yBACd,YAAY,CAAC,aAAa,KAC7B,YAAY,EAAE,kBAAkB,CAAC,aAAa,GAC/C,CAAC;wCAGI,aAAa,GAAG,kBAAkB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;wCAE9D,YAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE;4CAC9B,SAAS,EAAE,oBAAoB;4CAC/B,MAAM,EAAE,eAAe;4CACvB,MAAM,EAAE,IAAI,CAAC,EAAE;yCAChB,CAAC,CAAC;wCAEG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wCAChB,qBAAM,gBAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gDACxC,IAAI,EAAE;oDACJ,MAAM,EAAE,IAAI,CAAC,EAAE;oDACf,KAAK,EAAE,aAAa,CAAC,KAAK;oDAC1B,YAAY,EAAE,aAAa,CAAC,YAAY;oDACxC,OAAO,EAAE,aAAa,CAAC,OAAO;oDAC9B,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,EAAE;oDAC1C,SAAS,EAAE,aAAa,CAAC,SAAS,IAAI,EAAE;oDACxC,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,EAAE;oDAClC,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,EAAE;oDACtC,QAAQ,EAAE,aAAa,CAAC,QAAQ;oDAChC,QAAQ,EAAE,aAAa,CAAC,QAAQ;iDACjC;6CACF,CAAC,EAAA;;wCAbI,MAAM,GAAG,SAab;wCAEI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC;wCAC5C,YAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE;4CAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;yCAChB,CAAC,CAAC;wCAEG,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wCAC7C,YAAG,CAAC,GAAG,CAAC,MAAM,EAAE,qBAAqB,EAAE,GAAG,EAAE,aAAa,EAAE;4CACzD,SAAS,EAAE,oBAAoB;4CAC/B,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;yCAC3B,CAAC,CAAC;wCAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,MAAM;6CACb,CAAC,EAAC;;;6BACJ,CACF,EAAC;;iBACH,CAA4C,EAAC;;KAC/C,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/route.ts"],
      sourcesContent: ["import { NextResponse, NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { ErrorReporter } from '@/lib/errorReporting';\nimport { log } from '@/lib/logger';\nimport { trackError } from '@/lib/errorTracking';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { z } from 'zod';\nimport { ValidationPipelines } from '@/lib/validation-pipeline';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\n// Validation schemas with proper length limits\nconst personalInfoSchema = z.object({\n  firstName: z.string()\n    .min(1, 'First name is required')\n    .max(50, 'First name must be less than 50 characters')\n    .regex(/^[a-zA-Z\\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),\n  lastName: z.string()\n    .min(1, 'Last name is required')\n    .max(50, 'Last name must be less than 50 characters')\n    .regex(/^[a-zA-Z\\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),\n  email: z.string()\n    .email('Valid email is required')\n    .max(254, 'Email is too long'),\n  phone: z.string()\n    .max(20, 'Phone number is too long')\n    .regex(/^[\\+]?[1-9][\\d\\s\\-\\(\\)]{0,15}$/, 'Please enter a valid phone number')\n    .optional()\n    .or(z.literal('')),\n  location: z.string()\n    .max(100, 'Location must be less than 100 characters')\n    .optional(),\n  website: z.string()\n    .url('Please enter a valid website URL')\n    .max(500, 'Website URL is too long')\n    .optional()\n    .or(z.literal('')),\n  linkedIn: z.string()\n    .url('Please enter a valid LinkedIn URL')\n    .max(500, 'LinkedIn URL is too long')\n    .optional()\n    .or(z.literal(''))\n});\n\nconst experienceSchema = z.object({\n  company: z.string().min(1, 'Company name is required'),\n  position: z.string().min(1, 'Position is required'),\n  startDate: z.string().min(1, 'Start date is required'),\n  endDate: z.string().optional(),\n  description: z.string().optional(),\n  achievements: z.array(z.string()).optional()\n});\n\nconst educationSchema = z.object({\n  institution: z.string().min(1, 'Institution is required'),\n  degree: z.string().min(1, 'Degree is required'),\n  field: z.string().optional(),\n  startDate: z.string().optional(),\n  endDate: z.string().optional(),\n  gpa: z.string().optional(),\n  honors: z.string().optional()\n});\n\nconst skillSchema = z.object({\n  name: z.string().min(1, 'Skill name is required'),\n  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),\n  category: z.string().optional()\n});\n\nconst resumeCreateSchema = z.object({\n  title: z.string()\n    .min(1, 'Resume title is required')\n    .max(200, 'Resume title must be less than 200 characters'),\n  personalInfo: personalInfoSchema,\n  summary: z.string()\n    .max(2000, 'Summary must be less than 2000 characters')\n    .optional(),\n  experience: z.array(experienceSchema)\n    .max(20, 'Maximum 20 experience entries allowed')\n    .optional(),\n  education: z.array(educationSchema)\n    .max(10, 'Maximum 10 education entries allowed')\n    .optional(),\n  skills: z.array(skillSchema)\n    .max(50, 'Maximum 50 skills allowed')\n    .optional(),\n  sections: z.record(z.any()).optional(),\n  template: z.string()\n    .max(50, 'Template name is too long')\n    .default('modern'),\n  isPublic: z.boolean().default(false)\n});\n\n// GET - List user's resumes\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes\n    async () => {\n      const startTime = Date.now();\n      const session = await getServerSession(authOptions);\n\n      if (!session?.user?.email) {\n        log.auth('resume_access_denied', undefined, false, {\n          component: 'resume_builder_api',\n          action: 'list_resumes'\n        });\n        const error = new Error('Not authenticated') as any;\n        error.statusCode = 401;\n        throw error;\n      }\n\n        log.info('Fetching user resumes', {\n          component: 'resume_builder_api',\n          action: 'list_resumes',\n          userId: session.user.email\n        });\n\n        const dbStartTime = Date.now();\n        const user = await prisma.user.findUnique({\n          where: { email: session.user.email },\n          select: { id: true }\n        });\n\n        if (!user) {\n          const error = new Error('User not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        const resumes = await prisma.resume.findMany({\n          where: { \n            userId: user.id,\n            isActive: true\n          },\n          select: {\n            id: true,\n            title: true,\n            template: true,\n            isPublic: true,\n            lastExported: true,\n            exportCount: true,\n            createdAt: true,\n            updatedAt: true\n          },\n          orderBy: { updatedAt: 'desc' }\n        });\n\n        const dbDuration = Date.now() - dbStartTime;\n        log.database('findMany', 'resume', dbDuration, {\n          userId: user.id\n        });\n\n        const totalDuration = Date.now() - startTime;\n        log.api('GET', '/api/resume-builder', 200, totalDuration, {\n          component: 'resume_builder_api',\n          userId: session.user.email\n        });\n\n        const response = NextResponse.json({\n          success: true,\n          data: resumes\n        });\n\n        // Add caching headers for better performance\n        response.headers.set('Cache-Control', 'private, max-age=30, stale-while-revalidate=120');\n\n        return response;\n    }\n  );\n});\n\n// POST - Create new resume\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 creates per 15 minutes\n      async () => {\n        const startTime = Date.now();\n        const session = await getServerSession(authOptions);\n\n        if (!session?.user?.email) {\n          const error = new Error('Not authenticated') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: { email: session.user.email },\n          select: { id: true }\n        });\n\n        if (!user) {\n          const error = new Error('User not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        const body = await request.json();\n\n        // Step 1: Use validation pipeline for comprehensive validation and sanitization\n        const personalInfoPipeline = ValidationPipelines.createPersonalInfoPipeline();\n        const resumePipeline = ValidationPipelines.createResumePipeline();\n\n        // Validate personal info\n        const personalInfoResult = await personalInfoPipeline.validate(body.personalInfo);\n        if (!personalInfoResult.isValid) {\n          const error = new Error('Personal information validation failed') as any;\n          error.statusCode = 400;\n          error.details = personalInfoResult.errors;\n          throw error;\n        }\n\n        // Validate resume data\n        const resumeResult = await resumePipeline.validate(body);\n        if (!resumeResult.isValid) {\n          const error = new Error('Resume data validation failed') as any;\n          error.statusCode = 400;\n          error.details = resumeResult.errors;\n          throw error;\n        }\n\n        // Step 2: Use sanitized data from validation pipeline\n        const sanitizedBody = {\n          ...resumeResult.sanitizedData,\n          personalInfo: personalInfoResult.sanitizedData\n        };\n\n        // Step 3: Additional Zod validation for complex structures\n        const validatedData = resumeCreateSchema.parse(sanitizedBody);\n\n        log.info('Creating new resume', {\n          component: 'resume_builder_api',\n          action: 'create_resume',\n          userId: user.id\n        });\n\n        const dbStartTime = Date.now();\n        const resume = await prisma.resume.create({\n          data: {\n            userId: user.id,\n            title: validatedData.title,\n            personalInfo: validatedData.personalInfo,\n            summary: validatedData.summary,\n            experience: validatedData.experience || [],\n            education: validatedData.education || [],\n            skills: validatedData.skills || [],\n            sections: validatedData.sections || {},\n            template: validatedData.template,\n            isPublic: validatedData.isPublic\n          }\n        });\n\n        const dbDuration = Date.now() - dbStartTime;\n        log.database('create', 'resume', dbDuration, {\n          userId: user.id\n        });\n\n        const totalDuration = Date.now() - startTime;\n        log.api('POST', '/api/resume-builder', 201, totalDuration, {\n          component: 'resume_builder_api',\n          userId: session.user.email\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: resume\n        });\n      }\n    );\n  }) as Promise<NextResponse<ApiResponse<any>>>;\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "061843e24a4ba9f88756dd0a616192aeeb80b860"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1uqmgm6g68 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1uqmgm6g68();
var __assign =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[0]++,
/* istanbul ignore next */
(cov_1uqmgm6g68().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1uqmgm6g68().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1uqmgm6g68().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1uqmgm6g68().f[0]++;
  cov_1uqmgm6g68().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[1]++;
    cov_1uqmgm6g68().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1uqmgm6g68().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1uqmgm6g68().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1uqmgm6g68().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1uqmgm6g68().b[2][0]++;
          cov_1uqmgm6g68().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1uqmgm6g68().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1uqmgm6g68().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1uqmgm6g68().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[11]++,
/* istanbul ignore next */
(cov_1uqmgm6g68().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1uqmgm6g68().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1uqmgm6g68().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1uqmgm6g68().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[3]++;
    cov_1uqmgm6g68().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().f[4]++;
      cov_1uqmgm6g68().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1uqmgm6g68().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().f[6]++;
      cov_1uqmgm6g68().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().f[7]++;
      cov_1uqmgm6g68().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().f[8]++;
      cov_1uqmgm6g68().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1uqmgm6g68().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1uqmgm6g68().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1uqmgm6g68().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[23]++,
/* istanbul ignore next */
(cov_1uqmgm6g68().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1uqmgm6g68().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1uqmgm6g68().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1uqmgm6g68().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1uqmgm6g68().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1uqmgm6g68().f[10]++;
        cov_1uqmgm6g68().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1uqmgm6g68().b[9][0]++;
          cov_1uqmgm6g68().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1uqmgm6g68().b[9][1]++;
        }
        cov_1uqmgm6g68().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1uqmgm6g68().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1uqmgm6g68().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[11]++;
    cov_1uqmgm6g68().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[12]++;
    cov_1uqmgm6g68().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().f[13]++;
      cov_1uqmgm6g68().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[14]++;
    cov_1uqmgm6g68().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().b[12][0]++;
      cov_1uqmgm6g68().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1uqmgm6g68().b[12][1]++;
    }
    cov_1uqmgm6g68().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1uqmgm6g68().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[18][0]++,
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[19][1]++,
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1uqmgm6g68().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1uqmgm6g68().b[15][0]++;
          cov_1uqmgm6g68().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1uqmgm6g68().b[15][1]++;
        }
        cov_1uqmgm6g68().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1uqmgm6g68().b[21][0]++;
          cov_1uqmgm6g68().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1uqmgm6g68().b[21][1]++;
        }
        cov_1uqmgm6g68().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1uqmgm6g68().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1uqmgm6g68().b[22][1]++;
            cov_1uqmgm6g68().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1uqmgm6g68().b[22][2]++;
            cov_1uqmgm6g68().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1uqmgm6g68().b[22][3]++;
            cov_1uqmgm6g68().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1uqmgm6g68().b[22][4]++;
            cov_1uqmgm6g68().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1uqmgm6g68().b[22][5]++;
            cov_1uqmgm6g68().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1uqmgm6g68().b[23][0]++;
              cov_1uqmgm6g68().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1uqmgm6g68().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1uqmgm6g68().b[23][1]++;
            }
            cov_1uqmgm6g68().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1uqmgm6g68().b[26][0]++;
              cov_1uqmgm6g68().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1uqmgm6g68().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1uqmgm6g68().b[26][1]++;
            }
            cov_1uqmgm6g68().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1uqmgm6g68().b[28][0]++;
              cov_1uqmgm6g68().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1uqmgm6g68().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1uqmgm6g68().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1uqmgm6g68().b[28][1]++;
            }
            cov_1uqmgm6g68().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1uqmgm6g68().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1uqmgm6g68().b[30][0]++;
              cov_1uqmgm6g68().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1uqmgm6g68().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1uqmgm6g68().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1uqmgm6g68().b[30][1]++;
            }
            cov_1uqmgm6g68().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1uqmgm6g68().b[32][0]++;
              cov_1uqmgm6g68().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1uqmgm6g68().b[32][1]++;
            }
            cov_1uqmgm6g68().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1uqmgm6g68().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1uqmgm6g68().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1uqmgm6g68().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().b[33][0]++;
      cov_1uqmgm6g68().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1uqmgm6g68().b[33][1]++;
    }
    cov_1uqmgm6g68().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1uqmgm6g68().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1uqmgm6g68().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[78]++,
/* istanbul ignore next */
(cov_1uqmgm6g68().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_1uqmgm6g68().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1uqmgm6g68().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1uqmgm6g68().f[15]++;
  cov_1uqmgm6g68().s[79]++;
  return /* istanbul ignore next */(cov_1uqmgm6g68().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_1uqmgm6g68().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1uqmgm6g68().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1uqmgm6g68().s[81]++;
exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[82]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[83]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[84]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[85]++, __importDefault(require("@/lib/prisma")));
var logger_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[86]++, require("@/lib/logger"));
var csrf_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[87]++, require("@/lib/csrf"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[88]++, require("@/lib/rateLimit"));
var zod_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[89]++, require("zod"));
var validation_pipeline_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[90]++, require("@/lib/validation-pipeline"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[91]++, require("@/lib/unified-api-error-handler"));
// Validation schemas with proper length limits
var personalInfoSchema =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[92]++, zod_1.z.object({
  firstName: zod_1.z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters').regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  lastName: zod_1.z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters').regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  email: zod_1.z.string().email('Valid email is required').max(254, 'Email is too long'),
  phone: zod_1.z.string().max(20, 'Phone number is too long').regex(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/, 'Please enter a valid phone number').optional().or(zod_1.z.literal('')),
  location: zod_1.z.string().max(100, 'Location must be less than 100 characters').optional(),
  website: zod_1.z.string().url('Please enter a valid website URL').max(500, 'Website URL is too long').optional().or(zod_1.z.literal('')),
  linkedIn: zod_1.z.string().url('Please enter a valid LinkedIn URL').max(500, 'LinkedIn URL is too long').optional().or(zod_1.z.literal(''))
}));
var experienceSchema =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[93]++, zod_1.z.object({
  company: zod_1.z.string().min(1, 'Company name is required'),
  position: zod_1.z.string().min(1, 'Position is required'),
  startDate: zod_1.z.string().min(1, 'Start date is required'),
  endDate: zod_1.z.string().optional(),
  description: zod_1.z.string().optional(),
  achievements: zod_1.z.array(zod_1.z.string()).optional()
}));
var educationSchema =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[94]++, zod_1.z.object({
  institution: zod_1.z.string().min(1, 'Institution is required'),
  degree: zod_1.z.string().min(1, 'Degree is required'),
  field: zod_1.z.string().optional(),
  startDate: zod_1.z.string().optional(),
  endDate: zod_1.z.string().optional(),
  gpa: zod_1.z.string().optional(),
  honors: zod_1.z.string().optional()
}));
var skillSchema =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[95]++, zod_1.z.object({
  name: zod_1.z.string().min(1, 'Skill name is required'),
  level: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  category: zod_1.z.string().optional()
}));
var resumeCreateSchema =
/* istanbul ignore next */
(cov_1uqmgm6g68().s[96]++, zod_1.z.object({
  title: zod_1.z.string().min(1, 'Resume title is required').max(200, 'Resume title must be less than 200 characters'),
  personalInfo: personalInfoSchema,
  summary: zod_1.z.string().max(2000, 'Summary must be less than 2000 characters').optional(),
  experience: zod_1.z.array(experienceSchema).max(20, 'Maximum 20 experience entries allowed').optional(),
  education: zod_1.z.array(educationSchema).max(10, 'Maximum 10 education entries allowed').optional(),
  skills: zod_1.z.array(skillSchema).max(50, 'Maximum 50 skills allowed').optional(),
  sections: zod_1.z.record(zod_1.z.any()).optional(),
  template: zod_1.z.string().max(50, 'Template name is too long').default('modern'),
  isPublic: zod_1.z.boolean().default(false)
}));
// GET - List user's resumes
/* istanbul ignore next */
cov_1uqmgm6g68().s[97]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1uqmgm6g68().f[16]++;
  cov_1uqmgm6g68().s[98]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[17]++;
    cov_1uqmgm6g68().s[99]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().f[18]++;
      cov_1uqmgm6g68().s[100]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 100
      },
      // 100 requests per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_1uqmgm6g68().f[19]++;
        cov_1uqmgm6g68().s[101]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1uqmgm6g68().f[20]++;
          var startTime, session, error, dbStartTime, user, error, resumes, dbDuration, totalDuration, response;
          var _a;
          /* istanbul ignore next */
          cov_1uqmgm6g68().s[102]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_1uqmgm6g68().f[21]++;
            cov_1uqmgm6g68().s[103]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_1uqmgm6g68().b[38][0]++;
                cov_1uqmgm6g68().s[104]++;
                startTime = Date.now();
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[105]++;
                return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1uqmgm6g68().b[38][1]++;
                cov_1uqmgm6g68().s[106]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[107]++;
                if (!(
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[41][0]++, (_a =
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[43][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[43][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[42][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[42][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[41][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[40][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1uqmgm6g68().b[40][1]++, _a.email))) {
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().b[39][0]++;
                  cov_1uqmgm6g68().s[108]++;
                  logger_1.log.auth('resume_access_denied', undefined, false, {
                    component: 'resume_builder_api',
                    action: 'list_resumes'
                  });
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().s[109]++;
                  error = new Error('Not authenticated');
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().s[110]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().s[111]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_1uqmgm6g68().b[39][1]++;
                }
                cov_1uqmgm6g68().s[112]++;
                logger_1.log.info('Fetching user resumes', {
                  component: 'resume_builder_api',
                  action: 'list_resumes',
                  userId: session.user.email
                });
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[113]++;
                dbStartTime = Date.now();
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[114]++;
                return [4 /*yield*/, prisma_1.default.user.findUnique({
                  where: {
                    email: session.user.email
                  },
                  select: {
                    id: true
                  }
                })];
              case 2:
                /* istanbul ignore next */
                cov_1uqmgm6g68().b[38][2]++;
                cov_1uqmgm6g68().s[115]++;
                user = _b.sent();
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[116]++;
                if (!user) {
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().b[44][0]++;
                  cov_1uqmgm6g68().s[117]++;
                  error = new Error('User not found');
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().s[118]++;
                  error.statusCode = 404;
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().s[119]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_1uqmgm6g68().b[44][1]++;
                }
                cov_1uqmgm6g68().s[120]++;
                return [4 /*yield*/, prisma_1.default.resume.findMany({
                  where: {
                    userId: user.id,
                    isActive: true
                  },
                  select: {
                    id: true,
                    title: true,
                    template: true,
                    isPublic: true,
                    lastExported: true,
                    exportCount: true,
                    createdAt: true,
                    updatedAt: true
                  },
                  orderBy: {
                    updatedAt: 'desc'
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_1uqmgm6g68().b[38][3]++;
                cov_1uqmgm6g68().s[121]++;
                resumes = _b.sent();
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[122]++;
                dbDuration = Date.now() - dbStartTime;
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[123]++;
                logger_1.log.database('findMany', 'resume', dbDuration, {
                  userId: user.id
                });
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[124]++;
                totalDuration = Date.now() - startTime;
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[125]++;
                logger_1.log.api('GET', '/api/resume-builder', 200, totalDuration, {
                  component: 'resume_builder_api',
                  userId: session.user.email
                });
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[126]++;
                response = server_1.NextResponse.json({
                  success: true,
                  data: resumes
                });
                // Add caching headers for better performance
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[127]++;
                response.headers.set('Cache-Control', 'private, max-age=30, stale-while-revalidate=120');
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[128]++;
                return [2 /*return*/, response];
            }
          });
        });
      })];
    });
  });
});
// POST - Create new resume
/* istanbul ignore next */
cov_1uqmgm6g68().s[129]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1uqmgm6g68().f[22]++;
  cov_1uqmgm6g68().s[130]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1uqmgm6g68().f[23]++;
    cov_1uqmgm6g68().s[131]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1uqmgm6g68().f[24]++;
      cov_1uqmgm6g68().s[132]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_1uqmgm6g68().f[25]++;
        cov_1uqmgm6g68().s[133]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1uqmgm6g68().f[26]++;
          cov_1uqmgm6g68().s[134]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_1uqmgm6g68().f[27]++;
            cov_1uqmgm6g68().s[135]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 20
            },
            // 20 creates per 15 minutes
            function () {
              /* istanbul ignore next */
              cov_1uqmgm6g68().f[28]++;
              cov_1uqmgm6g68().s[136]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_1uqmgm6g68().f[29]++;
                var startTime, session, error, user, error, body, personalInfoPipeline, resumePipeline, personalInfoResult, error, resumeResult, error, sanitizedBody, validatedData, dbStartTime, resume, dbDuration, totalDuration;
                var _a;
                /* istanbul ignore next */
                cov_1uqmgm6g68().s[137]++;
                return __generator(this, function (_b) {
                  /* istanbul ignore next */
                  cov_1uqmgm6g68().f[30]++;
                  cov_1uqmgm6g68().s[138]++;
                  switch (_b.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().b[45][0]++;
                      cov_1uqmgm6g68().s[139]++;
                      startTime = Date.now();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[140]++;
                      return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
                    case 1:
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().b[45][1]++;
                      cov_1uqmgm6g68().s[141]++;
                      session = _b.sent();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[142]++;
                      if (!(
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[48][0]++, (_a =
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[50][0]++, session === null) ||
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[50][1]++, session === void 0) ?
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[49][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[49][1]++, session.user)) === null) ||
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[48][1]++, _a === void 0) ?
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[47][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_1uqmgm6g68().b[47][1]++, _a.email))) {
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().b[46][0]++;
                        cov_1uqmgm6g68().s[143]++;
                        error = new Error('Not authenticated');
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[144]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[145]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_1uqmgm6g68().b[46][1]++;
                      }
                      cov_1uqmgm6g68().s[146]++;
                      return [4 /*yield*/, prisma_1.default.user.findUnique({
                        where: {
                          email: session.user.email
                        },
                        select: {
                          id: true
                        }
                      })];
                    case 2:
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().b[45][2]++;
                      cov_1uqmgm6g68().s[147]++;
                      user = _b.sent();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[148]++;
                      if (!user) {
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().b[51][0]++;
                        cov_1uqmgm6g68().s[149]++;
                        error = new Error('User not found');
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[150]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[151]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_1uqmgm6g68().b[51][1]++;
                      }
                      cov_1uqmgm6g68().s[152]++;
                      return [4 /*yield*/, request.json()];
                    case 3:
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().b[45][3]++;
                      cov_1uqmgm6g68().s[153]++;
                      body = _b.sent();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[154]++;
                      personalInfoPipeline = validation_pipeline_1.ValidationPipelines.createPersonalInfoPipeline();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[155]++;
                      resumePipeline = validation_pipeline_1.ValidationPipelines.createResumePipeline();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[156]++;
                      return [4 /*yield*/, personalInfoPipeline.validate(body.personalInfo)];
                    case 4:
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().b[45][4]++;
                      cov_1uqmgm6g68().s[157]++;
                      personalInfoResult = _b.sent();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[158]++;
                      if (!personalInfoResult.isValid) {
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().b[52][0]++;
                        cov_1uqmgm6g68().s[159]++;
                        error = new Error('Personal information validation failed');
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[160]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[161]++;
                        error.details = personalInfoResult.errors;
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[162]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_1uqmgm6g68().b[52][1]++;
                      }
                      cov_1uqmgm6g68().s[163]++;
                      return [4 /*yield*/, resumePipeline.validate(body)];
                    case 5:
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().b[45][5]++;
                      cov_1uqmgm6g68().s[164]++;
                      resumeResult = _b.sent();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[165]++;
                      if (!resumeResult.isValid) {
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().b[53][0]++;
                        cov_1uqmgm6g68().s[166]++;
                        error = new Error('Resume data validation failed');
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[167]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[168]++;
                        error.details = resumeResult.errors;
                        /* istanbul ignore next */
                        cov_1uqmgm6g68().s[169]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_1uqmgm6g68().b[53][1]++;
                      }
                      cov_1uqmgm6g68().s[170]++;
                      sanitizedBody = __assign(__assign({}, resumeResult.sanitizedData), {
                        personalInfo: personalInfoResult.sanitizedData
                      });
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[171]++;
                      validatedData = resumeCreateSchema.parse(sanitizedBody);
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[172]++;
                      logger_1.log.info('Creating new resume', {
                        component: 'resume_builder_api',
                        action: 'create_resume',
                        userId: user.id
                      });
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[173]++;
                      dbStartTime = Date.now();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[174]++;
                      return [4 /*yield*/, prisma_1.default.resume.create({
                        data: {
                          userId: user.id,
                          title: validatedData.title,
                          personalInfo: validatedData.personalInfo,
                          summary: validatedData.summary,
                          experience:
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[54][0]++, validatedData.experience) ||
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[54][1]++, []),
                          education:
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[55][0]++, validatedData.education) ||
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[55][1]++, []),
                          skills:
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[56][0]++, validatedData.skills) ||
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[56][1]++, []),
                          sections:
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[57][0]++, validatedData.sections) ||
                          /* istanbul ignore next */
                          (cov_1uqmgm6g68().b[57][1]++, {}),
                          template: validatedData.template,
                          isPublic: validatedData.isPublic
                        }
                      })];
                    case 6:
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().b[45][6]++;
                      cov_1uqmgm6g68().s[175]++;
                      resume = _b.sent();
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[176]++;
                      dbDuration = Date.now() - dbStartTime;
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[177]++;
                      logger_1.log.database('create', 'resume', dbDuration, {
                        userId: user.id
                      });
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[178]++;
                      totalDuration = Date.now() - startTime;
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[179]++;
                      logger_1.log.api('POST', '/api/resume-builder', 201, totalDuration, {
                        component: 'resume_builder_api',
                        userId: session.user.email
                      });
                      /* istanbul ignore next */
                      cov_1uqmgm6g68().s[180]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: resume
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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