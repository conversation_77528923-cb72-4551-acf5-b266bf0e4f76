91b79c51fa616c0403ecc4a399079a78
"use strict";

/**
 * Performance monitoring and caching strategies for Skill Gap Analyzer
 * Implements comprehensive performance tracking and optimization
 */
/* istanbul ignore next */
function cov_1pnbn5pbao() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/skill-gap-performance.ts";
  var hash = "2fdfb9596ac03c56a4ae05be56fd59f7be154b89";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/skill-gap-performance.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "2": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "4": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "5": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "6": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "8": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "9": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "11": {
        start: {
          line: 17,
          column: 16
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "12": {
        start: {
          line: 18,
          column: 28
        },
        end: {
          line: 18,
          column: 110
        }
      },
      "13": {
        start: {
          line: 18,
          column: 91
        },
        end: {
          line: 18,
          column: 106
        }
      },
      "14": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 24,
          column: 7
        }
      },
      "15": {
        start: {
          line: 20,
          column: 36
        },
        end: {
          line: 20,
          column: 97
        }
      },
      "16": {
        start: {
          line: 20,
          column: 42
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "17": {
        start: {
          line: 20,
          column: 85
        },
        end: {
          line: 20,
          column: 95
        }
      },
      "18": {
        start: {
          line: 21,
          column: 35
        },
        end: {
          line: 21,
          column: 100
        }
      },
      "19": {
        start: {
          line: 21,
          column: 41
        },
        end: {
          line: 21,
          column: 73
        }
      },
      "20": {
        start: {
          line: 21,
          column: 88
        },
        end: {
          line: 21,
          column: 98
        }
      },
      "21": {
        start: {
          line: 22,
          column: 32
        },
        end: {
          line: 22,
          column: 116
        }
      },
      "22": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 78
        }
      },
      "23": {
        start: {
          line: 26,
          column: 18
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "24": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "25": {
        start: {
          line: 27,
          column: 43
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "26": {
        start: {
          line: 27,
          column: 57
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "27": {
        start: {
          line: 27,
          column: 69
        },
        end: {
          line: 27,
          column: 81
        }
      },
      "28": {
        start: {
          line: 27,
          column: 119
        },
        end: {
          line: 27,
          column: 196
        }
      },
      "29": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 160
        }
      },
      "30": {
        start: {
          line: 28,
          column: 141
        },
        end: {
          line: 28,
          column: 153
        }
      },
      "31": {
        start: {
          line: 29,
          column: 23
        },
        end: {
          line: 29,
          column: 68
        }
      },
      "32": {
        start: {
          line: 29,
          column: 45
        },
        end: {
          line: 29,
          column: 65
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "34": {
        start: {
          line: 31,
          column: 15
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "35": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "36": {
        start: {
          line: 32,
          column: 50
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "37": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "38": {
        start: {
          line: 33,
          column: 160
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "39": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "40": {
        start: {
          line: 34,
          column: 26
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "41": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "42": {
        start: {
          line: 36,
          column: 32
        },
        end: {
          line: 36,
          column: 39
        }
      },
      "43": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 46
        }
      },
      "44": {
        start: {
          line: 37,
          column: 24
        },
        end: {
          line: 37,
          column: 34
        }
      },
      "45": {
        start: {
          line: 37,
          column: 35
        },
        end: {
          line: 37,
          column: 72
        }
      },
      "46": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "47": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 45
        }
      },
      "48": {
        start: {
          line: 38,
          column: 46
        },
        end: {
          line: 38,
          column: 55
        }
      },
      "49": {
        start: {
          line: 38,
          column: 56
        },
        end: {
          line: 38,
          column: 65
        }
      },
      "50": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 41
        }
      },
      "51": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "52": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "53": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 128
        }
      },
      "54": {
        start: {
          line: 41,
          column: 110
        },
        end: {
          line: 41,
          column: 116
        }
      },
      "55": {
        start: {
          line: 41,
          column: 117
        },
        end: {
          line: 41,
          column: 126
        }
      },
      "56": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 106
        }
      },
      "57": {
        start: {
          line: 42,
          column: 81
        },
        end: {
          line: 42,
          column: 97
        }
      },
      "58": {
        start: {
          line: 42,
          column: 98
        },
        end: {
          line: 42,
          column: 104
        }
      },
      "59": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 89
        }
      },
      "60": {
        start: {
          line: 43,
          column: 57
        },
        end: {
          line: 43,
          column: 72
        }
      },
      "61": {
        start: {
          line: 43,
          column: 73
        },
        end: {
          line: 43,
          column: 80
        }
      },
      "62": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 87
        }
      },
      "63": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "64": {
        start: {
          line: 44,
          column: 47
        },
        end: {
          line: 44,
          column: 62
        }
      },
      "65": {
        start: {
          line: 44,
          column: 63
        },
        end: {
          line: 44,
          column: 78
        }
      },
      "66": {
        start: {
          line: 44,
          column: 79
        },
        end: {
          line: 44,
          column: 85
        }
      },
      "67": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "68": {
        start: {
          line: 45,
          column: 30
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "69": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 33
        }
      },
      "70": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 43
        }
      },
      "71": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 48,
          column: 39
        }
      },
      "72": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "73": {
        start: {
          line: 49,
          column: 35
        },
        end: {
          line: 49,
          column: 41
        }
      },
      "74": {
        start: {
          line: 49,
          column: 54
        },
        end: {
          line: 49,
          column: 64
        }
      },
      "75": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "76": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "77": {
        start: {
          line: 50,
          column: 36
        },
        end: {
          line: 50,
          column: 89
        }
      },
      "78": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 62
        }
      },
      "79": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 81
        }
      },
      "80": {
        start: {
          line: 55,
          column: 31
        },
        end: {
          line: 55,
          column: 70
        }
      },
      "81": {
        start: {
          line: 56,
          column: 27
        },
        end: {
          line: 56,
          column: 62
        }
      },
      "82": {
        start: {
          line: 57,
          column: 35
        },
        end: {
          line: 57,
          column: 87
        }
      },
      "83": {
        start: {
          line: 58,
          column: 48
        },
        end: {
          line: 373,
          column: 3
        }
      },
      "84": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 67,
          column: 10
        }
      },
      "85": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 76,
          column: 10
        }
      },
      "86": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 85,
          column: 9
        }
      },
      "87": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 79,
          column: 88
        }
      },
      "88": {
        start: {
          line: 80,
          column: 12
        },
        end: {
          line: 80,
          column: 88
        }
      },
      "89": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 43
        }
      },
      "90": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 84,
          column: 41
        }
      },
      "91": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 86,
          column: 75
        }
      },
      "92": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 143,
          column: 6
        }
      },
      "93": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 142,
          column: 11
        }
      },
      "94": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 141,
          column: 15
        }
      },
      "95": {
        start: {
          line: 95,
          column: 16
        },
        end: {
          line: 140,
          column: 17
        }
      },
      "96": {
        start: {
          line: 97,
          column: 24
        },
        end: {
          line: 97,
          column: 47
        }
      },
      "97": {
        start: {
          line: 98,
          column: 24
        },
        end: {
          line: 98,
          column: 79
        }
      },
      "98": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 99,
          column: 37
        }
      },
      "99": {
        start: {
          line: 101,
          column: 24
        },
        end: {
          line: 101,
          column: 50
        }
      },
      "100": {
        start: {
          line: 102,
          column: 24
        },
        end: {
          line: 102,
          column: 78
        }
      },
      "101": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 104,
          column: 49
        }
      },
      "102": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 115,
          column: 25
        }
      },
      "103": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 106,
          column: 68
        }
      },
      "104": {
        start: {
          line: 107,
          column: 28
        },
        end: {
          line: 113,
          column: 31
        }
      },
      "105": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 114,
          column: 64
        }
      },
      "106": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 58
        }
      },
      "107": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 43
        }
      },
      "108": {
        start: {
          line: 119,
          column: 24
        },
        end: {
          line: 119,
          column: 62
        }
      },
      "109": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 121,
          column: 148
        }
      },
      "110": {
        start: {
          line: 124,
          column: 24
        },
        end: {
          line: 124,
          column: 34
        }
      },
      "111": {
        start: {
          line: 126,
          column: 24
        },
        end: {
          line: 132,
          column: 27
        }
      },
      "112": {
        start: {
          line: 133,
          column: 24
        },
        end: {
          line: 133,
          column: 54
        }
      },
      "113": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 135,
          column: 44
        }
      },
      "114": {
        start: {
          line: 136,
          column: 24
        },
        end: {
          line: 136,
          column: 62
        }
      },
      "115": {
        start: {
          line: 137,
          column: 24
        },
        end: {
          line: 137,
          column: 90
        }
      },
      "116": {
        start: {
          line: 138,
          column: 24
        },
        end: {
          line: 138,
          column: 38
        }
      },
      "117": {
        start: {
          line: 139,
          column: 28
        },
        end: {
          line: 139,
          column: 50
        }
      },
      "118": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 187,
          column: 6
        }
      },
      "119": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 186,
          column: 11
        }
      },
      "120": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 185,
          column: 15
        }
      },
      "121": {
        start: {
          line: 151,
          column: 16
        },
        end: {
          line: 184,
          column: 17
        }
      },
      "122": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 153,
          column: 47
        }
      },
      "123": {
        start: {
          line: 154,
          column: 24
        },
        end: {
          line: 154,
          column: 37
        }
      },
      "124": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 50
        }
      },
      "125": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 157,
          column: 58
        }
      },
      "126": {
        start: {
          line: 159,
          column: 24
        },
        end: {
          line: 159,
          column: 43
        }
      },
      "127": {
        start: {
          line: 160,
          column: 24
        },
        end: {
          line: 160,
          column: 62
        }
      },
      "128": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 161,
          column: 70
        }
      },
      "129": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 169
        }
      },
      "130": {
        start: {
          line: 164,
          column: 24
        },
        end: {
          line: 164,
          column: 34
        }
      },
      "131": {
        start: {
          line: 166,
          column: 24
        },
        end: {
          line: 171,
          column: 27
        }
      },
      "132": {
        start: {
          line: 172,
          column: 24
        },
        end: {
          line: 172,
          column: 54
        }
      },
      "133": {
        start: {
          line: 174,
          column: 24
        },
        end: {
          line: 174,
          column: 44
        }
      },
      "134": {
        start: {
          line: 175,
          column: 24
        },
        end: {
          line: 175,
          column: 62
        }
      },
      "135": {
        start: {
          line: 176,
          column: 24
        },
        end: {
          line: 181,
          column: 27
        }
      },
      "136": {
        start: {
          line: 182,
          column: 24
        },
        end: {
          line: 182,
          column: 38
        }
      },
      "137": {
        start: {
          line: 183,
          column: 28
        },
        end: {
          line: 183,
          column: 50
        }
      },
      "138": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 255,
          column: 6
        }
      },
      "139": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 254,
          column: 11
        }
      },
      "140": {
        start: {
          line: 195,
          column: 12
        },
        end: {
          line: 253,
          column: 15
        }
      },
      "141": {
        start: {
          line: 196,
          column: 16
        },
        end: {
          line: 252,
          column: 17
        }
      },
      "142": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 47
        }
      },
      "143": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 86
        }
      },
      "144": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 72
        }
      },
      "145": {
        start: {
          line: 201,
          column: 24
        },
        end: {
          line: 201,
          column: 37
        }
      },
      "146": {
        start: {
          line: 203,
          column: 24
        },
        end: {
          line: 203,
          column: 50
        }
      },
      "147": {
        start: {
          line: 204,
          column: 24
        },
        end: {
          line: 204,
          column: 78
        }
      },
      "148": {
        start: {
          line: 206,
          column: 24
        },
        end: {
          line: 206,
          column: 49
        }
      },
      "149": {
        start: {
          line: 207,
          column: 24
        },
        end: {
          line: 218,
          column: 25
        }
      },
      "150": {
        start: {
          line: 208,
          column: 28
        },
        end: {
          line: 208,
          column: 68
        }
      },
      "151": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 216,
          column: 31
        }
      },
      "152": {
        start: {
          line: 217,
          column: 28
        },
        end: {
          line: 217,
          column: 106
        }
      },
      "153": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 58
        }
      },
      "154": {
        start: {
          line: 221,
          column: 24
        },
        end: {
          line: 221,
          column: 43
        }
      },
      "155": {
        start: {
          line: 222,
          column: 24
        },
        end: {
          line: 222,
          column: 62
        }
      },
      "156": {
        start: {
          line: 224,
          column: 24
        },
        end: {
          line: 224,
          column: 160
        }
      },
      "157": {
        start: {
          line: 227,
          column: 24
        },
        end: {
          line: 227,
          column: 34
        }
      },
      "158": {
        start: {
          line: 229,
          column: 24
        },
        end: {
          line: 236,
          column: 27
        }
      },
      "159": {
        start: {
          line: 237,
          column: 24
        },
        end: {
          line: 237,
          column: 54
        }
      },
      "160": {
        start: {
          line: 239,
          column: 24
        },
        end: {
          line: 239,
          column: 44
        }
      },
      "161": {
        start: {
          line: 240,
          column: 24
        },
        end: {
          line: 240,
          column: 62
        }
      },
      "162": {
        start: {
          line: 241,
          column: 24
        },
        end: {
          line: 249,
          column: 27
        }
      },
      "163": {
        start: {
          line: 250,
          column: 24
        },
        end: {
          line: 250,
          column: 38
        }
      },
      "164": {
        start: {
          line: 251,
          column: 28
        },
        end: {
          line: 251,
          column: 50
        }
      },
      "165": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 296,
          column: 6
        }
      },
      "166": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 295,
          column: 11
        }
      },
      "167": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 28
        }
      },
      "168": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 294,
          column: 15
        }
      },
      "169": {
        start: {
          line: 264,
          column: 16
        },
        end: {
          line: 293,
          column: 17
        }
      },
      "170": {
        start: {
          line: 266,
          column: 24
        },
        end: {
          line: 269,
          column: 26
        }
      },
      "171": {
        start: {
          line: 270,
          column: 24
        },
        end: {
          line: 287,
          column: 31
        }
      },
      "172": {
        start: {
          line: 270,
          column: 79
        },
        end: {
          line: 287,
          column: 27
        }
      },
      "173": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 286,
          column: 31
        }
      },
      "174": {
        start: {
          line: 273,
          column: 32
        },
        end: {
          line: 285,
          column: 33
        }
      },
      "175": {
        start: {
          line: 275,
          column: 40
        },
        end: {
          line: 275,
          column: 93
        }
      },
      "176": {
        start: {
          line: 276,
          column: 40
        },
        end: {
          line: 280,
          column: 42
        }
      },
      "177": {
        start: {
          line: 281,
          column: 40
        },
        end: {
          line: 281,
          column: 171
        }
      },
      "178": {
        start: {
          line: 283,
          column: 40
        },
        end: {
          line: 283,
          column: 50
        }
      },
      "179": {
        start: {
          line: 284,
          column: 40
        },
        end: {
          line: 284,
          column: 62
        }
      },
      "180": {
        start: {
          line: 288,
          column: 24
        },
        end: {
          line: 288,
          column: 75
        }
      },
      "181": {
        start: {
          line: 290,
          column: 24
        },
        end: {
          line: 290,
          column: 34
        }
      },
      "182": {
        start: {
          line: 291,
          column: 24
        },
        end: {
          line: 291,
          column: 118
        }
      },
      "183": {
        start: {
          line: 292,
          column: 24
        },
        end: {
          line: 292,
          column: 46
        }
      },
      "184": {
        start: {
          line: 300,
          column: 4
        },
        end: {
          line: 304,
          column: 6
        }
      },
      "185": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 302,
          column: 86
        }
      },
      "186": {
        start: {
          line: 302,
          column: 12
        },
        end: {
          line: 302,
          column: 86
        }
      },
      "187": {
        start: {
          line: 303,
          column: 8
        },
        end: {
          line: 303,
          column: 62
        }
      },
      "188": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 312,
          column: 6
        }
      },
      "189": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 310,
          column: 72
        }
      },
      "190": {
        start: {
          line: 310,
          column: 12
        },
        end: {
          line: 310,
          column: 72
        }
      },
      "191": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 311,
          column: 50
        }
      },
      "192": {
        start: {
          line: 316,
          column: 4
        },
        end: {
          line: 336,
          column: 6
        }
      },
      "193": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 335,
          column: 11
        }
      },
      "194": {
        start: {
          line: 319,
          column: 12
        },
        end: {
          line: 334,
          column: 15
        }
      },
      "195": {
        start: {
          line: 320,
          column: 16
        },
        end: {
          line: 333,
          column: 17
        }
      },
      "196": {
        start: {
          line: 322,
          column: 24
        },
        end: {
          line: 322,
          column: 50
        }
      },
      "197": {
        start: {
          line: 323,
          column: 24
        },
        end: {
          line: 323,
          column: 78
        }
      },
      "198": {
        start: {
          line: 325,
          column: 24
        },
        end: {
          line: 325,
          column: 49
        }
      },
      "199": {
        start: {
          line: 326,
          column: 24
        },
        end: {
          line: 326,
          column: 72
        }
      },
      "200": {
        start: {
          line: 327,
          column: 24
        },
        end: {
          line: 327,
          column: 91
        }
      },
      "201": {
        start: {
          line: 329,
          column: 24
        },
        end: {
          line: 329,
          column: 44
        }
      },
      "202": {
        start: {
          line: 330,
          column: 24
        },
        end: {
          line: 330,
          column: 71
        }
      },
      "203": {
        start: {
          line: 331,
          column: 24
        },
        end: {
          line: 331,
          column: 53
        }
      },
      "204": {
        start: {
          line: 332,
          column: 28
        },
        end: {
          line: 332,
          column: 50
        }
      },
      "205": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 345,
          column: 6
        }
      },
      "206": {
        start: {
          line: 339,
          column: 8
        },
        end: {
          line: 340,
          column: 19
        }
      },
      "207": {
        start: {
          line: 340,
          column: 12
        },
        end: {
          line: 340,
          column: 19
        }
      },
      "208": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 341,
          column: 92
        }
      },
      "209": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 344,
          column: 9
        }
      },
      "210": {
        start: {
          line: 343,
          column: 12
        },
        end: {
          line: 343,
          column: 128
        }
      },
      "211": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 350,
          column: 6
        }
      },
      "212": {
        start: {
          line: 347,
          column: 8
        },
        end: {
          line: 348,
          column: 19
        }
      },
      "213": {
        start: {
          line: 348,
          column: 12
        },
        end: {
          line: 348,
          column: 19
        }
      },
      "214": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 349,
          column: 85
        }
      },
      "215": {
        start: {
          line: 351,
          column: 4
        },
        end: {
          line: 355,
          column: 6
        }
      },
      "216": {
        start: {
          line: 352,
          column: 8
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "217": {
        start: {
          line: 353,
          column: 12
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "218": {
        start: {
          line: 354,
          column: 8
        },
        end: {
          line: 354,
          column: 135
        }
      },
      "219": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 363,
          column: 6
        }
      },
      "220": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 358,
          column: 19
        }
      },
      "221": {
        start: {
          line: 358,
          column: 12
        },
        end: {
          line: 358,
          column: 19
        }
      },
      "222": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 359,
          column: 166
        }
      },
      "223": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 362,
          column: 9
        }
      },
      "224": {
        start: {
          line: 361,
          column: 12
        },
        end: {
          line: 361,
          column: 135
        }
      },
      "225": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 371,
          column: 6
        }
      },
      "226": {
        start: {
          line: 366,
          column: 25
        },
        end: {
          line: 366,
          column: 168
        }
      },
      "227": {
        start: {
          line: 366,
          column: 122
        },
        end: {
          line: 366,
          column: 141
        }
      },
      "228": {
        start: {
          line: 367,
          column: 25
        },
        end: {
          line: 367,
          column: 127
        }
      },
      "229": {
        start: {
          line: 368,
          column: 20
        },
        end: {
          line: 368,
          column: 119
        }
      },
      "230": {
        start: {
          line: 369,
          column: 19
        },
        end: {
          line: 369,
          column: 135
        }
      },
      "231": {
        start: {
          line: 370,
          column: 8
        },
        end: {
          line: 370,
          column: 37
        }
      },
      "232": {
        start: {
          line: 372,
          column: 4
        },
        end: {
          line: 372,
          column: 38
        }
      },
      "233": {
        start: {
          line: 374,
          column: 0
        },
        end: {
          line: 374,
          column: 64
        }
      },
      "234": {
        start: {
          line: 376,
          column: 0
        },
        end: {
          line: 376,
          column: 70
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 17,
            column: 45
          }
        },
        loc: {
          start: {
            line: 17,
            column: 89
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 18
          }
        },
        loc: {
          start: {
            line: 18,
            column: 26
          },
          end: {
            line: 18,
            column: 112
          }
        },
        line: 18
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 71
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 18,
            column: 108
          }
        },
        line: 18
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 36
          },
          end: {
            line: 19,
            column: 37
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 24,
            column: 5
          }
        },
        line: 19
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 20,
            column: 17
          },
          end: {
            line: 20,
            column: 26
          }
        },
        loc: {
          start: {
            line: 20,
            column: 34
          },
          end: {
            line: 20,
            column: 99
          }
        },
        line: 20
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 25
          }
        },
        loc: {
          start: {
            line: 21,
            column: 33
          },
          end: {
            line: 21,
            column: 102
          }
        },
        line: 21
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 21
          }
        },
        loc: {
          start: {
            line: 22,
            column: 30
          },
          end: {
            line: 22,
            column: 118
          }
        },
        line: 22
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 26,
            column: 49
          }
        },
        loc: {
          start: {
            line: 26,
            column: 73
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 26
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 27,
            column: 30
          },
          end: {
            line: 27,
            column: 31
          }
        },
        loc: {
          start: {
            line: 27,
            column: 41
          },
          end: {
            line: 27,
            column: 83
          }
        },
        line: 27
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 28,
            column: 128
          },
          end: {
            line: 28,
            column: 129
          }
        },
        loc: {
          start: {
            line: 28,
            column: 139
          },
          end: {
            line: 28,
            column: 155
          }
        },
        line: 28
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 29,
            column: 13
          },
          end: {
            line: 29,
            column: 17
          }
        },
        loc: {
          start: {
            line: 29,
            column: 21
          },
          end: {
            line: 29,
            column: 70
          }
        },
        line: 29
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 29,
            column: 31
          }
        },
        loc: {
          start: {
            line: 29,
            column: 43
          },
          end: {
            line: 29,
            column: 67
          }
        },
        line: 29
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 22
          },
          end: {
            line: 51,
            column: 5
          }
        },
        line: 30
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 58,
            column: 48
          },
          end: {
            line: 58,
            column: 49
          }
        },
        loc: {
          start: {
            line: 58,
            column: 60
          },
          end: {
            line: 373,
            column: 1
          }
        },
        line: 58
      },
      "16": {
        name: "SkillGapPerformanceMonitor",
        decl: {
          start: {
            line: 59,
            column: 13
          },
          end: {
            line: 59,
            column: 39
          }
        },
        loc: {
          start: {
            line: 59,
            column: 42
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 59
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 91,
            column: 62
          },
          end: {
            line: 91,
            column: 63
          }
        },
        loc: {
          start: {
            line: 91,
            column: 98
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 91
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 92,
            column: 48
          },
          end: {
            line: 92,
            column: 49
          }
        },
        loc: {
          start: {
            line: 92,
            column: 60
          },
          end: {
            line: 142,
            column: 9
          }
        },
        line: 92
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 94,
            column: 37
          },
          end: {
            line: 94,
            column: 38
          }
        },
        loc: {
          start: {
            line: 94,
            column: 51
          },
          end: {
            line: 141,
            column: 13
          }
        },
        line: 94
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 147,
            column: 66
          },
          end: {
            line: 147,
            column: 67
          }
        },
        loc: {
          start: {
            line: 147,
            column: 108
          },
          end: {
            line: 187,
            column: 5
          }
        },
        line: 147
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 148,
            column: 48
          },
          end: {
            line: 148,
            column: 49
          }
        },
        loc: {
          start: {
            line: 148,
            column: 60
          },
          end: {
            line: 186,
            column: 9
          }
        },
        line: 148
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 150,
            column: 37
          },
          end: {
            line: 150,
            column: 38
          }
        },
        loc: {
          start: {
            line: 150,
            column: 51
          },
          end: {
            line: 185,
            column: 13
          }
        },
        line: 150
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 191,
            column: 64
          },
          end: {
            line: 191,
            column: 65
          }
        },
        loc: {
          start: {
            line: 191,
            column: 110
          },
          end: {
            line: 255,
            column: 5
          }
        },
        line: 191
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 192,
            column: 48
          },
          end: {
            line: 192,
            column: 49
          }
        },
        loc: {
          start: {
            line: 192,
            column: 60
          },
          end: {
            line: 254,
            column: 9
          }
        },
        line: 192
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 195,
            column: 37
          },
          end: {
            line: 195,
            column: 38
          }
        },
        loc: {
          start: {
            line: 195,
            column: 51
          },
          end: {
            line: 253,
            column: 13
          }
        },
        line: 195
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 259,
            column: 66
          },
          end: {
            line: 259,
            column: 67
          }
        },
        loc: {
          start: {
            line: 259,
            column: 78
          },
          end: {
            line: 296,
            column: 5
          }
        },
        line: 259
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 260,
            column: 48
          },
          end: {
            line: 260,
            column: 49
          }
        },
        loc: {
          start: {
            line: 260,
            column: 60
          },
          end: {
            line: 295,
            column: 9
          }
        },
        line: 260
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 263,
            column: 37
          },
          end: {
            line: 263,
            column: 38
          }
        },
        loc: {
          start: {
            line: 263,
            column: 51
          },
          end: {
            line: 294,
            column: 13
          }
        },
        line: 263
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 270,
            column: 60
          },
          end: {
            line: 270,
            column: 61
          }
        },
        loc: {
          start: {
            line: 270,
            column: 77
          },
          end: {
            line: 287,
            column: 29
          }
        },
        line: 270
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 270,
            column: 119
          },
          end: {
            line: 270,
            column: 120
          }
        },
        loc: {
          start: {
            line: 270,
            column: 131
          },
          end: {
            line: 287,
            column: 25
          }
        },
        line: 270
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 272,
            column: 53
          },
          end: {
            line: 272,
            column: 54
          }
        },
        loc: {
          start: {
            line: 272,
            column: 67
          },
          end: {
            line: 286,
            column: 29
          }
        },
        line: 272
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 300,
            column: 64
          },
          end: {
            line: 300,
            column: 65
          }
        },
        loc: {
          start: {
            line: 300,
            column: 76
          },
          end: {
            line: 304,
            column: 5
          }
        },
        line: 300
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 308,
            column: 63
          },
          end: {
            line: 308,
            column: 64
          }
        },
        loc: {
          start: {
            line: 308,
            column: 75
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 308
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 316,
            column: 55
          },
          end: {
            line: 316,
            column: 56
          }
        },
        loc: {
          start: {
            line: 316,
            column: 67
          },
          end: {
            line: 336,
            column: 5
          }
        },
        line: 316
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 317,
            column: 48
          },
          end: {
            line: 317,
            column: 49
          }
        },
        loc: {
          start: {
            line: 317,
            column: 60
          },
          end: {
            line: 335,
            column: 9
          }
        },
        line: 317
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 319,
            column: 37
          },
          end: {
            line: 319,
            column: 38
          }
        },
        loc: {
          start: {
            line: 319,
            column: 51
          },
          end: {
            line: 334,
            column: 13
          }
        },
        line: 319
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 338,
            column: 68
          },
          end: {
            line: 338,
            column: 69
          }
        },
        loc: {
          start: {
            line: 338,
            column: 87
          },
          end: {
            line: 345,
            column: 5
          }
        },
        line: 338
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 346,
            column: 66
          },
          end: {
            line: 346,
            column: 67
          }
        },
        loc: {
          start: {
            line: 346,
            column: 112
          },
          end: {
            line: 350,
            column: 5
          }
        },
        line: 346
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 351,
            column: 72
          },
          end: {
            line: 351,
            column: 73
          }
        },
        loc: {
          start: {
            line: 351,
            column: 91
          },
          end: {
            line: 355,
            column: 5
          }
        },
        line: 351
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 356,
            column: 70
          },
          end: {
            line: 356,
            column: 71
          }
        },
        loc: {
          start: {
            line: 356,
            column: 89
          },
          end: {
            line: 363,
            column: 5
          }
        },
        line: 356
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 364,
            column: 62
          },
          end: {
            line: 364,
            column: 63
          }
        },
        loc: {
          start: {
            line: 364,
            column: 97
          },
          end: {
            line: 371,
            column: 5
          }
        },
        line: 364
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 366,
            column: 107
          },
          end: {
            line: 366,
            column: 108
          }
        },
        loc: {
          start: {
            line: 366,
            column: 120
          },
          end: {
            line: 366,
            column: 143
          }
        },
        line: 366
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 16
          },
          end: {
            line: 25,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 21
          }
        }, {
          start: {
            line: 17,
            column: 25
          },
          end: {
            line: 17,
            column: 39
          }
        }, {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 25,
            column: 1
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 18,
            column: 35
          },
          end: {
            line: 18,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 56
          },
          end: {
            line: 18,
            column: 61
          }
        }, {
          start: {
            line: 18,
            column: 64
          },
          end: {
            line: 18,
            column: 109
          }
        }],
        line: 18
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 17
          }
        }, {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 19,
            column: 33
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 22,
            column: 32
          },
          end: {
            line: 22,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 46
          },
          end: {
            line: 22,
            column: 67
          }
        }, {
          start: {
            line: 22,
            column: 70
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "7": {
        loc: {
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 61
          }
        }, {
          start: {
            line: 23,
            column: 65
          },
          end: {
            line: 23,
            column: 67
          }
        }],
        line: 23
      },
      "8": {
        loc: {
          start: {
            line: 26,
            column: 18
          },
          end: {
            line: 52,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 19
          },
          end: {
            line: 26,
            column: 23
          }
        }, {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 26,
            column: 43
          }
        }, {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 52,
            column: 1
          }
        }],
        line: 26
      },
      "9": {
        loc: {
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "10": {
        loc: {
          start: {
            line: 27,
            column: 134
          },
          end: {
            line: 27,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 27,
            column: 167
          },
          end: {
            line: 27,
            column: 175
          }
        }, {
          start: {
            line: 27,
            column: 178
          },
          end: {
            line: 27,
            column: 184
          }
        }],
        line: 27
      },
      "11": {
        loc: {
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 102
          }
        }, {
          start: {
            line: 28,
            column: 107
          },
          end: {
            line: 28,
            column: 155
          }
        }],
        line: 28
      },
      "12": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "13": {
        loc: {
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 16
          }
        }, {
          start: {
            line: 32,
            column: 21
          },
          end: {
            line: 32,
            column: 44
          }
        }],
        line: 32
      },
      "14": {
        loc: {
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 33
          }
        }, {
          start: {
            line: 32,
            column: 38
          },
          end: {
            line: 32,
            column: 43
          }
        }],
        line: 32
      },
      "15": {
        loc: {
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "16": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 24
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 125
          }
        }, {
          start: {
            line: 33,
            column: 130
          },
          end: {
            line: 33,
            column: 158
          }
        }],
        line: 33
      },
      "17": {
        loc: {
          start: {
            line: 33,
            column: 33
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 45
          },
          end: {
            line: 33,
            column: 56
          }
        }, {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "18": {
        loc: {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        }, {
          start: {
            line: 33,
            column: 119
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "19": {
        loc: {
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 77
          }
        }, {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 115
          }
        }],
        line: 33
      },
      "20": {
        loc: {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 83
          },
          end: {
            line: 33,
            column: 98
          }
        }, {
          start: {
            line: 33,
            column: 103
          },
          end: {
            line: 33,
            column: 112
          }
        }],
        line: 33
      },
      "21": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "22": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 47,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 36,
            column: 23
          }
        }, {
          start: {
            line: 36,
            column: 24
          },
          end: {
            line: 36,
            column: 46
          }
        }, {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 72
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 46,
            column: 43
          }
        }],
        line: 35
      },
      "23": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "24": {
        loc: {
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 74
          }
        }, {
          start: {
            line: 41,
            column: 79
          },
          end: {
            line: 41,
            column: 90
          }
        }, {
          start: {
            line: 41,
            column: 94
          },
          end: {
            line: 41,
            column: 105
          }
        }],
        line: 41
      },
      "25": {
        loc: {
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 54
          }
        }, {
          start: {
            line: 41,
            column: 58
          },
          end: {
            line: 41,
            column: 73
          }
        }],
        line: 41
      },
      "26": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "27": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 35
          }
        }, {
          start: {
            line: 42,
            column: 40
          },
          end: {
            line: 42,
            column: 42
          }
        }, {
          start: {
            line: 42,
            column: 47
          },
          end: {
            line: 42,
            column: 59
          }
        }, {
          start: {
            line: 42,
            column: 63
          },
          end: {
            line: 42,
            column: 75
          }
        }],
        line: 42
      },
      "28": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "29": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 39
          },
          end: {
            line: 43,
            column: 53
          }
        }],
        line: 43
      },
      "30": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "31": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 25
          }
        }, {
          start: {
            line: 44,
            column: 29
          },
          end: {
            line: 44,
            column: 43
          }
        }],
        line: 44
      },
      "32": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "33": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "34": {
        loc: {
          start: {
            line: 50,
            column: 52
          },
          end: {
            line: 50,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 60
          },
          end: {
            line: 50,
            column: 65
          }
        }, {
          start: {
            line: 50,
            column: 68
          },
          end: {
            line: 50,
            column: 74
          }
        }],
        line: 50
      },
      "35": {
        loc: {
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 85,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 85,
            column: 9
          }
        }, {
          start: {
            line: 82,
            column: 13
          },
          end: {
            line: 85,
            column: 9
          }
        }],
        line: 78
      },
      "36": {
        loc: {
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 140,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 96,
            column: 20
          },
          end: {
            line: 99,
            column: 37
          }
        }, {
          start: {
            line: 100,
            column: 20
          },
          end: {
            line: 102,
            column: 78
          }
        }, {
          start: {
            line: 103,
            column: 20
          },
          end: {
            line: 116,
            column: 58
          }
        }, {
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 121,
            column: 148
          }
        }, {
          start: {
            line: 122,
            column: 20
          },
          end: {
            line: 133,
            column: 54
          }
        }, {
          start: {
            line: 134,
            column: 20
          },
          end: {
            line: 138,
            column: 38
          }
        }, {
          start: {
            line: 139,
            column: 20
          },
          end: {
            line: 139,
            column: 50
          }
        }],
        line: 95
      },
      "37": {
        loc: {
          start: {
            line: 105,
            column: 24
          },
          end: {
            line: 115,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 24
          },
          end: {
            line: 115,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "38": {
        loc: {
          start: {
            line: 109,
            column: 45
          },
          end: {
            line: 109,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 109,
            column: 75
          },
          end: {
            line: 109,
            column: 94
          }
        }, {
          start: {
            line: 109,
            column: 97
          },
          end: {
            line: 109,
            column: 98
          }
        }],
        line: 109
      },
      "39": {
        loc: {
          start: {
            line: 128,
            column: 41
          },
          end: {
            line: 128,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 128,
            column: 41
          },
          end: {
            line: 128,
            column: 54
          }
        }, {
          start: {
            line: 128,
            column: 58
          },
          end: {
            line: 128,
            column: 59
          }
        }],
        line: 128
      },
      "40": {
        loc: {
          start: {
            line: 151,
            column: 16
          },
          end: {
            line: 184,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 152,
            column: 20
          },
          end: {
            line: 154,
            column: 37
          }
        }, {
          start: {
            line: 155,
            column: 20
          },
          end: {
            line: 157,
            column: 58
          }
        }, {
          start: {
            line: 158,
            column: 20
          },
          end: {
            line: 162,
            column: 169
          }
        }, {
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 172,
            column: 54
          }
        }, {
          start: {
            line: 173,
            column: 20
          },
          end: {
            line: 182,
            column: 38
          }
        }, {
          start: {
            line: 183,
            column: 20
          },
          end: {
            line: 183,
            column: 50
          }
        }],
        line: 151
      },
      "41": {
        loc: {
          start: {
            line: 196,
            column: 16
          },
          end: {
            line: 252,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 201,
            column: 37
          }
        }, {
          start: {
            line: 202,
            column: 20
          },
          end: {
            line: 204,
            column: 78
          }
        }, {
          start: {
            line: 205,
            column: 20
          },
          end: {
            line: 219,
            column: 58
          }
        }, {
          start: {
            line: 220,
            column: 20
          },
          end: {
            line: 224,
            column: 160
          }
        }, {
          start: {
            line: 225,
            column: 20
          },
          end: {
            line: 237,
            column: 54
          }
        }, {
          start: {
            line: 238,
            column: 20
          },
          end: {
            line: 250,
            column: 38
          }
        }, {
          start: {
            line: 251,
            column: 20
          },
          end: {
            line: 251,
            column: 50
          }
        }],
        line: 196
      },
      "42": {
        loc: {
          start: {
            line: 207,
            column: 24
          },
          end: {
            line: 218,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 24
          },
          end: {
            line: 218,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "43": {
        loc: {
          start: {
            line: 211,
            column: 44
          },
          end: {
            line: 211,
            column: 134
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 45
          },
          end: {
            line: 211,
            column: 128
          }
        }, {
          start: {
            line: 211,
            column: 133
          },
          end: {
            line: 211,
            column: 134
          }
        }],
        line: 211
      },
      "44": {
        loc: {
          start: {
            line: 211,
            column: 45
          },
          end: {
            line: 211,
            column: 128
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 211,
            column: 110
          },
          end: {
            line: 211,
            column: 116
          }
        }, {
          start: {
            line: 211,
            column: 119
          },
          end: {
            line: 211,
            column: 128
          }
        }],
        line: 211
      },
      "45": {
        loc: {
          start: {
            line: 211,
            column: 45
          },
          end: {
            line: 211,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 45
          },
          end: {
            line: 211,
            column: 90
          }
        }, {
          start: {
            line: 211,
            column: 94
          },
          end: {
            line: 211,
            column: 107
          }
        }],
        line: 211
      },
      "46": {
        loc: {
          start: {
            line: 231,
            column: 40
          },
          end: {
            line: 231,
            column: 130
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 41
          },
          end: {
            line: 231,
            column: 124
          }
        }, {
          start: {
            line: 231,
            column: 129
          },
          end: {
            line: 231,
            column: 130
          }
        }],
        line: 231
      },
      "47": {
        loc: {
          start: {
            line: 231,
            column: 41
          },
          end: {
            line: 231,
            column: 124
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 231,
            column: 106
          },
          end: {
            line: 231,
            column: 112
          }
        }, {
          start: {
            line: 231,
            column: 115
          },
          end: {
            line: 231,
            column: 124
          }
        }],
        line: 231
      },
      "48": {
        loc: {
          start: {
            line: 231,
            column: 41
          },
          end: {
            line: 231,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 41
          },
          end: {
            line: 231,
            column: 86
          }
        }, {
          start: {
            line: 231,
            column: 90
          },
          end: {
            line: 231,
            column: 103
          }
        }],
        line: 231
      },
      "49": {
        loc: {
          start: {
            line: 243,
            column: 40
          },
          end: {
            line: 243,
            column: 130
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 41
          },
          end: {
            line: 243,
            column: 124
          }
        }, {
          start: {
            line: 243,
            column: 129
          },
          end: {
            line: 243,
            column: 130
          }
        }],
        line: 243
      },
      "50": {
        loc: {
          start: {
            line: 243,
            column: 41
          },
          end: {
            line: 243,
            column: 124
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 243,
            column: 106
          },
          end: {
            line: 243,
            column: 112
          }
        }, {
          start: {
            line: 243,
            column: 115
          },
          end: {
            line: 243,
            column: 124
          }
        }],
        line: 243
      },
      "51": {
        loc: {
          start: {
            line: 243,
            column: 41
          },
          end: {
            line: 243,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 41
          },
          end: {
            line: 243,
            column: 86
          }
        }, {
          start: {
            line: 243,
            column: 90
          },
          end: {
            line: 243,
            column: 103
          }
        }],
        line: 243
      },
      "52": {
        loc: {
          start: {
            line: 264,
            column: 16
          },
          end: {
            line: 293,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 265,
            column: 20
          },
          end: {
            line: 288,
            column: 75
          }
        }, {
          start: {
            line: 289,
            column: 20
          },
          end: {
            line: 292,
            column: 46
          }
        }],
        line: 264
      },
      "53": {
        loc: {
          start: {
            line: 273,
            column: 32
          },
          end: {
            line: 285,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 274,
            column: 36
          },
          end: {
            line: 281,
            column: 171
          }
        }, {
          start: {
            line: 282,
            column: 36
          },
          end: {
            line: 284,
            column: 62
          }
        }],
        line: 273
      },
      "54": {
        loc: {
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 302,
            column: 86
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 302,
            column: 86
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 301
      },
      "55": {
        loc: {
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 310,
            column: 72
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 310,
            column: 72
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 309
      },
      "56": {
        loc: {
          start: {
            line: 320,
            column: 16
          },
          end: {
            line: 333,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 321,
            column: 20
          },
          end: {
            line: 323,
            column: 78
          }
        }, {
          start: {
            line: 324,
            column: 20
          },
          end: {
            line: 327,
            column: 91
          }
        }, {
          start: {
            line: 328,
            column: 20
          },
          end: {
            line: 331,
            column: 53
          }
        }, {
          start: {
            line: 332,
            column: 20
          },
          end: {
            line: 332,
            column: 50
          }
        }],
        line: 320
      },
      "57": {
        loc: {
          start: {
            line: 327,
            column: 46
          },
          end: {
            line: 327,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 327,
            column: 46
          },
          end: {
            line: 327,
            column: 58
          }
        }, {
          start: {
            line: 327,
            column: 62
          },
          end: {
            line: 327,
            column: 89
          }
        }],
        line: 327
      },
      "58": {
        loc: {
          start: {
            line: 339,
            column: 8
          },
          end: {
            line: 340,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 339,
            column: 8
          },
          end: {
            line: 340,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 339
      },
      "59": {
        loc: {
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "60": {
        loc: {
          start: {
            line: 347,
            column: 8
          },
          end: {
            line: 348,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 347,
            column: 8
          },
          end: {
            line: 348,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 347
      },
      "61": {
        loc: {
          start: {
            line: 352,
            column: 8
          },
          end: {
            line: 353,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 8
          },
          end: {
            line: 353,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 352
      },
      "62": {
        loc: {
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 358,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 358,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 357
      },
      "63": {
        loc: {
          start: {
            line: 360,
            column: 8
          },
          end: {
            line: 362,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 8
          },
          end: {
            line: 362,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 360
      },
      "64": {
        loc: {
          start: {
            line: 366,
            column: 25
          },
          end: {
            line: 366,
            column: 168
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 366,
            column: 26
          },
          end: {
            line: 366,
            column: 161
          }
        }, {
          start: {
            line: 366,
            column: 166
          },
          end: {
            line: 366,
            column: 168
          }
        }],
        line: 366
      },
      "65": {
        loc: {
          start: {
            line: 366,
            column: 26
          },
          end: {
            line: 366,
            column: 161
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 366,
            column: 91
          },
          end: {
            line: 366,
            column: 97
          }
        }, {
          start: {
            line: 366,
            column: 100
          },
          end: {
            line: 366,
            column: 161
          }
        }],
        line: 366
      },
      "66": {
        loc: {
          start: {
            line: 366,
            column: 26
          },
          end: {
            line: 366,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 366,
            column: 26
          },
          end: {
            line: 366,
            column: 71
          }
        }, {
          start: {
            line: 366,
            column: 75
          },
          end: {
            line: 366,
            column: 88
          }
        }],
        line: 366
      },
      "67": {
        loc: {
          start: {
            line: 367,
            column: 25
          },
          end: {
            line: 367,
            column: 127
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 367,
            column: 26
          },
          end: {
            line: 367,
            column: 120
          }
        }, {
          start: {
            line: 367,
            column: 125
          },
          end: {
            line: 367,
            column: 127
          }
        }],
        line: 367
      },
      "68": {
        loc: {
          start: {
            line: 367,
            column: 26
          },
          end: {
            line: 367,
            column: 120
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 367,
            column: 94
          },
          end: {
            line: 367,
            column: 100
          }
        }, {
          start: {
            line: 367,
            column: 103
          },
          end: {
            line: 367,
            column: 120
          }
        }],
        line: 367
      },
      "69": {
        loc: {
          start: {
            line: 367,
            column: 26
          },
          end: {
            line: 367,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 367,
            column: 26
          },
          end: {
            line: 367,
            column: 74
          }
        }, {
          start: {
            line: 367,
            column: 78
          },
          end: {
            line: 367,
            column: 91
          }
        }],
        line: 367
      },
      "70": {
        loc: {
          start: {
            line: 368,
            column: 20
          },
          end: {
            line: 368,
            column: 119
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 368,
            column: 21
          },
          end: {
            line: 368,
            column: 112
          }
        }, {
          start: {
            line: 368,
            column: 117
          },
          end: {
            line: 368,
            column: 119
          }
        }],
        line: 368
      },
      "71": {
        loc: {
          start: {
            line: 368,
            column: 21
          },
          end: {
            line: 368,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 368,
            column: 89
          },
          end: {
            line: 368,
            column: 95
          }
        }, {
          start: {
            line: 368,
            column: 98
          },
          end: {
            line: 368,
            column: 112
          }
        }],
        line: 368
      },
      "72": {
        loc: {
          start: {
            line: 368,
            column: 21
          },
          end: {
            line: 368,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 368,
            column: 21
          },
          end: {
            line: 368,
            column: 69
          }
        }, {
          start: {
            line: 368,
            column: 73
          },
          end: {
            line: 368,
            column: 86
          }
        }],
        line: 368
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0, 0, 0, 0, 0],
      "41": [0, 0, 0, 0, 0, 0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0, 0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/skill-gap-performance.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uEAAkE;AAClE,+DAA4D;AAC5D,wFAA8E;AA2B9E;IAwBE;QAnBA,6CAA6C;QAC5B,cAAS,GAAG;YAC3B,YAAY,EAAE,MAAM,EAAS,YAAY;YACzC,gBAAgB,EAAE,OAAO,EAAI,SAAS;YACtC,cAAc,EAAE,OAAO,EAAM,aAAa;YAC1C,cAAc,EAAE,OAAO,EAAM,UAAU;YACvC,WAAW,EAAE,QAAQ,EAAQ,WAAW;SACzC,CAAC;QAEF,yBAAyB;QACR,eAAU,GAAG;YAC5B,oBAAoB,EAAE,IAAI,EAAO,WAAW;YAC5C,qBAAqB,EAAE,IAAI,EAAM,YAAY;YAC7C,gBAAgB,EAAE,IAAI,EAAW,YAAY;YAC7C,iBAAiB,EAAE,KAAK,EAAS,aAAa;YAC9C,sBAAsB,EAAE,EAAE,EAAO,MAAM;YACvC,uBAAuB,EAAE,EAAE,EAAM,MAAM;SACxC,CAAC;QAGA,2CAA2C;QAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,2CAAkB,EAAE,CAAC;YACnD,IAAI,CAAC,gBAAgB,GAAG,qCAAgB,CAAC,WAAW,EAAE,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,8CAAiB,CAAC;IACxC,CAAC;IAED;;OAEG;IACG,uDAAkB,GAAxB,UACE,KAAa,EACb,SAA6B,EAC7B,MAAe;uCACd,OAAO;;;;;wBACF,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACvB,QAAQ,GAAG,uBAAgB,KAAK,CAAC,WAAW,EAAE,CAAE,CAAC;;;;wBAIhC,qBAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAM,QAAQ,CAAC,EAAA;;wBAAzD,YAAY,GAAG,SAA0C;wBAC/D,IAAI,YAAY,EAAE,CAAC;4BACX,iBAAe,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;4BAC5C,IAAI,CAAC,wBAAwB,CAAC;gCAC5B,KAAK,OAAA;gCACL,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gCAClE,YAAY,gBAAA;gCACZ,QAAQ,EAAE,IAAI;gCACd,MAAM,QAAA;6BACP,CAAC,CAAC;4BACH,sBAAO,YAAY,EAAC;wBACtB,CAAC;wBAGc,qBAAM,SAAS,EAAE,EAAA;;wBAA1B,MAAM,GAAG,SAAiB;wBAC1B,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAE5C,mBAAmB;wBACnB,qBAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,EAAA;;wBAD3G,mBAAmB;wBACnB,SAA2G,CAAC;wBAE5G,iBAAiB;wBACjB,IAAI,CAAC,wBAAwB,CAAC;4BAC5B,KAAK,OAAA;4BACL,WAAW,EAAE,MAAM,CAAC,MAAM,IAAI,CAAC;4BAC/B,YAAY,cAAA;4BACZ,QAAQ,EAAE,KAAK;4BACf,MAAM,QAAA;yBACP,CAAC,CAAC;wBAEH,sBAAO,MAAM,EAAC;;;wBAER,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC5C,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,YAAY,EAAE,OAAc,EAAE,MAAM,CAAC,CAAC;wBACzE,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;OAEG;IACG,2DAAsB,GAA5B,UACE,WAAkB,EAClB,SAA6B,EAC7B,MAAc;uCACb,OAAO;;;;;wBACF,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAGZ,qBAAM,SAAS,EAAE,EAAA;;wBAA1B,MAAM,GAAG,SAAiB;wBAC1B,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAGtC,QAAQ,GAAG,2BAAoB,MAAM,CAAE,CAAC;wBAC9C,qBAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,EAAE,CAAC,kBAAkB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAA;;wBAAhI,SAAgI,CAAC;wBAEjI,iBAAiB;wBACjB,IAAI,CAAC,4BAA4B,CAAC;4BAChC,eAAe,EAAE,WAAW,CAAC,MAAM;4BACnC,YAAY,cAAA;4BACZ,OAAO,EAAE,IAAI;4BACb,MAAM,QAAA;yBACP,CAAC,CAAC;wBAEH,sBAAO,MAAM,EAAC;;;wBAER,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC5C,IAAI,CAAC,4BAA4B,CAAC;4BAChC,eAAe,EAAE,WAAW,CAAC,MAAM;4BACnC,YAAY,cAAA;4BACZ,OAAO,EAAE,KAAK;4BACd,MAAM,QAAA;yBACP,CAAC,CAAC;wBACH,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;OAEG;IACG,yDAAoB,GAA1B,UACE,eAAoB,EACpB,SAA6B,EAC7B,MAAc;uCACb,OAAO;;;;;;wBACF,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACvB,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;wBAC9D,QAAQ,GAAG,yBAAkB,UAAU,CAAE,CAAC;;;;wBAIzB,qBAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAM,QAAQ,CAAC,EAAA;;wBAAzD,YAAY,GAAG,SAA0C;wBAC/D,IAAI,YAAY,EAAE,CAAC;4BACX,iBAAe,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;4BAC5C,IAAI,CAAC,0BAA0B,CAAC;gCAC9B,YAAY,EAAE,eAAe;gCAC7B,UAAU,EAAE,CAAA,MAAA,eAAe,CAAC,aAAa,0CAAE,MAAM,KAAI,CAAC;gCACtD,YAAY,gBAAA;gCACZ,OAAO,EAAE,IAAI;gCACb,QAAQ,EAAE,IAAI;gCACd,MAAM,QAAA;6BACP,CAAC,CAAC;4BACH,4CAAY,YAAY,KAAE,MAAM,EAAE,IAAI,KAAG;wBAC3C,CAAC;wBAGc,qBAAM,SAAS,EAAE,EAAA;;wBAA1B,MAAM,GAAG,SAAiB;wBAC1B,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAE5C,mBAAmB;wBACnB,qBAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,gBAAgB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAA;;wBADvH,mBAAmB;wBACnB,SAAuH,CAAC;wBAExH,iBAAiB;wBACjB,IAAI,CAAC,0BAA0B,CAAC;4BAC9B,YAAY,EAAE,eAAe;4BAC7B,UAAU,EAAE,CAAA,MAAA,eAAe,CAAC,aAAa,0CAAE,MAAM,KAAI,CAAC;4BACtD,YAAY,cAAA;4BACZ,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,KAAK;4BACf,MAAM,QAAA;yBACP,CAAC,CAAC;wBAEH,sBAAO,MAAM,EAAC;;;wBAER,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC5C,IAAI,CAAC,0BAA0B,CAAC;4BAC9B,YAAY,EAAE,eAAe;4BAC7B,UAAU,EAAE,CAAA,MAAA,eAAe,CAAC,aAAa,0CAAE,MAAM,KAAI,CAAC;4BACtD,YAAY,cAAA;4BACZ,OAAO,EAAE,KAAK;4BACd,QAAQ,EAAE,KAAK;4BACf,MAAM,QAAA;4BACN,KAAK,EAAG,OAAe,CAAC,OAAO;yBAChC,CAAC,CAAC;wBACH,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;OAEG;IACG,2DAAsB,GAA5B;uCAAgC,OAAO;;;;;;wBAC/B,aAAa,GAAG;4BACpB,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY;4BACxD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ;yBACtC,CAAC;wBAEI,eAAe,GAAG,aAAa,CAAC,GAAG,CAAC,UAAO,KAAK;;;;;wCAC9C,QAAQ,GAAG,qBAAc,KAAK,CAAC,WAAW,EAAE,CAAE,CAAC;wCAC/C,SAAS,GAAG;4CAChB,IAAI,EAAE,KAAK;4CACX,OAAO,EAAE,IAAI;4CACb,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;yCACrB,CAAC;wCACF,qBAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAA;;wCAAlH,SAAkH,CAAC;;;;6BACpH,CAAC,CAAC;wBAEH,qBAAM,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAA;;wBAAlC,SAAkC,CAAC;wBACnC,OAAO,CAAC,GAAG,CAAC,wCAAuB,aAAa,CAAC,MAAM,oBAAiB,CAAC,CAAC;;;;;KAC3E;IAED;;OAEG;IACH,yDAAoB,GAApB;QACE,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;QACxG,OAAO,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,wDAAmB,GAAnB;QACE,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;QACxF,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACG,gDAAW,GAAjB;uCAAqB,OAAO;;;;;;wBAEH,qBAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,EAAA;;wBAApD,YAAY,GAAG,SAAqC;wBACpD,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAEtD,sBAAO,YAAY,IAAI,iBAAiB,CAAC,SAAS,EAAC;;;wBAEnD,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,OAAK,CAAC,CAAC;wBAC7C,sBAAO,KAAK,EAAC;;;;;KAEhB;IAED,yBAAyB;IAEjB,6DAAwB,GAAhC,UAAiC,OAA2B;QAC1D,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,CAAC,sBAAsB;QAE5D,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,cAAc,EAAE,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAEpF,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAChE,OAAO,CAAC,IAAI,CAAC,0CAAyB,OAAO,CAAC,KAAK,mBAAS,OAAO,CAAC,YAAY,OAAI,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAEO,2DAAsB,GAA9B,UAA+B,KAAa,EAAE,YAAoB,EAAE,KAAY,EAAE,MAAe;QAC/F,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,CAAC,sBAAsB;QAE5D,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,cAAc,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IAC/E,CAAC;IAEO,iEAA4B,GAApC,UAAqC,OAA+B;QAClE,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,CAAC,sBAAsB;QAE1D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CACnC,yBAAyB,EACzB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,OAAO,EACf,KAAK,EACL,OAAO,CAAC,MAAM,CACf,CAAC;IACJ,CAAC;IAEO,+DAA0B,GAAlC,UAAmC,OAA6B;QAC9D,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,CAAC,sBAAsB;QAE1D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CACnC,8BAA8B,EAC9B,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,KAAK,CACd,CAAC;QAEF,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,oDAAmC,OAAO,CAAC,MAAM,eAAK,OAAO,CAAC,YAAY,OAAI,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAEO,uDAAkB,GAA1B,UAA2B,eAAoB,EAAE,MAAc;;QAC7D,IAAM,UAAU,GAAG,CAAA,MAAA,eAAe,CAAC,aAAa,0CAAE,GAAG,CAAC,UAAC,CAAM,IAAK,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,EAAE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAI,EAAE,CAAC;QACtG,IAAM,UAAU,GAAG,CAAA,MAAA,eAAe,CAAC,gBAAgB,0CAAE,cAAc,KAAI,EAAE,CAAC;QAC1E,IAAM,KAAK,GAAG,CAAA,MAAA,eAAe,CAAC,gBAAgB,0CAAE,WAAW,KAAI,EAAE,CAAC;QAElE,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAG,MAAM,cAAI,UAAU,cAAI,UAAU,cAAI,KAAK,CAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC9F,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/B,CAAC;IACH,iCAAC;AAAD,CAAC,AAvSD,IAuSC;AAvSY,gEAA0B;AAySvC,4BAA4B;AACf,QAAA,0BAA0B,GAAG,IAAI,0BAA0B,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/skill-gap-performance.ts"],
      sourcesContent: ["/**\n * Performance monitoring and caching strategies for Skill Gap Analyzer\n * Implements comprehensive performance tracking and optimization\n */\n\nimport { PerformanceMonitor } from '@/lib/performance-monitoring';\nimport { AIServiceMonitor } from '@/lib/ai-service-monitor';\nimport { consolidatedCache } from '@/lib/services/consolidated-cache-service';\n\ninterface SkillSearchMetrics {\n  query: string;\n  resultCount: number;\n  responseTime: number;\n  cacheHit: boolean;\n  userId?: string;\n}\n\ninterface SkillAssessmentMetrics {\n  assessmentCount: number;\n  responseTime: number;\n  success: boolean;\n  userId: string;\n}\n\ninterface SkillAnalysisMetrics {\n  analysisType: 'basic' | 'comprehensive';\n  skillCount: number;\n  responseTime: number;\n  success: boolean;\n  cacheHit: boolean;\n  userId: string;\n  error?: string;\n}\n\nexport class SkillGapPerformanceMonitor {\n  private performanceMonitor: PerformanceMonitor | null;\n  private aiServiceMonitor: AIServiceMonitor | null;\n  private cacheService: typeof consolidatedCache;\n\n  // Cache TTL configurations (in milliseconds)\n  private readonly CACHE_TTL = {\n    SKILL_SEARCH: 300000,        // 5 minutes\n    USER_ASSESSMENTS: 3600000,   // 1 hour\n    SKILL_ANALYSIS: 1800000,     // 30 minutes\n    POPULAR_SKILLS: 7200000,     // 2 hours\n    MARKET_DATA: 86400000,       // 24 hours\n  };\n\n  // Performance thresholds\n  private readonly THRESHOLDS = {\n    SKILL_SEARCH_WARNING: 1000,      // 1 second\n    SKILL_SEARCH_CRITICAL: 3000,     // 3 seconds\n    ANALYSIS_WARNING: 5000,          // 5 seconds\n    ANALYSIS_CRITICAL: 15000,        // 15 seconds\n    CACHE_HIT_RATE_WARNING: 70,      // 70%\n    CACHE_HIT_RATE_CRITICAL: 50,     // 50%\n  };\n\n  constructor() {\n    // Only initialize monitoring in production\n    if (process.env.NODE_ENV === 'production') {\n      this.performanceMonitor = new PerformanceMonitor();\n      this.aiServiceMonitor = AIServiceMonitor.getInstance();\n    } else {\n      this.performanceMonitor = null;\n      this.aiServiceMonitor = null;\n    }\n    this.cacheService = consolidatedCache;\n  }\n\n  /**\n   * Monitor skill search performance\n   */\n  async monitorSkillSearch(\n    query: string,\n    operation: () => Promise<any>,\n    userId?: string\n  ): Promise<any> {\n    const startTime = Date.now();\n    const cacheKey = `skill_search:${query.toLowerCase()}`;\n    \n    try {\n      // Check cache first\n      const cachedResult = await this.cacheService.get<any>(cacheKey);\n      if (cachedResult) {\n        const responseTime = Date.now() - startTime;\n        this.recordSkillSearchMetrics({\n          query,\n          resultCount: Array.isArray(cachedResult) ? cachedResult.length : 0,\n          responseTime,\n          cacheHit: true,\n          userId\n        });\n        return cachedResult;\n      }\n\n      // Execute operation\n      const result = await operation();\n      const responseTime = Date.now() - startTime;\n\n      // Cache the result\n      await this.cacheService.set(cacheKey, result, { ttl: this.CACHE_TTL.SKILL_SEARCH, tags: ['skill_search'] });\n\n      // Record metrics\n      this.recordSkillSearchMetrics({\n        query,\n        resultCount: result.length || 0,\n        responseTime,\n        cacheHit: false,\n        userId\n      });\n\n      return result;\n    } catch (error) {\n      const responseTime = Date.now() - startTime;\n      this.recordSkillSearchError(query, responseTime, error as Error, userId);\n      throw error;\n    }\n  }\n\n  /**\n   * Monitor skill assessment submission performance\n   */\n  async monitorSkillAssessment(\n    assessments: any[],\n    operation: () => Promise<any>,\n    userId: string\n  ): Promise<any> {\n    const startTime = Date.now();\n    \n    try {\n      const result = await operation();\n      const responseTime = Date.now() - startTime;\n\n      // Cache user assessments\n      const cacheKey = `user_assessments:${userId}`;\n      await this.cacheService.set(cacheKey, assessments, { ttl: this.CACHE_TTL.USER_ASSESSMENTS, tags: ['user_assessments', userId] });\n\n      // Record metrics\n      this.recordSkillAssessmentMetrics({\n        assessmentCount: assessments.length,\n        responseTime,\n        success: true,\n        userId\n      });\n\n      return result;\n    } catch (error) {\n      const responseTime = Date.now() - startTime;\n      this.recordSkillAssessmentMetrics({\n        assessmentCount: assessments.length,\n        responseTime,\n        success: false,\n        userId\n      });\n      throw error;\n    }\n  }\n\n  /**\n   * Monitor comprehensive skill analysis performance\n   */\n  async monitorSkillAnalysis(\n    analysisRequest: any,\n    operation: () => Promise<any>,\n    userId: string\n  ): Promise<any> {\n    const startTime = Date.now();\n    const analysisId = this.generateAnalysisId(analysisRequest, userId);\n    const cacheKey = `skill_analysis:${analysisId}`;\n    \n    try {\n      // Check cache first\n      const cachedResult = await this.cacheService.get<any>(cacheKey);\n      if (cachedResult) {\n        const responseTime = Date.now() - startTime;\n        this.recordSkillAnalysisMetrics({\n          analysisType: 'comprehensive',\n          skillCount: analysisRequest.currentSkills?.length || 0,\n          responseTime,\n          success: true,\n          cacheHit: true,\n          userId\n        });\n        return { ...cachedResult, cached: true };\n      }\n\n      // Execute operation\n      const result = await operation();\n      const responseTime = Date.now() - startTime;\n\n      // Cache the result\n      await this.cacheService.set(cacheKey, result, { ttl: this.CACHE_TTL.SKILL_ANALYSIS, tags: ['skill_analysis', userId] });\n\n      // Record metrics\n      this.recordSkillAnalysisMetrics({\n        analysisType: 'comprehensive',\n        skillCount: analysisRequest.currentSkills?.length || 0,\n        responseTime,\n        success: true,\n        cacheHit: false,\n        userId\n      });\n\n      return result;\n    } catch (error) {\n      const responseTime = Date.now() - startTime;\n      this.recordSkillAnalysisMetrics({\n        analysisType: 'comprehensive',\n        skillCount: analysisRequest.currentSkills?.length || 0,\n        responseTime,\n        success: false,\n        cacheHit: false,\n        userId,\n        error: (error as Error).message\n      });\n      throw error;\n    }\n  }\n\n  /**\n   * Warm cache with popular skills\n   */\n  async warmPopularSkillsCache(): Promise<void> {\n    const popularSkills = [\n      'JavaScript', 'Python', 'React', 'Node.js', 'TypeScript',\n      'Java', 'C++', 'SQL', 'AWS', 'Docker'\n    ];\n\n    const warmingPromises = popularSkills.map(async (skill) => {\n      const cacheKey = `skill_data:${skill.toLowerCase()}`;\n      const skillData = {\n        name: skill,\n        popular: true,\n        warmedAt: Date.now()\n      };\n      await this.cacheService.set(cacheKey, skillData, { ttl: this.CACHE_TTL.POPULAR_SKILLS, tags: ['popular_skills'] });\n    });\n\n    await Promise.all(warmingPromises);\n    console.log(`\uD83D\uDD25 Warmed cache for ${popularSkills.length} popular skills`);\n  }\n\n  /**\n   * Get performance status for skill gap analyzer\n   */\n  getPerformanceStatus(): any {\n    if (!this.performanceMonitor) return { isHealthy: true, message: 'Monitoring disabled in development' };\n    return this.performanceMonitor.getPerformanceStatus();\n  }\n\n  /**\n   * Get AI service metrics\n   */\n  getAIServiceMetrics(): any {\n    if (!this.aiServiceMonitor) return { message: 'AI monitoring disabled in development' };\n    return this.aiServiceMonitor.getMetrics();\n  }\n\n  /**\n   * Perform health check\n   */\n  async healthCheck(): Promise<boolean> {\n    try {\n      const cacheHealthy = await this.cacheService.healthCheck();\n      const performanceStatus = this.getPerformanceStatus();\n\n      return cacheHealthy && performanceStatus.isHealthy;\n    } catch (error) {\n      console.error('Health check failed:', error);\n      return false;\n    }\n  }\n\n  // Private helper methods\n\n  private recordSkillSearchMetrics(metrics: SkillSearchMetrics): void {\n    if (!this.performanceMonitor) return; // Skip in development\n\n    this.performanceMonitor.recordOperation('skill_search', metrics.responseTime, true);\n\n    if (metrics.responseTime > this.THRESHOLDS.SKILL_SEARCH_WARNING) {\n      console.warn(`\u26A0\uFE0F Slow skill search: ${metrics.query} took ${metrics.responseTime}ms`);\n    }\n  }\n\n  private recordSkillSearchError(query: string, responseTime: number, error: Error, userId?: string): void {\n    if (!this.performanceMonitor) return; // Skip in development\n\n    this.performanceMonitor.recordOperation('skill_search', responseTime, false);\n  }\n\n  private recordSkillAssessmentMetrics(metrics: SkillAssessmentMetrics): void {\n    if (!this.aiServiceMonitor) return; // Skip in development\n\n    this.aiServiceMonitor.recordOperation(\n      'skill_assessment_submit',\n      metrics.responseTime,\n      metrics.success,\n      false,\n      metrics.userId\n    );\n  }\n\n  private recordSkillAnalysisMetrics(metrics: SkillAnalysisMetrics): void {\n    if (!this.aiServiceMonitor) return; // Skip in development\n\n    this.aiServiceMonitor.recordOperation(\n      'comprehensive_skill_analysis',\n      metrics.responseTime,\n      metrics.success,\n      metrics.cacheHit,\n      metrics.userId,\n      metrics.error\n    );\n\n    if (metrics.responseTime > this.THRESHOLDS.ANALYSIS_WARNING) {\n      console.warn(`\u26A0\uFE0F Slow skill analysis for user ${metrics.userId}: ${metrics.responseTime}ms`);\n    }\n  }\n\n  private generateAnalysisId(analysisRequest: any, userId: string): string {\n    const skillNames = analysisRequest.currentSkills?.map((s: any) => s.skillName).sort().join(',') || '';\n    const careerPath = analysisRequest.targetCareerPath?.careerPathName || '';\n    const level = analysisRequest.targetCareerPath?.targetLevel || '';\n    \n    const hash = Buffer.from(`${userId}:${skillNames}:${careerPath}:${level}`).toString('base64');\n    return hash.substring(0, 16);\n  }\n}\n\n// Export singleton instance\nexport const skillGapPerformanceMonitor = new SkillGapPerformanceMonitor();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2fdfb9596ac03c56a4ae05be56fd59f7be154b89"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1pnbn5pbao = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1pnbn5pbao();
var __assign =
/* istanbul ignore next */
(cov_1pnbn5pbao().s[0]++,
/* istanbul ignore next */
(cov_1pnbn5pbao().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1pnbn5pbao().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1pnbn5pbao().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1pnbn5pbao().f[0]++;
  cov_1pnbn5pbao().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1pnbn5pbao().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1pnbn5pbao().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[1]++;
    cov_1pnbn5pbao().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1pnbn5pbao().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1pnbn5pbao().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1pnbn5pbao().b[2][0]++;
          cov_1pnbn5pbao().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1pnbn5pbao().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1pnbn5pbao().s[11]++,
/* istanbul ignore next */
(cov_1pnbn5pbao().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1pnbn5pbao().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1pnbn5pbao().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1pnbn5pbao().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[3]++;
    cov_1pnbn5pbao().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[4]++;
      cov_1pnbn5pbao().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1pnbn5pbao().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1pnbn5pbao().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[6]++;
      cov_1pnbn5pbao().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[7]++;
      cov_1pnbn5pbao().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[8]++;
      cov_1pnbn5pbao().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1pnbn5pbao().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1pnbn5pbao().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1pnbn5pbao().s[23]++,
/* istanbul ignore next */
(cov_1pnbn5pbao().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1pnbn5pbao().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1pnbn5pbao().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1pnbn5pbao().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1pnbn5pbao().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1pnbn5pbao().f[10]++;
        cov_1pnbn5pbao().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1pnbn5pbao().b[9][0]++;
          cov_1pnbn5pbao().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1pnbn5pbao().b[9][1]++;
        }
        cov_1pnbn5pbao().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1pnbn5pbao().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1pnbn5pbao().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1pnbn5pbao().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[11]++;
    cov_1pnbn5pbao().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[12]++;
    cov_1pnbn5pbao().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[13]++;
      cov_1pnbn5pbao().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[14]++;
    cov_1pnbn5pbao().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[12][0]++;
      cov_1pnbn5pbao().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[12][1]++;
    }
    cov_1pnbn5pbao().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[18][0]++,
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[19][1]++,
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1pnbn5pbao().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1pnbn5pbao().b[15][0]++;
          cov_1pnbn5pbao().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1pnbn5pbao().b[15][1]++;
        }
        cov_1pnbn5pbao().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1pnbn5pbao().b[21][0]++;
          cov_1pnbn5pbao().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1pnbn5pbao().b[21][1]++;
        }
        cov_1pnbn5pbao().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[22][1]++;
            cov_1pnbn5pbao().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[22][2]++;
            cov_1pnbn5pbao().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[22][3]++;
            cov_1pnbn5pbao().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[22][4]++;
            cov_1pnbn5pbao().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[22][5]++;
            cov_1pnbn5pbao().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().b[23][0]++;
              cov_1pnbn5pbao().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1pnbn5pbao().b[23][1]++;
            }
            cov_1pnbn5pbao().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().b[26][0]++;
              cov_1pnbn5pbao().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pnbn5pbao().b[26][1]++;
            }
            cov_1pnbn5pbao().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().b[28][0]++;
              cov_1pnbn5pbao().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pnbn5pbao().b[28][1]++;
            }
            cov_1pnbn5pbao().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().b[30][0]++;
              cov_1pnbn5pbao().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pnbn5pbao().b[30][1]++;
            }
            cov_1pnbn5pbao().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().b[32][0]++;
              cov_1pnbn5pbao().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1pnbn5pbao().b[32][1]++;
            }
            cov_1pnbn5pbao().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1pnbn5pbao().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[33][0]++;
      cov_1pnbn5pbao().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[33][1]++;
    }
    cov_1pnbn5pbao().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1pnbn5pbao().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1pnbn5pbao().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1pnbn5pbao().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1pnbn5pbao().s[79]++;
exports.skillGapPerformanceMonitor = exports.SkillGapPerformanceMonitor = void 0;
var performance_monitoring_1 =
/* istanbul ignore next */
(cov_1pnbn5pbao().s[80]++, require("@/lib/performance-monitoring"));
var ai_service_monitor_1 =
/* istanbul ignore next */
(cov_1pnbn5pbao().s[81]++, require("@/lib/ai-service-monitor"));
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_1pnbn5pbao().s[82]++, require("@/lib/services/consolidated-cache-service"));
var SkillGapPerformanceMonitor =
/* istanbul ignore next */
(/** @class */cov_1pnbn5pbao().s[83]++, function () {
  /* istanbul ignore next */
  cov_1pnbn5pbao().f[15]++;
  function SkillGapPerformanceMonitor() {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[16]++;
    cov_1pnbn5pbao().s[84]++;
    // Cache TTL configurations (in milliseconds)
    this.CACHE_TTL = {
      SKILL_SEARCH: 300000,
      // 5 minutes
      USER_ASSESSMENTS: 3600000,
      // 1 hour
      SKILL_ANALYSIS: 1800000,
      // 30 minutes
      POPULAR_SKILLS: 7200000,
      // 2 hours
      MARKET_DATA: 86400000 // 24 hours
    };
    // Performance thresholds
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[85]++;
    this.THRESHOLDS = {
      SKILL_SEARCH_WARNING: 1000,
      // 1 second
      SKILL_SEARCH_CRITICAL: 3000,
      // 3 seconds
      ANALYSIS_WARNING: 5000,
      // 5 seconds
      ANALYSIS_CRITICAL: 15000,
      // 15 seconds
      CACHE_HIT_RATE_WARNING: 70,
      // 70%
      CACHE_HIT_RATE_CRITICAL: 50 // 50%
    };
    // Only initialize monitoring in production
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[86]++;
    if (process.env.NODE_ENV === 'production') {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[35][0]++;
      cov_1pnbn5pbao().s[87]++;
      this.performanceMonitor = new performance_monitoring_1.PerformanceMonitor();
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[88]++;
      this.aiServiceMonitor = ai_service_monitor_1.AIServiceMonitor.getInstance();
    } else {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[35][1]++;
      cov_1pnbn5pbao().s[89]++;
      this.performanceMonitor = null;
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[90]++;
      this.aiServiceMonitor = null;
    }
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[91]++;
    this.cacheService = consolidated_cache_service_1.consolidatedCache;
  }
  /**
   * Monitor skill search performance
   */
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[92]++;
  SkillGapPerformanceMonitor.prototype.monitorSkillSearch = function (query, operation, userId) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[17]++;
    cov_1pnbn5pbao().s[93]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[18]++;
      var startTime, cacheKey, cachedResult, responseTime_1, result, responseTime, error_1, responseTime;
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[94]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().f[19]++;
        cov_1pnbn5pbao().s[95]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[36][0]++;
            cov_1pnbn5pbao().s[96]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[97]++;
            cacheKey = "skill_search:".concat(query.toLowerCase());
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[98]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[36][1]++;
            cov_1pnbn5pbao().s[99]++;
            _a.trys.push([1, 5,, 6]);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[100]++;
            return [4 /*yield*/, this.cacheService.get(cacheKey)];
          case 2:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[36][2]++;
            cov_1pnbn5pbao().s[101]++;
            cachedResult = _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[102]++;
            if (cachedResult) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().b[37][0]++;
              cov_1pnbn5pbao().s[103]++;
              responseTime_1 = Date.now() - startTime;
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[104]++;
              this.recordSkillSearchMetrics({
                query: query,
                resultCount: Array.isArray(cachedResult) ?
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[38][0]++, cachedResult.length) :
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[38][1]++, 0),
                responseTime: responseTime_1,
                cacheHit: true,
                userId: userId
              });
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[105]++;
              return [2 /*return*/, cachedResult];
            } else
            /* istanbul ignore next */
            {
              cov_1pnbn5pbao().b[37][1]++;
            }
            cov_1pnbn5pbao().s[106]++;
            return [4 /*yield*/, operation()];
          case 3:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[36][3]++;
            cov_1pnbn5pbao().s[107]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[108]++;
            responseTime = Date.now() - startTime;
            // Cache the result
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[109]++;
            return [4 /*yield*/, this.cacheService.set(cacheKey, result, {
              ttl: this.CACHE_TTL.SKILL_SEARCH,
              tags: ['skill_search']
            })];
          case 4:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[36][4]++;
            cov_1pnbn5pbao().s[110]++;
            // Cache the result
            _a.sent();
            // Record metrics
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[111]++;
            this.recordSkillSearchMetrics({
              query: query,
              resultCount:
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[39][0]++, result.length) ||
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[39][1]++, 0),
              responseTime: responseTime,
              cacheHit: false,
              userId: userId
            });
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[112]++;
            return [2 /*return*/, result];
          case 5:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[36][5]++;
            cov_1pnbn5pbao().s[113]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[114]++;
            responseTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[115]++;
            this.recordSkillSearchError(query, responseTime, error_1, userId);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[116]++;
            throw error_1;
          case 6:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[36][6]++;
            cov_1pnbn5pbao().s[117]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Monitor skill assessment submission performance
   */
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[118]++;
  SkillGapPerformanceMonitor.prototype.monitorSkillAssessment = function (assessments, operation, userId) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[20]++;
    cov_1pnbn5pbao().s[119]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[21]++;
      var startTime, result, responseTime, cacheKey, error_2, responseTime;
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[120]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().f[22]++;
        cov_1pnbn5pbao().s[121]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[40][0]++;
            cov_1pnbn5pbao().s[122]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[123]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[40][1]++;
            cov_1pnbn5pbao().s[124]++;
            _a.trys.push([1, 4,, 5]);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[125]++;
            return [4 /*yield*/, operation()];
          case 2:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[40][2]++;
            cov_1pnbn5pbao().s[126]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[127]++;
            responseTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[128]++;
            cacheKey = "user_assessments:".concat(userId);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[129]++;
            return [4 /*yield*/, this.cacheService.set(cacheKey, assessments, {
              ttl: this.CACHE_TTL.USER_ASSESSMENTS,
              tags: ['user_assessments', userId]
            })];
          case 3:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[40][3]++;
            cov_1pnbn5pbao().s[130]++;
            _a.sent();
            // Record metrics
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[131]++;
            this.recordSkillAssessmentMetrics({
              assessmentCount: assessments.length,
              responseTime: responseTime,
              success: true,
              userId: userId
            });
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[132]++;
            return [2 /*return*/, result];
          case 4:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[40][4]++;
            cov_1pnbn5pbao().s[133]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[134]++;
            responseTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[135]++;
            this.recordSkillAssessmentMetrics({
              assessmentCount: assessments.length,
              responseTime: responseTime,
              success: false,
              userId: userId
            });
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[136]++;
            throw error_2;
          case 5:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[40][5]++;
            cov_1pnbn5pbao().s[137]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Monitor comprehensive skill analysis performance
   */
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[138]++;
  SkillGapPerformanceMonitor.prototype.monitorSkillAnalysis = function (analysisRequest, operation, userId) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[23]++;
    cov_1pnbn5pbao().s[139]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[24]++;
      var startTime, analysisId, cacheKey, cachedResult, responseTime_2, result, responseTime, error_3, responseTime;
      var _a, _b, _c;
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[140]++;
      return __generator(this, function (_d) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().f[25]++;
        cov_1pnbn5pbao().s[141]++;
        switch (_d.label) {
          case 0:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[41][0]++;
            cov_1pnbn5pbao().s[142]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[143]++;
            analysisId = this.generateAnalysisId(analysisRequest, userId);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[144]++;
            cacheKey = "skill_analysis:".concat(analysisId);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[145]++;
            _d.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[41][1]++;
            cov_1pnbn5pbao().s[146]++;
            _d.trys.push([1, 5,, 6]);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[147]++;
            return [4 /*yield*/, this.cacheService.get(cacheKey)];
          case 2:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[41][2]++;
            cov_1pnbn5pbao().s[148]++;
            cachedResult = _d.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[149]++;
            if (cachedResult) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().b[42][0]++;
              cov_1pnbn5pbao().s[150]++;
              responseTime_2 = Date.now() - startTime;
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[151]++;
              this.recordSkillAnalysisMetrics({
                analysisType: 'comprehensive',
                skillCount:
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[43][0]++,
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[45][0]++, (_a = analysisRequest.currentSkills) === null) ||
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[45][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[44][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[44][1]++, _a.length)) ||
                /* istanbul ignore next */
                (cov_1pnbn5pbao().b[43][1]++, 0),
                responseTime: responseTime_2,
                success: true,
                cacheHit: true,
                userId: userId
              });
              /* istanbul ignore next */
              cov_1pnbn5pbao().s[152]++;
              return [2 /*return*/, __assign(__assign({}, cachedResult), {
                cached: true
              })];
            } else
            /* istanbul ignore next */
            {
              cov_1pnbn5pbao().b[42][1]++;
            }
            cov_1pnbn5pbao().s[153]++;
            return [4 /*yield*/, operation()];
          case 3:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[41][3]++;
            cov_1pnbn5pbao().s[154]++;
            result = _d.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[155]++;
            responseTime = Date.now() - startTime;
            // Cache the result
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[156]++;
            return [4 /*yield*/, this.cacheService.set(cacheKey, result, {
              ttl: this.CACHE_TTL.SKILL_ANALYSIS,
              tags: ['skill_analysis', userId]
            })];
          case 4:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[41][4]++;
            cov_1pnbn5pbao().s[157]++;
            // Cache the result
            _d.sent();
            // Record metrics
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[158]++;
            this.recordSkillAnalysisMetrics({
              analysisType: 'comprehensive',
              skillCount:
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[46][0]++,
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[48][0]++, (_b = analysisRequest.currentSkills) === null) ||
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[48][1]++, _b === void 0) ?
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[47][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[47][1]++, _b.length)) ||
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[46][1]++, 0),
              responseTime: responseTime,
              success: true,
              cacheHit: false,
              userId: userId
            });
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[159]++;
            return [2 /*return*/, result];
          case 5:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[41][5]++;
            cov_1pnbn5pbao().s[160]++;
            error_3 = _d.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[161]++;
            responseTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[162]++;
            this.recordSkillAnalysisMetrics({
              analysisType: 'comprehensive',
              skillCount:
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[49][0]++,
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[51][0]++, (_c = analysisRequest.currentSkills) === null) ||
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[51][1]++, _c === void 0) ?
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[50][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[50][1]++, _c.length)) ||
              /* istanbul ignore next */
              (cov_1pnbn5pbao().b[49][1]++, 0),
              responseTime: responseTime,
              success: false,
              cacheHit: false,
              userId: userId,
              error: error_3.message
            });
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[163]++;
            throw error_3;
          case 6:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[41][6]++;
            cov_1pnbn5pbao().s[164]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Warm cache with popular skills
   */
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[165]++;
  SkillGapPerformanceMonitor.prototype.warmPopularSkillsCache = function () {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[26]++;
    cov_1pnbn5pbao().s[166]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[27]++;
      var popularSkills, warmingPromises;
      var _this =
      /* istanbul ignore next */
      (cov_1pnbn5pbao().s[167]++, this);
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[168]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().f[28]++;
        cov_1pnbn5pbao().s[169]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[52][0]++;
            cov_1pnbn5pbao().s[170]++;
            popularSkills = ['JavaScript', 'Python', 'React', 'Node.js', 'TypeScript', 'Java', 'C++', 'SQL', 'AWS', 'Docker'];
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[171]++;
            warmingPromises = popularSkills.map(function (skill) {
              /* istanbul ignore next */
              cov_1pnbn5pbao().f[29]++;
              cov_1pnbn5pbao().s[172]++;
              return __awaiter(_this, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_1pnbn5pbao().f[30]++;
                var cacheKey, skillData;
                /* istanbul ignore next */
                cov_1pnbn5pbao().s[173]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_1pnbn5pbao().f[31]++;
                  cov_1pnbn5pbao().s[174]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_1pnbn5pbao().b[53][0]++;
                      cov_1pnbn5pbao().s[175]++;
                      cacheKey = "skill_data:".concat(skill.toLowerCase());
                      /* istanbul ignore next */
                      cov_1pnbn5pbao().s[176]++;
                      skillData = {
                        name: skill,
                        popular: true,
                        warmedAt: Date.now()
                      };
                      /* istanbul ignore next */
                      cov_1pnbn5pbao().s[177]++;
                      return [4 /*yield*/, this.cacheService.set(cacheKey, skillData, {
                        ttl: this.CACHE_TTL.POPULAR_SKILLS,
                        tags: ['popular_skills']
                      })];
                    case 1:
                      /* istanbul ignore next */
                      cov_1pnbn5pbao().b[53][1]++;
                      cov_1pnbn5pbao().s[178]++;
                      _a.sent();
                      /* istanbul ignore next */
                      cov_1pnbn5pbao().s[179]++;
                      return [2 /*return*/];
                  }
                });
              });
            });
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[180]++;
            return [4 /*yield*/, Promise.all(warmingPromises)];
          case 1:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[52][1]++;
            cov_1pnbn5pbao().s[181]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[182]++;
            console.log("\uD83D\uDD25 Warmed cache for ".concat(popularSkills.length, " popular skills"));
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[183]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Get performance status for skill gap analyzer
   */
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[184]++;
  SkillGapPerformanceMonitor.prototype.getPerformanceStatus = function () {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[32]++;
    cov_1pnbn5pbao().s[185]++;
    if (!this.performanceMonitor) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[54][0]++;
      cov_1pnbn5pbao().s[186]++;
      return {
        isHealthy: true,
        message: 'Monitoring disabled in development'
      };
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[54][1]++;
    }
    cov_1pnbn5pbao().s[187]++;
    return this.performanceMonitor.getPerformanceStatus();
  };
  /**
   * Get AI service metrics
   */
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[188]++;
  SkillGapPerformanceMonitor.prototype.getAIServiceMetrics = function () {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[33]++;
    cov_1pnbn5pbao().s[189]++;
    if (!this.aiServiceMonitor) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[55][0]++;
      cov_1pnbn5pbao().s[190]++;
      return {
        message: 'AI monitoring disabled in development'
      };
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[55][1]++;
    }
    cov_1pnbn5pbao().s[191]++;
    return this.aiServiceMonitor.getMetrics();
  };
  /**
   * Perform health check
   */
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[192]++;
  SkillGapPerformanceMonitor.prototype.healthCheck = function () {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[34]++;
    cov_1pnbn5pbao().s[193]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[35]++;
      var cacheHealthy, performanceStatus, error_4;
      /* istanbul ignore next */
      cov_1pnbn5pbao().s[194]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1pnbn5pbao().f[36]++;
        cov_1pnbn5pbao().s[195]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[56][0]++;
            cov_1pnbn5pbao().s[196]++;
            _a.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[197]++;
            return [4 /*yield*/, this.cacheService.healthCheck()];
          case 1:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[56][1]++;
            cov_1pnbn5pbao().s[198]++;
            cacheHealthy = _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[199]++;
            performanceStatus = this.getPerformanceStatus();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[200]++;
            return [2 /*return*/,
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[57][0]++, cacheHealthy) &&
            /* istanbul ignore next */
            (cov_1pnbn5pbao().b[57][1]++, performanceStatus.isHealthy)];
          case 2:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[56][2]++;
            cov_1pnbn5pbao().s[201]++;
            error_4 = _a.sent();
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[202]++;
            console.error('Health check failed:', error_4);
            /* istanbul ignore next */
            cov_1pnbn5pbao().s[203]++;
            return [2 /*return*/, false];
          case 3:
            /* istanbul ignore next */
            cov_1pnbn5pbao().b[56][3]++;
            cov_1pnbn5pbao().s[204]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Private helper methods
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[205]++;
  SkillGapPerformanceMonitor.prototype.recordSkillSearchMetrics = function (metrics) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[37]++;
    cov_1pnbn5pbao().s[206]++;
    if (!this.performanceMonitor) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[58][0]++;
      cov_1pnbn5pbao().s[207]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[58][1]++;
    } // Skip in development
    cov_1pnbn5pbao().s[208]++;
    this.performanceMonitor.recordOperation('skill_search', metrics.responseTime, true);
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[209]++;
    if (metrics.responseTime > this.THRESHOLDS.SKILL_SEARCH_WARNING) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[59][0]++;
      cov_1pnbn5pbao().s[210]++;
      console.warn("\u26A0\uFE0F Slow skill search: ".concat(metrics.query, " took ").concat(metrics.responseTime, "ms"));
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[59][1]++;
    }
  };
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[211]++;
  SkillGapPerformanceMonitor.prototype.recordSkillSearchError = function (query, responseTime, error, userId) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[38]++;
    cov_1pnbn5pbao().s[212]++;
    if (!this.performanceMonitor) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[60][0]++;
      cov_1pnbn5pbao().s[213]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[60][1]++;
    } // Skip in development
    cov_1pnbn5pbao().s[214]++;
    this.performanceMonitor.recordOperation('skill_search', responseTime, false);
  };
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[215]++;
  SkillGapPerformanceMonitor.prototype.recordSkillAssessmentMetrics = function (metrics) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[39]++;
    cov_1pnbn5pbao().s[216]++;
    if (!this.aiServiceMonitor) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[61][0]++;
      cov_1pnbn5pbao().s[217]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[61][1]++;
    } // Skip in development
    cov_1pnbn5pbao().s[218]++;
    this.aiServiceMonitor.recordOperation('skill_assessment_submit', metrics.responseTime, metrics.success, false, metrics.userId);
  };
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[219]++;
  SkillGapPerformanceMonitor.prototype.recordSkillAnalysisMetrics = function (metrics) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[40]++;
    cov_1pnbn5pbao().s[220]++;
    if (!this.aiServiceMonitor) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[62][0]++;
      cov_1pnbn5pbao().s[221]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[62][1]++;
    } // Skip in development
    cov_1pnbn5pbao().s[222]++;
    this.aiServiceMonitor.recordOperation('comprehensive_skill_analysis', metrics.responseTime, metrics.success, metrics.cacheHit, metrics.userId, metrics.error);
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[223]++;
    if (metrics.responseTime > this.THRESHOLDS.ANALYSIS_WARNING) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().b[63][0]++;
      cov_1pnbn5pbao().s[224]++;
      console.warn("\u26A0\uFE0F Slow skill analysis for user ".concat(metrics.userId, ": ").concat(metrics.responseTime, "ms"));
    } else
    /* istanbul ignore next */
    {
      cov_1pnbn5pbao().b[63][1]++;
    }
  };
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[225]++;
  SkillGapPerformanceMonitor.prototype.generateAnalysisId = function (analysisRequest, userId) {
    /* istanbul ignore next */
    cov_1pnbn5pbao().f[41]++;
    var _a, _b, _c;
    var skillNames =
    /* istanbul ignore next */
    (cov_1pnbn5pbao().s[226]++,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[64][0]++,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[66][0]++, (_a = analysisRequest.currentSkills) === null) ||
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[66][1]++, _a === void 0) ?
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[65][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[65][1]++, _a.map(function (s) {
      /* istanbul ignore next */
      cov_1pnbn5pbao().f[42]++;
      cov_1pnbn5pbao().s[227]++;
      return s.skillName;
    }).sort().join(','))) ||
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[64][1]++, ''));
    var careerPath =
    /* istanbul ignore next */
    (cov_1pnbn5pbao().s[228]++,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[67][0]++,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[69][0]++, (_b = analysisRequest.targetCareerPath) === null) ||
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[69][1]++, _b === void 0) ?
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[68][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[68][1]++, _b.careerPathName)) ||
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[67][1]++, ''));
    var level =
    /* istanbul ignore next */
    (cov_1pnbn5pbao().s[229]++,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[70][0]++,
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[72][0]++, (_c = analysisRequest.targetCareerPath) === null) ||
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[72][1]++, _c === void 0) ?
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[71][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[71][1]++, _c.targetLevel)) ||
    /* istanbul ignore next */
    (cov_1pnbn5pbao().b[70][1]++, ''));
    var hash =
    /* istanbul ignore next */
    (cov_1pnbn5pbao().s[230]++, Buffer.from("".concat(userId, ":").concat(skillNames, ":").concat(careerPath, ":").concat(level)).toString('base64'));
    /* istanbul ignore next */
    cov_1pnbn5pbao().s[231]++;
    return hash.substring(0, 16);
  };
  /* istanbul ignore next */
  cov_1pnbn5pbao().s[232]++;
  return SkillGapPerformanceMonitor;
}());
/* istanbul ignore next */
cov_1pnbn5pbao().s[233]++;
exports.SkillGapPerformanceMonitor = SkillGapPerformanceMonitor;
// Export singleton instance
/* istanbul ignore next */
cov_1pnbn5pbao().s[234]++;
exports.skillGapPerformanceMonitor = new SkillGapPerformanceMonitor();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMXBuYm41cGJhbyIsInBhdGgiLCJoYXNoIiwiZ2xvYmFsIiwiRnVuY3Rpb24iLCJnY3YiLCJjb3ZlcmFnZURhdGEiLCJzdGF0ZW1lbnRNYXAiLCJzdGFydCIsImxpbmUiLCJjb2x1bW4iLCJlbmQiLCJmbk1hcCIsIm5hbWUiLCJkZWNsIiwibG9jIiwiYnJhbmNoTWFwIiwidHlwZSIsImxvY2F0aW9ucyIsInVuZGVmaW5lZCIsInMiLCJmIiwiYiIsImlucHV0U291cmNlTWFwIiwiZmlsZSIsIm1hcHBpbmdzIiwibmFtZXMiLCJzb3VyY2VzIiwic291cmNlc0NvbnRlbnQiLCJ2ZXJzaW9uIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsInBlcmZvcm1hbmNlX21vbml0b3JpbmdfMSIsInJlcXVpcmUiLCJhaV9zZXJ2aWNlX21vbml0b3JfMSIsImNvbnNvbGlkYXRlZF9jYWNoZV9zZXJ2aWNlXzEiLCJTa2lsbEdhcFBlcmZvcm1hbmNlTW9uaXRvciIsIkNBQ0hFX1RUTCIsIlNLSUxMX1NFQVJDSCIsIlVTRVJfQVNTRVNTTUVOVFMiLCJTS0lMTF9BTkFMWVNJUyIsIlBPUFVMQVJfU0tJTExTIiwiTUFSS0VUX0RBVEEiLCJUSFJFU0hPTERTIiwiU0tJTExfU0VBUkNIX1dBUk5JTkciLCJTS0lMTF9TRUFSQ0hfQ1JJVElDQUwiLCJBTkFMWVNJU19XQVJOSU5HIiwiQU5BTFlTSVNfQ1JJVElDQUwiLCJDQUNIRV9ISVRfUkFURV9XQVJOSU5HIiwiQ0FDSEVfSElUX1JBVEVfQ1JJVElDQUwiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJwZXJmb3JtYW5jZU1vbml0b3IiLCJQZXJmb3JtYW5jZU1vbml0b3IiLCJhaVNlcnZpY2VNb25pdG9yIiwiQUlTZXJ2aWNlTW9uaXRvciIsImdldEluc3RhbmNlIiwiY2FjaGVTZXJ2aWNlIiwiY29uc29saWRhdGVkQ2FjaGUiLCJwcm90b3R5cGUiLCJtb25pdG9yU2tpbGxTZWFyY2giLCJxdWVyeSIsIm9wZXJhdGlvbiIsInVzZXJJZCIsIlByb21pc2UiLCJzdGFydFRpbWUiLCJEYXRlIiwibm93IiwiY2FjaGVLZXkiLCJjb25jYXQiLCJ0b0xvd2VyQ2FzZSIsImdldCIsImNhY2hlZFJlc3VsdCIsIl9hIiwic2VudCIsInJlc3BvbnNlVGltZV8xIiwicmVjb3JkU2tpbGxTZWFyY2hNZXRyaWNzIiwicmVzdWx0Q291bnQiLCJBcnJheSIsImlzQXJyYXkiLCJsZW5ndGgiLCJyZXNwb25zZVRpbWUiLCJjYWNoZUhpdCIsInJlc3VsdCIsInNldCIsInR0bCIsInRhZ3MiLCJyZWNvcmRTa2lsbFNlYXJjaEVycm9yIiwiZXJyb3JfMSIsIm1vbml0b3JTa2lsbEFzc2Vzc21lbnQiLCJhc3Nlc3NtZW50cyIsInJlY29yZFNraWxsQXNzZXNzbWVudE1ldHJpY3MiLCJhc3Nlc3NtZW50Q291bnQiLCJzdWNjZXNzIiwiZXJyb3JfMiIsIm1vbml0b3JTa2lsbEFuYWx5c2lzIiwiYW5hbHlzaXNSZXF1ZXN0IiwiYW5hbHlzaXNJZCIsImdlbmVyYXRlQW5hbHlzaXNJZCIsIl9kIiwicmVzcG9uc2VUaW1lXzIiLCJyZWNvcmRTa2lsbEFuYWx5c2lzTWV0cmljcyIsImFuYWx5c2lzVHlwZSIsInNraWxsQ291bnQiLCJjdXJyZW50U2tpbGxzIiwiX19hc3NpZ24iLCJjYWNoZWQiLCJfYiIsIl9jIiwiZXJyb3IiLCJlcnJvcl8zIiwibWVzc2FnZSIsIndhcm1Qb3B1bGFyU2tpbGxzQ2FjaGUiLCJwb3B1bGFyU2tpbGxzIiwid2FybWluZ1Byb21pc2VzIiwibWFwIiwic2tpbGwiLCJfX2F3YWl0ZXIiLCJfdGhpcyIsInNraWxsRGF0YSIsInBvcHVsYXIiLCJ3YXJtZWRBdCIsImFsbCIsImNvbnNvbGUiLCJsb2ciLCJnZXRQZXJmb3JtYW5jZVN0YXR1cyIsImlzSGVhbHRoeSIsImdldEFJU2VydmljZU1ldHJpY3MiLCJnZXRNZXRyaWNzIiwiaGVhbHRoQ2hlY2siLCJjYWNoZUhlYWx0aHkiLCJwZXJmb3JtYW5jZVN0YXR1cyIsImVycm9yXzQiLCJtZXRyaWNzIiwicmVjb3JkT3BlcmF0aW9uIiwid2FybiIsInNraWxsTmFtZXMiLCJza2lsbE5hbWUiLCJzb3J0Iiwiam9pbiIsImNhcmVlclBhdGgiLCJ0YXJnZXRDYXJlZXJQYXRoIiwiY2FyZWVyUGF0aE5hbWUiLCJsZXZlbCIsInRhcmdldExldmVsIiwiQnVmZmVyIiwiZnJvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIiwiZXhwb3J0cyIsInNraWxsR2FwUGVyZm9ybWFuY2VNb25pdG9yIl0sInNvdXJjZXMiOlsiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9saWIvcGVyZm9ybWFuY2Uvc2tpbGwtZ2FwLXBlcmZvcm1hbmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUGVyZm9ybWFuY2UgbW9uaXRvcmluZyBhbmQgY2FjaGluZyBzdHJhdGVnaWVzIGZvciBTa2lsbCBHYXAgQW5hbHl6ZXJcbiAqIEltcGxlbWVudHMgY29tcHJlaGVuc2l2ZSBwZXJmb3JtYW5jZSB0cmFja2luZyBhbmQgb3B0aW1pemF0aW9uXG4gKi9cblxuaW1wb3J0IHsgUGVyZm9ybWFuY2VNb25pdG9yIH0gZnJvbSAnQC9saWIvcGVyZm9ybWFuY2UtbW9uaXRvcmluZyc7XG5pbXBvcnQgeyBBSVNlcnZpY2VNb25pdG9yIH0gZnJvbSAnQC9saWIvYWktc2VydmljZS1tb25pdG9yJztcbmltcG9ydCB7IGNvbnNvbGlkYXRlZENhY2hlIH0gZnJvbSAnQC9saWIvc2VydmljZXMvY29uc29saWRhdGVkLWNhY2hlLXNlcnZpY2UnO1xuXG5pbnRlcmZhY2UgU2tpbGxTZWFyY2hNZXRyaWNzIHtcbiAgcXVlcnk6IHN0cmluZztcbiAgcmVzdWx0Q291bnQ6IG51bWJlcjtcbiAgcmVzcG9uc2VUaW1lOiBudW1iZXI7XG4gIGNhY2hlSGl0OiBib29sZWFuO1xuICB1c2VySWQ/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBTa2lsbEFzc2Vzc21lbnRNZXRyaWNzIHtcbiAgYXNzZXNzbWVudENvdW50OiBudW1iZXI7XG4gIHJlc3BvbnNlVGltZTogbnVtYmVyO1xuICBzdWNjZXNzOiBib29sZWFuO1xuICB1c2VySWQ6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFNraWxsQW5hbHlzaXNNZXRyaWNzIHtcbiAgYW5hbHlzaXNUeXBlOiAnYmFzaWMnIHwgJ2NvbXByZWhlbnNpdmUnO1xuICBza2lsbENvdW50OiBudW1iZXI7XG4gIHJlc3BvbnNlVGltZTogbnVtYmVyO1xuICBzdWNjZXNzOiBib29sZWFuO1xuICBjYWNoZUhpdDogYm9vbGVhbjtcbiAgdXNlcklkOiBzdHJpbmc7XG4gIGVycm9yPzogc3RyaW5nO1xufVxuXG5leHBvcnQgY2xhc3MgU2tpbGxHYXBQZXJmb3JtYW5jZU1vbml0b3Ige1xuICBwcml2YXRlIHBlcmZvcm1hbmNlTW9uaXRvcjogUGVyZm9ybWFuY2VNb25pdG9yIHwgbnVsbDtcbiAgcHJpdmF0ZSBhaVNlcnZpY2VNb25pdG9yOiBBSVNlcnZpY2VNb25pdG9yIHwgbnVsbDtcbiAgcHJpdmF0ZSBjYWNoZVNlcnZpY2U6IHR5cGVvZiBjb25zb2xpZGF0ZWRDYWNoZTtcblxuICAvLyBDYWNoZSBUVEwgY29uZmlndXJhdGlvbnMgKGluIG1pbGxpc2Vjb25kcylcbiAgcHJpdmF0ZSByZWFkb25seSBDQUNIRV9UVEwgPSB7XG4gICAgU0tJTExfU0VBUkNIOiAzMDAwMDAsICAgICAgICAvLyA1IG1pbnV0ZXNcbiAgICBVU0VSX0FTU0VTU01FTlRTOiAzNjAwMDAwLCAgIC8vIDEgaG91clxuICAgIFNLSUxMX0FOQUxZU0lTOiAxODAwMDAwLCAgICAgLy8gMzAgbWludXRlc1xuICAgIFBPUFVMQVJfU0tJTExTOiA3MjAwMDAwLCAgICAgLy8gMiBob3Vyc1xuICAgIE1BUktFVF9EQVRBOiA4NjQwMDAwMCwgICAgICAgLy8gMjQgaG91cnNcbiAgfTtcblxuICAvLyBQZXJmb3JtYW5jZSB0aHJlc2hvbGRzXG4gIHByaXZhdGUgcmVhZG9ubHkgVEhSRVNIT0xEUyA9IHtcbiAgICBTS0lMTF9TRUFSQ0hfV0FSTklORzogMTAwMCwgICAgICAvLyAxIHNlY29uZFxuICAgIFNLSUxMX1NFQVJDSF9DUklUSUNBTDogMzAwMCwgICAgIC8vIDMgc2Vjb25kc1xuICAgIEFOQUxZU0lTX1dBUk5JTkc6IDUwMDAsICAgICAgICAgIC8vIDUgc2Vjb25kc1xuICAgIEFOQUxZU0lTX0NSSVRJQ0FMOiAxNTAwMCwgICAgICAgIC8vIDE1IHNlY29uZHNcbiAgICBDQUNIRV9ISVRfUkFURV9XQVJOSU5HOiA3MCwgICAgICAvLyA3MCVcbiAgICBDQUNIRV9ISVRfUkFURV9DUklUSUNBTDogNTAsICAgICAvLyA1MCVcbiAgfTtcblxuICBjb25zdHJ1Y3RvcigpIHtcbiAgICAvLyBPbmx5IGluaXRpYWxpemUgbW9uaXRvcmluZyBpbiBwcm9kdWN0aW9uXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgICAgIHRoaXMucGVyZm9ybWFuY2VNb25pdG9yID0gbmV3IFBlcmZvcm1hbmNlTW9uaXRvcigpO1xuICAgICAgdGhpcy5haVNlcnZpY2VNb25pdG9yID0gQUlTZXJ2aWNlTW9uaXRvci5nZXRJbnN0YW5jZSgpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnBlcmZvcm1hbmNlTW9uaXRvciA9IG51bGw7XG4gICAgICB0aGlzLmFpU2VydmljZU1vbml0b3IgPSBudWxsO1xuICAgIH1cbiAgICB0aGlzLmNhY2hlU2VydmljZSA9IGNvbnNvbGlkYXRlZENhY2hlO1xuICB9XG5cbiAgLyoqXG4gICAqIE1vbml0b3Igc2tpbGwgc2VhcmNoIHBlcmZvcm1hbmNlXG4gICAqL1xuICBhc3luYyBtb25pdG9yU2tpbGxTZWFyY2goXG4gICAgcXVlcnk6IHN0cmluZyxcbiAgICBvcGVyYXRpb246ICgpID0+IFByb21pc2U8YW55PixcbiAgICB1c2VySWQ/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTxhbnk+IHtcbiAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgIGNvbnN0IGNhY2hlS2V5ID0gYHNraWxsX3NlYXJjaDoke3F1ZXJ5LnRvTG93ZXJDYXNlKCl9YDtcbiAgICBcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgY2FjaGUgZmlyc3RcbiAgICAgIGNvbnN0IGNhY2hlZFJlc3VsdCA9IGF3YWl0IHRoaXMuY2FjaGVTZXJ2aWNlLmdldDxhbnk+KGNhY2hlS2V5KTtcbiAgICAgIGlmIChjYWNoZWRSZXN1bHQpIHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2VUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgICAgdGhpcy5yZWNvcmRTa2lsbFNlYXJjaE1ldHJpY3Moe1xuICAgICAgICAgIHF1ZXJ5LFxuICAgICAgICAgIHJlc3VsdENvdW50OiBBcnJheS5pc0FycmF5KGNhY2hlZFJlc3VsdCkgPyBjYWNoZWRSZXN1bHQubGVuZ3RoIDogMCxcbiAgICAgICAgICByZXNwb25zZVRpbWUsXG4gICAgICAgICAgY2FjaGVIaXQ6IHRydWUsXG4gICAgICAgICAgdXNlcklkXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gY2FjaGVkUmVzdWx0O1xuICAgICAgfVxuXG4gICAgICAvLyBFeGVjdXRlIG9wZXJhdGlvblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgb3BlcmF0aW9uKCk7XG4gICAgICBjb25zdCByZXNwb25zZVRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuXG4gICAgICAvLyBDYWNoZSB0aGUgcmVzdWx0XG4gICAgICBhd2FpdCB0aGlzLmNhY2hlU2VydmljZS5zZXQoY2FjaGVLZXksIHJlc3VsdCwgeyB0dGw6IHRoaXMuQ0FDSEVfVFRMLlNLSUxMX1NFQVJDSCwgdGFnczogWydza2lsbF9zZWFyY2gnXSB9KTtcblxuICAgICAgLy8gUmVjb3JkIG1ldHJpY3NcbiAgICAgIHRoaXMucmVjb3JkU2tpbGxTZWFyY2hNZXRyaWNzKHtcbiAgICAgICAgcXVlcnksXG4gICAgICAgIHJlc3VsdENvdW50OiByZXN1bHQubGVuZ3RoIHx8IDAsXG4gICAgICAgIHJlc3BvbnNlVGltZSxcbiAgICAgICAgY2FjaGVIaXQ6IGZhbHNlLFxuICAgICAgICB1c2VySWRcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCByZXNwb25zZVRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuICAgICAgdGhpcy5yZWNvcmRTa2lsbFNlYXJjaEVycm9yKHF1ZXJ5LCByZXNwb25zZVRpbWUsIGVycm9yIGFzIEVycm9yLCB1c2VySWQpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIE1vbml0b3Igc2tpbGwgYXNzZXNzbWVudCBzdWJtaXNzaW9uIHBlcmZvcm1hbmNlXG4gICAqL1xuICBhc3luYyBtb25pdG9yU2tpbGxBc3Nlc3NtZW50KFxuICAgIGFzc2Vzc21lbnRzOiBhbnlbXSxcbiAgICBvcGVyYXRpb246ICgpID0+IFByb21pc2U8YW55PixcbiAgICB1c2VySWQ6IHN0cmluZ1xuICApOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG9wZXJhdGlvbigpO1xuICAgICAgY29uc3QgcmVzcG9uc2VUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcblxuICAgICAgLy8gQ2FjaGUgdXNlciBhc3Nlc3NtZW50c1xuICAgICAgY29uc3QgY2FjaGVLZXkgPSBgdXNlcl9hc3Nlc3NtZW50czoke3VzZXJJZH1gO1xuICAgICAgYXdhaXQgdGhpcy5jYWNoZVNlcnZpY2Uuc2V0KGNhY2hlS2V5LCBhc3Nlc3NtZW50cywgeyB0dGw6IHRoaXMuQ0FDSEVfVFRMLlVTRVJfQVNTRVNTTUVOVFMsIHRhZ3M6IFsndXNlcl9hc3Nlc3NtZW50cycsIHVzZXJJZF0gfSk7XG5cbiAgICAgIC8vIFJlY29yZCBtZXRyaWNzXG4gICAgICB0aGlzLnJlY29yZFNraWxsQXNzZXNzbWVudE1ldHJpY3Moe1xuICAgICAgICBhc3Nlc3NtZW50Q291bnQ6IGFzc2Vzc21lbnRzLmxlbmd0aCxcbiAgICAgICAgcmVzcG9uc2VUaW1lLFxuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICB1c2VySWRcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCByZXNwb25zZVRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuICAgICAgdGhpcy5yZWNvcmRTa2lsbEFzc2Vzc21lbnRNZXRyaWNzKHtcbiAgICAgICAgYXNzZXNzbWVudENvdW50OiBhc3Nlc3NtZW50cy5sZW5ndGgsXG4gICAgICAgIHJlc3BvbnNlVGltZSxcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIHVzZXJJZFxuICAgICAgfSk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogTW9uaXRvciBjb21wcmVoZW5zaXZlIHNraWxsIGFuYWx5c2lzIHBlcmZvcm1hbmNlXG4gICAqL1xuICBhc3luYyBtb25pdG9yU2tpbGxBbmFseXNpcyhcbiAgICBhbmFseXNpc1JlcXVlc3Q6IGFueSxcbiAgICBvcGVyYXRpb246ICgpID0+IFByb21pc2U8YW55PixcbiAgICB1c2VySWQ6IHN0cmluZ1xuICApOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgY29uc3QgYW5hbHlzaXNJZCA9IHRoaXMuZ2VuZXJhdGVBbmFseXNpc0lkKGFuYWx5c2lzUmVxdWVzdCwgdXNlcklkKTtcbiAgICBjb25zdCBjYWNoZUtleSA9IGBza2lsbF9hbmFseXNpczoke2FuYWx5c2lzSWR9YDtcbiAgICBcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgY2FjaGUgZmlyc3RcbiAgICAgIGNvbnN0IGNhY2hlZFJlc3VsdCA9IGF3YWl0IHRoaXMuY2FjaGVTZXJ2aWNlLmdldDxhbnk+KGNhY2hlS2V5KTtcbiAgICAgIGlmIChjYWNoZWRSZXN1bHQpIHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2VUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgICAgdGhpcy5yZWNvcmRTa2lsbEFuYWx5c2lzTWV0cmljcyh7XG4gICAgICAgICAgYW5hbHlzaXNUeXBlOiAnY29tcHJlaGVuc2l2ZScsXG4gICAgICAgICAgc2tpbGxDb3VudDogYW5hbHlzaXNSZXF1ZXN0LmN1cnJlbnRTa2lsbHM/Lmxlbmd0aCB8fCAwLFxuICAgICAgICAgIHJlc3BvbnNlVGltZSxcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIGNhY2hlSGl0OiB0cnVlLFxuICAgICAgICAgIHVzZXJJZFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHsgLi4uY2FjaGVkUmVzdWx0LCBjYWNoZWQ6IHRydWUgfTtcbiAgICAgIH1cblxuICAgICAgLy8gRXhlY3V0ZSBvcGVyYXRpb25cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG9wZXJhdGlvbigpO1xuICAgICAgY29uc3QgcmVzcG9uc2VUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcblxuICAgICAgLy8gQ2FjaGUgdGhlIHJlc3VsdFxuICAgICAgYXdhaXQgdGhpcy5jYWNoZVNlcnZpY2Uuc2V0KGNhY2hlS2V5LCByZXN1bHQsIHsgdHRsOiB0aGlzLkNBQ0hFX1RUTC5TS0lMTF9BTkFMWVNJUywgdGFnczogWydza2lsbF9hbmFseXNpcycsIHVzZXJJZF0gfSk7XG5cbiAgICAgIC8vIFJlY29yZCBtZXRyaWNzXG4gICAgICB0aGlzLnJlY29yZFNraWxsQW5hbHlzaXNNZXRyaWNzKHtcbiAgICAgICAgYW5hbHlzaXNUeXBlOiAnY29tcHJlaGVuc2l2ZScsXG4gICAgICAgIHNraWxsQ291bnQ6IGFuYWx5c2lzUmVxdWVzdC5jdXJyZW50U2tpbGxzPy5sZW5ndGggfHwgMCxcbiAgICAgICAgcmVzcG9uc2VUaW1lLFxuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBjYWNoZUhpdDogZmFsc2UsXG4gICAgICAgIHVzZXJJZFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgICB0aGlzLnJlY29yZFNraWxsQW5hbHlzaXNNZXRyaWNzKHtcbiAgICAgICAgYW5hbHlzaXNUeXBlOiAnY29tcHJlaGVuc2l2ZScsXG4gICAgICAgIHNraWxsQ291bnQ6IGFuYWx5c2lzUmVxdWVzdC5jdXJyZW50U2tpbGxzPy5sZW5ndGggfHwgMCxcbiAgICAgICAgcmVzcG9uc2VUaW1lLFxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgY2FjaGVIaXQ6IGZhbHNlLFxuICAgICAgICB1c2VySWQsXG4gICAgICAgIGVycm9yOiAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2VcbiAgICAgIH0pO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFdhcm0gY2FjaGUgd2l0aCBwb3B1bGFyIHNraWxsc1xuICAgKi9cbiAgYXN5bmMgd2FybVBvcHVsYXJTa2lsbHNDYWNoZSgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCBwb3B1bGFyU2tpbGxzID0gW1xuICAgICAgJ0phdmFTY3JpcHQnLCAnUHl0aG9uJywgJ1JlYWN0JywgJ05vZGUuanMnLCAnVHlwZVNjcmlwdCcsXG4gICAgICAnSmF2YScsICdDKysnLCAnU1FMJywgJ0FXUycsICdEb2NrZXInXG4gICAgXTtcblxuICAgIGNvbnN0IHdhcm1pbmdQcm9taXNlcyA9IHBvcHVsYXJTa2lsbHMubWFwKGFzeW5jIChza2lsbCkgPT4ge1xuICAgICAgY29uc3QgY2FjaGVLZXkgPSBgc2tpbGxfZGF0YToke3NraWxsLnRvTG93ZXJDYXNlKCl9YDtcbiAgICAgIGNvbnN0IHNraWxsRGF0YSA9IHtcbiAgICAgICAgbmFtZTogc2tpbGwsXG4gICAgICAgIHBvcHVsYXI6IHRydWUsXG4gICAgICAgIHdhcm1lZEF0OiBEYXRlLm5vdygpXG4gICAgICB9O1xuICAgICAgYXdhaXQgdGhpcy5jYWNoZVNlcnZpY2Uuc2V0KGNhY2hlS2V5LCBza2lsbERhdGEsIHsgdHRsOiB0aGlzLkNBQ0hFX1RUTC5QT1BVTEFSX1NLSUxMUywgdGFnczogWydwb3B1bGFyX3NraWxscyddIH0pO1xuICAgIH0pO1xuXG4gICAgYXdhaXQgUHJvbWlzZS5hbGwod2FybWluZ1Byb21pc2VzKTtcbiAgICBjb25zb2xlLmxvZyhg8J+UpSBXYXJtZWQgY2FjaGUgZm9yICR7cG9wdWxhclNraWxscy5sZW5ndGh9IHBvcHVsYXIgc2tpbGxzYCk7XG4gIH1cblxuICAvKipcbiAgICogR2V0IHBlcmZvcm1hbmNlIHN0YXR1cyBmb3Igc2tpbGwgZ2FwIGFuYWx5emVyXG4gICAqL1xuICBnZXRQZXJmb3JtYW5jZVN0YXR1cygpOiBhbnkge1xuICAgIGlmICghdGhpcy5wZXJmb3JtYW5jZU1vbml0b3IpIHJldHVybiB7IGlzSGVhbHRoeTogdHJ1ZSwgbWVzc2FnZTogJ01vbml0b3JpbmcgZGlzYWJsZWQgaW4gZGV2ZWxvcG1lbnQnIH07XG4gICAgcmV0dXJuIHRoaXMucGVyZm9ybWFuY2VNb25pdG9yLmdldFBlcmZvcm1hbmNlU3RhdHVzKCk7XG4gIH1cblxuICAvKipcbiAgICogR2V0IEFJIHNlcnZpY2UgbWV0cmljc1xuICAgKi9cbiAgZ2V0QUlTZXJ2aWNlTWV0cmljcygpOiBhbnkge1xuICAgIGlmICghdGhpcy5haVNlcnZpY2VNb25pdG9yKSByZXR1cm4geyBtZXNzYWdlOiAnQUkgbW9uaXRvcmluZyBkaXNhYmxlZCBpbiBkZXZlbG9wbWVudCcgfTtcbiAgICByZXR1cm4gdGhpcy5haVNlcnZpY2VNb25pdG9yLmdldE1ldHJpY3MoKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBQZXJmb3JtIGhlYWx0aCBjaGVja1xuICAgKi9cbiAgYXN5bmMgaGVhbHRoQ2hlY2soKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhY2hlSGVhbHRoeSA9IGF3YWl0IHRoaXMuY2FjaGVTZXJ2aWNlLmhlYWx0aENoZWNrKCk7XG4gICAgICBjb25zdCBwZXJmb3JtYW5jZVN0YXR1cyA9IHRoaXMuZ2V0UGVyZm9ybWFuY2VTdGF0dXMoKTtcblxuICAgICAgcmV0dXJuIGNhY2hlSGVhbHRoeSAmJiBwZXJmb3JtYW5jZVN0YXR1cy5pc0hlYWx0aHk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0hlYWx0aCBjaGVjayBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIC8vIFByaXZhdGUgaGVscGVyIG1ldGhvZHNcblxuICBwcml2YXRlIHJlY29yZFNraWxsU2VhcmNoTWV0cmljcyhtZXRyaWNzOiBTa2lsbFNlYXJjaE1ldHJpY3MpOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMucGVyZm9ybWFuY2VNb25pdG9yKSByZXR1cm47IC8vIFNraXAgaW4gZGV2ZWxvcG1lbnRcblxuICAgIHRoaXMucGVyZm9ybWFuY2VNb25pdG9yLnJlY29yZE9wZXJhdGlvbignc2tpbGxfc2VhcmNoJywgbWV0cmljcy5yZXNwb25zZVRpbWUsIHRydWUpO1xuXG4gICAgaWYgKG1ldHJpY3MucmVzcG9uc2VUaW1lID4gdGhpcy5USFJFU0hPTERTLlNLSUxMX1NFQVJDSF9XQVJOSU5HKSB7XG4gICAgICBjb25zb2xlLndhcm4oYOKaoO+4jyBTbG93IHNraWxsIHNlYXJjaDogJHttZXRyaWNzLnF1ZXJ5fSB0b29rICR7bWV0cmljcy5yZXNwb25zZVRpbWV9bXNgKTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHJlY29yZFNraWxsU2VhcmNoRXJyb3IocXVlcnk6IHN0cmluZywgcmVzcG9uc2VUaW1lOiBudW1iZXIsIGVycm9yOiBFcnJvciwgdXNlcklkPzogc3RyaW5nKTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLnBlcmZvcm1hbmNlTW9uaXRvcikgcmV0dXJuOyAvLyBTa2lwIGluIGRldmVsb3BtZW50XG5cbiAgICB0aGlzLnBlcmZvcm1hbmNlTW9uaXRvci5yZWNvcmRPcGVyYXRpb24oJ3NraWxsX3NlYXJjaCcsIHJlc3BvbnNlVGltZSwgZmFsc2UpO1xuICB9XG5cbiAgcHJpdmF0ZSByZWNvcmRTa2lsbEFzc2Vzc21lbnRNZXRyaWNzKG1ldHJpY3M6IFNraWxsQXNzZXNzbWVudE1ldHJpY3MpOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMuYWlTZXJ2aWNlTW9uaXRvcikgcmV0dXJuOyAvLyBTa2lwIGluIGRldmVsb3BtZW50XG5cbiAgICB0aGlzLmFpU2VydmljZU1vbml0b3IucmVjb3JkT3BlcmF0aW9uKFxuICAgICAgJ3NraWxsX2Fzc2Vzc21lbnRfc3VibWl0JyxcbiAgICAgIG1ldHJpY3MucmVzcG9uc2VUaW1lLFxuICAgICAgbWV0cmljcy5zdWNjZXNzLFxuICAgICAgZmFsc2UsXG4gICAgICBtZXRyaWNzLnVzZXJJZFxuICAgICk7XG4gIH1cblxuICBwcml2YXRlIHJlY29yZFNraWxsQW5hbHlzaXNNZXRyaWNzKG1ldHJpY3M6IFNraWxsQW5hbHlzaXNNZXRyaWNzKTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLmFpU2VydmljZU1vbml0b3IpIHJldHVybjsgLy8gU2tpcCBpbiBkZXZlbG9wbWVudFxuXG4gICAgdGhpcy5haVNlcnZpY2VNb25pdG9yLnJlY29yZE9wZXJhdGlvbihcbiAgICAgICdjb21wcmVoZW5zaXZlX3NraWxsX2FuYWx5c2lzJyxcbiAgICAgIG1ldHJpY3MucmVzcG9uc2VUaW1lLFxuICAgICAgbWV0cmljcy5zdWNjZXNzLFxuICAgICAgbWV0cmljcy5jYWNoZUhpdCxcbiAgICAgIG1ldHJpY3MudXNlcklkLFxuICAgICAgbWV0cmljcy5lcnJvclxuICAgICk7XG5cbiAgICBpZiAobWV0cmljcy5yZXNwb25zZVRpbWUgPiB0aGlzLlRIUkVTSE9MRFMuQU5BTFlTSVNfV0FSTklORykge1xuICAgICAgY29uc29sZS53YXJuKGDimqDvuI8gU2xvdyBza2lsbCBhbmFseXNpcyBmb3IgdXNlciAke21ldHJpY3MudXNlcklkfTogJHttZXRyaWNzLnJlc3BvbnNlVGltZX1tc2ApO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgZ2VuZXJhdGVBbmFseXNpc0lkKGFuYWx5c2lzUmVxdWVzdDogYW55LCB1c2VySWQ6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgY29uc3Qgc2tpbGxOYW1lcyA9IGFuYWx5c2lzUmVxdWVzdC5jdXJyZW50U2tpbGxzPy5tYXAoKHM6IGFueSkgPT4gcy5za2lsbE5hbWUpLnNvcnQoKS5qb2luKCcsJykgfHwgJyc7XG4gICAgY29uc3QgY2FyZWVyUGF0aCA9IGFuYWx5c2lzUmVxdWVzdC50YXJnZXRDYXJlZXJQYXRoPy5jYXJlZXJQYXRoTmFtZSB8fCAnJztcbiAgICBjb25zdCBsZXZlbCA9IGFuYWx5c2lzUmVxdWVzdC50YXJnZXRDYXJlZXJQYXRoPy50YXJnZXRMZXZlbCB8fCAnJztcbiAgICBcbiAgICBjb25zdCBoYXNoID0gQnVmZmVyLmZyb20oYCR7dXNlcklkfToke3NraWxsTmFtZXN9OiR7Y2FyZWVyUGF0aH06JHtsZXZlbH1gKS50b1N0cmluZygnYmFzZTY0Jyk7XG4gICAgcmV0dXJuIGhhc2guc3Vic3RyaW5nKDAsIDE2KTtcbiAgfVxufVxuXG4vLyBFeHBvcnQgc2luZ2xldG9uIGluc3RhbmNlXG5leHBvcnQgY29uc3Qgc2tpbGxHYXBQZXJmb3JtYW5jZU1vbml0b3IgPSBuZXcgU2tpbGxHYXBQZXJmb3JtYW5jZU1vbml0b3IoKTtcbiJdLCJtYXBwaW5ncyI6Ijs7QUFBQTs7OztBQUFBO0FBQUEsU0FBQUEsZUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxJQUFBO0VBQUEsSUFBQUMsTUFBQSxPQUFBQyxRQUFBO0VBQUEsSUFBQUMsR0FBQTtFQUFBLElBQUFDLFlBQUE7SUFBQUwsSUFBQTtJQUFBTSxZQUFBO01BQUE7UUFBQUMsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7SUFBQTtJQUFBRSxLQUFBO01BQUE7UUFBQUMsSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO0lBQUE7SUFBQU8sU0FBQTtNQUFBO1FBQUFELEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFXLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLGNBQUE7TUFBQUMsSUFBQTtNQUFBQyxRQUFBO01BQUFDLEtBQUE7TUFBQUMsT0FBQTtNQUFBQyxjQUFBO01BQUFDLE9BQUE7SUFBQTtJQUFBQyxlQUFBO0lBQUE1QixJQUFBO0VBQUE7RUFBQSxJQUFBNkIsUUFBQSxHQUFBNUIsTUFBQSxDQUFBRSxHQUFBLE1BQUFGLE1BQUEsQ0FBQUUsR0FBQTtFQUFBLEtBQUEwQixRQUFBLENBQUE5QixJQUFBLEtBQUE4QixRQUFBLENBQUE5QixJQUFBLEVBQUFDLElBQUEsS0FBQUEsSUFBQTtJQUFBNkIsUUFBQSxDQUFBOUIsSUFBQSxJQUFBSyxZQUFBO0VBQUE7RUFBQSxJQUFBMEIsY0FBQSxHQUFBRCxRQUFBLENBQUE5QixJQUFBO0VBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS0EsSUFBQWdDLHdCQUFBO0FBQUE7QUFBQSxDQUFBakMsY0FBQSxHQUFBb0IsQ0FBQSxRQUFBYyxPQUFBO0FBQ0EsSUFBQUMsb0JBQUE7QUFBQTtBQUFBLENBQUFuQyxjQUFBLEdBQUFvQixDQUFBLFFBQUFjLE9BQUE7QUFDQSxJQUFBRSw0QkFBQTtBQUFBO0FBQUEsQ0FBQXBDLGNBQUEsR0FBQW9CLENBQUEsUUFBQWMsT0FBQTtBQTJCQSxJQUFBRywwQkFBQTtBQUFBO0FBQUEsY0FBQXJDLGNBQUEsR0FBQW9CLENBQUE7RUFBQTtFQUFBcEIsY0FBQSxHQUFBcUIsQ0FBQTtFQXdCRSxTQUFBZ0IsMkJBQUE7SUFBQTtJQUFBckMsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQW5CQTtJQUNpQixLQUFBa0IsU0FBUyxHQUFHO01BQzNCQyxZQUFZLEVBQUUsTUFBTTtNQUFTO01BQzdCQyxnQkFBZ0IsRUFBRSxPQUFPO01BQUk7TUFDN0JDLGNBQWMsRUFBRSxPQUFPO01BQU07TUFDN0JDLGNBQWMsRUFBRSxPQUFPO01BQU07TUFDN0JDLFdBQVcsRUFBRSxRQUFRLENBQVE7S0FDOUI7SUFFRDtJQUFBO0lBQUEzQyxjQUFBLEdBQUFvQixDQUFBO0lBQ2lCLEtBQUF3QixVQUFVLEdBQUc7TUFDNUJDLG9CQUFvQixFQUFFLElBQUk7TUFBTztNQUNqQ0MscUJBQXFCLEVBQUUsSUFBSTtNQUFNO01BQ2pDQyxnQkFBZ0IsRUFBRSxJQUFJO01BQVc7TUFDakNDLGlCQUFpQixFQUFFLEtBQUs7TUFBUztNQUNqQ0Msc0JBQXNCLEVBQUUsRUFBRTtNQUFPO01BQ2pDQyx1QkFBdUIsRUFBRSxFQUFFLENBQU07S0FDbEM7SUFHQztJQUFBO0lBQUFsRCxjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSStCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxRQUFRLEtBQUssWUFBWSxFQUFFO01BQUE7TUFBQXJELGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDekMsSUFBSSxDQUFDa0Msa0JBQWtCLEdBQUcsSUFBSXJCLHdCQUFBLENBQUFzQixrQkFBa0IsRUFBRTtNQUFDO01BQUF2RCxjQUFBLEdBQUFvQixDQUFBO01BQ25ELElBQUksQ0FBQ29DLGdCQUFnQixHQUFHckIsb0JBQUEsQ0FBQXNCLGdCQUFnQixDQUFDQyxXQUFXLEVBQUU7SUFDeEQsQ0FBQyxNQUFNO01BQUE7TUFBQTFELGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDTCxJQUFJLENBQUNrQyxrQkFBa0IsR0FBRyxJQUFJO01BQUM7TUFBQXRELGNBQUEsR0FBQW9CLENBQUE7TUFDL0IsSUFBSSxDQUFDb0MsZ0JBQWdCLEdBQUcsSUFBSTtJQUM5QjtJQUFDO0lBQUF4RCxjQUFBLEdBQUFvQixDQUFBO0lBQ0QsSUFBSSxDQUFDdUMsWUFBWSxHQUFHdkIsNEJBQUEsQ0FBQXdCLGlCQUFpQjtFQUN2QztFQUVBOzs7RUFBQTtFQUFBNUQsY0FBQSxHQUFBb0IsQ0FBQTtFQUdNaUIsMEJBQUEsQ0FBQXdCLFNBQUEsQ0FBQUMsa0JBQWtCLEdBQXhCLFVBQ0VDLEtBQWEsRUFDYkMsU0FBNkIsRUFDN0JDLE1BQWU7SUFBQTtJQUFBakUsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTttQ0FDZDhDLE9BQU87TUFBQTtNQUFBbEUsY0FBQSxHQUFBcUIsQ0FBQTs7Ozs7Ozs7Ozs7OztZQUNGOEMsU0FBUyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsRUFBRTtZQUFDO1lBQUFyRSxjQUFBLEdBQUFvQixDQUFBO1lBQ3ZCa0QsUUFBUSxHQUFHLGdCQUFBQyxNQUFBLENBQWdCUixLQUFLLENBQUNTLFdBQVcsRUFBRSxDQUFFO1lBQUM7WUFBQXhFLGNBQUEsR0FBQW9CLENBQUE7Ozs7Ozs7OztZQUloQyxxQkFBTSxJQUFJLENBQUN1QyxZQUFZLENBQUNjLEdBQUcsQ0FBTUgsUUFBUSxDQUFDOzs7OztZQUF6REksWUFBWSxHQUFHQyxFQUFBLENBQUFDLElBQUEsRUFBMEM7WUFBQTtZQUFBNUUsY0FBQSxHQUFBb0IsQ0FBQTtZQUMvRCxJQUFJc0QsWUFBWSxFQUFFO2NBQUE7Y0FBQTFFLGNBQUEsR0FBQXNCLENBQUE7Y0FBQXRCLGNBQUEsR0FBQW9CLENBQUE7Y0FDVnlELGNBQUEsR0FBZVQsSUFBSSxDQUFDQyxHQUFHLEVBQUUsR0FBR0YsU0FBUztjQUFDO2NBQUFuRSxjQUFBLEdBQUFvQixDQUFBO2NBQzVDLElBQUksQ0FBQzBELHdCQUF3QixDQUFDO2dCQUM1QmYsS0FBSyxFQUFBQSxLQUFBO2dCQUNMZ0IsV0FBVyxFQUFFQyxLQUFLLENBQUNDLE9BQU8sQ0FBQ1AsWUFBWSxDQUFDO2dCQUFBO2dCQUFBLENBQUExRSxjQUFBLEdBQUFzQixDQUFBLFdBQUdvRCxZQUFZLENBQUNRLE1BQU07Z0JBQUE7Z0JBQUEsQ0FBQWxGLGNBQUEsR0FBQXNCLENBQUEsV0FBRyxDQUFDO2dCQUNsRTZELFlBQVksRUFBQU4sY0FBQTtnQkFDWk8sUUFBUSxFQUFFLElBQUk7Z0JBQ2RuQixNQUFNLEVBQUFBO2VBQ1AsQ0FBQztjQUFDO2NBQUFqRSxjQUFBLEdBQUFvQixDQUFBO2NBQ0gsc0JBQU9zRCxZQUFZO1lBQ3JCLENBQUM7WUFBQTtZQUFBO2NBQUExRSxjQUFBLEdBQUFzQixDQUFBO1lBQUE7WUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7WUFHYyxxQkFBTTRDLFNBQVMsRUFBRTs7Ozs7WUFBMUJxQixNQUFNLEdBQUdWLEVBQUEsQ0FBQUMsSUFBQSxFQUFpQjtZQUFBO1lBQUE1RSxjQUFBLEdBQUFvQixDQUFBO1lBQzFCK0QsWUFBWSxHQUFHZixJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO1lBRTNDO1lBQUE7WUFBQW5FLGNBQUEsR0FBQW9CLENBQUE7WUFDQSxxQkFBTSxJQUFJLENBQUN1QyxZQUFZLENBQUMyQixHQUFHLENBQUNoQixRQUFRLEVBQUVlLE1BQU0sRUFBRTtjQUFFRSxHQUFHLEVBQUUsSUFBSSxDQUFDakQsU0FBUyxDQUFDQyxZQUFZO2NBQUVpRCxJQUFJLEVBQUUsQ0FBQyxjQUFjO1lBQUMsQ0FBRSxDQUFDOzs7OztZQUQzRztZQUNBYixFQUFBLENBQUFDLElBQUEsRUFBMkc7WUFFM0c7WUFBQTtZQUFBNUUsY0FBQSxHQUFBb0IsQ0FBQTtZQUNBLElBQUksQ0FBQzBELHdCQUF3QixDQUFDO2NBQzVCZixLQUFLLEVBQUFBLEtBQUE7Y0FDTGdCLFdBQVc7Y0FBRTtjQUFBLENBQUEvRSxjQUFBLEdBQUFzQixDQUFBLFdBQUErRCxNQUFNLENBQUNILE1BQU07Y0FBQTtjQUFBLENBQUFsRixjQUFBLEdBQUFzQixDQUFBLFdBQUksQ0FBQztjQUMvQjZELFlBQVksRUFBQUEsWUFBQTtjQUNaQyxRQUFRLEVBQUUsS0FBSztjQUNmbkIsTUFBTSxFQUFBQTthQUNQLENBQUM7WUFBQztZQUFBakUsY0FBQSxHQUFBb0IsQ0FBQTtZQUVILHNCQUFPaUUsTUFBTTs7Ozs7Ozs7WUFFUEYsWUFBWSxHQUFHZixJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO1lBQUM7WUFBQW5FLGNBQUEsR0FBQW9CLENBQUE7WUFDNUMsSUFBSSxDQUFDcUUsc0JBQXNCLENBQUMxQixLQUFLLEVBQUVvQixZQUFZLEVBQUVPLE9BQWMsRUFBRXpCLE1BQU0sQ0FBQztZQUFDO1lBQUFqRSxjQUFBLEdBQUFvQixDQUFBO1lBQ3pFLE1BQU1zRSxPQUFLOzs7Ozs7Ozs7R0FFZDtFQUVEOzs7RUFBQTtFQUFBMUYsY0FBQSxHQUFBb0IsQ0FBQTtFQUdNaUIsMEJBQUEsQ0FBQXdCLFNBQUEsQ0FBQThCLHNCQUFzQixHQUE1QixVQUNFQyxXQUFrQixFQUNsQjVCLFNBQTZCLEVBQzdCQyxNQUFjO0lBQUE7SUFBQWpFLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7bUNBQ2I4QyxPQUFPO01BQUE7TUFBQWxFLGNBQUEsR0FBQXFCLENBQUE7Ozs7Ozs7Ozs7Ozs7WUFDRjhDLFNBQVMsR0FBR0MsSUFBSSxDQUFDQyxHQUFHLEVBQUU7WUFBQztZQUFBckUsY0FBQSxHQUFBb0IsQ0FBQTs7Ozs7Ozs7O1lBR1oscUJBQU00QyxTQUFTLEVBQUU7Ozs7O1lBQTFCcUIsTUFBTSxHQUFHVixFQUFBLENBQUFDLElBQUEsRUFBaUI7WUFBQTtZQUFBNUUsY0FBQSxHQUFBb0IsQ0FBQTtZQUMxQitELFlBQVksR0FBR2YsSUFBSSxDQUFDQyxHQUFHLEVBQUUsR0FBR0YsU0FBUztZQUFDO1lBQUFuRSxjQUFBLEdBQUFvQixDQUFBO1lBR3RDa0QsUUFBUSxHQUFHLG9CQUFBQyxNQUFBLENBQW9CTixNQUFNLENBQUU7WUFBQztZQUFBakUsY0FBQSxHQUFBb0IsQ0FBQTtZQUM5QyxxQkFBTSxJQUFJLENBQUN1QyxZQUFZLENBQUMyQixHQUFHLENBQUNoQixRQUFRLEVBQUVzQixXQUFXLEVBQUU7Y0FBRUwsR0FBRyxFQUFFLElBQUksQ0FBQ2pELFNBQVMsQ0FBQ0UsZ0JBQWdCO2NBQUVnRCxJQUFJLEVBQUUsQ0FBQyxrQkFBa0IsRUFBRXZCLE1BQU07WUFBQyxDQUFFLENBQUM7Ozs7O1lBQWhJVSxFQUFBLENBQUFDLElBQUEsRUFBZ0k7WUFFaEk7WUFBQTtZQUFBNUUsY0FBQSxHQUFBb0IsQ0FBQTtZQUNBLElBQUksQ0FBQ3lFLDRCQUE0QixDQUFDO2NBQ2hDQyxlQUFlLEVBQUVGLFdBQVcsQ0FBQ1YsTUFBTTtjQUNuQ0MsWUFBWSxFQUFBQSxZQUFBO2NBQ1pZLE9BQU8sRUFBRSxJQUFJO2NBQ2I5QixNQUFNLEVBQUFBO2FBQ1AsQ0FBQztZQUFDO1lBQUFqRSxjQUFBLEdBQUFvQixDQUFBO1lBRUgsc0JBQU9pRSxNQUFNOzs7Ozs7OztZQUVQRixZQUFZLEdBQUdmLElBQUksQ0FBQ0MsR0FBRyxFQUFFLEdBQUdGLFNBQVM7WUFBQztZQUFBbkUsY0FBQSxHQUFBb0IsQ0FBQTtZQUM1QyxJQUFJLENBQUN5RSw0QkFBNEIsQ0FBQztjQUNoQ0MsZUFBZSxFQUFFRixXQUFXLENBQUNWLE1BQU07Y0FDbkNDLFlBQVksRUFBQUEsWUFBQTtjQUNaWSxPQUFPLEVBQUUsS0FBSztjQUNkOUIsTUFBTSxFQUFBQTthQUNQLENBQUM7WUFBQztZQUFBakUsY0FBQSxHQUFBb0IsQ0FBQTtZQUNILE1BQU00RSxPQUFLOzs7Ozs7Ozs7R0FFZDtFQUVEOzs7RUFBQTtFQUFBaEcsY0FBQSxHQUFBb0IsQ0FBQTtFQUdNaUIsMEJBQUEsQ0FBQXdCLFNBQUEsQ0FBQW9DLG9CQUFvQixHQUExQixVQUNFQyxlQUFvQixFQUNwQmxDLFNBQTZCLEVBQzdCQyxNQUFjO0lBQUE7SUFBQWpFLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7bUNBQ2I4QyxPQUFPO01BQUE7TUFBQWxFLGNBQUEsR0FBQXFCLENBQUE7Ozs7Ozs7Ozs7Ozs7O1lBQ0Y4QyxTQUFTLEdBQUdDLElBQUksQ0FBQ0MsR0FBRyxFQUFFO1lBQUM7WUFBQXJFLGNBQUEsR0FBQW9CLENBQUE7WUFDdkIrRSxVQUFVLEdBQUcsSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQ0YsZUFBZSxFQUFFakMsTUFBTSxDQUFDO1lBQUM7WUFBQWpFLGNBQUEsR0FBQW9CLENBQUE7WUFDOURrRCxRQUFRLEdBQUcsa0JBQUFDLE1BQUEsQ0FBa0I0QixVQUFVLENBQUU7WUFBQztZQUFBbkcsY0FBQSxHQUFBb0IsQ0FBQTs7Ozs7Ozs7O1lBSXpCLHFCQUFNLElBQUksQ0FBQ3VDLFlBQVksQ0FBQ2MsR0FBRyxDQUFNSCxRQUFRLENBQUM7Ozs7O1lBQXpESSxZQUFZLEdBQUcyQixFQUFBLENBQUF6QixJQUFBLEVBQTBDO1lBQUE7WUFBQTVFLGNBQUEsR0FBQW9CLENBQUE7WUFDL0QsSUFBSXNELFlBQVksRUFBRTtjQUFBO2NBQUExRSxjQUFBLEdBQUFzQixDQUFBO2NBQUF0QixjQUFBLEdBQUFvQixDQUFBO2NBQ1ZrRixjQUFBLEdBQWVsQyxJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO2NBQUM7Y0FBQW5FLGNBQUEsR0FBQW9CLENBQUE7Y0FDNUMsSUFBSSxDQUFDbUYsMEJBQTBCLENBQUM7Z0JBQzlCQyxZQUFZLEVBQUUsZUFBZTtnQkFDN0JDLFVBQVU7Z0JBQUU7Z0JBQUEsQ0FBQXpHLGNBQUEsR0FBQXNCLENBQUE7Z0JBQUE7Z0JBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsWUFBQXFELEVBQUEsR0FBQXVCLGVBQWUsQ0FBQ1EsYUFBYTtnQkFBQTtnQkFBQSxDQUFBMUcsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBcUQsRUFBQTtnQkFBQTtnQkFBQSxDQUFBM0UsY0FBQSxHQUFBc0IsQ0FBQTtnQkFBQTtnQkFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBcUQsRUFBQSxDQUFFTyxNQUFNO2dCQUFBO2dCQUFBLENBQUFsRixjQUFBLEdBQUFzQixDQUFBLFdBQUksQ0FBQztnQkFDdEQ2RCxZQUFZLEVBQUFtQixjQUFBO2dCQUNaUCxPQUFPLEVBQUUsSUFBSTtnQkFDYlgsUUFBUSxFQUFFLElBQUk7Z0JBQ2RuQixNQUFNLEVBQUFBO2VBQ1AsQ0FBQztjQUFDO2NBQUFqRSxjQUFBLEdBQUFvQixDQUFBO2NBQ0gsc0JBQUF1RixRQUFBLENBQUFBLFFBQUEsS0FBWWpDLFlBQVk7Z0JBQUVrQyxNQUFNLEVBQUU7Y0FBSTtZQUN4QyxDQUFDO1lBQUE7WUFBQTtjQUFBNUcsY0FBQSxHQUFBc0IsQ0FBQTtZQUFBO1lBQUF0QixjQUFBLEdBQUFvQixDQUFBO1lBR2MscUJBQU00QyxTQUFTLEVBQUU7Ozs7O1lBQTFCcUIsTUFBTSxHQUFHZ0IsRUFBQSxDQUFBekIsSUFBQSxFQUFpQjtZQUFBO1lBQUE1RSxjQUFBLEdBQUFvQixDQUFBO1lBQzFCK0QsWUFBWSxHQUFHZixJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO1lBRTNDO1lBQUE7WUFBQW5FLGNBQUEsR0FBQW9CLENBQUE7WUFDQSxxQkFBTSxJQUFJLENBQUN1QyxZQUFZLENBQUMyQixHQUFHLENBQUNoQixRQUFRLEVBQUVlLE1BQU0sRUFBRTtjQUFFRSxHQUFHLEVBQUUsSUFBSSxDQUFDakQsU0FBUyxDQUFDRyxjQUFjO2NBQUUrQyxJQUFJLEVBQUUsQ0FBQyxnQkFBZ0IsRUFBRXZCLE1BQU07WUFBQyxDQUFFLENBQUM7Ozs7O1lBRHZIO1lBQ0FvQyxFQUFBLENBQUF6QixJQUFBLEVBQXVIO1lBRXZIO1lBQUE7WUFBQTVFLGNBQUEsR0FBQW9CLENBQUE7WUFDQSxJQUFJLENBQUNtRiwwQkFBMEIsQ0FBQztjQUM5QkMsWUFBWSxFQUFFLGVBQWU7Y0FDN0JDLFVBQVU7Y0FBRTtjQUFBLENBQUF6RyxjQUFBLEdBQUFzQixDQUFBO2NBQUE7Y0FBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxZQUFBdUYsRUFBQSxHQUFBWCxlQUFlLENBQUNRLGFBQWE7Y0FBQTtjQUFBLENBQUExRyxjQUFBLEdBQUFzQixDQUFBLFdBQUF1RixFQUFBO2NBQUE7Y0FBQSxDQUFBN0csY0FBQSxHQUFBc0IsQ0FBQTtjQUFBO2NBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsV0FBQXVGLEVBQUEsQ0FBRTNCLE1BQU07Y0FBQTtjQUFBLENBQUFsRixjQUFBLEdBQUFzQixDQUFBLFdBQUksQ0FBQztjQUN0RDZELFlBQVksRUFBQUEsWUFBQTtjQUNaWSxPQUFPLEVBQUUsSUFBSTtjQUNiWCxRQUFRLEVBQUUsS0FBSztjQUNmbkIsTUFBTSxFQUFBQTthQUNQLENBQUM7WUFBQztZQUFBakUsY0FBQSxHQUFBb0IsQ0FBQTtZQUVILHNCQUFPaUUsTUFBTTs7Ozs7Ozs7WUFFUEYsWUFBWSxHQUFHZixJQUFJLENBQUNDLEdBQUcsRUFBRSxHQUFHRixTQUFTO1lBQUM7WUFBQW5FLGNBQUEsR0FBQW9CLENBQUE7WUFDNUMsSUFBSSxDQUFDbUYsMEJBQTBCLENBQUM7Y0FDOUJDLFlBQVksRUFBRSxlQUFlO2NBQzdCQyxVQUFVO2NBQUU7Y0FBQSxDQUFBekcsY0FBQSxHQUFBc0IsQ0FBQTtjQUFBO2NBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsWUFBQXdGLEVBQUEsR0FBQVosZUFBZSxDQUFDUSxhQUFhO2NBQUE7Y0FBQSxDQUFBMUcsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBd0YsRUFBQTtjQUFBO2NBQUEsQ0FBQTlHLGNBQUEsR0FBQXNCLENBQUE7Y0FBQTtjQUFBLENBQUF0QixjQUFBLEdBQUFzQixDQUFBLFdBQUF3RixFQUFBLENBQUU1QixNQUFNO2NBQUE7Y0FBQSxDQUFBbEYsY0FBQSxHQUFBc0IsQ0FBQSxXQUFJLENBQUM7Y0FDdEQ2RCxZQUFZLEVBQUFBLFlBQUE7Y0FDWlksT0FBTyxFQUFFLEtBQUs7Y0FDZFgsUUFBUSxFQUFFLEtBQUs7Y0FDZm5CLE1BQU0sRUFBQUEsTUFBQTtjQUNOOEMsS0FBSyxFQUFHQyxPQUFlLENBQUNDO2FBQ3pCLENBQUM7WUFBQztZQUFBakgsY0FBQSxHQUFBb0IsQ0FBQTtZQUNILE1BQU00RixPQUFLOzs7Ozs7Ozs7R0FFZDtFQUVEOzs7RUFBQTtFQUFBaEgsY0FBQSxHQUFBb0IsQ0FBQTtFQUdNaUIsMEJBQUEsQ0FBQXdCLFNBQUEsQ0FBQXFELHNCQUFzQixHQUE1QjtJQUFBO0lBQUFsSCxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO21DQUFnQzhDLE9BQU87TUFBQTtNQUFBbEUsY0FBQSxHQUFBcUIsQ0FBQTs7Ozs7Ozs7Ozs7Ozs7OztZQUMvQjhGLGFBQWEsR0FBRyxDQUNwQixZQUFZLEVBQUUsUUFBUSxFQUFFLE9BQU8sRUFBRSxTQUFTLEVBQUUsWUFBWSxFQUN4RCxNQUFNLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsUUFBUSxDQUN0QztZQUFDO1lBQUFuSCxjQUFBLEdBQUFvQixDQUFBO1lBRUlnRyxlQUFlLEdBQUdELGFBQWEsQ0FBQ0UsR0FBRyxDQUFDLFVBQU9DLEtBQUs7Y0FBQTtjQUFBdEgsY0FBQSxHQUFBcUIsQ0FBQTtjQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtjQUFBLE9BQUFtRyxTQUFBLENBQUFDLEtBQUE7Z0JBQUE7Z0JBQUF4SCxjQUFBLEdBQUFxQixDQUFBOzs7Ozs7Ozs7Ozs7O3NCQUM5Q2lELFFBQVEsR0FBRyxjQUFBQyxNQUFBLENBQWMrQyxLQUFLLENBQUM5QyxXQUFXLEVBQUUsQ0FBRTtzQkFBQztzQkFBQXhFLGNBQUEsR0FBQW9CLENBQUE7c0JBQy9DcUcsU0FBUyxHQUFHO3dCQUNoQjVHLElBQUksRUFBRXlHLEtBQUs7d0JBQ1hJLE9BQU8sRUFBRSxJQUFJO3dCQUNiQyxRQUFRLEVBQUV2RCxJQUFJLENBQUNDLEdBQUc7dUJBQ25CO3NCQUFDO3NCQUFBckUsY0FBQSxHQUFBb0IsQ0FBQTtzQkFDRixxQkFBTSxJQUFJLENBQUN1QyxZQUFZLENBQUMyQixHQUFHLENBQUNoQixRQUFRLEVBQUVtRCxTQUFTLEVBQUU7d0JBQUVsQyxHQUFHLEVBQUUsSUFBSSxDQUFDakQsU0FBUyxDQUFDSSxjQUFjO3dCQUFFOEMsSUFBSSxFQUFFLENBQUMsZ0JBQWdCO3NCQUFDLENBQUUsQ0FBQzs7Ozs7c0JBQWxIYixFQUFBLENBQUFDLElBQUEsRUFBa0g7c0JBQUM7c0JBQUE1RSxjQUFBLEdBQUFvQixDQUFBOzs7OzthQUNwSCxDQUFDO1lBQUM7WUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7WUFFSCxxQkFBTThDLE9BQU8sQ0FBQzBELEdBQUcsQ0FBQ1IsZUFBZSxDQUFDOzs7OztZQUFsQ3pDLEVBQUEsQ0FBQUMsSUFBQSxFQUFrQztZQUFDO1lBQUE1RSxjQUFBLEdBQUFvQixDQUFBO1lBQ25DeUcsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUNBQUF2RCxNQUFBLENBQXVCNEMsYUFBYSxDQUFDakMsTUFBTSxvQkFBaUIsQ0FBQztZQUFDO1lBQUFsRixjQUFBLEdBQUFvQixDQUFBOzs7OztHQUMzRTtFQUVEOzs7RUFBQTtFQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBaUIsMEJBQUEsQ0FBQXdCLFNBQUEsQ0FBQWtFLG9CQUFvQixHQUFwQjtJQUFBO0lBQUEvSCxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ0UsSUFBSSxDQUFDLElBQUksQ0FBQ2tDLGtCQUFrQixFQUFFO01BQUE7TUFBQXRELGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFPO1FBQUU0RyxTQUFTLEVBQUUsSUFBSTtRQUFFZixPQUFPLEVBQUU7TUFBb0MsQ0FBRTtJQUFBLENBQUM7SUFBQTtJQUFBO01BQUFqSCxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDeEcsT0FBTyxJQUFJLENBQUNrQyxrQkFBa0IsQ0FBQ3lFLG9CQUFvQixFQUFFO0VBQ3ZELENBQUM7RUFFRDs7O0VBQUE7RUFBQS9ILGNBQUEsR0FBQW9CLENBQUE7RUFHQWlCLDBCQUFBLENBQUF3QixTQUFBLENBQUFvRSxtQkFBbUIsR0FBbkI7SUFBQTtJQUFBakksY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNFLElBQUksQ0FBQyxJQUFJLENBQUNvQyxnQkFBZ0IsRUFBRTtNQUFBO01BQUF4RCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBTztRQUFFNkYsT0FBTyxFQUFFO01BQXVDLENBQUU7SUFBQSxDQUFDO0lBQUE7SUFBQTtNQUFBakgsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ3hGLE9BQU8sSUFBSSxDQUFDb0MsZ0JBQWdCLENBQUMwRSxVQUFVLEVBQUU7RUFDM0MsQ0FBQztFQUVEOzs7RUFBQTtFQUFBbEksY0FBQSxHQUFBb0IsQ0FBQTtFQUdNaUIsMEJBQUEsQ0FBQXdCLFNBQUEsQ0FBQXNFLFdBQVcsR0FBakI7SUFBQTtJQUFBbkksY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTttQ0FBcUI4QyxPQUFPO01BQUE7TUFBQWxFLGNBQUEsR0FBQXFCLENBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7WUFFSCxxQkFBTSxJQUFJLENBQUNzQyxZQUFZLENBQUN3RSxXQUFXLEVBQUU7Ozs7O1lBQXBEQyxZQUFZLEdBQUd6RCxFQUFBLENBQUFDLElBQUEsRUFBcUM7WUFBQTtZQUFBNUUsY0FBQSxHQUFBb0IsQ0FBQTtZQUNwRGlILGlCQUFpQixHQUFHLElBQUksQ0FBQ04sb0JBQW9CLEVBQUU7WUFBQztZQUFBL0gsY0FBQSxHQUFBb0IsQ0FBQTtZQUV0RDtZQUFPO1lBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsV0FBQThHLFlBQVk7WUFBQTtZQUFBLENBQUFwSSxjQUFBLEdBQUFzQixDQUFBLFdBQUkrRyxpQkFBaUIsQ0FBQ0wsU0FBUzs7Ozs7Ozs7WUFFbERILE9BQU8sQ0FBQ2QsS0FBSyxDQUFDLHNCQUFzQixFQUFFdUIsT0FBSyxDQUFDO1lBQUM7WUFBQXRJLGNBQUEsR0FBQW9CLENBQUE7WUFDN0Msc0JBQU8sS0FBSzs7Ozs7Ozs7O0dBRWY7RUFFRDtFQUFBO0VBQUFwQixjQUFBLEdBQUFvQixDQUFBO0VBRVFpQiwwQkFBQSxDQUFBd0IsU0FBQSxDQUFBaUIsd0JBQXdCLEdBQWhDLFVBQWlDeUQsT0FBMkI7SUFBQTtJQUFBdkksY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUMxRCxJQUFJLENBQUMsSUFBSSxDQUFDa0Msa0JBQWtCLEVBQUU7TUFBQTtNQUFBdEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQSxFQUFDO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRXRDLElBQUksQ0FBQ2tDLGtCQUFrQixDQUFDa0YsZUFBZSxDQUFDLGNBQWMsRUFBRUQsT0FBTyxDQUFDcEQsWUFBWSxFQUFFLElBQUksQ0FBQztJQUFDO0lBQUFuRixjQUFBLEdBQUFvQixDQUFBO0lBRXBGLElBQUltSCxPQUFPLENBQUNwRCxZQUFZLEdBQUcsSUFBSSxDQUFDdkMsVUFBVSxDQUFDQyxvQkFBb0IsRUFBRTtNQUFBO01BQUE3QyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQy9EeUcsT0FBTyxDQUFDWSxJQUFJLENBQUMsbUNBQUFsRSxNQUFBLENBQXlCZ0UsT0FBTyxDQUFDeEUsS0FBSyxZQUFBUSxNQUFBLENBQVNnRSxPQUFPLENBQUNwRCxZQUFZLE9BQUksQ0FBQztJQUN2RixDQUFDO0lBQUE7SUFBQTtNQUFBbkYsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0VBQ0gsQ0FBQztFQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBRU9pQiwwQkFBQSxDQUFBd0IsU0FBQSxDQUFBNEIsc0JBQXNCLEdBQTlCLFVBQStCMUIsS0FBYSxFQUFFb0IsWUFBb0IsRUFBRTRCLEtBQVksRUFBRTlDLE1BQWU7SUFBQTtJQUFBakUsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUMvRixJQUFJLENBQUMsSUFBSSxDQUFDa0Msa0JBQWtCLEVBQUU7TUFBQTtNQUFBdEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQSxFQUFDO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRXRDLElBQUksQ0FBQ2tDLGtCQUFrQixDQUFDa0YsZUFBZSxDQUFDLGNBQWMsRUFBRXJELFlBQVksRUFBRSxLQUFLLENBQUM7RUFDOUUsQ0FBQztFQUFBO0VBQUFuRixjQUFBLEdBQUFvQixDQUFBO0VBRU9pQiwwQkFBQSxDQUFBd0IsU0FBQSxDQUFBZ0MsNEJBQTRCLEdBQXBDLFVBQXFDMEMsT0FBK0I7SUFBQTtJQUFBdkksY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNsRSxJQUFJLENBQUMsSUFBSSxDQUFDb0MsZ0JBQWdCLEVBQUU7TUFBQTtNQUFBeEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQSxFQUFDO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRXBDLElBQUksQ0FBQ29DLGdCQUFnQixDQUFDZ0YsZUFBZSxDQUNuQyx5QkFBeUIsRUFDekJELE9BQU8sQ0FBQ3BELFlBQVksRUFDcEJvRCxPQUFPLENBQUN4QyxPQUFPLEVBQ2YsS0FBSyxFQUNMd0MsT0FBTyxDQUFDdEUsTUFBTSxDQUNmO0VBQ0gsQ0FBQztFQUFBO0VBQUFqRSxjQUFBLEdBQUFvQixDQUFBO0VBRU9pQiwwQkFBQSxDQUFBd0IsU0FBQSxDQUFBMEMsMEJBQTBCLEdBQWxDLFVBQW1DZ0MsT0FBNkI7SUFBQTtJQUFBdkksY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUM5RCxJQUFJLENBQUMsSUFBSSxDQUFDb0MsZ0JBQWdCLEVBQUU7TUFBQTtNQUFBeEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQSxFQUFDO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRXBDLElBQUksQ0FBQ29DLGdCQUFnQixDQUFDZ0YsZUFBZSxDQUNuQyw4QkFBOEIsRUFDOUJELE9BQU8sQ0FBQ3BELFlBQVksRUFDcEJvRCxPQUFPLENBQUN4QyxPQUFPLEVBQ2Z3QyxPQUFPLENBQUNuRCxRQUFRLEVBQ2hCbUQsT0FBTyxDQUFDdEUsTUFBTSxFQUNkc0UsT0FBTyxDQUFDeEIsS0FBSyxDQUNkO0lBQUM7SUFBQS9HLGNBQUEsR0FBQW9CLENBQUE7SUFFRixJQUFJbUgsT0FBTyxDQUFDcEQsWUFBWSxHQUFHLElBQUksQ0FBQ3ZDLFVBQVUsQ0FBQ0csZ0JBQWdCLEVBQUU7TUFBQTtNQUFBL0MsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUMzRHlHLE9BQU8sQ0FBQ1ksSUFBSSxDQUFDLDZDQUFBbEUsTUFBQSxDQUFtQ2dFLE9BQU8sQ0FBQ3RFLE1BQU0sUUFBQU0sTUFBQSxDQUFLZ0UsT0FBTyxDQUFDcEQsWUFBWSxPQUFJLENBQUM7SUFDOUYsQ0FBQztJQUFBO0lBQUE7TUFBQW5GLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtFQUNILENBQUM7RUFBQTtFQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtFQUVPaUIsMEJBQUEsQ0FBQXdCLFNBQUEsQ0FBQXVDLGtCQUFrQixHQUExQixVQUEyQkYsZUFBb0IsRUFBRWpDLE1BQWM7SUFBQTtJQUFBakUsY0FBQSxHQUFBcUIsQ0FBQTs7SUFDN0QsSUFBTXFILFVBQVU7SUFBQTtJQUFBLENBQUExSSxjQUFBLEdBQUFvQixDQUFBO0lBQUc7SUFBQSxDQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsWUFBQXFELEVBQUEsR0FBQXVCLGVBQWUsQ0FBQ1EsYUFBYTtJQUFBO0lBQUEsQ0FBQTFHLGNBQUEsR0FBQXNCLENBQUEsV0FBQXFELEVBQUE7SUFBQTtJQUFBLENBQUEzRSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBcUQsRUFBQSxDQUFFMEMsR0FBRyxDQUFDLFVBQUNqRyxDQUFNO01BQUE7TUFBQXBCLGNBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFBSyxPQUFBQSxDQUFDLENBQUN1SCxTQUFTO0lBQVgsQ0FBVyxFQUFFQyxJQUFJLEdBQUdDLElBQUksQ0FBQyxHQUFHLENBQUM7SUFBQTtJQUFBLENBQUE3SSxjQUFBLEdBQUFzQixDQUFBLFdBQUksRUFBRTtJQUNyRyxJQUFNd0gsVUFBVTtJQUFBO0lBQUEsQ0FBQTlJLGNBQUEsR0FBQW9CLENBQUE7SUFBRztJQUFBLENBQUFwQixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxZQUFBdUYsRUFBQSxHQUFBWCxlQUFlLENBQUM2QyxnQkFBZ0I7SUFBQTtJQUFBLENBQUEvSSxjQUFBLEdBQUFzQixDQUFBLFdBQUF1RixFQUFBO0lBQUE7SUFBQSxDQUFBN0csY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsV0FBQXVGLEVBQUEsQ0FBRW1DLGNBQWM7SUFBQTtJQUFBLENBQUFoSixjQUFBLEdBQUFzQixDQUFBLFdBQUksRUFBRTtJQUN6RSxJQUFNMkgsS0FBSztJQUFBO0lBQUEsQ0FBQWpKLGNBQUEsR0FBQW9CLENBQUE7SUFBRztJQUFBLENBQUFwQixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxZQUFBd0YsRUFBQSxHQUFBWixlQUFlLENBQUM2QyxnQkFBZ0I7SUFBQTtJQUFBLENBQUEvSSxjQUFBLEdBQUFzQixDQUFBLFdBQUF3RixFQUFBO0lBQUE7SUFBQSxDQUFBOUcsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsV0FBQXdGLEVBQUEsQ0FBRW9DLFdBQVc7SUFBQTtJQUFBLENBQUFsSixjQUFBLEdBQUFzQixDQUFBLFdBQUksRUFBRTtJQUVqRSxJQUFNcEIsSUFBSTtJQUFBO0lBQUEsQ0FBQUYsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHK0gsTUFBTSxDQUFDQyxJQUFJLENBQUMsR0FBQTdFLE1BQUEsQ0FBR04sTUFBTSxPQUFBTSxNQUFBLENBQUltRSxVQUFVLE9BQUFuRSxNQUFBLENBQUl1RSxVQUFVLE9BQUF2RSxNQUFBLENBQUkwRSxLQUFLLENBQUUsQ0FBQyxDQUFDSSxRQUFRLENBQUMsUUFBUSxDQUFDO0lBQUM7SUFBQXJKLGNBQUEsR0FBQW9CLENBQUE7SUFDOUYsT0FBT2xCLElBQUksQ0FBQ29KLFNBQVMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDO0VBQzlCLENBQUM7RUFBQTtFQUFBdEosY0FBQSxHQUFBb0IsQ0FBQTtFQUNILE9BQUFpQiwwQkFBQztBQUFELENBQUMsQ0F2U0Q7QUF1U0M7QUFBQXJDLGNBQUEsR0FBQW9CLENBQUE7QUF2U1ltSSxPQUFBLENBQUFsSCwwQkFBQSxHQUFBQSwwQkFBQTtBQXlTYjtBQUFBO0FBQUFyQyxjQUFBLEdBQUFvQixDQUFBO0FBQ2FtSSxPQUFBLENBQUFDLDBCQUEwQixHQUFHLElBQUluSCwwQkFBMEIsRUFBRSIsImlnbm9yZUxpc3QiOltdfQ==