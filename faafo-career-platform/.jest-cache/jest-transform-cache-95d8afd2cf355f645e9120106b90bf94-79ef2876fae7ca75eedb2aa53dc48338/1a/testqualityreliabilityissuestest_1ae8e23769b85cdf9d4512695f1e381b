2dc853e1d0964a3adf58a68468e2e46b
"use strict";
/**
 * Test Quality and Reliability Issues Tests
 *
 * These tests prove test quality problems including over-reliance on mocks,
 * unrealistic timeouts, and inadequate assertions.
 *
 * EXPECTED TO FAIL - These tests demonstrate test quality issues that need fixing.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
(0, globals_1.describe)('Test Quality and Reliability Issues', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Over-reliance on Mocks', function () {
        (0, globals_1.it)('should fail - tests over-rely on mocks instead of real implementations', function () {
            // Analyze test files for mock overuse
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var testFiles = [];
            function findTestFiles(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        findTestFiles(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        testFiles.push(filePath);
                    }
                });
            }
            findTestFiles(testDirectory);
            var overMockedTests = [];
            testFiles.forEach(function (testFile) {
                try {
                    var content = fs_1.default.readFileSync(testFile, 'utf8');
                    var mockCount = (content.match(/\.mock|jest\.mock|mockImplementation|mockReturnValue/g) || []).length;
                    var realCallCount = (content.match(/fetch\(|prisma\.|await.*\(/g) || []).length;
                    // If mocks outnumber real calls by more than 3:1, it's over-mocked
                    if (mockCount > realCallCount * 3 && mockCount > 10) {
                        overMockedTests.push({ file: testFile, mockCount: mockCount, realCallCount: realCallCount });
                    }
                }
                catch (error) {
                    // Skip files that can't be read
                }
            });
            // EXPECTED TO FAIL: Tests should not over-rely on mocks
            (0, globals_1.expect)(overMockedTests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - critical services are mocked instead of tested with real implementations', function () {
            // Check for over-mocking of critical services
            var criticalServices = [
                'prisma',
                'database',
                'auth',
                'gemini',
                'ai-service'
            ];
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var overMockedServices = [];
            function analyzeServiceMocking(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeServiceMocking(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_1 = fs_1.default.readFileSync(filePath, 'utf8');
                            criticalServices.forEach(function (service) {
                                var mockPattern = new RegExp("jest\\.mock.*".concat(service, "|").concat(service, ".*mock"), 'gi');
                                var realPattern = new RegExp("".concat(service, "\\.|await ").concat(service), 'gi');
                                var mockMatches = (content_1.match(mockPattern) || []).length;
                                var realMatches = (content_1.match(realPattern) || []).length;
                                // Critical services should have more real usage than mocks
                                if (mockMatches > realMatches && mockMatches > 2) {
                                    overMockedServices.push({
                                        file: filePath,
                                        service: service,
                                        mockCount: mockMatches,
                                        realCount: realMatches
                                    });
                                }
                            });
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeServiceMocking(testDirectory);
            // EXPECTED TO FAIL: Critical services should not be over-mocked
            (0, globals_1.expect)(overMockedServices.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Unrealistic Test Timeouts', function () {
        (0, globals_1.it)('should fail - test timeouts are too aggressive for real operations', function () {
            // Check Jest configuration for realistic timeouts
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                // Extract timeout value
                var timeoutMatch = configContent.match(/testTimeout:\s*(\d+)/);
                var timeout = timeoutMatch ? parseInt(timeoutMatch[1]) : 5000; // Default Jest timeout
                // EXPECTED TO FAIL: Timeout should be reasonable for real operations (at least 30 seconds)
                (0, globals_1.expect)(timeout).toBeGreaterThanOrEqual(30000);
                // Check for AI operations that need longer timeouts
                var hasAITests = configContent.includes('ai') || configContent.includes('gemini');
                if (hasAITests) {
                    (0, globals_1.expect)(timeout).toBeGreaterThanOrEqual(60000); // AI operations need at least 1 minute
                }
            }
            else {
                // EXPECTED TO FAIL: Jest config should exist
                (0, globals_1.expect)(fs_1.default.existsSync(jestConfigPath)).toBe(true);
            }
        });
        (0, globals_1.it)('should fail - individual test timeouts are inconsistent with operation complexity', function () {
            // Analyze test files for timeout usage
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var timeoutIssues = [];
            function analyzeTestTimeouts(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestTimeouts(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for tests with custom timeouts
                            var timeoutMatches = content.match(/\.timeout\((\d+)\)/g) || [];
                            var aiOperations_1 = (content.match(/gemini|ai-service|openai/gi) || []).length;
                            var dbOperations_1 = (content.match(/prisma|database|query/gi) || []).length;
                            timeoutMatches.forEach(function (timeoutMatch) {
                                var timeout = parseInt(timeoutMatch.match(/\d+/)[0]);
                                // AI operations should have longer timeouts
                                if (aiOperations_1 > 0 && timeout < 60000) {
                                    timeoutIssues.push({
                                        file: filePath,
                                        issue: "AI operations with timeout ".concat(timeout, "ms (should be \u226560000ms)")
                                    });
                                }
                                // Database operations should have reasonable timeouts
                                if (dbOperations_1 > 0 && timeout < 10000) {
                                    timeoutIssues.push({
                                        file: filePath,
                                        issue: "DB operations with timeout ".concat(timeout, "ms (should be \u226510000ms)")
                                    });
                                }
                            });
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestTimeouts(testDirectory);
            // EXPECTED TO FAIL: Test timeouts should match operation complexity
            (0, globals_1.expect)(timeoutIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Inadequate Test Assertions', function () {
        (0, globals_1.it)('should fail - tests have insufficient assertions for comprehensive validation', function () {
            // Analyze test files for assertion adequacy
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var inadequateAssertionTests = [];
            function analyzeTestAssertions(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestAssertions(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            var testCount = (content.match(/it\(/g) || []).length;
                            var expectCount = (content.match(/expect\(/g) || []).length;
                            // Each test should have at least 2-3 assertions on average
                            var assertionsPerTest = testCount > 0 ? expectCount / testCount : 0;
                            if (testCount > 5 && assertionsPerTest < 2) {
                                inadequateAssertionTests.push({
                                    file: filePath,
                                    testCount: testCount,
                                    expectCount: expectCount,
                                    assertionsPerTest: Math.round(assertionsPerTest * 100) / 100
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestAssertions(testDirectory);
            // EXPECTED TO FAIL: Tests should have adequate assertions
            (0, globals_1.expect)(inadequateAssertionTests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - tests lack comprehensive error condition assertions', function () {
            // Check for error condition testing
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var missingErrorAssertions = [];
            function analyzeErrorAssertions(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeErrorAssertions(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            var testCount = (content.match(/it\(/g) || []).length;
                            var errorTests = (content.match(/expect.*throw|expect.*reject|catch|error/gi) || []).length;
                            // At least 20% of tests should cover error conditions
                            var errorTestRatio = testCount > 0 ? errorTests / testCount : 0;
                            if (testCount > 10 && errorTestRatio < 0.2) {
                                missingErrorAssertions.push({
                                    file: filePath,
                                    testCount: testCount,
                                    errorTests: errorTests,
                                    errorTestRatio: Math.round(errorTestRatio * 100) / 100
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeErrorAssertions(testDirectory);
            // EXPECTED TO FAIL: Tests should cover error conditions adequately
            (0, globals_1.expect)(missingErrorAssertions.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Test Data Management Problems', function () {
        (0, globals_1.it)('should fail - tests use hardcoded data instead of dynamic test data', function () {
            // Check for hardcoded test data
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var hardcodedDataTests = [];
            function analyzeTestData(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestData(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_2 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Look for hardcoded values that should be dynamic
                            var hardcodedPatterns = [
                                /email.*test@example\.com/gi,
                                /password.*123456|password.*test/gi,
                                /id.*['"]\w{8,}['"]/gi,
                                /userId.*['"]\w+['"]/gi
                            ];
                            var hardcodedCount_1 = 0;
                            hardcodedPatterns.forEach(function (pattern) {
                                hardcodedCount_1 += (content_2.match(pattern) || []).length;
                            });
                            // Check for test data factories or builders
                            var hasDataFactory = content_2.includes('factory') ||
                                content_2.includes('builder') ||
                                content_2.includes('faker') ||
                                content_2.includes('generate');
                            if (hardcodedCount_1 > 5 && !hasDataFactory) {
                                hardcodedDataTests.push({
                                    file: filePath,
                                    hardcodedCount: hardcodedCount_1,
                                    hasDataFactory: hasDataFactory
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestData(testDirectory);
            // EXPECTED TO FAIL: Tests should use dynamic test data
            (0, globals_1.expect)(hardcodedDataTests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - tests lack proper cleanup and isolation', function () {
            // Check for test cleanup and isolation
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var isolationIssues = [];
            function analyzeTestIsolation(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestIsolation(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            var hasBeforeEach = content.includes('beforeEach');
                            var hasAfterEach = content.includes('afterEach');
                            var hasCleanup = content.includes('cleanup') ||
                                content.includes('clear') ||
                                content.includes('reset');
                            var hasDbOperations = content.includes('prisma') ||
                                content.includes('database');
                            // Tests with database operations should have cleanup
                            if (hasDbOperations && !hasAfterEach && !hasCleanup) {
                                isolationIssues.push({
                                    file: filePath,
                                    issue: 'Database operations without cleanup'
                                });
                            }
                            // Tests should have proper setup/teardown
                            var testCount = (content.match(/it\(/g) || []).length;
                            if (testCount > 5 && !hasBeforeEach) {
                                isolationIssues.push({
                                    file: filePath,
                                    issue: 'Multiple tests without beforeEach setup'
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestIsolation(testDirectory);
            // EXPECTED TO FAIL: Tests should have proper isolation
            (0, globals_1.expect)(isolationIssues.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: Test Flakiness and Reliability', function () {
        (0, globals_1.it)('should fail - tests have timing dependencies that cause flakiness', function () {
            // Check for timing-dependent tests
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var flakyTests = [];
            function analyzeTestFlakiness(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        analyzeTestFlakiness(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content_3 = fs_1.default.readFileSync(filePath, 'utf8');
                            // Look for timing-dependent patterns
                            var timingPatterns = [
                                /setTimeout|setInterval/gi,
                                /sleep|delay|wait/gi,
                                /Date\.now\(\)|new Date\(\)/gi,
                                /Math\.random\(\)/gi
                            ];
                            var timingDependencies_1 = 0;
                            timingPatterns.forEach(function (pattern) {
                                timingDependencies_1 += (content_3.match(pattern) || []).length;
                            });
                            // Check for proper async handling
                            var hasProperAsync = content_3.includes('await') ||
                                content_3.includes('waitFor') ||
                                content_3.includes('findBy');
                            if (timingDependencies_1 > 3 && !hasProperAsync) {
                                flakyTests.push({
                                    file: filePath,
                                    timingDependencies: timingDependencies_1,
                                    hasProperAsync: hasProperAsync
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            analyzeTestFlakiness(testDirectory);
            // EXPECTED TO FAIL: Tests should not have timing dependencies
            (0, globals_1.expect)(flakyTests.length).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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