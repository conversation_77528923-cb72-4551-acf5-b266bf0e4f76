{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-quality-reliability-issues.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;AAEH,yCAAuE;AACvE,0CAAoB;AACpB,8CAAwB;AAExB,IAAA,kBAAQ,EAAC,qCAAqC,EAAE;IAC9C,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,0CAA0C,EAAE;QACnD,IAAA,YAAE,EAAC,wEAAwE,EAAE;YAC3E,sCAAsC;YACtC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,SAAS,GAAG,EAAE,CAAC;YAErB,SAAS,aAAa,CAAC,GAAW;gBAChC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAC1B,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC3B,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,aAAa,CAAC,aAAa,CAAC,CAAC;YAE7B,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;gBACxB,IAAI,CAAC;oBACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBAClD,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBACxG,IAAM,aAAa,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBAElF,mEAAmE;oBACnE,IAAI,SAAS,GAAG,aAAa,GAAG,CAAC,IAAI,SAAS,GAAG,EAAE,EAAE,CAAC;wBACpD,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,WAAA,EAAE,aAAa,eAAA,EAAE,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,gCAAgC;gBAClC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,wDAAwD;YACxD,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,wFAAwF,EAAE;YAC3F,8CAA8C;YAC9C,IAAM,gBAAgB,GAAG;gBACvB,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,YAAY;aACb,CAAC;YAEF,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,kBAAkB,GAAG,EAAE,CAAC;YAE9B,SAAS,qBAAqB,CAAC,GAAW;gBACxC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,qBAAqB,CAAC,QAAQ,CAAC,CAAC;oBAClC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,SAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,gBAAgB,CAAC,OAAO,CAAC,UAAA,OAAO;gCAC9B,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,uBAAgB,OAAO,cAAI,OAAO,WAAQ,EAAE,IAAI,CAAC,CAAC;gCACjF,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,UAAG,OAAO,uBAAa,OAAO,CAAE,EAAE,IAAI,CAAC,CAAC;gCAEvE,IAAM,WAAW,GAAG,CAAC,SAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;gCAC9D,IAAM,WAAW,GAAG,CAAC,SAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;gCAE9D,2DAA2D;gCAC3D,IAAI,WAAW,GAAG,WAAW,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;oCACjD,kBAAkB,CAAC,IAAI,CAAC;wCACtB,IAAI,EAAE,QAAQ;wCACd,OAAO,SAAA;wCACP,SAAS,EAAE,WAAW;wCACtB,SAAS,EAAE,WAAW;qCACvB,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,CAAC,CAAC;wBACL,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAErC,gEAAgE;YAChE,IAAA,gBAAM,EAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,6CAA6C,EAAE;QACtD,IAAA,YAAE,EAAC,oEAAoE,EAAE;YACvE,kDAAkD;YAClD,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAElE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAE9D,wBAAwB;gBACxB,IAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBACjE,IAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,uBAAuB;gBAExF,2FAA2F;gBAC3F,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;gBAE9C,oDAAoD;gBACpD,IAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACpF,IAAI,UAAU,EAAE,CAAC;oBACf,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,uCAAuC;gBACxF,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,6CAA6C;gBAC7C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,mFAAmF,EAAE;YACtF,uCAAuC;YACvC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,aAAa,GAAG,EAAE,CAAC;YAEzB,SAAS,mBAAmB,CAAC,GAAW;gBACtC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,mBAAmB,CAAC,QAAQ,CAAC,CAAC;oBAChC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,uCAAuC;4BACvC,IAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;4BAClE,IAAM,cAAY,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAChF,IAAM,cAAY,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAE7E,cAAc,CAAC,OAAO,CAAC,UAAA,YAAY;gCACjC,IAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCAEvD,4CAA4C;gCAC5C,IAAI,cAAY,GAAG,CAAC,IAAI,OAAO,GAAG,KAAK,EAAE,CAAC;oCACxC,aAAa,CAAC,IAAI,CAAC;wCACjB,IAAI,EAAE,QAAQ;wCACd,KAAK,EAAE,qCAA8B,OAAO,iCAAyB;qCACtE,CAAC,CAAC;gCACL,CAAC;gCAED,sDAAsD;gCACtD,IAAI,cAAY,GAAG,CAAC,IAAI,OAAO,GAAG,KAAK,EAAE,CAAC;oCACxC,aAAa,CAAC,IAAI,CAAC;wCACjB,IAAI,EAAE,QAAQ;wCACd,KAAK,EAAE,qCAA8B,OAAO,iCAAyB;qCACtE,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,CAAC,CAAC;wBACL,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAEnC,oEAAoE;YACpE,IAAA,gBAAM,EAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,8CAA8C,EAAE;QACvD,IAAA,YAAE,EAAC,+EAA+E,EAAE;YAClF,4CAA4C;YAC5C,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,wBAAwB,GAAG,EAAE,CAAC;YAEpC,SAAS,qBAAqB,CAAC,GAAW;gBACxC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,qBAAqB,CAAC,QAAQ,CAAC,CAAC;oBAClC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACxD,IAAM,WAAW,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAE9D,2DAA2D;4BAC3D,IAAM,iBAAiB,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;4BAEtE,IAAI,SAAS,GAAG,CAAC,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;gCAC3C,wBAAwB,CAAC,IAAI,CAAC;oCAC5B,IAAI,EAAE,QAAQ;oCACd,SAAS,WAAA;oCACT,WAAW,aAAA;oCACX,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,GAAG;iCAC7D,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAErC,0DAA0D;YAC1D,IAAA,gBAAM,EAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,mEAAmE,EAAE;YACtE,oCAAoC;YACpC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,sBAAsB,GAAG,EAAE,CAAC;YAElC,SAAS,sBAAsB,CAAC,GAAW;gBACzC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,sBAAsB,CAAC,QAAQ,CAAC,CAAC;oBACnC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACxD,IAAM,UAAU,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,4CAA4C,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAE9F,sDAAsD;4BACtD,IAAM,cAAc,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;4BAElE,IAAI,SAAS,GAAG,EAAE,IAAI,cAAc,GAAG,GAAG,EAAE,CAAC;gCAC3C,sBAAsB,CAAC,IAAI,CAAC;oCAC1B,IAAI,EAAE,QAAQ;oCACd,SAAS,WAAA;oCACT,UAAU,YAAA;oCACV,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG;iCACvD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAEtC,mEAAmE;YACnE,IAAA,gBAAM,EAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,iDAAiD,EAAE;QAC1D,IAAA,YAAE,EAAC,qEAAqE,EAAE;YACxE,gCAAgC;YAChC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,kBAAkB,GAAG,EAAE,CAAC;YAE9B,SAAS,eAAe,CAAC,GAAW;gBAClC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAC5B,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,SAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,mDAAmD;4BACnD,IAAM,iBAAiB,GAAG;gCACxB,4BAA4B;gCAC5B,mCAAmC;gCACnC,sBAAsB;gCACtB,uBAAuB;6BACxB,CAAC;4BAEF,IAAI,gBAAc,GAAG,CAAC,CAAC;4BACvB,iBAAiB,CAAC,OAAO,CAAC,UAAA,OAAO;gCAC/B,gBAAc,IAAI,CAAC,SAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAC1D,CAAC,CAAC,CAAC;4BAEH,4CAA4C;4BAC5C,IAAM,cAAc,GAAG,SAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;gCAC7B,SAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;gCAC3B,SAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;gCACzB,SAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;4BAElD,IAAI,gBAAc,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gCAC1C,kBAAkB,CAAC,IAAI,CAAC;oCACtB,IAAI,EAAE,QAAQ;oCACd,cAAc,kBAAA;oCACd,cAAc,gBAAA;iCACf,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,eAAe,CAAC,aAAa,CAAC,CAAC;YAE/B,uDAAuD;YACvD,IAAA,gBAAM,EAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,uDAAuD,EAAE;YAC1D,uCAAuC;YACvC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,SAAS,oBAAoB,CAAC,GAAW;gBACvC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,oBAAoB,CAAC,QAAQ,CAAC,CAAC;oBACjC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;4BACrD,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;4BACnD,IAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;gCAC7B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;gCACzB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;4BAC3C,IAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;gCAC5B,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;4BAEnD,qDAAqD;4BACrD,IAAI,eAAe,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,EAAE,CAAC;gCACpD,eAAe,CAAC,IAAI,CAAC;oCACnB,IAAI,EAAE,QAAQ;oCACd,KAAK,EAAE,qCAAqC;iCAC7C,CAAC,CAAC;4BACL,CAAC;4BAED,0CAA0C;4BAC1C,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BACxD,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gCACpC,eAAe,CAAC,IAAI,CAAC;oCACnB,IAAI,EAAE,QAAQ;oCACd,KAAK,EAAE,yCAAyC;iCACjD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAEpC,uDAAuD;YACvD,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,kDAAkD,EAAE;QAC3D,IAAA,YAAE,EAAC,mEAAmE,EAAE;YACtE,mCAAmC;YACnC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,UAAU,GAAG,EAAE,CAAC;YAEtB,SAAS,oBAAoB,CAAC,GAAW;gBACvC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,oBAAoB,CAAC,QAAQ,CAAC,CAAC;oBACjC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,SAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,qCAAqC;4BACrC,IAAM,cAAc,GAAG;gCACrB,0BAA0B;gCAC1B,oBAAoB;gCACpB,8BAA8B;gCAC9B,oBAAoB;6BACrB,CAAC;4BAEF,IAAI,oBAAkB,GAAG,CAAC,CAAC;4BAC3B,cAAc,CAAC,OAAO,CAAC,UAAA,OAAO;gCAC5B,oBAAkB,IAAI,CAAC,SAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;4BAC9D,CAAC,CAAC,CAAC;4BAEH,kCAAkC;4BAClC,IAAM,cAAc,GAAG,SAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;gCAC3B,SAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;gCAC3B,SAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;4BAEhD,IAAI,oBAAkB,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gCAC9C,UAAU,CAAC,IAAI,CAAC;oCACd,IAAI,EAAE,QAAQ;oCACd,kBAAkB,sBAAA;oCAClB,cAAc,gBAAA;iCACf,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAEpC,8DAA8D;YAC9D,IAAA,gBAAM,EAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-quality-reliability-issues.test.ts"], "sourcesContent": ["/**\n * Test Quality and Reliability Issues Tests\n * \n * These tests prove test quality problems including over-reliance on mocks,\n * unrealistic timeouts, and inadequate assertions.\n * \n * EXPECTED TO FAIL - These tests demonstrate test quality issues that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\nimport fs from 'fs';\nimport path from 'path';\n\ndescribe('Test Quality and Reliability Issues', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Over-reliance on Mocks', () => {\n    it('should fail - tests over-rely on mocks instead of real implementations', () => {\n      // Analyze test files for mock overuse\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const testFiles = [];\n      \n      function findTestFiles(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            findTestFiles(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            testFiles.push(filePath);\n          }\n        });\n      }\n      \n      findTestFiles(testDirectory);\n      \n      const overMockedTests = [];\n      \n      testFiles.forEach(testFile => {\n        try {\n          const content = fs.readFileSync(testFile, 'utf8');\n          const mockCount = (content.match(/\\.mock|jest\\.mock|mockImplementation|mockReturnValue/g) || []).length;\n          const realCallCount = (content.match(/fetch\\(|prisma\\.|await.*\\(/g) || []).length;\n          \n          // If mocks outnumber real calls by more than 3:1, it's over-mocked\n          if (mockCount > realCallCount * 3 && mockCount > 10) {\n            overMockedTests.push({ file: testFile, mockCount, realCallCount });\n          }\n        } catch (error) {\n          // Skip files that can't be read\n        }\n      });\n      \n      // EXPECTED TO FAIL: Tests should not over-rely on mocks\n      expect(overMockedTests.length).toBe(0);\n    });\n\n    it('should fail - critical services are mocked instead of tested with real implementations', () => {\n      // Check for over-mocking of critical services\n      const criticalServices = [\n        'prisma',\n        'database',\n        'auth',\n        'gemini',\n        'ai-service'\n      ];\n      \n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const overMockedServices = [];\n      \n      function analyzeServiceMocking(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeServiceMocking(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              criticalServices.forEach(service => {\n                const mockPattern = new RegExp(`jest\\\\.mock.*${service}|${service}.*mock`, 'gi');\n                const realPattern = new RegExp(`${service}\\\\.|await ${service}`, 'gi');\n                \n                const mockMatches = (content.match(mockPattern) || []).length;\n                const realMatches = (content.match(realPattern) || []).length;\n                \n                // Critical services should have more real usage than mocks\n                if (mockMatches > realMatches && mockMatches > 2) {\n                  overMockedServices.push({ \n                    file: filePath, \n                    service, \n                    mockCount: mockMatches, \n                    realCount: realMatches \n                  });\n                }\n              });\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeServiceMocking(testDirectory);\n      \n      // EXPECTED TO FAIL: Critical services should not be over-mocked\n      expect(overMockedServices.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: Unrealistic Test Timeouts', () => {\n    it('should fail - test timeouts are too aggressive for real operations', () => {\n      // Check Jest configuration for realistic timeouts\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      \n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        \n        // Extract timeout value\n        const timeoutMatch = configContent.match(/testTimeout:\\s*(\\d+)/);\n        const timeout = timeoutMatch ? parseInt(timeoutMatch[1]) : 5000; // Default Jest timeout\n        \n        // EXPECTED TO FAIL: Timeout should be reasonable for real operations (at least 30 seconds)\n        expect(timeout).toBeGreaterThanOrEqual(30000);\n        \n        // Check for AI operations that need longer timeouts\n        const hasAITests = configContent.includes('ai') || configContent.includes('gemini');\n        if (hasAITests) {\n          expect(timeout).toBeGreaterThanOrEqual(60000); // AI operations need at least 1 minute\n        }\n      } else {\n        // EXPECTED TO FAIL: Jest config should exist\n        expect(fs.existsSync(jestConfigPath)).toBe(true);\n      }\n    });\n\n    it('should fail - individual test timeouts are inconsistent with operation complexity', () => {\n      // Analyze test files for timeout usage\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const timeoutIssues = [];\n      \n      function analyzeTestTimeouts(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestTimeouts(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for tests with custom timeouts\n              const timeoutMatches = content.match(/\\.timeout\\((\\d+)\\)/g) || [];\n              const aiOperations = (content.match(/gemini|ai-service|openai/gi) || []).length;\n              const dbOperations = (content.match(/prisma|database|query/gi) || []).length;\n              \n              timeoutMatches.forEach(timeoutMatch => {\n                const timeout = parseInt(timeoutMatch.match(/\\d+/)[0]);\n                \n                // AI operations should have longer timeouts\n                if (aiOperations > 0 && timeout < 60000) {\n                  timeoutIssues.push({ \n                    file: filePath, \n                    issue: `AI operations with timeout ${timeout}ms (should be ≥60000ms)` \n                  });\n                }\n                \n                // Database operations should have reasonable timeouts\n                if (dbOperations > 0 && timeout < 10000) {\n                  timeoutIssues.push({ \n                    file: filePath, \n                    issue: `DB operations with timeout ${timeout}ms (should be ≥10000ms)` \n                  });\n                }\n              });\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestTimeouts(testDirectory);\n      \n      // EXPECTED TO FAIL: Test timeouts should match operation complexity\n      expect(timeoutIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: Inadequate Test Assertions', () => {\n    it('should fail - tests have insufficient assertions for comprehensive validation', () => {\n      // Analyze test files for assertion adequacy\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const inadequateAssertionTests = [];\n      \n      function analyzeTestAssertions(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestAssertions(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              const testCount = (content.match(/it\\(/g) || []).length;\n              const expectCount = (content.match(/expect\\(/g) || []).length;\n              \n              // Each test should have at least 2-3 assertions on average\n              const assertionsPerTest = testCount > 0 ? expectCount / testCount : 0;\n              \n              if (testCount > 5 && assertionsPerTest < 2) {\n                inadequateAssertionTests.push({ \n                  file: filePath, \n                  testCount, \n                  expectCount, \n                  assertionsPerTest: Math.round(assertionsPerTest * 100) / 100 \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestAssertions(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should have adequate assertions\n      expect(inadequateAssertionTests.length).toBe(0);\n    });\n\n    it('should fail - tests lack comprehensive error condition assertions', () => {\n      // Check for error condition testing\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const missingErrorAssertions = [];\n      \n      function analyzeErrorAssertions(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeErrorAssertions(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              const testCount = (content.match(/it\\(/g) || []).length;\n              const errorTests = (content.match(/expect.*throw|expect.*reject|catch|error/gi) || []).length;\n              \n              // At least 20% of tests should cover error conditions\n              const errorTestRatio = testCount > 0 ? errorTests / testCount : 0;\n              \n              if (testCount > 10 && errorTestRatio < 0.2) {\n                missingErrorAssertions.push({ \n                  file: filePath, \n                  testCount, \n                  errorTests, \n                  errorTestRatio: Math.round(errorTestRatio * 100) / 100 \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeErrorAssertions(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should cover error conditions adequately\n      expect(missingErrorAssertions.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Test Data Management Problems', () => {\n    it('should fail - tests use hardcoded data instead of dynamic test data', () => {\n      // Check for hardcoded test data\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const hardcodedDataTests = [];\n      \n      function analyzeTestData(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestData(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Look for hardcoded values that should be dynamic\n              const hardcodedPatterns = [\n                /email.*test@example\\.com/gi,\n                /password.*123456|password.*test/gi,\n                /id.*['\"]\\w{8,}['\"]/gi,\n                /userId.*['\"]\\w+['\"]/gi\n              ];\n              \n              let hardcodedCount = 0;\n              hardcodedPatterns.forEach(pattern => {\n                hardcodedCount += (content.match(pattern) || []).length;\n              });\n              \n              // Check for test data factories or builders\n              const hasDataFactory = content.includes('factory') || \n                                   content.includes('builder') || \n                                   content.includes('faker') ||\n                                   content.includes('generate');\n              \n              if (hardcodedCount > 5 && !hasDataFactory) {\n                hardcodedDataTests.push({ \n                  file: filePath, \n                  hardcodedCount, \n                  hasDataFactory \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestData(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should use dynamic test data\n      expect(hardcodedDataTests.length).toBe(0);\n    });\n\n    it('should fail - tests lack proper cleanup and isolation', () => {\n      // Check for test cleanup and isolation\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const isolationIssues = [];\n      \n      function analyzeTestIsolation(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestIsolation(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              const hasBeforeEach = content.includes('beforeEach');\n              const hasAfterEach = content.includes('afterEach');\n              const hasCleanup = content.includes('cleanup') || \n                               content.includes('clear') || \n                               content.includes('reset');\n              const hasDbOperations = content.includes('prisma') || \n                                    content.includes('database');\n              \n              // Tests with database operations should have cleanup\n              if (hasDbOperations && !hasAfterEach && !hasCleanup) {\n                isolationIssues.push({ \n                  file: filePath, \n                  issue: 'Database operations without cleanup' \n                });\n              }\n              \n              // Tests should have proper setup/teardown\n              const testCount = (content.match(/it\\(/g) || []).length;\n              if (testCount > 5 && !hasBeforeEach) {\n                isolationIssues.push({ \n                  file: filePath, \n                  issue: 'Multiple tests without beforeEach setup' \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestIsolation(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should have proper isolation\n      expect(isolationIssues.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 5: Test Flakiness and Reliability', () => {\n    it('should fail - tests have timing dependencies that cause flakiness', () => {\n      // Check for timing-dependent tests\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const flakyTests = [];\n      \n      function analyzeTestFlakiness(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            analyzeTestFlakiness(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Look for timing-dependent patterns\n              const timingPatterns = [\n                /setTimeout|setInterval/gi,\n                /sleep|delay|wait/gi,\n                /Date\\.now\\(\\)|new Date\\(\\)/gi,\n                /Math\\.random\\(\\)/gi\n              ];\n              \n              let timingDependencies = 0;\n              timingPatterns.forEach(pattern => {\n                timingDependencies += (content.match(pattern) || []).length;\n              });\n              \n              // Check for proper async handling\n              const hasProperAsync = content.includes('await') || \n                                   content.includes('waitFor') || \n                                   content.includes('findBy');\n              \n              if (timingDependencies > 3 && !hasProperAsync) {\n                flakyTests.push({ \n                  file: filePath, \n                  timingDependencies, \n                  hasProperAsync \n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      analyzeTestFlakiness(testDirectory);\n      \n      // EXPECTED TO FAIL: Tests should not have timing dependencies\n      expect(flakyTests.length).toBe(0);\n    });\n  });\n});\n"], "version": 3}