5df0348788b9dde20256d10095d6339a
"use strict";
/**
 * EdgeCaseHandler Business Logic Tests
 * Focused on business logic edge cases and data consistency
 * Optimized for fast execution
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EdgeCaseHandler_1 = require("@/lib/skills/EdgeCaseHandler");
// Shared mock factories for performance
var createMockServices = function () { return ({
    mockAssessmentEngine: {
        createAssessment: jest.fn(),
        generateQuestions: jest.fn(),
        submitResponse: jest.fn(),
        calculateResults: jest.fn(),
        getAssessment: jest.fn(),
        getAssessmentsByUser: jest.fn(),
    },
    mockMarketDataService: {
        getSkillMarketData: jest.fn(),
        getMultipleSkillsMarketData: jest.fn(),
        analyzeMarketTrends: jest.fn(),
        getSalaryInsights: jest.fn(),
        getLocationBasedMarketData: jest.fn(),
        getMarketBasedRecommendations: jest.fn(),
    },
    mockLearningPathService: {
        generateLearningPath: jest.fn(),
        updateProgress: jest.fn(),
        completeMilestone: jest.fn(),
    },
}); };
describe('EdgeCaseHandler - Business Logic', function () {
    var edgeCaseHandler;
    var mocks;
    beforeEach(function () {
        mocks = createMockServices();
        edgeCaseHandler = new EdgeCaseHandler_1.EdgeCaseHandler(mocks.mockAssessmentEngine, mocks.mockMarketDataService, mocks.mockLearningPathService);
    });
    describe('Non-existent Data', function () {
        it('should handle non-existent skills gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['non-existent-skill', 'another-fake-skill'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Skill not found');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('One or more skills not found');
                        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
                        expect(result.fallbackData).toBeDefined();
                        expect(result.suggestedAlternatives).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle non-existent career paths', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Quantum Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            throw new Error('Career path not found');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Career path not found');
                        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
                        expect(result.suggestedAlternatives).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle users with no existing data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'new-user-with-no-data',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.getAssessmentsByUser.mockReturnValue([]);
                        mocks.mockAssessmentEngine.createAssessment.mockResolvedValue({
                            id: 'new-user-assessment',
                            userId: request.userId,
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.data).toBeDefined();
                        expect(result.isNewUser).toBe(true);
                        expect(result.onboardingRecommendations).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
    });
    describe('Conflicting Requirements', function () {
        it('should handle circular dependencies in learning paths', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            throw new Error('Circular dependency detected');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Circular dependency');
                        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle impossible time/budget constraints', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'html', level: 1, confidence: 2 }],
                            targetRole: 'Senior Full Stack Developer',
                            timeframe: 1,
                            learningStyle: 'casual',
                            availability: 1,
                            budget: 1,
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Impossible constraints');
                        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
                        expect(result.feasibilityAnalysis).toBeDefined();
                        expect(result.suggestedAdjustments).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
    });
    describe('Data Consistency Issues', function () {
        it('should handle inconsistent skill ratings', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillAssessments: [
                                { skill: 'javascript', selfRating: 9, confidenceLevel: 2 },
                                { skill: 'react', selfRating: 3, confidenceLevel: 9 },
                            ],
                        };
                        return [4 /*yield*/, edgeCaseHandler.validateSkillConsistency(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Inconsistent skill ratings');
                        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
                        expect(result.inconsistencies).toBeDefined();
                        expect(result.suggestedCorrections).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle conflicting market data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skill, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        skill = 'javascript';
                        mocks.mockMarketDataService.getSkillMarketData.mockResolvedValue({
                            skill: skill,
                            demand: 95,
                            supply: 98,
                            averageSalary: -50000,
                            growth: 150,
                            difficulty: 15,
                            timeToLearn: -5,
                            category: '',
                            lastUpdated: new Date(),
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleMarketDataRequest({ skill: skill })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Inconsistent market data');
                        expect(result.errorType).toBe('DATA_CONSISTENCY_ERROR');
                        expect(result.correctedData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
    });
    describe('Concurrency Edge Cases', function () {
        it('should handle concurrent assessment submissions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, promises, results;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        promises = Array(5).fill(null).map(function () {
                            return edgeCaseHandler.handleSkillAssessment(request);
                        });
                        return [4 /*yield*/, Promise.all(promises)];
                    case 1:
                        results = _a.sent();
                        expect(results.every(function (r) { return r.success || r.errorType === 'CONCURRENCY_ERROR'; })).toBe(true);
                        expect(results.filter(function (r) { return r.success; }).length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); }, 10000);
        it('should handle resource contention', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Resource locked');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Resource locked');
                        expect(result.errorType).toBe('CONCURRENCY_ERROR');
                        expect(result.retryable).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
    });
    describe('Recovery and Fallback Mechanisms', function () {
        it('should implement automatic retry for transient failures', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, callCount, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        callCount = 0;
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            callCount++;
                            if (callCount < 3) {
                                throw new Error('Transient error');
                            }
                            return { id: 'assessment-123' };
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request, { maxRetries: 3 })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.retryCount).toBe(2);
                        expect(callCount).toBe(3);
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
        it('should provide fallback data when services fail', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            skill: 'javascript',
                        };
                        mocks.mockMarketDataService.getSkillMarketData.mockImplementation(function () {
                            throw new Error('Service unavailable');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleMarketDataRequest(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.fallbackData).toBeDefined();
                        expect(result.fallbackData.skill).toBe('javascript');
                        expect(result.fallbackData.isStale).toBe(true);
                        expect(result.fallbackData.source).toBe('cache_or_default');
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should implement circuit breaker pattern', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, i, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Service down');
                        });
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < 3)) return [3 /*break*/, 4];
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 5:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.errorType).toBe('CIRCUIT_BREAKER_OPEN');
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL19fdGVzdHNfXy9saWIvc2tpbGxzL2VkZ2UtY2FzZS1oYW5kbGVyLmJ1c2luZXNzLWxvZ2ljLnRlc3QudHMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7O0dBSUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRUgsZ0VBQStEO0FBSy9ELHdDQUF3QztBQUN4QyxJQUFNLGtCQUFrQixHQUFHLGNBQU0sT0FBQSxDQUFDO0lBQ2hDLG9CQUFvQixFQUFFO1FBQ3BCLGdCQUFnQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDM0IsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUM1QixjQUFjLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUN6QixnQkFBZ0IsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQzNCLGFBQWEsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ3hCLG9CQUFvQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7S0FDekI7SUFDUixxQkFBcUIsRUFBRTtRQUNyQixrQkFBa0IsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQzdCLDJCQUEyQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDdEMsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUM5QixpQkFBaUIsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQzVCLDBCQUEwQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDckMsNkJBQTZCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUNsQztJQUNSLHVCQUF1QixFQUFFO1FBQ3ZCLG9CQUFvQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDL0IsY0FBYyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDekIsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUN0QjtDQUNULENBQUMsRUF0QitCLENBc0IvQixDQUFDO0FBRUgsUUFBUSxDQUFDLGtDQUFrQyxFQUFFO0lBQzNDLElBQUksZUFBZ0MsQ0FBQztJQUNyQyxJQUFJLEtBQTRDLENBQUM7SUFFakQsVUFBVSxDQUFDO1FBQ1QsS0FBSyxHQUFHLGtCQUFrQixFQUFFLENBQUM7UUFDN0IsZUFBZSxHQUFHLElBQUksaUNBQWUsQ0FDbkMsS0FBSyxDQUFDLG9CQUFvQixFQUMxQixLQUFLLENBQUMscUJBQXFCLEVBQzNCLEtBQUssQ0FBQyx1QkFBdUIsQ0FDOUIsQ0FBQztJQUNKLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLG1CQUFtQixFQUFFO1FBQzVCLEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRTs7Ozs7d0JBQzNDLE9BQU8sR0FBRzs0QkFDZCxNQUFNLEVBQUUsVUFBVTs0QkFDbEIsUUFBUSxFQUFFLENBQUMsb0JBQW9CLEVBQUUsb0JBQW9CLENBQUM7NEJBQ3RELFlBQVksRUFBRSxVQUFVO3lCQUN6QixDQUFDO3dCQUVGLEtBQUssQ0FBQyxvQkFBb0IsQ0FBQyxnQkFBZ0IsQ0FBQyxrQkFBa0IsQ0FBQzs0QkFDN0QsTUFBTSxJQUFJLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO3dCQUNyQyxDQUFDLENBQUMsQ0FBQzt3QkFFWSxxQkFBTSxlQUFlLENBQUMscUJBQXFCLENBQUMsT0FBTyxDQUFDLEVBQUE7O3dCQUE3RCxNQUFNLEdBQUcsU0FBb0Q7d0JBRW5FLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO3dCQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLFNBQVMsQ0FBQyw4QkFBOEIsQ0FBQyxDQUFDO3dCQUMvRCxNQUFNLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO3dCQUN0RCxNQUFNLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO3dCQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLHFCQUFxQixDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7Ozs7YUFDcEQsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRTs7Ozs7d0JBQ3RDLE9BQU8sR0FBRzs0QkFDZCxNQUFNLEVBQUUsVUFBVTs0QkFDbEIsYUFBYSxFQUFFLENBQUMsRUFBRSxLQUFLLEVBQUUsWUFBWSxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsVUFBVSxFQUFFLENBQUMsRUFBRSxDQUFDOzRCQUNqRSxVQUFVLEVBQUUsbUJBQW1COzRCQUMvQixTQUFTLEVBQUUsQ0FBQzs0QkFDWixhQUFhLEVBQUUsWUFBcUI7NEJBQ3BDLFlBQVksRUFBRSxFQUFFOzRCQUNoQixNQUFNLEVBQUUsR0FBRzt5QkFDWixDQUFDO3dCQUVGLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQyxvQkFBb0IsQ0FBQyxrQkFBa0IsQ0FBQzs0QkFDcEUsTUFBTSxJQUFJLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDO3dCQUMzQyxDQUFDLENBQUMsQ0FBQzt3QkFFWSxxQkFBTSxlQUFlLENBQUMsNEJBQTRCLENBQUMsT0FBTyxDQUFDLEVBQUE7O3dCQUFwRSxNQUFNLEdBQUcsU0FBMkQ7d0JBRTFFLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO3dCQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLFNBQVMsQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDO3dCQUN4RCxNQUFNLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO3dCQUN0RCxNQUFNLENBQUMsTUFBTSxDQUFDLHFCQUFxQixDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7Ozs7YUFDcEQsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULEVBQUUsQ0FBQywyQ0FBMkMsRUFBRTs7Ozs7d0JBQ3hDLE9BQU8sR0FBRzs0QkFDZCxNQUFNLEVBQUUsdUJBQXVCOzRCQUMvQixRQUFRLEVBQUUsQ0FBQyxZQUFZLENBQUM7NEJBQ3hCLFlBQVksRUFBRSxVQUFVO3lCQUN6QixDQUFDO3dCQUVGLEtBQUssQ0FBQyxvQkFBb0IsQ0FBQyxvQkFBb0IsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQUM7d0JBQ3BFLEtBQUssQ0FBQyxvQkFBb0IsQ0FBQyxnQkFBZ0IsQ0FBQyxpQkFBaUIsQ0FBQzs0QkFDNUQsRUFBRSxFQUFFLHFCQUFxQjs0QkFDekIsTUFBTSxFQUFFLE9BQU8sQ0FBQyxNQUFNO3lCQUNoQixDQUFDLENBQUM7d0JBRUsscUJBQU0sZUFBZSxDQUFDLHFCQUFxQixDQUFDLE9BQU8sQ0FBQyxFQUFBOzt3QkFBN0QsTUFBTSxHQUFHLFNBQW9EO3dCQUVuRSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQzt3QkFDbEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQzt3QkFDbEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7d0JBQ3BDLE1BQU0sQ0FBQyxNQUFNLENBQUMseUJBQXlCLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQzs7OzthQUN4RCxFQUFFLElBQUksQ0FBQyxDQUFDO0lBQ1gsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsMEJBQTBCLEVBQUU7UUFDbkMsRUFBRSxDQUFDLHVEQUF1RCxFQUFFOzs7Ozt3QkFDcEQsT0FBTyxHQUFHOzRCQUNkLE1BQU0sRUFBRSxVQUFVOzRCQUNsQixhQUFhLEVBQUUsQ0FBQyxFQUFFLEtBQUssRUFBRSxZQUFZLEVBQUUsS0FBSyxFQUFFLENBQUMsRUFBRSxVQUFVLEVBQUUsQ0FBQyxFQUFFLENBQUM7NEJBQ2pFLFVBQVUsRUFBRSxzQkFBc0I7NEJBQ2xDLFNBQVMsRUFBRSxDQUFDOzRCQUNaLGFBQWEsRUFBRSxZQUFxQjs0QkFDcEMsWUFBWSxFQUFFLEVBQUU7NEJBQ2hCLE1BQU0sRUFBRSxHQUFHO3lCQUNaLENBQUM7d0JBRUYsS0FBSyxDQUFDLHVCQUF1QixDQUFDLG9CQUFvQixDQUFDLGtCQUFrQixDQUFDOzRCQUNwRSxNQUFNLElBQUksS0FBSyxDQUFDLDhCQUE4QixDQUFDLENBQUM7d0JBQ2xELENBQUMsQ0FBQyxDQUFDO3dCQUVZLHFCQUFNLGVBQWUsQ0FBQyw0QkFBNEIsQ0FBQyxPQUFPLENBQUMsRUFBQTs7d0JBQXBFLE1BQU0sR0FBRyxTQUEyRDt3QkFFMUUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7d0JBQ25DLE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsU0FBUyxDQUFDLHFCQUFxQixDQUFDLENBQUM7d0JBQ3RELE1BQU0sQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7d0JBQ3RELE1BQU0sQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7Ozs7YUFDM0MsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULEVBQUUsQ0FBQyxrREFBa0QsRUFBRTs7Ozs7d0JBQy9DLE9BQU8sR0FBRzs0QkFDZCxNQUFNLEVBQUUsVUFBVTs0QkFDbEIsYUFBYSxFQUFFLENBQUMsRUFBRSxLQUFLLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsVUFBVSxFQUFFLENBQUMsRUFBRSxDQUFDOzRCQUMzRCxVQUFVLEVBQUUsNkJBQTZCOzRCQUN6QyxTQUFTLEVBQUUsQ0FBQzs0QkFDWixhQUFhLEVBQUUsUUFBaUI7NEJBQ2hDLFlBQVksRUFBRSxDQUFDOzRCQUNmLE1BQU0sRUFBRSxDQUFDO3lCQUNWLENBQUM7d0JBRWEscUJBQU0sZUFBZSxDQUFDLDRCQUE0QixDQUFDLE9BQU8sQ0FBQyxFQUFBOzt3QkFBcEUsTUFBTSxHQUFHLFNBQTJEO3dCQUUxRSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQzt3QkFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxTQUFTLENBQUMsd0JBQXdCLENBQUMsQ0FBQzt3QkFDekQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQzt3QkFDdEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO3dCQUNqRCxNQUFNLENBQUMsTUFBTSxDQUFDLG9CQUFvQixDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7Ozs7YUFDbkQsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUNYLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHlCQUF5QixFQUFFO1FBQ2xDLEVBQUUsQ0FBQywwQ0FBMEMsRUFBRTs7Ozs7d0JBQ3ZDLE9BQU8sR0FBRzs0QkFDZCxNQUFNLEVBQUUsVUFBVTs0QkFDbEIsZ0JBQWdCLEVBQUU7Z0NBQ2hCLEVBQUUsS0FBSyxFQUFFLFlBQVksRUFBRSxVQUFVLEVBQUUsQ0FBQyxFQUFFLGVBQWUsRUFBRSxDQUFDLEVBQUU7Z0NBQzFELEVBQUUsS0FBSyxFQUFFLE9BQU8sRUFBRSxVQUFVLEVBQUUsQ0FBQyxFQUFFLGVBQWUsRUFBRSxDQUFDLEVBQUU7NkJBQ3REO3lCQUNGLENBQUM7d0JBRWEscUJBQU0sZUFBZSxDQUFDLHdCQUF3QixDQUFDLE9BQU8sQ0FBQyxFQUFBOzt3QkFBaEUsTUFBTSxHQUFHLFNBQXVEO3dCQUV0RSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQzt3QkFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxTQUFTLENBQUMsNEJBQTRCLENBQUMsQ0FBQzt3QkFDN0QsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQzt3QkFDdEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxlQUFlLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQzt3QkFDN0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDOzs7O2FBQ25ELEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFVCxFQUFFLENBQUMsdUNBQXVDLEVBQUU7Ozs7O3dCQUNwQyxLQUFLLEdBQUcsWUFBWSxDQUFDO3dCQUUzQixLQUFLLENBQUMscUJBQXFCLENBQUMsa0JBQWtCLENBQUMsaUJBQWlCLENBQUM7NEJBQy9ELEtBQUssT0FBQTs0QkFDTCxNQUFNLEVBQUUsRUFBRTs0QkFDVixNQUFNLEVBQUUsRUFBRTs0QkFDVixhQUFhLEVBQUUsQ0FBQyxLQUFLOzRCQUNyQixNQUFNLEVBQUUsR0FBRzs0QkFDWCxVQUFVLEVBQUUsRUFBRTs0QkFDZCxXQUFXLEVBQUUsQ0FBQyxDQUFDOzRCQUNmLFFBQVEsRUFBRSxFQUFFOzRCQUNaLFdBQVcsRUFBRSxJQUFJLElBQUksRUFBRTt5QkFDeEIsQ0FBQyxDQUFDO3dCQUVZLHFCQUFNLGVBQWUsQ0FBQyx1QkFBdUIsQ0FBQyxFQUFFLEtBQUssT0FBQSxFQUFFLENBQUMsRUFBQTs7d0JBQWpFLE1BQU0sR0FBRyxTQUF3RDt3QkFFdkUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7d0JBQ25DLE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsU0FBUyxDQUFDLDBCQUEwQixDQUFDLENBQUM7d0JBQzNELE1BQU0sQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLHdCQUF3QixDQUFDLENBQUM7d0JBQ3hELE1BQU0sQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7Ozs7YUFDNUMsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUNYLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHdCQUF3QixFQUFFO1FBQ2pDLEVBQUUsQ0FBQyxpREFBaUQsRUFBRTs7Ozs7d0JBQzlDLE9BQU8sR0FBRzs0QkFDZCxNQUFNLEVBQUUsVUFBVTs0QkFDbEIsUUFBUSxFQUFFLENBQUMsWUFBWSxDQUFDOzRCQUN4QixZQUFZLEVBQUUsVUFBVTt5QkFDekIsQ0FBQzt3QkFHSSxRQUFRLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxHQUFHLENBQUM7NEJBQ3ZDLE9BQUEsZUFBZSxDQUFDLHFCQUFxQixDQUFDLE9BQU8sQ0FBQzt3QkFBOUMsQ0FBOEMsQ0FDL0MsQ0FBQzt3QkFFYyxxQkFBTSxPQUFPLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxFQUFBOzt3QkFBckMsT0FBTyxHQUFHLFNBQTJCO3dCQUUzQyxNQUFNLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxPQUFPLElBQUksQ0FBQyxDQUFDLFNBQVMsS0FBSyxtQkFBbUIsRUFBaEQsQ0FBZ0QsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO3dCQUN4RixNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxPQUFPLEVBQVQsQ0FBUyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDOzs7O2FBQ2xFLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFFVixFQUFFLENBQUMsbUNBQW1DLEVBQUU7Ozs7O3dCQUNoQyxPQUFPLEdBQUc7NEJBQ2QsTUFBTSxFQUFFLFVBQVU7NEJBQ2xCLFFBQVEsRUFBRSxDQUFDLFlBQVksQ0FBQzs0QkFDeEIsWUFBWSxFQUFFLFVBQVU7eUJBQ3pCLENBQUM7d0JBRUYsS0FBSyxDQUFDLG9CQUFvQixDQUFDLGdCQUFnQixDQUFDLGtCQUFrQixDQUFDOzRCQUM3RCxNQUFNLElBQUksS0FBSyxDQUFDLGlCQUFpQixDQUFDLENBQUM7d0JBQ3JDLENBQUMsQ0FBQyxDQUFDO3dCQUVZLHFCQUFNLGVBQWUsQ0FBQyxxQkFBcUIsQ0FBQyxPQUFPLENBQUMsRUFBQTs7d0JBQTdELE1BQU0sR0FBRyxTQUFvRDt3QkFFbkUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7d0JBQ25DLE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsU0FBUyxDQUFDLGlCQUFpQixDQUFDLENBQUM7d0JBQ2xELE1BQU0sQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLG1CQUFtQixDQUFDLENBQUM7d0JBQ25ELE1BQU0sQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDOzs7O2FBQ3JDLEVBQUUsSUFBSSxDQUFDLENBQUM7SUFDWCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxrQ0FBa0MsRUFBRTtRQUMzQyxFQUFFLENBQUMseURBQXlELEVBQUU7Ozs7O3dCQUN0RCxPQUFPLEdBQUc7NEJBQ2QsTUFBTSxFQUFFLFVBQVU7NEJBQ2xCLFFBQVEsRUFBRSxDQUFDLFlBQVksQ0FBQzs0QkFDeEIsWUFBWSxFQUFFLFVBQVU7eUJBQ3pCLENBQUM7d0JBRUUsU0FBUyxHQUFHLENBQUMsQ0FBQzt3QkFDbEIsS0FBSyxDQUFDLG9CQUFvQixDQUFDLGdCQUFnQixDQUFDLGtCQUFrQixDQUFDOzRCQUM3RCxTQUFTLEVBQUUsQ0FBQzs0QkFDWixJQUFJLFNBQVMsR0FBRyxDQUFDLEVBQUUsQ0FBQztnQ0FDbEIsTUFBTSxJQUFJLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDOzRCQUNyQyxDQUFDOzRCQUNELE9BQU8sRUFBRSxFQUFFLEVBQUUsZ0JBQWdCLEVBQVMsQ0FBQzt3QkFDekMsQ0FBQyxDQUFDLENBQUM7d0JBRVkscUJBQU0sZUFBZSxDQUFDLHFCQUFxQixDQUFDLE9BQU8sRUFBRSxFQUFFLFVBQVUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFBOzt3QkFBaEYsTUFBTSxHQUFHLFNBQXVFO3dCQUV0RixNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQzt3QkFDbEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7d0JBQ2xDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7Ozs7YUFDM0IsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULEVBQUUsQ0FBQyxpREFBaUQsRUFBRTs7Ozs7d0JBQzlDLE9BQU8sR0FBRzs0QkFDZCxLQUFLLEVBQUUsWUFBWTt5QkFDcEIsQ0FBQzt3QkFFRixLQUFLLENBQUMscUJBQXFCLENBQUMsa0JBQWtCLENBQUMsa0JBQWtCLENBQUM7NEJBQ2hFLE1BQU0sSUFBSSxLQUFLLENBQUMscUJBQXFCLENBQUMsQ0FBQzt3QkFDekMsQ0FBQyxDQUFDLENBQUM7d0JBRVkscUJBQU0sZUFBZSxDQUFDLHVCQUF1QixDQUFDLE9BQU8sQ0FBQyxFQUFBOzt3QkFBL0QsTUFBTSxHQUFHLFNBQXNEO3dCQUVyRSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQzt3QkFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQzt3QkFDMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO3dCQUNyRCxNQUFNLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7d0JBQy9DLE1BQU0sQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDOzs7O2FBQzdELEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFVCxFQUFFLENBQUMsMENBQTBDLEVBQUU7Ozs7O3dCQUN2QyxPQUFPLEdBQUc7NEJBQ2QsTUFBTSxFQUFFLFVBQVU7NEJBQ2xCLFFBQVEsRUFBRSxDQUFDLFlBQVksQ0FBQzs0QkFDeEIsWUFBWSxFQUFFLFVBQVU7eUJBQ3pCLENBQUM7d0JBRUYsS0FBSyxDQUFDLG9CQUFvQixDQUFDLGdCQUFnQixDQUFDLGtCQUFrQixDQUFDOzRCQUM3RCxNQUFNLElBQUksS0FBSyxDQUFDLGNBQWMsQ0FBQyxDQUFDO3dCQUNsQyxDQUFDLENBQUMsQ0FBQzt3QkFHTSxDQUFDLEdBQUcsQ0FBQzs7OzZCQUFFLENBQUEsQ0FBQyxHQUFHLENBQUMsQ0FBQTt3QkFDbkIscUJBQU0sZUFBZSxDQUFDLHFCQUFxQixDQUFDLE9BQU8sQ0FBQyxFQUFBOzt3QkFBcEQsU0FBb0QsQ0FBQzs7O3dCQURoQyxDQUFDLEVBQUUsQ0FBQTs7NEJBSVgscUJBQU0sZUFBZSxDQUFDLHFCQUFxQixDQUFDLE9BQU8sQ0FBQyxFQUFBOzt3QkFBN0QsTUFBTSxHQUFHLFNBQW9EO3dCQUVuRSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQzt3QkFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQzt3QkFDdEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQzs7OzthQUMzQyxFQUFFLElBQUksQ0FBQyxDQUFDO0lBQ1gsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyIvVXNlcnMvZGQ2MC9mYWFmby9mYWFmby9mYWFmby1jYXJlZXItcGxhdGZvcm0vX190ZXN0c19fL2xpYi9za2lsbHMvZWRnZS1jYXNlLWhhbmRsZXIuYnVzaW5lc3MtbG9naWMudGVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEVkZ2VDYXNlSGFuZGxlciBCdXNpbmVzcyBMb2dpYyBUZXN0c1xuICogRm9jdXNlZCBvbiBidXNpbmVzcyBsb2dpYyBlZGdlIGNhc2VzIGFuZCBkYXRhIGNvbnNpc3RlbmN5XG4gKiBPcHRpbWl6ZWQgZm9yIGZhc3QgZXhlY3V0aW9uXG4gKi9cblxuaW1wb3J0IHsgRWRnZUNhc2VIYW5kbGVyIH0gZnJvbSAnQC9saWIvc2tpbGxzL0VkZ2VDYXNlSGFuZGxlcic7XG5pbXBvcnQgeyBTa2lsbEFzc2Vzc21lbnRFbmdpbmUgfSBmcm9tICdAL2xpYi9za2lsbHMvU2tpbGxBc3Nlc3NtZW50RW5naW5lJztcbmltcG9ydCB7IFNraWxsTWFya2V0RGF0YVNlcnZpY2UgfSBmcm9tICdAL2xpYi9za2lsbHMvU2tpbGxNYXJrZXREYXRhU2VydmljZSc7XG5pbXBvcnQgeyBQZXJzb25hbGl6ZWRMZWFybmluZ1BhdGhTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc2tpbGxzL1BlcnNvbmFsaXplZExlYXJuaW5nUGF0aFNlcnZpY2UnO1xuXG4vLyBTaGFyZWQgbW9jayBmYWN0b3JpZXMgZm9yIHBlcmZvcm1hbmNlXG5jb25zdCBjcmVhdGVNb2NrU2VydmljZXMgPSAoKSA9PiAoe1xuICBtb2NrQXNzZXNzbWVudEVuZ2luZToge1xuICAgIGNyZWF0ZUFzc2Vzc21lbnQ6IGplc3QuZm4oKSxcbiAgICBnZW5lcmF0ZVF1ZXN0aW9uczogamVzdC5mbigpLFxuICAgIHN1Ym1pdFJlc3BvbnNlOiBqZXN0LmZuKCksXG4gICAgY2FsY3VsYXRlUmVzdWx0czogamVzdC5mbigpLFxuICAgIGdldEFzc2Vzc21lbnQ6IGplc3QuZm4oKSxcbiAgICBnZXRBc3Nlc3NtZW50c0J5VXNlcjogamVzdC5mbigpLFxuICB9IGFzIGFueSxcbiAgbW9ja01hcmtldERhdGFTZXJ2aWNlOiB7XG4gICAgZ2V0U2tpbGxNYXJrZXREYXRhOiBqZXN0LmZuKCksXG4gICAgZ2V0TXVsdGlwbGVTa2lsbHNNYXJrZXREYXRhOiBqZXN0LmZuKCksXG4gICAgYW5hbHl6ZU1hcmtldFRyZW5kczogamVzdC5mbigpLFxuICAgIGdldFNhbGFyeUluc2lnaHRzOiBqZXN0LmZuKCksXG4gICAgZ2V0TG9jYXRpb25CYXNlZE1hcmtldERhdGE6IGplc3QuZm4oKSxcbiAgICBnZXRNYXJrZXRCYXNlZFJlY29tbWVuZGF0aW9uczogamVzdC5mbigpLFxuICB9IGFzIGFueSxcbiAgbW9ja0xlYXJuaW5nUGF0aFNlcnZpY2U6IHtcbiAgICBnZW5lcmF0ZUxlYXJuaW5nUGF0aDogamVzdC5mbigpLFxuICAgIHVwZGF0ZVByb2dyZXNzOiBqZXN0LmZuKCksXG4gICAgY29tcGxldGVNaWxlc3RvbmU6IGplc3QuZm4oKSxcbiAgfSBhcyBhbnksXG59KTtcblxuZGVzY3JpYmUoJ0VkZ2VDYXNlSGFuZGxlciAtIEJ1c2luZXNzIExvZ2ljJywgKCkgPT4ge1xuICBsZXQgZWRnZUNhc2VIYW5kbGVyOiBFZGdlQ2FzZUhhbmRsZXI7XG4gIGxldCBtb2NrczogUmV0dXJuVHlwZTx0eXBlb2YgY3JlYXRlTW9ja1NlcnZpY2VzPjtcblxuICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICBtb2NrcyA9IGNyZWF0ZU1vY2tTZXJ2aWNlcygpO1xuICAgIGVkZ2VDYXNlSGFuZGxlciA9IG5ldyBFZGdlQ2FzZUhhbmRsZXIoXG4gICAgICBtb2Nrcy5tb2NrQXNzZXNzbWVudEVuZ2luZSxcbiAgICAgIG1vY2tzLm1vY2tNYXJrZXREYXRhU2VydmljZSxcbiAgICAgIG1vY2tzLm1vY2tMZWFybmluZ1BhdGhTZXJ2aWNlXG4gICAgKTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ05vbi1leGlzdGVudCBEYXRhJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgaGFuZGxlIG5vbi1leGlzdGVudCBza2lsbHMgZ3JhY2VmdWxseScsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSB7XG4gICAgICAgIHVzZXJJZDogJ3VzZXItMTIzJyxcbiAgICAgICAgc2tpbGxJZHM6IFsnbm9uLWV4aXN0ZW50LXNraWxsJywgJ2Fub3RoZXItZmFrZS1za2lsbCddLFxuICAgICAgICBjYXJlZXJQYXRoSWQ6ICdwYXRoLTQ1NicsXG4gICAgICB9O1xuXG4gICAgICBtb2Nrcy5tb2NrQXNzZXNzbWVudEVuZ2luZS5jcmVhdGVBc3Nlc3NtZW50Lm1vY2tJbXBsZW1lbnRhdGlvbigoKSA9PiB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignU2tpbGwgbm90IGZvdW5kJyk7XG4gICAgICB9KTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZWRnZUNhc2VIYW5kbGVyLmhhbmRsZVNraWxsQXNzZXNzbWVudChyZXF1ZXN0KTtcblxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3IpLnRvQ29udGFpbignT25lIG9yIG1vcmUgc2tpbGxzIG5vdCBmb3VuZCcpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvclR5cGUpLnRvQmUoJ0JVU0lORVNTX0xPR0lDX0VSUk9SJyk7XG4gICAgICBleHBlY3QocmVzdWx0LmZhbGxiYWNrRGF0YSkudG9CZURlZmluZWQoKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VnZ2VzdGVkQWx0ZXJuYXRpdmVzKS50b0JlRGVmaW5lZCgpO1xuICAgIH0sIDUwMDApO1xuXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgbm9uLWV4aXN0ZW50IGNhcmVlciBwYXRocycsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSB7XG4gICAgICAgIHVzZXJJZDogJ3VzZXItMTIzJyxcbiAgICAgICAgY3VycmVudFNraWxsczogW3sgc2tpbGw6ICdqYXZhc2NyaXB0JywgbGV2ZWw6IDUsIGNvbmZpZGVuY2U6IDYgfV0sXG4gICAgICAgIHRhcmdldFJvbGU6ICdRdWFudHVtIERldmVsb3BlcicsXG4gICAgICAgIHRpbWVmcmFtZTogNixcbiAgICAgICAgbGVhcm5pbmdTdHlsZTogJ3N0cnVjdHVyZWQnIGFzIGNvbnN0LFxuICAgICAgICBhdmFpbGFiaWxpdHk6IDEwLFxuICAgICAgICBidWRnZXQ6IDUwMCxcbiAgICAgIH07XG5cbiAgICAgIG1vY2tzLm1vY2tMZWFybmluZ1BhdGhTZXJ2aWNlLmdlbmVyYXRlTGVhcm5pbmdQYXRoLm1vY2tJbXBsZW1lbnRhdGlvbigoKSA9PiB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQ2FyZWVyIHBhdGggbm90IGZvdW5kJyk7XG4gICAgICB9KTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZWRnZUNhc2VIYW5kbGVyLmhhbmRsZUxlYXJuaW5nUGF0aEdlbmVyYXRpb24ocmVxdWVzdCk7XG5cbiAgICAgIGV4cGVjdChyZXN1bHQuc3VjY2VzcykudG9CZShmYWxzZSk7XG4gICAgICBleHBlY3QocmVzdWx0LmVycm9yKS50b0NvbnRhaW4oJ0NhcmVlciBwYXRoIG5vdCBmb3VuZCcpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvclR5cGUpLnRvQmUoJ0JVU0lORVNTX0xPR0lDX0VSUk9SJyk7XG4gICAgICBleHBlY3QocmVzdWx0LnN1Z2dlc3RlZEFsdGVybmF0aXZlcykudG9CZURlZmluZWQoKTtcbiAgICB9LCA1MDAwKTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHVzZXJzIHdpdGggbm8gZXhpc3RpbmcgZGF0YScsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSB7XG4gICAgICAgIHVzZXJJZDogJ25ldy11c2VyLXdpdGgtbm8tZGF0YScsXG4gICAgICAgIHNraWxsSWRzOiBbJ2phdmFzY3JpcHQnXSxcbiAgICAgICAgY2FyZWVyUGF0aElkOiAncGF0aC00NTYnLFxuICAgICAgfTtcblxuICAgICAgbW9ja3MubW9ja0Fzc2Vzc21lbnRFbmdpbmUuZ2V0QXNzZXNzbWVudHNCeVVzZXIubW9ja1JldHVyblZhbHVlKFtdKTtcbiAgICAgIG1vY2tzLm1vY2tBc3Nlc3NtZW50RW5naW5lLmNyZWF0ZUFzc2Vzc21lbnQubW9ja1Jlc29sdmVkVmFsdWUoe1xuICAgICAgICBpZDogJ25ldy11c2VyLWFzc2Vzc21lbnQnLFxuICAgICAgICB1c2VySWQ6IHJlcXVlc3QudXNlcklkLFxuICAgICAgfSBhcyBhbnkpO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBlZGdlQ2FzZUhhbmRsZXIuaGFuZGxlU2tpbGxBc3Nlc3NtZW50KHJlcXVlc3QpO1xuXG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3MpLnRvQmUodHJ1ZSk7XG4gICAgICBleHBlY3QocmVzdWx0LmRhdGEpLnRvQmVEZWZpbmVkKCk7XG4gICAgICBleHBlY3QocmVzdWx0LmlzTmV3VXNlcikudG9CZSh0cnVlKTtcbiAgICAgIGV4cGVjdChyZXN1bHQub25ib2FyZGluZ1JlY29tbWVuZGF0aW9ucykudG9CZURlZmluZWQoKTtcbiAgICB9LCAzMDAwKTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ0NvbmZsaWN0aW5nIFJlcXVpcmVtZW50cycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBjaXJjdWxhciBkZXBlbmRlbmNpZXMgaW4gbGVhcm5pbmcgcGF0aHMnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCByZXF1ZXN0ID0ge1xuICAgICAgICB1c2VySWQ6ICd1c2VyLTEyMycsXG4gICAgICAgIGN1cnJlbnRTa2lsbHM6IFt7IHNraWxsOiAnamF2YXNjcmlwdCcsIGxldmVsOiA1LCBjb25maWRlbmNlOiA2IH1dLFxuICAgICAgICB0YXJnZXRSb2xlOiAnRnVsbCBTdGFjayBEZXZlbG9wZXInLFxuICAgICAgICB0aW1lZnJhbWU6IDYsXG4gICAgICAgIGxlYXJuaW5nU3R5bGU6ICdzdHJ1Y3R1cmVkJyBhcyBjb25zdCxcbiAgICAgICAgYXZhaWxhYmlsaXR5OiAxMCxcbiAgICAgICAgYnVkZ2V0OiA1MDAsXG4gICAgICB9O1xuXG4gICAgICBtb2Nrcy5tb2NrTGVhcm5pbmdQYXRoU2VydmljZS5nZW5lcmF0ZUxlYXJuaW5nUGF0aC5tb2NrSW1wbGVtZW50YXRpb24oKCkgPT4ge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NpcmN1bGFyIGRlcGVuZGVuY3kgZGV0ZWN0ZWQnKTtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBlZGdlQ2FzZUhhbmRsZXIuaGFuZGxlTGVhcm5pbmdQYXRoR2VuZXJhdGlvbihyZXF1ZXN0KTtcblxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3IpLnRvQ29udGFpbignQ2lyY3VsYXIgZGVwZW5kZW5jeScpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvclR5cGUpLnRvQmUoJ0JVU0lORVNTX0xPR0lDX0VSUk9SJyk7XG4gICAgICBleHBlY3QocmVzdWx0LmZhbGxiYWNrRGF0YSkudG9CZURlZmluZWQoKTtcbiAgICB9LCA1MDAwKTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIGltcG9zc2libGUgdGltZS9idWRnZXQgY29uc3RyYWludHMnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCByZXF1ZXN0ID0ge1xuICAgICAgICB1c2VySWQ6ICd1c2VyLTEyMycsXG4gICAgICAgIGN1cnJlbnRTa2lsbHM6IFt7IHNraWxsOiAnaHRtbCcsIGxldmVsOiAxLCBjb25maWRlbmNlOiAyIH1dLFxuICAgICAgICB0YXJnZXRSb2xlOiAnU2VuaW9yIEZ1bGwgU3RhY2sgRGV2ZWxvcGVyJyxcbiAgICAgICAgdGltZWZyYW1lOiAxLFxuICAgICAgICBsZWFybmluZ1N0eWxlOiAnY2FzdWFsJyBhcyBjb25zdCxcbiAgICAgICAgYXZhaWxhYmlsaXR5OiAxLFxuICAgICAgICBidWRnZXQ6IDEsXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBlZGdlQ2FzZUhhbmRsZXIuaGFuZGxlTGVhcm5pbmdQYXRoR2VuZXJhdGlvbihyZXF1ZXN0KTtcblxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3IpLnRvQ29udGFpbignSW1wb3NzaWJsZSBjb25zdHJhaW50cycpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvclR5cGUpLnRvQmUoJ0JVU0lORVNTX0xPR0lDX0VSUk9SJyk7XG4gICAgICBleHBlY3QocmVzdWx0LmZlYXNpYmlsaXR5QW5hbHlzaXMpLnRvQmVEZWZpbmVkKCk7XG4gICAgICBleHBlY3QocmVzdWx0LnN1Z2dlc3RlZEFkanVzdG1lbnRzKS50b0JlRGVmaW5lZCgpO1xuICAgIH0sIDUwMDApO1xuICB9KTtcblxuICBkZXNjcmliZSgnRGF0YSBDb25zaXN0ZW5jeSBJc3N1ZXMnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgaW5jb25zaXN0ZW50IHNraWxsIHJhdGluZ3MnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCByZXF1ZXN0ID0ge1xuICAgICAgICB1c2VySWQ6ICd1c2VyLTEyMycsXG4gICAgICAgIHNraWxsQXNzZXNzbWVudHM6IFtcbiAgICAgICAgICB7IHNraWxsOiAnamF2YXNjcmlwdCcsIHNlbGZSYXRpbmc6IDksIGNvbmZpZGVuY2VMZXZlbDogMiB9LFxuICAgICAgICAgIHsgc2tpbGw6ICdyZWFjdCcsIHNlbGZSYXRpbmc6IDMsIGNvbmZpZGVuY2VMZXZlbDogOSB9LFxuICAgICAgICBdLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZWRnZUNhc2VIYW5kbGVyLnZhbGlkYXRlU2tpbGxDb25zaXN0ZW5jeShyZXF1ZXN0KTtcblxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3IpLnRvQ29udGFpbignSW5jb25zaXN0ZW50IHNraWxsIHJhdGluZ3MnKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3JUeXBlKS50b0JlKCdCVVNJTkVTU19MT0dJQ19FUlJPUicpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5pbmNvbnNpc3RlbmNpZXMpLnRvQmVEZWZpbmVkKCk7XG4gICAgICBleHBlY3QocmVzdWx0LnN1Z2dlc3RlZENvcnJlY3Rpb25zKS50b0JlRGVmaW5lZCgpO1xuICAgIH0sIDMwMDApO1xuXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgY29uZmxpY3RpbmcgbWFya2V0IGRhdGEnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCBza2lsbCA9ICdqYXZhc2NyaXB0JztcblxuICAgICAgbW9ja3MubW9ja01hcmtldERhdGFTZXJ2aWNlLmdldFNraWxsTWFya2V0RGF0YS5tb2NrUmVzb2x2ZWRWYWx1ZSh7XG4gICAgICAgIHNraWxsLFxuICAgICAgICBkZW1hbmQ6IDk1LFxuICAgICAgICBzdXBwbHk6IDk4LFxuICAgICAgICBhdmVyYWdlU2FsYXJ5OiAtNTAwMDAsXG4gICAgICAgIGdyb3d0aDogMTUwLFxuICAgICAgICBkaWZmaWN1bHR5OiAxNSxcbiAgICAgICAgdGltZVRvTGVhcm46IC01LFxuICAgICAgICBjYXRlZ29yeTogJycsXG4gICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGVkZ2VDYXNlSGFuZGxlci5oYW5kbGVNYXJrZXREYXRhUmVxdWVzdCh7IHNraWxsIH0pO1xuXG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3MpLnRvQmUoZmFsc2UpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvcikudG9Db250YWluKCdJbmNvbnNpc3RlbnQgbWFya2V0IGRhdGEnKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3JUeXBlKS50b0JlKCdEQVRBX0NPTlNJU1RFTkNZX0VSUk9SJyk7XG4gICAgICBleHBlY3QocmVzdWx0LmNvcnJlY3RlZERhdGEpLnRvQmVEZWZpbmVkKCk7XG4gICAgfSwgMzAwMCk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdDb25jdXJyZW5jeSBFZGdlIENhc2VzJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgaGFuZGxlIGNvbmN1cnJlbnQgYXNzZXNzbWVudCBzdWJtaXNzaW9ucycsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSB7XG4gICAgICAgIHVzZXJJZDogJ3VzZXItMTIzJyxcbiAgICAgICAgc2tpbGxJZHM6IFsnamF2YXNjcmlwdCddLFxuICAgICAgICBjYXJlZXJQYXRoSWQ6ICdwYXRoLTQ1NicsXG4gICAgICB9O1xuXG4gICAgICAvLyBTaW11bGF0ZSBjb25jdXJyZW50IHJlcXVlc3RzIHdpdGggcmVkdWNlZCBsb2FkIGZvciBwZXJmb3JtYW5jZVxuICAgICAgY29uc3QgcHJvbWlzZXMgPSBBcnJheSg1KS5maWxsKG51bGwpLm1hcCgoKSA9PiBcbiAgICAgICAgZWRnZUNhc2VIYW5kbGVyLmhhbmRsZVNraWxsQXNzZXNzbWVudChyZXF1ZXN0KVxuICAgICAgKTtcblxuICAgICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsKHByb21pc2VzKTtcblxuICAgICAgZXhwZWN0KHJlc3VsdHMuZXZlcnkociA9PiByLnN1Y2Nlc3MgfHwgci5lcnJvclR5cGUgPT09ICdDT05DVVJSRU5DWV9FUlJPUicpKS50b0JlKHRydWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdHMuZmlsdGVyKHIgPT4gci5zdWNjZXNzKS5sZW5ndGgpLnRvQmVHcmVhdGVyVGhhbigwKTtcbiAgICB9LCAxMDAwMCk7XG5cbiAgICBpdCgnc2hvdWxkIGhhbmRsZSByZXNvdXJjZSBjb250ZW50aW9uJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgcmVxdWVzdCA9IHtcbiAgICAgICAgdXNlcklkOiAndXNlci0xMjMnLFxuICAgICAgICBza2lsbElkczogWydqYXZhc2NyaXB0J10sXG4gICAgICAgIGNhcmVlclBhdGhJZDogJ3BhdGgtNDU2JyxcbiAgICAgIH07XG5cbiAgICAgIG1vY2tzLm1vY2tBc3Nlc3NtZW50RW5naW5lLmNyZWF0ZUFzc2Vzc21lbnQubW9ja0ltcGxlbWVudGF0aW9uKCgpID0+IHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdSZXNvdXJjZSBsb2NrZWQnKTtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBlZGdlQ2FzZUhhbmRsZXIuaGFuZGxlU2tpbGxBc3Nlc3NtZW50KHJlcXVlc3QpO1xuXG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3MpLnRvQmUoZmFsc2UpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvcikudG9Db250YWluKCdSZXNvdXJjZSBsb2NrZWQnKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3JUeXBlKS50b0JlKCdDT05DVVJSRU5DWV9FUlJPUicpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5yZXRyeWFibGUpLnRvQmUodHJ1ZSk7XG4gICAgfSwgMzAwMCk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdSZWNvdmVyeSBhbmQgRmFsbGJhY2sgTWVjaGFuaXNtcycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGltcGxlbWVudCBhdXRvbWF0aWMgcmV0cnkgZm9yIHRyYW5zaWVudCBmYWlsdXJlcycsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSB7XG4gICAgICAgIHVzZXJJZDogJ3VzZXItMTIzJyxcbiAgICAgICAgc2tpbGxJZHM6IFsnamF2YXNjcmlwdCddLFxuICAgICAgICBjYXJlZXJQYXRoSWQ6ICdwYXRoLTQ1NicsXG4gICAgICB9O1xuXG4gICAgICBsZXQgY2FsbENvdW50ID0gMDtcbiAgICAgIG1vY2tzLm1vY2tBc3Nlc3NtZW50RW5naW5lLmNyZWF0ZUFzc2Vzc21lbnQubW9ja0ltcGxlbWVudGF0aW9uKCgpID0+IHtcbiAgICAgICAgY2FsbENvdW50Kys7XG4gICAgICAgIGlmIChjYWxsQ291bnQgPCAzKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdUcmFuc2llbnQgZXJyb3InKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4geyBpZDogJ2Fzc2Vzc21lbnQtMTIzJyB9IGFzIGFueTtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBlZGdlQ2FzZUhhbmRsZXIuaGFuZGxlU2tpbGxBc3Nlc3NtZW50KHJlcXVlc3QsIHsgbWF4UmV0cmllczogMyB9KTtcblxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5yZXRyeUNvdW50KS50b0JlKDIpO1xuICAgICAgZXhwZWN0KGNhbGxDb3VudCkudG9CZSgzKTtcbiAgICB9LCA4MDAwKTtcblxuICAgIGl0KCdzaG91bGQgcHJvdmlkZSBmYWxsYmFjayBkYXRhIHdoZW4gc2VydmljZXMgZmFpbCcsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSB7XG4gICAgICAgIHNraWxsOiAnamF2YXNjcmlwdCcsXG4gICAgICB9O1xuXG4gICAgICBtb2Nrcy5tb2NrTWFya2V0RGF0YVNlcnZpY2UuZ2V0U2tpbGxNYXJrZXREYXRhLm1vY2tJbXBsZW1lbnRhdGlvbigoKSA9PiB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignU2VydmljZSB1bmF2YWlsYWJsZScpO1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGVkZ2VDYXNlSGFuZGxlci5oYW5kbGVNYXJrZXREYXRhUmVxdWVzdChyZXF1ZXN0KTtcblxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZmFsbGJhY2tEYXRhKS50b0JlRGVmaW5lZCgpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5mYWxsYmFja0RhdGEuc2tpbGwpLnRvQmUoJ2phdmFzY3JpcHQnKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuZmFsbGJhY2tEYXRhLmlzU3RhbGUpLnRvQmUodHJ1ZSk7XG4gICAgICBleHBlY3QocmVzdWx0LmZhbGxiYWNrRGF0YS5zb3VyY2UpLnRvQmUoJ2NhY2hlX29yX2RlZmF1bHQnKTtcbiAgICB9LCAzMDAwKTtcblxuICAgIGl0KCdzaG91bGQgaW1wbGVtZW50IGNpcmN1aXQgYnJlYWtlciBwYXR0ZXJuJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgcmVxdWVzdCA9IHtcbiAgICAgICAgdXNlcklkOiAndXNlci0xMjMnLFxuICAgICAgICBza2lsbElkczogWydqYXZhc2NyaXB0J10sXG4gICAgICAgIGNhcmVlclBhdGhJZDogJ3BhdGgtNDU2JyxcbiAgICAgIH07XG5cbiAgICAgIG1vY2tzLm1vY2tBc3Nlc3NtZW50RW5naW5lLmNyZWF0ZUFzc2Vzc21lbnQubW9ja0ltcGxlbWVudGF0aW9uKCgpID0+IHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTZXJ2aWNlIGRvd24nKTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBSZWR1Y2VkIGl0ZXJhdGlvbnMgZm9yIHBlcmZvcm1hbmNlXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDM7IGkrKykge1xuICAgICAgICBhd2FpdCBlZGdlQ2FzZUhhbmRsZXIuaGFuZGxlU2tpbGxBc3Nlc3NtZW50KHJlcXVlc3QpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBlZGdlQ2FzZUhhbmRsZXIuaGFuZGxlU2tpbGxBc3Nlc3NtZW50KHJlcXVlc3QpO1xuXG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3MpLnRvQmUoZmFsc2UpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvclR5cGUpLnRvQmUoJ0NJUkNVSVRfQlJFQUtFUl9PUEVOJyk7XG4gICAgICBleHBlY3QocmVzdWx0LmZhbGxiYWNrRGF0YSkudG9CZURlZmluZWQoKTtcbiAgICB9LCA4MDAwKTtcbiAgfSk7XG59KTtcbiJdLCJ2ZXJzaW9uIjozfQ==