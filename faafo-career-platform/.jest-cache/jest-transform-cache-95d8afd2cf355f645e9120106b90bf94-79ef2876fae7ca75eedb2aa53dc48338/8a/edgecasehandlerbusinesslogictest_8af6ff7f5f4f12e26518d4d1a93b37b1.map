{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/edge-case-handler.business-logic.test.ts", "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,gEAA+D;AAK/D,wCAAwC;AACxC,IAAM,kBAAkB,GAAG,cAAM,OAAA,CAAC;IAChC,oBAAoB,EAAE;QACpB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB;IACR,qBAAqB,EAAE;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;QACtC,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;QACrC,6BAA6B,EAAE,IAAI,CAAC,EAAE,EAAE;KAClC;IACR,uBAAuB,EAAE;QACvB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC/B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;KACtB;CACT,CAAC,EAtB+B,CAsB/B,CAAC;AAEH,QAAQ,CAAC,kCAAkC,EAAE;IAC3C,IAAI,eAAgC,CAAC;IACrC,IAAI,KAA4C,CAAC;IAEjD,UAAU,CAAC;QACT,KAAK,GAAG,kBAAkB,EAAE,CAAC;QAC7B,eAAe,GAAG,IAAI,iCAAe,CACnC,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,qBAAqB,EAC3B,KAAK,CAAC,uBAAuB,CAC9B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,8CAA8C,EAAE;;;;;wBAC3C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;4BACtD,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BAC7D,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;wBACrC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;wBAC/D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1C,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACpD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,yCAAyC,EAAE;;;;;wBACtC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,mBAAmB;4BAC/B,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEF,KAAK,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;4BACpE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;wBAC3C,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;wBACxD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACpD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,2CAA2C,EAAE;;;;;wBACxC,OAAO,GAAG;4BACd,MAAM,EAAE,uBAAuB;4BAC/B,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;wBACpE,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;4BAC5D,EAAE,EAAE,qBAAqB;4BACzB,MAAM,EAAE,OAAO,CAAC,MAAM;yBAChB,CAAC,CAAC;wBAEK,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACpC,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACxD,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,uDAAuD,EAAE;;;;;wBACpD,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEF,KAAK,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;4BACpE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;wBAClD,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,kDAAkD,EAAE;;;;;wBAC/C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BAC3D,UAAU,EAAE,6BAA6B;4BACzC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,QAAiB;4BAChC,YAAY,EAAE,CAAC;4BACf,MAAM,EAAE,CAAC;yBACV,CAAC;wBAEa,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;wBACzD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjD,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,0CAA0C,EAAE;;;;;wBACvC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,gBAAgB,EAAE;gCAChB,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;gCAC1D,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;6BACtD;yBACF,CAAC;wBAEa,qBAAM,eAAe,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAA;;wBAAhE,MAAM,GAAG,SAAuD;wBAEtE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;wBAC7D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7C,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,KAAK,GAAG,YAAY,CAAC;wBAE3B,KAAK,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC;4BAC/D,KAAK,OAAA;4BACL,MAAM,EAAE,EAAE;4BACV,MAAM,EAAE,EAAE;4BACV,aAAa,EAAE,CAAC,KAAK;4BACrB,MAAM,EAAE,GAAG;4BACX,UAAU,EAAE,EAAE;4BACd,WAAW,EAAE,CAAC,CAAC;4BACf,QAAQ,EAAE,EAAE;4BACZ,WAAW,EAAE,IAAI,IAAI,EAAE;yBACxB,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,uBAAuB,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,EAAA;;wBAAjE,MAAM,GAAG,SAAwD;wBAEvE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;wBAC3D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;wBACxD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC5C,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,iDAAiD,EAAE;;;;;wBAC9C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAGI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;4BACvC,OAAA,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC;wBAA9C,CAA8C,CAC/C,CAAC;wBAEc,qBAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAA;;wBAArC,OAAO,GAAG,SAA2B;wBAE3C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,SAAS,KAAK,mBAAmB,EAAhD,CAAgD,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACxF,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,EAAT,CAAS,CAAC,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aAClE,EAAE,KAAK,CAAC,CAAC;QAEV,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BAC7D,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;wBACrC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBACnD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACrC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE;QAC3C,EAAE,CAAC,yDAAyD,EAAE;;;;;wBACtD,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEE,SAAS,GAAG,CAAC,CAAC;wBAClB,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BAC7D,SAAS,EAAE,CAAC;4BACZ,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gCAClB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;4BACrC,CAAC;4BACD,OAAO,EAAE,EAAE,EAAE,gBAAgB,EAAS,CAAC;wBACzC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAA;;wBAAhF,MAAM,GAAG,SAAuE;wBAEtF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAClC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC3B,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,iDAAiD,EAAE;;;;;wBAC9C,OAAO,GAAG;4BACd,KAAK,EAAE,YAAY;yBACpB,CAAC;wBAEF,KAAK,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,kBAAkB,CAAC;4BAChE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;wBACzC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAA;;wBAA/D,MAAM,GAAG,SAAsD;wBAErE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACrD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC/C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aAC7D,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,0CAA0C,EAAE;;;;;wBACvC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BAC7D,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;wBAClC,CAAC,CAAC,CAAC;wBAGM,CAAC,GAAG,CAAC;;;6BAAE,CAAA,CAAC,GAAG,CAAC,CAAA;wBACnB,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAApD,SAAoD,CAAC;;;wBADhC,CAAC,EAAE,CAAA;;4BAIX,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/edge-case-handler.business-logic.test.ts"], "sourcesContent": ["/**\n * EdgeCaseHandler Business Logic Tests\n * Focused on business logic edge cases and data consistency\n * Optimized for fast execution\n */\n\nimport { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';\nimport { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';\nimport { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';\nimport { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';\n\n// Shared mock factories for performance\nconst createMockServices = () => ({\n  mockAssessmentEngine: {\n    createAssessment: jest.fn(),\n    generateQuestions: jest.fn(),\n    submitResponse: jest.fn(),\n    calculateResults: jest.fn(),\n    getAssessment: jest.fn(),\n    getAssessmentsByUser: jest.fn(),\n  } as any,\n  mockMarketDataService: {\n    getSkillMarketData: jest.fn(),\n    getMultipleSkillsMarketData: jest.fn(),\n    analyzeMarketTrends: jest.fn(),\n    getSalaryInsights: jest.fn(),\n    getLocationBasedMarketData: jest.fn(),\n    getMarketBasedRecommendations: jest.fn(),\n  } as any,\n  mockLearningPathService: {\n    generateLearningPath: jest.fn(),\n    updateProgress: jest.fn(),\n    completeMilestone: jest.fn(),\n  } as any,\n});\n\ndescribe('EdgeCaseHandler - Business Logic', () => {\n  let edgeCaseHandler: EdgeCaseHandler;\n  let mocks: ReturnType<typeof createMockServices>;\n\n  beforeEach(() => {\n    mocks = createMockServices();\n    edgeCaseHandler = new EdgeCaseHandler(\n      mocks.mockAssessmentEngine,\n      mocks.mockMarketDataService,\n      mocks.mockLearningPathService\n    );\n  });\n\n  describe('Non-existent Data', () => {\n    it('should handle non-existent skills gracefully', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['non-existent-skill', 'another-fake-skill'],\n        careerPathId: 'path-456',\n      };\n\n      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        throw new Error('Skill not found');\n      });\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('One or more skills not found');\n      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');\n      expect(result.fallbackData).toBeDefined();\n      expect(result.suggestedAlternatives).toBeDefined();\n    }, 5000);\n\n    it('should handle non-existent career paths', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Quantum Developer',\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 500,\n      };\n\n      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {\n        throw new Error('Career path not found');\n      });\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Career path not found');\n      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');\n      expect(result.suggestedAlternatives).toBeDefined();\n    }, 5000);\n\n    it('should handle users with no existing data', async () => {\n      const request = {\n        userId: 'new-user-with-no-data',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mocks.mockAssessmentEngine.getAssessmentsByUser.mockReturnValue([]);\n      mocks.mockAssessmentEngine.createAssessment.mockResolvedValue({\n        id: 'new-user-assessment',\n        userId: request.userId,\n      } as any);\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(true);\n      expect(result.data).toBeDefined();\n      expect(result.isNewUser).toBe(true);\n      expect(result.onboardingRecommendations).toBeDefined();\n    }, 3000);\n  });\n\n  describe('Conflicting Requirements', () => {\n    it('should handle circular dependencies in learning paths', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 500,\n      };\n\n      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {\n        throw new Error('Circular dependency detected');\n      });\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Circular dependency');\n      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');\n      expect(result.fallbackData).toBeDefined();\n    }, 5000);\n\n    it('should handle impossible time/budget constraints', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'html', level: 1, confidence: 2 }],\n        targetRole: 'Senior Full Stack Developer',\n        timeframe: 1,\n        learningStyle: 'casual' as const,\n        availability: 1,\n        budget: 1,\n      };\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Impossible constraints');\n      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');\n      expect(result.feasibilityAnalysis).toBeDefined();\n      expect(result.suggestedAdjustments).toBeDefined();\n    }, 5000);\n  });\n\n  describe('Data Consistency Issues', () => {\n    it('should handle inconsistent skill ratings', async () => {\n      const request = {\n        userId: 'user-123',\n        skillAssessments: [\n          { skill: 'javascript', selfRating: 9, confidenceLevel: 2 },\n          { skill: 'react', selfRating: 3, confidenceLevel: 9 },\n        ],\n      };\n\n      const result = await edgeCaseHandler.validateSkillConsistency(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Inconsistent skill ratings');\n      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');\n      expect(result.inconsistencies).toBeDefined();\n      expect(result.suggestedCorrections).toBeDefined();\n    }, 3000);\n\n    it('should handle conflicting market data', async () => {\n      const skill = 'javascript';\n\n      mocks.mockMarketDataService.getSkillMarketData.mockResolvedValue({\n        skill,\n        demand: 95,\n        supply: 98,\n        averageSalary: -50000,\n        growth: 150,\n        difficulty: 15,\n        timeToLearn: -5,\n        category: '',\n        lastUpdated: new Date(),\n      });\n\n      const result = await edgeCaseHandler.handleMarketDataRequest({ skill });\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Inconsistent market data');\n      expect(result.errorType).toBe('DATA_CONSISTENCY_ERROR');\n      expect(result.correctedData).toBeDefined();\n    }, 3000);\n  });\n\n  describe('Concurrency Edge Cases', () => {\n    it('should handle concurrent assessment submissions', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      // Simulate concurrent requests with reduced load for performance\n      const promises = Array(5).fill(null).map(() => \n        edgeCaseHandler.handleSkillAssessment(request)\n      );\n\n      const results = await Promise.all(promises);\n\n      expect(results.every(r => r.success || r.errorType === 'CONCURRENCY_ERROR')).toBe(true);\n      expect(results.filter(r => r.success).length).toBeGreaterThan(0);\n    }, 10000);\n\n    it('should handle resource contention', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        throw new Error('Resource locked');\n      });\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Resource locked');\n      expect(result.errorType).toBe('CONCURRENCY_ERROR');\n      expect(result.retryable).toBe(true);\n    }, 3000);\n  });\n\n  describe('Recovery and Fallback Mechanisms', () => {\n    it('should implement automatic retry for transient failures', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      let callCount = 0;\n      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        callCount++;\n        if (callCount < 3) {\n          throw new Error('Transient error');\n        }\n        return { id: 'assessment-123' } as any;\n      });\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request, { maxRetries: 3 });\n\n      expect(result.success).toBe(true);\n      expect(result.retryCount).toBe(2);\n      expect(callCount).toBe(3);\n    }, 8000);\n\n    it('should provide fallback data when services fail', async () => {\n      const request = {\n        skill: 'javascript',\n      };\n\n      mocks.mockMarketDataService.getSkillMarketData.mockImplementation(() => {\n        throw new Error('Service unavailable');\n      });\n\n      const result = await edgeCaseHandler.handleMarketDataRequest(request);\n\n      expect(result.success).toBe(false);\n      expect(result.fallbackData).toBeDefined();\n      expect(result.fallbackData.skill).toBe('javascript');\n      expect(result.fallbackData.isStale).toBe(true);\n      expect(result.fallbackData.source).toBe('cache_or_default');\n    }, 3000);\n\n    it('should implement circuit breaker pattern', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        throw new Error('Service down');\n      });\n\n      // Reduced iterations for performance\n      for (let i = 0; i < 3; i++) {\n        await edgeCaseHandler.handleSkillAssessment(request);\n      }\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.errorType).toBe('CIRCUIT_BREAKER_OPEN');\n      expect(result.fallbackData).toBeDefined();\n    }, 8000);\n  });\n});\n"], "version": 3}