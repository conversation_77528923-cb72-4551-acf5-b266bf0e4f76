{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/resource-recommendation-logic-flaws.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;;;;;;;;;AAEH,yCAAuE;AAEvE,IAAA,kBAAQ,EAAC,qCAAqC,EAAE;IAC9C,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,4DAA4D,EAAE;QACrE,IAAA,YAAE,EAAC,8EAA8E,EAAE;YACjF,oBAAoB;YACpB,IAAM,WAAW,GAAG;gBAClB,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC;gBAC1C,eAAe,EAAE,cAAc;gBAC/B,WAAW,EAAE,CAAC,sBAAsB,CAAC;gBACrC,kBAAkB,EAAE,CAAC,cAAc,EAAE,iBAAiB,CAAC;aACxD,CAAC;YAEF,sCAAsC;YACtC,IAAM,yBAAyB,GAAG,CAAC,gBAAgB,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;YACrF,IAAM,yBAAyB,GAAG,CAAC,qBAAqB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;YAChG,IAAM,qCAAqC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;YACrF,IAAM,2BAA2B,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAE/F,8FAA8F;YAC9F,IAAM,UAAU,GAAG,yBAAyB,CAAC,IAAI,CAAC,UAAA,GAAG;gBACnD,OAAA,yBAAyB,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACvC,qCAAqC,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACnD,2BAA2B,CAAC,QAAQ,CAAC,GAAG,CAAC;YAFzC,CAEyC,CAC1C,CAAC;YAEF,IAAA,gBAAM,EAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,2CAA2C;YAE1E,0DAA0D;YAC1D,IAAM,kBAAkB,+DACnB,yBAAyB,SACzB,yBAAyB,SACzB,qCAAqC,SACrC,2BAA2B,OAC/B,CAAC;YAEF,IAAM,uBAAuB,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAA,GAAG;gBAC3D,OAAA,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAA3E,CAA2E,CAC5E,CAAC;YAEF,6EAA6E;YAC7E,IAAA,gBAAM,EAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,wEAAwE,EAAE;YAC3E,IAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,gBAAgB;gBACpB,KAAK,EAAE,yBAAyB;gBAChC,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;gBAC/B,UAAU,EAAE,UAAU;aACvB,CAAC;YAEF,+BAA+B;YAC/B,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,yBAAyB;YACtD,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,iCAAiC;YAC9D,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,6BAA6B;YAC1D,IAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,wBAAwB;YAEvD,wEAAwE;YACxE,IAAM,cAAc,GAAG,CAAC,eAAe,GAAG,eAAe,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;YACjF,IAAM,cAAc,GAAG,eAAe,GAAG,GAAG,GAAG,eAAe,GAAG,GAAG,GAAG,eAAe,GAAG,GAAG,CAAC;YAC7F,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;YAEnF,gFAAgF;YAChF,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC,CAAC;YACnE,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC,CAAC;YAEnE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,oBAAoB;YAChE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,4CAA4C,EAAE;QACrD,IAAA,YAAE,EAAC,+DAA+D,EAAE;YAClE,mCAAmC;YACnC,IAAM,OAAO,GAAG;gBACd,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE;gBAC3C,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,iBAAiB;gBAC9D,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,iBAAiB;gBAC/D,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,iBAAiB;gBAChE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,iBAAiB;aAChE,CAAC;YAEF,mDAAmD;YACnD,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,UAAA,CAAC;gBACnC,OAAA,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;oBAC5B,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;oBAChB,CAAC,CAAC,MAAM,IAAI,CAAC;oBACb,CAAC,CAAC,MAAM,IAAI,CAAC;oBACb,CAAC,CAAC,MAAM,GAAG,CAAC;YAJZ,CAIY,CACb,CAAC;YAEF,IAAM,aAAa,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC;gBAC3C,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAzB,CAAyB,EAAE,CAAC,CAAC;oBAC7D,YAAY,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,MAAM,EAAd,CAAc,EAAE,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC,CAAC;YAEN,0DAA0D;YAC1D,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACzC,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAA,gBAAM,EAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,2CAA2C;YAC3C,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;QAC9E,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,2DAA2D,EAAE;YAC9D,IAAM,OAAO,GAAG;gBACd,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE;gBACzE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE;gBACxE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE;gBACzE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE;aACxE,CAAC;YAEF,uCAAuC;YACvC,IAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,MAAM,EAAd,CAAc,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YAErF,2CAA2C;YAC3C,IAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,eAAe,EAAlC,CAAkC,EAAE,CAAC,CAAC;gBAClE,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,GAAG,GAAG,CAAC,CAAC,eAAe,EAAvB,CAAuB,EAAE,CAAC,CAAC,CAAC;YAE9E,2FAA2F;YAC3F,yCAAyC;YACzC,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,eAAe,CAAC,CAAC;YAC7D,IAAA,gBAAM,EAAC,UAAU,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,oCAAoC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,sDAAsD,EAAE;QAC/D,IAAA,YAAE,EAAC,qDAAqD,EAAE;YACxD,iEAAiE;YACjE,IAAM,KAAK,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;YAC9E,IAAM,KAAK,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;YAE9E,8BAA8B;YAC9B,IAAM,SAAS,GAAG,0BAAmB,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,cAAI,KAAK,CAAC,KAAK,CAAE,CAAC;YAC7E,IAAM,SAAS,GAAG,0BAAmB,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,cAAI,KAAK,CAAC,KAAK,CAAE,CAAC;YAE7E,qEAAqE;YACrE,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEtC,oCAAoC;YACpC,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACtC,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,kDAAkD,EAAE;YACrD,mBAAmB;YACnB,IAAM,KAAK,GAAG,IAAI,GAAG,CAAC;gBACpB,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,aAAa;gBAC/G,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,cAAc;gBAChH,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,iBAAiB;aACpH,CAAC,CAAC;YAEH,IAAM,eAAe,GAAG,OAAO,CAAC,CAAC,SAAS;YAC1C,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE/B,8CAA8C;YAC9C,IAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,UAAC,EAAY;oBAAX,GAAG,QAAA,EAAE,KAAK,QAAA;gBACpE,OAAA,WAAW,GAAG,KAAK,CAAC,SAAS,GAAG,eAAe;YAA/C,CAA+C,CAChD,CAAC;YAEF,6DAA6D;YAC7D,IAAA,gBAAM,EAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;YAE3E,2CAA2C;YAC3C,cAAc,CAAC,OAAO,CAAC,UAAC,EAAK;oBAAJ,GAAG,QAAA;gBAAM,OAAA,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC;YAAjB,CAAiB,CAAC,CAAC;YACrD,IAAA,gBAAM,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,+CAA+C,EAAE;QACxD,IAAA,YAAE,EAAC,8DAA8D,EAAE;;YACjE,IAAM,mBAAmB,GAAG;gBAC1B,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC7E,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;gBAChF,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC7E,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;aAC/E,CAAC;YAEF,oCAAoC;YACpC,IAAM,eAAe,GAAG,mBAAmB,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI;gBAC3D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;oBAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,eAAe,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;gBACvE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC;gBACtD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;gBAC1B,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,IAAM,aAAa,GAAG,MAAA,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;iBAClD,GAAG,CAAC,UAAC,EAAY;oBAAX,IAAI,QAAA,EAAE,IAAI,QAAA;gBAAM,OAAA,CAAC,EAAE,IAAI,MAAA,EAAE,aAAa,EAAE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAA5D,CAA4D,CAAC;iBACnF,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,EAAjC,CAAiC,CAAC,CAAC,CAAC,CAAC,0CAAE,IAAI,CAAC;YAE9D,mEAAmE;YACnE,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEpC,mDAAmD;YACnD,IAAM,eAAe,GAAG;gBACtB,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE;gBAC3C,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;gBACzC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE;aAC3C,CAAC;YAEF,yCAAyC;YACzC,IAAM,uBAAuB,GAAG,eAAe,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,uBACtD,GAAG,KACN,aAAa,EAAE,GAAG,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,IACvE,EAHyD,CAGzD,CAAC,CAAC;YAEJ,IAAM,iBAAiB,GAAG,uBAAuB;iBAC9C,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,EAAjC,CAAiC,CAAC,CAAC,CAAC,CAAC,CAAC;YAExD,IAAA,gBAAM,EAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC;QACnF,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sDAAsD,EAAE;YACzD,IAAM,WAAW,GAAG;gBAClB,MAAM,EAAE,CAAC,OAAO,CAAC;gBACjB,SAAS,EAAE,CAAC,UAAU,CAAC;gBACvB,kBAAkB,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,cAAc,CAAC;aACpE,CAAC;YAEF,4CAA4C;YAC5C,IAAM,eAAe,GAAG;gBACtB,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;gBACzE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;gBACzE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;gBACzE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;gBACtE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE;aACzE,CAAC;YAEF,yCAAyC;YACzC,IAAM,YAAY,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,MAAM,EAAV,CAAU,CAAC,CAAC,CAAC;YACzE,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,QAAQ,EAAZ,CAAY,CAAC,CAAC,CAAC;YAE3E,oEAAoE;YACpE,IAAA,gBAAM,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;YAChF,IAAA,gBAAM,EAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC;YAExF,2DAA2D;YAC3D,IAAM,0BAA0B,GAAG,eAAe,CAAC,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,UAAU,GAAG,GAAG,EAApB,CAAoB,CAAC,CAAC;YACvF,IAAA,gBAAM,EAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,6CAA6C,EAAE;QACtD,IAAA,YAAE,EAAC,qEAAqE,EAAE;YACxE,qBAAqB;YACrB,IAAI,WAAW,GAAG;gBAChB,MAAM,EAAE,CAAC,YAAY,CAAC;gBACtB,KAAK,EAAE,UAAU;aAClB,CAAC;YAEF,IAAI,qBAAqB,GAAG;gBAC1B,EAAE,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE;gBAC3D,EAAE,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE;aAC/D,CAAC;YAEF,qDAAqD;YACrD,WAAW,GAAG;gBACZ,MAAM,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC;gBAC1C,KAAK,EAAE,UAAU;aAClB,CAAC;YAEF,uEAAuE;YACvE,IAAM,sBAAsB,GAAG;gBAC7B,EAAE,EAAE,EAAE,mBAAmB,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE;gBACnE,EAAE,EAAE,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE;aAChE,CAAC;YAEF,0CAA0C;YAC1C,IAAA,gBAAM,EAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YAE9D,yDAAyD;YACzD,IAAM,eAAe,GAAG,qBAAqB,CAAC,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,UAAU,KAAK,UAAU,EAA7B,CAA6B,CAAC,CAAC;YAC3F,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,kEAAkE,EAAE;YACrE,IAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,cAAc;gBAClB,aAAa,EAAE,GAAG;gBAClB,WAAW,EAAE,GAAG;aACjB,CAAC;YAEF,sBAAsB;YACtB,IAAM,SAAS,GAAG,CAAC,CAAC;YACpB,IAAM,cAAc,GAAG,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC;YAChD,IAAM,iBAAiB,GAAG,CAAC,CAAC,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,GAAG,cAAc,CAAC;YAEzG,mDAAmD;YACnD,IAAI,WAAW,GAAG,GAAG,CAAC,CAAC,sBAAsB;YAE7C,mEAAmE;YACnE,IAAM,YAAY,GAAG,GAAG,CAAC,CAAC,4CAA4C;YACtE,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEvC,oCAAoC;YACpC,IAAA,gBAAM,EAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,sDAAsD,EAAE;QAC/D,IAAA,YAAE,EAAC,wEAAwE,EAAE;YAC3E,qBAAqB;YACrB,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC;gBACzD,EAAE,EAAE,mBAAY,CAAC,CAAE;gBACnB,MAAM,EAAE,CAAC,gBAAS,CAAC,GAAG,EAAE,CAAE,CAAC,EAAE,sBAAsB;gBACnD,UAAU,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;aAC1B,CAAC,EALwD,CAKxD,CAAC,CAAC;YAEJ,IAAM,WAAW,GAAG;gBAClB,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;gBAC1C,KAAK,EAAE,cAAc;aACtB,CAAC;YAEF,0CAA0C;YAC1C,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,gDAAgD;YAChD,IAAM,eAAe,GAAG,SAAS;iBAC9B,MAAM,CAAC,UAAA,QAAQ;gBACd,OAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAlC,CAAkC,CAAC;oBACjE,QAAQ,CAAC,UAAU,KAAK,WAAW,CAAC,KAAK;YADzC,CACyC,CAC1C;iBACA,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAnB,CAAmB,CAAC;iBACnC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAEhB,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,IAAM,cAAc,GAAG,OAAO,GAAG,SAAS,CAAC;YAE3C,2DAA2D;YAC3D,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,8BAA8B;YACxE,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sEAAsE,EAAE;YACzE,qBAAqB;YACrB,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;YAEtC,sCAAsC;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/B,IAAM,MAAM,GAAG,eAAQ,CAAC,CAAE,CAAC;gBAC3B,IAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC;oBAC5D,EAAE,EAAE,mBAAY,CAAC,CAAE;oBACnB,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;iBACrB,CAAC,EAH2D,CAG3D,CAAC,CAAC;gBAEJ,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE;oBAC9B,IAAI,EAAE,eAAe;oBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,QAAQ,EAAE,EAAE,WAAW,EAAE,EAAE,MAAM,EAAE,CAAC,gBAAS,CAAC,GAAG,GAAG,CAAE,CAAC,EAAE,EAAE;iBAC5D,CAAC,CAAC;YACL,CAAC;YAED,kDAAkD;YAClD,IAAM,YAAY,GAAG,IAAI,CAAC,CAAC,iCAAiC;YAC5D,IAAA,gBAAM,EAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAEnE,gCAAgC;YAChC,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;iBAC1D,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAA/B,CAA+B,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtD,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,2BAA2B;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/resource-recommendation-logic-flaws.test.ts"], "sourcesContent": ["/**\n * Resource Recommendation Logic Flaws Tests\n * \n * These tests prove critical flaws in resource recommendation algorithms,\n * rating calculations, and cache management that affect user experience.\n * \n * EXPECTED TO FAIL - These tests demonstrate recommendation logic flaws that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\n\ndescribe('Resource Recommendation Logic Flaws', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Recommendation Algorithm Inconsistencies', () => {\n    it('should fail - multiple recommendation algorithms produce conflicting results', () => {\n      // Mock user profile\n      const userProfile = {\n        skills: ['React', 'JavaScript', 'Node.js'],\n        experienceLevel: 'INTERMEDIATE',\n        careerGoals: ['Full Stack Developer'],\n        completedResources: ['react-basics', 'js-fundamentals']\n      };\n      \n      // Different recommendation algorithms\n      const skillBasedRecommendations = ['advanced-react', 'react-hooks', 'react-testing'];\n      const careerPathRecommendations = ['backend-development', 'database-design', 'api-development'];\n      const collaborativeFilteringRecommendations = ['vue-js', 'angular', 'python-basics'];\n      const contentBasedRecommendations = ['react-performance', 'react-patterns', 'react-ecosystem'];\n      \n      // EXPECTED TO FAIL: Different algorithms should produce overlapping, relevant recommendations\n      const hasOverlap = skillBasedRecommendations.some(rec => \n        careerPathRecommendations.includes(rec) || \n        collaborativeFilteringRecommendations.includes(rec) ||\n        contentBasedRecommendations.includes(rec)\n      );\n      \n      expect(hasOverlap).toBe(true); // Should have some overlap for consistency\n      \n      // All recommendations should be relevant to user's skills\n      const allRecommendations = [\n        ...skillBasedRecommendations,\n        ...careerPathRecommendations,\n        ...collaborativeFilteringRecommendations,\n        ...contentBasedRecommendations\n      ];\n      \n      const relevantRecommendations = allRecommendations.filter(rec => \n        rec.includes('react') || rec.includes('javascript') || rec.includes('node')\n      );\n      \n      // EXPECTED TO FAIL: Most recommendations should be relevant to user's skills\n      expect(relevantRecommendations.length).toBeGreaterThan(allRecommendations.length * 0.7);\n    });\n\n    it('should fail - recommendation scoring is inconsistent across algorithms', () => {\n      const resource = {\n        id: 'react-advanced',\n        title: 'Advanced React Patterns',\n        skills: ['React', 'JavaScript'],\n        difficulty: 'ADVANCED'\n      };\n      \n      // Different scoring algorithms\n      const skillMatchScore = 0.8; // Based on skill overlap\n      const difficultyScore = 0.6; // Based on user experience level\n      const popularityScore = 0.9; // Based on community ratings\n      const personalizedScore = 0.4; // Based on user history\n      \n      // EXPECTED TO FAIL: Scores should be weighted and combined consistently\n      const combinedScore1 = (skillMatchScore + difficultyScore + popularityScore) / 3;\n      const combinedScore2 = skillMatchScore * 0.4 + difficultyScore * 0.3 + popularityScore * 0.3;\n      const combinedScore3 = Math.max(skillMatchScore, difficultyScore, popularityScore);\n      \n      // Different combination methods should produce similar results for good matches\n      const scoreDifference1 = Math.abs(combinedScore1 - combinedScore2);\n      const scoreDifference2 = Math.abs(combinedScore2 - combinedScore3);\n      \n      expect(scoreDifference1).toBeLessThan(0.2); // Should be similar\n      expect(scoreDifference2).toBeLessThan(0.2);\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: Rating Calculation Flaws', () => {\n    it('should fail - rating calculations can produce invalid results', () => {\n      // Mock rating data with edge cases\n      const ratings = [\n        { userId: 'user1', rating: 5, weight: 1.0 },\n        { userId: 'user2', rating: 0, weight: 1.0 }, // Invalid rating\n        { userId: 'user3', rating: 11, weight: 1.0 }, // Invalid rating\n        { userId: 'user4', rating: NaN, weight: 1.0 }, // Invalid rating\n        { userId: 'user5', rating: 4, weight: -0.5 }, // Invalid weight\n      ];\n      \n      // Calculate average rating (flawed implementation)\n      const validRatings = ratings.filter(r => \n        typeof r.rating === 'number' && \n        !isNaN(r.rating) && \n        r.rating >= 1 && \n        r.rating <= 5 &&\n        r.weight > 0\n      );\n      \n      const averageRating = validRatings.length > 0 \n        ? validRatings.reduce((sum, r) => sum + r.rating * r.weight, 0) / \n          validRatings.reduce((sum, r) => sum + r.weight, 0)\n        : 0;\n      \n      // EXPECTED TO FAIL: Should handle invalid data gracefully\n      expect(averageRating).toBeGreaterThan(0);\n      expect(averageRating).toBeLessThanOrEqual(5);\n      expect(Number.isFinite(averageRating)).toBe(true);\n      \n      // Should have filtered out invalid ratings\n      expect(validRatings.length).toBe(2); // Only user1 and user4 should be valid\n    });\n\n    it('should fail - rating aggregation ignores user credibility', () => {\n      const ratings = [\n        { userId: 'expert1', rating: 5, userCredibility: 0.9, expertise: 'high' },\n        { userId: 'novice1', rating: 1, userCredibility: 0.1, expertise: 'low' },\n        { userId: 'expert2', rating: 4, userCredibility: 0.8, expertise: 'high' },\n        { userId: 'spam1', rating: 1, userCredibility: 0.0, expertise: 'none' }\n      ];\n      \n      // Simple average (ignores credibility)\n      const simpleAverage = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length;\n      \n      // Weighted average (considers credibility)\n      const weightedAverage = ratings.reduce((sum, r) => sum + r.rating * r.userCredibility, 0) / \n                             ratings.reduce((sum, r) => sum + r.userCredibility, 0);\n      \n      // EXPECTED TO FAIL: Weighted average should be significantly different from simple average\n      // when there are credibility differences\n      const difference = Math.abs(simpleAverage - weightedAverage);\n      expect(difference).toBeGreaterThan(1.0); // Should be significantly different\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: Cache Key Conflicts and Management', () => {\n    it('should fail - recommendation cache keys can collide', () => {\n      // Different users with similar profiles might get same cache key\n      const user1 = { id: 'user1', skills: ['React', 'JS'], level: 'INTERMEDIATE' };\n      const user2 = { id: 'user2', skills: ['React', 'JS'], level: 'INTERMEDIATE' };\n      \n      // Flawed cache key generation\n      const cacheKey1 = `recommendations_${user1.skills.join(',')}_${user1.level}`;\n      const cacheKey2 = `recommendations_${user2.skills.join(',')}_${user2.level}`;\n      \n      // EXPECTED TO FAIL: Different users should have different cache keys\n      expect(cacheKey1).not.toBe(cacheKey2);\n      \n      // Cache keys should include user ID\n      expect(cacheKey1).toContain(user1.id);\n      expect(cacheKey2).toContain(user2.id);\n    });\n\n    it('should fail - cache invalidation logic is flawed', () => {\n      // Mock cache state\n      const cache = new Map([\n        ['user1_recommendations', { data: ['resource1', 'resource2'], timestamp: Date.now() - 3600000 }], // 1 hour old\n        ['user2_recommendations', { data: ['resource3', 'resource4'], timestamp: Date.now() - 7200000 }], // 2 hours old\n        ['user3_recommendations', { data: ['resource5', 'resource6'], timestamp: Date.now() - 1800000 }], // 30 minutes old\n      ]);\n      \n      const cacheExpiryTime = 3600000; // 1 hour\n      const currentTime = Date.now();\n      \n      // Check which cache entries should be expired\n      const expiredEntries = Array.from(cache.entries()).filter(([key, value]) => \n        currentTime - value.timestamp > cacheExpiryTime\n      );\n      \n      // EXPECTED TO FAIL: Should properly identify expired entries\n      expect(expiredEntries.length).toBe(2); // user1 and user2 should be expired\n      \n      // Cache should be cleaned up automatically\n      expiredEntries.forEach(([key]) => cache.delete(key));\n      expect(cache.size).toBe(1); // Only user3 should remain\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Personalization Logic Flaws', () => {\n    it('should fail - personalization ignores user learning patterns', () => {\n      const userLearningHistory = [\n        { resourceId: 'video1', type: 'video', completionRate: 0.9, timeSpent: 3600 },\n        { resourceId: 'article1', type: 'article', completionRate: 0.3, timeSpent: 300 },\n        { resourceId: 'video2', type: 'video', completionRate: 0.8, timeSpent: 2400 },\n        { resourceId: 'course1', type: 'course', completionRate: 0.1, timeSpent: 600 }\n      ];\n      \n      // Calculate preferred learning type\n      const typePreferences = userLearningHistory.reduce((acc, item) => {\n        if (!acc[item.type]) acc[item.type] = { totalCompletion: 0, count: 0 };\n        acc[item.type].totalCompletion += item.completionRate;\n        acc[item.type].count += 1;\n        return acc;\n      }, {});\n      \n      const preferredType = Object.entries(typePreferences)\n        .map(([type, data]) => ({ type, avgCompletion: data.totalCompletion / data.count }))\n        .sort((a, b) => b.avgCompletion - a.avgCompletion)[0]?.type;\n      \n      // EXPECTED TO FAIL: Should prefer videos based on completion rates\n      expect(preferredType).toBe('video');\n      \n      // Recommendations should prioritize preferred type\n      const recommendations = [\n        { id: 'rec1', type: 'article', score: 0.8 },\n        { id: 'rec2', type: 'video', score: 0.7 },\n        { id: 'rec3', type: 'course', score: 0.9 }\n      ];\n      \n      // Should boost scores for preferred type\n      const adjustedRecommendations = recommendations.map(rec => ({\n        ...rec,\n        adjustedScore: rec.type === preferredType ? rec.score * 1.2 : rec.score\n      }));\n      \n      const topRecommendation = adjustedRecommendations\n        .sort((a, b) => b.adjustedScore - a.adjustedScore)[0];\n      \n      expect(topRecommendation.type).toBe('video'); // Should prioritize preferred type\n    });\n\n    it('should fail - personalization creates filter bubbles', () => {\n      const userProfile = {\n        skills: ['React'],\n        interests: ['Frontend'],\n        completedResources: ['react-basics', 'react-hooks', 'react-router']\n      };\n      \n      // Mock recommendations that are too similar\n      const recommendations = [\n        { id: 'rec1', skills: ['React'], category: 'Frontend', similarity: 0.95 },\n        { id: 'rec2', skills: ['React'], category: 'Frontend', similarity: 0.92 },\n        { id: 'rec3', skills: ['React'], category: 'Frontend', similarity: 0.88 },\n        { id: 'rec4', skills: ['Vue'], category: 'Frontend', similarity: 0.3 },\n        { id: 'rec5', skills: ['Python'], category: 'Backend', similarity: 0.1 }\n      ];\n      \n      // Check for diversity in recommendations\n      const uniqueSkills = new Set(recommendations.flatMap(rec => rec.skills));\n      const uniqueCategories = new Set(recommendations.map(rec => rec.category));\n      \n      // EXPECTED TO FAIL: Should have diversity to prevent filter bubbles\n      expect(uniqueSkills.size).toBeGreaterThan(2); // Should recommend diverse skills\n      expect(uniqueCategories.size).toBeGreaterThan(1); // Should include different categories\n      \n      // Should include some low-similarity items for exploration\n      const exploratoryRecommendations = recommendations.filter(rec => rec.similarity < 0.5);\n      expect(exploratoryRecommendations.length).toBeGreaterThan(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 5: Real-time Update Failures', () => {\n    it('should fail - recommendations dont update when user profile changes', () => {\n      // Mock initial state\n      let userProfile = {\n        skills: ['JavaScript'],\n        level: 'BEGINNER'\n      };\n      \n      let cachedRecommendations = [\n        { id: 'js-basics', difficulty: 'BEGINNER', relevance: 0.9 },\n        { id: 'js-functions', difficulty: 'BEGINNER', relevance: 0.8 }\n      ];\n      \n      // User completes advanced course and profile updates\n      userProfile = {\n        skills: ['JavaScript', 'React', 'Node.js'],\n        level: 'ADVANCED'\n      };\n      \n      // EXPECTED TO FAIL: Recommendations should update to match new profile\n      const updatedRecommendations = [\n        { id: 'advanced-patterns', difficulty: 'ADVANCED', relevance: 0.9 },\n        { id: 'system-design', difficulty: 'ADVANCED', relevance: 0.8 }\n      ];\n      \n      // Cache should be invalidated and updated\n      expect(cachedRecommendations).toEqual(updatedRecommendations);\n      \n      // Should not recommend beginner content to advanced user\n      const beginnerContent = cachedRecommendations.filter(rec => rec.difficulty === 'BEGINNER');\n      expect(beginnerContent.length).toBe(0);\n    });\n\n    it('should fail - recommendation scores dont update with new ratings', () => {\n      const resource = {\n        id: 'react-course',\n        currentRating: 4.2,\n        ratingCount: 100\n      };\n      \n      // New rating comes in\n      const newRating = 5;\n      const newRatingCount = resource.ratingCount + 1;\n      const expectedNewRating = ((resource.currentRating * resource.ratingCount) + newRating) / newRatingCount;\n      \n      // Mock cached recommendation score (should update)\n      let cachedScore = 0.7; // Based on old rating\n      \n      // EXPECTED TO FAIL: Cached score should update when ratings change\n      const updatedScore = 0.8; // Should be higher due to new 5-star rating\n      expect(cachedScore).toBe(updatedScore);\n      \n      // Resource rating should be updated\n      expect(resource.currentRating).toBe(expectedNewRating);\n    });\n  });\n\n  describe('CRITICAL ISSUE 6: Performance and Scalability Issues', () => {\n    it('should fail - recommendation generation is too slow for large datasets', () => {\n      // Mock large dataset\n      const resources = Array.from({ length: 10000 }, (_, i) => ({\n        id: `resource-${i}`,\n        skills: [`skill-${i % 50}`], // 50 different skills\n        difficulty: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'][i % 3],\n        rating: Math.random() * 5\n      }));\n      \n      const userProfile = {\n        skills: ['skill-1', 'skill-5', 'skill-10'],\n        level: 'INTERMEDIATE'\n      };\n      \n      // Simulate recommendation generation time\n      const startTime = Date.now();\n      \n      // Inefficient algorithm - checks every resource\n      const recommendations = resources\n        .filter(resource => \n          resource.skills.some(skill => userProfile.skills.includes(skill)) &&\n          resource.difficulty === userProfile.level\n        )\n        .sort((a, b) => b.rating - a.rating)\n        .slice(0, 10);\n      \n      const endTime = Date.now();\n      const processingTime = endTime - startTime;\n      \n      // EXPECTED TO FAIL: Should process recommendations quickly\n      expect(processingTime).toBeLessThan(100); // Should take less than 100ms\n      expect(recommendations.length).toBeGreaterThan(0);\n    });\n\n    it('should fail - memory usage grows unbounded with recommendation cache', () => {\n      // Mock growing cache\n      const recommendationCache = new Map();\n      \n      // Simulate adding many users to cache\n      for (let i = 0; i < 10000; i++) {\n        const userId = `user-${i}`;\n        const recommendations = Array.from({ length: 50 }, (_, j) => ({\n          id: `resource-${j}`,\n          score: Math.random()\n        }));\n        \n        recommendationCache.set(userId, {\n          data: recommendations,\n          timestamp: Date.now(),\n          metadata: { userProfile: { skills: [`skill-${i % 100}`] } }\n        });\n      }\n      \n      // EXPECTED TO FAIL: Cache should have size limits\n      const maxCacheSize = 1000; // Maximum number of cached users\n      expect(recommendationCache.size).toBeLessThanOrEqual(maxCacheSize);\n      \n      // Should implement LRU eviction\n      const oldestEntry = Array.from(recommendationCache.entries())\n        .sort((a, b) => a[1].timestamp - b[1].timestamp)[0];\n      \n      expect(oldestEntry).toBeUndefined(); // Should have been evicted\n    });\n  });\n});\n"], "version": 3}