5e551f449384ab5d097216c0728af787
"use strict";
/**
 * Resource Recommendation Logic Flaws Tests
 *
 * These tests prove critical flaws in resource recommendation algorithms,
 * rating calculations, and cache management that affect user experience.
 *
 * EXPECTED TO FAIL - These tests demonstrate recommendation logic flaws that need fixing.
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
(0, globals_1.describe)('Resource Recommendation Logic Flaws', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Recommendation Algorithm Inconsistencies', function () {
        (0, globals_1.it)('should fail - multiple recommendation algorithms produce conflicting results', function () {
            // Mock user profile
            var userProfile = {
                skills: ['React', 'JavaScript', 'Node.js'],
                experienceLevel: 'INTERMEDIATE',
                careerGoals: ['Full Stack Developer'],
                completedResources: ['react-basics', 'js-fundamentals']
            };
            // Different recommendation algorithms
            var skillBasedRecommendations = ['advanced-react', 'react-hooks', 'react-testing'];
            var careerPathRecommendations = ['backend-development', 'database-design', 'api-development'];
            var collaborativeFilteringRecommendations = ['vue-js', 'angular', 'python-basics'];
            var contentBasedRecommendations = ['react-performance', 'react-patterns', 'react-ecosystem'];
            // EXPECTED TO FAIL: Different algorithms should produce overlapping, relevant recommendations
            var hasOverlap = skillBasedRecommendations.some(function (rec) {
                return careerPathRecommendations.includes(rec) ||
                    collaborativeFilteringRecommendations.includes(rec) ||
                    contentBasedRecommendations.includes(rec);
            });
            (0, globals_1.expect)(hasOverlap).toBe(true); // Should have some overlap for consistency
            // All recommendations should be relevant to user's skills
            var allRecommendations = __spreadArray(__spreadArray(__spreadArray(__spreadArray([], skillBasedRecommendations, true), careerPathRecommendations, true), collaborativeFilteringRecommendations, true), contentBasedRecommendations, true);
            var relevantRecommendations = allRecommendations.filter(function (rec) {
                return rec.includes('react') || rec.includes('javascript') || rec.includes('node');
            });
            // EXPECTED TO FAIL: Most recommendations should be relevant to user's skills
            (0, globals_1.expect)(relevantRecommendations.length).toBeGreaterThan(allRecommendations.length * 0.7);
        });
        (0, globals_1.it)('should fail - recommendation scoring is inconsistent across algorithms', function () {
            var resource = {
                id: 'react-advanced',
                title: 'Advanced React Patterns',
                skills: ['React', 'JavaScript'],
                difficulty: 'ADVANCED'
            };
            // Different scoring algorithms
            var skillMatchScore = 0.8; // Based on skill overlap
            var difficultyScore = 0.6; // Based on user experience level
            var popularityScore = 0.9; // Based on community ratings
            var personalizedScore = 0.4; // Based on user history
            // EXPECTED TO FAIL: Scores should be weighted and combined consistently
            var combinedScore1 = (skillMatchScore + difficultyScore + popularityScore) / 3;
            var combinedScore2 = skillMatchScore * 0.4 + difficultyScore * 0.3 + popularityScore * 0.3;
            var combinedScore3 = Math.max(skillMatchScore, difficultyScore, popularityScore);
            // Different combination methods should produce similar results for good matches
            var scoreDifference1 = Math.abs(combinedScore1 - combinedScore2);
            var scoreDifference2 = Math.abs(combinedScore2 - combinedScore3);
            (0, globals_1.expect)(scoreDifference1).toBeLessThan(0.2); // Should be similar
            (0, globals_1.expect)(scoreDifference2).toBeLessThan(0.2);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Rating Calculation Flaws', function () {
        (0, globals_1.it)('should fail - rating calculations can produce invalid results', function () {
            // Mock rating data with edge cases
            var ratings = [
                { userId: 'user1', rating: 5, weight: 1.0 },
                { userId: 'user2', rating: 0, weight: 1.0 }, // Invalid rating
                { userId: 'user3', rating: 11, weight: 1.0 }, // Invalid rating
                { userId: 'user4', rating: NaN, weight: 1.0 }, // Invalid rating
                { userId: 'user5', rating: 4, weight: -0.5 }, // Invalid weight
            ];
            // Calculate average rating (flawed implementation)
            var validRatings = ratings.filter(function (r) {
                return typeof r.rating === 'number' &&
                    !isNaN(r.rating) &&
                    r.rating >= 1 &&
                    r.rating <= 5 &&
                    r.weight > 0;
            });
            var averageRating = validRatings.length > 0
                ? validRatings.reduce(function (sum, r) { return sum + r.rating * r.weight; }, 0) /
                    validRatings.reduce(function (sum, r) { return sum + r.weight; }, 0)
                : 0;
            // EXPECTED TO FAIL: Should handle invalid data gracefully
            (0, globals_1.expect)(averageRating).toBeGreaterThan(0);
            (0, globals_1.expect)(averageRating).toBeLessThanOrEqual(5);
            (0, globals_1.expect)(Number.isFinite(averageRating)).toBe(true);
            // Should have filtered out invalid ratings
            (0, globals_1.expect)(validRatings.length).toBe(2); // Only user1 and user4 should be valid
        });
        (0, globals_1.it)('should fail - rating aggregation ignores user credibility', function () {
            var ratings = [
                { userId: 'expert1', rating: 5, userCredibility: 0.9, expertise: 'high' },
                { userId: 'novice1', rating: 1, userCredibility: 0.1, expertise: 'low' },
                { userId: 'expert2', rating: 4, userCredibility: 0.8, expertise: 'high' },
                { userId: 'spam1', rating: 1, userCredibility: 0.0, expertise: 'none' }
            ];
            // Simple average (ignores credibility)
            var simpleAverage = ratings.reduce(function (sum, r) { return sum + r.rating; }, 0) / ratings.length;
            // Weighted average (considers credibility)
            var weightedAverage = ratings.reduce(function (sum, r) { return sum + r.rating * r.userCredibility; }, 0) /
                ratings.reduce(function (sum, r) { return sum + r.userCredibility; }, 0);
            // EXPECTED TO FAIL: Weighted average should be significantly different from simple average
            // when there are credibility differences
            var difference = Math.abs(simpleAverage - weightedAverage);
            (0, globals_1.expect)(difference).toBeGreaterThan(1.0); // Should be significantly different
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Cache Key Conflicts and Management', function () {
        (0, globals_1.it)('should fail - recommendation cache keys can collide', function () {
            // Different users with similar profiles might get same cache key
            var user1 = { id: 'user1', skills: ['React', 'JS'], level: 'INTERMEDIATE' };
            var user2 = { id: 'user2', skills: ['React', 'JS'], level: 'INTERMEDIATE' };
            // Flawed cache key generation
            var cacheKey1 = "recommendations_".concat(user1.skills.join(','), "_").concat(user1.level);
            var cacheKey2 = "recommendations_".concat(user2.skills.join(','), "_").concat(user2.level);
            // EXPECTED TO FAIL: Different users should have different cache keys
            (0, globals_1.expect)(cacheKey1).not.toBe(cacheKey2);
            // Cache keys should include user ID
            (0, globals_1.expect)(cacheKey1).toContain(user1.id);
            (0, globals_1.expect)(cacheKey2).toContain(user2.id);
        });
        (0, globals_1.it)('should fail - cache invalidation logic is flawed', function () {
            // Mock cache state
            var cache = new Map([
                ['user1_recommendations', { data: ['resource1', 'resource2'], timestamp: Date.now() - 3600000 }], // 1 hour old
                ['user2_recommendations', { data: ['resource3', 'resource4'], timestamp: Date.now() - 7200000 }], // 2 hours old
                ['user3_recommendations', { data: ['resource5', 'resource6'], timestamp: Date.now() - 1800000 }], // 30 minutes old
            ]);
            var cacheExpiryTime = 3600000; // 1 hour
            var currentTime = Date.now();
            // Check which cache entries should be expired
            var expiredEntries = Array.from(cache.entries()).filter(function (_a) {
                var key = _a[0], value = _a[1];
                return currentTime - value.timestamp > cacheExpiryTime;
            });
            // EXPECTED TO FAIL: Should properly identify expired entries
            (0, globals_1.expect)(expiredEntries.length).toBe(2); // user1 and user2 should be expired
            // Cache should be cleaned up automatically
            expiredEntries.forEach(function (_a) {
                var key = _a[0];
                return cache.delete(key);
            });
            (0, globals_1.expect)(cache.size).toBe(1); // Only user3 should remain
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Personalization Logic Flaws', function () {
        (0, globals_1.it)('should fail - personalization ignores user learning patterns', function () {
            var _a;
            var userLearningHistory = [
                { resourceId: 'video1', type: 'video', completionRate: 0.9, timeSpent: 3600 },
                { resourceId: 'article1', type: 'article', completionRate: 0.3, timeSpent: 300 },
                { resourceId: 'video2', type: 'video', completionRate: 0.8, timeSpent: 2400 },
                { resourceId: 'course1', type: 'course', completionRate: 0.1, timeSpent: 600 }
            ];
            // Calculate preferred learning type
            var typePreferences = userLearningHistory.reduce(function (acc, item) {
                if (!acc[item.type])
                    acc[item.type] = { totalCompletion: 0, count: 0 };
                acc[item.type].totalCompletion += item.completionRate;
                acc[item.type].count += 1;
                return acc;
            }, {});
            var preferredType = (_a = Object.entries(typePreferences)
                .map(function (_a) {
                var type = _a[0], data = _a[1];
                return ({ type: type, avgCompletion: data.totalCompletion / data.count });
            })
                .sort(function (a, b) { return b.avgCompletion - a.avgCompletion; })[0]) === null || _a === void 0 ? void 0 : _a.type;
            // EXPECTED TO FAIL: Should prefer videos based on completion rates
            (0, globals_1.expect)(preferredType).toBe('video');
            // Recommendations should prioritize preferred type
            var recommendations = [
                { id: 'rec1', type: 'article', score: 0.8 },
                { id: 'rec2', type: 'video', score: 0.7 },
                { id: 'rec3', type: 'course', score: 0.9 }
            ];
            // Should boost scores for preferred type
            var adjustedRecommendations = recommendations.map(function (rec) { return (__assign(__assign({}, rec), { adjustedScore: rec.type === preferredType ? rec.score * 1.2 : rec.score })); });
            var topRecommendation = adjustedRecommendations
                .sort(function (a, b) { return b.adjustedScore - a.adjustedScore; })[0];
            (0, globals_1.expect)(topRecommendation.type).toBe('video'); // Should prioritize preferred type
        });
        (0, globals_1.it)('should fail - personalization creates filter bubbles', function () {
            var userProfile = {
                skills: ['React'],
                interests: ['Frontend'],
                completedResources: ['react-basics', 'react-hooks', 'react-router']
            };
            // Mock recommendations that are too similar
            var recommendations = [
                { id: 'rec1', skills: ['React'], category: 'Frontend', similarity: 0.95 },
                { id: 'rec2', skills: ['React'], category: 'Frontend', similarity: 0.92 },
                { id: 'rec3', skills: ['React'], category: 'Frontend', similarity: 0.88 },
                { id: 'rec4', skills: ['Vue'], category: 'Frontend', similarity: 0.3 },
                { id: 'rec5', skills: ['Python'], category: 'Backend', similarity: 0.1 }
            ];
            // Check for diversity in recommendations
            var uniqueSkills = new Set(recommendations.flatMap(function (rec) { return rec.skills; }));
            var uniqueCategories = new Set(recommendations.map(function (rec) { return rec.category; }));
            // EXPECTED TO FAIL: Should have diversity to prevent filter bubbles
            (0, globals_1.expect)(uniqueSkills.size).toBeGreaterThan(2); // Should recommend diverse skills
            (0, globals_1.expect)(uniqueCategories.size).toBeGreaterThan(1); // Should include different categories
            // Should include some low-similarity items for exploration
            var exploratoryRecommendations = recommendations.filter(function (rec) { return rec.similarity < 0.5; });
            (0, globals_1.expect)(exploratoryRecommendations.length).toBeGreaterThan(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: Real-time Update Failures', function () {
        (0, globals_1.it)('should fail - recommendations dont update when user profile changes', function () {
            // Mock initial state
            var userProfile = {
                skills: ['JavaScript'],
                level: 'BEGINNER'
            };
            var cachedRecommendations = [
                { id: 'js-basics', difficulty: 'BEGINNER', relevance: 0.9 },
                { id: 'js-functions', difficulty: 'BEGINNER', relevance: 0.8 }
            ];
            // User completes advanced course and profile updates
            userProfile = {
                skills: ['JavaScript', 'React', 'Node.js'],
                level: 'ADVANCED'
            };
            // EXPECTED TO FAIL: Recommendations should update to match new profile
            var updatedRecommendations = [
                { id: 'advanced-patterns', difficulty: 'ADVANCED', relevance: 0.9 },
                { id: 'system-design', difficulty: 'ADVANCED', relevance: 0.8 }
            ];
            // Cache should be invalidated and updated
            (0, globals_1.expect)(cachedRecommendations).toEqual(updatedRecommendations);
            // Should not recommend beginner content to advanced user
            var beginnerContent = cachedRecommendations.filter(function (rec) { return rec.difficulty === 'BEGINNER'; });
            (0, globals_1.expect)(beginnerContent.length).toBe(0);
        });
        (0, globals_1.it)('should fail - recommendation scores dont update with new ratings', function () {
            var resource = {
                id: 'react-course',
                currentRating: 4.2,
                ratingCount: 100
            };
            // New rating comes in
            var newRating = 5;
            var newRatingCount = resource.ratingCount + 1;
            var expectedNewRating = ((resource.currentRating * resource.ratingCount) + newRating) / newRatingCount;
            // Mock cached recommendation score (should update)
            var cachedScore = 0.7; // Based on old rating
            // EXPECTED TO FAIL: Cached score should update when ratings change
            var updatedScore = 0.8; // Should be higher due to new 5-star rating
            (0, globals_1.expect)(cachedScore).toBe(updatedScore);
            // Resource rating should be updated
            (0, globals_1.expect)(resource.currentRating).toBe(expectedNewRating);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 6: Performance and Scalability Issues', function () {
        (0, globals_1.it)('should fail - recommendation generation is too slow for large datasets', function () {
            // Mock large dataset
            var resources = Array.from({ length: 10000 }, function (_, i) { return ({
                id: "resource-".concat(i),
                skills: ["skill-".concat(i % 50)], // 50 different skills
                difficulty: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'][i % 3],
                rating: Math.random() * 5
            }); });
            var userProfile = {
                skills: ['skill-1', 'skill-5', 'skill-10'],
                level: 'INTERMEDIATE'
            };
            // Simulate recommendation generation time
            var startTime = Date.now();
            // Inefficient algorithm - checks every resource
            var recommendations = resources
                .filter(function (resource) {
                return resource.skills.some(function (skill) { return userProfile.skills.includes(skill); }) &&
                    resource.difficulty === userProfile.level;
            })
                .sort(function (a, b) { return b.rating - a.rating; })
                .slice(0, 10);
            var endTime = Date.now();
            var processingTime = endTime - startTime;
            // EXPECTED TO FAIL: Should process recommendations quickly
            (0, globals_1.expect)(processingTime).toBeLessThan(100); // Should take less than 100ms
            (0, globals_1.expect)(recommendations.length).toBeGreaterThan(0);
        });
        (0, globals_1.it)('should fail - memory usage grows unbounded with recommendation cache', function () {
            // Mock growing cache
            var recommendationCache = new Map();
            // Simulate adding many users to cache
            for (var i = 0; i < 10000; i++) {
                var userId = "user-".concat(i);
                var recommendations = Array.from({ length: 50 }, function (_, j) { return ({
                    id: "resource-".concat(j),
                    score: Math.random()
                }); });
                recommendationCache.set(userId, {
                    data: recommendations,
                    timestamp: Date.now(),
                    metadata: { userProfile: { skills: ["skill-".concat(i % 100)] } }
                });
            }
            // EXPECTED TO FAIL: Cache should have size limits
            var maxCacheSize = 1000; // Maximum number of cached users
            (0, globals_1.expect)(recommendationCache.size).toBeLessThanOrEqual(maxCacheSize);
            // Should implement LRU eviction
            var oldestEntry = Array.from(recommendationCache.entries())
                .sort(function (a, b) { return a[1].timestamp - b[1].timestamp; })[0];
            (0, globals_1.expect)(oldestEntry).toBeUndefined(); // Should have been evicted
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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