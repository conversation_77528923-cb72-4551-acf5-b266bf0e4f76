e36cfaa7bec0f5b99d97368f2adcab6c
"use strict";

/* istanbul ignore next */
function cov_1vg1prwurh() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/health/database/route.ts";
  var hash = "56b9856c51635cea519de92df127a026d753a38b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/health/database/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 27
        },
        end: {
          line: 5,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 115
        }
      },
      "2": {
        start: {
          line: 3,
          column: 33
        },
        end: {
          line: 3,
          column: 86
        }
      },
      "3": {
        start: {
          line: 3,
          column: 96
        },
        end: {
          line: 3,
          column: 113
        }
      },
      "4": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 18
        }
      },
      "5": {
        start: {
          line: 6,
          column: 16
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "6": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "7": {
        start: {
          line: 7,
          column: 91
        },
        end: {
          line: 7,
          column: 106
        }
      },
      "8": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 13,
          column: 7
        }
      },
      "9": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "10": {
        start: {
          line: 9,
          column: 42
        },
        end: {
          line: 9,
          column: 70
        }
      },
      "11": {
        start: {
          line: 9,
          column: 85
        },
        end: {
          line: 9,
          column: 95
        }
      },
      "12": {
        start: {
          line: 10,
          column: 35
        },
        end: {
          line: 10,
          column: 100
        }
      },
      "13": {
        start: {
          line: 10,
          column: 41
        },
        end: {
          line: 10,
          column: 73
        }
      },
      "14": {
        start: {
          line: 10,
          column: 88
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "15": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 116
        }
      },
      "16": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 78
        }
      },
      "17": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "18": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 104
        }
      },
      "19": {
        start: {
          line: 16,
          column: 43
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "20": {
        start: {
          line: 16,
          column: 57
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "21": {
        start: {
          line: 16,
          column: 69
        },
        end: {
          line: 16,
          column: 81
        }
      },
      "22": {
        start: {
          line: 16,
          column: 119
        },
        end: {
          line: 16,
          column: 196
        }
      },
      "23": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 160
        }
      },
      "24": {
        start: {
          line: 17,
          column: 141
        },
        end: {
          line: 17,
          column: 153
        }
      },
      "25": {
        start: {
          line: 18,
          column: 23
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "26": {
        start: {
          line: 18,
          column: 45
        },
        end: {
          line: 18,
          column: 65
        }
      },
      "27": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "28": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "29": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "30": {
        start: {
          line: 21,
          column: 50
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "31": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "32": {
        start: {
          line: 22,
          column: 160
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "33": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "34": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "35": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 36,
          column: 13
        }
      },
      "36": {
        start: {
          line: 25,
          column: 32
        },
        end: {
          line: 25,
          column: 39
        }
      },
      "37": {
        start: {
          line: 25,
          column: 40
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "38": {
        start: {
          line: 26,
          column: 24
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "39": {
        start: {
          line: 26,
          column: 35
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "40": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "41": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 45
        }
      },
      "42": {
        start: {
          line: 27,
          column: 46
        },
        end: {
          line: 27,
          column: 55
        }
      },
      "43": {
        start: {
          line: 27,
          column: 56
        },
        end: {
          line: 27,
          column: 65
        }
      },
      "44": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "45": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "46": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "47": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 128
        }
      },
      "48": {
        start: {
          line: 30,
          column: 110
        },
        end: {
          line: 30,
          column: 116
        }
      },
      "49": {
        start: {
          line: 30,
          column: 117
        },
        end: {
          line: 30,
          column: 126
        }
      },
      "50": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 106
        }
      },
      "51": {
        start: {
          line: 31,
          column: 81
        },
        end: {
          line: 31,
          column: 97
        }
      },
      "52": {
        start: {
          line: 31,
          column: 98
        },
        end: {
          line: 31,
          column: 104
        }
      },
      "53": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 89
        }
      },
      "54": {
        start: {
          line: 32,
          column: 57
        },
        end: {
          line: 32,
          column: 72
        }
      },
      "55": {
        start: {
          line: 32,
          column: 73
        },
        end: {
          line: 32,
          column: 80
        }
      },
      "56": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "57": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "58": {
        start: {
          line: 33,
          column: 47
        },
        end: {
          line: 33,
          column: 62
        }
      },
      "59": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "60": {
        start: {
          line: 33,
          column: 79
        },
        end: {
          line: 33,
          column: 85
        }
      },
      "61": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "62": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "63": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 33
        }
      },
      "64": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 43
        }
      },
      "65": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "66": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "67": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 41
        }
      },
      "68": {
        start: {
          line: 38,
          column: 54
        },
        end: {
          line: 38,
          column: 64
        }
      },
      "69": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "70": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "71": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "72": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 62
        }
      },
      "73": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 21
        }
      },
      "74": {
        start: {
          line: 44,
          column: 15
        },
        end: {
          line: 44,
          column: 37
        }
      },
      "75": {
        start: {
          line: 45,
          column: 15
        },
        end: {
          line: 45,
          column: 38
        }
      },
      "76": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 76
        }
      },
      "77": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 79,
          column: 7
        }
      },
      "78": {
        start: {
          line: 47,
          column: 86
        },
        end: {
          line: 79,
          column: 3
        }
      },
      "79": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 78,
          column: 7
        }
      },
      "80": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 77,
          column: 9
        }
      },
      "81": {
        start: {
          line: 52,
          column: 16
        },
        end: {
          line: 52,
          column: 39
        }
      },
      "82": {
        start: {
          line: 54,
          column: 16
        },
        end: {
          line: 63,
          column: 37
        }
      },
      "83": {
        start: {
          line: 54,
          column: 83
        },
        end: {
          line: 63,
          column: 23
        }
      },
      "84": {
        start: {
          line: 55,
          column: 24
        },
        end: {
          line: 62,
          column: 27
        }
      },
      "85": {
        start: {
          line: 56,
          column: 28
        },
        end: {
          line: 61,
          column: 29
        }
      },
      "86": {
        start: {
          line: 57,
          column: 40
        },
        end: {
          line: 57,
          column: 179
        }
      },
      "87": {
        start: {
          line: 59,
          column: 36
        },
        end: {
          line: 59,
          column: 46
        }
      },
      "88": {
        start: {
          line: 60,
          column: 36
        },
        end: {
          line: 60,
          column: 58
        }
      },
      "89": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 26
        }
      },
      "90": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 67,
          column: 54
        }
      },
      "91": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 76,
          column: 24
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 66
          },
          end: {
            line: 2,
            column: 67
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 5,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 6,
            column: 45
          }
        },
        loc: {
          start: {
            line: 6,
            column: 89
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 6
      },
      "2": {
        name: "adopt",
        decl: {
          start: {
            line: 7,
            column: 13
          },
          end: {
            line: 7,
            column: 18
          }
        },
        loc: {
          start: {
            line: 7,
            column: 26
          },
          end: {
            line: 7,
            column: 112
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 71
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 7,
            column: 108
          }
        },
        line: 7
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 63
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 8
      },
      "5": {
        name: "fulfilled",
        decl: {
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 26
          }
        },
        loc: {
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 99
          }
        },
        line: 9
      },
      "6": {
        name: "rejected",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 33
          },
          end: {
            line: 10,
            column: 102
          }
        },
        line: 10
      },
      "7": {
        name: "step",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 21
          }
        },
        loc: {
          start: {
            line: 11,
            column: 30
          },
          end: {
            line: 11,
            column: 118
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 49
          }
        },
        loc: {
          start: {
            line: 15,
            column: 73
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 15
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 16,
            column: 30
          },
          end: {
            line: 16,
            column: 31
          }
        },
        loc: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 83
          }
        },
        line: 16
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 17,
            column: 128
          },
          end: {
            line: 17,
            column: 129
          }
        },
        loc: {
          start: {
            line: 17,
            column: 139
          },
          end: {
            line: 17,
            column: 155
          }
        },
        line: 17
      },
      "11": {
        name: "verb",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 70
          }
        },
        line: 18
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 31
          }
        },
        loc: {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 67
          }
        },
        line: 18
      },
      "13": {
        name: "step",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 19
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 47,
            column: 72
          },
          end: {
            line: 47,
            column: 73
          }
        },
        loc: {
          start: {
            line: 47,
            column: 84
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 47,
            column: 127
          },
          end: {
            line: 47,
            column: 128
          }
        },
        loc: {
          start: {
            line: 47,
            column: 139
          },
          end: {
            line: 79,
            column: 1
          }
        },
        line: 47
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 49,
            column: 29
          },
          end: {
            line: 49,
            column: 30
          }
        },
        loc: {
          start: {
            line: 49,
            column: 43
          },
          end: {
            line: 78,
            column: 5
          }
        },
        line: 49
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 54,
            column: 69
          },
          end: {
            line: 54,
            column: 70
          }
        },
        loc: {
          start: {
            line: 54,
            column: 81
          },
          end: {
            line: 63,
            column: 25
          }
        },
        line: 54
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 54,
            column: 124
          },
          end: {
            line: 54,
            column: 125
          }
        },
        loc: {
          start: {
            line: 54,
            column: 136
          },
          end: {
            line: 63,
            column: 21
          }
        },
        line: 54
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 55,
            column: 49
          },
          end: {
            line: 55,
            column: 50
          }
        },
        loc: {
          start: {
            line: 55,
            column: 63
          },
          end: {
            line: 62,
            column: 25
          }
        },
        line: 55
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 27
          },
          end: {
            line: 5,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 28
          },
          end: {
            line: 2,
            column: 32
          }
        }, {
          start: {
            line: 2,
            column: 36
          },
          end: {
            line: 2,
            column: 61
          }
        }, {
          start: {
            line: 2,
            column: 66
          },
          end: {
            line: 5,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 115
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 115
          }
        }, {
          start: {
            line: 3,
            column: 94
          },
          end: {
            line: 3,
            column: 115
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 21
          }
        }, {
          start: {
            line: 6,
            column: 25
          },
          end: {
            line: 6,
            column: 39
          }
        }, {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 56
          },
          end: {
            line: 7,
            column: 61
          }
        }, {
          start: {
            line: 7,
            column: 64
          },
          end: {
            line: 7,
            column: 109
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 17
          }
        }, {
          start: {
            line: 8,
            column: 22
          },
          end: {
            line: 8,
            column: 33
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 32
          },
          end: {
            line: 11,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 11,
            column: 46
          },
          end: {
            line: 11,
            column: 67
          }
        }, {
          start: {
            line: 11,
            column: 70
          },
          end: {
            line: 11,
            column: 115
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 61
          }
        }, {
          start: {
            line: 12,
            column: 65
          },
          end: {
            line: 12,
            column: 67
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 41,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 23
          }
        }, {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 43
          }
        }, {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 41,
            column: 1
          }
        }],
        line: 15
      },
      "8": {
        loc: {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 134
          },
          end: {
            line: 16,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 167
          },
          end: {
            line: 16,
            column: 175
          }
        }, {
          start: {
            line: 16,
            column: 178
          },
          end: {
            line: 16,
            column: 184
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 102
          }
        }, {
          start: {
            line: 17,
            column: 107
          },
          end: {
            line: 17,
            column: 155
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "12": {
        loc: {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 16
          }
        }, {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "13": {
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 43
          }
        }],
        line: 21
      },
      "14": {
        loc: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "15": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 125
          }
        }, {
          start: {
            line: 22,
            column: 130
          },
          end: {
            line: 22,
            column: 158
          }
        }],
        line: 22
      },
      "16": {
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 22,
            column: 56
          }
        }, {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "17": {
        loc: {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        }, {
          start: {
            line: 22,
            column: 119
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "18": {
        loc: {
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 77
          }
        }, {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "19": {
        loc: {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 83
          },
          end: {
            line: 22,
            column: 98
          }
        }, {
          start: {
            line: 22,
            column: 103
          },
          end: {
            line: 22,
            column: 112
          }
        }],
        line: 22
      },
      "20": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "21": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 23
          }
        }, {
          start: {
            line: 25,
            column: 24
          },
          end: {
            line: 25,
            column: 46
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 72
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 65
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 24
      },
      "22": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "23": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 74
          }
        }, {
          start: {
            line: 30,
            column: 79
          },
          end: {
            line: 30,
            column: 90
          }
        }, {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 30,
            column: 105
          }
        }],
        line: 30
      },
      "24": {
        loc: {
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 73
          }
        }],
        line: 30
      },
      "25": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "26": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 35
          }
        }, {
          start: {
            line: 31,
            column: 40
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: 31,
            column: 47
          },
          end: {
            line: 31,
            column: 59
          }
        }, {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 31,
            column: 75
          }
        }],
        line: 31
      },
      "27": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "28": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 39
          },
          end: {
            line: 32,
            column: 53
          }
        }],
        line: 32
      },
      "29": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "30": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 25
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "31": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "32": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 52
          },
          end: {
            line: 39,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 60
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 68
          },
          end: {
            line: 39,
            column: 74
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 77,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 63,
            column: 37
          }
        }, {
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 76,
            column: 24
          }
        }],
        line: 50
      },
      "35": {
        loc: {
          start: {
            line: 56,
            column: 28
          },
          end: {
            line: 61,
            column: 29
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 57,
            column: 32
          },
          end: {
            line: 57,
            column: 179
          }
        }, {
          start: {
            line: 58,
            column: 32
          },
          end: {
            line: 60,
            column: 58
          }
        }],
        line: 56
      },
      "36": {
        loc: {
          start: {
            line: 57,
            column: 87
          },
          end: {
            line: 57,
            column: 176
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 87
          },
          end: {
            line: 57,
            column: 103
          }
        }, {
          start: {
            line: 57,
            column: 108
          },
          end: {
            line: 57,
            column: 175
          }
        }],
        line: 57
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0, 0, 0, 0, 0],
      "22": [0, 0],
      "23": [0, 0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0, 0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/health/database/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAA2C;AAC3C,uCAAyD;AACzD,6EAAwF;AAU3E,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC;;;;;gBACpC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,4CAA4C;gBAC5C,qBAAM,IAAA,0BAAiB,EAAC;;;wCACtB,qBAAM,eAAM,CAAC,SAAS,6EAAA,UAAU,MAAA;;oCAAhC,SAAgC,CAAC;;;;yBAClC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAA;;gBAHX,4CAA4C;gBAC5C,SAEW,CAAC;gBAEN,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE5C,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,MAAM,EAAE,SAAS;4BACjB,QAAQ,EAAE,WAAW;4BACrB,YAAY,EAAE,UAAG,YAAY,OAAI;4BACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/health/database/route.ts"],
      sourcesContent: ["import { NextResponse } from 'next/server';\nimport { prisma, withDatabaseRetry } from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface DatabaseHealthResponse {\n  status: 'healthy' | 'unhealthy';\n  database: 'connected' | 'disconnected';\n  responseTime?: string;\n  timestamp: string;\n  error?: string;\n}\n\nexport const GET = withUnifiedErrorHandling(async () => {\n  const startTime = Date.now();\n\n  // Test database connection with retry logic\n  await withDatabaseRetry(async () => {\n    await prisma.$queryRaw`SELECT 1`;\n  }, 2, 1000);\n\n  const responseTime = Date.now() - startTime;\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      status: 'healthy',\n      database: 'connected',\n      responseTime: `${responseTime}ms`,\n      timestamp: new Date().toISOString()\n    }\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "56b9856c51635cea519de92df127a026d753a38b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1vg1prwurh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1vg1prwurh();
var __makeTemplateObject =
/* istanbul ignore next */
(cov_1vg1prwurh().s[0]++,
/* istanbul ignore next */
(cov_1vg1prwurh().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1vg1prwurh().b[0][1]++, this.__makeTemplateObject) ||
/* istanbul ignore next */
(cov_1vg1prwurh().b[0][2]++, function (cooked, raw) {
  /* istanbul ignore next */
  cov_1vg1prwurh().f[0]++;
  cov_1vg1prwurh().s[1]++;
  if (Object.defineProperty) {
    /* istanbul ignore next */
    cov_1vg1prwurh().b[1][0]++;
    cov_1vg1prwurh().s[2]++;
    Object.defineProperty(cooked, "raw", {
      value: raw
    });
  } else {
    /* istanbul ignore next */
    cov_1vg1prwurh().b[1][1]++;
    cov_1vg1prwurh().s[3]++;
    cooked.raw = raw;
  }
  /* istanbul ignore next */
  cov_1vg1prwurh().s[4]++;
  return cooked;
}));
var __awaiter =
/* istanbul ignore next */
(cov_1vg1prwurh().s[5]++,
/* istanbul ignore next */
(cov_1vg1prwurh().b[2][0]++, this) &&
/* istanbul ignore next */
(cov_1vg1prwurh().b[2][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1vg1prwurh().b[2][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1vg1prwurh().f[1]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1vg1prwurh().f[2]++;
    cov_1vg1prwurh().s[6]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[3][0]++, value) :
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[3][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1vg1prwurh().f[3]++;
      cov_1vg1prwurh().s[7]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1vg1prwurh().s[8]++;
  return new (
  /* istanbul ignore next */
  (cov_1vg1prwurh().b[4][0]++, P) ||
  /* istanbul ignore next */
  (cov_1vg1prwurh().b[4][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1vg1prwurh().f[4]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1vg1prwurh().f[5]++;
      cov_1vg1prwurh().s[9]++;
      try {
        /* istanbul ignore next */
        cov_1vg1prwurh().s[10]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1vg1prwurh().s[11]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1vg1prwurh().f[6]++;
      cov_1vg1prwurh().s[12]++;
      try {
        /* istanbul ignore next */
        cov_1vg1prwurh().s[13]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1vg1prwurh().s[14]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1vg1prwurh().f[7]++;
      cov_1vg1prwurh().s[15]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1vg1prwurh().b[5][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1vg1prwurh().b[5][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1vg1prwurh().s[16]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[6][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[6][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1vg1prwurh().s[17]++,
/* istanbul ignore next */
(cov_1vg1prwurh().b[7][0]++, this) &&
/* istanbul ignore next */
(cov_1vg1prwurh().b[7][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1vg1prwurh().b[7][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1vg1prwurh().f[8]++;
  var _ =
    /* istanbul ignore next */
    (cov_1vg1prwurh().s[18]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1vg1prwurh().f[9]++;
        cov_1vg1prwurh().s[19]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1vg1prwurh().b[8][0]++;
          cov_1vg1prwurh().s[20]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1vg1prwurh().b[8][1]++;
        }
        cov_1vg1prwurh().s[21]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1vg1prwurh().s[22]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[9][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[9][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1vg1prwurh().s[23]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1vg1prwurh().b[10][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1vg1prwurh().b[10][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1vg1prwurh().f[10]++;
    cov_1vg1prwurh().s[24]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1vg1prwurh().f[11]++;
    cov_1vg1prwurh().s[25]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1vg1prwurh().f[12]++;
      cov_1vg1prwurh().s[26]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1vg1prwurh().f[13]++;
    cov_1vg1prwurh().s[27]++;
    if (f) {
      /* istanbul ignore next */
      cov_1vg1prwurh().b[11][0]++;
      cov_1vg1prwurh().s[28]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1vg1prwurh().b[11][1]++;
    }
    cov_1vg1prwurh().s[29]++;
    while (
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[12][0]++, g) &&
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[12][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[13][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1vg1prwurh().b[13][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1vg1prwurh().s[30]++;
      try {
        /* istanbul ignore next */
        cov_1vg1prwurh().s[31]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[15][0]++, y) &&
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[15][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[16][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[16][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[17][0]++,
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[18][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[18][1]++,
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[19][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[19][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[17][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1vg1prwurh().b[15][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1vg1prwurh().b[14][0]++;
          cov_1vg1prwurh().s[32]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1vg1prwurh().b[14][1]++;
        }
        cov_1vg1prwurh().s[33]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1vg1prwurh().b[20][0]++;
          cov_1vg1prwurh().s[34]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1vg1prwurh().b[20][1]++;
        }
        cov_1vg1prwurh().s[35]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1vg1prwurh().b[21][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1vg1prwurh().b[21][1]++;
            cov_1vg1prwurh().s[36]++;
            t = op;
            /* istanbul ignore next */
            cov_1vg1prwurh().s[37]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1vg1prwurh().b[21][2]++;
            cov_1vg1prwurh().s[38]++;
            _.label++;
            /* istanbul ignore next */
            cov_1vg1prwurh().s[39]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1vg1prwurh().b[21][3]++;
            cov_1vg1prwurh().s[40]++;
            _.label++;
            /* istanbul ignore next */
            cov_1vg1prwurh().s[41]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1vg1prwurh().s[42]++;
            op = [0];
            /* istanbul ignore next */
            cov_1vg1prwurh().s[43]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1vg1prwurh().b[21][4]++;
            cov_1vg1prwurh().s[44]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1vg1prwurh().s[45]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1vg1prwurh().s[46]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1vg1prwurh().b[21][5]++;
            cov_1vg1prwurh().s[47]++;
            if (
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[23][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[24][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[24][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[23][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[23][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1vg1prwurh().b[22][0]++;
              cov_1vg1prwurh().s[48]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1vg1prwurh().s[49]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1vg1prwurh().b[22][1]++;
            }
            cov_1vg1prwurh().s[50]++;
            if (
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[26][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[26][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[26][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[26][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1vg1prwurh().b[25][0]++;
              cov_1vg1prwurh().s[51]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1vg1prwurh().s[52]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1vg1prwurh().b[25][1]++;
            }
            cov_1vg1prwurh().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[28][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[28][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1vg1prwurh().b[27][0]++;
              cov_1vg1prwurh().s[54]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1vg1prwurh().s[55]++;
              t = op;
              /* istanbul ignore next */
              cov_1vg1prwurh().s[56]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1vg1prwurh().b[27][1]++;
            }
            cov_1vg1prwurh().s[57]++;
            if (
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[30][0]++, t) &&
            /* istanbul ignore next */
            (cov_1vg1prwurh().b[30][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1vg1prwurh().b[29][0]++;
              cov_1vg1prwurh().s[58]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1vg1prwurh().s[59]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1vg1prwurh().s[60]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1vg1prwurh().b[29][1]++;
            }
            cov_1vg1prwurh().s[61]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1vg1prwurh().b[31][0]++;
              cov_1vg1prwurh().s[62]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1vg1prwurh().b[31][1]++;
            }
            cov_1vg1prwurh().s[63]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1vg1prwurh().s[64]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1vg1prwurh().s[65]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1vg1prwurh().s[66]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1vg1prwurh().s[67]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1vg1prwurh().s[68]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1vg1prwurh().s[69]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1vg1prwurh().b[32][0]++;
      cov_1vg1prwurh().s[70]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1vg1prwurh().b[32][1]++;
    }
    cov_1vg1prwurh().s[71]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1vg1prwurh().b[33][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1vg1prwurh().b[33][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1vg1prwurh().s[72]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1vg1prwurh().s[73]++;
exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1vg1prwurh().s[74]++, require("next/server"));
var prisma_1 =
/* istanbul ignore next */
(cov_1vg1prwurh().s[75]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1vg1prwurh().s[76]++, require("@/lib/unified-api-error-handler"));
/* istanbul ignore next */
cov_1vg1prwurh().s[77]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function () {
  /* istanbul ignore next */
  cov_1vg1prwurh().f[14]++;
  cov_1vg1prwurh().s[78]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1vg1prwurh().f[15]++;
    var startTime, responseTime;
    /* istanbul ignore next */
    cov_1vg1prwurh().s[79]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1vg1prwurh().f[16]++;
      cov_1vg1prwurh().s[80]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_1vg1prwurh().b[34][0]++;
          cov_1vg1prwurh().s[81]++;
          startTime = Date.now();
          // Test database connection with retry logic
          /* istanbul ignore next */
          cov_1vg1prwurh().s[82]++;
          return [4 /*yield*/, (0, prisma_1.withDatabaseRetry)(function () {
            /* istanbul ignore next */
            cov_1vg1prwurh().f[17]++;
            cov_1vg1prwurh().s[83]++;
            return __awaiter(void 0, void 0, void 0, function () {
              /* istanbul ignore next */
              cov_1vg1prwurh().f[18]++;
              cov_1vg1prwurh().s[84]++;
              return __generator(this, function (_a) {
                /* istanbul ignore next */
                cov_1vg1prwurh().f[19]++;
                cov_1vg1prwurh().s[85]++;
                switch (_a.label) {
                  case 0:
                    /* istanbul ignore next */
                    cov_1vg1prwurh().b[35][0]++;
                    cov_1vg1prwurh().s[86]++;
                    return [4 /*yield*/, prisma_1.prisma.$queryRaw(
                    /* istanbul ignore next */
                    (cov_1vg1prwurh().b[36][0]++, templateObject_1) ||
                    /* istanbul ignore next */
                    (cov_1vg1prwurh().b[36][1]++, templateObject_1 = __makeTemplateObject(["SELECT 1"], ["SELECT 1"])))];
                  case 1:
                    /* istanbul ignore next */
                    cov_1vg1prwurh().b[35][1]++;
                    cov_1vg1prwurh().s[87]++;
                    _a.sent();
                    /* istanbul ignore next */
                    cov_1vg1prwurh().s[88]++;
                    return [2 /*return*/];
                }
              });
            });
          }, 2, 1000)];
        case 1:
          /* istanbul ignore next */
          cov_1vg1prwurh().b[34][1]++;
          cov_1vg1prwurh().s[89]++;
          // Test database connection with retry logic
          _a.sent();
          /* istanbul ignore next */
          cov_1vg1prwurh().s[90]++;
          responseTime = Date.now() - startTime;
          /* istanbul ignore next */
          cov_1vg1prwurh().s[91]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              status: 'healthy',
              database: 'connected',
              responseTime: "".concat(responseTime, "ms"),
              timestamp: new Date().toISOString()
            }
          })];
      }
    });
  });
});
var templateObject_1;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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