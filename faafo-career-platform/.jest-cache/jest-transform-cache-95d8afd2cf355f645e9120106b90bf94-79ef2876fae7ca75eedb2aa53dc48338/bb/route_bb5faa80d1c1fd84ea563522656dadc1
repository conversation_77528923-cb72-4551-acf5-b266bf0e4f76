c88a74a1972c5c8f81815290a51fd12d
"use strict";

/* istanbul ignore next */
function cov_mm2ldjm2e() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/career-paths/bookmarks/route.ts";
  var hash = "2b7fc7cbcc65643370bc43e729fa2af61aa1d8c5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/career-paths/bookmarks/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 38
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "85": {
        start: {
          line: 57,
          column: 15
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "86": {
        start: {
          line: 58,
          column: 13
        },
        end: {
          line: 58,
          column: 34
        }
      },
      "87": {
        start: {
          line: 59,
          column: 34
        },
        end: {
          line: 59,
          column: 76
        }
      },
      "88": {
        start: {
          line: 61,
          column: 0
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "89": {
        start: {
          line: 61,
          column: 93
        },
        end: {
          line: 142,
          column: 3
        }
      },
      "90": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 141,
          column: 7
        }
      },
      "91": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "92": {
        start: {
          line: 66,
          column: 20
        },
        end: {
          line: 66,
          column: 96
        }
      },
      "93": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 68,
          column: 36
        }
      },
      "94": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 73,
          column: 17
        }
      },
      "95": {
        start: {
          line: 70,
          column: 20
        },
        end: {
          line: 70,
          column: 65
        }
      },
      "96": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 71,
          column: 43
        }
      },
      "97": {
        start: {
          line: 72,
          column: 20
        },
        end: {
          line: 72,
          column: 32
        }
      },
      "98": {
        start: {
          line: 74,
          column: 16
        },
        end: {
          line: 74,
          column: 65
        }
      },
      "99": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 75,
          column: 65
        }
      },
      "100": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 76,
          column: 68
        }
      },
      "101": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 42
        }
      },
      "102": {
        start: {
          line: 78,
          column: 16
        },
        end: {
          line: 109,
          column: 24
        }
      },
      "103": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 111,
          column: 38
        }
      },
      "104": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 116,
          column: 24
        }
      },
      "105": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 118,
          column: 39
        }
      },
      "106": {
        start: {
          line: 119,
          column: 16
        },
        end: {
          line: 125,
          column: 23
        }
      },
      "107": {
        start: {
          line: 119,
          column: 73
        },
        end: {
          line: 125,
          column: 19
        }
      },
      "108": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 139,
          column: 24
        }
      },
      "109": {
        start: {
          line: 144,
          column: 0
        },
        end: {
          line: 192,
          column: 7
        }
      },
      "110": {
        start: {
          line: 144,
          column: 96
        },
        end: {
          line: 192,
          column: 3
        }
      },
      "111": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 191,
          column: 7
        }
      },
      "112": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 190,
          column: 20
        }
      },
      "113": {
        start: {
          line: 146,
          column: 84
        },
        end: {
          line: 190,
          column: 15
        }
      },
      "114": {
        start: {
          line: 149,
          column: 16
        },
        end: {
          line: 189,
          column: 19
        }
      },
      "115": {
        start: {
          line: 150,
          column: 20
        },
        end: {
          line: 188,
          column: 21
        }
      },
      "116": {
        start: {
          line: 151,
          column: 32
        },
        end: {
          line: 151,
          column: 108
        }
      },
      "117": {
        start: {
          line: 153,
          column: 28
        },
        end: {
          line: 153,
          column: 48
        }
      },
      "118": {
        start: {
          line: 154,
          column: 28
        },
        end: {
          line: 158,
          column: 29
        }
      },
      "119": {
        start: {
          line: 155,
          column: 32
        },
        end: {
          line: 155,
          column: 77
        }
      },
      "120": {
        start: {
          line: 156,
          column: 32
        },
        end: {
          line: 156,
          column: 55
        }
      },
      "121": {
        start: {
          line: 157,
          column: 32
        },
        end: {
          line: 157,
          column: 44
        }
      },
      "122": {
        start: {
          line: 159,
          column: 28
        },
        end: {
          line: 159,
          column: 65
        }
      },
      "123": {
        start: {
          line: 161,
          column: 28
        },
        end: {
          line: 161,
          column: 45
        }
      },
      "124": {
        start: {
          line: 162,
          column: 28
        },
        end: {
          line: 162,
          column: 61
        }
      },
      "125": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 167,
          column: 29
        }
      },
      "126": {
        start: {
          line: 164,
          column: 32
        },
        end: {
          line: 164,
          column: 80
        }
      },
      "127": {
        start: {
          line: 165,
          column: 32
        },
        end: {
          line: 165,
          column: 55
        }
      },
      "128": {
        start: {
          line: 166,
          column: 32
        },
        end: {
          line: 166,
          column: 44
        }
      },
      "129": {
        start: {
          line: 168,
          column: 28
        },
        end: {
          line: 173,
          column: 36
        }
      },
      "130": {
        start: {
          line: 175,
          column: 28
        },
        end: {
          line: 175,
          column: 56
        }
      },
      "131": {
        start: {
          line: 176,
          column: 28
        },
        end: {
          line: 180,
          column: 29
        }
      },
      "132": {
        start: {
          line: 177,
          column: 32
        },
        end: {
          line: 177,
          column: 72
        }
      },
      "133": {
        start: {
          line: 178,
          column: 32
        },
        end: {
          line: 178,
          column: 55
        }
      },
      "134": {
        start: {
          line: 179,
          column: 32
        },
        end: {
          line: 179,
          column: 44
        }
      },
      "135": {
        start: {
          line: 181,
          column: 28
        },
        end: {
          line: 187,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 61,
            column: 72
          },
          end: {
            line: 61,
            column: 73
          }
        },
        loc: {
          start: {
            line: 61,
            column: 91
          },
          end: {
            line: 142,
            column: 5
          }
        },
        line: 61
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 61,
            column: 134
          },
          end: {
            line: 61,
            column: 135
          }
        },
        loc: {
          start: {
            line: 61,
            column: 146
          },
          end: {
            line: 142,
            column: 1
          }
        },
        line: 61
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 64,
            column: 29
          },
          end: {
            line: 64,
            column: 30
          }
        },
        loc: {
          start: {
            line: 64,
            column: 43
          },
          end: {
            line: 141,
            column: 5
          }
        },
        line: 64
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 119,
            column: 51
          },
          end: {
            line: 119,
            column: 52
          }
        },
        loc: {
          start: {
            line: 119,
            column: 71
          },
          end: {
            line: 125,
            column: 21
          }
        },
        line: 119
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 144,
            column: 75
          },
          end: {
            line: 144,
            column: 76
          }
        },
        loc: {
          start: {
            line: 144,
            column: 94
          },
          end: {
            line: 192,
            column: 5
          }
        },
        line: 144
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 144,
            column: 137
          },
          end: {
            line: 144,
            column: 138
          }
        },
        loc: {
          start: {
            line: 144,
            column: 149
          },
          end: {
            line: 192,
            column: 1
          }
        },
        line: 144
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 145,
            column: 29
          },
          end: {
            line: 145,
            column: 30
          }
        },
        loc: {
          start: {
            line: 145,
            column: 43
          },
          end: {
            line: 191,
            column: 5
          }
        },
        line: 145
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 146,
            column: 70
          },
          end: {
            line: 146,
            column: 71
          }
        },
        loc: {
          start: {
            line: 146,
            column: 82
          },
          end: {
            line: 190,
            column: 17
          }
        },
        line: 146
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 146,
            column: 125
          },
          end: {
            line: 146,
            column: 126
          }
        },
        loc: {
          start: {
            line: 146,
            column: 137
          },
          end: {
            line: 190,
            column: 13
          }
        },
        line: 146
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 149,
            column: 41
          },
          end: {
            line: 149,
            column: 42
          }
        },
        loc: {
          start: {
            line: 149,
            column: 55
          },
          end: {
            line: 189,
            column: 17
          }
        },
        line: 149
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 65,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 66,
            column: 12
          },
          end: {
            line: 66,
            column: 96
          }
        }, {
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 109,
            column: 24
          }
        }, {
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 116,
            column: 24
          }
        }, {
          start: {
            line: 117,
            column: 12
          },
          end: {
            line: 139,
            column: 24
          }
        }],
        line: 65
      },
      "39": {
        loc: {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 73,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 73,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "40": {
        loc: {
          start: {
            line: 69,
            column: 22
          },
          end: {
            line: 69,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 120
          },
          end: {
            line: 69,
            column: 126
          }
        }, {
          start: {
            line: 69,
            column: 129
          },
          end: {
            line: 69,
            column: 134
          }
        }],
        line: 69
      },
      "41": {
        loc: {
          start: {
            line: 69,
            column: 22
          },
          end: {
            line: 69,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 22
          },
          end: {
            line: 69,
            column: 100
          }
        }, {
          start: {
            line: 69,
            column: 104
          },
          end: {
            line: 69,
            column: 117
          }
        }],
        line: 69
      },
      "42": {
        loc: {
          start: {
            line: 69,
            column: 28
          },
          end: {
            line: 69,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 69
          },
          end: {
            line: 69,
            column: 75
          }
        }, {
          start: {
            line: 69,
            column: 78
          },
          end: {
            line: 69,
            column: 90
          }
        }],
        line: 69
      },
      "43": {
        loc: {
          start: {
            line: 69,
            column: 28
          },
          end: {
            line: 69,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 28
          },
          end: {
            line: 69,
            column: 44
          }
        }, {
          start: {
            line: 69,
            column: 48
          },
          end: {
            line: 69,
            column: 66
          }
        }],
        line: 69
      },
      "44": {
        loc: {
          start: {
            line: 75,
            column: 32
          },
          end: {
            line: 75,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 32
          },
          end: {
            line: 75,
            column: 56
          }
        }, {
          start: {
            line: 75,
            column: 60
          },
          end: {
            line: 75,
            column: 63
          }
        }],
        line: 75
      },
      "45": {
        loc: {
          start: {
            line: 76,
            column: 33
          },
          end: {
            line: 76,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 76,
            column: 33
          },
          end: {
            line: 76,
            column: 58
          }
        }, {
          start: {
            line: 76,
            column: 62
          },
          end: {
            line: 76,
            column: 66
          }
        }],
        line: 76
      },
      "46": {
        loc: {
          start: {
            line: 122,
            column: 183
          },
          end: {
            line: 124,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 123,
            column: 30
          },
          end: {
            line: 123,
            column: 65
          }
        }, {
          start: {
            line: 124,
            column: 30
          },
          end: {
            line: 124,
            column: 93
          }
        }],
        line: 122
      },
      "47": {
        loc: {
          start: {
            line: 124,
            column: 41
          },
          end: {
            line: 124,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 124,
            column: 41
          },
          end: {
            line: 124,
            column: 84
          }
        }, {
          start: {
            line: 124,
            column: 88
          },
          end: {
            line: 124,
            column: 92
          }
        }],
        line: 124
      },
      "48": {
        loc: {
          start: {
            line: 150,
            column: 20
          },
          end: {
            line: 188,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 151,
            column: 24
          },
          end: {
            line: 151,
            column: 108
          }
        }, {
          start: {
            line: 152,
            column: 24
          },
          end: {
            line: 159,
            column: 65
          }
        }, {
          start: {
            line: 160,
            column: 24
          },
          end: {
            line: 173,
            column: 36
          }
        }, {
          start: {
            line: 174,
            column: 24
          },
          end: {
            line: 187,
            column: 36
          }
        }],
        line: 150
      },
      "49": {
        loc: {
          start: {
            line: 154,
            column: 28
          },
          end: {
            line: 158,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 28
          },
          end: {
            line: 158,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "50": {
        loc: {
          start: {
            line: 154,
            column: 34
          },
          end: {
            line: 154,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 132
          },
          end: {
            line: 154,
            column: 138
          }
        }, {
          start: {
            line: 154,
            column: 141
          },
          end: {
            line: 154,
            column: 146
          }
        }],
        line: 154
      },
      "51": {
        loc: {
          start: {
            line: 154,
            column: 34
          },
          end: {
            line: 154,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 34
          },
          end: {
            line: 154,
            column: 112
          }
        }, {
          start: {
            line: 154,
            column: 116
          },
          end: {
            line: 154,
            column: 129
          }
        }],
        line: 154
      },
      "52": {
        loc: {
          start: {
            line: 154,
            column: 40
          },
          end: {
            line: 154,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 81
          },
          end: {
            line: 154,
            column: 87
          }
        }, {
          start: {
            line: 154,
            column: 90
          },
          end: {
            line: 154,
            column: 102
          }
        }],
        line: 154
      },
      "53": {
        loc: {
          start: {
            line: 154,
            column: 40
          },
          end: {
            line: 154,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 40
          },
          end: {
            line: 154,
            column: 56
          }
        }, {
          start: {
            line: 154,
            column: 60
          },
          end: {
            line: 154,
            column: 78
          }
        }],
        line: 154
      },
      "54": {
        loc: {
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 167,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 167,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "55": {
        loc: {
          start: {
            line: 176,
            column: 28
          },
          end: {
            line: 180,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 28
          },
          end: {
            line: 180,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0, 0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/career-paths/bookmarks/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,wDAAkC;AAClC,mCAAgD;AAChD,6EAAwF;AAyBxF,uEAAuE;AAC1D,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;;;;oBACrD,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAES,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;gBACjD,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;gBACpD,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;gBAGd,qBAAM,gBAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;wBACzD,KAAK,EAAE;4BACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;yBACxB;wBACD,OAAO,EAAE;4BACP,UAAU,EAAE;gCACV,OAAO,EAAE;oCACP,iBAAiB,EAAE;wCACjB,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;wCACzB,MAAM,EAAE;4CACN,EAAE,EAAE,IAAI;4CACR,KAAK,EAAE,IAAI;4CACX,UAAU,EAAE,IAAI;4CAChB,IAAI,EAAE,IAAI;yCACX;wCACD,IAAI,EAAE,CAAC;qCACR;oCACD,aAAa,EAAE;wCACb,MAAM,EAAE;4CACN,EAAE,EAAE,IAAI;4CACR,IAAI,EAAE,IAAI;yCACX;qCACF;iCACF;6BACF;yBACF;wBACD,OAAO,EAAE;4BACP,SAAS,EAAE,MAAM;yBAClB;wBACD,IAAI,MAAA;wBACJ,IAAI,EAAE,KAAK;qBACZ,CAAC,EAAA;;gBA/BI,SAAS,GAAG,SA+BhB;gBAGiB,qBAAM,gBAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;wBACvD,KAAK,EAAE;4BACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;yBACxB;qBACF,CAAC,EAAA;;gBAJI,UAAU,GAAG,SAIjB;gBAGI,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,CAAC;oBACpD,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,YAAY,EAAE,QAAQ,CAAC,SAAS;oBAChC,UAAU,wBACL,QAAQ,CAAC,UAAU,KACtB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAC1C,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAC1C,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC;4BACjE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe;4BACrC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,EACnE,YAAY,EAAE,IAAI,GACnB;iBACF,CAAC,EAZmD,CAYnD,CAAC,CAAC;gBAEN,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,SAAS,EAAE,kBAAkB;4BAC7B,UAAU,EAAE;gCACV,IAAI,MAAA;gCACJ,KAAK,OAAA;gCACL,KAAK,EAAE,UAAU;gCACjB,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gCACzC,OAAO,EAAE,IAAI,GAAG,KAAK,GAAG,UAAU;gCAClC,OAAO,EAAE,IAAI,GAAG,CAAC;6BAClB;yBACF;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC;AAEH,uDAAuD;AAC1C,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACxE,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;;;;gCACjB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;gCAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;4BAA3B,IAAI,GAAG,SAAoB;4BACzB,YAAY,GAAK,IAAI,aAAT,CAAU;4BAE9B,IAAI,CAAC,YAAY,EAAE,CAAC;gCACZ,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAQ,CAAC;gCAC7D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAGuB,qBAAM,gBAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;oCACjE,KAAK,EAAE;wCACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;wCACvB,YAAY,cAAA;qCACb;iCACF,CAAC,EAAA;;4BALI,eAAe,GAAG,SAKtB;4BAEF,IAAI,eAAe,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;gCAC1B,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAQ,CAAC;gCACrD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE;wCACJ,OAAO,EAAE,IAAI;wCACb,OAAO,EAAE,+BAA+B;qCACzC;iCACF,CAAC,EAAC;;;iBACJ,CAAC,EAAC;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/career-paths/bookmarks/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface BookmarkedCareerPath {\n  id: string;\n  bookmarkedAt: Date;\n  careerPath: any;\n}\n\ninterface BookmarksResponse {\n  bookmarks: BookmarkedCareerPath[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\ninterface DeleteBookmarkResponse {\n  success: boolean;\n  message: string;\n}\n\n// GET /api/career-paths/bookmarks - Get user's bookmarked career paths\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const skip = (page - 1) * limit;\n\n    // Get user's bookmarked career paths\n    const bookmarks = await prisma.careerPathBookmark.findMany({\n      where: {\n        userId: session.user.id,\n      },\n      include: {\n        careerPath: {\n          include: {\n            learningResources: {\n              where: { isActive: true },\n              select: {\n                id: true,\n                title: true,\n                skillLevel: true,\n                cost: true\n              },\n              take: 3\n            },\n            relatedSkills: {\n              select: {\n                id: true,\n                name: true\n              }\n            }\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc',\n      },\n      skip,\n      take: limit,\n    });\n\n    // Get total count for pagination\n    const totalCount = await prisma.careerPathBookmark.count({\n      where: {\n        userId: session.user.id,\n      },\n    });\n\n    // Format the response\n    const formattedBookmarks = bookmarks.map(bookmark => ({\n      id: bookmark.id,\n      bookmarkedAt: bookmark.createdAt,\n      careerPath: {\n        ...bookmark.careerPath,\n        pros: JSON.parse(bookmark.careerPath.pros),\n        cons: JSON.parse(bookmark.careerPath.cons),\n        actionableSteps: Array.isArray(bookmark.careerPath.actionableSteps)\n          ? bookmark.careerPath.actionableSteps\n          : JSON.parse(String(bookmark.careerPath.actionableSteps) || '[]'),\n        isBookmarked: true\n      }\n    }));\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      bookmarks: formattedBookmarks,\n      pagination: {\n        page,\n        limit,\n        total: totalCount,\n        totalPages: Math.ceil(totalCount / limit),\n        hasNext: page * limit < totalCount,\n        hasPrev: page > 1\n      }\n    }\n  });\n});\n\n// DELETE /api/career-paths/bookmarks - Remove bookmark\nexport const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user?.id) {\n      const error = new Error('Authentication required') as any;\n      error.statusCode = 401;\n      throw error;\n    }\n\n    const body = await request.json();\n    const { careerPathId } = body;\n\n    if (!careerPathId) {\n      const error = new Error('Career path ID is required') as any;\n      error.statusCode = 400;\n      throw error;\n    }\n\n    // Remove bookmark\n    const deletedBookmark = await prisma.careerPathBookmark.deleteMany({\n      where: {\n        userId: session.user.id,\n        careerPathId,\n      },\n    });\n\n    if (deletedBookmark.count === 0) {\n      const error = new Error('Bookmark not found') as any;\n      error.statusCode = 404;\n      throw error;\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        success: true,\n        message: 'Bookmark removed successfully'\n      }\n    });\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2b7fc7cbcc65643370bc43e729fa2af61aa1d8c5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_mm2ldjm2e = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_mm2ldjm2e();
var __assign =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[0]++,
/* istanbul ignore next */
(cov_mm2ldjm2e().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_mm2ldjm2e().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_mm2ldjm2e().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_mm2ldjm2e().f[0]++;
  cov_mm2ldjm2e().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[1]++;
    cov_mm2ldjm2e().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_mm2ldjm2e().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_mm2ldjm2e().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_mm2ldjm2e().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[2][0]++;
          cov_mm2ldjm2e().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_mm2ldjm2e().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_mm2ldjm2e().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_mm2ldjm2e().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[11]++,
/* istanbul ignore next */
(cov_mm2ldjm2e().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_mm2ldjm2e().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_mm2ldjm2e().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_mm2ldjm2e().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[3]++;
    cov_mm2ldjm2e().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().f[4]++;
      cov_mm2ldjm2e().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_mm2ldjm2e().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().f[6]++;
      cov_mm2ldjm2e().s[15]++;
      try {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().f[7]++;
      cov_mm2ldjm2e().s[18]++;
      try {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().f[8]++;
      cov_mm2ldjm2e().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_mm2ldjm2e().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_mm2ldjm2e().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_mm2ldjm2e().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[23]++,
/* istanbul ignore next */
(cov_mm2ldjm2e().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_mm2ldjm2e().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_mm2ldjm2e().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_mm2ldjm2e().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_mm2ldjm2e().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_mm2ldjm2e().f[10]++;
        cov_mm2ldjm2e().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[9][0]++;
          cov_mm2ldjm2e().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_mm2ldjm2e().b[9][1]++;
        }
        cov_mm2ldjm2e().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_mm2ldjm2e().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_mm2ldjm2e().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[11]++;
    cov_mm2ldjm2e().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[12]++;
    cov_mm2ldjm2e().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().f[13]++;
      cov_mm2ldjm2e().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[14]++;
    cov_mm2ldjm2e().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().b[12][0]++;
      cov_mm2ldjm2e().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_mm2ldjm2e().b[12][1]++;
    }
    cov_mm2ldjm2e().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_mm2ldjm2e().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().s[36]++;
      try {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[18][0]++,
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[19][1]++,
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_mm2ldjm2e().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[15][0]++;
          cov_mm2ldjm2e().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_mm2ldjm2e().b[15][1]++;
        }
        cov_mm2ldjm2e().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[21][0]++;
          cov_mm2ldjm2e().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_mm2ldjm2e().b[21][1]++;
        }
        cov_mm2ldjm2e().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_mm2ldjm2e().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_mm2ldjm2e().b[22][1]++;
            cov_mm2ldjm2e().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_mm2ldjm2e().b[22][2]++;
            cov_mm2ldjm2e().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_mm2ldjm2e().b[22][3]++;
            cov_mm2ldjm2e().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_mm2ldjm2e().b[22][4]++;
            cov_mm2ldjm2e().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_mm2ldjm2e().b[22][5]++;
            cov_mm2ldjm2e().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_mm2ldjm2e().b[23][0]++;
              cov_mm2ldjm2e().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_mm2ldjm2e().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_mm2ldjm2e().b[23][1]++;
            }
            cov_mm2ldjm2e().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_mm2ldjm2e().b[26][0]++;
              cov_mm2ldjm2e().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_mm2ldjm2e().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_mm2ldjm2e().b[26][1]++;
            }
            cov_mm2ldjm2e().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_mm2ldjm2e().b[28][0]++;
              cov_mm2ldjm2e().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_mm2ldjm2e().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_mm2ldjm2e().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_mm2ldjm2e().b[28][1]++;
            }
            cov_mm2ldjm2e().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_mm2ldjm2e().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_mm2ldjm2e().b[30][0]++;
              cov_mm2ldjm2e().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_mm2ldjm2e().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_mm2ldjm2e().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_mm2ldjm2e().b[30][1]++;
            }
            cov_mm2ldjm2e().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_mm2ldjm2e().b[32][0]++;
              cov_mm2ldjm2e().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_mm2ldjm2e().b[32][1]++;
            }
            cov_mm2ldjm2e().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_mm2ldjm2e().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_mm2ldjm2e().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().b[33][0]++;
      cov_mm2ldjm2e().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_mm2ldjm2e().b[33][1]++;
    }
    cov_mm2ldjm2e().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_mm2ldjm2e().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_mm2ldjm2e().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[78]++,
/* istanbul ignore next */
(cov_mm2ldjm2e().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_mm2ldjm2e().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_mm2ldjm2e().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_mm2ldjm2e().f[15]++;
  cov_mm2ldjm2e().s[79]++;
  return /* istanbul ignore next */(cov_mm2ldjm2e().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_mm2ldjm2e().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_mm2ldjm2e().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_mm2ldjm2e().s[81]++;
exports.DELETE = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[82]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[83]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[84]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[85]++, __importDefault(require("@/lib/prisma")));
var csrf_1 =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[86]++, require("@/lib/csrf"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_mm2ldjm2e().s[87]++, require("@/lib/unified-api-error-handler"));
// GET /api/career-paths/bookmarks - Get user's bookmarked career paths
/* istanbul ignore next */
cov_mm2ldjm2e().s[88]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_mm2ldjm2e().f[16]++;
  cov_mm2ldjm2e().s[89]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[17]++;
    var session, error, searchParams, page, limit, skip, bookmarks, totalCount, formattedBookmarks;
    var _a;
    /* istanbul ignore next */
    cov_mm2ldjm2e().s[90]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().f[18]++;
      cov_mm2ldjm2e().s[91]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[38][0]++;
          cov_mm2ldjm2e().s[92]++;
          return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[38][1]++;
          cov_mm2ldjm2e().s[93]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[94]++;
          if (!(
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[41][0]++, (_a =
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[43][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[43][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[42][0]++, void 0) :
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[42][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[41][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[40][0]++, void 0) :
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[40][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_mm2ldjm2e().b[39][0]++;
            cov_mm2ldjm2e().s[95]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[96]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_mm2ldjm2e().s[97]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_mm2ldjm2e().b[39][1]++;
          }
          cov_mm2ldjm2e().s[98]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[99]++;
          page = parseInt(
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[44][0]++, searchParams.get('page')) ||
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[44][1]++, '1'));
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[100]++;
          limit = parseInt(
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[45][0]++, searchParams.get('limit')) ||
          /* istanbul ignore next */
          (cov_mm2ldjm2e().b[45][1]++, '10'));
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[101]++;
          skip = (page - 1) * limit;
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[102]++;
          return [4 /*yield*/, prisma_1.default.careerPathBookmark.findMany({
            where: {
              userId: session.user.id
            },
            include: {
              careerPath: {
                include: {
                  learningResources: {
                    where: {
                      isActive: true
                    },
                    select: {
                      id: true,
                      title: true,
                      skillLevel: true,
                      cost: true
                    },
                    take: 3
                  },
                  relatedSkills: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              }
            },
            orderBy: {
              createdAt: 'desc'
            },
            skip: skip,
            take: limit
          })];
        case 2:
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[38][2]++;
          cov_mm2ldjm2e().s[103]++;
          bookmarks = _b.sent();
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[104]++;
          return [4 /*yield*/, prisma_1.default.careerPathBookmark.count({
            where: {
              userId: session.user.id
            }
          })];
        case 3:
          /* istanbul ignore next */
          cov_mm2ldjm2e().b[38][3]++;
          cov_mm2ldjm2e().s[105]++;
          totalCount = _b.sent();
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[106]++;
          formattedBookmarks = bookmarks.map(function (bookmark) {
            /* istanbul ignore next */
            cov_mm2ldjm2e().f[19]++;
            cov_mm2ldjm2e().s[107]++;
            return {
              id: bookmark.id,
              bookmarkedAt: bookmark.createdAt,
              careerPath: __assign(__assign({}, bookmark.careerPath), {
                pros: JSON.parse(bookmark.careerPath.pros),
                cons: JSON.parse(bookmark.careerPath.cons),
                actionableSteps: Array.isArray(bookmark.careerPath.actionableSteps) ?
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[46][0]++, bookmark.careerPath.actionableSteps) :
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[46][1]++, JSON.parse(
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[47][0]++, String(bookmark.careerPath.actionableSteps)) ||
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[47][1]++, '[]'))),
                isBookmarked: true
              })
            };
          });
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[108]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              bookmarks: formattedBookmarks,
              pagination: {
                page: page,
                limit: limit,
                total: totalCount,
                totalPages: Math.ceil(totalCount / limit),
                hasNext: page * limit < totalCount,
                hasPrev: page > 1
              }
            }
          })];
      }
    });
  });
});
// DELETE /api/career-paths/bookmarks - Remove bookmark
/* istanbul ignore next */
cov_mm2ldjm2e().s[109]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_mm2ldjm2e().f[20]++;
  cov_mm2ldjm2e().s[110]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_mm2ldjm2e().f[21]++;
    cov_mm2ldjm2e().s[111]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_mm2ldjm2e().f[22]++;
      cov_mm2ldjm2e().s[112]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_mm2ldjm2e().f[23]++;
        cov_mm2ldjm2e().s[113]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_mm2ldjm2e().f[24]++;
          var session, error, body, careerPathId, error, deletedBookmark, error;
          var _a;
          /* istanbul ignore next */
          cov_mm2ldjm2e().s[114]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_mm2ldjm2e().f[25]++;
            cov_mm2ldjm2e().s[115]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_mm2ldjm2e().b[48][0]++;
                cov_mm2ldjm2e().s[116]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_mm2ldjm2e().b[48][1]++;
                cov_mm2ldjm2e().s[117]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_mm2ldjm2e().s[118]++;
                if (!(
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[51][0]++, (_a =
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[53][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[53][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[52][0]++, void 0) :
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[52][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[51][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[50][0]++, void 0) :
                /* istanbul ignore next */
                (cov_mm2ldjm2e().b[50][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().b[49][0]++;
                  cov_mm2ldjm2e().s[119]++;
                  error = new Error('Authentication required');
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().s[120]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().s[121]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_mm2ldjm2e().b[49][1]++;
                }
                cov_mm2ldjm2e().s[122]++;
                return [4 /*yield*/, request.json()];
              case 2:
                /* istanbul ignore next */
                cov_mm2ldjm2e().b[48][2]++;
                cov_mm2ldjm2e().s[123]++;
                body = _b.sent();
                /* istanbul ignore next */
                cov_mm2ldjm2e().s[124]++;
                careerPathId = body.careerPathId;
                /* istanbul ignore next */
                cov_mm2ldjm2e().s[125]++;
                if (!careerPathId) {
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().b[54][0]++;
                  cov_mm2ldjm2e().s[126]++;
                  error = new Error('Career path ID is required');
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().s[127]++;
                  error.statusCode = 400;
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().s[128]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_mm2ldjm2e().b[54][1]++;
                }
                cov_mm2ldjm2e().s[129]++;
                return [4 /*yield*/, prisma_1.default.careerPathBookmark.deleteMany({
                  where: {
                    userId: session.user.id,
                    careerPathId: careerPathId
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_mm2ldjm2e().b[48][3]++;
                cov_mm2ldjm2e().s[130]++;
                deletedBookmark = _b.sent();
                /* istanbul ignore next */
                cov_mm2ldjm2e().s[131]++;
                if (deletedBookmark.count === 0) {
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().b[55][0]++;
                  cov_mm2ldjm2e().s[132]++;
                  error = new Error('Bookmark not found');
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().s[133]++;
                  error.statusCode = 404;
                  /* istanbul ignore next */
                  cov_mm2ldjm2e().s[134]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_mm2ldjm2e().b[55][1]++;
                }
                cov_mm2ldjm2e().s[135]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: {
                    success: true,
                    message: 'Bookmark removed successfully'
                  }
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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