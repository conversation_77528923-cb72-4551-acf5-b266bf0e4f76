e54c25f6807da66b45c24087c7415cee
"use strict";

/* istanbul ignore next */
function cov_x86nk9mhy() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/progress/analytics/route.ts";
  var hash = "1241b8d620cd236d2e41f7dc938e6d2b4a52ba73";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/progress/analytics/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 39
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 13
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "73": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "74": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "75": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 76
        }
      },
      "76": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "77": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 294,
          column: 7
        }
      },
      "78": {
        start: {
          line: 51,
          column: 93
        },
        end: {
          line: 294,
          column: 3
        }
      },
      "79": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 293,
          column: 7
        }
      },
      "80": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 292,
          column: 9
        }
      },
      "81": {
        start: {
          line: 56,
          column: 20
        },
        end: {
          line: 56,
          column: 91
        }
      },
      "82": {
        start: {
          line: 58,
          column: 16
        },
        end: {
          line: 58,
          column: 36
        }
      },
      "83": {
        start: {
          line: 59,
          column: 16
        },
        end: {
          line: 63,
          column: 17
        }
      },
      "84": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 65
        }
      },
      "85": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 61,
          column: 43
        }
      },
      "86": {
        start: {
          line: 62,
          column: 20
        },
        end: {
          line: 62,
          column: 32
        }
      },
      "87": {
        start: {
          line: 64,
          column: 16
        },
        end: {
          line: 64,
          column: 65
        }
      },
      "88": {
        start: {
          line: 65,
          column: 16
        },
        end: {
          line: 65,
          column: 63
        }
      },
      "89": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 33
        }
      },
      "90": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 82,
          column: 17
        }
      },
      "91": {
        start: {
          line: 69,
          column: 24
        },
        end: {
          line: 69,
          column: 99
        }
      },
      "92": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 70,
          column: 30
        }
      },
      "93": {
        start: {
          line: 72,
          column: 24
        },
        end: {
          line: 72,
          column: 99
        }
      },
      "94": {
        start: {
          line: 73,
          column: 24
        },
        end: {
          line: 73,
          column: 30
        }
      },
      "95": {
        start: {
          line: 75,
          column: 24
        },
        end: {
          line: 75,
          column: 99
        }
      },
      "96": {
        start: {
          line: 76,
          column: 24
        },
        end: {
          line: 76,
          column: 30
        }
      },
      "97": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 99
        }
      },
      "98": {
        start: {
          line: 79,
          column: 24
        },
        end: {
          line: 79,
          column: 30
        }
      },
      "99": {
        start: {
          line: 81,
          column: 24
        },
        end: {
          line: 81,
          column: 48
        }
      },
      "100": {
        start: {
          line: 83,
          column: 16
        },
        end: {
          line: 91,
          column: 24
        }
      },
      "101": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 93,
          column: 34
        }
      },
      "102": {
        start: {
          line: 94,
          column: 16
        },
        end: {
          line: 111,
          column: 24
        }
      },
      "103": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 45
        }
      },
      "104": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 122,
          column: 24
        }
      },
      "105": {
        start: {
          line: 124,
          column: 16
        },
        end: {
          line: 124,
          column: 45
        }
      },
      "106": {
        start: {
          line: 125,
          column: 16
        },
        end: {
          line: 125,
          column: 42
        }
      },
      "107": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 126,
          column: 110
        }
      },
      "108": {
        start: {
          line: 126,
          column: 64
        },
        end: {
          line: 126,
          column: 99
        }
      },
      "109": {
        start: {
          line: 127,
          column: 16
        },
        end: {
          line: 127,
          column: 104
        }
      },
      "110": {
        start: {
          line: 127,
          column: 61
        },
        end: {
          line: 127,
          column: 93
        }
      },
      "111": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 128,
          column: 102
        }
      },
      "112": {
        start: {
          line: 129,
          column: 16
        },
        end: {
          line: 131,
          column: 19
        }
      },
      "113": {
        start: {
          line: 130,
          column: 20
        },
        end: {
          line: 130,
          column: 55
        }
      },
      "114": {
        start: {
          line: 132,
          column: 16
        },
        end: {
          line: 137,
          column: 24
        }
      },
      "115": {
        start: {
          line: 134,
          column: 39
        },
        end: {
          line: 134,
          column: 90
        }
      },
      "116": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 135,
          column: 72
        }
      },
      "117": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 148,
          column: 23
        }
      },
      "118": {
        start: {
          line: 139,
          column: 35
        },
        end: {
          line: 139,
          column: 48
        }
      },
      "119": {
        start: {
          line: 140,
          column: 20
        },
        end: {
          line: 142,
          column: 21
        }
      },
      "120": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 141,
          column: 67
        }
      },
      "121": {
        start: {
          line: 143,
          column: 20
        },
        end: {
          line: 143,
          column: 42
        }
      },
      "122": {
        start: {
          line: 144,
          column: 20
        },
        end: {
          line: 146,
          column: 21
        }
      },
      "123": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 50
        }
      },
      "124": {
        start: {
          line: 147,
          column: 20
        },
        end: {
          line: 147,
          column: 31
        }
      },
      "125": {
        start: {
          line: 149,
          column: 16
        },
        end: {
          line: 157,
          column: 19
        }
      },
      "126": {
        start: {
          line: 150,
          column: 35
        },
        end: {
          line: 150,
          column: 40
        }
      },
      "127": {
        start: {
          line: 150,
          column: 49
        },
        end: {
          line: 150,
          column: 54
        }
      },
      "128": {
        start: {
          line: 151,
          column: 20
        },
        end: {
          line: 156,
          column: 23
        }
      },
      "129": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 158,
          column: 37
        }
      },
      "130": {
        start: {
          line: 159,
          column: 16
        },
        end: {
          line: 177,
          column: 18
        }
      },
      "131": {
        start: {
          line: 160,
          column: 37
        },
        end: {
          line: 160,
          column: 87
        }
      },
      "132": {
        start: {
          line: 161,
          column: 35
        },
        end: {
          line: 161,
          column: 89
        }
      },
      "133": {
        start: {
          line: 162,
          column: 44
        },
        end: {
          line: 164,
          column: 29
        }
      },
      "134": {
        start: {
          line: 163,
          column: 24
        },
        end: {
          line: 163,
          column: 90
        }
      },
      "135": {
        start: {
          line: 165,
          column: 46
        },
        end: {
          line: 167,
          column: 29
        }
      },
      "136": {
        start: {
          line: 166,
          column: 24
        },
        end: {
          line: 166,
          column: 121
        }
      },
      "137": {
        start: {
          line: 168,
          column: 45
        },
        end: {
          line: 170,
          column: 33
        }
      },
      "138": {
        start: {
          line: 169,
          column: 24
        },
        end: {
          line: 169,
          column: 98
        }
      },
      "139": {
        start: {
          line: 171,
          column: 20
        },
        end: {
          line: 176,
          column: 23
        }
      },
      "140": {
        start: {
          line: 178,
          column: 16
        },
        end: {
          line: 180,
          column: 17
        }
      },
      "141": {
        start: {
          line: 179,
          column: 20
        },
        end: {
          line: 179,
          column: 31
        }
      },
      "142": {
        start: {
          line: 181,
          column: 16
        },
        end: {
          line: 183,
          column: 101
        }
      },
      "143": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 182,
          column: 61
        }
      },
      "144": {
        start: {
          line: 183,
          column: 44
        },
        end: {
          line: 183,
          column: 97
        }
      },
      "145": {
        start: {
          line: 184,
          column: 16
        },
        end: {
          line: 184,
          column: 34
        }
      },
      "146": {
        start: {
          line: 185,
          column: 16
        },
        end: {
          line: 185,
          column: 34
        }
      },
      "147": {
        start: {
          line: 186,
          column: 16
        },
        end: {
          line: 186,
          column: 31
        }
      },
      "148": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 187,
          column: 50
        }
      },
      "149": {
        start: {
          line: 188,
          column: 16
        },
        end: {
          line: 188,
          column: 86
        }
      },
      "150": {
        start: {
          line: 189,
          column: 16
        },
        end: {
          line: 195,
          column: 17
        }
      },
      "151": {
        start: {
          line: 190,
          column: 20
        },
        end: {
          line: 190,
          column: 43
        }
      },
      "152": {
        start: {
          line: 191,
          column: 20
        },
        end: {
          line: 194,
          column: 21
        }
      },
      "153": {
        start: {
          line: 192,
          column: 24
        },
        end: {
          line: 192,
          column: 40
        }
      },
      "154": {
        start: {
          line: 193,
          column: 24
        },
        end: {
          line: 193,
          column: 88
        }
      },
      "155": {
        start: {
          line: 197,
          column: 16
        },
        end: {
          line: 213,
          column: 17
        }
      },
      "156": {
        start: {
          line: 198,
          column: 20
        },
        end: {
          line: 212,
          column: 21
        }
      },
      "157": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 39
        }
      },
      "158": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 202,
          column: 63
        }
      },
      "159": {
        start: {
          line: 203,
          column: 24
        },
        end: {
          line: 203,
          column: 68
        }
      },
      "160": {
        start: {
          line: 204,
          column: 24
        },
        end: {
          line: 204,
          column: 107
        }
      },
      "161": {
        start: {
          line: 205,
          column: 24
        },
        end: {
          line: 211,
          column: 25
        }
      },
      "162": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 206,
          column: 41
        }
      },
      "163": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 209,
          column: 80
        }
      },
      "164": {
        start: {
          line: 210,
          column: 28
        },
        end: {
          line: 210,
          column: 43
        }
      },
      "165": {
        start: {
          line: 214,
          column: 16
        },
        end: {
          line: 214,
          column: 68
        }
      },
      "166": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 215,
          column: 30
        }
      },
      "167": {
        start: {
          line: 216,
          column: 16
        },
        end: {
          line: 230,
          column: 17
        }
      },
      "168": {
        start: {
          line: 217,
          column: 20
        },
        end: {
          line: 221,
          column: 23
        }
      },
      "169": {
        start: {
          line: 223,
          column: 21
        },
        end: {
          line: 230,
          column: 17
        }
      },
      "170": {
        start: {
          line: 224,
          column: 20
        },
        end: {
          line: 229,
          column: 23
        }
      },
      "171": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 245,
          column: 17
        }
      },
      "172": {
        start: {
          line: 232,
          column: 20
        },
        end: {
          line: 236,
          column: 23
        }
      },
      "173": {
        start: {
          line: 238,
          column: 21
        },
        end: {
          line: 245,
          column: 17
        }
      },
      "174": {
        start: {
          line: 239,
          column: 20
        },
        end: {
          line: 244,
          column: 23
        }
      },
      "175": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 261,
          column: 17
        }
      },
      "176": {
        start: {
          line: 247,
          column: 20
        },
        end: {
          line: 252,
          column: 23
        }
      },
      "177": {
        start: {
          line: 254,
          column: 21
        },
        end: {
          line: 261,
          column: 17
        }
      },
      "178": {
        start: {
          line: 255,
          column: 20
        },
        end: {
          line: 260,
          column: 23
        }
      },
      "179": {
        start: {
          line: 262,
          column: 16
        },
        end: {
          line: 287,
          column: 18
        }
      },
      "180": {
        start: {
          line: 279,
          column: 81
        },
        end: {
          line: 284,
          column: 27
        }
      },
      "181": {
        start: {
          line: 288,
          column: 16
        },
        end: {
          line: 291,
          column: 24
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 51,
            column: 72
          },
          end: {
            line: 51,
            column: 73
          }
        },
        loc: {
          start: {
            line: 51,
            column: 91
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 51
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 51,
            column: 135
          },
          end: {
            line: 51,
            column: 136
          }
        },
        loc: {
          start: {
            line: 51,
            column: 147
          },
          end: {
            line: 294,
            column: 1
          }
        },
        line: 51
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 54,
            column: 29
          },
          end: {
            line: 54,
            column: 30
          }
        },
        loc: {
          start: {
            line: 54,
            column: 43
          },
          end: {
            line: 293,
            column: 5
          }
        },
        line: 54
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 126,
            column: 46
          },
          end: {
            line: 126,
            column: 47
          }
        },
        loc: {
          start: {
            line: 126,
            column: 62
          },
          end: {
            line: 126,
            column: 101
          }
        },
        line: 126
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 127,
            column: 43
          },
          end: {
            line: 127,
            column: 44
          }
        },
        loc: {
          start: {
            line: 127,
            column: 59
          },
          end: {
            line: 127,
            column: 95
          }
        },
        line: 127
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 129,
            column: 54
          },
          end: {
            line: 129,
            column: 55
          }
        },
        loc: {
          start: {
            line: 129,
            column: 70
          },
          end: {
            line: 131,
            column: 17
          }
        },
        line: 129
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 133,
            column: 63
          },
          end: {
            line: 133,
            column: 64
          }
        },
        loc: {
          start: {
            line: 133,
            column: 84
          },
          end: {
            line: 136,
            column: 21
          }
        },
        line: 133
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 138,
            column: 49
          },
          end: {
            line: 138,
            column: 50
          }
        },
        loc: {
          start: {
            line: 138,
            column: 70
          },
          end: {
            line: 148,
            column: 17
          }
        },
        line: 138
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 149,
            column: 79
          },
          end: {
            line: 149,
            column: 80
          }
        },
        loc: {
          start: {
            line: 149,
            column: 93
          },
          end: {
            line: 157,
            column: 17
          }
        },
        line: 149
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 159,
            column: 26
          },
          end: {
            line: 159,
            column: 27
          }
        },
        loc: {
          start: {
            line: 159,
            column: 39
          },
          end: {
            line: 177,
            column: 17
          }
        },
        line: 159
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 162,
            column: 57
          },
          end: {
            line: 162,
            column: 58
          }
        },
        loc: {
          start: {
            line: 162,
            column: 73
          },
          end: {
            line: 164,
            column: 21
          }
        },
        line: 162
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 165,
            column: 59
          },
          end: {
            line: 165,
            column: 60
          }
        },
        loc: {
          start: {
            line: 165,
            column: 75
          },
          end: {
            line: 167,
            column: 21
          }
        },
        line: 165
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 168,
            column: 69
          },
          end: {
            line: 168,
            column: 70
          }
        },
        loc: {
          start: {
            line: 168,
            column: 89
          },
          end: {
            line: 170,
            column: 21
          }
        },
        line: 168
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 181,
            column: 70
          },
          end: {
            line: 181,
            column: 71
          }
        },
        loc: {
          start: {
            line: 181,
            column: 90
          },
          end: {
            line: 183,
            column: 17
          }
        },
        line: 181
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 183,
            column: 26
          },
          end: {
            line: 183,
            column: 27
          }
        },
        loc: {
          start: {
            line: 183,
            column: 42
          },
          end: {
            line: 183,
            column: 99
          }
        },
        line: 183
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 279,
            column: 65
          },
          end: {
            line: 279,
            column: 66
          }
        },
        loc: {
          start: {
            line: 279,
            column: 79
          },
          end: {
            line: 284,
            column: 29
          }
        },
        line: 279
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 292,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 91
          }
        }, {
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 91,
            column: 24
          }
        }, {
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 111,
            column: 24
          }
        }, {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 122,
            column: 24
          }
        }, {
          start: {
            line: 123,
            column: 12
          },
          end: {
            line: 291,
            column: 24
          }
        }],
        line: 55
      },
      "36": {
        loc: {
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 63,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 63,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "37": {
        loc: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 120
          },
          end: {
            line: 59,
            column: 126
          }
        }, {
          start: {
            line: 59,
            column: 129
          },
          end: {
            line: 59,
            column: 134
          }
        }],
        line: 59
      },
      "38": {
        loc: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 100
          }
        }, {
          start: {
            line: 59,
            column: 104
          },
          end: {
            line: 59,
            column: 117
          }
        }],
        line: 59
      },
      "39": {
        loc: {
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 69
          },
          end: {
            line: 59,
            column: 75
          }
        }, {
          start: {
            line: 59,
            column: 78
          },
          end: {
            line: 59,
            column: 90
          }
        }],
        line: 59
      },
      "40": {
        loc: {
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 44
          }
        }, {
          start: {
            line: 59,
            column: 48
          },
          end: {
            line: 59,
            column: 66
          }
        }],
        line: 59
      },
      "41": {
        loc: {
          start: {
            line: 65,
            column: 24
          },
          end: {
            line: 65,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 65,
            column: 24
          },
          end: {
            line: 65,
            column: 49
          }
        }, {
          start: {
            line: 65,
            column: 53
          },
          end: {
            line: 65,
            column: 62
          }
        }],
        line: 65
      },
      "42": {
        loc: {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 82,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 68,
            column: 20
          },
          end: {
            line: 70,
            column: 30
          }
        }, {
          start: {
            line: 71,
            column: 20
          },
          end: {
            line: 73,
            column: 30
          }
        }, {
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 76,
            column: 30
          }
        }, {
          start: {
            line: 77,
            column: 20
          },
          end: {
            line: 79,
            column: 30
          }
        }, {
          start: {
            line: 80,
            column: 20
          },
          end: {
            line: 81,
            column: 48
          }
        }],
        line: 67
      },
      "43": {
        loc: {
          start: {
            line: 128,
            column: 33
          },
          end: {
            line: 128,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 128,
            column: 50
          },
          end: {
            line: 128,
            column: 97
          }
        }, {
          start: {
            line: 128,
            column: 100
          },
          end: {
            line: 128,
            column: 101
          }
        }],
        line: 128
      },
      "44": {
        loc: {
          start: {
            line: 132,
            column: 40
          },
          end: {
            line: 137,
            column: 23
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 133,
            column: 22
          },
          end: {
            line: 136,
            column: 58
          }
        }, {
          start: {
            line: 137,
            column: 22
          },
          end: {
            line: 137,
            column: 23
          }
        }],
        line: 132
      },
      "45": {
        loc: {
          start: {
            line: 140,
            column: 20
          },
          end: {
            line: 142,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 20
          },
          end: {
            line: 142,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "46": {
        loc: {
          start: {
            line: 144,
            column: 20
          },
          end: {
            line: 146,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 144,
            column: 20
          },
          end: {
            line: 146,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 144
      },
      "47": {
        loc: {
          start: {
            line: 155,
            column: 36
          },
          end: {
            line: 155,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 155,
            column: 53
          },
          end: {
            line: 155,
            column: 100
          }
        }, {
          start: {
            line: 155,
            column: 103
          },
          end: {
            line: 155,
            column: 104
          }
        }],
        line: 155
      },
      "48": {
        loc: {
          start: {
            line: 163,
            column: 31
          },
          end: {
            line: 163,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 163,
            column: 31
          },
          end: {
            line: 163,
            column: 59
          }
        }, {
          start: {
            line: 163,
            column: 63
          },
          end: {
            line: 163,
            column: 89
          }
        }],
        line: 163
      },
      "49": {
        loc: {
          start: {
            line: 166,
            column: 31
          },
          end: {
            line: 166,
            column: 120
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 31
          },
          end: {
            line: 166,
            column: 58
          }
        }, {
          start: {
            line: 166,
            column: 62
          },
          end: {
            line: 166,
            column: 90
          }
        }, {
          start: {
            line: 166,
            column: 94
          },
          end: {
            line: 166,
            column: 120
          }
        }],
        line: 166
      },
      "50": {
        loc: {
          start: {
            line: 169,
            column: 31
          },
          end: {
            line: 169,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 31
          },
          end: {
            line: 169,
            column: 63
          }
        }, {
          start: {
            line: 169,
            column: 67
          },
          end: {
            line: 169,
            column: 97
          }
        }],
        line: 169
      },
      "51": {
        loc: {
          start: {
            line: 189,
            column: 16
          },
          end: {
            line: 195,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 16
          },
          end: {
            line: 195,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "52": {
        loc: {
          start: {
            line: 189,
            column: 20
          },
          end: {
            line: 189,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 189,
            column: 20
          },
          end: {
            line: 189,
            column: 47
          }
        }, {
          start: {
            line: 189,
            column: 51
          },
          end: {
            line: 189,
            column: 82
          }
        }],
        line: 189
      },
      "53": {
        loc: {
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 212,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 212,
            column: 21
          }
        }, {
          start: {
            line: 201,
            column: 25
          },
          end: {
            line: 212,
            column: 21
          }
        }],
        line: 198
      },
      "54": {
        loc: {
          start: {
            line: 205,
            column: 24
          },
          end: {
            line: 211,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 24
          },
          end: {
            line: 211,
            column: 25
          }
        }, {
          start: {
            line: 208,
            column: 29
          },
          end: {
            line: 211,
            column: 25
          }
        }],
        line: 205
      },
      "55": {
        loc: {
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 230,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 230,
            column: 17
          }
        }, {
          start: {
            line: 223,
            column: 21
          },
          end: {
            line: 230,
            column: 17
          }
        }],
        line: 216
      },
      "56": {
        loc: {
          start: {
            line: 223,
            column: 21
          },
          end: {
            line: 230,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 21
          },
          end: {
            line: 230,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "57": {
        loc: {
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 245,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 245,
            column: 17
          }
        }, {
          start: {
            line: 238,
            column: 21
          },
          end: {
            line: 245,
            column: 17
          }
        }],
        line: 231
      },
      "58": {
        loc: {
          start: {
            line: 238,
            column: 21
          },
          end: {
            line: 245,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 21
          },
          end: {
            line: 245,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      },
      "59": {
        loc: {
          start: {
            line: 246,
            column: 16
          },
          end: {
            line: 261,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 16
          },
          end: {
            line: 261,
            column: 17
          }
        }, {
          start: {
            line: 254,
            column: 21
          },
          end: {
            line: 261,
            column: 17
          }
        }],
        line: 246
      },
      "60": {
        loc: {
          start: {
            line: 254,
            column: 21
          },
          end: {
            line: 261,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 21
          },
          end: {
            line: 261,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/progress/analytics/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAClC,6EAAwF;AAExF,yCAAyC;AAC5B,QAAA,OAAO,GAAG,eAAe,CAAC;AA4CvC,4DAA4D;AAC/C,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;;oBAC/D,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEO,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC;gBAG/C,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;gBAGvB,QAAQ,KAAK,EAAE,CAAC;oBACd,KAAK,QAAQ;wBACX,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC3E,MAAM;oBACR,KAAK,SAAS;wBACZ,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC3E,MAAM;oBACR,KAAK,SAAS;wBACZ,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC3E,MAAM;oBACR,KAAK,OAAO;wBACV,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC3E,MAAM;oBACR;wBACE,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;gBACxC,CAAC;gBAGe,qBAAM,gBAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;wBAC3C,KAAK,EAAE;4BACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;4BACvB,SAAS,EAAE;gCACT,GAAG,EAAE,SAAS;6BACf;yBACF;wBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;qBAC/B,CAAC,EAAA;;gBARI,KAAK,GAAG,SAQZ;gBAGuB,qBAAM,gBAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;wBAC7D,KAAK,EAAE;4BACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;4BACvB,UAAU,EAAE;gCACV,GAAG,EAAE,SAAS;6BACf;yBACF;wBACD,OAAO,EAAE;4BACP,WAAW,EAAE;gCACX,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,KAAK,EAAE,IAAI;oCACX,MAAM,EAAE,IAAI;iCACb;6BACF;yBACF;wBACD,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;qBAChC,CAAC,EAAA;;gBAjBI,gBAAgB,GAAG,SAiBvB;gBAGuB,qBAAM,gBAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;wBAClE,KAAK,EAAE;4BACL,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;4BACvB,SAAS,EAAE;gCACT,GAAG,EAAE,SAAS;6BACf;yBACF;wBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;qBAC/B,CAAC,EAAA;;gBARI,gBAAgB,GAAG,SAQvB;gBAGI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC1B,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,WAAW,EAA3B,CAA2B,CAAC,CAAC,MAAM,CAAC;gBAC1E,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAxB,CAAwB,CAAC,CAAC,MAAM,CAAC;gBACpE,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAGtF,sBAAsB,GAAG,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI;oBAC9C,OAAA,IAAI,CAAC,MAAM,KAAK,WAAW;gBAA3B,CAA2B,CAC5B,CAAC;gBACI,qBAAqB,GAAG,sBAAsB,CAAC,MAAM,GAAG,CAAC;oBAC7D,CAAC,CAAC,IAAI,CAAC,KAAK,CACR,sBAAsB,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI;wBACtC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;wBACrE,OAAO,GAAG,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,kBAAkB;oBACrE,CAAC,EAAE,CAAC,CAAC,GAAG,sBAAsB,CAAC,MAAM,CACtC;oBACH,CAAC,CAAC,CAAC,CAAC;gBAGA,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI;oBAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACnB,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;oBAC7C,CAAC;oBACD,GAAG,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;oBACtB,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;wBAChC,GAAG,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC;oBAC5B,CAAC;oBACD,OAAO,GAAG,CAAC;gBACb,CAAC,EAAE,EAA0D,CAAC,CAAC;gBAEzD,sBAAsB,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,UAAC,EAAgB;wBAAf,QAAQ,QAAA,EAAE,IAAI,QAAA;oBAAM,OAAA,CAAC;wBAC1F,QAAQ,UAAA;wBACR,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,UAAU,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;qBACjF,CAAC;gBALyF,CAKzF,CAAC,CAAC;gBAGE,eAAe,GAAG,EAAE,CAAC;oCAClB,CAAC;oBACR,IAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;oBACtE,IAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;oBAExE,IAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI;wBACzC,OAAA,IAAI,CAAC,SAAS,IAAI,UAAU,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ;oBAA1D,CAA0D,CAC3D,CAAC,MAAM,CAAC;oBAET,IAAM,mBAAmB,GAAG,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI;wBAC3C,OAAA,IAAI,CAAC,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,SAAS,IAAI,UAAU,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ;oBAAzF,CAAyF,CAC1F,CAAC,MAAM,CAAC;oBAET,IAAM,kBAAkB,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAA,QAAQ;wBACzD,OAAA,QAAQ,CAAC,SAAS,IAAI,UAAU,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ;oBAAlE,CAAkE,CACnE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,wCAAwC;oBAEtD,eAAe,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,UAAU,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;wBAClF,YAAY,EAAE,iBAAiB;wBAC/B,cAAc,EAAE,mBAAmB;wBACnC,aAAa,EAAE,kBAAkB;qBAClC,CAAC,CAAC;;gBArBL,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;4BAAlB,CAAC;iBAsBT;gBAGK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CACpC,gBAAgB,CAAC,GAAG,CAAC,UAAA,QAAQ;oBAC3B,OAAA,QAAQ,CAAC,SAAS,CAAC,YAAY,EAAE;gBAAjC,CAAiC,CAClC,CACF,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAA7C,CAA6C,CAAC,CAAC;gBAE7D,aAAa,GAAG,CAAC,CAAC;gBAClB,aAAa,GAAG,CAAC,CAAC;gBAClB,UAAU,GAAG,CAAC,CAAC;gBAGb,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC;gBAClC,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;gBAE5E,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC/D,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC3B,OAAO,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;wBACtD,aAAa,EAAE,CAAC;wBAChB,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;oBAClE,CAAC;gBACH,CAAC;gBAED,2BAA2B;gBAC3B,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC5C,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBACZ,UAAU,GAAG,CAAC,CAAC;oBACjB,CAAC;yBAAM,CAAC;wBACA,WAAW,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvC,YAAY,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBAC5C,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;wBAEzF,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;4BAClB,UAAU,EAAE,CAAC;wBACf,CAAC;6BAAM,CAAC;4BACN,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;4BACpD,UAAU,GAAG,CAAC,CAAC;wBACjB,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;gBAG9C,QAAQ,GAAG,EAAE,CAAC;gBAEpB,IAAI,cAAc,IAAI,EAAE,EAAE,CAAC;oBACzB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,6BAA6B;wBACpC,WAAW,EAAE,qBAAc,cAAc,oDAAiD;qBAC3F,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,cAAc,GAAG,EAAE,EAAE,CAAC;oBAC/B,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,iCAAiC;wBACxC,WAAW,EAAE,uCAAgC,cAAc,0FAAuF;wBAClJ,MAAM,EAAE,6DAA6D;qBACtE,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;oBACvB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,0BAA0B;wBACjC,WAAW,EAAE,gDAAyC,aAAa,0CAAuC;qBAC3G,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;oBAC/B,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,2BAA2B;wBAClC,WAAW,EAAE,kGAAkG;wBAC/G,MAAM,EAAE,yDAAyD;qBAClE,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;oBACtB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,iBAAiB;wBACxB,WAAW,EAAE,kGAAkG;wBAC/G,MAAM,EAAE,mDAAmD;qBAC5D,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;oBAC3B,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,mBAAmB;wBAC1B,WAAW,EAAE,mBAAY,WAAW,wEAAqE;wBACzG,MAAM,EAAE,0CAA0C;qBACnD,CAAC,CAAC;gBACL,CAAC;gBAEK,aAAa,GAAG;oBACpB,SAAS,EAAE;wBACT,KAAK,EAAE,UAAU;wBACjB,SAAS,EAAE,cAAc;wBACzB,MAAM,EAAE,WAAW;wBACnB,cAAc,gBAAA;wBACd,qBAAqB,uBAAA;qBACtB;oBACD,iBAAiB,EAAE,sBAAsB;oBACzC,eAAe,iBAAA;oBACf,UAAU,EAAE;wBACV,aAAa,eAAA;wBACb,aAAa,eAAA;wBACb,eAAe,EAAE,WAAW,CAAC,MAAM;qBACpC;oBACD,YAAY,EAAE;wBACZ,KAAK,EAAE,gBAAgB,CAAC,MAAM;wBAC9B,MAAM,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC;4BAC9C,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE;4BACrB,KAAK,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK;4BAC3B,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE;4BACvC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM;yBAC9B,CAAC,EAL6C,CAK7C,CAAC;qBACJ;oBACD,QAAQ,UAAA;iBACT,CAAC;gBAEJ,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAa;wBACtB,IAAI,EAAE,aAAa;qBACpB,CAAC,EAAC;;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/progress/analytics/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\n// Force dynamic rendering for this route\nexport const dynamic = 'force-dynamic';\n\ninterface ProgressAnalyticsResponse {\n  goalStats: {\n    total: number;\n    completed: number;\n    active: number;\n    completionRate: number;\n    averageTimeToComplete: number;\n  };\n  categoryBreakdown: Array<{\n    category: string;\n    count: number;\n    completed: number;\n    percentage: number;\n  }>;\n  monthlyProgress: Array<{\n    month: string;\n    goalsCreated: number;\n    goalsCompleted: number;\n    learningHours: number;\n  }>;\n  streakData: {\n    currentStreak: number;\n    longestStreak: number;\n    totalActiveDays: number;\n  };\n  achievements: {\n    total: number;\n    recent: Array<{\n      id: string;\n      title: string;\n      unlockedAt: string;\n      points: number;\n    }>;\n  };\n  insights: Array<{\n    type: string;\n    title: string;\n    description: string;\n    action?: string;\n  }>;\n}\n\n// GET /api/progress/analytics - Get user progress analytics\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ProgressAnalyticsResponse>>> => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const { searchParams } = new URL(request.url);\n  const range = searchParams.get('range') || '6months';\n\n  // Calculate date range\n  const now = new Date();\n  let startDate: Date;\n\n  switch (range) {\n    case '1month':\n      startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());\n      break;\n    case '3months':\n      startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());\n      break;\n    case '6months':\n      startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());\n      break;\n    case '1year':\n      startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());\n      break;\n    default:\n      startDate = new Date(0); // All time\n  }\n\n    // Fetch user goals\n    const goals = await prisma.userGoal.findMany({\n      where: {\n        userId: session.user.id,\n        createdAt: {\n          gte: startDate,\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n    });\n\n    // Fetch user achievements\n    const userAchievements = await prisma.userAchievement.findMany({\n      where: {\n        userId: session.user.id,\n        unlockedAt: {\n          gte: startDate,\n        },\n      },\n      include: {\n        achievement: {\n          select: {\n            id: true,\n            title: true,\n            points: true,\n          },\n        },\n      },\n      orderBy: { unlockedAt: 'desc' },\n    });\n\n    // Fetch learning progress for streak calculation\n    const learningProgress = await prisma.userLearningProgress.findMany({\n      where: {\n        userId: session.user.id,\n        updatedAt: {\n          gte: startDate,\n        },\n      },\n      orderBy: { updatedAt: 'desc' },\n    });\n\n    // Calculate goal statistics\n    const totalGoals = goals.length;\n    const completedGoals = goals.filter(goal => goal.status === 'COMPLETED').length;\n    const activeGoals = goals.filter(goal => goal.status === 'ACTIVE').length;\n    const completionRate = totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0;\n\n    // Calculate average time to complete (using updatedAt as completion time)\n    const completedGoalsWithTime = goals.filter(goal =>\n      goal.status === 'COMPLETED'\n    );\n    const averageTimeToComplete = completedGoalsWithTime.length > 0\n      ? Math.round(\n          completedGoalsWithTime.reduce((sum, goal) => {\n            const timeDiff = goal.updatedAt.getTime() - goal.createdAt.getTime();\n            return sum + (timeDiff / (1000 * 60 * 60 * 24)); // Convert to days\n          }, 0) / completedGoalsWithTime.length\n        )\n      : 0;\n\n    // Calculate category breakdown\n    const categoryBreakdown = goals.reduce((acc, goal) => {\n      const category = goal.category;\n      if (!acc[category]) {\n        acc[category] = { count: 0, completed: 0 };\n      }\n      acc[category].count++;\n      if (goal.status === 'COMPLETED') {\n        acc[category].completed++;\n      }\n      return acc;\n    }, {} as Record<string, { count: number; completed: number }>);\n\n    const categoryBreakdownArray = Object.entries(categoryBreakdown).map(([category, data]) => ({\n      category,\n      count: data.count,\n      completed: data.completed,\n      percentage: data.count > 0 ? Math.round((data.completed / data.count) * 100) : 0,\n    }));\n\n    // Calculate monthly progress\n    const monthlyProgress = [];\n    for (let i = 5; i >= 0; i--) {\n      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);\n      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);\n      \n      const monthGoalsCreated = goals.filter(goal => \n        goal.createdAt >= monthStart && goal.createdAt <= monthEnd\n      ).length;\n      \n      const monthGoalsCompleted = goals.filter(goal =>\n        goal.status === 'COMPLETED' && goal.updatedAt >= monthStart && goal.updatedAt <= monthEnd\n      ).length;\n\n      const monthLearningHours = learningProgress.filter(progress => \n        progress.updatedAt >= monthStart && progress.updatedAt <= monthEnd\n      ).length * 2; // Estimate 2 hours per learning session\n\n      monthlyProgress.push({\n        month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),\n        goalsCreated: monthGoalsCreated,\n        goalsCompleted: monthGoalsCompleted,\n        learningHours: monthLearningHours,\n      });\n    }\n\n    // Calculate streak data\n    const uniqueDates = Array.from(new Set(\n      learningProgress.map(progress =>\n        progress.updatedAt.toDateString()\n      )\n    )).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());\n\n    let currentStreak = 0;\n    let longestStreak = 0;\n    let tempStreak = 0;\n\n    // Calculate current streak\n    const today = new Date().toDateString();\n    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();\n    \n    if (uniqueDates.includes(today) || uniqueDates.includes(yesterday)) {\n      let checkDate = new Date();\n      while (uniqueDates.includes(checkDate.toDateString())) {\n        currentStreak++;\n        checkDate = new Date(checkDate.getTime() - 24 * 60 * 60 * 1000);\n      }\n    }\n\n    // Calculate longest streak\n    for (let i = 0; i < uniqueDates.length; i++) {\n      if (i === 0) {\n        tempStreak = 1;\n      } else {\n        const currentDate = new Date(uniqueDates[i]);\n        const previousDate = new Date(uniqueDates[i - 1]);\n        const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);\n        \n        if (dayDiff === 1) {\n          tempStreak++;\n        } else {\n          longestStreak = Math.max(longestStreak, tempStreak);\n          tempStreak = 1;\n        }\n      }\n    }\n    longestStreak = Math.max(longestStreak, tempStreak);\n\n    // Generate insights\n    const insights = [];\n\n    if (completionRate >= 80) {\n      insights.push({\n        type: 'success',\n        title: 'Excellent Goal Achievement!',\n        description: `You have a ${completionRate}% goal completion rate. Keep up the great work!`,\n      });\n    } else if (completionRate < 50) {\n      insights.push({\n        type: 'warning',\n        title: 'Goal Completion Needs Attention',\n        description: `Your goal completion rate is ${completionRate}%. Consider setting more achievable goals or breaking large goals into smaller tasks.`,\n        action: 'Try setting smaller, more specific goals to build momentum.',\n      });\n    }\n\n    if (currentStreak >= 7) {\n      insights.push({\n        type: 'success',\n        title: 'Amazing Learning Streak!',\n        description: `You've been consistently learning for ${currentStreak} days. This is building great habits!`,\n      });\n    } else if (currentStreak === 0) {\n      insights.push({\n        type: 'info',\n        title: 'Time to Get Back on Track',\n        description: 'You haven\\'t logged any learning activity recently. Even 15 minutes a day can make a difference!',\n        action: 'Set a small daily learning goal to rebuild your streak.',\n      });\n    }\n\n    if (activeGoals === 0) {\n      insights.push({\n        type: 'info',\n        title: 'No Active Goals',\n        description: 'You don\\'t have any active goals. Setting clear objectives can help guide your learning journey.',\n        action: 'Create a new goal to focus your learning efforts.',\n      });\n    } else if (activeGoals > 5) {\n      insights.push({\n        type: 'warning',\n        title: 'Many Active Goals',\n        description: `You have ${activeGoals} active goals. Consider focusing on fewer goals for better results.`,\n        action: 'Try focusing on 2-3 key goals at a time.',\n      });\n    }\n\n    const analyticsData = {\n      goalStats: {\n        total: totalGoals,\n        completed: completedGoals,\n        active: activeGoals,\n        completionRate,\n        averageTimeToComplete,\n      },\n      categoryBreakdown: categoryBreakdownArray,\n      monthlyProgress,\n      streakData: {\n        currentStreak,\n        longestStreak,\n        totalActiveDays: uniqueDates.length,\n      },\n      achievements: {\n        total: userAchievements.length,\n        recent: userAchievements.slice(0, 5).map(ua => ({\n          id: ua.achievement.id,\n          title: ua.achievement.title,\n          unlockedAt: ua.unlockedAt.toISOString(),\n          points: ua.achievement.points,\n        })),\n      },\n      insights,\n    };\n\n  return NextResponse.json({\n    success: true as const,\n    data: analyticsData\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1241b8d620cd236d2e41f7dc938e6d2b4a52ba73"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_x86nk9mhy = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_x86nk9mhy();
var __awaiter =
/* istanbul ignore next */
(cov_x86nk9mhy().s[0]++,
/* istanbul ignore next */
(cov_x86nk9mhy().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_x86nk9mhy().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_x86nk9mhy().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_x86nk9mhy().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_x86nk9mhy().f[1]++;
    cov_x86nk9mhy().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_x86nk9mhy().f[2]++;
      cov_x86nk9mhy().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_x86nk9mhy().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_x86nk9mhy().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_x86nk9mhy().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_x86nk9mhy().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_x86nk9mhy().f[4]++;
      cov_x86nk9mhy().s[4]++;
      try {
        /* istanbul ignore next */
        cov_x86nk9mhy().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_x86nk9mhy().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_x86nk9mhy().f[5]++;
      cov_x86nk9mhy().s[7]++;
      try {
        /* istanbul ignore next */
        cov_x86nk9mhy().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_x86nk9mhy().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_x86nk9mhy().f[6]++;
      cov_x86nk9mhy().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_x86nk9mhy().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_x86nk9mhy().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_x86nk9mhy().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_x86nk9mhy().s[12]++,
/* istanbul ignore next */
(cov_x86nk9mhy().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_x86nk9mhy().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_x86nk9mhy().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_x86nk9mhy().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_x86nk9mhy().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_x86nk9mhy().f[8]++;
        cov_x86nk9mhy().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_x86nk9mhy().b[6][0]++;
          cov_x86nk9mhy().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_x86nk9mhy().b[6][1]++;
        }
        cov_x86nk9mhy().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_x86nk9mhy().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_x86nk9mhy().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_x86nk9mhy().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_x86nk9mhy().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_x86nk9mhy().f[9]++;
    cov_x86nk9mhy().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_x86nk9mhy().f[10]++;
    cov_x86nk9mhy().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_x86nk9mhy().f[11]++;
      cov_x86nk9mhy().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_x86nk9mhy().f[12]++;
    cov_x86nk9mhy().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_x86nk9mhy().b[9][0]++;
      cov_x86nk9mhy().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_x86nk9mhy().b[9][1]++;
    }
    cov_x86nk9mhy().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_x86nk9mhy().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_x86nk9mhy().s[25]++;
      try {
        /* istanbul ignore next */
        cov_x86nk9mhy().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[15][0]++,
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[16][1]++,
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_x86nk9mhy().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_x86nk9mhy().b[12][0]++;
          cov_x86nk9mhy().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_x86nk9mhy().b[12][1]++;
        }
        cov_x86nk9mhy().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_x86nk9mhy().b[18][0]++;
          cov_x86nk9mhy().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_x86nk9mhy().b[18][1]++;
        }
        cov_x86nk9mhy().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_x86nk9mhy().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_x86nk9mhy().b[19][1]++;
            cov_x86nk9mhy().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_x86nk9mhy().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_x86nk9mhy().b[19][2]++;
            cov_x86nk9mhy().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_x86nk9mhy().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_x86nk9mhy().b[19][3]++;
            cov_x86nk9mhy().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_x86nk9mhy().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_x86nk9mhy().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_x86nk9mhy().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_x86nk9mhy().b[19][4]++;
            cov_x86nk9mhy().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_x86nk9mhy().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_x86nk9mhy().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_x86nk9mhy().b[19][5]++;
            cov_x86nk9mhy().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[20][0]++;
              cov_x86nk9mhy().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_x86nk9mhy().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[20][1]++;
            }
            cov_x86nk9mhy().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[23][0]++;
              cov_x86nk9mhy().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_x86nk9mhy().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[23][1]++;
            }
            cov_x86nk9mhy().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[25][0]++;
              cov_x86nk9mhy().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_x86nk9mhy().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_x86nk9mhy().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[25][1]++;
            }
            cov_x86nk9mhy().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_x86nk9mhy().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[27][0]++;
              cov_x86nk9mhy().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_x86nk9mhy().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_x86nk9mhy().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[27][1]++;
            }
            cov_x86nk9mhy().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[29][0]++;
              cov_x86nk9mhy().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[29][1]++;
            }
            cov_x86nk9mhy().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_x86nk9mhy().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_x86nk9mhy().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_x86nk9mhy().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_x86nk9mhy().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_x86nk9mhy().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_x86nk9mhy().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_x86nk9mhy().b[30][0]++;
      cov_x86nk9mhy().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_x86nk9mhy().b[30][1]++;
    }
    cov_x86nk9mhy().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_x86nk9mhy().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_x86nk9mhy().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_x86nk9mhy().s[67]++,
/* istanbul ignore next */
(cov_x86nk9mhy().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_x86nk9mhy().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_x86nk9mhy().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_x86nk9mhy().f[13]++;
  cov_x86nk9mhy().s[68]++;
  return /* istanbul ignore next */(cov_x86nk9mhy().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_x86nk9mhy().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_x86nk9mhy().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_x86nk9mhy().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_x86nk9mhy().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_x86nk9mhy().s[70]++;
exports.GET = exports.dynamic = void 0;
var server_1 =
/* istanbul ignore next */
(cov_x86nk9mhy().s[71]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_x86nk9mhy().s[72]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_x86nk9mhy().s[73]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_x86nk9mhy().s[74]++, __importDefault(require("@/lib/prisma")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_x86nk9mhy().s[75]++, require("@/lib/unified-api-error-handler"));
// Force dynamic rendering for this route
/* istanbul ignore next */
cov_x86nk9mhy().s[76]++;
exports.dynamic = 'force-dynamic';
// GET /api/progress/analytics - Get user progress analytics
/* istanbul ignore next */
cov_x86nk9mhy().s[77]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_x86nk9mhy().f[14]++;
  cov_x86nk9mhy().s[78]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_x86nk9mhy().f[15]++;
    var session, error, searchParams, range, now, startDate, goals, userAchievements, learningProgress, totalGoals, completedGoals, activeGoals, completionRate, completedGoalsWithTime, averageTimeToComplete, categoryBreakdown, categoryBreakdownArray, monthlyProgress, _loop_1, i, uniqueDates, currentStreak, longestStreak, tempStreak, today, yesterday, checkDate, i, currentDate, previousDate, dayDiff, insights, analyticsData;
    var _a;
    /* istanbul ignore next */
    cov_x86nk9mhy().s[79]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_x86nk9mhy().f[16]++;
      cov_x86nk9mhy().s[80]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_x86nk9mhy().b[35][0]++;
          cov_x86nk9mhy().s[81]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_x86nk9mhy().b[35][1]++;
          cov_x86nk9mhy().s[82]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_x86nk9mhy().s[83]++;
          if (!(
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[38][0]++, (_a =
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[40][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[40][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[39][0]++, void 0) :
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[39][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[38][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[37][0]++, void 0) :
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[37][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[36][0]++;
            cov_x86nk9mhy().s[84]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_x86nk9mhy().s[85]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_x86nk9mhy().s[86]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_x86nk9mhy().b[36][1]++;
          }
          cov_x86nk9mhy().s[87]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_x86nk9mhy().s[88]++;
          range =
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[41][0]++, searchParams.get('range')) ||
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[41][1]++, '6months');
          /* istanbul ignore next */
          cov_x86nk9mhy().s[89]++;
          now = new Date();
          /* istanbul ignore next */
          cov_x86nk9mhy().s[90]++;
          switch (range) {
            case '1month':
              /* istanbul ignore next */
              cov_x86nk9mhy().b[42][0]++;
              cov_x86nk9mhy().s[91]++;
              startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
              /* istanbul ignore next */
              cov_x86nk9mhy().s[92]++;
              break;
            case '3months':
              /* istanbul ignore next */
              cov_x86nk9mhy().b[42][1]++;
              cov_x86nk9mhy().s[93]++;
              startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
              /* istanbul ignore next */
              cov_x86nk9mhy().s[94]++;
              break;
            case '6months':
              /* istanbul ignore next */
              cov_x86nk9mhy().b[42][2]++;
              cov_x86nk9mhy().s[95]++;
              startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
              /* istanbul ignore next */
              cov_x86nk9mhy().s[96]++;
              break;
            case '1year':
              /* istanbul ignore next */
              cov_x86nk9mhy().b[42][3]++;
              cov_x86nk9mhy().s[97]++;
              startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
              /* istanbul ignore next */
              cov_x86nk9mhy().s[98]++;
              break;
            default:
              /* istanbul ignore next */
              cov_x86nk9mhy().b[42][4]++;
              cov_x86nk9mhy().s[99]++;
              startDate = new Date(0);
            // All time
          }
          /* istanbul ignore next */
          cov_x86nk9mhy().s[100]++;
          return [4 /*yield*/, prisma_1.default.userGoal.findMany({
            where: {
              userId: session.user.id,
              createdAt: {
                gte: startDate
              }
            },
            orderBy: {
              createdAt: 'desc'
            }
          })];
        case 2:
          /* istanbul ignore next */
          cov_x86nk9mhy().b[35][2]++;
          cov_x86nk9mhy().s[101]++;
          goals = _b.sent();
          /* istanbul ignore next */
          cov_x86nk9mhy().s[102]++;
          return [4 /*yield*/, prisma_1.default.userAchievement.findMany({
            where: {
              userId: session.user.id,
              unlockedAt: {
                gte: startDate
              }
            },
            include: {
              achievement: {
                select: {
                  id: true,
                  title: true,
                  points: true
                }
              }
            },
            orderBy: {
              unlockedAt: 'desc'
            }
          })];
        case 3:
          /* istanbul ignore next */
          cov_x86nk9mhy().b[35][3]++;
          cov_x86nk9mhy().s[103]++;
          userAchievements = _b.sent();
          /* istanbul ignore next */
          cov_x86nk9mhy().s[104]++;
          return [4 /*yield*/, prisma_1.default.userLearningProgress.findMany({
            where: {
              userId: session.user.id,
              updatedAt: {
                gte: startDate
              }
            },
            orderBy: {
              updatedAt: 'desc'
            }
          })];
        case 4:
          /* istanbul ignore next */
          cov_x86nk9mhy().b[35][4]++;
          cov_x86nk9mhy().s[105]++;
          learningProgress = _b.sent();
          /* istanbul ignore next */
          cov_x86nk9mhy().s[106]++;
          totalGoals = goals.length;
          /* istanbul ignore next */
          cov_x86nk9mhy().s[107]++;
          completedGoals = goals.filter(function (goal) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[17]++;
            cov_x86nk9mhy().s[108]++;
            return goal.status === 'COMPLETED';
          }).length;
          /* istanbul ignore next */
          cov_x86nk9mhy().s[109]++;
          activeGoals = goals.filter(function (goal) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[18]++;
            cov_x86nk9mhy().s[110]++;
            return goal.status === 'ACTIVE';
          }).length;
          /* istanbul ignore next */
          cov_x86nk9mhy().s[111]++;
          completionRate = totalGoals > 0 ?
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[43][0]++, Math.round(completedGoals / totalGoals * 100)) :
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[43][1]++, 0);
          /* istanbul ignore next */
          cov_x86nk9mhy().s[112]++;
          completedGoalsWithTime = goals.filter(function (goal) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[19]++;
            cov_x86nk9mhy().s[113]++;
            return goal.status === 'COMPLETED';
          });
          /* istanbul ignore next */
          cov_x86nk9mhy().s[114]++;
          averageTimeToComplete = completedGoalsWithTime.length > 0 ?
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[44][0]++, Math.round(completedGoalsWithTime.reduce(function (sum, goal) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[20]++;
            var timeDiff =
            /* istanbul ignore next */
            (cov_x86nk9mhy().s[115]++, goal.updatedAt.getTime() - goal.createdAt.getTime());
            /* istanbul ignore next */
            cov_x86nk9mhy().s[116]++;
            return sum + timeDiff / (1000 * 60 * 60 * 24); // Convert to days
          }, 0) / completedGoalsWithTime.length)) :
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[44][1]++, 0);
          /* istanbul ignore next */
          cov_x86nk9mhy().s[117]++;
          categoryBreakdown = goals.reduce(function (acc, goal) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[21]++;
            var category =
            /* istanbul ignore next */
            (cov_x86nk9mhy().s[118]++, goal.category);
            /* istanbul ignore next */
            cov_x86nk9mhy().s[119]++;
            if (!acc[category]) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[45][0]++;
              cov_x86nk9mhy().s[120]++;
              acc[category] = {
                count: 0,
                completed: 0
              };
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[45][1]++;
            }
            cov_x86nk9mhy().s[121]++;
            acc[category].count++;
            /* istanbul ignore next */
            cov_x86nk9mhy().s[122]++;
            if (goal.status === 'COMPLETED') {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[46][0]++;
              cov_x86nk9mhy().s[123]++;
              acc[category].completed++;
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[46][1]++;
            }
            cov_x86nk9mhy().s[124]++;
            return acc;
          }, {});
          /* istanbul ignore next */
          cov_x86nk9mhy().s[125]++;
          categoryBreakdownArray = Object.entries(categoryBreakdown).map(function (_a) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[22]++;
            var category =
              /* istanbul ignore next */
              (cov_x86nk9mhy().s[126]++, _a[0]),
              data =
              /* istanbul ignore next */
              (cov_x86nk9mhy().s[127]++, _a[1]);
            /* istanbul ignore next */
            cov_x86nk9mhy().s[128]++;
            return {
              category: category,
              count: data.count,
              completed: data.completed,
              percentage: data.count > 0 ?
              /* istanbul ignore next */
              (cov_x86nk9mhy().b[47][0]++, Math.round(data.completed / data.count * 100)) :
              /* istanbul ignore next */
              (cov_x86nk9mhy().b[47][1]++, 0)
            };
          });
          /* istanbul ignore next */
          cov_x86nk9mhy().s[129]++;
          monthlyProgress = [];
          /* istanbul ignore next */
          cov_x86nk9mhy().s[130]++;
          _loop_1 = function (i) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[23]++;
            var monthStart =
            /* istanbul ignore next */
            (cov_x86nk9mhy().s[131]++, new Date(now.getFullYear(), now.getMonth() - i, 1));
            var monthEnd =
            /* istanbul ignore next */
            (cov_x86nk9mhy().s[132]++, new Date(now.getFullYear(), now.getMonth() - i + 1, 0));
            var monthGoalsCreated =
            /* istanbul ignore next */
            (cov_x86nk9mhy().s[133]++, goals.filter(function (goal) {
              /* istanbul ignore next */
              cov_x86nk9mhy().f[24]++;
              cov_x86nk9mhy().s[134]++;
              return /* istanbul ignore next */(cov_x86nk9mhy().b[48][0]++, goal.createdAt >= monthStart) &&
              /* istanbul ignore next */
              (cov_x86nk9mhy().b[48][1]++, goal.createdAt <= monthEnd);
            }).length);
            var monthGoalsCompleted =
            /* istanbul ignore next */
            (cov_x86nk9mhy().s[135]++, goals.filter(function (goal) {
              /* istanbul ignore next */
              cov_x86nk9mhy().f[25]++;
              cov_x86nk9mhy().s[136]++;
              return /* istanbul ignore next */(cov_x86nk9mhy().b[49][0]++, goal.status === 'COMPLETED') &&
              /* istanbul ignore next */
              (cov_x86nk9mhy().b[49][1]++, goal.updatedAt >= monthStart) &&
              /* istanbul ignore next */
              (cov_x86nk9mhy().b[49][2]++, goal.updatedAt <= monthEnd);
            }).length);
            var monthLearningHours =
            /* istanbul ignore next */
            (cov_x86nk9mhy().s[137]++, learningProgress.filter(function (progress) {
              /* istanbul ignore next */
              cov_x86nk9mhy().f[26]++;
              cov_x86nk9mhy().s[138]++;
              return /* istanbul ignore next */(cov_x86nk9mhy().b[50][0]++, progress.updatedAt >= monthStart) &&
              /* istanbul ignore next */
              (cov_x86nk9mhy().b[50][1]++, progress.updatedAt <= monthEnd);
            }).length * 2); // Estimate 2 hours per learning session
            /* istanbul ignore next */
            cov_x86nk9mhy().s[139]++;
            monthlyProgress.push({
              month: monthStart.toLocaleDateString('en-US', {
                month: 'short',
                year: 'numeric'
              }),
              goalsCreated: monthGoalsCreated,
              goalsCompleted: monthGoalsCompleted,
              learningHours: monthLearningHours
            });
          };
          /* istanbul ignore next */
          cov_x86nk9mhy().s[140]++;
          for (i = 5; i >= 0; i--) {
            /* istanbul ignore next */
            cov_x86nk9mhy().s[141]++;
            _loop_1(i);
          }
          /* istanbul ignore next */
          cov_x86nk9mhy().s[142]++;
          uniqueDates = Array.from(new Set(learningProgress.map(function (progress) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[27]++;
            cov_x86nk9mhy().s[143]++;
            return progress.updatedAt.toDateString();
          }))).sort(function (a, b) {
            /* istanbul ignore next */
            cov_x86nk9mhy().f[28]++;
            cov_x86nk9mhy().s[144]++;
            return new Date(b).getTime() - new Date(a).getTime();
          });
          /* istanbul ignore next */
          cov_x86nk9mhy().s[145]++;
          currentStreak = 0;
          /* istanbul ignore next */
          cov_x86nk9mhy().s[146]++;
          longestStreak = 0;
          /* istanbul ignore next */
          cov_x86nk9mhy().s[147]++;
          tempStreak = 0;
          /* istanbul ignore next */
          cov_x86nk9mhy().s[148]++;
          today = new Date().toDateString();
          /* istanbul ignore next */
          cov_x86nk9mhy().s[149]++;
          yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();
          /* istanbul ignore next */
          cov_x86nk9mhy().s[150]++;
          if (
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[52][0]++, uniqueDates.includes(today)) ||
          /* istanbul ignore next */
          (cov_x86nk9mhy().b[52][1]++, uniqueDates.includes(yesterday))) {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[51][0]++;
            cov_x86nk9mhy().s[151]++;
            checkDate = new Date();
            /* istanbul ignore next */
            cov_x86nk9mhy().s[152]++;
            while (uniqueDates.includes(checkDate.toDateString())) {
              /* istanbul ignore next */
              cov_x86nk9mhy().s[153]++;
              currentStreak++;
              /* istanbul ignore next */
              cov_x86nk9mhy().s[154]++;
              checkDate = new Date(checkDate.getTime() - 24 * 60 * 60 * 1000);
            }
          } else
          /* istanbul ignore next */
          {
            cov_x86nk9mhy().b[51][1]++;
          }
          // Calculate longest streak
          cov_x86nk9mhy().s[155]++;
          for (i = 0; i < uniqueDates.length; i++) {
            /* istanbul ignore next */
            cov_x86nk9mhy().s[156]++;
            if (i === 0) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[53][0]++;
              cov_x86nk9mhy().s[157]++;
              tempStreak = 1;
            } else {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[53][1]++;
              cov_x86nk9mhy().s[158]++;
              currentDate = new Date(uniqueDates[i]);
              /* istanbul ignore next */
              cov_x86nk9mhy().s[159]++;
              previousDate = new Date(uniqueDates[i - 1]);
              /* istanbul ignore next */
              cov_x86nk9mhy().s[160]++;
              dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);
              /* istanbul ignore next */
              cov_x86nk9mhy().s[161]++;
              if (dayDiff === 1) {
                /* istanbul ignore next */
                cov_x86nk9mhy().b[54][0]++;
                cov_x86nk9mhy().s[162]++;
                tempStreak++;
              } else {
                /* istanbul ignore next */
                cov_x86nk9mhy().b[54][1]++;
                cov_x86nk9mhy().s[163]++;
                longestStreak = Math.max(longestStreak, tempStreak);
                /* istanbul ignore next */
                cov_x86nk9mhy().s[164]++;
                tempStreak = 1;
              }
            }
          }
          /* istanbul ignore next */
          cov_x86nk9mhy().s[165]++;
          longestStreak = Math.max(longestStreak, tempStreak);
          /* istanbul ignore next */
          cov_x86nk9mhy().s[166]++;
          insights = [];
          /* istanbul ignore next */
          cov_x86nk9mhy().s[167]++;
          if (completionRate >= 80) {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[55][0]++;
            cov_x86nk9mhy().s[168]++;
            insights.push({
              type: 'success',
              title: 'Excellent Goal Achievement!',
              description: "You have a ".concat(completionRate, "% goal completion rate. Keep up the great work!")
            });
          } else {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[55][1]++;
            cov_x86nk9mhy().s[169]++;
            if (completionRate < 50) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[56][0]++;
              cov_x86nk9mhy().s[170]++;
              insights.push({
                type: 'warning',
                title: 'Goal Completion Needs Attention',
                description: "Your goal completion rate is ".concat(completionRate, "%. Consider setting more achievable goals or breaking large goals into smaller tasks."),
                action: 'Try setting smaller, more specific goals to build momentum.'
              });
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[56][1]++;
            }
          }
          /* istanbul ignore next */
          cov_x86nk9mhy().s[171]++;
          if (currentStreak >= 7) {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[57][0]++;
            cov_x86nk9mhy().s[172]++;
            insights.push({
              type: 'success',
              title: 'Amazing Learning Streak!',
              description: "You've been consistently learning for ".concat(currentStreak, " days. This is building great habits!")
            });
          } else {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[57][1]++;
            cov_x86nk9mhy().s[173]++;
            if (currentStreak === 0) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[58][0]++;
              cov_x86nk9mhy().s[174]++;
              insights.push({
                type: 'info',
                title: 'Time to Get Back on Track',
                description: 'You haven\'t logged any learning activity recently. Even 15 minutes a day can make a difference!',
                action: 'Set a small daily learning goal to rebuild your streak.'
              });
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[58][1]++;
            }
          }
          /* istanbul ignore next */
          cov_x86nk9mhy().s[175]++;
          if (activeGoals === 0) {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[59][0]++;
            cov_x86nk9mhy().s[176]++;
            insights.push({
              type: 'info',
              title: 'No Active Goals',
              description: 'You don\'t have any active goals. Setting clear objectives can help guide your learning journey.',
              action: 'Create a new goal to focus your learning efforts.'
            });
          } else {
            /* istanbul ignore next */
            cov_x86nk9mhy().b[59][1]++;
            cov_x86nk9mhy().s[177]++;
            if (activeGoals > 5) {
              /* istanbul ignore next */
              cov_x86nk9mhy().b[60][0]++;
              cov_x86nk9mhy().s[178]++;
              insights.push({
                type: 'warning',
                title: 'Many Active Goals',
                description: "You have ".concat(activeGoals, " active goals. Consider focusing on fewer goals for better results."),
                action: 'Try focusing on 2-3 key goals at a time.'
              });
            } else
            /* istanbul ignore next */
            {
              cov_x86nk9mhy().b[60][1]++;
            }
          }
          /* istanbul ignore next */
          cov_x86nk9mhy().s[179]++;
          analyticsData = {
            goalStats: {
              total: totalGoals,
              completed: completedGoals,
              active: activeGoals,
              completionRate: completionRate,
              averageTimeToComplete: averageTimeToComplete
            },
            categoryBreakdown: categoryBreakdownArray,
            monthlyProgress: monthlyProgress,
            streakData: {
              currentStreak: currentStreak,
              longestStreak: longestStreak,
              totalActiveDays: uniqueDates.length
            },
            achievements: {
              total: userAchievements.length,
              recent: userAchievements.slice(0, 5).map(function (ua) {
                /* istanbul ignore next */
                cov_x86nk9mhy().f[29]++;
                cov_x86nk9mhy().s[180]++;
                return {
                  id: ua.achievement.id,
                  title: ua.achievement.title,
                  unlockedAt: ua.unlockedAt.toISOString(),
                  points: ua.achievement.points
                };
              })
            },
            insights: insights
          };
          /* istanbul ignore next */
          cov_x86nk9mhy().s[181]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: analyticsData
          })];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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