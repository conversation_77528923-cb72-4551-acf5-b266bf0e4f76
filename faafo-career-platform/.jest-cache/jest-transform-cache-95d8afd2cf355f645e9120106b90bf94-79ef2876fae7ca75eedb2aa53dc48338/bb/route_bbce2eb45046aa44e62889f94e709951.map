{"version": 3, "names": ["server_1", "cov_1vg1prwurh", "s", "require", "prisma_1", "unified_api_error_handler_1", "exports", "GET", "withUnifiedErrorHandling", "f", "__awaiter", "startTime", "Date", "now", "withDatabaseRetry", "prisma", "$queryRaw", "b", "templateObject_1", "__makeTemplateObject", "_a", "sent", "responseTime", "NextResponse", "json", "success", "data", "status", "database", "concat", "timestamp", "toISOString"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/health/database/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { prisma, withDatabaseRetry } from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface DatabaseHealthResponse {\n  status: 'healthy' | 'unhealthy';\n  database: 'connected' | 'disconnected';\n  responseTime?: string;\n  timestamp: string;\n  error?: string;\n}\n\nexport const GET = withUnifiedErrorHandling(async () => {\n  const startTime = Date.now();\n\n  // Test database connection with retry logic\n  await withDatabaseRetry(async () => {\n    await prisma.$queryRaw`SELECT 1`;\n  }, 2, 1000);\n\n  const responseTime = Date.now() - startTime;\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      status: 'healthy',\n      database: 'connected',\n      responseTime: `${responseTime}ms`,\n      timestamp: new Date().toISOString()\n    }\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,QAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,2BAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAC,OAAA;AAAwF;AAAAF,cAAA,GAAAC,CAAA;AAU3EI,OAAA,CAAAC,GAAG,GAAG,IAAAF,2BAAA,CAAAG,wBAAwB,EAAC;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAC,CAAA;EAAA,OAAAQ,SAAA;IAAA;IAAAT,cAAA,GAAAQ,CAAA;;;;;;;;;;;;;UACpCE,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;UAE5B;UAAA;UAAAZ,cAAA,GAAAC,CAAA;UACA,qBAAM,IAAAE,QAAA,CAAAU,iBAAiB,EAAC;YAAA;YAAAb,cAAA,GAAAQ,CAAA;YAAAR,cAAA,GAAAC,CAAA;YAAA,OAAAQ,SAAA;cAAA;cAAAT,cAAA,GAAAQ,CAAA;cAAAR,cAAA,GAAAC,CAAA;;;;;;;;;;oBACtB,qBAAME,QAAA,CAAAW,MAAM,CAACC,SAAS;oBAAA;oBAAA,CAAAf,cAAA,GAAAgB,CAAA,WAAAC,gBAAA;oBAAA;oBAAA,CAAAjB,cAAA,GAAAgB,CAAA,WAAAC,gBAAA,GAAAC,oBAAA,0BAAU;;;;;oBAAhCC,EAAA,CAAAC,IAAA,EAAgC;oBAAC;oBAAApB,cAAA,GAAAC,CAAA;;;;;WAClC,EAAE,CAAC,EAAE,IAAI,CAAC;;;;;UAHX;UACAkB,EAAA,CAAAC,IAAA,EAEW;UAAC;UAAApB,cAAA,GAAAC,CAAA;UAENoB,YAAY,GAAGV,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;UAAC;UAAAV,cAAA,GAAAC,CAAA;UAE5C,sBAAOF,QAAA,CAAAuB,YAAY,CAACC,IAAI,CAAC;YACvBC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACJC,MAAM,EAAE,SAAS;cACjBC,QAAQ,EAAE,WAAW;cACrBN,YAAY,EAAE,GAAAO,MAAA,CAAGP,YAAY,OAAI;cACjCQ,SAAS,EAAE,IAAIlB,IAAI,EAAE,CAACmB,WAAW;;WAEpC,CAAC;;;;CACH,CAAC", "ignoreList": []}